#pragma vertex VSMain
#pragma pixel PSMain

#define DEFERRED_SHADING

#include "../ShaderLibrary/Common.hlsl"
#include "ViewModeVisualizeDef.hlsl"
#include "../Material/MaterialCommon.hlsl"
#include "../Material/Lit/LitCommonStruct.hlsl"
#include "../ShaderLibrary/GlobalModelVariables.hlsl"
#include "../Material/Lit/GbufferEncoderDecoder.hlsl"

struct VS2PS
{
    float2 UV  : TEXCOORD0;
    float4 Pos : SV_POSITION;
};

SHADER_CONST(bool, ENABLE_AO, false);
SHADER_CONST(bool, ENABLE_REFLECTION, false);
SHADER_CONST(bool, ENABLE_SEPARATE_TRANSLUCENCY, false);
SHADER_CONST(bool, ENABLE_GI, false);
SHADER_CONST(bool, ENABLE_DEBUG_TEX, false);

Texture2D<float4>     AOTex                    : register(space0);
Texture2D<float4>     ReflectionTex            : register(space0);
Texture2D<float4>     SeparateTranslucencyTex  : register(space0);
Texture2D<float4>     OpacityColorTex          : register(space0);
Texture2D<float4>     GITex                    : register(space0);
Texture2D<float4>     WireframeTex             : register(space0);
Texture2D<float4>     SceneColorTex            : register(space0);
Texture2D<float4>     EmissiveColorTex         : register(space0);


Texture2D<float4>    DebugTex   : register(space0);

void DrawRectangle(
    float4 InPosition,
    float2 InTexCoord,
    out float4 OutPosition,
    out float2 OutTexCoord)
{
    OutPosition = InPosition;
    OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
    OutPosition.xy *= float2(1, -1);
    OutTexCoord.xy = InTexCoord.xy;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
    VS2PS ret;
    float4 Outpos;
    float2 Outuv;
    DrawRectangle(
        Pos,
        uv,
        Outpos,
        Outuv);
    ret.Pos = Outpos;
    ret.UV = Outuv;
    return ret;
}

float3 LinearToSrgb(float3 lin) 
{
	lin = max(6.10352e-5, lin); // minimum positive non-denormal (fixes black problem on DX11 AMD and NV)
	return min(lin * 12.92, pow(max(lin, 0.00313067), 1.0/2.4) * 1.055 - 0.055);
}

// Keep aligned with UE's shading model
float3 GetShadingModelColor(BSDFData bsdfData)
{
    if (bsdfData.materialType == MaterialType_Standard)
    {
        return float3(0, 1, 0);
    } else if (bsdfData.materialType == MaterialType_Unlit)
    {
        return float3(0, 0, 1);
    } else if (bsdfData.materialType == MaterialType_Subsurface || bsdfData.materialType == MaterialType_TwosidedFoliage || bsdfData.materialType == MaterialType_ImposterFoliage)
    {
        return float3(1, 0, 0);
    }
    return float3(0.6, 0.4, 0.1); // The color of Preintegrated Skin
}

float4 PSMain(VS2PS input) : SV_TARGET
{
    float4 visColor = SceneColorTex.Sample(ce_Sampler_Point, input.UV);

    BSDFData bsdfData;
	BuiltinData builtinData;
	DecodeFromGBuffer(input.UV, bsdfData, builtinData);

    if (VIEW_MODE == ViewMode_Lit)
    {
        visColor = SceneColorTex.Sample(ce_Sampler_Point, input.UV);
    }
    if (VIEW_MODE == ViewMode_Unlit)
    {
        visColor = float4(LinearToSrgb(bsdfData.diffuseColor), 1.0);
    }
    if (VIEW_MODE == ViewMode_Wireframe)
    {
        visColor = SceneColorTex.Sample(ce_Sampler_Point, input.UV);
    }
    if (VIEW_MODE == ViewMode_DetailLighting)
    {
        visColor = SceneColorTex.Sample(ce_Sampler_Point, input.UV);
    }
    if (VIEW_MODE == ViewMode_LightingOnly)
    {
        visColor = SceneColorTex.Sample(ce_Sampler_Point, input.UV);
    }
    if (VIEW_MODE == ViewMode_BaseColor)
    {
        visColor = float4(LinearToSrgb(bsdfData.diffuseColor), 1.0);
    }
    if (VIEW_MODE == ViewMode_DebugColor)
    {
        visColor = float4(LinearToSrgb(bsdfData.diffuseColor), 1.0);
    }
        
    if (VIEW_MODE == ViewMode_Depth)
    {
        float depth = _DepthMap.Sample(ce_Sampler_Point, input.UV);
        visColor = float4(pow(depth, 0.3).xxx, 1.0);
    }

    if (VIEW_MODE == ViewMode_WorldNormal)
    {
        visColor = float4(normalize((bsdfData.geomNormalWS.rgb + 1.0) / 2.0), 1.0);
    }
        
    if (VIEW_MODE == ViewMode_DiffuseColor) {
        float3 diffuseColor = bsdfData.diffuseColor * bsdfData.ambientOcclusion;
        visColor = float4(LinearToSrgb(diffuseColor), 1.0);
    }
    if (VIEW_MODE == ViewMode_MaterialAO)
        visColor = float4(bsdfData.ambientOcclusion.xxx, 1.0);
        
    if (VIEW_MODE == ViewMode_Metallic)
        visColor = float4(bsdfData.metallic.xxx, 1.0);
        
    if (VIEW_MODE == ViewMode_Opacity)
        visColor = float4(bsdfData.opacity.xxx, 1.0);
        
    if (VIEW_MODE == ViewMode_Roughness)
        visColor = float4(bsdfData.roughness.xxx, 1.0);
        
    if (VIEW_MODE == ViewMode_Specular) {
        float specular = _GBuffer1.Sample(ce_Sampler_Point, input.UV).w;
        visColor = float4(LinearToSrgb(specular.xxx), 1.0);
    }
    if (VIEW_MODE == ViewMode_SpecularColor)
        visColor = float4(bsdfData.fresnel0, 1.0);
        
    if (VIEW_MODE == ViewMode_SubsurfaceColor)
        visColor = float4(bsdfData.subsurfaceColor.rgb, 1.0);
        
    if (VIEW_MODE == ViewMode_ShadingModel)
        visColor = float4(GetShadingModelColor(bsdfData), 1.0);
        
    if (VIEW_MODE == ViewMode_MotionVector)
    {
        float2 motionVector = _GBuffer3.Sample(ce_Sampler_Point, input.UV).xy;
        visColor = float4(motionVector, 0, 1.0);
    }
    if (VIEW_MODE == ViewMode_AmbientOcclusion)
    {
        if (ENABLE_AO)
        {
            float3 aoVal = GITex.Sample(ce_Sampler_Point, input.UV).xyz;
            visColor = float4(aoVal, 1.0);
        }
        else
        {
            visColor = float4(1.xxx, 1.0);
        }
    }
    if (VIEW_MODE == ViewMode_BentNormal)
    {
        if (ENABLE_AO)
        {
            float3 bentNormal = AOTex.Sample(ce_Sampler_Point, input.UV).xyz;
            visColor = float4(bentNormal, 1.0);
        }
    }
    if (VIEW_MODE == ViewMode_Reflections)
    {
        float3 reflection = GITex.Sample(ce_Sampler_Point, input.UV).xyz;
        visColor = float4(reflection, 1.0);
        
        visColor = float4(0.xxx, 1);
        if (ENABLE_GI) // This combines SmartGI's glossy reflection and ReflectionIndirect's reflection
        {
            float3 reflections = GITex.Sample(ce_Sampler_Point, input.UV).xyz;
            visColor = float4(reflections, 1.0);
        }else if (ENABLE_REFLECTION)
        {
            float3 reflections = ReflectionTex.Sample(ce_Sampler_Point, input.UV).xyz;
            visColor = float4(reflections, 1.0);
        }
    }
    if (VIEW_MODE == ViewMode_SeparateTranslucencyRGB)
    {
        if (ENABLE_SEPARATE_TRANSLUCENCY)
        {
            float3 separateTranslucency = SeparateTranslucencyTex.Sample(ce_Sampler_Point, input.UV).xyz;
            visColor = float4(separateTranslucency, 1.0);
        }
    }
    if (VIEW_MODE == ViewMode_GlobalIllumination)
    {
        visColor = float4(0.xxx, 1);
        if (ENABLE_GI)
        {
            float3 diffuseGI = GITex.Sample(ce_Sampler_Point, input.UV).xyz;
            visColor = float4(diffuseGI, 1.0);
        }
    }
    if (VIEW_MODE == ViewMode_GILighting)
    {
        visColor = float4(0.xxx, 1);
        if (ENABLE_GI)
        {
            float3 giLighting = GITex.Sample(ce_Sampler_Point, input.UV).xyz;
            visColor = float4(giLighting, 1.0);
        }
    }
    if (VIEW_MODE == ViewMode_SceneColor)
    {
        float3 opacityColor = OpacityColorTex.Sample(ce_Sampler_Point, input.UV).xyz;
        visColor = float4(opacityColor, 1.0);
    }
    if (VIEW_MODE == ViewMode_EmissiveColor)
    {
        float3 emissiveColor = EmissiveColorTex.Sample(ce_Sampler_Point, input.UV).xyz;
        visColor = float4(emissiveColor, 1.0);
    }


    // Render debug texes to screen
    if (ENABLE_DEBUG_TEX)
    {
        float4 debugColor = DebugTex.Sample(ce_Sampler_Point, input.UV);
        visColor = float4(lerp(visColor.xyz, debugColor.xyz, debugColor.w), 1.0);
    }
    
    return visColor;
}