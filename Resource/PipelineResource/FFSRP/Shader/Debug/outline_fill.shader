#pragma vertex VSMain
#pragma pixel PSMain

// #pragma keyword USE_VEGETATION_ANIM
// #pragma keyword QTANGENT
#pragma keyword INSTANCING
#pragma keyword CE_USE_DOUBLE_TRANSFORM
#pragma keyword TERRAIN_RENDERING
#pragma keyword TERRAIN_USE_INSTANCING
#define NUM_MATERIAL_TEXCOORDS 2
// #pragma keyword TEXTURE_ARRAY_ENABLE

#include "../ShaderLibrary/GlobalModelVariables.hlsl"
// #include "../ShaderLibrary/ShaderMaterialVariables.hlsl"

#if defined(TERRAIN_RENDERING)
#define VERTEX_TYPE VertexType_Terrain
#include "../Material/Lit/LitVariablesTerrain.hlsl"
#else
// #define VERTEX_TYPE VertexType_Vegetation
#include "../Material/Lit/LitUEVariables.hlsl"
#endif
#include "../ShaderLibrary/Vertex.hlsl"

#include "../Material/Material.hlsl"
#include "../Lighting/Lighting.hlsl"

#if defined(TERRAIN_RENDERING)
#include "../Material/Lit/LitDataTerrain.hlsl"
#else
#include "../Material/Lit/LitDataUE.hlsl"
#endif
// #include "../Material/Lit/Lit.hlsl"
// #include "../../RenderPass/ShaderPassGBuffer.hlsl"
// #include "../Material/MaterialUtilities.hlsl"


VSOutput VSMain(VSInput input)
{
	return VSInputToVSOutput(input);
}

float4 PSMain(VSOutput input) : SV_TARGET
{
	return float4(1,1,1,1);
}