#pragma compute SubsurfaceScattering

#include "../../ShaderLibrary/Common.hlsl"
#include "../../ShaderLibrary/Packing.hlsl"
#include "../../ShaderLibrary/Sampling/Fibonacci.hlsl"
#include "../../ShaderLibrary/SpaceFillingCurves.hlsl"
#include "SubsurfaceScattering.hlsl"

#define SSS_USE_LDS_CACHE 1

#define MILLIMETERS_PER_METER 1000
#define SSS_PIXELS_PER_SAMPLE 4
#define GOLDEN_RATIO 1.618033988749895
#define EPSILON 1e-5

#define GROUP_SIZE_1D         16
#define GROUP_SIZE_2D         (GROUP_SIZE_1D * GROUP_SIZE_1D)
#define TEXTURE_CACHE_BORDER  2
#define TEXTURE_CACHE_SIZE_1D (GROUP_SIZE_1D + 2 * TEXTURE_CACHE_BORDER)
#define TEXTURE_CACHE_SIZE_2D (TEXTURE_CACHE_SIZE_1D * TEXTURE_CACHE_SIZE_1D)

//--------------------------------------------------------------------------------------------------
// Inputs & outputs
//--------------------------------------------------------------------------------------------------

cbuffer cbPass
{
	float4x4 _Proj;
	float4x4 _InvProj;
	float4x4 _InvView;
	float4 _ScreenParams;
}

Buffer<float4> _ShapeParamsAndMaxScatterDists;
Buffer<float4> _WorldScalesAndFilterRadiiAndThicknessRemaps;

Texture2D<half4> _DiffuseMap;
Texture2D<float> _DepthMap;
Texture2D<float2> _SSSMap;
RWTexture2D<float3> _OutputMap;

//--------------------------------------------------------------------------------------------------
// Implementation
//--------------------------------------------------------------------------------------------------

int Mad24(int a, int b, int c)
{
	return a * b + c;
}

#if SSS_USE_LDS_CACHE
groupshared float2 textureCache0[TEXTURE_CACHE_SIZE_2D]; // {irradiance.rg}
groupshared float2 textureCache1[TEXTURE_CACHE_SIZE_2D]; // {irradiance.b, deviceDepth}

void StoreSampleToCacheMemory(float4 value, int2 cacheCoord)
{
	int linearCoord = Mad24(TEXTURE_CACHE_SIZE_1D, cacheCoord.y, cacheCoord.x);

	textureCache0[linearCoord] = value.rg;
	textureCache1[linearCoord] = value.ba;
}

float4 LoadSampleFromCacheMemory(int2 cacheCoord)
{
	int linearCoord = Mad24(TEXTURE_CACHE_SIZE_1D, cacheCoord.y, cacheCoord.x);

	return float4(textureCache0[linearCoord],
		textureCache1[linearCoord]);
}
#endif

float4 LoadSampleFromVideoMemory(int2 pixelCoord)
{
	float3 irradiance = _DiffuseMap[pixelCoord].rgb;
	float  depth = _DepthMap[pixelCoord].x;

	return float4(irradiance, depth);
}

// Returns {irradiance, linearDepth}.
float4 LoadSample(int2 pixelCoord, int2 cacheOffset)
{
	float4 value;

#if SSS_USE_LDS_CACHE
	int2 cacheCoord = pixelCoord - cacheOffset;
	bool isInCache = max((uint)cacheCoord.x, (uint)cacheCoord.y) < TEXTURE_CACHE_SIZE_1D;

	if (isInCache)
	{
		value = LoadSampleFromCacheMemory(cacheCoord);
	}
	else
#endif
	{
		// Always load both irradiance and depth.
		// Avoid dependent texture reads at the cost of extra bandwidth.
		value = LoadSampleFromVideoMemory(pixelCoord);
	}

	value.a = LinearEyeDepth(value.a, _Proj);

	return value;
}

bool IsValidPixelCoord(int2 pixelCoord)
{
	return pixelCoord.x < _ScreenParams.x&& pixelCoord.y < _ScreenParams.y;
}

float3 ComputeViewSpacePosition(float2 positionNDC, float deviceDepth, float4x4 invProjMatrix)
{
	float4 positionCS = float4(positionNDC * 2.0 - 1.0, deviceDepth, 1.0);
	positionCS.y = -positionCS.y;
	float4 positionVS = mul(invProjMatrix, positionCS);
	return positionVS.xyz / positionVS.w;
}

float3 ComputeBilateralWeight(float xy2, float z, float mmPerUnit, float3 S, float rcpPdf)
{
	float r = sqrt(xy2 + (z * mmPerUnit) * (z * mmPerUnit));
	float area = rcpPdf;

	return EvalBurleyDiffusionProfile(r, S) * area;
}

void EvaluateSample(int i, int n, int2 pixelCoord, int2 cacheOffset, float3 S, float d,
	float pixelsPerMm, float mmPerUnit, float phase,
	inout float3 totalIrradiance, inout float3 totalWeight, float linearDepth)
{
	const float scale = rcp(n);
	const float offset = rcp(n) * 0.5;

	float sinPhase, cosPhase;
	sincos(phase, sinPhase, cosPhase);

	float r, rcpPdf;
	SampleBurleyDiffusionProfile(i * scale + offset, d, r, rcpPdf);

	float phi = SampleDiskGolden(i, n).y;
	float sinPhi, cosPhi;
	sincos(phi, sinPhi, cosPhi);

	float sinPsi = cosPhase * sinPhi + sinPhase * cosPhi; // sin(phase + phi)
	float cosPsi = cosPhase * cosPhi - sinPhase * sinPhi; // cos(phase + phi)

	float2 vec = r * float2(cosPsi, sinPsi);

	// Compute the screen-space position and the squared distance (in mm) in the image plane.
	float2 position = pixelCoord + (int2)round((pixelsPerMm * r) * float2(cosPsi, sinPsi));
	float xy2 = r * r;

	float4 textureSample = LoadSample(position, cacheOffset);
	float3 irradiance = textureSample.rgb;

	if (irradiance.b > 0) // TestLightingForSSS
	{
		// Apply bilateral weighting.
		float viewZ = textureSample.a;
		float relZ = viewZ - linearDepth;
		float3 weight = ComputeBilateralWeight(xy2, relZ, mmPerUnit, S, rcpPdf);

		// Note: if the texture sample if off-screen, (z = 0) -> (viewZ = far) -> (weight �� 0).
		totalIrradiance += weight * irradiance;
		totalWeight += weight;
	}
}

[numthreads(GROUP_SIZE_2D, 1, 1)]
void SubsurfaceScattering(
	uint3 groupId : SV_GroupID,
	uint groupThreadId : SV_GroupThreadID,
	uint3 dispatchThreadId : SV_DispatchThreadID)
{
	// Note: any factor of 64 is a suitable wave size for our algorithm.
	uint waveIndex = groupThreadId / 64;
	uint laneIndex = groupThreadId % 64;
	uint quadIndex = laneIndex / 4;

	// Arrange threads in the Morton order to optimally match the memory layout of GCN tiles.
	uint2 groupCoord = DecodeMorton2D(groupThreadId);
	uint2 groupOffset = groupId.xy * GROUP_SIZE_1D;
	uint2 pixelCoord = groupOffset + groupCoord;
	int2 cacheOffset = (int2)groupOffset - TEXTURE_CACHE_BORDER;

	float3 centerIrradiance = 0;
	float centerDepth = 0;
	float2 sssBuffer = 0;
	uint2 cacheCoord = groupCoord + TEXTURE_CACHE_BORDER;

	if (IsValidPixelCoord(pixelCoord))
	{
		centerIrradiance = _DiffuseMap[pixelCoord].rgb;
		sssBuffer = _SSSMap[pixelCoord].xy;
		float distScale = sssBuffer.x;

		if (distScale != 0)
		{
			centerDepth = _DepthMap[pixelCoord].x;
		}
	}

#if SSS_USE_LDS_CACHE
	// Populate the central region of the LDS cache.
	StoreSampleToCacheMemory(float4(centerIrradiance, centerDepth), cacheCoord);

	uint numBorderQuadsPerWave = TEXTURE_CACHE_SIZE_1D / 2 - 1;
	uint halfCacheWidthInQuads = TEXTURE_CACHE_SIZE_1D / 4;

	if (quadIndex < numBorderQuadsPerWave)
	{
		// Fetch another texel into the LDS.
		uint2 startQuad = halfCacheWidthInQuads * DeinterleaveQuad(waveIndex);

		uint2 quadCoord;

		// The traversal order is such that the quad's X coordinate is monotonically increasing.
		// The corner is always the near the block of the corresponding wavefront.
		// Note: the compiler can heavily optimize the code below, as the switch is scalar,
		// and there are very few unique values due to the symmetry.
		switch (waveIndex)
		{
		case 0:  // Bottom left
			quadCoord.x = max(0, (int)(quadIndex - (halfCacheWidthInQuads - 1)));
			quadCoord.y = max(0, (int)((halfCacheWidthInQuads - 1) - quadIndex));
			break;
		case 1:  // Bottom right
			quadCoord.x = min(quadIndex, halfCacheWidthInQuads - 1);
			quadCoord.y = max(0, (int)(quadIndex - (halfCacheWidthInQuads - 1)));
			break;
		case 2:  // Top left
			quadCoord.x = max(0, (int)(quadIndex - (halfCacheWidthInQuads - 1)));
			quadCoord.y = min(quadIndex, halfCacheWidthInQuads - 1);
			break;
		default: // Top right
			quadCoord.x = min(quadIndex, halfCacheWidthInQuads - 1);
			quadCoord.y = min(halfCacheWidthInQuads - 1, 2 * (halfCacheWidthInQuads - 1) - quadIndex);
			break;
		}

		uint2 cacheCoord2 = 2 * (startQuad + quadCoord) + DeinterleaveQuad(laneIndex);
		int2 pixelCoord2 = (int2)(groupOffset + cacheCoord2) - TEXTURE_CACHE_BORDER;
		float3 irradiance2 = 0;
		float depth2 = 0;
		float2 sssBuffer2 = 0;

		if (IsValidPixelCoord(pixelCoord2))
		{
			sssBuffer2 = _SSSMap[pixelCoord2].xy;
			float distScale = sssBuffer2.x;

			if (distScale != 0)
			{
				irradiance2 = _DiffuseMap[pixelCoord2].rgb;
				depth2 = _DepthMap[pixelCoord2].x;
			}
		}

		// Populate the border region of the LDS cache.
		StoreSampleToCacheMemory(float4(irradiance2, depth2), cacheCoord2);
	}

	// Wait for the LDS.
	GroupMemoryBarrierWithGroupSync();
#endif

	if (sssBuffer.x == 0)
	{
		_OutputMap[pixelCoord] = centerIrradiance;
		return;
	}

	PositionInputs posInput = GetPositionInput(pixelCoord, _ScreenParams.zw);

	int profileIndex = UnpackByte(sssBuffer.y);
	float distScale = sssBuffer.x;
	float3 S = _ShapeParamsAndMaxScatterDists[profileIndex].xyz;
	float d = _ShapeParamsAndMaxScatterDists[profileIndex].w;
	float metersPerUnit = _WorldScalesAndFilterRadiiAndThicknessRemaps[profileIndex].x;
	float filterRadius = _WorldScalesAndFilterRadiiAndThicknessRemaps[profileIndex].y; // In millimeters

	// Reconstruct the view-space position corresponding to the central sample.
	float2 centerPosNDC = posInput.positionNDC;
	float2 cornerPosNDC = centerPosNDC + 0.5 * _ScreenParams.zw;
	float3 centerPosVS = ComputeViewSpacePosition(centerPosNDC, centerDepth, _InvProj);
	float3 cornerPosVS = ComputeViewSpacePosition(cornerPosNDC, centerDepth, _InvProj);

	// Rescaling the filter is equivalent to inversely scaling the world.
	float mmPerUnit = MILLIMETERS_PER_METER * (metersPerUnit * rcp(distScale));
	float unitsPerMm = rcp(mmPerUnit);

	// Compute the view-space dimensions of the pixel as a quad projected onto geometry.
	// Assuming square pixels, both X and Y are have the same dimensions.
	float unitsPerPixel = max(0.0001f, 2 * abs(cornerPosVS.x - centerPosVS.x));
	float pixelsPerMm = rcp(unitsPerPixel) * unitsPerMm;

	// Area of a disk.
	float filterArea = M_PI * Square(filterRadius * pixelsPerMm);
	uint sampleCount = (uint)(filterArea * rcp(SSS_PIXELS_PER_SAMPLE));
	uint sampleBudget = 16;

	if (sampleCount < 1)
	{
		_OutputMap[pixelCoord] = centerIrradiance;
		return;
	}

	float phase = 0;

	uint n = min(sampleCount, sampleBudget);

	// Accumulate filtered irradiance and bilateral weights (for renormalization).
	float3 totalIrradiance = 0;
	float3 totalWeight = 0;

	float linearDepth = LinearEyeDepth(centerDepth, _Proj);
	for (uint i = 0; i < n; i++)
	{
		// Integrate over the image in the view space.
		EvaluateSample(i, n, pixelCoord, cacheOffset, S, d, pixelsPerMm,
			mmPerUnit, phase, totalIrradiance, totalWeight, linearDepth);
	}

	totalWeight = max(totalWeight, M_FloatMin);

	_OutputMap[pixelCoord] = totalIrradiance / totalWeight;
}

