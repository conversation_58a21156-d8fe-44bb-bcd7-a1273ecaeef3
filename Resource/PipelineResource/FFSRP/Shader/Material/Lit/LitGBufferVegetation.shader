#pragma vertex VSMain
#pragma pixel PSMain

// #pragma keyword USE_VEGETATION_ANIM
#pragma keyword QTANGENT
#pragma keyword CE_INSTANCING
// #pragma keyword CE_USE_DOUBLE_TRANSFORM
#pragma keyword TEXTURE_ARRAY_ENABLE

#define ENABLE_VIEW_MODE_VISUALIZE
#define CE_USE_DOUBLE_TRANSFORM 1
#define NUM_MATERIAL_TEXCOORDS 2


#include "Common/SceneData.hlsl"
#include "../../ShaderLibrary/GlobalModelVariables.hlsl"
#include "../../Material/Lit/LitUEVariables.hlsl"

#define VERTEX_TYPE VertexType_Vegetation
#include "../../ShaderLibrary/Vertex.hlsl"

#include "../../Material/Material.hlsl"
#include "../../Lighting/Lighting.hlsl"

#include "../../Material/Lit/LitDataUE.hlsl"
#include "../../Material/Lit/Lit.hlsl"
#include "../../RenderPass/ShaderPassGBuffer.hlsl"