#pragma vertex VSMain
#pragma pixel PSMain

#pragma keyword LIGHT_MAP_ENABLE
#pragma keyword LIGHT_MAP_UV_CHANNEL_0
#pragma keyword CE_USE_DOUBLE_TRANSFORM
#pragma keyword CE_INSTANCING

#define NUM_MATERIAL_TEXCOORDS 1

#define ENABLE_VIEW_MODE_VISUALIZE
#define USE_VERTEX_COLOR
#include "Common/SceneData.hlsl"
#include "ShaderLibrary/GlobalModelVariables.hlsl"

#ifdef CE_INSTANCING
    StructuredBuffer<ObjectSceneData> ce_PerObject : register(space2);
    #include "ShaderLibrary/Vertex_Instancing.hlsl"
#else
    #include "ShaderLibrary/Vertex.hlsl"
#endif

#include "Lighting/TransparentShadingUtils.hlsl"

VSOutput VSMain(VSInput input)
{
	return VSInputToVSOutput(input);
}

#ifdef CUSTOM_PS_MAIN
// user defined custom pxiel shader 
#else

Texture2D<float4> color_texture : register(space1);   
TransparentPSOutput PSMain(VSOutput vsoutput, bool isFrontFace : SV_IsFrontFace) 
{
#ifdef CE_INSTANCING
    float2 uv = float2(vsoutput.uv.xy);
#else
    float2 uv = float2(vsoutput.uvs[0].rg);
#endif
    float4 texColor = color_texture.Sample(ce_Sampler_Clamp, uv);
	float4 verColor = vsoutput.color;
	TransparentPSOutput psOut = (TransparentPSOutput)0;
	float4 outColor = texColor * verColor;
	psOut.sceneColor = float4(outColor.rgb, 1.0);
	return psOut;
}

#endif