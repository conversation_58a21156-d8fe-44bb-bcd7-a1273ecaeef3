#pragma vertex VSMain
#pragma pixel PSMain

#pragma keyword CE_INSTANCING

#define ENABLE_VIEW_MODE_VISUALIZE
#define CE_USE_DOUBLE_TRANSFORM 1
#define NUM_MATERIAL_TEXCOORDS 2

#include "Common/SceneData.hlsl"
#include "ShaderLibrary/GlobalModelVariables.hlsl"
#include "Material/Lit/LitUEVariables.hlsl"

#ifdef CE_INSTANCING
    StructuredBuffer<ObjectSceneData> ce_PerObject : register(space2);
    #include "ShaderLibrary/Vertex_Instancing.hlsl"
#else
    #include "ShaderLibrary/Vertex.hlsl"
#endif

#include "Material/Material.hlsl"
#include "Lighting/Lighting.hlsl"

#include "Material/Lit/LitDataUE.hlsl"
#include "Material/Lit/Lit.hlsl"
#include "RenderPass/ShaderPassDepthOnly.hlsl"