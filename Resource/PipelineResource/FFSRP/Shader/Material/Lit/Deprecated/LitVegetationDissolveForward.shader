#pragma vertex VSMain
#pragma pixel PSMain

#pragma keyword LIGHT_MAP_ENABLE
#pragma keyword LIGHT_MAP_UV_CHANNEL_0
#pragma keyword USE_VEGETATION_ANIM
#pragma keyword INSTANCING
#pragma keyword QTANGENT
#pragma keyword VEGATATION_AO_UV
#pragma keyword USE_VERTEX_COLOR

[[vk::constant_id(0)]]
int MATERIAL_TYPE = 0;

[[vk::constant_id(1)]]
bool ALPHA_CLIPPING = false;

[[vk::constant_id(2)]]
bool _NORMALMAP_TANGENT_SPACE = true;

[[vk::constant_id(3)]]
bool DOUBLE_SIDED = false;

[[vk::constant_id(4)]]
bool ENABLE_SSS = false;

[[vk::constant_id(5)]]
bool LIGHE_MAP_ENABLE = false;

[[vk::constant_id(6)]]
bool USE_LM_DIRECTIONALITY = false;

[[vk::constant_id(7)]]
bool ENABLE_LM_AO = false;

[[vk::constant_id(8)]]
bool ANIM_SINGLE_PIVOT_COLOR = false;

[[vk::constant_id(9)]]
bool ANIM_HIERARCHY_PIVOT = false;

[[vk::constant_id(10)]]
bool ANIM_PROCEDURAL_BRANCH = false;

[[vk::constant_id(11)]]
bool DISABLE_LM_GI = false;

[[vk::constant_id(12)]]
bool DEBUG_LM_GI_ONLY = false;

[[vk::constant_id(13)]]
bool DEBUG_LM_COLOR_ONLY = false;

[[vk::constant_id(14)]]
bool ENABLE_LIGHT_PROBE = false;

[[vk::constant_id(15)]]
bool ENABLE_VOLUMETRIC_LIGHT_MAP = false;

[[vk::constant_id(16)]]
bool USE_SKY_OCCLUSION = false;

[[vk::constant_id(17)]]
bool NO_INVERTUV = false;

[[vk::constant_id(18)]]
int ALPHA_CLIPPING_TYPE = 0;

[[vk::constant_id(19)]]
bool DISABLE_BAKE_SHADOW = false;

[[vk::constant_id(20)]]
bool DISABLE_BAKE_NORMAL = false;

[[vk::constant_id(21)]]
bool SHOW_DEBUG_LM_UV = false;

[[vk::constant_id(22)]]
bool SHOW_DEBUG_NORMAL = false;

[[vk::constant_id(23)]]
bool FLIP_NORMAL_Y = true;

[[vk::constant_id(24)]]
bool CHANNEL_PACKING = false;

[[vk::constant_id(25)]]
bool ENABLE_LIGHTMAP_PRT_API = false;

[[vk::constant_id(26)]]
bool ENABLE_LOCAL_LM_PRT = false;

#include "../../ShaderLibrary/GlobalModelVariables.hlsl"

cbuffer cbMtl : register(space1) {
    float4 _BaseColor;
    float _Metallic;

    float _Smoothness;
    float _NormalScale;

    float _SmoothnessRemapMin;
    float _SmoothnessRemapMax;

    float _MinAdjustClipDistance;
    float _MaxAdjustClipDistance;
    float _HashedAlphaDistance;

    float4 _DoubleSidedConstants;

    float _Specular;
    float _Emissive;
    float _Thickness;
    int _DiffusionProfileID;
    float _SubsurfaceMask;
    float _AlphaClip;
    float _AO_Intensity;

#ifdef USE_VEGETATION_ANIM
    //procedure
    float _WindFakeSingleObjectPivot;

    //SinglePivot//procedure//Hierarchy
    float _WindElasticityLvlB;
    float _WindRangeLvlB;
    float _WindFlutterElasticity;
    float _WindFlutterPhase;
    float _WindFlutterScale;
    float _WindFlutterPeriodScale;

    //Hierarchy
    float _WindElasticityLvl0;
    float _WindElasticityLvl1;
    float _WindRangeLvl0;
    float _WindRangeLvl1;
#endif
    //float _GrassOcclusionAmountTerrain;

    float _TextureSampleBias;
    float _ReflectionProbeIntensity;
    float _LightSpecIntensity;
    float _VLMReflectionProbeIntensity;
    float _VLMReflectionProbeAOIntensity;

    float _DissolvedBase;
    float _DisSoftness;
    float3 _emissiveColor;

}

//new lit
Buffer<float4> _ShapeParamsAndMaxScatterDists;
Buffer<float4> _WorldScalesAndFilterRadiiAndThicknessRemaps;

Texture2D<float4> _BaseMap : register(space1);
Texture2D<float4> _NormalMap : register(space1);
Texture2D<float4> _MaskMap : register(space1);
Texture2D<float4> _ThicknessMap : register(space1);
Texture2D<float4> _EnvBRDFLutMap : register(space1);
Texture2D<float4> _DissoveGradiant : register(space1);

#include "../../ShaderLibrary/VertexVegetation.hlsl"
#include "../../Material/Material.hlsl"
#include "../../Lighting/Lighting.hlsl"

#include "LitCommonStruct.hlsl"
#include "../MaterialUtilities.hlsl"
#include "../NormalBuffer.hlsl"

#define AlphaClipType_Standrad 0
#define AlphaClipType_Adjuct 1
#define AlphaClipType_Hashed 2

#ifndef DEFERRED_SHADING
SurfaceData GetSurfaceData(float3 V, PSInput psInput, bool isFrontFace)
{
    SurfaceData surfaceData = (SurfaceData)0;

    // BaseColor Alpha
    float4 color = _BaseMap.SampleBias(texture_sampler, psInput.uv, _TextureSampleBias);
    float4 dssvgradiant = _DissoveGradiant.SampleBias(texture_sampler, psInput.uv2, _TextureSampleBias);
    surfaceData.baseColor = _BaseColor.xyz * color.xyz;
    float tempalpha = _BaseColor.w * color.w;
    //float emissiveStrength = _Emissive * clamp(0.1 , 0 , (max(0, (dssvgradiant.x + _DisSoftness - _DissolvedBase ))- max(0, ( dssvgradiant.x  - _DissolvedBase ))) ) * 10.0f;
    float emissiveStrength = _Emissive * (clamp(0.2,0 , (dssvgradiant.x + _DisSoftness - _DissolvedBase )) - clamp(0.2,0, ( dssvgradiant.x  - _DissolvedBase - _DisSoftness )))  * 5.0f;
    tempalpha = tempalpha * max(0, dssvgradiant.x - _DissolvedBase);


    surfaceData.alpha = tempalpha;

#ifdef USE_VERTEX_COLOR
    surfaceData.baseColor *= psInput.color.rgb;
#endif

    // AlphaClip
    if (ALPHA_CLIPPING)
    {
        // AlphaClip Function
        float alphaclip = 0;
        if (ALPHA_CLIPPING_TYPE == AlphaClipType_Adjuct)
        {
            alphaclip = _AlphaClip - clamp((abs(length(ce_CameraPos.xyz - psInput.positionWS.xyz)) - _MinAdjustClipDistance) / _MaxAdjustClipDistance, 0, _AlphaClip);
        }
        else if (ALPHA_CLIPPING_TYPE == AlphaClipType_Hashed)
        {
            if (abs(length(ce_CameraPos.xyz - psInput.positionWS.xyz)) - _HashedAlphaDistance > 0)	/* hashed alpha */
                alphaclip = clamp(hashedAlpha(float3(psInput.uv, 1.0)), _AlphaClip, 1);
        }
        else // AlphaClipType_Standrad
        {
            alphaclip = _AlphaClip;
        }

        if (surfaceData.alpha < alphaclip)
            discard;
    }

#ifndef SHADOW_PASS
    // Normal
    surfaceData.normalTS = UnpackNormalUE(_NormalMap.SampleBias(texture_sampler, psInput.uv, _TextureSampleBias), _NormalScale);

    // Roughness Metallic
    float4 mask = _MaskMap.SampleBias(texture_sampler, psInput.uv, _TextureSampleBias);

    surfaceData.metallic = mask.r * _Metallic;

    float ambientOcclusion = mask.g;
    surfaceData.ambientOcclusion = ambientOcclusion * _MaskMap.SampleBias(texture_sampler, psInput.uv2, _TextureSampleBias).b;

    float smoothness = mask.a * _Smoothness;
    smoothness = lerp(_SmoothnessRemapMin, _SmoothnessRemapMax, smoothness);
    surfaceData.roughness = 1 - smoothness;

    // Emissive

    surfaceData.emissive = emissiveStrength;


    // MaterialType
    surfaceData.materialType = MATERIAL_TYPE;

    if (MATERIAL_TYPE == MaterialType_Transmission)
    {
        // Thickness
        surfaceData.thickness = _Thickness * _ThicknessMap.SampleBias(texture_sampler, psInput.uv, _TextureSampleBias).x;

        // DiffusionProfileID
        surfaceData.diffusionProfileID = _DiffusionProfileID;
    }

#endif // SHADOW_PASS 
    return surfaceData;
}
#endif

#include "../../Material/Lit/Lit.hlsl"
#include "../../Lighting/LightLoop/LightLoop.hlsl"

#include "../../Material/MaterialUtilities.hlsl"
#include "../../ShaderLibrary/NormalSurfaceGradient.hlsl"

VSOutput VSMain(VSInput input)
{
    return VSInputToVSOutput(input);
}

struct PSOutput
{
    float3 diffuse : SV_Target0;
    float4 specularRoughness : SV_Target1;
    float2 sss : SV_Target2;
};

PSOutput PSMain(VSOutput vsoutput, bool isFrontFace : SV_IsFrontFace)
{
    PSOutput output = (PSOutput)0;
    PSInput input = VSOutputToPSInput(vsoutput);

    PositionInputs posInput = GetPositionInput(input.positionWS, input.screenUV);

    float3 V = GetWorldSpaceNormalizeViewDir(posInput.positionWS);

    SurfaceData surfaceData;
    BuiltinData builtinData;
    GetSurfaceAndBuiltinData(V, input, isFrontFace, surfaceData, builtinData);

    BSDFData bsdfData = ConvertSurfaceDataToBSDFData(surfaceData);

#if defined(LIGHT_MAP_ENABLE) && LIGHT_MAP_ENABLE == 1
    bsdfData.lm_uv = input.uv1;
#else
    bsdfData.lm_uv = 0;
#endif

    LightLoopOutput lightLoopOutput = LightLoop(V, posInput, bsdfData, builtinData, input);

    output.diffuse = lightLoopOutput.diffuseLighting;

    output.specularRoughness.rgb = lightLoopOutput.specularLighting * _LightSpecIntensity;

    output.sss = float2(0, 0);

    //half skyOcclusion = SampleOcclusionProbes(input.positionWS);

    half3 bakedDiffuse = SampleBakedGI(input.positionWS, surfaceData.normalWS, bsdfData.lm_uv, 1, 1, 1);

    if (MATERIAL_TYPE == MaterialType_Transmission)
    {
        bakedDiffuse += SampleBakedGI(input.positionWS, -surfaceData.normalWS, bsdfData.lm_uv, 1, 1, 1) * bsdfData.transmittance;
    }
    float2 suv = vsoutput.positionNDC.xy * ce_ScreenParams.zw;

    float indirectDiffuseOcclusion = ao_texture.Sample(texture_sampler, suv).r;
    surfaceData.ambientOcclusion = indirectDiffuseOcclusion;
    saturate(surfaceData.ambientOcclusion);
    //half indirectDiffuseOcclusion = ao_texture.Sample(texture_sampler, posInput.uv).r;

    if (DISABLE_LM_GI) {
        bakedDiffuse = half3(0, 0, 0);
    }
    output.diffuse += surfaceData.baseColor * bakedDiffuse;

    output.diffuse *= (1.0 - _AO_Intensity * (1.0 - surfaceData.ambientOcclusion));

#ifdef LOD_VISUALIZER
    output.diffuse *= _LODColorMask.xyz;
#endif

    float3 viewDirW = normalize(ce_CameraPos.xyz - posInput.positionWS.xyz);
    float3 refleDirW = normalize(reflect(-viewDirW, surfaceData.normalWS));
    float2 envBRDFLutColor = _EnvBRDFLutMap.SampleLevel(texture_sampler, float2(saturate(dot(surfaceData.normalWS, viewDirW)), (1.0 - surfaceData.roughness)), 0).rg;
    float3 envBRDF = bsdfData.fresnel0 * envBRDFLutColor.r + envBRDFLutColor.g;
    half3 rpColor = half3(0, 0, 0);
    for (uint i = 0; i < ce_ReflectionProbeCount; i++)
    {
        if (ce_ReflectionProbeIndex[i] == 0)
        {
            float distance = length(posInput.positionWS.xyz - ce_ReflectionProbePosDistance[0].xyz);
            float maxD = max(ce_RPExtentMipmapCount[0].x, max(ce_RPExtentMipmapCount[0].y, ce_RPExtentMipmapCount[0].z));
            float blendWeight = distance < ce_ReflectionProbePosDistance[0].w ? 1.0 : clamp((maxD - distance) / (maxD - ce_ReflectionProbePosDistance[0].w), 0.0, 1.0);
            rpColor += ce_ReflectionProbeMap1.SampleLevel(texture_sampler, refleDirW, (ce_RPExtentMipmapCount[0].w - 1) * surfaceData.roughness).rgb * ce_ReflectionProbeWeight[i] * blendWeight;
        }
        else if (ce_ReflectionProbeIndex[i] == 1)
        {
            float distance = length(posInput.positionWS.xyz - ce_ReflectionProbePosDistance[1].xyz);
            float maxD = max(ce_RPExtentMipmapCount[1].x, max(ce_RPExtentMipmapCount[1].y, ce_RPExtentMipmapCount[1].z));
            float blendWeight = distance < ce_ReflectionProbePosDistance[1].w ? 1.0 : clamp((maxD - distance) / (maxD - ce_ReflectionProbePosDistance[1].w), 0.0, 1.0);
            rpColor += ce_ReflectionProbeMap2.SampleLevel(texture_sampler, refleDirW, (ce_RPExtentMipmapCount[1].w - 1) * surfaceData.roughness).rgb * ce_ReflectionProbeWeight[i] * blendWeight;
        }
        else if (ce_ReflectionProbeIndex[i] == 2)
        {
            float distance = length(posInput.positionWS.xyz - ce_ReflectionProbePosDistance[2].xyz);
            float maxD = max(ce_RPExtentMipmapCount[2].x, max(ce_RPExtentMipmapCount[2].y, ce_RPExtentMipmapCount[2].z));
            float blendWeight = distance < ce_ReflectionProbePosDistance[2].w ? 1.0 : clamp((maxD - distance) / (maxD - ce_ReflectionProbePosDistance[2].w), 0.0, 1.0);
            rpColor += ce_ReflectionProbeMap3.SampleLevel(texture_sampler, refleDirW, (ce_RPExtentMipmapCount[2].w - 1) * surfaceData.roughness).rgb * ce_ReflectionProbeWeight[i] * blendWeight;
        }
        else if (ce_ReflectionProbeIndex[i] == 3)
        {
            float distance = length(posInput.positionWS.xyz - ce_ReflectionProbePosDistance[3].xyz);
            float maxD = max(ce_RPExtentMipmapCount[3].x, max(ce_RPExtentMipmapCount[3].y, ce_RPExtentMipmapCount[3].z));
            float blendWeight = distance < ce_ReflectionProbePosDistance[3].w ? 1.0 : clamp((maxD - distance) / (maxD - ce_ReflectionProbePosDistance[3].w), 0.0, 1.0);
            rpColor += ce_ReflectionProbeMap4.SampleLevel(texture_sampler, refleDirW, (ce_RPExtentMipmapCount[3].w - 1) * surfaceData.roughness).rgb * ce_ReflectionProbeWeight[i] * blendWeight;
        }
    }

    float vlmAO = SampleBakedSkyVisibility(input.positionWS, refleDirW);
    rpColor += (SampleBakedSpecGI(input.positionWS, refleDirW, _VLMReflectionProbeAOIntensity) * _VLMReflectionProbeIntensity);
    rpColor *= lerp(1.0f, vlmAO, _VLMReflectionProbeAOIntensity);

 
    output.specularRoughness.rgb += vlmAO * _ReflectionProbeIntensity * rpColor * envBRDF;
    output.specularRoughness.a = surfaceData.roughness;
    output.diffuse += _emissiveColor * surfaceData.emissiveColor;

    const int Debug = 0;

    if (Debug == 1)
    {
        float alpha;
        int shadowSplitIndex = EvalShadow_GetSplitIndex(input.positionWS, alpha);
        float3 cascadeColor;
        switch (shadowSplitIndex)
        {
        case -1:
            cascadeColor = float3(1, 0, 0);
            break;
        case 0:
            cascadeColor = float3(1, 0.5, 0);
            break;
        case 1:
            cascadeColor = float3(1, 1, 0);
            break;
        case 2:
            cascadeColor = float3(0.5, 1, 0);
            break;
        case 3:
            cascadeColor = float3(0, 1, 0);
            break;
        case 4:
            cascadeColor = float3(0, 1, 0.5);
            break;
        default:
            cascadeColor = float3(0, 1, 1);
            break;
        }
        output.diffuse = lerp(output.diffuse, cascadeColor, 0.9);
    }

    if (DEBUG_LM_GI_ONLY)
    {
        if (DEBUG_LM_COLOR_ONLY)
        {
            output.diffuse = bakedDiffuse;
        }
        else
        {
            output.diffuse = surfaceData.baseColor * bakedDiffuse;
        }
        output.specularRoughness = float4(0, 0, 0, 0);
        output.sss = float2(0, 0);
        return output;
    }
    else if (SHOW_DEBUG_LM_UV)
    {
        output.diffuse = float3(bsdfData.lm_uv, 0);
        output.specularRoughness = float4(0, 0, 0, 0);
        output.sss = float2(0, 0);
        return output;
    }
    else if (SHOW_DEBUG_NORMAL)
    {
        output.diffuse = surfaceData.normalWS * 0.5f + 0.5f;
        output.specularRoughness = float4(0, 0, 0, 0);
        output.sss = float2(0, 0);
        return output;
    }

    return output;
}