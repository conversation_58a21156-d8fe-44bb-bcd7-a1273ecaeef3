#include "../../ShaderLibrary/Packing.hlsl"

float3 ADD_IDX(GetNormalTS)(LayerTexCoord layerTexCoord)
{
    float3 normalTS;
    //UnpackNormalRGB,_NormalScale
    normalTS = UnpackNormalmapRGorAG(ADD_IDX(_NormalMap).SampleBias(texture_sampler, ADD_IDX(layerTexCoord.baseN), _ColorNormalTexSampleBias.y), ADD_IDX(_NormalScale));
    return normalTS;
}

//float3 ADD_IDX(GetBentNormalTS)(LayerTexCoord layerTexCoord)
//{
//    float3 bentNormalTS;
//    bentNormalTS = UnpackNormalRGB(ADD_IDX(_BentNormalMap).Sample(texture_sampler, ADD_IDX(layerTexCoord.base)), ADD_IDX(_NormalScale));
//
//    return bentNormalTS;
//}

// Return opacity
//float ADD_IDX(GetSurfaceData)(LayerTexCoord layerTexCoord, out SurfaceData surfaceData, out float3 normalTS, out float3 bentNormalTS)
float ADD_IDX(GetSurfaceData)(LayerTexCoord layerTexCoord, out SurfaceData surfaceData, out float3 normalTS)
{
    surfaceData = (SurfaceData)0;

    surfaceData.baseColor = ADD_IDX(_BaseColorMap).SampleBias(texture_sampler, ADD_IDX(layerTexCoord.base), _ColorNormalTexSampleBias.x).rgb * ADD_IDX(_BaseColor).rgb;
    ////float3 distanceTilingDecayColor = ADD_IDX(_BaseColorMap).Sample(texture_sampler, ADD_IDX(layerTexCoord.base)* layerTexCoord.distanceTilingDecay).rgb * ADD_IDX(_BaseColor).rgb;
    //float smallMacro = 3.0*ADD_IDX(_BaseColorMap).SampleBias(texture_sampler, ADD_IDX(layerTexCoord.base) *0.2, _ColorNormalTexSampleBias.x).r;
    //float mediumMacro = 3.0 * ADD_IDX(_BaseColorMap).SampleBias(texture_sampler, ADD_IDX(layerTexCoord.base) * 0.02, _ColorNormalTexSampleBias.x).r;
    //float largeMacro = 3.0 * ADD_IDX(_BaseColorMap).SampleBias(texture_sampler, ADD_IDX(layerTexCoord.base) * 0.002, _ColorNormalTexSampleBias.x).r;
    //float macroTiling = lerp(0.5, 1.0, (smallMacro + 0.5) * (mediumMacro + 0.5) * (largeMacro + 0.5));
    ////surfaceData.baseColor = lerp(surfaceData.baseColor, distanceTilingDecayColor, pow((1.0 - layerTexCoord.distanceTilingDecay) / 0.6,2.0));
    ////surfaceData.baseColor *= macroTiling;

    float alpha = ADD_IDX(_BaseColorMap).SampleBias(texture_sampler, ADD_IDX(layerTexCoord.base), _ColorNormalTexSampleBias.x).a * ADD_IDX(_BaseColor).a;

    normalTS = ADD_IDX(GetNormalTS)(layerTexCoord);

    surfaceData.metallic = ADD_IDX(_Metallic);

    surfaceData.ambientOcclusion = 1.0;// maskColor.g;

    surfaceData.roughness = ADD_IDX(_Roughness);

    surfaceData.thickness = 1.0;
    surfaceData.diffusionProfileID = 0;

    // surfaceData.subsurfaceMask = 0.0;

    surfaceData.materialType = 0;

    return alpha;
}
