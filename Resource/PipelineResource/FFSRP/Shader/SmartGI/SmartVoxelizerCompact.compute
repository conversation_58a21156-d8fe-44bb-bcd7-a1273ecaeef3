#pragma compute SetupVoxelizeIndirectArgsCS
#pragma compute ClearVoxelizeVisBufferCS
#pragma compute CompactVoxelizeSceneCS
#pragma compute CompactOpacityVoxelCS

//#pragma enable debug_symbol
#define USE_CLIPMAP_WRAP 1
#include "SmartVoxelizeCommon.hlsl"

#ifndef THREADGROUP_SIZE
#define THREADGROUP_SIZE 8
#endif

StructuredBuffer<uint> VoxelVisBufferAllocator;
RWStructuredBuffer<uint> RWVoxelizeIndirectArgs;
[numthreads(1, 1, 1)]
void SetupVoxelizeIndirectArgsCS(
	uint3 GroupId : SV_GroupID,
	uint3 DispatchThreadId : SV_DispatchThreadID,
	uint3 GroupThreadId : SV_GroupThreadID)
{
    RWVoxelizeIndirectArgs[0] = (VoxelVisBufferAllocator[ClipmapIndex] + THREADGROUP_SIZE - 1) / THREADGROUP_SIZE;
    RWVoxelizeIndirectArgs[1] = 1;
    RWVoxelizeIndirectArgs[2] = 1; 

    RWVoxelizeIndirectArgs[3] = VoxelVisBufferAllocator[ClipmapIndex];
    RWVoxelizeIndirectArgs[4] = 1;
    RWVoxelizeIndirectArgs[5] = 1;
}

RWStructuredBuffer<uint> RWVoxelVisBufferAllocator;
[numthreads(1, 1, 1)]
void ClearVoxelizeVisBufferCS(
	uint3 GroupId : SV_GroupID,
	uint3 DispatchThreadId : SV_DispatchThreadID,
	uint3 GroupThreadId : SV_GroupThreadID)
{
    RWVoxelVisBufferAllocator[ClipmapIndex] = 0;
}

Texture3D<uint> AlbedoTexture;
RWTexture3D<float4> RWLightingTexture;
RWStructuredBuffer<uint> RWVoxelVisBuffer;
[numthreads(THREADGROUP_SIZE, THREADGROUP_SIZE, THREADGROUP_SIZE)]
void CompactVoxelizeSceneCS(
	uint3 GroupId : SV_GroupID,
	uint3 DispatchThreadId : SV_DispatchThreadID,
	uint3 GroupThreadId : SV_GroupThreadID)
{
    if(all(DispatchThreadId < ClipmapResolution))
    {
        uint3 TexelCoord = DispatchThreadId + uint3(0, ClipmapIndex * ClipmapResolution.y, 0);

        // Only Clear Lighting when voxel is invalid
        float4 DiffuseColor = UnpackRGBA8Color(AlbedoTexture.Load(int4(TexelCoord, 0)));
        if(DiffuseColor.a > 0.0f)
        {
            int BufferIndex;
			InterlockedAdd(RWVoxelVisBufferAllocator[ClipmapIndex], 1, BufferIndex);
            RWVoxelVisBuffer[BufferIndex] = PackVoxelInfo(DispatchThreadId);  // Coord with direction, range from (0, 0, 0) to (63, 63, 63 * 6)
        }
		else
		{
			RWLightingTexture[TexelCoord] = float4(0, 0, 0, 0);
		}
    }
}

//Texture3D<float> OpacityTexture;
RWTexture3D<uint> RWOpacityCompactTexture;
[numthreads(8, 8, 8)]
void CompactOpacityVoxelCS(
    uint3 GroupId : SV_GroupID,
	uint3 DispatchThreadId : SV_DispatchThreadID,
	uint3 GroupThreadId : SV_GroupThreadID)
{
    if(all(DispatchThreadId * 2 < ClipmapResolution))
    {
        uint3 CompactTexelLocalCoord = DispatchThreadId;
        uint OpacityCompactValue = 0;
        UNROLL
        for(uint z = 0; z < 2; z++)
        {
            UNROLL
            for(uint y = 0; y < 2; y++)
            {
                UNROLL
                for(uint x = 0; x < 2; x++)
                {
                    uint CompactValBitIndex = z * 4 + y * 2 + x;
                    uint3 VoxelCoord = CompactTexelLocalCoord * 2 + uint3(x, y, z);
                    //uint3 TexCoord = GetClipmapCoordinate(ClipmapIndex, VoxelCoord);
                    float Opacity = 0;
                    for(uint FaceDirection = 0; FaceDirection < 6; FaceDirection++)
                    {
                        uint3 TexCoord = GetClipmapCoordinate(ClipmapIndex, FaceDirection, VoxelCoord);
                        float4 DiffuseColor = UnpackRGBA8Color(AlbedoTexture.Load(int4(TexCoord, 0)));
                        Opacity = max(Opacity, DiffuseColor.w);
                        // if(Opacity >= MIN_DEPTH_OPACITY)
                        // {
                        //     break;
                        // }
                    }
                    if(Opacity < MIN_DEPTH_OPACITY)
                    {
                        OpacityCompactValue &= (~(1U << CompactValBitIndex));            
                    }
                    else
                    {
                        OpacityCompactValue |= (1U << CompactValBitIndex);
                    }
                }
            }
        }
        uint3 CompactTexelCoord = CompactTexelLocalCoord + uint3(0, ClipmapIndex*ClipmapResolution.y/2, 0);
        RWOpacityCompactTexture[CompactTexelCoord] = OpacityCompactValue & 0xFF;
    }
}