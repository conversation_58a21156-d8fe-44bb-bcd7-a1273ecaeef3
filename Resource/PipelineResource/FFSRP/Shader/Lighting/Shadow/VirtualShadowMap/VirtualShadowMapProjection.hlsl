#ifndef VIRTUAL_SHADOW_MAP_PROJECTION_HLSL
#define VIRTUAL_SHADOW_MAP_PROJECTION_HLSL

#include "VirtualShadowMapProjectionCommon.hlsl"
#include "VirtualShadowMapTransmissionCommon.hlsl"

StructuredBuffer<uint> _PageTable;
RWTexture2D<uint> _PhysicalPagePool;
RWTexture2D<uint> _PhysicalPagePool_AssetGUID;

#include "VirtualShadowMapProjectionDirectional.hlsl"

struct VirtualShadowMapClipmapRelativeTransform
{
	float scale;
	float3 bias;
};

VirtualShadowMapClipmapRelativeTransform CalcClipmapRelativeTransform(int clipmapId, int levelOffset)
{
	const VirtualShadowMapProjectionData projectionDataA = GetVirtualShadowMapProjectionData(clipmapId);
	const VirtualShadowMapProjectionData projectionDataB = GetVirtualShadowMapProjectionData(clipmapId + levelOffset);

	float2 offsetA = float2(projectionDataA.clipmapCornerOffset);
	float2 offsetB = float2(projectionDataB.clipmapCornerOffset);

	VirtualShadowMapClipmapRelativeTransform result;
	result.scale = levelOffset >= 0 ? rcp(float(1u << levelOffset)) : float(1u << (-levelOffset)); // "1u << (-levelOffset)" ???
	result.bias.xy = 0.25f * (offsetB - result.scale * offsetA);

	// NOTE: relative Z bias can change when caching is enabled due to cached levels pinning the depth range
	float offsetZA = projectionDataA.shadowViewToClipMatrix[2][3];
	float offsetZB = projectionDataB.shadowViewToClipMatrix[2][3];
	result.bias.z = offsetZB - result.scale * offsetZA;

	return result;
}

struct VirtualShadowMapSample
{
    bool isValid;
    float depth;
    uint assetGUID;
};

uint2 CalcClipmapOffsetLevelPage(uint2 basePage, int clipmapId, uint levelOffset)
{
	const VirtualShadowMapProjectionData projectionDataA = GetVirtualShadowMapProjectionData(clipmapId);
	const VirtualShadowMapProjectionData projectionDataB = GetVirtualShadowMapProjectionData(clipmapId + levelOffset);

	const int offsetScale = (VSM_LEVEL0_DIM_PAGES_XY >> 2);
	int2 basePageOffset  = offsetScale * projectionDataA.clipmapCornerOffset;
	int2 levelPageOffset = offsetScale * projectionDataB.clipmapCornerOffset;
	return (basePage - basePageOffset + (levelPageOffset << levelOffset)) >> levelOffset;
}

VirtualShadowMapSample SampleVirtualShadowMapClipmap(int virtualShadowMapId, float2 shadowMapUV)
{
    VirtualShadowMapSample result = (VirtualShadowMapSample)0;
    result.isValid = false;

    uint2 vAddress = shadowMapUV * VSM_VIRTUAL_MAX_RESOLUTION_XY;
    const uint2 basePage = vAddress >> VSM_LOG2_PAGE_SIZE;
    ShadowPhysicalPage page = ShadowGetPhysicalPage(_PageTable, CalcPageOffset(virtualShadowMapId, 0, basePage));

    if (page.isAnyLODValid)
    {
        uint clipmapLevelOffset = page.lodOffset;
        float depthLevelScale = 1.0f;
		float depthLevelBias = 0.0f;

        if (clipmapLevelOffset > 0)
        {
            int clipmapLevelId = virtualShadowMapId + clipmapLevelOffset;
            uint2 vPage = CalcClipmapOffsetLevelPage(basePage, virtualShadowMapId, clipmapLevelOffset);
            page = ShadowGetPhysicalPage(_PageTable, CalcPageOffset(clipmapLevelId, 0, vPage));

            VirtualShadowMapClipmapRelativeTransform transform = CalcClipmapRelativeTransform(virtualShadowMapId, clipmapLevelOffset);
            float2 clampUV = shadowMapUV * transform.scale + transform.bias.xy;
            depthLevelScale = transform.scale;
			depthLevelBias = transform.bias.z;
            vAddress = clampUV * VSM_VIRTUAL_MAX_RESOLUTION_XY;
        }

        uint2 pAddress = page.physicalAddress * VSM_PAGE_SIZE + (vAddress & VSM_PAGE_SIZE_MASK);
        float sampleDepth = (asfloat(_PhysicalPagePool[pAddress]) - depthLevelBias) / depthLevelScale;

        result.isValid = true;
        result.depth = sampleDepth;
    }

    return result;
}
bool SampleVirtualShadowMapClipmap(int virtualShadowMapId, float2 shadowMapUV, out float depth)
{
    VirtualShadowMapSample sample = SampleVirtualShadowMapClipmap(virtualShadowMapId, shadowMapUV);
    depth = sample.depth;
    return sample.isValid;
}
VirtualShadowMapSampleResult SampleVirtualShadowMap(int virtualShadowMapId, float3 positionWS, float rayStartDistance, float3 estimatedGeoWorldNormal)
{
    VirtualShadowMapProjectionData projectionData = GetVirtualShadowMapProjectionData(virtualShadowMapId);

    if (projectionData.lightType == LIGHT_TYPE_DIRECTIONAL)
    {
        const int firstClipmapLevel = projectionData.clipmapLevel;
        const int clipmapLevel = CalcClipmapLevel(projectionData, positionWS);
        int clipmapIndex = max(0, clipmapLevel - firstClipmapLevel);

        if (clipmapIndex < projectionData.clipmapLevelCount)
        {
            int clipmapLevelVirtualShadowMapId = virtualShadowMapId + clipmapIndex;
            VirtualShadowMapProjectionData projectionData = GetVirtualShadowMapProjectionData(clipmapLevelVirtualShadowMapId);

            float4 shadowNDC = mul(projectionData.worldToShadowMatrix, float4(positionWS, 1.0));
            const float2 shadowMapUV = float2(shadowNDC.x * 0.5 + 0.5, -shadowNDC.y * 0.5 + 0.5);
            VirtualShadowMapSample smSample = SampleVirtualShadowMapClipmap(clipmapLevelVirtualShadowMapId, shadowMapUV);

            if (smSample.isValid)
            {
                float sampleDepth = smSample.depth;
                float sceneDepth = shadowNDC.z;

                float rayStartBias = -rayStartDistance * projectionData.shadowViewToClipMatrix[2][2];
                float biasDepth = sampleDepth - rayStartBias;

                VirtualShadowMapSampleResult result;
                result.occluderDistance = -1.0;

                if (sceneDepth > biasDepth)
                {
                    result.shadowFactor = 1.0;
                    result.assetGUID = 0xffffffff;
                }
                else
                {
                    result.shadowFactor = 0.0;
                    result.assetGUID = smSample.assetGUID;
                    result.occluderDistance = ComputeOccluderDistanceOrtho(projectionData.shadowViewToClipMatrix, sampleDepth, sceneDepth);
                }

                return result;
            }
        }
    }

    VirtualShadowMapSampleResult result;
    result.shadowFactor = 1.0;
    result.occluderDistance = -1.0;
    return result;
}

// for VolumetricFog and Atmosphere
float SampleVirtualShadowMap(int virtualShadowMapId, float3 positionWS)
{
    VirtualShadowMapSampleResult result = SampleVirtualShadowMap(virtualShadowMapId, positionWS, 0, float3(0, 0, 0));
    return result.shadowFactor;
}

#ifdef ENABLE_CONTACT_SHADOW
#define CONTACT_SHADOW_SAMPLES 8

float ShadowRayCast(float3 rayOrigin, float3 rayDirection, float rayLength, int numSteps, float stepOffset, float toleranceScale, bool isOnlyTraceFoliage, out bool isHitCastContactShadow)
{
    float4 rayStartClip = mul(ce_Projection, mul(ce_View, float4(rayOrigin, 1.0)));
    float4 rayDirClip = mul(ce_Projection, mul(ce_View, float4(rayDirection * rayLength, 0.0)));
    float4 rayEndClip = rayStartClip + rayDirClip;

    float3 rayStartScreen = rayStartClip.xyz / rayStartClip.w;
    float3 rayEndScreen = rayEndClip.xyz / rayEndClip.w;

    float3 rayStepScreen = rayEndScreen - rayStartScreen;

    float3 rayStartUVz = float3(rayStartScreen.x * 0.5 + 0.5, -rayStartScreen.y * 0.5 + 0.5, rayStartScreen.z);
    float3 rayStepUVz = float3(rayStepScreen.x * 0.5, -rayStepScreen.y * 0.5, rayStepScreen.z);

    float4 rayDepthClip = rayStartClip + mul(ce_Projection, float4(0, 0, rayLength, 0));
    float3 rayDepthScreen = rayDepthClip.xyz / rayDepthClip.w;

    const float step = 1.0 / numSteps;

    const float compareTolerance = abs(rayDepthScreen.z - rayStartScreen.z) * step * toleranceScale;

    float sampleTime = stepOffset * step + step;

    float firstHitTime = -1.0;

    const float startDepth = _DepthMap.SampleLevel(ce_Sampler_Point, rayStartUVz.xy, 0.0).x;

    for (int i = 0; i < numSteps; i++)
    {
        float3 sampleUVz = rayStartUVz + rayStepUVz * sampleTime;

        if (isOnlyTraceFoliage && !IsCurrPixelFoliage(sampleUVz.xy))
        {
            sampleTime += step;
            continue;
        }

        float sampleDepth = _DepthMap.SampleLevel(ce_Sampler_Point, sampleUVz.xy, 0.0).x;

        float depthDiff = sampleUVz.z - sampleDepth;
        bool hit = abs(depthDiff + compareTolerance) < compareTolerance;
        hit = hit && abs(sampleDepth - startDepth) > 0.000001;

        firstHitTime = (hit && firstHitTime < 0.0) ? sampleTime : firstHitTime;

        sampleTime += step;
    }

    float hitDistance = -1.0;
    isHitCastContactShadow = false;

    if (firstHitTime > 0.0)
    {
        isHitCastContactShadow = true;

        float3 sampleUVz = rayStartUVz + rayStepUVz * firstHitTime;
        bool isValidUV = all(and(0.0 < sampleUVz.xy, sampleUVz.xy < 1.0));
        hitDistance = isValidUV ? (firstHitTime * rayLength) : -1.0;
    }

    return hitDistance;
}

float VirtualShadowMapScreenRayCast(float3 rayOrigin, float3 rayDirection, float rayLength, int numSteps, float dither, float toleranceScale, bool isOnlyTraceFoliage)
{
    float stepOffset = dither - 0.5;
    bool isHitCastContactShadow = false;
    return ShadowRayCast(rayOrigin, rayDirection, rayLength, numSteps, stepOffset, toleranceScale, isOnlyTraceFoliage, isHitCastContactShadow);
}

// for render ShadowMask
VirtualShadowMapSampleResult ProjectLight(int virtualShadowMapId, uint2 positionSS, float3 positionWS, float3 normalWS, 
                            float3 lightDirection, float lightSourceAngle, float contactShadowLengthWorld, float contactShadowLengthScale, float noise)
{
    const float SMRTRayLengthScale = 1.5;
    const float SMRTTexelDitherScale = 2.0;
    const float NormalBias = 0.5 / 1000.0;

    const float distanceToCamera = length(positionWS - ce_CameraPos.xyz);
    const float normalBiasLength = max(0.01, NormalBias * distanceToCamera * ce_InvProjection[0][0]);
    positionWS += normalWS * normalBiasLength;

    VirtualShadowMapSampleResult result = (VirtualShadowMapSampleResult)0;
    result.occluderDistance = -1.0;

    if (_SMRTRayCount > 0)
    {
        result = TraceDirectional(virtualShadowMapId, positionSS, positionWS, normalWS, lightDirection, lightSourceAngle, contactShadowLengthWorld, SMRTRayLengthScale, SMRTTexelDitherScale, noise);
    }
    else
    {
        result = SampleVirtualShadowMap(virtualShadowMapId, positionWS, contactShadowLengthWorld, normalWS);
    }

    float contactShadowHitDistance = -1.0;

    // if (contactShadowLengthWorld > 0.0)
    // {
    //     contactShadowHitDistance = VirtualShadowMapScreenRayCast(positionWS, -lightDirection, contactShadowLengthWorld, CONTACT_SHADOW_SAMPLES, noise, 1.0, false);
    //     if (contactShadowHitDistance > 0.0)
    //     {
    //         result.shadowFactor = 0.0;
    //     }
    // }

    if (ENABLE_FOLIAGEONLY_CONTACTSHADOW && contactShadowHitDistance < 0.0)
    {
        contactShadowHitDistance = VirtualShadowMapScreenRayCast(positionWS, -lightDirection, min(0.1 * contactShadowLengthScale, 3000), CONTACT_SHADOW_SAMPLES, noise, 2.0, true);
        if (contactShadowHitDistance > 0.0)
        {
            result.shadowFactor = 0.0;
            result.occluderDistance = contactShadowHitDistance;
        }
    }

    return result;
}
#endif

#endif