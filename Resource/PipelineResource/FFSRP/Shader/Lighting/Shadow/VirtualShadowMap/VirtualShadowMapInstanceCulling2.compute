#pragma compute CullPerPageDrawUnits
#pragma compute AllocateCommandInstanceOutputSpace
#pragma compute OutputCommandInstanceLists

#include "VirtualShadowMapViewCommon.hlsl"
#include "SceneData.hlsl"

#define NUM_THREADS_PER_GROUP 64
#define PERSISTENT_CULLING_GROUP_SIZE_X 64
#define PERSISTENT_CULLING_GROUP_SIZE_Y 8
#define PERSISTENT_CULLING_INSTANCE_COUNT_PER_GROUP PERSISTENT_CULLING_GROUP_SIZE_X * 32
#define INDIRECT_ARGS_NUM_WORDS 5

cbuffer cbPass
{
    uint4 _UIntParams0;
    uint4 _UIntParams1;
}

#define _FirstPrimaryView                   _UIntParams0.x
#define _PrimaryViewCount                   _UIntParams0.y
#define _CurrSceneFrameCount                _UIntParams0.z
#define _PayloadCount                       _UIntParams1.x
#define _VisibleObjectCommandBufferMaxNum   _UIntParams1.y
#define _IndirectArgCount                   _UIntParams1.z
#define _RangePayloadOffset                 _UIntParams1.w

struct VisibleObjectCommand
{
    uint packedPageInfo;
    uint objectIndex;
    uint indirectArgIndex;
};

StructuredBuffer<uint> _PageFlags;
StructuredBuffer<uint4> _PageRectBounds;
StructuredBuffer<VirtualShadowMapViewData> _VirtualShadowMapViewDatas;

StructuredBuffer<PrimitiveCullingData> _GPUScenePrimitiveCullingDatas;
StructuredBuffer<ObjectCullingData> _GPUSceneObjectCullingDatas;

RWStructuredBuffer<VisibleObjectCommand> _OutVisibleObjectCommands;
RWStructuredBuffer<uint> _OutVisibleObjectCommandCount;
RWStructuredBuffer<uint> _OutDrawIndirectArgs;

struct GroupObjectPayloadData
{
    int objectCullingGUID;
    int objectIndex;
    uint indirectArgIndex;
};

StructuredBuffer<ObjectPayloadData2> _ObjectPayloadDatas;
RWStructuredBuffer<uint> _ObjectPayloadIndexAndObjectOffsetAndMutex;

#define _ObjectPayloadIndex _ObjectPayloadIndexAndObjectOffsetAndMutex[0]
#define _ObjectPayloadObjectOffset _ObjectPayloadIndexAndObjectOffsetAndMutex[1]
#define _ObjectPayloadMutex _ObjectPayloadIndexAndObjectOffsetAndMutex[2]

groupshared uint _GroupObjectPayloadIndex;
groupshared uint _GroupObjectPayloadIndexOffset;
groupshared uint _GroupObjectPayloadIndexEnd;
groupshared uint _GroupObjectPayloadIndexOffsetEnd;

SamplerState ce_Sampler_Point;

#include "VirtualShadowMapPageOverlap.hlsl"

void LockAcquire()
{
    uint value = 1u;
    while (value)
    {
        InterlockedCompareExchange(_ObjectPayloadMutex, 0u, 1u, value);
    }
}

void LockRelease()
{
    uint value;
    InterlockedExchange(_ObjectPayloadMutex, 0, value);
}


void WriteCommand(uint mipViewId, uint objectIndex, uint indirectArgIndex)
{
    PageInfo pageInfo;
    pageInfo.viewId = mipViewId;
    pageInfo.isStaticPage = false;

    VisibleObjectCommand visibleObjectCommand;
    visibleObjectCommand.packedPageInfo = PackPageInfo(pageInfo);
    visibleObjectCommand.objectIndex = objectIndex;
    visibleObjectCommand.indirectArgIndex = indirectArgIndex;

    uint visibleObjectCommandOutputOffset = 0u;
    InterlockedAdd(_OutVisibleObjectCommandCount[0], 1u, visibleObjectCommandOutputOffset);
    if (visibleObjectCommandOutputOffset < _VisibleObjectCommandBufferMaxNum)
    {
        _OutVisibleObjectCommands[visibleObjectCommandOutputOffset] = visibleObjectCommand;
    }
}

struct ObjectMatrix
{
    matrix mat;
    float pad;
};

groupshared ObjectMatrix objectMatrix[PERSISTENT_CULLING_GROUP_SIZE_X];

void InstanceCulling(const GroupObjectPayloadData payload, const uint2 groupThreadID, bool predicate)
{
    int objectCullingGUID = payload.objectCullingGUID;
    int objectIndex = payload.objectIndex;

    predicate &= objectCullingGUID >= 0;
    PrimitiveCullingData primitiveCullingData;
    if (predicate)
    {
        ObjectCullingData objectCullingData = _GPUSceneObjectCullingDatas[objectCullingGUID];
        primitiveCullingData = _GPUScenePrimitiveCullingDatas[objectCullingData.primitiveCullingGUID];

        if (groupThreadID.y == 0)
        {
            objectMatrix[groupThreadID.x].mat = objectCullingData.worldMatrix;
        }
    }

    bool hasMoved = primitiveCullingData.lastUpdateSceneFrameCount == _CurrSceneFrameCount;
    GroupMemoryBarrierWithGroupSync();

    uint visibleViewCount = 0;

    for (uint primaryViewId = _FirstPrimaryView + groupThreadID.y; primaryViewId < _FirstPrimaryView + _PrimaryViewCount; primaryViewId += PERSISTENT_CULLING_GROUP_SIZE_Y)
    {
    #if 1
        VirtualShadowMapViewData viewData;

        if (predicate)
        {
        
            viewData = _VirtualShadowMapViewDatas[primaryViewId];
            uint virtualShadowMapId = viewData.virtualShadowMapId;
            matrix localToClip = CombineTranslationMatrix(objectMatrix[groupThreadID.x].mat, viewData.worldToShadowMatrix, primitiveCullingData.tilePosition, viewData.tilePosition);

            FrustumCullData cull = BoxCullFrustum(primitiveCullingData.localBoundsCenter, primitiveCullingData.localBoundsExtent, localToClip, false);
            if (cull.isVisible)
            {
                ScreenRect rect = GetScreenRect(viewData.viewRect, cull, 4);
                uint flagMask = VSM_ALLOCATED_FLAG;
                if (!hasMoved)
                {
                    flagMask |= VSM_STATIC_UNCACHED_FLAG | VSM_DYNAMIC_UNCACHED_FLAG;
                }

                if (OverlapsAnyValidPage(virtualShadowMapId, 0, rect, flagMask))
                {
                    uint4 rectPages = uint4(viewData.viewRect.xyxy + rect.pixels) >> VSM_LOG2_PAGE_SIZE;

                    uint4 allocatedBounds = _PageRectBounds[virtualShadowMapId * VSM_MAX_MIP_LEVELS];
                    rectPages.xy = max(rectPages.xy, allocatedBounds.xy);
                    rectPages.zw = min(rectPages.zw, allocatedBounds.zw);
                    if (all(rectPages.zw >= rectPages.xy))
                    {
                        ++visibleViewCount;
                        WriteCommand(primaryViewId, objectIndex, payload.indirectArgIndex);
                    }
                }            
            }
        }
    #else
        if (predicate)
        {
            ++visibleViewCount;
            WriteCommand(primaryViewId, objectIndex, payload.indirectArgIndex);
        }
    #endif


    }

    if (predicate && visibleViewCount > 0)
        InterlockedAdd(_OutDrawIndirectArgs[payload.indirectArgIndex * INDIRECT_ARGS_NUM_WORDS + 1], visibleViewCount);
}

[numthreads(PERSISTENT_CULLING_GROUP_SIZE_X, PERSISTENT_CULLING_GROUP_SIZE_Y, 1)]
void CullPerPageDrawUnits(uint dispatchThreadId : SV_DispatchThreadID, uint groupIndex : SV_GroupIndex, uint2 groupThreadID : SV_GroupThreadID)
{
    // Step0
    const uint threadCount = 32 * PERSISTENT_CULLING_GROUP_SIZE_X;
    uint payloadIndex = dispatchThreadId;
    while (payloadIndex - groupThreadID.x < _RangePayloadOffset)
    {
        bool predicate = payloadIndex < _RangePayloadOffset;
        ObjectPayloadData2 currentPayload;
        if (predicate)
            currentPayload = _ObjectPayloadDatas[payloadIndex];

        GroupObjectPayloadData groupPayload;
        groupPayload.objectCullingGUID = currentPayload.objectCullingGUID;
        groupPayload.objectIndex = currentPayload.objectIndex;
        groupPayload.indirectArgIndex = currentPayload.indirectArgIndex;
    
        InstanceCulling(groupPayload, groupThreadID, predicate);
    
        payloadIndex += threadCount;
    }

    // Step1
    while(true)
    {
        GroupMemoryBarrierWithGroupSync();

        if (groupIndex == 0)
        {
            LockAcquire();

            uint objectPayloadIndexStart = _ObjectPayloadIndex;
            uint objectPayloadObjectOffsetStart = _ObjectPayloadObjectOffset;
            uint objectPayloadIndex = objectPayloadIndexStart;
            uint objectPayloadObjectOffset = objectPayloadObjectOffsetStart;

            uint groupPayloadDataIndex = 0;
            while (groupPayloadDataIndex < PERSISTENT_CULLING_INSTANCE_COUNT_PER_GROUP)
            {
                if (objectPayloadIndex >= _PayloadCount)
                    break;

                ObjectPayloadData2 currentPayload = _ObjectPayloadDatas[objectPayloadIndex];

                if (objectPayloadObjectOffset < currentPayload.objectCount)
                {
                    uint count = min(PERSISTENT_CULLING_INSTANCE_COUNT_PER_GROUP - groupPayloadDataIndex, currentPayload.objectCount - objectPayloadObjectOffset);
                    groupPayloadDataIndex += count;
                    objectPayloadObjectOffset += count;
                }

                if (objectPayloadObjectOffset >= currentPayload.objectCount)
                {
                    objectPayloadIndex++;
                    objectPayloadObjectOffset = 0;
                }
            }

            uint originValue;
            InterlockedExchange(_ObjectPayloadIndex, objectPayloadIndex, originValue);
            InterlockedExchange(_ObjectPayloadObjectOffset, objectPayloadObjectOffset, originValue);

            LockRelease();

            _GroupObjectPayloadIndex = objectPayloadIndexStart;
            _GroupObjectPayloadIndexOffset = objectPayloadObjectOffsetStart;
            _GroupObjectPayloadIndexEnd = objectPayloadIndex;
            _GroupObjectPayloadIndexOffsetEnd = objectPayloadObjectOffset;
        }

        GroupMemoryBarrierWithGroupSync();

        if (_GroupObjectPayloadIndex >= _PayloadCount)
            break;

        while (true)
        {            
            GroupMemoryBarrierWithGroupSync();

            uint payloadIndex = _GroupObjectPayloadIndex;
            if (payloadIndex > _GroupObjectPayloadIndexEnd || payloadIndex == _GroupObjectPayloadIndexEnd && _GroupObjectPayloadIndexOffset >= _GroupObjectPayloadIndexOffsetEnd)
                break;

            uint payloadObjectOffset = groupThreadID.x + _GroupObjectPayloadIndexOffset;

            GroupMemoryBarrierWithGroupSync();


            ObjectPayloadData2 currentPayload = _ObjectPayloadDatas[payloadIndex];
            {
                bool predicate = payloadObjectOffset < currentPayload.objectCount;
                GroupObjectPayloadData groupPayload;
                groupPayload.objectCullingGUID = currentPayload.objectCullingGUID + payloadObjectOffset;
                groupPayload.objectIndex = currentPayload.objectIndex + payloadObjectOffset;
                groupPayload.indirectArgIndex = currentPayload.indirectArgIndex;
            
                InstanceCulling(groupPayload, groupThreadID, predicate);
            }


            if (groupIndex == 0)
            {
                _GroupObjectPayloadIndexOffset += PERSISTENT_CULLING_GROUP_SIZE_X;

                if (_GroupObjectPayloadIndexOffset >= currentPayload.objectCount)
                {
                    _GroupObjectPayloadIndex++;
                    _GroupObjectPayloadIndexOffset = 0;
                }
            }
        }
    }
}

StructuredBuffer<uint> _DrawIndirectArgs;
RWStructuredBuffer<uint> _OutOffsetBufferCount;
RWStructuredBuffer<uint> _OutObjectIndexOffsetBuffer;
RWStructuredBuffer<uint> _OutTempObjectIndexOffsetBuffer;
 
[numthreads(NUM_THREADS_PER_GROUP, 1, 1)]
void AllocateCommandInstanceOutputSpace(uint indirectArgIndex : SV_DispatchThreadID)
{
    if (indirectArgIndex < _IndirectArgCount)
    {
        uint objectCommandCount = _DrawIndirectArgs[indirectArgIndex * INDIRECT_ARGS_NUM_WORDS + 1];
        uint objectCommandOffset = 0u;
        if (objectCommandCount > 0u)
        {
            InterlockedAdd(_OutOffsetBufferCount[0], objectCommandCount, objectCommandOffset);
        }
        _OutObjectIndexOffsetBuffer[indirectArgIndex] = objectCommandOffset;
        _OutTempObjectIndexOffsetBuffer[indirectArgIndex] = objectCommandOffset;
    }
}

StructuredBuffer<VisibleObjectCommand> _VisibleObjectCommands;
StructuredBuffer<uint> _VisibleObjectCommandCount;
RWStructuredBuffer<uint> _OutObjectIndexBuffer;
RWStructuredBuffer<uint> _OutPageInfoBuffer;

[numthreads(NUM_THREADS_PER_GROUP, 1, 1)]
void OutputCommandInstanceLists(uint visibleObjectCommandIndex : SV_DispatchThreadID)
{
    uint visibleObjectCommandCount = _VisibleObjectCommandCount[0];

    if (visibleObjectCommandIndex < visibleObjectCommandCount)
    {
        VisibleObjectCommand visibleObjectCommand = _VisibleObjectCommands[visibleObjectCommandIndex];

        uint objectIndexOutputOffset = 0u;
        InterlockedAdd(_OutTempObjectIndexOffsetBuffer[visibleObjectCommand.indirectArgIndex], 1u, objectIndexOutputOffset);

        _OutObjectIndexBuffer[objectIndexOutputOffset] = visibleObjectCommand.objectIndex;
        _OutPageInfoBuffer[objectIndexOutputOffset] = visibleObjectCommand.packedPageInfo;
    }
}