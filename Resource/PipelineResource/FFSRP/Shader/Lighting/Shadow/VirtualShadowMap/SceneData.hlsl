#ifndef SCENE_DATA_HLSL
#define SCENE_DATA_HLSL

#include "Features/GPUScene/CullingSceneData.hlsl"

struct ObjectSceneData
{
    float4x4 ce_World;
    float4x4 ce_PreWorld;
    float4x4 ce_InvTransposeWorld;
    float3 ce_TilePosition;
    float3 ce_PreTilePosition;
};

// // Foliage
// struct FoliageObjectSceneData
// {
//     float4x4 world;
//     float4x4 preWorld;
//     float3 tilePosition;
//     float3 preTilePosition;
// };

struct FoliagePayloadData
{
    float4 boundingSphere;
    uint lodDataStart;
    uint lodDataCount;
    uint objectIndexStart;
    uint objectIndexCount;
    uint indirectArgIndexOffset;
    float culledHeight;
    float2 _pad;
};

#endif