#pragma compute InitIndirectArgs1D

cbuffer cbPass
{
    uint4 _UIntParams0;
}

#define _Multiplier         _UIntParams0.x
#define _Divisor            _UIntParams0.y
#define _InputCountOffset   _UIntParams0.z

StructuredBuffer<uint> _InputCountBuffer;
RWStructuredBuffer<uint> _OutIndirectDispatchArgs;

[numthreads(1, 1, 1)]
void InitIndirectArgs1D(uint3 dispatchThreadId : SV_DispatchThreadID)
{
    uint groupCount = (_InputCountBuffer[_InputCountOffset] * _Multiplier + _Divisor - 1u) / _Divisor;

    _OutIndirectDispatchArgs[0] = groupCount;
	_OutIndirectDispatchArgs[1] = 1;
	_OutIndirectDispatchArgs[2] = 1;
}