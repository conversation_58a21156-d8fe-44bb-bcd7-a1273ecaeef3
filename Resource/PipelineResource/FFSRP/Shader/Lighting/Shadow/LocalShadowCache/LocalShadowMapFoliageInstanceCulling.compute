#pragma compute CullPerLightDrawUnits

SamplerState ce_Sampler_Point;

#include "../VirtualShadowMap/SceneData.hlsl"
#include "../../../Features/InstanceCulling/HZBCull.hlsl"
#include "../../../ShaderLibrary/Common.hlsl"
#include "../../../ShaderLibrary/CommonStruct.hlsl"

#define NUM_THREADS_PER_GROUP 64
#define INDIRECT_ARGS_NUM_WORDS 5

cbuffer cbPass
{
    uint4 _UIntParams1;
    matrix ce_View;
    matrix ce_Projection;
    float3 ce_CameraTilePosition;
    float4 ce_ScreenParams;
    float _LODScaleFactor;
}

#define _ObjectCount                        _UIntParams1.x
#define _VisibleObjectCommandBufferMaxNum   _UIntParams1.y
#define _FoliagePayloadDataCount            _UIntParams1.z
#define _CurrSceneFrameCount                _UIntParams1.w

struct VisibleObjectCommand
{
    uint objectIndex;
    uint indirectArgIndex;
};

cbuffer localLightViewData
{
    matrix lightViewProjMatrix;
    float3 lightTile;
}

StructuredBuffer<FoliageCompactSceneData> _FoliageObjectSceneDatas;
StructuredBuffer<FoliageEntityData> _FoliageEntityBuffer;
StructuredBuffer<float> _LODDatas;
StructuredBuffer<int> _IndirectArgIndices;
StructuredBuffer<FoliagePayloadData> _FoliagePayloadDatas;

RWStructuredBuffer<VisibleObjectCommand> _OutVisibleObjectCommands;
RWStructuredBuffer<uint> _OutVisibleObjectCommandCount;
RWStructuredBuffer<uint> _OutDrawIndirectArgs;

#define BASE_SCREEN_HEIGHT 1080

float GetScreenSizeY(float3 viewSphereCenter, float radius, matrix proj)
{
    float dist = length(viewSphereCenter);
    float screenSize = max(proj._m00, proj._m11) * 0.5;
    float screenRadius = screenSize * radius / max(1.0, dist);
    return 2 * screenRadius;
}

int SelectLOD(float3 worldBoundsCenter, float worldBoundsRadius, FoliagePayloadData payloadData, FoliageObjectSceneData objectData)
{
    worldBoundsCenter = GetLargeCoordinateReltvPosition(worldBoundsCenter, objectData.tilePosition, ce_CameraTilePosition);
    float3 viewBoundsCenter = mul(ce_View, float4(worldBoundsCenter, 1.0)).xyz;

    float screenSizeY = GetScreenSizeY(viewBoundsCenter, worldBoundsRadius, ce_Projection);
    float sizeFactor = 0.8 * sqrt(ce_ScreenParams.y / BASE_SCREEN_HEIGHT);

    if (screenSizeY < payloadData.culledHeight)
    {
        return -1;
    }

    uint lodIndex = 0;
    for (; lodIndex < payloadData.lodDataCount; lodIndex++)
    {
        float screenReleativeTransitionHeight = _LODDatas[payloadData.lodDataStart + lodIndex];

        if (screenSizeY * sizeFactor * _LODScaleFactor > screenReleativeTransitionHeight)
        {
            break;
        }
    }

    return min(lodIndex, payloadData.lodDataCount - 1);
}

void WriteCommand(uint objectIndex, uint indirectArgIndex)
{
    VisibleObjectCommand visibleObjectCommand;
    visibleObjectCommand.objectIndex = objectIndex;
    visibleObjectCommand.indirectArgIndex = indirectArgIndex;

    uint visibleObjectCommandOutputOffset = 0u;
    InterlockedAdd(_OutVisibleObjectCommandCount[0], 1u, visibleObjectCommandOutputOffset);
    if (visibleObjectCommandOutputOffset < _VisibleObjectCommandBufferMaxNum)
    {
        _OutVisibleObjectCommands[visibleObjectCommandOutputOffset] = visibleObjectCommand;
    }
}

[numthreads(NUM_THREADS_PER_GROUP, 1, 1)]
void CullPerLightDrawUnits(uint groupIndex : SV_GroupIndex, uint dispatchThreadId : SV_DispatchThreadID)
{
    if (dispatchThreadId >= _ObjectCount)
    {
        return;
    }

    // Get FoliagePayloadData
    FoliagePayloadData foliagePayloadData;
    uint prevObjectCount = 0;
    uint objectIndex = 0;
    
    for (uint payloadIndex = 0; payloadIndex < _FoliagePayloadDataCount; payloadIndex++)
    {
        foliagePayloadData = _FoliagePayloadDatas[payloadIndex];
        uint currObjectCount = prevObjectCount + foliagePayloadData.objectIndexCount;

        if (dispatchThreadId < currObjectCount)
        {
            objectIndex = dispatchThreadId - prevObjectCount + foliagePayloadData.objectIndexStart;
            break;
        }

        prevObjectCount = currObjectCount;
    }
    
    // Get FoliageObjectSceneDatas
    FoliageCompactSceneData compactData = _FoliageObjectSceneDatas[objectIndex];
    FoliageObjectSceneData foliageObjectSceneData;
    DecodeFoliageCompactSceneData(compactData, _FoliageEntityBuffer[compactData.entityIndex], foliageObjectSceneData);

    // Get worldspace BoundingSphere
    float3 worldBoundsCenter = mul(foliageObjectSceneData.world, float4(foliagePayloadData.boundingSphere.xyz, 1.0)).xyz;
    float scale = max(max(length(foliageObjectSceneData.world[0].xyz), length(foliageObjectSceneData.world[1].xyz)), length(foliageObjectSceneData.world[2].xyz));
    float worldBoundsRadius = foliagePayloadData.boundingSphere.w * scale;

    // LOD Selection
    int lodIndex = SelectLOD(worldBoundsCenter, worldBoundsRadius, foliagePayloadData, foliageObjectSceneData);
    lodIndex = min(lodIndex, 3); // ???? temp
    if (lodIndex == -1)
    {
        return;
    }

    // InstanceCulling
    int indirectArgIndex = _IndirectArgIndices[foliagePayloadData.indirectArgIndexOffset + lodIndex];
    if (indirectArgIndex == -1)
    {
        return;
    }
    
    bool hasMoved = false; // temp

    uint visibleViewCount = 0;
    
    float3 worldBoundsCenter2 = GetLargeCoordinateReltvPosition(worldBoundsCenter, foliageObjectSceneData.tilePosition, lightTile);

    FrustumCullData cull = BoxCullFrustum(worldBoundsCenter2, worldBoundsRadius, lightViewProjMatrix, false);
    if (cull.isVisible)
    {
        ++visibleViewCount;
        WriteCommand(objectIndex, indirectArgIndex);       
    }
    InterlockedAdd(_OutDrawIndirectArgs[indirectArgIndex * INDIRECT_ARGS_NUM_WORDS + 1], visibleViewCount);
}