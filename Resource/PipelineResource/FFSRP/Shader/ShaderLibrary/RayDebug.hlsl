#ifndef RAY_DEBUG_HLSL
#define RAY_DEBUG_HLSL

#include "Common.hlsl"

SHADER_CONST(bool, ENABLE_SSRT_RAY_DEBUG, false);

RWStructuredBuffer<float4> _DebugRayInfoOutput;

bool IsCenterOfScreen(float2 positionSS, float4 screenParams)
{
	return (uint)positionSS.x == (uint)(screenParams.x/2) && 
           (uint)positionSS.y == (uint)(screenParams.y/2);
}

void AddDebugRay(inout int rayIndex, float3 rayStart, float3 rayEnd, float3 rayColor)
{
	_DebugRayInfoOutput[1 + rayIndex * 3 + 0] = float4(rayStart, 0.0);
	_DebugRayInfoOutput[1 + rayIndex * 3 + 1] = float4(rayEnd, 0.0);
	_DebugRayInfoOutput[1 + rayIndex * 3 + 2] = float4(rayColor, 0.0);
	rayIndex++;
	_DebugRayInfoOutput[0] = float4(rayIndex, 0.0, 0.0, 0.0);
}

void AddDebugCross(inout int rayIndex, float3 center, float3 rayColor, float size = 100.0)
{
	AddDebugRay(rayIndex, center + float3(0.0, size, 0.0), center + float3(0.0, -size, 0.0), rayColor);
	AddDebugRay(rayIndex, center + float3(size, 0.0, 0.0), center + float3(-size, 0.0, 0.0), rayColor);
	AddDebugRay(rayIndex, center + float3(0.0, 0.0, size), center + float3(0.0, 0.0, -size), rayColor);
}

#endif