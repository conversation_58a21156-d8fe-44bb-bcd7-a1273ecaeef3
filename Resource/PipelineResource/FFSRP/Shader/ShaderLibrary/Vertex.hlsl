#ifndef VERTEX_HLSL
#define VERTEX_HLSL

#ifdef QTANGENT
	#include "QTangents.hlsl"
#endif

#ifdef USE_VERTEX_AERIAL_PERSPECTIVE
#include "/Features/SkyAtmosphere/UE/Interfaces.hlsl"
#endif

#include "VertexFactoryHeader.hlsl"

#ifndef VERTEX_MODIFICATION
// be care full, when change positionWS in shader,
// note, change positionWS in this fucntion is useless, must use the GetWorldPositionOffset
VSOutput GetVertexOutput(in VSOutput vOut, in VSInput vIn)
{
	return vOut;
}
#endif

#ifndef WORLD_POSITION_OFFSET
// be carefull with potential mv, shadow, prez, all geometric relelated pass;
float3 GetWorldPositionOffset(float3 Cur_or_Prev_worldPosition, in VSOutput vOut, in VSInput vIn)
{
	return Cur_or_Prev_worldPosition;
}
#endif

#ifndef WORLD_POSITION_OFFSET_PREVIOUS
float3 GetPreviousWorldPositionOffset(float3 Cur_or_Prev_worldPosition, in VSOutput vOut, in VSInput vIn)
{
	return Cur_or_Prev_worldPosition;
}
#endif

#ifndef PARTICLE_VERTEX_ASSEMBLE
VSOutput AssembleParticleVertex(in VSOutput output, inout VSInput vIn)
{
	return output;
}
#endif

#ifndef TRANSFER_CUSTOM_DATA
void TransferCustomDataVS(in VSInput vsInput, inout VSOutput vsOutput)
{
}

void TransferCustomDataPS(in VSOutput vsOutput, inout PSInput psInput)
{
}
#endif

VSOutput ExecuteVertexOut(in VSOutput vOut, in VSInput vIn)
{
    VSOutput output = GetVertexOutput(vOut, vIn);
	return output;
}

#if defined(VERTEX_TYPE) && VERTEX_TYPE == VertexType_Vegetation
	#ifdef CE_INSTANCING
		#include "VertexFoliage.hlsl" 
	#else
    	#include "VertexVegetation.hlsl"
	#endif
#elif defined(VERTEX_TYPE) && VERTEX_TYPE == VertexType_Terrain
	#include "VertexTerrain.hlsl"
#else

#if WRITES_VELOCITY_TO_GBUFFER
	#include "VertexMotionVector.hlsl"
#endif

float3 normalize_safe(float3 in_vec)
{
	float3 len2 = max(0.001, dot(in_vec, in_vec));
	return in_vec * rsqrt(len2);
}

float2 FlipUV(float2 uv, bool flip)
{
	if(flip)
	{
		return float2(uv.x, 1.0 - uv.y);
	}
	return uv;
}

VSOutput VSInputToVSOutput(VSInput input)
{ 
	VSOutput output = (VSOutput)0;

#if defined(PARTICLE) && PARTICLE == 1
	output = AssembleParticleVertex(output, input);
#endif

#if defined(INSTANCING) && INSTANCING == 1
    float4 positionWS = mul(input.ce_World, float4(input.position.xyz, 1.0));
#else
    float4 positionWS = mul(ce_World, float4(input.position.xyz, 1.0));
#endif

#ifdef CE_USE_DOUBLE_TRANSFORM
	#if defined(INSTANCING) && INSTANCING == 1
		positionWS.xyz = GetLargeCoordinateReltvPosition(positionWS.xyz, input.ce_TilePosition, ce_CameraTilePosition);
	#else
		positionWS.xyz = GetLargeCoordinateReltvPosition(positionWS.xyz, ce_TilePosition, ce_CameraTilePosition);
	#endif
#endif

	float4 tangent;
	float3 normal;

#ifndef QTANGENT
	tangent = input.tangent;
	normal = input.normal;
#else
	// transform Qtangent to tangents.
	decode_QTangent_to_tangent(input.Qtangent, normal, tangent);
#endif

#if defined(INSTANCING) && INSTANCING == 1
	output.normalWS = normalize_safe(mul(input.ce_InvTransposeWorld, float4(normalize_safe(normal), 0)).xyz);
	output.tangentWS.xyz = normalize_safe(mul(input.ce_World, float4(normalize_safe(tangent.xyz), 0)).xyz);
#else
	output.normalWS = normalize_safe(mul(ce_InvTransposeWorld, float4(normalize_safe(normal), 0)).xyz);
	output.tangentWS.xyz = normalize_safe(mul(ce_World, float4(normalize_safe(tangent.xyz), 0)).xyz);
#endif

	output.tangentWS.w = tangent.w;

// #if NUM_MATERIAL_TEXCOORDS
// 	if(NO_INVERTUV)
// 	{
// 		output.uv[0].xy = float2(input.uv.x,input.uv.y);
// 	}
// 	else
// 	{
// 		output.uv[0].xy = float2(input.uv.x, 1 - input.uv.y);
// 	}
// #endif


	// Set UV
#if NUM_MATERIAL_TEXCOORDS > 0
	output.uvs[0].xy = FlipUV(input.uv, !NO_INVERTUV);
#endif

#if NUM_MATERIAL_TEXCOORDS > 1
	output.uvs[0].zw = FlipUV(input.uv1, false);

#endif

#if NUM_MATERIAL_TEXCOORDS > 2
	output.uvs[1].xy = FlipUV(input.uv2, false);
#endif


#if defined(TEXTURE_ARRAY_ENABLE) && TEXTURE_ARRAY_ENABLE == 1
	output.uvIdx = input.uvIdx;
#endif


#ifdef USE_VERTEX_COLOR
    output.color = input.color;
#endif

	float3 reservedPositionWS = positionWS;
	float4 reservedPositionNDC = mul(ce_Projection, mul(ce_View, float4(reservedPositionWS, 1)));
    output = ExecuteVertexOut(output, input);
	positionWS = float4(GetWorldPositionOffset(reservedPositionWS, output, input), 1.0);

#if WRITES_VELOCITY_TO_GBUFFER
	ComputeMotionVector(reservedPositionNDC, positionWS, input, output);
#endif
	output.positionNDC = mul(ce_Projection, mul(ce_View, positionWS));

#ifdef USE_VERTEX_AERIAL_PERSPECTIVE
	float vertexDepthInKm =  DefaultUnitToKm(length(positionWS.xyz - ce_CameraPos));
	float2 ndc = output.positionNDC.xy / output.positionNDC.w;
	float2 screenUV = float2((ndc.x + 1) / 2, (1 - ndc.y) / 2);
	output.vertexAerialPerspective = float4(0, 0, 0, 1);

	if(USE_SKY_ATMOSPHERE)
	{
		output.vertexAerialPerspective = GetAtmosphereAerialPerspective(screenUV, vertexDepthInKm);
	}
#endif
	TransferCustomDataVS(input, output);

	return output;
}

PSInput VSOutputToPSInput(VSOutput input)
{
	PSInput output = (PSInput)0;
	output.normalWS = input.normalWS;
	output.tangentWS = input.tangentWS.xyz;
	output.binormalWS = input.tangentWS.w * cross(output.normalWS, output.tangentWS);
#if NUM_MATERIAL_TEXCOORDS > 0
	output.uv = input.uvs[0].xy;
	output.uvs = input.uvs;
#endif
#if NUM_MATERIAL_TEXCOORDS > 1
	output.uv1 = input.uvs[0].zw;
#endif

#if NUM_MATERIAL_TEXCOORDS > 2
	output.uv2 = input.uvs[1].xy;
#endif

#if defined(TEXTURE_ARRAY_ENABLE) && TEXTURE_ARRAY_ENABLE == 1
	output.uvIdx = input.uvIdx;
#endif
	float2 screenUV = input.positionNDC.xy * ce_ScreenParams.zw;
    screenUV = float2(screenUV.x, 1 - screenUV.y) * 2 - 1.0;
	float4 posNDC = float4(float3(screenUV, input.positionNDC.z), 1.0);
	float4 posWS = mul(ce_InvViewProjMatrix, posNDC);
	output.positionWS = posWS.xyz / posWS.w;

	output.screenUV = input.positionNDC.xy * ce_ScreenParams.zw;
	output.positionNDC = input.positionNDC;

#if WRITES_VELOCITY_TO_GBUFFER
	output.prePositionNDC=input.prePositionNDC;
    output.nowPositionNDC=input.nowPositionNDC;
#endif

#ifdef USE_VERTEX_COLOR
    output.color = input.color;
#endif

#ifdef USE_VERTEX_AERIAL_PERSPECTIVE
	output.vertexAerialPerspective = input.vertexAerialPerspective;
#endif


	TransferCustomDataPS(input, output);

	return output;
}

PSInput VSInputToPSInput(VSInput input)
{
	return VSOutputToPSInput(VSInputToVSOutput(input));
}

#endif
#endif