#ifndef VERTEX_HLSL
#define VERTEX_HLSL

#ifdef QTANGENT
	#include "QTangents.hlsl"
#endif

#include "VertexLayout.hlsl"
SHADER_CONST(bool, USE_FOLIAGE_INSTANCE, false)

#ifndef VERTEX_MODIFICATION
// be care full, when change positionWS in shader,
// note, change positionWS in this fucntion is useless, must use the GetWorldPositionOffset
VSOutput GetVertexOutput(in VSOutput vOut, in VSInput vIn)
{
	return vOut;
}
#endif

#ifndef WORLD_POSITION_OFFSET
// be carefull with potential mv, shadow, prez, all geometric relelated pass;
float3 GetWorldPositionOffset(float3 Cur_or_Prev_worldPosition, in VSOutput vOut, in VSInput vIn)
{
	return Cur_or_Prev_worldPosition;
}
#endif

#ifndef WORLD_POSITION_OFFSET_PREVIOUS
float3 GetPreviousWorldPositionOffset(float3 Cur_or_Prev_worldPosition, in VSOutput vOut, in VSInput vIn)
{
	return Cur_or_Prev_worldPosition;
}
#endif

#ifndef PARTICLE_VERTEX_ASSEMBLE
void AssembleParticleVertex(in ObjectSceneData objData, inout VSInput vIn)
{
	return;
}
#endif

#ifndef TRANSFER_CUSTOM_DATA
void TransferCustomDataVS(in VSInput vsInput, inout VSOutput vsOutput)
{
}

void TransferCustomDataPS(in VSOutput vsOutput, inout PSInput psInput)
{
}
#endif

VSOutput ExecuteVertexOut(in VSOutput vOut, in VSInput vIn)
{
    VSOutput output = GetVertexOutput(vOut, vIn);
	return output;
}

#include "VertexMotionVector.hlsl"

#ifdef ENABLE_VSM
float4 GetClipDistance(VirtualShadowMapViewData viewData, float4 positionNDC, PageInfo pageInfo)
{
	// for orthographic shadow camera
	float2 uv = positionNDC.xy * float2(0.5, -0.5) + 0.5;
	float2 pixels = uv * (uint(VSM_VIRTUAL_MAX_RESOLUTION_XY) >> viewData.levelIndex);
	
	uint4 pageRect = _PageRectBounds[viewData.virtualShadowMapId * VSM_MAX_MIP_LEVELS + viewData.levelIndex];

	float2 minClip = pixels - (pageRect.xy + 0) * VSM_PAGE_SIZE;
	float2 maxClip = -pixels + (pageRect.zw + 1) * VSM_PAGE_SIZE;

	return float4(minClip, maxClip);
}
#endif


float3 normalize_safe(float3 in_vec)
{
	float3 len2 = max(0.001, dot(in_vec, in_vec));
	return in_vec * rsqrt(len2);
	
}

VSOutput VSInputToVSOutput(VSInput input)
{
	VSOutput output = (VSOutput)0;

	uint instanceIndex = input.instanceIDOffset + input.instanceID;
	uint objectGUID = _ObjectIndexBuffer[instanceIndex];
	output.instanceID = objectGUID;

#ifdef ENABLE_VSM
	uint packedPageInfo = _PageInfoBuffer[instanceIndex];
	PageInfo pageInfo = UnpackPageInfo(packedPageInfo);

	VirtualShadowMapViewData viewData = _VirtualShadowMapViewDatas[pageInfo.viewId];
#endif

#ifdef USE_MULTI_VIEWPORT
	uint lightViewIndex = _LightViewIndexBuffer[instanceIndex];
	LocalLightViewData viewData = _LocalLightViewDatas[lightViewIndex];
	output.ViewportArrayIndex = viewData.viewportIndex;
    output.RenderTargetArrayIndex = viewData.arrayIndex;
	output.lightViewIndex = lightViewIndex;
#endif

/// ---POSITION SECTION---
	matrix worldMatrix;
	matrix invTransposeWorldMatrix;
	matrix prevWorldMatrix;
	float3 tilePosition;
	float3 preTilePosition;

#if defined(FOLIAGE_VSM)
	if (USE_FOLIAGE_INSTANCE)
	{
		FoliageCompactSceneData compactData = _FoliageObjectSceneDatas[objectGUID];
		FoliageObjectSceneData objectData;
		DecodeFoliageCompactSceneData(compactData, _FoliageEntityBuffer[compactData.entityIndex], objectData);

		worldMatrix = objectData.world;
		invTransposeWorldMatrix = objectData.invTransposeWorld;
		prevWorldMatrix = objectData.preWorld;
		tilePosition = objectData.tilePosition;
		preTilePosition = objectData.preTilePosition;//TODO: it's wrong, but foliage don't have preTilePosition
	}
	else
	{
		ObjectSceneData objectData = ce_PerObject[objectGUID];
		worldMatrix = objectData.ce_World;
		invTransposeWorldMatrix = objectData.ce_InvTransposeWorld;
		prevWorldMatrix = objectData.ce_PreWorld;
		tilePosition = objectData.ce_TilePosition;
		preTilePosition = objectData.ce_PreTilePosition;
	}
#elif defined(LOCAL_SHADOW)
	if (USE_FOLIAGE_INSTANCE)
	{
		FoliageCompactSceneData compactData = _FoliageObjectSceneDatas[instanceIndex];
		FoliageObjectSceneData objectData;
		DecodeFoliageCompactSceneData(compactData, _FoliageEntityBuffer[compactData.entityIndex], objectData);

		worldMatrix = objectData.world;
		invTransposeWorldMatrix = objectData.invTransposeWorld;
		prevWorldMatrix = objectData.preWorld;
		tilePosition = objectData.tilePosition;
		preTilePosition = objectData.preTilePosition;
	}
	else
	{
		ObjectSceneData objectData = ce_PerObject[objectGUID];
		worldMatrix = objectData.ce_World;
		invTransposeWorldMatrix = objectData.ce_InvTransposeWorld;
		prevWorldMatrix = objectData.ce_PreWorld;
		tilePosition = objectData.ce_TilePosition;
		preTilePosition = objectData.ce_PreTilePosition;
	}
#else 
	ObjectSceneData objectData = ce_PerObject[objectGUID];

#if defined(PARTICLE) && PARTICLE == 1
	AssembleParticleVertex(objectData, input);
#endif

	worldMatrix = objectData.ce_World;
	invTransposeWorldMatrix = objectData.ce_InvTransposeWorld;
	prevWorldMatrix = objectData.ce_PreWorld;
	tilePosition = objectData.ce_TilePosition;
	preTilePosition = objectData.ce_PreTilePosition;
#endif

#if defined(PARTICLE) && PARTICLE == 1
	float4 positionWS = float4(input.position.xyz, 1.0);
#else
	// PositionWS
    float4 positionWS = mul(worldMatrix, float4(input.position.xyz, 1.0));
#endif // END PARTICLE

#ifdef ENABLE_VSM
    positionWS.xyz = GetLargeCoordinateReltvPosition(positionWS.xyz, tilePosition, viewData.tilePosition);
#elif defined(USE_MULTI_VIEWPORT)
	positionWS.xyz = GetLargeCoordinateReltvPosition(positionWS.xyz, tilePosition, viewData.lightTileAndRange.xyz);
#else
    positionWS.xyz = GetLargeCoordinateReltvPosition(positionWS.xyz, tilePosition, ce_CameraTilePosition);
#endif

    output.positionWS = positionWS.xyz;

/// ---END POSITION SECTION---

#ifdef ENABLE_VSM
	#ifdef VSM_ALPHACLIP
		output.uv.xy = float2(input.uv.x,input.uv.y);
	#endif
#else
	// UV Section
	output.uv.xy = float2(input.uv.x,input.uv.y);
	#if defined(TEXTURE_ARRAY_ENABLE) && TEXTURE_ARRAY_ENABLE == 1
		output.uvIdx = input.uvIdx;
	#endif

	output.uv1 = float2(input.uv1.x, input.uv1.y);

	#if defined(LIGHT_MAP_ENABLE) && LIGHT_MAP_ENABLE == 1
		#if defined(LIGHT_MAP_UV_CHANNEL_0) && LIGHT_MAP_UV_CHANNEL_0 == 1
			output.uv1 = float2(input.uv.x, input.uv.y);
		#endif
	#endif // END UV Section

	// Normal Section 
    float4 tangent;
	float3 normal;

	#ifndef QTANGENT
		tangent = input.tangent;
		normal = input.normal;
	#else
		// transform Qtangent to tangents.
		decode_QTangent_to_tangent(input.Qtangent, normal, tangent);
	#endif

	#if defined(PARTICLE) && PARTICLE == 1
		output.normalWS = normal;
		output.tangentWS.xyz = tangent.xyz;
		output.tangentWS.w = 1;
	#else
		output.normalWS = normalize_safe(mul(invTransposeWorldMatrix, float4(normalize_safe(normal), 0)).xyz);
		output.tangentWS.xyz = normalize_safe(mul(worldMatrix, float4(normalize_safe(tangent.xyz), 0)).xyz);
		output.tangentWS.w = tangent.w;
	#endif // END Normal Section

	// VertexColor Section
	#ifdef USE_VERTEX_COLOR
		output.color = input.color;
	#endif // END VertexColor Section
#endif // END ENABLE_VSM

	float3 reservedPositionWS = output.positionWS;
    output = ExecuteVertexOut(output, input);
	output.positionWS = GetWorldPositionOffset(reservedPositionWS, output, input);

#ifdef CE_INSTANCING
#ifndef ENABLE_VSM
	ComputeMotionVector(output.positionNDC, prevWorldMatrix, preTilePosition, ce_PrevCameraTilePosition, float4(reservedPositionWS, 1), input, output);
#endif
#endif

	// PositionNDC
	positionWS = float4(output.positionWS, 1.0);
#ifdef ENABLE_VSM
	output.positionNDC = mul(viewData.worldToShadowMatrix, positionWS);
#elif defined(USE_MULTI_VIEWPORT)
	output.positionNDC = mul(viewData.lightViewProjMatrix, positionWS);
#else
	output.positionNDC = mul(ce_Projection, mul(ce_View, positionWS));
#endif

#ifdef ENABLE_VSM
	output.packedPageInfo = packedPageInfo;
	output.virtualSmPageClip = GetClipDistance(viewData, output.positionNDC, pageInfo);
#endif

	TransferCustomDataVS(input, output);

	return output;
}

PSInput VSOutputToPSInput(VSOutput input)
{
	PSInput output = (PSInput)0;
	output.positionWS = input.positionWS;
	output.screenUV = input.positionNDC.xy * ce_ScreenParams.zw;
	output.positionNDC = input.positionNDC;
	output.instanceID = input.instanceID;

#ifdef ENABLE_VSM
	output.packedPageInfo = input.packedPageInfo;

	#ifdef VSM_ALPHACLIP
		output.uv = input.uv;
	#endif
#else
	output.normalWS = input.normalWS;
	output.tangentWS = input.tangentWS.xyz;
	output.binormalWS = input.tangentWS.w * cross(output.normalWS, output.tangentWS);
    output.uv = input.uv;

	#if defined(TEXTURE_ARRAY_ENABLE) && TEXTURE_ARRAY_ENABLE == 1
		output.uvIdx = input.uvIdx;
	#endif

	output.prePositionNDC=input.prePositionNDC;
    output.nowPositionNDC=input.nowPositionNDC;
	output.uv1 = input.uv1;

	#ifdef USE_VERTEX_COLOR
		output.color = input.color;
	#endif
#endif

#if defined(USE_MULTI_VIEWPORT)
	output.lightViewIndex = input.lightViewIndex;
#endif

	TransferCustomDataPS(input, output);

	return output;
}

PSInput VSInputToPSInput(VSInput input)
{
	return VSOutputToPSInput(VSInputToVSOutput(input));
}

#endif