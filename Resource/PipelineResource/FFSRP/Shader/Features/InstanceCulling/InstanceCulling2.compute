#pragma compute CullPerPageDrawUnits
#pragma compute AllocateCommandInstanceOutputSpace
#pragma compute OutputCommandInstanceLists

#include "Features/GPUScene/CullingSceneData.hlsl"
#include "ShaderLibrary/Common.hlsl"

#define NUM_THREADS_PER_GROUP 64
#define PERSISTENT_CULLING_GROUP_SIZE 256
#define PERSISTENT_CULLING_GROUP_COUNT 64
#define PERSISTENT_CULLING_INSTANCE_COUNT_PER_GROUP PERSISTENT_CULLING_GROUP_SIZE * 64
#define INDIRECT_ARGS_NUM_WORDS 5
#define INSTANCE_COUNT_CACHE_SIZE PERSISTENT_CULLING_GROUP_SIZE * 4

cbuffer cbPass
{
    matrix ce_Projection;
    matrix ce_View;
    matrix ce_PreViewMatrix;
    matrix ce_PreProjMatrixJitter;
    float3 ce_CameraTilePosition;
    float3 ce_PrevCameraTilePosition;
    float4 ce_ScreenParams;

    uint _CurrSceneFrameCount;
    uint _PayloadCount;
    uint _RangePayloadOffset;
    uint _RangePayloadCount;
    uint _LargePayloadCount;
    uint _VisibleObjectCommandBufferMaxNum;
    uint _IndirectArgCount;
    uint _UseCurrHiZ;
}

struct VisibleObjectCommand
{
    uint objectIndex;
    uint indirectArgIndex;
};

StructuredBuffer<PrimitiveCullingData> _GPUScenePrimitiveCullingDatas;
StructuredBuffer<ObjectCullingData> _GPUSceneObjectCullingDatas;

RWStructuredBuffer<VisibleObjectCommand> _OutVisibleObjectCommands;
RWStructuredBuffer<uint> _OutVisibleObjectCommandCount;
RWStructuredBuffer<uint> _OutDrawIndirectArgs;

SamplerState ce_Sampler_Point;

#include "HZBCull.hlsl"

void WriteCommand(uint objectIndex, uint indirectArgIndex)
{
    VisibleObjectCommand visibleObjectCommand;
    visibleObjectCommand.objectIndex = objectIndex;
    visibleObjectCommand.indirectArgIndex = indirectArgIndex;

    uint visibleObjectCommandOutputOffset = 0u;
    InterlockedAdd(_OutVisibleObjectCommandCount[0], 1u, visibleObjectCommandOutputOffset);
    if (visibleObjectCommandOutputOffset < _VisibleObjectCommandBufferMaxNum)
    {
        _OutVisibleObjectCommands[visibleObjectCommandOutputOffset] = visibleObjectCommand;
    }
}

struct GroupObjectPayloadData
{
    int objectCullingGUID;
    int objectIndex;
    uint indirectArgIndex;
};

StructuredBuffer<ObjectPayloadData2> _ObjectPayloadDatas;
RWStructuredBuffer<uint> _ObjectPayloadIndexAndObjectOffsetAndMutex;

#define _ObjectPayloadIndex _ObjectPayloadIndexAndObjectOffsetAndMutex[0]
#define _ObjectPayloadObjectOffset _ObjectPayloadIndexAndObjectOffsetAndMutex[1]
#define _ObjectPayloadMutex _ObjectPayloadIndexAndObjectOffsetAndMutex[2]

groupshared uint _GroupObjectPayloadIndex;
groupshared uint _GroupObjectPayloadIndexOffset;
groupshared uint _GroupObjectPayloadIndexEnd;
groupshared uint _GroupObjectPayloadIndexOffsetEnd;

void LockAcquire()
{
    uint value = 1u;
    while (value)
    {
        InterlockedCompareExchange(_ObjectPayloadMutex, 0u, 1u, value);
    }
}

void LockRelease()
{
    uint value;
    InterlockedExchange(_ObjectPayloadMutex, 0, value);
}

void InstanceCulling(GroupObjectPayloadData payload)
{
    int objectCullingGUID = payload.objectCullingGUID;
    int objectIndex = payload.objectIndex;

    if (objectCullingGUID == -1)
    {
        return;
    }

    ObjectCullingData objectCullingData = _GPUSceneObjectCullingDatas[objectCullingGUID];
    PrimitiveCullingData primitiveCullingData = _GPUScenePrimitiveCullingDatas[objectCullingData.primitiveCullingGUID];

    uint flag = primitiveCullingData.flag;
    bool isAlwaysVisible = flag & CULLING_FLAG_ALWAYSVISIBLE_BIT_MASK;

    if (isAlwaysVisible)
    {
        WriteCommand(objectIndex, payload.indirectArgIndex);
        InterlockedAdd(_OutDrawIndirectArgs[payload.indirectArgIndex * INDIRECT_ARGS_NUM_WORDS + 1], 1);
    }
    else
    {
        // FrustumCulling
        matrix currViewProjMatrix = mul(ce_Projection, ce_View);
        matrix localToClip = CombineTranslationMatrix(objectCullingData.worldMatrix, currViewProjMatrix, primitiveCullingData.tilePosition, ce_CameraTilePosition);

        FrustumCullData currCull = BoxCullFrustum(primitiveCullingData.localBoundsCenter, primitiveCullingData.localBoundsExtent, localToClip, true);
        bool isVisible = currCull.isVisible;

        // OcclusionCulling
        if (isVisible)
        {
            if (_UseCurrHiZ > 0)
            {
                ScreenRect rect = GetScreenRect(int4(0, 0, ce_ScreenParams.xy), currCull, 4);
                isVisible = IsVisibleHZB(rect, true);
            }
            else
            {
                matrix prevViewProjMatrix = mul(ce_PreProjMatrixJitter, ce_PreViewMatrix);
                matrix prevLocalToClip = CombineTranslationMatrix(objectCullingData.worldMatrix, prevViewProjMatrix, primitiveCullingData.tilePosition, ce_PrevCameraTilePosition);

                FrustumCullData prevCull = BoxCullFrustum(primitiveCullingData.localBoundsCenter, primitiveCullingData.localBoundsExtent, prevLocalToClip, true);

                if ((prevCull.isVisible || prevCull.isFrustumSideCulled) && !prevCull.isCrossesNearPlane)
                {
                    ScreenRect prevRect = GetScreenRect(int4(0, 0, ce_ScreenParams.xy), prevCull, 4);
                    isVisible = IsVisibleHZB(prevRect, true);
                }
            }
        }

        if (isVisible)
        {
            WriteCommand(objectIndex, payload.indirectArgIndex);
            InterlockedAdd(_OutDrawIndirectArgs[payload.indirectArgIndex * INDIRECT_ARGS_NUM_WORDS + 1], 1);
        }
    }
}

groupshared uint instanceCountCache[INSTANCE_COUNT_CACHE_SIZE];
groupshared uint partialSum[PERSISTENT_CULLING_GROUP_SIZE + 1];

void Scan(uint groupIndex, uint payloadCount)
{
    const uint batchSize = (payloadCount + PERSISTENT_CULLING_GROUP_SIZE - 1) / PERSISTENT_CULLING_GROUP_SIZE;
    const uint batchOffset = groupIndex * batchSize;
    uint size = min(batchSize, select(batchOffset >= payloadCount, 0, payloadCount - batchOffset));

    uint cnt = 0;
    for (int i = 0; i < size; i++)
    {
        uint tmp = instanceCountCache[batchOffset + i];
        instanceCountCache[batchOffset + i] = cnt;
        cnt += tmp;
    }

    partialSum[groupIndex] = cnt;

    // GroupMemoryBarrierWithGroupSync();
    //     if (groupIndex == 0)
    //     {
    //         cnt = 0;
    //         for (int i = 0; i < PERSISTENT_CULLING_GROUP_SIZE; i++)
    //         {
    //             uint tmp = partialSum[i];
    //             partialSum[i] = cnt;
    //             cnt += tmp;
    //         }

    //         instanceCountCache[payloadCount] = cnt;
    //     }
    // GroupMemoryBarrierWithGroupSync();
    
    // Blelloch Scan
    uint offset = 1;
    // build sum in place up the tree
    for (int d = PERSISTENT_CULLING_GROUP_SIZE >> 1; d > 0; d >>= 1)
    {
        GroupMemoryBarrierWithGroupSync();

        if (groupIndex < d)
        {
            int ai = offset * (2 * groupIndex + 1) - 1;
            int bi = offset * (2 * groupIndex + 2) - 1;

            partialSum[bi] += partialSum[ai];
        }

        offset = offset << 1;
    }

    if (groupIndex == 0) // clear the last element
    {
        instanceCountCache[payloadCount] = partialSum[PERSISTENT_CULLING_GROUP_SIZE - 1];
        partialSum[PERSISTENT_CULLING_GROUP_SIZE - 1] = 0;
    }

    offset = PERSISTENT_CULLING_GROUP_SIZE;
    for (int d = 1; d < PERSISTENT_CULLING_GROUP_SIZE; d *= 2) // traverse down tree & build scan
    {
        offset >>= 1;
        GroupMemoryBarrierWithGroupSync();
        if (groupIndex < d)
        {
            int ai = offset * (2 * groupIndex + 1) - 1;
            int bi = offset * (2 * groupIndex + 2) - 1;
            uint t = partialSum[bi];
            partialSum[bi] += partialSum[ai];
            partialSum[ai] = t;
        }
        GroupMemoryBarrierWithGroupSync();
    }

    cnt = partialSum[groupIndex];

    for (int i = 0; i < size; i++)
    {
        instanceCountCache[batchOffset + i] += cnt;
    }

    GroupMemoryBarrierWithGroupSync();
}

[numthreads(PERSISTENT_CULLING_GROUP_SIZE, 1, 1)]
void CullPerPageDrawUnits(uint dispatchThreadId : SV_DispatchThreadID, uint groupThreadID : SV_GroupThreadID, uint groupIndex : SV_GroupIndex, uint groupID: SV_GroupID)
{
    // Step0
    const uint batchSize = (_RangePayloadOffset + PERSISTENT_CULLING_GROUP_COUNT - 1) / PERSISTENT_CULLING_GROUP_COUNT;
    const uint batchOffset = batchSize * groupID;
    const uint batchEnd = min(batchOffset + batchSize, _RangePayloadOffset);

    const uint threadCount = PERSISTENT_CULLING_GROUP_SIZE;
    uint payloadIndex = batchOffset + groupIndex;
    while (payloadIndex < batchEnd)
    {
        ObjectPayloadData2 currentPayload = _ObjectPayloadDatas[payloadIndex];

        GroupObjectPayloadData groupPayload;
        groupPayload.objectCullingGUID = currentPayload.objectCullingGUID;
        groupPayload.objectIndex = currentPayload.objectIndex;
        groupPayload.indirectArgIndex = currentPayload.indirectArgIndex;
    
        InstanceCulling(groupPayload);
    
        payloadIndex += threadCount;
    }

    // Step1
    const int payloadBatchSize = (_RangePayloadCount + PERSISTENT_CULLING_GROUP_COUNT - 1) / PERSISTENT_CULLING_GROUP_COUNT;
    int payloadOffset = payloadBatchSize * groupID + _RangePayloadOffset;
    const int payloadEnd = min(payloadOffset + payloadBatchSize, _RangePayloadOffset + _RangePayloadCount);
    int payloadCount = payloadEnd - payloadOffset;

    if (payloadCount <= 0)
        return;

    while (payloadOffset < payloadEnd)  // payloadOffset += INSTANCE_COUNT_CACHE_SIZE;
    {
        payloadCount = min(INSTANCE_COUNT_CACHE_SIZE, payloadEnd - payloadOffset);
        payloadIndex = payloadOffset + groupIndex;
        const int currentPayloadEnd = payloadOffset + payloadCount;

        GroupMemoryBarrierWithGroupSync();

        // Load objectCount
        while (payloadIndex < currentPayloadEnd)
        {
            ObjectPayloadData2 currentPayload = _ObjectPayloadDatas[payloadIndex];

            instanceCountCache[payloadIndex - payloadOffset] = currentPayload.objectCount;

            payloadIndex += threadCount;
        }

        GroupMemoryBarrierWithGroupSync();

        // if (groupIndex == 0)
        // {
        //     uint cnt = 0;
        //     for (int i = 0; i < payloadCount; i++)
        //     {
        //         uint tmp = instanceCountCache[i];
        //         instanceCountCache[i] = cnt;
        //         cnt += tmp;
        //     }

        //     instanceCountCache[payloadCount] = cnt;
        // }

        // GroupMemoryBarrierWithGroupSync();

        // Exclusive prefix sum
        Scan(groupIndex, payloadCount);
        
        const uint instanceTot = instanceCountCache[payloadCount];
        const uint instanceBatch = (instanceTot + PERSISTENT_CULLING_GROUP_SIZE - 1) / PERSISTENT_CULLING_GROUP_SIZE;
        const uint instanceOffset = instanceBatch * groupIndex;

        if (instanceOffset < instanceTot)
        {
            const uint instanceCount = min(instanceBatch, instanceTot - instanceOffset);
            int instancePtr = 0;

            int left = 0;
            int right = payloadCount; 
            int found = payloadCount; 

            while (left < right) {
                int mid = left + (right - left) / 2;
                if (instanceCountCache[mid + 1] > instanceOffset) 
                {
                    found = mid;
                    right = mid;
                } else {
                    left = mid + 1;
                }
            }

            if (found < payloadCount) {
                instancePtr = instanceOffset - instanceCountCache[found];
                payloadIndex = found + payloadOffset;
            } else {
                instancePtr = 0;
                payloadIndex = -1;
            }
            
            // for (int i = 0; i < payloadCount; i++)
            // {
            //     if (instanceCountCache[i + 1] > instanceOffset)
            //     {
            //         instancePtr = instanceOffset - instanceCountCache[i];
            //         payloadIndex = i + payloadOffset;
            //         break;
            //     }
            // }

            if (payloadIndex >= 0)
            {
                uint cnt = 0;
                ObjectPayloadData2 currentPayload = _ObjectPayloadDatas[payloadIndex];

                while (true)
                {
                    GroupObjectPayloadData groupPayload;
                    groupPayload.indirectArgIndex = currentPayload.indirectArgIndex;
                    groupPayload.objectCullingGUID = currentPayload.objectCullingGUID + instancePtr;
                    groupPayload.objectIndex = currentPayload.objectIndex + instancePtr;
                    InstanceCulling(groupPayload);

                    instancePtr++;
                    cnt++;

                    if (cnt == instanceCount)
                        break;

                    if (instancePtr == currentPayload.objectCount)
                    {
                        instancePtr = 0;
                        payloadIndex++;
                        currentPayload = _ObjectPayloadDatas[payloadIndex];
                    }
                }
            }
        }

        payloadOffset += INSTANCE_COUNT_CACHE_SIZE;
    }

    if (_LargePayloadCount == 0)
        return;

    while (true)
    {
        GroupMemoryBarrierWithGroupSync();

        if (groupIndex == 0)
        {
            LockAcquire();

            uint objectPayloadIndexStart = _ObjectPayloadIndex;
            uint objectPayloadObjectOffsetStart = _ObjectPayloadObjectOffset;
            uint objectPayloadIndex = objectPayloadIndexStart;
            uint objectPayloadObjectOffset = objectPayloadObjectOffsetStart;

            uint groupPayloadDataIndex = 0;
            while (groupPayloadDataIndex < PERSISTENT_CULLING_INSTANCE_COUNT_PER_GROUP)
            {
                if (objectPayloadIndex >= _PayloadCount)
                    break;

                ObjectPayloadData2 currentPayload = _ObjectPayloadDatas[objectPayloadIndex];

                if (objectPayloadObjectOffset < currentPayload.objectCount)
                {
                    uint count = min(PERSISTENT_CULLING_INSTANCE_COUNT_PER_GROUP - groupPayloadDataIndex, currentPayload.objectCount - objectPayloadObjectOffset);
                    groupPayloadDataIndex += count;
                    objectPayloadObjectOffset += count;
                }

                if (objectPayloadObjectOffset >= currentPayload.objectCount)
                {
                    objectPayloadIndex++;
                    objectPayloadObjectOffset = 0;
                }
            }

            uint originValue;
            InterlockedExchange(_ObjectPayloadIndex, objectPayloadIndex, originValue);
            InterlockedExchange(_ObjectPayloadObjectOffset, objectPayloadObjectOffset, originValue);

            LockRelease();

            _GroupObjectPayloadIndex = objectPayloadIndexStart;
            _GroupObjectPayloadIndexOffset = objectPayloadObjectOffsetStart;
            _GroupObjectPayloadIndexEnd = objectPayloadIndex;
            _GroupObjectPayloadIndexOffsetEnd = objectPayloadObjectOffset;
        }

        GroupMemoryBarrierWithGroupSync();

        if (_GroupObjectPayloadIndex >= _PayloadCount)
            break;

        while (true)
        {            
            GroupMemoryBarrierWithGroupSync();

            uint payloadIndex = _GroupObjectPayloadIndex;
            if (payloadIndex > _GroupObjectPayloadIndexEnd || payloadIndex == _GroupObjectPayloadIndexEnd && _GroupObjectPayloadIndexOffset >= _GroupObjectPayloadIndexOffsetEnd)
                break;

            uint payloadObjectOffset = _GroupObjectPayloadIndexOffset + groupIndex;

            ObjectPayloadData2 currentPayload = _ObjectPayloadDatas[payloadIndex];

            GroupMemoryBarrierWithGroupSync();

            if (payloadIndex < _GroupObjectPayloadIndexEnd && payloadObjectOffset < currentPayload.objectCount || 
                payloadIndex == _GroupObjectPayloadIndexEnd && payloadObjectOffset < _GroupObjectPayloadIndexOffsetEnd)
            {
                GroupObjectPayloadData groupPayload;
                groupPayload.objectCullingGUID = currentPayload.objectCullingGUID + payloadObjectOffset;
                groupPayload.objectIndex = currentPayload.objectIndex + payloadObjectOffset;
                groupPayload.indirectArgIndex = currentPayload.indirectArgIndex;
            
                InstanceCulling(groupPayload);
            }

            if (groupIndex == 0)
            {
                _GroupObjectPayloadIndexOffset += PERSISTENT_CULLING_GROUP_SIZE;

                if (_GroupObjectPayloadIndexOffset >= currentPayload.objectCount)
                {
                    _GroupObjectPayloadIndex++;
                    _GroupObjectPayloadIndexOffset = 0;
                }
            }
        }
    }
}

StructuredBuffer<uint> _DrawIndirectArgs;
RWStructuredBuffer<uint> _OutOffsetBufferCount;
RWStructuredBuffer<uint> _OutObjectIndexOffsetBuffer;
RWStructuredBuffer<uint> _OutTempObjectIndexOffsetBuffer;
 
[numthreads(NUM_THREADS_PER_GROUP, 1, 1)]
void AllocateCommandInstanceOutputSpace(uint indirectArgIndex : SV_DispatchThreadID)
{
    if (indirectArgIndex < _IndirectArgCount)
    {
        uint objectCommandCount = _DrawIndirectArgs[indirectArgIndex * INDIRECT_ARGS_NUM_WORDS + 1];
        uint objectCommandOffset = 0u;
        if (objectCommandCount > 0u)
        {
            InterlockedAdd(_OutOffsetBufferCount[0], objectCommandCount, objectCommandOffset);
        }
        _OutObjectIndexOffsetBuffer[indirectArgIndex] = objectCommandOffset;
        _OutTempObjectIndexOffsetBuffer[indirectArgIndex] = objectCommandOffset;
    }
}

StructuredBuffer<VisibleObjectCommand> _VisibleObjectCommands;
StructuredBuffer<uint> _VisibleObjectCommandCount;
RWStructuredBuffer<uint> _OutObjectIndexBuffer;

[numthreads(NUM_THREADS_PER_GROUP, 1, 1)]
void OutputCommandInstanceLists(uint visibleObjectCommandIndex : SV_DispatchThreadID)
{
    uint visibleObjectCommandCount = _VisibleObjectCommandCount[0];

    if (visibleObjectCommandIndex < visibleObjectCommandCount)
    {
        VisibleObjectCommand visibleObjectCommand = _VisibleObjectCommands[visibleObjectCommandIndex];

        uint objectIndexOutputOffset = 0u;
        InterlockedAdd(_OutTempObjectIndexOffsetBuffer[visibleObjectCommand.indirectArgIndex], 1u, objectIndexOutputOffset);

        _OutObjectIndexBuffer[objectIndexOutputOffset] = visibleObjectCommand.objectIndex;
    }
}