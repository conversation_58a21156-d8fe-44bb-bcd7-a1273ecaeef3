//#pragma enable debug_symbol

#pragma compute CSPrefilterDepths16x16
#pragma compute CSG<PERSON>OLow
#pragma compute CSGTAOMedium
#pragma compute CSG<PERSON><PERSON>igh
#pragma compute CSGTAOUltra
#pragma compute CSGTAOUltraX
#pragma compute CSDenoisePass
#pragma compute CSDenoiseLastPass

#include "../../ShaderLibrary/Common.hlsl"
#include "../../ShaderLibrary/Packing.hlsl"
#include "../../Material/Lit/LitCommonStruct.hlsl"
#include "../../Material/NormalBuffer.hlsl"

#define GTAO_PI             3.1415926535897932384626433832795
#define GTAO_PI_HALF        1.5707963267948966192313216916398
#define GTAO_RCP_PI         0.3183098862

#define GTAO_DEPTH_MIP_LEVELS                    5             // this one is hard-coded to 5 for now
#define GTAO_NUMTHREADS_X                        8             // these can be changed
#define GTAO_NUMTHREADS_Y                        8             // these can be changed

#define GTAO_USE_HALF_FLOAT_PRECISION            false
#if GTAO_USE_HALF_FLOAT_PRECISION
#define GTAO_VIEW_SPACE_DEPTH_MAX                65504
#else
#define GTAO_VIEW_SPACE_DEPTH_MAX                3.402823466e+38
#endif

#define MULTIBOUNCE_CUBIC_POLYNOMIAL_FIT         true

#define GTAO_CENTI_VIEW_SPACE_DEPTH_MAX_RCP      100.0 / GTAO_VIEW_SPACE_DEPTH_MAX

SHADER_CONST(bool, GTAO_COMPUTE_BENT_NORMALS, true);
SHADER_CONST(bool, GTAO_COMPUTE_MULTI_BOUNCE, true);
SHADER_CONST(bool, GTAO_USE_DEFAULT_CONSTANTS, true);
SHADER_CONST(bool, ENABLE_SLICE_NOISE, false);
SHADER_CONST(bool, ENABLE_STEP_NOISE, true);

// some constants reduce performance if provided as dynamic values; if these constants are not required to be dynamic and they match default values, 
// set GTAO_USE_DEFAULT_CONSTANTS and the code will compile into a more efficient shader
#define GTAO_DEFAULT_FALLOFF_RANGE                   3.0f      // distant samples contribute less
#define GTAO_DEFAULT_SAMPLE_DISTRIBUTION_POWER       2.0f      // small crevices more important than big surfaces
#define GTAO_DEFAULT_THIN_OCCLUDER_COMPENSATION      0.0f      // the new 'thickness heuristic' approach
#define GTAO_DEFAULT_VISIBILITY_VALUE_POWER          2.2f      // modifies the visibility value using power function - this allows some of the above heuristics to do different things
#define GTAO_DEFAULT_INTENSITY                       1.0f      // regulates the final AO value linearly
#define GTAO_DEFAULT_DEPTH_MIP_SAMPLING_OFFSET       0.0f      // main trade-off between performance (memory bandwidth) and quality (temporal stability is the first affected, thin objects next)

#define GTAO_OCCLUSION_TERM_SCALE                    1.5f      // for packing in UNORM (because raw, pre-denoised occlusion term can overshoot 1 but will later average out to 1)

// #define GTAO_COMPUTE_BENT_NORMALS true

cbuffer GTAOConstantBuffer
{
    float4x4                ViewMatrix;
    float4x4                InverseViewMatrix;

    int2                    ViewportSize;
    float2                  ViewportPixelSize;                  // .zw == 1.0 / ViewportSize.xy

    float2                  DepthUnpackConsts;

    float2                  NDCToViewMul;
    float2                  NDCToViewAdd;

    float2                  NDCToViewMul_x_PixelSize;
    float                   AbsoluteSplitDepthDifferenceRcp;
    float                   EffectRadius;                       // world (viewspace) maximum size of the shadow
    float                   EffectFalloffRange;

    float                   VisibilityValuePower;
    float                   Intensity;
    float                   DenoiseBlurBeta;

    float                   SampleDistributionPower;
    float                   ThinOccluderCompensation;
    float                   DepthMIPSamplingOffset;
    int                     NoiseIndex;                         // frameIndex % 64 if using TAA or 0 otherwise
}

SamplerState ce_Sampler_Point : register(space0);
SamplerState ce_Sampler_Clamp : register(space0);

// input output textures for the first pass (GTAO_PrefilterDepths16x16)
Texture2D<float>            _srcRawDepth           : register(space0);   // source depth buffer data (in NDC space in DirectX)
RWTexture2D<float>          _outWorkingDepthMIP0   : register(space0);   // output viewspace depth MIP (these are views into _srcWorkingDepth MIP levels)
RWTexture2D<float>          _outWorkingDepthMIP1   : register(space0);   // output viewspace depth MIP (these are views into _srcWorkingDepth MIP levels)
RWTexture2D<float>          _outWorkingDepthMIP2   : register(space0);   // output viewspace depth MIP (these are views into _srcWorkingDepth MIP levels)
RWTexture2D<float>          _outWorkingDepthMIP3   : register(space0);   // output viewspace depth MIP (these are views into _srcWorkingDepth MIP levels)
RWTexture2D<float>          _outWorkingDepthMIP4   : register(space0);   // output viewspace depth MIP (these are views into _srcWorkingDepth MIP levels)

// input output textures for the second pass (GTAO_MainPass)
Buffer<uint>                _HilbertLUT             : register(space0);   // hilbert lookup table  (if any)
Texture2D<float4>           _GBuffer0               : register(space0);   // input, diffuse color in x y z
Texture2D<float4>           _GBuffer1               : register(space0);   // input, world space normals in x y z
Texture2D<float4>           _GBuffer2               : register(space0);   // input, material ID in w
Texture2D<float4>           _GBuffer3               : register(space0);   // input, subsurface color in x y z
Texture2D<float>            _srcWorkingDepth        : register(space0);   // viewspace depth with MIPs, output by GTAO_PrefilterDepths16x16 and consumed by GTAO_MainPass
RWTexture2D<float4>         _outWorkingAOTerm       : register(space0);   // output AO term
RWTexture2D<float4>         _outWorkingBentNormal   : register(space0);   // output bent normal
RWTexture2D<float>          _outWorkingEdges        : register(space0);   // output depth-based edges used by the denoiser

// input output textures for the third pass (GTAO_Denoise)
Texture2D<float4>           _srcWorkingAOTerm       : register(space0);   // coming from previous pass
Texture2D<float>            _srcWorkingEdges        : register(space0);   // coming from previous pass
Texture2D<float4>           _srcWorkingBentNormal   : register(space0);   // coming from previous pass
RWTexture2D<float4>         _outFinalAOTerm         : register(space0);   // final AO term - just 'visibility' or 'visibility + bent normals'

float4 GTAO_R8G8B8A8_UNORM_to_FLOAT4( uint packedInput )
{
    float4 unpackedOutput;
    unpackedOutput.x = (float)( packedInput & 0x000000ff ) / (float)255;
    unpackedOutput.y = (float)( ( ( packedInput >> 8 ) & 0x000000ff ) ) / (float)255;
    unpackedOutput.z = (float)( ( ( packedInput >> 16 ) & 0x000000ff ) ) / (float)255;
    unpackedOutput.w = (float)( packedInput >> 24 ) / (float)255;
    return unpackedOutput;
}

uint GTAO_FLOAT4_to_R8G8B8A8_UNORM( float4 unpackedInput )
{
    return (( uint( saturate( unpackedInput.x ) * (float)255 + (float)0.5 ) ) |
            ( uint( saturate( unpackedInput.y ) * (float)255 + (float)0.5 ) << 8 ) |
            ( uint( saturate( unpackedInput.z ) * (float)255 + (float)0.5 ) << 16 ) |
            ( uint( saturate( unpackedInput.w ) * (float)255 + (float)0.5 ) << 24 ) );
}

// This is also a good place to do non-linear depth conversion for cases where one wants the 'radius' (effectively the threshold between near-field and far-field GI), 
// is required to be non-linear (i.e. very large outdoors environments).
float GTAO_ClampDepth( float depth )
{
    return (float)clamp( depth, 0.0, GTAO_VIEW_SPACE_DEPTH_MAX );
}

float GTAO_ScreenSpaceToViewSpaceDepth( const float screenDepth )
{
    float depthLinearizeMul = DepthUnpackConsts.x;
    float depthLinearizeAdd = DepthUnpackConsts.y;
    // Optimised version of "-cameraClipNear / (cameraClipFar - projDepth * (cameraClipFar - cameraClipNear)) * cameraClipFar"
    return depthLinearizeMul / (depthLinearizeAdd - screenDepth);
}

float4 GTAO_ScreenSpaceToViewSpaceDepth( const float4 screenDepth )
{
    float4 depthLinearizeMul = DepthUnpackConsts.xxxx;
    float4 depthLinearizeAdd = DepthUnpackConsts.yyyy;
    // Optimised version of "-cameraClipNear / (cameraClipFar - projDepth * (cameraClipFar - cameraClipNear)) * cameraClipFar"
    return depthLinearizeMul / (depthLinearizeAdd - screenDepth);
}

// weighted average depth filter
float GTAO_DepthMIPFilter( float depth0, float depth1, float depth2, float depth3 )
{
    float maxDepth = max( max( depth0, depth1 ), max( depth2, depth3 ) );

    const float depthRangeScaleFactor = 0.75; // found empirically :)
    float effectRadius = depthRangeScaleFactor * EffectRadius;
    float falloffRange;
    if (GTAO_USE_DEFAULT_CONSTANTS)
    {
        falloffRange                = GTAO_DEFAULT_FALLOFF_RANGE * effectRadius;
    }
    else
    {
        falloffRange                = EffectFalloffRange * effectRadius;
    }
    const float falloffFrom       = effectRadius * (1.0 - EffectFalloffRange);
    // fadeout precompute optimisation
    const float falloffMul        = -1.0 / falloffRange;
    const float falloffAdd        = falloffFrom / falloffRange + 1.0;

    float weight0 = saturate( (maxDepth-depth0) * falloffMul + falloffAdd );
    float weight1 = saturate( (maxDepth-depth1) * falloffMul + falloffAdd );
    float weight2 = saturate( (maxDepth-depth2) * falloffMul + falloffAdd );
    float weight3 = saturate( (maxDepth-depth3) * falloffMul + falloffAdd );

    float weightSum = weight0 + weight1 + weight2 + weight3;
    return (weight0 * depth0 + weight1 * depth1 + weight2 * depth2 + weight3 * depth3) / weightSum;
}

groupshared float g_scratchDepths[8][8];
void GTAO_PrefilterDepths16x16( uint2 dispatchThreadID /*: SV_DispatchThreadID*/, uint2 groupThreadID /*: SV_GroupThreadID*/, Texture2D<float> sourceNDCDepth, SamplerState depthSampler, RWTexture2D<float> outDepth0, RWTexture2D<float> outDepth1, RWTexture2D<float> outDepth2, RWTexture2D<float> outDepth3, RWTexture2D<float> outDepth4 )
{
    // MIP 0
    const uint2 baseCoord = dispatchThreadID;
    const uint2 pixCoord = baseCoord * 2;
    float4 depths4 = sourceNDCDepth.GatherRed( depthSampler, float2( pixCoord * ViewportPixelSize ), int2(1,1) );
    float depth0 = GTAO_ClampDepth( GTAO_ScreenSpaceToViewSpaceDepth( depths4.w ) );
    float depth1 = GTAO_ClampDepth( GTAO_ScreenSpaceToViewSpaceDepth( depths4.z ) );
    float depth2 = GTAO_ClampDepth( GTAO_ScreenSpaceToViewSpaceDepth( depths4.x ) );
    float depth3 = GTAO_ClampDepth( GTAO_ScreenSpaceToViewSpaceDepth( depths4.y ) );
    outDepth0[ pixCoord + uint2(0, 0) ] = (float)depth0;
    outDepth0[ pixCoord + uint2(1, 0) ] = (float)depth1;
    outDepth0[ pixCoord + uint2(0, 1) ] = (float)depth2;
    outDepth0[ pixCoord + uint2(1, 1) ] = (float)depth3;
    
    // outDepth0[pixCoord] = sourceNDCDepth[pixCoord];

    // MIP 1
    float dm1 = GTAO_DepthMIPFilter( depth0, depth1, depth2, depth3 );
    outDepth1[ baseCoord ] = (float)dm1;
    g_scratchDepths[ groupThreadID.x ][ groupThreadID.y ] = dm1;

    GroupMemoryBarrierWithGroupSync( );

    // MIP 2
    [branch]
    if( all( ( groupThreadID.xy % 2.xx ) == 0 ) )
    {
        float inTL = g_scratchDepths[groupThreadID.x+0][groupThreadID.y+0];
        float inTR = g_scratchDepths[groupThreadID.x+1][groupThreadID.y+0];
        float inBL = g_scratchDepths[groupThreadID.x+0][groupThreadID.y+1];
        float inBR = g_scratchDepths[groupThreadID.x+1][groupThreadID.y+1];

        float dm2 = GTAO_DepthMIPFilter( inTL, inTR, inBL, inBR );
        outDepth2[ baseCoord / 2 ] = (float)dm2;
        g_scratchDepths[ groupThreadID.x ][ groupThreadID.y ] = dm2;
    }

    GroupMemoryBarrierWithGroupSync( );

    // MIP 3
    [branch]
    if( all( ( groupThreadID.xy % 4.xx ) == 0 ) )
    {
        float inTL = g_scratchDepths[groupThreadID.x+0][groupThreadID.y+0];
        float inTR = g_scratchDepths[groupThreadID.x+2][groupThreadID.y+0];
        float inBL = g_scratchDepths[groupThreadID.x+0][groupThreadID.y+2];
        float inBR = g_scratchDepths[groupThreadID.x+2][groupThreadID.y+2];

        float dm3 = GTAO_DepthMIPFilter( inTL, inTR, inBL, inBR );
        outDepth3[ baseCoord / 4 ] = (float)dm3;
        g_scratchDepths[ groupThreadID.x ][ groupThreadID.y ] = dm3;
    }

    GroupMemoryBarrierWithGroupSync( );

    // MIP 4
    [branch]
    if( all( ( groupThreadID.xy % 8.xx ) == 0 ) )
    {
        float inTL = g_scratchDepths[groupThreadID.x+0][groupThreadID.y+0];
        float inTR = g_scratchDepths[groupThreadID.x+4][groupThreadID.y+0];
        float inBL = g_scratchDepths[groupThreadID.x+0][groupThreadID.y+4];
        float inBR = g_scratchDepths[groupThreadID.x+4][groupThreadID.y+4];

        float dm4 = GTAO_DepthMIPFilter( inTL, inTR, inBL, inBR );
        outDepth4[ baseCoord / 8 ] = (float)dm4;
        //g_scratchDepths[ groupThreadID.x ][ groupThreadID.y ] = dm4;
    }
}

// Inputs are screen XY and viewspace depth, output is viewspace position
float3 GTAO_ComputeViewspacePosition( const float2 screenPos, const float viewspaceDepth )
{
    float3 ret;
    ret.xy = (NDCToViewMul * screenPos.xy + NDCToViewAdd) * viewspaceDepth;
    ret.z = viewspaceDepth;
    return ret;
}

float4 GTAO_CalculateEdges( const float centerZ, const float leftZ, const float rightZ, const float topZ, const float bottomZ )
{
    float4 edgesLRTB = float4( leftZ, rightZ, topZ, bottomZ ) - (float)centerZ;

    float slopeLR = (edgesLRTB.y - edgesLRTB.x) * 0.5;
    float slopeTB = (edgesLRTB.w - edgesLRTB.z) * 0.5;
    float4 edgesLRTBSlopeAdjusted = edgesLRTB + float4( slopeLR, -slopeLR, slopeTB, -slopeTB );
    edgesLRTB = min( abs( edgesLRTB ), abs( edgesLRTBSlopeAdjusted ) );
    return float4(saturate( ( 1.25 - edgesLRTB / (centerZ * 0.011) ) ));
}

// packing/unpacking for edges; 2 bits per edge mean 4 gradient values (0, 0.33, 0.66, 1) for smoother transitions!
float GTAO_PackEdges( float4 edgesLRTB )
{
    // integer version:
    // edgesLRTB = saturate(edgesLRTB) * 2.9.xxxx + 0.5.xxxx;
    // return (((uint)edgesLRTB.x) << 6) + (((uint)edgesLRTB.y) << 4) + (((uint)edgesLRTB.z) << 2) + (((uint)edgesLRTB.w));
    // 
    // optimized, should be same as above
    edgesLRTB = round( saturate( edgesLRTB ) * 2.9 );
    return dot( edgesLRTB, float4( 64.0 / 255.0, 16.0 / 255.0, 4.0 / 255.0, 1.0 / 255.0 ) ) ;
}

float3 GTAO_CalculateNormal( const float4 edgesLRTB, float3 pixCenterPos, float3 pixLPos, float3 pixRPos, float3 pixTPos, float3 pixBPos )
{
    // Get this pixel's viewspace normal
    float4 acceptedNormals  = saturate( float4( edgesLRTB.x*edgesLRTB.z, edgesLRTB.z*edgesLRTB.y, edgesLRTB.y*edgesLRTB.w, edgesLRTB.w*edgesLRTB.x ) + 0.01 );

    pixLPos = normalize(pixLPos - pixCenterPos);
    pixRPos = normalize(pixRPos - pixCenterPos);
    pixTPos = normalize(pixTPos - pixCenterPos);
    pixBPos = normalize(pixBPos - pixCenterPos);

    float3 pixelNormal =  acceptedNormals.x * cross( pixLPos, pixTPos ) +
                        + acceptedNormals.y * cross( pixTPos, pixRPos ) +
                        + acceptedNormals.z * cross( pixRPos, pixBPos ) +
                        + acceptedNormals.w * cross( pixBPos, pixLPos );
    pixelNormal = normalize( pixelNormal );

    return pixelNormal;
}

#ifdef GTAO_SHOW_DEBUG_VIZ
float4 DbgGetSliceColor(int slice, int sliceCount, bool mirror)
{
    float red = (float)slice / (float)sliceCount; float green = 0.01; float blue = 1.0 - (float)slice / (float)sliceCount;
    return (mirror)?(float4(blue, green, red, 0.9)):(float4(red, green, blue, 0.9));
}
#endif

// http://h14s.p5r.org/2012/09/0x5f3759df.html, [Drobot2014a] Low Level Optimizations for GCN, https://blog.selfshadow.com/publications/s2016-shading-course/activision/s2016_pbs_activision_occlusion.pdf slide 63
float GTAO_FastSqrt( float x )
{
    return (float)(asfloat( 0x1fbd1df5 + ( asint( x ) >> 1 ) ));
}
// input [-1, 1] and output [0, PI], from https://seblagarde.wordpress.com/2014/12/01/inverse-trigonometric-functions-gpu-optimization-for-amd-gcn-architecture/
float GTAO_FastACos( float inX )
{ 
    const float PI = 3.141593;
    const float HALF_PI = 1.570796;
    float x = abs(inX); 
    float res = -0.156583 * x + HALF_PI; 
    res *= GTAO_FastSqrt(1.0 - x); 
    return (inX >= 0) ? res : PI - res; 
}

uint GTAO_EncodeVisibilityBentNormal( float visibility, float3 bentNormal )
{
    return GTAO_FLOAT4_to_R8G8B8A8_UNORM( float4( bentNormal * 0.5 + 0.5, visibility ) );
}

void GTAO_DecodeVisibilityBentNormal( const uint packedValue, out float visibility, out float3 bentNormal )
{
    float4 decoded = GTAO_R8G8B8A8_UNORM_to_FLOAT4( packedValue );
    bentNormal = decoded.xyz * 2.0.xxx - 1.0.xxx;   // could normalize - don't want to since it's done so many times, better to do it at the final step only
    visibility = decoded.w;
}

void GTAO_OutputWorkingTerm( const uint2 pixCoord, float4 aoData, float3 bentNormal, RWTexture2D<float4> outWorkingAOTerm )
{
    aoData = aoData / float(GTAO_OCCLUSION_TERM_SCALE);

    if (GTAO_COMPUTE_BENT_NORMALS)
    {
        _outWorkingBentNormal[pixCoord].xyz = bentNormal;
    }
    outWorkingAOTerm[pixCoord] = aoData;
}

// "Efficiently building a matrix to rotate one vector to another"
// http://cs.brown.edu/research/pubs/pdfs/1999/Moller-1999-EBA.pdf / https://dl.acm.org/doi/10.1080/10867651.1999.10487509
// (using https://github.com/assimp/assimp/blob/master/include/assimp/matrix3x3.inl#L275 as a code reference as it seems to be best)
float3x3 GTAO_RotFromToMatrix( float3 from, float3 to )
{
    const float e       = dot(from, to);
    const float f       = abs(e); //(e < 0)? -e:e;

    // WARNING: This has not been tested/worked through, especially not for 16bit floats; seems to work in our special use case (from is always {0, 0, -1}) but wouldn't use it in general
    if( f > float( 1.0 - 0.0003 ) )
        return float3x3( 1, 0, 0, 0, 1, 0, 0, 0, 1 );

    const float3 v      = cross( from, to );
    /* ... use this hand optimized version (9 mults less) */
    const float h       = (1.0)/(1.0 + e);      /* optimization by Gottfried Chen */
    const float hvx     = h * v.x;
    const float hvz     = h * v.z;
    const float hvxy    = hvx * v.y;
    const float hvxz    = hvx * v.z;
    const float hvyz    = hvz * v.y;

    float3x3 mtx;
    mtx[0][0] = e + hvx * v.x;
    mtx[0][1] = hvxy - v.z;
    mtx[0][2] = hvxz + v.y;

    mtx[1][0] = hvxy + v.z;
    mtx[1][1] = e + h * v.y * v.y;
    mtx[1][2] = hvyz - v.x;

    mtx[2][0] = hvxz - v.y;
    mtx[2][1] = hvyz + v.x;
    mtx[2][2] = e + hvz * v.z;

    return mtx;
}

// #define GTAO_GENERATE_NORMALS_INPLACE

void GTAO_MainPass( const uint2 pixCoord, float sliceCount, float stepsPerSlice, const float2 localNoise, float3 worldspaceNormal, 
    Texture2D<float> sourceDepth, SamplerState depthSampler, RWTexture2D<float4> outWorkingAOTerm, RWTexture2D<float> outWorkingEdges )
{
    const uint materialType = UnpackByte(_GBuffer2[pixCoord].w);
    if (materialType == MaterialType_Unlit)
    {
        outWorkingAOTerm[pixCoord] = 1.0.xxxx;
        return;
    }

    float4 depthUL = sourceDepth.GatherRed( depthSampler, float2( pixCoord * ViewportPixelSize ) );
    float depthCenter = depthUL.y;

    if (depthCenter == 0.0)
    {
        outWorkingAOTerm[pixCoord] = 1.0.xxxx;
        return;
    }

    float4 depthBR = sourceDepth.GatherRed( depthSampler, float2( pixCoord * ViewportPixelSize ), int2( 1, 1 ) );

    float4 valuesUL   = GTAO_ScreenSpaceToViewSpaceDepth(depthUL);
    float4 valuesBR   = GTAO_ScreenSpaceToViewSpaceDepth(depthBR);

    // viewspace Z at the center
    float viewspaceZ  = valuesUL.y;
    // viewspace Zs left top right bottom
    const float pixLZ = valuesUL.x;
    const float pixTZ = valuesUL.z;
    const float pixRZ = valuesBR.z;
    const float pixBZ = valuesBR.x;

    float4 edgesLRTB  = GTAO_CalculateEdges( (float)viewspaceZ, (float)pixLZ, (float)pixRZ, (float)pixTZ, (float)pixBZ );
    float _edge = GTAO_PackEdges(edgesLRTB);
    outWorkingEdges[pixCoord] = _edge;

    float3 viewspaceNormal;
    float2 normalizedScreenPos = (pixCoord + 0.5.xx) * ViewportPixelSize;
	// Generating screen space normals in-place is faster than generating normals in a separate pass but requires
	// use of 32bit depth buffer (16bit works but visibly degrades quality) which in turn slows everything down. So to
	// reduce complexity and allow for screen space normal reuse by other effects, we've pulled it out into a separate
	// pass.
	// However, we leave this code in, in case anyone has a use-case where it fits better.
#ifdef GTAO_GENERATE_NORMALS_INPLACE
    float3 CENTER   = GTAO_ComputeViewspacePosition( normalizedScreenPos, viewspaceZ );
    float3 LEFT     = GTAO_ComputeViewspacePosition( normalizedScreenPos + float2(-1,  0) * ViewportPixelSize, pixLZ );
    float3 RIGHT    = GTAO_ComputeViewspacePosition( normalizedScreenPos + float2( 1,  0) * ViewportPixelSize, pixRZ );
    float3 TOP      = GTAO_ComputeViewspacePosition( normalizedScreenPos + float2( 0, -1) * ViewportPixelSize, pixTZ );
    float3 BOTTOM   = GTAO_ComputeViewspacePosition( normalizedScreenPos + float2( 0,  1) * ViewportPixelSize, pixBZ );
    viewspaceNormal = (float3)GTAO_CalculateNormal( edgesLRTB, CENTER, LEFT, RIGHT, TOP, BOTTOM );
#else
    viewspaceNormal = mul(ViewMatrix, float4(worldspaceNormal, 0.0)).xyz;
#endif

    // Move center pixel slightly towards camera to avoid imprecision artifacts due to depth buffer imprecision; offset depends on depth texture format used
#ifdef GTAO_FP32_DEPTHS
    viewspaceZ *= 0.99999;     // this is good for FP32 depth buffer
#else
    viewspaceZ *= 0.99920;     // this is good for FP16 depth buffer
#endif

    const float3 pixCenterPos   = GTAO_ComputeViewspacePosition( normalizedScreenPos, viewspaceZ );
    const float3 viewVec      = (float3)normalize(-pixCenterPos);
    
    // prevents normals that are facing away from the view vector - xeGTAO struggles with extreme cases, but in Vanilla it seems rare so it's disabled by default
    // viewspaceNormal = normalize( viewspaceNormal + max( 0, -dot( viewspaceNormal, viewVec ) ) * viewVec );

#ifdef GTAO_SHOW_NORMALS
    g_outputDbgImage[pixCoord] = float4( DisplayNormalSRGB( viewspaceNormal.xyz ), 1 );
#endif

#ifdef GTAO_SHOW_EDGES
    g_outputDbgImage[pixCoord] = 1.0 - float4( edgesLRTB.x, edgesLRTB.y * 0.5 + edgesLRTB.w * 0.5, edgesLRTB.z, 1.0 );
#endif

    float absoluteSplitDepthDifferenceRcp;
    float effectRadius = EffectRadius;
    float sampleDistributionPower;
    float intensity;
    float thinOccluderCompensation;
    float falloffRange;
    float falloffFrom;
    if (GTAO_USE_DEFAULT_CONSTANTS)
    {
        absoluteSplitDepthDifferenceRcp = GTAO_CENTI_VIEW_SPACE_DEPTH_MAX_RCP;
        sampleDistributionPower   = GTAO_DEFAULT_SAMPLE_DISTRIBUTION_POWER;
        intensity                 = GTAO_DEFAULT_INTENSITY;
        thinOccluderCompensation  = GTAO_DEFAULT_THIN_OCCLUDER_COMPENSATION;
        falloffRange              = GTAO_DEFAULT_FALLOFF_RANGE * effectRadius;
        falloffFrom               = effectRadius * (1.0 - GTAO_DEFAULT_FALLOFF_RANGE);
    }
    else
    {
        absoluteSplitDepthDifferenceRcp = AbsoluteSplitDepthDifferenceRcp;
        sampleDistributionPower   = SampleDistributionPower;
        intensity                 = Intensity;
        thinOccluderCompensation  = ThinOccluderCompensation;
        falloffRange              = EffectFalloffRange * effectRadius;
        falloffFrom               = effectRadius * (1.0 - EffectFalloffRange);
    }

    // fadeout precompute optimisation
    const float falloffMul        = -1.0 / falloffRange;
    const float falloffAdd        = falloffFrom / falloffRange + 1.0;

    float visibility = 0;
    float4 aoData;
    float3 bentNormal;
    if (GTAO_COMPUTE_BENT_NORMALS)
    {
        bentNormal = 0.xxx;
    }
    else
    {
        bentNormal = worldspaceNormal;
    }

#ifdef GTAO_SHOW_DEBUG_VIZ
    float3 dbgWorldPos          = mul(g_globals.ViewInv, float4(pixCenterPos, 1)).xyz;
#endif

    // see "Algorithm 1" in https://www.activision.com/cdn/research/Practical_Real_Time_Strategies_for_Accurate_Indirect_Occlusion_NEW%20VERSION_COLOR.pdf
    {
        const float noiseSlice  = (float)localNoise.x;
        const float noiseSample = (float)localNoise.y;

        // quality settings / tweaks / hacks
        const float pixelTooCloseThreshold  = 1.3;      // if the offset is under approx pixel size (pixelTooCloseThreshold), push it out to the minimum distance

        // approx viewspace pixel size at pixCoord; approximation of NDCToViewspace( normalizedScreenPos.xy + ViewportPixelSize.xy, pixCenterPos.z ).xy - pixCenterPos.xy;
        const float pixelDirRBViewspaceSizeAtCenterZ = viewspaceZ * NDCToViewMul_x_PixelSize.x;

        float screenspaceRadius = effectRadius / pixelDirRBViewspaceSizeAtCenterZ;

        // fade out for small screen radii 
        visibility += saturate((10 - screenspaceRadius)/100)*0.5;

#if 0   // sensible early-out for even more performance; disabled because not yet tested
        [branch]
        if( screenspaceRadius < pixelTooCloseThreshold )
        {
            GTAO_OutputWorkingTerm( pixCoord, 1.0.xxxx, Convert_minus1to0_To_0to1(worldspaceNormal), outWorkingAOTerm );
            return;
        }
#endif

#ifdef GTAO_SHOW_DEBUG_VIZ
        [branch] if (IsUnderCursorRange(pixCoord, int2(1, 1)))
        {
            float3 dbgWorldNorm     = mul((float3x3)g_globals.ViewInv, viewspaceNormal).xyz;
            float3 dbgWorldViewVec  = mul((float3x3)g_globals.ViewInv, viewVec).xyz;
            //DebugDraw3DArrow(dbgWorldPos, dbgWorldPos + 0.5 * dbgWorldViewVec, 0.02, float4(0, 1, 0, 0.95));
            //DebugDraw2DCircle(pixCoord, screenspaceRadius, float4(1, 0, 0.2, 1));
            DebugDraw3DSphere(dbgWorldPos, effectRadius, float4(1, 0.2, 0, 0.1));
            //DebugDraw3DText(dbgWorldPos, float2(0, 0), float4(0.6, 0.3, 0.3, 1), float4( pixelDirRBViewspaceSizeAtCenterZ.xy, 0, screenspaceRadius) );
        }
#endif

        float effectRadiusSqr = effectRadius * effectRadius;
        
        //[unroll]
        for( float slice = 0; slice < sliceCount; slice++ )
        {
            float sliceK = (noiseSlice + slice) / sliceCount;
            // lines 5, 6 from the paper
            float phi = sliceK * GTAO_PI;
            float cosPhi = cos(phi);
            float sinPhi = sin(phi);
            float2 omega = float2(cosPhi, -sinPhi);       //float2 on omega causes issues with big radii

            // convert to screen units (pixels) for later use
            omega *= screenspaceRadius;

            // line 8 from the paper
            const float3 directionVec = float3(cosPhi, sinPhi, 0);

            // line 9 from the paper
            const float3 orthoDirectionVec = directionVec - (dot(directionVec, viewVec) * viewVec);

            // line 10 from the paper
            //axisVec is orthogonal to directionVec and viewVec, used to define projectedNormal
            const float3 axisVec = normalize( cross(orthoDirectionVec, viewVec) );

            // alternative line 9 from the paper
            // float3 orthoDirectionVec = cross( viewVec, axisVec );

            // line 11 from the paper
            float3 projectedNormalVec = viewspaceNormal - axisVec * dot(viewspaceNormal, axisVec);

            // line 13 from the paper
            float signNorm = (float)sign( dot( orthoDirectionVec, projectedNormalVec ) );

            // line 14 from the paper
            float projectedNormalVecLength = length(projectedNormalVec);
            float cosNorm = (float)saturate(dot(projectedNormalVec, viewVec) / projectedNormalVecLength); // equals to 'dot(normalize(projectedNormalVec), viewVec)'

            // line 15 from the paper
            // xiaoyige: the paper's implimentation: float n = signNorm * GTAO_FastACos(cosNorm); , inputing signed n into the final intigration seems wrong
            float n = signNorm * GTAO_FastACos(cosNorm);
            // float n = signNorm * acos(cosNorm);

            // this is a lower weight target; not using -1 as in the original paper because it is under horizon, so a 'weight' has different meaning based on the normal
            const float lowHorizonCos0  = cos(n + GTAO_PI_HALF);
            const float lowHorizonCos1  = cos(n - GTAO_PI_HALF);

            // lines 17, 18 from the paper, manually unrolled the 'side' loop
            float horizonCos0           = lowHorizonCos0; //-1;
            float horizonCos1           = lowHorizonCos1; //-1;

            [unroll]
            for( float step = 1; step <= stepsPerSlice; step++ )
            {
                // R1 sequence (http://extremelearning.com.au/unreasonable-effectiveness-of-quasirandom-sequences/)
                const float stepBaseNoise = float(slice + step * stepsPerSlice) * 0.6180339887498948482; // <- this should unroll
                float stepNoise = frac(noiseSample + stepBaseNoise);

                // approx line 20 from the paper, with added noise
                // float s = (step + stepNoise) / (stepsPerSlice); // + (float2)1e-6f);
                float s = (step + stepNoise) / stepsPerSlice;

                // additional distribution modifier
                s       = (float)pow( s, (float)sampleDistributionPower );

                // approx lines 21-22 from the paper, unrolled
                float2 sampleOffset = s * omega;

                float sampleOffsetLength = length( sampleOffset );

                // note: when sampling, using point_point_point or point_point_linear sampler works, but linear_linear_linear will cause unwanted interpolation between neighbouring depth values on the same MIP level!
                /*float mipLevel;
                if (GTAO_USE_DEFAULT_CONSTANTS)
                {
                    mipLevel    = clamp( log2( sampleOffsetLength ) - GTAO_DEFAULT_DEPTH_MIP_SAMPLING_OFFSET, 0, GTAO_DEPTH_MIP_LEVELS );
                }
                else
                {
                    mipLevel    = clamp( log2( sampleOffsetLength ) - DepthMIPSamplingOffset, 0, GTAO_DEPTH_MIP_LEVELS );
                }*/

                // Snap to pixel center (more correct direction math, avoids artifacts due to sampling pos not matching depth texel center - messes up slope - but adds other 
                // artifacts due to them being pushed off the slice). Also use full precision for high res cases.
                sampleOffset = round(sampleOffset) * (float2)ViewportPixelSize;

#ifdef GTAO_SHOW_DEBUG_VIZ
                int mipLevelU = (int)round(mipLevel);
                float4 mipColor = saturate( float4( mipLevelU>=3, mipLevelU>=1 && mipLevelU<=3, mipLevelU<=1, 1.0 ) );
                if( all( sampleOffset == 0 ) )
                    DebugDraw2DText( pixCoord, float4( 1, 0, 0, 1), pixelTooCloseThreshold );
                [branch] if (IsUnderCursorRange(pixCoord, int2(1, 1)))
                {
                    //DebugDraw2DText( (normalizedScreenPos + sampleOffset) * ViewportSize, mipColor, mipLevelU );
                    //DebugDraw2DText( (normalizedScreenPos + sampleOffset) * ViewportSize, mipColor, (uint)slice );
                    //DebugDraw2DText( (normalizedScreenPos - sampleOffset) * ViewportSize, mipColor, (uint)slice );
                    //DebugDraw2DText( (normalizedScreenPos - sampleOffset) * ViewportSize, saturate( float4( mipLevelU>=3, mipLevelU>=1 && mipLevelU<=3, mipLevelU<=1, 1.0 ) ), mipLevelU );
                }
#endif

                float boundaryDistance = sqrt(effectRadiusSqr - s * s);
                float boundaryViewZ = viewspaceZ - boundaryDistance;

                float2 sampleScreenPos0 = normalizedScreenPos + sampleOffset;
                float  SZ0 = GTAO_ScreenSpaceToViewSpaceDepth(sourceDepth.SampleLevel( depthSampler, sampleScreenPos0, 0 ).x);
                float distanceAttenuationFactor0 = max(viewspaceZ - SZ0, 0.0) * absoluteSplitDepthDifferenceRcp;
                SZ0 = max(SZ0, lerp(boundaryViewZ, viewspaceZ, distanceAttenuationFactor0));
                float3 samplePos0 = GTAO_ComputeViewspacePosition( sampleScreenPos0, SZ0 );

                float2 sampleScreenPos1 = normalizedScreenPos - sampleOffset;
                float  SZ1 = GTAO_ScreenSpaceToViewSpaceDepth(sourceDepth.SampleLevel( depthSampler, sampleScreenPos1, 0 ).x);
                float distanceAttenuationFactor1 = max(viewspaceZ - SZ1, 0.0) * absoluteSplitDepthDifferenceRcp;
                SZ1 = max(SZ1, lerp(boundaryViewZ, viewspaceZ, distanceAttenuationFactor1));
                float3 samplePos1 = GTAO_ComputeViewspacePosition( sampleScreenPos1, SZ1 );

                float3 sampleDelta0     = (samplePos0 - float3(pixCenterPos)); // using float for sampleDelta causes precision issues
                float3 sampleDelta1     = (samplePos1 - float3(pixCenterPos)); // using float for sampleDelta causes precision issues
                float sampleDist0     = (float)length( sampleDelta0 );
                float sampleDist1     = (float)length( sampleDelta1 );

                // approx lines 23, 24 from the paper, unrolled
                float3 sampleHorizonVec0 = (float3)(sampleDelta0 / sampleDist0);
                float3 sampleHorizonVec1 = (float3)(sampleDelta1 / sampleDist1);

                // any sample out of radius should be discarded - also use fallof range for smooth transitions; this is a modified idea from "4.3 Implementation details, Bounding the sampling area"
                // float weight0;
                // float weight1;
                // if (GTAO_USE_DEFAULT_CONSTANTS)
                // {
                //     weight0                 = saturate( sampleDist0 * falloffMul + falloffAdd );
                //     weight1                 = saturate( sampleDist1 * falloffMul + falloffAdd );
                // }
                // else
                // {
                //     // this is our own thickness heuristic that relies on sooner discarding samples behind the center
                //     float falloffBase0      = length( float3(sampleDelta0.x, sampleDelta0.y, sampleDelta0.z * (1+thinOccluderCompensation) ) );
                //     float falloffBase1      = length( float3(sampleDelta1.x, sampleDelta1.y, sampleDelta1.z * (1+thinOccluderCompensation) ) );
                //     weight0                 = saturate( falloffBase0 * falloffMul + falloffAdd );
                //     weight1                 = saturate( falloffBase1 * falloffMul + falloffAdd );
                // }

                // sample horizon cos
                float shc0 = (float)dot(sampleHorizonVec0, viewVec);
                float shc1 = (float)dot(sampleHorizonVec1, viewVec);

                // discard unwanted samples
                // shc0 = lerp( lowHorizonCos0, shc0, weight0 ); // this would be more correct but too expensive: cos(lerp( acos(lowHorizonCos0), acos(shc0), weight0 ));
                // shc1 = lerp( lowHorizonCos1, shc1, weight1 ); // this would be more correct but too expensive: cos(lerp( acos(lowHorizonCos1), acos(shc1), weight1 ));

                // thickness heuristic - see "4.3 Implementation details, Height-field assumption considerations"
#if 0   // (disabled, not used) this should match the paper
                float newhorizonCos0 = max( horizonCos0, shc0 );
                float newhorizonCos1 = max( horizonCos1, shc1 );
                horizonCos0 = (horizonCos0 > shc0)?( lerp( newhorizonCos0, shc0, thinOccluderCompensation ) ):( newhorizonCos0 );
                horizonCos1 = (horizonCos1 > shc1)?( lerp( newhorizonCos1, shc1, thinOccluderCompensation ) ):( newhorizonCos1 );
#elif 0 // (disabled, not used) this is slightly different from the paper but cheaper and provides very similar results
                horizonCos0 = lerp( max( horizonCos0, shc0 ), shc0, thinOccluderCompensation );
                horizonCos1 = lerp( max( horizonCos1, shc1 ), shc1, thinOccluderCompensation );
#else   // this is a version where thicknessHeuristic is completely disabled
                horizonCos0 = max( horizonCos0, shc0 );
                horizonCos1 = max( horizonCos1, shc1 );
#endif


#ifdef GTAO_SHOW_DEBUG_VIZ
                [branch] if (IsUnderCursorRange(pixCoord, int2(1, 1)))
                {
                    float3 WS_samplePos0 = mul(g_globals.ViewInv, float4(samplePos0, 1)).xyz;
                    float3 WS_samplePos1 = mul(g_globals.ViewInv, float4(samplePos1, 1)).xyz;
                    float3 WS_sampleHorizonVec0 = mul( (float3x3)g_globals.ViewInv, sampleHorizonVec0).xyz;
                    float3 WS_sampleHorizonVec1 = mul( (float3x3)g_globals.ViewInv, sampleHorizonVec1).xyz;
                    // DebugDraw3DSphere( WS_samplePos0, effectRadius * 0.02, DbgGetSliceColor(slice, sliceCount, false) );
                    // DebugDraw3DSphere( WS_samplePos1, effectRadius * 0.02, DbgGetSliceColor(slice, sliceCount, true) );
                    DebugDraw3DSphere( WS_samplePos0, effectRadius * 0.02, mipColor );
                    DebugDraw3DSphere( WS_samplePos1, effectRadius * 0.02, mipColor );
                    // DebugDraw3DArrow( WS_samplePos0, WS_samplePos0 - WS_sampleHorizonVec0, 0.002, float4(1, 0, 0, 1 ) );
                    // DebugDraw3DArrow( WS_samplePos1, WS_samplePos1 - WS_sampleHorizonVec1, 0.002, float4(1, 0, 0, 1 ) );
                    // DebugDraw3DText( WS_samplePos0, float2(0,  0), float4( 1, 0, 0, 1), weight0 );
                    // DebugDraw3DText( WS_samplePos1, float2(0,  0), float4( 1, 0, 0, 1), weight1 );

                    // DebugDraw2DText( float2( 500, 94+(step+slice*3)*12 ), float4( 0, 1, 0, 1 ), float4( projectedNormalVecLength, 0, horizonCos0, horizonCos1 ) );
                }
#endif
            }

#if 0       // I can't figure out the slight overdarkening on high slopes, so I'm adding this fudge - in the training set, 0.05 is close (PSNR 21.34) to disabled (PSNR 21.45)
            projectedNormalVecLength = lerp( projectedNormalVecLength, 1, 0.05 );
#endif

            // line ~27, unrolled
            float h0 = GTAO_FastACos(horizonCos0);
            float h1 = -GTAO_FastACos(horizonCos1);
            // float h0 = acos(horizonCos0);
            // float h1 = -acos(horizonCos1);
#if 1       // we can skip clamping for a tiny little bit more performance
            h0 = n + clamp( h0-n, -GTAO_PI_HALF, GTAO_PI_HALF );
            h1 = n + clamp( h1-n, -GTAO_PI_HALF, GTAO_PI_HALF );
#endif
            float iarc0 = ( cosNorm + 2.0 * h0 * sin(n) - cos(2.0 * h0 - n) ) / 4;
            float iarc1 = ( cosNorm + 2.0 * h1 * sin(n) - cos(2.0 * h1 - n) ) / 4;
            float localVisibility = projectedNormalVecLength * (iarc0 + iarc1);
            visibility += localVisibility;

            if (GTAO_COMPUTE_BENT_NORMALS)
            {
                // see "Algorithm 2 Extension that computes bent normals b."
                float t0 = (6*sin(h0-n)-sin(3*h0-n)+6*sin(h1-n)-sin(3*h1-n)+16*sin(n)-3*(sin(h0+n)+sin(h1+n)))/12;
                float t1 = (-cos(3 * h0-n)-cos(3 * h1-n) +8 * cos(n)-3 * (cos(h0+n) +cos(h1+n)))/12;
                float3 localBentNormal = float3( directionVec.x * t0, directionVec.y * t0, -float(t1) );
                localBentNormal = (float3)mul( GTAO_RotFromToMatrix( float3(0,0,-1), viewVec ), localBentNormal ) * projectedNormalVecLength;
                bentNormal += localBentNormal;
            }
        }

        visibility /= sliceCount;

        float3 subsurfaceColor = _GBuffer2[pixCoord].rgb;
        aoData.rgb = (1.0 - visibility) * subsurfaceColor;

        if (GTAO_COMPUTE_MULTI_BOUNCE)
        {
            float3 multiBounceFit;
            float3 albedo = _GBuffer0[pixCoord].rgb;
#if MULTIBOUNCE_CUBIC_POLYNOMIAL_FIT
            float3 a =  2.0404 * albedo - 0.3324;
            float3 b = -4.7951 * albedo + 0.6417;
            float3 c =  2.7552 * albedo - 0.3097;
            multiBounceFit = ((a * visibility + b) * visibility + c) * visibility;
#else
            if (GTAO_COMPUTE_BENT_NORMALS)
	        {
                multiBounceFit = visibility / (1.0 + albedo * (-1 + visibility)) - visibility;
            }
            else
            {
                multiBounceFit = (visibility / (1.0 + albedo * (-1 + visibility)) - visibility) / albedo;
            }
#endif
            aoData.rgb += multiBounceFit;
        }

        if (GTAO_USE_DEFAULT_CONSTANTS)
        {
            visibility = pow( visibility, GTAO_DEFAULT_VISIBILITY_VALUE_POWER );
        }
        else
        {
            visibility = pow( visibility, VisibilityValuePower );
        }
        
        visibility = 1.0 - lerp(0.0, (1.0 - visibility), intensity);

        visibility = max( 0.03, visibility ); // disallow total occlusion (which wouldn't make any sense anyhow since pixel is visible but also helps with packing bent normals)

        aoData.a = visibility;

        if (GTAO_COMPUTE_BENT_NORMALS)
        {
            bentNormal = mul(InverseViewMatrix, float4(bentNormal, 0.0)).xyz;
            bentNormal = normalize(bentNormal);
            bentNormal = Convert_minus1to1_To_0to1(bentNormal);
        }
    }

    GTAO_OutputWorkingTerm( pixCoord, aoData, bentNormal, outWorkingAOTerm );
}

// Engine-specific screen & temporal noise loader
float2 SpatioTemporalNoise( uint2 pixCoord, uint temporalIndex )    // without TAA, temporalIndex is always 0
{
    float2 noise = float2(0.0, 0.0);

    // Hilbert curve driving R2 (see https://www.shadertoy.com/view/3tB3z3)
    if (ENABLE_SLICE_NOISE || ENABLE_STEP_NOISE)
    {
        uint2 coord = pixCoord % 64;
        uint i = coord.x + coord.y * 64;
        uint index = _HilbertLUT.Load(i);
        index += 288 * temporalIndex; // why 288? tried out a few and that's the best so far (with HILBERT_LEVEL 6U) - but there's probably better :)

        float2 R2SequenceFactor = float2(0.0, 0.0);
        if (ENABLE_SLICE_NOISE)
        {
            R2SequenceFactor.x = 0.75487766624669276005;
        }
        if (ENABLE_STEP_NOISE)
        {
            R2SequenceFactor.y = 0.5698402909980532659114;
        }

        noise = frac(0.5 + index * R2SequenceFactor); // R2 sequence - see http://extremelearning.com.au/unreasonable-effectiveness-of-quasirandom-sequences/
    }

    return noise;
}

float4 GTAO_UnpackEdges( float _packedVal )
{
    uint packedVal = (uint)(_packedVal * 255.5);
    float4 edgesLRTB;
    edgesLRTB.x = float((packedVal >> 6) & 0x03) / 3.0;          // there's really no need for mask (as it's an 8 bit input) but I'll leave it in so it doesn't cause any trouble in the future
    edgesLRTB.y = float((packedVal >> 4) & 0x03) / 3.0;
    edgesLRTB.z = float((packedVal >> 2) & 0x03) / 3.0;
    edgesLRTB.w = float((packedVal >> 0) & 0x03) / 3.0;

    return saturate( edgesLRTB );
}

typedef float4 AOTermType;             // colored AO

void GTAO_AddSample( AOTermType ssaoValue, float edgeValue, inout AOTermType sum, inout AOTermType sumWeight )
{
    float weight = edgeValue;    

    sum += (weight * ssaoValue);
    sumWeight += weight;
}

void GTAO_Output( uint2 pixCoord, RWTexture2D<float4> outputTexture, AOTermType outputValue, const uniform bool finalApply )
{
    outputValue *= finalApply ? GTAO_OCCLUSION_TERM_SCALE : 1;
    outputValue = min(outputValue, 1.0);

    float3 bentNormal = _srcWorkingBentNormal[pixCoord].xyz;
    
    outputTexture[pixCoord] = float4(bentNormal, outputValue.w);
}

void GTAO_DecodeGatherPartial( const float4 packedValue, out AOTermType outDecoded[4] )
{
    for( int i = 0; i < 4; i++ )
    {
        outDecoded[i] = float(packedValue[i]);
    }
}

void GTAO_DecodeGatherPartialRGB( const float4 packedR, const float4 packedG, const float4 packedB, const float4 packedA, out AOTermType outDecoded[4] )
{
    for( int i = 0; i < 4; i++ )
    {
        outDecoded[i] = float4(packedR[i], packedG[i], packedB[i], packedA[i]);
    }
}

void GTAO_Denoise( const uint2 pixCoordBase, Texture2D<float4> sourceAOTerm, Texture2D<float> sourceEdges, SamplerState texSampler, RWTexture2D<float4> outputTexture, const uniform bool finalApply )
{
    const int2 pixCoord0 = int2( pixCoordBase.x    , pixCoordBase.y );
    const int2 pixCoord1 = int2( pixCoordBase.x + 1, pixCoordBase.y );
    const uint materialType0 = UnpackByte(_GBuffer2[pixCoord0].w);
    const uint materialType1 = UnpackByte(_GBuffer2[pixCoord1].w);
    if (materialType0 == MaterialType_Unlit || materialType1 == MaterialType_Unlit)
    {
        if (finalApply)
        {
            outputTexture[pixCoord0] = float4(0.0.xxx, 1.0);
            outputTexture[pixCoord1] = float4(0.0.xxx, 1.0);
        }
        return;
    }

    const float blurAmount = (finalApply)?((float)DenoiseBlurBeta):((float)DenoiseBlurBeta/(float)5.0);
    float diagWeight = 0.85 * 0.5;

    AOTermType aoTerm[2];   // pixel pixCoordBase and pixel pixCoordBase + int2( 1, 0 )
    float4 edgesC_LRTB[2];
    float weightTL[2];
    float weightTR[2];
    float weightBL[2];
    float weightBR[2];

    // gather edge and visibility quads, used later
    const float2 gatherCenter = float2( pixCoordBase.x, pixCoordBase.y ) * ViewportPixelSize;
    float4 edgesQ0        = sourceEdges.GatherRed( texSampler, gatherCenter, int2( 0, 0 ) );
    float4 edgesQ1        = sourceEdges.GatherRed( texSampler, gatherCenter, int2( 2, 0 ) );
    float4 edgesQ2        = sourceEdges.GatherRed( texSampler, gatherCenter, int2( 1, 2 ) );

    float4 packedR1 = sourceAOTerm.GatherRed( texSampler, gatherCenter, int2( 0, 0 ) );
    float4 packedG1 = sourceAOTerm.GatherGreen( texSampler, gatherCenter, int2( 0, 0 ) );
    float4 packedB1 = sourceAOTerm.GatherBlue( texSampler, gatherCenter, int2( 0, 0 ) );
    float4 packedA1 = sourceAOTerm.GatherAlpha( texSampler, gatherCenter, int2( 0, 0 ) );
    float4 packedR2 = sourceAOTerm.GatherRed( texSampler, gatherCenter, int2( 2, 0 ) );
    float4 packedG2 = sourceAOTerm.GatherGreen( texSampler, gatherCenter, int2( 2, 0 ) );
    float4 packedB2 = sourceAOTerm.GatherBlue( texSampler, gatherCenter, int2( 2, 0 ) );
    float4 packedA2 = sourceAOTerm.GatherAlpha( texSampler, gatherCenter, int2( 2, 0 ) );
    float4 packedR3 = sourceAOTerm.GatherRed( texSampler, gatherCenter, int2( 0, 2 ) );
    float4 packedG3 = sourceAOTerm.GatherGreen( texSampler, gatherCenter, int2( 0, 2 ) );
    float4 packedB3 = sourceAOTerm.GatherBlue( texSampler, gatherCenter, int2( 0, 2 ) );
    float4 packedA3 = sourceAOTerm.GatherAlpha( texSampler, gatherCenter, int2( 0, 2 ) );
    float4 packedR4 = sourceAOTerm.GatherRed( texSampler, gatherCenter, int2( 2, 2 ) );
    float4 packedG4 = sourceAOTerm.GatherGreen( texSampler, gatherCenter, int2( 2, 2 ) );
    float4 packedB4 = sourceAOTerm.GatherBlue( texSampler, gatherCenter, int2( 2, 2 ) );
    float4 packedA4 = sourceAOTerm.GatherAlpha( texSampler, gatherCenter, int2( 2, 2 ) );

    AOTermType visQ0[4] = { float4(0,0,0,0), float4(0,0,0,0), float4(0,0,0,0), float4(0,0,0,0) };    GTAO_DecodeGatherPartialRGB( packedR1, packedG1, packedB1, packedA1, visQ0 );
    AOTermType visQ1[4] = { float4(0,0,0,0), float4(0,0,0,0), float4(0,0,0,0), float4(0,0,0,0) };    GTAO_DecodeGatherPartialRGB( packedR2, packedG2, packedB2, packedA2, visQ1 );
    AOTermType visQ2[4] = { float4(0,0,0,0), float4(0,0,0,0), float4(0,0,0,0), float4(0,0,0,0) };    GTAO_DecodeGatherPartialRGB( packedR3, packedG3, packedB3, packedA3, visQ2 );
    AOTermType visQ3[4] = { float4(0,0,0,0), float4(0,0,0,0), float4(0,0,0,0), float4(0,0,0,0) };    GTAO_DecodeGatherPartialRGB( packedR4, packedG4, packedB4, packedA4, visQ3 );

    for( int side = 0; side < 2; side++ )
    {
        const int2 pixCoord = int2( pixCoordBase.x + side, pixCoordBase.y );

        float4 edgesL_LRTB  = GTAO_UnpackEdges( (side==0)?(edgesQ0.x):(edgesQ0.y) );
        float4 edgesT_LRTB  = GTAO_UnpackEdges( (side==0)?(edgesQ0.z):(edgesQ1.w) );
        float4 edgesR_LRTB  = GTAO_UnpackEdges( (side==0)?(edgesQ1.x):(edgesQ1.y) );
        float4 edgesB_LRTB  = GTAO_UnpackEdges( (side==0)?(edgesQ2.w):(edgesQ2.z) );

        edgesC_LRTB[side]     = GTAO_UnpackEdges( (side==0)?(edgesQ0.y):(edgesQ1.x) );

        // Edges aren't perfectly symmetrical: edge detection algorithm does not guarantee that a left edge on the right pixel will match the right edge on the left pixel (although
        // they will match in majority of cases). This line further enforces the symmetricity, creating a slightly sharper blur. Works real nice with TAA.
        edgesC_LRTB[side] *= float4( edgesL_LRTB.y, edgesR_LRTB.x, edgesT_LRTB.w, edgesB_LRTB.z );

#if 1   // this allows some small amount of AO leaking from neighbours if there are 3 or 4 edges; this reduces both spatial and temporal aliasing
        const float leak_threshold = 2.5; const float leak_strength = 0.5;
        float edginess = (saturate(4.0 - leak_threshold - dot( edgesC_LRTB[side], 1.0.xxxx )) / (4-leak_threshold)) * leak_strength;
        edgesC_LRTB[side] = saturate( edgesC_LRTB[side] + edginess );
#endif

#ifdef GTAO_SHOW_EDGES
        g_outputDbgImage[pixCoord] = 1.0 - float4( edgesC_LRTB[side].x, edgesC_LRTB[side].y * 0.5 + edgesC_LRTB[side].w * 0.5, edgesC_LRTB[side].z, 1.0 );
        //g_outputDbgImage[pixCoord] = 1 - float4( edgesC_LRTB[side].z, edgesC_LRTB[side].w , 1, 0 );
        //g_outputDbgImage[pixCoord] = edginess.xxxx;
#endif

        // for diagonals; used by first and second pass
        weightTL[side] = diagWeight * (edgesC_LRTB[side].x * edgesL_LRTB.z + edgesC_LRTB[side].z * edgesT_LRTB.x);
        weightTR[side] = diagWeight * (edgesC_LRTB[side].z * edgesT_LRTB.y + edgesC_LRTB[side].y * edgesR_LRTB.z);
        weightBL[side] = diagWeight * (edgesC_LRTB[side].w * edgesB_LRTB.x + edgesC_LRTB[side].x * edgesL_LRTB.w);
        weightBR[side] = diagWeight * (edgesC_LRTB[side].y * edgesR_LRTB.w + edgesC_LRTB[side].w * edgesB_LRTB.y);

        // first pass
        AOTermType ssaoValue     = (side==0)?(visQ0[1]):(visQ1[0]);
        AOTermType ssaoValueL    = (side==0)?(visQ0[0]):(visQ0[1]);
        AOTermType ssaoValueT    = (side==0)?(visQ0[2]):(visQ1[3]);
        AOTermType ssaoValueR    = (side==0)?(visQ1[0]):(visQ1[1]);
        AOTermType ssaoValueB    = (side==0)?(visQ2[2]):(visQ3[3]);
        AOTermType ssaoValueTL   = (side==0)?(visQ0[3]):(visQ0[2]);
        AOTermType ssaoValueBR   = (side==0)?(visQ3[3]):(visQ3[2]);
        AOTermType ssaoValueTR   = (side==0)?(visQ1[3]):(visQ1[2]);
        AOTermType ssaoValueBL   = (side==0)?(visQ2[3]):(visQ2[2]);

        AOTermType sumWeight = blurAmount.xxxx;
        AOTermType sum = ssaoValue * sumWeight;

        GTAO_AddSample( ssaoValueL, edgesC_LRTB[side].x, sum, sumWeight );
        GTAO_AddSample( ssaoValueR, edgesC_LRTB[side].y, sum, sumWeight );
        GTAO_AddSample( ssaoValueT, edgesC_LRTB[side].z, sum, sumWeight );
        GTAO_AddSample( ssaoValueB, edgesC_LRTB[side].w, sum, sumWeight );

        GTAO_AddSample( ssaoValueTL, weightTL[side], sum, sumWeight );
        GTAO_AddSample( ssaoValueTR, weightTR[side], sum, sumWeight );
        GTAO_AddSample( ssaoValueBL, weightBL[side], sum, sumWeight );
        GTAO_AddSample( ssaoValueBR, weightBR[side], sum, sumWeight );

        aoTerm[side] = sum / sumWeight;

        GTAO_Output( pixCoord, outputTexture, aoTerm[side], finalApply );

#ifdef GTAO_SHOW_BENT_NORMALS
        if( finalApply )
        {
            g_outputDbgImage[pixCoord] = float4( DisplayNormalSRGB( aoTerm[side].xyz /** aoTerm[side].www*/ ), 1 );
        }
#endif

    }
}

// Engine-specific normal map loader
float3 LoadNormal( int2 pos )
{
    float3 normalData;
    float4 gBuffer1 = _GBuffer1.Load( int3(pos, 0) );
    DecodeFromNormalBuffer(gBuffer1, normalData);
    // float3 normal = mul(ViewMatrix, float4(normalData.normalWS, 0.0)).xyz;

    return normalData;
}



// Engine-specific entry point for the first pass
[numthreads(8, 8, 1)]   // <- hard coded to 8x8; each thread computes 2x2 blocks so processing 16x16 block: Dispatch needs to be called with (width + 16-1) / 16, (height + 16-1) / 16
void CSPrefilterDepths16x16( uint2 dispatchThreadID : SV_DispatchThreadID, uint2 groupThreadID : SV_GroupThreadID )
{
    GTAO_PrefilterDepths16x16( dispatchThreadID, groupThreadID, _srcRawDepth, ce_Sampler_Point, _outWorkingDepthMIP0, _outWorkingDepthMIP1, _outWorkingDepthMIP2, _outWorkingDepthMIP3, _outWorkingDepthMIP4 );
}

// Engine-specific entry point for the second pass
[numthreads(GTAO_NUMTHREADS_X, GTAO_NUMTHREADS_Y, 1)]
void CSGTAOLow( const uint2 pixCoord : SV_DispatchThreadID )
{
    // ce_Sampler_Point is a sampler with D3D12_FILTER_MIN_MAG_MIP_POINT filter and D3D12_TEXTURE_ADDRESS_MODE_CLAMP addressing mode
    GTAO_MainPass( pixCoord, 1, 2, SpatioTemporalNoise(pixCoord, NoiseIndex), LoadNormal(pixCoord), _srcWorkingDepth, ce_Sampler_Clamp, _outWorkingAOTerm, _outWorkingEdges );
}

// Engine-specific entry point for the second pass
[numthreads(GTAO_NUMTHREADS_X, GTAO_NUMTHREADS_Y, 1)]
void CSGTAOMedium( const uint2 pixCoord : SV_DispatchThreadID )
{
    // ce_Sampler_Point is a sampler with D3D12_FILTER_MIN_MAG_MIP_POINT filter and D3D12_TEXTURE_ADDRESS_MODE_CLAMP addressing mode
    GTAO_MainPass( pixCoord, 2, 2, SpatioTemporalNoise(pixCoord, NoiseIndex), LoadNormal(pixCoord), _srcWorkingDepth, ce_Sampler_Clamp, _outWorkingAOTerm, _outWorkingEdges );
}

// Engine-specific entry point for the second pass
[numthreads(GTAO_NUMTHREADS_X, GTAO_NUMTHREADS_Y, 1)]
void CSGTAOHigh( const uint2 pixCoord : SV_DispatchThreadID )
{
    // ce_Sampler_Point is a sampler with D3D12_FILTER_MIN_MAG_MIP_POINT filter and D3D12_TEXTURE_ADDRESS_MODE_CLAMP addressing mode
    GTAO_MainPass( pixCoord, 3, 3, SpatioTemporalNoise(pixCoord, NoiseIndex), LoadNormal(pixCoord), _srcWorkingDepth, ce_Sampler_Clamp, _outWorkingAOTerm, _outWorkingEdges );
}

// Engine-specific entry point for the second pass
[numthreads(GTAO_NUMTHREADS_X, GTAO_NUMTHREADS_Y, 1)]
void CSGTAOUltra( const uint2 pixCoord : SV_DispatchThreadID )
{
    // ce_Sampler_Point is a sampler with D3D12_FILTER_MIN_MAG_MIP_POINT filter and D3D12_TEXTURE_ADDRESS_MODE_CLAMP addressing mode
    GTAO_MainPass( pixCoord, 9, 3, SpatioTemporalNoise( pixCoord, NoiseIndex ), LoadNormal( pixCoord ), _srcWorkingDepth, ce_Sampler_Clamp, _outWorkingAOTerm, _outWorkingEdges );
}

// Engine-specific entry point for the second pass
[numthreads(GTAO_NUMTHREADS_X, GTAO_NUMTHREADS_Y, 1)]
void CSGTAOUltraX( const uint2 pixCoord : SV_DispatchThreadID )
{
    // ce_Sampler_Point is a sampler with D3D12_FILTER_MIN_MAG_MIP_POINT filter and D3D12_TEXTURE_ADDRESS_MODE_CLAMP addressing mode
    GTAO_MainPass( pixCoord, 9, 3, SpatioTemporalNoise( pixCoord, NoiseIndex ), LoadNormal( pixCoord ), _srcWorkingDepth, ce_Sampler_Clamp, _outWorkingAOTerm, _outWorkingEdges );
}

// Engine-specific entry point for the third pass
[numthreads(GTAO_NUMTHREADS_X, GTAO_NUMTHREADS_Y, 1)]
void CSDenoisePass( const uint2 dispatchThreadID : SV_DispatchThreadID )
{
    const uint2 pixCoordBase = dispatchThreadID * uint2( 2, 1 );    // we're computing 2 horizontal pixels at a time (performance optimization)
    // ce_Sampler_Point is a sampler with D3D12_FILTER_MIN_MAG_MIP_POINT filter and D3D12_TEXTURE_ADDRESS_MODE_CLAMP addressing mode
    GTAO_Denoise( pixCoordBase, _srcWorkingAOTerm, _srcWorkingEdges, ce_Sampler_Clamp, _outFinalAOTerm, false );
}

[numthreads(GTAO_NUMTHREADS_X, GTAO_NUMTHREADS_Y, 1)]
void CSDenoiseLastPass( const uint2 dispatchThreadID : SV_DispatchThreadID )
{
    const uint2 pixCoordBase = dispatchThreadID * uint2( 2, 1 );    // we're computing 2 horizontal pixels at a time (performance optimization)
    // ce_Sampler_Point is a sampler with D3D12_FILTER_MIN_MAG_MIP_POINT filter and D3D12_TEXTURE_ADDRESS_MODE_CLAMP addressing mode
    GTAO_Denoise( pixCoordBase, _srcWorkingAOTerm, _srcWorkingEdges, ce_Sampler_Clamp, _outFinalAOTerm, true );
}