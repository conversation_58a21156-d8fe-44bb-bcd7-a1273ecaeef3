Texture2D<float4> edge_texture : register(space0);
SamplerState ce_Sampler_Clamp : register(space0);
SamplerState ce_Sampler_Point : register(space0);

Texture2D<float4> search_texture : register(space1);
Texture2D<float4> area_texture : register(space1);

cbuffer para : register(space0)
{
    float4 SMAA_RT_METRICS;
}

#define SMAA_HLSL_4
#define SMAA_PRESET_HIGH
#include "SMAA.hlsl"

struct VS2PS
{
	float2 UV : 		TEXCOORD0;
	float2 pixcoord:    TEXCOORD1;
	float4 Pos : 		SV_POSITION;
	float4 offset[3] :  TEXCOORD2;
};

void DrawRectangle(
	float4 InPosition,
	float2 InTexCoord,
	out float4 OutPosition,
	out float2 OutTexCoord)
{
	OutPosition = InPosition;
	OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
	OutPosition.xy *= float2(1, -1);
	OutTexCoord.xy = InTexCoord.xy;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
	VS2PS ret;
	float4 Outpos;
	float2 Outuv;
	DrawRectangle(
		Pos,
		uv,
		Outpos,
		Outuv);
	ret.Pos = Outpos;
	ret.UV = Outuv;

	ret.pixcoord = Outuv * SMAA_RT_METRICS.zw;

	ret.offset[0] = mad(SMAA_RT_METRICS.xyxy, float4(-0.25, -0.125,  1.25, -0.125), Outuv.xyxy);
    ret.offset[1] = mad(SMAA_RT_METRICS.xyxy, float4(-0.125, -0.25, -0.125,  1.25), Outuv.xyxy);
    ret.offset[2] = mad(SMAA_RT_METRICS.xxyy,
                    float4(-2.0, 2.0, -2.0, 2.0) * float(SMAA_MAX_SEARCH_STEPS),
                    float4(ret.offset[0].xz, ret.offset[1].yw));
	return ret;
}


float4 PSMain(VS2PS input) : SV_TARGET
{
	return SMAABlendingWeightCalculationPS(
		input.UV, input.pixcoord, input.offset, edge_texture, area_texture, search_texture, 0);
}