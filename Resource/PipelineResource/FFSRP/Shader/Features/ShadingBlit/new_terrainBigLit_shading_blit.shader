
#define _MAX_LAYER 7
#define _LAYER_COUNT 7

//==============pass parameter===============
SamplerState ce_Sampler_Clamp : register(space0);
SamplerState texture_sampler : register(space0);

//=============material parameter============
cbuffer cbMtl : register(space1)
{
	float4 _BaseColor0;
	float4 _BaseColor1;
	float4 _BaseColor2;
	float4 _BaseColor3;
	float4 _BaseColor4;
    float4 _BaseColor5;
    float4 _BaseColor6;

	float _BaseColorMap0_Tile;
	float _BaseColorMap1_Tile;
	float _BaseColorMap2_Tile;
	float _BaseColorMap3_Tile;
	float _BaseColorMap4_Tile;
    float _BaseColorMap5_Tile;
    float _BaseColorMap6_Tile;
}

Texture2D<float4> _BaseColorMap0 : register(space1);
Texture2D<float4> _BaseColorMap1 : register(space1);
Texture2D<float4> _BaseColorMap2 : register(space1);
Texture2D<float4> _BaseColorMap3 : register(space1);
Texture2D<float4> _BaseColorMap4 : register(space1);
Texture2D<float4> _BaseColorMap5 : register(space1);
Texture2D<float4> _BaseColorMap6 : register(space1);
Texture2D<float4> _MergeMaskMap0 : register(space1);
Texture2D<float4> _MergeMaskMap1 : register(space1);

struct LayerTexCoord
{
	float2 layerBase;
	// Regular texcoord
	float2 base0;
	float2 base1;
	float2 base2;
	float2 base3;
	float2 base4;
	float2 base5;
    float2 base6;
};


void ComputeMaskWeights(float inWeights[_MAX_LAYER], out float outWeights[_MAX_LAYER])
{
    outWeights[0] = 0.0;
    outWeights[1] = 0.0;
    outWeights[2] = 0.0;
    outWeights[3] = 0.0;
    outWeights[4] = 0.0;
    outWeights[5] = 0.0;
    outWeights[6] = 0.0;

    // calculate weight of each layers
    // Algorithm is like this:
    // Top layer have priority on others layers
    // If a top layer doesn't use the full weight, the remaining can be use by the following layer.
    float weightsSum = 0.0;
    for (int i = 0; i < _LAYER_COUNT; ++i)
    {
        outWeights[i] = min(inWeights[i], (1.0 - weightsSum));
        weightsSum = saturate(weightsSum + inWeights[i]);
    }
    if (weightsSum < 1.0)
    {
        float addMultiplier = (1.0 - weightsSum) / weightsSum;
        for (int i = 0; i < _LAYER_COUNT; ++i)
        {
            outWeights[i] *= (1.0 + addMultiplier);
        }
    }
}

//Compute weights for each layer
void ComputeLayerWeights(LayerTexCoord layerTexCoord, out float outWeights[_MAX_LAYER])
{
    //TODO 
    //blendMasks is influenced by inputAlphaMask
    float masks[_MAX_LAYER];
    float4 mergeMask0 = _MergeMaskMap0.Sample(texture_sampler, layerTexCoord.layerBase);
    float4 mergeMask1 = _MergeMaskMap1.Sample(texture_sampler, layerTexCoord.layerBase);
    masks[0] = mergeMask0.r;
    masks[1] = mergeMask0.g;
    masks[2] = mergeMask0.b;
    masks[3] = mergeMask1.r;
    masks[4] = mergeMask1.g;
    masks[5] = mergeMask1.b;
    masks[6] = mergeMask1.a;

    ComputeMaskWeights(masks, outWeights);
}

struct VS2PS
{
	float2 UV : 		TEXCOORD0;
	float4 Pos : 		SV_POSITION;
};

void DrawRectangle(
	float4 InPosition,
	float2 InTexCoord,
	out float4 OutPosition,
	out float2 OutTexCoord)
{
	OutPosition = InPosition;
	OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
	OutPosition.xy *= float2(1, -1);
	OutTexCoord.xy = InTexCoord.xy;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
	VS2PS ret;
	float4 Outpos;
	float2 Outuv;
	DrawRectangle(
		Pos,
		uv,
		Outpos,
		Outuv);
	ret.Pos = Outpos;
	ret.UV = Outuv;
	return ret;
}

float4 PSMain(VS2PS psInput) : SV_TARGET
{
	LayerTexCoord layerTexCoord = (LayerTexCoord)0;
    float2 muv = float2(psInput.UV.x, psInput.UV.y);
    layerTexCoord.base0 = muv * _BaseColorMap0_Tile;
    layerTexCoord.base1 = muv * _BaseColorMap1_Tile;
    layerTexCoord.base2 = muv * _BaseColorMap2_Tile;
    layerTexCoord.base3 = muv * _BaseColorMap3_Tile;
	layerTexCoord.base4 = muv * _BaseColorMap4_Tile;
    layerTexCoord.base5 = muv * _BaseColorMap5_Tile;
    layerTexCoord.base6 = muv * _BaseColorMap6_Tile;

	float4 baseColor0, baseColor1, baseColor2, baseColor3,baseColor4, baseColor5, baseColor6;
	float smallMacro;float mediumMacro;float largeMacro;float macroTiling;
	baseColor0 = _BaseColorMap0.Sample(texture_sampler, layerTexCoord.base0).rgba * _BaseColor0.rgba;
	smallMacro = 3.0*_BaseColorMap0.Sample(texture_sampler, layerTexCoord.base0 *0.2).r;
    mediumMacro = 3.0 * _BaseColorMap0.Sample(texture_sampler,layerTexCoord.base0 * 0.02).r;
    largeMacro = 3.0 * _BaseColorMap0.Sample(texture_sampler, layerTexCoord.base0 * 0.002).r;
	macroTiling = lerp(0.5, 1.0, (smallMacro + 0.5) * (mediumMacro + 0.5) * (largeMacro + 0.5));
    baseColor0 *= macroTiling;

	baseColor1 = _BaseColorMap1.Sample(texture_sampler, layerTexCoord.base1).rgba * _BaseColor1.rgba;
	smallMacro = 3.0*_BaseColorMap1.Sample(texture_sampler, layerTexCoord.base1 *0.2).r;
    mediumMacro = 3.0 * _BaseColorMap1.Sample(texture_sampler,layerTexCoord.base1 * 0.02).r;
    largeMacro = 3.0 * _BaseColorMap1.Sample(texture_sampler, layerTexCoord.base1 * 0.002).r;
	macroTiling = lerp(0.5, 1.0, (smallMacro + 0.5) * (mediumMacro + 0.5) * (largeMacro + 0.5));
    baseColor1 *= macroTiling;

	baseColor2 = _BaseColorMap2.Sample(texture_sampler, layerTexCoord.base2).rgba * _BaseColor2.rgba;
	smallMacro = 3.0*_BaseColorMap2.Sample(texture_sampler, layerTexCoord.base2 *0.2).r;
    mediumMacro = 3.0 * _BaseColorMap2.Sample(texture_sampler,layerTexCoord.base2 * 0.02).r;
    largeMacro = 3.0 * _BaseColorMap2.Sample(texture_sampler, layerTexCoord.base2 * 0.002).r;
	macroTiling = lerp(0.5, 1.0, (smallMacro + 0.5) * (mediumMacro + 0.5) * (largeMacro + 0.5));
    baseColor2 *= macroTiling;

	baseColor3 = _BaseColorMap3.Sample(texture_sampler, layerTexCoord.base3).rgba * _BaseColor3.rgba;
	smallMacro = 3.0*_BaseColorMap3.Sample(texture_sampler, layerTexCoord.base3 *0.2).r;
    mediumMacro = 3.0 * _BaseColorMap3.Sample(texture_sampler,layerTexCoord.base3 * 0.02).r;
    largeMacro = 3.0 * _BaseColorMap3.Sample(texture_sampler, layerTexCoord.base3 * 0.002).r;
	macroTiling = lerp(0.5, 1.0, (smallMacro + 0.5) * (mediumMacro + 0.5) * (largeMacro + 0.5));
    baseColor3 *= macroTiling;

	baseColor4 = _BaseColorMap4.Sample(texture_sampler, layerTexCoord.base4).rgba * _BaseColor4.rgba;
	smallMacro = 3.0*_BaseColorMap4.Sample(texture_sampler, layerTexCoord.base4 *0.2).r;
    mediumMacro = 3.0 * _BaseColorMap4.Sample(texture_sampler,layerTexCoord.base4 * 0.02).r;
    largeMacro = 3.0 * _BaseColorMap4.Sample(texture_sampler, layerTexCoord.base4 * 0.002).r;
	macroTiling = lerp(0.5, 1.0, (smallMacro + 0.5) * (mediumMacro + 0.5) * (largeMacro + 0.5));
    baseColor4 *= macroTiling;

	baseColor5 = _BaseColorMap5.Sample(texture_sampler, layerTexCoord.base5).rgba * _BaseColor5.rgba;
	smallMacro = 3.0*_BaseColorMap5.Sample(texture_sampler, layerTexCoord.base5 *0.2).r;
    mediumMacro = 3.0 * _BaseColorMap5.Sample(texture_sampler,layerTexCoord.base5 * 0.02).r;
    largeMacro = 3.0 * _BaseColorMap5.Sample(texture_sampler, layerTexCoord.base5 * 0.002).r;
	macroTiling = lerp(0.5, 1.0, (smallMacro + 0.5) * (mediumMacro + 0.5) * (largeMacro + 0.5));
    baseColor5 *= macroTiling;

    baseColor6 = _BaseColorMap6.Sample(texture_sampler, layerTexCoord.base6).rgba * _BaseColor6.rgba;
    smallMacro = 3.0 * _BaseColorMap6.Sample(texture_sampler, layerTexCoord.base6 * 0.2).r;
    mediumMacro = 3.0 * _BaseColorMap6.Sample(texture_sampler, layerTexCoord.base6 * 0.02).r;
    largeMacro = 3.0 * _BaseColorMap6.Sample(texture_sampler, layerTexCoord.base6 * 0.002).r;
    macroTiling = lerp(0.5, 1.0, (smallMacro + 0.5) * (mediumMacro + 0.5) * (largeMacro + 0.5));
    baseColor6 *= macroTiling;

	float weights[_LAYER_COUNT];
	ComputeLayerWeights(layerTexCoord, weights);

	return baseColor0 * weights[0] + baseColor1 * weights[1] + baseColor2 * weights[2] + baseColor3 * weights[3]+ baseColor4 * weights[4] + baseColor5 * weights[5] + baseColor6 * weights[6];
}