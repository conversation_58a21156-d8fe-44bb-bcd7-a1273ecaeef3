#pragma compute CloudTemporalReconstruction

//#pragma enable debug_symbol

#define CE_USE_DOUBLE_TRANSFORM

Texture2D<float4> HistoryColorTex     : register(space0);
Texture2D<float4> RayMarchingColorTex : register(space0);
Texture2D<float>  CloudIntersectionTex : register(space0);
Texture2D<float2> RayMarchingDepthTex : register(space0);
Texture2D<float>  DownsampleDepthTex   : register(space0);

Texture2D<float4> BlueNoise : register(space0);

RWTexture2D<float4> TempReconsColorTex : register(space0);

#include "SkyCloudCommon.hlsl"

cbuffer cb0 : register(space0)
{
    float4 TempReconsTexSize; // width, height, invWidth, invHeight
    float4 RayMarchingTexSize;
    matrix ReprojectionMat;
    uint TempDownRatio;
    uint Debug_Option;
};

#define velocity_thres 0.1

uint2 getInterleavedScreenCoord(uint2 screen_coord)
{
    uint s_interleaved_size = TempDownRatio;
    //const uint2 s_interleaved_samples[4] = {uint2(0,0), uint2(1,1), uint2(0,1), uint2(1,0) };
    uint2 interleaved_screen_coord = uint2(screen_coord.x, screen_coord.y);
    interleaved_screen_coord /= s_interleaved_size;

    float interleaved_frame_count = s_interleaved_size * s_interleaved_size;
    int2 noise_pos = int2(interleaved_screen_coord % 256);
    float ray_placement_noise = BlueNoise.Load(int3(noise_pos.x, noise_pos.y, 0)).x;
    const uint frame_selector = uint(ceFrameNumber + int(min(ray_placement_noise * interleaved_frame_count, interleaved_frame_count - 1))) % int(interleaved_frame_count);

    if (s_interleaved_size == 2)
    {
        interleaved_screen_coord = interleaved_screen_coord * s_interleaved_size + s_interleaved_samples_2x2[frame_selector];
    } else if (s_interleaved_size == 4)
    {
        interleaved_screen_coord = interleaved_screen_coord * s_interleaved_size + s_interleaved_samples_4x4[frame_selector];
    }
    return interleaved_screen_coord;
}

float2 getUv(uint2 screen_coord, float4 size)
{
    float2 uv = float2(float(screen_coord.x) / size.x, float(screen_coord.y) / size.y);
    uv.xy += size.zw * 0.5f;
    return uv;
}

struct ReprojectionData
{
    float4 reprojected_color;
    float4 upsampled_color;
    float2 velocity;
    float4 debug_color;
    bool isEdge;
};

// depth based reconstruction
float4 getNearestDepthColor(float2 upsampled_uv, float depth)
{
    float upsampled_depth = TEXTURE_BIAS_ZERO(ce_Sampler_Point, RayMarchingDepthTex, upsampled_uv).y; //RayMarchingDepthTex.SampleLevel(ce_Sampler_Point, upsampled_uv, 0).y;
    float4 neighbor_depths;
    neighbor_depths.x = TEXTURE_OFFSET(ce_Sampler_Point, RayMarchingDepthTex, upsampled_uv, int2(0, 1)).y;
    neighbor_depths.y = TEXTURE_OFFSET(ce_Sampler_Point, RayMarchingDepthTex, upsampled_uv, int2(0, -1)).y;
    neighbor_depths.z = TEXTURE_OFFSET(ce_Sampler_Point, RayMarchingDepthTex, upsampled_uv, int2(-1, 0)).y;
    neighbor_depths.w = TEXTURE_OFFSET(ce_Sampler_Point, RayMarchingDepthTex, upsampled_uv, int2(1, 0)).y;
		
    float4 diagonal_neighbor_depths;
    diagonal_neighbor_depths.x = TEXTURE_OFFSET(ce_Sampler_Point, RayMarchingDepthTex, upsampled_uv, int2( 1, 1)).y;
    diagonal_neighbor_depths.y = TEXTURE_OFFSET(ce_Sampler_Point, RayMarchingDepthTex, upsampled_uv, int2(-1, 1)).y;
    diagonal_neighbor_depths.z = TEXTURE_OFFSET(ce_Sampler_Point, RayMarchingDepthTex, upsampled_uv, int2( 1, -1)).y;
    diagonal_neighbor_depths.w = TEXTURE_OFFSET(ce_Sampler_Point, RayMarchingDepthTex, upsampled_uv, int2(-1, -1)).y;
		
    float upsampled_distance = abs(upsampled_depth - depth);
    float4 neighbor_depths_distances = abs(neighbor_depths - depth.xxxx);
    float4 diagonal_neighbor_depths_distances = abs(diagonal_neighbor_depths - depth.xxxx);
		
    float min_distance = min(min(min4(neighbor_depths_distances), min4(diagonal_neighbor_depths_distances)), upsampled_distance);
		
    float4 color = 0.xxxx;
    if (min_distance == upsampled_distance)
        color = TEXTURE_BIAS_ZERO(ce_Sampler_Linear_Point_Clamp, RayMarchingColorTex, upsampled_uv);
    else if (min_distance == neighbor_depths_distances.x)
        color = TEXTURE_OFFSET(ce_Sampler_Linear_Point_Clamp, RayMarchingColorTex, upsampled_uv, int2(0, 1));
    else if (min_distance == neighbor_depths_distances.y)
        color = TEXTURE_OFFSET(ce_Sampler_Linear_Point_Clamp, RayMarchingColorTex, upsampled_uv, int2(0, -1));
    else if (min_distance == neighbor_depths_distances.z)
        color = TEXTURE_OFFSET(ce_Sampler_Linear_Point_Clamp, RayMarchingColorTex, upsampled_uv, int2(-1, 0));
    else if (min_distance == neighbor_depths_distances.w)
        color = TEXTURE_OFFSET(ce_Sampler_Linear_Point_Clamp, RayMarchingColorTex, upsampled_uv, int2(1, 0));
    else if (min_distance == diagonal_neighbor_depths_distances.x)
        color = TEXTURE_OFFSET(ce_Sampler_Linear_Point_Clamp, RayMarchingColorTex, upsampled_uv, int2(1, 1));
    else if (min_distance == diagonal_neighbor_depths_distances.y)
        color = TEXTURE_OFFSET(ce_Sampler_Linear_Point_Clamp, RayMarchingColorTex, upsampled_uv, int2(-1, 1));
    else if (min_distance == diagonal_neighbor_depths_distances.z)
        color = TEXTURE_OFFSET(ce_Sampler_Linear_Point_Clamp, RayMarchingColorTex, upsampled_uv, int2(1, -1));
    else if (min_distance == diagonal_neighbor_depths_distances.w)
        color = TEXTURE_OFFSET(ce_Sampler_Linear_Point_Clamp, RayMarchingColorTex, upsampled_uv, int2(-1, -1));
		
    return color;
}
	
// Get a stable color by spatial filtering when velocity is large
float4 getStableColor(float2 upsampled_uv, float4 center_color)
{
    float4 stable_color = center_color;
    
    // Use a 3x3 box filter
    float total_weight = 1.0;
    float kernel_size = 1.0;
    
    [unroll]
    for(int y = -1; y <= 1; y++)
    {
        [unroll]
        for(int x = -1; x <= 1; x++)
        {
            if(x == 0 && y == 0) continue;
            
            float2 offset = float2(x, y) * RayMarchingTexSize.zw * kernel_size;
            float2 sample_uv = upsampled_uv + offset;
            float4 sample_color = TEXTURE_BIAS_ZERO(ce_Sampler_Linear_Point_Clamp, RayMarchingColorTex, sample_uv);
            
            // Simple box filter
            float weight = 1.0;
            stable_color += sample_color * weight;
            total_weight += weight;
        }
    }
    
    stable_color /= total_weight;
    return stable_color;
}

ReprojectionData getReprojectedColor(uint2 screen_coord, uint2 interleaved_screen_coord)
{
    const uint s_interleaved_size = TempDownRatio;
    const float s_depth_reconstruction_threshold = 1.f;

    float2 uv = getUv(screen_coord, TempReconsTexSize);
    float2 upsampled_uv = getUv(screen_coord / s_interleaved_size, RayMarchingTexSize);

    // linear depth unit meter
    float depth = RayMarchingDepthTex.SampleLevel(ce_Sampler_Point, upsampled_uv, 0).x;
    float cloud_ndc_depth = GetNdcDepth(depth * 100.f, upsampled_uv);

    float3 position = -screenUVToViewDirection(upsampled_uv) * depth;
		
    float scene_depth = DownsampleDepthTex.SampleLevel(ce_Sampler_Point, uv, 0).x;
    float scene_linear_depth = GetLinearDepth(scene_depth, uv);
    float4 upsampled_color = getNearestDepthColor(upsampled_uv, scene_depth);
		
    float2 velocity = getStaticVelocity(upsampled_uv, ReprojectionMat, cloud_ndc_depth);
    
    // New edge detection: Check for transitions in DownsampleDepthTex (scene depth).
    // 0 indicates background/sky, >0 indicates a scene object.
    // 'scene_depth' variable (from DownsampleDepthTex at current 'uv') is used for the current pixel.
    bool is_edge = false; 
    
    int2 pixel_offsets_scene[4] = { int2(1,0), int2(-1,0), int2(0,1), int2(0,-1) };
    bool current_px_is_bg = (scene_depth <= EPSILON); // scene_depth is current pixel's scene depth

    for(int i_n = 0; i_n < 4; ++i_n) // Using a different loop variable name
    {
        // Calculate neighbor UV for DownsampleDepthTex
        float2 neighbor_uv_scene = uv + (float2)pixel_offsets_scene[i_n] * TempReconsTexSize.zw;
        float neighbor_depth_scene = DownsampleDepthTex.SampleLevel(ce_Sampler_Point, neighbor_uv_scene, 0).x;
        
        bool neighbor_px_is_bg = (neighbor_depth_scene <= EPSILON);

        if (current_px_is_bg != neighbor_px_is_bg)
        {
            is_edge = true; // Edge detected (transition between scene and background/sky)
            break; 
        }
    }
    // 'is_edge' is now true if the current pixel is at the boundary of a scene object and the background/sky.

    velocity = cloud_ndc_depth <= EPSILON ? 0.xx : velocity;
		
    float threshold = s_depth_reconstruction_threshold * scene_linear_depth * 0.5f;
		
    float4 neighbor_min = upsampled_color;
    float4 neighbor_max = upsampled_color;
    float4 neighbor = upsampled_color;
    float neighbor_depth;
    float neighbor_scene_depth;
    // We have debug the velocity and it seems right
    float3 neighbor_position;
    float2 neighbor_uv;
    float2 neighbor_velocity;
    float neighbor_cloud_ndc_depth;
    #define SAMPLE(X, Y) \
		neighbor = TEXTURE_OFFSET(ce_Sampler_Linear_Point_Clamp, RayMarchingColorTex, upsampled_uv, int2(X, Y)); \
		neighbor_scene_depth = GetLinearDepth(TEXTURE_OFFSET(ce_Sampler_Point, RayMarchingDepthTex, upsampled_uv, int2(X, Y)).y, uv); \
		if (abs(neighbor_scene_depth  - scene_linear_depth) < threshold \
            || min(neighbor.a, upsampled_color.a) >= (1.0 - 0.1f)) \
		{ \
			neighbor_min = min(neighbor_min, neighbor); \
			neighbor_max = max(neighbor_max, neighbor); \
		} \
		neighbor_depth = TEXTURE_OFFSET(ce_Sampler_Point, RayMarchingDepthTex, upsampled_uv, int2(X, Y)).x; \
        neighbor_cloud_ndc_depth = GetNdcDepth(neighbor_depth * 100.f, upsampled_uv); \
        neighbor_position = -screenUVToViewDirection(clamp(getUv(interleaved_screen_coord + int2(X, Y) * s_interleaved_size, TempReconsTexSize), 0.xx, 1.xx)) * neighbor_depth; \
        neighbor_uv = clamp(getUv(interleaved_screen_coord + int2(X, Y) * s_interleaved_size, TempReconsTexSize), 0.xx, 1.xx); \
        neighbor_velocity = getStaticVelocity(neighbor_uv, ReprojectionMat, neighbor_cloud_ndc_depth);\
        velocity = length(velocity) > length(neighbor_velocity) ? velocity : neighbor_velocity;

    // 
    
	SAMPLE(0, 1)
	SAMPLE(0, -1)
	SAMPLE(-1, 0)
	SAMPLE(1, 0)
	SAMPLE(1, 1)
	SAMPLE(-1, 1)
	SAMPLE(1, -1)
	SAMPLE(-1, -1)
		
    float2 target_uv = uv + velocity;
		
    float4 color = 0.xxxx;
		
    target_uv = clamp(target_uv, 0.xx, 1.xx);
    color = TEXTURE_BIAS_ZERO(ce_Sampler_Linear_Point_Clamp, HistoryColorTex, target_uv);
		
    float4 color_clamped = clamp(color, neighbor_min, neighbor_max);
		
    float4 viewport_bound = float4(TempReconsTexSize.zw * s_interleaved_size, 1.xx - TempReconsTexSize.zw * s_interleaved_size);
    bool skip_interleaved_block = length(velocity) > 0.001f && (target_uv.x < viewport_bound.x || target_uv.y < viewport_bound.y 
			|| target_uv.x > viewport_bound.z || target_uv.y > viewport_bound.w);
    // TODO we neglect fog first
    float haze_alpha = 1.f;   // physicalHazeVisibility(depth, mul3(s_imodelview, -screenUVToViewDirection(uv)));
    if (haze_alpha < 0.01f)
        skip_interleaved_block = true;
		
    if (skip_interleaved_block)
        color_clamped = upsampled_color; // decrease haze ghosting
		
    ReprojectionData retval;
    retval.reprojected_color = color_clamped;
    retval.upsampled_color = upsampled_color;
    retval.velocity = velocity;
    retval.debug_color = 0.xxxx;
    retval.isEdge = is_edge;

    // Visualize neighbor_min to check its stability

    // debug downsample depth
    if (Debug_Option == 1)
    {
        float debug_downDepth = DownsampleDepthTex.SampleLevel(ce_Sampler_Point, uv, 0).x;
        retval.debug_color = float4(pow(debug_downDepth, 0.3), 0, 0, 1);
    } else if (Debug_Option == 2)
    {
        float cloud_intersect_val = CloudIntersectionTex.SampleLevel(ce_Sampler_Point, uv, 0).x;
        if (cloud_intersect_val == INFINITY)
            retval.debug_color = float4(1, 0, 0, 1);
        else
        {
            cloud_intersect_val = saturate(cloud_intersect_val / CloudCutOffDistance);
            retval.debug_color = float4(cloud_intersect_val.xxx, 1.f);
        }
    } else if (Debug_Option == 3)
    {
        float2 encode_vel = EncodeVelocityToTexture(velocity);
        retval.debug_color = float4(encode_vel, 0, 1.f);
    } else if (Debug_Option == 4)
    {
        retval.debug_color = retval.reprojected_color;
    } else if (Debug_Option == 5)
    {
        retval.debug_color = retval.upsampled_color;
    } else if (Debug_Option == 6)
    {
        retval.debug_color = float4(upsampled_uv, 0, 1.f);

    } else if (Debug_Option == 7)
    {
        if (is_edge)
            retval.debug_color = float4(1, 0, 0, 1.0);
        else
            retval.debug_color = float4(0.xxx, 1.0);
    } else if (Debug_Option == 8)
    {
        // 可视化velocity的大小（magnitude）
        float velocity_length = length(velocity);
        retval.debug_color = float4(velocity_length.xxx * 100, 1.0); // 乘以100.0来增强显示效果
    } else if (Debug_Option == 9)
    {
        // 分别显示velocity的x和y分量
        retval.debug_color = float4(velocity.x * 0.5 + 0.5, velocity.y * 0.5 + 0.5, 0, 1.0);
    } else if (Debug_Option == 10)
    {
        // 可视化重投影前后的UV差异
        float2 uv = getUv(screen_coord, TempReconsTexSize);
        float4 ndc = float4(uv.xy * 2.f - 1.f, cloud_ndc_depth, 1.f);
        ndc.y = -ndc.y;
        float4 lastNdc = mul(ReprojectionMat, ndc);
        lastNdc /= lastNdc.w;
        float2 lastUV = (lastNdc.xy + 1.f) * 0.5f;
        lastUV.y = 1.f - lastUV.y;
        
        float uv_diff = length(lastUV - uv);
        retval.debug_color = float4(uv_diff * 10.0, 0, 0, 1.0); // 乘以10.0来增强显示效果
    }

    return retval;
}

[numthreads(8, 8, 1)]
void CloudTemporalReconstruction(uint3 id : SV_DISPATCHTHREADID)
{
    if (id.x >= uint(TempReconsTexSize.x) || id.y >= uint(TempReconsTexSize.y))
        return;

    uint2 screen_coord = uint2(id.xy);
    uint2 interleaved_screen_coord = getInterleavedScreenCoord(screen_coord);
    
    ReprojectionData data = getReprojectedColor(screen_coord, interleaved_screen_coord);

    if (Debug_Option > 0)
    {
        TempReconsColorTex[id.xy] = data.debug_color;
        return;
    }
#if 0
    {
        if (interleaved_screen_coord.x == screen_coord.x && interleaved_screen_coord.y == screen_coord.y)
        {
            float frames_velocity_threshold = 100.0f;
            float velocity_length = length(data.velocity);
            
            // Get stable color when velocity is large
            float4 stable_upsampled = data.upsampled_color;
            if (velocity_length > velocity_thres)
            {
                float2 upsampled_uv = getUv(screen_coord / TempDownRatio, RayMarchingTexSize);
                stable_upsampled = getStableColor(upsampled_uv, data.upsampled_color);
            }
            
            // Modify accumulation frames calculation
            float max_frames = 32.0;
            float min_frames = 2.0;
            float velocity_factor = saturate(velocity_length * frames_velocity_threshold);
            float4 accumulated_frames = (lerp(max_frames, min_frames, velocity_factor)).xxxx;
            
            // Use stable color when velocity is large
            float4 final_current = lerp(data.upsampled_color, stable_upsampled, saturate(velocity_length * 50.0));
            float4 color = lerp(data.reprojected_color, final_current, 1 / accumulated_frames);
            
            TempReconsColorTex[id.xy] = color;
        }
        else
        {
            
            float velocity_length = length(data.velocity);
            if (velocity_length > velocity_thres)
            {
                // When velocity is large, use stable color instead of reprojected color
                float2 upsampled_uv = getUv(screen_coord / TempDownRatio, RayMarchingTexSize);
                float4 stable_color = getStableColor(upsampled_uv, data.upsampled_color);
                
                // Smoothly blend between reprojected and stable color based on velocity
                float blend_factor = saturate(velocity_length * 50.0);
                TempReconsColorTex[id.xy] = lerp(data.reprojected_color, stable_color, blend_factor);
                TempReconsColorTex[id.xy] = float4(1, 0, 0, 1);
            }
            else
            {
                TempReconsColorTex[id.xy] = data.reprojected_color;
            }
        }
    }
#else
    if (interleaved_screen_coord.x == screen_coord.x && interleaved_screen_coord.y == screen_coord.y)
    {
        float frames_velocity_threshold = 100.0f;        
        float4 accumulated_frames = lerpFixed(4.0f, 1.0f, length(data.velocity) * frames_velocity_threshold).xxxx;
        float4 color = lerp(data.reprojected_color, data.upsampled_color, 1 / accumulated_frames);
        TempReconsColorTex[id.xy] = color;
    }
    else
    {
        TempReconsColorTex[id.xy] = data.reprojected_color;
    }
#endif
}