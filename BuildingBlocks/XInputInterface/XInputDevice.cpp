#include "XInputDevice.h"

namespace cross
{

XInputDevice::XInputDevice(IApplicationMessageHandler* InMessageHandler)
    : IInputDevice()
    , MessageHandler(InMessageHandler)
{
    mDeviceInfo.DeviceID = IInputDevice::GenerateDeviceID();
    mDeviceInfo.InterfaceName = "XInput";
    mDeviceInfo.DeviceName = "XInput Controller";
    mDeviceInfo.DeviceType = cross::input::InputDeviceType::Gamepad;

    ZeroMemory(&ControllerStates, sizeof(XINPUT_STATE));

    InitialButtonRepeatDelay = 0.2f;
    ButtonRepeatDelay = 0.1f;

    Buttons[0] = "Gamepad_FaceButton_Bottom";
    Buttons[1] = "Gamepad_FaceButton_Right";
    Buttons[2] = "Gamepad_FaceButton_Left";
    Buttons[3] = "Gamepad_FaceButton_Top";
    Buttons[4] = "Gamepad_LeftShoulder";
    Buttons[5] = "Gamepad_RightShoulder";
    Buttons[6] = "Gamepad_Special_Left";
    Buttons[7] = "Gamepad_Special_Right";
    Buttons[8] = "Gamepad_LeftThumbstick";
    Buttons[9] = "Gamepad_RightThumbstick";
    Buttons[10] = "Gamepad_LeftTrigger";
    Buttons[11] = "Gamepad_RightTrigger";
    Buttons[12] = "Gamepad_DPad_Up";
    Buttons[13] = "Gamepad_DPad_Down";
    Buttons[14] = "Gamepad_DPad_Left";
    Buttons[15] = "Gamepad_DPad_Right";
    Buttons[16] = "Gamepad_LeftStick_Up";
    Buttons[17] = "Gamepad_LeftStick_Down";
    Buttons[18] = "Gamepad_LeftStick_Left";
    Buttons[19] = "Gamepad_LeftStick_Right";
    Buttons[20] = "Gamepad_RightStick_Up";
    Buttons[21] = "Gamepad_RightStick_Down";
    Buttons[22] = "Gamepad_RightStick_Left";
    Buttons[23] = "Gamepad_RightStick_Right";
}

float ShortToNormalizedFloat(int AxisVal)
{
    // normalize [-32768..32767] -> [-1..1]
    const float Norm = (AxisVal <= 0 ? 32768.f : 32767.f);
    return float(AxisVal) / Norm;
}

void XInputDevice::Tick(float DeltaTime)
{
    XINPUT_STATE XInputStates[MAX_NUM_XINPUT_CONTROLLERS];
    memset(XInputStates, 0, sizeof(XInputStates));

    mGamepadAttached = false;
    for (int controllerIdx = 0; controllerIdx < MAX_NUM_XINPUT_CONTROLLERS; controllerIdx++)
    {
        XINPUT_STATE& XInputState = XInputStates[controllerIdx];

        if (XInputGetState(controllerIdx, &XInputState) != ERROR_SUCCESS)
        {
            continue;
        }
        mGamepadAttached = true;

        bool CurrentStates[MAX_NUM_CONTROLLER_BUTTONS] = {0};
        CurrentStates[0] = !!(XInputState.Gamepad.wButtons & XINPUT_GAMEPAD_A);
        CurrentStates[1] = !!(XInputState.Gamepad.wButtons & XINPUT_GAMEPAD_B);
        CurrentStates[2] = !!(XInputState.Gamepad.wButtons & XINPUT_GAMEPAD_X);
        CurrentStates[3] = !!(XInputState.Gamepad.wButtons & XINPUT_GAMEPAD_Y);
        CurrentStates[4] = !!(XInputState.Gamepad.wButtons & XINPUT_GAMEPAD_LEFT_SHOULDER);
        CurrentStates[5] = !!(XInputState.Gamepad.wButtons & XINPUT_GAMEPAD_RIGHT_SHOULDER);
        CurrentStates[6] = !!(XInputState.Gamepad.wButtons & XINPUT_GAMEPAD_BACK);
        CurrentStates[7] = !!(XInputState.Gamepad.wButtons & XINPUT_GAMEPAD_START);
        CurrentStates[8] = !!(XInputState.Gamepad.wButtons & XINPUT_GAMEPAD_LEFT_THUMB);
        CurrentStates[9] = !!(XInputState.Gamepad.wButtons & XINPUT_GAMEPAD_RIGHT_THUMB);
        CurrentStates[10] = !!(XInputState.Gamepad.bLeftTrigger > XINPUT_GAMEPAD_TRIGGER_THRESHOLD);
        CurrentStates[11] = !!(XInputState.Gamepad.bRightTrigger > XINPUT_GAMEPAD_TRIGGER_THRESHOLD);
        CurrentStates[12] = !!(XInputState.Gamepad.wButtons & XINPUT_GAMEPAD_DPAD_UP);
        CurrentStates[13] = !!(XInputState.Gamepad.wButtons & XINPUT_GAMEPAD_DPAD_DOWN);
        CurrentStates[14] = !!(XInputState.Gamepad.wButtons & XINPUT_GAMEPAD_DPAD_LEFT);
        CurrentStates[15] = !!(XInputState.Gamepad.wButtons & XINPUT_GAMEPAD_DPAD_RIGHT);
        CurrentStates[16] = !!(XInputState.Gamepad.sThumbLY > XINPUT_GAMEPAD_LEFT_THUMB_DEADZONE);
        CurrentStates[17] = !!(XInputState.Gamepad.sThumbLY < -XINPUT_GAMEPAD_LEFT_THUMB_DEADZONE);
        CurrentStates[18] = !!(XInputState.Gamepad.sThumbLX < -XINPUT_GAMEPAD_LEFT_THUMB_DEADZONE);
        CurrentStates[19] = !!(XInputState.Gamepad.sThumbLX > XINPUT_GAMEPAD_LEFT_THUMB_DEADZONE);
        CurrentStates[20] = !!(XInputState.Gamepad.sThumbRY > XINPUT_GAMEPAD_RIGHT_THUMB_DEADZONE);
        CurrentStates[21] = !!(XInputState.Gamepad.sThumbRY < -XINPUT_GAMEPAD_RIGHT_THUMB_DEADZONE);
        CurrentStates[22] = !!(XInputState.Gamepad.sThumbRX < -XINPUT_GAMEPAD_RIGHT_THUMB_DEADZONE);
        CurrentStates[23] = !!(XInputState.Gamepad.sThumbRX > XINPUT_GAMEPAD_RIGHT_THUMB_DEADZONE);

        auto OnControllerAnalog = [this](const UniqueString& KeyName, const auto& NewAxisValue, auto& OldAxisValue, const float NormalizedAxisValue, const auto& DeadZone) {
            if (OldAxisValue != NewAxisValue || std::abs((int)NewAxisValue) > DeadZone)
            {
                MessageHandler->OnControllerAnalog(KeyName, NormalizedAxisValue);
            }
            OldAxisValue = NewAxisValue;
        };

        OnControllerAnalog("Gamepad_LeftX", XInputState.Gamepad.sThumbLX, ControllerStates[controllerIdx].LeftXAnalog, ShortToNormalizedFloat(XInputState.Gamepad.sThumbLX), XINPUT_GAMEPAD_LEFT_THUMB_DEADZONE);
        OnControllerAnalog("Gamepad_LeftY", XInputState.Gamepad.sThumbLY, ControllerStates[controllerIdx].LeftYAnalog, ShortToNormalizedFloat(XInputState.Gamepad.sThumbLY), XINPUT_GAMEPAD_LEFT_THUMB_DEADZONE);

        OnControllerAnalog("Gamepad_RightX", XInputState.Gamepad.sThumbRX, ControllerStates[controllerIdx].RightXAnalog, ShortToNormalizedFloat(XInputState.Gamepad.sThumbRX), XINPUT_GAMEPAD_RIGHT_THUMB_DEADZONE);
        OnControllerAnalog("Gamepad_RightY", XInputState.Gamepad.sThumbRY, ControllerStates[controllerIdx].RightYAnalog, ShortToNormalizedFloat(XInputState.Gamepad.sThumbRY), XINPUT_GAMEPAD_RIGHT_THUMB_DEADZONE);

        OnControllerAnalog("Gamepad_LeftTriggerAxis", XInputState.Gamepad.bLeftTrigger, ControllerStates[controllerIdx].LeftTriggerAnalog, XInputState.Gamepad.bLeftTrigger / 255.f, XINPUT_GAMEPAD_TRIGGER_THRESHOLD);
        OnControllerAnalog("Gamepad_RightTriggerAxis", XInputState.Gamepad.bRightTrigger, ControllerStates[controllerIdx].RightTriggerAnalog, XInputState.Gamepad.bRightTrigger / 255.f, XINPUT_GAMEPAD_TRIGGER_THRESHOLD);

        const double currentTime = std::chrono::duration<double>(std::chrono::high_resolution_clock::now().time_since_epoch()).count();

        for (int buttonIdx = 0; buttonIdx < MAX_NUM_CONTROLLER_BUTTONS; buttonIdx++)
        {
            if (CurrentStates[buttonIdx] != ControllerStates[controllerIdx].ButtonStates[buttonIdx])
            {
                if (CurrentStates[buttonIdx])
                {
                    MessageHandler->OnControllerButtonPressed(Buttons[buttonIdx], false);
                }
                else
                {
                    MessageHandler->OnControllerButtonReleased(Buttons[buttonIdx], false);
                }

                if (CurrentStates[buttonIdx] != 0)
                {
                    ControllerStates[controllerIdx].NextRepeatTime[buttonIdx] = currentTime + InitialButtonRepeatDelay;
                }
            }
            else if (CurrentStates[buttonIdx] != 0 && ControllerStates[controllerIdx].NextRepeatTime[buttonIdx] <= currentTime)
            {
                MessageHandler->OnControllerButtonPressed(Buttons[buttonIdx], true);

                ControllerStates[controllerIdx].NextRepeatTime[buttonIdx] = currentTime + ButtonRepeatDelay;
            }

            // Update the state for next time
            ControllerStates[controllerIdx].ButtonStates[buttonIdx] = CurrentStates[buttonIdx];
        }
    }
}

bool XInputDevice::IsGamepadAttached() const
{
    return mGamepadAttached;
}

}   // namespace cross