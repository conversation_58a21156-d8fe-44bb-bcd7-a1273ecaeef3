#include "XInputModule.h"
#include "memoryhooker/Module.h"
IMPORT_MODULE
//void XInputRegister();
void SystemRegisterR();
void SystemRegisterG();

namespace cross{
XInputModule::XInputModule()
{
    //XInputRegister();
    SystemRegisterG();
    SystemRegisterR();
}

gbf::ModuleCallReturnStatus XInputModule::Init()
{
    mXInputDevice = XInputDevice::Create(SlateApplication::Instance());

    SlateApplication::Instance()->PendingExternalInputDevice(mXInputDevice);

    return gbf::ModuleCallReturnStatus::Succeed;
}
}   // namespace cross
MAKE_MODULE(cross::XInputModule, XInputInterface)