#include "DirectInputModule.h"
#include <regex>
#include "memoryhooker/Module.h"
IMPORT_MODULE
//void DirectInputRegister();
void SystemRegisterR();
void SystemRegisterG();

namespace cross{

using CEKeyConext = cross::input::CEKeyConext;
using CEKeys = cross::input::CEKeys;
using CEKey = cross::input::CEKey;
using CEKeysCategory = cross::input::CEKeysCategory;

BOOL CALLBACK EnumJoysticksCallback(const DIDEVICEINSTANCE* pdidInstance, VOID* pContext) noexcept;

DirectInputModule::DirectInputModule()
{
    //DirectInputRegister();
    SystemRegisterG();
    SystemRegisterR();

    SetupXInputDeviceList();
    InitDirectInput();
    CleanupXInputDeviceList();
}

HRESULT DirectInputModule::InitDirectInput()
{
    HRESULT hr;

    if (FAILED(hr = DirectInput8Create(GetModuleHandle(nullptr), DIRECTINPUT_VERSION, IID_IDirectInput8, (VOID**)&g_pDI, nullptr)))
        return hr;

    if (FAILED(hr = g_pDI->EnumDevices(DI8DEVCLASS_GAMECTRL, EnumJoysticksCallback, this, DIEDFL_ATTACHEDONLY)))
        return hr;

    /*if (FAILED(hr = g_pJoystick->SetCooperativeLevel(hDlg, DISCL_EXCLUSIVE | DISCL_FOREGROUND)))
        return hr;*/

    return S_OK;
}

BOOL CALLBACK EnumJoysticksCallback(const DIDEVICEINSTANCE* pdidInstance, VOID* pvRef) noexcept
{
    DirectInputModule* mModule = reinterpret_cast<DirectInputModule*>(pvRef);
    LPDIRECTINPUTDEVICE8 pJoystick = nullptr;

    if (mModule->IsXInputDevice(&pdidInstance->guidProduct))
    {
        return DIENUM_CONTINUE;
    }

    HRESULT hr = mModule->g_pDI->CreateDevice(pdidInstance->guidInstance, &pJoystick, nullptr);
    if (FAILED(hr = pJoystick->SetDataFormat(&c_dfDIJoystick2)))
        return hr;
    std::string name = pdidInstance->tszProductName;
    // Format joystick input device name
    std::regex pattern("\\s+");
    name = std::regex_replace(name, pattern, "_");
    GUID guid = pdidInstance->guidInstance;
    char buffer[64];
    snprintf(buffer, sizeof(buffer), 
            "%08lx-%04x-%04x-%02x%02x-%02x%02x%02x%02x%02x%02x",
             guid.Data1, guid.Data2, guid.Data3,
             guid.Data4[0], guid.Data4[1], guid.Data4[2], guid.Data4[3],
             guid.Data4[4], guid.Data4[5], guid.Data4[6], guid.Data4[7]);
    std::string guidStr = std::string(buffer);

    /*  Register Axes  */
    CEKeys::AddKey(CEKeyConext(CEKey(("Joystick_X_" + name).data()), {CEKeyConext::CEKeyFlags::GamepadKey | CEKeyConext::CEKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
    CEKeys::AddKey(CEKeyConext(CEKey(("Joystick_Y_" + name).data()), {CEKeyConext::CEKeyFlags::GamepadKey | CEKeyConext::CEKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
    CEKeys::AddKey(CEKeyConext(CEKey(("Joystick_Z_" + name).data()), {CEKeyConext::CEKeyFlags::GamepadKey | CEKeyConext::CEKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
    CEKeys::AddKey(CEKeyConext(CEKey(("Joystick_RX_" + name).data()), {CEKeyConext::CEKeyFlags::GamepadKey | CEKeyConext::CEKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
    CEKeys::AddKey(CEKeyConext(CEKey(("Joystick_RY_" + name).data()), {CEKeyConext::CEKeyFlags::GamepadKey | CEKeyConext::CEKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
    CEKeys::AddKey(CEKeyConext(CEKey(("Joystick_RZ_" + name).data()), {CEKeyConext::CEKeyFlags::GamepadKey | CEKeyConext::CEKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
    CEKeys::AddKey(CEKeyConext(CEKey(("Joystick_Slider_1_" + name).data()), {CEKeyConext::CEKeyFlags::GamepadKey | CEKeyConext::CEKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
    CEKeys::AddKey(CEKeyConext(CEKey(("Joystick_Slider_2_" + name).data()), {CEKeyConext::CEKeyFlags::GamepadKey | CEKeyConext::CEKeyFlags::Axis1D}, CEKeysCategory::NAME_GamepadCategory));
    CEKeys::AddKey(CEKeyConext(CEKey(("Joystick_POV_Up_" + name).data()), {CEKeyConext::CEKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
    CEKeys::AddKey(CEKeyConext(CEKey(("Joystick_POV_UpRight_" + name).data()), {CEKeyConext::CEKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
    CEKeys::AddKey(CEKeyConext(CEKey(("Joystick_POV_Right_" + name).data()), {CEKeyConext::CEKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
    CEKeys::AddKey(CEKeyConext(CEKey(("Joystick_POV_DownRight_" + name).data()), {CEKeyConext::CEKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
    CEKeys::AddKey(CEKeyConext(CEKey(("Joystick_POV_Down_" + name).data()), {CEKeyConext::CEKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
    CEKeys::AddKey(CEKeyConext(CEKey(("Joystick_POV_DownLeft_" + name).data()), {CEKeyConext::CEKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
    CEKeys::AddKey(CEKeyConext(CEKey(("Joystick_POV_Left_" + name).data()), {CEKeyConext::CEKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
    CEKeys::AddKey(CEKeyConext(CEKey(("Joystick_POV_UpLeft_" + name).data()), {CEKeyConext::CEKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));

    /*  Register Buttons  */
    // Index starts with 1 to match Windows joy.cpl settings
    for (int i = 1; i <= 128; i++)
    {
        std::string buttonName = "Joystick_Button_" + std::to_string(i) + "_" + name;
        CEKeys::AddKey(CEKeyConext(CEKey(buttonName.data()), {CEKeyConext::CEKeyFlags::GamepadKey}, CEKeysCategory::NAME_GamepadCategory));
    }

    std::shared_ptr<cross::input::IInputDevice> devicePtr = DirectInputDevice::Create(SlateApplication::Instance(), pJoystick, name, guidStr);

    SlateApplication::Instance()->PendingExternalInputDevice(devicePtr);
    mModule->mDirectInputDevices.push_back(devicePtr);

    return DIENUM_CONTINUE;
}

bool DirectInputModule::IsXInputDevice(const GUID* pGuidProductFromDirectInput)
{
    // Check each xinput device to see if this device's vid/pid matches
    XINPUT_DEVICE_NODE* pNode = g_pXInputDeviceList;
    while (pNode)
    {
        if (pNode->dwVidPid == pGuidProductFromDirectInput->Data1)
            return true;
        pNode = pNode->pNext;
    }

    return false;
}

HRESULT DirectInputModule::SetupXInputDeviceList()
{
    // COM needs to be initialized on this thread before the enumeration.

    // So we can call VariantClear() later, even if we never had a successful IWbemClassObject::Get().
    VARIANT var = {};
    VariantInit(&var);

    // Create BSTRs for WMI
    ScopedBSTR bstrNamespace(SysAllocString(L"\\\\.\\root\\cimv2"));
    ScopedBSTR bstrClassName(SysAllocString(L"Win32_PNPEntity"));
    ScopedBSTR bstrDeviceID(SysAllocString(L"DeviceID"));

    // Create WMI
    Microsoft::WRL::ComPtr<IWbemLocator> pIWbemLocator;
    HRESULT hr = CoCreateInstance(__uuidof(WbemLocator), nullptr, CLSCTX_INPROC_SERVER, IID_PPV_ARGS(&pIWbemLocator));
    if (FAILED(hr) || pIWbemLocator == nullptr)
        return hr;

    // Connect to WMI
    Microsoft::WRL::ComPtr<IWbemServices> pIWbemServices;
    hr = pIWbemLocator->ConnectServer(bstrNamespace.get(), nullptr, nullptr, 0L, 0L, nullptr, nullptr, &pIWbemServices);
    if (FAILED(hr) || pIWbemServices == nullptr)
        return hr;

    // Switch security level to IMPERSONATE
    hr = CoSetProxyBlanket(pIWbemServices.Get(), RPC_C_AUTHN_WINNT, RPC_C_AUTHZ_NONE, nullptr, RPC_C_AUTHN_LEVEL_CALL, RPC_C_IMP_LEVEL_IMPERSONATE, nullptr, EOAC_NONE);
    if (FAILED(hr))
        return hr;

    // Get list of Win32_PNPEntity devices
    Microsoft::WRL::ComPtr<IEnumWbemClassObject> pEnumDevices;
    hr = pIWbemServices->CreateInstanceEnum(bstrClassName.get(), 0, nullptr, &pEnumDevices);
    if (FAILED(hr) || pEnumDevices == nullptr)
        return hr;

    // Loop over all devices
    IWbemClassObject* pDevices[20] = {};
    for (;;)
    {
        ULONG uReturned = 0;
        hr = pEnumDevices->Next(10000, _countof(pDevices), pDevices, &uReturned);
        if (FAILED(hr))
            return hr;

        if (uReturned == 0)
            break;

        assert(uReturned <= _countof(pDevices));
        _Analysis_assume_(uReturned <= _countof(pDevices));

        for (size_t iDevice = 0; iDevice < uReturned; ++iDevice)
        {
            if (!pDevices[iDevice])
                continue;

            // For each device, get its device ID
            hr = pDevices[iDevice]->Get(bstrDeviceID.get(), 0L, &var, nullptr, nullptr);
            if (SUCCEEDED(hr) && var.vt == VT_BSTR && var.bstrVal != nullptr)
            {
                // Check if the device ID contains "IG_".  If it does, then it's an XInput device
                // Unfortunately this information can not be found by just using DirectInput
                if (wcsstr(var.bstrVal, L"IG_"))
                {
                    // If it does, then get the VID/PID from var.bstrVal
                    DWORD dwPid = 0, dwVid = 0;
                    const WCHAR* strVid = wcsstr(var.bstrVal, L"VID_");
                    if (strVid && swscanf_s(strVid, L"VID_%4X", &dwVid) != 1)
                        dwVid = 0;
                    const WCHAR* strPid = wcsstr(var.bstrVal, L"PID_");
                    if (strPid && swscanf_s(strPid, L"PID_%4X", &dwPid) != 1)
                        dwPid = 0;

                    const DWORD dwVidPid = MAKELONG(dwVid, dwPid);

                    // Add the VID/PID to a linked list
                    auto pNewNode = new (std::nothrow) XINPUT_DEVICE_NODE;
                    if (pNewNode)
                    {
                        pNewNode->dwVidPid = dwVidPid;
                        pNewNode->pNext = g_pXInputDeviceList;
                        g_pXInputDeviceList = pNewNode;
                    }
                }
            }

            VariantClear(&var);
            SAFE_RELEASE(pDevices[iDevice]);
        }
    }

    VariantClear(&var);

    for (size_t iDevice = 0; iDevice < _countof(pDevices); ++iDevice)
        SAFE_RELEASE(pDevices[iDevice]);

    return hr;
}

void DirectInputModule::CleanupXInputDeviceList()
{
    //Clean up linked list
    XINPUT_DEVICE_NODE* p_node = g_pXInputDeviceList;
    while (p_node)
    {
        XINPUT_DEVICE_NODE* pDelete = p_node;
        p_node = p_node->pNext;
        SAFE_DELETE(pDelete);
    }
}
}
MAKE_MODULE(cross::DirectInputModule, DirectInputInterface)
