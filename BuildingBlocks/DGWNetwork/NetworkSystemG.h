#pragma once

#include "CECommon/Common/GameSystemBase.h"
#include "CloudClient.h" 
#include "Network.h"
#include "ECS/Develop/Framework/Types.h"

namespace cross {
    struct NetworkComponentG final : ecs::IComponent
    {
        CEComponentInternal(SystemType = NetworkSystemG)
        bool moveable{true};
        bool clickable{true};
        bool alive{false};
#if USE_NETWORK
        std::unique_ptr<CloudClient> client;
        std::shared_ptr<asio::io_context> context;
        std::unique_ptr<std::thread> thread;
#endif
        CEFunction(Reflect)
        static ecs::ComponentDesc* GetDesc();
    };

    class NetworkSystemG : public GameSystemBase
    {
        CESystemInternal(ComponentType = NetworkComponentG)
    public: 
        using NetworkComponentHandle = ecs::ComponentHandle<NetworkComponentG>;
        DEFINE_COMPONENT_READER_WRITER(NetworkComponentG, NetworkCompReader, NetworkCompWriter)

        // Serialize
        static SerializeNode SerializeComponent(ISerializeWorld* world, ecs::IComponent* comp);
        static void DeserializeComponent(ISerializeWorld* world, const DeserializeNode& json, ecs::IComponent* componentPtr);
        static void PostDeserializeComponent(const DeserializeNode& json, ecs::IComponent* comp, GameWorld* gameWorld, ecs::EntityID entityId);

        NetworkSystemG() {}

        CEFunction(Reflect)
        static NetworkSystemG* CreateInstance();

        virtual void Release() override;

        CEFunction(Reflect)
        void OnBuildUpdateTasks(FrameParam* frameParam) override;

        virtual void NotifyAddRenderSystemToRenderWorld() override {}

        virtual RenderSystemBase* GetRenderSystem() override
        {
            return nullptr;
        }

        virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override {}

    public:
        CEFunction(Script) 
        void Connect(const NetworkCompWriter& writer, std::string address, std::string port);
        CEFunction(Script)
        bool GetConnected(const NetworkCompReader& reader);
        CEFunction(Script)
        void SendStartDone(const NetworkCompReader& reader);
        CEFunction(Script)
        void SendResetDone(const NetworkCompReader& reader);
        CEFunction(Script)
        void SendSceneId(const NetworkCompReader& reader, int scene_id);
        CEFunction(Script)
        void SetTouchMoveable(const NetworkCompWriter& writer, bool moveable)
        {
            writer->moveable = moveable;
        }
        CEFunction(Script)
        void SetTouchClickable(const NetworkCompWriter& writer, bool clickable)
        {
            writer->clickable = clickable;
        }
    };
}