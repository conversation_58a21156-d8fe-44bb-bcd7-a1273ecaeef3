#include "BudgetStreamingModule.h"
#include "memoryhooker/Module.h"
IMPORT_MODULE
#pragma warning(push)
#pragma warning(disable : 4100)
void BudgetStreamingRegister();
void SystemRegisterR();
void SystemRegisterG();

namespace cross {
BudgetStreamingModule::BudgetStreamingModule()
{
    BudgetStreamingRegister();
    SystemRegisterG();
    SystemRegisterR();
}

gbf::ModuleCallReturnStatus BudgetStreamingModule::Init() 
{
    return gbf::ModuleCallReturnStatus::Succeed;
}


gbf::ModuleCallReturnStatus BudgetStreamingModule::Start() 
{
    return gbf::ModuleCallReturnStatus::Succeed;
}

void BudgetStreamingModule::Free() 
{
    delete this;
}
}   // namespace cross
#pragma warning(pop)
MAKE_MODULE(cross::BudgetStreamingModule, BudgetStreaming)
