using CEngine;

namespace CrossEditor
{
    class Streaming : Component
    {
        static string[] _NativeNames = { "cross::StreamComponentG" };

        //public StreamComponentG ComponentImp = new StreamComponentG();

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public Streaming()
        { }

        static int _ComponentOrder = 3;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        static int _GroupOrder = 4;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        [PropertyInfo(PropertyType = "Struct", ToolTips = "Streaming Controll Parameters")]
        public StreamingPolicyConfig StreamingPolicy
        {
            get
            {
                var ComponentImp = new StreamingPolicyConfig();
                if (Entity.World != null)
                {
                    BudgetStreamingSystemG.GetStreamPolicyConfig(Entity.World.GetNativePointer(), Entity.EntityID, ComponentImp);
                }
                return ComponentImp;
            }

            set
            {
                var ComponentImp = value;
                BudgetStreamingSystemG.SetStreamPolicyConfig(Entity.World.GetNativePointer(), Entity.EntityID, ComponentImp);
            }
        }

        [PropertyInfo(PropertyType = "Struct", ToolTips = "Streaming Controll Parameters")]
        public StreamingAssetSetting StreamingAssetSetting
        {
            get
            {
                var ComponentImp = new StreamingAssetSetting();
                if (Entity.World != null)
                {
                    BudgetStreamingSystemG.GetStreamingAssetSetting(Entity.World.GetNativePointer(), Entity.EntityID, ComponentImp);
                }
                return ComponentImp;
            }

            set
            {
                var ComponentImp = value;
                BudgetStreamingSystemG.SetStreamingAssetSetting(Entity.World.GetNativePointer(), Entity.EntityID, ComponentImp);
            }
        }
    }


    class StreamingAssetComponent : Component
    {
        static string[] _NativeNames = { "cross::StreamingAssetComponentG" };


        //public StreamingAssetComponentG ComponentImp = new StreamingAssetComponentG();

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public StreamingAssetComponent()
        { }

        static int _ComponentOrder = 3;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        static int _GroupOrder = 4;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        [PropertyInfo(PropertyType = "Struct", bAutoExpandStruct = true, ToolTips = "Streaming Controll Parameters")]
        public StreamingAssetSetting SubStreamingAssetSetting
        {
            get
            {
                var ComponentImp = new StreamingAssetSetting();
                if (Entity.World != null)
                {
                    BudgetStreamingSystemG.GetStreamingAssetSettingComponent(Entity.World.GetNativePointer(), Entity.EntityID, ComponentImp);
                }
                return ComponentImp;
            }

            set
            {
                var ComponentImp = value;
                BudgetStreamingSystemG.SetStreamingAssetSettingComponent(Entity.World.GetNativePointer(), Entity.EntityID, ComponentImp);
            }
        }
    }
}
