//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 4.0.2
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------

namespace CEngine {

using Newtonsoft.Json.Converters;
using Newtonsoft.Json;
using Clicross;

public class StreamingAssetComponentG : global::System.IDisposable {
  private global::System.Runtime.InteropServices.HandleRef swigCPtr;
  protected bool swigCMemOwn;

  public StreamingAssetComponentG(global::System.IntPtr cPtr, bool cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = new global::System.Runtime.InteropServices.HandleRef(this, cPtr);
  }

  public static global::System.Runtime.InteropServices.HandleRef getCPtr(StreamingAssetComponentG obj) {
    return (obj == null) ? new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero) : obj.swigCPtr;
  }

  ~StreamingAssetComponentG() {
    Dispose(false);
  }

  public void Dispose() {
    Dispose(true);
    global::System.GC.SuppressFinalize(this);
  }

  protected virtual void Dispose(bool disposing) {
    lock(this) {
      if (swigCPtr.Handle != global::System.IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          BudgetStreamingPINVOKE.delete_StreamingAssetComponentG(swigCPtr);
        }
        swigCPtr = new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero);
      }
    }
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public StreamingAssetSetting StreamingAssetSetting {
    set {
      BudgetStreamingPINVOKE.StreamingAssetComponentG_StreamingAssetSetting_set(swigCPtr, StreamingAssetSetting.getCPtr(value));
    } 
    get {
      global::System.IntPtr cPtr = BudgetStreamingPINVOKE.StreamingAssetComponentG_StreamingAssetSetting_get(swigCPtr);
      StreamingAssetSetting ret = (cPtr == global::System.IntPtr.Zero) ? null : new StreamingAssetSetting(cPtr, false);
      return ret;
    } 
  }

  public StreamingAssetComponentG() : this(BudgetStreamingPINVOKE.new_StreamingAssetComponentG(), true) {
  }

}

}
