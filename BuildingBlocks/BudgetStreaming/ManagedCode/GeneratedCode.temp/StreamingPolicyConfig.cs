//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 4.0.2
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------

namespace CEngine {

using Newtonsoft.Json.Converters;
using Newtonsoft.Json;
using Clicross;

public class StreamingPolicyConfig : global::System.IDisposable {
  private global::System.Runtime.InteropServices.HandleRef swigCPtr;
  protected bool swigCMemOwn;

  public StreamingPolicyConfig(global::System.IntPtr cPtr, bool cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = new global::System.Runtime.InteropServices.HandleRef(this, cPtr);
  }

  public static global::System.Runtime.InteropServices.HandleRef getCPtr(StreamingPolicyConfig obj) {
    return (obj == null) ? new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero) : obj.swigCPtr;
  }

  ~StreamingPolicyConfig() {
    Dispose(false);
  }

  public void Dispose() {
    Dispose(true);
    global::System.GC.SuppressFinalize(this);
  }

  protected virtual void Dispose(bool disposing) {
    lock(this) {
      if (swigCPtr.Handle != global::System.IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          BudgetStreamingPINVOKE.delete_StreamingPolicyConfig(swigCPtr);
        }
        swigCPtr = new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero);
      }
    }
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public bool mEnableInEditor {
    set {
      BudgetStreamingPINVOKE.StreamingPolicyConfig_mEnableInEditor_set(swigCPtr, value);
    } 
    get {
      bool ret = BudgetStreamingPINVOKE.StreamingPolicyConfig_mEnableInEditor_get(swigCPtr);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public bool mRequestTexture {
    set {
      BudgetStreamingPINVOKE.StreamingPolicyConfig_mRequestTexture_set(swigCPtr, value);
    } 
    get {
      bool ret = BudgetStreamingPINVOKE.StreamingPolicyConfig_mRequestTexture_get(swigCPtr);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public bool mRequestMesh {
    set {
      BudgetStreamingPINVOKE.StreamingPolicyConfig_mRequestMesh_set(swigCPtr, value);
    } 
    get {
      bool ret = BudgetStreamingPINVOKE.StreamingPolicyConfig_mRequestMesh_get(swigCPtr);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public bool mVisualize {
    set {
      BudgetStreamingPINVOKE.StreamingPolicyConfig_mVisualize_set(swigCPtr, value);
    } 
    get {
      bool ret = BudgetStreamingPINVOKE.StreamingPolicyConfig_mVisualize_get(swigCPtr);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public bool mAABBVis {
    set {
      BudgetStreamingPINVOKE.StreamingPolicyConfig_mAABBVis_set(swigCPtr, value);
    } 
    get {
      bool ret = BudgetStreamingPINVOKE.StreamingPolicyConfig_mAABBVis_get(swigCPtr);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public bool mOtherDistVis {
    set {
      BudgetStreamingPINVOKE.StreamingPolicyConfig_mOtherDistVis_set(swigCPtr, value);
    } 
    get {
      bool ret = BudgetStreamingPINVOKE.StreamingPolicyConfig_mOtherDistVis_get(swigCPtr);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public bool mVisualize_Ideal {
    set {
      BudgetStreamingPINVOKE.StreamingPolicyConfig_mVisualize_Ideal_set(swigCPtr, value);
    } 
    get {
      bool ret = BudgetStreamingPINVOKE.StreamingPolicyConfig_mVisualize_Ideal_get(swigCPtr);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public bool IORequestLOG {
    set {
      BudgetStreamingPINVOKE.StreamingPolicyConfig_IORequestLOG_set(swigCPtr, value);
    } 
    get {
      bool ret = BudgetStreamingPINVOKE.StreamingPolicyConfig_IORequestLOG_get(swigCPtr);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public bool mFreezeRequest {
    set {
      BudgetStreamingPINVOKE.StreamingPolicyConfig_mFreezeRequest_set(swigCPtr, value);
    } 
    get {
      bool ret = BudgetStreamingPINVOKE.StreamingPolicyConfig_mFreezeRequest_get(swigCPtr);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public float mLimitTextureGraphicsMemory {
    set {
      BudgetStreamingPINVOKE.StreamingPolicyConfig_mLimitTextureGraphicsMemory_set(swigCPtr, value);
    } 
    get {
      float ret = BudgetStreamingPINVOKE.StreamingPolicyConfig_mLimitTextureGraphicsMemory_get(swigCPtr);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public CameraUtility.ProjectMethod mAreaProjection {
    set {
      BudgetStreamingPINVOKE.StreamingPolicyConfig_mAreaProjection_set(swigCPtr, (int)value);
    } 
    get {
      CameraUtility.ProjectMethod ret = (CameraUtility.ProjectMethod)BudgetStreamingPINVOKE.StreamingPolicyConfig_mAreaProjection_get(swigCPtr);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public float PixelAreaDecay {
    set {
      BudgetStreamingPINVOKE.StreamingPolicyConfig_PixelAreaDecay_set(swigCPtr, value);
    } 
    get {
      float ret = BudgetStreamingPINVOKE.StreamingPolicyConfig_PixelAreaDecay_get(swigCPtr);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public bool mEnablePriority {
    set {
      BudgetStreamingPINVOKE.StreamingPolicyConfig_mEnablePriority_set(swigCPtr, value);
    } 
    get {
      bool ret = BudgetStreamingPINVOKE.StreamingPolicyConfig_mEnablePriority_get(swigCPtr);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public bool mEnableVisiblePriority {
    set {
      BudgetStreamingPINVOKE.StreamingPolicyConfig_mEnableVisiblePriority_set(swigCPtr, value);
    } 
    get {
      bool ret = BudgetStreamingPINVOKE.StreamingPolicyConfig_mEnableVisiblePriority_get(swigCPtr);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public float mInvisiblePenalty {
    set {
      BudgetStreamingPINVOKE.StreamingPolicyConfig_mInvisiblePenalty_set(swigCPtr, value);
    } 
    get {
      float ret = BudgetStreamingPINVOKE.StreamingPolicyConfig_mInvisiblePenalty_get(swigCPtr);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public bool nRejectIO {
    set {
      BudgetStreamingPINVOKE.StreamingPolicyConfig_nRejectIO_set(swigCPtr, value);
    } 
    get {
      bool ret = BudgetStreamingPINVOKE.StreamingPolicyConfig_nRejectIO_get(swigCPtr);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public int mStreamingThread {
    set {
      BudgetStreamingPINVOKE.StreamingPolicyConfig_mStreamingThread_set(swigCPtr, value);
    } 
    get {
      int ret = BudgetStreamingPINVOKE.StreamingPolicyConfig_mStreamingThread_get(swigCPtr);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public int mNumRequestPerframe {
    set {
      BudgetStreamingPINVOKE.StreamingPolicyConfig_mNumRequestPerframe_set(swigCPtr, value);
    } 
    get {
      int ret = BudgetStreamingPINVOKE.StreamingPolicyConfig_mNumRequestPerframe_get(swigCPtr);
      return ret;
    } 
  }

  public StreamingPolicyConfig() : this(BudgetStreamingPINVOKE.new_StreamingPolicyConfig(), true) {
  }

}

}
