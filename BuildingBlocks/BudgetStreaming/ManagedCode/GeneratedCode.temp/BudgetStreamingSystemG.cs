//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 4.0.2
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------

namespace CEngine {

using Newtonsoft.Json.Converters;
using Newtonsoft.Json;
using Clicross;

public class BudgetStreamingSystemG : global::System.IDisposable {
  private global::System.Runtime.InteropServices.HandleRef swigCPtr;
  protected bool swigCMemOwn;

  public BudgetStreamingSystemG(global::System.IntPtr cPtr, bool cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = new global::System.Runtime.InteropServices.HandleRef(this, cPtr);
  }

  public static global::System.Runtime.InteropServices.HandleRef getCPtr(BudgetStreamingSystemG obj) {
    return (obj == null) ? new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero) : obj.swigCPtr;
  }

  ~BudgetStreamingSystemG() {
    Dispose(false);
  }

  public void Dispose() {
    Dispose(true);
    global::System.GC.SuppressFinalize(this);
  }

  protected virtual void Dispose(bool disposing) {
    lock(this) {
      if (swigCPtr.Handle != global::System.IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          BudgetStreamingPINVOKE.delete_BudgetStreamingSystemG(swigCPtr);
        }
        swigCPtr = new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero);
      }
    }
  }

  public static void GetStreamComponent(global::System.IntPtr gworld, ulong component, StreamComponentG outValue) {
    BudgetStreamingPINVOKE.BudgetStreamingSystemG_GetStreamComponent(gworld, component, StreamComponentG.getCPtr(outValue));
    if (BudgetStreamingPINVOKE.SWIGPendingException.Pending) throw BudgetStreamingPINVOKE.SWIGPendingException.Retrieve();
  }

  public static void SetStreamComponent(global::System.IntPtr gworld, ulong component, StreamComponentG inValue) {
    BudgetStreamingPINVOKE.BudgetStreamingSystemG_SetStreamComponent(gworld, component, StreamComponentG.getCPtr(inValue));
    if (BudgetStreamingPINVOKE.SWIGPendingException.Pending) throw BudgetStreamingPINVOKE.SWIGPendingException.Retrieve();
  }

  public static void GetStreamingAssetComponent(global::System.IntPtr gworld, ulong component, StreamingAssetComponentG outValue) {
    BudgetStreamingPINVOKE.BudgetStreamingSystemG_GetStreamingAssetComponent(gworld, component, StreamingAssetComponentG.getCPtr(outValue));
    if (BudgetStreamingPINVOKE.SWIGPendingException.Pending) throw BudgetStreamingPINVOKE.SWIGPendingException.Retrieve();
  }

  public static void SetStreamingAssetComponent(global::System.IntPtr gworld, ulong component, StreamingAssetComponentG inValue) {
    BudgetStreamingPINVOKE.BudgetStreamingSystemG_SetStreamingAssetComponent(gworld, component, StreamingAssetComponentG.getCPtr(inValue));
    if (BudgetStreamingPINVOKE.SWIGPendingException.Pending) throw BudgetStreamingPINVOKE.SWIGPendingException.Retrieve();
  }

  public static void GetStreamPolicyConfig(global::System.IntPtr gworld, ulong component, StreamingPolicyConfig outValue) {
    BudgetStreamingPINVOKE.BudgetStreamingSystemG_GetStreamPolicyConfig(gworld, component, StreamingPolicyConfig.getCPtr(outValue));
    if (BudgetStreamingPINVOKE.SWIGPendingException.Pending) throw BudgetStreamingPINVOKE.SWIGPendingException.Retrieve();
  }

  public static void SetStreamPolicyConfig(global::System.IntPtr gworld, ulong component, StreamingPolicyConfig inValue) {
    BudgetStreamingPINVOKE.BudgetStreamingSystemG_SetStreamPolicyConfig(gworld, component, StreamingPolicyConfig.getCPtr(inValue));
    if (BudgetStreamingPINVOKE.SWIGPendingException.Pending) throw BudgetStreamingPINVOKE.SWIGPendingException.Retrieve();
  }

  public static void GetStreamingAssetSetting(global::System.IntPtr gworld, ulong component, StreamingAssetSetting outValue) {
    BudgetStreamingPINVOKE.BudgetStreamingSystemG_GetStreamingAssetSetting(gworld, component, StreamingAssetSetting.getCPtr(outValue));
    if (BudgetStreamingPINVOKE.SWIGPendingException.Pending) throw BudgetStreamingPINVOKE.SWIGPendingException.Retrieve();
  }

  public static void SetStreamingAssetSetting(global::System.IntPtr gworld, ulong component, StreamingAssetSetting inValue) {
    BudgetStreamingPINVOKE.BudgetStreamingSystemG_SetStreamingAssetSetting(gworld, component, StreamingAssetSetting.getCPtr(inValue));
    if (BudgetStreamingPINVOKE.SWIGPendingException.Pending) throw BudgetStreamingPINVOKE.SWIGPendingException.Retrieve();
  }

  public static void GetStreamingAssetSettingComponent(global::System.IntPtr gworld, ulong component, StreamingAssetSetting outValue) {
    BudgetStreamingPINVOKE.BudgetStreamingSystemG_GetStreamingAssetSettingComponent(gworld, component, StreamingAssetSetting.getCPtr(outValue));
    if (BudgetStreamingPINVOKE.SWIGPendingException.Pending) throw BudgetStreamingPINVOKE.SWIGPendingException.Retrieve();
  }

  public static void SetStreamingAssetSettingComponent(global::System.IntPtr gworld, ulong component, StreamingAssetSetting inValue) {
    BudgetStreamingPINVOKE.BudgetStreamingSystemG_SetStreamingAssetSettingComponent(gworld, component, StreamingAssetSetting.getCPtr(inValue));
    if (BudgetStreamingPINVOKE.SWIGPendingException.Pending) throw BudgetStreamingPINVOKE.SWIGPendingException.Retrieve();
  }

}

}
