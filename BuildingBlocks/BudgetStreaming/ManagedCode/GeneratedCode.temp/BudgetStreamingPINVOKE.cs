//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 4.0.2
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------

namespace CEngine {

class BudgetStreamingPINVOKE {

  protected class SWIGExceptionHelper {

    public delegate void ExceptionDelegate(string message);
    public delegate void ExceptionArgumentDelegate(string message, string paramName);

    static ExceptionDelegate applicationDelegate = new ExceptionDelegate(SetPendingApplicationException);
    static ExceptionDelegate arithmeticDelegate = new ExceptionDelegate(SetPendingArithmeticException);
    static ExceptionDelegate divideByZeroDelegate = new ExceptionDelegate(SetPendingDivideByZeroException);
    static ExceptionDelegate indexOutOfRangeDelegate = new ExceptionDelegate(SetPendingIndexOutOfRangeException);
    static ExceptionDelegate invalidCastDelegate = new ExceptionDelegate(SetPendingInvalidCastException);
    static ExceptionDelegate invalidOperationDelegate = new ExceptionDelegate(SetPendingInvalidOperationException);
    static ExceptionDelegate ioDelegate = new ExceptionDelegate(SetPendingIOException);
    static ExceptionDelegate nullReferenceDelegate = new ExceptionDelegate(SetPendingNullReferenceException);
    static ExceptionDelegate outOfMemoryDelegate = new ExceptionDelegate(SetPendingOutOfMemoryException);
    static ExceptionDelegate overflowDelegate = new ExceptionDelegate(SetPendingOverflowException);
    static ExceptionDelegate systemDelegate = new ExceptionDelegate(SetPendingSystemException);

    static ExceptionArgumentDelegate argumentDelegate = new ExceptionArgumentDelegate(SetPendingArgumentException);
    static ExceptionArgumentDelegate argumentNullDelegate = new ExceptionArgumentDelegate(SetPendingArgumentNullException);
    static ExceptionArgumentDelegate argumentOutOfRangeDelegate = new ExceptionArgumentDelegate(SetPendingArgumentOutOfRangeException);

    [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="SWIGRegisterExceptionCallbacks_BudgetStreaming")]
    public static extern void SWIGRegisterExceptionCallbacks_BudgetStreaming(
                                ExceptionDelegate applicationDelegate,
                                ExceptionDelegate arithmeticDelegate,
                                ExceptionDelegate divideByZeroDelegate, 
                                ExceptionDelegate indexOutOfRangeDelegate, 
                                ExceptionDelegate invalidCastDelegate,
                                ExceptionDelegate invalidOperationDelegate,
                                ExceptionDelegate ioDelegate,
                                ExceptionDelegate nullReferenceDelegate,
                                ExceptionDelegate outOfMemoryDelegate, 
                                ExceptionDelegate overflowDelegate, 
                                ExceptionDelegate systemExceptionDelegate);

    [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="SWIGRegisterExceptionArgumentCallbacks_BudgetStreaming")]
    public static extern void SWIGRegisterExceptionCallbacksArgument_BudgetStreaming(
                                ExceptionArgumentDelegate argumentDelegate,
                                ExceptionArgumentDelegate argumentNullDelegate,
                                ExceptionArgumentDelegate argumentOutOfRangeDelegate);

    static void SetPendingApplicationException(string message) {
      SWIGPendingException.Set(new global::System.ApplicationException(message, SWIGPendingException.Retrieve()));
    }
    static void SetPendingArithmeticException(string message) {
      SWIGPendingException.Set(new global::System.ArithmeticException(message, SWIGPendingException.Retrieve()));
    }
    static void SetPendingDivideByZeroException(string message) {
      SWIGPendingException.Set(new global::System.DivideByZeroException(message, SWIGPendingException.Retrieve()));
    }
    static void SetPendingIndexOutOfRangeException(string message) {
      SWIGPendingException.Set(new global::System.IndexOutOfRangeException(message, SWIGPendingException.Retrieve()));
    }
    static void SetPendingInvalidCastException(string message) {
      SWIGPendingException.Set(new global::System.InvalidCastException(message, SWIGPendingException.Retrieve()));
    }
    static void SetPendingInvalidOperationException(string message) {
      SWIGPendingException.Set(new global::System.InvalidOperationException(message, SWIGPendingException.Retrieve()));
    }
    static void SetPendingIOException(string message) {
      SWIGPendingException.Set(new global::System.IO.IOException(message, SWIGPendingException.Retrieve()));
    }
    static void SetPendingNullReferenceException(string message) {
      SWIGPendingException.Set(new global::System.NullReferenceException(message, SWIGPendingException.Retrieve()));
    }
    static void SetPendingOutOfMemoryException(string message) {
      SWIGPendingException.Set(new global::System.OutOfMemoryException(message, SWIGPendingException.Retrieve()));
    }
    static void SetPendingOverflowException(string message) {
      SWIGPendingException.Set(new global::System.OverflowException(message, SWIGPendingException.Retrieve()));
    }
    static void SetPendingSystemException(string message) {
      SWIGPendingException.Set(new global::System.SystemException(message, SWIGPendingException.Retrieve()));
    }

    static void SetPendingArgumentException(string message, string paramName) {
      SWIGPendingException.Set(new global::System.ArgumentException(message, paramName, SWIGPendingException.Retrieve()));
    }
    static void SetPendingArgumentNullException(string message, string paramName) {
      global::System.Exception e = SWIGPendingException.Retrieve();
      if (e != null) message = message + " Inner Exception: " + e.Message;
      SWIGPendingException.Set(new global::System.ArgumentNullException(paramName, message));
    }
    static void SetPendingArgumentOutOfRangeException(string message, string paramName) {
      global::System.Exception e = SWIGPendingException.Retrieve();
      if (e != null) message = message + " Inner Exception: " + e.Message;
      SWIGPendingException.Set(new global::System.ArgumentOutOfRangeException(paramName, message));
    }

    static SWIGExceptionHelper() {
      SWIGRegisterExceptionCallbacks_BudgetStreaming(
                                applicationDelegate,
                                arithmeticDelegate,
                                divideByZeroDelegate,
                                indexOutOfRangeDelegate,
                                invalidCastDelegate,
                                invalidOperationDelegate,
                                ioDelegate,
                                nullReferenceDelegate,
                                outOfMemoryDelegate,
                                overflowDelegate,
                                systemDelegate);

      SWIGRegisterExceptionCallbacksArgument_BudgetStreaming(
                                argumentDelegate,
                                argumentNullDelegate,
                                argumentOutOfRangeDelegate);
    }
  }

  protected static SWIGExceptionHelper swigExceptionHelper = new SWIGExceptionHelper();

  public class SWIGPendingException {
    [global::System.ThreadStatic]
    private static global::System.Exception pendingException = null;
    private static int numExceptionsPending = 0;
    private static global::System.Object exceptionsLock = null;

    public static bool Pending {
      get {
        bool pending = false;
        if (numExceptionsPending > 0)
          if (pendingException != null)
            pending = true;
        return pending;
      } 
    }

    public static void Set(global::System.Exception e) {
      if (pendingException != null)
        throw new global::System.ApplicationException("FATAL: An earlier pending exception from unmanaged code was missed and thus not thrown (" + pendingException.ToString() + ")", e);
      pendingException = e;
      lock(exceptionsLock) {
        numExceptionsPending++;
      }
    }

    public static global::System.Exception Retrieve() {
      global::System.Exception e = null;
      if (numExceptionsPending > 0) {
        if (pendingException != null) {
          e = pendingException;
          pendingException = null;
          lock(exceptionsLock) {
            numExceptionsPending--;
          }
        }
      }
      return e;
    }

    static SWIGPendingException() {
      exceptionsLock = new global::System.Object();
    }
  }


  protected class SWIGStringHelper {

    public delegate string SWIGStringDelegate(string message);
    static SWIGStringDelegate stringDelegate = new SWIGStringDelegate(CreateString);

    [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="SWIGRegisterStringCallback_BudgetStreaming")]
    public static extern void SWIGRegisterStringCallback_BudgetStreaming(SWIGStringDelegate stringDelegate);

    static string CreateString(string cString) {
      return cString;
    }

    static SWIGStringHelper() {
      SWIGRegisterStringCallback_BudgetStreaming(stringDelegate);
    }
  }

  static protected SWIGStringHelper swigStringHelper = new SWIGStringHelper();


  static BudgetStreamingPINVOKE() {
  }


  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingAssetSetting_mLeastLOD_set")]
  public static extern void StreamingAssetSetting_mLeastLOD_set(global::System.Runtime.InteropServices.HandleRef jarg1, int jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingAssetSetting_mLeastLOD_get")]
  public static extern int StreamingAssetSetting_mLeastLOD_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingAssetSetting_LODDist_set")]
  public static extern void StreamingAssetSetting_LODDist_set(global::System.Runtime.InteropServices.HandleRef jarg1, global::System.Runtime.InteropServices.HandleRef jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingAssetSetting_LODDist_get")]
  public static extern global::System.IntPtr StreamingAssetSetting_LODDist_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingAssetSetting_LODTextureProperty_set")]
  public static extern void StreamingAssetSetting_LODTextureProperty_set(global::System.Runtime.InteropServices.HandleRef jarg1, global::System.Runtime.InteropServices.HandleRef jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingAssetSetting_LODTextureProperty_get")]
  public static extern global::System.IntPtr StreamingAssetSetting_LODTextureProperty_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingAssetSetting_mTexturePriority_set")]
  public static extern void StreamingAssetSetting_mTexturePriority_set(global::System.Runtime.InteropServices.HandleRef jarg1, global::System.Runtime.InteropServices.HandleRef jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingAssetSetting_mTexturePriority_get")]
  public static extern global::System.IntPtr StreamingAssetSetting_mTexturePriority_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_new_StreamingAssetSetting")]
  public static extern global::System.IntPtr new_StreamingAssetSetting();

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_delete_StreamingAssetSetting")]
  public static extern void delete_StreamingAssetSetting(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mEnableInEditor_set")]
  public static extern void StreamingPolicyConfig_mEnableInEditor_set(global::System.Runtime.InteropServices.HandleRef jarg1, bool jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mEnableInEditor_get")]
  public static extern bool StreamingPolicyConfig_mEnableInEditor_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mRequestTexture_set")]
  public static extern void StreamingPolicyConfig_mRequestTexture_set(global::System.Runtime.InteropServices.HandleRef jarg1, bool jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mRequestTexture_get")]
  public static extern bool StreamingPolicyConfig_mRequestTexture_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mRequestMesh_set")]
  public static extern void StreamingPolicyConfig_mRequestMesh_set(global::System.Runtime.InteropServices.HandleRef jarg1, bool jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mRequestMesh_get")]
  public static extern bool StreamingPolicyConfig_mRequestMesh_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mVisualize_set")]
  public static extern void StreamingPolicyConfig_mVisualize_set(global::System.Runtime.InteropServices.HandleRef jarg1, bool jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mVisualize_get")]
  public static extern bool StreamingPolicyConfig_mVisualize_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mAABBVis_set")]
  public static extern void StreamingPolicyConfig_mAABBVis_set(global::System.Runtime.InteropServices.HandleRef jarg1, bool jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mAABBVis_get")]
  public static extern bool StreamingPolicyConfig_mAABBVis_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mOtherDistVis_set")]
  public static extern void StreamingPolicyConfig_mOtherDistVis_set(global::System.Runtime.InteropServices.HandleRef jarg1, bool jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mOtherDistVis_get")]
  public static extern bool StreamingPolicyConfig_mOtherDistVis_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mVisualize_Ideal_set")]
  public static extern void StreamingPolicyConfig_mVisualize_Ideal_set(global::System.Runtime.InteropServices.HandleRef jarg1, bool jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mVisualize_Ideal_get")]
  public static extern bool StreamingPolicyConfig_mVisualize_Ideal_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_IORequestLOG_set")]
  public static extern void StreamingPolicyConfig_IORequestLOG_set(global::System.Runtime.InteropServices.HandleRef jarg1, bool jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_IORequestLOG_get")]
  public static extern bool StreamingPolicyConfig_IORequestLOG_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mFreezeRequest_set")]
  public static extern void StreamingPolicyConfig_mFreezeRequest_set(global::System.Runtime.InteropServices.HandleRef jarg1, bool jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mFreezeRequest_get")]
  public static extern bool StreamingPolicyConfig_mFreezeRequest_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mLimitTextureGraphicsMemory_set")]
  public static extern void StreamingPolicyConfig_mLimitTextureGraphicsMemory_set(global::System.Runtime.InteropServices.HandleRef jarg1, float jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mLimitTextureGraphicsMemory_get")]
  public static extern float StreamingPolicyConfig_mLimitTextureGraphicsMemory_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mAreaProjection_set")]
  public static extern void StreamingPolicyConfig_mAreaProjection_set(global::System.Runtime.InteropServices.HandleRef jarg1, int jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mAreaProjection_get")]
  public static extern int StreamingPolicyConfig_mAreaProjection_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_PixelAreaDecay_set")]
  public static extern void StreamingPolicyConfig_PixelAreaDecay_set(global::System.Runtime.InteropServices.HandleRef jarg1, float jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_PixelAreaDecay_get")]
  public static extern float StreamingPolicyConfig_PixelAreaDecay_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mEnablePriority_set")]
  public static extern void StreamingPolicyConfig_mEnablePriority_set(global::System.Runtime.InteropServices.HandleRef jarg1, bool jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mEnablePriority_get")]
  public static extern bool StreamingPolicyConfig_mEnablePriority_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mEnableVisiblePriority_set")]
  public static extern void StreamingPolicyConfig_mEnableVisiblePriority_set(global::System.Runtime.InteropServices.HandleRef jarg1, bool jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mEnableVisiblePriority_get")]
  public static extern bool StreamingPolicyConfig_mEnableVisiblePriority_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mInvisiblePenalty_set")]
  public static extern void StreamingPolicyConfig_mInvisiblePenalty_set(global::System.Runtime.InteropServices.HandleRef jarg1, float jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mInvisiblePenalty_get")]
  public static extern float StreamingPolicyConfig_mInvisiblePenalty_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_nRejectIO_set")]
  public static extern void StreamingPolicyConfig_nRejectIO_set(global::System.Runtime.InteropServices.HandleRef jarg1, bool jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_nRejectIO_get")]
  public static extern bool StreamingPolicyConfig_nRejectIO_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mStreamingThread_set")]
  public static extern void StreamingPolicyConfig_mStreamingThread_set(global::System.Runtime.InteropServices.HandleRef jarg1, int jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mStreamingThread_get")]
  public static extern int StreamingPolicyConfig_mStreamingThread_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mNumRequestPerframe_set")]
  public static extern void StreamingPolicyConfig_mNumRequestPerframe_set(global::System.Runtime.InteropServices.HandleRef jarg1, int jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingPolicyConfig_mNumRequestPerframe_get")]
  public static extern int StreamingPolicyConfig_mNumRequestPerframe_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_new_StreamingPolicyConfig")]
  public static extern global::System.IntPtr new_StreamingPolicyConfig();

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_delete_StreamingPolicyConfig")]
  public static extern void delete_StreamingPolicyConfig(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamComponentG_StreamingPolicy_set")]
  public static extern void StreamComponentG_StreamingPolicy_set(global::System.Runtime.InteropServices.HandleRef jarg1, global::System.Runtime.InteropServices.HandleRef jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamComponentG_StreamingPolicy_get")]
  public static extern global::System.IntPtr StreamComponentG_StreamingPolicy_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamComponentG_StreamingAssetSetting_set")]
  public static extern void StreamComponentG_StreamingAssetSetting_set(global::System.Runtime.InteropServices.HandleRef jarg1, global::System.Runtime.InteropServices.HandleRef jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamComponentG_StreamingAssetSetting_get")]
  public static extern global::System.IntPtr StreamComponentG_StreamingAssetSetting_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_new_StreamComponentG")]
  public static extern global::System.IntPtr new_StreamComponentG();

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_delete_StreamComponentG")]
  public static extern void delete_StreamComponentG(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingAssetComponentG_StreamingAssetSetting_set")]
  public static extern void StreamingAssetComponentG_StreamingAssetSetting_set(global::System.Runtime.InteropServices.HandleRef jarg1, global::System.Runtime.InteropServices.HandleRef jarg2);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_StreamingAssetComponentG_StreamingAssetSetting_get")]
  public static extern global::System.IntPtr StreamingAssetComponentG_StreamingAssetSetting_get(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_new_StreamingAssetComponentG")]
  public static extern global::System.IntPtr new_StreamingAssetComponentG();

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_delete_StreamingAssetComponentG")]
  public static extern void delete_StreamingAssetComponentG(global::System.Runtime.InteropServices.HandleRef jarg1);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_BudgetStreamingSystemG_GetStreamComponent")]
  public static extern void BudgetStreamingSystemG_GetStreamComponent(global::System.IntPtr jarg1, ulong jarg2, global::System.Runtime.InteropServices.HandleRef jarg3);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_BudgetStreamingSystemG_SetStreamComponent")]
  public static extern void BudgetStreamingSystemG_SetStreamComponent(global::System.IntPtr jarg1, ulong jarg2, global::System.Runtime.InteropServices.HandleRef jarg3);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_BudgetStreamingSystemG_GetStreamingAssetComponent")]
  public static extern void BudgetStreamingSystemG_GetStreamingAssetComponent(global::System.IntPtr jarg1, ulong jarg2, global::System.Runtime.InteropServices.HandleRef jarg3);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_BudgetStreamingSystemG_SetStreamingAssetComponent")]
  public static extern void BudgetStreamingSystemG_SetStreamingAssetComponent(global::System.IntPtr jarg1, ulong jarg2, global::System.Runtime.InteropServices.HandleRef jarg3);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_BudgetStreamingSystemG_GetStreamPolicyConfig")]
  public static extern void BudgetStreamingSystemG_GetStreamPolicyConfig(global::System.IntPtr jarg1, ulong jarg2, global::System.Runtime.InteropServices.HandleRef jarg3);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_BudgetStreamingSystemG_SetStreamPolicyConfig")]
  public static extern void BudgetStreamingSystemG_SetStreamPolicyConfig(global::System.IntPtr jarg1, ulong jarg2, global::System.Runtime.InteropServices.HandleRef jarg3);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_BudgetStreamingSystemG_GetStreamingAssetSetting")]
  public static extern void BudgetStreamingSystemG_GetStreamingAssetSetting(global::System.IntPtr jarg1, ulong jarg2, global::System.Runtime.InteropServices.HandleRef jarg3);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_BudgetStreamingSystemG_SetStreamingAssetSetting")]
  public static extern void BudgetStreamingSystemG_SetStreamingAssetSetting(global::System.IntPtr jarg1, ulong jarg2, global::System.Runtime.InteropServices.HandleRef jarg3);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_BudgetStreamingSystemG_GetStreamingAssetSettingComponent")]
  public static extern void BudgetStreamingSystemG_GetStreamingAssetSettingComponent(global::System.IntPtr jarg1, ulong jarg2, global::System.Runtime.InteropServices.HandleRef jarg3);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_BudgetStreamingSystemG_SetStreamingAssetSettingComponent")]
  public static extern void BudgetStreamingSystemG_SetStreamingAssetSettingComponent(global::System.IntPtr jarg1, ulong jarg2, global::System.Runtime.InteropServices.HandleRef jarg3);

  [global::System.Runtime.InteropServices.DllImport("BudgetStreaming", EntryPoint="CSharp_CEngine_delete_BudgetStreamingSystemG")]
  public static extern void delete_BudgetStreamingSystemG(global::System.Runtime.InteropServices.HandleRef jarg1);
}

}
