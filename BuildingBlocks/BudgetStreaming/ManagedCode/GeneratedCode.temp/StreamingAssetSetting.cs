//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 4.0.2
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------

namespace CEngine {

using Newtonsoft.Json.Converters;
using Newtonsoft.Json;
using Clicross;

public class StreamingAssetSetting : global::System.IDisposable {
  private global::System.Runtime.InteropServices.HandleRef swigCPtr;
  protected bool swigCMemOwn;

  public StreamingAssetSetting(global::System.IntPtr cPtr, bool cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = new global::System.Runtime.InteropServices.HandleRef(this, cPtr);
  }

  public static global::System.Runtime.InteropServices.HandleRef getCPtr(StreamingAssetSetting obj) {
    return (obj == null) ? new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero) : obj.swigCPtr;
  }

  ~StreamingAssetSetting() {
    Dispose(false);
  }

  public void Dispose() {
    Dispose(true);
    global::System.GC.SuppressFinalize(this);
  }

  protected virtual void Dispose(bool disposing) {
    lock(this) {
      if (swigCPtr.Handle != global::System.IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          BudgetStreamingPINVOKE.delete_StreamingAssetSetting(swigCPtr);
        }
        swigCPtr = new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero);
      }
    }
  }

  [PropertyInfo(PropertyType = "Auto", ToolTips = "Type")]
  public int mLeastLOD {
    set {
      BudgetStreamingPINVOKE.StreamingAssetSetting_mLeastLOD_set(swigCPtr, value);
    } 
    get {
      int ret = BudgetStreamingPINVOKE.StreamingAssetSetting_mLeastLOD_get(swigCPtr);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "List", ToolTips = "Type")]
  public vector_float LODDist {
    set {
      BudgetStreamingPINVOKE.StreamingAssetSetting_LODDist_set(swigCPtr, vector_float.getCPtr(value));
    } 
    get {
      global::System.IntPtr cPtr = BudgetStreamingPINVOKE.StreamingAssetSetting_LODDist_get(swigCPtr);
      vector_float ret = (cPtr == global::System.IntPtr.Zero) ? null : new vector_float(cPtr, false);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "List", ToolTips = "Type")]
  public vector_string LODTextureProperty {
    set {
      BudgetStreamingPINVOKE.StreamingAssetSetting_LODTextureProperty_set(swigCPtr, vector_string.getCPtr(value));
    } 
    get {
      global::System.IntPtr cPtr = BudgetStreamingPINVOKE.StreamingAssetSetting_LODTextureProperty_get(swigCPtr);
      vector_string ret = (cPtr == global::System.IntPtr.Zero) ? null : new vector_string(cPtr, false);
      return ret;
    } 
  }

  [PropertyInfo(PropertyType = "List", ToolTips = "Type")]
  public vector_float mTexturePriority {
    set {
      BudgetStreamingPINVOKE.StreamingAssetSetting_mTexturePriority_set(swigCPtr, vector_float.getCPtr(value));
    } 
    get {
      global::System.IntPtr cPtr = BudgetStreamingPINVOKE.StreamingAssetSetting_mTexturePriority_get(swigCPtr);
      vector_float ret = (cPtr == global::System.IntPtr.Zero) ? null : new vector_float(cPtr, false);
      return ret;
    } 
  }

  public StreamingAssetSetting() : this(BudgetStreamingPINVOKE.new_StreamingAssetSetting(), true) {
  }

}

}
