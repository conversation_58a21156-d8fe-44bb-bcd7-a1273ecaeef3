using CEngine;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    class ProceduralSceneKaplaTower
    {
        static ProceduralSceneKaplaTower _Instance = new ProceduralSceneKaplaTower();
        public static ProceduralSceneKaplaTower GetInstance()
        {
            return _Instance;
        }

        public void GenerateKaplaTowerAndAddToWorld(World World)
        {
            Entity KaplaTowerEntity = CreateEntity(World, new Vector3f(0, 0, 0), new Quaternionf(0, 0, 0, 1), new Vector3f(1, 1, 1), "KaplaTower");

            Vector3f BoxHalfExt = new Vector3f(8f, 25f, 100f);
            Vector3f CenterPos = new Vector3f(0, 0, 0);

            uint TowerI = 0;
            CreateCylindricalTower(World, KaplaTowerEntity, 40, 250, 15/*35*/, BoxHalfExt, CenterPos, TowerI++);
            CreateCylindricalTower(World, KaplaTowerEntity, 48, 450, 8, BoxHalfExt, CenterPos, TowerI++);
            CreateCylindricalTower(World, KaplaTowerEntity, 72, 650, 2, BoxHalfExt, CenterPos, TowerI++);
            //CreateCylindricalTower(World, KaplaTowerEntity, 96, 550, 2, BoxHalfExt, CenterPos, TowerI++);


            CenterPos.Z = -2000;
            CreateCylindricalTower(World, KaplaTowerEntity, 30, 250, 3, BoxHalfExt, CenterPos, TowerI++);
            CreateTwistTower(World, KaplaTowerEntity, 40, 6, BoxHalfExt, CenterPos, TowerI++);

            CenterPos.Z = 2000;
            CreateCylindricalTower(World, KaplaTowerEntity, 30, 250, 3, BoxHalfExt, CenterPos, TowerI++);
            CreateTwistTower(World, KaplaTowerEntity, 40, 6, BoxHalfExt, CenterPos, TowerI++);

            CenterPos.Z = 0;
            CenterPos.X = -2000;
            CreateCylindricalTower(World, KaplaTowerEntity, 30, 250, 3, BoxHalfExt, CenterPos, TowerI++);
            CreateTwistTower(World, KaplaTowerEntity, 40, 6, BoxHalfExt, CenterPos, TowerI++);

            CenterPos.X = 2000;
            CreateCylindricalTower(World, KaplaTowerEntity, 30, 250, 3, BoxHalfExt, CenterPos, TowerI++);
            CreateTwistTower(World, KaplaTowerEntity, 40, 6, BoxHalfExt, CenterPos, TowerI++);

            Quaternionf Identity = new Quaternionf(0, 0, 0, 1);
            Entity Ground = CreateEntity(World, new Vector3f(0, -50.01f, 0), Identity, new Vector3f(50, 1, 50), "Ground", false, "EngineResource/Model/Cube.nda");
            Physics GroundPhy = Ground.CreateComponent<Physics>();
            GroundPhy.CollisionType = CollisionTypeValue.WorldStatic;
            GroundPhy.BlockMask.BlockWith(CollisionTypeValue.WorldStatic, CollisionTypeValue.WorldDynamic, CollisionTypeValue.Actor);

            Entity Wall1 = CreateEntity(World, new Vector3f(0, 500, 2500), Identity, new Vector3f(50, 10, 1f), "Wall1", false, "EngineResource/Model/Cube.nda");
            Physics Wall1Phy = Wall1.CreateComponent<Physics>();
            Wall1Phy.CollisionType = CollisionTypeValue.WorldStatic;
            Wall1Phy.BlockMask.BlockWith(CollisionTypeValue.WorldStatic, CollisionTypeValue.WorldDynamic, CollisionTypeValue.Actor);

            Entity Wall2 = CreateEntity(World, new Vector3f(2500, 500, 0), Identity, new Vector3f(1f, 10, 50), "Wall2", false, "EngineResource/Model/Cube.nda");
            Physics Wall2Phy = Wall2.CreateComponent<Physics>();
            Wall2Phy.CollisionType = CollisionTypeValue.WorldStatic;
            Wall2Phy.BlockMask.BlockWith(CollisionTypeValue.WorldStatic, CollisionTypeValue.WorldDynamic, CollisionTypeValue.Actor);

            Entity Wall3 = CreateEntity(World, new Vector3f(0, 500, -2500), Identity, new Vector3f(50, 10, 1f), "Wall3", false, "EngineResource/Model/Cube.nda");
            Physics Wall3Phy = Wall3.CreateComponent<Physics>();
            Wall3Phy.CollisionType = CollisionTypeValue.WorldStatic;
            Wall3Phy.BlockMask.BlockWith(CollisionTypeValue.WorldStatic, CollisionTypeValue.WorldDynamic, CollisionTypeValue.Actor);

            Entity Wall4 = CreateEntity(World, new Vector3f(-2500, 500, 0), Identity, new Vector3f(1f, 10, 50), "Wall4", false, "EngineResource/Model/Cube.nda");
            Physics Wall4Phy = Wall4.CreateComponent<Physics>();
            Wall4Phy.CollisionType = CollisionTypeValue.WorldStatic;
            Wall4Phy.BlockMask.BlockWith(CollisionTypeValue.WorldStatic, CollisionTypeValue.WorldDynamic, CollisionTypeValue.Actor);

            Entity SkyBox = CreateEntity(World, new Vector3f(0, 0, 0), Identity, new Vector3f(10000, 10000, 10000), "SkyBox");
            List<Model> SkyBoxModelList = new List<Model>();
            SkyBoxModelList.Add(new Model("EngineResource/Model/Sphere.nda", "Contents/Material/SkyScattering.nda"));
            SkyBox.CreateComponent<ModelComponent>().Models = SkyBoxModelList;

            Entity Camera = World.Root.FindChildByName("GameCamera");
            ((Script)Camera.GetComponent(typeof(Script))).Path = "Contents/lua/KalpaTower/OrbitFollowCameraController.lua";
            Camera.GetTransformComponent().Translation = new Vector3f(-1284, 139, -1817);
            Camera.GetTransformComponent().Rotation = new Vector3f(0, 19, 0);

            Entity Mannequin = CreateEntity(World, new Vector3f(-1284, 139, -1817), Identity, new Vector3f(1, 1, 1), "Mannequin", false, "Contents/Model/Mannequin/Mannequin.nda");
            Mannequin.GetTransformComponent().Translation = new Vector3f(-1284, 139, -1817);
            Mannequin.GetTransformComponent().Rotation = new Vector3f(0, 19, 0);
            Mannequin.CreateComponent<Animator>();
            Mannequin.CreateComponent<Script>().Path = "Contents/lua/KalpaTower/Character.lua";
            Mannequin.CreateComponent<Skeleton>();

            Entity LeftFoot = CreateEntity(World, new Vector3f(-1284, 139, -1817), Identity, new Vector3f(1, 1, 1), "LeftFoot", false, null, Mannequin);
            LeftFoot.CreateComponent<Script>().Path = "Contents/lua/Demo_Anim_Mannequin/FootRay.lua";
            LeftFoot.CreateComponent<SkeltSocket>();

            Entity RightFoot = CreateEntity(World, new Vector3f(-1284, 139, -1817), Identity, new Vector3f(1, 1, 1), "RightFoot", false, null, Mannequin);
            RightFoot.CreateComponent<SkeltSocket>();


            HierarchyUI.GetInstance().UpdateHierarchy();
        }
        static float NextFloat(float min, float max)
        {
            System.Random random = new System.Random();
            double val = (random.NextDouble() * (max - min) + min);
            return (float)val;
        }
        private Entity CreateEntity(World World, Vector3f Trans, Quaternionf Rotation, Vector3f Scale, string Name, bool CreatePhysics = false, string ModelPath = null, Entity Parent = null, bool randomcolor = false)
        {
            Entity NewEntity = World.CreateEntity();

            Transform Transform = NewEntity.CreateComponent<Transform>();//At least one transform Component
            Transform.Translation = Trans;
            Transform.Scale = Scale;
            Transform.Rotation = Quaternionf.ToEuler(Rotation);

            if (Parent != null)
                Parent.AddChildEntity(NewEntity);
            else
                World.Root.AddChildEntity(NewEntity);

            NewEntity.SetName(Name);
            Clicross.GameWorldInterface.World_SetEntityName(World._WorldInterface, NewEntity.EntityID, Name);

            NewEntity.RuntimeJointToParent();

            if (ModelPath != null)
            {
                ModelComponent ModelComponent = NewEntity.CreateComponent<ModelComponent>();
                List<Model> ModelList = new List<Model>();

                ModelList.Add(new Model(ModelPath));

                ModelComponent.Models = ModelList;

                if (randomcolor)
                {
                    string defaultFX = CrossEngineApi.GetSettingManager().GetRenderPipelineSettingForEditor().DefaultFX;
                    Material randmat = new Material(Clicross.resource.Material.MaterialCreateMaterial(defaultFX));
                    randmat.Refresh();
                    randmat.SetPropertyValue("BaseColor", new Vector4f(NextFloat(0, 1), NextFloat(0, 1), NextFloat(0, 1), 1));
                    randmat.SetPropertyValue("COLOR_MAP", false);
                    randmat.SetPropertyValue("Metalic", 0.8f);
                    randmat.SetPropertyValue("Specular", 1.0f);
                    randmat.SetPropertyValue("Roughness", 0.4f);
                    uint SubMeshCount = ModelSystemG.GetSubModelCount(NewEntity.World._World, NewEntity.EntityID, 0, 0);
                    for (int i = 0; i < SubMeshCount; i++)
                    {
                        Clicross.ModelSystemG.Model_SetMaterialInstance(NewEntity.World._WorldInterface, NewEntity.EntityID, randmat.ResourcePtr as Clicross.resource.MaterialInterface, i, 0);
                    }
                }
            }

            if (CreatePhysics)
            {
                Physics PhysicsComponent = NewEntity.CreateComponent<Physics>();
                PhysicsComponent.CollisionType = CollisionTypeValue.WorldDynamic;
                PhysicsComponent.BlockMask.BlockWith(CollisionTypeValue.WorldDynamic, CollisionTypeValue.WorldDynamic, CollisionTypeValue.WorldDynamic);
                PhysicsComponent.MaxDepenetrationVelocity = 20.0f;
                PhysicsComponent.MassSpaceInertiaTensorMultiplier = new Float3(4.0f, 4.0f, 4.0f);
                PhysicsComponent.StartupAsleep = true;
            }

            EditorScene.GetInstance().SetDirty();

            return NewEntity;
        }

        private Entity CreateCube(World World, string Name, Entity Parent, Vector3f Trans, Quaternionf Rotation, Vector3f HalfExt, float Density, bool randomcolor)
        {
            const float CubeHalfExtInModule = 50.0f;

            Entity NewEntity = CreateEntity(World, Trans, Rotation
                , new Vector3f(HalfExt.X / CubeHalfExtInModule, HalfExt.Y / CubeHalfExtInModule, HalfExt.Z / CubeHalfExtInModule)
                , Name, true, "EngineResource/Model/Cube.nda", Parent, randomcolor);
            NewEntity.GetPhysicsComponent().UpdateMassAndInertia(Density);
            return NewEntity;
        }

        private void CreateCylindricalTower(World World, Entity Parent, uint RadialPoints, float Radius, uint Height, Vector3f HalfExt, Vector3f CenterPos, uint TowerI)
        {
            float StartHeight = 0.0f;
            Vector3f UpAxis = new Vector3f(0.0f, 1.0f, 0.0f);
            float Density = 0.0015f;

            for (uint i = 0; i < Height; ++i)
            {
                for (uint a = 0; a < RadialPoints; ++a)
                {
                    float angle = (float)Math.PI * 2 * (float)a / (float)RadialPoints;
                    Vector3f innerPos = new Vector3f((float)Math.Cos(angle) * Radius, HalfExt.Y + StartHeight, (float)Math.Sin(angle) * Radius);
                    Quaternionf rot = FromAxisAngle((float)Math.PI / 2.0f - angle, UpAxis);
                    CreateCube(World, "Tower_" + TowerI + "_Cylindrial_" + i + "_RadialPoint_" + a, Parent, CenterPos + innerPos, rot, HalfExt, Density, true);
                }

                float innerCircumference = (Radius - (HalfExt.Z - HalfExt.X)) * (float)Math.PI * 2.0f;
                float midCircumference = Radius * (float)Math.PI * 2.0f;
                float outerCircumference = (Radius + (HalfExt.Z - HalfExt.X)) * (float)Math.PI * 2.0f;

                uint nbInnerSlabs = (uint)(innerCircumference / (HalfExt.Z * 2));
                uint nbMidSlabs = (uint)(midCircumference / (HalfExt.Z * 2));
                uint nbOuterSlabs = (uint)(outerCircumference / (HalfExt.Z * 2));


                for (uint a = 0; a < nbInnerSlabs; a++)
                {
                    float Angle = (float)Math.PI * 2.0f * (float)a / (float)nbInnerSlabs;
                    Vector3f InnerPos = new Vector3f((float)Math.Cos(Angle) * (Radius - (HalfExt.Z - HalfExt.X)), 3.0f * HalfExt.Y + StartHeight, (float)Math.Sin(Angle) * (Radius - (HalfExt.Z - HalfExt.X)));
                    Quaternionf Rot = FromAxisAngle(-Angle, UpAxis);
                    CreateCube(World, "Tower_" + TowerI + "_Cylindrial_" + i + "_InnerSlab_" + a, Parent, CenterPos + InnerPos, Rot, HalfExt, Density, true);
                }

                for (uint a = 0; a < nbMidSlabs; a++)
                {
                    float Angle = (float)Math.PI * 2.0f * (float)(a) / (float)nbMidSlabs;
                    Vector3f InnerPos = new Vector3f((float)Math.Cos(Angle) * Radius, 3.0f * HalfExt.Y + StartHeight, (float)Math.Sin(Angle) * (Radius));
                    Quaternionf Rot = FromAxisAngle(-Angle, UpAxis);
                    CreateCube(World, "Tower_" + TowerI + "_Cylindrial_" + i + "_MidSlab_" + a, Parent, CenterPos + InnerPos, Rot, HalfExt, Density, true);
                }

                for (uint a = 0; a < nbOuterSlabs; a++)
                {
                    float Angle = (float)Math.PI * 2.0f * (float)a / (float)nbOuterSlabs;
                    Vector3f outerPos = new Vector3f((float)Math.Cos(Angle) * (Radius + (HalfExt.Z - HalfExt.X)), 3.0f * HalfExt.Y + StartHeight, (float)Math.Sin(Angle) * (Radius + (HalfExt.Z - HalfExt.X)));
                    Quaternionf Rot = FromAxisAngle(-Angle, UpAxis);
                    CreateCube(World, "Tower_" + TowerI + "_Cylindrial_" + i + "_OuterSlab_" + a, Parent, CenterPos + outerPos, Rot, HalfExt, Density, true);
                }

                StartHeight += 4.0f * HalfExt.Y;
                Density *= 0.975f;
            }

            float MidCircumferenceLid = (Radius - HalfExt.Z) * (float)Math.PI * 2.0f;
            uint MidSlabsLid = (uint)(MidCircumferenceLid / (HalfExt.Y * 2.0f));

            Quaternionf baseRotation = FromAxisAngle((float)Math.PI / 2.0f, new Vector3f(0.0f, 0.0f, 1.0f));

            for (uint a = 0; a < MidSlabsLid; ++a)
            {
                float Angle = (float)Math.PI * 2.0f * (float)a / (float)MidSlabsLid;
                Vector3f InnerPos = new Vector3f((float)Math.Cos(Angle) * Radius, HalfExt.X + StartHeight, (float)Math.Sin(Angle) * Radius);
                Quaternionf Rot = FromAxisAngle((float)Math.PI / 2.0f - Angle, UpAxis);
                CreateCube(World, "Tower_" + TowerI + "_CylindrialLidSlab_" + a, Parent, CenterPos + InnerPos, Quaternionf.Concatenate(baseRotation, Rot), HalfExt, Density, true);
            }
        }

        private void CreateTwistTower(World World, Entity Parent, uint Height, uint BlocksPerLayer, Vector3f HalfExt, Vector3f CenterPos, uint TowerI)
        {
            Quaternionf Rotation = new Quaternionf(0, 0, 0, 1);
            Quaternionf rotationDelta = FromAxisAngle((float)Math.PI / 10.0f, new Vector3f(0, 1, 0));
            float Density = 0.0015f;

            for (uint i = 0; i < Height; ++i)
            {
                float StartY = i * HalfExt.Y * 2 + HalfExt.Y;
                Vector3f xVec = GetBasisX(Rotation);
                for (uint a = 0; a < BlocksPerLayer; ++a)
                {
                    Vector3f pos = xVec * HalfExt.X * 2 * ((float)a - (float)BlocksPerLayer / 2.0f) + new Vector3f(0, StartY, 0);

                    CreateCube(World, "Tower_" + TowerI + "_CylindrialLidSlab_" + a, Parent, pos + CenterPos, Rotation, HalfExt, Density, true);
                }

                Rotation = Quaternionf.Concatenate(rotationDelta, Rotation);
                Density *= 0.95f;
            }
        }

        private Quaternionf FromAxisAngle(float angleRadians, Vector3f unitAxis)
        {
            System.Diagnostics.Debug.Assert(Math.Abs(1.0f - unitAxis.Length()) < 1e-3f);
            float a = angleRadians * 0.5f;
            float s = (float)Math.Sin(a);
            return new Quaternionf(unitAxis.X * s, unitAxis.Y * s, unitAxis.Z * s, (float)Math.Cos(a));
        }

        private Vector3f GetBasisX(Quaternionf q)
        {
            float X2 = q.X * 2.0f;
            float w2 = q.W * 2.0f;
            return new Vector3f((q.W * w2) - 1.0f + q.X * X2, (q.Z * w2) + q.Y * X2, (-q.Y * w2) + q.Z * X2);
        }
    }
}
