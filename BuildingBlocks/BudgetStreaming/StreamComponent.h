#pragma once
#include "ECS/Develop/Framework/Types.h"
#include "RenderEngine/RenderCamera.h"
namespace cross
{

    enum class VisualzeMethod
    {
        Mesh,
        Texture0,
        Texture1,
        Texture2,
        Ideal,
        Count
    };

    struct StreamingAssetSetting
    {
        // use 1 by default to avoid crash temporally. 
        CEMeta(<PERSON><PERSON><PERSON>, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            int mLeastLOD = 1;

        CEMeta(Serial<PERSON>, Editor, EditorPropertyInfo(PropertyType = "List", ToolTips = "Type"))
            std::vector<float> LODDist =
            //{ 0.5, 0.25, 0.125, 0.0625, 0.03125, 0.015625 };
            //{ 0.25, 0.125, 0.0625, 0.03125, 0.015625, 0.0078125 };
        { 0.25f, 0.0625f, 0.015625f, 0.00390625f, 0.0009765625f, 0.000244140625f, 0.000061f, 0.00001525f };

        CEMeta(<PERSON><PERSON><PERSON>, Editor, EditorPropertyInfo(PropertyType = "List", ToolTips = "Type"))
            std::vector<std::string> LODTextureProperty = { "_BaseMap", "_NormalMap", "_ThicknessMap" };
        
        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "List", ToolTips = "Type"))
        std::vector<float> mTexturePriority = { 1.0f, 0.5f, 0.2f };

        CE_Serialize_Deserialize;
    };

    struct StreamingPolicyConfig
    {
        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            bool mEnableInEditor = false;

        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            bool mRequestTexture = true;
        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            bool mRequestMesh = true;
        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            bool mVisualize = true;

        VisualzeMethod  mVisualizeMethod = VisualzeMethod::Mesh;

        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            bool mAABBVis = false;
        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            bool mOtherDistVis = true;
        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            bool mVisualize_Ideal = false;
        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            bool IORequestLOG = false;
        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            bool mFreezeRequest = false;

        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            float  mLimitTextureGraphicsMemory = 0;

        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            CameraUtility::ProjectMethod mAreaProjection = CameraUtility::ProjectMethod::BBOX_Accurate;

        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            float PixelAreaDecay = 0.5f;

        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            bool mEnablePriority = true;
        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            bool mEnableVisiblePriority = true;
        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            float mInvisiblePenalty = 0.5f;

        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            bool nRejectIO = true;

        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            int   mStreamingThread = 2;

        // now we measure IO request budget by count
        // we may measure IO request budget by actual bytes;
        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
            int  mNumRequestPerframe = 20;

        CE_Serialize_Deserialize;
    };


    /**
     * ControllerComponent are shell component that can possess a Entity to control
     */
    struct StreamComponentG : ecs::IComponent
    {
        CEComponentInternal(SystemType = BudgetStreamingSystemG) 

        CEFunction(Reflect)
        static ecs::ComponentDesc* GetDesc();

        //CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
        //    ControllableUnitType Type;
        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
        StreamingPolicyConfig StreamingPolicy;
        

        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
        StreamingAssetSetting StreamingAssetSetting;

        CE_Serialize_Deserialize;
    };


    struct StreamingAssetComponentG : ecs::IComponent
    {
        CEComponentInternal(SystemType = BudgetStreamingSystemG) 
            
        CEFunction(Reflect)
        static ecs::ComponentDesc* GetDesc();

        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Type"))
        StreamingAssetSetting StreamingAssetSetting;

        CE_Serialize_Deserialize;
    };

   
}