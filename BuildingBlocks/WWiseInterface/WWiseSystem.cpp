#include "WWiseSystem.h"
#include <Runtime/Audio/AudioEngineManager.h>
#include "CECommon/Common/SettingsManager.h"
#include "CrossBase/Log.h"

#define DEFAULT_LANGUAGE "English(US)"
#define DEFAULT_ROOTPATH "/EngineResource/Audio/Windows"

namespace cross {
    const GlobalSystemDesc& WWiseSystem::GetDesc()
    {
        static const GlobalSystemDesc* sDesc{nullptr};
        if (!sDesc)
        {
            auto* descSystem = EngineGlobal::GetGlobalSystemDescSystem();
            sDesc = descSystem->CreateOrGetGlobalSystemDesc("WWise");
        }
        return *sDesc;
    }
        
    WWiseSystem* WWiseSystem::CreateInstance() 
    {
        return new WWiseSystem();
    }

    WWiseSystem::WWiseSystem()
    {
        // variables
        std::string rootPath = DEFAULT_ROOTPATH;
        std::string language = DEFAULT_LANGUAGE;

        // load config
        //std::string const currentPath = PathHelper::GetCurrentDirectoryPath();
        //std::string const configPath = currentPath + std::string("/EngineResource/Audio/WWiseInterface.json");
        //if (!PathHelper::IsFileExist(configPath))
        //    return;

        //std::optional<cross::SerializeNode> const configRoot = cross::EngineGlobal::GetSettingMgr()->LoadConfigFile(configPath);

        //if(configRoot.value().HasMember("BankPath"))
        //    rootPath = currentPath + configRoot.value()["BankPath"].AsString();

        //if(configRoot.value().HasMember("Language"))
        //    language = configRoot.value()["Language"].AsStringView();

        // create audio engine
        mAudioEngine = new WWiseAudioEngine();
        mAudioEngine->Init(rootPath.c_str(), language.c_str());
        //mAudioEngine->ObjectRegister(AudioEngine::DEFAULT_LISTENER);
        //mAudioEngine->ObjectSetDefaultListener(AudioEngine::DEFAULT_LISTENER);

        // setup
        mPreviousAudioEngine = EngineGlobal::Inst().GetAudioEngine();
        dynamic_cast<AudioEngineManager*>(EngineGlobal::Inst().GetAudioEngine())->SetAudioEngine(mAudioEngine);
    }

    void WWiseSystem::Release()
    {
        dynamic_cast<AudioEngineManager*>(EngineGlobal::Inst().GetAudioEngine())->SetAudioEngine(mPreviousAudioEngine);
        //mAudioEngine->ObjectUnregister(AudioEngine::DEFAULT_LISTENER);
        mAudioEngine->Deinit();
        delete this;
    }

    void WWiseSystem::NotifyAddRenderSystemToRenderEngine()
    {
    }

    void WWiseSystem::NotifyShutdownEngine()
    {
    }

    void WWiseSystem::OnFirstUpdate(FrameParam* frameParam)
    {
    }

    void WWiseSystem::OnBeginFrame(FrameParam* frameParam)
    {
    }

    void WWiseSystem::OnUpdate(FrameParam* frameParam)
    {
        mAudioEngine->Process();
    }

    GlobalRenderSystemBase* WWiseSystem::GetRenderSystem()
    {
        return nullptr;
    }
}   // namespace cross
