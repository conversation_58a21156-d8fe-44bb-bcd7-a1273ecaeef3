#include "WWiseModule.h"
#include "CrossBase/Log.h"
#include "memoryhooker/Module.h"
IMPORT_MODULE

extern "C"
{
    gbf::IModule* DynlibCreateModule(gbf::IInterfaceMgr* interfaceMgr)
    {
        auto* tmodule = new cross::WWiseModule();
        if (interfaceMgr)
        {
            interfaceMgr->RegisterModule(cross::kModuleWWiseModule, tmodule);
        }

        return tmodule;
    }

    void DynlibDestroyModule(gbf::IModule* tmodule) {}
}

void SystemRegisterR();
void SystemRegisterG();
void WWiseInterfaceRegister();

namespace cross
{
    WWiseModule::WWiseModule()
    {
        WWiseInterfaceRegister();
        SystemRegisterG();
        SystemRegisterR();
        LOG_DEBUG("[WWiseModule] WWiseModule");
    }

    gbf::ModuleCallReturnStatus WWiseModule::Init()
    {
        LOG_DEBUG("[WWiseModule] Init");
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    gbf::ModuleCallReturnStatus WWiseModule::Start()
    {
        LOG_DEBUG("[WWiseModule] Start");
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    gbf::ModuleCallReturnStatus WWiseModule::Update()
    {
        LOG_DEBUG("[WWiseModule] Update");
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    gbf::ModuleCallReturnStatus WWiseModule::Stop()
    {
        LOG_DEBUG("[WWiseModule] Stop");
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    gbf::ModuleCallReturnStatus WWiseModule::Release()
    {
        LOG_DEBUG("[WWiseModule] Release");
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    void WWiseModule::Free()
    {
        LOG_DEBUG("[WWiseModule] Free");
        delete this;
    }
}