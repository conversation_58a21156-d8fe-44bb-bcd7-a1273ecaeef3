#pragma once
#include "EditorRuntimeModule.h"
#include "Runtime/Input/Core/InputEvents.h"
#include "EditorUIRenderInterface.h"
#include "base/core/utils/byte_buffer.h"
#include "EditorKey.h"
#include "imgui_extra_math.h"
struct ImGuiContext;
namespace cross
{
    struct CEEditorRuntime_API CEMeta(Cli, IsOverride) EditorImGuiCallback
    {
        CEMeta(Cli, IsOverride)
        virtual void OnSetInputScreenPosition(int x, int y) {}
        CEMeta(Cli, IsOverride)
        virtual void OnSetCursor(ImGuiMouseCursor cursor) {}
    };

    class CEEditorRuntime_API CEMeta(Cli) EditorImGuiContext
    {
    protected:
        static inline VertexStreamLayout gLayout{
            VertexChannelLayout{VertexChannel::Position0, VertexFormat::Float2, 0},
            VertexChannelLayout{VertexChannel::TexCoord0, VertexFormat::Float2, sizeof(Float2)},
            VertexChannelLayout{VertexChannel::Color0, VertexFormat::UByte4_Norm, sizeof(Float2) + sizeof(Float2)},
        };

    public:
        EditorImGuiContext();
        EditorImGuiContext(EditorImGuiCallback* callback);

        virtual ~EditorImGuiContext();

        CEMeta(Cli)
        void OnMouseMoveEvent(int mouseX, int mouseY);

        CEMeta(Cli)
        void OnMouseWheelEvent(int mouseDeltaZ);

        CEMeta(Cli)
        void OnKeyEvent(EditorKey btn, bool isDown);

        CEMeta(Cli)
        void OnInputCharacter(ImWchar16 c);

        CEMeta(Cli)
        void OnActivate(bool active);

        void OnSetInputScreenPos(int x, int y);

        CEMeta(Cli)
        void Update(const Float2 & offset, const Float2 & size);

        CEMeta(Cli)
        void Paint(const Float2 & offset, const Float2 & size, EditorUIRenderInterface & renderInterface);

    protected:
        bool UpdateMouseCursor();
        void RecreateFontAtlas();

        virtual void OnUpdate(const Float2 & offset, const Float2 & size) {}

    protected:
        ImGuiContext* mGuiContext = nullptr;
        ImTextureID mFontImage = nullptr;
        ImGuiMouseCursor mLastMouseCursor = ImGuiMouseCursor_COUNT;

        std::vector<UInt8> mEnglishFontDate;
        std::vector<UInt8> mChineseFontDate;

        ImFont* m_DefaultFont = nullptr;
        ImFont* m_SubTitleFont = nullptr;

        EditorImGuiCallback* mCallback;
        bool m_ShowUI;
    };
} 