//#include "EnginePrefix.h"


#include <iterator>
#include <stdexcept>
#include <functional>

#include "CrossBase/Platform/PlatformTypes.h"
#include "CrossBase/Template/Functional.hpp"
#include "CrossBase/CrossBaseForward.h"
#include "CrossBase/FileSystem/PathHelper.h"
#include "CrossBase/Annotations.h"
#include "CrossBase/Log.h"

#include "packagefileloader.h"
#include "zippackage.h"


namespace cross
{
    namespace filesystem
    {
        IFilePtr ZipPackage::Load(const std::string& inVp, bool inTryStream)
        {
            Assert(0);            
            return nullptr;
        }

        bool ZipPackage::HaveFile(const std::string& inVp) const
        {
            Assert(0);
            return false;
        }

        bool ZipPackage::DoInit(const io::InputPtr& inRealFile)
        {
            Assert(0);
            return false;
        }        
        
        FileLoaderPtr ZipFileLoaderCreator::CreateNewFileLoader()
        {
            return FileLoaderPtr(new ZipFileLoader()) ;
            // return nullptr;
        }

        ZipFileLoaderCreator& ZipFileLoaderCreator::Instance()
        {
            static ZipFileLoaderCreator sInstance;
            return sInstance;
        }

        
    }
}
