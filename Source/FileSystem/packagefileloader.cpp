//#include "EnginePrefix.h"
#include "CrossBase/Platform/PlatformTypes.h"
#include <memory>
#include <string>
#include <map>
#include <unordered_map>

#include "CrossBase/Platform/PlatformTypes.h"
#include "CrossBase/CrossBaseForward.h"
#include "CrossBase/FileSystem/PathHelper.h"
#include "CrossBase/Annotations.h"
#include "CrossBase/Log.h"

#include "ifile.h"
#include "filesystem_io.h"
#include "fileloader.h"
#include "packagefileloader.h"

#include "filesystemutil.h"


namespace cross
{
    namespace filesystem
    {

        bool Package::Init(const io::InputPtr& inRealFile)
        {
            return false;
        }

        size_t Package::ReadRaw(char* inDest, size_t inOffset, size_t inLength)
        {
            return 0;
        }

        void Package::SetKey(const KeyType& inKey)
        {
            return;
        }


        IFilePtr PackageFileLoader::Open(const std::string& inVp, bool inTryStream)
        {
            return nullptr;
        }
        IFile* PackageFileLoader::MemoryMapping(const std::string& inVp, UInt64 mappedBytes, CacheHint hint) 
        {
            return nullptr;
        }
        void PackageFileLoader::Reload()
        {
        }
        
        bool PackageFileLoader::HaveFile(const std::string& inVp) const
        {
            return false;
        }
        
        bool PackageFileLoader::GetAbsolutePath(const std::string& inVp, std::string& outResult) const
        {
            return false;            
        }

        bool PackageFileLoader::GetRelativePath(const std::string& inRealPath, std::string& outResult) const
        {
            return false;            
        }        

        time_t PackageFileLoader::GetTimeStamp(const std::string& inVp) const
        {
            return static_cast<time_t>(-1);
        }
        
        void PackageFileLoader::SetKey(const KeyType& inKey)
        {
            //Assert(0);
        }

        bool PackageFileLoader::DoInit(io::Opener* inOpener, std::string& inRootPath, UInt32 inDepth)
        {
            return true;
        }
        
    }
}
