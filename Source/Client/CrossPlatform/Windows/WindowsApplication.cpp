#include "EnginePrefix.h"
#include "Client/CrossPlatform/Public/PublicClient.h"
#include "Client/CrossPlatform/Windows/WindowsApplication.h"
#include "Client/CrossPlatform/Windows/WindowsCursor.h"
#include "Runtime/Input/Core/Public/SlateApplication.h"
#include "Runtime/Input/Core/GameViewPortWidget.h"
#include "Runtime/Input/Core/InputKeys.h"
#include "CECommon/Common/SettingsManager.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/InGameTerminalManager.h"

#include <Windowsx.h>
#include <regex>
extern std::unique_ptr<cross::PublicClient> gClient;

namespace cross
{
    UniqueString WindowsApplication::windowClassStr = "CrossEngine Standalone";

    HINSTANCE WindowsApplication::InstanceHandle;

    HICON WindowsApplication::IconHandle;

    WindowsApplication* Application = nullptr;

    bool WindowsApplication::bConsumeAltSpace = false;

    WindowsApplication::WindowsApplication()
        : PlatformApplication()
    {
        mCursor = std::make_shared<WindowsCursor>();
        mInputMethodManager = std::make_shared<WindowsInputMethodManager>();
        QueryConnectedMouse();
    }

    WindowsApplication::~WindowsApplication()
    {
        mCursor.reset();
        mInputMethodManager.reset();
    }

    void WindowsApplication::ToggleMonitor(Rect monitor)
    {
        mToggleState = true;
        mClientLeft = monitor.Left;
        mClientTop = monitor.Top;
        ResizeClientWindowActive(monitor.Right - monitor.Left, monitor.Bottom - monitor.Top);
    }

    void WindowsApplication::ResizeClientWindowActive(SInt32 width, SInt32 height, FullScreenMode mode) 
    {

        gClient->mNewRECT.Left = mode == FullScreenMode::Windowed ? mClientLeft:0;
        gClient->mNewRECT.Top = mode == FullScreenMode::Windowed ? mClientTop:0;
        gClient->mNewRECT.Bottom = gClient->mNewRECT.Top + height;
        gClient->mNewRECT.Right = gClient->mNewRECT.Left + width;
        gClient->mNewRECT.Mode = mode;
        gClient->mWindowChanged = true;
        mWindowWidth = width;
        mWindowHeight = height;
        mGameViewportWidget.get()->SetWidgetSize(static_cast<SInt32>(width), static_cast<SInt32>(height));
    }

    void WindowsApplication::CreateViewPortWidget(PlatformUser* inUser) 
    {
        mGameViewportWidget.reset(new GameViewPortWidget(inUser, mClientLeft, mClientTop, mWindowWidth, mWindowHeight));

        // Apply the last known "all users" focus widget path to this new user if they do not have a specific one
        SlateApplication::Instance()->SetUserFocusToGameViewport(inUser->GetUserHandle());

        InGameTerminalManager::DeclareTerminalVar("Engine.monitor", mResSetString, [this](TerminalVar var) 
        {
            std::smatch varnamem;
            std::regex_match(mResSetString.cbegin(), mResSetString.cend(), varnamem, std::regex("(\\d+)x(\\d+)\\((\\d)\\)")); //1920x1080(0)
            std::string width = varnamem[1].str();
            std::string height = varnamem[2].str();
            std::string mode = varnamem[3].str();
            if (!width.empty() && !height.empty())
            {
                int widthf = std::atoi(width.c_str());
                int heightf = std::atoi(height.c_str());
                FullScreenMode modef = FullScreenMode(std::atoi(mode.c_str()));
                ResizeClientWindowActive(widthf, heightf, modef);
            }
         });
        InGameTerminalManager::DeclareTerminalVar("SwitchMonitor", mResSetString, [this](TerminalVar var)
            {
                std::smatch varnamem;
                std::regex_match(mResSetString.cbegin(), mResSetString.cend(), varnamem, std::regex("(.*)"));
                std::string monitorID = varnamem[1].str();
                if (!monitorID.empty())
                {
                    int index = std::atoi(monitorID.c_str());
                    if (gClient->mMonitorInfos.size() >= index + 1)
                    {
                        this->ToggleMonitor(gClient->mMonitorInfos[index]);
                    }
                }
            });
        gClient->OnTickEnd([this]() {
            if (mToggleState)
            {
                if (gClient->mWindowChanged)
                {
                    gClient->FlushRendering();
                    RECT rc = { gClient->mNewRECT.Left, gClient->mNewRECT.Top, gClient->mNewRECT.Right - gClient->mNewRECT.Left, gClient->mNewRECT.Bottom - gClient->mNewRECT.Top };
                    auto dwStyle = GetWindowLong(mHWnd, GWL_STYLE);
                    auto dwExStyle = GetWindowLong(mHWnd, GWL_EXSTYLE);
                    AdjustWindowRectEx(&rc, dwStyle, FALSE, dwExStyle);
                    LOG_INFO("WindowsChanged Pos {} {} Size {} {}", gClient->mNewRECT.Left, gClient->mNewRECT.Top, gClient->mNewRECT.Right - gClient->mNewRECT.Left, gClient->mNewRECT.Bottom - gClient->mNewRECT.Top);
                    SetWindowPos(mHWnd, HWND_TOP, gClient->mNewRECT.Left, gClient->mNewRECT.Top, gClient->mNewRECT.Right - gClient->mNewRECT.Left, gClient->mNewRECT.Bottom - gClient->mNewRECT.Top, SWP_FRAMECHANGED);
                    gClient->mWindowChanged = false;
                }
                mToggleState = false;
            }
            else
            {
                if (gClient->mWindowChanged)
                {
                    gClient->FlushRendering();
                    RECT rc = {gClient->mNewRECT.Left, gClient->mNewRECT.Top, gClient->mNewRECT.Right, gClient->mNewRECT.Bottom};
                    auto dwStyle = gClient->mNewRECT.Mode == FullScreenMode::Windowed ? ((WS_OVERLAPPEDWINDOW ^ (WS_MAXIMIZEBOX | WS_THICKFRAME)) | WS_CLIPSIBLINGS | WS_CLIPCHILDREN)
                                                                                      : (gClient->mNewRECT.Mode == FullScreenMode::Exclusive ? WS_POPUP : WS_OVERLAPPED | WS_CLIPSIBLINGS | WS_CLIPCHILDREN);
                    auto dwExStyle = WS_EX_APPWINDOW;
                    // this function will adjust rc to include window borders; so we need to set style first according to display mode;
                    if (gClient->mNewRECT.Mode == FullScreenMode::Windowed)
                        AdjustWindowRectEx(&rc, dwStyle, FALSE, dwExStyle);
                    gClient->mNewRECT.Left = rc.left < 0 ? 0 : rc.left;
                    gClient->mNewRECT.Top = rc.top < 0 ? 0 : rc.top;
                    gClient->mNewRECT.Bottom = rc.top < 0 ? rc.bottom - rc.top : rc.bottom;
                    gClient->mNewRECT.Right = rc.left < 0 ? rc.right - rc.left : rc.right;


                    // change monitor's resolution if necessary
                    DEVMODE devMode;
                    devMode.dmSize = sizeof(devMode);
                    EnumDisplaySettings(NULL, ENUM_CURRENT_SETTINGS, &devMode);
                   
                    switch (gClient->mNewRECT.Mode)
                    {
                    case FullScreenMode::Windowed:
                    {
                        if (devMode.dmPelsWidth != unsigned(gClient->mMonitorInfos[0].Right - gClient->mMonitorInfos[0].Left) || devMode.dmPelsHeight != unsigned(gClient->mMonitorInfos[0].Bottom - gClient->mMonitorInfos[0].Top))
                        {
                            devMode.dmPelsWidth = gClient->mMonitorInfos[0].Right - gClient->mMonitorInfos[0].Left;
                            devMode.dmPelsHeight = gClient->mMonitorInfos[0].Bottom - gClient->mMonitorInfos[0].Top;
                            devMode.dmBitsPerPel = 32;
                            devMode.dmFields = DM_BITSPERPEL | DM_PELSWIDTH | DM_PELSHEIGHT;
                            if (auto res = ChangeDisplaySettingsEx(NULL, &devMode, NULL, NULL, NULL); res != DISP_CHANGE_SUCCESSFUL)
                            {
                                LOG_WARN("Windowed mode changed failed {}", res);
                            }
                        }
                        break;
                    }
                        case FullScreenMode::Exclusive:
                        {
                        }
                        case FullScreenMode::BoardlessWindowed:
                        {
                            if (devMode.dmPelsWidth != (unsigned)gClient->mNewRECT.Right || devMode.dmPelsHeight != (unsigned)gClient->mNewRECT.Bottom)
                            {
                                devMode.dmPelsWidth = gClient->mNewRECT.Right;
                                devMode.dmPelsHeight = gClient->mNewRECT.Bottom;
                                devMode.dmBitsPerPel = 32;
                                devMode.dmFields = DM_BITSPERPEL | DM_PELSWIDTH | DM_PELSHEIGHT;

                                if (auto res = ChangeDisplaySettingsEx(NULL, &devMode, NULL, CDS_FULLSCREEN, NULL); res != DISP_CHANGE_SUCCESSFUL)
                                {
                                LOG_WARN("Windowed mode changed failed {}", res);
                                }
                            }
                            break;
                        }
                        default:
                        {
                            break;
                        }
                        }
                        // 

                   ::SetWindowPos(mHWnd,
                                  gClient->mNewRECT.Mode == FullScreenMode::Exclusive ? HWND_TOPMOST : HWND_NOTOPMOST,
                                  gClient->mNewRECT.Left,
                                  gClient->mNewRECT.Top,
                                  gClient->mNewRECT.Right - gClient->mNewRECT.Left,
                                  gClient->mNewRECT.Bottom - gClient->mNewRECT.Top,
                                  SWP_FRAMECHANGED | SWP_NOACTIVATE);

                   ::SetWindowLong(mHWnd, GWL_STYLE, dwStyle);

                    if (gClient->mNewRECT.Mode == FullScreenMode::Exclusive)
                        ShowWindow(mHWnd, SW_MAXIMIZE);
                    else
                        ShowWindow(mHWnd, SW_SHOW);
                    ::UpdateWindow(mHWnd);
                    gClient->mWindowChanged = false;
                }
            }
        });
    }

    bool WindowsApplication::IsViewPortWidgetActivated() const
    {
        return SlateApplication::Instance()->IsActive();
    }

    void WindowsApplication::OnViewPortWidgetActivatedChanged(bool inFocus)
    {
        if (InputSetting::GetInputSetting().ShowDebugInfo)
            LOG_DEBUG("OnViewPortWidgetActivatedChanged {}", inFocus);

        // Lock cursor into current widget or not
        if (inFocus)
        {
            WINDOWINFO windowInfo;
            ::GetWindowInfo((HWND)GetOSWindowHandle(), &windowInfo);
            HashString UnLockCursor("UnLockCursor");
            if (!(EngineGlobal::GetSettingMgr()->HasKey<bool>(UnLockCursor) && EngineGlobal::GetSettingMgr()->GetKeyValue<bool>(UnLockCursor)))
            {
                mCursor->Lock(Float2(static_cast<float>(windowInfo.rcClient.left), static_cast<float>(windowInfo.rcClient.top)),
                              Float2(static_cast<float>(windowInfo.rcClient.right - windowInfo.rcClient.left), static_cast<float>(windowInfo.rcClient.bottom - windowInfo.rcClient.top)));
            }
        }

        // System cursor is currently by Absolute or Relate coordinate
        {
            HWND hwnd = NULL;
            DWORD flags = RIDEV_REMOVE;

            if (inFocus && InstanceHandle)
            {
                flags = 0;

                if (mGameViewportWidget != nullptr)
                    hwnd = (HWND)GetOSWindowHandle();
            }

            // NOTE: Currently has to be created every time due to conflicts with Direct8 Input used by the wx unrealed
            RAWINPUTDEVICE RawInputDevice;

            // The HID standard for mouse
            const UInt16 StandardMouse = 0x02;

            RawInputDevice.usUsagePage = 0x01;
            RawInputDevice.usUsage = StandardMouse;
            RawInputDevice.dwFlags = flags;

            // Process input for just the window that requested it.  NOTE: If we pass NULL here events are routed to the window with keyboard focus
            // which is not always known at the HWND level with Slate
            RawInputDevice.hwndTarget = hwnd;

            // Register the raw input device
            ::RegisterRawInputDevices(&RawInputDevice, 1, sizeof(RAWINPUTDEVICE));
        }
    }

    input::CEModifierKeyStates WindowsApplication::GetModifierKeys() const
    { 
        return input::CEModifierKeyStates(
                                mModifierKeyState[input::CEModifierKey::LeftShift],
                                mModifierKeyState[input::CEModifierKey::RightShift],
                                mModifierKeyState[input::CEModifierKey::LeftControl],
                                mModifierKeyState[input::CEModifierKey::RightControl],
                                mModifierKeyState[input::CEModifierKey::LeftAlt],
                                mModifierKeyState[input::CEModifierKey::RightAlt],
                                false,
                                false,
                                mModifierKeyState[input::CEModifierKey::CapsLock]);   // Win key is ignored
    }

    void WindowsApplication::UpdateAllModifierKeyStates()
    {
        mModifierKeyState[input::CEModifierKey::LeftShift] = (::GetAsyncKeyState(VK_LSHIFT) & 0x8000) != 0;
        mModifierKeyState[input::CEModifierKey::RightShift] = (::GetAsyncKeyState(VK_RSHIFT) & 0x8000) != 0;
        mModifierKeyState[input::CEModifierKey::LeftControl] = (::GetAsyncKeyState(VK_LCONTROL) & 0x8000) != 0;
        mModifierKeyState[input::CEModifierKey::RightControl] = (::GetAsyncKeyState(VK_RCONTROL) & 0x8000) != 0;
        mModifierKeyState[input::CEModifierKey::LeftAlt] = (::GetAsyncKeyState(VK_LMENU) & 0x8000) != 0;
        mModifierKeyState[input::CEModifierKey::RightAlt] = (::GetAsyncKeyState(VK_RMENU) & 0x8000) != 0;
        mModifierKeyState[input::CEModifierKey::CapsLock] = (::GetKeyState(VK_CAPITAL) & 0x0001) != 0;
    }

    PlatformApplication* WindowsApplication::CreateApplication() 
    {
        if (Application != nullptr)
        {
            LOG_ERROR("application Repeate create, please check the initialization order!");
            return Application;
        }

        // generate platform reltved application
        Application = new WindowsApplication();

        // Disable the process from being showing "ghosted" not responding messages during slow tasks
        // This is a hack.  A more permanent solution is to make our slow tasks not block the editor for so long
        // that message pumping doesn't occur (which causes these messages).
        ::DisableProcessWindowsGhosting();

        // Register the Win32 class for Slate windows and assign the application instance and icon        
        RegisterClass(InstanceHandle, IconHandle);

        return Application;
    }

    void WindowsApplication::SetProcessDPIAware() 
    {
        SetProcessDpiAwarenessContext(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE);
    }

    WindowsApplication::TouchArrayH WindowsApplication::GetFirstFreeTouchIndex()
    {
        for (UInt32 i = 0; i < mTouchInfoArray.size(); i++)
        {
            if (!mTouchInfoArray[i].index.has_value())
                return static_cast<WindowsApplication::TouchArrayH>(i);
        }

        mTouchInfoArray.push_back(TouchInfo());
        return static_cast<WindowsApplication::TouchArrayH>(mTouchInfoArray.size() - 1);
    }

    WindowsApplication::TouchArrayH WindowsApplication::GetTouchIndexForID(UInt32 touchID)
    {
        for (UInt32 i = 0; i < mTouchInfoArray.size(); i++)
        {
            if (mTouchInfoArray[i].index.has_value() && static_cast<UInt32>(mTouchInfoArray[i].index.value()) == touchID)
                return static_cast<WindowsApplication::TouchArrayH>(i);
        }

        return TouchArrayInvalidH;
    }

    bool WindowsApplication::RegisterClass(const HINSTANCE HInstance, const HICON HIcon) 
    {
        WNDCLASS wc;
        memset(&wc, 0, sizeof(wc));

        wc.style = CS_DBLCLKS;   // We want to receive double clicks
        wc.lpfnWndProc = AppWndProc;
        wc.cbClsExtra = 0;
        wc.cbWndExtra = 0;
        wc.hInstance = HInstance;
        wc.hIcon = HIcon;
        wc.hCursor = LoadCursor(nullptr, IDC_ARROW); // We manage the cursor ourselves
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);   // Transparent
        wc.lpszMenuName = NULL;
        wc.lpszClassName = WindowsApplication::windowClassStr.GetCString();

        if (!::RegisterClass(&wc))
        {
            LOG_ERROR("Window Registration Failed!");
            return false;
        }

        return true;
    }

    LRESULT CALLBACK WindowsApplication::AppWndProc(HWND hwnd, UInt32 msg, WPARAM wParam, LPARAM lParam)
    {
        return Application->ProcessMessage(hwnd, msg, wParam, lParam);
    }

    SInt32 WindowsApplication::ProcessMessage(HWND hwnd, UInt32 msg, WPARAM wParam, LPARAM lParam) 
    {
        switch (msg)
        {
        case WM_INPUTLANGCHANGEREQUEST:
        case WM_INPUTLANGCHANGE:
        case WM_IME_SETCONTEXT:
        case WM_IME_STARTCOMPOSITION:
        case WM_IME_COMPOSITION:
        case WM_IME_ENDCOMPOSITION:
        case WM_IME_CHAR:                   // char convert by system input method editor
            ProcessMessageImpl(hwnd, msg, wParam, lParam);
            break;
        case WM_IME_NOTIFY:

            return 0;
        case WM_IME_REQUEST:

            return 0;
        case WM_CHAR:                       // char from default keyboard
            ProcessMessageImpl(hwnd, msg, wParam, lParam);
            return 0;
        case WM_SYSCHAR:                    // combine key happened while ALT is down
            {   
                // Do not handle Alt+Space so that it passes through and opens the window system menu
                if (!bConsumeAltSpace && (HIWORD(lParam) & 0x2000) != 0 && wParam == VK_SPACE)
                    break;
                else
                    return 0;
            }
            break;

        case WM_SYSKEYDOWN:                 // ALT is down
            {   // Alt-F4 or Alt+Space was pressed.
                if (wParam == VK_F4)
                    // Allow alt+f4 to close the window, but write a log warning
                    LOG_WARN("Alt-F4 pressed!");
                // If we're consuming alt+space, pass it along
                else if (!bConsumeAltSpace || wParam != VK_SPACE)
                    ProcessMessageImpl(hwnd, msg, wParam, lParam);
            }
            break;

        // Mouse & Keyboard input
        case WM_KEYDOWN:
        case WM_SYSKEYUP:
        case WM_KEYUP:
        case WM_LBUTTONDBLCLK:
        case WM_LBUTTONDOWN:
        case WM_MBUTTONDBLCLK:
        case WM_MBUTTONDOWN:
        case WM_RBUTTONDBLCLK:
        case WM_RBUTTONDOWN:
        case WM_XBUTTONDBLCLK:
        case WM_XBUTTONDOWN:
        case WM_XBUTTONUP:
        case WM_LBUTTONUP:
        case WM_MBUTTONUP:
        case WM_RBUTTONUP:
        case WM_NCMOUSEMOVE:
        case WM_MOUSEMOVE:
        case WM_MOUSEWHEEL:
#if WINVER >= 0x0601
        case WM_TOUCH:
#endif
            ProcessMessageImpl(hwnd, msg, wParam, lParam);
            return 0;

        // Mouse Movement handle with relative coordinate for game engine logic while current window is activated
        case WM_INPUT:
            {
                static std::vector<char> rawInputData;

                UInt32 size = 0;
                ::GetRawInputData((HRAWINPUT)lParam, RID_INPUT, NULL, &size, sizeof(RAWINPUTHEADER));
            
                if (rawInputData.size() < size)
                    rawInputData.resize(size + 1);

                //rawInputData.clear();
                if (::GetRawInputData((HRAWINPUT)lParam, RID_INPUT, rawInputData.data(), &size, sizeof(RAWINPUTHEADER)) == size)
                {
                    const RAWINPUT* const Raw = (const RAWINPUT* const)rawInputData.data();

                    if (Raw->header.dwType == RIM_TYPEMOUSE) 
                    {
                        const bool isAbsoluteInput = (Raw->data.mouse.usFlags & MOUSE_MOVE_ABSOLUTE) == MOUSE_MOVE_ABSOLUTE;
                        if (isAbsoluteInput) 
                        {
                            // Since the raw input is coming in as absolute it is likely the user is using a tablet
                            // or perhaps is interacting through a virtual desktop
                            return 1;
                        }

                        // Since raw input is coming in as relative it is likely a traditional mouse device
                        const int xPosRelative = Raw->data.mouse.lLastX;
                        const int yPosRelative = Raw->data.mouse.lLastY;

                        ProcessMessageImpl(hwnd, msg, wParam, lParam, xPosRelative, yPosRelative, MOUSE_MOVE_RELATIVE);
                        return 1;
                    }
                }
            }
            break;

        // Window focus and activation
        case WM_ACTIVATE:
        case WM_ACTIVATEAPP:
            ProcessMessageImpl(hwnd, msg, wParam, lParam);
            break;

        case WM_CLOSE:                  // OS close button is pressed, in the very beginning got WM_CLOSE 
            gClient->FlushRendering();
            break;

        case WM_DESTROY:                // OS close button is pressed, following msg got WM_DESTROY 
            ProcessMessageImpl(hwnd, msg, wParam, lParam);
            return 0;

        case WM_SIZING:
            {
                gClient->FlushRendering();
            }
            break;
        case WM_SYSCOMMAND:
            switch (wParam)
            {
            case SC_MINIMIZE:
            case SC_MAXIMIZE:
            case SC_RESTORE:
                gClient->FlushRendering();
                break;
            default:
                break;
            }
            break;
        case WM_WINDOWPOSCHANGING:
            {
                auto* pos = reinterpret_cast<WINDOWPOS*>(lParam);

                RECT rect{};
                GetWindowRect(pos->hwnd, &rect);
                auto width = rect.right - rect.left;
                auto height = rect.bottom - rect.top;
                if (mHWnd && (width != pos->cx || height != pos->cy))
                {
                    gClient->FlushRendering();

                    if (pos->x <= -32000 && pos->y <= -32000)
                    {
                        gClient->ResizeRenderWindow(0, 0);
                    }
                }
                break;
            }
        //case WM_STYLECHANGED:
        case WM_SIZE:
            {
                RECT rect;
                ::GetClientRect(hwnd, &rect);
                mWindowWidth = rect.right - rect.left;
                mWindowHeight = rect.bottom - rect.top;

                mClientLeft = rect.left;
                mClientTop = rect.top;

                LOG_INFO("WM_SIZEING Pos {} {} Size {} {}", rect.left, rect.top, rect.right - rect.left, rect.bottom - rect.top);

                gClient->ResizeRenderWindow(rect.right - rect.left, rect.bottom - rect.top);
                mGameViewportWidget.get()->SetWidgetSize(static_cast<SInt32>(rect.right - rect.left), static_cast<SInt32>(rect.bottom - rect.top));

                POINT screenPoint{ rect.left, rect.top };
                ::ClientToScreen(hwnd, &screenPoint);
                mGameViewportWidget.get()->SetOriginInScreenSpace(static_cast<SInt32>(screenPoint.x), static_cast<SInt32>(screenPoint.y));
            }
            break;

        case WM_MOVE: // app window got moved by DWM or Initialized
            {
                // client area position (left - top) with out title bar
                RECT clientRect{};
                GetClientRect(hwnd, &clientRect);
                mWindowWidth = clientRect.right - clientRect.left;
                mWindowHeight = clientRect.bottom - clientRect.top;

                mClientLeft = clientRect.left;
                mClientTop = clientRect.top;

                // Default init fire a wm_move message so that we prevents deactivate while window initialized
                if (mClientLeft >= 0 && mClientTop >= 0)
                    if (SlateApplication::Instance()->IsActive())
                        SlateApplication::Instance()->OnWindowActivationChanged(CEWindowActivation::Deactivate);

                POINT screenPoint{ clientRect.left, clientRect.top };
                ::ClientToScreen(hwnd, &screenPoint);
                mGameViewportWidget.get()->SetOriginInScreenSpace(static_cast<SInt32>(screenPoint.x), static_cast<SInt32>(screenPoint.y));
            }
            break;
        case WM_NCCALCSIZE:
        {
            if (EngineGlobal::GetSettingMgr()->GetFullScreen() == FullScreenMode::BoardlessWindowed) 
            {
                auto nonclient = *reinterpret_cast<RECT*>(lParam);
                DefWindowProcW(hwnd, WM_NCCALCSIZE, wParam, lParam);
                *reinterpret_cast<RECT*>(lParam) = nonclient;
                return 0;
            }
            else
            {
                break;
            }
        }
        case WM_NCHITTEST: // app return 
            {
                SInt32 zone = (SInt32) DefWindowProc(hwnd, msg, wParam, lParam);

                if (SlateApplication::Instance()->IsActive())
                    zone = HTCLIENT;

                return zone;
            }
            break;

        case WM_PAINT:
            ValidateRect(hwnd, 0);
            break;
        case WM_SHOWWINDOW:
            ProcessMessageImpl(hwnd, msg, wParam, lParam);
            break;
        default:
            {}
        }

        return (SInt32) DefWindowProc(hwnd, msg, wParam, lParam);
    }

    SInt32 WindowsApplication::ProcessMessageImpl(HWND hwnd, UInt32 msg, WPARAM wParam, LPARAM lParam, SInt32 mouseX, SInt32 mouseY, UInt32 rawInputFlags)
    {
        switch (msg)
        {
        // Char convert by system input method editor
        case WM_INPUTLANGCHANGEREQUEST:
        case WM_INPUTLANGCHANGE:
        case WM_IME_SETCONTEXT:
        case WM_IME_STARTCOMPOSITION:
        case WM_IME_COMPOSITION:
        case WM_IME_ENDCOMPOSITION:
        case WM_IME_CHAR:                   
            {
                if (msg == WM_INPUTLANGCHANGE)
                    SlateApplication::Instance()->OnInputLanguageChanged();
            }
            return 0;

        // Char down, make it response for client CONSOLE  
        case WM_CHAR:
            {
                // Character code is stored in WPARAM
                const char charCode = static_cast<char>(wParam);

                // LPARAM bit 30 will be ZERO for new presses, or ONE if this is a repeat
                const bool bIsRepeat = (lParam & 0x40000000) != 0;

                // LOG_INFO("code {} repeat {}", Character, bIsRepeat);
                SlateApplication::Instance()->OnKeyChar(charCode, bIsRepeat);
            }

            // Note: always return 0 to handle the message.  Win32 beeps if WM_CHAR is not handled...
            return 0;

        // Any keyboard input down, make it response for client game logic
        case WM_SYSKEYDOWN:
        case WM_KEYDOWN:
            {
                // Character code is stored in WPARAM
                const SInt32 Win32Key = static_cast<SInt32>(wParam);
                 
                // The actual key to use.  Some keys will be translated into other keys.
                // I.E VK_CONTROL will be translated to either VK_LCONTROL or VK_RCONTROL as these
                // keys are never sent on their own
                SInt32 actualKey = Win32Key;

                // LPARAM bit 30 will be ZERO for new presses, or ONE if this is a repeat
                bool bIsRepeat = (lParam & 0x40000000) != 0;

                switch (Win32Key)
                {
                case VK_MENU:
                    // Differentiate between left and right alt
                    if ((lParam & 0x1000000) == 0)
                    {
                        actualKey = VK_LMENU;
                        bIsRepeat = mModifierKeyState[input::CEModifierKey::LeftAlt];
                        mModifierKeyState[input::CEModifierKey::LeftAlt] = true;
                    }
                    else
                    {
                        actualKey = VK_RMENU;
                        bIsRepeat = mModifierKeyState[input::CEModifierKey::RightAlt];
                        mModifierKeyState[input::CEModifierKey::RightAlt] = true;
                    }
                    break;
                case VK_CONTROL:
                    // Differentiate between left and right control
                    if ((lParam & 0x1000000) == 0)
                    {
                        actualKey = VK_LCONTROL;
                        bIsRepeat = mModifierKeyState[input::CEModifierKey::LeftControl];
                        mModifierKeyState[input::CEModifierKey::LeftControl] = true;
                    }
                    else
                    {
                        actualKey = VK_RCONTROL;
                        bIsRepeat = mModifierKeyState[input::CEModifierKey::RightControl];
                        mModifierKeyState[input::CEModifierKey::RightControl] = true;
                    }
                    break;
                case VK_SHIFT:
                    // Differentiate between left and right shift
                    actualKey = MapVirtualKey((lParam & 0x00ff0000) >> 16, MAPVK_VSC_TO_VK_EX);
                    if (actualKey == VK_LSHIFT)
                    {
                        bIsRepeat = mModifierKeyState[input::CEModifierKey::LeftShift];
                        mModifierKeyState[input::CEModifierKey::LeftShift] = true;
                    }
                    else
                    {
                        bIsRepeat = mModifierKeyState[input::CEModifierKey::RightShift];
                        mModifierKeyState[input::CEModifierKey::RightShift] = true;
                    }
                    break;
                case VK_CAPITAL:
                    mModifierKeyState[input::CEModifierKey::CapsLock] = (::GetKeyState(VK_CAPITAL) & 0x0001) != 0;
                    break;
                default:
                    // No translation needed
                    break;
                }

                // Get the character code from the virtual key pressed.  If 0, no translation from virtual key to character exists
                UInt32 charCode = ::MapVirtualKey(Win32Key, MAPVK_VK_TO_CHAR);
                SlateApplication::Instance()->OnKeyDown(actualKey, charCode, bIsRepeat);
            }

            return 0;
        
         // Any keyboard input up, make it response for client game logic
        case WM_SYSKEYUP:
        case WM_KEYUP:
        {
            // Character code is stored in WPARAM
            const SInt32 Win32Key = static_cast<SInt32>(wParam);

            // The actual key to use.  Some keys will be translated into other keys.
            // I.E VK_CONTROL will be translated to either VK_LCONTROL or VK_RCONTROL as these
            // keys are never sent on their own
            SInt32 actualKey = Win32Key;

            switch (Win32Key)
            {
            case VK_MENU:
                // Differentiate between left and right alt
                if ((lParam & 0x1000000) == 0)
                {
                    actualKey = VK_LMENU;
                    mModifierKeyState[input::CEModifierKey::LeftAlt] = false;
                }
                else
                {
                    actualKey = VK_RMENU;
                    mModifierKeyState[input::CEModifierKey::RightAlt] = false;
                }
                break;
            case VK_CONTROL:
                // Differentiate between left and right control
                if ((lParam & 0x1000000) == 0)
                {
                    actualKey = VK_LCONTROL;
                    mModifierKeyState[input::CEModifierKey::LeftControl] = false;
                }
                else
                {
                    actualKey = VK_RCONTROL;
                    mModifierKeyState[input::CEModifierKey::RightControl] = false;
                }
                break;
            case VK_SHIFT:
                // Differentiate between left and right shift
                actualKey = MapVirtualKey((lParam & 0x00ff0000) >> 16, MAPVK_VSC_TO_VK_EX);
                if (actualKey == VK_LSHIFT)
                {
                    mModifierKeyState[input::CEModifierKey::LeftShift] = false;
                }
                else
                {
                    mModifierKeyState[input::CEModifierKey::RightShift] = false;
                }
                break;
            case VK_CAPITAL:
                mModifierKeyState[input::CEModifierKey::CapsLock] = (::GetKeyState(VK_CAPITAL) & 0x0001) != 0;
                break;
            default:
                // No translation needed
                break;
            }

            // Get the character code from the virtual key pressed.  If 0, no translation from virtual key to character exists
            UInt32 charCode = ::MapVirtualKey(Win32Key, MAPVK_VK_TO_CHAR);

            // Key up events are never repeats
            const bool bIsRepeat = false;

            //LOG_INFO("WindowsApplication::ProcessMessageImpl Down {} {}", actualKey, charCode);
            SlateApplication::Instance()->OnKeyUp(actualKey, charCode, bIsRepeat);
        }
            return 0;

        // Any Mouse Button click concerns, make it response for client game logic
        case WM_LBUTTONDBLCLK:
        case WM_LBUTTONDOWN:
        case WM_MBUTTONDBLCLK:
        case WM_MBUTTONDOWN:
        case WM_RBUTTONDBLCLK:
        case WM_RBUTTONDOWN:
        case WM_XBUTTONDBLCLK:
        case WM_XBUTTONDOWN:
        case WM_LBUTTONUP:
        case WM_MBUTTONUP:
        case WM_RBUTTONUP:
        case WM_XBUTTONUP:
        {
            POINT cursorPoint;
            cursorPoint.x = GET_X_LPARAM(lParam);
            cursorPoint.y = GET_Y_LPARAM(lParam);

            ClientToScreen(hwnd, &cursorPoint);
            const Float2 cursorPos(static_cast<float>(cursorPoint.x), static_cast<float>(cursorPoint.y));

            input::CEMouseButton::Type mouseButton = input::CEMouseButton::Invalid;
            bool bDoubleClick = false;
            bool bMouseUp = false;

            switch (msg)
            {
            case WM_LBUTTONDBLCLK:
                bDoubleClick = true;
                mouseButton = input::CEMouseButton::Left;
                break;
            case WM_LBUTTONUP:
                bMouseUp = true;
                mouseButton = input::CEMouseButton::Left;
                break;
            case WM_LBUTTONDOWN:
                mouseButton = input::CEMouseButton::Left;
                break;
            case WM_MBUTTONDBLCLK:
                bDoubleClick = true;
                mouseButton = input::CEMouseButton::Middle;
                break;
            case WM_MBUTTONUP:
                bMouseUp = true;
                mouseButton = input::CEMouseButton::Middle;
                break;
            case WM_MBUTTONDOWN:
                mouseButton = input::CEMouseButton::Middle;
                break;
            case WM_RBUTTONDBLCLK:
                bDoubleClick = true;
                mouseButton = input::CEMouseButton::Right;
                break;
            case WM_RBUTTONUP:
                bMouseUp = true;
                mouseButton = input::CEMouseButton::Right;
                break;
            case WM_RBUTTONDOWN:
                mouseButton = input::CEMouseButton::Right;
                break;
            case WM_XBUTTONDBLCLK:
                bDoubleClick = true;
                mouseButton = (HIWORD(wParam) & XBUTTON1) ? input::CEMouseButton::Thumb01 : input::CEMouseButton::Thumb02;
                break;
            case WM_XBUTTONUP:
                bMouseUp = true;
                mouseButton = (HIWORD(wParam) & XBUTTON1) ? input::CEMouseButton::Thumb01 : input::CEMouseButton::Thumb02;
                break;
            case WM_XBUTTONDOWN:
                mouseButton = (HIWORD(wParam) & XBUTTON1) ? input::CEMouseButton::Thumb01 : input::CEMouseButton::Thumb02;
                break;
            default:
                {}
            }

            if (bMouseUp)
            {
                SlateApplication::Instance()->OnMouseUp(mouseButton, cursorPos);
            }
            else if (bDoubleClick)
            {
                SlateApplication::Instance()->OnMouseDoubleClick(mouseButton, cursorPos);
            }
            else
            {
                if (SlateApplication::Instance()->OnMouseDown(mouseButton, cursorPos))
                {
                    // While drag title bar, DWM make current window keep activated in os but clear focus & cursor locking state immediately
                    // A click inside window area should revoke widget & lock cursor again
                    if (!SlateApplication::Instance()->IsActive())
                        SlateApplication::Instance()->OnWindowActivationChanged(CEWindowActivation::ActivateByMouse);
                }
            }
        }
            return 0;

        // Mouse Movement with reltv movement, make it response for client game logic
        case WM_INPUT:
        {
            if (rawInputFlags == MOUSE_MOVE_RELATIVE)
            {
                SlateApplication::Instance()->OnRawMouseMove(mouseX, mouseY);
            }
            else
            {
                // Absolute coordinates given through raw input are simulated using MouseMove to get relative coordinates
                SlateApplication::Instance()->OnMouseMove();
            }
        }
            return 0;

        // Mouse Movement while window not captured
        case WM_NCMOUSEMOVE:
        case WM_MOUSEMOVE:
        {
            SlateApplication::Instance()->OnMouseMove();
        }
            return 0;
        
        // Mouse Movement 
        case WM_MOUSEWHEEL:
        {
            const SHORT wheelDelta = GET_WHEEL_DELTA_WPARAM(wParam);
            const SHORT scaledWheelDelta = wheelDelta / WHEEL_DELTA;

            POINT cursorPoint;
            cursorPoint.x = GET_X_LPARAM(lParam);
            cursorPoint.y = GET_Y_LPARAM(lParam); 

            const Float2 cursorPos(static_cast<float>(cursorPoint.x), static_cast<float>(cursorPoint.y));
            SlateApplication::Instance()->OnMouseWheel(static_cast<float>(scaledWheelDelta), cursorPos);
        }
            return 0;

        // Touch Start & Move & End
        case WM_TOUCH:
        {
            UInt32 inputCount = LOWORD(wParam);
            if (inputCount > 0)
            {
                std::unique_ptr<TOUCHINPUT[]> inputs = std::make_unique<TOUCHINPUT[]>(inputCount);
                if (GetTouchInputInfo((HTOUCHINPUT)lParam, inputCount, inputs.get(), sizeof(TOUCHINPUT)))
                {
                    for (UInt32 i = 0; i < inputCount; i++)
                    {
                        TOUCHINPUT const& input = inputs[i];
                        Float2 location(static_cast<float>(input.x / 100.0f), static_cast<float>(input.y / 100.0f));
                        if (input.dwFlags & TOUCHEVENTF_DOWN)
                        {
                            TouchArrayH touchIndex = GetTouchIndexForID(input.dwID);
                            if (touchIndex == INDEX_NONE)
                            {
                                touchIndex = GetFirstFreeTouchIndex();
                                Assert(touchIndex >= 0);

                                mTouchInfoArray[touchIndex].index = input.dwID;
                                mTouchInfoArray[touchIndex].hasMoved = false;
                                mTouchInfoArray[touchIndex].previousLocation = location;

                                if (InputSetting::GetInputSetting().ShowDebugInfo)
                                    LOG_INFO("OnTouchStarted at ({}, {}), finger {} (system touch id {})", location.x, location.y, touchIndex, input.dwID);
                                SlateApplication::Instance()->OnTouchStarted(location, 1.0f, touchIndex, {0});
                            }
                            else
                            {
                                // Error handling.
                            }
                        }
                        else if (input.dwFlags & TOUCHEVENTF_MOVE)
                        {
                            TouchArrayH touchIndex = GetTouchIndexForID(input.dwID);
                            if (touchIndex >= 0)
                            {
                                // LOG_INFO("OnTouchMoved at ({}, {}), finger {} (system touch id {})", location.x, location.y, touchIndex, input.dwID);

                                if (bEnableFirstTouchEvent)
                                {
                                    // track first move event, for helping with "pop" on the filtered small movements
                                    if (!mTouchInfoArray[touchIndex].hasMoved)
                                    {
                                        if (mTouchInfoArray[touchIndex].previousLocation != location)
                                        {
                                            mTouchInfoArray[touchIndex].hasMoved = true;
                                            SlateApplication::Instance()->OnTouchFirstMove(location, 1.0f, touchIndex, {0});
                                        }
                                    }
                                }

                                mTouchInfoArray[touchIndex].previousLocation = location;
                                SlateApplication::Instance()->OnTouchMoved(location, 1.0f, touchIndex, {0});
                            }
                        }
                        else if (input.dwFlags & TOUCHEVENTF_UP)
                        {
                            TouchArrayH touchIndex = GetTouchIndexForID(input.dwID);
                            if (touchIndex >= 0)
                            {
                                mTouchInfoArray[touchIndex].index = std::optional<UInt32>();

                                if (InputSetting::GetInputSetting().ShowDebugInfo)
                                    LOG_INFO("OnTouchEnded at ({}, {}), finger {} (system touch id {})", location.x, location.y, touchIndex, input.dwID);
                                SlateApplication::Instance()->OnTouchEnded(location, touchIndex, {0});
                            }
                            else
                            {
                                // Error handling.
                            }
                        }
                    }
                    ::CloseTouchInputHandle((HTOUCHINPUT)lParam);
                    return 0;
                }
            }
        }
        return 0;

        // Window focus and activation
        // This message will send to the current window of the application
        case WM_ACTIVATE:
        {
            CEWindowActivation activation;
            
            if (LOWORD(wParam) & WA_ACTIVE)
                activation = CEWindowActivation::Activate;
            else if (LOWORD(wParam) & WA_CLICKACTIVE)
                activation = CEWindowActivation::ActivateByMouse;
            else
                activation = CEWindowActivation::Deactivate;

            UpdateAllModifierKeyStates();

            SlateApplication::Instance()->OnWindowActivationChanged(activation);
        }
            return 0;
        // This message will send to all windows of an application
        case WM_ACTIVATEAPP:
        {
            UpdateAllModifierKeyStates();

            SlateApplication::Instance()->OnApplicationActivationChanged( !!wParam );
        }
            return 0;

        case WM_DESTROY:
            {
                PostQuitMessage(0);
            }
            return 0;
        default:
            return 0;
        }
    }

    bool WindowsApplication::IsKeyboardInputMessage(UInt32 msg)
    {
        switch (msg)
        {
        // Keyboard input notification messages...
        case WM_CHAR:
        case WM_SYSCHAR:
        case WM_SYSKEYDOWN:
        case WM_KEYDOWN:
        case WM_SYSKEYUP:
        case WM_KEYUP:
        case WM_SYSCOMMAND:
            return true;
        }
        return false;
    }

    bool WindowsApplication::IsMouseInputMessage(UInt32 msg)
    {
        switch (msg)
        {
        // Mouse input notification messages...
        case WM_MOUSEHWHEEL:
        case WM_MOUSEWHEEL:
        case WM_MOUSEHOVER:
        case WM_MOUSELEAVE:
        case WM_MOUSEMOVE:
        case WM_NCMOUSEHOVER:
        case WM_NCMOUSELEAVE:
        case WM_NCMOUSEMOVE:
        case WM_NCMBUTTONDBLCLK:
        case WM_NCMBUTTONDOWN:
        case WM_NCMBUTTONUP:
        case WM_NCRBUTTONDBLCLK:
        case WM_NCRBUTTONDOWN:
        case WM_NCRBUTTONUP:
        case WM_NCXBUTTONDBLCLK:
        case WM_NCXBUTTONDOWN:
        case WM_NCXBUTTONUP:
        case WM_LBUTTONDBLCLK:
        case WM_LBUTTONDOWN:
        case WM_LBUTTONUP:
        case WM_MBUTTONDBLCLK:
        case WM_MBUTTONDOWN:
        case WM_MBUTTONUP:
        case WM_RBUTTONDBLCLK:
        case WM_RBUTTONDOWN:
        case WM_RBUTTONUP:
        case WM_XBUTTONDBLCLK:
        case WM_XBUTTONDOWN:
        case WM_XBUTTONUP:
            return true;
        }
        return false;
    }

    #define MOUSEEVENTF_FROMTOUCH 0xFF515700

    bool WindowsApplication::IsFakeMouseInputMessage(UInt32 msg)
    {
        // TODO: Need further implementation
        bool preventDuplicateMouseEventsForTouch = false;

        if (preventDuplicateMouseEventsForTouch && IsMouseInputMessage(msg))
        {
            // This is only legal to call when handling messages in the pump, and is not valid
            // to call in a deferred fashion.
            // Click was generated by wisptis / Windows Touch
            return (GetMessageExtraInfo() & MOUSEEVENTF_FROMTOUCH) == MOUSEEVENTF_FROMTOUCH;
        }

        // Click was generated by the mouse.
        return false;
    }

    bool WindowsApplication::IsInputMessage(UInt32 msg)
    {
        	if (IsKeyboardInputMessage(msg) || IsMouseInputMessage(msg))
	{
		return true;
	}

	switch(msg)
	{
	// Raw input notification messages...
	case WM_INPUT:
	case WM_INPUT_DEVICE_CHANGE:
		return true;
	}
	return false;
    }

    bool WindowsApplication::IsMouseAttached() const
    {
        return mMouseCount > 0;
    }

    bool WindowsApplication::IsGamepadAttached() const
    {
        for (const auto& device : mExternalDevices)
        {
            if (device->IsGamepadAttached())
            {
                return true;
            }
        }
        return false;
    }

    void WindowsApplication::QueryConnectedMouse()
    {
        std::vector<RAWINPUTDEVICELIST> deviceList;
        UInt32 deviceCount = 0;

        mMouseCount = 0;
        GetRawInputDeviceList(nullptr, &deviceCount, sizeof(RAWINPUTDEVICELIST));
        if (deviceCount != 0)
        {
            deviceList.resize(deviceCount);
            GetRawInputDeviceList(deviceList.data(), &deviceCount, sizeof(RAWINPUTDEVICELIST));

            for (const auto& device : deviceList)
            {
                UInt32 length = 0;
                std::string name;
                if (device.dwType != RIM_TYPEMOUSE)
                    continue;

                // Force the use of ANSI versions of these calls
                if (GetRawInputDeviceInfoA(device.hDevice, RIDI_DEVICENAME, nullptr, &length) == static_cast<UInt32>(-1))
                    continue;

                name.resize(length + 1);
                if (GetRawInputDeviceInfoA(device.hDevice, RIDI_DEVICENAME, name.data(), &length) == static_cast<UInt32>(-1))
                    continue;

                name[length] = 0;
                name = StringHelper::Replace(name, "#", "\\");

                // Name XP starts with \??\, vista+ starts \\?\
                // In the device list exists a fake Mouse device with the device name of RDP_MOU
                // This is used for Remote Desktop so ignore it.
                if (StringHelper::StartsWith(name, "\\??\\ROOT\\RDP_MOU\\") || StringHelper::StartsWith(name, "\\\\??\\ROOT\\RDP_MOU\\"))
                {
                    continue;
                }

                mMouseCount++;
            }

            // If the session is a remote desktop session - assume that a mouse is present, it seems that you can end up
            // in a situation where RDP mice don't have a valid name, so the code above fails to locate a valid mouse,
            // even though one is present.
            if (mMouseCount == 0)
            {
                if (GetSystemMetrics(SM_REMOTESESSION))
                {
                    mMouseCount++;
                }
            }
        }
    }

} // namespace cross
