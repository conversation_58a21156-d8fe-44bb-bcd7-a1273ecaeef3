#include "EnginePrefix.h"

#include "PxWindowEventDelegate.h"

namespace cross::px {

void PxWindowEventDelegate::onActivateInput(EKeyboardType emKeyboardType, const char* pszDefaultText, const char* pszHintText, ActiveInputCallBack callback, void* ud)
{
    if (callback)
        callback(this, true, ud);
}

void PxWindowEventDelegate::onMessage(const char* pszMessage)
{
    // LOG_INFO("PxWindow::onMessage {}", pszMessage);

    auto entityID = mContext->GetEntityID();
    mContext->GetCanvas()->mMessageCallback(entityID, pszMessage);
}
}   // namespace cross::px