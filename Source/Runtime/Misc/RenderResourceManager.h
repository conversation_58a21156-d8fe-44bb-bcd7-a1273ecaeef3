#pragma once
#include "RenderEngine/RenderEngineForward.h"
#include "Resource/IResourceInterface.h"
namespace cross {
class CreateRenderObjectMgr : public ICreateRenderObjectMgr
{
public:
    static CreateRenderObjectMgr* Inst();

    CreateRenderObjectMgr() = default;

    virtual std::unique_ptr<cross::IGPUTexture> GetGpuTex(const TextureInfo& textureInfo) override;
    virtual std::shared_ptr<IMaterialR> GetMaterialR(resource::MaterialInterface* material) override;
    virtual std::unique_ptr<IMeshR> GetMeshR(MeshAssetData* meshAssetData) override;
    virtual std::shared_ptr<IStreamableResource> GetStreamable(const ResourcePtr& resource) override;
    virtual NGIResourceGroupLayout* AllocateResourceGroupLayout(const NGIResourceGroupLayoutDesc& desc) override;
    virtual NGIPipelineLayout* AllocatePipelineLayout(const NGIPipelineLayoutDesc& desc) override;
    virtual IComputeShaderR* GetComputeShaderR(resource::ComputeShader* inComputeShader) override;
    virtual IRayTracingShaderR* GetRayTracingShaderR(resource::RayTracingShader* inRayTracingShader) override;
    // TODO(scolu): Need to add render texture to bindless resources?
    virtual std::unique_ptr<cross::IGPUTexture> GetGpuRenderTex(const resource::RenderTextureInfo& info) override;

    virtual void NotifyInstanceDataResourceChange(const resource::InstanceDataResource* resource) override;
};
}   // namespace cross