#include "RenderResourceManager.h"

#include "CECommon/Common/EngineGlobal.h"

#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/Texture/GPUTexture.h"
#include "RenderEngine/ComputeShaderR.h"
#include "RenderEngine/RayTracingShaderR.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/WorldSystemR.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDResourceManager.h"
#include "Runtime/GameWorld/WorldSystemG.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/InstancedStaticModelSystemG.h"
#include "Runtime/Streaming/StreamableResource.h"
#include "RayTracingScene/BindlessResourceManager.h"
#include "RayTracingScene/RayTracingScene.h"


namespace cross {
CreateRenderObjectMgr* CreateRenderObjectMgr::Inst()
{
    static CreateRenderObjectMgr sCreateRenderObjectMgr;
    return &sCreateRenderObjectMgr;
}

std::unique_ptr<cross::IGPUTexture> CreateRenderObjectMgr::GetGpuTex(const TextureInfo& textureInfo)
{
    std::unique_ptr<cross::IGPUTexture> ret;
    if (textureInfo.EnableVirtualTextureStreaming)
    {
        if (textureInfo.UDIM)
        {
            ret = std::make_unique<VirtualUDIMGPUTexture>();
        }
        else
        {
            ret = std::make_unique<VirtualGPUTexture>();
        }
    }
    else
    {
        ret = std::make_unique<GPUTexture>();
    }

    return ret;
}

std::shared_ptr<IMaterialR> CreateRenderObjectMgr::GetMaterialR(resource::MaterialInterface* material)
{
    return std::make_shared<MaterialR>(material);
}

std::unique_ptr<IMeshR> CreateRenderObjectMgr::GetMeshR(MeshAssetData* meshAssetData)
{
    return std::make_unique<MeshR>(meshAssetData);
}

std::shared_ptr<IStreamableResource> CreateRenderObjectMgr::GetStreamable(const ResourcePtr& resource)
{
    return std::make_shared<StreamableResource>(resource);
}

NGIResourceGroupLayout* CreateRenderObjectMgr::AllocateResourceGroupLayout(const NGIResourceGroupLayoutDesc& desc)
{
    RendererSystemR* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    Assert(rendererSystem);
    auto* ngiLayout = rendererSystem->GetPersistentResourceManager()->AllocateResourceGroupLayout(desc);

    return ngiLayout;
}

cross::NGIPipelineLayout* CreateRenderObjectMgr::AllocatePipelineLayout(const NGIPipelineLayoutDesc& desc)
{
    RendererSystemR* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    Assert(rendererSystem);
    cross::NGIPipelineLayout* ngiLayout = rendererSystem->GetPersistentResourceManager()->AllocatePipelineLayout(desc);

    return ngiLayout;
}

cross::IComputeShaderR* CreateRenderObjectMgr::GetComputeShaderR(resource::ComputeShader* inComputeShader)
{
    return new cross::ComputeShaderR(inComputeShader);
}

IRayTracingShaderR* CreateRenderObjectMgr::GetRayTracingShaderR(resource::RayTracingShader* inRayTracingShader)
{
    return new cross::RayTracingShaderR(inRayTracingShader);
}

std::unique_ptr<cross::IGPUTexture> CreateRenderObjectMgr::GetGpuRenderTex(const resource::RenderTextureInfo& info)
{
    return std::make_unique<RenderTextureR>(info);
}

void CreateRenderObjectMgr::NotifyInstanceDataResourceChange(const resource::InstanceDataResource* resource)
{
    // Update AABB
    auto* worldSystem = EngineGlobal::GetEngine()->GetGlobalSystem<WorldSystemG>();
    for (auto beg = worldSystem->BeginWorld(); beg != worldSystem->EndWorld(); beg++)
    {
        if ((*beg)->IsPendingToDestroy())
            continue;

        auto* instancedStaticModelSys = (*beg)->GetGameSystem<InstancedStaticModelSystemG>();
        instancedStaticModelSys->NotifyInstanceDataResourceChange(resource);
    }
}
}   // namespace cross
