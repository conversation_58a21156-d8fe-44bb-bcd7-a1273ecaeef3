#include "Runtime/GameWorld/InstancedStaticModelSystemG.h"

#include "EnginePrefix.h"
#include "CrossBase/Template/TypeCast.hpp"
#include "CrossBase/Math/CrossMath.h"
#include "Threading/RenderingThread.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Common/SettingsManager.h"

#include "Resource/MeshAssetDataResource.h"
#include "Resource/Resource.h"
#include "Resource/ResourceManager.h"
#include "Resource/AssetStreaming.h"
#include "Resource/Fx.h"

#include "RenderEngine/RenderMaterial.h"

#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/TransformSystemG.h"
#include "Runtime/GameWorld/AABBSystemG.h"

namespace
{
    template<typename T>
    cross::CE_REAL_PTR_IMPLMENT<T> LoadAsset(std::string const& path)
    {
        return TypeCast<T>(gAssetStreamingManager->LoadSynchronously(path));
    }
}

namespace cross {

ecs::ComponentDesc* cross::InstancedStaticModelComponentG::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::InstancedStaticModelComponentG>({false, true, true, false},
                                                                                                               &InstancedStaticModelSystemG::SerializeInstancedStaticModelComponent,
                                                                                                               &InstancedStaticModelSystemG::DeserializeInstancedStaticModelComponent,
                                                                                                               &InstancedStaticModelSystemG::PostDeserializeInstancedStaticModelComponent,
                                                                                                               &InstancedStaticModelSystemG::UpdateDeserializeInstancedStaticModelComponent,
                                                                                                               &InstancedStaticModelSystemG::GetResourceInstancedStaticModelComponent);
}

void InstancedStaticModelComponentG::AdditionalDeserialize(const DeserializeNode& inNode, SerializeContext& context)
{
    if (!inNode.HasMember("mSubModelProperties"))
    {
        mAsset = LoadAsset<resource::MeshAssetDataResource>(mAssetPath);

        for (auto& singleLODModelProperty : mLODModelProperties)
        {
            for (auto& subModelProperty : singleLODModelProperty.mSubModelProperties)
            {
                subModelProperty.mMaterial = LoadAsset<resource::MaterialInterface>(subModelProperty.mMaterialPath);
            }
        }

        mInstanceDataResource = LoadAsset<resource::InstanceDataResource>(mInstanceDataResourcePath);
    }
    // To be compatible with the old model
    else
    {
        this->mVisible = inNode.Value("mVisible", true);
        this->mReceiveDecals = inNode.Value("mReceiveDecals", true);

        auto assetPath = inNode["mAsset"].AsString();
        this->mAsset = LoadAsset<resource::MeshAssetDataResource>(assetPath);

        auto assetData = this->mAsset->GetAssetData();

        auto lodCount = assetData->GetLodCount();
        auto subModelCount = inNode["mSubModelProperties"].Size();
        this->mLODModelProperties.resize(lodCount);

        for (size_t lodIndex = 0ull; lodIndex < lodCount; lodIndex++)
        {
            auto& singleLODModelProperty = this->mLODModelProperties[lodIndex];

            singleLODModelProperty.mSubModelProperties.resize(subModelCount);
            // Convert old model property to current model property.
            for (size_t subModelIndex = 0; subModelIndex < subModelCount; ++subModelIndex)
            {
                auto oldSubModelPropertyNode = inNode["mSubModelProperties"][subModelIndex];
                auto& subModelProperty = singleLODModelProperty.mSubModelProperties[subModelIndex];
                if (auto materialsNode = oldSubModelPropertyNode.HasMember("mMaterials"); materialsNode)
                {
                    auto lodIdxStr = std::to_string(lodIndex);
                    if (materialsNode->HasMember(lodIdxStr))
                    {
                        subModelProperty.mMaterialPath = materialsNode.value()[lodIdxStr].AsString();
                        subModelProperty.mMaterial = LoadAsset<resource::MaterialInterface>(subModelProperty.mMaterialPath);
                    }
                }

                subModelProperty.mVisible = oldSubModelPropertyNode["mVisible"].AsBoolean();
            }
        }

        // DistanceCulling
        if (inNode.HasMember("mDistanceCulling"))
        {
            this->mDistanceCulling.Deserialize(inNode["mDistanceCulling"], context);
        }

        // InstanceDatas
        if (auto path = inNode.HasMember("InstanceDataResource"); path)
        {
            this->mInstanceDataResource = LoadAsset<resource::InstanceDataResource>(path->AsString());
        }
    }
}

SerializeNode InstancedStaticModelSystemG::SerializeInstancedStaticModelComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    SCOPED_CPU_TIMING(GroupSerialize, "Serialize InstancedStaticModelComponent");

    auto comp = static_cast<InstancedStaticModelComponentG*>(componentPtr);

    SerializeContext context;
    SerializeNode rootNode = comp->Serialize(context);

    if (comp->mInstanceDataResource)
    {
        comp->mInstanceDataResource->Serialize({}, comp->mInstanceDataResource->GetName());
    }

    return rootNode;
}

void InstancedStaticModelSystemG::DeserializeInstancedStaticModelComponent(ISerializeWorld* serializeWorld, const DeserializeNode& rootNode, ecs::IComponent* componentPtr)
{
    SCOPED_CPU_TIMING(GroupSerialize, "Deserialize InstancedStaticModelComponent");

    if (rootNode.IsNull())
    {
        return;
    }

    auto comp = static_cast<InstancedStaticModelComponentG*>(componentPtr);
    SerializeContext context;
    comp->Deserialize(rootNode, context);
}

void InstancedStaticModelSystemG::PostDeserializeInstancedStaticModelComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
{
    // all seems quite of wasting since read from component and then set it back
    // need further investigation

    // Set Model and Material to run needed logic
    auto modelH = gameWorld->GetComponent<InstancedStaticModelComponentG>(entityId);
    auto modelSys = gameWorld->GetGameSystem<InstancedStaticModelSystemG>();
    const auto& writer = modelH.Write();

    // model
    modelSys->SetModelVisible(writer, writer->mVisible);
    modelSys->SetModelReceiveDecals(writer, writer->mReceiveDecals);
    modelSys->SetModelAsset(writer, writer->mAsset);

    int lodIdx = 0;
    for (auto& singleLODModelProperty : writer->mLODModelProperties)
    {
        int subModelIdx = 0;
        for (auto& subModelProperty : singleLODModelProperty.mSubModelProperties)
        {
            modelSys->SetSubModelVisible(writer, subModelProperty.mVisible, lodIdx, subModelIdx);
            modelSys->SetModelMaterial(writer, subModelProperty.mMaterial, subModelIdx, lodIdx);
            subModelIdx++;
        }
        lodIdx++;
    }

    // GlobalScale
    modelSys->SetGlobalScale(writer, writer->mGlobalScale,false);

    // DistanceCulling
    modelSys->SetModelEnityDistanceCulling(writer, writer->mDistanceCulling);

    // InstanceDataResource
    modelSys->SetInstanceDataResource(writer, writer->mInstanceDataResource);


}

void InstancedStaticModelSystemG::UpdateDeserializeInstancedStaticModelComponent(const DeserializeNode& rootNode, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
{
    if (rootNode.IsNull())
    {
        return;
    }

    auto modelH = gameWorld->GetComponent<InstancedStaticModelComponentG>(entityId);
    auto modelSys = gameWorld->GetGameSystem<InstancedStaticModelSystemG>();
    const auto& writer = modelH.Write();

    SerializeContext context;
    writer->Deserialize(rootNode, context);
    PostDeserializeInstancedStaticModelComponent(rootNode, componentPtr, gameWorld, entityId);
}

void InstancedStaticModelSystemG::GetResourceInstancedStaticModelComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr, ResourcePtr resource)
{
    auto modelPtr = static_cast<InstancedStaticModelComponentG*>(componentPtr);
    // Get models array(set main model as the first)


    for (auto& singleLODModelProperty : modelPtr->mLODModelProperties)
    {
        for (auto& prop : singleLODModelProperty.mSubModelProperties)
        {
            if (auto materialPtr = prop.mMaterial; materialPtr)
            {
                resource->AddReferenceResource(materialPtr->GetGuid_Str());
            }
        }
    }

    if (modelPtr->mAsset)
    {
        resource->AddReferenceResource(modelPtr->mAsset->GetGuid_Str());
    }
    if (modelPtr->mInstanceDataResource)
    {
        resource->AddReferenceResource(modelPtr->mInstanceDataResource->GetGuid_Str());
    }
}

InstancedStaticModelSystemG* InstancedStaticModelSystemG::CreateInstance()
{
    return new InstancedStaticModelSystemG();
}

InstancedStaticModelSystemG::InstancedStaticModelSystemG()
{
    mRenderSystem = InstancedStaticModelSystemR::CreateInstance();
}

InstancedStaticModelSystemG::~InstancedStaticModelSystemG()
{
    if (mIsRenderObjectOwner)
    {
        mRenderSystem->Release();
    }
    mRenderSystem = nullptr;
}

void InstancedStaticModelSystemG::Release()
{
    delete this;
}

RenderSystemBase* InstancedStaticModelSystemG::GetRenderSystem()
{
    return mRenderSystem;
}

void InstancedStaticModelSystemG::OnBeginFrame(FrameParam* frameParam)
{
    mModelChangeList.GetContainer().clear();
#if CROSSENGINE_EDITOR
    int resId = ClassID(MeshAssetDataResource);
    mFirstUpdateEnable |= gResourceMgr.CheckChangedResByID(resId);
#endif
}

void InstancedStaticModelSystemG::OnFirstUpdate(FrameParam* frameParam)
{
#if CROSSENGINE_EDITOR
    int resId = ClassID(MeshAssetDataResource);
    const auto* changedResList = gResourceMgr.GetChangedResListById(resId);
    if (changedResList)
    {
        for (auto& res : *changedResList)
        {
            OnMeshChanged(TypeCast<resource::MeshAssetDataResource>(res));
        }
    }
#endif
}

void InstancedStaticModelSystemG::OnEndFrame(FrameParam* frameParam) {}

bool InstancedStaticModelSystemG::SetModelAsset(const InstancedStaticModelComponentWriter& modelH, const MeshAssetDataResourcePtr& meshAssetDataResourcePtr)
{
    if (!meshAssetDataResourcePtr || !meshAssetDataResourcePtr->GetAssetData())
        return false;

    modelH->mAssetPath = meshAssetDataResourcePtr->GetGuid_Str();
    modelH->mAsset = meshAssetDataResourcePtr;

     //find the largest mesh parts;
    if (meshAssetDataResourcePtr)
    {
        const MeshAssetData* meshAsset = meshAssetDataResourcePtr->GetAssetData();

        for (auto lodIndex = 0; lodIndex < meshAsset->GetLodCount(); lodIndex++)
        {
            auto& lod = lodIndex < modelH->mLODModelProperties.size() ? modelH->mLODModelProperties[lodIndex] : modelH->mLODModelProperties.emplace_back();
            for (auto submeshIndex = lod.mSubModelProperties.size(); submeshIndex < meshAsset->GetMeshPartCount(lodIndex); submeshIndex++)
            {
                auto& submesh = lod.mSubModelProperties.emplace_back();
                submesh.mMaterial = mDefaultMaterial;
                submesh.mMaterialPath = mDefaultMaterial->GetGuid_Str();
            }
            lod.mSubModelProperties.resize(meshAsset->GetMeshPartCount(lodIndex));
        }
        modelH->mLODModelProperties.resize(meshAsset->GetLodCount());
        meshAssetDataResourcePtr->OnDataUploaded();
    }
    else
    {
        modelH->mLODModelProperties.clear();
    }

    UpdateAABB(modelH.GetEntityID());

    DispatchRenderingCommandWithToken([=, entity = modelH.GetEntityID()]() { mRenderSystem->SetModelAsset(entity, meshAssetDataResourcePtr); });

    return true;
}

bool InstancedStaticModelSystemG::SetModelAssetPath(const InstancedStaticModelComponentWriter& modelH, const std::string& assetpath)
{
    auto LoadMeshAsset = [](const std::string& assetpath) {
        auto resourceptr = gAssetStreamingManager->LoadSynchronously(assetpath.c_str());
        auto asset = cross::TypeCast<resource::MeshAssetDataResource>(resourceptr);
        return asset;
    };

    return SetModelAsset(modelH, LoadMeshAsset(assetpath));
}

void InstancedStaticModelSystemG::SetModelMaterial(const InstancedStaticModelComponentWriter& modelH, MaterialInterfacePtr material, SInt32 subModelIndex, SInt32 lodIndex)
{
    auto SetSubModelMaterial = [](auto& singleLODModelProperty, MaterialInterfacePtr material, SInt32 subModelIndex) {
        if (subModelIndex >= 0)
        {
            Assert(subModelIndex < singleLODModelProperty.mSubModelProperties.size());
            singleLODModelProperty.mSubModelProperties[subModelIndex].mMaterial = material;
            singleLODModelProperty.mSubModelProperties[subModelIndex].mMaterialPath = material->GetGuid_Str();
        }
        else
        {
            for (auto& prop : singleLODModelProperty.mSubModelProperties)
            {
                prop.mMaterial = material;
                prop.mMaterialPath = material->GetGuid_Str();
            }
        }
    };

    auto& lodModelProperties = modelH->mLODModelProperties;
    if (lodIndex >= 0)
    {
        if (lodIndex >= lodModelProperties.size())
        {
            lodModelProperties.resize(lodIndex + 1);
        }

        SetSubModelMaterial(lodModelProperties[lodIndex], material, subModelIndex);
    }
    else
    {
        for (auto& singleLODModelProperty : lodModelProperties)
        {
            SetSubModelMaterial(singleLODModelProperty, material, subModelIndex);
        }
    }

    DispatchRenderingCommandWithToken([=, entity = modelH.GetEntityID()] {
        auto* renderMaterial = TYPE_CAST(MaterialR*, material->GetRenderMaterial());
        Assert(renderMaterial);

        mRenderSystem->SetModelMaterial(entity, renderMaterial, subModelIndex, lodIndex);
    });
}

bool InstancedStaticModelSystemG::SetModelMaterialPath(const InstancedStaticModelComponentWriter& modelH, std::string const& materialPath, SInt32 subModelIndex, SInt32 lodIndex)
{
    auto LoadMaterialAsset = [](const std::string& assetpath) {
        auto materialResource = cross::TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(assetpath.c_str()));
        return materialResource;
    };

    auto materialResource = LoadMaterialAsset(materialPath);
    if (!materialResource.get())
        return false;

    SetModelMaterial(modelH, materialResource, (SInt8)subModelIndex, lodIndex);
    mModelChangeList.EmplaceChangeData(modelH.GetEntityID());
    return true;
}

MaterialInterfacePtr InstancedStaticModelSystemG::GetModelMaterial(const InstancedStaticModelComponentReader& modelH, UInt32 subModelIndex, UInt32 lodIndex) const
{
    if (modelH->mAsset)
    {
        if (lodIndex < modelH->mLODModelProperties.size() && subModelIndex < modelH->mLODModelProperties[lodIndex].mSubModelProperties.size())
        {
            return modelH->mLODModelProperties[lodIndex].mSubModelProperties[subModelIndex].mMaterial;
        }
    }

    return MaterialInterfacePtr();
}

void InstancedStaticModelSystemG::SetModelEnityDistanceCulling(const InstancedStaticModelComponentWriter& modelH, const EntityDistanceCulling& entityCulling)
{
    modelH->mDistanceCulling = entityCulling;

    DispatchRenderingCommandWithToken([=, entity = modelH.GetEntityID()]() {
        mRenderSystem->SetModelEntityDistanceCulling(entity, entityCulling);   // Must set before model
    });
}

EntityDistanceCulling InstancedStaticModelSystemG::GetModelEnityDistanceCulling(const InstancedStaticModelComponentReader& model)
{
    return model->mDistanceCulling;
}

bool InstancedStaticModelSystemG::IsModelVisible(const InstancedStaticModelComponentReader& modelH) const
{
    return modelH->mVisible;
}

void InstancedStaticModelSystemG::SetModelVisible(const InstancedStaticModelComponentWriter& modelH, bool isVisible)
{
    modelH->mVisible = isVisible;

    mModelChangeList.EmplaceChangeData(modelH.GetEntityID());

    DispatchRenderingCommandWithToken([=, entity = modelH.GetEntityID()]() { mRenderSystem->SetModelVisible(entity, isVisible); });
}

bool InstancedStaticModelSystemG::IsSubModelVisible(const InstancedStaticModelComponentReader& modelH, UInt32 lodIndex, UInt32 subModelIndex) const
{
    Assert(modelH->mLODModelProperties.size() > 0ull && subModelIndex < static_cast<int>(modelH->mLODModelProperties[lodIndex].mSubModelProperties.size()));
    return modelH->mLODModelProperties[lodIndex].mSubModelProperties[subModelIndex].mVisible;
}

void InstancedStaticModelSystemG::SetSubModelVisible(const InstancedStaticModelComponentWriter& modelH, bool isVisible, UInt32 lodIndex, SInt32 subModelIndex)
{
    if (subModelIndex >= 0)
    {
        modelH->mLODModelProperties[lodIndex].mSubModelProperties[subModelIndex].mVisible = isVisible;
    }
    else
    {
        for (auto& prop : modelH->mLODModelProperties[lodIndex].mSubModelProperties)
        {
            prop.mVisible = isVisible;
        }
    }

    DispatchRenderingCommandWithToken([=, entity = modelH.GetEntityID()]() { mRenderSystem->SetSubModelVisible(entity, isVisible, lodIndex, subModelIndex); });
}

void InstancedStaticModelSystemG::SetModelReceiveDecals(const InstancedStaticModelComponentWriter& modelH, bool value)
{
    modelH->mReceiveDecals = value;

    mModelChangeList.EmplaceChangeData(modelH.GetEntityID());

    DispatchRenderingCommandWithToken([=, entity = modelH.GetEntityID()]() { mRenderSystem->SetModelReceiveDecals(entity, value); });
}

bool InstancedStaticModelSystemG::GetModelReceiveDecals(const InstancedStaticModelComponentReader& modelH) const
{
    return modelH->mReceiveDecals;
}

void InstancedStaticModelSystemG::SetModelDirty(const InstancedStaticModelComponentWriter& modelH)
{
    DispatchRenderingCommandWithToken([=, entity = modelH.GetEntityID()]() { mRenderSystem->SetModelDirty(entity); });
}

void InstancedStaticModelSystemG::SetGlobalScale(const InstancedStaticModelComponentWriter& modelH, float value, bool rebuildClusterTree)
{
    float globalScale = std::max(1e-2f, value);
    modelH->mGlobalScale = globalScale;
    DispatchRenderingCommandWithToken([=, entity = modelH.GetEntityID()]() { mRenderSystem->SetGlobalScale(entity, globalScale,rebuildClusterTree); });
}


float InstancedStaticModelSystemG::GetGlobalScale(const InstancedStaticModelComponentReader& modelH) const
{
    return modelH->mGlobalScale;
}
// void InstancedStaticModelSystemG::SetInstanceCount(const InstancedStaticModelComponentWriter& modelH, UInt32 instanceCount)
//{
//     modelH->mInstanceDataResource->mInstanceCount = instanceCount;
//
//     mModelChangeList.EmplaceChangeData(modelH.GetEntityID());
//     {
//         InstancedStaticModelChangeEvent event;
//         event.mData.mEntity = modelH.GetEntityID();
//         DispatchImmediateEvent<InstancedStaticModelChangeEvent>(event);
//     }
// }
//
// UInt32 InstancedStaticModelSystemG::GetInstanceCount(const InstancedStaticModelComponentReader& modelH) const
//{
//     return modelH->mInstanceDataResource->mInstanceCount;
// }
//
// void InstancedStaticModelSystemG::ClearAllInstanceDatas(const InstancedStaticModelComponentWriter& modelH)
//{
//     modelH->mInstanceDataResource->mInstanceMembers.clear();
// }
//
// void InstancedStaticModelSystemG::ClearInstanceMemberData(const InstancedStaticModelComponentWriter& modelH, const std::string& name)
//{
//     modelH->mInstanceDataResource->mInstanceMembers.erase(name);
// }
//
// void InstancedStaticModelSystemG::SetInstanceMemberData(const InstancedStaticModelComponentWriter& modelH, const std::string& name, UInt32 type, const void* data, UInt32 size, UInt32 stride)
//{
//     auto& instanceMemberData = modelH->mInstanceDataResource->mInstanceMembers[name];
//     instanceMemberData.Type = static_cast<InstanceMemberType>(type);
//     instanceMemberData.Stride = stride;
//     instanceMemberData.Data.resize(size);
//     memcpy(instanceMemberData.Data.data(), data, size);
//
//     mModelChangeList.EmplaceChangeData(modelH.GetEntityID());
//     {
//         InstancedStaticModelChangeEvent event;
//         event.mData.mEntity = modelH.GetEntityID();
//         DispatchImmediateEvent<InstancedStaticModelChangeEvent>(event);
//     }
// }
//
// std::vector<std::string> InstancedStaticModelSystemG::GetInstanceMemberDataNames(const InstancedStaticModelComponentWriter& modelH) const
//{
//     std::vector<std::string> result;
//     for (auto& [name, _] : modelH->mInstanceDataResource->mInstanceMembers)
//     {
//         result.push_back(name);
//     }
//     return result;
// }
//
// void InstancedStaticModelSystemG::GetInstanceMemberData(const InstancedStaticModelComponentWriter& modelH, const std::string& name, UInt32& outType, const void*& outData, UInt32& outSize, UInt32& outStride)
//{
//     const auto& instanceMemberData = modelH->mInstanceDataResource->mInstanceMembers.at(name);
//     outType = ToUnderlying(instanceMemberData.Type);
//     outData = instanceMemberData.Data.data();
//     outSize = static_cast<UInt32>(instanceMemberData.Data.size());
//     outStride = instanceMemberData.Stride;
// }

bool InstancedStaticModelSystemG::SetInstanceDataResource(const InstancedStaticModelComponentWriter& modelH, const InstanceDataResourcePtr& instanceDataResourcePtr)
{
    if (!instanceDataResourcePtr)
        return false;

    modelH->mInstanceDataResource = instanceDataResourcePtr;
    modelH->mInstanceDataResourcePath = instanceDataResourcePtr->GetGuid_Str();
    // dispatchEvent
    InstanceDataChangeEvent e;
    e.mData.mEntity = modelH.GetEntityID();
    DispatchImmediateEvent(e);
    UpdateAABB(modelH.GetEntityID());

    DispatchRenderingCommandWithToken([=, entity = modelH.GetEntityID()]() { mRenderSystem->SetInstanceDataResource(entity, instanceDataResourcePtr); });

    return true;
}

bool InstancedStaticModelSystemG::SetInstanceDataResourcePath(const InstancedStaticModelComponentWriter& modelH, const std::string& resourcePath)
{
    if (resourcePath.empty())
    {
        return false;
    }

    auto LoadInstanceDataResource = [](const std::string& assetpath) {
        auto resourceptr = gAssetStreamingManager->LoadSynchronously(assetpath.c_str());
        auto asset = cross::TypeCast<resource::InstanceDataResource>(resourceptr);
        return asset;
    };

    return SetInstanceDataResource(modelH, LoadInstanceDataResource(resourcePath));
}

BoundingBox InstancedStaticModelSystemG::GetCurrentBoundingBox(ecs::EntityID entity) const
{
    auto reader = mGameWorld->GetComponent<InstancedStaticModelComponentG>(entity).Read();
    if (!reader->mAsset)
    {
        return BoundingBox{};
    }

    BoundingBox primitiveBoundingBox;

    if (!reader->mInstanceDataResource)
    {
        return primitiveBoundingBox;
    }

    if (!reader->mInstanceDataResource->HasMember("Translation") || !reader->mInstanceDataResource->HasMember("Rotation") || !reader->mInstanceDataResource->HasMember("Scale"))
    {
        return primitiveBoundingBox;
    }

    const float* translationPtr = reinterpret_cast<const float*>(reader->mInstanceDataResource->GetInstanceMemberData("Translation").mData.data());
    const float* rotationPtr = reinterpret_cast<const float*>(reader->mInstanceDataResource->GetInstanceMemberData("Rotation").mData.data());
    const float* scalePtr = reinterpret_cast<const float*>(reader->mInstanceDataResource->GetInstanceMemberData("Scale").mData.data());
    const float globalScale = reader->mGlobalScale;
    for (UInt32 instanceIndex = 0; instanceIndex < reader->mInstanceDataResource->mInstanceCount; instanceIndex++)
    {
        Float3 translation(translationPtr);
        translationPtr += 3;

        Float3 rotation(rotationPtr);
        rotationPtr += 3;

        Float3 scale(scalePtr);
        scalePtr += 3;

        Float4x4 worldMatrix = Float4x4::Compose(scale * globalScale, Quaternion::EulerToQuaternion(rotation), translation);

        BoundingBox currInstanceBoundingBox;
        reader->mAsset->GetAssetData()->GetBoundingBox().Transform(currInstanceBoundingBox, worldMatrix);

        BoundingBox::CreateMerged(primitiveBoundingBox, primitiveBoundingBox, currInstanceBoundingBox);
    }

    return primitiveBoundingBox;
}

void InstancedStaticModelSystemG::OnMeshChanged(MeshAssetDataResourcePtr meshAsset)
{
    if (mGameWorld->GetEnable())
    {
        auto transSys = mGameWorld->GetGameSystem<TransformSystemG>();
        transSys->TraverseHierarchyDepth(transSys->GetRootEntity(), [this, meshAsset](ecs::EntityID entity) {
            if (mGameWorld->IsEntityAlive(entity))
            {
                auto modelComp = mGameWorld->GetComponent<InstancedStaticModelComponentG>(entity);
                if (modelComp.IsValid())
                {
                    if (modelComp.Read()->mAsset == meshAsset)
                    {
                        SetModelAsset(modelComp.Write(), meshAsset);
                    }
                }
            }
        });
    }
}

void InstancedStaticModelSystemG::SetIntersection(const InstancedStaticModelComponentWriter& modelH, bool enable)
{
    modelH->mEnabledIntersection = enable;
}

bool InstancedStaticModelSystemG::GetIntersection(const InstancedStaticModelComponentReader& modelH) const
{
    return modelH->mEnabledIntersection;
}

void InstancedStaticModelSystemG::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    GameSystemBase::NotifyEvent(event, flag);

    if (event.mEventType == OnSystemAddToGameWorldEvent::sEventType)
    {
        mDefaultMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(EngineGlobal::Inst().GetSettingMgr()->GetRenderPipelineSetting()->DefaultMaterial));
    }
}

void InstancedStaticModelSystemG::NotifyInstanceDataResourceChange(const resource::InstanceDataResource* instanceDataResource)
{
    auto instancedStaicModelComps = mGameWorld->Query<InstancedStaticModelComponentG>();
    for (auto modelComp : instancedStaicModelComps)
    {
        if (modelComp.Read()->mInstanceDataResource.get() == instanceDataResource)
        {
            InstanceDataChangeEvent e;
            e.mData.mEntity = modelComp.GetEntityID();
            DispatchImmediateEvent(e);
            UpdateAABB(modelComp.GetEntityID());
        }
    }

    mRenderSystem->NotifyInstanceDataResourceChange(instanceDataResource);
}

void InstancedStaticModelSystemG::UpdateAABB(ecs::EntityID entity)
{
    auto AABBComp = mGameWorld->GetComponent<AABBComponentG>(entity);
    mGameWorld->GetGameSystem<AABBSystemG>()->SetLocalAABB(AABBComp.Write(), GetCurrentBoundingBox(entity));
}

UInt32 InstancedStaticModelSystemG::GetModelAssetLODCount(const InstancedStaticModelComponentReader& modelH) const
{
    if (modelH->mAsset)
    {
        return modelH->mAsset->GetAssetData()->GetLodCount();
    }
    else
    {
        return 0;
    }
}

}   // namespace cross
