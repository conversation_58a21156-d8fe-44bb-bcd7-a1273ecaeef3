#include "EnginePrefix.h"
#include "Runtime/GameWorld/SkinnedMeshBuilderG.h"
#include "RenderEngine/SkinnedMeshBuilderR.h"

namespace cross
{

SkinnedMeshBuilderG::SkinnedMeshBuilderG()
	:MeshBuilderBaseG("CrossSkinnedMeshBuilder")
{
	mRenderObject = SkinnedMeshBuilderR::CreateInstance();
}

SkinnedMeshBuilderG::~SkinnedMeshBuilderG()
{
	if (mOwnRenderObject)
	{
		mRenderObject->Release();
	}
	mRenderObject = nullptr;
}

cross::MeshBuilderBaseR* SkinnedMeshBuilderG::GetRenderObject()
{
	return mRenderObject;
}

}