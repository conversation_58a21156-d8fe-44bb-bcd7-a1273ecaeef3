#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "Runtime/GameWorld/VolumeTriggerSystemG.h"
#include "CrossPhysics/PhysicsEngine/PhysicsGeometry.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/TransformSystemG.h"
#include "Runtime/GameWorld/PhysicsSystemG.h"
#include "Threading/RenderingThread.h"
#include "CECommon/Common/SettingsManager.h"

namespace cross 
{
ecs::ComponentDesc* VolumeTriggerComponentG::GetDesc() 
{
    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::VolumeTriggerComponentG>(
        { false,
        true,
        true },
        &VolumeTriggerSystemG::SerializeVolumeTriggerComponent, 
        &VolumeTriggerSystemG::DeserializeVolumeTriggerComponent);
}

VolumeTriggerSystemG* VolumeTriggerSystemG::CreateInstance()
{
    return new VolumeTriggerSystemG();
}

VolumeTriggerSystemG::VolumeTriggerSystemG() 
{
    mVolumeTriggerSystemR = VolumeTriggerSystemR::CreateInstance();
}
VolumeTriggerSystemG::~VolumeTriggerSystemG() 
{
    if (mIsRenderObjectOwner) 
    {
        mVolumeTriggerSystemR->Release();
    }
    mVolumeTriggerSystemR = nullptr;
}

void VolumeTriggerSystemG::Release() 
{
    delete this;
}

void VolumeTriggerSystemG::NotifyEvent(const SystemEventBase& event, UInt32& flag) 
{}

void VolumeTriggerSystemG::NotifyAddRenderSystemToRenderWorld() 
{
    mIsRenderObjectOwner = false;
}

RenderSystemBase* VolumeTriggerSystemG::GetRenderSystem()
{
    return mVolumeTriggerSystemR;
}

void VolumeTriggerSystemG::OnBeginFrame(FrameParam* frameParam) 
{
}

void VolumeTriggerSystemG::OnBuildUpdateTasks(FrameParam* frameParam)
{
    CreateTaskFunction<threading::ThreadID::GameThreadLocal>(FrameTickStage::Update, {}, [this]() {
        QUICK_SCOPED_CPU_TIMING("VolumeTriggerUpdate");
        auto transSys = mGameWorld->GetGameSystem<TransformSystemG>();
        auto physSys = mGameWorld->GetGameSystem<PhysicsSystemG>();

        std::array<cross::PhysicsHitResult, 10> queryResults;
        auto compHandles = mGameWorld->Query<VolumeTriggerComponentG>();
        auto PhysHandles = mGameWorld->Query<PhysicsComponentG>();

        if (mGameWorld->GetWorldType() == WorldTypeTag::PIEWorld || EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeStandAlone) 
        {
            for (const auto& compH : compHandles) 
            {
                ecs::EntityID entity = compH.GetEntityID();
                if (compH.Read()->GenerateOverlap) 
                {
                    PhysicsGeometryBase* queryGeo = nullptr;
                    auto transComp = mGameWorld->GetComponent<WorldTransformComponentG>(entity).Read();
                    if (mGameWorld->HasComponent<PhysicsComponentG>(entity))
                    {
                        auto physComp = mGameWorld->GetComponent<PhysicsComponentG>(entity).Read();
                        auto physWriteComp = mGameWorld->GetComponent<PhysicsComponentG>(entity).Write();

                        auto translation = transSys->GetWorldTranslation(transComp);
                        auto scale = transSys->GetWorldScale(transComp);
                        auto rotation = transSys->GetWorldRotation(transComp);

                        auto boxGeometries = physSys->GetExtraShape(physComp);
                        if (boxGeometries == nullptr) 
                        {
                            PhysicsGeometryBox box(translation, Quaternion::Identity(), scale);
                            physSys->AddExtraBoxShape(physWriteComp, box);
                            boxGeometries = physSys->GetExtraShape(physComp);
                        }

                        if (boxGeometries->mBoxGeometry.size() > 0)
                        {
                            queryGeo = &boxGeometries->mBoxGeometry[0];
                        }
                        
                        if (queryGeo)
                        {
                            auto hitNum = physSys->Overlap(queryGeo, translation, rotation, scale, physSys->GetCollisionMask(physComp), static_cast<UInt32>(queryResults.size()), queryResults.data());
                            auto CurVector = compH.Write()->curEntitys;
                            auto& ContainerVector = compH.Write()->containerEntitys;

                            for (UInt32 i = 0; i < hitNum; i++) 
                            {
                                auto hitEntity = queryResults[i].hitActor->GetCustomData<CustomPhyData>()->entityId;
                                if (hitEntity == entity)
                                {
                                    continue;
                                }
                                if (CurVector.size() == 0) 
                                {
                                    CurVector.emplace_back(hitEntity);
                                }
                                else
                                {
                                    auto result = std::find(CurVector.begin(), CurVector.end(), hitEntity);
                                    if (result == CurVector.end())
                                    {
                                        CurVector.emplace_back(hitEntity);
                                    }
                                }
                            }

                            std::vector<ecs::EntityID> CompareVector(10);
                            std::sort(CurVector.begin(), CurVector.end());
                            std::sort(ContainerVector.begin(), ContainerVector.end());
                            std::vector<ecs::EntityID>::iterator it = std::set_symmetric_difference(CurVector.begin(), CurVector.end(), ContainerVector.begin(), ContainerVector.end(), CompareVector.begin());

                            for (auto index = CompareVector.begin(); index != it; ++index) 
                            {
                                auto result = std::find(CurVector.begin(), CurVector.end(), *index);
                                if (result != CurVector.end()) 
                                {
                                    ContainerVector.emplace_back(*result);
                                    OverlapChangeEvent event;
                                    event.mData.mEntity = *result;
                                    event.mData.mType = OverlapType::Begin;
                                    event.mData.mTriggerVolume = entity;
                                    DispatchImmediateEvent<OverlapChangeEvent>(event);
                                }
                                else
                                {
                                    auto containerResult = std::find(ContainerVector.begin(), ContainerVector.end(), *index);
                                    OverlapChangeEvent event;
                                    event.mData.mEntity = *containerResult;
                                    event.mData.mType = OverlapType::End;
                                    event.mData.mTriggerVolume = entity;
                                    DispatchImmediateEvent<OverlapChangeEvent>(event);
                                    ContainerVector.erase(containerResult);
                                }
                            }
                        }
                    }
                }
            }
        }

        if (mGameWorld->GetWorldType() == WorldTypeTag::DefaultWorld) 
        {
            if(compHandles.GetEntityNum() != 0)
            {
                for (const auto& compH : compHandles) 
                {
                    ecs::EntityID entity = compH.GetEntityID();
                    auto transComp = mGameWorld->GetComponent<WorldTransformComponentG>(entity).Read();
                    auto translation = transSys->GetWorldTranslation(transComp);
                    auto rotation = transSys->GetWorldRotation(transComp);
                    auto scale = transSys->GetWorldScale(transComp);
                    DispatchRenderingCommandWithToken([renderSystem = mVolumeTriggerSystemR, entity = entity, extent = scale, center = translation, rotation]()
                        {
                            renderSystem->UpdatePrimitive(entity, extent, center, rotation);
                        }
                    );
                }
            }
        }
    });
}

void VolumeTriggerSystemG::SetGizmoShowing(bool state)
{
    VolumeTriggerSystemR::SetGizmoShowing(state);
}

void VolumeTriggerSystemG::OnEndFrame(FrameParam* frameParam) 
{
    Assert(true);
}

void VolumeTriggerSystemG::GetVolumeTriggerComponent(const VolumeTriggerComponentReader& component, VolumeTriggerComponentG& outValue) 
{
    outValue.GenerateOverlap = component->GenerateOverlap;
}

void VolumeTriggerSystemG::SetVolumeTriggerComponent(const VolumeTriggerComponentWriter& component, const VolumeTriggerComponentG& inValue) 
{
    component->GenerateOverlap = inValue.GenerateOverlap;
}

SerializeNode VolumeTriggerSystemG::SerializeVolumeTriggerComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    auto comp = static_cast<VolumeTriggerComponentG*>(componentPtr);
    SerializeContext context;
    SerializeNode json;
    json = comp->Serialize(context);
    return json;
}

void VolumeTriggerSystemG::DeserializeVolumeTriggerComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr)
{
    if (json.IsNull())
    {
        return;
    }
    auto cmpntPtr = static_cast<VolumeTriggerComponentG*>(componentPtr);
    SerializeContext context;
    cmpntPtr->Deserialize(json, context);
}
}