#include "EnginePrefix.h"
#include "FoliageSystemG.h"
#include "AABBSystemG.h"
#include "TransformSystemG.h"
#include "RenderEngine/FoliageSystemR.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "Resource/MeshAssetDataResource.h"
#include "Resource/Prefab/PrefabResource.h"
#include "Resource/BinaryResource.h"
#include "AssetStreaming.h"
#include "GameWorld.h"
#include "RenderPropertySystemG.h"
#include "RenderEngine/RenderMaterial.h"
#include "String/StringCodec.h"
#include "CECommon/Geometry/AABBTree.h"
#include "Prefab/PrefabSystemG.h"
#include "ModelSystemG.h"
#include "LightSystemG.h"
#include "EntityMetaSystem.h"
#include "CECommon/Utilities/Random.h"
#include "CECommon/Common/OctahedralCommon.h"
#include "WorldSystemG.h"
#define AABBTREECOUNT          1000U
#define INSTANCESPLITCOUNTTEST 50000U /*100U*/

namespace cross {
FoliageComponentG::~FoliageComponentG() 
{
    if (mDataContainer)
    {
        mDataContainer->mInstanceData.clear();
        std::vector<InstanceDataVecContainer::InstanceData>().swap(mDataContainer->mInstanceData);
        mDataContainer->mTransformVector.clear();
        std::vector<FoliageInstanceCompactData>().swap(mDataContainer->mTransformVector);
        mDataContainer->mInstanceDataLight.clear();
        std::vector<InstanceDataVecContainer::InstanceDataLight>().swap(mDataContainer->mInstanceDataLight);
    }

}

ecs::ComponentDesc* FoliageComponentG::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<FoliageComponentG>({false, true, true, false},
                                                                                           &FoliageSystemG::SerializeFoliageComponent,
                                                                                           &FoliageSystemG::DeserializeFoliageComponent,
                                                                                           &FoliageSystemG::PostDeserializeFoliageComponent,
                                                                                           &FoliageSystemG::UpdateDeserializeModelComponent,
                                                                                           &FoliageSystemG::GetResourceFoliageComponent);
}


FoliageSystemG* FoliageSystemG::CreateInstance()
{
    return new FoliageSystemG;
}

void FoliageSystemG::OnFirstUpdate(FrameParam* frameParam)
{
    auto* settingMgr = EngineGlobal::GetSettingMgr();
    settingMgr->SubscribeEvent<RenderPipelineSettingEvent>(this, 0);
    mGameWorld->SubscribeRemainedEvent<EntityDestroyEvent>(this, true);
}

void FoliageSystemG::OnBuildUpdateTasks(FrameParam* frameParam)
{
    CreateTaskFunction<threading::ThreadID::GameThreadLocal>(FrameTickStage::Update, {}, [this] 
    {
#if USE_COROUTINE
        std::vector<ecs::EntityID> localLoadingList;
        {
            std::scoped_lock lock(mLoadMutex);
            localLoadingList = std::move(mLoadingList);
        }
        Assert(mLoadingList.empty());

        for (const auto& entity : localLoadingList)
        {
            if (mGameWorld->IsEntityAlive(entity))
            {
                auto [aabbComp, foliageComp] = mGameWorld->GetComponent<AABBComponentG, FoliageComponentG>(entity);
                foliageComp.Write()->mInstanceDataReady = true;
                BoundingBox localbounds = GetLocalBound(foliageComp.Write());
                mGameWorld->GetGameSystem<AABBSystemG>()->SetLocalAABB(aabbComp.Write(), localbounds);
                RefreshRenderState(mGameWorld, entity);
            }
        }
#else
        std::scoped_lock lock(mLoadMutex);
        for (auto it = mLoadingList.begin(); it != mLoadingList.end();)
        {

            if (it->second.first->Complete())
            {
                it->second.second(it->first);
                
                it = mLoadingList.erase(it);
            }
            else
            {
                it++;
            }
        }
#endif
    });
    if (mDestroyList.size() > 0)
    {
        if (mDelayFrame > 2)
        {
            for (auto entity : mDestroyList)
            {
                mGameWorld->DestroyEntityByHierarchy(entity);
            }
            mDestroyList.clear();
            mGameWorld->CallEditorUpdateHierarchyCallback();
        }
        ++mDelayFrame;
    }
}

SerializeNode FoliageSystemG::SerializeFoliageComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr, ecs::EntityID entityID)
{
    SCOPED_CPU_TIMING(GroupSerialize, "SerializeFoliageComponent");

    SerializeNode componentJson;
    const auto foliageComponent = static_cast<FoliageComponentG*>(componentPtr);
    componentJson["mEnable"] = foliageComponent->mEnable;
    componentJson["mPrefabResource"] = foliageComponent->mPrefabResource;
    componentJson["mPrimaryMeshAsset"] = foliageComponent->mPrimaryMeshAsset->GetGuid_Str();
    componentJson["mPrimaryMaterial"] = foliageComponent->mPrimaryMaterial->GetGuid_Str();
    componentJson["mLightCastShadow"] = foliageComponent->mLightCastShadow;
    componentJson["mFoliageGenerationType"] = foliageComponent->mFoliageGenerationType;
    componentJson["mLocalRotation"] = foliageComponent->mLocalTransform.rotation.Serialize();
    componentJson["mLocalTranslation"] = foliageComponent->mLocalTransform.translation.Serialize();
    componentJson["mLocalScale"] = foliageComponent->mLocalTransform.scale.Serialize();
    componentJson["mLightLocalRotation"] = foliageComponent->mLightLocalTransform.rotation.Serialize();
    componentJson["mLightLocalTranslation"] = foliageComponent->mLightLocalTransform.translation.Serialize();
    componentJson["mLightLocalScale"] = foliageComponent->mLightLocalTransform.scale.Serialize();
    componentJson["mGlobalScale"] = foliageComponent->mGlobalScale;
    componentJson["mGlobalRangeScale"] = foliageComponent->mGlobalRangeScale;
    componentJson["mMaxRandomCulling"] = foliageComponent->mMaxRandomCulling;
    componentJson["mDensity"] = foliageComponent->mDensity;
    componentJson["mPCGReservedCapacity"] = foliageComponent->mPCGReservedCapacity;

    auto EntityInstanceName = [](ISerializeWorld* serializeWorld, ecs::EntityID entityID) { 

        GameWorld* gameworld = dynamic_cast<GameWorld*>(serializeWorld);

        if (!gameworld)
            return std::make_pair(std::string(""), std::string(""));

        auto entitymetasys = gameworld->GetGameSystem<EntityMetaSystem>();
        auto transform = gameworld->GetGameSystem<TransformSystemG>();

 

        auto ent = entityID;
        std::string name = "";
        while ((!PrefabManager::GetInstance().IsPrefabInheritInstanceRoot(gameworld, ent) && !PrefabManager::GetInstance().IsPrefabInstanceRoot(gameworld, ent)) && ent != ecs::EntityID::InvalidHandle())
        {
            auto entityMeta = gameworld->GetComponent<ecs::EntityMetaComponentG>(ent);
            auto ent_name = entitymetasys->GetName(entityMeta.Read());
            name = ent_name + "_" + name;

            ent = transform->GetEntityParent(ent);
        }

        auto prefabPath = PrefabManager::GetInstance().IsPrefabInheritInstanceRoot(gameworld, ent) ? PrefabManager::GetInstance().GetInheritPrefabId(gameworld, ent) : PrefabManager::GetInstance().GetPrefabId(gameworld, ent);
        auto path = gResourceMgr.ConvertGuidToPath(prefabPath);
        auto directory = PathHelper::GetParentPath(path);
        auto filename = PathHelper::GetBaseFileName(path);

        return std::make_pair(directory + "/Prefab" + filename, name);

    };

    for (const auto& prop : foliageComponent->mSubmeshProperty)
    {
        SerializeNode propJson;
        propJson["mVisible"] = prop.visible;
        componentJson["mSubmeshProperty"].PushBack(std::move(propJson));
    }

    // Assert(!foliageComponent->mLoDSections.empty());
    for (const auto& section : foliageComponent->mLoDSections)
    {
        SerializeNode sectionJson;
        if (section.mDefaultMaterial)
        {
            sectionJson["mDefaultMaterial"] = section.mDefaultMaterial->GetGuid_Str();
        }
        for (const auto& subSection : section.mSubSectionMaterials)
        {
            sectionJson["mSubSectionMaterials"].PushBack(subSection->GetGuid_Str());
        }
        componentJson["mLoDSections"].PushBack(std::move(sectionJson));
    }

    if (foliageComponent->mDataContainer->mInstanceData.empty())
    {
        // componentJson["mInstanceData"] = nullptr;
        componentJson["mInstanceDataBase64"] = nullptr;
    }
    else
    {
        auto instanceCount = foliageComponent->mDataContainer->mInstanceData.size();
        UInt32 stride = sizeof(Transform);
        auto memSize = instanceCount * stride;
        unsigned char* data = new unsigned char[memSize];
        int i = 0;
        for (auto& instance : foliageComponent->mDataContainer->mInstanceData)
        {
            // SerializeNode instanceJson;
            // instanceJson["mTransform"] = {"mTranslation"_k = instance.mTransform.translation.Serialize(), "mScale"_k = instance.mTransform.scale.Serialize(), "mRotation"_k = instance.mTransform.rotation.Serialize()};
            // componentJson["mInstanceData"].PushBack(std::move(instanceJson));
            if (instance.mDelete)
                continue;
            memcpy(data + i * stride, &instance.mTransform, stride);
            ++i;
        }
        if (i > 0)
        {
            auto serializeSize = i * stride;
          

            std::string nda_path;
            if (foliageComponent->mInstanceResource)
            {
                nda_path = foliageComponent->mInstanceResource->GetName();
            }
            else
            {
                // a hack to get correct path data
                auto [directory, name] = EntityInstanceName(serializeWorld, entityID);
                nda_path = directory.empty() ? "Contents/" : directory + "/" + name + "_instance.nda";
            }
            auto binaryResource = gResourceMgr.CreateResourceAs<resource::BinaryResource>();
            binaryResource->CreateAsset(nda_path);
            binaryResource->Serialize(data, serializeSize, nda_path);

            foliageComponent->mInstanceResource = binaryResource;

            if (!foliageComponent->mInstanceResource)
            {
                std::string base64_desc = Base64Codec::encode(reinterpret_cast<char*>(data), serializeSize);
                componentJson["mInstanceDataBase64"] = base64_desc;
            }
            else
            {
                componentJson["mInstanceDataBase64"] = nullptr;
            }
            componentJson["mInstanceDataResource"] = binaryResource->GetGuid_Str();


        }
        else
        {
            componentJson["mInstanceDataBase64"] = nullptr;
        }

        delete[] data;
    }

    if (foliageComponent->mDataContainer->mInstanceDataLight.empty())
    {
        // componentJson["mInstanceDataLight"] = nullptr;
        componentJson["mInstanceDataLightBase64"] = nullptr;
    }
    else
    {
        auto instanceCount = foliageComponent->mDataContainer->mInstanceDataLight.size();
        UInt32 stride = sizeof(InstanceDataVecContainer::InstanceDataLight);
        auto memSize = instanceCount * stride;
        unsigned char* data = new unsigned char[memSize];
        int i = 0;
        for (const auto& instance : foliageComponent->mDataContainer->mInstanceDataLight)
        {
            // SerializeNode instanceJson;
            // instanceJson["mTransform"] =
            //{
            //     "mTranslation"_k = instance.mTransform.translation.Serialize(),
            //     "mScale"_k = instance.mTransform.scale.Serialize(),
            //     "mRotation"_k = instance.mTransform.rotation.Serialize()
            // };
            // instanceJson["mLightDirPos"] = instance.mLightDirPos.Serialize();
            // instanceJson["mLightTilePosition"] = instance.mLightTilePosition.Serialize();
            // instanceJson["mLightAttenuation"] = instance.mLightAttenuation.Serialize();
            // instanceJson["mLightColor"] = instance.mLightColor.Serialize();
            // instanceJson["mLightSpotDiretion"] = instance.mLightSpotDiretion.Serialize();
            // componentJson["mInstanceDataLight"].PushBack(std::move(instanceJson));

            memcpy(data + i * stride, &instance, stride);
            ++i;
        }


        
        std::string nda_path;
        if (foliageComponent->mInstanceDataLightResource)
        {
            nda_path = foliageComponent->mInstanceDataLightResource->GetName();
        }
        else
        {
            auto [directory, name] = EntityInstanceName(serializeWorld, entityID);
            nda_path = directory.empty() ? "Contents/" : directory + "/" + name + "_instanceLight.nda";
        }

        auto binaryResource = gResourceMgr.CreateResourceAs<resource::BinaryResource>();
        binaryResource->CreateAsset(nda_path);
        binaryResource->Serialize(data, memSize, nda_path);
        foliageComponent->mInstanceDataLightResource = binaryResource;

        if (!foliageComponent->mInstanceDataLightResource)
        {
            std::string base64_desc = Base64Codec::encode(reinterpret_cast<char*>(data), memSize);
            componentJson["mInstanceDataLightBase64"] = base64_desc;
        }
        else
        {
            componentJson["mInstanceDataLightBase64"] = nullptr;
        }


        componentJson["mInstanceDataLightResource"] = binaryResource->GetGuid_Str();
        delete[] data;
    }

    return componentJson;
}

void FoliageSystemG::DeserializeFoliageComponent(ISerializeWorld* serializeWorld, const DeserializeNode& componentJson, ecs::IComponent* componentPtr)
{
    SCOPED_CPU_TIMING(GroupSerialize, "DeserializeFoliageComponent");


    if (!componentJson.IsNull())
    {
        auto foliageComponent = static_cast<FoliageComponentG*>(componentPtr);



        if (componentJson.HasMember("mEnable"))
        {
            foliageComponent->mEnable = componentJson["mEnable"].AsBoolean();
        }

        if (componentJson.HasMember("mPrefabResource"))
        {
            foliageComponent->mPrefabResource = componentJson["mPrefabResource"].AsString();
            foliageComponent->mPrefabResourceHolder = gAssetStreamingManager->LoadSynchronously(foliageComponent->mPrefabResource);
        }

        if (componentJson.HasMember("mPCGReservedCapacity"))
        {
            foliageComponent->mPCGReservedCapacity = componentJson["mPCGReservedCapacity"].AsUInt32();
        }

        const auto& meshAssetPath = componentJson["mPrimaryMeshAsset"].AsString();
        foliageComponent->mPrimaryMeshAsset = TypeCast<resource::MeshAssetDataResource>(gAssetStreamingManager->LoadSynchronously(meshAssetPath.c_str()));
        Assert(foliageComponent->mPrimaryMeshAsset);

        const auto& materialPath = componentJson["mPrimaryMaterial"].AsString();
        foliageComponent->mPrimaryMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(materialPath.c_str()));
        Assert(foliageComponent->mPrimaryMaterial);

        if (componentJson.HasMember("mInstanceDataResource") && !componentJson["mInstanceDataResource"].IsNull())
            foliageComponent->mInstanceResource = TypeCast<resource::BinaryResource>(gAssetStreamingManager->LoadSynchronously(componentJson["mInstanceDataResource"].AsString()));

        if (componentJson.HasMember("mInstanceDataLightResource") && !componentJson["mInstanceDataLightResource"].IsNull())
            foliageComponent->mInstanceDataLightResource = TypeCast<resource::BinaryResource>(gAssetStreamingManager->LoadSynchronously(componentJson["mInstanceDataLightResource"].AsString()));



        if (componentJson.HasMember("mLightCastShadow"))
        {
            foliageComponent->mLightCastShadow = componentJson["mLightCastShadow"].AsBoolean();
        }

        if (componentJson.HasMember("mFoliageGenerationType"))
        {
            foliageComponent->mFoliageGenerationType = (FoliageGenerationType)componentJson["mFoliageGenerationType"].AsInt8();
        }

        if (componentJson.HasMember("mLocalRotation"))
        {
            foliageComponent->mLocalTransform.rotation.Deserialize(componentJson["mLocalRotation"]);
        }

        if (componentJson.HasMember("mLocalTranslation"))
        {
            foliageComponent->mLocalTransform.translation.Deserialize(componentJson["mLocalTranslation"]);
        }

        if (componentJson.HasMember("mLocalScale"))
        {
            foliageComponent->mLocalTransform.scale.Deserialize(componentJson["mLocalScale"]);
        }

        if (componentJson.HasMember("mLightLocalRotation"))
        {
            foliageComponent->mLightLocalTransform.rotation.Deserialize(componentJson["mLightLocalRotation"]);
        }

        if (componentJson.HasMember("mLightLocalTranslation"))
        {
            foliageComponent->mLightLocalTransform.translation.Deserialize(componentJson["mLightLocalTranslation"]);
        }

        if (componentJson.HasMember("mLightLocalScale"))
        {
            foliageComponent->mLightLocalTransform.scale.Deserialize(componentJson["mLightLocalScale"]);
        }

        if (componentJson.HasMember("mGlobalScale"))
        {
            foliageComponent->mGlobalScale = componentJson["mGlobalScale"].AsFloat();
        }

        if (componentJson.HasMember("mGlobalRangeScale"))
        {
            foliageComponent->mGlobalRangeScale = componentJson["mGlobalRangeScale"].AsFloat();
        }

        if (componentJson.HasMember("mMaxRandomCulling"))
        {
            foliageComponent->mMaxRandomCulling = componentJson["mMaxRandomCulling"].AsFloat();
        }

        if (componentJson.HasMember("mDensity"))
        {
            foliageComponent->mDensity = componentJson["mDensity"].AsFloat();
        }

        if (componentJson.HasMember("mSubmeshProperty"))
        {
            const auto count = componentJson["mSubmeshProperty"].Size();
            foliageComponent->mSubmeshProperty.clear();
            foliageComponent->mSubmeshProperty.reserve(count);
            for (auto i = 0; i != count; i++)
            {
                const auto propJson = componentJson["mSubmeshProperty"][i];
                SubmeshProperty prop;
                if (propJson.HasMember("mVisible"))
                {
                    prop.visible = propJson["mVisible"].AsBoolean();
                }
                foliageComponent->mSubmeshProperty.push_back(std::move(prop));
            }
        }

        if (componentJson.HasMember("mLoDSections"))
        {
            const auto count = componentJson["mLoDSections"].Size();
            foliageComponent->mLoDSections.clear();
            foliageComponent->mLoDSections.reserve(count);
            for (auto i = 0; i != count; i++)
            {
                const auto sectionJson = componentJson["mLoDSections"][i];
                FoliageComponentG::LoDSection section;
                if (sectionJson.HasMember("mDefaultMaterial"))
                {
                    section.mDefaultMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(sectionJson["mDefaultMaterial"].AsString().c_str()));
                }
                else
                {
                    section.mDefaultMaterial = foliageComponent->mPrimaryMaterial;
                }
                if (sectionJson.HasMember("mSubSectionMaterials"))
                {
                    const auto subSectionCount = sectionJson["mSubSectionMaterials"].Size();
                    section.mSubSectionMaterials.reserve(subSectionCount);
                    for (auto j = 0; j != subSectionCount; j++)
                    {
                        section.mSubSectionMaterials.push_back(TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(sectionJson["mSubSectionMaterials"][j].AsString().c_str())));
                    }
                }
                foliageComponent->mLoDSections.push_back(std::move(section));
            }
        }
        else
        {
            foliageComponent->mLoDSections.push_back({foliageComponent->mPrimaryMaterial});
        }
    }
}

void FoliageSystemG::PostDeserializeFoliageComponent(const DeserializeNode& componentJson, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityID)
{
    if (!gameWorld->HasComponent<RenderPropertyComponentG>(entityID))
    {
        auto prototype = ecs::GetOrCreatePrototypeG<RenderPropertyComponentG>();
        gameWorld->AddComponentByPrototype(entityID, prototype);
    }

    auto transformSystem = gameWorld->GetGameSystem<TransformSystemG>();
    auto foliageSys = gameWorld->GetGameSystem<FoliageSystemG>();
    auto transformComponent = gameWorld->GetComponent<WorldTransformComponentG>(entityID);
    transformSystem->AddMovementEventListener(transformComponent.Write(), gameWorld->GetGameSystem<FoliageSystemG>());

    foliageSys->TryAdjustSubmeshPropertySize(entityID);

     auto foliageComponent = static_cast<FoliageComponentG*>(componentPtr);
    SerializeNode copyNode = SerializeNode::CreateFromDeserializeNode(componentJson);

#if USE_COROUTINE
    //threading::Coro([foliageSys, foliageComponent, entityID, node = std::move(copyNode), &mutex = foliageSys->mLoadMutex]() -> threading::CoroDispatchPtr<>
    //{
    //    co_await threading::Coro([foliageSys, foliageComponent, entityID, &node]()->threading::CoroAsyncPtr<>
    //    {
    //        foliageSys->ReadFoliageInstanceData(node, foliageComponent, entityID);
    //        co_return;
    //    });

    //    {
    //        std::scoped_lock lock(mutex);
    //        foliageSys->mLoadingList.emplace_back(entityID);
    //    }
    //}).Resume();
#else
    auto loadingtask = threading::Dispatch<threading::ThreadID::TaskThread>([foliageSys = foliageSys, copyNode = copyNode, dataContainer = foliageComponent->mDataContainer, 
        instanceResource = foliageComponent->mInstanceResource, instanceLightResource = foliageComponent->mInstanceDataLightResource, componentPtr = foliageComponent, entityID = entityID](auto) 
        { 
            foliageSys->ReadFoliageInstanceData(std::move(copyNode), dataContainer, instanceResource, instanceLightResource, componentPtr, entityID);
        });

    std::pair<threading::TaskEventPtr, std::function<void(ecs::EntityID)>> job = {loadingtask, std::bind(&FoliageSystemG::FoliageRefreshEvents, foliageSys, std::placeholders::_1)};
    if(EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpType::AppStartUpTypeStandAlone)
    {
        loadingtask->WaitForCompletion();
        job.second(entityID);
        job.second = [](ecs::EntityID) {};
    }
    
    {
        std::scoped_lock lockpush(foliageSys->mLoadMutex);
        foliageSys->mLoadingList.push_back({entityID, job});
    }
#endif
}

void FoliageSystemG::UpdateDeserializeModelComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId) 
{
     DeserializeFoliageComponent(gameWorld, json, componentPtr);
     PostDeserializeFoliageComponent(json, componentPtr, gameWorld, entityId);
}

void FoliageSystemG::GetResourceFoliageComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr, ResourcePtr resource)
{
    auto foliagePtr = static_cast<FoliageComponentG*>(componentPtr);
    resource->AddReferenceResource(foliagePtr->mPrimaryMeshAsset->GetGuid_Str());
    resource->AddReferenceResource(foliagePtr->mPrimaryMaterial->GetGuid_Str());
    resource->AddReferenceResource(foliagePtr->mPrefabResourceHolder ? foliagePtr->mPrefabResourceHolder->GetGuid_Str() : foliagePtr->mPrefabResource);
    for (auto& lodSection : foliagePtr->mLoDSections)
    {
        resource->AddReferenceResource(lodSection.mDefaultMaterial->GetGuid_Str());
        for (auto& setMat : lodSection.mSubSectionMaterials)
        {
            resource->AddReferenceResource(setMat->GetGuid_Str());
        }
    }

    if (foliagePtr->mInstanceResource)
        resource->AddReferenceResource(foliagePtr->mInstanceResource->GetGuid_Str());
    if (foliagePtr->mInstanceDataLightResource)
        resource->AddReferenceResource(foliagePtr->mInstanceDataLightResource->GetGuid_Str());
}

void FoliageSystemG::Release()
{
    delete this;
}

void FoliageSystemG::OnPendingToDestroy() 
{
    threading::FlushRenderingCommands();
}

void FoliageSystemG::OnBeginFrame(FrameParam* frameParam)
{
    mChangeList.BeginFrame(frameParam, FRAME_STAGE_GAME);
}

void FoliageSystemG::OnEndFrame(FrameParam* frameParam) {}

RenderSystemBase* FoliageSystemG::GetRenderSystem()
{
    Assert(mFoliageSystemR);

    return mFoliageSystemR;
}

FoliageComponentResources FoliageSystemG::GetEditorResources(const FoliageComponentReader& reader) const
{
    FoliageComponentResources editorResource;

    if (reader->mPrimaryMeshAsset)
        editorResource.mPrimaryMeshAssetPath = reader->mPrimaryMeshAsset->GetGuid_Str();
    if (reader->mPrimaryMaterial)
        editorResource.mPrimaryMaterialPath = reader->mPrimaryMaterial->GetGuid_Str();

    for (const auto& prop : reader->mSubmeshProperty)
    {
        editorResource.mSubmeshProperty.push_back(prop);
    }
    for (const auto& lodSection : reader->mLoDSections)
    {
        FoliageComponentLoDSection editorLoDSection;
        editorLoDSection.mDefaultMaterialPath = lodSection.mDefaultMaterial->GetGuid_Str();
        for (const auto& subSection : lodSection.mSubSectionMaterials)
        {
            editorLoDSection.mSubSectionMaterialPaths.push_back(subSection->GetGuid_Str());
        }
        editorResource.mLoDSections.push_back(std::move(editorLoDSection));
    }
    editorResource.mEnabledIntersection = reader->mIntersection;
    editorResource.mGlobalScale = reader->mGlobalScale;
    editorResource.mPrefabResource = reader->mPrefabResource;

    if (reader->mInstanceResource)
        editorResource.mInstanceResource = reader->mInstanceResource->GetGuid_Str();
    if (reader->mInstanceDataLightResource)
        editorResource.mInstanceLightResource = reader->mInstanceDataLightResource->GetGuid_Str();

    return editorResource;
}

void FoliageSystemG::SetEditorResources(const FoliageComponentWriter& writer, const FoliageComponentResources& resources)
{
    writer->mPrimaryMeshAsset = TypeCast<resource::MeshAssetDataResource>(gAssetStreamingManager->LoadSynchronously(resources.mPrimaryMeshAssetPath));
    writer->mPrimaryMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(resources.mPrimaryMaterialPath));

    writer->mLoDSections.clear();
    for (const auto& lodSection : resources.mLoDSections)
    {
        FoliageComponentG::LoDSection section;
        section.mDefaultMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(lodSection.mDefaultMaterialPath));
        for (const auto& subSection : lodSection.mSubSectionMaterialPaths)
        {
            section.mSubSectionMaterials.push_back(TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(subSection)));
        }
        writer->mLoDSections.push_back(section);
    }

    writer->mSubmeshProperty = resources.mSubmeshProperty;
    writer->mIntersection = resources.mEnabledIntersection;
    writer->mGlobalScale = resources.mGlobalScale;
    RefreshRenderState(mGameWorld, writer.GetEntityID());
}

void FoliageSystemG::SetPrimaryMeshPath(const FoliageComponentWriter& writer, const std::string& path)
{
    writer->mPrimaryMeshAsset = TypeCast<resource::MeshAssetDataResource>(gAssetStreamingManager->LoadSynchronously(path));

    TryAdjustSubmeshPropertySize(writer.GetEntityID());

    RefreshRenderState(mGameWorld, writer.GetEntityID());
}

void FoliageSystemG::SetPrimaryMaterialPath(const FoliageComponentWriter& writer, const std::string& path)
{
    writer->mPrimaryMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(path));
    RefreshRenderState(mGameWorld, writer.GetEntityID());
}

FoliageComponentInstanceData FoliageSystemG::GetEditorInstanceData(const FoliageComponentReader& reader) const
{
    QUICK_SCOPED_CPU_TIMING("FoliageSystemG::GetEditorInstanceData");
    if (reader->mEditorInstanceData.mInstanceData.size() != 0)
    {
        return reader->mEditorInstanceData;
    }
    else
    {
        FoliageComponentInstanceData editorInstanceData;

        for (const auto& instanceData : reader->mDataContainer->mInstanceData)
        {
            editorInstanceData.mInstanceData.push_back(FoliageComponentTransform{instanceData.mTransform.translation, instanceData.mTransform.scale, instanceData.mTransform.rotation, instanceData.mDelete});
        }
        return editorInstanceData;
    }
}

UInt32 FoliageSystemG::GetEditorInstanceDataSize(const FoliageComponentReader& reader) const
{
    if (reader->mEditorInstanceData.mInstanceData.size() != 0)
    {
        return static_cast<UInt32>(reader->mEditorInstanceData.mInstanceData.size());
    }
    else
    {
        return static_cast<UInt32>(reader->mDataContainer->mInstanceData.size());
    }
}

FoliageComponentInstanceData& FoliageSystemG::GetEditorInstanceDataRef(const FoliageComponentWriter& writer)
{
    if (writer->mEditorInstanceData.mInstanceData.size() == 0)
    {
        FoliageComponentInstanceData editorInstanceData;

        for (const auto& instanceData : writer->mDataContainer->mInstanceData)
        {
            editorInstanceData.mInstanceData.push_back(FoliageComponentTransform{instanceData.mTransform.translation, instanceData.mTransform.scale, instanceData.mTransform.rotation});
        }
        writer->mEditorInstanceData = editorInstanceData;
    }
    return writer->mEditorInstanceData;
}

void FoliageSystemG::SetEditorInstanceData(const FoliageComponentWriter& writer, const FoliageComponentInstanceData& instanceData, std::vector<int> indexes)
{
    QUICK_SCOPED_CPU_TIMING("FoliageSystemG::SetEditorInstanceData");
    // dispatchEvent
    InstanceDataChangedEvent e;
    e.mData.mEntity = writer.GetEntityID();
    DispatchImmediateEvent(e);

    // update aabb tree
    bool IsInsert = false;
    bool IsDelete = false;
    InstanceType type = InstanceType::None;

    size_t editorSize = writer->mDataContainer->mInstanceData.size();

    if (editorSize != 0 && indexes.size() != 0)
    {
        if (instanceData.mInstanceData.size() > editorSize)
        {
            IsInsert = true;
            type = InstanceType::Insert;
        }
        // logic delete so ==
        if (instanceData.mInstanceData.size() == editorSize)
        {
            IsDelete = true;
            type = InstanceType::Delete;
        }
    }

    // foliage instance update
    writer->mEditorInstanceData = instanceData;
    if (indexes.size() == 0 && !IsInsert && !IsDelete)
    {
        FoliageInstanceUpdate(writer, instanceData, InstanceType::None, indexes);
        return;
    }
    FoliageInstanceUpdate(writer, instanceData, type, indexes);

    if (IsInsert)
    {
        UpdateFoliageAABBTree(writer, cross::AABBTREETYPE::Insert, indexes);
    }
    if (IsDelete)
    {
        UpdateFoliageAABBTree(writer, cross::AABBTREETYPE::Delete, indexes);
    }
}

void FoliageSystemG::AddEditorInstanceData(const FoliageComponentWriter& writer, const FoliageComponentInstanceData& instanceData, std::vector<int> indexes)
{
    // dispatchEvent
    InstanceDataChangedEvent e;
    e.mData.mEntity = writer.GetEntityID();
    DispatchImmediateEvent(e);
    FoliageInstanceUpdate(writer, instanceData, InstanceType::Insert, indexes);
    UpdateFoliageAABBTree(writer, cross::AABBTREETYPE::Insert, indexes);
}

void FoliageSystemG::UpdateFoliageAABBTree(const FoliageComponentWriter& writer, cross::AABBTREETYPE type, std::vector<int> indexes)
{
    if (!writer->mInstanceBVHAABBTree)
        return;

    auto foliageComponent = mGameWorld->GetComponent<FoliageComponentG>(writer.GetEntityID());
    auto reader = foliageComponent.Read();
    const auto meshBound = reader->mPrimaryMeshAsset->GetAssetData()->GetBoundingBox();
    for (auto& value : indexes)
    {
        const auto worldTransform = GetWorldTransform(reader, static_cast<size_t>(value < 0 ? 0 : value));
        auto worldMatrix = Float4x4::Compose(worldTransform.scale, worldTransform.rotation, worldTransform.translation);
        BoundingBox singleAABB;
        meshBound.Transform(singleAABB, worldMatrix);

        if (type == cross::AABBTREETYPE::Modify)
        {
            if (!writer->mInstanceBVHAABBTree->IsExitElement(value))
            {
                return;
            }
            writer->mInstanceBVHAABBTree->modifyObject(value, singleAABB);
        }
        else if (type == cross::AABBTREETYPE::Insert)
        {
            writer->mInstanceBVHAABBTree->insertObject(value, singleAABB);
        }
        else if (type == cross::AABBTREETYPE::Delete)
        {
            if (!writer->mInstanceBVHAABBTree->IsExitElement(value))
            {
                return;
            }
            writer->mInstanceBVHAABBTree->logicRemoveObject(value);
        }
    }
}

void FoliageSystemG::FoliageInstanceUpdate(const FoliageComponentWriter& writer, const FoliageComponentInstanceData& instanceData, InstanceType type, std::vector<int> indexes)
{
    QUICK_SCOPED_CPU_TIMING("FoliageSystemG::FoliageInstanceUpdate");
    // foliage instance update
    if (writer->mDataContainer->mInstanceData.size() == 0 || indexes.size() == 0)
    {
        writer->mDataContainer->mInstanceData.clear();
        for (const auto& transform : instanceData.mInstanceData)
        {
            writer->mDataContainer->mInstanceData.push_back(InstanceDataVecContainer::InstanceData{Transform{transform.mTranslation, transform.mScale, transform.mRotation}, transform.mDelete});
        }
    }
    else
    {
        if (type == InstanceType::Insert)
        {
            for (int i = 0; i < indexes.size(); i++)
            {
                auto& transform = instanceData.mInstanceData[i];
                writer->mDataContainer->mInstanceData.push_back(InstanceDataVecContainer::InstanceData{Transform{transform.mTranslation, transform.mScale, transform.mRotation}, transform.mDelete});
                writer->mEditorInstanceData.mInstanceData.push_back(instanceData.mInstanceData[i]);
            }
        }
        else if (type == InstanceType::Delete)
        {
            for (int i = 0; i < indexes.size(); i++)
            {
                writer->mDataContainer->mInstanceData[indexes[i]].mDelete = true;
                writer->mEditorInstanceData.mInstanceData[i].mDelete = true;
            }
        }
    }
    RefreshRenderState(mGameWorld, writer.GetEntityID());
}

void FoliageSystemG::TraverseInstanceData(const FoliageComponentWriter& writer, std::function<void(InstanceDataVecContainer::InstanceData& insData)> func)
{
    for (auto& insData : writer->mDataContainer->mInstanceData)
    {
        func(insData);
    }
}

void FoliageSystemG::TraverseInstanceDataLight(const FoliageComponentWriter& writer, std::function<void(InstanceDataVecContainer::InstanceDataLight& insData)> func)
{
    for (auto& insData : writer->mDataContainer->mInstanceDataLight)
    {
        func(insData);
    }
}

bool FoliageSystemG::GetSubmeshVisible(const FoliageComponentReader& reader, UInt32 submeshIndex)
{
    if (submeshIndex < reader->mSubmeshProperty.size())
    {
        return reader->mSubmeshProperty[submeshIndex].visible;
    }
    return false;
}

void FoliageSystemG::SetSubmeshVisible(const FoliageComponentWriter& writer, UInt32 submeshIndex, bool visible)
{
    UInt32 validSubmeshIndex = submeshIndex;
    if (submeshIndex >= writer->mSubmeshProperty.size())
    {
        if (writer->mPrimaryMeshAsset)
        {
            auto meshAssetData = writer->mPrimaryMeshAsset->GetAssetData();
            UInt32 meshPartStartIndex, subSectionCount;
            meshAssetData->GetMeshLodInfo(0, meshPartStartIndex, subSectionCount);
            validSubmeshIndex = std::min(submeshIndex, subSectionCount - 1);
            writer->mSubmeshProperty.resize(subSectionCount);
            writer->mSubmeshProperty[validSubmeshIndex].visible = visible;
        }
    }
    else
    {
        writer->mSubmeshProperty[validSubmeshIndex].visible = visible;
    }
    RefreshRenderState(mGameWorld, writer.GetEntityID());
}

void FoliageSystemG::SetLightCastShadow(const FoliageComponentWriter& writer, bool castShadow)
{
    writer->mLightCastShadow = castShadow;
    RefreshRenderState(mGameWorld, writer.GetEntityID());
}

bool FoliageSystemG::GetLightCastShadow(const FoliageComponentReader& reader) const
{
    return reader->mLightCastShadow;
}

void FoliageSystemG::SetEditorPrefabResource(const FoliageComponentWriter& writer, const std::string& prefabPath)
{
    PrefabResourcePtr prefab = TypeCast<resource::PrefabResource>(gAssetStreamingManager->LoadSynchronously(prefabPath));
    if (!prefab)
    {
        return;
    }
    auto transformSystem = mGameWorld->GetGameSystem<TransformSystemG>();
    auto rootEntity = transformSystem->GetRootEntity();
    auto prefabSys = mGameWorld->GetGameSystem<PrefabSystemG>();
    auto lightSys = mGameWorld->GetGameSystem<LightSystemG>();
    auto metaSys = mGameWorld->GetGameSystem<EntityMetaSystem>();
    auto prefabEntity = prefabSys->CreatePrefabInstance(prefabPath, rootEntity);
    bool hasModel = false;
    ecs::EntityID modelCompEntity;
    writer->mLocalTransform.rotation = Quaternion::Identity();
    writer->mLocalTransform.translation = Float3::Zero();
    writer->mLocalTransform.scale = Float3::One();

    auto GenLightInstance = [&](Transform& transform, Float4x4& relMatrix, LightSystemG::LightComponentHandle lightComp) {
        auto lightReader = lightComp.Read();
        const auto localMatrix = Float4x4::Compose(transform.scale, transform.rotation, transform.translation);
        const auto matrix = relMatrix * localMatrix;
        Transform lightTransform;
        matrix.Decompose(lightTransform.scale, lightTransform.rotation, lightTransform.translation);

        InstanceDataVecContainer::InstanceDataLight instanceLight;
        switch (lightSys->GetLightType(lightReader))
        {
        case LightType::Point:
        {
            auto range = lightSys->GetLightRange(lightReader);
            auto range2 = range * range;
            instanceLight.mLightDirPos = Float4(lightTransform.translation, range);
            instanceLight.mLightTilePosition = Float4(0, 0, 0, 0);
            instanceLight.mLightAttenuation = Float4(-1.f, 1.f, 1.0f / range2, range2);
            instanceLight.mLightColor = Float4(lightSys->GetLightColor(lightReader) * lightSys->GetLightIntensity(lightReader), -1.0f);
            instanceLight.mTransform = transform;
            break;
        }
        case LightType::Spot:
        {
            auto range = lightSys->GetLightRange(lightReader);

            // NOTE: Inner/Outer cone angle is already the half angle of cone, no need to divide 2.f here, but since foliage system is not work now, haven't modified here
            auto spotInnerCosHalfAngle = cosf(lightSys->GetLightInnerConeAngleInDegree(lightReader) / 2.0f * M_PI / 180.0f);
            auto spotOuterCosHalfAngle = cosf(lightSys->GetLightOuterConeAngleInDegree(lightReader) / 2.0f * M_PI / 180.0f);
            auto fadeIntensity = lightSys->GetLightConeFadeIntensity(lightReader);
            auto overflowLength = lightSys->GetLightConeOverFlowLength(lightReader);
            auto spotDistanceExp = lightSys->GetLightSpotDistanceExp(lightReader);

            // auto spotRotQuat = transformSystem->GetWorldRotation(transformComponent.Read());
            auto spotRotEuler = QuaternionA::QuaternionToEuler(lightTransform.rotation);

            instanceLight.mLightDirPos = Float4(lightTransform.translation, range);
            instanceLight.mLightTilePosition = Float4(0, 0, 0, 0);
            instanceLight.mLightSpotDiretion = Float4(spotRotEuler, fadeIntensity);
            instanceLight.mLightAttenuation = Float4(spotInnerCosHalfAngle, spotInnerCosHalfAngle, spotOuterCosHalfAngle, spotOuterCosHalfAngle);
            instanceLight.mLightColor = Float4(lightSys->GetLightColor(lightReader) * lightSys->GetLightIntensity(lightReader), spotDistanceExp);
            instanceLight.mTransform = transform;

            break;
        }
        default:
            break;
        }
        return instanceLight;
    };

    if (mGameWorld->HasComponent<ModelComponentG>(prefabEntity))
    {
        modelCompEntity = prefabEntity;
        hasModel = true;
    }
    else
    {
        auto childCount = transformSystem->GetChildEntityNumber(prefabEntity);
        for (auto childIndex = 0; childIndex < childCount; ++childIndex)
        {
            auto childEntity = transformSystem->GetChildEntity(prefabEntity, childIndex);
            if (mGameWorld->HasComponent<ModelComponentG>(childEntity))
            {
                modelCompEntity = childEntity;
                hasModel = true;
                auto transformComponent = mGameWorld->GetComponent<LocalTransformComponentG>(childEntity);
                writer->mLocalTransform.rotation = transformSystem->GetLocalRotation(transformComponent.Read());
                writer->mLocalTransform.translation = transformSystem->GetLocalTranslation(transformComponent.Read());
                writer->mLocalTransform.scale = transformSystem->GetLocalScale(transformComponent.Read());
                break;
            }
        }
    }

    if (hasModel)
    {
        writer->mPrefabResource = prefabPath;
        writer->mPrefabResourceHolder = gAssetStreamingManager->LoadSynchronously(prefabPath);
        auto modelComp = mGameWorld->GetComponent<ModelComponentG>(modelCompEntity);
        assert(modelComp);
        auto modelSysG = mGameWorld->GetGameSystem<ModelSystemG>();
        auto modelCompReader = modelComp.Read();
        std::string modelPath, materialPath;
        auto meshAssetData = modelSysG->GetModelAsset(modelCompReader, 0)->GetAssetData();
        modelSysG->GetModelAssetPath(modelCompReader, 0, modelPath);
        materialPath = modelSysG->GetModelMaterialPath(modelCompReader, 0, 0, 0);

        writer->mPrimaryMeshAsset = TypeCast<resource::MeshAssetDataResource>(gAssetStreamingManager->LoadSynchronously(modelPath));
        writer->mPrimaryMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(materialPath));

        auto lodCount = meshAssetData->GetLodCount();
        writer->mLoDSections.clear();
        for (UInt8 lodIndex = 0; lodIndex < lodCount; ++lodIndex)
        {
            FoliageComponentG::LoDSection section;
            UInt32 meshPartStartIndex, subSectionCount;
            meshAssetData->GetMeshLodInfo(lodIndex, meshPartStartIndex, subSectionCount);
            section.mDefaultMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(materialPath));
            for (UInt32 submodelIndex = 0; submodelIndex < subSectionCount; ++submodelIndex)
            {
                auto lodMat = modelSysG->GetModelLodMaterial(modelCompReader, submodelIndex, lodIndex, 0);
                section.mSubSectionMaterials.push_back(lodMat);
            }
            writer->mLoDSections.push_back(section);
        }

        RefreshRenderState(mGameWorld, writer.GetEntityID());
    }

    // generate PCG light
    bool foundLight = false;
    int lightEntityCount = 0;
    auto childCount = transformSystem->GetChildEntityNumber(prefabEntity);
    for (auto childIndex = 0; childIndex < childCount; ++childIndex)
    {
        auto childEntity = transformSystem->GetChildEntity(prefabEntity, childIndex);
        if (mGameWorld->HasComponent<LightComponentG>(childEntity))
        {
            writer->mPrefabResource = prefabPath;
            writer->mPrefabResourceHolder = gAssetStreamingManager->LoadSynchronously(prefabPath);
            auto [lightComp, transformComponent] = mGameWorld->GetComponent<LightComponentG, LocalTransformComponentG>(childEntity);
            auto lightReader = lightComp.Read();

            auto pcgEntityID = writer.GetEntityID();
            if (hasModel)
            {
                pcgEntityID = mGameWorld->CreateEntity("Foliage");
                auto metaHandle = mGameWorld->GetComponent<ecs::EntityMetaComponentG>(pcgEntityID).Write();
                std::string entityName = "PCGLight";
                entityName += lightEntityCount > 0 ? std::to_string(lightEntityCount) : "";
                metaSys->SetEUID(metaHandle, CrossUUID::GenerateCrossUUID());
                metaSys->SetName(metaHandle, entityName);
                transformSystem->Joint(pcgEntityID, writer.GetEntityID(), false);
                transformSystem->UpdateWorldTransform(pcgEntityID);
            }
            ++lightEntityCount;
            auto foliageComp = mGameWorld->GetComponent<FoliageComponentG>(pcgEntityID);
            auto foliageWriter = foliageComp.Write();
            if (!foundLight)
            {
                foundLight = true;
                foliageWriter->mDataContainer->mInstanceDataLight.clear();
            }
            foliageWriter->mLightLocalTransform.rotation = transformSystem->GetLocalRotation(transformComponent.Read());
            foliageWriter->mLightLocalTransform.translation = transformSystem->GetLocalTranslation(transformComponent.Read());
            foliageWriter->mLightLocalTransform.scale = transformSystem->GetLocalScale(transformComponent.Read());
            auto relMatrix = Float4x4::Compose(foliageWriter->mLightLocalTransform.scale, foliageWriter->mLightLocalTransform.rotation, foliageWriter->mLightLocalTransform.translation);

            if (!hasModel && foliageWriter->mFoliageGenerationType == FoliageGenerationType::FOLIAGE_GENERATION_TYPE_LIGHT)
            {
                auto instanceCount = writer->mDataContainer->mInstanceDataLight.size();
                //foliageWriter->mDataContainer->mInstanceDataLight.resize(instanceCount);
                for (auto i = 0; i < instanceCount; ++i)
                {
                    auto& transform = writer->mDataContainer->mInstanceDataLight[i].mTransform;

                    foliageWriter->mDataContainer->mInstanceDataLight.push_back(GenLightInstance(transform, relMatrix, lightComp));
                }
            }
            else
            {
                auto instanceCount = writer->mDataContainer->mInstanceData.size();
                //foliageWriter->mDataContainer->mInstanceDataLight.resize(instanceCount);
                for (auto i = 0; i < instanceCount; ++i)
                {
                    auto& transform = writer->mDataContainer->mInstanceData[i].mTransform;

                    foliageWriter->mDataContainer->mInstanceDataLight.push_back(GenLightInstance(transform, relMatrix, lightComp));
                }
            }

            if (!hasModel)
            {
                writer->mLoDSections.clear();
                writer->mDataContainer->mInstanceData.clear();
                RefreshRenderState(mGameWorld, writer.GetEntityID());
            }
            foliageWriter->mFoliageGenerationType = FoliageGenerationType::FOLIAGE_GENERATION_TYPE_LIGHT;
            foliageWriter->mPrimaryMeshAsset = TypeCast<resource::MeshAssetDataResource>(gAssetStreamingManager->LoadSynchronously("EngineResource/Model/Cube.nda"));
            foliageWriter->mPrimaryMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously("Material/DefaultFoliage.nda"));
            foliageWriter->mDataContainer->PrepareLightInstanceData(foliageWriter->mGlobalScale, foliageWriter->mLightLocalTransform, foliageWriter->mGlobalRangeScale, foliageWriter->mLightCastShadow);
            foliageWriter->mInstanceDataReady = true;
            RefreshRenderState(mGameWorld, foliageWriter.GetEntityID());

            //break;
        }
    }

    // mGameWorld->DestroyEntityByHierarchy(prefabEntity);
    mDestroyList.push_back(prefabEntity);
    mDelayFrame = 0;
}

void FoliageSystemG::FoliageRefreshEvents(ecs::EntityID entity)
{
    auto [aabbComp, foliageComp] = mGameWorld->GetComponent<AABBComponentG, FoliageComponentG>(entity);

    foliageComp.Write()->mInstanceDataReady = true;
    BoundingBox localbounds = GetLocalBound(foliageComp.Write());
    mGameWorld->GetGameSystem<AABBSystemG>()->SetLocalAABB(aabbComp.Write(), localbounds);
    RefreshRenderState(mGameWorld, entity);
    InstanceDataChangedEvent e;
    e.mData.mEntity = entity;
    DispatchImmediateEvent(e);
}

void FoliageSystemG::TryAdjustSubmeshPropertySize(ecs::EntityID entity)
{
    auto foliageComponent = mGameWorld->GetComponent<FoliageComponentG>(entity);
    if (foliageComponent.IsValid())
    {
        auto writer = foliageComponent.Write();
        if (writer->mPrimaryMeshAsset)
        {
            auto meshAssetData = writer->mPrimaryMeshAsset->GetAssetData();
            UInt32 meshPartStartIndex, subSectionCount;
            meshAssetData->GetMeshLodInfo(0, meshPartStartIndex, subSectionCount);
            auto subPropCount = writer->mSubmeshProperty.size();
            if (subPropCount != subSectionCount)
            {
                writer->mSubmeshProperty.resize(subSectionCount);
            }
        }
    }
}
const std::vector<InstanceDataVecContainer::InstanceData>& FoliageSystemG::GetInstanceData(const FoliageComponentReader& reader) const
{
    return reader->mDataContainer->mInstanceData;
}
std::vector<InstanceDataVecContainer::InstanceDataLight>& FoliageSystemG::GetInstanceLightData(const FoliageComponentWriter& writer)
{
    return writer->mDataContainer->mInstanceDataLight;
}

//only uesd in editor to modify foliage light
void FoliageSystemG::RefreshFoliageLightInstance(const FoliageComponentWriter& writer) {
    writer->mDataContainer->PrepareLightInstanceData(writer->mGlobalScale, writer->mLightLocalTransform, writer->mGlobalRangeScale, writer->mLightCastShadow);
    RefreshRenderState(mGameWorld, writer.GetEntityID());
}
Transform FoliageSystemG::GetWorldTransform(const FoliageComponentReader& reader, size_t instanceIdx)
{
    auto transformSystem = mGameWorld->GetGameSystem<TransformSystemG>();
    auto transformComponent = mGameWorld->GetComponent<WorldTransformComponentG>(reader.GetEntityID());
    Transform localTransform;
    /*if (reader->mEditorInstanceData.mInstanceData.size() == 0)
    {*/
    localTransform = reader->mDataContainer->mInstanceData[instanceIdx].mTransform;
    //}
    /*else
    {
        const auto& editorTransform = reader->mEditorInstanceData.mInstanceData[instanceIdx];
        localTransform.translation = editorTransform.mTranslation;
        localTransform.rotation = editorTransform.mRotation;
        localTransform.scale = editorTransform.mScale;
    }*/
    const auto worldScale = transformSystem->GetWorldScale(transformComponent.Read());
    const auto worldRotation = transformSystem->GetWorldRotation(transformComponent.Read());
    const auto worldMatrix = transformSystem->GetWorldMatrix(transformComponent.Read());

    Transform transform;
    transform.translation = cross::Float4x4::TransformPointF3(worldMatrix, localTransform.translation);
    transform.rotation = localTransform.rotation * worldRotation;
    transform.scale = reader->mGlobalScale * localTransform.scale * worldScale;

    return transform;
}

Transform FoliageSystemG::GetRelativeWorldTransform(const FoliageComponentReader& reader, size_t instanceIdx, Float3 BaseTilePosition)
{
    QUICK_SCOPED_CPU_TIMING("FoliageSystemG::GetRelativeWorldTransform");
#ifdef CE_USE_DOUBLE_TRANSFORM
    auto transformSystem = mGameWorld->GetGameSystem<TransformSystemG>();
    auto transformComponent = mGameWorld->GetComponent<WorldTransformComponentG>(reader.GetEntityID());
    const auto& localTransform = reader->mDataContainer->mInstanceData[instanceIdx].mTransform;
    const auto worldScale = transformSystem->GetWorldScale(transformComponent.Read());
    const auto worldRotation = transformSystem->GetWorldRotation(transformComponent.Read());
    const auto worldMatrix = transformSystem->GetWorldMatrixT(transformComponent.Read());
    Float4x4 relativeWorldMatrix = AbsoluteMatrixToRelativeMatrix(BaseTilePosition, worldMatrix);

    Transform transform;
    transform.translation = cross::Float4x4::TransformPointF3(relativeWorldMatrix, localTransform.translation);
    transform.rotation = localTransform.rotation * worldRotation;
    transform.scale = reader->mGlobalScale * localTransform.scale * worldScale;

    return transform;
#else
    return GetWorldTransform(reader, instanceIdx - count);
#endif
}

BoundingBox FoliageSystemG::GetWorldBound(const FoliageComponentReader& reader, const BoundingBox& localBound) const
{
    auto transformSystem = mGameWorld->GetGameSystem<TransformSystemG>();
    auto transformComponent = mGameWorld->GetComponent<WorldTransformComponentG>(reader.GetEntityID());

    const auto worldScale = transformSystem->GetWorldScale(transformComponent.Read());
    const auto worldRotation = transformSystem->GetWorldRotation(transformComponent.Read());
    const auto worldTranslation = transformSystem->GetWorldTranslation(transformComponent.Read());

    BoundingBox worldBound;
    localBound.Transform(worldBound, Float4x4::Compose(worldScale, worldRotation, worldTranslation));

    return worldBound;
}

BoundingBox FoliageSystemG::UpdateLocalBound(FoliageComponentG* writer) const
{
    BoundingBox boundingBox{BoundingBox::Flags::MergeIdentity};
    if (writer->mPrimaryMeshAsset && writer->mInstanceDataReady)
    {
        const BoundingBox meshBoundingBox{writer->mPrimaryMeshAsset->GetAssetData()->GetBoundingBox()};

        UInt32 instanceCount = static_cast<UInt32>(writer->mDataContainer->mInstanceData.size());
        const auto numWorkers = threading::TaskSystem::GetNumWorkerThreadsForTask();
        UInt32 batchSize = instanceCount / static_cast<UInt32>(numWorkers) + 1;
        std::vector<BoundingBox> boundingBoxes;
        boundingBoxes.resize(numWorkers, boundingBox);
        cross::threading::ParallelFor(static_cast<SInt32>(numWorkers), [&](auto taskIndex) {
            for (UInt32 i = 0; i < batchSize; ++i)
            {
                UInt32 index = taskIndex * batchSize + i;
                if (index < instanceCount)
                {
                    const auto& data = writer->mDataContainer->mInstanceData[index];
                    BoundingBox worldBound;
                    const auto& relativeMatrix = Float4x4::Compose(writer->mLocalTransform.scale, writer->mLocalTransform.rotation, writer->mLocalTransform.translation);
                    const auto localMatrix = relativeMatrix * Float4x4::Compose(data.mTransform.scale * writer->mGlobalScale, data.mTransform.rotation, data.mTransform.translation);
                    meshBoundingBox.Transform(worldBound, localMatrix);
                    BoundingBox::CreateMerged(boundingBoxes[taskIndex], boundingBoxes[taskIndex], worldBound);
                }
            }
        });

        for (int i = 0; i < numWorkers; ++i)
        {
            BoundingBox::CreateMerged(boundingBox, boundingBox, boundingBoxes[i]);
        }
    }
    writer->mLocalBoundingBox = boundingBox;

    return boundingBox;
}

BoundingBox FoliageSystemG::GetLocalBound(const FoliageComponentWriter& writer)
{
    if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeStandAlone)
    {
        return writer->mLocalBoundingBox;
    }
    return UpdateLocalBound(writer.operator->());
}

void FoliageSystemG::GenerateFoliageAABBTree(const FoliageComponentWriter& writer)
{
    auto foliageComponent = mGameWorld->GetComponent<FoliageComponentG>(writer.GetEntityID());
    auto reader = foliageComponent.Read();

    const auto meshBound = reader->mPrimaryMeshAsset->GetAssetData()->GetBoundingBox();
    writer->mInstanceBVHAABBTree = std::make_unique<AABBTree<UInt32>>(static_cast<UInt32>(writer->mDataContainer->mInstanceData.size() == 0 ? AABBTREECOUNT : writer->mDataContainer->mInstanceData.size() * AABBTREECOUNT));
    std::vector<UInt32> elements;
    for (UInt32 index = 0; index < reader->mDataContainer->mInstanceData.size(); ++index)
    {
        const auto worldTransform = GetWorldTransform(reader, static_cast<size_t>(index));
        Float3 translation = worldTransform.translation;
        auto worldMatrix = Float4x4::Compose(worldTransform.scale, worldTransform.rotation, worldTransform.translation);
        BoundingBox worldMeshBound;
        meshBound.Transform(worldMeshBound, worldMatrix);

        elements.emplace_back(index);

        writer->mInstanceBVHAABBTree->insertObject(index, worldMeshBound);
    }
}

void FoliageSystemG::UpdateFoliageAllAABB(const FoliageComponentWriter& writer, Float4x4A AbsoluteWorldInverseMatrix)
{
    if (writer->mInstanceBVHAABBTree.get() != nullptr)
    {
        updateObjectAABB(writer->mInstanceBVHAABBTree.get(), writer.GetEntityID(), AbsoluteWorldInverseMatrix);
    }
}

void FoliageSystemG::updateObjectAABB(AABBTree<UInt32>* aabbTree, cross::ecs::EntityID entityID, Float4x4A AbsoluteWorldInverseMatrix)
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
    auto transformSys = mGameWorld->GetGameSystem<TransformSystemG>();
    auto worldTransformComponentHandle = mGameWorld->GetComponent<cross::WorldTransformComponentG>(entityID);
    Float4x4A AbsoluteWorldMatrix = static_cast<Float4x4A>(transformSys->GetWorldMatrixT(worldTransformComponentHandle.Read()));

    UInt32 index = 0;
    for (auto& node : aabbTree->GetAllNodes())
    {
        if (index > aabbTree->GetAllocateNodeCount())
            break;

        cross::BoundingBox transformedInverseBox, transformedBox;
        node.aabb.Transform(transformedInverseBox, AbsoluteWorldInverseMatrix);
        transformedInverseBox.Transform(transformedBox, AbsoluteWorldMatrix);
        node.aabb = transformedBox;
        ++index;
    }
}

AABBTree<UInt32>* FoliageSystemG::GetInstanceBVHAABBTree(const FoliageComponentReader& reader) const
{
    return reader->mInstanceBVHAABBTree.get();
}
void FoliageSystemG::SetIntersection(const FoliageComponentWriter& writer, bool enable)
{
    writer->mIntersection = enable;
}

bool FoliageSystemG::GetIntersection(const FoliageComponentReader& reader) const
{
    return reader->mIntersection;
}

void FoliageSystemG::SetEnable(const FoliageComponentWriter& writer, bool enable)
{
    writer->mEnable = enable;
    auto entityID = writer.GetEntityID();
    auto renderWorld = mGameWorld->GetRenderWorld();
    if (writer->mInitialized && writer->mInstanceDataReady)
    {
        DispatchRenderingCommandWithToken([foliageSystemR = mFoliageSystemR, entityID, enable, renderWorld] {
            foliageSystemR->SetEnable(entityID, enable);
            foliageSystemR->SetDirty(entityID);
        });
    }
}
void FoliageSystemG::SetFoliageLodMaterial(const FoliageComponentWriter& writer, MaterialInterfacePtr ptr, int subMeshIndex, int lodIndex)
{
    writer->mPrimaryMaterial = ptr;
    writer->mLoDSections[lodIndex].mDefaultMaterial = ptr;
    writer->mLoDSections[lodIndex].mSubSectionMaterials[subMeshIndex] = ptr;
    RefreshRenderState(mGameWorld, writer.GetEntityID());
    /*auto entityID = writer.GetEntityID();
    auto renderWorld = mGameWorld->GetRenderWorld();
    DispatchRenderingCommandWithToken([foliageSystemR = mFoliageSystemR, entityID, material = TYPE_CAST(MaterialR*, ptr->GetRenderMaterial()), subMeshIndex, lodIndex, renderWorld] {
            Assert(material);
            foliageSystemR->SetFoliageLodMaterial(entityID, material, subMeshIndex, lodIndex);
    });*/
}

bool FoliageSystemG::GetEnable(const FoliageComponentReader& reader) const
{
    return reader->mEnable;
}

void FoliageSystemG::SetGlobalScale(const FoliageComponentWriter& writer, float globalScale)
{
    writer->mGlobalScale = globalScale;
    RefreshRenderState(mGameWorld, writer.GetEntityID());
}

float FoliageSystemG::GetGlobalScale(const FoliageComponentReader& reader) const
{
    return reader->mGlobalScale;
}

void FoliageSystemG::SetGlobalRangeScale(const FoliageComponentWriter& writer, float globalScale)
{
    writer->mGlobalRangeScale = globalScale;
    RefreshRenderState(mGameWorld, writer.GetEntityID());
}

float FoliageSystemG::GetGlobalRangeScale(const FoliageComponentReader& reader) const
{
    return reader->mGlobalRangeScale;
}

void FoliageSystemG::SetFoliageGenerationType(const FoliageComponentWriter& writer, FoliageGenerationType generationType)
{
    writer->mFoliageGenerationType = generationType;
    RefreshRenderState(mGameWorld, writer.GetEntityID());
}

void FoliageSystemG::SetMaxRandomCulling(const FoliageComponentWriter& writer, float maxRandomculling) 
{
    writer->mMaxRandomCulling = maxRandomculling;
    RefreshRenderState(mGameWorld, writer.GetEntityID());
}

float FoliageSystemG::GetMaxRandomCulling(const FoliageComponentReader& reader) const
{
    return reader->mMaxRandomCulling;
}

void FoliageSystemG::SetDensity(const FoliageComponentWriter& writer, float density)
{
    writer->mDensity = density;
    RefreshRenderState(mGameWorld, writer.GetEntityID());
}

float FoliageSystemG::GetDensity(const FoliageComponentReader& reader) const
{
    return reader->mDensity;
}

FoliageGenerationType FoliageSystemG::GetFoliageGenerationType(const FoliageComponentReader& reader) const
{
    return reader->mFoliageGenerationType;
}

void FoliageSystemG::SetPCGReservedCapacity(const FoliageComponentWriter& writer, UInt32 count)
{
    writer->mPCGReservedCapacity = count;
    RefreshRenderState(mGameWorld, writer.GetEntityID());
}

UInt32 FoliageSystemG::GetPCGReservedCapacity(const FoliageComponentReader& reader) const
{
    return reader->mPCGReservedCapacity;
}

float FoliageSystemG::GetScreenSizeScale(const FoliageComponentReader& reader, const BoundingBox& worldBound, const Float4x4& worldMatrix) const
{
    auto screenSizeScale = 0.f;
    // const BoundingBox meshBoundingBox{ reader->mPrimaryMeshAsset->GetAssetData()->GetBoundingBox() };
    // for (const auto& instanceData : reader->mInstanceData)
    //{
    //    BoundingBox meshWorldBound;
    //    meshBoundingBox.Transform(meshWorldBound, Float4x4::Compose(instanceData.mTransform.scale, instanceData.mTransform.rotation, instanceData.mTransform.translation) * worldMatrix);
    //    //Assert(worldBound.Contains(meshWorldBound));

    //    Float3 worldBoundExtent{};
    //    worldBound.GetExtent(&worldBoundExtent);
    //    Float3 meshBoundExtent{};
    //    meshWorldBound.GetExtent(&meshBoundExtent);
    //    const auto extentFactor = meshBoundExtent / worldBoundExtent;
    //    screenSizeScale = std::max(screenSizeScale, std::max(extentFactor.x, std::max(extentFactor.y, extentFactor.z)));
    //}

    Assert(screenSizeScale <= 1.01f);
    return screenSizeScale;
}
Float4 GetLODColor(UInt32 MeshLod)
{
    Float4 colors[7] = {{1.0f, 0.0f, 0.0f, 0.0f}, {0.5f, 0.5f, 0.0f, 0.0f}, {0.0, 1.0f, 0.0f, 0.0f}, {0.0f, 0.5f, 0.5f, 0.0f}, {0.0, 0.0f, 1.0f, 0.0f}, {0.5, 0.0f, 1.0f, 0.0f}, {1.0, 0.0f, 0.5f, 0.0f}};

    return colors[std::min<UInt32>(MeshLod, 6)];
}

void FoliageSystemG::RefreshAllFoliageEntities() 
{
    auto allentities = mGameWorld->Query<FoliageComponentG>();
    for (auto ent : allentities)
    {
        if (mGameWorld->IsEntityAlive(ent.GetEntityID()))
        {
            RefreshRenderState(mGameWorld, ent.GetEntityID());
        }
    }
}

Float4x4 FoliageSystemG::GetFinalMatrix(UInt32 index, std::vector<InstanceDataVecContainer::InstanceData>& instanceData, float density, float globalScale)
{
    Rand rand(index);
    float randomValue = rand.GetFloat();
    const auto& transform = instanceData[index].mTransform;
    Float4x4 localMatrix = Float4x4::Zeros();
    if (!instanceData[index].mDelete && randomValue <= density)
        localMatrix = Float4x4::Compose(transform.scale * globalScale, transform.rotation, transform.translation);
    return localMatrix;
}

void FoliageSystemG::RefreshRenderState(GameWorld* gameWorld, ecs::EntityID entityID, bool updateInstanceData)
{
    QUICK_SCOPED_CPU_TIMING("FoliageSystemG::RefreshRenderState");
    auto aabbSystem = gameWorld->GetGameSystem<AABBSystemG>();
    auto foliageSystem = gameWorld->GetGameSystem<FoliageSystemG>();
    auto transformSystem = gameWorld->GetGameSystem<TransformSystemG>();
    auto aabbComponent = gameWorld->GetComponent<AABBComponentG>(entityID);
    auto transformComponent = gameWorld->GetComponent<WorldTransformComponentG>(entityID);
    auto foliageComponent = gameWorld->GetComponent<FoliageComponentG>(entityID);
    if (foliageComponent.Write()->mInstanceDataReady == false)
        return;
    BoundingBox localBound{BoundingBox::Flags::MergeIdentity};
    // const auto localBound = foliageSystem->GetLocalBound(foliageComponent.Read());
    // aabbSystem->SetLocalAABB(aabbComponent.Write(), localBound);

    std::vector<FoliageComponentR::LoDSection> sections;
    std::vector<FoliageComponentR::SubmeshProperty> submeshProperties;
    {
        auto reader = foliageComponent.Read();
        UInt32 LodIndex = 0;
        sections.reserve(reader->mLoDSections.size());
        for (const auto& section : reader->mLoDSections)
        {
            FoliageComponentR::LoDSection sectionR{reader->mPrimaryMeshAsset, TYPE_CAST(MaterialR*, section.mDefaultMaterial->GetRenderMaterial())};
            sectionR.mSubSectionMaterials.reserve(section.mSubSectionMaterials.size());
            for (auto material : section.mSubSectionMaterials)
            {
                sectionR.mSubSectionMaterials.push_back(TYPE_CAST(MaterialR*, material->GetRenderMaterial()));
            }
            sections.push_back(sectionR);
            LodIndex++;
        }

        for (const auto& prop : reader->mSubmeshProperty)
        {
            FoliageComponentR::SubmeshProperty submeshProp;
            submeshProp.visible = prop.visible;
            submeshProperties.push_back(submeshProp);
        }
    }

    const auto& worldMatrix = transformSystem->GetWorldMatrix(transformComponent.Read());
    auto reader = foliageComponent.Read();

    //// light instance data
    //std::vector<LightInstanceData> instanceDataPointLight;
    //std::vector<LightInstanceData> instanceDataSpotLight;
    //for (UInt32 i = 0; i < reader->mInstanceDataLight.size(); i++)
    //{
    //    auto& lightData = reader->mInstanceDataLight[i];
    //    LightInstanceData data;
    //    float range = lightData.mLightDirPos.w * reader->mGlobalRangeScale;
    //    float range2 = range * range;
    //    if (lightData.mLightColor.w < 0)
    //    {
    //        data.singleLightAttenuation = Float4(lightData.mLightAttenuation.x, lightData.mLightAttenuation.y, 1.0f / range2, range2);
    //    }
    //    else
    //    {
    //        data.singleLightAttenuation = lightData.mLightAttenuation;
    //    }
    //    data.singleLightColor = Float4(lightData.mLightColor.XYZ() * reader->mGlobalScale, lightData.mLightColor.w);
    //    data.singleLightDirPos = Float4(lightData.mLightDirPos.XYZ(), range);
    //    data.singleLightSpotDirection = lightData.mLightSpotDiretion;
    //    data.singleLightTilePosition = lightData.mLightTilePosition;
    //    const auto& transform = lightData.mTransform;
    //    auto localMatrix = Float4x4::Compose(reader->mLightLocalTransform.translation, reader->mLightLocalTransform.rotation, reader->mLightLocalTransform.scale);
    //    const auto lightWorldMatrix = localMatrix * Float4x4::Compose(range * transform.scale * reader->mGlobalScale, transform.rotation, transform.translation);
    //    data.world = lightWorldMatrix;
    //    data.flags |= reader->mLightCastShadow ? LIGHT_INSTANCE_FLAG_CAST_SHADOW : 0;
    //    if (lightData.mLightColor.w < 0)
    //    {
    //        instanceDataPointLight.push_back(data);
    //    }
    //    else
    //    {
    //        instanceDataSpotLight.push_back(data);
    //    }
    //}
    const auto& worldBound = foliageSystem->GetWorldBound(foliageComponent.Read(), localBound);
    const auto screenSizeScale = foliageSystem->GetScreenSizeScale(foliageComponent.Read(), worldBound, worldMatrix);
    const auto maxRandomCulling = foliageSystem->GetMaxRandomCulling(foliageComponent.Read());
    const auto density = foliageSystem->GetDensity(foliageComponent.Read());
    const auto generationType = foliageSystem->GetFoliageGenerationType(foliageComponent.Read());
    const auto enable = foliageSystem->GetEnable(foliageComponent.Read());
    DispatchRenderingCommandWithToken([foliageSystemR = foliageSystem->mFoliageSystemR,
                                           entityID,
                                           instanceCount = foliageComponent.Read()->mDataContainer->mInstanceData.size(),
                                           sectionsCopy = sections,
                                           submeshPropertiesCopy = submeshProperties,
                                           localRotation = reader->mLocalTransform.rotation,
                                           localTranslation = reader->mLocalTransform.translation,
                                           localScale = reader->mLocalTransform.scale,
                                           globalScale = reader->mGlobalScale,
                                           containerPtr = std::move(reader->mDataContainer),
                                           worldBound,
                                           screenSizeScale,
                                           generationType,
                                           maxRandomCulling,
                                           density,
                                           mPCGReservedCapacity = reader->mPCGReservedCapacity,
                                           worldMatrix,
                                           enable, updateInstanceData] {
           

            UInt32 count = static_cast<UInt32>(instanceCount);
            std::vector<FoliageInstanceCompactData>& instanceData = foliageSystemR->GetLocalInstanceData(entityID);

            if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpType::AppStartUpTypeStandAlone)
            {
                instanceData.resize(count);
                auto instanceDataPtr = reinterpret_cast<unsigned char *>(instanceData.data());
                const auto numWorkers = threading::TaskSystem::GetNumWorkerThreadsForTask();
                UInt32 batchSize = count / static_cast<UInt32>(numWorkers) + 1;
                cross::threading::ParallelFor(static_cast<SInt32>(numWorkers), [&](auto taskIndex) {
                    for (UInt32 i = 0; i < batchSize; ++i)
                    {
                        UInt32 index = taskIndex * batchSize + i;
                        if (index < count)
                        {
                            auto localMatrix = GetFinalMatrix(index, containerPtr->mInstanceData, density, globalScale);
                            FoliageInstanceCompactData foliageInstanceCompactData;
                            foliageInstanceCompactData.entityIndex = 0;
                            EncodeTransform(localMatrix, foliageInstanceCompactData.rotationScale, foliageInstanceCompactData.translation);
                            std::memcpy(instanceDataPtr + index * sizeof(FoliageInstanceCompactData), &foliageInstanceCompactData, sizeof(FoliageInstanceCompactData));
                        }
                    }
                });
            }
            else
            {
                LOG_INFO("Entity {} Before Swap {} {} ll {} {}",
                         entityID.GetValue(),
                         instanceData.size(),
                         static_cast<void*>(instanceData.data()),
                         containerPtr->mTransformVector.size(),
                         static_cast<void*>(containerPtr->mTransformVector.data()));
                if (updateInstanceData && instanceData.size() <= containerPtr->mTransformVector.size())
                {
                    instanceData.swap(containerPtr->mTransformVector);
                }
                LOG_INFO("After Swap {} {} ll {} {}", instanceData.size(), static_cast<void*>(instanceData.data()), containerPtr->mTransformVector.size(), static_cast<void*>(containerPtr->mTransformVector.data()));

            }


            foliageSystemR->SetFoliageGenerationType(entityID, generationType);
            foliageSystemR->SetLoDSections(entityID, sectionsCopy);
            foliageSystemR->SetSubmeshPropertys(entityID, submeshPropertiesCopy);
            foliageSystemR->SetInstanceDataCount(entityID, instanceCount);
            foliageSystemR->SetInstanceDataLight(entityID, containerPtr->mInstanceDataPointLight, containerPtr->mInstanceDataSpotLight);
            // foliageSystemR->UpdateInstanceData(entityID);
            foliageSystemR->SetWorldMatrix(entityID, worldMatrix);
            foliageSystemR->SetWorldBound(entityID, worldBound);
            foliageSystemR->SetScreenSizeScale(entityID, screenSizeScale);
            foliageSystemR->SetMaxRandomCulling(entityID, maxRandomCulling);
            foliageSystemR->SetPCGReservedCapacity(entityID, mPCGReservedCapacity);
            foliageSystemR->SetEnable(entityID, enable);
            foliageSystemR->SetDirty(entityID);
    });
    foliageComponent.Write()->mInitialized = true;
    foliageSystem->GetChangeList().EmplaceChangeData(entityID);
}

FoliageSystemG::FoliageSystemG()
    : mFoliageSystemR(new FoliageSystemR)
{}

FoliageSystemG::~FoliageSystemG()
{
    auto* settingMgr = EngineGlobal::GetSettingMgr();
    settingMgr->Unsubscribe<RenderPipelineSettingEvent>(this);
    mGameWorld->Unsubscribe<EntityDestroyEvent>(this);
    if (mIsRenderObjectOwner)
    {
        mFoliageSystemR->Release();
    }
    mFoliageSystemR = nullptr;
}

void FoliageSystemG::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    GameSystemBase::NotifyEvent(event, flag);

    if (event.mEventType == TRSChangedEvent::sEventType)
    {
        const TRSChangedEvent& changeEvent = TYPE_CAST(const TRSChangedEvent&, event);
        ecs::EntityID entityID = changeEvent.mData.mEntity;
        // do not update instance buffer during standalone
        RefreshRenderState(mGameWorld, entityID);
    }
    else if (event.mEventType == RenderPipelineSettingEvent::sEventType)
    {
        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeCrossEditor)
        {
            auto AllFoliages = mGameWorld->Query<FoliageComponentG>(); 
            std::scoped_lock lock(mLoadMutex);
            for (const auto& Ent : AllFoliages)
            {
                RefreshRenderState(mGameWorld, Ent.GetEntityID());
            }
        }

        DispatchRenderingCommandWithToken([foliageSystemR = mFoliageSystemR] { foliageSystemR->OnRenderPipelineSettingChange(); });
    }
#if USE_COROUTINE == 0
    else if (event.mEventType == RemainedEventUpdatedEvent::sEventType)
    {
        const RemainedEventUpdatedEvent& e = static_cast<const RemainedEventUpdatedEvent&>(event);
        for (UInt32 eventIndex = e.mData.mFirstIndex; eventIndex <= e.mData.mLastIndex; ++eventIndex)
        {
            const EntityDestroyEvent& entityLifeCycleEvent = mGameWorld->GetRemainedEvent<EntityDestroyEvent>(eventIndex);
            for (auto it = mLoadingList.begin(); it != mLoadingList.end();)
            {
                std::scoped_lock lock(mLoadMutex);
                if (it->first == entityLifeCycleEvent.mData.mEntityID)
                {
                    it = mLoadingList.erase(it);
                }
                else
                {
                    it++;
                }
            }     
        }
    }
#endif
}


void FoliageSystemG::ReadFoliageInstanceData(const SerializeNode& componentJson, std::shared_ptr<InstanceDataVecContainer> dataContainer, BinaryResourcePtr instanceResource, BinaryResourcePtr instancelightResource,
                                             FoliageComponentG* foliageComponent, ecs::EntityID entityID)
{
    if (mGameWorld->IsEntityAlive(entityID) && dataContainer)
    {
        QUICK_SCOPED_CPU_TIMING("ReadFoliageInstanceData");
        dataContainer->mInstanceData.clear();
        dataContainer->mInstanceDataLight.clear();

        if (instanceResource)
        {
            auto& decoded = instanceResource->mData;
            UInt32 stride = sizeof(Transform);
            auto instanceCount = decoded.size() / stride;
            dataContainer->AllocInstanceData(instanceCount);
            {
                //auto meta = mGameWorld->GetComponent<cross::ecs::EntityMetaComponentG>(entityID);
                //LOG_WARN("foliage {} instance count = {}", mGameWorld->GetGameSystem<EntityMetaSystem>()->GetName(meta.Read()), instanceCount);
                //LOG_WARN("instance data address = {}", (UInt64)&(instanceResource->mData));
            }
            for (UInt32 i = 0; i < instanceCount; ++i)
            {
                InstanceDataVecContainer::InstanceData instance;
                memcpy(&instance.mTransform, decoded.data() + i * stride, stride);
                dataContainer->mInstanceData[i] = instance;
            }

            // The code below may cause unexpected 0 instance count, when two foliage component use the same instance resource

            //if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeStandAlone)
            //{
            //    instanceResource->ClearData(); 
            //}
        }
        else if (componentJson.HasMember("mInstanceDataBase64") && !componentJson["mInstanceDataBase64"].IsNull())
        {
            std::string base64_desc = componentJson["mInstanceDataBase64"].AsString();
            std::vector<uint8_t> decoded = Base64Codec::decode(base64_desc.c_str(), base64_desc.size());
            UInt32 stride = sizeof(Transform);
            auto instanceCount = decoded.size() / stride;
            dataContainer->AllocInstanceData(instanceCount);
            for (UInt32 i = 0; i < instanceCount; ++i)
            {
                InstanceDataVecContainer::InstanceData instance;
                memcpy(&instance.mTransform, decoded.data() + i * stride, stride);
                dataContainer->mInstanceData[i] = instance;
            }
        }
        else
        {
            if (componentJson.HasMember("mInstanceData") && !componentJson["mInstanceData"].IsNull())
            {
                if (const auto count = componentJson["mInstanceData"].Size())
                {
                    {
                        const auto instanceJson = componentJson["mInstanceData"][0];
                        auto transformJson = instanceJson["mTransform"];
                        InstanceDataVecContainer::InstanceData instance;
                        instance.mTransform.translation.Deserialize(transformJson["mTranslation"]);
                        instance.mTransform.scale.Deserialize(transformJson["mScale"]);
                        instance.mTransform.rotation.Deserialize(transformJson["mRotation"]);
                        dataContainer->mInstanceData.push_back(instance);
                    }

                    std::mt19937 rng(0);
                    std::uniform_real_distribution<float> dist;
                    const auto loadFactor = EngineGlobal::GetSettingMgr()->GetFoliageInstanceLoadFactor();
                    dataContainer->mInstanceData.reserve(count);
                    for (auto i = 1; i != count; i++)
                    {
                        if (loadFactor == 1.f || dist(rng) <= loadFactor)
                        {
                            const auto instanceJson = componentJson["mInstanceData"][i];
                            auto transformJson = instanceJson["mTransform"];
                            InstanceDataVecContainer::InstanceData instance;
                            instance.mTransform.translation.Deserialize(transformJson["mTranslation"]);
                            instance.mTransform.scale.Deserialize(transformJson["mScale"]);
                            instance.mTransform.rotation.Deserialize(transformJson["mRotation"]);
                            dataContainer->mInstanceData.push_back(instance);
                        }
                    }
                }
            }
        }

        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpType::AppStartUpTypeStandAlone)
        {
            dataContainer->AllocTransformVector(dataContainer->mInstanceData.size());
            for (int i = 0; i < dataContainer->mInstanceData.size(); i++)
            {
                FoliageInstanceCompactData foliageInstanceCompactData;
                foliageInstanceCompactData.entityIndex = 0;
                auto localMatrix = GetFinalMatrix(i, dataContainer->mInstanceData, foliageComponent->mDensity, foliageComponent->mGlobalScale);
                EncodeTransform(localMatrix, foliageInstanceCompactData.rotationScale, foliageInstanceCompactData.translation);
                dataContainer->mTransformVector[i] = foliageInstanceCompactData;
            }

            UpdateLocalBound(foliageComponent);
        } 

        if (instancelightResource)
        {
            auto& decoded = instancelightResource->mData;
            UInt32 stride = sizeof(InstanceDataVecContainer::InstanceDataLight);
            auto instanceCount = decoded.size() / stride;
            dataContainer->AllocInstanceLightData(instanceCount);
            for (UInt32 i = 0; i < instanceCount; ++i)
            {
                InstanceDataVecContainer::InstanceDataLight instance;
                memcpy(&instance, decoded.data() + i * stride, stride);
                dataContainer->mInstanceDataLight[i] = instance;
            }
        }
        else if (componentJson.HasMember("mInstanceDataLightBase64") && !componentJson["mInstanceDataLightBase64"].IsNull())
        {
            std::string base64_desc = componentJson["mInstanceDataLightBase64"].AsString();
            std::vector<uint8_t> decoded = Base64Codec::decode(base64_desc.c_str(), base64_desc.size());
            UInt32 stride = sizeof(InstanceDataVecContainer::InstanceDataLight);
            auto instanceCount = decoded.size() / stride;
            dataContainer->AllocInstanceLightData(instanceCount);
            for (UInt32 i = 0; i < instanceCount; ++i)
            {
                InstanceDataVecContainer::InstanceDataLight instance;
                memcpy(&instance, decoded.data() + i * stride, stride);
                dataContainer->mInstanceDataLight[i] = instance;
            }
        }
        else
        {
            if (componentJson.HasMember("mInstanceDataLight") && !componentJson["mInstanceDataLight"].IsNull())
            {
                if (const auto count = componentJson["mInstanceDataLight"].Size())
                {
                    for (auto i = 0; i < count; i++)
                    {
                        const auto instanceJson = componentJson["mInstanceDataLight"][i];
                        auto transformJson = instanceJson["mTransform"];
                        InstanceDataVecContainer::InstanceDataLight instance;
                        instance.mTransform.translation.Deserialize(transformJson["mTranslation"]);
                        instance.mTransform.scale.Deserialize(transformJson["mScale"]);
                        instance.mTransform.rotation.Deserialize(transformJson["mRotation"]);
                        instance.mLightDirPos.Deserialize(instanceJson["mLightDirPos"]);
                        instance.mLightTilePosition.Deserialize(instanceJson["mLightTilePosition"]);
                        instance.mLightAttenuation.Deserialize(instanceJson["mLightAttenuation"]);
                        instance.mLightColor.Deserialize(instanceJson["mLightColor"]);
                        instance.mLightSpotDiretion.Deserialize(instanceJson["mLightSpotDiretion"]);
                        dataContainer->mInstanceDataLight.push_back(instance);
                    }
                }
            }
        }
        dataContainer->PrepareLightInstanceData(foliageComponent->mGlobalScale, foliageComponent->mLightLocalTransform, foliageComponent->mGlobalRangeScale, foliageComponent->mLightCastShadow);
    }
}

void FoliageSystemG::FoliageBVHGenerate(const FoliageComponentWriter& writer)
{
    SetGenerateBVH(writer, true);
    GenerateFoliageAABBTree(writer);
}
}   // namespace cross
