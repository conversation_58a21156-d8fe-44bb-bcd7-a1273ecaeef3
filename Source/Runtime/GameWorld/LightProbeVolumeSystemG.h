#pragma once
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/Framework/Types.h"
#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/GameSystemBase.h"
#include "CECommon/Common/FrameContainer.h"
#include "RenderEngine/LightProbeCache.h"
#include "RenderEngine/LightProbeVolumeSystemR.h"

namespace cross {
class LightProbeVolumeSystemR;

struct LightProbeVolumeComponentG : ecs::IComponent
{
    CEComponentInternal(SystemType = LightProbeVolumeSystemG)

    CEFunction(Reflect)
    ENGINE_API static ecs::ComponentDesc* GetDesc();

public:
    enum CEMeta(Editor) VolumeSizeType
    {
        AUTO_INCLUDE_LIGHT_PROBE_ENTITY = 0,
        AUTO_INCLUDE_BAKE_ENTITY,
        MANUAL
    };
    CEMeta(Serialize, Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Brick Size"))
    Float3 mBrickSize{4, 4, 4};

    CEMeta(<PERSON>ial<PERSON>, Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Brick Size", bReadOnly = true)) 
    std::string mCacheFile;

    CEMeta(Serialize) 
    std::unique_ptr<VolumetricLightmapData> mVLMData{nullptr};

    CEMeta(Serialize, Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Use VolumetricLightMap")) 
    bool mUseVolumetricLightMap{false};

    CEMeta(Serialize, Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "LightProbeVolume Size Type"))
    VolumeSizeType mBoundingBoxType;

    CEMeta(Serialize, Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "VLMReflectionProbeIntensity")) 
    float mVLMReflectionProbeIntensity{0.0f};

    CEMeta(Serialize, Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "VLMReflectionProbeAOIntensity"))
    float mVLMReflectionProbeAOIntensity{0.0f};

    CEMeta(Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Debug Show")) 
    bool mDebugShow{false};

    CEMeta(Serialize, Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Enable", bHide = true, bReadOnly = true)) bool mEnable{true};

    CE_Serialize_Deserialize;

protected:
    #if CROSSENGINE_EDITOR && CROSSENGINE_WIN
    std::vector<Float3> mvDebugPosition;
    std::vector<ColorRGBAf> mvAmbientColor;
    std::vector<Float4> mvSkyBentNormal;
    #endif
    friend class LightProbeVolumeSystemG; 
};

class LightProbeVolumeSystemG : public GameSystemBase
{
    CESystemInternal(ComponentType = LightProbeVolumeComponentG)
    DEFINE_COMPONENT_READER_WRITER(LightProbeVolumeComponentG, LightProbeVolumeComponentReader, LightProbeVolumeComponentWriter)
public:
    using LightProbeVolumeComponentHandle = ecs::ComponentHandle<LightProbeVolumeComponentG>;

    CEFunction(Reflect)
    static LightProbeVolumeSystemG* CreateInstance();

    virtual void Release() override;

    CEFunction(Reflect)
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

    virtual RenderSystemBase* GetRenderSystem() override;

    virtual void NotifyAddRenderSystemToRenderWorld() override
    {
        mIsRenderObjectOwner = false;
    };

protected:
    LightProbeVolumeSystemG();

    virtual ~LightProbeVolumeSystemG();

    void AfterInitComponent(ecs::EntityID entityID);

public:
    CEFunction(Editor) 
    void GetLightProbeVolumeComponent(const LightProbeVolumeComponentReader& component, cross::LightProbeVolumeComponentG& outValue);
    CEFunction(Editor) 
    void SetLightProbeVolumeComponent(const LightProbeVolumeComponentWriter& component, const cross::LightProbeVolumeComponentG& inValue);

public:
#define LIGHTPROBE_VOLUME_COMPONENT_PROPERTY(PROP, TYPE)                                                                                                                                                                                       \
    inline void SetLightProbeVolume##PROP(const LightProbeVolumeComponentWriter& comp, const TYPE& val)                                                                                                                                        \
    {                                                                                                                                                                                                                                          \
        comp->m##PROP = val;                                                                                                                                                                                                                   \
    }                                                                                                                                                                                                                                          \
    inline const TYPE GetLightProbeVolume##PROP(const LightProbeVolumeComponentReader& comp) const                                                                                                                                             \
    {                                                                                                                                                                                                                                          \
        return comp->m##PROP;                                                                                                                                                                                                                  \
    }

#define LIGHTPROBE_VOLUME_COMPONENT_PROPERTY_SYNC_R(PROP, TYPE)                                                                                                                                                                                \
    inline void SetLightProbeVolume##PROP(const LightProbeVolumeComponentWriter& comp, const TYPE& val)                                                                                                                                        \
    {                                                                                                                                                                                                                                          \
        comp->m##PROP = val;                                                                                                                                                                                                                   \
        DispatchRenderingCommandWithToken([renderSystem = mLightProbeVolumeSystemR, eID = comp.GetEntityID(), val]() { renderSystem->SetLightProbeVolume##PROP(eID, val); });                                                                \
    }                                                                                                                                                                                                                                          \
    inline const TYPE GetLightProbeVolume##PROP(const LightProbeVolumeComponentReader& comp) const                                                                                                                                             \
    {                                                                                                                                                                                                                                          \
        return comp->m##PROP;                                                                                                                                                                                                                  \
    }

    LIGHTPROBE_VOLUME_COMPONENT_PROPERTY_SYNC_R(Enable, bool);
    LIGHTPROBE_VOLUME_COMPONENT_PROPERTY_SYNC_R(CacheFile, std::string);
    LIGHTPROBE_VOLUME_COMPONENT_PROPERTY(DebugShow, bool);
    LIGHTPROBE_VOLUME_COMPONENT_PROPERTY(BrickSize, Float3A);
    LIGHTPROBE_VOLUME_COMPONENT_PROPERTY_SYNC_R(UseVolumetricLightMap, bool);
    LIGHTPROBE_VOLUME_COMPONENT_PROPERTY(BoundingBoxType, LightProbeVolumeComponentG::VolumeSizeType);
    LIGHTPROBE_VOLUME_COMPONENT_PROPERTY_SYNC_R(VLMReflectionProbeIntensity, float);
    LIGHTPROBE_VOLUME_COMPONENT_PROPERTY_SYNC_R(VLMReflectionProbeAOIntensity, float);

    #if CROSSENGINE_EDITOR && CROSSENGINE_WIN
    bool SetLightProbeCache(ecs::EntityID entityID, const std::string& filename, std::shared_ptr<LightProbeCache> lightProbeCache);
    bool SetVolumetricLightMap(ecs::EntityID entityID, const std::string& indirectionTexName, const std::string& ambientVector, 
        const std::vector<std::string>& shCoefficients, const std::string& skyBentNormalAO, const BoundingBox& volumeBounds, int brickSize);
    #endif
public:
    static SerializeNode SerializeLightProbeVolumeComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr);

    static void DeserializeLightProbeVolumeComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);

    static void PostDeserializeLightProbeVolumeComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);

    static void GetResourceLightProbeVolumeComponent(ISerializeWorld * serializeWorld, ecs::IComponent * componentPtr, ResourcePtr resource);

private:
    void CreateVolumetricLightmap(const LightProbeVolumeComponentWriter& lpvComp);

    #if CROSSENGINE_EDITOR && CROSSENGINE_WIN
    void UpdateDebugProbeColor(const LightProbeVolumeComponentWriter& lpvComp, bool force = false);
    #endif

private:
    LightProbeVolumeSystemR* mLightProbeVolumeSystemR{nullptr};
    bool mIsRenderObjectOwner{true};
};
}   // namespace cross
