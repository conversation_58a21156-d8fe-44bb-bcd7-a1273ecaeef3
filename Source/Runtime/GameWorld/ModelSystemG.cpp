#include "EnginePrefix.h"
#include "CrossBase/Template/TypeCast.hpp"
#include "CrossBase/Math/CrossMath.h"
#include "Threading/RenderingThread.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Common/SettingsManager.h"

#include "Resource/MeshAssetDataResource.h"
#include "Resource/Resource.h"
#include "Resource/ResourceManager.h"
#include "Resource/AssetStreaming.h"
#include "Resource/Fx.h"

#include "RenderEngine/ModelSystemR.h"
#include "RenderEngine/RenderMaterial.h"

#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/AABBSystemG.h"
#include "Runtime/GameWorld/ModelSystemG.h"
#include "Runtime/GameWorld/SkeletonSystemG.h"
#include "Runtime/Animation/Skeleton/SkeletonComponent.h"
#include "Runtime/GameWorld/TransformSystemG.h"

//namespace {
//    std::ofstream outfile;   // debug
//    std::mutex fileMutex;
//    int multiModelsCount = 0; 
//}

namespace cross {
ecs::ComponentDesc* cross::ModelComponentG::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::ModelComponentG>(
        { false,
        true,
        true, false},
        &ModelSystemG::SerializeModelComponent,
        &ModelSystemG::DeserializeModelComponent,
        &ModelSystemG::PostDeserializeModelComponent,
        &ModelSystemG::UpdateDeserializeModelComponent,
        &ModelSystemG::GetResourceModelComponent);
}

void ModelComponentG::AdditionalDeserialize(const DeserializeNode& inNode, SerializeContext& context)
{
    if (auto mainModel = inNode.HasMember("mMainModel"); mainModel)
    {
        auto LoadMeshAssetAndMaterialFor = [](IndividualModel& model)
        {
            model.mAsset = TypeCast<resource::MeshAssetDataResource>(gAssetStreamingManager->LoadSynchronously(model.mAssetPath.c_str()));
            for (auto& singleLODModelProperty : model.mSingleLODModelProperties)
            {
                for (auto& subModelProperty : singleLODModelProperty.mSubModelProperties)
                {
                    subModelProperty.mMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(subModelProperty.mMaterialPath.c_str()));
                }
            }
        };

        LoadMeshAssetAndMaterialFor(mMainModel);
        for (auto& model : mChangeableModels)
        {
            LoadMeshAssetAndMaterialFor(model);
        }
    }
    else if (auto models = inNode.HasMember("mModels"); models)
    {
        // To be compatible with the old model
        auto DeserializeOneModel = [&](const DeserializeNode& modelNode, IndividualModel& model)
        {
            model.mAssetPath = modelNode["mAsset"].AsString();
            model.mAsset = TypeCast<resource::MeshAssetDataResource>(gAssetStreamingManager->LoadSynchronously(model.mAssetPath));
            model.mVisible = modelNode.Value("mVisible", true);
            model.mReceiveDecals = modelNode.Value("mReceiveDecals", true);

            auto assetData = model.mAsset->GetAssetData();
            size_t lodCount = assetData->GetLodCount();
            size_t subModelCount = modelNode["mSubModelProperties"].Size();

            model.mSingleLODModelProperties.resize(lodCount);
            for (int lodIdx = 0; lodIdx < lodCount; lodIdx++)
            {
                auto& singleLODModelProperty = model.mSingleLODModelProperties[lodIdx];

                //int subModelCount = assetData->GetMeshPartCount(lodIdx);
                singleLODModelProperty.mSubModelProperties.resize(subModelCount);
                // Convert old model property to current model property.
                for (size_t subModelIndex = 0; subModelIndex < subModelCount; ++subModelIndex)
                {
                    auto oldSubModelPropertyNode = modelNode["mSubModelProperties"][subModelIndex];
                    auto& subModelProperty = singleLODModelProperty.mSubModelProperties[subModelIndex];
                    if (lodIdx == 0)
                    {
                        subModelProperty.mMaterialPath = oldSubModelPropertyNode["mMaterial"].AsString();
                        subModelProperty.mMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(subModelProperty.mMaterialPath));
                    }
                    else if (oldSubModelPropertyNode.HasMember("mLODMaterials"))
                    {
                        auto lodMaterialsNode = oldSubModelPropertyNode["mLODMaterials"];

                        auto lodIdxStr = std::to_string(lodIdx);
                        if (lodMaterialsNode.HasMember(lodIdxStr))
                        {
                            subModelProperty.mMaterialPath = lodMaterialsNode[lodIdxStr].AsString();
                            subModelProperty.mMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(subModelProperty.mMaterialPath));
                        }
                    }
                    subModelProperty.mVisible = oldSubModelPropertyNode["mVisible"].AsBoolean();
                }
            }
        };
        
        DeserializeOneModel(models.value()[0], this->mMainModel);
        UInt32 changeableModelCount = (UInt32)models.value().Size() - 1;
        this->mChangeableModels.resize(changeableModelCount);

        //{
        //    std::lock_guard lock{fileMutex};

        //    if (!outfile.is_open())
        //        outfile.open("ZGGG.txt", std::ios::out | std::ios::app);

        //    if (outfile)
        //    {
        //        if (changeableModelCount > 0)
        //        {
        //            auto assetData = mMainModel.mAsset->GetAssetData();
        //            auto& assetName = assetData->GetName();
        //            outfile << "---------- Num " << multiModelsCount++ << " ----------" << std::endl;
        //            outfile << "Model: " << assetName << "\nPath: " << gResourceMgr.ConvertGuidToPath(mMainModel.mAssetPath) << "\nTotal Model Count: " << changeableModelCount + 1 << "." << std::endl;
        //        }

        //        outfile.close();
        //    }
        //}

        for (UInt32 i = 0; i < changeableModelCount; ++i)
        {
            DeserializeOneModel(models.value()[i + 1] , this->mChangeableModels[i]);
        }

        if (inNode.HasMember("mEnableGPUSkin"))
        {
            this->mEnableGPUSkin = inNode["mEnableGPUSkin"].AsBoolean();
        }
        else
        {
            this->mEnableGPUSkin = true;
        }

        if (inNode.HasMember("mCacheableForDrawing"))
        {
            this->mCacheableForDrawing = inNode["mCacheableForDrawing"].AsBoolean();
        }
        else
        {
            this->mCacheableForDrawing = false;
        }

        if (inNode.HasMember("mDistanceCulling"))
        {
            this->mDistanceCulling.Deserialize(inNode["mDistanceCulling"], context);
        }
    }
}

SerializeNode ModelSystemG::SerializeModelComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    SCOPED_CPU_TIMING(GroupSerialize, "SerializeModelComponent");

    auto comp = static_cast<ModelComponentG*>(componentPtr);
    SerializeContext context;
    SerializeNode json;
    json = comp->Serialize(context);
    return json;

    //SerializeNode json;
    //auto modelPtr = static_cast<ModelComponentG*>(componentPtr);

    //// Serialize models array(set main model as the first)
    //auto SerializeOneModel = [&json](const ModelComponentG::IndividualModel& model) {
    //    SerializeNode individualModelNode;
    //    for (auto& singleLODModelProperty : model.mSingleLODModelProperties)
    //    {
    //        SerializeNode singleLODModelPropertyNode;
    //        Assert(singleLODModelProperty.mSubModelProperties.size() > 0u);
    //        for (auto& subModelProperty : singleLODModelProperty.mSubModelProperties)
    //        {
    //            SerializeNode subModelPropertyNode;
    //            Assert(subModelProperty.mMaterial->HasAsset());
    //            subModelPropertyNode["mMaterial"] = subModelProperty.mMaterial->GetGuid_Str();
    //            subModelPropertyNode["mVisible"] = subModelProperty.mVisible;
    //            singleLODModelPropertyNode["mSubModelProperties"].PushBack(std::move(subModelPropertyNode));
    //        }

    //        individualModelNode["mSingleLODModelProperties"].PushBack(std::move(singleLODModelPropertyNode));
    //    }
    //    Assert(model.mAsset);
    //    
    //    //model.mAsset->Serialize("linsh_mesh_test.nda");
    //    
    //    individualModelNode["mAsset"] = model.mAsset->GetGuid_Str();
    //    //resource->AddReferenceResource(model.mAsset->GetName());
    //    individualModelNode["mVisible"] = model.mVisible;
    //    individualModelNode["mReceiveDecals"] = model.mReceiveDecals;
    //    json["mModels"].PushBack(std::move(individualModelNode));

    //    // test mesh serialize
    //    // model.mAsset->Serialize("D:/testMesh.nda");
    //};
    //SerializeOneModel(modelPtr->mMainModel);
    //for (const auto& model : modelPtr->mChangeableModels)
    //{
    //    SerializeOneModel(model);
    //}
    //json["mEnableGPUSkin"] = modelPtr->mEnableGPUSkin;
    //json["mCacheableForDrawing"] = modelPtr->mCacheableForDrawing;
    //// TODO RenderEffect\BatchInfo... ( Ignore unused properties now)

    //SerializeContext context;
    //json["mDistanceCulling"] = std::move(modelPtr->mDistanceCulling.Serialize(context));

    //return json;
}

ModelComponentG ModelSystemG::CreateModelComp(const std::string& modelPath, const std::vector<std::string>& materialPaths, bool isVisible, float maxDis)
{
    using namespace resource;

    ModelComponentG comp;

    auto& mainModel = comp.mMainModel;
    mainModel.mAssetPath = modelPath;
    mainModel.mAsset = TypeCast<resource::MeshAssetDataResource>(gResourceMgr.GetResource(modelPath.c_str()));
    mainModel.mVisible = isVisible;

    auto modelLODCount = mainModel.mAsset->GetAssetData()->GetLodCount();
    auto subModelCount = materialPaths.size();
    mainModel.mSingleLODModelProperties.resize(modelLODCount);
    mainModel.mSingleLODModelProperties[0].mSubModelProperties.resize(subModelCount);
    for (auto subModelIndex = 0; subModelIndex < subModelCount; ++subModelIndex)
    {
        auto& subModelProperty = mainModel.mSingleLODModelProperties[0].mSubModelProperties[subModelIndex];
        subModelProperty.mMaterialPath = materialPaths[subModelIndex];//TypeCast<MaterialInterface>(gResourceMgr.GetResource(materialPaths[subModelIndex].c_str()));
        subModelProperty.mVisible = isVisible;
    }

    comp.mEnableGPUSkin = false;
    comp.mDistanceCulling.maxCullingDistance = maxDis;
    return comp;
}

void ModelSystemG::DeserializeModelComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr)
{
    SCOPED_CPU_TIMING(GroupSerialize, "DeserializeModelComponent");

    if (json.IsNull())
    {
        return;
    }
    auto cmpntPtr = static_cast<ModelComponentG*>(componentPtr);
    SerializeContext context;
    cmpntPtr->Deserialize(json, context);

    //if (json.IsNull())
    //{
    //    return;
    //}
    //auto modelPtr = static_cast<ModelComponentG*>(componentPtr);
    //using namespace resource;

    //auto DeserializeOneModel = [&json](ModelComponentG::IndividualModel& descModel, UInt32 index) 
    //{
    //    auto individualModelNode = json["mModels"][index];

    //    //  deserialize lod model asset
    //    size_t singleLODModelCount = individualModelNode["mSingleLODModelProperties"].Size();
    //    descModel.mSingleLODModelProperties.resize(singleLODModelCount);
    //    for (size_t singleLODModelIndex = 0ull; singleLODModelIndex < singleLODModelCount; ++singleLODModelIndex)
    //    {
    //        auto singleLODModelPropertyNode = individualModelNode["mSingleLODModelProperties"][singleLODModelIndex];
    //        auto& singleLODModelProperty = descModel.mSingleLODModelProperties[singleLODModelIndex];

    //        // deserialize sub model asset
    //        size_t subModelCount = singleLODModelPropertyNode["mSubModelProperties"].Size();
    //        singleLODModelProperty.mSubModelProperties.resize(subModelCount);
    //        for (size_t subModelIndex = 0ull; subModelIndex < subModelCount; ++subModelIndex)
    //        {
    //            auto subModelPropertyNode = singleLODModelPropertyNode["mSubModelProperties"][subModelIndex];
    //            auto& subModelProperty = singleLODModelProperty.mSubModelProperties[subModelIndex];
    //            auto materialPath = subModelPropertyNode["mMaterial"].AsString();
    //            auto material = TypeCast<MaterialInterface>(gAssetStreamingManager->LoadSynchronously(materialPath.c_str()));

    //            subModelProperty.mMaterial = material;
    //            subModelProperty.mVisible = subModelPropertyNode["mVisible"].AsBoolean();
    //        }
    //    }

        //size_t subModelCount = individualModelNode["mSubModelProperties"].Size();
        //descModel.mSubModelProperties.resize(subModelCount);
        //for (size_t subModelIndex = 0; subModelIndex < subModelCount; ++subModelIndex)
        //{
        //    auto subModelPropertyNode = individualModelNode["mSubModelProperties"][subModelIndex];
        //    auto& subModelProperty = descModel.mSubModelProperties[subModelIndex];
        //    auto materialPath = subModelPropertyNode["mMaterial"].AsString();
        //    auto material = TypeCast<MaterialInterface>(gAssetStreamingManager->LoadSynchronously(materialPath.c_str()));
        //    
        //    subModelProperty.mMaterial = material;
        //    
        //    if (subModelPropertyNode.HasMember("mLODMaterials"))
        //    {
        //        auto lodMaterialsNode = subModelPropertyNode["mLODMaterials"];
        //        for (UInt32 lodIndex = 1; lodIndex <= MAX_MESH_LOD_NUM; ++lodIndex)
        //        {
        //            if (lodMaterialsNode.HasMember(std::to_string(lodIndex)))
        //            {
        //                subModelProperty.mLODMaterials.resize(lodIndex);
        //                auto subModelMatertail = TypeCast<MaterialInterface>(gAssetStreamingManager->LoadSynchronously(lodMaterialsNode[std::to_string(lodIndex)].AsString().c_str()));
        //                subModelProperty.mLODMaterials[lodIndex - 1] = subModelMatertail;
        //            }
        //        }
        //    }

        //    subModelProperty.mVisible = subModelPropertyNode["mVisible"].AsBoolean();
        //}

    //    auto assetPath = individualModelNode["mAsset"].AsString();
    //    descModel.mAsset = TypeCast<resource::MeshAssetDataResource>(gAssetStreamingManager->LoadSynchronously(assetPath.c_str()));
    //    descModel.mVisible = individualModelNode.Value("mVisible", true);
    //    descModel.mReceiveDecals = individualModelNode.Value("mReceiveDecals", true);
    //};

    //DeserializeOneModel(modelPtr->mMainModel, 0);
    //UInt32 changeableModelCount = (UInt32)json["mModels"].Size() - 1;
    //modelPtr->mChangeableModels.resize(changeableModelCount);
    //for (UInt32 i = 0; i < changeableModelCount; ++i)
    //{
    //    DeserializeOneModel(modelPtr->mChangeableModels[i], i + 1);
    //}

    //if (json.HasMember("mEnableGPUSkin"))
    //{
    //    modelPtr->mEnableGPUSkin = json["mEnableGPUSkin"].AsBoolean();
    //}
    //else
    //{
    //    modelPtr->mEnableGPUSkin = true;
    //}

    //if (json.HasMember("mCacheableForDrawing"))
    //{
    //    modelPtr->mCacheableForDrawing = json["mCacheableForDrawing"].AsBoolean();
    //}
    //else
    //{
    //    modelPtr->mCacheableForDrawing = false;
    //}

    //if (json.HasMember("mDistanceCulling"))
    //{
    //    SerializeContext context;
    //    modelPtr->mDistanceCulling.Deserialize(json["mDistanceCulling"], context);
    //}

    return;
}
void ModelSystemG::Model_SetMaterialInstance(cross::IGameWorld* world, UInt64 entity, cross::resource::MaterialInterface* materialInterface, int subModelIndex, int modelIndex) 
{
    cross::ecs::EntityID entityId = cross::ecs::EntityID(entity);
    if (world)
    {
        auto gameworld = (cross::GameWorld*)world;
        auto componenthandle = gameworld->GetComponent<cross::ModelComponentG>(entityId);
        if (componenthandle.IsValid())
        {
            gameworld->GetGameSystem<cross::ModelSystemG>()->SetModelLodMaterial(componenthandle.Write(), cross::MaterialInterfacePtr(materialInterface), subModelIndex, 0, modelIndex);
        }
    }
}
cross::Resource* ModelSystemG::Model_GetMaterialResource(cross::IGameWorld* world, UInt64 entity, SInt32 modelIndex, SInt32 subModelIndex) 
{
    cross::GameWorld* gameWorld = (cross::GameWorld*)world;
    auto modelSystem = gameWorld->GetGameSystem<cross::ModelSystemG>();

    cross::ecs::EntityID entityId{entity};
    if (gameWorld->HasComponent<cross::ModelComponentG>(entityId) == false)
        return nullptr;
    auto modelComp = gameWorld->GetComponent<cross::ModelComponentG>(entityId);

   //auto result = modelSystem->GetModelLodMaterial(modelComp.Read(), subModelIndex, modelIndex);
    return nullptr;
}

void ModelSystemG::PostDeserializeModelComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
{
    // all seems quite of wasting since read from component and then set it back
    // need further investigation

    // Set Model and Material to run needed logic
    auto modelH = gameWorld->GetComponent<ModelComponentG>(entityId);
    auto modelSys = gameWorld->GetGameSystem<ModelSystemG>();
    const auto& writer = modelH.Write();

    // model
    bool hasCluster = false;
    for (UInt32 mi = 0; mi < modelSys->GetModelCount(ecs::GrantReadAccess(writer)); ++mi)
    {
        auto& model = modelSys->GetModel(ecs::GrantReadAccess(writer), mi);
        modelSys->SetModelAsset(writer, model.mAsset, mi);
        modelSys->SetModelVisibility(writer, model.mVisible, mi, true);
        modelSys->SetModelReceiveDecals(writer, model.mReceiveDecals, mi);

        int lodIdx = 0;
        for (auto& singleLODModelProperty : model.mSingleLODModelProperties)
        {
            int subModelIdx = 0;
            for (auto& subModelProperty : singleLODModelProperty.mSubModelProperties)
            {
                modelSys->SetSubModelVisibility(writer, subModelProperty.mVisible, subModelIdx, lodIdx, mi);
                modelSys->SetModelLodMaterial(writer, subModelProperty.mMaterial, subModelIdx, lodIdx, mi);
                subModelIdx++;
            }
            lodIdx++;
        }

        if (!model.mVisible)
            continue;

        model.mAsset->OnDataUploaded();
    }

    bool gloablGPUSkin = EngineGlobal::Inst().GetSettingMgr()->GetGPUSkinEnable();
    const auto& reader = modelH.Read();
    if (modelSys->IsSupportGPUSkin(reader))
    {
        modelSys->SetSkeltModelGPUSkin(writer, reader->mEnableGPUSkin && gloablGPUSkin);
    }
    else
    {
        modelSys->SetSkeltModelGPUSkin(writer, false);
    }

    modelSys->SetModelEnityDistanceCulling(writer, writer->mDistanceCulling);
}

void ModelSystemG::UpdateDeserializeModelComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId) {
    if (json.IsNull()){
        return;
    }

    auto modelH = gameWorld->GetComponent<ModelComponentG>(entityId);
    auto modelSys = gameWorld->GetGameSystem<ModelSystemG>();
    auto modelPtr = static_cast<ModelComponentG*>(componentPtr);

    SerializeContext context;
    modelPtr->Deserialize(json, context);
    PostDeserializeModelComponent(json, componentPtr, gameWorld, entityId);

    //// EnableGPUSkin
    //modelPtr->mEnableGPUSkin = json.HasMember("mEnableGPUSkin") ? json["mEnableGPUSkin"].AsBoolean() : false;
    //bool gloablGPUSkin = EngineGlobal::Inst().GetSettingMgr()->GetGPUSkinEnable();
    //modelSys->SetSkeltModelGPUSkin(modelH.Write(), modelSys->IsSupportGPUSkin(modelH.Read()) ? modelH.Read()->mEnableGPUSkin && gloablGPUSkin : false);
    //// update func
    //auto UpdateOneModel = [&](IndividualModel& model, UInt32 index) {
    //    // update model
    //    auto individualModelNode = json["mModels"][index];
    //    auto assetPath = individualModelNode["mAsset"].AsString();
    //    model.mVisible = individualModelNode.Value("mVisible", true);
    //    model.mReceiveDecals = individualModelNode.Value("mReceiveDecals", true);
    //    if (assetPath != model.mAsset->GetName())
    //    {
    //        modelSys->SetModelAssetPath(modelH.Write(), assetPath, index);
    //        modelSys->SetModelVisibility(modelH.Write(), model.mVisible, index);
    //        modelSys->SetModelReceiveDecals(modelH.Write(), model.mReceiveDecals, index);
    //    }
    //    // update sub model
    //    size_t singleLODModelCount = individualModelNode["mSingleLODModelProperties"].Size();
    //    model.mSingleLODModelProperties.resize(singleLODModelCount);
    //    int lodIdx = 0;
    //    for (auto& singleLODModelProperty : model.mSingleLODModelProperties)
    //    {
    //        auto singleLODModelPropertyNode = individualModelNode["mSingleLODModelProperties"][lodIdx];
    //        size_t subModelCount = singleLODModelPropertyNode.Size();
    //        singleLODModelProperty.mSubModelProperties.resize(subModelCount);
    //        int subModelIdx = 0;
    //        for (auto& subModelProperty : singleLODModelProperty.mSubModelProperties)
    //        {
    //            auto subModelPropertyNode = singleLODModelPropertyNode["mSubModelProperties"][subModelIdx];
    //            auto subMaterialPath = subModelPropertyNode["mMaterial"].AsString();
    //            if (!subModelProperty.mMaterial || subMaterialPath != subModelProperty.mMaterial->GetName())
    //            {
    //                modelSys->SetModelLodMaterialPath(modelH.Write(), subMaterialPath, subModelIdx, lodIdx, index);
    //                modelSys->SetSubModelVisibility(modelH.Write(), subModelPropertyNode["mVisible"].AsBoolean(), subModelIdx, index);
    //            }

    //            subModelIdx++;
    //        }
    //        lodIdx++;
    //    }
    //};
    //// add func
    //auto AddOneModel = [&](UInt32 index) {
    //    auto modelNode = json["mModels"][index];
    //    // add model
    //    auto assetPath = modelNode["mAsset"].AsString();
    //    modelSys->AddModelByAssetPath(modelH.Write(), assetPath);
    //    modelSys->SetModelVisibility(modelH.Write(), modelNode.Value("mVisible", true), index);
    //    modelSys->SetModelReceiveDecals(modelH.Write(), modelNode.Value("mReceiveDecals", true), index);
    //    auto model = modelSys->GetModel(ecs::GrantReadAccess(modelH.Write()), index);
    //    // set lod count
    //    size_t singleLODModelCount = modelNode["mSingleLODModelProperties"].Size();
    //    model.mSingleLODModelProperties.resize(singleLODModelCount);
    //    int lodIdx = 0;
    //    for (auto& singleLODModelProperty : model.mSingleLODModelProperties)
    //    {
    //        auto singleLODModelPropertyNode = modelNode["mSingleLODModelProperties"][lodIdx];
    //        size_t subModelCount = singleLODModelPropertyNode.Size();
    //        singleLODModelProperty.mSubModelProperties.resize(subModelCount);
    //        int subModelIdx = 0;
    //        for (auto& subModelProperty : singleLODModelProperty.mSubModelProperties)
    //        {
    //            auto subModelPropertyNode = singleLODModelPropertyNode["mSubModelProperties"][subModelIdx];
    //            auto subMaterialPath = subModelPropertyNode["mMaterial"].AsString();
    //            modelSys->SetModelLodMaterialPath(modelH.Write(), subMaterialPath, subModelIdx, lodIdx, index);
    //            modelSys->SetSubModelVisibility(modelH.Write(), subModelPropertyNode["mVisible"].AsBoolean(), subModelIdx, index);
    //            subModelIdx++;
    //        }
    //        lodIdx++;
    //    }
    //};

    //UpdateOneModel(modelPtr->mMainModel, 0);
    //UInt32 newModelCount = (UInt32)json["mModels"].Size();
    //auto modelCount = modelSys->GetModelCount(ecs::GrantReadAccess(modelH.Write()));
    //UInt32 minCount = std::min(newModelCount, modelCount);
    //// update
    //for (UInt32 i = 1; i < minCount; i++)
    //{
    //    UpdateOneModel(modelPtr->mChangeableModels[i - 1], i);
    //}
    //// add
    //if (newModelCount > minCount)
    //{
    //    for (UInt32 i = minCount; i < newModelCount; i++)
    //    {
    //        AddOneModel(i);
    //    }
    //}
    //// remove
    //else if (modelCount > minCount)
    //{
    //    for (UInt32 i = modelCount; i > minCount; i--)
    //    {
    //        modelSys->RemoveModel(modelH.Write(), i - 1);
    //    }
    //}
}

void ModelSystemG::GetResourceModelComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr, ResourcePtr resource)
{
    auto modelPtr = static_cast<ModelComponentG*>(componentPtr);
    // Get models array(set main model as the first)
    auto GetOneModelResource = [&](const IndividualModel& model) {
        for (auto const& singleLODModelProperty : model.mSingleLODModelProperties)
        {
            for (auto const& subModelProperty : singleLODModelProperty.mSubModelProperties)
            {
                if (subModelProperty.mMaterial)
                {
                    resource->AddReferenceResource(subModelProperty.mMaterial->GetGuid_Str());
                }
            }
        }

        Assert(model.mAsset);
        resource->AddReferenceResource(model.mAsset->GetGuid_Str());
    };

    GetOneModelResource(modelPtr->mMainModel);
    for (const auto& model : modelPtr->mChangeableModels)
    {
        GetOneModelResource(model);
    }
}

ModelSystemG* ModelSystemG::CreateInstance() { return new ModelSystemG(); }

ModelSystemG::ModelSystemG() { mRenderMeshSystem = ModelSystemR::CreateInstance(); }

ModelSystemG::~ModelSystemG()
{
    if (mIsRenderObjectOwner)
    {
        mRenderMeshSystem->Release();
    }
    mRenderMeshSystem = nullptr;
}

void ModelSystemG::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    GameSystemBase::NotifyEvent(event, flag);

    if (event.mEventType == OnSystemAddToGameWorldEvent::sEventType)
    {
        mDefaultMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(EngineGlobal::Inst().GetSettingMgr()->GetRenderPipelineSetting()->DefaultMaterial));
    }
}

void ModelSystemG::Release() { delete this; }

RenderSystemBase* ModelSystemG::GetRenderSystem() { return mRenderMeshSystem; }

void ModelSystemG::OnBeginFrame(FrameParam* frameParam) { 
    mModelChangeList.GetContainer().clear();
#if CROSSENGINE_EDITOR
    int resId = ClassID(MeshAssetDataResource);
    mFirstUpdateEnable |= gResourceMgr.CheckChangedResByID(resId);
#endif
}

void ModelSystemG::OnFirstUpdate(FrameParam* frameParam) {
#if CROSSENGINE_EDITOR
    int resId = ClassID(MeshAssetDataResource);
    const auto* changedResList = gResourceMgr.GetChangedResListById(resId);
    if (changedResList)
    {
        for (auto& res : *changedResList)
        {
            OnMeshChanged(TypeCast<resource::MeshAssetDataResource>(res));
        }
    }
#endif
}

void ModelSystemG::OnEndFrame(FrameParam* frameParam) {}

MaterialInterfacePtr LoadMaterialAsset(const std::string& assetpath)
{
    auto materialResource = cross::TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(assetpath.c_str()));
    return materialResource;
}

MeshAssetDataResourcePtr LoadMeshAsset(const std::string& assetpath)
{
    auto resourceptr = gAssetStreamingManager->LoadSynchronously(assetpath.c_str());
    auto asset = cross::TypeCast<resource::MeshAssetDataResource>(resourceptr);
    return asset;
}

bool ModelSystemG::SetModelAssetPath(const ModelComponentWriter& modelH, std::string const& assetpath) { return SetModelAssetPath(modelH, assetpath, 0); }


bool ModelSystemG::SetModelAssetPath(const ModelComponentWriter& modelH, const std::string& assetpath, UInt32 modelIndex)
{
    return SetModelAsset(modelH, LoadMeshAsset(assetpath), modelIndex);
}

bool ModelSystemG::SetModelMaterialPath(const ModelComponentWriter& modelH, const std::string& assetpath, int subModelIndex /* = -1 */, UInt32 lodindex, UInt32 modelIndex)
{
    auto materialResource = LoadMaterialAsset(assetpath);
    if (!materialResource.get())
        return false;

    SetModelLodMaterial(modelH, materialResource, (SInt8)subModelIndex, lodindex, modelIndex);
    mModelChangeList.EmplaceChangeData(modelH.GetEntityID());
    return true;
}

void ModelSystemG::SetModelVisibility(const ModelComponentWriter& modelH, bool isVisible, UInt32 modelIndex, bool forceUpdate)
{
    // check visible change to avoid potential overhead of rendering command
    // forceUpdate so the model can be set during first frame.
    auto& part = GetModel(modelH, modelIndex);
    if (part.mVisible != isVisible || forceUpdate)
    {
        part.mVisible = isVisible;
        // NOTE: Since model visibility change does not affect its bounding box, there is no need to add it to the change list,
        // which will cause AABB update
        // mModelChangeList.EmplaceChangeData(modelH.GetEntityID());
        DispatchRenderingCommandWithToken([renderSystem = mRenderMeshSystem, entity = modelH.GetEntityID(), isVisible, modelIndex]() { renderSystem->SetModelVisibility(entity, isVisible, modelIndex); });
    }
}
UInt32 ModelSystemG::GetSubModelCount(const ModelComponentReader& modelH, UInt32 modelIndex, UInt32 lodIndex) const
{
    auto model = GetModel(modelH, modelIndex);
    return UInt32(model.mSingleLODModelProperties.empty() ? 0ull : model.mSingleLODModelProperties[lodIndex].mSubModelProperties.size());
}
UInt32 ModelSystemG::GetLODCount(const ModelComponentReader& modelH, UInt32 modelIndex) const
{
    auto model = GetModel(modelH, modelIndex);
    return static_cast<UInt32>(model.mSingleLODModelProperties.empty() ? 0ull : model.mSingleLODModelProperties.size());
}
void ModelSystemG::SetSubModelVisibility(const ModelComponentWriter& modelH, bool isVisible, int subModelIndex, UInt32 lodindex, UInt32 modelIndex)
{
    auto& model = GetModel(modelH, modelIndex);
    if (subModelIndex > -1)
    {
        model.mSingleLODModelProperties[lodindex].mSubModelProperties[subModelIndex].mVisible = isVisible;
    }
    else
    {
        for (auto& singleLODModelProperty : model.mSingleLODModelProperties)
        {
            for (auto& prop : singleLODModelProperty.mSubModelProperties)
            {
                prop.mVisible = isVisible;
            }
        }
    }
    DispatchRenderingCommandWithToken(
        [renderSystem = mRenderMeshSystem, entity = modelH.GetEntityID(), isVisible, subModelIndex, modelIndex, lodindex]() { renderSystem->SetModelSubVisibility(entity, isVisible, (SInt8)subModelIndex, lodindex, modelIndex); });
}

void ModelSystemG::SetModelReceiveDecals(const ModelComponentWriter& modelH, bool value, UInt32 modelIndex)
{
    GetModel(modelH, modelIndex).mReceiveDecals = value;
    mModelChangeList.EmplaceChangeData(modelH.GetEntityID());
    DispatchRenderingCommandWithToken([renderSystem = mRenderMeshSystem, entity = modelH.GetEntityID(), value, modelIndex]() { renderSystem->SetModelReceiveDecals(entity, value, modelIndex); });
}

bool ModelSystemG::GetModelReceiveDecals(const ModelComponentReader& modelH, UInt32 modelIndex) const
{
    return GetModel(modelH, modelIndex).mReceiveDecals;
}

void ModelSystemG::SetModelDirty(const ModelComponentWriter& modelH)
{
    DispatchRenderingCommandWithToken([renderSystem = mRenderMeshSystem, entity = modelH.GetEntityID()]() { renderSystem->SetModelDirty(entity); });
}

bool ModelSystemG::IsSkeletalModel(const ModelComponentReader& modelH) const
{
    return modelH->mMainModel.mAsset ? modelH->mMainModel.mAsset->IsSkinValid() : false;
}

bool ModelSystemG::SetModelAsset(const ModelComponentWriter& modelH, const MeshAssetDataResourcePtr& meshAssetDataResourcePtr, UInt32 modelIndex)
{
    if (!meshAssetDataResourcePtr || !meshAssetDataResourcePtr->GetAssetData())
        return false;
    if (modelIndex == 0 && modelH->mChangeableModels.size() > 0 && meshAssetDataResourcePtr->IsSkinValid() != modelH->mChangeableModels[0].mAsset->IsSkinValid())
        return false;
    if (modelIndex > 0 && modelH->mMainModel.mAsset && meshAssetDataResourcePtr->IsSkinValid() != modelH->mMainModel.mAsset->IsSkinValid())
        return false;

    auto& model = GetModel(modelH, modelIndex);

    // For Skinned Mesh
    if (meshAssetDataResourcePtr->IsSkinValid())
    {
        // check Compatible with skeleton if skeltComp exists and skeleton asset is assigned
        auto skeltComp = mGameWorld->GetComponent<SkeletonComponentG>(modelH.GetEntityID());
        auto skeltSys = mGameWorld->GetGameSystem<SkeletonSystemG>();

        if (skeltComp.IsValid())
        {
            if (skeltComp.Read()->RunSkelt && !skeltComp.Read()->RunSkelt->IsCompatibleMesh(meshAssetDataResourcePtr->GetRefSkeleton()))
            {
                //Skeletal Mesh is not Compatible with Skeleton
                LOG_ERROR("Skeletal Mesh: {} is not Compatible with Skeleton: {}", meshAssetDataResourcePtr->GetName(), skeltComp.Read()->SkeletonAssetPtr->GetName());
                return false;
            }

            skeltSys->SetSkeletonCompEnable(skeltComp.Write(), true);
        }

      

        // create skeletal mesh pose if nullptr
        if (model.mPosePtr == nullptr)
        {
            model.mPosePtr = std::make_shared<anim::SkeltMeshPose>();
        }

        const auto meshRefSkelt = meshAssetDataResourcePtr->GetRefSkeleton();
        const auto boneNum = meshRefSkelt->GetRawBoneNum();

        std::vector<SkBoneHandle> allBones(boneNum, {0});
        for (SkBoneHandle curBone = {0}; curBone < boneNum; ++curBone)
        {
            allBones[curBone] = curBone;
        }

        model.mPosePtr->InitPose(allBones, meshAssetDataResourcePtr->GetAssetData());
    }

    model.mAsset = meshAssetDataResourcePtr;
    model.mAssetPath = model.mAsset->GetGuid_Str();
    const MeshAssetData* meshAsset = meshAssetDataResourcePtr->GetAssetData();
    const auto lodCount = meshAsset->GetLodCount();
    if (meshAssetDataResourcePtr)
    {
        const auto& lodStartIndexArray = meshAsset->GetLodStartIndex();

        // For blend shape
        model.mSingleLODModelProperties.resize(lodCount);
        model.mHasBlendShape = false;
        for (int LODIdx = 0; LODIdx < lodCount; LODIdx++)
        {
            int curLODMeshCount = meshAsset->GetMeshPartCount(LODIdx);
            model.mSingleLODModelProperties[LODIdx].mSubModelProperties.resize(curLODMeshCount);
            for (int subModelIdx = 0; subModelIdx < curLODMeshCount; subModelIdx++)
            {
                const auto& curLODMeshPart = meshAsset->GetMeshPartInfo(lodStartIndexArray[LODIdx] + subModelIdx);
                auto& subModel = model.mSingleLODModelProperties[LODIdx].mSubModelProperties[subModelIdx];
                subModel.mHasBlendShape = curLODMeshPart.HasBlendShape();

                if (subModel.mHasBlendShape)
                {
                    model.mHasBlendShape = true;
                    subModel.mChannelWeight.clear();
                    InitChannelWeightFromBlendShape(subModel.mChannelWeight, curLODMeshPart.mBlendShape);
                }
            }
        }
    }

    mModelChangeList.EmplaceChangeData(modelH.GetEntityID());
    {
        ModelChangeEvent event;
        event.mData.mEntity = modelH.GetEntityID();
        event.mData.mResourcePtr = meshAssetDataResourcePtr;
        event.mData.mModelIndex = modelIndex;
        DispatchImmediateEvent<ModelChangeEvent>(event);
    }

    modelH->mBatchInfo = GenerateBatchInfo(meshAssetDataResourcePtr);
    DispatchRenderingCommandWithToken([renderSystem = mRenderMeshSystem, entity = modelH.GetEntityID(), meshAssetDataResourcePtr, batchInfo = modelH->mBatchInfo, modelIndex]() 
    {
        renderSystem->SetBatchInfo(entity, batchInfo);   // Must set before model
        renderSystem->SetModelAsset(entity, meshAssetDataResourcePtr, modelIndex);
    });

    // Set default material for null slot // TODO(yuanwan) No need for extra setting in shipping games
    for (int lodIdx = 0; lodIdx < lodCount; lodIdx++)
    {
        int subModelCount = static_cast<int>(model.mSingleLODModelProperties[lodIdx].mSubModelProperties.size());
        for (int subModelIdx = 0; subModelIdx < subModelCount; subModelIdx++)
        {
            if (!model.mSingleLODModelProperties[lodIdx].mSubModelProperties[subModelIdx].mMaterial)
            {
                SetModelLodMaterial(modelH, mDefaultMaterial, subModelIdx, lodIdx, modelIndex);
            }
        }
    }

    return true;
}

void ModelSystemG::SetSkeltModelPose(const ModelComponentWriter& modelH, UInt32 modelIndex, const anim::RootSpacePose* skeletonPose)
{
    auto& model = GetModel(modelH, modelIndex);

    Assert(model.mAsset->IsSkinValid());
    Assert(model.mAsset);
    if (skeletonPose)
    {
        model.mPosePtr->MapSkeltPoseToMeshPose(*skeletonPose);
        anim::PoseBoneHandle boneNum = {static_cast<UInt32>(model.mPosePtr->GetNumBones())};
        { 
            // pass skeletal mesh pose to render system by frame vector
            auto frameAllocator = EngineGlobal::GetFrameParamMgr()->GetCurrentGameFrameParam()->GetFrameAllocator();
            FrameVector<SIMDMatrix>* completePose = frameAllocator->CreateFrameContainer<FrameVector<SIMDMatrix>>(FRAME_STAGE_GAME_RENDER, boneNum.mVal, MEM_ALIGN_16);
            completePose->Resize(boneNum.mVal);

            // Store pose for refreshing aabb
            model.mCompletePose = frameAllocator->CreateFrameContainer<FrameVector<SIMDMatrix>>(FRAME_STAGE_GAME_RENDER, boneNum.mVal, MEM_ALIGN_16);
            model.mCompletePose->Resize(boneNum.mVal);

            for (anim::PoseBoneHandle iBone = {0}; iBone < boneNum; ++iBone)
            {
                auto& boneTransform = (*model.mPosePtr)[iBone];
                boneTransform.GetTransformMatrix(completePose->At(iBone));
                model.mCompletePose->At(iBone) = completePose->At(iBone);
            }

            DispatchRenderingCommandWithToken([renderSystem = mRenderMeshSystem, entity = modelH.GetEntityID(), modelIndex, completePose] { renderSystem->SetSkeltModelPose(entity, modelIndex, completePose); });
        }
    }
    // when skeleton pose is null, set model completePose to null.
    else
    {
        DispatchRenderingCommandWithToken([renderSystem = mRenderMeshSystem, entity = modelH.GetEntityID(), modelIndex] 
        {
            renderSystem->SetSkeltModelPose(entity, modelIndex, nullptr); 
        });
    }
}

void ModelSystemG::RefreshSkelModelAABB(const ModelComponentWriter& modelH, UInt32 modelIndex) 
{
    SCOPED_CPU_TIMING(GroupAnimation, "Refresh AABB");

#if _WIN32
    auto frameAllocator = EngineGlobal::GetFrameParamMgr()->GetCurrentGameFrameParam()->GetFrameAllocator();
    FrameVector<SIMDMatrix>* meshPosePtr = GetSkeltModelPose(modelH, modelIndex);
    const MeshAssetData* meshAsset = GetModel(modelH, modelIndex).mAsset->GetAssetData();
    UInt32 boneNum = (UInt32)(meshAsset->GetRefSkeleton()->GetRawBoneNum());
    const auto& BindPoseInvMatices = meshAsset->GetBindPoseInvMat();
    FrameVector<SIMDMatrix>* SkinningMatrices = frameAllocator->CreateFrameContainer<FrameVector<SIMDMatrix>>(FRAME_STAGE_GAME_RENDER, boneNum, MEM_ALIGN_16);
    SkinningMatrices->Resize(boneNum);

    for (UInt32 iBone = 0; iBone < boneNum; iBone++)
    {
        SkinningMatrices->At(iBone) = MathSIMD::MatrixMultiply(BindPoseInvMatices[iBone], meshPosePtr->At(iBone));
    }

    // Use the skin matrix to transform the maximum and minimum aabb points
    BoundingBox preBoudingBox = meshAsset->GetBoundingBox();
    Float3 preBoxMin;
    Float3 preBoxMax;
    preBoudingBox.GetMinMax(&preBoxMin, &preBoxMax);
    Float3 curBoxMin = preBoxMin;
    Float3 curBoxMax = preBoxMax;
    for (UInt32 iBone = 0; iBone < boneNum; iBone++)
    {
        const SIMDMatrix& BoneMat0 = (*SkinningMatrices)[iBone];
        SIMDVector4 Weight = MathSIMD::VectorReplicate(1.0);
        SIMDVector4 M00 = MathSIMD::VectorMultiply(BoneMat0.r[0], Weight);
        SIMDVector4 M10 = MathSIMD::VectorMultiply(BoneMat0.r[1], Weight);
        SIMDVector4 M20 = MathSIMD::VectorMultiply(BoneMat0.r[2], Weight);
        SIMDVector4 M30 = MathSIMD::VectorMultiply(BoneMat0.r[3], Weight);

        SIMDVector4 Pos_xxxx = MathSIMD::VectorReplicate(preBoxMin.x);
        SIMDVector4 Pos_yyyy = MathSIMD::VectorReplicate(preBoxMin.y);
        SIMDVector4 Pos_zzzz = MathSIMD::VectorReplicate(preBoxMin.z);
        SIMDVector4 Pos_resultMin = MathSIMD::VectorMultiplyAdd(Pos_xxxx, M00, MathSIMD::VectorMultiplyAdd(Pos_yyyy, M10, MathSIMD::VectorMultiplyAdd(Pos_zzzz, M20, M30)));
        Float3 tempBoxMin = Float3(Pos_resultMin.m128_f32[0], Pos_resultMin.m128_f32[1], Pos_resultMin.m128_f32[2]) / Pos_resultMin.m128_f32[3];

        Pos_xxxx = MathSIMD::VectorReplicate(preBoxMax.x);
        Pos_yyyy = MathSIMD::VectorReplicate(preBoxMax.y);
        Pos_zzzz = MathSIMD::VectorReplicate(preBoxMax.z);
        SIMDVector4 Pos_resultMax = MathSIMD::VectorMultiplyAdd(Pos_xxxx, M00, MathSIMD::VectorMultiplyAdd(Pos_yyyy, M10, MathSIMD::VectorMultiplyAdd(Pos_zzzz, M20, M30)));
        Float3 tempBoxMax = Float3(Pos_resultMax.m128_f32[0], Pos_resultMax.m128_f32[1], Pos_resultMax.m128_f32[2]) / Pos_resultMax.m128_f32[3];

        curBoxMin.x = std::min(curBoxMin.x, std::min(tempBoxMin.x, tempBoxMax.x));
        curBoxMin.y = std::min(curBoxMin.y, std::min(tempBoxMin.y, tempBoxMax.y));
        curBoxMin.z = std::min(curBoxMin.z, std::min(tempBoxMin.z, tempBoxMax.z));

        curBoxMax.x = std::max(curBoxMax.x, std::max(tempBoxMin.x, tempBoxMax.x));
        curBoxMax.y = std::max(curBoxMax.y, std::max(tempBoxMin.y, tempBoxMax.y));
        curBoxMax.z = std::max(curBoxMax.z, std::max(tempBoxMin.z, tempBoxMax.z));
    }
    // Refresh skeleton model aabb with the new max-min point
    BoundingBox curBoundingBox;
    BoundingBox::CreateFromPoints(curBoundingBox, curBoxMin, curBoxMax);
    auto* aabbSys = mGameWorld->GetGameSystem<AABBSystemG>();
    auto aabbComp = mGameWorld->GetComponent<AABBComponentG>(modelH.GetEntityID());

    auto preLocalBoudingBox = aabbSys->GetPreLocalAABB(aabbComp.Read());
    Float3 preLocalBoxMin;
    Float3 preLocalBoxMax;
    preLocalBoudingBox.GetMinMax(&preLocalBoxMin, &preLocalBoxMax);

    auto localBoudingBox = aabbSys->GetLocalAABB(aabbComp.Read());
    Float3 localBoxMin;
    Float3 localBoxMax;
    localBoudingBox.GetMinMax(&localBoxMin, &localBoxMax);

    if (Float3::Distance(curBoxMin, preLocalBoxMin) > 0.01 || Float3::Distance(curBoxMax, preLocalBoxMax) > 0.01)
    {
        aabbSys->SetPreLocalAABB(aabbComp.Write(), curBoundingBox);
        aabbSys->SetLocalAABB(aabbComp.Write(), curBoundingBox);
    }
    else
    {
        if (Float3::Distance(preBoxMin, localBoxMin) > 100.0 || Float3::Distance(preBoxMax, localBoxMax) > 100.0)
        {
            aabbSys->SetLocalAABB(aabbComp.Write(), preBoudingBox);
        }
    }

#endif
}

FrameVector<SIMDMatrix>* ModelSystemG::GetSkeltModelPose(const ModelComponentWriter& modelH, UInt32 modelIndex) const
{
    const auto& model = GetModel(modelH, modelIndex);

    Assert(model.mAsset->IsSkinValid());

    return model.mCompletePose;
}

void ModelSystemG::SetSkeltModelGPUSkin(const ModelComponentWriter& writer, bool enable)
{
    // if this is static mesh
    const auto& reader = ecs::GrantReadAccess(writer);
    bool globalGPUSkin = EngineGlobal::Inst().GetSettingMgr()->GetGPUSkinEnable();
    if (!IsSupportGPUSkin(reader))
    {
        writer->mEnableGPUSkin = false; 
        return;
    }
    enable = enable && globalGPUSkin;

    writer->mEnableGPUSkin = enable;

    for (UInt32 mi = 0; mi < GetModelCount(reader); ++mi)
    {
        auto& model = GetModel(reader, mi); 
        for (UInt32 lod = 0; lod < GetLODCount(reader, mi); ++lod)
        {
            UInt32 subModelCount = static_cast<UInt32>(model.mSingleLODModelProperties[lod].mSubModelProperties.size());
            for (UInt32 si = 0; si < subModelCount; ++si)
            {
                auto mat = GetModelLodMaterial(reader, si, lod, mi);
                mat->SetBool("GPU_SKIN", enable);
            }
        }
        //DispatchRenderingCommandWithToken([renderSystem = mRenderMeshSystem, entity = writer.GetEntityID(), mi, enable] { renderSystem->SetGPUSkin(entity, mi, enable); });
    }
    DispatchRenderingCommandWithToken([renderSystem = mRenderMeshSystem, entity = writer.GetEntityID()] { renderSystem->SetModelDirty(entity); });
}

bool ModelSystemG::GetSkeltModelGPUSkin(const ModelComponentReader& reader) const
{
    if (!IsSupportGPUSkin(reader))
    {
        return false;
    }
    /*
    * We don't check each materials' GPU_SKIN macro for efficiency and use reader->mEnableGPUSkin instead when running Release.
     */
    Assert((reader->mEnableGPUSkin && IsGPUSkinEnabledInternal(reader)) || !reader->mEnableGPUSkin);
    bool globalGPUSkin = EngineGlobal::Inst().GetSettingMgr()->GetGPUSkinEnable();
    return reader->mEnableGPUSkin && globalGPUSkin;
}

void ModelSystemG::SetIntersection(const ModelComponentWriter& modelH, bool enable) 
{
    modelH->mEnabledIntersection = enable;
}

bool ModelSystemG::GetIntersection(const ModelComponentReader& modelH) const 
{
    return modelH->mEnabledIntersection;
}

bool ModelSystemG::IsSupportGPUSkin(const ModelComponentReader& reader) const
{
    bool globalGPUSkin = EngineGlobal::Inst().GetSettingMgr()->GetGPUSkinEnable();
    if (!IsSkeletalModel(reader) || !globalGPUSkin)
    {
        return false;
    }

    for (UInt32 mi = 0; mi < GetModelCount(reader); ++mi)
    {
        auto& model = GetModel(reader, mi);
        for (UInt32 lod = 0; lod < GetLODCount(reader, mi); lod++)
        {
            UInt32 subModelCount = static_cast<UInt32>(model.mSingleLODModelProperties[lod].mSubModelProperties.size());

            for (UInt32 si = 0; si < subModelCount; ++si)
            {
                auto mat = GetModelLodMaterial(reader, si, lod, mi);
                if (mat && mat->GetFx())
                {
                    auto flag = mat->GetFx()->GetPropertyFlag("GPU_SKIN");
                    if (!(flag & resource::Fx::Shader_Macro))
                    {
                        return false;
                    }
                }
            }
        }
    }
    return true;
}

bool ModelSystemG::IsGPUSkinEnabledInternal(const ModelComponentReader& reader) const 
{
    for (UInt32 mi = 0; mi < GetModelCount(reader); ++mi)
    {
        auto& model = GetModel(reader, mi);
        for (UInt32 lod = 0; lod < GetLODCount(reader, mi); lod++)
        {
            UInt32 subModelCount = static_cast<UInt32>(model.mSingleLODModelProperties[lod].mSubModelProperties.size());
            for (UInt32 si = 0; si < subModelCount; ++si)
            {
                auto mat = GetModelLodMaterial(reader, si, lod, mi);
                if (mat && mat->GetFx())
                {
                    auto flag = mat->GetFx()->GetPropertyFlag("GPU_SKIN");
                    if (!(flag & resource::Fx::Shader_Macro))
                    {
                        return false;
                    }
                    auto hasMacro = std::get_if<bool>(mat->GetProperty("GPU_SKIN"));
                    if (!(hasMacro && *hasMacro))
                    {
                        return false;
                    }
                }
            }
        }
    }
    return true;
}

void OnMaterialSet(MaterialInterfacePtr material, bool enableGPUSkin)
{
    Assert(material);
    Assert(material->GetRenderMaterial());

    auto flag = material->GetFx()->GetPropertyFlag("GPU_SKIN");
    if ((flag & resource::Fx::Shader_Macro))
    {
        material->SetBool("GPU_SKIN", enableGPUSkin);
    }
}

void ModelSystemG::SetModelLodMaterial(const ModelComponentWriter& modelH, MaterialInterfacePtr material, UInt32 subModelIndex, UInt32 lodIndex, UInt32 modelIndex)
{
    auto& singleLODProperties = GetModel(modelH, modelIndex).mSingleLODModelProperties;

    if (singleLODProperties.size() <= lodIndex)
    {
        singleLODProperties.resize(lodIndex + 1);
    }

    auto& subModelProperties = singleLODProperties[lodIndex].mSubModelProperties;
    if (subModelIndex >= 0)
    {
        Assert(subModelIndex < subModelProperties.size());
        subModelProperties[subModelIndex].mMaterial = material;
        if (material->GetAsset())
        {
            subModelProperties[subModelIndex].mMaterialPath = material->GetGuid_Str();
        }
    }
    else
    {
        for (auto& prop : subModelProperties)
        {
            prop.mMaterial = material;
            if (material->GetAsset())
            {
                prop.mMaterialPath = material->GetGuid_Str();
            }
        }
    }

    DispatchRenderingCommandWithToken([renderSystem = mRenderMeshSystem, entity = modelH.GetEntityID(), material = TYPE_CAST(MaterialR*, material->GetRenderMaterial()), subModelIndex, lodIndex, modelIndex]{
        Assert(material);
        renderSystem->SetModelLodMaterial(entity, material, subModelIndex, lodIndex, modelIndex);
    });
}

bool ModelSystemG::SetModelLodMaterialPath(const ModelComponentWriter& modelH, const std::string& materialPath, UInt32 subModelIndex, UInt32 lodIndex, UInt32 modelIndex)
{
    auto materialResource = LoadMaterialAsset(materialPath);
    if (!materialResource.get())
        return false;
    if (lodIndex >= GetModel(modelH, modelIndex).mAsset->GetAssetData()->GetLodCount())
        return false;

    SetModelLodMaterial(modelH, materialResource, subModelIndex, lodIndex, modelIndex);
    return true;
}

int ModelSystemG::AddModelByAsset(const ModelComponentWriter& modelH, const MeshAssetDataResourcePtr& meshAssetDataResourcePtr)
{
    if (modelH->mMainModel.mAsset->IsSkinValid() != meshAssetDataResourcePtr->IsSkinValid())
        return -1;

    modelH->mChangeableModels.push_back({});
    if (SetModelAsset(modelH, meshAssetDataResourcePtr, GetModelCount(ecs::GrantReadAccess(modelH)) - 1))
    {
        return (int)modelH->mChangeableModels.size();
    }
    else
    {
        modelH->mChangeableModels.pop_back();
        return -1;
    }
}

int ModelSystemG::AddModelByAssetPath(const ModelComponentWriter& modelH, const std::string& assetPath)
{
    modelH->mChangeableModels.push_back({});
    if (SetModelAssetPath(modelH, assetPath, GetModelCount(ecs::GrantReadAccess(modelH)) - 1))
    {
        return (int)modelH->mChangeableModels.size();
    }
    else
    {
        modelH->mChangeableModels.pop_back();
        return -1;
    }
}

void ModelSystemG::RemoveModel(const ModelComponentWriter& modelH, UInt32 modelIndex)
{
    Assert(modelIndex > 0 && modelH->mChangeableModels.size() >= modelIndex);
    modelH->mChangeableModels.erase(modelH->mChangeableModels.begin() + modelIndex - 1);
    mModelChangeList.EmplaceChangeData(modelH.GetEntityID());
    DispatchRenderingCommandWithToken([renderSystem = mRenderMeshSystem, entity = modelH.GetEntityID(), modelIndex]() { renderSystem->RemoveModel(entity, modelIndex); });
    {
        ModelChangeEvent event;
        event.mData.mEntity = modelH.GetEntityID();
        event.mData.mModelIndex = modelIndex;
        event.mData.mResourcePtr.reset();
        DispatchImmediateEvent<ModelChangeEvent>(event);
    }
}

void ModelSystemG::SetModelMaterialFloat(const ModelComponentReader& modelH, int subModelIndex, UInt32 modelIndex, const std::string& propName, float propValue)
{
    auto& subModelProperties = GetModel(modelH, modelIndex).mSingleLODModelProperties[0].mSubModelProperties;
    if (subModelIndex > -1)
    {
        Assert(subModelIndex < subModelProperties.size());
        subModelProperties[subModelIndex].mMaterial->SetFloat(NameID(propName), propValue);
    }
}

float ModelSystemG::GetModelMaterialFloat(const ModelComponentReader& modelH, int subModelIndex, UInt32 modelIndex, const std::string& propName) 
{
    auto& subModelProperties = GetModel(modelH, modelIndex).mSingleLODModelProperties[0].mSubModelProperties;
    if (subModelIndex > -1)
    {
        Assert(subModelIndex < subModelProperties.size());
        {
            auto prop = subModelProperties[subModelIndex].mMaterial->GetProperty(NameID(propName));
            if (auto v = std::get_if<std::vector<float>>(prop))
            {
                return (*v)[0];
            }
        }
    }
    return -1.0f;
}

std::vector<float> ModelSystemG::GetModelMaterialVectorFloat(const ModelComponentReader& modelH, int subModelIndex, UInt32 modelIndex, const std::string& propName)
{
    auto& subModelProperties = GetModel(modelH, modelIndex).mSingleLODModelProperties[0].mSubModelProperties;
    if (subModelIndex > -1)
    {
        Assert(subModelIndex < subModelProperties.size());
        {
            auto prop = subModelProperties[subModelIndex].mMaterial->GetProperty(NameID(propName));
            if (auto v = std::get_if<std::vector<float>>(prop))
            {
                return std::move(*v);
            }
        }
    }
    return {};
}

void ModelSystemG::SetModelMaterialBool(const ModelComponentReader& modelH, int subModelIndex, UInt32 modelIndex, const std::string& propName, bool propValue, bool FX)
{
    auto& subModelProperties = GetModel(modelH, modelIndex).mSingleLODModelProperties[0].mSubModelProperties;
    if (subModelIndex > -1)
    {
        Assert(subModelIndex < subModelProperties.size());
        if (FX)
        {
            auto fx = subModelProperties[subModelIndex].mMaterial->GetFx();
            if (fx)
                fx->SetBool(NameID(propName), propValue);
        }
        else
        {
            subModelProperties[subModelIndex].mMaterial->SetBool(NameID(propName), propValue);
        }
    }
}

bool ModelSystemG::GetModelMaterialBool(const ModelComponentReader& modelH, int subModelIndex, UInt32 modelIndex, const std::string& propName, bool FX) 
{
    auto& subModelProperties = GetModel(modelH, modelIndex).mSingleLODModelProperties[0].mSubModelProperties;
    if (subModelIndex > -1)
    {
        Assert(subModelIndex < subModelProperties.size());
        if (FX)
        {
            auto fx = subModelProperties[subModelIndex].mMaterial->GetFx();
            if (fx)
            {
                auto prop = fx->GetProperty(NameID(propName));
                if (auto v = std::get_if<1>(prop))
                {
                    return *v;
                }
            }
        }
        else
        {
            auto prop = subModelProperties[subModelIndex].mMaterial->GetProperty(NameID(propName));
            if (auto v = std::get_if<1>(prop))
            {
                return *v;
            }
        }
    }
    return false;
}

int ModelSystemG::GetModelIndex(const ModelComponentReader& modelH, const std::string& modelName) const
{
    // TODO? UniqueString
    for (UInt32 i = 0; i < GetModelCount(modelH); ++i)
    {
        if (GetModel(modelH, i).mAsset->GetName() == modelName)
        {
            return (int)i;
        }
    }
    return -1;
}

int ModelSystemG::GetSubModelIndex(const ModelComponentReader& modelH, const std::string& subModelName, UInt32 modelIndex) const
{
    // TODO? UniqueString
    auto& model = GetModel(modelH, modelIndex);
    const MeshAssetData* assetData = model.mAsset->GetAssetData();
    for (UInt32 i = 0; i < assetData->GetMeshPartCount(0); ++i)
    {
        if (!assetData->GetMeshPartName(i))
            continue;
        if (subModelName == (*assetData->GetMeshPartName(i)))
        {
            return (int)i;
        }
    }
    return -1;
}

std::string ModelSystemG::GetModelName(const ModelComponentReader& modelH, UInt32 modelIndex) const
{
    auto& model = GetModel(modelH, modelIndex);
    return model.mAsset->GetName();
}

std::string ModelSystemG::GetSubModelName(const ModelComponentReader& modelH, UInt32 subModelIndex, UInt32 modelIndex) const
{
    auto& model = GetModel(modelH, modelIndex);
    Assert(model.mSingleLODModelProperties.size() > 0 && subModelIndex < (UInt32)model.mSingleLODModelProperties[0].mSubModelProperties.size());
    auto* name = model.mAsset->GetAssetData()->GetMeshPartName(subModelIndex);
    if (!name)
        return "";
    return *name;
}


MaterialInterfacePtr ModelSystemG::GetModelLodMaterial(const ModelComponentReader& modelH, UInt32 subModelIndex, UInt32 lodIndex, UInt32 modelIndex) const
{
    auto& model = GetModel(modelH, modelIndex);
    if (model.mAsset)
    {
        Assert(model.mSingleLODModelProperties.size() > lodIndex && subModelIndex < (UInt32)model.mSingleLODModelProperties[lodIndex].mSubModelProperties.size());
        if (lodIndex >= model.mSingleLODModelProperties.size() || subModelIndex >= model.mSingleLODModelProperties[lodIndex].mSubModelProperties.size() || !model.mSingleLODModelProperties[lodIndex].mSubModelProperties[subModelIndex].mMaterial)
        {
            return model.mSingleLODModelProperties[0].mSubModelProperties[subModelIndex].mMaterial;
        }
        return model.mSingleLODModelProperties[lodIndex].mSubModelProperties[subModelIndex].mMaterial;
    }
    else
    {
        return MaterialInterfacePtr();
    }
}

std::vector<MaterialInterfacePtr> ModelSystemG::GetModelLodMaterialPtr(const ModelComponentReader& modelH, UInt32 subModelIndex, UInt32 modelIndex) const
{
    auto& model = GetModel(modelH, modelIndex);
    if (model.mAsset)
    {
        Assert(model.mSingleLODModelProperties.size() > 0ull && subModelIndex < (UInt32)model.mSingleLODModelProperties[0].mSubModelProperties.size());
        std::vector<MaterialInterfacePtr> lodMaterial;
        for (auto const& singleLODModelProperty : model.mSingleLODModelProperties)
        {
            lodMaterial.emplace_back(singleLODModelProperty.mSubModelProperties[subModelIndex].mMaterial);
        }
        return lodMaterial;
    }
    else
    {
        std::vector<MaterialInterfacePtr> lodPtr;
        return lodPtr;
    }
}

bool ModelSystemG::IsModelVisible(const ModelComponentReader& modelH, UInt32 modelIndex) const
{
    return GetModel(modelH, modelIndex).mVisible;
}

bool ModelSystemG::IsSubModelVisible(const ModelComponentReader& modelH, int subModelIndex, UInt32 modelIndex, UInt32 lodindex) const
{
    auto& model = GetModel(modelH, modelIndex);
    Assert(model.mSingleLODModelProperties.size() > lodindex && subModelIndex < static_cast<int>(model.mSingleLODModelProperties[lodindex].mSubModelProperties.size()));
    return model.mSingleLODModelProperties[lodindex].mSubModelProperties[subModelIndex].mVisible;
}

MeshBatchInfo ModelSystemG::GenerateBatchInfo(MeshAssetDataResourcePtr inMeshResource)
{
    static MeshBatchInfo sDefaultBatchInfo((MeshBatchFlag)(MESH_BATCH_STATIC | MESH_BATCH_DO_NOT_MERGE | MESH_BATCH_INSTANCING), 0, MESH_BUILD_POLICY_DEFAULT);
    static MeshBatchInfo sDefaultSkinnedMeshBatchInfo((MeshBatchFlag)(MESH_BATCH_SKIN | MESH_BATCH_DO_NOT_MERGE | MESH_BATCH_INSTANCING), 0, MESH_BUILD_POLICY_DEFAULT);

    if (inMeshResource && inMeshResource->IsSkinValid())
        return sDefaultSkinnedMeshBatchInfo;
    else
        return sDefaultBatchInfo;
}

BoundingBox ModelSystemG::GetCurrentBoundingBox(const ModelComponentReader& modelH) const
{
    BoundingBox box = GetModel(modelH, 0).mAsset->GetAssetData()->GetBoundingBox();
    UInt32 modelCount = GetModelCount(modelH);
    for (UInt32 i = 1; i < modelCount; ++i)
    {
        auto& model = GetModel(modelH, i);
        if (model.mVisible && model.mAsset)
        {
            BoundingBox::CreateMerged(box, box, model.mAsset->GetAssetData()->GetBoundingBox());
        }
    }
    return box;
}

void ModelSystemG::OnMeshChanged(MeshAssetDataResourcePtr meshAsset)
{
    if (mGameWorld->GetEnable())
    {
        auto transSys = mGameWorld->GetGameSystem<TransformSystemG>();
        transSys->TraverseHierarchyDepth(transSys->GetRootEntity(), [this, meshAsset](ecs::EntityID entity) {
            if (mGameWorld->IsEntityAlive(entity))
            {
                auto modelComp = mGameWorld->GetComponent<ModelComponentG>(entity);
                if (modelComp.IsValid())
                {
                    const UInt32 modelCount = GetModelCount(modelComp.Read());
                    for (UInt32 modelIdx = 0; modelIdx < modelCount; ++modelIdx)
                    {
                        auto& model = GetModel(modelComp.Write(), modelIdx);
                        if (model.mAsset == meshAsset)
                        {
                            SetModelAsset(modelComp.Write(), meshAsset, modelIdx);
                        }
                    }
                }
            }
        });
    }
}

bool ModelSystemG::IsSubModelHasBlendShape(const ModelComponentReader& modelH, UInt32 modelIndex, UInt32 subModelIndex) const
{
    const auto& model = GetModel(modelH, modelIndex);
    Assert(model.mSingleLODModelProperties.size() > 0ull && subModelIndex < (UInt32)model.mSingleLODModelProperties[0].mSubModelProperties.size());
    return model.mSingleLODModelProperties[0].mSubModelProperties[subModelIndex].mHasBlendShape;
}

bool ModelSystemG::IsSubModelHasBlendShape(const ModelComponentWriter& modelH, UInt32 modelIndex, UInt32 subModelIndex) const
{
    const auto& model = GetModel(modelH, modelIndex);
    Assert(model.mSingleLODModelProperties.size() > 0ull && subModelIndex < (UInt32)model.mSingleLODModelProperties[0].mSubModelProperties.size());
    return model.mSingleLODModelProperties[0].mSubModelProperties[subModelIndex].mHasBlendShape;
}

bool ModelSystemG::SetSubModelBlendShapeChannelWeight(const ModelComponentWriter& modelH, UInt32 modelIndex, UInt32 subModelIndex, HashString channelName, float weight, UInt32 lodIndex)
{
    if (!IsSubModelHasBlendShape(modelH, modelIndex, subModelIndex))
    {
        return false;
    }

    auto& model = GetModel(modelH, modelIndex);

    const auto lodCount = model.mAsset->GetAssetData()->GetLodCount();
    if (lodIndex >= lodCount)
    {
        return false;
    }

    auto& curLodShapeChannelWeight = model.mSingleLODModelProperties[lodIndex].mSubModelProperties[subModelIndex].mChannelWeight;
    auto res = curLodShapeChannelWeight.find(channelName);
    if (res == curLodShapeChannelWeight.end())
    {
        return false;
    }

    if (res->second != weight)
    {
        res->second = MathUtils::Clamp<float>(weight, 0.0f, 1.0f);
        DispatchRenderingCommandWithToken([renderSystem = mRenderMeshSystem, entity = modelH.GetEntityID(), modelIndex, subModelIndex, channelName, weight, lodIndex]() 
        {
            if (!renderSystem->SetSubModelBlendShapeChannelWeight(entity, modelIndex, subModelIndex, channelName, weight, lodIndex))
            {
                LOG_ERROR("Set blend shape channel weight for channel {} failed", channelName.GetCString());
            }
        });
    }

    return true;
}

float ModelSystemG::GetSubModelBlendShapeChannelWeight(const ModelComponentReader& modelH, UInt32 modelIndex, UInt32 subModelIndex, HashString channelName, UInt32 lodIndex) const
{
    if (!IsSubModelHasBlendShape(modelH, modelIndex, subModelIndex))
    {
        return -1.0f;
    }

    auto& model = GetModel(modelH, modelIndex);

    const auto lodCount = model.mAsset->GetAssetData()->GetLodCount();
    if (lodIndex >= lodCount)
    {
        return -1.0f;
    }

    auto& curLodShapeChannelWeight = model.mSingleLODModelProperties[lodIndex].mSubModelProperties[subModelIndex].mChannelWeight;
    auto res = curLodShapeChannelWeight.find(channelName);
    if (res == curLodShapeChannelWeight.end())
    {
        return -1.0f;
    }

    return res->second;
}

const NameMap<HashString, UInt32>& ModelSystemG::GetSubModelBlendShapeChannelNameMap(const ModelComponentReader& modelH, UInt32 modelIndex, UInt32 subModelIndex, UInt32 lodIndex) const
{
    const auto& model = GetModel(modelH, modelIndex);
    const auto* meshAsset = model.mAsset->GetAssetData();
    const auto& lodStartIndexArray = meshAsset->GetLodStartIndex();
    const auto& meshPart = meshAsset->GetMeshPartInfo(lodStartIndexArray[lodIndex] + subModelIndex);
    return meshPart.mBlendShape.ChannelNameToIndexMap;
}

bool ModelSystemG::IsModelHasBlendShape(const ModelComponentReader& modelH, UInt32 modelIndex) const
{
    return GetModel(modelH, modelIndex).mHasBlendShape;
}

UInt32 ModelSystemG::GetModelBlendShapeChannelCount(const ModelComponentReader& modelH, UInt32 modelIndex)
{
    auto& nameMap = GetModelBlendShapeChannelMap(modelH, modelIndex);
    return static_cast<UInt32>(nameMap.size());
}

void ModelSystemG::GetModelBlendShapeChannelNames(const ModelComponentReader& modelH, UInt32 modelIndex, std::vector<std::string>& OutNames) 
{
    auto& nameMap = GetModelBlendShapeChannelMap(modelH, modelIndex);
    auto mapIter = nameMap.begin();
    OutNames.clear();
    for (int i = 0; i < nameMap.size(); ++i)
    {
        OutNames.push_back(std::string(mapIter->first.GetCString()));
        mapIter++;
    }
}

const NameMap<HashString, ChannelCollectInfo>& ModelSystemG::GetModelBlendShapeChannelMap(const ModelComponentReader& modelH, UInt32 modelIndex, UInt32 lodIndex) const
{
    const auto& model = GetModel(modelH, modelIndex);
    const auto* meshAsset = model.mAsset->GetAssetData();
    return meshAsset->GetBlendShapeChannelMap(lodIndex);
}

bool ModelSystemG::SetModelBlendShapeChannelWeight(const ModelComponentWriter& modelH, UInt32 modelIndex, HashString channelName, float weight, UInt32 lodIndex)
{
    const auto& model = GetModel(modelH, modelIndex);
    const auto* meshAsset = model.mAsset->GetAssetData();
    const auto& blendShapeChannelMap = meshAsset->GetBlendShapeChannelMap(lodIndex);

    auto result = blendShapeChannelMap.find(channelName);
    if (result != blendShapeChannelMap.end())
    {
        const auto& channelCollectInfo = result->second;
        for (const auto& pair : channelCollectInfo)
        {
            if (!SetSubModelBlendShapeChannelWeight(modelH, modelIndex, pair.first, channelName, weight, lodIndex))
            {
                return false;
            }
        }
        return true;
    }

    return false;
}

void ModelSystemG::GetModelAssetPath(const ModelComponentReader& modelH, UInt32 modelIndex, std::string& Result) 
{
    auto asset = GetModelAsset(modelH, (UInt32)modelIndex);
    if (asset)
    {
        if (asset->GetName().size())
        {
            Result = asset->GetGuid_Str();
        }
    }
}

std::string ModelSystemG::GetModelMaterialPath(const ModelComponentReader& modelH, UInt32 modelIndex, UInt32 subModelIndex, UInt32 lodindex) 
{
    std::string Result;
    auto material = GetModelLodMaterial(modelH, (UInt32)subModelIndex, lodindex, (UInt32)modelIndex);
    if (material)
    {
        auto&& path = material->GetName();

        if (path.size())
        {
            Result = path;
        }
    }
    return Result;
}

float ModelSystemG::GetModelBlendShapeChannelWeight(const ModelComponentReader& modelH, UInt32 modelIndex, HashString channelName, UInt32 lodIndex) const
{
    const auto& model = GetModel(modelH, modelIndex);
    const auto* meshAsset = model.mAsset->GetAssetData();
    const auto& blendShapeChannelMap = meshAsset->GetBlendShapeChannelMap(lodIndex);

    auto result = blendShapeChannelMap.find(channelName);
    if (result != blendShapeChannelMap.end())
    {
        const auto& channelCollectInfo = result->second;
        if (!channelCollectInfo.empty())
        {
            return GetSubModelBlendShapeChannelWeight(modelH, modelIndex, channelCollectInfo[0].first, channelName, lodIndex);
        }

        return -1.0f;
    }

    return -1.0f;
}

void ModelSystemG::SetAutoBlendShapeByAnimation(ecs::EntityID entity, bool autoBlendShape)
{
    auto modelComp = mGameWorld->GetComponent<ModelComponentG>(entity);
    const UInt32 modelCount = GetModelCount(modelComp.Read());
    for (UInt32 modelIdx = 0; modelIdx < modelCount; ++modelIdx)
    {
        auto& model = GetModel(modelComp.Write(), modelIdx);
        model.mAutoBlendShapeByAnimation = autoBlendShape;
    }
}

bool ModelSystemG::GetAutoBlendShapeByAnimation(ecs::EntityID entity) const
{
    auto modelComp = mGameWorld->GetComponent<ModelComponentG>(entity);
    const UInt32 modelCount = GetModelCount(modelComp.Read());
    if(modelCount > 0)
    {
        return GetModel(modelComp.Read(), 0U).mAutoBlendShapeByAnimation;
    }

    return false;
}

void ModelSystemG::SetModelEnityDistanceCulling(const ModelComponentWriter& modelH, const EntityDistanceCulling& entityCulling)
{
    modelH->mDistanceCulling = entityCulling;
    DispatchRenderingCommandWithToken([renderSystem = mRenderMeshSystem, entity = modelH.GetEntityID(), entityCulling]()
        {
            renderSystem->SetModelEnityDistanceCulling(entity, entityCulling);   // Must set before model
        });

}

EntityDistanceCulling ModelSystemG::GetModelEnityDistanceCulling(const ModelComponentReader& model)
{
    return model->mDistanceCulling;
}

bool ModelSystemG::IsModelAssetStreamable(const ModelComponentReader& modelH, UInt32 modelIndex) const
{
    const auto& meshAssetDataRes = GetModelAsset(modelH, modelIndex);
    return meshAssetDataRes && meshAssetDataRes->IsMeshAssetStreamable();
}

void ModelSystemG::SetModelAssetStreamable(const ModelComponentWriter& modelH, UInt32 modelIndex, bool enabled)
{
    auto& meshAssetDataRes = GetModel(modelH, modelIndex).mAsset;
    if (meshAssetDataRes)
    {
        meshAssetDataRes->SetMeshAssetStreamable(enabled);
    }
}

}   // namespace cross
