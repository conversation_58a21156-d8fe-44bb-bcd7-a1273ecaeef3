#include "EnginePrefix.h"
#include "CrossBase/Template/TypeCast.hpp"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Common/FrameContainer.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Common/SettingsManager.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/PivotSystemG.h"

#include "Runtime/GameWorld/TransformSystemG.h"
#include "Threading/RenderingThread.h"
#include "Resource/AssetStreaming.h"

using namespace cross::skeleton;

namespace cross {

ecs::ComponentDesc* PivotComponentG::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::PivotComponentG>(
        { false, true, true }, &PivotSystemG::SerializePivotComponent, &PivotSystemG::DeserializePivotComponent, &PivotSystemG::PostDeserializePivotComponent);
}

PivotSystemG* PivotSystemG::CreateInstance()
{
    return new PivotSystemG();
}

RenderSystemBase* PivotSystemG::GetRenderSystem()
{
    return nullptr;
}

PivotSystemG::PivotSystemG()
{
}

PivotSystemG::~PivotSystemG()
{
}

void PivotSystemG::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    GameSystemBase::NotifyEvent(event, flag);
}

void PivotSystemG::Release()
{
    delete this;
}

void PivotSystemG::NotifyAddRenderSystemToRenderWorld()
{
}

void PivotSystemG::OnBuildUpdateTasks(FrameParam* frameParam)
{
    CreateTaskFunction(FrameTickStage::Update, {}, [this] {
    });
}

SerializeNode PivotSystemG::SerializePivotComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    auto comp = static_cast<PivotComponentG*>(componentPtr);
    SerializeContext context;
    SerializeNode json;
    json = comp->Serialize(context);
    return json;
}

void PivotSystemG::DeserializePivotComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr)
{
    if (json.IsNull())
    {
        return;
    }
    auto cmpntPtr = static_cast<PivotComponentG*>(componentPtr);
    SerializeContext context;
    cmpntPtr->Deserialize(json, context);
}

void PivotSystemG::PostDeserializePivotComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId) {}

void PivotSystemG::OnFirstUpdate(FrameParam* frameParam)
{
}

}   // namespace cross
