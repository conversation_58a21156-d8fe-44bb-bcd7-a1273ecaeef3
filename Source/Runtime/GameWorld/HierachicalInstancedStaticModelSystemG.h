#pragma once
#include "InstancedStaticModelSystemG.h"
#include "RenderEngine/HierachicalInstancedStaticModelSystemR.h"

namespace cross {
struct HierachicalInstancedStaticModelComponentG : ecs::IComponent
{
    CEFunction(Reflect)
    static ENGINE_API ecs::ComponentDesc* GetDesc();

    CEProperty()
    int mPartitionCount = 1;

    CEProperty()
    float mDensityLodDistanceScalar = 8;

public:
    bool m_Test;

    CE_Serialize_Deserialize;
};

class ModelSystemR;
class ENGINE_API CEMeta(Cli) HierachicalInstancedStaticModelSystemG : public GameSystemBase
{
    CESystemInternal(ComponentType = HierachicalInstancedStaticModelComponentG)
public:
    using HierachicalInstancedStaticModelComponentHandle = ecs::ComponentHandle<HierachicalInstancedStaticModelComponentG>;
    DEFINE_COMPONENT_READER_WRITER(HierachicalInstancedStaticModelComponentG, HierachicalInstancedStaticModelComponentReader, HierachicalInstancedStaticModelComponentWriter)

    HierachicalInstancedStaticModelSystemG();

    virtual void Release() override;

    virtual RenderSystemBase* GetRenderSystem() override;

    virtual void NotifyAddRenderSystemToRenderWorld() override
    {
        mIsRenderObjectOwner = false;
    };

public:
    CEFunction(Reflect)
    static HierachicalInstancedStaticModelSystemG* CreateInstance();

    static SerializeNode SerializeHierachicalInstancedStaticModelComponent(ISerializeWorld * serializeWorld, ecs::IComponent * componentPtr);

    static void DeserializeHierachicalInstancedStaticModelComponent(ISerializeWorld * serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);

    static void PostDeserializeHierachicalInstancedStaticModelComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);

    static void UpdateDeserializeHierachicalInstancedStaticModelComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);

    static void GetResourceHierachicalInstancedStaticModelComponent(ISerializeWorld * serializeWorld, ecs::IComponent * componentPtr, ResourcePtr resource);

    CEFunction(Editor, Script)
    void SetPartitionCount(const HierachicalInstancedStaticModelComponentWriter& writer, int count, bool needRebuild);

    CEFunction(Editor, Script)
    int GetPartitionCount(const HierachicalInstancedStaticModelComponentReader& reader)
    {
        return reader->mPartitionCount;
    }

    CEFunction(Editor, Script)
    void SetDensityLodDistanceScalar(const HierachicalInstancedStaticModelComponentWriter& writer, float value);

    CEFunction(Editor, Script)
    float GetDensityLodDistanceScalar(const HierachicalInstancedStaticModelComponentReader& reader)
    {
        return reader->mDensityLodDistanceScalar;
    }

protected:
    virtual ~HierachicalInstancedStaticModelSystemG();

private:
    HierachicalInstancedStaticModelSystemR* mRenderSystem = nullptr;
    bool mIsRenderObjectOwner = true;
};

}   // namespace cross
