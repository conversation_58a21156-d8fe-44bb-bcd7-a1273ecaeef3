#include "EnginePrefix.h"
#include "Runtime/Animation/Sequence/LinearReductionSeq.h"
#include "CrossBase/Math/CrossMath.h"
#include "CEAnimation/AnimRuntime.h"

namespace cross::anim
{
	/////////////////////////////////////////////
	// LinearReductionSeq
	//
	/////////////////////////////////////////////
	NodeTransform LinearReductionSeq::ExtractRootMotionFromDeltaTime(const RawH& startPos, float deltaTime, bool bAllowLoop) const
	{
		RootMotionParams rootMotionParam;

		if (deltaTime != 0.f)
		{
			const bool bPlayBwd = (deltaTime < 0.f);

            RawH prevPos = {std::clamp(startPos.mVal, 0.f, GetRunLength().mVal)};
			RawH curPos = startPos;
			float desiredDelta = deltaTime;

			do
			{
				// disable looping here. Advance to desired position, or beginning / end of animation 
				const AdvanceAnim::Type advanceType = AnimRuntime::AdvanceTime(false, desiredDelta, curPos, GetRunLength());

				rootMotionParam.Accumulate(ExtractRootMotionFromRange(prevPos, curPos));

				// If we've hit the end of the animation, and we're allowed to loop, keep going.
				if ((advanceType == AdvanceAnim::AA_Finished) && bAllowLoop)
				{
					const float actualDelta = curPos - prevPos;
					desiredDelta -= actualDelta;

                    prevPos = bPlayBwd ? GetRunLength() : RawH::From(0.f);
                    curPos = prevPos;
                }
                else
                {
                    break;
                }
            } while (true);
        }
        return rootMotionParam.GetRootMotionTransform();
    }

	NodeTransform LinearReductionSeq::ExtractRootMotionFromRange(const RawH& startPos, const RawH& endPos) const
	{
		const bool bSampleBwd = (startPos > endPos);

		NodeTransform outRootMotionTransform;
		mSampler->Interpolate(outRootMotionTransform, startPos, endPos,
			(bSampleBwd ? IAnimSampler::SampleDirection::Backward : IAnimSampler::SampleDirection::Forward));

		return outRootMotionTransform;
	}

    void LinearReductionSeq::GetBoneTransform(NodeTransform& outBoneTrans, SkBoneHandle boneIndex, const float currentTime) const 
    {
        return mSampler->GetBoneTransform(outBoneTrans, boneIndex, currentTime); 
    }

    void LinearReductionSeq::GetAnimationFullPose(RootSpacePose& outPose, AnimExtractContext<RawH>& extractContext) const
    {
        // consider bone requirement later
        mSampler->Interpolate(outPose, extractContext.CurrentTime(), static_cast<IAnimSampler::SampleDirection>(extractContext.PlayDirection));

        // once pose has been extracted, snap root bone to RootLockTransform if we have extracted root motion.
        if (extractContext.CanExtractRootMotion() && HasRootMotion())
        {
            outPose[POSE_BONE_INDEX_ROOT] = mSampler->GetRootLockTransform();
        }
    }

    /////////////////////////////////////////////
    // LinearKeyReductionSampler
    //
    /////////////////////////////////////////////
    LinearKeyReductionSampler::LinearKeyReductionSampler(const LinearReductionSeq* inAnimPtr, const LinearReductionStreamableData* inStreamableDataPtr)
        : mTranslationKeys(inStreamableDataPtr->mTranslationKeys)
        , mRotationKeys(inStreamableDataPtr->mRotationKeys)
        , mScaleKeys(inStreamableDataPtr->mScaleKeys)
        , mTranslationKeysBwd(inStreamableDataPtr->mTranslationKeysBwd)
        , mRotationKeysBwd(inStreamableDataPtr->mRotationKeysBwd)
        , mScaleKeysBwd(inStreamableDataPtr->mScaleKeysBwd)
    {
        Assert(inAnimPtr != nullptr);
        mAnimPtr = inAnimPtr;

        // initialize
        Initialize();
    }

	void LinearKeyReductionSampler::Initialize()
	{
		// initialize SampleCache
		auto skeletonBoneNum = mAnimPtr->GetReferenceSkeleton().GetRawBoneNum();
		mTranslationSampleCache.Initialize(skeletonBoneNum, Float3A(0, 0, 0));
		mRotationSampleCache.Initialize(skeletonBoneNum, QuaternionA::Identity());
		mScaleSampleCache.Initialize(skeletonBoneNum, Float3A(1, 1, 1));

		// update SampleCache to anim first frame
		mCurSampleDirect = SampleDirection::Forward;
		UpdateSampleCache(0.f);

		// get root transform at anim first frame
		GetBoneTransform(mLastFrameRootTrans, SK_BONE_INDEX_ROOT, 0.f);
		mCurFrameRootTrans = mLastFrameRootTrans;

		// initialize RootLockTransform
		switch (mAnimPtr->mStreamingAnimDataPtr->RootLockType)
		{
		case RootMotionRootLock::AnimFirstFrame:
			mRootLockTransform = mCurFrameRootTrans;
			break;
		case RootMotionRootLock::Zero:
			mRootLockTransform = NodeTransform::Identity();
			break;
		default:
		case RootMotionRootLock::RefPose:
			mRootLockTransform =
			{
				mAnimPtr->mRefSkeleton.GetRawRefBonePoseScale()[0],
				mAnimPtr->mRefSkeleton.GetRawRefBonePoseRotate()[0],
				mAnimPtr->mRefSkeleton.GetRawRefBonePoseTranslate()[0]
			};
			break;
		}
	}

	template<typename T>
	void LinearKeyReductionSampler::SampleCache<T>::Initialize(size_t boneNumber, DataType initValue)
	{
		CachedData.resize(boneNumber, { initValue });
	}

	template<typename T>
	void LinearKeyReductionSampler::SampleCache<T>::UpdateCache(
		float currentTime,
		const std::vector <LRSD::PropertyKey<T>>& keys,
		const std::vector<LRSD::KeyForSampleBwd<T>>& keysBwd,
		IAnimSampler::SampleDirection sampleDirect)
	{
		if (sampleDirect == IAnimSampler::SampleDirection::Forward)
		{
			for (; Cursor < keys.size() && keys[Cursor].PrevTime <= currentTime; ++Cursor)
			{
				int boneIndex = keys[Cursor].BoneIndex;
				int& flop = CachedData[boneIndex].Flop; //flop is 0 or 1

				CachedData[boneIndex].InterpKeyValues[flop] = keys[Cursor].Value;
				CachedData[boneIndex].InterpKeyTimes[flop] = keys[Cursor].Time;
				flop ^= 1;
			}
		}
		else
		{
			for (; Cursor >= 0 && keysBwd[Cursor].NextTime >= currentTime; --Cursor)
			{
				int boneIndex = keysBwd[Cursor].BoneIndex;
				int& flop = CachedData[boneIndex].Flop; //flop is 0 or 1

				CachedData[boneIndex].InterpKeyValues[flop] = keys[keysBwd[Cursor].PropertyKeyIndex].Value;
				CachedData[boneIndex].InterpKeyTimes[flop] = keys[keysBwd[Cursor].PropertyKeyIndex].Time;
				flop ^= 1;
			}
		}
	}

	void LinearKeyReductionSampler::Interpolate(RootSpacePose& outPose, const UInt32 curFrameIndex, SampleDirection sampleDirect)
	{
		Interpolate(outPose, mAnimPtr->GetTimeAtFrame(curFrameIndex), sampleDirect);
	}

	void LinearKeyReductionSampler::Interpolate(RootSpacePose& outPose, const RawH& currentTime, SampleDirection sampleDirect)
	{
		auto curTime = Clamp<float>(currentTime.mVal, 0.0f, mAnimPtr->GetRunLength());

		InterpolateInternal(curTime, sampleDirect);

		// extract pose from Cache
		UpdateBoneLclProperty(outPose, curTime);
	}

	void LinearKeyReductionSampler::Interpolate(
		NodeTransform& outRootMotionTrans,
		const RawH& previousTime,
		const RawH& currentTime,
		SampleDirection sampleDirect)
	{
		float lastTime = Clamp<float>(previousTime.mVal, 0.0f, mAnimPtr->GetRunLength());
		float curTime = Clamp<float>(currentTime.mVal, 0.0f, mAnimPtr->GetRunLength());

		auto temp = NodeTransform(mRootLockTransform.GetRotation().Inverse());

		InterpolateInternal(lastTime, sampleDirect);
		GetBoneTransform(mLastFrameRootTrans, SK_BONE_INDEX_ROOT, lastTime);
		mLastFrameRootTrans = temp * mLastFrameRootTrans;

		InterpolateInternal(curTime, sampleDirect);
		GetBoneTransform(mCurFrameRootTrans, SK_BONE_INDEX_ROOT, curTime);
		mCurFrameRootTrans = temp * mCurFrameRootTrans;

		outRootMotionTrans  = NodeTransform::GetRelativeTransform(mLastFrameRootTrans, mCurFrameRootTrans);
	}

	void LinearKeyReductionSampler::InterpolateInternal(float curTime, SampleDirection sampleDirect)
	{
		// sample direction need to be contiguous for LinearKeyReductionSampler. 
		// when it changes, we need to restart cache.
		if ((mCurSampleDirect == SampleDirection::Forward && curTime < mCurSampleTime) ||
			(mCurSampleDirect == SampleDirection::Backward && curTime > mCurSampleTime))
		{
			// record new sampleDirection
			mCurSampleDirect = sampleDirect;
			// restart cache
			RestartSampleCache();
		}

		// when curTime != CurSampleTime, we need to update cache.
		if (mCurSampleTime != curTime)
		{
			// update cache
			UpdateSampleCache(curTime);
		}
	}

	void LinearKeyReductionSampler::RestartSampleCache()
	{
		if (mCurSampleDirect == SampleDirection::Forward)
		{
			mTranslationSampleCache.Cursor = mRotationSampleCache.Cursor = mScaleSampleCache.Cursor = 0;
		}
		else
		{
			mTranslationSampleCache.Cursor = static_cast<SInt32>(mTranslationKeys.size() - 1);
			mRotationSampleCache.Cursor = static_cast<SInt32>(mRotationKeys.size() - 1);
			mScaleSampleCache.Cursor = static_cast<SInt32>(mScaleKeys.size() - 1);
		}
	}

	void LinearKeyReductionSampler::UpdateSampleCache(float curTime)
	{
		// record sample time
		mCurSampleTime = curTime;

		mTranslationSampleCache.UpdateCache(curTime, mTranslationKeys, mTranslationKeysBwd, mCurSampleDirect);
		mRotationSampleCache.UpdateCache(curTime, mRotationKeys, mRotationKeysBwd, mCurSampleDirect);
		mScaleSampleCache.UpdateCache(curTime, mScaleKeys, mScaleKeysBwd, mCurSampleDirect);
	}

	void LinearKeyReductionSampler::UpdateBoneLclProperty(RootSpacePose& outPose, const float currentTime) const
	{
        if (mAnimPtr->mRunSkeltPtr == nullptr)
        {
            LOG_ERROR("RunSkeltPtr is null");
            return;
        }

        SkBoneHandle seqBonesNum = {  static_cast<UInt32>(mAnimPtr->GetReferenceSkeleton().GetRawBoneNum()) };

        const auto& runSkRefSkeleton = mAnimPtr->mRunSkeltPtr->GetReferenceSkeleton();
        // Grab runtime skeleton Ref-Pose translate
        const auto& runSkRefSkeltTranslateArray = runSkRefSkeleton.GetRawRefBonePoseTranslate();
        // Grab animation Ref-Pose translate
        const auto& animRefSkeltTranslateArray = mAnimPtr->mRefSkeleton.GetRawRefBonePoseTranslate();

		for (SkBoneHandle seq_bone_index = { 0 }; seq_bone_index < seqBonesNum; ++seq_bone_index)
		{
			// map animation bone into skeleton bone
			SkBoneHandle skel_bone_index = mAnimPtr->GetSkeletonIndexFromSeqSkeltIndex(seq_bone_index);

			// Not found run_skelt_bone in anim_skelt, continue
			if (skel_bone_index == SK_BONE_INDEX_NONE)
				continue;

			// map skeleton bone into FilteredPose Bone
			PoseBoneHandle pose_bone_index = outPose.GetPoseIndexFromFilteredBoneIndex(skel_bone_index);

			// Not found mapped filter_pose_bone in FilteredPose from run skeleton bone, continue
			if (pose_bone_index == POSE_BONE_INDEX_NONE)
				continue;

			GetBoneTransform(outPose[pose_bone_index], seq_bone_index, currentTime);

            switch (mAnimPtr->mRunSkeltPtr->GetBoneRetargetingMode(skel_bone_index))
            {
            case BoneTranslateRetargetingMode::AnimScaledBySkelt:
            {
                const float runSkeltBoneLength = runSkRefSkeltTranslateArray[skel_bone_index].XYZ().Length();
                const float animBoneLength = animRefSkeltTranslateArray[seq_bone_index].XYZ().Length();

                if (runSkeltBoneLength > 0.001f)
                {
                    outPose[pose_bone_index].SetTranslation(outPose[pose_bone_index].GetTranslation() * animBoneLength / runSkeltBoneLength);
                }
                break;
            }

            default:
                break;
            }
		}
	}

	void LinearKeyReductionSampler::GetBoneTransform(NodeTransform& outBoneTrans, SkBoneHandle boneIndex, const float currentTime) const
	{
		GetBoneTransform<LRSD::Translation>(outBoneTrans, boneIndex, currentTime);
		GetBoneTransform<LRSD::Rotation>(outBoneTrans, boneIndex, currentTime);
		GetBoneTransform<LRSD::Scale>(outBoneTrans, boneIndex, currentTime);
	}

	template<typename T>
	void LinearKeyReductionSampler::GetBoneTransform(NodeTransform& outBoneTrans, SkBoneHandle boneIndex, const float currentTime) const
	{
		using InterpDataType = typename SampleCache<T>::InterpData;

		std::vector<InterpDataType> const* sampleCacheData = nullptr;

		if constexpr (std::is_same_v<T, LRSD::Translation>)
			sampleCacheData = &(mTranslationSampleCache.CachedData);

		if constexpr (std::is_same_v<T, LRSD::Rotation>)
			sampleCacheData = &(mRotationSampleCache.CachedData);

		if constexpr (std::is_same_v<T, LRSD::Scale>)
			sampleCacheData = &(mScaleSampleCache.CachedData);

		auto const& curCacheData = (*sampleCacheData)[boneIndex];
		auto const& keyValueOne = curCacheData.InterpKeyValues[0];
		auto const& keyValueTwo = curCacheData.InterpKeyValues[1];

		float ratio = (curCacheData.InterpKeyTimes[0] - currentTime) / (curCacheData.InterpKeyTimes[0] - curCacheData.InterpKeyTimes[1]);

		if constexpr (std::is_same_v<T, LRSD::Translation>)
		{
			outBoneTrans.SetTranslation(keyValueOne * (1.0f - ratio) + keyValueTwo * ratio);
		}

		if constexpr (std::is_same_v<T, LRSD::Rotation>)
		{
			auto simdRot = NodeTransform::AccumulateQuaternionShortestPath(
				MathSIMD::VectorMultiply(SIMD::LoadQuaternionA(&keyValueOne), MathSIMD::VectorReplicate(1.0f - ratio)),
				SIMD::LoadQuaternionA(&keyValueTwo),
				MathSIMD::VectorReplicate(ratio));
			
			outBoneTrans.SetRotation(simdRot);
			outBoneTrans.NormalizeRotation();
		}

		if constexpr (std::is_same_v<T, LRSD::Scale>)
		{
			outBoneTrans.SetScale(keyValueOne * (1.0f - ratio) + keyValueTwo * ratio);
		}
	}
}
