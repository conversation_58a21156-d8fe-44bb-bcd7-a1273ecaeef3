#include "EnginePrefix.h"
#include "Resource/Animation/Composite/AnimatrixRes.h"
#include "CEAnimation/AnimRuntime.h"
#include "Runtime/Animation/Animator/Animator.h"
#include "Runtime/Animation/Composite/Animatrix.h"

namespace cross::anim
{
    /////////////////////////////////////////////
    // Animatrix
    //
    /////////////////////////////////////////////
    Animatrix::Animatrix(const AnimatrixResPtr& inAnimatrixResPtr)
        : AnimCompositeBase(inAnimatrixResPtr->mNotifyTracks, inAnimatrixResPtr->mCurveList)
        , mAnimatrixResPtr(inAnimatrixResPtr)
    {}

    bool Animatrix::InitializeSlot(CEName const& inSlotName, std::vector<AnimSeqPtr>& inRunAnims)
    {
        auto slotResPtr = mAnimatrixResPtr->GetValidatedSlotRes(inSlotName);
        if (slotResPtr == nullptr)
            return false;

        if (slotResPtr->IsAnySectionExists())
        {
            Assert(mSecTracks.find(slotResPtr->SlotName) == mSecTracks.end());
            mSecTracks.emplace(std::make_pair(slotResPtr->SlotName, AnimSectionedTrack()));

            auto& curTrack = mSecTracks.at(slotResPtr->SlotName);
            curTrack.Deserialize(*slotResPtr, inRunAnims, mNotifies);
        }
        else
        {
            Assert(mDefTracks.find(slotResPtr->SlotName) == mDefTracks.end());
            mDefTracks.emplace(std::make_pair(slotResPtr->SlotName, AnimTrack()));

            auto& curTrack = mDefTracks.at(slotResPtr->SlotName);
            curTrack.Deserialize(*slotResPtr, inRunAnims, mNotifies);
        }

        // initialize single slot track data
        for (const auto& seqPtr : inRunAnims) 
        {
            mRunAnims.emplace_back(seqPtr);
        }

        return true;
    }

    void Animatrix::GetPose(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext, const CEName& slotName) const
    {
        Assert(IsValidSlot(slotName) && "GetPose from for SlotName is invalid!");

        const AnimTrack* track = GetTrack(slotName);
        Assert(track != nullptr);

        track->GetPose(outPose, extractContext);
    }

    const CEName& Animatrix::GetGroupName() const
    {
        if (mAnimatrixResPtr != nullptr)
            return mAnimatrixResPtr->mGroupName;

        return AnimSlotGroup::sDefaultGroupName;
    }

    AnimTrack const* Animatrix::GetTrack(CEName const& inSlotName) const
    {
        auto itr_def = mDefTracks.find(inSlotName);
        if (itr_def != mDefTracks.end())
            return &itr_def->second;

        auto itr_sec = mSecTracks.find(inSlotName);
        if (itr_sec != mSecTracks.end())
            return &itr_sec->second;

        return nullptr;
    }

    bool Animatrix::IsSectionedTrack(CEName const& inSlotName) const
    {
        auto itr_def = mDefTracks.find(inSlotName);
        if (itr_def != mDefTracks.end())
            return false;

        auto itr_sec = mSecTracks.find(inSlotName);
        if (itr_sec != mSecTracks.end())
            return true;

        return false;
    }

    bool Animatrix::IsValidSlot(const CEName& inSlotName) const
    {
        return IsDefaultTrack(inSlotName) || IsSectionedTrack(inSlotName);
    }

    bool Animatrix::IsDefaultTrack(CEName const& inSlotName) const
    {
        auto itr_def = mDefTracks.find(inSlotName);
        if (itr_def != mDefTracks.end())
            return true;

        auto itr_sec = mSecTracks.find(inSlotName);
        if (itr_sec != mSecTracks.end())
            return false;

        return false;
    }

    bool Animatrix::AttachTo(const Skeleton* inSkeleton)
    {
        auto itr_def = std::find_if(mDefTracks.begin(), mDefTracks.end(), [inSkeleton](auto& elem) 
        {
            auto& track_def = elem.second;
            for (auto& seq : track_def.AnimSequences)
            {
                if (!seq->AttachTo(inSkeleton))
                    return true;
            }
            return false;
        });

        if (itr_def != mDefTracks.end())
            return false;

        auto itr_sec = std::find_if(mSecTracks.begin(), mSecTracks.end(), [inSkeleton](auto& elem) 
        {
            auto& track_sec = elem.second;
            for (auto& seq : track_sec.AnimSequences)
            {
                if (!seq->AttachTo(inSkeleton))
                    return true;
            }
            return false;
        });

        if (itr_sec != mSecTracks.end())
            return false;

        return true;
    }

    bool Animatrix::IsSkeletonAttached(Skeleton const* specifiedSkelt /*= nullptr*/) const
    {
        auto itr_def = std::find_if(mDefTracks.begin(), mDefTracks.end(), [specifiedSkelt](auto& elem) 
        {
            auto& track_def = elem.second;
            for (auto& seq : track_def.AnimSequences)
            {
                if (!seq->IsSkeletonAttached(specifiedSkelt))
                    return true;
            }
            return false;
        });

        if (itr_def != mDefTracks.end())
            return false;

        auto itr_sec = std::find_if(mSecTracks.begin(), mSecTracks.end(), [specifiedSkelt](auto& elem) 
        {
            auto& track_sec = elem.second;
            for (auto& seq : track_sec.AnimSequences)
            {
                if (!seq->IsSkeletonAttached(specifiedSkelt))
                    return true;
            }
            return false;
        });

        if (itr_sec != mSecTracks.end())
            return false;

        return true;
    }

    bool Animatrix::HasRootMotion() const
    {
        auto itr_def = std::find_if(mDefTracks.begin(), mDefTracks.end(), [](auto& elem) 
        {
            auto& track_def = elem.second;
            if (track_def.HasRootMotion())
                return true;

            return false;
        });

        if (itr_def != mDefTracks.end())
            return true;

        auto itr_sec = std::find_if(mSecTracks.begin(), mSecTracks.end(), [](auto& elem) 
        {
            auto& track_sec = elem.second;
            if (track_sec.HasRootMotion())
                return true;

            return false;
        });

        if (itr_sec != mSecTracks.end())
            return true;

        return false;
    }

    /////////////////////////////////////////////
    // AnimatrixInstance
    //
    /////////////////////////////////////////////
    AnimatrixInstance::AnimatrixInstance(Animatrix* inAnimShell)
        : AnimCmpInstanceBase(false)
    {
        if (inAnimShell)
        {
            mAnimatrixPtr.reset(inAnimShell);

            mPrevPos = mCurPos = { 0.0f };
            mBlend.SetValueRange(0.f, 1.0f);

            mSyncParam = AnimSyncParams(mAnimatrixPtr->GetSyncGroupName());
            if (mSyncParam.SyncGroupName != NAME_NONE)
            {
                mSyncParam.Method = AnimSyncMethod::SyncGroup;
            }
        }
    }

    void AnimatrixInstance::Terminate() 
    {
        AnimCmpInstanceBase::Terminate();  
    }

    void AnimatrixInstance::Advance(float deltaTime)
    {
        Assert(IsValid());
        if (!IsPlaying())
            return;

        // set time
        mDeltaTime = {deltaTime * mPlayRate};

        // Step 1. Update blend
        mBlend.Update(mDeltaTime);

        // Step 2.1 If no activated slot holding by instance, move cursor without activated track
        if (IsAnySlotActivated() == false)
        {
            mPrevPos = mCurPos;
            AnimRuntime::AdvanceTime(false, mDeltaTime, mCurPos, {GetRunLength()});
        }
        // Step 2.2 Update all activated slot cursor otherwise
        else
        {
            std::for_each(mActivatedSectionTracks.begin(), mActivatedSectionTracks.end(), [this](auto& elem) {
                if (elem.IsCompleted())
                    return;
                elem.Advance(mDeltaTime);
                });

            std::for_each(mActivatedDefaultTracks.begin(), mActivatedDefaultTracks.end(), [this](auto& elem) {
                if (elem.IsCompleted())
                    return;
                elem.Advance(mDeltaTime);
                });
        }

        // Step 3. blend out check by meet instance's end got no interrupt flag assign
        if (!IsStopped())
        {
            // no slot activated at life-cycle, out naturally
            if (IsAnySlotActivated() == false)
            {
                if ((std::abs)(mCurPos - GetRunLength()) < mAnimatrixPtr->mBlendOutTime)
                    Stop(mAnimatrixPtr->mBlendOutTime, false);
            }   
            // if got slots activated, instance blend out while the longest slot meet its end
            else if (mLongestRefTrack->IsCompleted(mAnimatrixPtr->mBlendOutTime))
                Stop(mAnimatrixPtr->mBlendOutTime, false);
        }
        // Step 4. blend outed check. if finish, nor more advance
        else if (IsBlendOuted())
            Terminate();
    }

    void AnimatrixInstance::Update(AnimExecUpdateRecord& execRecord, AnimExecUpdateContext& updateContext)
    {
        Assert(IsValid());
        if (!IsPlaying())
            return;

        // animatrixs are treated as trigger animations used for override pose from fsm or cached poses
        // 'override' means animmatrixs should not overlap each other in GRAPH_LAYER (trigger anims got individual separation bones Nine times out of ten)  
        // so, we allow animatrix sync group only when animatrix is blending in or blending out
        if (IsBlendingIn() || IsBlendingOut() || IsBlendOuted())
            return;

        auto activatedSlot = GetActivatedSlot(execRecord.SlotName);
        Assert(activatedSlot != nullptr);

        mDeltaTime = {updateContext.DeltaTime * mPlayRate};

        // this instance is leader
        if (updateContext.IsLeader())
        {
            updateContext.MarkerUpdateContext.SetSyncTrackMarkerData(&activatedSlot->GetSyncTrackMarkerData());
            updateContext.SetLeaderDelta(mDeltaTime);

            // if use marker based sync valid, extract sync info here
            // Ungrouped executable anims got bCanUseMarkerSync = false forever
            if (execRecord.bCanUseMarkerSync && updateContext.CanUseMarkerSync())
            {
                // Trigger animation is advanced in 'Animator:Update' already, revert then re-advance
                if (mIsTrigger == true)
                    activatedSlot->Decline();

                UpdateByMarkerAsLeader(
                    activatedSlot->GetSyncMarkerUpdateRecord(),
                    updateContext.MarkerUpdateContext,
                    activatedSlot->GetPreCursor(),
                    activatedSlot->GetCursor(),
                    mDeltaTime);
            }
            else if (mIsTrigger == false)
            {
                // Trigger animation is advanced in 'Animator:Update' already, out early
                // 
                activatedSlot->Advance(mDeltaTime);
            }

            updateContext.SetPrevAnimLengthRatio(activatedSlot->GetPreCursor().mVal / activatedSlot->GetRunLength());
            updateContext.SetCurAnimLengthRatio(activatedSlot->GetCursor().mVal / activatedSlot->GetRunLength());
        }
        //  this instance follow one leader
        else
        {
            // can use marker based sync
            if (execRecord.bCanUseMarkerSync)
            {
                // leader is valid
                if (updateContext.CanUseMarkerSync() && updateContext.MarkerUpdateContext.IsMarkerSyncStartValid())
                {
                    updateContext.MarkerUpdateContext.SetSyncTrackMarkerData(&activatedSlot->GetSyncTrackMarkerData());

                    UpdateByMarkerAsFollower(activatedSlot->GetSyncMarkerUpdateRecord(),
                        updateContext.MarkerUpdateContext,
                        activatedSlot->GetPreCursor(),
                        activatedSlot->GetCursor(),
                        updateContext.GetLeaderDelta());
                }
                // If leader is not valid, advance time as normal, do not jump position and pop.
                // Trigger animation is advanced in 'Animator:Update' already, out early
                else if (mIsTrigger == false)
                {
                    activatedSlot->Advance(mDeltaTime);
                }
            }
            // no sync markers involved, just scale by ratio
            else
                activatedSlot->RatioScaleCursor(
                    updateContext.GetPrevAnimLengthRatio(), updateContext.GetCurAnimLengthRatio());
        }
    }

    void AnimatrixInstance::Evaluate(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext, const CEName& inSlotName)
    {
        Assert(IsValid());

        auto activatedSlot = GetActivatedSlot(inSlotName);
        activatedSlot->GetPose(outPose, extractContext);

        // Extract anim curves from own curve list and specific track
        if (extractContext.AnimCurveDataPtr)
        {
            AnimCmpInstanceBase::ExtractAnimCurves(inSlotName, activatedSlot->GetCursor(), *extractContext.AnimCurveDataPtr);
        }
    }

    void AnimatrixInstance::ExtractDataArbitraryDeltaTime(const CEName& slotName, AnimExtractContext<TrackUnWrapperH>& extractContext)
    {
        if (!IsValid())
            return;

        if (std::abs(mDeltaTime) < 0.001f)
            return;

        auto activatedSlot = GetActivatedSlot(slotName);
        if (activatedSlot == nullptr)
            return;

        bool bExtractRootMotion = extractContext.CanExtractRootMotion();
        bool bExtractNotifies = extractContext.CanExtractNotify();

        if (!mAnimatrixPtr->IsNotifyAvailable())
            bExtractNotifies = false;

        if (!mAnimatrixPtr->HasRootMotion())
            bExtractRootMotion = false;

        // Early out if we have no reltv data to extract
        if (bExtractRootMotion == false && bExtractNotifies == false)
            return;

        if (mAnimatrixPtr->IsDefaultTrack(slotName))
        {
            ExtractDataArbitraryDeltaTime_DefaultTrack(
                *(TYPE_CAST(AnimReferenceDefTrack*, activatedSlot)), 
                bExtractRootMotion, 
                extractContext.RootMotionParamsPtr, 
                bExtractNotifies, 
                extractContext.AnimNotifyQueuePtr);
        }
        else
        {
            ExtractDataArbitraryDeltaTime_SectionedTrack(
                extractContext.AnimatorPtr, 
                *(TYPE_CAST(AnimReferenceSecTrack*, activatedSlot)), 
                bExtractRootMotion, 
                extractContext.RootMotionParamsPtr, 
                bExtractNotifies, 
                extractContext.AnimNotifyQueuePtr);
        }
    }

    AnimSyncMarkerData const& AnimatrixInstance::GetSyncMarkerData(CEName const& inSlotName /*= ""*/) const
    {
        Assert(IsSlotActivated(inSlotName));
        return GetActivatedSlot(inSlotName)->GetSyncTrackMarkerData();
    }

    SyncMarkerUpdateRecord& AnimatrixInstance::GetMarkerUpdateRecord(CEName inSlotName /*= ""*/)
    { 
        Assert(IsSlotActivated(inSlotName)); 
        return GetActivatedSlot(inSlotName)->GetSyncMarkerUpdateRecord();
    }

    bool AnimatrixInstance::AttachTo(const Skeleton* inSkeleton)
    {
        if (!IsValid() || !GetMatrixShell()->AttachTo(inSkeleton))
            return false;

        mRunSkelt = inSkeleton;
        return true;
    }

    float AnimatrixInstance::GetRunLength() const
    {
        if (mActivatedDefaultTracks.size() == 0 && mActivatedSectionTracks.size() == 0)
        {
            AnimTrack const* trackPtr = mAnimatrixPtr->mDefTracks.size() != 0 ?
                &mAnimatrixPtr->mDefTracks.begin()->second :
                &mAnimatrixPtr->mSecTracks.begin()->second;

            return trackPtr->GetRunLength();
        }

        Assert(mLongestRefTrack != nullptr);
        return mLongestRefTrack->GetRunLength() / MathUtils::Sign<float>(mPlayRate);
    }

    float AnimatrixInstance::GetRunLength(CEName const& inSlotName) const
    {
        auto trackPtr = mAnimatrixPtr->GetTrack(inSlotName);
        if (trackPtr == nullptr)
            return -1.0f;

        return trackPtr->GetRunLength() / MathUtils::Sign<float>(mPlayRate);
    }

    bool AnimatrixInstance::ActivateSingleSlot(CEName const& inSlotName)
    {
        if (IsSlotActivated(inSlotName))
            return false;

        AnimTrack const& curTrack = *mAnimatrixPtr->GetTrack(inSlotName);

        if (mAnimatrixPtr->IsDefaultTrack(inSlotName))
        {
            mActivatedDefaultTracks.emplace_back(curTrack);

            if (mLongestRefTrack == nullptr ||
                mLongestRefTrack->GetRunLength() > curTrack.GetRunLength())
                mLongestRefTrack = GetActivatedSlot(inSlotName);

            return true;
        }
        else
        {
            mActivatedSectionTracks.emplace_back(*(TYPE_CAST(AnimSectionedTrack const*, &curTrack)));

            if (mLongestRefTrack == nullptr || 
                mLongestRefTrack->GetRunLength() > curTrack.GetRunLength())
                mLongestRefTrack = GetActivatedSlot(inSlotName);

            return true;
        }

        return false;
    }

    bool AnimatrixInstance::IsSlotActivated(CEName const& inSlotName) const
    {
        auto activatedSlot = GetActivatedSlot(inSlotName);
        if (activatedSlot == nullptr)
            return false;

        return true;
    }

    bool AnimatrixInstance::IsSlotCompleted(CEName const& inSlotName) const
    {
        auto activatedSlot = GetActivatedSlot(inSlotName);
        if (activatedSlot == nullptr)
            return false;

        return activatedSlot->IsCompleted();
    }

    void AnimatrixInstance::GetActivatedSlots(std::vector<AnimReferenceTrackBase const*>& outSlots) const
    {
        std::for_each(mActivatedSectionTracks.begin(), mActivatedSectionTracks.end(), [this, &outSlots](auto& elem) {
            outSlots.push_back(&elem);
            });

        std::for_each(mActivatedDefaultTracks.begin(), mActivatedDefaultTracks.end(), [this, &outSlots](auto& elem) {
            outSlots.push_back(&elem);
            });
    }

    AnimReferenceTrackBase const* AnimatrixInstance::GetActivatedSlot(CEName const& inSlotName) const
    {
        auto itr_sec = std::find_if(mActivatedSectionTracks.begin(), mActivatedSectionTracks.end(), [this, inSlotName](auto& elem) {
            if (elem.GetReference().SlotName == inSlotName)  
                return true;
            return false;
        });

        if (itr_sec != mActivatedSectionTracks.end())
            return &(*itr_sec);

        auto itr_def = std::find_if(mActivatedDefaultTracks.begin(), mActivatedDefaultTracks.end(), [this, inSlotName](auto& elem) {
            if (elem.GetReference().SlotName == inSlotName)
                return true;
            return false;
        });

        if (itr_def != mActivatedDefaultTracks.end())
            return &(*itr_sec);

        return nullptr;
    }

    AnimReferenceTrackBase* AnimatrixInstance::GetActivatedSlot(CEName const& inSlotName)
    {
        return const_cast<AnimReferenceTrackBase*>(const_cast<const AnimatrixInstance*>(this)->GetActivatedSlot(inSlotName));
    }

    void AnimatrixInstance::SetNextSectionName(CEName CurrentSection, CEName NextSection)
    {
        //mAdvancePreTasks
        for (auto& track : mActivatedSectionTracks)
        {
            track.SetNextSection(CurrentSection, NextSection);
        }
    }

    void AnimatrixInstance::SetNextSectionID(UInt32 CurSectionID, UInt32 NextSectionID)
    {
        for (auto& track : mActivatedSectionTracks)
        {
            track.SetNextSection({CurSectionID}, {NextSectionID});
        }
    }
}   // namespace cross::anim
