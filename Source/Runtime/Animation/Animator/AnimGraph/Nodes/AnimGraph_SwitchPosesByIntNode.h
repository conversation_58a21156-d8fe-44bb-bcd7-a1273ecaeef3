#pragma once
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_SwitchPosesBaseNode.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimGraph.h"
#include "CEAnimation/Transform/AnimBlend.h"

namespace cross::anim {

class AnimGraph_SwitchPosesByIntNode : public AnimGraph_SwitchPosesBaseNode
{
public:
    AnimGraph_SwitchPosesByIntNode(AnimGraph const* inAnimGraph, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
        const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap);

    //// ~AnimGraph_BaseNode Interface Begin~
    ////

    virtual void Update(const AnimUpdateContext& inContext) override;

protected:
    virtual bool PostAssemble(const DeserializeNode& inNodeJson, const std::vector<AnimGraph_ParameterLink*>& inParamLinks) override;

    ////
    //// ~AnimGraph_BaseNode Interface End~

public:
    static AnimGraph_BaseNode* Produce(const AnimGraph* inOwner, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
        const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap);

private:
    AnimGraph_IntParamLink mActiveValueParamLink;

    friend class AnimAssembler;
};

}   // namespace cross::anim
