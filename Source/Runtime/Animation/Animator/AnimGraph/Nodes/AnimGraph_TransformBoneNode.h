#pragma once

#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_BaseNode.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimGraph.h"
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_ControlBonesBaseNode.h"
#include "Runtime/Animation/Animator/AnimGraph/Links/AnimGraph_LocalPoseLink.h"

namespace cross::anim {
class AnimGraph_TransformBoneNode : public AnimGraph_BaseNode
{
public:
    AnimGraph_TransformBoneNode(AnimGraph const* inAnimGraph, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap);

    virtual ~AnimGraph_TransformBoneNode() = default;

    virtual void Initialize(const AnimInitContext& inContext) override;

    virtual void Update(const AnimUpdateContext& inContext) override;

    virtual void EvaluateRootSpace(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& inContext) override;
    virtual void EvaluateLocalSpace(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& inContext) override;

protected:
    virtual void ResetNodeInternal() override;

private:
    inline AnimGraph_LocalPoseLink* PoseLink();

public:
    static AnimGraph_BaseNode* Produce(const AnimGraph* inOwner, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap);
    virtual bool PostAssemble(const DeserializeNode& inNodeJson, const std::vector<AnimGraph_ParameterLink*>& inParamLinks) override;

protected:
    CEName mBoneName = NAME_NONE;

    BoneControlSpace mTranslationSpace;
    BoneModificationMode mTranslationMode;

    BoneControlSpace mRotationSpace;
    BoneModificationMode mRotationMode;

    BoneControlSpace mScaleSpace;
    BoneModificationMode mScaleMode;

    AnimGraph_Vec3ParamLink mTranslationLink;
    AnimGraph_Vec3ParamLink mRotationLink;
    AnimGraph_Vec3ParamLink mScaleLink;
    const Animator* mAnimator{nullptr};
    SkBoneHandle mBoneHandle;
    SkBoneHandle mParentBoneHandle;

    friend class AnimAssembler;
};
}   // namespace cross::anim