#pragma once
#include "Resource/Resource.h"
#include "Runtime/Animation/MotionMatch/MMBase/MotionDataAsset.h"
#include "CEAnimation/Composite/AnimCompositeBase.h"
#include "Runtime/Animation/MotionMatch/MMBase/MotionMatchHelpers.h"
#include "Runtime/GameWorld/PrimitiveRenderSystemG.h"
#include "CEAnimation/AnimRuntime.h"

namespace cross
{

    struct PrimitiveData;
    typedef std::shared_ptr<PrimitiveData> PrimitiveDataPtr;

}

namespace cross::anim
{


    // previous implementation, require CompositeInstance to provide rootmotion
    // so this code is dprecated when graph node can provide rootmotion it self
    // what about notify?
    //class BasicMotionMatchInstance : public AnimCmpInstanceBase
    //{
    //public:
    //    BasicMotionMatchInstance() : AnimCmpInstanceBase(false) {}

    //    virtual const AnimSyncMarkerData& GetSyncMarkerData(CEName const& inSlotName = "") const override { return mSyncMarkerData; }

    //    virtual SyncMarkerUpdateRecord& GetMarkerUpdateRecord(CEName slotName = "") override { throw new std::exception(); }

    //    virtual AnimCompositeBase const* GetInstanceShell() const override { return IsValid() ? nullptr : nullptr; }
    //    virtual bool AttachTo(const Skeleton* inSkeleton) override { throw new std::exception(); }

    //    virtual float GetTimeRemainingToEndPos() const override { return 0.0f; }

    //    virtual bool IsLoopingAllowed() const override { return true; }

    //    virtual void Update(AnimExecUpdateRecord& execRecord, AnimExecUpdateContext& updateContext) override {}

    //    virtual void PostUpdate() override {}

    //protected:
    //    AnimSyncMarkerData mSyncMarkerData;
    //};

    class MotionMatchNodeBase;

    class ENGINE_API MMVisualizer
    {
    public:
        bool IsDataReady() const
        {
            return  mPrimitiveRender != nullptr && mAnimationAnalyzer->mAnimationSequences.size() == mAnimationsInMemory.size();
        }

        void Initialize(MotionDataAssetPtr analyzer, PrimitiveRenderSystemG* PrimitiveRenderSystem, std::vector<AnimSequencePtr>& sequences)
        {
            mAnimationAnalyzer = analyzer;
            mPrimitiveRender = PrimitiveRenderSystem;
            mAnimationsInMemory = sequences;
            for (size_t i = 0; i < mAnimationsInMemory.size(); i++)
            {
                mLoadedAnimation[analyzer->mAnimationSequences[i]] = i;
            }
        }
#if CROSSENGINE_EDITOR
        void LoadSequence(AnimFactory& factory, Skeleton* runtimeSkel, anim::CEName anim_sequence_path);
#endif

        float GetRemapedTime(int AnimId, float CurrentTime);

        void GetPose(int AnimId, float RemappedTime, RootSpacePose& outPose);

        void DebugAnimationDatabase(int AnimId, float RemapedTime, RootSpacePose& outPose);

        bool IsAnimationLoaded(const CEName& path) {
            return mLoadedAnimation.count(path) != 0;
        }

    public:
        /// Debug Visualizer
        virtual void DrawPrimitive(const PrimitiveDataPtr& primitive, const Float4x4& rootWorldTransform, const cross::PrimitiveRenderSystemG::PrimitiveLook& primitiveLook) {
            if (mPrimitiveRender)
            {
                mPrimitiveRender->DrawPrimitive(primitive.get(), rootWorldTransform, primitiveLook);
            }
        }

        void DrawDebugTrajctory(const FTransform& rootWorldTransform, const Trajectory& directions, std::vector<PrimitiveDataPtr>& primitiveData, ColorRGBAf line, ColorRGBAf point);

        void DrawDebugPose(const FTransform& rootWorldTransform, const std::vector<JointFeature>& poses, std::vector<PrimitiveDataPtr>& primitiveData, ColorRGBAf line, ColorRGBAf point);

        void DrawCurrentPlayer(const NodeTransform& rootWorldTransform);

    protected:
        void DebugAnimationTrack(int AnimId, const NodeTransform& transform);

        void DebugAnimationPoseTrack(int animid, PoseBoneHandle handle, RootSpacePose& outPose);

    public:
        bool FutureTraj = true;
        bool SelectedTraj = true;
        bool Pose = true;
        // when preview Anim is set valid (>=0 & < maxAnimID) 
        // disbale the MM, play and visualize the animation 
        int PreviewAnimID = -1;
    public:
        std::vector<PrimitiveDataPtr> sWorldposVis;

        std::vector<PrimitiveDataPtr> mDesiredTraj;
        std::vector<PrimitiveDataPtr> mPredictTraj;


        std::vector<PrimitiveDataPtr> mPredictedSkeletonVis;
        std::vector<PrimitiveDataPtr> mDesiredSkeletonVis;
        

        bool mStartPoseRecored = false;
        std::map<int, std::vector<NodeTransform>> mCachedTrajs;
        std::map<std::pair<int, PoseBoneHandle>, std::pair<std::vector<NodeTransform>, std::vector<PrimitiveDataPtr>>> mCachedPoseTraks;

        std::map<int, std::vector<NodeTransform>> mCachedExtracted;

    protected:
        // MotionMatch database
        CE_REAL_PTR_IMPLMENT<MotionDataAsset> mAnimationAnalyzer;

        std::vector<AnimSequencePtr> mAnimationsInMemory;
        std::map<CEName, size_t> mLoadedAnimation;

        PrimitiveRenderSystemG* mPrimitiveRender{nullptr};
    };


    class MotionMatchNodeBase 
    {
    public:
        virtual bool IsValid() const  { return mAnimationAnalyzer != nullptr; }

        virtual void Evaluate(
            RootSpacePose& outPose,
            AnimExtractContext<TrackUnWrapperH>& extractContext,
            const CEName& slotName);


        virtual void ExtractDataArbitraryDeltaTime(const CEName& slotName, AnimExtractContext<TrackUnWrapperH>& extractContext);

        virtual void Advance(float deltaTime);

    protected:
        MotionMatchNodeBase(AnimationAnalyzerPtr analyzer) :mAnimationAnalyzer(analyzer) {}
        MotionMatchNodeBase() = default;
    public:
        /*
        * Do pose search in database
        */
        virtual int LinearPoseSearch(float& finalCost) = 0;
        /*
        * Compute to get current/desired player Traj;
        */
        virtual void ComputeCurrentTraj() = 0;

        /*
        * Compute to get desired/current player poses [selected bones]
        */
        virtual void ExtractCurrentPose() = 0;

        /*
        *  Get Final Pose
        */
        virtual void GetFinalPose(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext);

        /*
        *  whether start search poses;
        */
        virtual void SchedulePoseSearch();

        /*
        *  Play the animation at a specified time
        */
        virtual void PlayAnimStartingReferenceClock(const int animIndex, const float startingTime) {
            mCurPos = { startingTime };
            AddAnimStartingReferenceClock(animIndex, startingTime);
        }
    protected:
        AnimSequencePtr GetCurrentAnimation() const {
            if (mMotionMatchAnimationsRef.size() > 0)
            {
                return mAnimationsInMemory[mMotionMatchAnimationsRef.back().AnimIndex];
            }
            return nullptr;
        }


        //add the animation into blend queue
        void AddAnimStartingReferenceClock(const int animIndex, const float startingTime);

        /*
        * Update the timeClock in choosed sequences
        */
        void MotionUpdate(float deltaTime);

        /*
        * Blend choosed poses to get final pose
        */
        void GetBlendPose(FTransform& resultingRootMotion, RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext) const;

        bool ShouldSearch() const;

        inline TrackUnWrapperH const& GetCursor() const { return mCurPos; }

        inline TrackUnWrapperH const& GetPreCursor() const { return mPrevPos; }

        const AnimationAnalyzerPtr& AnimationAnalyzer() const {
            return mAnimationAnalyzer;
        }

    public:
        // Motion match configs
        // Animation Blend Time
        float BlendTime = 0.3f;

        int LeastAnimInQueue = 3;

        // Time threshold to add time slice
        float TimeBetweenBlends = 0.1f;

        // final speed;
        float Strenth = 420.0;

        TransitMode mTransitionMode = TransitMode::CrossFade;

        InertializationPose mInertializationPose;

    protected:
        virtual void MotionUpdateBlendQueueImp(float deltaTime);

        virtual void MotionUpdateInertialization(float deltaTime);

    protected:
        // MotionMatch runtime Status
        MotionMatchingMathPosePtr mCurrentPose;

        // Debug Info
        FTransform mRootWorldTransform;
        Float3     mInputVector = Float3::Zero();

        // choosed clip id;
        int   CurrentAnimationReferencesIndex = -1;
        // CurerntTime
        float mCurrentTime = 0.f;
        // Motions' number in the blend queue
        int   WorldTimer = 0;
        // interval for pose search
        float ReferenceMMTimer = 0.0f;

        bool  bAllowLooping;

        /// inertialization runtime members
        bool        mForceSearch = false;
        float        mLastSearchTime = 0.0f;

        TrackUnWrapperH mPrevPos{0.f};

        TrackUnWrapperH mCurPos{0.f};

        float mDeltaTime = 0.f;

        MMVisualizer mVisualizer;

        std::vector<AnimSequencePtr> mAnimationsInMemory;

    protected:
        // MotionMatch database
        AnimationAnalyzerPtr mAnimationAnalyzer{ nullptr };
        // the queue for found motion slices 
        std::vector<FMotionAnim> mMotionMatchAnimationsRef;
    };

}
