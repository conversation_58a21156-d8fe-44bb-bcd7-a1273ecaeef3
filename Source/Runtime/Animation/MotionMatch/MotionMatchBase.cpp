#include "EnginePrefix.h"

#include "MotionMatchBase.h"
#include "Runtime/Animation/AnimFactory.h"
#include "CEAnimation/AnimRuntime.h"
#include "RenderEngine/PrimitiveGenerator.h"

#include "Runtime/Animation/MotionMatch/MMBase/MotionDataAsset.h"


namespace cross::anim
{
    void MotionMatchNodeBase::Evaluate(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext, const CEName& slotName) {
        if (mVisualizer.PreviewAnimID >= 0 && mVisualizer.PreviewAnimID < mAnimationsInMemory.size())
        {
            mVisualizer.DebugAnimationDatabase(mVisualizer.PreviewAnimID, mCurrentTime, outPose);
            return;
        }




        LOG_SMALL("WorldTime {} ReferenceTimer {}", WorldTimer, ReferenceMMTimer);

        if (mMotionMatchAnimationsRef.size() > 0)
        {
            if ((mMotionMatchAnimationsRef.size() > 1) && (BlendTime != 0.f))
            {
                FTransform MotionMatchTransformRM;

                GetBlendPose(MotionMatchTransformRM, outPose, extractContext);
            }
            else
            {
                auto context = extractContext.Move<RawH>({ mMotionMatchAnimationsRef.back().Position });
                GetCurrentAnimation()->GetPose(outPose, context);
            }
        }
        if (mMotionMatchAnimationsRef.empty() == false)
        {
            auto animationRef = GetCurrentAnimation();
            LOG_SMALL_WARN("MM Play {} {} at {} {}", PathHelper::GetBaseFileName(animationRef->GetAssetPath()), mInertializationPose.GetState(), mMotionMatchAnimationsRef.back().Position, mMotionMatchAnimationsRef.back().Limit);
        }



        if (mTransitionMode == TransitMode::Inertialization)
        {
            SCOPED_CPU_TIMING(GroupMotionMatch, "Inertialization");
            if (mInertializationPose.GetState() == InertializationPose::Starting)
            {
                mInertializationPose.Start(outPose, BlendTime);
                mInertializationPose.SetState(InertializationPose::Active);
            }

            if (mInertializationPose.GetState() == InertializationPose::Active)
            {
                if (mMotionMatchAnimationsRef.back().Limit >= 1.0f)
                {
                    mInertializationPose.SetState(InertializationPose::InActive);
                }
                else
                {
                    mInertializationPose.Apply(outPose, mMotionMatchAnimationsRef.back().Limit * BlendTime);
                }
            }


            mInertializationPose.PushPose(outPose);
            mInertializationPose.PushDeltaTime(mDeltaTime);
        }


        SchedulePoseSearch();
    }

    void MotionMatchNodeBase::ExtractDataArbitraryDeltaTime(const CEName& slotName, AnimExtractContext<TrackUnWrapperH>& extractContext)
    {
        auto CurrentAnimSeq = GetCurrentAnimation();
        if (CurrentAnimSeq)
        {
            if (extractContext.CanExtractRootMotion() && CurrentAnimSeq->HasRootMotion())
            {
                NodeTransform deltaTransform = CurrentAnimSeq->ExtractRootMotionFromRange({ mPrevPos.mVal }, { mCurPos.mVal });
                LOG_SMALL("Anim {} RootMotion {} at {} ", PathHelper::GetBaseFileName(CurrentAnimSeq->GetAssetPath()).c_str(), deltaTransform.GetTranslation().ToString(), mPrevPos.mVal);

                deltaTransform = mAnimationAnalyzer->FlattenY() ? UMotionMatchingHelpers::FlattenRootMotionY(deltaTransform) : deltaTransform;
                extractContext.RootMotionParamsPtr->Accumulate(deltaTransform);
            }
        }
    }
    void MotionMatchNodeBase::Advance(float deltaTime)
    {
        auto CurrentAnim = GetCurrentAnimation();

        if (CurrentAnim)
        {
            TrackUnWrapperH compositeLength = { CurrentAnim->GetRunLength() };

            mPrevPos = mCurPos;

            // Advance time, grab current time here
            AnimRuntime::AdvanceTime(false, deltaTime, mCurPos, compositeLength);
        }
    }
    void MotionMatchNodeBase::GetFinalPose(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext)
    {
    }
    void MotionMatchNodeBase::AddAnimStartingReferenceClock(const int animIndex, const float startingTime)
    {
        if (mTransitionMode != TransitMode::Inertialization)
        {
            WorldTimer++;
        }
        else
        {
            if (mInertializationPose.GetState() != InertializationPose::InActive)
            {
                LOG_SMALL_WARN("Multiple InertializationPose happed");
            }
            mInertializationPose.SetState(InertializationPose::Starting);
            mMotionMatchAnimationsRef.clear();
        }
        FMotionAnim CreateAnimation = FMotionAnim(animIndex, startingTime);
        mMotionMatchAnimationsRef.push_back(CreateAnimation);

        auto animationRef = mAnimationsInMemory[animIndex];

        LOG_SMALL("MM Add {} {} into Lib {}", PathHelper::GetBaseFileName(animationRef->GetAssetPath().c_str()), startingTime, mMotionMatchAnimationsRef.size() - 1);
    }

    void MotionMatchNodeBase::MotionUpdate(float deltaTime)
    {
        mCurrentTime += deltaTime;

        if (mTransitionMode == TransitMode::Inertialization)
        {
            MotionUpdateInertialization(deltaTime);
        }
        else
        {
            MotionUpdateBlendQueueImp(deltaTime);
        }
    }
    void MotionMatchNodeBase::GetBlendPose(FTransform& resultingRootMotion, RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext) const
    {
        SCOPED_CPU_TIMING(GroupMotionMatch, "BlendPose");
        const int AllowedAnimationMatchCount = static_cast<int>(mMotionMatchAnimationsRef.size());

        LOG_SMALL("MM BlendPose {}", AllowedAnimationMatchCount);

        if (AllowedAnimationMatchCount > 0)
        {
            float AdditionalMath = 0.f;

            std::vector<float> UnderParentConstruct(AllowedAnimationMatchCount, 0.0f);
            std::vector<RootSpacePose> UnderParentAnimationRefs;
            UnderParentAnimationRefs.reserve(AllowedAnimationMatchCount);
            //std::vector<NodeTransform> UndeParentGatheredTransformMotion(AllowedAnimationMatchCount);
            //LOG_INFO("BlendAnim");
            for (int I = 0; I < AllowedAnimationMatchCount; I++)
            {
                //FCompactPose& AnimationMatch = UnderParentAnimationRefs[I];

                UnderParentConstruct[I] = mMotionMatchAnimationsRef[I].Limit * (((static_cast<float>(I + 1)) / (static_cast<float>(AllowedAnimationMatchCount))));
                AdditionalMath += UnderParentConstruct[I];

                auto AnimationRef = mAnimationsInMemory[mMotionMatchAnimationsRef[I].AnimIndex];

                float WorldClock = Clamp<float>(mMotionMatchAnimationsRef[I].Position, 0.f, AnimationRef->GetRunLength());

                //AnimationRef->GetAnimationPose(AnimationMatch, UnderParentRefCurve[I], FAnimExtractContext(WorldClock, true));

                //UnderParentGatheredTransformMotion[I] = CurrentAnimationRef(I)->ExtractRootMotion(WorldClock - DeltaTime, DeltaTime, true);

                RootSpacePose pose = outPose;
                auto context = extractContext.Move<RawH>({ WorldClock });
                AnimationRef->GetPose(pose, context);
                UnderParentAnimationRefs.push_back(std::move(pose));
                //UndeParentGatheredTransformMotion[I] = AnimationRef->ExtractRootMotionFromDeltaTime({ WorldClock - DeltaTime }, DeltaTime, true);

                //LOG_INFO("MM BlendAnim {} {} weight {} RootMotion {} ", PathHelper::GetBaseFileName(AnimationRef->GetAssetPath()), WorldClock, UnderParentConstruct[I], UndeParentGatheredTransformMotion[I].GetTranslation().ToString().c_str());
            }

            if (AdditionalMath > 0.f)
            {
                //TArrayView<FCompactPose> UnderParentAnimationMatchRefs(UnderParentAnimationRefs);

                for (int i = 0; i < UnderParentConstruct.size(); i++)
                {
                    UnderParentConstruct[i] = UnderParentConstruct[i] / AdditionalMath;

                    auto AnimationRef = mAnimationsInMemory[mMotionMatchAnimationsRef[i].AnimIndex];
                }

                cross::anim::BlendPosesTogether(UnderParentAnimationRefs, UnderParentConstruct, outPose);
                outPose.NormalizeRotations();
                //FRootMotionMovementParams MotionMatchSet;
                //MotionMatchSet.Clear();

                //for (int j = 0; j < AllowedAnimationMatchCount; j++)
                //{
                // MotionMatchSet.AccumulateWithBlend(UnderParentGatheredTransformMotion[j], UnderParentConstruct[j]);
                //}

                //ResultingRootMotion = MotionMatchSet.GetRootMotionTransform();
                //ResultingRootMotion.NormalizeRotation();
            }
            else
            {
                //ResultingRootMotion = CurrentAnimationRef(AllowedAnimationMatchCount - 1)->ExtractRootMotion(MotionMatchAnimationsRef.Last().Position - DeltaTime, DeltaTime, true);

                //FindAnimationInRef()->GetAnimationPose(ResultingAnimationMM, ResultingRefCurve, FAnimExtractContext(MotionMatchAnimationsRef.Last().Position, true));
                auto AnimationRef = mAnimationsInMemory[mMotionMatchAnimationsRef.back().AnimIndex];

                auto context = extractContext.Move<RawH>({ mMotionMatchAnimationsRef.back().Position });
                AnimationRef->GetPose(outPose, context);
            }
        }
        else
        {
            // use defaulat pose;
        }
    }
    void MotionMatchNodeBase::SchedulePoseSearch()
    {
        if (mAnimationAnalyzer)
        {
            if (mMotionMatchAnimationsRef.size() > 0)
            {
                if (ShouldSearch())
                {
                    FTransform BoneRootTransform;
                    ExtractCurrentPose();

                    float ResultingCostForMM;


                    mLastSearchTime = mCurrentTime;
                    int Solution_MM = LinearPoseSearch(ResultingCostForMM);


                    std::string animName = "Empty";
                    float clock = -1.0;

                    if (Solution_MM >= 0)
                    {
                        animName = PathHelper::GetBaseFileName(mAnimationsInMemory[mAnimationAnalyzer->GetPoseInfo(Solution_MM).SrcAnimIndex]->GetAssetPath());
                        clock = mAnimationAnalyzer->GetPoseInfo(Solution_MM).StartTime;
                    }

                    LOG_SMALL("MM Find {} in {} start {} cost {}", Solution_MM, animName, clock, ResultingCostForMM);


                    if (Solution_MM >= 0)
                    {
                        bool TheOptimalTargetIsAtTheSameLocation =
                            (mAnimationAnalyzer->GetPoseInfo(Solution_MM).SrcAnimIndex == mMotionMatchAnimationsRef.back().AnimIndex)
                            &&
                            (fabs(mAnimationAnalyzer->GetPoseInfo(Solution_MM).StartTime - mMotionMatchAnimationsRef.back().Position) < .2f);

                        if (!TheOptimalTargetIsAtTheSameLocation)
                        {
                            CurrentAnimationReferencesIndex = Solution_MM;

                            PlayAnimStartingReferenceClock(mAnimationAnalyzer->GetPoseInfo(Solution_MM).SrcAnimIndex, mAnimationAnalyzer->GetPoseInfo(Solution_MM).StartTime);
                        }
                    }
                }
            }
            else
            {
                PlayAnimStartingReferenceClock(0, 0.f);
            }
        }
    }


    ///Debug Visualizer
    void MMVisualizer::DrawDebugPose(const FTransform& rootWorldTransform, const std::vector<JointFeature>& poses, std::vector<PrimitiveDataPtr>& primitiveData, ColorRGBAf line, ColorRGBAf point)
    {
        if (primitiveData.size() != poses.size() * 2)
        {
            primitiveData.resize(poses.size() * 2);
            for (int i = 0; i < poses.size(); i++)
            {
                primitiveData[i * 2] = std::make_shared<PrimitiveData>();
                PrimitiveGenerator::GenerateCubeFrame(primitiveData[i * 2].get(), 10.f);
                primitiveData[i * 2 + 1] = std::make_shared<PrimitiveData>();
            }
        }

        //auto world = mOwenrAnimator->GetWorld();
        //auto primitiveSystem = world->GetGameSystem<PrimitiveRenderSystemG>();

        for (int i = 0; i < poses.size(); i++)
        {
            NodeTransform pose;
            pose.SetTranslation(poses[i].BoneTransformPosition);
            auto newWorldPos = pose * rootWorldTransform;
            DrawPrimitive(primitiveData[i * 2], newWorldPos.GetTransformMatrix(), cross::PrimitiveRenderSystemG::PrimitiveLook(point));

            NodeTransform VeloDir;
            VeloDir.SetTranslation(poses[i].BoneTransformPosition - poses[i].BoneTransformVelocity * 0.2f);

            auto ArrowPose = VeloDir * rootWorldTransform;
            PrimitiveGenerator::GenerateLine(primitiveData[i * 2 + 1].get(), newWorldPos.GetTranslation(), ArrowPose.GetTranslation());
            DrawPrimitive(primitiveData[i * 2 + 1], Float4x4::Identity(), cross::PrimitiveRenderSystemG::PrimitiveLook(line));
        }
    }


    void MMVisualizer::DrawDebugTrajctory(const FTransform& rootWorldTransform, const Trajectory& directions, std::vector<PrimitiveDataPtr>& primitiveData, ColorRGBAf line, ColorRGBAf point)
    {
        auto& traj = directions;

        std::vector<FTransform> trajs(traj.size());

        for (int i = 0; i < traj.size(); i++)
        {
            trajs[i] = traj[i].CurrentRefTransform * rootWorldTransform;
        }


        if (primitiveData.size() != (traj.size() * 3-1) && trajs.size() > 0)
        {
            primitiveData.resize(trajs.size() * 3 - 1);
            for (int i = 0; i < trajs.size(); i++)
            {
                primitiveData[i * 3] = std::make_shared<PrimitiveData>();
                PrimitiveGenerator::GenerateSphere(primitiveData[i * 3].get(), 6.0, 10, 10);
                primitiveData[i * 3 + 1] = std::make_shared<PrimitiveData>();
                if (i < trajs.size() - 1)
                {
                    primitiveData[i * 3 + 2] = std::make_shared<PrimitiveData>();
                }
            }
        }

        for (int i = 0; i < trajs.size(); i++)
        {
            DrawPrimitive(primitiveData[i * 3], trajs[i].GetTransformMatrix(), cross::PrimitiveRenderSystemG::PrimitiveLook(point));


            auto ForwardVector = trajs[i].GetRotation().GetForwardVector();
            auto startPoint = trajs[i].GetTranslation();
            auto DirectionPoint = startPoint + ForwardVector * 50;

            PrimitiveGenerator::GenerateLine(primitiveData[i * 3 + 1].get(), startPoint, DirectionPoint);

            cross::ColorRGBAf directionColor = line;
            directionColor.r = 0.5;

            DrawPrimitive(primitiveData[i * 3 + 1], Float4x4::Identity(), cross::PrimitiveRenderSystemG::PrimitiveLook(directionColor, PrimitiveDepth::SceneDepth, 5));

            if (i < trajs.size() - 1)
            {
                // PrimitiveGenerator::GenerateLine(primitiveData[i * 3 + 2].get(), traj[i].CurrentRefTransform.GetTranslation(), traj[i + 1].CurrentRefTransform.GetTranslation());
                // primitiveSystem->DrawDebugPrimitive(primitiveData[i * 3 + 2].get(), rootWorldTransform.GetTransformMatrix(), cross::PrimitiveRenderSystemG::DebugPrimitiveLook(line,5));
            }
        }
    }

    void MMVisualizer::DrawCurrentPlayer(const NodeTransform & rootWorldTransform)
    {
        if (sWorldposVis.empty())
        {
            sWorldposVis.push_back(std::make_shared<PrimitiveData>());
            PrimitiveGenerator::GenerateSphere(sWorldposVis[0].get(), 8.0);
            sWorldposVis.push_back(std::make_shared<PrimitiveData>());
        }

        //auto world = mOwenrAnimator->GetWorld();
        //auto primitiveSystem = world->GetGameSystem<PrimitiveRenderSystemG>();

        //  Visualize Current player' pose
        DrawPrimitive(sWorldposVis[0], rootWorldTransform.GetTransformMatrix(), cross::PrimitiveRenderSystemG::PrimitiveLook(cross::ColorRGBAf(1.0, 1.0, 1.)));

        auto forward = rootWorldTransform.GetRotation().GetForwardVector();
        auto start_point = rootWorldTransform.GetTranslation();
        auto direction_point = start_point + forward * 100;
        PrimitiveGenerator::GenerateLine(sWorldposVis[1].get(), start_point, direction_point);
        DrawPrimitive(sWorldposVis[1], Float4x4::Identity(), cross::PrimitiveRenderSystemG::PrimitiveLook(ColorRGBAf(1.f, 1.f, 1.f), PrimitiveDepth::SceneDepth, 5));
    }


    void MMVisualizer::DebugAnimationDatabase(int AnimId, float RemapedTime, RootSpacePose& outPose)
    {
        if (!IsDataReady()) return;


        bool anim_processed = mAnimationAnalyzer->mAnimToStartPoseId.contains(AnimId);

        bool show_picked_poses = anim_processed;

        float final_time = RemapedTime;

        auto sequence = mAnimationsInMemory[AnimId];

        MotionMatchPoseData pose_data;

        if (show_picked_poses && false)
        {
            int start_pose_id = mAnimationAnalyzer->mAnimToStartPoseId[AnimId];
            int pose_offset = static_cast<int>(final_time / mAnimationAnalyzer->GetConfig().PoseInterval);
            int final_pose_id = start_pose_id + pose_offset;

            final_time = mAnimationAnalyzer->GetPoseInfo(final_pose_id).StartTime;

            pose_data = mAnimationAnalyzer->GetPoseData(final_pose_id);
        }
        else
        {
            auto symponyDataset = TypeCast<MotionDataAsset>(mAnimationAnalyzer);
            MotionMatchingPose pose;
            symponyDataset->ExtractDataFromAnimation(pose, mAnimationsInMemory[AnimId], AnimId, final_time);

            pose_data = pose.mPoseData;
        }


        auto tContext = AnimExtractContext<RawH>({ final_time });

        auto RootMotion = sequence->ExtractRootMotionFromRange({ 0.0 }, { final_time });
        RootMotion = mAnimationAnalyzer->FlattenY() ? UMotionMatchingHelpers::FlattenRootMotionY(RootMotion) : RootMotion;

        sequence->GetPose(outPose, tContext);

        NodeTransform firstFrame;
        sequence->GetBoneTransform(firstFrame, { 0 }, 0.0f);

        //LOG_ERROR("MM {} {} {}", final_time, RootMotion.GetTranslation().ToString(), (void*)sequence.get());

        DrawDebugPose(firstFrame * RootMotion, pose_data.MotionJointData, mPredictedSkeletonVis, cross::ColorRGBAf(0.0, 1.0, 1.0), cross::ColorRGBAf(0.0, 1.0, 1.0));

        DrawDebugTrajctory(RootMotion, pose_data.TrajectoryData, mDesiredTraj, cross::ColorRGBAf(0.0, 0.0, 1.), cross::ColorRGBAf(0.0, 0.0, 1.0));

        DrawCurrentPlayer(RootMotion);

        //trajectory of processed rootmotion
        DebugAnimationTrack(AnimId,cross::NodeTransform::Identity());

        //trajectory of a specific joints
        DebugAnimationPoseTrack(AnimId, { 0 }, outPose);
    }

    void MMVisualizer::DebugAnimationTrack(int AnimId, const NodeTransform& transform)
    {
        static std::vector<PrimitiveDataPtr> lines;
        static PrimitiveDataPtr sphere;

        if (mCachedTrajs.count(AnimId) == 0)
        {
            std::vector<NodeTransform> points;
            points.push_back(transform);

            auto CurrentTransform = transform;

            float TimeForCalc = 0.f;
            float TimeStep = 0.1f;
            auto sequence = mAnimationsInMemory[AnimId];

            while (TimeForCalc <= sequence->GetRunLength())
            {
                auto refTransform = sequence->ExtractRootMotionFromRange({ 0.0 }, { TimeForCalc + TimeStep });
                refTransform.NormalizeRotation();

                NodeTransform normalizedTransform = mAnimationAnalyzer->FlattenY() ? UMotionMatchingHelpers::FlattenRootMotionY(refTransform) : refTransform;
                auto NewCurrentTransform = normalizedTransform * CurrentTransform;
                points.push_back(NewCurrentTransform);
                TimeForCalc += TimeStep;
            }
            mCachedTrajs[AnimId] = points;
        }

        if (mCachedExtracted.count(AnimId) == 0 && mAnimationAnalyzer->mAnimToStartPoseId.contains(AnimId))
        {
            int start_pose_id = mAnimationAnalyzer->mAnimToStartPoseId[AnimId];

            std::vector<NodeTransform> points;
            auto CurrentTransform = transform;
            auto sequence = mAnimationsInMemory[AnimId];

            while (start_pose_id < mAnimationAnalyzer->PoseNum() && mAnimationAnalyzer->GetPoseInfo(start_pose_id).SrcAnimIndex == AnimId)
            {
                auto& poseInfo = mAnimationAnalyzer->GetPoseInfo(start_pose_id);
                auto refTransform = sequence->ExtractRootMotionFromRange({ 0.0 }, { poseInfo.StartTime });
                refTransform.NormalizeRotation();

                NodeTransform normalizedTransform = mAnimationAnalyzer->FlattenY() ? UMotionMatchingHelpers::FlattenRootMotionY(refTransform) : refTransform;
                auto NewCurrentTransform = normalizedTransform * CurrentTransform;

                points.push_back(NewCurrentTransform);

                start_pose_id++;
            }
            mCachedExtracted[AnimId] = points;
        }

        const auto& points = mCachedTrajs[AnimId];

        for (int i = 0; i < points.size() - 1; i++)
        {
            if (lines.size() <= i)
            {
                lines.push_back(std::make_shared<PrimitiveData>());
            }

            PrimitiveGenerator::GenerateLine(lines[i].get(), points[i].GetTranslation(), points[i + 1].GetTranslation());

            DrawPrimitive(lines[i], Float4x4::Identity(), cross::PrimitiveRenderSystemG::PrimitiveLook(cross::ColorRGBAf(0.0, 1.0, 0.0), PrimitiveDepth::SceneDepth, 5));
        }

        if (mCachedExtracted.count(AnimId))
        {
            if (!sphere)
            {
                sphere = std::make_shared<PrimitiveData>();
                PrimitiveGenerator::GenerateCube(sphere.get(), 3.0);
            }

            const std::vector<NodeTransform>& extracted_points = mCachedExtracted[AnimId];
            for (int i = 0; i < extracted_points.size(); i++)
            {
                DrawPrimitive(sphere, extracted_points[i].GetTransformMatrix(), cross::PrimitiveRenderSystemG::PrimitiveLook(cross::ColorRGBAf(1.0, 0.0, 0.0), PrimitiveDepth::SceneDepth, 5));
            }
        }
    }

    void MMVisualizer::DebugAnimationPoseTrack(int AnimId, PoseBoneHandle handle, RootSpacePose& outPose)
    {
        if (mCachedPoseTraks.count({ AnimId, handle }) == 0)
        {
            std::vector<NodeTransform> points;
            float TimeForCalc = 0.f;
            float TimeStep = 0.1f;
            auto sequence = mAnimationsInMemory[AnimId];

            while (TimeForCalc <= sequence->GetRunLength())
            {
                auto pose = outPose;

                auto tContext = AnimExtractContext<RawH>({ TimeForCalc }, RootMotion::ExtractMode::DoNotExtract);
                sequence->GetPose(pose, tContext);
                points.push_back(pose.GetRootSpaceTransform(handle));
                TimeForCalc += TimeStep;
            }
            mCachedPoseTraks[{AnimId, handle}] = { points, std::vector<PrimitiveDataPtr>() };
        }

        auto cached_track = mCachedPoseTraks[{AnimId, handle}];
        auto& points = cached_track.first;
        auto& lines = cached_track.second;
        for (int i = 0; i < points.size() - 1; i++)
        {
            if (lines.size() <= i)
            {
                lines.push_back(std::make_shared<PrimitiveData>());
            }

            PrimitiveGenerator::GenerateLine(lines[i].get(), points[i].GetTranslation(), points[i + 1].GetTranslation());

            DrawPrimitive(lines[i], Float4x4::Identity(), cross::PrimitiveRenderSystemG::PrimitiveLook(cross::ColorRGBAf(1.0, 1.0, 0.0), PrimitiveDepth::SceneDepth, 5));
        }
    }

    float MMVisualizer::GetRemapedTime(int AnimId, float CurrentTime)
    {
        float scaled_time = CurrentTime;

        auto MMDataset = mAnimationAnalyzer;

        if (!IsDataReady() || AnimId >= mAnimationsInMemory.size() || !mAnimationsInMemory[AnimId])
            return CurrentTime;

        auto sequence = mAnimationsInMemory[AnimId];
        float sequence_len = sequence->GetRunLength() - MMDataset->mStartAnimClamp + MMDataset->mEndAnimClamp;
        float clamped_time = fmodf(scaled_time, sequence_len);//MathUtils::Clamp(fmodf(CurrentTime, sequence_len), 1.0f, sequence_len);
        return clamped_time;
    }

    void MMVisualizer::GetPose(int AnimId, float RemappedTime, RootSpacePose& outPose)
    {
        if (!IsDataReady()) return;

        auto sequence = mAnimationsInMemory[AnimId];

#if CROSSENGINE_EDITOR
        if (sequence->IsSkeletonAttached() == false)
            return;
#endif

        auto tContext = AnimExtractContext<RawH>({ RemappedTime });

        sequence->GetPose(outPose, tContext);
    }
#if CROSSENGINE_EDITOR
    void MMVisualizer::LoadSequence(AnimFactory& factory, Skeleton* runtimeSkel, anim::CEName anim_sequence_path)
    {
        FactoryErrorCode::Type status;
        auto result = factory.SyncCreateAnimSeq(anim_sequence_path.GetCString(), status, runtimeSkel);

        if (status != FactoryErrorCode::Type::Success)
            return;

        mAnimationsInMemory.push_back(std::get<0>(result));
        mLoadedAnimation[anim_sequence_path] = mAnimationsInMemory.size() - 1;
    }
#endif

    void MotionMatchNodeBase::MotionUpdateBlendQueueImp(float deltaTime)
    {
        SCOPED_CPU_TIMING(GroupMotionMatch, "MotionUpdateBlendQueueImp");
        if (WorldTimer > LeastAnimInQueue)
        {
            ReferenceMMTimer += deltaTime;
        }
        if (ReferenceMMTimer > TimeBetweenBlends)
        {
            ReferenceMMTimer = 0.f;
            WorldTimer = 0;
        }

        auto AnimationCount = mMotionMatchAnimationsRef.size();

        //LOG_INFO("MM Frame {} delta {} {}", Frame++, mDeltaTime, AnimationCount);

        if (AnimationCount > 0)
        {
            auto Animation_RefIndex = static_cast<int>(AnimationCount - 1);

            for (int i = Animation_RefIndex; i > -1; i--)
            {
                if (mMotionMatchAnimationsRef[i].ApplyTime(deltaTime, BlendTime, i == Animation_RefIndex))
                {
                    mMotionMatchAnimationsRef.erase(mMotionMatchAnimationsRef.begin() + i);
                }
                else
                {
                    auto animName = PathHelper::GetBaseFileName(mAnimationsInMemory[mMotionMatchAnimationsRef[i].AnimIndex]->GetAssetPath());
                    //LOG_INFO("MM anim {} {} position {} transit {} Limit {}", i, animName, mMotionMatchAnimationsRef[i].Position, mMotionMatchAnimationsRef[i].AllowedMotionTransitionTime, mMotionMatchAnimationsRef[i].Limit);
                }
            }
        }
    }

    void MotionMatchNodeBase::MotionUpdateInertialization(float deltaTime)
    {
        SCOPED_CPU_TIMING(GroupMotionMatch, "MotionUpdateInertialization");
        if (!mMotionMatchAnimationsRef.empty())
        {
            auto& anim = mMotionMatchAnimationsRef.back();
            anim.ApplyTime(deltaTime, BlendTime, true);

            if (anim.Position >= mAnimationsInMemory[anim.AnimIndex]->GetRunLength())
            {
                mForceSearch = true;
            }
        }
    }

    bool MotionMatchNodeBase::ShouldSearch() const
    {
        if (mTransitionMode != TransitMode::Inertialization)
        {
            return WorldTimer <= LeastAnimInQueue;
        } 
        else
        {
            return mForceSearch || (mCurrentTime - mLastSearchTime) > TimeBetweenBlends;
        }
    }
}

