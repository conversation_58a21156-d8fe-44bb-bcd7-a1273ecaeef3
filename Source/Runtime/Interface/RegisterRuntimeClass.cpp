#include "EnginePrefix.h"
#include "Runtime/Interface/RegisterRuntimeClass.h"

#include "Resource/ResourceManager.h"
#include "Resource/BaseClasses/RegisterClassesForward.h"
#include "Resource/Texture/TextureCube.h"
#include "Resource/Texture/Texture2DArray.h"
#include "Resource/Texture/TextureUDIM.h"
#include "Resource/Animation/Animator/AnimatorResource.h"
#include "Resource/Animation/Sequence/AnimSequenceRes.h"
#include "Resource/Animation/Curve/CurveControllerRes.h"
#include "Resource/Animation/Skeleton/SkeletonResource.h"
#include "Resource/ParticleSystem/ParticleSystemResource.h"
#include "Resource/ParticleSystem/ParticleEmitterResource.h"
#include "Resource/TerrainResource.h"
#include "Resource/FontResource.h"
#include "Resource/RenderTextureResource.h"
#include "Resource/RuntimePCG/PCGResource.h"
#include "Resource/AirportResource.h"
#include "Resource/MaterialParameterCollection.h"
#include "Resource/MaterialFunction.h"
#include "Resource/BinaryResource.h"
#include "Resource/InstanceDataResource.h"
#include "Resource/WorkflowGraphResource.h"
#include "Resource/InputActionMappingResource.h"
#include "Resource/DataAssetResource.h"
#include "Resource/TsResource.h"
#include "Resource/HLODResource.h"
#include "Resource/Shader.h"
#include "Runtime/GameWorld/Prefab/PrefabManager.h"
#include "Runtime/GameWorld/WorldPartition/WorldPartitionBlockResource.h"
#include "Runtime/Animation/Skeleton/SkeletonPhysicsResource.h"
#include "Runtime/Animation/MotionMatch/MMBase/MotionDataAsset.h"
#include "Runtime/GameWorld/GameWindowSystem.h"
#include "Reflection/runtime.hpp"
#include "Reflection/builder.hpp"

using namespace cross::anim;

namespace cross
{
    template<typename T>
    std::string ClassNameToCompiler()
    {
        return typeid(T).name();
    }

#define REGISTER_RUNTIME_RESOURCE_NEW(res, parent, ns)                                                                                                                                                                                         \
    __append_register_cxx_type<ns::res>().custom_id(ClassID(res)).constructor<>();


#define REGISTER_RUNTIME_RESOURCE_OLD(res, parent, ns) \
    Object::RegisterRuntimeClass(ClassID(res), ClassID(parent), ClassNameToCompiler<ns::res>(), sizeof(ns::res), &ns::res::Produce, false)

#define REGISTER_RUNTIME_RESOURCE(res, parent, ns) \
    REGISTER_RUNTIME_RESOURCE_NEW(res, parent, ns)

    // object
    void RegisterRuntimeClasses()
    {
        struct NullType {};

        //Object::RegisterRuntimeClass(ClassID(NullType), -1, "NullType", 1, nullptr, true);
        //std::string className = ClassNameToCompiler<NullType>();
        // ------------------ This Class ID ------------------- Base Class ID ------ Class Name ------- Class Size ------------ Constructor --------------- Is Abstract
        Object::RegisterRuntimeClass(ClassID(NullType),                -1,                ClassNameToCompiler<NullType>(),                        1,                                       nullptr,                                   true);
        Object::RegisterRuntimeClass(ClassID(Object),                  -1,                ClassNameToCompiler<Object>(),                          sizeof(Object),                          nullptr,                                   true);
        Object::RegisterRuntimeClass(ClassID(Named),                   ClassID(Object),   ClassNameToCompiler<Named>(),                           sizeof(Named),                           nullptr,                                   true);
        //Object::RegisterRuntimeClass(ClassID(Resource),                ClassID(Named),    ClassNameToCompiler<Resource>(),                        sizeof(Resource),                        &Resource::Produce,                        true);
        Object::RegisterRuntimeClass(ClassID(Window),            ClassID(Named),    ClassNameToCompiler<Window>(),                    sizeof(Window),                    &Window::Produce,                    false);

        //REGISTER_RUNTIME_RESOURCE(Named                      , Object,   cross);
        REGISTER_RUNTIME_RESOURCE(Resource                   , Object,   cross);
        REGISTER_RUNTIME_RESOURCE(CurveControllerRes         , Resource, cross);
        REGISTER_RUNTIME_RESOURCE(Material                   , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(MaterialParameterCollection, Resource, resource);
        REGISTER_RUNTIME_RESOURCE(MaterialFunction           , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(Shader                     , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(Fx                         , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(ComputeShader              , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(RayTracingShader           , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(Texture                    , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(Texture2D                  , Texture , resource);
        REGISTER_RUNTIME_RESOURCE(Texture3D                  , Texture , resource);
        REGISTER_RUNTIME_RESOURCE(TextureCube                , Texture , resource);
        REGISTER_RUNTIME_RESOURCE(Texture2DArray             , Texture , resource);
        REGISTER_RUNTIME_RESOURCE(Texture2DVirtual           , Texture2D, resource);
        REGISTER_RUNTIME_RESOURCE(TextureUDIM                , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(MeshAssetDataResource      , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(ScriptResource             , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(WorldBlock                 , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(PrefabResource             , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(TerrainResource            , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(FontResource               , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(PCGResource                , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(BinaryResource          , Resource, resource);
#if AIRPORT_SCENE_EDITOR
        REGISTER_RUNTIME_RESOURCE(AirportResource            , Resource, resource);
#endif
        REGISTER_RUNTIME_RESOURCE(RenderTextureResource      , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(SkeletonResource           , Resource, skeleton);
        REGISTER_RUNTIME_RESOURCE(SkeletonPhysicsResource    , Resource, skeleton);
        REGISTER_RUNTIME_RESOURCE(AnimSequenceRes            , Resource, anim);
        REGISTER_RUNTIME_RESOURCE(AnimCompositeRes           , Resource, anim);
        REGISTER_RUNTIME_RESOURCE(AnimatrixRes               , Resource, anim);
        REGISTER_RUNTIME_RESOURCE(AnimatorRes                , Resource, anim);
        REGISTER_RUNTIME_RESOURCE(AnimBlendSpaceRes          , Resource, anim);
        REGISTER_RUNTIME_RESOURCE(MotionDataAsset            , Resource, anim);
        REGISTER_RUNTIME_RESOURCE(ParticleSystemResource     , Resource, fx);
        REGISTER_RUNTIME_RESOURCE(ParticleEmitterResource    , Resource, fx);
        REGISTER_RUNTIME_RESOURCE(WorkflowGraphResource      , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(InstanceDataResource       , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(TsResource                 , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(InputActionMappingResource , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(DataAssetResource          , Resource, resource);
        REGISTER_RUNTIME_RESOURCE(HLODResource               , Resource, resource);
    }
}
