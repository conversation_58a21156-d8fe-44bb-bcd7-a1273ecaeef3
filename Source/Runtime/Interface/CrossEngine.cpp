#include "EnginePrefix.h"
#include <vector>
#include "Runtime/Interface/CrossEngineImp.h"

#if !(CROSSENGINE_IOS || CROSSENGINE_ANDROID)

#include "memory/Module.h"
IMPORT_MODULE_WITHNAME("CrossEngine")

#endif

bool gDisableHDR = false;


ICrossEngine * CreateCrossEngine()
{
    return new cross::CrossEngine{};
}

void DestroyCrossEngine(ICrossEngine * engine)
{
	delete static_cast<cross::CrossEngine*>(engine);
}