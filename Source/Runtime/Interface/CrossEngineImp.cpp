
#include "EnginePrefix.h"
#include "Runtime/Interface/CrossEngineImp.h"
#include "Runtime/Interface/EngineCrashMonitor.h"
#include "Threading/RenderingThread.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/EngineVersion.h"

#include "CECommon/Common/SettingsManager.h"
#include "CECommon/Common/FrameCounter.h"
#include "CECommon/Common/GlobalSystemBase.h"
#include "CECommon/Common/GlobalSystemDesc.h"
#include "CECommon/Common/SystemEvent.h"
#include "CECommon/Common/InGameTerminalManager.h"
#include "CECommon/Common/ExtensionRegister.h"
#include "FileSystem/filesystem.h"
#include "Resource/AssetStreaming.h"
#include "Resource/ResourceSyncSystem.h"


#include "Runtime/Interface/RegisterRuntimeClass.h"
#include "Runtime/Interface/ECSConfig.h"
#include "Runtime/Interface/EngineModuleConfig.h"
#include "Runtime/Interface/IGenericApplicationHandler.h"

#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/WorldSystemG.h"
#include "Runtime/GameWorld/GameWindowSystem.h"
#include "Runtime/GameWorld/InputSystemG.h"
#include "Runtime/GameWorld/EditorGlobalCallBackSystemG.h"
#include "Runtime/GameWorld/RendererSystemG.h"
#include "Runtime/GameWorld/Prefab/PrefabManager.h"
#include "Runtime/GameWorld/FrameSynchronizationSystemG.h"
#include "Runtime/GameWorld/VirtualTextureSystemG.h" 
#include "Runtime/Scripts/EngineLayer/ScriptWorld.h"

#include "Runtime/Input/InputManager.h"
#include "Runtime/Input/Core/InputKeys.h"
#include "Runtime/Input/Core/Public/SlateApplication.h"

#include "Runtime/Streaming/StreamingManager.h"

#include "Runtime/Audio/AudioEngineManager.h"
#include "Runtime/Reflection/ReflectionRegister.h"
#include "meta/reflection/meta/meta_class.hpp"
#include "reflection/builder/class_builder.inl"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderPipeline/BuiltinRenderPipelineSetting.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"
#include "Runtime/Misc/RenderResourceManager.h"

#include "NativeGraphicsInterface/NGIManager.h"

#include "Scripts/ScriptModule.h"
#include "TypeScriptEngine/TypeScriptModule.h"
#include "PhysicsEngine/PhysicsEngine.h"
#include "RenderDocHelper.h"
#include "GameplayBaseFramework/imodules/imod_shared/shared_global.hpp"
#include "memoryhooker/Module.h"
#if NCNN_ASSEMBLE
#include "InferenceEngine/InferenceEngine.h"
#include "Runtime/Inference/SuperResolutionSystemG.h"
#include "Runtime/Inference/DetectionSystemG.h"
#endif

//////////////////////////////////////////////////////////////////////////////////////
// test new ecs framework
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/DataTypes.h"
//////////////////////////////////////////////////////////////////////////////////////
#include "CrossBase/Globals/Globals.h"
#include "CECommon/Utilities/ImguiwsConsole.h"
#include "CECommon/Common/CmdSettings.h"
#if _WIN32
    #include "imodules/imod_shared/imodules/iexception_system_module.h"
#endif
#include <codecvt>
using namespace cross::anim;
namespace cross::scripts {
void CodeGenRegisterGeneratedClass();
}
namespace cross
{
    class IPlatformCursor;
    class IApplicationUserHandler;

    CrossEngine::CrossEngine()
        : mDeltaTime(-1.0f)
        , mRunAverageDeltaTime(0.01f)
    {
        EngineGlobal::InitEngineGlobal();
        auto& global = EngineGlobal::Inst();
        global.SetECSFramework(new ecs::Framework());
        global.GetECSFramework().Init();
        global.SetGlobalSystemDescSystem(new GlobalSystemDescSystem());
        global.SetCompSystemDescSystem(new ComponentSystemDescSystem());
        global.SetCrossEngine(this);
        global.SetMemoryBlockPool(new MemBlockPool());
        global.SetPrefabMgr(new PrefabManager());
        global.SetTerminalManager(new InGameTerminalManager());
    }

    CrossEngine::~CrossEngine()
    {
    }

    /** shut down the engine */
    void CrossEngine::ShutDown()
    {
#if CROSSENGINE_OSX//exit on macos for let the unitest can work on macos, TODO investigate crash when shutdown
        _exit(0);
#endif
        GetGlobalSystem<cross::RendererSystemG>()->Flush();

        auto& global = EngineGlobal::Inst();

        // 10/24/2024
        // shutdown Engine at reverse order to make sure code is executed correctly (haven't figure out why entirely)
        // decouple the shutdown into two procedure, shutdown and release, since some of the class's deconstructor need access global object
        // the render object is release in render engine.
        // but still some check failed.
        // quite a mess in fact
        // we may need a clearer architecture for shutdown and release resource, but right now, just leave it, sad.

        // Stop streaming process and unsubscribe events before global system shutdown and release
        gStreamingManager.Shutdown();

        //tracy::GetProfiler().RequestShutdown();
        //ResourceManager::Instance().GarbageCollect(true);
        for (int i = static_cast<int>(mSystemUpdateList.size()) - 1; i >= 0 ; i--)
        //for (size_t i = 0; i < mSystemUpdateList.size(); i++)
        {
            mSystemUpdateList[i]->NotifyShutdownEngine();
        }

        for (int i = static_cast<int>(mSystemUpdateList.size()) - 1; i >= 0; i--)
        //for (size_t i = 0; i < mSystemUpdateList.size(); i++)
        {
            mSystemUpdateList[i]->Release();
        }
#if SUPPORT_LUA    
         cross::scripts::Entity::Class().GetInstanceDefine().GetFunctions().clear();
        ///Destroy default resource
        ScriptModule::Instance().Destroy();
#endif
        //AnimationBuilder::Instance().Destroy();

        ResourceManager::Instance().CheckLeak();

        profiling::Shutdown();
        cross::GameFramework()->RunToFinishState();
        delete GameFramework();

		global.GetAudioEngine()->End(); // end the audio engine anyway
        PhysicsEngine::Destroy(global.GetPhysicsEngine());

#if NCNN_ASSEMBLE
        global.GetInferenceEngine()->Release();
#endif
        if(EngineGlobal::GetRenderEngine())
        {  // don't know why need two interface
            EngineGlobal::GetRenderEngine()->ShutDown();
            EngineGlobal::GetRenderEngine()->Release();
            DestroyNGIDevice();
        }
        if (mEnableStreamingScratchBuffer)   
            StreamingScratchBuffer::Release();
        threading::FlushRenderingCommands();
        delete global.GetInputManager();


        delete global.GetFrameParamMgr();

        delete global.GetSettingMgr();

#if ENABLE_GUARDED_COMPONENT_ACCESS_CHECKS
        {
            auto numLeaks = ecs::DataRaceGuard::sNumInstances.load(std::memory_order_relaxed);
            AssertMsg(numLeaks == 0, "{} leaks for component access guard detected.", numLeaks);   
        }
#endif
        threading::TaskSystem::Shutdown();
    }
    CROSS_CODE CrossEngine::Init(const cross::InitInfo& info)
    {
#if CROSSENGINE_IOS || CROSSENGINE_ANDROID
        if(info.DocumentPath != nullptr)
        {
            PathHelper::SetDocumentDirectoryPath(info.DocumentPath);
        }
        else
        {
            LOG_ERROR("Invalid document path!!");
        }
#endif
        mIsInitializing = true;

        // init validate keys
        cross::input::CEKeys::Initialize(info);
        SetGlobalGbf(new gbf::GameFramework());
        profiling::Startup();
        // set current directory
        if (!info.AssetPath.empty() && info.AssetPath[0] != '\0')
        {
            PathHelper::SetCurrentDirectoryPath(info.AssetPath);
        }
        else
        {
            LOG_ERROR("Invalid asset resource path!!");
        }



        if (!info.EngineResourcePath.empty())
        {
            PathHelper::SetEngineResourceDirectoryPath(info.EngineResourcePath);
        }
        else
        {
            LOG_ERROR("Invalid engine resource path!!");
        }

        // log move the first init for other module need it.
        // init logger
        // thumbnail should not produce log file
        if (info.LogDelegate)
        {
            LogModule::Instance().Init(info.StartupType == AppStartUpTypeCrossEditor || info.StartupType == AppStartUpTypeHeadless,
                [](Logger::LogLevel level, const char* message, SourceLoc sourceLoc, void* userData) {
                    CrossEngineLogDelegate logDelegate = reinterpret_cast<CrossEngineLogDelegate>(userData);
                    logDelegate(int(level), message);
                },
                reinterpret_cast<void*>(info.LogDelegate),
                !info.IsThumbnailProcess,
                CmdSettings::Inst().gEnableLogToUdp);
            GLog->SetEditorLogDelegate(info.LogDelegate);
        }
        else
        {
            LogModule::Instance().Init(!info.IsThumbnailProcess, CmdSettings::Inst().gEnableLogToUdp, info.StartupType == AppStartUpTypeCrossEditor);
        }

        auto& global = EngineGlobal::Inst();
        global.SetFileSystem(new filesystem::FileSystem());

        InitReflectionModule();
        auto* settingMgr = new SettingsManager(global.GetFileSystem());
        settingMgr->SetIsThumbnailProcess(info.IsThumbnailProcess);
        global.SetSettingManager(settingMgr);
        if (!settingMgr->LoadFromFile())
        {
            LOG_ERROR("Failed to load engine seting file!!!");
        }

        // global.SetPipelineConfigure(new RenderPipelineConfigure());
        settingMgr->SetAppStartUpType((AppStartUpType)info.StartupType);
        settingMgr->InitDisplayModeList();

        // Init profiler for threads
        // make sure the thread dispaly order in profiler for better visualization
        // but right now, seems framepro has some bugs
        if (CmdSettings::Inst().gVsync)
        {
            // GProfiler->ThreadOrder(VSYNCSpyTaskRunnable::ThreadName);
        }
        bool value = false;
        if (CmdSettings::Inst().GetConfigValueByName<bool>("gUseEngineStats", value) && value)
        {
            // GProfiler->ThreadOrder(GPUSpyTaskRunnable::ThreadName);
            // GProfiler->ThreadOrder(PresentSpyTaskRunnable::ThreadName);
            LOG_INFO("UseEngineStats.....Open");
        }
        // GetProfilerModule()->ThreadOrder("Main Thread");
        // GetProfilerModule()->ThreadOrder("Rendering Thread");
        // GetProfilerModule()->RegisterConnectionChangedCallback([](bool connected, const wchar_t* p_recording_filename, void* user_data) {
        //     if (connected)
        //     {
        //         auto& engineGlobal = EngineGlobal::Inst();
        //         auto* frameParamMgr = engineGlobal.GetFrameParamMgr();
        //         auto fp = frameParamMgr->GetCurrentGameFrameParam();
        //         LOG_INFO("FramePro Connected {}", fp->GetFrameCount());
        //     }
        //     }, nullptr);
		
        const auto numTaskThreads = settingMgr->GetNumTaskThreads();
        SInt32 numAsyncThreads = -1;
        settingMgr->GetValue("NumAsyncThreads", numAsyncThreads);
		if (info.StartupType != AppStartUpTypeHeadless)
		{
			const auto useSeparateRenderingThread = settingMgr->GetUseSeparateRenderingThread();
			const auto useSeperatePresentThread = settingMgr->GetUseSeparatePresentThread();
	        threading::TaskSystem::Startup(useSeparateRenderingThread ? threading::MasterThreadScheduling::Separated : threading::MasterThreadScheduling::Inlined,
	                                       useSeperatePresentThread ? threading::MasterThreadScheduling::Separated : threading::MasterThreadScheduling::Inlined,
	                                       numTaskThreads, numAsyncThreads);
		}
		else
		{
	        threading::TaskSystem::Startup(threading::MasterThreadScheduling::Disabled, threading::MasterThreadScheduling::Disabled, numTaskThreads, numAsyncThreads);
		}
        // toggle single rendering thread
        // threading::TaskSystem::Startup(false, 0);

#if CROSSENGINE_WIN
        if (settingMgr->GetRenderdocEnable())
        {
            RenderDocHelper::InitInstance();
        }
#endif
        InitScriptModule();
#if _WIN32
        // if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeStandAlone)
        {
            std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>> converter;
            AddDllDirectory(converter.from_bytes(std::filesystem::current_path().generic_string()).c_str());
            if (info.StartupType != AppStartUpTypeHeadless)
			{
	            cross::GameFramework()->LoadDynamicModule("exception_system", "exception_system");
	            cross::GameFramework()->LoadDynamicModule("package_system", "package_system");

	            // set current app resource path( source_path/data )
                std::string localPath = GFileSystem->GetAppCurrentPath();
                localPath = GFileSystem->CombinePath(localPath, "Data");
	            GFileSystem->SetAppResourcePath(localPath);
			}
        }
#endif


        CmdSettings::Inst().ParseValueContainer(settingMgr->GetContext());
        if (info.StartupType != AppStartUpTypeHeadless)
		{
        	global.SetRenderEngine(RenderEngine::CreateInstance());
		}

        global.SetSystemPriorityConfig(new ECSConfig());

        global.SetEngineModuleConfig(new EngineModuleConfig());
        global.GetEngineModuleConfig()->EnumerateModulesFromConfig(settingMgr->mEngineConfig.value(), settingMgr->mProjectConfig.value(), static_cast<cross::AppStartUpType>(info.StartupType));
        global.GetEngineModuleConfig()->LoadAllConfigModules();
        auto crash_path = std::string(info.AssetPath) + "/" + PathHelper::GetSavedPath();
        if (!PathHelper::IsDirectoryExist(crash_path))
        {
            PathHelper::MakeDirectory(crash_path);
        }
        crash_path += "/Crashes";
        if (!PathHelper::IsDirectoryExist(crash_path))
        {
            PathHelper::MakeDirectory(crash_path);
        }
        if (info.StartupType != AppStartUpTypeHeadless)
		{
            bool UseFullDump = CmdSettings::Inst().gUseFullDump;
	        dynamic_cast<gbf::IExceptionSystemModule*>(cross::GameFramework()->QueryModule(gbf::kModuleExceptionSystemName))->SetExceptionDumpPath(crash_path.c_str());
	        dynamic_cast<gbf::IExceptionSystemModule*>(cross::GameFramework()->QueryModule(gbf::kModuleExceptionSystemName))->SetUseFullDump(UseFullDump);
	        auto flushcallback = []() {
	            LOG_ERROR("Engine Crashed Caused By Reason After.");
	            LogModule::Instance().Flush();
	        };
	        dynamic_cast<gbf::IExceptionSystemModule*>(cross::GameFramework()->QueryModule(gbf::kModuleExceptionSystemName))->InitWithCallBack(flushcallback);
		}
        global.SetFrameParamManager(new FrameParamManager(threading::TaskSystem::Get()->UseSeparateRenderingThread()));
        global.SetInputManager(new InputManager);

        global.SetPhysicsEngine(PhysicsEngine::Create());
        global.SetAudioEngine(new AudioEngineManager());

        InitGlobalSystems();

        switch (info.StartupType)
        {
        case AppStartUpTypeCrossEditor:
        case AppStartUpTypeThumbnailProcessor:
        case AppStartUpTypeHeadless:
        {
            /// Add global Call Back System
            auto* editorglobalupdatesystem = EditorGlobalCallBackSystemG::CreateInstance();
            editorglobalupdatesystem->GetDesc().mBeginFramePriority = 0;
            editorglobalupdatesystem->GetDesc().mEndFramePriority = 1000;
            editorglobalupdatesystem->GetDesc().mUpdatePriority = 1000;
            AddGlobalSystem(editorglobalupdatesystem);
            break;
        }
        default:
            global.GetAudioEngine()->Begin();   // start the audio engine in standalone mode, otherwise start it in PIE.
            break;
        }

        cross::GameFramework()->LoadDynamicModule("GameFramework", "GameFramework");
        cross::GameFramework()->LoadDynamicModule("CEGameplay", "CEGameplay");
        cross::GameFramework()->LoadDynamicModule("CEEditorRuntime", "CEEditorRuntime");
#if NCNN_ASSEMBLE
        global.SetInferenceEngine(InferenceEngine::Instance());
        auto* srSystem = SuperResolutionSystemG::CreateInstance();
        srSystem->GetDesc().mBeginFramePriority = 0;
        srSystem->GetDesc().mEndFramePriority = 0;
        srSystem->GetDesc().mUpdatePriority = 0;
        AddGlobalSystem(srSystem);
#endif

        LOG_INFO("Initializing CrossEngine.");

        RegisterRuntimeClasses();
        if (info.StartupType != AppStartUpTypeHeadless)
		{
        	GetGlobalSystem<cross::RendererSystemG>()->PreWindowCreationInit();
		}

        mIsInitializing = false;

        InitEngineGraphicsResource();

        InitTypescriptModule();

        InitStreamingModule();

        InitResourceModule();

        return CROSS_CODE::CROSS_SUCC;
    }
    CROSS_CODE CrossEngine::InitByNoGraphicsCard(const cross::InitInfo& info)
    {
#if CROSSENGINE_IOS || CROSSENGINE_ANDROID
        if (info.DocumentPath != nullptr)
        {
            PathHelper::SetDocumentDirectoryPath(info.DocumentPath);
        }
        else
        {
            LOG_ERROR("Invalid document path!!");
        }
#endif
        mIsInitializing = true;

        // init validate keys
        cross::input::CEKeys::Initialize(info);
        SetGlobalGbf(new gbf::GameFramework());
        profiling::Startup();
        // set current directory
        if (!info.AssetPath.empty() && info.AssetPath[0] != '\0')
        {
            PathHelper::SetCurrentDirectoryPath(info.AssetPath);
        }
        else
        {
            LOG_ERROR("Invalid asset resource path!!");
        }

        if (!info.EngineResourcePath.empty())
        {
            PathHelper::SetEngineResourceDirectoryPath(info.EngineResourcePath);
        }
        else
        {
            LOG_ERROR("Invalid engine resource path!!");
        }

        LogModule::Instance().Init(!info.IsThumbnailProcess, CmdSettings::Inst().gEnableLogToUdp);


        auto& global = EngineGlobal::Inst();
        global.SetFileSystem(new filesystem::FileSystem());

        InitReflectionModule();
        auto* settingMgr = new SettingsManager(global.GetFileSystem());
        settingMgr->SetIsThumbnailProcess(info.IsThumbnailProcess);
        global.SetSettingManager(settingMgr);
        if (!settingMgr->LoadFromFile())
        {
            LOG_ERROR("Failed to load engine seting file!!!");
        }
        // global.SetPipelineConfigure(new RenderPipelineConfigure());
        settingMgr->SetAppStartUpType((AppStartUpType)info.StartupType);
        {
            cross::GameFramework()->LoadDynamicModule("exception_system", "exception_system");
            cross::GameFramework()->LoadDynamicModule("package_system", "package_system");
        }
        mIsInitializing = false;

        scripts::CodeGenRegisterGeneratedClass();
        TypeScriptModule::Instance()->Start();


        return CROSS_CODE::CROSS_SUCC;
    }

    void CrossEngine::PostInit()
    {
        cross::GameFramework()->RunToUpdateState();
    }


    EditorOnlyModules* CrossEngine::GetEditorModeModules() const
    {
        auto& global = EngineGlobal::Inst();

        EditorOnlyModules *modules = new EditorOnlyModules;
        for (auto& itr : global.GetEngineModuleConfig()->GetLoadedModulesInfo())
        {
            if (itr.second.mModuleType != ModuleType::GameOnly)
            {
                modules->mNames.push_back(
                    itr.first.GetCString());
                modules->mPathes.push_back(itr.second.mModuleFullPath);
            }
        }

        return modules;
    }

    void ConvertAllResource()
    {
#if 0//CROSSENGINE_EDITOR
        //strFileName    std::string    "EngineResource/Texture/DefaultTexture.nda"
        //ResourcePtr& resItem = gAssetStreamingManager->Load("/Volumes/SSD/workspace/CrossEngine_workspace/CrossEngine_PlayGround/Contents/Material/NewMaterial.nda");

        //ResourcePtr& resItem = gAssetStreamingManager->Load("/Volumes/SSD/workspace/CrossEngine_workspace/CrossEngine_PlayGround/Contents/Shader/DefaultShader.nda");
        //ResourcePtr& resItem = gAssetStreamingManager->Load("/Volumes/SSD/workspace/CrossEngine_workspace/CrossEngine_PlayGround/EngineResource/Texture/Camera.nda");

        //std::string fileName = dirEntry.path();
        //bool neeSkipConvert = NeedSkipFilePath(fileName);

        //std::string strResPath("EngineResource/Texture/DefaultTexture.nda");
        std::string strResPath("EngineResource/Texture/Light.nda");
        ResourcePtr& resItem = gResourceMgr.AsyncGetResource(strResPath.c_str());
        ResourceMetaHeader metaHeader;
        bool hasMetaHeader = gResourceAssetMgr.GetResourceMetaHeader(strResPath.c_str(), metaHeader);
        //if(resItem.get() && !hasMetaHeader)
        //    resItem->SerializeEngineConfigToFile();

        gResourceMgr.TraverseWorkDirToAddHeader();
#endif
    }

    void CrossEngine::InitEngineGraphicsResource()
    {
        gResourceMgr.EnableAsyncLoading(EngineGlobal::GetSettingMgr()->GetIsAsyncLoading());
        gResourceMgr.mCreateRenderObjectMgr = CreateRenderObjectMgr::Inst();

        if(mEnableStreamingScratchBuffer)
            StreamingScratchBuffer::Get();
    }

    void CrossEngine::InitTypescriptModule()
    {
        // TODO should only Init, Start when begin PIE, stop when end PIE.
            ///Warning: Register this before start otherwise cpp type is not complete
        scripts::CodeGenRegisterGeneratedClass();
        TypeScriptModule::Instance()->Start();
        class TypeScriptHotReloadMgr : public ITypeScriptHotReloadMgr
        {
        public:
            static TypeScriptHotReloadMgr* Inst()
            {
                static TypeScriptHotReloadMgr instance;
                return &instance;
            }

            bool ReloadScriptModule(std::string_view model_name, const std::string& script_content) override
            {
                return TypeScriptModule::Instance()->ReloadScriptModule(model_name, script_content);
            }
        };
        gResourceMgr.mTypeScriptHotReloadMgr = TypeScriptHotReloadMgr::Inst();

    }

    void CrossEngine::InitStreamingModule()
    {
        gResourceMgr.mStreamingMgr = &gStreamingManager;
    }

    void CrossEngine::InitResourceModule() {
        gResourceMgr.Initialize();
    }

    void CrossEngine::AddGlobalSystem(GlobalGameSystemBase* system)
    {
        if (!system)
            return;

        auto& engineGlobal = EngineGlobal::Inst();

        const auto& systemDesc = system->GetSystemDesc();

        Assert(systemDesc.mIsGameSystem);

        SInt32 systemID = systemDesc.mID;
        if (systemID < mGlobalSystems.size())
        {
            if (mGlobalSystems[systemID] != nullptr && mGlobalSystems[systemID] != system)
            {
                LOG_ERROR("System with same id already exist in engine, name: {}, id: {}.",
                    systemDesc.mName.GetCString(),
                    systemDesc.mID);
                return;
            }
        }
        else
        {
            auto newSize = engineGlobal.GetGlobalSystemDescSystem()->GetGlobalSystemCount();
            mGlobalSystems.resize(newSize);
        }

        mGlobalSystems[systemID] = system;
        mRunFirstUpdateList.push_back(system);
        system->NotifyAddToEngine(this);

        {
            bool addToSortedList = false;
            SInt32 priority = systemDesc.mUpdatePriority;
            Assert(priority > -1);
            for (size_t i = 0; i < mSystemUpdateList.size(); i++)
            {
                if (mSystemUpdateList[i]->GetSystemDesc().mUpdatePriority > priority)
                {
                    mSystemUpdateList.insert(mSystemUpdateList.begin() + i, system);
                    addToSortedList = true;
                    break;
                }
            }
            if (!addToSortedList)
            {
                mSystemUpdateList.emplace_back(system);
            }
        }

        {
            bool addToSortedList = false;
            SInt32 priority = systemDesc.mBeginFramePriority;
            Assert(priority > -1);
            for (size_t i = 0; i < mSystemBeginFrameList.size(); i++)
            {
                if (mSystemBeginFrameList[i]->GetSystemDesc().mBeginFramePriority > priority)
                {
                    mSystemBeginFrameList.insert(mSystemBeginFrameList.begin() + i, system);
                    addToSortedList = true;
                    break;
                }
            }
            if (!addToSortedList)
            {
                mSystemBeginFrameList.emplace_back(system);
            }
        }

        {
            bool addToSortedList = false;
            SInt32 priority = systemDesc.mEndFramePriority;
            Assert(priority > -1);
            for (size_t i = 0; i < mSystemEndFrameList.size(); i++)
            {
                if (mSystemEndFrameList[i]->GetSystemDesc().mEndFramePriority > priority)
                {
                    mSystemEndFrameList.insert(mSystemEndFrameList.begin() + i, system);
                    addToSortedList = true;
                    break;
                }
            }
            if (!addToSortedList)
            {
                mSystemEndFrameList.emplace_back(system);
            }
        }

        auto* renderObject = system->GetRenderSystem();
        if (renderObject && EngineGlobal::GetEngine()->GetSettingManager()->GetAppStartUpType() != AppStartUpTypeHeadless)
        {
            if (!mIsInitializing)
            {
                DispatchRenderingCommandWithToken([renderObject]
                    {
                        EngineGlobal::GetRenderEngine()->AddGlobalSystem(renderObject);
                    });
            }
            else
            {
                engineGlobal.GetRenderEngine()->AddGlobalSystem(renderObject);
            }
            system->NotifyAddRenderSystemToRenderEngine();
        }
    }

    GlobalGameSystemBase* CrossEngine::GetGlobalSystemByID(UInt32 systemID)
    {
        if (systemID < mGlobalSystems.size())
        {
            return mGlobalSystems[systemID];
        }
        return nullptr;
    }
    void CrossEngine::SetAssetPath(const char* assetPath) 
    {
        cross::PathHelper::SetCurrentDirectoryPath(assetPath);
    }

    int CrossEngine::GetRendererMode() 
    {
        auto& device = cross::GetNGIDevice();
        return static_cast<int>(device.GetPlatform());
    }
    /** create a world */
    IGameWorld* CrossEngine::CreateWorld(char const* worldName, UInt32 WorldTypeTag)
    {
        WorldSystemG* system = TYPE_CAST(WorldSystemG*, GetGlobalSystemByID(WorldSystemG::GetDesc().mID));

        CreateGameWorldParam Param;
        Param.mWorldTypeTag = WorldTypeTag;
        auto World = system->CreateWorld(worldName, Param);
        return World;
    }

    /** destroy a world */
    void CrossEngine::Destroy(IGameWorld* world)
    {
        WorldSystemG* system = TYPE_CAST(WorldSystemG*, GetGlobalSystemByID(WorldSystemG::GetDesc().mID));
        system->DestroyWorld(TYPE_CAST(GameWorld*, world));
    }

    IRenderWindow* CrossEngine::CreateRenderWindow(RenderWindowInitInfo const& info)
    {
        WindowSystemG* system = TYPE_CAST(WindowSystemG*, GetGlobalSystemByID(WindowSystemG::GetDesc().mID));
        auto* result = system->CreateWindow(info);
        return result;
    }

    void CrossEngine::Destroy(cross::IRenderWindow* renderWindow)
    {
        if (renderWindow)
        {
            WindowSystemG* system = GetGlobalSystem<WindowSystemG>();
            system->DestroyWindow(dynamic_cast<Window*>(renderWindow));
        }
    }

    void CrossEngine::PreTick()
    {
        SCOPED_CPU_TIMING(GroupEngine, "PreloadEngineResources");

        static std::vector<ResourcePtr> requestedLoads;
        for (const auto& assetPath : EngineGlobal::GetSettingMgr()->GetEnginePreloadResources())
        {
            requestedLoads.push_back(gAssetStreamingManager->LoadSynchronously(assetPath));
        }
    }
    void CrossEngine::InitScriptModule()
    {
#if SUPPORT_LUA
        auto& scriptModule = ScriptModule::Instance();
#if !CROSSENGINE_UNITTEST
        //todo
#if SCRIPT_ENGINE_BACKEND_LUA
        scriptModule.RunBaseFile("EngineResource/lua/Foundation.lua");
        scriptModule.RunBaseFile("EngineResource/lua/LuaBuiltIn.lua");
        scriptModule.RunBaseFile("EngineResource/lua/json.lua");
        scriptModule.RunBaseFile("EngineResource/lua/CEClass.lua");
        scriptModule.InitBuildInFunction();
#endif
        scriptModule.RegisterAll();
        scriptModule.RegisterEngineClass();
#endif
#endif
    }

    double CrossEngine::GetRealTimeSeconds() const 
    {
        auto result = std::chrono::duration_cast<std::chrono::microseconds>(currentTimePoint - firstTimePoint).count() * 1e-6f;
        return result;
    }

    double CrossEngine::GetDeltaSeconds() const 
    {
        return mDeltaTime;
    }

    float CrossEngine::GetRunAverageDeltaTime() const
    {
        return mRunAverageDeltaTime;
    }

    void CrossEngine::InitReflectionModule()
    {
        ReflectionRegister();
    }

    float CrossEngine::UpdateAndThrottleTickRates(FrameParam* fp)
    {
        static auto lastTickTime = std::chrono::high_resolution_clock::now();

        if (mDeltaTime < 0.f)
        {
            lastTimePoint = std::chrono::high_resolution_clock::now();
            firstTimePoint = std::chrono::high_resolution_clock::now();
        }

        currentTimePoint = std::chrono::high_resolution_clock::now();


        mDeltaTime = std::chrono::duration_cast<std::chrono::microseconds>(currentTimePoint - lastTimePoint).count() * 1e-6f;

        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeHeadless)
        {
            const auto maxTickRates = GetSettingManager()->GetMaxTickRates();

            if (maxTickRates != GetGlobalSystem<FrameSynchronizationSystemG>()->GetFrameRate())
            {
                GetGlobalSystem<FrameSynchronizationSystemG>()->SetFrameRates(maxTickRates);
            }
            // TODO
            // current implementation is not yet complete,
            // the ultimate goal of imp is similar in
            // https://blog.unity.com/engine-platform/fixing-time-deltatime-in-unity-2020-2-for-smoother-gameplay
            // 1. use vync to throttle 
            // 2. get time stamp
            // 3. properly handle the relationship between os message loop and engine input processing
            GetGlobalSystem<FrameSynchronizationSystemG>()->Throttle(fp);
        }

        lastTickTime = std::chrono::high_resolution_clock::now();
        lastTimePoint = currentTimePoint;

        auto time = currentTimePoint - firstTimePoint;
        return std::chrono::duration_cast<std::chrono::microseconds>(time).count() * 1e-6f;
    }
    void CrossEngine::SetEngineBinaryPath(const char* assetPath) 
    {
        cross::PathHelper::SetEngineBinaryDirectoryPath(assetPath);
    }
    void CrossEngine::SetFixedUpdateMode(bool state, float fixedDeltaTime)
    {
        mUseFixedUpdateMode = state;
        mFixedDeltaTime = fixedDeltaTime;
    }
    void CrossEngine::InitGlobalSystems() 
    {
        const std::string worldSystemConfigPath = "EngineResource/Config/WorldSystemConfig.json";
        auto configFile = EngineGlobal::GetSettingMgr()->LoadConfigFile(PathHelper::GetEngineResourceAbsolutePath(worldSystemConfigPath));
        if (!configFile)
        {
#if CROSSENGINE_IOS || CROSSENGINE_ANDROID
            std::string filePathInDocument = PathHelper::GetDocumentDirectoryPath() + worldSystemConfigPath;
            configFile = EngineGlobal::GetSettingMgr()->LoadConfigFile(filePathInDocument);
            if (!configFile)
#endif
            {
                LOG_ERROR("Faile to load system config file!!!");
                Assert(0);
                return;
            }
        }
        auto confignode = configFile->HasMember("GlobalSystemConfig");
        if (!confignode)
        {
            LOG_ERROR("Invalid config file!!!");
        }
        
        else if(!confignode.value().IsArray())
        {
            return;
        }
        for (size_t t = 0; t < confignode.value().Size(); t++)
        {
            std::string system_name = confignode.value()[t]["Name"].AsString();

            if (GetSettingManager()->GetAppStartUpType() == AppStartUpTypeHeadless)
            {
                static const std::set<std::string_view> gDisabledSystems{
                    "cross::WindowSystemG",
                    "cross::InputSystemG",
                    "cross::RendererSystemG",
                    "cross::FrameSynchronizationSystemG",
                    "cross::ResourceSyncSystem",
                    "cross::VirtualTextureSystemG",
                    "cross::ImguiwsConsole",
                };
                if (gDisabledSystems.find(system_name) != gDisabledSystems.end())
                {
                    continue;
                }
            }

            UInt32 BeginPrio = confignode.value()[t]["BeginFramePriority"].AsUInt32();
            UInt32 UpdatePrio = confignode.value()[t]["UpdatePriority"].AsUInt32();
            UInt32 EndPrio = confignode.value()[t]["EndFramePriority"].AsUInt32();
            auto metaclass = gbf::reflection::query_meta_class_by_name(system_name);
            if (metaclass)
            {
                auto& create_system_func = metaclass->GetFunction("CreateInstance");
                auto& getdesc_func = metaclass->GetFunction("GetDesc");
                auto instance = gbf::reflection::cxx::CallStatic(create_system_func);
                auto desc = gbf::reflection::cxx::CallStatic(getdesc_func);
                cross::GlobalGameSystemBase* ptr = &instance.Ref<cross::GlobalGameSystemBase>();
                const GlobalSystemDesc& descref = desc.Ref<const GlobalSystemDesc>();
                descref.mBeginFramePriority = BeginPrio;
                descref.mEndFramePriority = EndPrio;
                descref.mUpdatePriority = UpdatePrio;
                AddGlobalSystem(ptr);
            }
            else
            {
                LOG_INFO("GlobalSystem is lost: {}", system_name);
            }
        }

         std::for_each(EngineGlobal::Inst().GetEngineModuleConfig()->GetLoadedModulesInfo().begin(), EngineGlobal::Inst().GetEngineModuleConfig()->GetLoadedModulesInfo().end(), [this](auto& moduleinfo) {
            if (moduleinfo.second.mEnable)
            {
                if (!moduleinfo.second.mGlobalSystemList.IsNull())
                {
                    for (size_t t = 0; t < moduleinfo.second.mGlobalSystemList.Size(); t++)
                    {
                        const auto& node = moduleinfo.second.mGlobalSystemList[t];
                        std::string system_name = node["Name"].AsString();
                        UInt32 BeginPrio = node["BeginFramePriority"].AsUInt32();
                        UInt32 UpdatePrio = node["UpdatePriority"].AsUInt32();
                        UInt32 EndPrio = node["EndFramePriority"].AsUInt32();
                        auto metaclass = gbf::reflection::query_meta_class_by_name(system_name);
                        if (metaclass)                       
                        {
                            auto& create_system_func = metaclass->GetFunction("CreateInstance");
                            auto& getdesc_func = metaclass->GetFunction("GetDesc");
                            auto instance = gbf::reflection::cxx::CallStatic(create_system_func);
                            auto desc = gbf::reflection::cxx::CallStatic(getdesc_func);
                            cross::GlobalGameSystemBase* ptr = &instance.Ref<cross::GlobalGameSystemBase>();
                            const GlobalSystemDesc& descref = desc.Ref<const GlobalSystemDesc>();
                            descref.mBeginFramePriority = BeginPrio;
                            descref.mEndFramePriority = EndPrio;
                            descref.mUpdatePriority = UpdatePrio;
                            EngineGlobal::Inst().GetEngine()->AddGlobalSystem(ptr);
                        }
                        else
                        {
                            LOG_INFO("GlobalSystem is lost: {}", system_name);
                        }
                    }
                }
            }
        });

    }
    void CrossEngine::Tick()
    {
        {
            FrameMarkNamed("GameFrame");
            SCOPED_CPU_TIMING(GroupEngine, "EngineTick");

            if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeHeadless)
            {
                if (mEnableStreamingScratchBuffer)
                    StreamingScratchBuffer::Get()->Tick();
                auto xrFrameInfo = GetXRRuntime()->BeginGameFrame([] { EngineGlobal::GetEngine()->FlushRendering(); });
                GetGlobalSystem<RendererSystemG>()->SetXRFrameInfo(xrFrameInfo);
            }

            auto& engineGlobal = EngineGlobal::Inst();

            auto* frameParamMgr = engineGlobal.GetFrameParamMgr();
            {
                SCOPED_CPU_TIMING(GroupEngine, "FrameParamMgBeginFrame");
                // we set mTime and delatTime as zero for now
                frameParamMgr->BeginGameFrame(0.0, 0.0);
                frameParamMgr->BeginRenderFrame();
            }

            auto fp = frameParamMgr->GetCurrentGameFrameParam();
            mFrameMemoryResource = std::move(FrameAllocatorPool(fp->GetFrameAllocator(), FRAME_STAGE_GAME_RENDER));
            float elapsedTime = mDeltaTime;

            float timeToFirstFrame = UpdateAndThrottleTickRates(fp);

            if (mUseFixedUpdateMode)
            {
                elapsedTime = mFixedDeltaTime;
                mTime += elapsedTime;
                mDeltaTime = mFixedDeltaTime;

                DispatchRenderingCommandWithToken([this] { EngineGlobal::Inst().GetRenderEngine()->SetAverageFrameTime(mFixedDeltaTime); });
            }
            else
            {
                mTime = timeToFirstFrame;
                elapsedTime = mDeltaTime;
                /// Calculate moving average
                constexpr float movingaverageweight = 1.0f / 30.0f;
                mRunAverageDeltaTime = mRunAverageDeltaTime + (cross::FloatMin(elapsedTime, 0.2f) - mRunAverageDeltaTime) * movingaverageweight;

                DispatchRenderingCommandWithToken([this] { EngineGlobal::Inst().GetRenderEngine()->SetAverageFrameTime(mRunAverageDeltaTime); });
            }

            fp->SetTime(mTime, mDeltaTime);
            //auto FrameIDString = std::string("FRAME_").append(std::to_string(fp->GetFrameCount()));
            //profiler->BeginSampleDynamic(FrameIDString.c_str(), "");
            mFrameSamples.Update(elapsedTime * 1000.);
            //LOG_INFO("{} mean {} variance {}", elapsedTime, mFrameSamples.mFrameMean, mFrameSamples.mFrameVariance);

            {
                SCOPED_CPU_TIMING(GroupEngine, "BeginFrame");

                for (size_t i = 0; i < mSystemBeginFrameList.size(); i++)
                {
                    mSystemBeginFrameList[i]->BeginFrame(fp);
                }
            }

            {
                SCOPED_CPU_TIMING(GroupEngine, "FirstUpdate");

                if (!mRunFirstUpdateList.empty())
                {
                    for (size_t i = 0; i < mRunFirstUpdateList.size(); i++)
                    {
                        mRunFirstUpdateList[i]->FirstUpdate(fp);
                    }
                    engineGlobal.GetTerminalManager()->InitManager();
                    mRunFirstUpdateList.clear();
                }
            }

            {
                SCOPED_CPU_TIMING(GroupEngine, "PreUpdate");

                for (size_t i = 0; i < mSystemUpdateList.size(); i++)
                {
                    mSystemUpdateList[i]->PreUpdate(fp);
                }
            }

            {
                SCOPED_CPU_TIMING(GroupEngine, "Update");

                for (size_t i = 0; i < mSystemUpdateList.size(); i++)
                {
                    mSystemUpdateList[i]->Update(fp);
                }
            }

            {
                SCOPED_CPU_TIMING(GroupEngine, "PostUpdate");

                for (size_t i = 0; i < mSystemUpdateList.size(); i++)
                {
                    mSystemUpdateList[i]->PostUpdate(fp);
                }
            }

            {
                SCOPED_CPU_TIMING(GroupEngine, "EndFrame");

                for (size_t i = 0; i < mSystemEndFrameList.size(); i++)
                {
                    mSystemEndFrameList[i]->EndFrame(fp);
                }
            }

            {
                SCOPED_CPU_TIMING(GroupEngine, "FrameParamMgrEndFrame");

                frameParamMgr->EndGameFrame();
                frameParamMgr->EndRenderFrame();
            }

            {
                SCOPED_CPU_TIMING(GroupEngine, "ResourceManager");

                gResourceMgr.Tick();
                gResourceMgr.GarbageCollect();
            }

            if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeHeadless)
            {
                SCOPED_CPU_TIMING(GroupEngine, "StreamingManager");
                gStreamingManager.Tick(fp);
            }

            {
                float MaxTickRates = static_cast<float>(EngineGlobal::GetSettingMgr()->GetMaxTickRates());
                float duration = 1.f / MaxTickRates * 1000.f;

                if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeHeadless)
                {
                    // VsyncSpyThread has already been initialized at this point
                    // SuggestGarbageCollect is skipped when max tick and vsync are not set
                    if (CmdSettings::Inst().gVsync && !EngineGlobal::GetSettingMgr()->GetUseSeparatePresentThread())
                        duration = std::max(duration, static_cast<float>(EngineGlobal::GetEngine()->GetGlobalSystem<FrameSynchronizationSystemG>()->GetVSYNCFrameDuration()) / 1000.f);
                }
                TypeScriptModule::Instance()->Tick(fp->GetGameDeltaTime(), duration);
            }

            frame::AdvanceGameFrame();
            if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeStandAlone)
            {
                if (!mEngineCrashMonitor)
                {
                    mEngineCrashMonitor = std::unique_ptr<EngineCrashMonitor>(new cross::EngineCrashMonitor());
                }

                if (mEngineCrashMonitor && mEngineCrashMonitor->Enable())
                {
                    auto CurrentFrame = EngineGlobal::GetFrameParamMgr()->GetCurrentGameFrameParam();
                    mEngineCrashMonitor->UpdateFrameID(CurrentFrame->GetFrameCount(), CurrentFrame->GetTime(), CurrentFrame->GetDeltaTime());
                }
            }
        }
    }

    IGameWorld* CrossEngine::GetWorldByName(char const* name)
    {
        WorldSystemG* system = TYPE_CAST(WorldSystemG*, GetGlobalSystemByID(WorldSystemG::GetDesc().mID));
        if (system)
        {
            return system->GetWorldByName(name);
        }
        return nullptr;
    }

    bool CrossEngine::Alive(IGameWorld* world)
    {
        WorldSystemG* system = TYPE_CAST(WorldSystemG*, GetGlobalSystemByID(WorldSystemG::GetDesc().mID));
        auto gworld = static_cast<GameWorld*>(world);
        if (gworld && system && system->GetWorldByID(gworld->GetRuntimeID()))
        {
            return (system->GetWorldByID(gworld->GetRuntimeID()) != nullptr);
        }
        return false;
    }

    bool CrossEngine::SetCurrentWorld(IGameWorld* world)
    {
        mCurrentWorld = world;
        return true;
    }

    IGameWorld* CrossEngine::GetCurrentWorld() const
    {
        return mCurrentWorld;
    }

    cross::SettingsManager* CrossEngine::GetSettingManager()
    {
        return EngineGlobal::GetSettingMgr();
    }

    void CrossEngine::FlushRendering()
    {
        GetGlobalSystem<cross::RendererSystemG>()->Flush();
    }
    CROSS_CODE CrossEngine::Init(const cross::InitInfo& info, int argc, char** argv)
    {
        std::vector<std::string> args;
        for (int i = 0; i < argc; i++)
        {
            args.push_back(argv[i]);
        }
        cross::CmdSettings::Inst().Parse(args);
        return Init(info);
    }
}


