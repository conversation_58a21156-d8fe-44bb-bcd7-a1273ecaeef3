#include "EnginePrefix.h"
#include "EngineModuleConfig.h"
#include "CrossBase/FileSystem/PathHelper.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/SettingsManager.h"
#include "Runtime/Interface/ECSConfig.h"
#include <filesystem>
#include "CrossBase/Globals/Globals.h"
#include <codecvt>
namespace cross {
EngineModuleConfig::EngineModuleConfig() {}
EngineModuleConfig::~EngineModuleConfig() 
{

}
void EngineModuleConfig::EnumerateModulesFromConfig(const cross::SerializeNode& engineConfig, const cross::SerializeNode& projectConfig, cross::AppStartUpType type)
{
#if CROSSENGINE_WIN || CROSSENGINE_OSX 
    DeserializeNode result;
    // Use project config override module settings
    if (projectConfig.HasMember("BuildingBlockDirectories"))
    {
        result = projectConfig.HasMember("BuildingBlockDirectories").value();
    }
    else if (engineConfig.HasMember("BuildingBlockDirectories"))
    {
        result = engineConfig.HasMember("BuildingBlockDirectories").value();
    }

    for (int32_t i = 0; i != result.Size(); i++)
    {
        Assert(result.At(i).IsString());
        const auto& modulepath = result.At(i).AsString();
        std::filesystem::path buildingblockPath{PathHelper::GetEngineResourceDirectoryPath()};
        buildingblockPath = buildingblockPath.parent_path();
        auto configfile = PathHelper::Combine(buildingblockPath.string().c_str(), modulepath.c_str()).append("/BuildingBlock.json");
        auto moduleconfig = EngineGlobal::GetSettingMgr()->LoadConfigFile(configfile);

        if (!moduleconfig.has_value())
        {
            // try project config
            configfile = PathHelper::Combine(PathHelper::GetCurrentDirectoryPath().c_str(), modulepath.c_str()).append("/BuildingBlock.json");
            moduleconfig = EngineGlobal::GetSettingMgr()->LoadConfigFile(configfile);
        }

        if (moduleconfig.has_value())
        {
            EngineModuleInfo moduleinfo;
            moduleinfo.mEnable = moduleconfig->HasMember("enable")->AsBoolean();
            if (moduleconfig->HasMember("Systemlist").has_value())
            {
                moduleinfo.mGameSystemList = moduleconfig->HasMember("Systemlist").value();
            }
            if (moduleconfig->HasMember("Dependencies").has_value())
            {
                moduleinfo.mDependencies = moduleconfig->HasMember("Dependencies").value();
            }
            if (moduleconfig->HasMember("ComponentPairs").has_value())
            {
                moduleinfo.mComponentPairs = moduleconfig->HasMember("ComponentPairs").value();
            }
            if (moduleconfig->HasMember("GlobalSystemConfig").has_value())
            {
                moduleinfo.mGlobalSystemList = moduleconfig->HasMember("GlobalSystemConfig").value();
            }
            if (type == AppStartUpTypeThumbnailProcessor)
            {
                if (moduleconfig->HasMember("name")->AsString() == "CEEditorRuntime")
                    mModuleListFromConfig.emplace(UniqueString(moduleconfig->HasMember("name")->AsString().c_str()), std::move(moduleinfo));
            }
            else
            {
                mModuleListFromConfig.emplace(UniqueString(moduleconfig->HasMember("name")->AsString().c_str()), std::move(moduleinfo));
            }
        }
    }
#endif
}
void EngineModuleConfig::LoadAllConfigModules()
{
#if (CROSSENGINE_WIN || CROSSENGINE_OSX)

    auto FoundModulePath = [](std::string module_name) {
        // change it to dll
        if (module_name.substr(module_name.find_last_of(".") + 1) != "dll")
            module_name += ".dll";

        // first engine path search
        if (PathHelper::IsFileExist("./"+module_name, true))
            return module_name;

        std::string binary = PathHelper::GetEngineBinaryDirectoryPath() + "/" + module_name;
        if (PathHelper::IsFileExist(binary))
            return binary;

#ifdef GENERATOR_PATH 
        #ifdef CMAKE_INTDIR
        // project path search
        binary = PathHelper::GetCurrentDirectoryPath() + "/bin/" + GENERATOR_PATH + "/" + CMAKE_INTDIR + "/" + module_name;
        if (PathHelper::IsFileExist(binary))
            return binary;
        #endif
#endif
        return std::string("");
    };

    for (auto& [name, moduleinfo] : mModuleListFromConfig)
    {
        if (moduleinfo.mEnable)
        {   
            auto moddule_path = FoundModulePath(name.GetCString());


            if (!moddule_path.empty())
            {
#ifdef CROSSENGINE_WIN
                std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>> converter;
                auto directory = PathHelper::GetDirectoryFromAbsolutePath(moddule_path);
                // be careful with the difference between SetDLLDirectory and the AddDllDirectory
                auto dll_cookie = AddDllDirectory(converter.from_bytes(directory).c_str());
#endif

                auto result = cross::GameFramework()->LoadDynamicModule(moddule_path, name.GetCString());
                if (result)
                {
                    moduleinfo.mLoadedModule = result;
                    moduleinfo.mModuleFullPath = moddule_path;
                    if (!moduleinfo.mComponentPairs.IsNull())
                    {
                        EngineGlobal::GetECSConfig()->RegisterComponentPairByConfig(moduleinfo.mComponentPairs);
                    }

                    LOG_INFO("Module {} Loaded.",name.GetCString());
                }
                else
                {
                    moduleinfo.mEnable = false;   // Disable this
                    
                    LOG_ERROR("Load Module {} failed", name.GetCString());
                    Assert(false);
                }

#ifdef CROSSENGINE_WIN
                // revert to default path, to avoid possible change
                //SetDllDirectory(default_directory);
                if (dll_cookie)
                {
                    RemoveDllDirectory(dll_cookie);
                }
#endif
            }
            else
            {
                moduleinfo.mEnable = false;
                LOG_ERROR("Cannot found Module {} file", name.GetCString());
            }
        }
    }
#endif
}
void EngineModuleConfig::InitModules() {
    for (auto& [name, moduleinfo] : mModuleListFromConfig)
    {
        if (moduleinfo.mEnable)
        {
            moduleinfo.mLoadedModule->Init();
        }
    }

}
}   // namespace crossk