#pragma once
#include "AssetPipeline/PCH/AssetPipelinePCH.h"
#include "CECommon/Common/MeshDefines.h"
#include "Resource/AssetFileHeader.h"
#include "AssetPipeline/Import/AssetImporter.h"
#include "AssetPipeline/Import/ModelImporter/MeshDescription.h"
#include "CrossBase/Serialization/ResourceMetaHeader.h"
#include "PhysicsEngine/PhysicsImportStruct.h"
#include "Resource/MeshAssetData.h"

#ifdef CROSSEDITOR_FBXNEWIMPORTER
// this file is for serialize static mesh to json/bson
using namespace cross::serialize;
#endif
namespace cross::editor {

struct ImportMeshCollisionNode
{
    int Index;
    int LeftIndex;
    int RightIndex;
    MeshBound Bound;
    std::vector<int> TriangleList;

    bool SeriallizeToJson(json& j)
    {
        j["Index"] = Index;
        j["LeftIndex"] = LeftIndex;
        j["RightIndex"] = RightIndex;
        Bound.SeriallizeToJson(j["Bound"]);
        j["TriangleList"] = TriangleList;
        return true;
    }

    ImportMeshCollisionNode& operator=(const ImportMeshCollisionNode& value)
    {
        if (this != &value)
        {
            this->Index = value.Index;
            this->LeftIndex = value.LeftIndex;
            this->RightIndex = value.RightIndex;
            this->Bound = value.Bound;
            TriangleList.resize(value.TriangleList.size());
            std::copy(value.TriangleList.begin(), value.TriangleList.end(), TriangleList.begin());
        }
        return *this;
    }
};

struct CustomAttributeInfo
{
    UInt32 mKeyNameHash{0};
    UInt32 mDataFlag{0};
    UInt32 mDataSizeInByte{0};
    UInt32 mDataOffset{0};

    bool SeriallizeToJson(json& j)
    {
        j["KeyNameHash"] = mKeyNameHash;
        j["DataFlag"] = mDataFlag;
        j["DataSizeInByte"] = mDataSizeInByte;
        j["DataOffset"] = mDataOffset;

        return true;
    }
};

struct VertexChannelAssetData
{
    UInt16 mStride{0};
    UInt16 mFrequency{1};
    SInt16 mStream{-1};
    SInt16 mStreamOffset{-1};
    UInt64 mMiscFlag{0};
    UInt64 mReserve0{0};
    UInt64 mReserve1{0};
    UInt32 mVertexChannel;
    UInt32 mDataFormat;
    std::vector<UInt8> mData;

    bool SeriallizeDataToJson(json& j)
    {
        if (mDataFormat == (UInt32)VertexFormat::Float || mDataFormat == (UInt32)VertexFormat::Float2 || mDataFormat == (UInt32)VertexFormat::Float3 || mDataFormat == (UInt32)VertexFormat::Float4)
        {
            int size = static_cast<int>(mData.size() / sizeof(float));
            std::vector<float> vecf;
            vecf.resize(size);
            float* mData32 = reinterpret_cast<float*>(mData.data());
            for (int i = 0; i < size; i++)
            {
                vecf[i] = mData32[i];
            }
            j["Data"] = vecf;
        }
        else
        {
            j["Data"] = mData;
        }

        return true;
    }

    bool SeriallizeToJson(json& j)
    {
        j["Stride"] = mStride;
        j["Frequency"] = mFrequency;
        j["Stream"] = mStream;
        j["StreamOffset"] = mStreamOffset;
        j["MiscFlag"] = mMiscFlag;
        j["Reserve0"] = mReserve0;
        j["Reserve1"] = mReserve1;
        j["VertexChannel"] = mVertexChannel;
        j["DataFormat"] = mDataFormat;
        // j["Data"]         =   mData;
        SeriallizeDataToJson(j);
        return true;
    }
};

struct IndexStreamAssetData
{
    UInt32 mCount{0};
    std::vector<UInt8> mData;
    bool mIs16BitIndex{true};

    UInt32 GetCount()
    {
        if (mIs16BitIndex)
        {
            return (UInt32)(mData.size() / 2);
        }
        else
        {
            return (UInt32)(mData.size() / 4);
        }
    }

    UInt32 At(UInt32 pos)
    {
        if (mIs16BitIndex)
        {
            UInt16* mData16 = reinterpret_cast<UInt16*>(mData.data());
            return (UInt32)mData16[pos];
        }
        else
        {
            UInt32* mData32 = reinterpret_cast<UInt32*>(mData.data());
            return (UInt32)mData32[pos];
        }
    }

    void AddIndices(const std::vector<UInt32>& inData)
    {
        if (inData.size() + mCount > 0xFFFF && mIs16BitIndex == true)
        {
            Convent16BitTo32Bit();
        }
        if (mIs16BitIndex == true)
        {
            assert(mCount == mData.size() / sizeof(UInt16));
            mData.resize((mCount + inData.size()) * sizeof(UInt16));
            UInt16* mData16 = reinterpret_cast<UInt16*>(mData.data());
            for (int i = 0; i < inData.size(); i++)
            {
                mData16[mCount + i] = (UInt16)inData[i];
            }
            mCount = mCount + (UInt32)inData.size();
        }
        else
        {
            assert(mCount == mData.size() / sizeof(UInt32));
            mData.resize((mCount + inData.size()) * sizeof(UInt32));
            UInt32* mData32 = reinterpret_cast<UInt32*>(mData.data());
            for (int i = 0; i < inData.size(); i++)
            {
                mData32[mCount + i] = inData[i];
            }
            mCount = mCount + (UInt32)inData.size();
        }
    }

    void Convent16BitTo32Bit()
    {
        if (mIs16BitIndex == true)
        {
            assert(mCount == mData.size() / sizeof(UInt16));
        }
        else
        {
            assert(mCount == mData.size() / sizeof(UInt32));
            return;
        }
        if (mCount == 0)
        {
            mIs16BitIndex = false;
            return;
        }
        std::vector<UInt8> tData;
        tData.resize(mCount * sizeof(UInt32));
        UInt16* mData16 = reinterpret_cast<UInt16*>(mData.data());
        UInt32* tData32 = reinterpret_cast<UInt32*>(tData.data());
        for (int i = 0; i < mData.size() / 2; i++)
        {
            tData32[i] = mData16[i];
        }
        mData.resize(mCount * sizeof(UInt32));
        std::copy(tData.begin(), tData.end(), mData.begin());
        mIs16BitIndex = false;   // set to 32bit flag
    }

    bool SeriallizeDataToJson(json& j)
    {
        int count = GetCount();
        std::vector<UInt32> tData;
        tData.resize(count);
        for (int i = 0; i < count; i++)
        {
            tData[i] = At(i);
        }
        j = tData;
        return true;
    }

    bool SeriallizeToJson(json& j)
    {
        j["Count"] = mCount;
        j["Is16BitIndex"] = mIs16BitIndex;
        // j["Data"] = mData;
        SeriallizeDataToJson(j["Data"]);
        return true;
    }
};

struct MeshPartAssetInfo
{
    SInt16 mNameIndex{-1};
    SInt16 mMaterialIndex{-1};
    UInt32 mVertexStart{0};
    UInt32 mVertexCount{0};
    UInt32 mIndexStart{0};
    UInt32 mIndexCount{0};
    UInt32 mPrimitiveCount{0};
    MeshBound mMeshBound;
    UInt64 mMiscFlag{0};
    float mShadowBias{0};
    float mShadowNormalBias{0};
    UInt32 mPrimitiveType{0};
    UInt8 mRenderPriority{0};

    CustomAttributeInfo mCustomAttributeInfo;
    std::vector<UInt8> mCustomAttributeData;
    std::vector<ImportMeshCollisionNode> mCollisionTree;

    BlendShapeInfo mBlendShape;

    bool SeriallizeToJson(json& j)
    {
        j["NameIndex"] = mNameIndex;
        j["MaterialIndex"] = mMaterialIndex;
        j["VertexStart"] = mVertexStart;
        j["VertexCount"] = mVertexCount;
        j["IndexStart"] = mIndexStart;
        j["IndexCount"] = mIndexCount;
        j["PrimitiveCount"] = mPrimitiveCount;
        mMeshBound.SeriallizeToJson(j["BindingInfo"]);

        j["MiscFlag"] = mMiscFlag;
        j["ShadowBias"] = mShadowBias;
        j["ShadowNormalBias"] = mShadowNormalBias;
        j["PrimitiveType"] = mPrimitiveType;
        j["RenderPriority"] = mRenderPriority;
        j["ShadowNormalBias"] = mShadowNormalBias;

        mCustomAttributeInfo.SeriallizeToJson(j["CustomAttributeInfo"]);
        j["CustomAttributeData"] = mCustomAttributeData;
        return true;
    }
};

enum class MeshAssetUVFlags : UInt32
{
    UVNone = 0x0000,
    UVChannel0 = 0x0001,
    UVChannel1 = 0x0002,
    UVChannel2 = 0x0004,
    UVChannel3 = 0x0008
};
MeshAssetUVFlags operator&(MeshAssetUVFlags lhs, MeshAssetUVFlags rhs);
MeshAssetUVFlags& operator|=(MeshAssetUVFlags& lhs, MeshAssetUVFlags rhs);


class ASSET_API MeshAssetData final
{
public:
    MeshAssetData() = default;
    ~MeshAssetData() = default;

    bool AddVertexPosition(VertexChannel posChannel, std::vector<CrossSchema::float3>& vertexPosVec);

    bool AddVertexTexCoord(VertexChannel texCoordChannel, std::vector<CrossSchema::float2>& texCoordVec);

    bool AddVertexTexCoord(VertexChannel texCoordChannel, std::vector<CrossSchema::float3>& texCoordVec);

    bool AddVertexTexCoord(VertexChannel texCoordChannel, std::vector<CrossSchema::float4>& texCoordVec);

    bool AddVertexColor(VertexChannel colorChannel, std::vector<UInt32>& colorVec);

    bool AddVertexNormal(VertexChannel normalChannel, std::vector<CrossSchema::float3>& normalVec);

    bool AddVertexNormal(VertexChannel normalChannel, std::vector<CrossSchema::float4>& normalVec);

    bool AddVertexTangent(VertexChannel tangentChannel, std::vector<CrossSchema::float4>& tangentVec);

    bool AddVertexBiNormal(VertexChannel binormalChannel, std::vector<CrossSchema::float4>& binormalVec);

    bool AddVertexBoneWeights(VertexChannel boneWeightChannel, std::vector<CrossSchema::float4> boneWeightVec);

    bool AddVertexBoneIds(VertexChannel boneIdChannel, std::vector<Short4> boneIdVec);

    bool AddVertexQuatTangent(VertexChannel quatTangentChannel, std::vector<CrossSchema::float4>& quatTangentVec);

    bool AddBlendShape(UInt32 meshPartIndex, const BlendShapeDeformer& blendShape);

    template<class T1, class T2>
    bool AddChannelData(VertexSemantic semantic, VertexChannel channel, VertexFormat format, std::vector<T1>& inVec)
    {
        if (!IfExistChannel(channel))
        {
            mVertexChannelSemanticMask |= static_cast<UInt32>(semantic);
            mVertexChannelData.emplace_back();
            VertexChannelAssetData& vertexChannel = mVertexChannelData.back();
            vertexChannel.mStride = sizeof(T2);
            vertexChannel.mVertexChannel = static_cast<UInt32>(channel);
            vertexChannel.mDataFormat = static_cast<UInt32>(format);
            vertexChannel.mData.clear();
        }

        VertexChannelAssetData& vertexChannel = GetChannelAssetData(channel);
        auto orgSize = static_cast<UInt32>(vertexChannel.mData.size());
        vertexChannel.mData.resize(orgSize + static_cast<UInt32>(vertexChannel.mStride) * inVec.size());
        assert(vertexChannel.mData.size() == orgSize + inVec.size() * sizeof(T2));
        memcpy(vertexChannel.mData.data() + orgSize, inVec.data(), inVec.size() * sizeof(T2));

        return true;
    }

    inline void AddIndexStream(const std::vector<UInt32>& indicesVec)
    {
        mIndexStream.AddIndices(indicesVec);
        // Correct? what if triangle is not define by indices?
        mPrimitiveCount = mIndexStream.GetCount() / 3;
    }

    inline const MeshPartAssetInfo& GetMeshPartInfo(UInt32 index) const
    {
        return mMeshPartInfo[index];
    }

    inline const IndexStreamAssetData& GetIndexStream() const
    {
        return mIndexStream;
    }

    inline const VertexChannelAssetData* GetVertexChannelData(VertexChannel vertexChannel) const
    {
        for (auto i = 0; i < mVertexChannelData.size(); i++)
        {
            if (mVertexChannelData[i].mVertexChannel == static_cast<UInt32>(vertexChannel))
            {
                return &mVertexChannelData[i];
            }
        }
        return nullptr;
    }

    const UInt32 GetVertexCount(VertexChannel posChannel  = VertexChannel::Position0) const;

    inline UInt8 GetLodCount() const
    {
        return static_cast<UInt8>(mMeshPartLodStartIndex.size());
    }

    inline void AddCollisionTree(ImportMeshCollisionNode& importCollision)
    {
        mCollisionTree.emplace_back(importCollision);
    }

    inline void AddCollisionTree(UInt32 meshPartIndex, ImportMeshCollisionNode& importCollision)
    {
        this->mMeshPartInfo[meshPartIndex].mCollisionTree.emplace_back(importCollision);
    }

    inline void SetName(const std::string& name)
    {
        mName.clear();
        mName.append(name.c_str());
    }

    inline void AddDependencys(std::string dep)
    {
        mDependencies.push_back(dep);
    }

    void GenerateMeshPartCluster();
public:
    struct GetRawVertexAndIndexDataOutput
    {
        const UInt8* vertexData = nullptr;
        UInt32 vertexCount = 0;
        UInt16 vertexStride = 0;

        const UInt8* indexData = nullptr;
        UInt32 indexCount = 0;
        UInt16 indexStride = 0;
    };

    struct MeshCompileSettings
    {
        bool UseFullPrecisionUV = false;
        bool UseFullPrecisionTangent = false;
        // store normal and tangents use QTangents.
        bool UseQTangents = false;

        //
        bool GenCollisionTree = true;
        bool UseTextureArray = false;
    };

    GetRawVertexAndIndexDataOutput GetRawVertexAndIndexData(UInt32 lod) const;

    bool ToImportMeshAssetDataT(CrossSchema::ImportMeshAssetDataT& meshAssetDataT);

    void AddSubMeshBegin(const MeshDescription& meshDesc);

    void AddSubMeshEnd(const MeshDescription& meshDesc);

    void AddSubMeshEnd(const MeshBound& meshBound);

    bool AddVertexNormalFullPrecision(VertexChannel normalChannel, std::vector<CrossSchema::float3>& normalVec)
    {
        if (!(normalChannel >= VertexChannel::Normal0) && (normalChannel <= VertexChannel::NormalLast))
        {
            return false;
        }

        return AddChannelData<CrossSchema::float3, CrossSchema::float3>(SemanticNormal, normalChannel, VertexFormat::Float3, normalVec);
    }

    bool AddVertexTangentFullPrecision(VertexChannel tangentChannel, std::vector<CrossSchema::float4>& tangentVec)
    {
        if (!(tangentChannel >= VertexChannel::Tangent0) && (tangentChannel <= VertexChannel::TangentLast))
        {
            return false;
        }

        return AddChannelData<CrossSchema::float4, CrossSchema::float4>(SemanticTangent, tangentChannel, VertexFormat::Float4, tangentVec);
    }
    bool CheckBigPosition(ImportScene& importScene, const std::vector<UInt32>& meshDescIndices);

    void InitBySpecificMeshDescs(ImportScene& importScene, MeshCompileSettings compile_seeting, const std::vector<UInt32>& meshDescIndices);

    void InitByLightPointMesh(ImportScene& importScene, MeshCompileSettings compile_seeting, const std::vector<UInt32>& meshDescIndices);

    void InitByImportScene(ImportScene& importScene, MeshCompileSettings compile_seeting);

    void InitPhysicsOnly(ImportScene& importScene);
    void InitEmpty();

    bool SerializeToFlatbufferFile(const std::string& ndaFilePath, const std::string& importSet);

    static bool DeSerializeToFlatbufferFile(const std::string& ndaFilePath, CrossSchema::ImportMeshAssetDataT& importSet); 

    bool SerializeToJson(json& j);

    void SetIsStreamFile(bool inNewValue ) { mIsStreamFile = inNewValue ;}

    void SetMeshStreamable(bool enabled) { mIsStreamable = enabled; }

    PhysicsCollisionImport& GetPhysicsCollision();

    void SetPhysicsMeshCollision(const PhysicsMeshCollisionImport& meshColl);

    void AddVersionFlag(UInt32 ver)
    {
        mVersion = mVersion | ver;
    };

    bool HaveVersionFlag(UInt32 ver) const
    {
        if (ver == 0)
            return true;
        return (mVersion & ver) > 0;
    };

private:
    const UInt32 GetMeshPartCount(UInt32 lod) const;

    void GetMeshLodInfo(UInt32 lod, UInt32& meshPartStartIndex, UInt32& meshPartCount) const;

    VertexChannelAssetData& GetChannelAssetData(VertexChannel channel);

    const VertexChannelAssetData& GetChannelAssetData(VertexChannel channel) const;

    const bool IfExistChannel(VertexChannel channel) const;

private:
    std::string mName;
    UInt32 mVersion{0};
    UInt32 mVertexCount{0};
    UInt32 mPrimitiveCount{0};
    UInt32 mVertexChannelSemanticMask{0};
    UInt16 mCustomAttributeVersion{0};
    UInt16 mCustomAttributeVersionFlag{0};

    std::vector<VertexChannelAssetData> mVertexChannelData;
    std::vector<MeshPartAssetInfo> mMeshPartInfo;
    std::vector<UInt32> mMeshPartLodStartIndex;
    std::vector<std::string> mMaterialNames;
    std::vector<std::string> mMeshPartNames;
    std::vector<ImportMeshCollisionNode> mCollisionTree;   // shoule be remove
    std::vector<std::string> mDependencies;
    std::vector<UInt8> mCustomAttribute;

    IndexStreamAssetData mIndexStream;
    PhysicsCollisionImport mPhysicsCollision;
    MeshBound mAABB;
    SkeletonDesc mRefSkeleton;
    CustomAttributeInfo mCustomAttributeInfo;
    bool mIsStreamFile = false; // Maybe unused?
    bool mIsStreamable = false;

    MeshCompileSettings mMeshCompileSettings;
};

}   // namespace cross::editor
