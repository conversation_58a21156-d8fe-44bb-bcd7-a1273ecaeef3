#include "ImportCurveData.h"
#include "Resource/ResourceManager.h"

namespace cross::editor {
    
    void CurveDataDesc::Reset()
    {
        EntityMap.clear();
        Res = gResourceMgr.CreateTempResourceAs<CurveControllerRes>();
    }

    void CurveDataDesc::SetupSystemProperty(CurveDataDesc::SystemType type, cross::CurveControllerDataItem& item)
    {
        if (type == SystemType::Unkonw || type == SystemType::Count)
            return;

        std::map<SystemType, const char*> SystemNameMap{
            {SystemType::Transform, "cross::TransformSystemG"},
            {SystemType::Camera, "cross::CameraSystemG"},
            {SystemType::Light, "cross::LightSystemG"},
        };

        std::map<SystemType, const char*> ComponentNameMap{
            {SystemType::Transform, "CrossEditor.Transform"},
            {SystemType::Camera, "CrossEditor.Camera"},
            {SystemType::Light, "CrossEditor.Light"},
        };

        item.SystemName = SystemNameMap[type];
        item.ComponentName = ComponentNameMap[type];
    }

}
