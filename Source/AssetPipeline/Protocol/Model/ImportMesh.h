#pragma once

/////////////////////////////////////////////////////
// Legacy
/////////////////////////////////////////////////////

namespace cross::editor 
{
	class ImportMeshAttributeBase
	{
	public:
		ImportMeshAttributeBase(const std::string& name) :mName(name) {}
		virtual ~ImportMeshAttributeBase() {}

	public:
		virtual void reserve(UInt32 n) = 0;
		virtual void resize(UInt32 n) = 0;
		virtual void clear() = 0;
		virtual size_t  nelements() const = 0;
		virtual void Serialize(cross::SimpleSerializer& s) const = 0;
		std::string mName;
	};

	template<typename attribute>
	class  ImportMeshAttribute :public ImportMeshAttributeBase
	{
	public:
		typedef attribute value;
		typedef std::vector<attribute> vectortype;
		ImportMeshAttribute(const std::string& name = "none")
			: ImportMeshAttributeBase(name) {}
		ImportMeshAttribute(vectortype vec, const std::string& name = "none")
			:ImportMeshAttributeBase(name) {
			mData = std::move(vec);
		}
		~ImportMeshAttribute() { mData.clear(); }

		virtual void reserve(UInt32 n) { mData.reserve(n); }
		virtual void resize(UInt32 n) { mData.resize(n); }
		virtual void clear() { mData.clear(); }
		virtual size_t nelements() const { return mData.size(); }
		virtual void Serialize(cross::SimpleSerializer& s) const { }
		void Append(ImportMeshAttribute<value>& rhs)
		{
			mData.insert(mData.end(), rhs.datavector().begin(), rhs.datavector().end());
		}
		vectortype& datavector() { return mData; }
	private:
		vectortype mData;
	};

	template<>
	class  ImportMeshAttribute<cross::data::Vec3f> :public ImportMeshAttributeBase
	{
	public:
		typedef cross::data::Vec3f value;
		typedef std::vector<cross::data::Vec3f> vectortype;
		ImportMeshAttribute(const std::string& name = "none")
			:ImportMeshAttributeBase(name) {
			mData = vectortype();
		}
		ImportMeshAttribute(vectortype vec, const std::string& name = "none")
			:ImportMeshAttributeBase(name) {
			mData = std::move(vec);
		}
		~ImportMeshAttribute()
		{
			mData.clear();
		}

		virtual void reserve(UInt32 n) { mData.reserve(n); }
		virtual void resize(UInt32 n) { mData.resize(n); }
		virtual void clear() { mData.clear(); }
		virtual size_t nelements() const { return  mData.size(); }
		virtual void Serialize(cross::SimpleSerializer& s) const
		{
			s.Write(mName);
			s.Write(mData.size());
			s.Write(mData);
		}
		vectortype& datavector() { return mData; }
		void Append(ImportMeshAttribute<value>& rhs)
		{
			mData.insert(mData.end(), rhs.datavector().begin(), rhs.datavector().end());
		}
	private:
		vectortype mData;
	};

	template<>
	class ImportMeshAttribute<cross::data::Vec2f> :public ImportMeshAttributeBase
	{
	public:
		typedef cross::data::Vec2f value;
		typedef std::vector<cross::data::Vec2f> vectortype;

		ImportMeshAttribute(const std::string& name = "none")
			: ImportMeshAttributeBase(name) {
			mData = vectortype();
		}
		ImportMeshAttribute(vectortype vec, const std::string& name = "none")
			:ImportMeshAttributeBase(name) {
			mData = std::move(vec);
		}
		~ImportMeshAttribute()
		{
			mData.clear();
		}

		virtual void reserve(UInt32 n) { mData.reserve(n); }
		virtual void resize(UInt32 n) { mData.resize(n); }
		virtual void clear() { mData.clear(); }
		virtual size_t nelements() const { return mData.size(); }
		virtual void Serialize(cross::SimpleSerializer& s) const
		{
			s.Write(mName);
			s.Write(mData.size());
			s.Write(mData);
		}

		vectortype& datavector() { return mData; }
		void Append(ImportMeshAttribute<value>& rhs)
		{
			mData.insert(mData.end(), rhs.datavector().begin(), rhs.datavector().end());
		}
	private:
		vectortype mData;
	};

	template<>
	class ImportMeshAttribute<cross::data::Vec4f> :public ImportMeshAttributeBase
	{
	public:
		typedef cross::data::Vec4f value;
		typedef std::vector<cross::data::Vec4f> vectortype;

		ImportMeshAttribute(const std::string& name = "none")
			:ImportMeshAttributeBase(name) {
			mData = vectortype();
		}
		ImportMeshAttribute(vectortype vec, const std::string& name = "none")
			:ImportMeshAttributeBase(name) {
			mData = std::move(vec);
		}
		~ImportMeshAttribute()
		{
			mData.clear();
		}

		virtual void reserve(UInt32 n) { mData.reserve(n); }
		virtual void resize(UInt32 n) { mData.resize(n); }
		virtual void clear() { mData.clear(); }
		virtual size_t nelements() const { return mData.size(); }
		virtual void Serialize(cross::SimpleSerializer& s) const
		{
			s.Write(mName);
			s.Write(mData.size());
			s.Write(mData);
		}

		vectortype& datavector() { return mData; }
		void Append(ImportMeshAttribute<value>& rhs)
		{
			mData.insert(mData.end(), rhs.datavector().begin(), rhs.datavector().end());
		}
	private:
		vectortype mData;
	};

	template<>
	class ImportMeshAttribute<UInt32> :public ImportMeshAttributeBase
	{
	public:
		typedef UInt32 value;
		typedef std::vector<UInt32> vectortype;
		ImportMeshAttribute(const std::string& name = "none")
			: ImportMeshAttributeBase(name) {
			mData = vectortype();
		}
		ImportMeshAttribute(vectortype vec, const std::string& name = "none")
			:ImportMeshAttributeBase(name) {
			mData = std::move(vec);
		}
		~ImportMeshAttribute()
		{
			mData.clear();
		}

		virtual void reserve(UInt32 n) { mData.reserve(n); }
		virtual void resize(UInt32 n) { mData.resize(n); }
		virtual void clear() { mData.clear(); }
		virtual size_t nelements() const { return mData.size(); }
		virtual void Serialize(cross::SimpleSerializer& s) const
		{
			s.Write(mName);
			s.Write(mData.size());
			s.Write(mData);
		}

		vectortype& datavector() { return mData; }
		void Append(ImportMeshAttribute<value>& rhs)
		{
			mData.insert(mData.end(), rhs.datavector().begin(), rhs.datavector().end());
		}
	private:
		vectortype mData;
	};

	template<>
	class ImportMeshAttribute<cross::data::Vec4u> :public ImportMeshAttributeBase
	{
	public:
		typedef cross::data::Vec4u value;
		typedef std::vector<cross::data::Vec4u> vectortype;

		ImportMeshAttribute(const std::string& name = "none")
			:ImportMeshAttributeBase(name) {
			mData = vectortype();
		}
		ImportMeshAttribute(vectortype vec, const std::string& name = "none")
			:ImportMeshAttributeBase(name) {
			mData = std::move(vec);
		}
		~ImportMeshAttribute()
		{
			mData.clear();
		}

		virtual void reserve(UInt32 n) { mData.reserve(n); }
		virtual void resize(UInt32 n) { mData.resize(n); }
		virtual void clear() { mData.clear(); }
		virtual size_t nelements() const { return mData.size(); }
		virtual void Serialize(cross::SimpleSerializer& s) const
		{
			s.Write(mName);
			s.Write(mData.size());
			s.Write(mData);
		}

		vectortype& datavector() { return mData; }
		void Append(ImportMeshAttribute<value>& rhs)
		{
			mData.insert(mData.end(), rhs.datavector().begin(), rhs.datavector().end());
		}
	private:
		vectortype mData;
	};


	using HandleVertex = int;

	class ImportMeshAttributeContainer
	{
	public:

		typedef std::vector<std::shared_ptr<ImportMeshAttributeBase>> ImportMeshAttributes;
		typedef ImportMeshAttributes::iterator       iterator;
		typedef ImportMeshAttributes::const_iterator const_iterator;

		ImportMeshAttributeContainer() {}
		virtual ~ImportMeshAttributeContainer() { mAttributes.clear(); }

		template<typename T>
		cross::THandle<T, UInt32> add(const std::string& name)
		{
			ImportMeshAttributes::iterator p_it = mAttributes.begin(), p_end = mAttributes.end();
			UInt32 idx = 0;
			for (; p_it != p_end && *p_it != nullptr; ++p_it, ++idx) {};
			if (p_it == p_end) mAttributes.push_back(nullptr);
			mAttributes[idx] = std::shared_ptr<ImportMeshAttributeBase>{ new ImportMeshAttribute<T>(name) };
			cross::THandle<T, UInt32> handle;
			handle.mVal = idx;
			return handle;
		}

		template<typename T>
		bool swap(ImportMeshAttribute<T>& attrib, HandleVertex handle)
		{
			if (mAttributes[handle] != nullptr)
			{
				ImportMeshAttribute<T>* attribute = dynamic_cast<ImportMeshAttribute<T>*>(mAttributes[handle].get());
				attribute->datavector().swap(attrib.datavector());
				return true;
			}
			return false;
		}

		template<typename T>
		ImportMeshAttribute<T>& Attribute(HandleVertex handle)
		{
			ImportMeshAttribute<T>* res = dynamic_cast<ImportMeshAttribute<T>*>(mAttributes[handle].get());
			return *res;
		}

		void Serialize(cross::SimpleSerializer& s) const
		{
			ImportMeshAttributes::const_iterator p_it = mAttributes.begin();
			for (; p_it != mAttributes.end(); ++p_it)
			{
				(*p_it)->Serialize(s);
			}
		}

		void ClearData()
		{
			ImportMeshAttributes::iterator p_it = mAttributes.begin();
			for (; p_it != mAttributes.end(); ++p_it)
			{
				(*p_it)->clear();
			}
		}

		const ImportMeshAttributes& Attributes()const { return mAttributes; }
	private:
		ImportMeshAttributes   mAttributes;
	};

	namespace Vertex
	{
		const std::string Position("pos");
		const std::string Normal("normal");
		const std::string UV1("uv1");
		const std::string UV2("uv2");
		const std::string UV3("uv3");
		const std::string UV4("uv4");
		const std::string Color("color");
		const std::string Tangent("tangent");
		const std::string Binormal("Binormal");
		const std::string BoneWeight("BoneWeight");
		const std::string BoneId("BoneId");
	}

	class ImportMeshVertexDescription
	{
	public:
		ImportMeshVertexDescription()
		{
			mVertexAttributes = std::make_unique<ImportMeshAttributeContainer>();
			mPos = mVertexAttributes->add<cross::data::Vec3f>(Vertex::Position);
			mNormal = mVertexAttributes->add<cross::data::Vec3f>(Vertex::Normal);
			mCol = mVertexAttributes->add<UInt32>(Vertex::Color);
			mUV1 = mVertexAttributes->add<cross::data::Vec2f>(Vertex::UV1);
			mUV2 = mVertexAttributes->add<cross::data::Vec2f>(Vertex::UV2);
			mUV3 = mVertexAttributes->add<cross::data::Vec2f>(Vertex::UV3);
			mUV4 = mVertexAttributes->add<cross::data::Vec2f>(Vertex::UV4);
			mTangent = mVertexAttributes->add<cross::data::Vec4f>(Vertex::Tangent);
			mBinormal = mVertexAttributes->add<cross::data::Vec4f>(Vertex::Binormal);
			mBoneWeights = mVertexAttributes->add<cross::data::Vec4f>(Vertex::BoneWeight);
			mBoneIds = mVertexAttributes->add<cross::data::Vec4u>(Vertex::BoneId);
		}

		~ImportMeshVertexDescription()
		{
		}

		bool IsEmpty()
		{
			auto verticepos = mVertexAttributes->Attribute<cross::data::Vec3f>(mPos);
			return verticepos.nelements() == 0;
		}

		std::unique_ptr<ImportMeshAttributeContainer> Clone() const
		{
			return  std::make_unique<ImportMeshAttributeContainer>(*mVertexAttributes);
		}

		void Serialize(cross::SimpleSerializer& s) const
		{
			mVertexAttributes->Serialize(s);
		}

		template<typename T>
		bool IsAttributeValid(const HandleVertex& handle)
		{
			return mVertexAttributes->Attribute<T>(handle).nelements() > 0;
		}

		HandleVertex mUV1;
		HandleVertex mUV2;
		HandleVertex mUV3;
		HandleVertex mUV4;
		HandleVertex mPos;
		HandleVertex mNormal;
		HandleVertex mCol;
		HandleVertex mTangent;
		HandleVertex mBinormal;
		HandleVertex mBoneWeights;
		HandleVertex mBoneIds;

		auto& GetAttributes() { return mVertexAttributes; }

		void AppendVertexData(ImportMeshVertexDescription& rhs)
		{
			mVertexAttributes->Attribute<data::Vec3f>(mPos).Append(rhs.GetAttributes()->Attribute<data::Vec3f>(rhs.mPos));
			mVertexAttributes->Attribute<data::Vec3f>(mNormal).Append(rhs.GetAttributes()->Attribute<data::Vec3f>(rhs.mNormal));
			mVertexAttributes->Attribute<UInt32>(mCol).Append(rhs.GetAttributes()->Attribute<UInt32>(rhs.mCol));
			mVertexAttributes->Attribute<data::Vec2f>(mUV2).Append(rhs.GetAttributes()->Attribute<data::Vec2f>(rhs.mUV2));
			mVertexAttributes->Attribute<data::Vec2f>(mUV1).Append(rhs.GetAttributes()->Attribute<data::Vec2f>(rhs.mUV1));
			mVertexAttributes->Attribute<data::Vec2f>(mUV3).Append(rhs.GetAttributes()->Attribute<data::Vec2f>(rhs.mUV3));
			mVertexAttributes->Attribute<data::Vec2f>(mUV4).Append(rhs.GetAttributes()->Attribute<data::Vec2f>(rhs.mUV4));
			mVertexAttributes->Attribute<data::Vec4f>(mTangent).Append(rhs.GetAttributes()->Attribute<data::Vec4f>(rhs.mTangent));
			mVertexAttributes->Attribute<data::Vec4f>(mBinormal).Append(rhs.GetAttributes()->Attribute<data::Vec4f>(rhs.mBinormal));
			mVertexAttributes->Attribute<data::Vec4f>(mBoneWeights).Append(rhs.GetAttributes()->Attribute<data::Vec4f>(rhs.mBoneWeights));
			mVertexAttributes->Attribute<data::Vec4u>(mBoneIds).Append(rhs.GetAttributes()->Attribute<data::Vec4u>(rhs.mBoneIds));
		}

		void ClearVertexData()
		{
			mVertexAttributes->ClearData();
		}

	private:
		std::unique_ptr<ImportMeshAttributeContainer> mVertexAttributes;

	};

    struct ImportVertexData2UV
    {
        data::Vec3f     Pos = { 0.0f, 0.0f, 0.0f };
        data::Vec3f     Normal = { 0.0f, 1.0f, 0.0f };
        UInt32          Color = 0xff00ffff;
        data::Vec2f     TexCoord = { 0.5f, 0.5f };
        data::Vec2f     TexCoord2 = { 0.5f , 0.5f };;
        data::Vec4f     Tangent = { 1.0f, 0.0f, 0.0f, 1.0f };
    };

    struct ImportVertexData
    {
        data::Vec3f     Pos = { 0.0f, 0.0f, 0.0f };
        data::Vec3f     Normal = { 0.0f, 1.0f, 0.0f };
        UInt32          Color = 0xff00ffff;
        data::Vec2f     TexCoord = { 0.5f, 0.5f };
        data::Vec4f     Tangent = { 1.0f, 0.0f, 0.0f, 1.0f };
    };

    struct ImportIndicesData
    {
        void Init(UInt32 indicesCount, UInt32 vertexCount)
        {
            if (vertexCount > (std::numeric_limits<UInt16>::max)())
            {
                IndexBuffer.resize(indicesCount * sizeof(UInt32));
            }
            else
            {
                IndexBuffer.resize(indicesCount * sizeof(UInt16));
            }
            IndicesCount = indicesCount;
        }

        template <typename T>
        auto GetData() noexcept -> std::enable_if_t<std::is_unsigned_v<T>, T*>
        {
            return reinterpret_cast<T*>(IndexBuffer.data());
        }

        UInt32                          IndicesCount;
        std::vector<UInt8>              IndexBuffer;
    };

	struct ImportMeshBound
	{
		data::Vec3f     Min = { INFINITY, INFINITY, INFINITY };
		data::Vec3f     Max = { -INFINITY, -INFINITY, -INFINITY };

		void Encapsulate(const data::Vec3f& inPoint)
		{
			Min = { (std::min)(Min.x(), inPoint.x()), (std::min)(Min.y(), inPoint.y()), (std::min)(Min.z(), inPoint.z()) };
			Max = { (std::max)(Max.x(), inPoint.x()), (std::max)(Max.y(), inPoint.y()), (std::max)(Max.z(), inPoint.z()) };
		}
	};

    struct ImportMeshCollisionNode
    {
        int                                     Index;
        int                                     LeftIndex;
        int                                     RightIndex;
        ImportMeshBound                         Bound;
        std::vector<int>                        TriangleList;
    };

	struct ImportMeshData
	{
		std::string                             Name;
		ImportIndicesData                       Indices;
		ImportMeshVertexDescription				VertexData;
        std::vector<ImportMeshCollisionNode>    CollisionTree;

		ImportMeshData()
		{
			Indices.IndicesCount = 0;
		}

		ImportMeshData(const ImportMeshData & rhs)
		{
			Name = rhs.Name;
			Indices.IndicesCount = rhs.Indices.IndicesCount;
			Indices.IndexBuffer = rhs.Indices.IndexBuffer;
			VertexData.GetAttributes() = rhs.VertexData.Clone();
			CollisionTree = rhs.CollisionTree;
		}

		ImportMeshData& operator = (const ImportMeshData& rhs)
		{
			Name = rhs.Name;
			Indices.IndicesCount = rhs.Indices.IndicesCount;
			Indices.IndexBuffer = rhs.Indices.IndexBuffer;
			VertexData.GetAttributes() = rhs.VertexData.Clone();
			CollisionTree = rhs.CollisionTree;
			return *this;
		}

		void Serialize(SimpleSerializer& s) const
		{
			s.Write(Name, Indices);
			VertexData.Serialize(s);
            s.Write(CollisionTree);
		}
	};

	struct ImportMeshes
	{
		std::string						Name;
		std::string                     PathToSkeleton;
		std::vector<ImportMeshData>		MeshesData;
		void Serialize(SimpleSerializer& s) const
		{
			s.Write(Name);
			s.Write(PathToSkeleton);
			s.Write(MeshesData.size());
			for (auto&mesh : MeshesData)
			{
				mesh.Serialize(s);
			}
		}
	};

	// for some special reason, the data is used in this format, you can modify this if you're interested
	struct ImportVertexBone
	{
		std::array<float, 4> Weights;
		std::array<int, 4> BoneIndices;
	};

    struct ImportVertexBone2
    {
        std::array<float, 2> Weights;
        std::array<int, 2> BoneIndices;
    };

	struct ImportMeshSkin
	{
		std::vector<ImportVertexBone> vBones;
	};

	struct ImportMeshBones
	{
		std::vector<ImportMeshSkin> meshSkin;
		std::vector<std::vector<int>> meshUsedBones;
	};
}

CROSS_REFLECT(cross::editor::ImportIndicesData, IndicesCount, IndexBuffer);
