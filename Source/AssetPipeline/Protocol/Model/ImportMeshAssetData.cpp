#pragma once
#include "AssetPipeline/PCH/AssetPipelinePCH.h"
#include "CrossBase/Math/QTangents.h"
#include "AssetPipeline/Protocol/Model/ImportMeshAssetData.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/SettingsManager.h"
#include "Resource/MeshletGenerator/D3D12MeshletGenerator.h"
#include "Resource/Resource.h"

#include "Resource/MeshAssetDataResource.h"
#include "Resource/ResourceManager.h"
#include "Resource/AssetStreaming.h"
#include "AssetPipeline/Import/ModelImporter/MeshPickBuilderHelper.h"

namespace cross::editor {

MeshAssetUVFlags operator&(MeshAssetUVFlags lhs, MeshAssetUVFlags rhs)
{
    return static_cast<MeshAssetUVFlags>(static_cast<std::underlying_type<MeshAssetUVFlags>::type>(lhs) & static_cast<std::underlying_type<MeshAssetUVFlags>::type>(rhs));
}

MeshAssetUVFlags& operator|=(MeshAssetUVFlags& lhs, MeshAssetUVFlags rhs)
{
    lhs = static_cast<MeshAssetUVFlags>(static_cast<std::underlying_type<MeshAssetUVFlags>::type>(lhs) | static_cast<std::underlying_type<MeshAssetUVFlags>::type>(rhs));
    return lhs;
}

MeshAssetData::GetRawVertexAndIndexDataOutput MeshAssetData::GetRawVertexAndIndexData(UInt32 lod) const
{
    GetRawVertexAndIndexDataOutput output;
    UInt32 lodCount = GetLodCount();
    Assert(lodCount > 0 && lod < lodCount);

    const VertexChannelAssetData* vertexChannel = GetVertexChannelData(VertexChannel::Position0);
    const IndexStreamAssetData& indexStream = GetIndexStream();

    UInt32 lodMeshPartStartIndex = 0;
    UInt32 lodMeshCount = 0;
    GetMeshLodInfo(lod, lodMeshPartStartIndex, lodMeshCount);
    Assert(lodMeshCount > 0);

    UInt32 vertexStart = GetMeshPartInfo(lodMeshPartStartIndex).mVertexStart;
    output.vertexData = vertexChannel->mData.data() + vertexStart;
    output.vertexCount = 0;
    output.vertexStride = vertexChannel->mStride;

    UInt32 indexStart = GetMeshPartInfo(lodMeshPartStartIndex).mIndexStart;
    output.indexData = indexStream.mData.data() + indexStart;
    output.indexCount = 0;
    output.indexStride = indexStream.mIs16BitIndex ? 2 : 4;

    for (UInt32 meshPart = lodMeshPartStartIndex; meshPart < lodMeshPartStartIndex + lodMeshCount; ++meshPart)
    {
        const MeshPartAssetInfo& part = GetMeshPartInfo(meshPart);
        output.vertexCount += part.mVertexCount;
        output.indexCount += part.mIndexCount;
    }
    return output;
}

template<class T1, class T2, class TypeConvFunc>
void  TypeConverter(const std::vector<T1>& inNormalVec, std::vector<T2>& out, TypeConvFunc func)
{
    out.resize(inNormalVec.size());
    size_t element_dim = T2::Dim();

    for (int i = 0; i < inNormalVec.size(); i++)
    {
        const float* ptr = reinterpret_cast<const float *>(&inNormalVec[i]);
        for (int j = 0; j < element_dim; j++)
        {
            func(ptr[j], out[i].data()[j]);
        }
    }
}


template<class T1, class T2>
void ConvertToSNORM(const std::vector<T1>& inNormalVec, std::vector<T2>& out)
{
    auto conv = [](const float& in, auto& out)
    {
        FloatToSNORM(in, out);
    };

    TypeConverter(inNormalVec, out, conv);
}


bool MeshAssetData::AddVertexPosition(VertexChannel posChannel, std::vector<CrossSchema::float3>& vertexPosVec)
{
    if (posChannel != VertexChannel::Position0 && posChannel != VertexChannel::Position1 && posChannel != VertexChannel::Position2 && posChannel != VertexChannel::Position3)
    {
        return false;
    }

    if (!AddChannelData<CrossSchema::float3, CrossSchema::float3>(SemanticPosition, posChannel, VertexFormat::Float3, vertexPosVec))
        return false;

    mVertexCount = (UInt32)GetVertexCount(posChannel);

    // make sure indices
    if (mVertexCount >= 0x0000FFFF && mIndexStream.mIs16BitIndex == true)
    {
        mIndexStream.Convent16BitTo32Bit();
    }
    return true;
}

bool MeshAssetData::AddVertexTexCoord(VertexChannel texCoordChannel, std::vector<CrossSchema::float2>& texCoordVec)
{
    if (!(texCoordChannel >= VertexChannel::TexCoord0) && (texCoordChannel <= VertexChannel::TexCoordLast))
    {
        return false;
    }

    if (mMeshCompileSettings.UseFullPrecisionUV)
    {
        return AddChannelData<CrossSchema::float2, CrossSchema::float2>(SemanticTexCoord, texCoordChannel, VertexFormat::Float2, texCoordVec);
    }
    else
    {
        std::vector<UShort2> normalized_data;
        FloatToHalfConverter converter;

        auto convertHalf = [&converter](const float& in, auto& out)
        {
            converter.Convert(in, out);
        };

        TypeConverter(texCoordVec, normalized_data, convertHalf);
        return AddChannelData<UShort2, UShort2>(SemanticTexCoord, texCoordChannel, VertexFormat::Half2, normalized_data);
    }
}

bool MeshAssetData::AddVertexTexCoord(VertexChannel texCoordChannel, std::vector<CrossSchema::float3>& texCoordVec)
{
    if (!(texCoordChannel >= VertexChannel::TexCoord0) && (texCoordChannel <= VertexChannel::TexCoordLast))
    {
        return false;
    }

    if (mMeshCompileSettings.UseFullPrecisionUV)
    {
        return AddChannelData<CrossSchema::float3, CrossSchema::float3>(SemanticTexCoord, texCoordChannel, VertexFormat::Float3, texCoordVec);
    }
    else
    {
        std::vector<UShort3> normalized_data;
        FloatToHalfConverter converter;

        auto convertHalf = [&converter](const float& in, auto& out) { converter.Convert(in, out); };

        TypeConverter(texCoordVec, normalized_data, convertHalf);
        return AddChannelData<UShort3, UShort3>(SemanticTexCoord, texCoordChannel, VertexFormat::Half3, normalized_data);
    }
}

bool MeshAssetData::AddVertexTexCoord(VertexChannel texCoordChannel, std::vector<CrossSchema::float4>& texCoordVec)
{
    if (!(texCoordChannel >= VertexChannel::TexCoord0) && (texCoordChannel <= VertexChannel::TexCoordLast))
    {
        return false;
    }

    if (mMeshCompileSettings.UseFullPrecisionUV)
    {
        return AddChannelData<CrossSchema::float4, CrossSchema::float4>(SemanticTexCoord, texCoordChannel, VertexFormat::Float4, texCoordVec);
    }
    else
    {
        std::vector<UShort4> normalized_data;
        FloatToHalfConverter converter;

        auto convertHalf = [&converter](const float& in, auto& out) { converter.Convert(in, out); };

        TypeConverter(texCoordVec, normalized_data, convertHalf);
        return AddChannelData<UShort4, UShort4>(SemanticTexCoord, texCoordChannel, VertexFormat::Half4, normalized_data);
    }
}

bool MeshAssetData::AddVertexColor(VertexChannel colorChannel, std::vector<UInt32>& colorVec)
{
    if (!(colorChannel >= VertexChannel::Color0) && (colorChannel <= VertexChannel::ColorLast))
    {
        return false;
    }

    return AddChannelData<UInt32, UInt32>(SemanticColor, colorChannel, VertexFormat::Color, colorVec);
}

bool MeshAssetData::AddVertexNormal(VertexChannel normalChannel, std::vector<CrossSchema::float3>& normalVec)
{
    if (!(normalChannel >= VertexChannel::Normal0) && (normalChannel <= VertexChannel::NormalLast))
    {
        return false;
    }

    // Skinned Mesh
    if (!mRefSkeleton.Bones.empty())
    {
        return AddChannelData<CrossSchema::float3, CrossSchema::float3>(SemanticNormal, normalChannel, VertexFormat::Float3, normalVec);
    }
    // Static Mesh
    else
    {
        // float3 -> float4
        std::vector<CrossSchema::float4> inData(normalVec.size());
        for (size_t i = 0; i < inData.size(); ++i) {
            const CrossSchema::float3& inNormal = normalVec[i];
            inData[i] = {inNormal.x(), inNormal.y(), inNormal.z(), 1.f};
        }
        
        if (mMeshCompileSettings.UseFullPrecisionTangent)
        {
            std::vector<UShort4> normalized_data;
            ConvertToSNORM(normalVec, normalized_data);
            return AddChannelData<UShort4, UShort4>(SemanticNormal, normalChannel, VertexFormat::Short4_Norm, normalized_data);
        }
        else
        {
            std::vector<UChar4> normalized_data;
            ConvertToSNORM(normalVec, normalized_data);
            return AddChannelData<UChar4, UChar4>(SemanticNormal, normalChannel, VertexFormat::Byte4_Norm, normalized_data);
        }
    }
}

bool MeshAssetData::AddVertexNormal(VertexChannel normalChannel, std::vector<CrossSchema::float4>& normalVec)
{
    if (!(normalChannel >= VertexChannel::Normal0) && (normalChannel <= VertexChannel::NormalLast))
    {
        return false;
    }

    // Skinned Mesh
    if (!mRefSkeleton.Bones.empty())
    {
        return AddChannelData<CrossSchema::float4, CrossSchema::float4>(SemanticNormal, normalChannel, VertexFormat::Float4, normalVec);
    }
    // Static Mesh
    else
    {
        if (mMeshCompileSettings.UseFullPrecisionTangent)
        {
            std::vector<UShort4> normalized_data;
            ConvertToSNORM(normalVec, normalized_data);
            return AddChannelData<UShort4, UShort4>(SemanticNormal, normalChannel, VertexFormat::Short4_Norm, normalized_data);
        }
        else
        {
            std::vector<UChar4> normalized_data;
            ConvertToSNORM(normalVec, normalized_data);
            return AddChannelData<UChar4, UChar4>(SemanticNormal, normalChannel, VertexFormat::Byte4_Norm, normalized_data);
        }
    }
}

bool MeshAssetData::AddVertexTangent(VertexChannel tangentChannel, std::vector<CrossSchema::float4>& tangentVec)
{
    if (!(tangentChannel >= VertexChannel::Tangent0) && (tangentChannel <= VertexChannel::TangentLast))
    {
        return false;
    }

    // Skinned Mesh
    if (!mRefSkeleton.Bones.empty())
    {
        return AddChannelData<CrossSchema::float4, CrossSchema::float4>(SemanticTangent, tangentChannel, VertexFormat::Float4, tangentVec);
    }
    // Static Mesh
    else
    {
        if (mMeshCompileSettings.UseFullPrecisionTangent)
        {
            std::vector<UShort4> normalized_data;
            ConvertToSNORM(tangentVec, normalized_data);
            return AddChannelData<UShort4, UShort4>(SemanticTangent, tangentChannel, VertexFormat::Short4_Norm, normalized_data);
        }
        else
        {
            std::vector<UChar4> normalized_data;
            ConvertToSNORM(tangentVec, normalized_data);
            return AddChannelData<UChar4, UChar4>(SemanticTangent, tangentChannel, VertexFormat::Byte4_Norm, normalized_data);
        }
    }
}

bool MeshAssetData::AddVertexBiNormal(VertexChannel binormalChannel, std::vector<CrossSchema::float4>& binormalVec)
{
    if (!(binormalChannel >= VertexChannel::BiNormal0) && (binormalChannel <= VertexChannel::BiNormalLast))
    {
        return false;
    }

    // Skinned Mesh
    if (!mRefSkeleton.Bones.empty())
    {
        return AddChannelData<CrossSchema::float4, CrossSchema::float4>(SemanticBiNormal, binormalChannel, VertexFormat::Float4, binormalVec);
    }
    // Static Mesh
    else
    {
        if (mMeshCompileSettings.UseFullPrecisionTangent)
        {
            std::vector<UShort4> normalized_data;
            ConvertToSNORM(binormalVec, normalized_data);
            return AddChannelData<UShort4, UShort4>(SemanticBiNormal, binormalChannel, VertexFormat::Short4_Norm, normalized_data);
        }
        else
        {
            std::vector<UChar4> normalized_data;
            ConvertToSNORM(binormalVec, normalized_data);
            return AddChannelData<UChar4, UChar4>(SemanticBiNormal, binormalChannel, VertexFormat::Byte4_Norm, normalized_data);
        }
    }
}

bool MeshAssetData::AddVertexBoneWeights(VertexChannel boneWeightChannel, std::vector<CrossSchema::float4> boneWeightVec)
{
    if (!(boneWeightChannel >= VertexChannel::BlendWeight0) && (boneWeightChannel <= VertexChannel::BlendWeightLast))
        return false;

    return AddChannelData<CrossSchema::float4, CrossSchema::float4>(SemanticBlendWeight, boneWeightChannel, VertexFormat::Float4, boneWeightVec);
}

bool MeshAssetData::AddVertexBoneIds(VertexChannel boneIdChannel, std::vector<Short4> boneIdVec)
{
    if (!(boneIdChannel >= VertexChannel::BlendIndex0) && (boneIdChannel <= VertexChannel::BlendIndexLast))
        return false;

    return AddChannelData<Short4, Short4>(SemanticBlendIndex, boneIdChannel, VertexFormat::Short4, boneIdVec);
}

bool MeshAssetData::AddVertexQuatTangent(VertexChannel quatTangentChannel, std::vector<CrossSchema::float4>& quatTangentVec)
{
    if (!(quatTangentChannel >= VertexChannel::QUATTAN0) && (quatTangentChannel <= VertexChannel::QUATTANLast))
        return false;
    if (mMeshCompileSettings.UseFullPrecisionTangent)
    {
        std::vector<UShort4> normalized_data;
        ConvertToSNORM(quatTangentVec, normalized_data);
        return AddChannelData<UShort4, UShort4>(SemanticQuatTan, quatTangentChannel, VertexFormat::Short4_Norm, normalized_data);
    }
    else
    {
        std::vector<UChar4> normalized_data;
        ConvertToSNORM(quatTangentVec, normalized_data);
        return AddChannelData<UChar4, UChar4>(SemanticQuatTan, quatTangentChannel, VertexFormat::Byte4_Norm, normalized_data);
    }
}

bool MeshAssetData::AddBlendShape(UInt32 meshPartIndex, const BlendShapeDeformer& blendShape)
{
    const auto channelNum = blendShape.Channels.size();
    auto& blendShapeInfo = mMeshPartInfo[meshPartIndex].mBlendShape;

    blendShapeInfo.ChannelShapeData.reserve(channelNum);
    for (auto channelIdx = 0; channelIdx < channelNum; ++channelIdx)
    {
        if (!blendShape.ChannelValidFlags[channelIdx])
        {
            continue;
        }

        const auto& curChannel = blendShape.Channels[channelIdx];

        auto& blendShapeChannelInfo = blendShapeInfo.ChannelShapeData.emplace_back();
        blendShapeInfo.ChannelNameData.emplace_back(curChannel.Name.c_str());

        const auto shapeNum = curChannel.DeltaShapes.size();
        for (auto shapeIdx = 0; shapeIdx < shapeNum; ++shapeIdx)
        {
            const auto& curDeltaShape = curChannel.DeltaShapes[shapeIdx];
            const bool hasNormal = curDeltaShape.VertexChannels & ImportVertexChannel::NORMAL;
            const bool hasTangent = curDeltaShape.VertexChannels & ImportVertexChannel::TANGENT;
            
            blendShapeChannelInfo.NormalizedFullWeights.push_back(curDeltaShape.NormalizedFullWeight);

            auto& deltaShapeInfo = blendShapeChannelInfo.DeltaShapes.emplace_back();
            deltaShapeInfo.mVertexStart = blendShapeInfo.GetVertexCount(VertexChannel::Position0);

            blendShapeInfo.AddPosition(curDeltaShape.Positions);
            blendShapeInfo.AddInfluencedID(curDeltaShape.InfluencedVertexIDs);
            if (hasNormal)
            {
                blendShapeInfo.AddNormal(curDeltaShape.Normals);
            }
            if (hasTangent)
            {
                blendShapeInfo.AddTangent(curDeltaShape.Tangents);
            }

            deltaShapeInfo.mVertexCount = blendShapeInfo.GetVertexCount(VertexChannel::Position0) - deltaShapeInfo.mVertexStart;
        }
    }

    return true;
}

void MeshAssetData::AddSubMeshBegin(const MeshDescription& meshDesc)
{
    MeshPartAssetInfo& meshPartInfo = mMeshPartInfo.emplace_back();

    mMeshPartNames.emplace_back(meshDesc.GetName());
    meshPartInfo.mNameIndex = static_cast<SInt16>(mMeshPartNames.size() - 1);

    // Need to be modified after importing material from FBX
    mMaterialNames.push_back(meshDesc.GetMaterialPath());
    meshPartInfo.mMaterialIndex = static_cast<SInt16>(mMaterialNames.size() - 1);

    // TODO(jihui): only support Position0, not support Position1,2,3
    meshPartInfo.mVertexStart = GetVertexCount(VertexChannel::Position0);

    meshPartInfo.mIndexStart = mIndexStream.GetCount();
    meshPartInfo.mPrimitiveType = static_cast<UInt32>(PrimitiveTopology::TriangleList);
}

void MeshAssetData::AddSubMeshEnd(const MeshDescription& meshDesc)
{
    MeshPartAssetInfo& meshPartInfo = mMeshPartInfo.back();

    meshPartInfo.mVertexCount = GetVertexCount(VertexChannel::Position0) - meshPartInfo.mVertexStart;
    meshPartInfo.mIndexCount = mIndexStream.GetCount() - meshPartInfo.mIndexStart;

    // TODO(jihui) attention: maybe not TriangleList
    meshPartInfo.mPrimitiveCount = meshPartInfo.mIndexCount / 3;

    // Calculate bound of current mesh part by FinalMeshVertices
    // Skeletal Mesh do not need to re-correct MeshBound because we import bind pose not frame 0
    const auto& FinalMeshVertices = meshDesc.GetMeshBuildVerticesConstRef();
    for (const auto meshVertex : FinalMeshVertices)
    {
        meshPartInfo.mMeshBound.Encapsulate(meshVertex.Position);
    }
    // Update mAABB of whole mesh asset
    mAABB.Encapsulate(meshPartInfo.mMeshBound);

    // TODO(jihui) calculate CollisionTree
}

void MeshAssetData::AddSubMeshEnd(const MeshBound& meshBound)
{
    MeshPartAssetInfo& meshPartInfo = mMeshPartInfo.back();
    
    meshPartInfo.mVertexCount = GetVertexCount(VertexChannel::Position0) - meshPartInfo.mVertexStart;
    meshPartInfo.mIndexCount = mIndexStream.GetCount() - meshPartInfo.mIndexStart;

    meshPartInfo.mPrimitiveCount = meshPartInfo.mIndexCount / 3;
    meshPartInfo.mMeshBound.Encapsulate(meshBound);
    mAABB.Encapsulate(meshPartInfo.mMeshBound);
}

Float3 ToFloat3(const CrossSchema::float3& value)
{
    return Float3(value.x(), value.y(), value.z());
}

Float4 ToFloat4(const CrossSchema::float4& value)
{
    return Float4(value.x(), value.y(), value.z(), value.w());
}

Quaternion ToQuaterion(const CrossSchema::float4& value)
{
    return Quaternion(value.x(), value.y(), value.z(), value.w());
}

CrossSchema::float3 Tofloat3(const Float3& value)
{
    return CrossSchema::float3(value.x, value.y, value.z);
}

CrossSchema::float4 Tofloat4(const Float4& value)
{
    return CrossSchema::float4(value.x, value.y, value.z, value.w);
}

CrossSchema::float4 Tofloat4(const Quaternion& value)
{
    return CrossSchema::float4(value.x, value.y, value.z, value.w);
}


bool MeshAssetData::CheckBigPosition(ImportScene& importScene,  const std::vector<UInt32>& meshDescIndices)
{
    bool ret = true;
    const static double maxBigPosition = 1000000.00;
    for (UInt32 meshDescIndex : meshDescIndices)
    {
        const MeshDescription& meshDescription = importScene.MeshDescriptions[meshDescIndex];

        const UInt32 vertexCount = meshDescription.GetMeshBuildVertexCount();
        for (UInt32 vertexIndex = 0u; vertexIndex < vertexCount; ++vertexIndex)
        {
            const auto& vertex = meshDescription.GetMeshBuildVertex(vertexIndex);
            if (vertex.Channels & ImportVertexChannel::POSITION)
            {
                if (vertex.Position.x >= maxBigPosition || vertex.Position.y >= maxBigPosition || vertex.Position.z >= maxBigPosition)
                {
                    ret = false;
                    break;
                }
            }
        }
    }
    return ret;
}

void MeshAssetData::InitBySpecificMeshDescs(ImportScene& importScene, MeshCompileSettings compile_seeting, const std::vector<UInt32>& meshDescIndices)
{
    mMeshCompileSettings = compile_seeting;
    bool EnableQtangents = mMeshCompileSettings.UseQTangents;

    if (importScene.LodCount > 0)
    {
        mMeshPartLodStartIndex = std::move(importScene.LodStartIndexArray);
    }
    else
    {
        mMeshPartLodStartIndex.emplace_back(0);
    }

    UInt32 meshPartIndex = 0;
    for (UInt32 meshDescIndex : meshDescIndices)
    {
        const MeshDescription& meshDescription = importScene.MeshDescriptions[meshDescIndex];
        this->AddSubMeshBegin(meshDescription);

        this->AddIndexStream(meshDescription.GetVertexInstanceVertices());

        const UInt32 vertexCount = meshDescription.GetMeshBuildVertexCount();

        std::vector<CrossSchema::float3> tmpPositions;
        tmpPositions.reserve(vertexCount);
        std::vector<CrossSchema::float3> tmpNormals;
        tmpNormals.reserve(vertexCount);
        std::vector<CrossSchema::float4> tmpTangents;
        tmpTangents.reserve(vertexCount);
        std::vector<uint32_t> tmpColors;
        tmpColors.reserve(vertexCount);
        std::array<std::vector<CrossSchema::float2>, MAX_UV_SUPPORT> tmpUVs;
        std::for_each(tmpUVs.begin(), tmpUVs.end(), [=](auto& v) { v.reserve(vertexCount); });

        std::vector<CrossSchema::float4> tmpQuat;
        tmpQuat.reserve(vertexCount);

        for (UInt32 vertexIndex = 0u; vertexIndex < vertexCount; ++vertexIndex)
        {
            const auto& vertex = meshDescription.GetMeshBuildVertex(vertexIndex);
            if (vertex.Channels & ImportVertexChannel::POSITION)
            {
                tmpPositions.emplace_back(vertex.Position.x, vertex.Position.y, vertex.Position.z);
            }
            if (vertex.Channels & ImportVertexChannel::NORMAL)
            {
                tmpNormals.emplace_back(vertex.Normal.x, vertex.Normal.y, vertex.Normal.z);
            }
            if (vertex.Channels & ImportVertexChannel::TANGENT)
            {
                tmpTangents.emplace_back(vertex.Tangent.x, vertex.Tangent.y, vertex.Tangent.z, vertex.Tangent.w);
            }
            if (vertex.Channels & ImportVertexChannel::COLOR)
            {
                tmpColors.emplace_back(vertex.Color.AsUInt32());
            }

            for (UInt32 uvChannelIndex = 0; uvChannelIndex < std::min<UInt32>(vertex.UVChannelNum, MAX_UV_SUPPORT); ++uvChannelIndex)
            {
                if (vertex.Channels & static_cast<ImportVertexChannel>(1 << (4 + uvChannelIndex)))
                {
                    tmpUVs[uvChannelIndex].emplace_back(vertex.UVs[uvChannelIndex].x, 1 - vertex.UVs[uvChannelIndex].y);
                }
            }
        }

        if (mMeshCompileSettings.UseQTangents || EnableQtangents)
        {
            if (tmpNormals.size() == 0 || tmpTangents.size() == 0)
            {
                LOG_EDITOR_ERROR("Use QuatTangent, but no tangent or normal");
            }
            else
            {
                for (UInt32 vertexIndex = 0u; vertexIndex < vertexCount; ++vertexIndex)
                {
                    auto normal = ToFloat3(tmpNormals[vertexIndex]);
                    auto tangent = ToFloat4(tmpTangents[vertexIndex]);

                    auto quat = tangent_space_to_quat(normal, tangent, mMeshCompileSettings.UseFullPrecisionTangent ? 16 : 8);

                    tmpQuat.emplace_back(quat.x, quat.y, quat.z, quat.w);
                }
            }
        }

        // Execute add vertex data into stream
        bool executed = false;
        {
            executed = tmpPositions.size() > 0 ? this->AddVertexPosition(VertexChannel::Position0, tmpPositions) : false;

            // comment it before we verify that Qtangent not have bugs
            if (!(mMeshCompileSettings.UseQTangents && EnableQtangents))
            {
                executed = tmpNormals.size() > 0 ? this->AddVertexNormal(VertexChannel::Normal0, tmpNormals) : false;
                executed = tmpTangents.size() > 0 ? this->AddVertexTangent(VertexChannel::Tangent0, tmpTangents) : false;
            }

            executed = tmpColors.size() > 0 ? this->AddVertexColor(VertexChannel::Color0, tmpColors) : false;
            SInt32 uvChannelBegin = static_cast<SInt32>(VertexChannel::TexCoord0);
            for (int uvIdx = 0; uvIdx < MAX_UV_SUPPORT; ++uvIdx)
            {
                executed = tmpUVs[uvIdx].size() > 0 ? this->AddVertexTexCoord(static_cast<VertexChannel>(uvChannelBegin | uvIdx), tmpUVs[uvIdx]) : false;
            }

            executed = tmpQuat.size() > 0 ? this->AddVertexQuatTangent(VertexChannel::QUATTAN0, tmpQuat) : false;
        }

        if (mMeshCompileSettings.GenCollisionTree)
        {
            // CollisionTree
            std::vector<std::unique_ptr<CrossSchema::CollisionNodeT>> cTreeVec;
            cross::editor::MeshPickBuilderHelper::BuildCollisionTree(meshDescription, cTreeVec);
            for (int cNodeIdx = 0; cNodeIdx < cTreeVec.size(); ++cNodeIdx)
            {
                ImportMeshCollisionNode collisionNode;
                collisionNode.Index = cTreeVec[cNodeIdx]->index;
                collisionNode.LeftIndex = cTreeVec[cNodeIdx]->leftindex;
                collisionNode.RightIndex = cTreeVec[cNodeIdx]->rightindex;

                Float3 Min = {cTreeVec[cNodeIdx]->minpos[0], cTreeVec[cNodeIdx]->minpos[1], cTreeVec[cNodeIdx]->minpos[2]};
                Float3 Max = {cTreeVec[cNodeIdx]->maxpos[0], cTreeVec[cNodeIdx]->maxpos[1], cTreeVec[cNodeIdx]->maxpos[2]};
                collisionNode.Bound.Max = Max;
                collisionNode.Bound.Min = Min;

                collisionNode.TriangleList.resize(cTreeVec[cNodeIdx]->trianglelist.size());

                std::copy(cTreeVec[cNodeIdx]->trianglelist.begin(), cTreeVec[cNodeIdx]->trianglelist.end(), collisionNode.TriangleList.begin());

                this->AddCollisionTree(meshPartIndex, collisionNode);
            }
        }

        ++meshPartIndex;

        this->AddSubMeshEnd(meshDescription);
    }
}

void MeshAssetData::InitByLightPointMesh(ImportScene& importScene, MeshCompileSettings compile_seeting, const std::vector<UInt32>& meshDescIndices)
{
    mMeshCompileSettings = compile_seeting;
    mMeshCompileSettings.UseFullPrecisionUV = true;
    mMeshCompileSettings.UseFullPrecisionTangent = true;

    if (importScene.LodCount > 0)
    {
        mMeshPartLodStartIndex = std::move(importScene.LodStartIndexArray);
    }
    else
    {
        mMeshPartLodStartIndex.emplace_back(0);
    }

    for (UInt32 meshDescIndex : meshDescIndices)
    {
        const MeshDescription& meshDescription = importScene.MeshDescriptions[meshDescIndex];
        this->AddSubMeshBegin(meshDescription);

        this->AddIndexStream(meshDescription.GetVertexInstanceVertices());

        const UInt32 vertexCount = meshDescription.GetMeshBuildVertexCount();

        std::vector<CrossSchema::float3> tmpPositions;
        tmpPositions.reserve(vertexCount);

        std::vector<CrossSchema::float3> tmpNormals;
        tmpNormals.reserve(vertexCount);

        std::vector<CrossSchema::float4> tmpTangents;
        tmpTangents.reserve(vertexCount);

        std::vector<uint32_t> tmpColors;
        tmpColors.reserve(vertexCount);

        std::array<std::vector<CrossSchema::float2>, MAX_UV_SUPPORT> tmpUVs;
        std::for_each(tmpUVs.begin(), tmpUVs.end(), [=](auto& v) { v.reserve(vertexCount); });

        for (UInt32 vertexIndex = 0u; vertexIndex < vertexCount; ++vertexIndex)
        {
            const auto& vertex = meshDescription.GetMeshBuildVertex(vertexIndex);
            if (vertex.Channels & ImportVertexChannel::POSITION)
            {
                tmpPositions.emplace_back(vertex.Position.x, vertex.Position.y, vertex.Position.z);
            }
            if (vertex.Channels & ImportVertexChannel::NORMAL)
            {
                tmpNormals.emplace_back(vertex.Normal.x, vertex.Normal.y, vertex.Normal.z);
            }
            if (vertex.Channels & ImportVertexChannel::TANGENT)
            {
                tmpTangents.emplace_back(vertex.Tangent.x, vertex.Tangent.y, vertex.Tangent.z, vertex.Tangent.w);
            }
            if (vertex.Channels & ImportVertexChannel::COLOR)
            {
                tmpColors.emplace_back(vertex.Color.AsUInt32());
            }

            for (UInt32 uvChannelIndex = 0; uvChannelIndex < std::min<UInt32>(vertex.UVChannelNum, MAX_UV_SUPPORT); ++uvChannelIndex)
            {
                if (vertex.Channels & static_cast<ImportVertexChannel>(1 << (4 + uvChannelIndex)))
                {
                    tmpUVs[uvChannelIndex].emplace_back(vertex.UVs[uvChannelIndex].x, 1 - vertex.UVs[uvChannelIndex].y);
                }
            }
        }

        // Execute add vertex data into stream
        bool executed = false;
        {
            executed = tmpPositions.size() > 0 ? this->AddVertexPosition(VertexChannel::Position0, tmpPositions) : false;

            executed = tmpNormals.size() > 0 ? this->AddVertexNormalFullPrecision(VertexChannel::Normal0, tmpNormals) : false;
            executed = tmpTangents.size() > 0 ? this->AddVertexTangentFullPrecision(VertexChannel::Tangent0, tmpTangents) : false;

            executed = tmpColors.size() > 0 ? this->AddVertexColor(VertexChannel::Color0, tmpColors) : false;

            SInt32 uvChannelBegin = static_cast<SInt32>(VertexChannel::TexCoord0);
            for (int uvIdx = 0; uvIdx < MAX_UV_SUPPORT; ++uvIdx)
            {
                executed = tmpUVs[uvIdx].size() > 0 ? this->AddVertexTexCoord(static_cast<VertexChannel>(uvChannelBegin | uvIdx), tmpUVs[uvIdx]) : false;
            }
        }

        this->AddSubMeshEnd(meshDescription);
    }
}

void MeshAssetData::InitByImportScene(ImportScene& importScene, MeshCompileSettings compile_seeting)
{
    mMeshCompileSettings = compile_seeting;
    bool EnableQtangents = EngineGlobal::GetSettingMgr()->GetEnableTangents();

    // Skeleton Data at first
    mRefSkeleton = std::move(importScene.ImportSkeltData);

    if (importScene.LodCount > 0)
    {
        mMeshPartLodStartIndex = std::move(importScene.LodStartIndexArray);
    }
    else
    {
        mMeshPartLodStartIndex.emplace_back(0);
    }

    for (int meshIdx = 0; meshIdx < importScene.MeshDescriptions.size(); ++meshIdx)
    {
        const MeshDescription& meshDescription = importScene.MeshDescriptions[meshIdx];
        this->AddSubMeshBegin(meshDescription);

        this->AddIndexStream(meshDescription.GetVertexInstanceVertices());

        const UInt32 vertexCount = meshDescription.GetMeshBuildVertexCount();

        std::vector<CrossSchema::float3> tmpPositions;
        tmpPositions.reserve(vertexCount);
        std::vector<CrossSchema::float3> tmpNormals;
        tmpNormals.reserve(vertexCount);
        std::vector<CrossSchema::float4> tmpTangents;
        tmpTangents.reserve(vertexCount);
        std::vector<uint32_t> tmpColors;
        tmpColors.reserve(vertexCount);
        std::array<std::vector<CrossSchema::float2>, MAX_UV_SUPPORT> tmpUVs;
        std::for_each(tmpUVs.begin(), tmpUVs.end(), [=](auto& v) { v.reserve(vertexCount); });
        std::vector<CrossSchema::float4> tmpTexIds;

        std::vector<CrossSchema::float4> tmpBoneWeights;
        tmpBoneWeights.reserve(vertexCount);
        std::vector<Short4> tmpBoneIDs;
        tmpBoneIDs.reserve(vertexCount);

        std::vector<CrossSchema::float4> tmpQuat;
        tmpQuat.reserve(vertexCount);

        for (UInt32 vertexIndex = 0u; vertexIndex < vertexCount; ++vertexIndex)
        {
            const auto& vertex = meshDescription.GetMeshBuildVertex(vertexIndex);
            if (vertex.Channels & ImportVertexChannel::POSITION)
            {
                tmpPositions.emplace_back(vertex.Position.x, vertex.Position.y, vertex.Position.z);
            }
            if (vertex.Channels & ImportVertexChannel::NORMAL)
            {
                tmpNormals.emplace_back(vertex.Normal.x, vertex.Normal.y, vertex.Normal.z);
            }
            if (vertex.Channels & ImportVertexChannel::TANGENT)
            {
                tmpTangents.emplace_back(vertex.Tangent.x, vertex.Tangent.y, vertex.Tangent.z, vertex.Tangent.w);
            }
            if (vertex.Channels & ImportVertexChannel::COLOR)
            {
                tmpColors.emplace_back(vertex.Color.AsUInt32()); 
            }

            for (UInt32 uvChannelIndex = 0; uvChannelIndex < std::min<UInt32>(vertex.UVChannelNum, MAX_UV_SUPPORT); ++uvChannelIndex)
            {
                if (vertex.Channels & static_cast<ImportVertexChannel>(1 << (4 + uvChannelIndex)))
                {
                    tmpUVs[uvChannelIndex].emplace_back(vertex.UVs[uvChannelIndex].x, 1 - vertex.UVs[uvChannelIndex].y);
                }
            }

            if (mMeshCompileSettings.UseTextureArray)
            {
                if (vertex.MatID < importScene.MatIdxToTexIds.size())
                {
                    const auto& matTexIdxs = importScene.MatIdxToTexIds[vertex.MatID];
                    CrossSchema::float4 texIdx;
                    texIdx.mutate_x(static_cast<float>(matTexIdxs[0]));
                    texIdx.mutate_y(static_cast<float>(matTexIdxs[1]));
                    texIdx.mutate_z(static_cast<float>(matTexIdxs[2]));
                    texIdx.mutate_w(static_cast<float>(matTexIdxs[3]));
                    tmpTexIds.emplace_back(texIdx);
                }
            }

            if (meshDescription.IsSkinnedMesh())
            {
                const auto& skinDescription = meshDescription.GetSkinDescriptionConstRef();
                const auto& skinVertex = skinDescription.GetSkinBuildVertex(vertexIndex);
                tmpBoneWeights.emplace_back(skinVertex.Weights[0], skinVertex.Weights[1], skinVertex.Weights[2], skinVertex.Weights[3]);
                tmpBoneIDs.emplace_back(
                    static_cast<UInt16>(skinVertex.BoneIDs[0]), 
                    static_cast<UInt16>(skinVertex.BoneIDs[1]), 
                    static_cast<UInt16>(skinVertex.BoneIDs[2]), 
                    static_cast<UInt16>(skinVertex.BoneIDs[3]));
            }
        }

        if (mMeshCompileSettings.UseQTangents || EnableQtangents)
        {
            if (tmpNormals.size() == 0 || tmpTangents.size() == 0)
            {
                LOG_EDITOR_ERROR("Use QuatTangent, but no tangent or normal");
            }
            else
            {
                for (UInt32 vertexIndex = 0u; vertexIndex < vertexCount; ++vertexIndex)
                {
                    auto normal = ToFloat3(tmpNormals[vertexIndex]);
                    auto tangent = ToFloat4(tmpTangents[vertexIndex]);

                    auto quat = tangent_space_to_quat(normal, tangent, mMeshCompileSettings.UseFullPrecisionTangent?16:8);

                    //Float3 new_normal;
                    //Float4 new_tangent;

                    //quat_to_tangent_frame(quat, new_normal, new_tangent);

                    tmpQuat.emplace_back(quat.x, quat.y, quat.z, quat.w);
                }
            }
        }

        // Execute add vertex data into stream
        bool executed = false;
        {
            executed = tmpPositions.size() > 0 ? this->AddVertexPosition(VertexChannel::Position0, tmpPositions) : false;

            // comment it before we verify that Qtangent not have bugs
            if (!(mMeshCompileSettings.UseQTangents && EnableQtangents))
            {
                executed = tmpNormals.size() > 0 ? this->AddVertexNormal(VertexChannel::Normal0, tmpNormals) : false;
                executed = tmpTangents.size() > 0 ? this->AddVertexTangent(VertexChannel::Tangent0, tmpTangents) : false;
            }

            executed = tmpColors.size() > 0 ? this->AddVertexColor(VertexChannel::Color0, tmpColors) : false;
            SInt32 uvChannelBegin = static_cast<SInt32>(VertexChannel::TexCoord0);


            for (int uvIdx = 0; uvIdx < MAX_UV_SUPPORT; ++uvIdx)
            {
                if (tmpUVs[uvIdx].size() <= 0)
                    break;
                executed = this->AddVertexTexCoord(static_cast<VertexChannel>(uvChannelBegin | uvIdx), tmpUVs[uvIdx]);
            }

            if (mMeshCompileSettings.UseTextureArray && tmpTexIds.size() > 0)
            {
                executed = this->AddChannelData<CrossSchema::float4, CrossSchema::float4>(SemanticNormal, static_cast<VertexChannel>(VertexChannel::Normal2), VertexFormat::Float4, tmpTexIds);
            }

            executed = tmpBoneWeights.size() > 0 ? this->AddVertexBoneWeights(VertexChannel::BlendWeight0, tmpBoneWeights) : false;
            executed = tmpBoneIDs.size() > 0 ? this->AddVertexBoneIds(VertexChannel::BlendIndex0, tmpBoneIDs) : false;
            executed = tmpQuat.size() > 0?  this->AddVertexQuatTangent(VertexChannel::QUATTAN0, tmpQuat):false;
        }

        if (mMeshCompileSettings.GenCollisionTree)
        {
            // CollisionTree
            std::vector<std::unique_ptr<CrossSchema::CollisionNodeT>> cTreeVec;
            cross::editor::MeshPickBuilderHelper::BuildCollisionTree(meshDescription, cTreeVec);
            for (int cNodeIdx = 0; cNodeIdx < cTreeVec.size(); ++cNodeIdx)
            {
                ImportMeshCollisionNode collisionNode;
                collisionNode.Index = cTreeVec[cNodeIdx]->index;
                collisionNode.LeftIndex = cTreeVec[cNodeIdx]->leftindex;
                collisionNode.RightIndex = cTreeVec[cNodeIdx]->rightindex;

                Float3 Min = { cTreeVec[cNodeIdx]->minpos[0], cTreeVec[cNodeIdx]->minpos[1], cTreeVec[cNodeIdx]->minpos[2] };
                Float3 Max = { cTreeVec[cNodeIdx]->maxpos[0], cTreeVec[cNodeIdx]->maxpos[1], cTreeVec[cNodeIdx]->maxpos[2] };
                collisionNode.Bound.Max = Max;
                collisionNode.Bound.Min = Min;

                collisionNode.TriangleList.resize(cTreeVec[cNodeIdx]->trianglelist.size());

                std::copy(cTreeVec[cNodeIdx]->trianglelist.begin(), cTreeVec[cNodeIdx]->trianglelist.end(), collisionNode.TriangleList.begin());

                this->AddCollisionTree(meshIdx, collisionNode);
            }
        }
       
        // BlendShape for this mesh part
        if (meshDescription.HasBlendShape())
        {
            AddBlendShape(meshIdx, meshDescription.GetBlendShapeDeformerConstRef());
        }

        this->AddSubMeshEnd(meshDescription);
    }

    // PhysicsCollison
    mPhysicsCollision = std::move(importScene.PhyCollision);
}

void MeshAssetData::InitEmpty()
{
    std::vector<UInt32> indices = {0, 0, 0};
    std::vector<CrossSchema::float3> tmpPositions = {{0, 0, 0}};

    mMeshPartLodStartIndex.emplace_back(0);
    MeshDescription meshDesc("Empty");
    AddSubMeshBegin(meshDesc);
    AddIndexStream(indices);
    AddVertexPosition(VertexChannel::Position0, tmpPositions);
    AddSubMeshEnd(meshDesc);
}

void MeshAssetData::InitPhysicsOnly(ImportScene& importScene)
{
    std::vector<UInt32> indices = {0, 0, 0};
    std::vector<CrossSchema::float3> tmpPositions = {{0, 0, 0}};

    mMeshPartLodStartIndex.emplace_back(0);
    MeshDescription meshDesc("Empty");
    AddSubMeshBegin(meshDesc);
    AddIndexStream(indices);
    AddVertexPosition(VertexChannel::Position0, tmpPositions);
    AddSubMeshEnd(meshDesc);
    //mAABB.Encapsulate(Float3::Zero());

    mPhysicsCollision = std::move(importScene.PhyCollision);
}

bool MeshAssetData::ToImportMeshAssetDataT(CrossSchema::ImportMeshAssetDataT& meshAssetDataT)
{
    // mAABB
    std::unique_ptr<CrossSchema::MeshBoundT> aabb = std::make_unique<CrossSchema::MeshBoundT>();
    aabb->fmax = std::make_unique<CrossSchema::float3>(mAABB.Max.x, mAABB.Max.y, mAABB.Max.z);
    aabb->fmin = std::make_unique<CrossSchema::float3>(mAABB.Min.x, mAABB.Min.y, mAABB.Min.z);
    meshAssetDataT.faabb = std::move(aabb);

    // mCustomAttribute
    meshAssetDataT.fcustomattributedata.resize(mCustomAttribute.size());
    std::copy(mCustomAttribute.begin(), mCustomAttribute.end(), meshAssetDataT.fcustomattributedata.begin());

    // mCustomAttributeInfo
    meshAssetDataT.fcustomattributeinfo = std::make_unique<CrossSchema::CustomAttributeInfoT>();
    meshAssetDataT.fcustomattributeinfo->fdataflag = mCustomAttributeInfo.mDataFlag;
    meshAssetDataT.fcustomattributeinfo->fdataoffset = mCustomAttributeInfo.mDataOffset;
    meshAssetDataT.fcustomattributeinfo->fdatasizeinbyte = mCustomAttributeInfo.mDataSizeInByte;
    meshAssetDataT.fcustomattributeinfo->fkeynamehash = mCustomAttributeInfo.mKeyNameHash;

    // mCustomAttributeVersion
    meshAssetDataT.fcustomattributeversion = mCustomAttributeVersion;

    // mCustomAttributeVersionFlag
    meshAssetDataT.fcustomattributeversionflag = mCustomAttributeVersionFlag;

    // mIndexStream
    meshAssetDataT.findexstream = std::make_unique<CrossSchema::IndexStreamAssetDataT>();
    meshAssetDataT.findexstream->fcount = mIndexStream.mCount;
    meshAssetDataT.findexstream->fdata.resize(mIndexStream.mData.size());
    std::copy(mIndexStream.mData.begin(), mIndexStream.mData.end(), meshAssetDataT.findexstream->fdata.begin());
    meshAssetDataT.findexstream->fis16bitindex = mIndexStream.mIs16BitIndex;

    // mMaterialNames
    meshAssetDataT.fmaterialnames.resize(mMaterialNames.size());
    std::copy(mMaterialNames.begin(), mMaterialNames.end(), meshAssetDataT.fmaterialnames.begin());

    // mMeshPartInfo
    meshAssetDataT.fmeshpartinfo.clear();
    meshAssetDataT.fmeshpartinfo.reserve(mMeshPartInfo.size());
    for (int meshPartIdx = 0; meshPartIdx < mMeshPartInfo.size(); ++meshPartIdx)
    {
        const auto& curMeshPart = mMeshPartInfo[meshPartIdx];
        auto& fbPartAssetInfo = meshAssetDataT.fmeshpartinfo.emplace_back(std::make_unique<CrossSchema::ImportMeshPartAssetInfoT>());

        // mesh bound of current mesh part
        fbPartAssetInfo->fbindinginfo = std::make_unique<CrossSchema::MeshBoundT>();
        fbPartAssetInfo->fbindinginfo->fmax = std::make_unique<CrossSchema::float3>(curMeshPart.mMeshBound.Max.x, curMeshPart.mMeshBound.Max.y, curMeshPart.mMeshBound.Max.z);
        fbPartAssetInfo->fbindinginfo->fmin = std::make_unique<CrossSchema::float3>(curMeshPart.mMeshBound.Min.x, curMeshPart.mMeshBound.Min.y, curMeshPart.mMeshBound.Min.z);
        fbPartAssetInfo->fcustomattributedata.resize(curMeshPart.mCustomAttributeData.size());
        std::copy(curMeshPart.mCustomAttributeData.begin(), curMeshPart.mCustomAttributeData.end(), fbPartAssetInfo->fcustomattributedata.begin());
        fbPartAssetInfo->fcustomattributeinfo = std::make_unique<CrossSchema::CustomAttributeInfoT>();
        fbPartAssetInfo->fcustomattributeinfo->fdataflag = curMeshPart.mCustomAttributeInfo.mDataFlag;
        fbPartAssetInfo->fcustomattributeinfo->fdataoffset = curMeshPart.mCustomAttributeInfo.mDataOffset;
        fbPartAssetInfo->fcustomattributeinfo->fdatasizeinbyte = curMeshPart.mCustomAttributeInfo.mDataSizeInByte;
        fbPartAssetInfo->fcustomattributeinfo->fkeynamehash = curMeshPart.mCustomAttributeInfo.mKeyNameHash;

        fbPartAssetInfo->findexcount = curMeshPart.mIndexCount;
        fbPartAssetInfo->findexstart = curMeshPart.mIndexStart;
        fbPartAssetInfo->fmaterialindex = curMeshPart.mMaterialIndex;
        fbPartAssetInfo->fmiscflag = curMeshPart.mMiscFlag;
        fbPartAssetInfo->fnameindex = curMeshPart.mNameIndex;
        fbPartAssetInfo->fprimitivecount = curMeshPart.mPrimitiveCount;
        fbPartAssetInfo->fprimitivetype = curMeshPart.mPrimitiveType;
        fbPartAssetInfo->frenderpriority = curMeshPart.mRenderPriority;
        fbPartAssetInfo->fshadowbias = curMeshPart.mShadowBias;
        fbPartAssetInfo->fshadownormalbias = curMeshPart.mShadowNormalBias;
        fbPartAssetInfo->fvertexcount = curMeshPart.mVertexCount;
        fbPartAssetInfo->fvertexstart = curMeshPart.mVertexStart;

        // partAssetInfoT->fcollisiontree
        for (int j = 0; j < curMeshPart.mCollisionTree.size(); j++)
        {
            std::unique_ptr<CrossSchema::CollisionNodeT> c = std::make_unique<CrossSchema::CollisionNodeT>();
            c->index = curMeshPart.mCollisionTree[j].Index;
            c->leftindex = curMeshPart.mCollisionTree[j].LeftIndex;
            c->rightindex = curMeshPart.mCollisionTree[j].RightIndex;
            c->maxpos.clear();
            c->maxpos.push_back(curMeshPart.mCollisionTree[j].Bound.Max.x);
            c->maxpos.push_back(curMeshPart.mCollisionTree[j].Bound.Max.y);
            c->maxpos.push_back(curMeshPart.mCollisionTree[j].Bound.Max.z);
            c->minpos.clear();
            c->minpos.push_back(curMeshPart.mCollisionTree[j].Bound.Min.x);
            c->minpos.push_back(curMeshPart.mCollisionTree[j].Bound.Min.y);
            c->minpos.push_back(curMeshPart.mCollisionTree[j].Bound.Min.z);

            c->trianglelist.resize(curMeshPart.mCollisionTree[j].TriangleList.size());
            std::copy(curMeshPart.mCollisionTree[j].TriangleList.begin(), curMeshPart.mCollisionTree[j].TriangleList.end(), c->trianglelist.begin());

            fbPartAssetInfo->fcollisiontree.push_back(std::move(c));
        }

        // Mesh blend shape
        const auto& meshBlendShape = curMeshPart.mBlendShape;
        if (meshBlendShape.HasBlendShape())
        {
            std::unique_ptr<CrossSchema::ImportBlendShapeInfoT> fbBlendShape = std::make_unique<CrossSchema::ImportBlendShapeInfoT>();
            fbBlendShape->fvertexchannelsemanticmask = meshBlendShape.VertexChannelSemanticMask;

            fbBlendShape->fvertexchanneldata.reserve(meshBlendShape.VertexChannelData.size());
            for (int i = 0; i < meshBlendShape.VertexChannelData.size(); i++)
            {
                const auto& curVertexChannel = meshBlendShape.VertexChannelData[i];
                auto& fbVertexChannel = fbBlendShape->fvertexchanneldata.emplace_back(std::make_unique<CrossSchema::VertexChannelAssetDataT>());

                fbVertexChannel->fdata.resize(curVertexChannel.mData.size());
                std::copy(curVertexChannel.mData.begin(), curVertexChannel.mData.end(), fbVertexChannel->fdata.begin());

                fbVertexChannel->fdataformat = static_cast<UInt32>(curVertexChannel.mDataFormat);
                fbVertexChannel->ffrequency = curVertexChannel.mFrequency;
                fbVertexChannel->fmiscflag = static_cast<UInt16>(curVertexChannel.mMiscFlag);
                fbVertexChannel->freserve0 = static_cast<UInt16>(curVertexChannel.mReserve0);
                fbVertexChannel->freserve1 = static_cast<UInt16>(curVertexChannel.mReserve1);
                fbVertexChannel->fstream = curVertexChannel.mStream;
                fbVertexChannel->fstreamoffset = curVertexChannel.mStreamOffset;
                fbVertexChannel->fstride = curVertexChannel.mStride;
                fbVertexChannel->fvertexchannel = static_cast<UInt32>(curVertexChannel.mVertexChannel);
            }

            fbBlendShape->fchannelinfos.reserve(meshBlendShape.ChannelShapeData.size());
            for (auto channelIdx = 0; channelIdx < meshBlendShape.ChannelShapeData.size(); ++channelIdx)
            {
                const auto& curChannelInfo = meshBlendShape.ChannelShapeData[channelIdx];
                auto& fbBlendShapeChannel = fbBlendShape->fchannelinfos.emplace_back(std::make_unique<CrossSchema::ImportBlendShapeChannelInfoT>());

                fbBlendShapeChannel->fname = std::string(meshBlendShape.ChannelNameData[channelIdx].GetCString());
                fbBlendShapeChannel->fnormalizedfullweights.resize(curChannelInfo.DeltaShapes.size());
                std::copy(curChannelInfo.NormalizedFullWeights.begin(), curChannelInfo.NormalizedFullWeights.end(), fbBlendShapeChannel->fnormalizedfullweights.begin());

                fbBlendShapeChannel->fdeltashapes.reserve(curChannelInfo.DeltaShapes.size());
                for (auto shapeIdx = 0; shapeIdx < curChannelInfo.DeltaShapes.size(); ++shapeIdx)
                {
                    const auto& curDeltaShape = curChannelInfo.DeltaShapes[shapeIdx];
                    auto fbDeltaShape = std::make_unique<CrossSchema::ImportDeltaShapeInfoT>();

                    fbDeltaShape->fvertexstart = curDeltaShape.mVertexStart;
                    fbDeltaShape->fvertexcount = curDeltaShape.mVertexCount;

                    fbBlendShapeChannel->fdeltashapes.emplace_back(std::move(fbDeltaShape));
                }
            }

            fbPartAssetInfo->fblendshape = std::move(fbBlendShape);
        }
    }

    // mMeshPartLodStartIndex
    meshAssetDataT.fmeshpartlodstartindex.resize(mMeshPartLodStartIndex.size());
    std::copy(mMeshPartLodStartIndex.begin(), mMeshPartLodStartIndex.end(), meshAssetDataT.fmeshpartlodstartindex.begin());

    // mMeshPartNames
    meshAssetDataT.fmeshpartnames.resize(mMeshPartNames.size());
    std::copy(mMeshPartNames.begin(), mMeshPartNames.end(), meshAssetDataT.fmeshpartnames.begin());

    // mName
    meshAssetDataT.fname = mName.length() <= 0 ? "unknow" : mName;

    // mPrimitiveCount
    meshAssetDataT.fprimitivecount = mPrimitiveCount;

    // mVersion
    meshAssetDataT.fversion = mVersion;

    // mVertexChannelData
    meshAssetDataT.fvertexchanneldata.reserve(mVertexChannelData.size());
    for (int i = 0; i < mVertexChannelData.size(); i++)
    {
        auto& vertexChannelAssetDataT = meshAssetDataT.fvertexchanneldata.emplace_back(std::make_unique<CrossSchema::VertexChannelAssetDataT>());

        vertexChannelAssetDataT->fdata.resize(mVertexChannelData[i].mData.size());
        std::copy(mVertexChannelData[i].mData.begin(), mVertexChannelData[i].mData.end(), vertexChannelAssetDataT->fdata.begin());

        vertexChannelAssetDataT->fdataformat = mVertexChannelData[i].mDataFormat;
        vertexChannelAssetDataT->ffrequency = mVertexChannelData[i].mFrequency;
        vertexChannelAssetDataT->fmiscflag = (uint16_t)mVertexChannelData[i].mMiscFlag;
        vertexChannelAssetDataT->freserve0 = (uint16_t)mVertexChannelData[i].mReserve0;
        vertexChannelAssetDataT->freserve1 = (uint16_t)mVertexChannelData[i].mReserve1;
        vertexChannelAssetDataT->fstream = mVertexChannelData[i].mStream;
        vertexChannelAssetDataT->fstreamoffset = mVertexChannelData[i].mStreamOffset;
        vertexChannelAssetDataT->fstride = mVertexChannelData[i].mStride;
        vertexChannelAssetDataT->fvertexchannel = mVertexChannelData[i].mVertexChannel;
    }

    // mVertexChannelSemanticMask
    meshAssetDataT.fvertexchannelsemanticmask = mVertexChannelSemanticMask;

    // mVertexCount
    meshAssetDataT.fvertexcount = mVertexCount;

    // PhysicsCollision
    meshAssetDataT.fphysicscollision = std::make_unique<CrossSchema::PhysicsCollisionT>();
    for (auto& boxCollision : mPhysicsCollision.boxCollision)
    {
        auto box = std::make_unique<CrossSchema::PhysicsBoxCollisionT>();
        box->position = std::make_unique<CrossSchema::float3>(boxCollision.position.x, boxCollision.position.y, boxCollision.position.z);
        box->rotate = std::make_unique<CrossSchema::float4>(boxCollision.rotate.x, boxCollision.rotate.y, boxCollision.rotate.z, boxCollision.rotate.w);
        box->halfextents = std::make_unique<CrossSchema::float3>(boxCollision.halfExtents.x, boxCollision.halfExtents.y, boxCollision.halfExtents.z);
        meshAssetDataT.fphysicscollision->boxcollision.emplace_back(std::move(box));
    }
    for (auto& sphereCollision : mPhysicsCollision.sphereCollision)
    {
        auto sphere = std::make_unique<CrossSchema::PhysicsSphereCollisionT>();
        sphere->position = std::make_unique<CrossSchema::float3>(sphereCollision.position.x, sphereCollision.position.y, sphereCollision.position.z);
        sphere->radius = sphereCollision.radius;
        meshAssetDataT.fphysicscollision->spherecollision.emplace_back(std::move(sphere));
    }
    for (auto& capsuleCollision : mPhysicsCollision.capsuleCollision)
    {
        auto capsule = std::make_unique<CrossSchema::PhysicsCapsuleCollisionT>();
        capsule->position = std::make_unique<CrossSchema::float3>(capsuleCollision.position.x, capsuleCollision.position.y, capsuleCollision.position.z);
        capsule->rotate = std::make_unique<CrossSchema::float4>(capsuleCollision.rotate.x, capsuleCollision.rotate.y, capsuleCollision.rotate.z, capsuleCollision.rotate.w);
        capsule->radius = capsuleCollision.radius;
        capsule->halfHeight = capsuleCollision.halfHeight;
        meshAssetDataT.fphysicscollision->capsulecollision.emplace_back(std::move(capsule));
    }
    for (auto& convexCollision : mPhysicsCollision.convexCollision)
    {
        auto convex = std::make_unique<CrossSchema::PhysicsConvexCollisionT>();
        convex->position = std::make_unique<CrossSchema::float3>(convexCollision.position.x, convexCollision.position.y, convexCollision.position.z);
        convex->rotate = std::make_unique<CrossSchema::float4>(convexCollision.rotate.x, convexCollision.rotate.y, convexCollision.rotate.z, convexCollision.rotate.w);
        convex->data = convexCollision.data;
        meshAssetDataT.fphysicscollision->convexcollision.emplace_back(std::move(convex));
    }
    for (auto& meshCollision : mPhysicsCollision.meshCollision)
    {
        auto mesh = std::make_unique<CrossSchema::PhysicsMeshCollisionT>();
        mesh->position = std::make_unique<CrossSchema::float3>(meshCollision.position.x, meshCollision.position.y, meshCollision.position.z);
        mesh->rotate = std::make_unique<CrossSchema::float4>(meshCollision.rotate.x, meshCollision.rotate.y, meshCollision.rotate.z, meshCollision.rotate.w);
        mesh->data = meshCollision.data;
        meshAssetDataT.fphysicscollision->meshcollision.emplace_back(std::move(mesh));
    }

    // Reference Skeleton
    meshAssetDataT.frefskeleton = std::make_unique<CrossSchema::ImportRefSkeletonT>();
    meshAssetDataT.frefskeleton->name = mRefSkeleton.Name;
    meshAssetDataT.frefskeleton->skelteon.reserve(mRefSkeleton.Bones.size());
    meshAssetDataT.fbindposeinv.reserve(mRefSkeleton.Bones.size());
    for (const SkeletonDesc::Bone& bone : mRefSkeleton.Bones)
    {
        auto& importNode = meshAssetDataT.frefskeleton->skelteon.emplace_back(std::make_unique<CrossSchema::ImportBoneNodeT>());
        importNode->name = bone.BoneName;
        importNode->boneid = bone.BoneID;
        importNode->parentid = bone.ParentID;

        // bind pose inverse matrix should not be stored in Skeleton but in MeshAssetData
        importNode->bindposeinv = std::vector<float>(bone.BindPoseInv.data(), bone.BindPoseInv.data() + 16);
        //

        importNode->worldmatrix = std::vector<float>(bone.RefPoseWorld.data(), bone.RefPoseWorld.data() + 16);
        importNode->bindposedef = std::vector<float>(bone.RefPoseBind.data(), bone.RefPoseBind.data() + 16);

        // Bind Pose Inverse Matrix
        auto& invmat = meshAssetDataT.fbindposeinv.emplace_back(std::make_unique<CrossSchema::invmatrixT>());
        invmat->val = std::vector<float>(bone.BindPoseInv.data(), bone.BindPoseInv.data() + 16);
    }

    return true;
}

 bool MeshAssetData::SerializeToFlatbufferFile(const std::string& ndaFilePath, const std::string& importSet)
{
    auto dir = PathHelper::GetDirectoryFromAbsolutePath(ndaFilePath);
    if (!PathHelper::IsDirectoryExist(dir))
    {
        PathHelper::CheckDirectory(dir);
    }
    using namespace CrossSchema;   // safe
    
    ImportMeshAssetDataT meshAssetDataT;
    if (!ToImportMeshAssetDataT(meshAssetDataT))
        return false;
    
    MeshAssetDataResourcePtr mesh;

    //There is no way to check if the file is corrupt, and reading it again will cause a crash, so create a new asset
    // need check for file exist or not, if nda is existed, need reload nda to avoid overide the lod info
    if (EngineGlobal::Inst().GetFileSystem()->HaveFile(ndaFilePath))
    {
        mesh = TypeCast<cross::resource::MeshAssetDataResource>(gAssetStreamingManager->LoadSynchronously(ndaFilePath, false));
    }
    else
    {
        mesh = gResourceMgr.CreateResourceAs<cross::resource::MeshAssetDataResource>();
        Assert(mesh);
        mesh->CreateAsset(ndaFilePath);
    }
    mesh->SetImportSet(importSet);
    mesh->GetRawLODSetting()->mIsStreamable = mIsStreamable;
    mesh->Serialize(meshAssetDataT);
    
    return true;
}

bool MeshAssetData::DeSerializeToFlatbufferFile(const std::string& ndaFilePath, CrossSchema::ImportMeshAssetDataT& importSet) 
 {
     if(ndaFilePath.size() == 0)
        return false;
     auto dir = PathHelper::GetDirectoryFromAbsolutePath(ndaFilePath);
     
     BinaryArchive* fileData = gResourceAssetMgr.ReadAssetFile(PathHelper::GetRelativePath(ndaFilePath).c_str());
     resource::LoadNDAFileInfo fileInfo;
     if (gResourceAssetMgr.GetLoadNDAInfo(fileData, fileInfo) == false)
         return false;
     auto* res = CrossSchema::GetResourceAsset(fileData->Data() + fileInfo.GetOffet());
     res->resource_as_ImportMeshAssetData()->UnPackTo(&importSet);
     return true;
 }

bool MeshAssetData::SerializeToJson(json& j)
{
    j[std::string(Resource::Version_KEY)] = mVersion;
    j["Name"] = mName;
    j["VertexCount"] = mVertexCount;
    j["PrimitiveCount"] = mPrimitiveCount;
    j["VertexChannelSemanticMask"] = mVertexChannelSemanticMask;
    mIndexStream.SeriallizeToJson(j["IndexStream"]);

    auto jsonVector = json::array();

    jsonVector.clear();
    for (int i = 0; i < mVertexChannelData.size(); i++)
    {
        auto tmpj = json::object();
        mVertexChannelData[i].SeriallizeToJson(tmpj);
        jsonVector.push_back(tmpj);
    }
    j["VertexChannelData"] = jsonVector;

    jsonVector.clear();
    for (int i = 0; i < mMeshPartInfo.size(); i++)
    {
        auto tmpj = json::object();
        mMeshPartInfo[i].SeriallizeToJson(tmpj);
        jsonVector.push_back(tmpj);
    }
    j["MeshPartInfo"] = jsonVector;

    j["MeshPartLodStartIndex"] = mMeshPartLodStartIndex;
    j["MaterialNames"] = mMaterialNames;
    j["MeshPartNames"] = mMeshPartNames;

    jsonVector.clear();
    for (int i = 0; i < mCollisionTree.size(); i++)
    {
        auto tmpj = json::object();
        mCollisionTree[i].SeriallizeToJson(tmpj);
        jsonVector.push_back(tmpj);
    }
    j["CollisionTree"] = jsonVector;

    mAABB.SeriallizeToJson(j["AABB"]);

    j["CustomAttributeVersion"] = mCustomAttributeVersion;
    j["CustomAttributeVersionFlag"] = mCustomAttributeVersionFlag;

    mCustomAttributeInfo.SeriallizeToJson(j["CustomAttributeInfo"]);
    j["CustomAttribute"] = mCustomAttribute;

    return true;
}

const UInt32 MeshAssetData::GetMeshPartCount(UInt32 lod) const
{
    if (lod < mMeshPartLodStartIndex.size())
    {
        if (lod + 1 < mMeshPartLodStartIndex.size())
            return mMeshPartLodStartIndex[lod + 1] - mMeshPartLodStartIndex[lod];
        else
            return (UInt32)mMeshPartInfo.size() - mMeshPartLodStartIndex[lod];
    }
    else
    {
        return 0;
    }
}

void MeshAssetData::GetMeshLodInfo(UInt32 lod, UInt32& meshPartStartIndex, UInt32& meshPartCount) const
{
    if (lod < mMeshPartLodStartIndex.size())
    {
        meshPartStartIndex = mMeshPartLodStartIndex[lod];
        meshPartCount = GetMeshPartCount(lod);
    }
    else
    {
        meshPartStartIndex = 0;
        meshPartCount = 0;
    }
}

VertexChannelAssetData& MeshAssetData::GetChannelAssetData(VertexChannel channel)
{
    for (int i = 0; i < mVertexChannelData.size(); i++)
    {
        if (mVertexChannelData[i].mVertexChannel == static_cast<UInt32>(channel))
        {
            return mVertexChannelData[i];
        }
    }
    throw std::runtime_error("GetChannelAssetData cannot find the channel in mVertexChannelData\n");
}

const VertexChannelAssetData& MeshAssetData::GetChannelAssetData(VertexChannel channel) const
{
    for (int i = 0; i < mVertexChannelData.size(); i++)
    {
        if (mVertexChannelData[i].mVertexChannel == static_cast<UInt32>(channel))
        {
            return mVertexChannelData[i];
        }
    }
    throw std::runtime_error("GetChannelAssetData cannot find the channel in mVertexChannelData\n");
}

const UInt32 MeshAssetData::GetVertexCount(VertexChannel posChannel) const
{
    if (!IfExistChannel(posChannel))
    {
        return 0;
    }
    const VertexChannelAssetData& vertexChannel = GetChannelAssetData(posChannel);
    return static_cast<UInt32>(vertexChannel.mData.size()) / static_cast<UInt32>(vertexChannel.mStride);
}

const bool MeshAssetData::IfExistChannel(VertexChannel channel) const
{
    for (int i = 0; i < mVertexChannelData.size(); i++)
    {
        if (mVertexChannelData[i].mVertexChannel == static_cast<UInt32>(channel))
        {
            return true;
        }
    }
    return false;
}

void MeshAssetData::GenerateMeshPartCluster()
{
}

PhysicsCollisionImport& MeshAssetData::GetPhysicsCollision()
{
    return mPhysicsCollision;
}

void MeshAssetData::SetPhysicsMeshCollision(const PhysicsMeshCollisionImport& meshColl)
{
    mPhysicsCollision.meshCollision.push_back(meshColl);
}

}   // namespace cross::editor
