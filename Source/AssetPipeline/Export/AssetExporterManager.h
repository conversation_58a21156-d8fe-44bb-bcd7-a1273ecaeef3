#pragma once
#include "CrossBase/CEMetaMacros.h"
#include "AssetPipeline/Utils/AssetPipelineAPI.h"
#include "AssetPipeline/Export/ModelExporter/FBXExporter/FBXExporter.h"

namespace cross::editor 
{
    class  AssetExporterManager
    {
    public:
         AssetExporterManager();
         ~AssetExporterManager();

         CEFunction(Editor) 
         ASSET_API bool ExportSelectModelsAsFBX(const char* ndapath, const char* fbxpath);

         CEFunction(Editor) 
         ASSET_API bool MergeSelectModelsAsFBX(std::vector<std::string> fbxPath, std::vector<cross::Float3> fbxPosition, std::vector<cross::Float3> fbxRotation, std::vector<cross::Float3> fbxScale, const char* fbxpath);

         CEFunction(Editor) 
         ASSET_API static AssetExporterManager& Instance() noexcept;

    private:
        FBXExporter* fbxExporter = nullptr;
    };
}