#include "AssetPipeline/PCH/AssetPipelinePCH.h"
#include "AssetPipeline/Interface/AssetPipeline.h"
#include "AssetPipeline/Cook/AssetCookerManager.h"
#include "AssetPipeline/Import/AssetImporter.h"
#include "AssetPipeline/Import/AssetImporterManager.h"
#include "AssetPipeline/Export/AssetExporterManager.h"
#include "AssetPipeline/Import/ShaderImporter/ShaderImportSettings.h"
#include "AssetPipeline/Import/TextureImporter/TextureImporter.h"
#include "AssetPipeline/Cook/TextureCooker/TextureCooker.h"

#include "CrossBase/FileSystem/PathHelper.h"
#include <fstream>
#include "CrossBase/Serialization/SerializeNode.h"
#include "CrossBase/Threading/TaskSystem.h"

#include "Runtime/Interface/CrossEngineImp.h"
#include "FileSystem/filesystem.h"
#include "Template/TypeTraits.hpp"

#if !(CROSSENGINE_IOS || CROSSENGINE_ANDROID)

#endif

/// globals
namespace cross::editor {
AssetImporterManager gAssetImportManager{};
AssetCookerManager gAssetCookManager{};
AssetExporterManager gAssetExportManager{};
}   // namespace cross::editor

//===============================================================================
// import files
//===============================================================================
int GetAssetType(const char* assetFilePath)
{
    return static_cast<int>(cross::editor::AssetImporterManager::Instance().GetAssetType(assetFilePath));
}

void SetImportSettings(int assetType, void* assetImportSettings)
{
    cross::editor::AssetImporterManager::Instance().SetImportSettings(static_cast<cross::editor::AssetType>(assetType), assetImportSettings);
}

bool ImportAsset(const char* assetFilePath, const char* ndaSavePath)
{
    cross::editor::AssetImportResult ret = cross::editor::AssetImporterManager::Instance().ImportAsset(assetFilePath, ndaSavePath);
    return ret.bSuccess;
}

bool CookAsset(char const* srcNdaPath, char const* dstNdaPath, int platform)
{
    return cross::editor::AssetCookerManager::Instance().CookAsset(srcNdaPath, dstNdaPath, static_cast<cross::editor::AssetPlatform>(platform));
}

char* GetSkeletalMeshReferenceString(char* srcNdaPath)
{
    static std::string ret;
    ret.clear();


    cross::filesystem::FileSystem* fileSystem = cross::EngineGlobal::Inst().GetFileSystem();
    Assert(fileSystem);
    cross::filesystem::IFilePtr file = fileSystem->Open(srcNdaPath);
    Assert(file);
    {
        cross::FileArchive archive{file};
        cross::FBSerializer serializer{archive};
        std::string pathToSkeleton;

        // only read the necessary field to get the Reference string
        // auto fbmesh = CrossSchema::GetResourceAsset(serializer.GetArchive().Data())->resource_as_ImportMeshes();
        auto fbmesh = CrossSchema::GetResourceAsset(serializer.GetCachedBuffer().data() + serializer.InitialOffset())->resource_as_ImportMeshes();
        if (serializer.Read(static_cast<const flatbuffers::Table*>(fbmesh), pathToSkeleton, CrossSchema::ImportMeshes::VT_PATHTOSKELETON))
        {
            ret += pathToSkeleton;
        }
        else
        {
            LOG_EDITOR_ERROR("GetSkeletalMeshReferenceString deserialize failed");
        }
    }
    return ret.data();
}

void SetImportAssetCompleteCallback(ImportAssetCompleteCallback callback)
{
    gImportAssetCompleteCallback = callback;
}

void SetShaderImportSettings(cross::editor::ShaderImportSettings* settings)
{
    cross::editor::AssetImporterManager::Instance().SetShaderImportSettings(*settings);
}

void SetComputeShaderImportSettings(cross::editor::ShaderImportSettings* settings)
{
    cross::editor::AssetImporterManager::Instance().SetComputeShaderImportSettings(*settings);
}

const char* AssetPipeline_CheckTextureFormat(unsigned char* data, size_t size) 
{
    if (size >= 4 && *reinterpret_cast <unsigned int*>(data) == 0x00000001)   // 1 -> .exr
    {
        return ".exr";
    }
    else if (size >= 4 && *reinterpret_cast<unsigned short*>(data) == 0x4D42)   // "BM"  -> .bmp
    {
        return ".bmp";
    }
    else if (size >= 4 && *reinterpret_cast<unsigned int*>(data) == 0x474E5089)   // ".PNG" -> .png
    {
        return ".png";
    }
    else if (size >= 4 && *reinterpret_cast<unsigned int*>(data) == 0x20444453)   // "DDS " -> .dds
    {
        return ".dds";
    }
    else if (size >= 10 && *reinterpret_cast<unsigned int*>(data + 6) == 0x4649464A)   // "JFIF" -> .jpg
    {
        return ".jpg";
    }
    else if (size >= 18 && *reinterpret_cast<unsigned int*>(data + size - 18) == 0x45555254)   // "TRUE" -> .tga
    {
        return ".tga";
    }
    return ".image";
}

bool AssetPipeline_ExportTexture(const char* target, const char* source)
{
    cross::editor::AssetImporterManager& importer_manager = cross::editor::AssetImporterManager::Instance();
    auto importer = static_cast<cross::editor::TextureImporter*>(importer_manager.GetAssetImporter(cross::editor::AssetType::Texture));

    cmft::Image src;
    CrossSchema::TextureAssetT texture;
    importer->ByPathGetTextureRawData(source, src, texture);

    size_t size = texture.rawdata.size();
    if (size > 0)
    {
        uint8_t* rawdata = texture.rawdata.data();
        bool isSRGB = (texture.colorspace == CrossSchema::ColorSpace::SRGB);

        const char* extension = AssetPipeline_CheckTextureFormat(rawdata, size);
        char target1[1024];
        sprintf(target1, "%s%s", target, extension);

        cross::filesystem::FileSystem* fileSystem = cross::EngineGlobal::Inst().GetFileSystem();
        if (fileSystem->HaveFile(target1) == false)
        {
            if (strcmp(extension, ".exr") == 0)
            {
                int width = *reinterpret_cast<int*>(rawdata + 4);
                int height = *reinterpret_cast<int*>(rawdata + 8);
                void* pixels = (rawdata + 12);
                imageio::save_exr(target1, width, height, pixels, isSRGB);
            }
            else
            {
                FILE* file = nullptr;
                fopen_s(&file, target1, "wb");
                fwrite(texture.rawdata.data(), 1, size, file);
                fclose(file);
            }
        }
        return true;
    }

    return false;
}