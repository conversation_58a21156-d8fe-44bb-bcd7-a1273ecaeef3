
#if WIN32

#    include "DXILReflection.h"

using namespace CrossSchema;

namespace cross::editor {

ShaderResourceType MapResourceType(D3D_SHADER_INPUT_TYPE type, D3D_SRV_DIMENSION dimension)
{
    switch (type)
    {
    case D3D_SIT_SAMPLER:
        return ShaderResourceType::Sampler;
    case D3D_SIT_TEXTURE:
        switch (dimension)
        {
        case D3D_SRV_DIMENSION_BUFFER:
            return ShaderResourceType::TexelBuffer;
        case D3D_SRV_DIMENSION_TEXTURE1D:
            return ShaderResourceType::Texture1D;
        case D3D_SRV_DIMENSION_TEXTURE1DARRAY:
            return ShaderResourceType::Texture1DArray;
        case D3D_SRV_DIMENSION_TEXTURE2D:
            return ShaderResourceType::Texture2D;
        case D3D_SRV_DIMENSION_TEXTURE2DARRAY:
            return ShaderResourceType::Texture2DArray;
        case D3D_SRV_DIMENSION_TEXTURE2DMS:
            return ShaderResourceType::Texture2DMS;
        case D3D_SRV_DIMENSION_TEXTURE2DMSARRAY:
            return ShaderResourceType::Texture2DMSArray;
        case D3D_SRV_DIMENSION_TEXTURE3D:
            return ShaderResourceType::Texture3D;
        case D3D_SRV_DIMENSION_TEXTURECUBE:
            return ShaderResourceType::TextureCube;
        case D3D_SRV_DIMENSION_TEXTURECUBEARRAY:
            return ShaderResourceType::TextureCubeArray;
        default:
            assert(false);
            return ShaderResourceType::Unknown;
        }
        break;
    case D3D11_SIT_UAV_RWTYPED:
        switch (dimension)
        {
        case D3D_SRV_DIMENSION_BUFFER:
            return ShaderResourceType::RWTexelBuffer;
        case D3D_SRV_DIMENSION_TEXTURE1D:
            return ShaderResourceType::RWTexture1D;
        case D3D_SRV_DIMENSION_TEXTURE1DARRAY:
            return ShaderResourceType::RWTexture1DArray;
        case D3D_SRV_DIMENSION_TEXTURE2D:
            return ShaderResourceType::RWTexture2D;
        case D3D_SRV_DIMENSION_TEXTURE2DARRAY:
            return ShaderResourceType::RWTexture2DArray;
        case D3D_SRV_DIMENSION_TEXTURE3D:
            return ShaderResourceType::RWTexture3D;
        default:
            assert(false);
            return ShaderResourceType::Unknown;
        }
        break;
    case D3D_SIT_RTACCELERATIONSTRUCTURE:
        return ShaderResourceType::AccelStruct;
    case D3D_SIT_STRUCTURED:
        return ShaderResourceType::StructuredBuffer;
    case D3D_SIT_UAV_RWSTRUCTURED:
        return ShaderResourceType::RWStructuredBuffer;
    case D3D_SIT_BYTEADDRESS:
        return ShaderResourceType::ByteAddressBuffer;
    case D3D_SIT_UAV_RWBYTEADDRESS:
        return ShaderResourceType::RWByteAddressBuffer;
    default:
        assert(false);
        return ShaderResourceType::Unknown;
    }
}

ShaderVariableType MapVariableType(D3D_SHADER_VARIABLE_TYPE type)
{
    switch (type)
    {
    case D3D_SVT_BOOL:
        return ShaderVariableType::Bool;
    case D3D_SVT_UINT8:
        return ShaderVariableType::UInt8;
    case D3D_SVT_UINT:
        return ShaderVariableType::UInt32;
    case D3D_SVT_INT:
        return ShaderVariableType::Int32;
    case D3D_SVT_MIN16FLOAT:
        return ShaderVariableType::Half;
    case D3D_SVT_FLOAT:
        return ShaderVariableType::Float;
    case D3D_SVT_DOUBLE:
        return ShaderVariableType::Double;
    case D3D_SVT_VOID:
        return ShaderVariableType::Unknown;
    default:
        assert(false);
        return ShaderVariableType::Unknown;
    }
}

ShaderVariableType MapReturnType(D3D_RESOURCE_RETURN_TYPE type)
{
    switch (type)
    {
    case D3D_RETURN_TYPE_UNORM:
    case D3D_RETURN_TYPE_SNORM:
    case D3D_RETURN_TYPE_FLOAT:
        return ShaderVariableType::Float;
    case D3D_RETURN_TYPE_DOUBLE:
        return ShaderVariableType::Double;
    case D3D_RETURN_TYPE_SINT:
        return ShaderVariableType::Int32;
    case D3D_RETURN_TYPE_UINT:
        return ShaderVariableType::UInt32;
    case D3D_RETURN_TYPE_MIXED:
    case D3D_RETURN_TYPE_CONTINUED:
    default:
        Assert(false);
        return ShaderVariableType::Unknown;
    }
}

UInt32 GetTypeSize(ID3D12ShaderReflectionType* type)
{
    /*
    * TODO(peterwjma): hacking to get type size
    * From
    * Project: DirectXShaderCompiler
    * branch: release-1.6.2109
    * */
    struct StringRef
    {
        const char* Data;
        size_t Length;
    };

    struct CShaderReflectionType : public ID3D12ShaderReflectionType
    {
        D3D12_SHADER_TYPE_DESC              m_Desc;
        UINT                                m_SizeInCBuffer;
        std::string                         m_Name;
        std::vector<StringRef>              m_MemberNames;
        std::vector<CShaderReflectionType*> m_MemberTypes;
        CShaderReflectionType* m_pSubType;
        CShaderReflectionType* m_pBaseClass;
        std::vector<CShaderReflectionType*> m_Interfaces;
        ULONG_PTR                           m_Identity;

        STDMETHOD(GetDesc)(D3D12_SHADER_TYPE_DESC* pDesc) { return {}; }
        STDMETHOD_(ID3D12ShaderReflectionType*, GetMemberTypeByIndex)(UINT Index) { return {}; }
        STDMETHOD_(ID3D12ShaderReflectionType*, GetMemberTypeByName)(LPCSTR Name) { return {}; }
        STDMETHOD_(LPCSTR, GetMemberTypeName)(UINT Index) { return {}; }
        STDMETHOD(IsEqual)(THIS_ ID3D12ShaderReflectionType* pType) { return {}; }
        STDMETHOD_(ID3D12ShaderReflectionType*, GetSubType)(THIS) { return {}; }
        STDMETHOD_(ID3D12ShaderReflectionType*, GetBaseClass)(THIS) { return {}; }
        STDMETHOD_(UINT, GetNumInterfaces)(THIS) { return {}; }
        STDMETHOD_(ID3D12ShaderReflectionType*, GetInterfaceByIndex)(THIS_ UINT uIndex) { return {}; }
        STDMETHOD(IsOfType)(THIS_ ID3D12ShaderReflectionType* pType) { return {}; }
        STDMETHOD(ImplementsInterface)(THIS_ ID3D12ShaderReflectionType* pBase) { return {}; }
    };

    auto hackType = reinterpret_cast<CShaderReflectionType*>(type);
    return hackType->m_SizeInCBuffer;
}

std::unique_ptr<CrossSchema::ShaderVariableExT> ReflectVariable(ID3D12ShaderReflectionType* type, LPCSTR name)
{
    D3D12_SHADER_TYPE_DESC desc{};
    type->GetDesc(&desc);
    
    auto var = std::make_unique<CrossSchema::ShaderVariableExT>();
    var->name = name;
    var->offset = desc.Offset;
    var->size = GetTypeSize(type);
    var->array_size = std::max(desc.Elements, 1u);

    if (desc.Class == D3D_SHADER_VARIABLE_CLASS::D3D_SVC_STRUCT)
    {
        var->type = ShaderVariableType::Struct;
        ID3D12ShaderReflectionType* memberType = nullptr;
        for (UInt32 i = 0; i < desc.Members; ++i)
        {
            memberType = type->GetMemberTypeByIndex(i);
            var->members.emplace_back(ReflectVariable(memberType, type->GetMemberTypeName(i)));
        }

        if (desc.Elements > 1)
        {
            /*
            * From 
            * Project: DirectXShaderCompiler 
            * branch: release-1.6.2109 
            * File: lib\HLSL\DxilContainerReflection.cpp
            * Line: 1153
            */
            D3D12_SHADER_TYPE_DESC lastMemberDesc;
            memberType->GetDesc(&lastMemberDesc);
            auto cleanSize = lastMemberDesc.Offset + GetTypeSize(memberType);
            var->array_stride = ((cleanSize + 15) & ~0xF);
        }
    }
    else
    {
        var->type = MapVariableType(desc.Type);
        var->row_count = desc.Rows;
        var->col_count = desc.Columns;

        if (desc.Elements > 1)
        {
            /*
            * From
            * Project: DirectXShaderCompiler
            * branch: release-1.6.2109
            * File: lib\HLSL\DxilContainerReflection.cpp
            * Line: 1213
            */
            if (var->type == ShaderVariableType::Double && var->row_count > 2)
            {
                var->array_stride = 32;
            }
            else
            {
                var->array_stride = 16;
            }
        }
    }

    return std::move(var);
}

void ReflectionDXIL(ID3D12ShaderReflection* pReflection, const ShaderStageBit stage, ShaderLayoutT* dxilStageLayout, CrossSchema::uint3* groupSize)
{
    // Use reflection interface here.
    D3D12_SHADER_DESC shader_desc;
    pReflection->GetDesc(&shader_desc);
    if (stage == ShaderStageBit::Compute)
    {
        Assert(groupSize);
        UINT x, y, z;
        pReflection->GetThreadGroupSize(&x, &y, &z);
        groupSize->mutate_x(x);
        groupSize->mutate_y(y);
        groupSize->mutate_z(z);
    }

    D3D12_SHADER_DESC shaderDesc{};
    pReflection->GetDesc(&shaderDesc);

    auto stageMask = static_cast<std::underlying_type_t<ShaderStageBit>>(stage);

    // Add parameters for shader resources (constant buffers, textures, samplers, etc.)
    for (UINT resourceIndex = 0; resourceIndex < shaderDesc.BoundResources; resourceIndex++)
    {
        D3D12_SHADER_INPUT_BIND_DESC bindDesc;
        pReflection->GetResourceBindingDesc(resourceIndex, &bindDesc);

        switch (bindDesc.Type)
        {
        case D3D_SIT_CBUFFER:
        case D3D_SIT_TBUFFER:
        {
            auto pCBuffer = pReflection->GetConstantBufferByName(bindDesc.Name);
            D3D12_SHADER_BUFFER_DESC cbDesc;
            pCBuffer->GetDesc(&cbDesc);

            // Track just the constant buffer itself.
            auto cbuffer = std::make_unique<ShaderConstantBufferT>();
            cbuffer->name = cbDesc.Name;
            cbuffer->type = cbDesc.Type == D3D_CT_CBUFFER ? ShaderResourceType::ConstantBuffer : ShaderResourceType::TextureBuffer;
            cbuffer->space = bindDesc.Space;
            cbuffer->index = bindDesc.BindPoint;
            cbuffer->size = cbDesc.Size;
            cbuffer->array_size = bindDesc.BindCount;
            cbuffer->stage_mask |= stageMask;

            // Track all variables in this constant buffer, and save them in struct_type->members, instead of members(deprecated).
            auto structType = std::make_unique<ShaderStructTypeT>();
            structType->size = cbDesc.Size;
            for (UINT i = 0; i < cbDesc.Variables; i++)
            {
                auto pVar = pCBuffer->GetVariableByIndex(i);
                D3D12_SHADER_VARIABLE_DESC varDesc;
                pVar->GetDesc(&varDesc);

                auto* pVarType = pVar->GetType();
                D3D12_SHADER_TYPE_DESC varTypeDesc;
                pVarType->GetDesc(&varTypeDesc);

                auto varEx = std::make_unique<ShaderVariableExT>();
                varEx->name = varDesc.Name;
                varEx->type = MapVariableType(varTypeDesc.Type);
                varEx->offset = varDesc.StartOffset;
                varEx->size = varDesc.Size;
                varEx->row_count = varTypeDesc.Rows;
                varEx->col_count = varTypeDesc.Columns;
                varEx->array_size = std::max(varTypeDesc.Elements, 1u);

                structType->members.emplace_back(std::move(varEx));
            }
            cbuffer->struct_type = std::move(structType);

            if (strcmp(cbDesc.Name, "$Globals") == 0)
            {
                assert(dxilStageLayout->specialization_constants == nullptr);
                dxilStageLayout->specialization_constants = std::move(cbuffer);
            }
            else
            {
                dxilStageLayout->constant_buffers.emplace_back(std::move(cbuffer));
            }

            break;
        }
        case D3D_SIT_STRUCTURED:
        case D3D_SIT_UAV_RWSTRUCTURED:
        case D3D_SIT_TEXTURE:
        case D3D_SIT_SAMPLER:
        case D3D_SIT_UAV_RWTYPED:
        case D3D_SIT_BYTEADDRESS:
        case D3D_SIT_UAV_RWBYTEADDRESS:
        {
            auto shaderResource = std::make_unique<ShaderResourceT>();
            shaderResource->name = bindDesc.Name;
            shaderResource->type = MapResourceType(bindDesc.Type, bindDesc.Dimension);
            if (bindDesc.Type == D3D_SIT_TEXTURE)
            {
                shaderResource->return_type = MapReturnType(bindDesc.ReturnType);
            }
            else
            {
                shaderResource->return_type = ShaderVariableType::Unknown;
            }
            shaderResource->space = bindDesc.Space;
            shaderResource->index = bindDesc.BindPoint;
            shaderResource->array_size = bindDesc.BindCount;
            shaderResource->stage_mask |= stageMask;

            if(bindDesc.Type == D3D_SIT_STRUCTURED || bindDesc.Type == D3D_SIT_UAV_RWSTRUCTURED)
            {
                // maybe structured buffer layout was also queried by this interface
                auto buffer = pReflection->GetConstantBufferByName(bindDesc.Name);
                D3D12_SHADER_BUFFER_DESC bufferDesc;
                buffer->GetDesc(&bufferDesc);

                Assert(bufferDesc.Variables == 1);
                auto* element = buffer->GetVariableByIndex(0);
                auto* elementType = element->GetType();
                D3D12_SHADER_TYPE_DESC elementTypeDesc;
                elementType->GetDesc(&elementTypeDesc);

                if (elementTypeDesc.Members)
                {
                    auto structType = std::make_unique<ShaderStructTypeT>();
                    structType->size = bufferDesc.Size;
                    for (UINT i = 0; i < elementTypeDesc.Members; i++)
                    {
                        auto member = ReflectVariable(elementType->GetMemberTypeByIndex(i), elementType->GetMemberTypeName(i));
                        structType->members.emplace_back(std::move(member));
                    }

                    shaderResource->struct_type = std::move(structType);
                }
            }

            dxilStageLayout->resources.emplace_back(std::move(shaderResource));
            break;
        }
        case D3D_SIT_RTACCELERATIONSTRUCTURE:
        {
            auto shaderResource = std::make_unique<ShaderResourceT>();
            shaderResource->name = bindDesc.Name;
            shaderResource->type = MapResourceType(bindDesc.Type, bindDesc.Dimension);
            shaderResource->space = bindDesc.Space;
            shaderResource->index = bindDesc.BindPoint;
            shaderResource->array_size = 1;
            shaderResource->stage_mask |= stageMask;
            dxilStageLayout->resources.emplace_back(std::move(shaderResource));
            break;
        }
        case D3D_SIT_UAV_APPEND_STRUCTURED:
        case D3D_SIT_UAV_CONSUME_STRUCTURED:
        case D3D_SIT_UAV_RWSTRUCTURED_WITH_COUNTER:
        default:
            assert(false);
            break;
        }
    }
}

}   // namespace cross::editor

#endif
