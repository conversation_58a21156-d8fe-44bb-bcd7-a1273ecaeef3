#pragma once
#include <unordered_map>
#include <set>
#include "AssetPipeline/Cook/CookSetting.h"
#include "AssetPipeline/Cook/AssetCooker.h"
#include "AssetPipeline/Utils/AssetIO.h"
#include "AssetPipeline/Utils/AssetPipelineAPI.h"

struct ASTCParam;

namespace cross::editor {
class ASSET_API TextureCookSettting : public ICookSetting
{
public:
    static ICookSetting* Produce() { return new TextureCookSettting(); }

public:
    //@brief Serialize
    virtual SerializeNode Serialize() override;
    //@brief Deserialize
    virtual bool Deserialize(const DeserializeNode& node) override;
    //@brief ClassName
    static constexpr const char* ClassName = "TextureCookSettting";
};

using CrossSchema::TextureAssetImage;
using CrossSchema::TextureAssetT;
using CrossSchema::TextureDimension;
using CrossSchema::TextureFormat;

class TextureCooker : public AssetCooker
{
public:
    ASSET_API bool CheckClassID(int classID) override;
    ASSET_API bool Cook(BinaryArchive* fileData, const resource::LoadNDAFileInfo& fileInfo, const char* dstNdaPath, AssetPlatform cookPlatform, CookSettingManager& settingMgr) override;
    bool CookTexture(const TextureAssetT& srcData, TextureAssetT& dstData);
};
}   // namespace cross::editor
