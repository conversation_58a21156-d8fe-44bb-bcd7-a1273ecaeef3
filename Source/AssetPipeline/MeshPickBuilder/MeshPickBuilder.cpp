#include <vector>
#include <numeric>

#include "MeshPickBuilder.h"
#include "CrossBase/Log.h"
#include "CECommon/Common/MeshDefines.h"
#include "memoryhooker/Module.h"
IMPORT_MODULE
namespace cross::editor {

constexpr SInt32 CollisionTriangleCountThreshold = 128;

void MeshPickBuilder::BuildCollisionTree(const CrossSchema::ImportMeshAssetDataT& mMesh, const CrossSchema::ImportMeshPartAssetInfoT& partAssetInfo, CollisionTree& collisionTree)
{
    const cross::Float3* positionHead = nullptr;
    if (mMesh.fvertexchannelsemanticmask & static_cast<std::uint32_t>(VertexSemantic::SemanticPosition))
    {
        auto result = mMesh.fvertexchanneldata.begin();
        for (; result != mMesh.fvertexchanneldata.end(); result++)
        {
            if (result->get()->fvertexchannel == static_cast<std::uint32_t>(VertexChannel::Position0))
                break;
        }
        if (result != mMesh.fvertexchanneldata.end())
        {
            const auto* positionchannel = result->get();
            positionHead = reinterpret_cast<const cross::Float3*>(positionchannel->fdata.data()) + partAssetInfo.fvertexstart;
        }
    }

    if (!positionHead)
        return;

    std::vector<UInt32> indices;
    indices.resize(partAssetInfo.findexcount);

    if (mMesh.findexstream->fis16bitindex)
    {
        const auto* head = reinterpret_cast<const std::uint16_t*>(mMesh.findexstream->fdata.data()) + partAssetInfo.findexstart;
        for (size_t i = 0; i < partAssetInfo.findexcount; i++)
        {
            indices[i] = *(head + i);
        }
    }
    else
    {
        const auto* head = reinterpret_cast<const std::uint32_t*>(mMesh.findexstream->fdata.data()) + partAssetInfo.findexstart;
        std::copy(head, head + partAssetInfo.findexcount, indices.data());
    }

    const UInt32 triangleNum = partAssetInfo.findexcount / 3;
    std::vector<Float3> centroids;
    centroids.resize(triangleNum);
    std::vector<UInt32> tmpIndices(triangleNum * 3);
    UInt32 wedgeIndex = 0;
    UInt32 centroidIndex = 0;
    for (size_t i = 0; i < triangleNum; ++i)
    {
        Float3 centroid = Float3::Zero();
        for (UInt32 corner = 0; corner < 3; ++corner)
        {
            centroid += *(positionHead + indices[(i * 3) + corner]);
            tmpIndices[wedgeIndex++] = indices[(i * 3) + corner];
        }
        centroids[centroidIndex++] = centroid * 0.3333333333f;
    }

    collisionTree.reserve(1000);
    CrossSchema::CollisionNodeT* rootNode = AllocateCollisionNode(collisionTree);
    rootNode->trianglelist.resize(triangleNum);
    std::iota(std::begin(rootNode->trianglelist), std::end(rootNode->trianglelist), 0);
    SplitCollisionNode(centroids, collisionTree, rootNode);
    CalculateNodeBound(rootNode, collisionTree, [&positionHead, &tmpIndices](UInt32 index) -> TrianglePos {
        Float3 pos1 = *(positionHead + tmpIndices[index]);
        Float3 pos2 = *(positionHead + tmpIndices[index + 1]);
        Float3 pos3 = *(positionHead + tmpIndices[index + 2]);
        return std::make_tuple(pos1, pos2, pos3);
    });
}

void MeshPickBuilder::SplitCollisionNode(const std::vector<Float3>& centroids, CollisionTree& collisionTree, CrossSchema::CollisionNodeT* node)
{
    std::vector<SInt32>& triangleList = node->trianglelist;
    SInt32 triangleCount = static_cast<SInt32>(triangleList.size());
    if (triangleCount < CollisionTriangleCountThreshold)
    {
        return;
    }

    SInt32 halfTriangleCount = triangleCount / 2;
    std::vector<SInt32> triangleListLeft;
    std::vector<SInt32> triangleListRight;
    triangleListLeft.reserve(halfTriangleCount);
    triangleListRight.reserve(halfTriangleCount);

    Float3 center = Float3(node->maxpos[0] + node->minpos[0], node->maxpos[1] + node->minpos[1], node->maxpos[2] + node->minpos[2]) * 0.5f;
    Float3 extent = Float3(node->maxpos[0] - node->minpos[0], node->maxpos[1] - node->minpos[1], node->maxpos[2] - node->minpos[2]);
    if (extent.x >= extent.y && extent.x >= extent.z)
    {
        for (SInt32 i = 0; i < triangleCount; i++)
        {
            SInt32 triangleIndex = triangleList[i];
            if (centroids[triangleIndex].x < center.x)
            {
                triangleListLeft.push_back(triangleIndex);
            }
            else
            {
                triangleListRight.push_back(triangleIndex);
            }
        }
    }
    else if (extent.y >= extent.x && extent.y >= extent.z)
    {
        for (SInt32 i = 0; i < triangleCount; i++)
        {
            SInt32 triangleIndex = triangleList[i];
            if (centroids[triangleIndex].y < center.y)
            {
                triangleListLeft.push_back(triangleIndex);
            }
            else
            {
                triangleListRight.push_back(triangleIndex);
            }
        }
    }
    else
    {
        for (SInt32 i = 0; i < triangleCount; i++)
        {
            SInt32 triangleIndex = triangleList[i];
            if (centroids[triangleIndex].z < center.z)
            {
                triangleListLeft.push_back(triangleIndex);
            }
            else
            {
                triangleListRight.push_back(triangleIndex);
            }
        }
    }

    if (triangleListLeft.size() == 0 || triangleListRight.size() == 0)
    {
        triangleListLeft.clear();
        triangleListRight.clear();
        for (SInt32 i = 0; i < triangleCount; i++)
        {
            SInt32 triangleIndex = triangleList[i];
            if (i <= halfTriangleCount)
            {
                triangleListLeft.push_back(triangleIndex);
            }
            else
            {
                triangleListRight.push_back(triangleIndex);
            }
        }
    }

    SInt32 nodeIndex = node->index;

    if (triangleListLeft.size() > 0)
    {
        CrossSchema::CollisionNodeT* left = AllocateCollisionNode(collisionTree);
        node = collisionTree[nodeIndex].get();
        node->leftindex = left->index;
        left->trianglelist = triangleListLeft;
        SplitCollisionNode(centroids, collisionTree, left);
    }

    if (triangleListRight.size() > 0)
    {
        CrossSchema::CollisionNodeT* right = AllocateCollisionNode(collisionTree);
        node = collisionTree[nodeIndex].get();
        node->rightindex = right->index;
        right->trianglelist = triangleListRight;
        SplitCollisionNode(centroids, collisionTree, right);
    }

    if (triangleListLeft.size() > 0 || triangleListRight.size() > 0)
    {
        triangleList.clear();
    }
}

CrossSchema::CollisionNodeT* MeshPickBuilder::AllocateCollisionNode(CollisionTree& collisionTree)
{
    int nodeIndex = static_cast<int>(collisionTree.size());
    collisionTree.push_back(std::move(std::unique_ptr<CrossSchema::CollisionNodeT>(new CrossSchema::CollisionNodeT)));
    CrossSchema::CollisionNodeT* node = (collisionTree[nodeIndex]).get();
    node->minpos = {INFINITY, INFINITY, INFINITY};
    node->maxpos = {-INFINITY, -INFINITY, -INFINITY};
    node->index = nodeIndex;
    node->leftindex = -1;
    node->rightindex = -1;
    return node;
}

void MeshPickBuilder::CalculateNodeBound(CrossSchema::CollisionNodeT* node, const CollisionTree& collisionTree, GetTrianglePos const& f)
{
    if (node->leftindex > 0 && node->leftindex < collisionTree.size())
    {
        CalculateNodeBound(collisionTree[node->leftindex].get(), collisionTree, f);
    }
    if (node->rightindex > 0 && node->rightindex < collisionTree.size())
    {
        CalculateNodeBound(collisionTree[node->rightindex].get(), collisionTree, f);
    }
    std::vector<int>& triangleList = node->trianglelist;
    const UInt32 triangleNums = static_cast<UInt32>(node->trianglelist.size());
    if (triangleNums > 0)
    {
        for (UInt32 i = 0u; i < triangleNums; ++i)
        {
            int triangleIndex = triangleList[i];
            int indexIndex = triangleIndex * 3;
            Float3 pos1, pos2, pos3;
            std::tie(pos1, pos2, pos3) = f(indexIndex);
            node->minpos = {std::min<float>(node->minpos[0], pos1.x), std::min<float>(node->minpos[1], pos1.y), std::min<float>(node->minpos[2], pos1.z)};
            node->maxpos = {std::max<float>(node->maxpos[0], pos1.x), std::max<float>(node->maxpos[1], pos1.y), std::max<float>(node->maxpos[2], pos1.z)};
            node->minpos = {std::min<float>(node->minpos[0], pos2.x), std::min<float>(node->minpos[1], pos2.y), std::min<float>(node->minpos[2], pos2.z)};
            node->maxpos = {std::max<float>(node->maxpos[0], pos2.x), std::max<float>(node->maxpos[1], pos2.y), std::max<float>(node->maxpos[2], pos2.z)};
            node->minpos = {std::min<float>(node->minpos[0], pos3.x), std::min<float>(node->minpos[1], pos3.y), std::min<float>(node->minpos[2], pos3.z)};
            node->maxpos = {std::max<float>(node->maxpos[0], pos3.x), std::max<float>(node->maxpos[1], pos3.y), std::max<float>(node->maxpos[2], pos3.z)};
        }
    }
    else
    {
        if (node->leftindex > 0 && node->leftindex < collisionTree.size())
        {
            node->minpos = {std::min<float>(node->minpos[0], collisionTree[node->leftindex].get()->minpos[0]),
                            std::min<float>(node->minpos[1], collisionTree[node->leftindex].get()->minpos[1]),
                            std::min<float>(node->minpos[2], collisionTree[node->leftindex].get()->minpos[2])};
            node->maxpos = {std::max<float>(node->maxpos[0], collisionTree[node->leftindex].get()->maxpos[0]),
                            std::max<float>(node->maxpos[1], collisionTree[node->leftindex].get()->maxpos[1]),
                            std::max<float>(node->maxpos[2], collisionTree[node->leftindex].get()->maxpos[2])};
        }
        if (node->rightindex > 0 && node->rightindex < collisionTree.size())
        {
            node->minpos = {std::min<float>(node->minpos[0], collisionTree[node->rightindex].get()->minpos[0]),
                            std::min<float>(node->minpos[1], collisionTree[node->rightindex].get()->minpos[1]),
                            std::min<float>(node->minpos[2], collisionTree[node->rightindex].get()->minpos[2])};
            node->maxpos = {std::max<float>(node->maxpos[0], collisionTree[node->rightindex].get()->maxpos[0]),
                            std::max<float>(node->maxpos[1], collisionTree[node->rightindex].get()->maxpos[1]),
                            std::max<float>(node->maxpos[2], collisionTree[node->rightindex].get()->maxpos[2])};
        }
    }
}

}   // namespace cross::editor