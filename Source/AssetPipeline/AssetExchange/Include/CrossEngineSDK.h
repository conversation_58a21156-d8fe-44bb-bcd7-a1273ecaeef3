/**
 * @file CrossEngineSDK.h
 * @brief CE SDK interface file providing C-style API for engine functionality
 *
 * This header defines the public interface for interacting with the Cross Engine.
 * It provides wrapper classes around internal engine implementation, allowing external
 * code to access engine features through a well-defined API.
 *
 * @copyright Copyright (c) 2025 CE Team
 * @version 1.0
 */

#pragma once
#include <AssetExchangeDefinitions.h>
#include <cstdint>
#include <string>

/**
 * @namespace cegf
 * @brief Core namespace for the Cross Engine Game Framework
 *
 * Contains forward declarations for core engine classes that are
 * wrapped by the SDK interface classes.
 */
namespace cegf {

class GameEngine;
class GameWorld;
class GameObject;
class GameObjectComponent;
}   // namespace cegf

namespace cross {

class Resource;
class ResourceManager;

}

namespace cross::resource {

class MaterialParameterCollection;

}


/**
 * @namespace cesdk
 * @brief Cross Engine SDK namespace containing the public API
 *
 * This namespace encapsulates all SDK functionality, providing
 * a clean separation between the SDK interface and engine internals.
 */
namespace cesdk {

/**
 * @namespace cesdk::cross
 * @brief Utility namespace for cross-platform data types and structures
 */
namespace cross {

    struct Float2
    {
        float x, y;
    };

    struct Float3
    {
        float x, y, z;
    };

    struct Float4
    {
        float x, y, z, w;
    };

    /**
     * @struct TRSVector3Type
     * @brief 3D vector representation using double precision
     *
     * Used to represent positions and scales in 3D space
     */
    struct TRSVector3Type
    {
        double x, y, z; /**< X, Y, Z components of the vector */
    };


    /**
     * @struct TRSVector4Type
     * @brief 4D vector representation using double precision
     *
     */
    struct TRSVector4Type
    {
        double x, y, z, w;
    };

    /**
     * @struct TRSQuaternionType
     * @brief Quaternion representation using double precision
     *
     * Used to represent rotations in 3D space
     */
    struct TRSQuaternionType
    {
        double x, y, z, w; /**< X, Y, Z, W components of the quaternion */
    };


}   // namespace cross

/**
 * @namespace cesdk::cegf
 * @brief Contains wrapper classes for the engine's game framework
 *
 * Provides SDK-friendly interfaces to core engine functionality
 */
namespace cegf {

    class SDKGameWorld;
    class SDKGameWorldBlock;
    class SDKGameObject;
    class SDKGameObjectComponent;

#pragma region GameObjectComponent
    /**
     * @class SDKGameObjectComponent
     * @brief Wrapper class for engine GameObjectComponentWrap
     *
     * Provides an interface for manipulating game objects, including
     * component management and hierarchy operations.
     */
    class AssetExchange_API SDKGameObjectComponent {
    public:
        /**
         * @brief Private constructor, only GameWorldWrap can create GameObjectComponentWrap instances
         *
         * @param componentInstance Pointer to the internal engine GameObjectComponentWrap instance
         */
        SDKGameObjectComponent(::cegf::GameObjectComponent* componentInstance)
        : componentInstance( componentInstance ) {}
        ::cegf::GameObjectComponent* componentInstance; /**< Pointer to the wrapped engine SDKGameObjectComponent */
    };
#pragma endregion GameObjectComponent

#pragma region GameObject
        /**
     * @class SDKGameObject
     * @brief Wrapper class for engine GameObjectWrap
         *
     * Provides an interface for manipulating game objects, including
     * component management and hierarchy operations.
         */
    class AssetExchange_API SDKGameObject
    {
    public:

        /**
         * @brief Private constructor, only GameWorldWrap can create GameObjectWrap instances
         *
         * @param gameObjectInstance Pointer to the internal engine GameObjectWrap instance
         */
        SDKGameObject(::cegf::GameObject* gameObjectInstance)
            : gameObjectInstance{gameObjectInstance}
        {}

        /**
         * @brief Checks if the game object has a component of the specified type
         *
         * @param componentName The name of the component type to check for
         * @return true If the game object has the component
         * @return false If the game object does not have the component
         */
        bool HasComponent(const char* componentName) const;

        /**
         * @brief Gets a component by its type name
         *
         * @param componentName The name of the component type
         * @return SDKGameObjectComponent The component, or an invalid component if not found
         */
        SDKGameObjectComponent GetComponentByName(const char* componentName) const;

        /**
         * @brief Gets all components attached to this game object
         *
         * @param outComponents Array to populate with components
         * @param maxComponentCount Maximum number of components to return
         * @return size_t The actual number of components returned
         */
        size_t GetAllComponents(SDKGameObjectComponent* outComponents, size_t maxComponentCount) const;

        /**
         * @brief Gets the number of components attached to this game object
         *
         * @return size_t The number of components
         */
        size_t GetComponentCount() const;

        /**
         * @brief Adds a component of the specified type to this game object
         *
         * @param componentName The name of the component type to add
         * @return SDKGameObjectComponent The newly created component, or an invalid component if creation failed
         */
        SDKGameObjectComponent AddComponent(const char* componentName);

        /**
         * @brief Removes a component from this game object
         *
         * @param component The component to remove
         * @return true If the component was successfully removed
         * @return false If the component could not be removed
         */
        bool RemoveComponent(SDKGameObjectComponent component);

        /**
         * @brief Gets the root component of this game object
         *
         * @return SDKGameObjectComponent The root component, or an invalid component if none exists
         */
        SDKGameObjectComponent GetRootComponent() const;

        /**
         * @brief Sets the root component of this game object
         *
         * @param component The component to set as root
         */
        void SetRootComponent(SDKGameObjectComponent component);

        /**
         * @brief Gets the local translation of this game object
         *
         * @return cross::TRSVector3Type The local translation
         */
        cross::TRSVector3Type GetLocalTranslation() const;

        /**
         * @brief Sets the local translation of this game object
         *
         * @param translation The local translation to set
         */
        void SetLocalTranslation(const cross::TRSVector3Type& translation);

        /**
         * @brief Gets the local rotation of this game object
         *
         * @return cross::TRSQuaternionType The local rotation
         */
        cross::TRSQuaternionType GetLocalRotation() const;

        /**
         * @brief Sets the local rotation of this game object
         *
         * @param rotation The local rotation to set
         */
        void SetLocalRotation(const cross::TRSQuaternionType& rotation);

        /**
         * @brief Gets the local scale of this game object
         *
         * @return cross::TRSVector3Type The local scale
         */
        cross::TRSVector3Type GetLocalScale() const;

        /**
         * @brief Sets the local scale of this game object
         *
         * @param scale The local scale to set
         */
        void SetLocalScale(const cross::TRSVector3Type& scale);

        ::cegf::GameObject* gameObjectInstance; /**< Pointer to the wrapped engine SDKGameObject */
    };
#pragma endregion GameObject

    /**
     * @class SDKGameWorld
     * @brief Wrapper class for engine GameWorldWrap
     *
     * Provides an interface for managing a game world, including objects, blocks,
     * and world lifecycle operations.
     */
    class AssetExchange_API SDKGameWorld
    {
    public:
        /**
         * @brief Begins the destruction process for this world
         *
         * Initiates cleanup but does not immediately destroy the world
         */
        void BeginDestroy();

        /**
         * @brief Finalizes the destruction of this world
         *
         * Called after BeginDestroy to complete the cleanup process
         */
        void Destroyed();

        /**
         * @brief Called at the beginning of each frame
         *
         * @param deltaTime Time elapsed since the last frame in seconds
         */
        void OnBeginFrame(float deltaTime);

        /**
         * @brief Main update function for the world
         *
         * @param deltaTime Time elapsed since the last frame in seconds
         */
        void Tick(float deltaTime);

        /**
         * @brief Starts gameplay in this world
         *
         * Initializes all gameplay systems and notifies objects that the game has started
         */
        void StartGame();

        /**
         * @brief Ends gameplay in this world
         *
         * Stops all gameplay systems and notifies objects that the game has ended
         */
        void EndGame();

        /**
         * @brief Checks if gameplay has started in this world
         *
         * @return true If gameplay has started
         * @return false If gameplay has not started
         */
        bool HasStarted() const;

        /**
         * @brief Checks if the world is fully loaded
         *
         * @return true If the world is loaded and ready
         * @return false If the world is still loading
         */
        bool IsLoaded() const;

        /**
         * @brief Gets the runtime ID of this world
         *
         * @return uint32_t The unique runtime identifier
         */
        uint32_t GetRuntimeID() const;

        /**
         * @brief Gets the file path of this world
         *
         * @return const char* The file path of the world
         */
        const char* GetWorldPath();

        /**
         * @brief Gets the name of this world
         *
         * @return const char* The world name
         */
        const char* GetName();

        /**
         * @brief Gets a world block by its ID
         *
         * @param blockId The unique identifier of the block
         * @return cegf::GameWorldBlockWrap* Pointer to the block, or nullptr if not found
         */
        cegf::SDKGameWorldBlock* GetWorldBlock(uint64_t blockId) const;

        /**
         * @brief Finds a GameObjectWrap by name
         *
         * @param name The name of the GameObjectWrap to find
         * @return GameObjectWrap The found GameObjectWrap, or an invalid GameObjectWrap if not found
         */
        SDKGameObject FindGameObject(const char* name) const;

        /**
         * @brief Gets the count of GameObjects created at runtime
         *
         * @return size_t The number of runtime-created objects
         */
        size_t GetRuntimeCreatedGameObjectCount() const;

        SDKGameObject CreateRootGameObject() const;

        /**
         * @brief Gets the root GameObjectWrap of the world
         *
         * @return GameObjectWrap The root GameObjectWrap
         */
        SDKGameObject GetRootGameObject() const;

        /**
         * @brief Creates a new GameObjectWrap in the world
         *
         * @param name The name for the new GameObjectWrap
         * @param parent The parent GameObjectWrap in the hierarchy
         * @param localLocation The initial position of the GameObjectWrap
         * @param localRotation The initial rotation of the GameObjectWrap
         * @param localScale The initial scale of the GameObjectWrap
         * @return GameObjectWrap The newly created GameObjectWrap
         */
        SDKGameObject CreateGameObject(const char* name, SDKGameObject parent, const cross::TRSVector3Type& localLocation, const cross::TRSQuaternionType& localRotation, const cross::TRSVector3Type& localScale);

        /**
         * @brief Destroys a GameObjectWrap
         *
         * @param gameObj The GameObjectWrap to destroy
         * @return true If the GameObjectWrap was successfully destroyed
         * @return false If the GameObjectWrap could not be destroyed
         */
        bool DestroyGameObject(SDKGameObject gameObj);

        /**
         * @brief Gets the number of controllers in the world
         *
         * @return uint32_t The number of controllers
         */
        uint32_t GetNumControllers() const;

        /**
         * @brief Gets the number of player controllers in the world
         *
         * @return uint32_t The number of player controllers
         */
        uint32_t GetNumPlayerControllers() const;

        /**
         * @brief Checks if the world has end-of-frame drawing
         *
         * @return true If end-of-frame drawing is enabled
         * @return false If end-of-frame drawing is disabled
         */
        bool HasEndOfFrameDraw() const;

        /**
         * @brief Performs end-of-frame drawing
         *
         * Called after all other drawing is complete to perform final rendering operations
         */
        void EndOfFrameDraw() const;

        /**
         * @brief Gets the main camera GameObjectWrap
         *
         * @return GameObjectWrap The main camera, or an invalid GameObjectWrap if none exists
         */
        SDKGameObject GetMainCamera() const;

        bool Save(const char* savePath) const;

        /**
         * @brief Private constructor, only GameEngineWrap can create GameWorldWrap instances
         *
         * @param gameWorldInstance Pointer to the internal engine GameWorldWrap instance
         */
        SDKGameWorld(::cegf::GameWorld* gameWorldInstance)
            : gameWorldInstance{gameWorldInstance}
        {}
        ::cegf::GameWorld* gameWorldInstance; /**< Pointer to the wrapped engine SDKGameWorld */
    };

    /**
     * @class GameEngineWrap
     * @brief Wrapper class for the main engine instance
     *
     * Provides an interface for interacting with the game engine, including
     * initialization, shutdown, and world management.
     */
    class AssetExchange_API GameEngineWrap
    {
    public:
        /**
         * @brief Initializes the game engine
         *
         * Sets up all engine systems and prepares for gameplay
         */
        void Init();
        /**
         * @brief Shuts down the game engine
         *
         * Cleans up all engine resources and terminates engine systems
         */
        void ShutDown();

        /**
         * @brief Called at the beginning of each frame
         *
         * @param deltaTime Time elapsed since the last frame in seconds
         */
        void OnBeginFrame(float deltaTime);

        /**
         * @brief Called before the main tick
         *
         * @param deltaTime Time elapsed since the last frame in seconds
         */
        void PreTick(float deltaTime);

        /**
         * @brief Main update function for the engine
         *
         * @param deltaTime Time elapsed since the last frame in seconds
         */
        void Tick(float deltaTime);

        /**
         * @brief Called after the main tick
         *
         * @param deltaTime Time elapsed since the last frame in seconds
         */
        void PostTick(float deltaTime);

        /**
         * @brief Notifies the engine that gameplay should start
         *
         * Initiates gameplay in the current world
         */
        void NotifyStartGame();

        SDKGameWorld LoadGameWorld(const char* worldName);

        SDKGameWorld CreateGameWorld(const char* worldName);

        /**
         * @brief Gets a world by its ID
         *
         * @param worldID The runtime ID of the world
         * @return GameWorldWrap The world with the specified ID, or an invalid world if not found
         */
        SDKGameWorld GetGameWorld(uint32_t worldID) const;

        /**
         * @brief Gets the currently active game world
         *
         * @return GameWorldWrap The current game world
         */
        SDKGameWorld GetCurrentGameWorld() const;

        /**
         * @brief Sets the currently active game world
         *
         * @param world The world to set as current
         */
        void SetCurrentGameWorld(SDKGameWorld world);

        /**
         * @brief Called when a new game world is created
         *
         * @param worldRuntimeID The runtime ID of the newly created world
         */
        void OnCreateGameWorld(uint32_t worldRuntimeID);

        /**
         * @brief Called when a game world is destroyed
         *
         * @param worldRuntimeID The runtime ID of the world being destroyed
         */
        void OnDestroyGameWorld(uint32_t worldRuntimeID);

        /**
         * @brief Private constructor, only the GetGameEngine function can create instances
         *
         * @param gameEngineInstance Pointer to the internal engine instance
         */
        GameEngineWrap(::cegf::GameEngine* gameEngineInstance)
            : gameEngineInstance{gameEngineInstance}
        {}
        ::cegf::GameEngine* gameEngineInstance;              /**< Pointer to the wrapped engine instance */
    };

    /**
     * @brief Gets the singleton instance of the game engine
     *
     * @return GameEngineWrap The game engine instance
     */
    GameEngineWrap AssetExchange_API GetGameEngine();

}   // namespace cegf

#pragma region Resource

namespace resource {

struct ResourceHolder;

class AssetExchange_API SDKResource
{
public:
    SDKResource() = default;
    SDKResource(std::string_view path, uint64_t classID);
    virtual ~SDKResource();
    SDKResource(const SDKResource&) = delete;
    SDKResource& operator=(const SDKResource&) = delete;
    SDKResource(SDKResource&& other) noexcept;
    SDKResource& operator=(SDKResource&& other) noexcept;

    //ResourceHolder& operator*() const { return *mResHolder; }
    //ResourceHolder* operator->() const { return mResHolder.get(); }

    //ResourceHolder* get() const { return mResHolder.get(); }
    //explicit operator bool() const { return mResHolder != nullptr; }

    void Serialize() const;
    const char* GetGuid() const;

    std::unique_ptr<ResourceHolder> mResHolder{};
    std::string mPath{}; /**< Path to the resource file */

protected:

    friend class SDKResourceManager; /**< Friend class for resource management */
};

class AssetExchange_API SDKResourceManager
{
public:
    SDKResourceManager(const SDKResourceManager&) = delete;
    SDKResourceManager& operator=(const SDKResourceManager&) = delete;

    static SDKResourceManager& Instance();
    std::unique_ptr<ResourceHolder> CreateResourceHolder(uint64_t classID, char const* path);
    std::unique_ptr<SDKResource> ResourceGet(char const* path);
    
    ::cross::ResourceManager* resourceManagerInstance; /**< Pointer to the internal resource manager instance */

private:
    SDKResourceManager() = default;
};

class AssetExchange_API SDKMaterialParameterCollection : public SDKResource
{
public:
    SDKMaterialParameterCollection(std::string_view path) : SDKResource(path, GetClassID()) { }

    static uint64_t GetClassID();
    void ClearParameters();
    void AddScalerParameter(const char* name, float value);
    void AddVectorParameter(const char* name, const cross::TRSVector4Type& value);
};

class AssetExchange_API SDKAnimCompositeResource : public SDKResource
{
public:
    SDKAnimCompositeResource(std::string_view path) : SDKResource(path, GetClassID()) { }

    static uint64_t GetClassID();
    void Init(const char* json);
};

class AssetExchange_API SDKAnimatrixResource : public SDKResource
{
public:
    SDKAnimatrixResource(std::string_view path) : SDKResource(path, GetClassID()) { }

    static uint64_t GetClassID();
    void Init(const char* json);
};

class AssetExchange_API SDKTerrainResource : public SDKResource
{
public:
    SDKTerrainResource(std::string_view path);

    static uint64_t GetClassID();

    void SetTerrainSize(int32_t gridSizeX, int32_t gridSizeY, int32_t blockSize, int32_t tileSize, float texelDensity);
    void SetTerrainPath(const char* rootDataPath, const char* heightmapPrefix, const char* weightTexturePrefix, const char* materialPath, int32_t numLayers);
};

class AssetExchange_API SDKInstancedStaticMeshResource : public SDKResource
{
public:
    SDKInstancedStaticMeshResource(std::string_view path);

    static uint64_t GetClassID();

    void SetTransforms(const uint8_t* translation, const uint8_t* rotation, const uint8_t* scale, int32_t count);

    void SetClusterNodes(const uint8_t* boundsMin, const uint8_t* boundsMax, const uint8_t* firstChild, const uint8_t* lastChild,
        const uint8_t* firstInstance, const uint8_t* lastInstance, int32_t count);
};

class AssetExchange_API SDKAnimatorResource : public SDKResource
{
public:
    SDKAnimatorResource(std::string_view path)
        : SDKResource(path, GetClassID())
    {}

    static uint64_t GetClassID();
    void SetDefault();
    void SetStoryBoard(const char* json);
};

class AssetExchange_API SDKBlendSpaceResource : public SDKResource
{
public:
    SDKBlendSpaceResource(std::string_view path)
        : SDKResource(path, GetClassID())
    {}

    static uint64_t GetClassID();
    void Init(const char* json);
};

class AssetExchange_API SDKFxResource : public SDKResource
{
public:
    SDKFxResource(std::string_view path);

    static uint64_t GetClassID();
    void Init(const char* json);
    void AddReferenceResource(std::string resPath);
};

class AssetExchange_API SDKMaterialFunctionResource : public SDKResource
{
public:
    SDKMaterialFunctionResource(std::string_view path);

    static uint64_t GetClassID();
    void Init(const char* json);
    void AddReferenceResource(std::string resPath);
};
    
class AssetExchange_API SDKMaterialResource : public SDKResource
{
public:
    SDKMaterialResource(std::string_view path);

    static uint64_t GetClassID();
    void Init(const char* json);
    void AddReferenceResource(std::string resPath);
};

AssetExchange_API SDKResourceManager& GetResourceManager();

}   // namespace resource
#pragma endregion Resource

using CEWorldLoadedCallBack = void (*)(void* customData, cegf::SDKGameWorld world);

using CrossEngineLogDelegate = void (*)(int, char const*);

void AssetExchange_API CreateGameEngine(const char* sdkAssetPath, const char* engineResourcePath, CrossEngineLogDelegate logDelegate);
void AssetExchange_API DeleteGameEngine();
void AssetExchange_API ExportGameWorld(const char* worldSavePath, cesdk::CEWorldLoadedCallBack callback, void* customData);

void ExportGameWorld(const char* worldSavePath, std::invocable<cegf::SDKGameWorld> auto&& callback)
{
    auto Callback = [](void* customData, cegf::SDKGameWorld world)
    {
        reinterpret_cast<std::decay_t<decltype(callback)>*>(customData)->operator()(world);
    };

    ExportGameWorld(worldSavePath, Callback, &callback);
}
using CEExportResourceCallBack = void (*)(void* customData);

void AssetExchange_API ExportResource(cesdk::CEExportResourceCallBack callback, void* customData);

void ExportResource(std::invocable auto&& callback)
{
    auto Callback = [](void* customData) { reinterpret_cast<std::decay_t<decltype(callback)>*>(customData)->operator()(); };
    ExportResource(Callback, &callback);
}

}   // namespace cesdk
