#include <fstream>
#include <algorithm>
#include <bitset>
#include "MeshAssemble.h"
#include "MaterialAssemble.h"
#include "PCH/CrossBasePCHPublic.h"
#include "CrossBase/Math/CrossMath.h"
#include "CrossBase/Serialization/SerializeNode.h"
#include "CrossBase/Serialization/ResourceMetaHeader.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/MeshDefines.h"
#include "CECommon/Common/RenderStateDefines.h"
#include "CECommon/Resource/Serialize/FlatbufferSerialize.h"
#include "CrossPhysics/PhysicsEngine/PhysicsEngine.h"
#include "CrossPhysics/PhysicsEngine/PhysicsCooker.h"
#include "CrossSchema/BasicStruct_generated.h"
#include "CrossSchema/ResourceAsset_generated.h"
#include "CrossSchema/ImportMeshAssetData_generated.h"
#include "AssetPipeline/MeshPickBuilder/MeshPickBuilder.h"

namespace CEAssetExchange {

extern std::string TryGetRefference(const std::string& normPath);

template<class T1, class T2, class TypeConvFunc>
void TypeConverter(const std::vector<T1>& inNormalVec, std::vector<T2>& out, TypeConvFunc func)
{
    out.resize(inNormalVec.size());
    constexpr size_t element_dim = T2::Dim();

    for (int i = 0; i < inNormalVec.size(); i++)
    {
        const float* ptr = reinterpret_cast<const float*>(&inNormalVec[i]);
        for (int j = 0; j < element_dim; j++)
        {
            func(ptr[j], out[i].data()[j]);
        }
    }
}

std::uint32_t VertexBufferLayoutStride(VertexBufferLayout layout, const std::uint32_t UVCount)
{
    // if (layout == VertexBufferLayout::PF3_NF3_TF3_BiTF3_CF4_UVF2)
    return (16 + UVCount * 2) * sizeof(float);
}

std::bitset<128> VertexBufferLayoutChannelMask(VertexBufferLayout layout, const std::uint32_t UVCount)
{
    std::bitset<128> mask{0};

    // if (layout == VertexBufferLayout::PF3_NF3_TF3_BiTF3_CF4_UVF2)
    mask.set(cross::GetVertexChannelMaskBit(cross::VertexChannel::Position0), true);
    mask.set(cross::GetVertexChannelMaskBit(cross::VertexChannel::Normal0), true);
    mask.set(cross::GetVertexChannelMaskBit(cross::VertexChannel::Tangent0), true);
    mask.set(cross::GetVertexChannelMaskBit(cross::VertexChannel::BiNormal0), true);
    mask.set(cross::GetVertexChannelMaskBit(cross::VertexChannel::Color0), true);

    for (std::uint32_t i = 0; i <= UVCount; i++)
    {
        mask.set(cross::GetVertexChannelMaskBit(cross::VertexChannel::TexCoord0 + i), true);
    }

    return mask;
}

MeshAssemble::MeshAssemble(std::filesystem::path& relative, std::filesystem::path& staging, std::string& guid)
    : Object(relative, staging, guid)
{
    mMesh.fname = mName;
    mMesh.faabb = std::make_unique<CrossSchema::MeshBoundT>();
    mMesh.faabb->fmax = std::make_unique<CrossSchema::float3>();
    mMesh.faabb->fmin = std::make_unique<CrossSchema::float3>();
    mMesh.findexstream = std::make_unique<CrossSchema::IndexStreamAssetDataT>();
    mMesh.frefskeleton = std::make_unique<CrossSchema::ImportRefSkeletonT>();
    mMesh.fphysicscollision = std::make_unique<CrossSchema::PhysicsCollisionT>();
    mMesh.findexstream->fis16bitindex = true;
}

void MeshAssemble::SetRawHeaderBuffers(const std::uint8_t* buffer, const std::uintmax_t size)
{
    assert(!mEndAssembled);
}

void MeshAssemble::SetRawFlatBuffers(const std::uint8_t* buffer, const std::uintmax_t size)
{
    assert(!mEndAssembled);
    flatbuffers::Verifier verifier(buffer, size);
    if (CrossSchema::VerifyResourceAssetBuffer(verifier))
    {
        auto* res = CrossSchema::GetResourceAsset(buffer);
        res->resource_as_ImportMeshAssetData()->UnPackTo(&mMesh);

        auto* normal = MeshAssemble::GetVertexChannelData(VertexChannel::Normal0);
        if (normal)
        {
            mSettings.mUseFullPrecisionTangent = normal->fstride == sizeof(float) * 3;
        }

        auto* uv = MeshAssemble::GetVertexChannelData(VertexChannel::TexCoord0);
        if (uv)
        {
            mSettings.mUseFullPrecisionUV = uv->fstride == sizeof(float) * 2;
        }
    }
    else
    {
        LOG_ERROR("[AssetExchange]: Error, buffer was not a valid mesh asset (flatbuffers).");
    }
}

std::uint32_t MeshAssemble::GetMeshPartCount()
{
    return static_cast<std::uint32_t>(mMesh.fmeshpartinfo.size());
}

std::tuple<std::uint32_t, std::uint32_t> MeshAssemble::GetMeshPartRange(std::uint32_t beginLOD, std::uint32_t endLOD)
{
    const std::uint32_t LODCount = GetLODCount();
    Assert(beginLOD < endLOD && beginLOD < LODCount && LODCount >= endLOD);

    const std::uint32_t begLODfirst = mMesh.fmeshpartlodstartindex[beginLOD];
    const std::uint32_t endLODfirst = endLOD == LODCount ? static_cast<std::uint32_t>(mMesh.fmeshpartinfo.size()) : mMesh.fmeshpartlodstartindex[endLOD];
    return std::make_tuple(begLODfirst, endLODfirst);
}

void MeshAssemble::GetMeshPartRange(std::uint32_t beginLOD, std::uint32_t endLOD, std::uint32_t* beginMeshPart, std::uint32_t* endMeshPart)
{
    auto [begin, end] = GetMeshPartRange(beginLOD, endLOD);
    *beginMeshPart = begin, *endMeshPart = end;
}

std::uint32_t MeshAssemble::GetLODCount()
{
    return static_cast<std::uint32_t>(mMesh.fmeshpartlodstartindex.size());
}

std::tuple<std::uint32_t, std::uint32_t> MeshAssemble::GetLODMeshPartRange(std::uint32_t LOD)
{
    return GetMeshPartRange(LOD, LOD + 1);
}

void MeshAssemble::GetLODMeshPartRange(std::uint32_t LOD, std::uint32_t* beginMeshPart, std::uint32_t* endMeshPart)
{
    auto [begin, end] = GetLODMeshPartRange(LOD);
    *beginMeshPart = begin, *endMeshPart = end;
}

std::uint32_t MeshAssemble::GetLODVertexCount(std::uint32_t LOD)
{
    AssertBreak(LOD >= 0 && LOD < mMesh.fmeshpartlodstartindex.size());
    auto [begin, end] = GetMeshPartRange(LOD, LOD + 1);
    std::uint32_t total = 0;

    for (std::uint32_t i = begin; i < end; i++)
    {
        auto* part = mMesh.fmeshpartinfo[i].get();
        total += part->fvertexcount;
    }

    return total;
}

std::uint32_t MeshAssemble::GetLODIndexCount(std::uint32_t LOD)
{
    AssertBreak(LOD >= 0 && LOD < mMesh.fmeshpartlodstartindex.size());
    auto [begin, end] = GetMeshPartRange(LOD, LOD + 1);
    std::uint32_t total = 0;

    for (std::uint32_t i = begin; i < end; i++)
    {
        auto* part = mMesh.fmeshpartinfo[i].get();
        total += part->findexcount;
    }

    return total;
}

std::uint32_t MeshAssemble::GetMeshPartVertexCount(std::uint32_t MeshPart)
{
    AssertBreak(MeshPart >= 0 && MeshPart < mMesh.fmeshpartinfo.size());
    return mMesh.fmeshpartinfo[MeshPart]->fvertexcount;
}

std::uint32_t MeshAssemble::GetMeshPartIndexCount(std::uint32_t MeshPart)
{
    AssertBreak(MeshPart >= 0 && MeshPart < mMesh.fmeshpartinfo.size());
    return mMesh.fmeshpartinfo[MeshPart]->findexcount;
}

void MeshAssemble::RemoveLODRange(std::uint32_t beginLOD, std::uint32_t endLOD)
{
    auto [begin, end] = GetMeshPartRange(beginLOD, endLOD);
    std::uint32_t vertexStart = 0, indexStart = 0, totalVertex = 0, totalIndex = 0, totalPrimitve = 0;

    for (std::uint32_t i = begin; i < end; i++)
    {
        auto* part = mMesh.fmeshpartinfo[i].get();

        if (i == begin)
        {
            indexStart = part->findexstart;
            vertexStart = part->fvertexstart;
        }
        else if (indexStart > part->findexstart)
        {
            indexStart = part->findexstart;
            vertexStart = part->fvertexstart;
        }

        totalIndex += part->findexcount;
        totalVertex += part->fvertexcount;
        totalPrimitve += part->fprimitivecount;
    }

    for (auto& vbChannel : mMesh.fvertexchanneldata)
    {
        auto stride = vbChannel->fstride;
        auto vbBeginIter = vbChannel->fdata.begin();
        const auto begVbChannel = vertexStart * stride;
        const auto endVbChannel = (vertexStart + totalVertex) * stride;
        // Assert(begVbChannel < endVbChannel && endVbChannel <= vbChannel->fdata.size());
        if (begVbChannel >= endVbChannel || endVbChannel > vbChannel->fdata.size())
            break;
        vbChannel->fdata.erase(vbBeginIter + begVbChannel, vbBeginIter + endVbChannel);
        vbChannel->fdata.shrink_to_fit();
    }

    mMesh.fvertexcount -= totalVertex;
    mMesh.fprimitivecount -= totalPrimitve;

    const auto ib = mMesh.findexstream.get();
    const std::uint32_t ibStride = ib->fis16bitindex ? 2 : 4;
    ib->fdata.erase(ib->fdata.begin() + indexStart * ibStride, ib->fdata.begin() + (indexStart + totalIndex) * ibStride);
    ib->fdata.shrink_to_fit();
    ib->fcount -= totalIndex;

    mMesh.fmeshpartnames.erase(mMesh.fmeshpartnames.begin() + begin, mMesh.fmeshpartnames.begin() + end);
    mMesh.fmeshpartinfo.erase(mMesh.fmeshpartinfo.begin() + begin, mMesh.fmeshpartinfo.begin() + end);

    for (std::uint32_t i = begin; i < mMesh.fmeshpartinfo.size(); ++i)
    {
        mMesh.fmeshpartinfo[i]->findexstart -= totalIndex;
        mMesh.fmeshpartinfo[i]->fvertexstart -= totalVertex;
        mMesh.fmeshpartinfo[i]->fnameindex = static_cast<std::int16_t>(i);
        AssertBreak(i < mMesh.fmeshpartnames.size());
    }

    mMesh.fmeshpartlodstartindex.erase(mMesh.fmeshpartlodstartindex.begin() + beginLOD, mMesh.fmeshpartlodstartindex.begin() + endLOD);

    for (std::uint32_t i = beginLOD; i < mMesh.fmeshpartlodstartindex.size(); ++i)
    {
        mMesh.fmeshpartlodstartindex[i] -= (end - begin);
    }

    DeleteUnusedBuffer();
}

void MeshAssemble::RemoveLOD(std::uint32_t LOD)
{
    RemoveLODRange(LOD, LOD + 1);
}

CrossSchema::VertexChannelAssetDataT* MeshAssemble::GetVertexChannelData(const VertexChannel channel)
{
    CrossSchema::VertexChannelAssetDataT* channelDataP = nullptr;
    auto& vecChannel = mMesh.fvertexchanneldata;
    auto result = vecChannel.begin();
    for (; result != vecChannel.end(); result++)
    {
        if (result->get()->fvertexchannel == static_cast<std::uint32_t>(channel))
            break;
    }

    if (result != vecChannel.end())
    {
        channelDataP = result->get();
    }

    return channelDataP;
}

void MeshAssemble::UpdateMeshPart(std::uint32_t Index, const MeshFlatBuffer* mb)
{
    const auto* vb = mb->mFlatVertexBuffer;
    const auto UVCount = (vb->mUVCount < 0 || vb->mUVCount > 16) ? 0 : vb->mUVCount;

    const std::uint32_t stride = VertexBufferLayoutStride(vb->mLayout, UVCount);
    assert(vb->mDataSize % stride == 0);

    const auto count = vb->mDataSize / stride;
    auto channelMask = VertexBufferLayoutChannelMask(vb->mLayout, UVCount);

    if (vb->mIgnoreChannels != nullptr)
    {
        for (std::uintmax_t i = 0; i < vb->mIgnoreChannelsNum; i++)
            channelMask[GetVertexChannelMaskBit(static_cast<cross::VertexChannel>(*(vb->mIgnoreChannels + i)))] = false;
    }

    std::vector<float> position;
    std::vector<float> normal;
    std::vector<float> tangent;
    std::vector<float> binormal;
    std::vector<std::uint32_t> color;
    std::vector<std::vector<float>> uvs;

    if (channelMask[GetVertexChannelMaskBit(cross::VertexChannel::Position0)])
        position.resize(3 * count);

    if (channelMask[GetVertexChannelMaskBit(cross::VertexChannel::Normal0)])
        normal.resize(3 * count);

    if (channelMask[GetVertexChannelMaskBit(cross::VertexChannel::Tangent0)])
        tangent.resize(4 * count);

    if (channelMask[GetVertexChannelMaskBit(cross::VertexChannel::BiNormal0)])
        binormal.resize(4 * count);

    if (channelMask[GetVertexChannelMaskBit(cross::VertexChannel::Color0)])
        color.resize(count);

    uvs.resize(UVCount);

    for (int i = 0; i < uvs.size(); i++)
    {
        if (channelMask[GetVertexChannelMaskBit(cross::VertexChannel::TexCoord0 + i)])
            uvs[i].resize(2 * count);
    }

    for (std::size_t i = 0; i < count; i++)
    {
        const float* headF = reinterpret_cast<const float*>(vb->mData + i * stride);

        position.size() && memcpy(position.data() + i * 3, headF, sizeof(float) * 3);
        normal.size() && memcpy(normal.data() + i * 3, headF + 3, sizeof(float) * 3);

        if (tangent.size())
        {
            memcpy(tangent.data() + i * 4, headF + 6, sizeof(float) * 3);
            tangent[i * 4 + 3] = 0;
        }

        if (binormal.size())
        {
            memcpy(binormal.data() + i * 4, headF + 9, sizeof(float) * 3);
            binormal[i * 4 + 3] = 0;
        }

        if (color.size())
        {
            constexpr auto c_size = sizeof(std::uint32_t);
            cross::ColorRGBA32 coloru32{cross::ColorRGBAf{headF[12], headF[13], headF[14], headF[15]}};
            memcpy(color.data() + i, &coloru32, c_size);
        }

        int offset = 16;
        for (int idx = 0; idx < uvs.size(); idx++)
        {
            uvs[idx].size() && memcpy(uvs[idx].data() + i * 2, headF + offset, sizeof(float) * 2);
            offset += 2;
        }
    }

    MeshSplitBuffer msb{};
    msb.mIndex = mb->mIndex;
    msb.mIndexNum = mb->mIndexNum;
    msb.mVertexNum = count;

    std::vector<VertexChannelBuffer> channelBuffers;

    auto AddVertexChannelBuffer = [&](VertexChannel channel, const void* buffer, std::uintmax_t size, std::uint32_t stride) {
        if (size == 0)
            return;
        VertexChannelBuffer vcb{};
        vcb.mChannel = channel;
        vcb.mData = reinterpret_cast<const char*>(buffer);
        vcb.mDataSize = size;
        vcb.mStride = stride;
        channelBuffers.emplace_back(std::move(vcb));
    };

    AddVertexChannelBuffer(VertexChannel::Position0, position.data(), position.size() * sizeof(position[0]), sizeof(position[0]) * 3);
    AddVertexChannelBuffer(VertexChannel::Color0, color.data(), color.size() * sizeof(color[0]), sizeof(color[0]));

    std::vector<cross::UShort3> Normal_US;
    std::vector<cross::UShort4> Tangent_US;
    std::vector<cross::UShort4> BiNormal_US;

    std::vector<cross::UChar3> Normal_UC;
    std::vector<cross::UChar4> Tangent_UC;
    std::vector<cross::UChar4> BiNormal_UC;

    std::vector<cross::Float3>* inDataNormal = reinterpret_cast<std::vector<cross::Float3>*>(&normal);
    std::vector<cross::Float4>* inDataTangent = reinterpret_cast<std::vector<cross::Float4>*>(&tangent);
    std::vector<cross::Float4>* inDataBiTangent = reinterpret_cast<std::vector<cross::Float4>*>(&binormal);

    if (mSettings.mUseFullPrecisionTangent)
    {
        if (inDataNormal->size())
        {
            TypeConverter(*inDataNormal, Normal_US, [](const float& in, auto& out) { cross::FloatToSNORM(in, out); });
            AddVertexChannelBuffer(VertexChannel::Normal0, Normal_US.data(), Normal_US.size() * sizeof(Normal_US[0]), sizeof(Normal_US[0]));
        }

        if (inDataTangent->size())
        {
            TypeConverter(*inDataTangent, Tangent_US, [](const float& in, auto& out) { cross::FloatToSNORM(in, out); });
            AddVertexChannelBuffer(VertexChannel::Tangent0, Tangent_US.data(), Tangent_US.size() * sizeof(Tangent_US[0]), sizeof(Tangent_US[0]));
        }

        if (inDataBiTangent->size())
        {
            TypeConverter(*inDataBiTangent, BiNormal_US, [](const float& in, auto& out) { cross::FloatToSNORM(in, out); });
            AddVertexChannelBuffer(VertexChannel::BiNormal0, BiNormal_US.data(), BiNormal_US.size() * sizeof(BiNormal_US[0]), sizeof(BiNormal_US[0]));
        }
    }
    else
    {
        if (inDataNormal->size())
        {
            TypeConverter(*inDataNormal, Normal_UC, [](const float& in, auto& out) { cross::FloatToSNORM(in, out); });
            AddVertexChannelBuffer(VertexChannel::Normal0, Normal_UC.data(), Normal_UC.size() * sizeof(Normal_UC[0]), sizeof(Normal_UC[0]));
        }

        if (inDataTangent->size())
        {
            TypeConverter(*inDataTangent, Tangent_UC, [](const float& in, auto& out) { cross::FloatToSNORM(in, out); });
            AddVertexChannelBuffer(VertexChannel::Tangent0, Tangent_UC.data(), Tangent_UC.size() * sizeof(Tangent_UC[0]), sizeof(Tangent_UC[0]));
        }

        if (inDataBiTangent->size())
        {
            TypeConverter(*inDataBiTangent, BiNormal_UC, [](const float& in, auto& out) { cross::FloatToSNORM(in, out); });
            AddVertexChannelBuffer(VertexChannel::BiNormal0, BiNormal_UC.data(), BiNormal_UC.size() * sizeof(BiNormal_UC[0]), sizeof(BiNormal_UC[0]));
        }
    }

    std::vector<std::vector<cross::UShort2>> uvs_half;
    uvs_half.resize(uvs.size());

    for (int i = 0; i < uvs.size(); i++)
    {
        std::vector<cross::Float2>* inData = reinterpret_cast<std::vector<cross::Float2>*>(&uvs[i]);

        if (inData->size() == 0)
            continue;

        if (mSettings.mUseFullPrecisionUV)
        {
            AddVertexChannelBuffer(static_cast<VertexChannel>(cross::VertexChannel::TexCoord0 + i), inData->data(), inData->size() * sizeof((*inData)[0]), sizeof((*inData)[0]));
        }
        else
        {
            uvs_half[i].resize(inData->size());
            cross::FloatToHalfConverter converter;
            TypeConverter(*inData, uvs_half[i], [&converter](const float& in, auto& out) { converter.Convert(in, out); });

            AddVertexChannelBuffer(static_cast<VertexChannel>(cross::VertexChannel::TexCoord0 + i), uvs_half[i].data(), uvs_half[i].size() * sizeof(uvs_half[0][0]), sizeof(uvs_half[0][0]));
        }
    }

    msb.mChannelBuffer = channelBuffers.data();
    msb.mChannelBufferNum = channelBuffers.size();
    UpdateMeshPart(Index, &msb);
}

void MeshAssemble::UpdateMeshPart(std::uint32_t Index, const MeshSplitBuffer* mb)
{
    if (Index < 0 || Index >= GetMeshPartCount())
        return;

    auto meshpart = mMesh.fmeshpartinfo[Index].get();

    if (meshpart->fvertexcount > mb->mVertexNum)
    {
        const std::uint32_t eraseCount = meshpart->fvertexcount - static_cast<std::uint32_t>(mb->mVertexNum);

        for (auto& vbChannel : mMesh.fvertexchanneldata)
        {
            const auto stride = vbChannel->fstride;
            auto vbBeginIter = vbChannel->fdata.begin();
            const auto begVbChannel = (meshpart->fvertexstart + mb->mVertexNum) * stride;
            const auto endVbChannel = (meshpart->fvertexstart + meshpart->fvertexcount) * stride;
            vbChannel->fdata.erase(vbBeginIter + begVbChannel, vbBeginIter + endVbChannel);
            vbChannel->fdata.shrink_to_fit();
        }

        for (std::uint32_t i = Index + 1; i < GetMeshPartCount(); i++)
        {
            auto meshpart2 = mMesh.fmeshpartinfo[i].get();
            meshpart2->fvertexstart -= eraseCount;
        }

        meshpart->fvertexcount -= eraseCount;
        mMesh.fvertexcount -= eraseCount;
    }
    else if (meshpart->fvertexcount < mb->mVertexNum)
    {
        const std::uint32_t addCount = static_cast<std::uint32_t>(mb->mVertexNum) - meshpart->fvertexcount;

        for (auto& vbChannel : mMesh.fvertexchanneldata)
        {
            const auto stride = vbChannel->fstride;
            auto bufferSize = vbChannel->fdata.size();
            vbChannel->fdata.resize(vbChannel->fdata.size() + addCount * stride);

            auto vbBeginIter = vbChannel->fdata.begin();
            const auto begVbChannel = (meshpart->fvertexstart + meshpart->fvertexcount) * stride;
            const auto newBegVbChannel = (meshpart->fvertexstart + mb->mVertexNum) * stride;

            const auto moveSize = bufferSize - begVbChannel;
            if (moveSize > 0)
                std::memmove(vbChannel->fdata.data() + newBegVbChannel, vbChannel->fdata.data() + begVbChannel, moveSize);
        }

        for (std::uint32_t i = Index + 1; i < GetMeshPartCount(); i++)
        {
            auto meshpart2 = mMesh.fmeshpartinfo[i].get();
            meshpart2->fvertexstart += addCount;
        }

        meshpart->fvertexcount += addCount;
        mMesh.fvertexcount += addCount;
    }

    // check ib: 16 => 32
    {
        using T = std::uint32_t;
        const auto bufferSize = mb->mIndexNum / (sizeof(T) / sizeof(decltype(mb->mIndex[0])));
        std::vector<T> inData{};
        inData.resize(bufferSize);
        memcpy(inData.data(), reinterpret_cast<const T*>(mb->mIndex), bufferSize * sizeof(T));
        CheckIndexStream(inData);
    }

    if (meshpart->findexcount > mb->mIndexNum)
    {
        const auto stride = mMesh.findexstream->fis16bitindex ? 2 : 4;
        const std::uint32_t eraseCount = meshpart->findexcount - static_cast<std::uint32_t>(mb->mIndexNum);
        auto ibBeginIter = mMesh.findexstream->fdata.begin();
        const auto begVbChannel = (meshpart->findexstart + static_cast<std::uint32_t>(mb->mIndexNum)) * stride;
        const auto endVbChannel = (meshpart->findexstart + meshpart->findexcount) * stride;
        mMesh.findexstream->fdata.erase(ibBeginIter + begVbChannel, ibBeginIter + endVbChannel);
        mMesh.findexstream->fdata.shrink_to_fit();

        for (std::uint32_t i = Index + 1; i < GetMeshPartCount(); i++)
        {
            auto meshpart2 = mMesh.fmeshpartinfo[i].get();
            meshpart2->findexstart -= eraseCount;
        }

        meshpart->findexcount -= eraseCount;
        mMesh.findexstream->fcount -= eraseCount;
    }
    else if (meshpart->findexcount < mb->mIndexNum)
    {
        const auto stride = mMesh.findexstream->fis16bitindex ? 2 : 4;
        const std::uint32_t addCount = static_cast<std::uint32_t>(mb->mIndexNum) - meshpart->findexcount;
        auto bufferSize = mMesh.findexstream->fdata.size();
        mMesh.findexstream->fdata.resize(mMesh.findexstream->fdata.size() + addCount * stride);

        auto vbBeginIter = mMesh.findexstream->fdata.begin();
        const auto begVbChannel = (meshpart->findexstart + meshpart->findexcount) * stride;
        const auto newBegVbChannel = (meshpart->findexstart + mb->mIndexNum) * stride;

        const auto moveSize = bufferSize - begVbChannel;
        if (moveSize > 0)
            std::memmove(mMesh.findexstream->fdata.data() + newBegVbChannel, mMesh.findexstream->fdata.data() + begVbChannel, moveSize);
        

        for (std::uint32_t i = Index + 1; i < GetMeshPartCount(); i++)
        {
            auto meshpart2 = mMesh.fmeshpartinfo[i].get();
            meshpart2->findexstart += addCount;
        }

        meshpart->findexcount += addCount;
        mMesh.findexstream->fcount += addCount;
    }

    {
        const auto stride = mMesh.findexstream->fis16bitindex ? 2 : 4;
        const auto begVbChannel = meshpart->findexstart * stride;
        if (mMesh.findexstream->fis16bitindex)
        {
            std::vector<uint16_t> indices;
            indices.resize(mb->mIndexNum);
            for (std::uint32_t i = 0; i < mb->mIndexNum; i++)
                indices[i] = static_cast<std::uint16_t>(mb->mIndex[i]);

            memcpy(mMesh.findexstream->fdata.data() + begVbChannel, indices.data(), indices.size() * stride);
        }
        else
        {
            memcpy(mMesh.findexstream->fdata.data() + begVbChannel, mb->mIndex, mb->mIndexNum * stride);
        }
    }

    for (std::uint32_t i = 0; i < mb->mChannelBufferNum; i++)
    {
        auto* channelbuf = mb->mChannelBuffer + i;

        if (!channelbuf)
            continue;

        auto* vbChannel = GetVertexChannelData(channelbuf->mChannel);
        auto stride = vbChannel->fstride;
        Assert(stride == channelbuf->mStride);
        auto vbBeginIter = vbChannel->fdata.begin();
        const auto begVbChannel = meshpart->fvertexstart * stride;
        const auto endVbChannel = (meshpart->fvertexstart + mb->mVertexNum) * stride;
        Assert(begVbChannel < endVbChannel && endVbChannel <= vbChannel->fdata.size());
        memcpy(vbChannel->fdata.data() + begVbChannel, channelbuf->mData, channelbuf->mDataSize);
    }
}

void MeshAssemble::CheckIndexStream(std::vector<std::uint32_t>& inData)
{
    auto& indexStream = mMesh.findexstream;
    auto& indices = indexStream->fdata;
    const auto count = indexStream->fcount;

    if (indexStream->fis16bitindex && *std::max_element(inData.begin(), inData.end()) > UINT16_MAX)
    {
        if (count != 0)
        {
            std::uint16_t* buffer = reinterpret_cast<std::uint16_t*>(indices.data());
            std::vector<std::uint16_t> vec16{buffer, buffer + count};
            std::vector<std::uint32_t> vec32{count};

            for (std::uint32_t i = 0; i < count; i++)
                vec32[i] = vec16[i];

            indices.resize(count * sizeof(std::uint32_t));
            memcpy(indices.data(), vec32.data(), indices.size());
        }

        indexStream->fis16bitindex = false;
    }
}

void MeshAssemble::SetSettings(const MeshSettings* settings)
{
    assert(!mEndAssembled);
    memcpy(&mSettings, settings, sizeof(MeshSettings));
}

void MeshAssemble::DeleteUnusedBuffer()
{
    if (mMesh.fmeshpartlodstartindex.size() == 0)
        return;

    // The mesh parts may be overlaped
    bool isOverlaped = false;
    std::uint32_t minVertexStart = UINT32_MAX;
    std::uint32_t maxVertexEnd = 0;
    std::uint32_t minIndexStart = UINT32_MAX;
    std::uint32_t maxIndexEnd = 0;
    for (auto beg = mMesh.fmeshpartinfo.begin(); beg != mMesh.fmeshpartinfo.end(); beg++)
    {
        const std::uint32_t verStart = beg->get()->fvertexstart;
        const std::uint32_t verEnd = beg->get()->fvertexstart + beg->get()->fvertexcount - 1;
        const std::uint32_t idxStart = beg->get()->findexstart;
        const std::uint32_t idxEnd = beg->get()->findexstart + beg->get()->findexcount - 1;

        if (!isOverlaped)
        {
            for (auto beg2 = beg + 1; beg2 != mMesh.fmeshpartinfo.end(); beg2++)
            {
                const std::uint32_t verStart2 = beg2->get()->fvertexstart;
                const std::uint32_t verEnd2 = beg2->get()->fvertexstart + beg2->get()->fvertexcount - 1;
                if ((verStart2 <= verEnd && verStart2 >= verStart) || (verEnd2 <= verEnd && verEnd2 >= verStart))
                {
                    // If the mesh parts have overlaped, then we should be more careful to delete the buffer
                    isOverlaped = true;
                }
            }
        }

        minVertexStart = minVertexStart > verStart ? verStart : minVertexStart;
        maxVertexEnd = maxVertexEnd < verEnd ? verEnd : maxVertexEnd;
        minIndexStart = minIndexStart > idxStart ? idxStart : minIndexStart;
        maxIndexEnd = maxIndexEnd < idxEnd ? idxEnd : maxIndexEnd;
    }

    Assert(maxIndexEnd >= minIndexStart && maxVertexEnd >= minVertexStart);
    const std::uint32_t totalVertex = maxVertexEnd + 1;
    const std::uint32_t totalIndex = maxIndexEnd + 1;

    for (int i = 0; i < mMesh.fvertexchanneldata.size(); i++)
    {
        auto& vbChannel = mMesh.fvertexchanneldata[i];
        auto stride = vbChannel->fstride;
        if (totalVertex < vbChannel->fdata.size() / stride)
            vbChannel->fdata.resize(totalVertex * stride);
        else if (totalVertex > vbChannel->fdata.size() / stride)
        {
            mMesh.fvertexchanneldata.erase(mMesh.fvertexchanneldata.begin() + i);
            i -= 1;
        }
        else
        {
            // Delete the buffer that have zero vaule only
            auto iter = std::find_if(std::begin(vbChannel->fdata), std::end(vbChannel->fdata), [](auto i) { return i != 0; });
            if (iter == std::end(vbChannel->fdata))
            {
                mMesh.fvertexchanneldata.erase(mMesh.fvertexchanneldata.begin() + i);
                i -= 1;
            }
        }
    }

    const auto ib = mMesh.findexstream.get();
    const std::uint32_t ibStride = ib->fis16bitindex ? 2 : 4;

    if (totalIndex < ib->fdata.size() / ibStride)
        ib->fdata.resize(totalIndex * ibStride);

    ib->fcount = static_cast<std::uint32_t>(ib->fdata.size() / ibStride);

    if (mMesh.fvertexchanneldata.size() > 0)
    {
        // Rebuild semantic mask
        mMesh.fvertexchannelsemanticmask = 0;
        mMesh.fvertexcount = static_cast<std::uint32_t>(mMesh.fvertexchanneldata[0]->fdata.size() / mMesh.fvertexchanneldata[0]->fstride);
        for (auto& vbChannel : mMesh.fvertexchanneldata)
        {
            cross::VertexChannel channel = static_cast<cross::VertexChannel>(vbChannel->fvertexchannel);
            mMesh.fvertexchannelsemanticmask |= cross::GetSemantic(channel);
        }
    }
    else
    {
        Assert(0);
    }
}

void MeshAssemble::AddMeshLODs(const MeshLODs* LODs)
{
    assert(!mEndAssembled);

    if (!LODs)
        return;

    if (LODs->mLODFirstIndices)
    {
        const auto numLODs = LODs->mLODFirstIndicesNum;
        const auto cLODIndex = mMesh.fmeshpartinfo.size();
        for (std::uint32_t i = 0; i < numLODs; i++)
        {
            mMesh.fmeshpartlodstartindex.emplace_back(LODs->mLODFirstIndices[i] + static_cast<std::uint32_t>(cLODIndex));
        }
    }

    if (!LODs->mSections)
        return;

    const auto numSections = LODs->mSectionsNum;
    for (std::uint32_t i = 0; i < numSections; i++)
    {
        const auto* section = LODs->mSections + i;

        auto& subMesh = mMesh.fmeshpartinfo.emplace_back(std::make_unique<CrossSchema::ImportMeshPartAssetInfoT>());
        {
            subMesh->fbindinginfo = std::make_unique<CrossSchema::MeshBoundT>();
            subMesh->fbindinginfo->fmax = std::make_unique<CrossSchema::float3>();
            subMesh->fbindinginfo->fmin = std::make_unique<CrossSchema::float3>();
        }

        subMesh->findexstart = section->mFirstIndex;
        subMesh->findexcount = section->mNumTriangles * 3;
        subMesh->fvertexstart = section->mMinVertexIndex;
        subMesh->fvertexcount = section->mMaxVertexIndex - section->mMinVertexIndex + 1;
        subMesh->fprimitivecount = section->mNumTriangles;
        subMesh->fprimitivetype = static_cast<std::uint32_t>(cross::PrimitiveTopology::TriangleList);

        // sub mesh name
        {
            std::string name = section->mName ? section->mName : "submesh_" + std::to_string(i);
            subMesh->fnameindex = static_cast<std::int16_t>(mMesh.fmeshpartnames.size());
            mMesh.fmeshpartnames.emplace_back(name);
        }

        // material reference
        if (section->mMaterial || section->mMaterial_A)
        {
            std::string materialReference = section->mMaterial ? section->mMaterial : AssembleProxy::GetAssetReference(section->mMaterial_A);
            materialReference = TryGetRefference(materialReference);
            auto& materialNames = mMesh.fmaterialnames;
            auto result = std::find_if(materialNames.begin(), materialNames.end(), [&](std::string& name) { return name == materialReference; });
            if (result == std::end(materialNames))
            {
                subMesh->fmaterialindex = static_cast<std::int16_t>(materialNames.size());
                materialNames.emplace_back(materialReference);
            }
            else
            {
                subMesh->fmaterialindex = static_cast<std::int16_t>(std::distance(std::begin(materialNames), result));
            }
        }
        else
        {
            if (i > 0)
                subMesh->fmaterialindex = mMesh.fmeshpartinfo[0]->fmaterialindex;
        }
    }

    DeleteUnusedBuffer();
}

void MeshAssemble::AddMeshLODSetting(MeshLODSetting LODSetting)
{
    mLODSetting = std::move(LODSetting);
}

void MeshAssemble::AddIndexStream(std::uint32_t const* data, const std::uintmax_t size)
{
    assert(!mEndAssembled);

    if (!data || !size)
        return;

    using T = std::uint32_t;
    const auto bufferSize = size / (sizeof(T) / sizeof(decltype(data[0])));
    std::vector<T> inData{};
    inData.resize(bufferSize);
    memcpy(inData.data(), reinterpret_cast<const T*>(data), bufferSize * sizeof(T));

    auto& indexStream = mMesh.findexstream;
    auto& indices = indexStream->fdata;
    const auto count = indexStream->fcount;
    const auto increment = inData.size();
    const auto amount = count + increment;

    CheckIndexStream(inData);

    if (indexStream->fis16bitindex)
    {
        indices.resize(amount * sizeof(std::uint16_t));
        std::uint16_t* buffer = reinterpret_cast<std::uint16_t*>(indices.data());
        for (int i = 0; i < inData.size(); i++)
            buffer[count + i] = static_cast<std::uint16_t>(inData[i]);
    }
    else
    {
        constexpr auto stride = sizeof(std::uint32_t);
        indices.resize(amount * stride);
        memcpy(indices.data() + count * stride, inData.data(), inData.size() * stride);
    }

    indexStream->fcount = static_cast<std::uint32_t>(amount);

    mMesh.fprimitivecount = static_cast<std::uint32_t>(amount / 3);
}

void MeshAssemble::AddVertexBuffer(const MeshVertexBuffer* vb)
{
    assert(!mEndAssembled);

    const auto UVCount = (vb->mUVCount < 0 || vb->mUVCount > 16) ? 0 : vb->mUVCount;

    const std::uint32_t stride = VertexBufferLayoutStride(vb->mLayout, UVCount);
    assert(vb->mDataSize % stride == 0);

    const auto count = vb->mDataSize / stride;
    auto channelMask = VertexBufferLayoutChannelMask(vb->mLayout, UVCount);

    if (vb->mIgnoreChannels != nullptr)
    {
        for (std::uintmax_t i = 0; i < vb->mIgnoreChannelsNum; i++)
            channelMask[GetVertexChannelMaskBit(static_cast<cross::VertexChannel>(*(vb->mIgnoreChannels + i)))] = false;
    }

    std::vector<float> position;
    std::vector<float> normal;
    std::vector<float> tangent;
    std::vector<float> binormal;
    std::vector<std::uint32_t> color;
    std::vector<std::vector<float>> uvs;

    if (channelMask[GetVertexChannelMaskBit(cross::VertexChannel::Position0)])
        position.resize(3 * count);

    if (channelMask[GetVertexChannelMaskBit(cross::VertexChannel::Normal0)])
        normal.resize(3 * count);

    if (channelMask[GetVertexChannelMaskBit(cross::VertexChannel::Tangent0)])
        tangent.resize(4 * count);

    if (channelMask[GetVertexChannelMaskBit(cross::VertexChannel::BiNormal0)])
        binormal.resize(4 * count);

    if (channelMask[GetVertexChannelMaskBit(cross::VertexChannel::Color0)])
        color.resize(count);

    uvs.resize(UVCount);

    for (int i = 0; i < uvs.size(); i++)
    {
        if (channelMask[GetVertexChannelMaskBit(cross::VertexChannel::TexCoord0 + i)])
            uvs[i].resize(2 * count);
    }

    for (std::size_t i = 0; i < count; i++)
    {
        const float* headF = reinterpret_cast<const float*>(vb->mData + i * stride);

        position.size() && memcpy(position.data() + i * 3, headF, sizeof(float) * 3);
        normal.size() && memcpy(normal.data() + i * 3, headF + 3, sizeof(float) * 3);

        if (tangent.size())
        {
            memcpy(tangent.data() + i * 4, headF + 6, sizeof(float) * 3);
            tangent[i * 4 + 3] = 0;
        }

        if (binormal.size())
        {
            memcpy(binormal.data() + i * 4, headF + 9, sizeof(float) * 3);
            binormal[i * 4 + 3] = 0;
        }

        if (color.size())
        {
            constexpr auto c_size = sizeof(std::uint32_t);
            cross::ColorRGBA32 coloru32{cross::ColorRGBAf{headF[12], headF[13], headF[14], headF[15]}};
            memcpy(color.data() + i, &coloru32, c_size);
        }

        int offset = 16;
        for (int idx = 0; idx < uvs.size(); idx++)
        {
            uvs[idx].size() && memcpy(uvs[idx].data() + i * 2, headF + offset, sizeof(float) * 2);
            offset += 2;
        }
    }

    if (position.size())
        AddVertexPosition(VertexChannel::Position0, position.data(), position.size());

    if (normal.size())
        AddVertexNormal(VertexChannel::Normal0, normal.data(), normal.size());

    if (tangent.size())
        AddVertexTangent(VertexChannel::Tangent0, tangent.data(), tangent.size());

    if (binormal.size())
        AddVertexBiNormal(VertexChannel::BiNormal0, binormal.data(), binormal.size());

    if (color.size())
        AddVertexColor(VertexChannel::Color0, color.data(), color.size());

    for (int i = 0; i < uvs.size(); i++)
    {
        auto channel = cross::VertexChannel::TexCoord0 + i;
        AddVertexTexCoord(static_cast<VertexChannel>(channel), uvs[i].data(), uvs[i].size());
    }
}

void MeshAssemble::AddVertexPosition(const VertexChannel channel, float const* data, const std::uintmax_t size)
{
    assert(!mEndAssembled);

    if (!data || !size)
        return;

    if (channel < VertexChannel::Position0 || channel > VertexChannel::PositionLast)
        return;

    using T = CrossSchema::float3;
    const auto bufferSize = size / (sizeof(T) / sizeof(decltype(data[0])));
    std::vector<T> inData{};
    inData.resize(bufferSize);
    memcpy(inData.data(), reinterpret_cast<const T*>(data), bufferSize * sizeof(T));

    auto* channelDataP = AddChannelData(cross::VertexSemantic::SemanticPosition, static_cast<cross::VertexChannel>(channel), cross::VertexFormat::Float3, inData);

    mMesh.fvertexcount = static_cast<std::uint32_t>(channelDataP->fdata.size() / sizeof(T));
}

void MeshAssemble::AddVertexTexCoord(const VertexChannel channel, float const* data, const std::uintmax_t size)
{
    assert(!mEndAssembled);

    if (!data || !size)
        return;

    if (channel < VertexChannel::TexCoord0 || channel > VertexChannel::TexCoordLast)
        return;

    using T = CrossSchema::float2;
    const auto bufferSize = size / (sizeof(T) / sizeof(decltype(data[0])));
    std::vector<T> inData{};
    inData.resize(bufferSize);
    memcpy(inData.data(), reinterpret_cast<const T*>(data), bufferSize * sizeof(T));

    if (mSettings.mUseFullPrecisionUV)
    {
        AddChannelData(cross::VertexSemantic::SemanticTexCoord, static_cast<cross::VertexChannel>(channel), cross::VertexFormat::Float2, inData);
    }
    else
    {
        std::vector<cross::UShort2> normalized_data;
        cross::FloatToHalfConverter converter;
        TypeConverter(inData, normalized_data, [&converter](const float& in, auto& out) { converter.Convert(in, out); });
        AddChannelData(cross::VertexSemantic::SemanticTexCoord, static_cast<cross::VertexChannel>(channel), cross::VertexFormat::Half2, normalized_data);
    }
}

void MeshAssemble::AddVertexColor(const VertexChannel channel, std::uint32_t const* data, const std::uintmax_t size)
{
    assert(!mEndAssembled);

    if (!data || !size)
        return;

    if (channel < VertexChannel::Color0 || channel > VertexChannel::ColorLast)
        return;

    using T = UInt32;
    const auto bufferSize = size / (sizeof(T) / sizeof(decltype(data[0])));
    std::vector<T> inData{};
    inData.resize(bufferSize);
    memcpy(inData.data(), reinterpret_cast<const T*>(data), bufferSize * sizeof(T));

    AddChannelData(cross::VertexSemantic::SemanticColor, static_cast<cross::VertexChannel>(channel), cross::VertexFormat::Color, inData);
}

void MeshAssemble::AddVertexNormal(const VertexChannel channel, float const* data, const std::uintmax_t size)
{
    assert(!mEndAssembled);

    if (!data || !size)
        return;

    if (channel < VertexChannel::Normal0 || channel > VertexChannel::NormalLast)
        return;

    // using T = CrossSchema::float3;
    // const auto bufferSize = size / (sizeof(T) / sizeof(decltype(data[0])));
    // std::vector<T> inData{};
    // inData.resize(bufferSize);
    // memcpy(inData.data(), reinterpret_cast<const T*>(data), bufferSize * sizeof(T));

    // float3 -> float4
    std::vector<CrossSchema::float4> inData(size / (sizeof(CrossSchema::float3) / sizeof(float)));
    for (size_t i = 0; i < inData.size(); ++i) {
        const CrossSchema::float3& inNormal = reinterpret_cast<const CrossSchema::float3*>(data)[i];
        inData[i] = {inNormal.x(), inNormal.y(), inNormal.z(), 1.f};
    }

    if (!mMesh.frefskeleton->skelteon.empty())
    {
        AddChannelData(cross::VertexSemantic::SemanticNormal, static_cast<cross::VertexChannel>(channel), cross::VertexFormat::Float3, inData);
    }
    else  // float3 -> short4 or byte4
    {
        if (mSettings.mUseFullPrecisionTangent)
        {
            std::vector<cross::UShort4> normalized_data;
            TypeConverter(inData, normalized_data, [](const float& in, auto& out) { cross::FloatToSNORM(in, out); });
            AddChannelData(cross::VertexSemantic::SemanticNormal, static_cast<cross::VertexChannel>(channel), cross::VertexFormat::Short4_Norm, normalized_data);
        }
        else
        {
            std::vector<cross::UChar4> normalized_data;
            TypeConverter(inData, normalized_data, [](const float& in, auto& out) { cross::FloatToSNORM(in, out); });
            AddChannelData(cross::VertexSemantic::SemanticNormal, static_cast<cross::VertexChannel>(channel), cross::VertexFormat::Byte4_Norm, normalized_data);
        }
    }
}

void MeshAssemble::AddVertexTangent(const VertexChannel channel, float const* data, const std::uintmax_t size)
{
    assert(!mEndAssembled);

    if (!data || !size)
        return;

    if (channel < VertexChannel::Tangent0 || channel > VertexChannel::TangentLast)
        return;

    using T = CrossSchema::float4;
    const auto bufferSize = size / (sizeof(T) / sizeof(decltype(data[0])));
    std::vector<T> inData{};
    inData.resize(bufferSize);
    memcpy(inData.data(), reinterpret_cast<const T*>(data), bufferSize * sizeof(T));

    if (!mMesh.frefskeleton->skelteon.empty())
    {
        AddChannelData(cross::VertexSemantic::SemanticTangent, static_cast<cross::VertexChannel>(channel), cross::VertexFormat::Float4, inData);
    }
    else
    {
        if (mSettings.mUseFullPrecisionTangent)
        {
            std::vector<cross::UShort4> normalized_data;
            TypeConverter(inData, normalized_data, [](const float& in, auto& out) { cross::FloatToSNORM(in, out); });
            AddChannelData(cross::VertexSemantic::SemanticTangent, static_cast<cross::VertexChannel>(channel), cross::VertexFormat::Short4_Norm, normalized_data);
        }
        else
        {
            std::vector<cross::UChar4> normalized_data;
            TypeConverter(inData, normalized_data, [](const float& in, auto& out) { cross::FloatToSNORM(in, out); });
            AddChannelData(cross::VertexSemantic::SemanticTangent, static_cast<cross::VertexChannel>(channel), cross::VertexFormat::Byte4_Norm, normalized_data);
        }
    }
}

void MeshAssemble::AddVertexBiNormal(const VertexChannel channel, float const* data, const std::uintmax_t size)
{
    assert(!mEndAssembled);

    if (!data || !size)
        return;

    if (channel < VertexChannel::BiNormal0 || channel > VertexChannel::BiNormalLast)
        return;

    using T = CrossSchema::float4;
    const auto bufferSize = size / (sizeof(T) / sizeof(decltype(data[0])));
    std::vector<T> inData{};
    inData.resize(bufferSize);
    memcpy(inData.data(), reinterpret_cast<const T*>(data), bufferSize * sizeof(T));

    if (!mMesh.frefskeleton->skelteon.empty())
    {
        AddChannelData(cross::VertexSemantic::SemanticBiNormal, static_cast<cross::VertexChannel>(channel), cross::VertexFormat::Float4, inData);
    }
    else
    {
        if (mSettings.mUseFullPrecisionTangent)
        {
            std::vector<cross::UShort4> normalized_data;
            TypeConverter(inData, normalized_data, [](const float& in, auto& out) { cross::FloatToSNORM(in, out); });
            AddChannelData(cross::VertexSemantic::SemanticBiNormal, static_cast<cross::VertexChannel>(channel), cross::VertexFormat::Short4_Norm, normalized_data);
        }
        else
        {
            std::vector<cross::UChar4> normalized_data;
            TypeConverter(inData, normalized_data, [](const float& in, auto& out) { cross::FloatToSNORM(in, out); });
            AddChannelData(cross::VertexSemantic::SemanticBiNormal, static_cast<cross::VertexChannel>(channel), cross::VertexFormat::Byte4_Norm, normalized_data);
        }
    }
}

void MeshAssemble::AddVertexBoneWeights(const VertexChannel channel, float const* data, const std::uintmax_t size)
{
    assert(!mEndAssembled);

    if (!data || !size)
        return;

    if (channel < VertexChannel::BlendWeight0 || channel > VertexChannel::BlendWeightLast)
        return;

    using T = CrossSchema::float4;
    const auto bufferSize = size / (sizeof(T) / sizeof(decltype(data[0])));
    std::vector<T> inData{};
    inData.resize(bufferSize);
    memcpy(inData.data(), reinterpret_cast<const T*>(data), bufferSize * sizeof(T));

    AddChannelData(cross::VertexSemantic::SemanticBlendWeight, static_cast<cross::VertexChannel>(channel), cross::VertexFormat::Float4, inData);
}

void MeshAssemble::AddVertexBoneIds(const VertexChannel channel, std::int16_t const* data, const std::uintmax_t size)
{
    assert(!mEndAssembled);

    if (!data || !size)
        return;

    if (channel < VertexChannel::BlendIndex0 || channel > VertexChannel::BlendIndexLast)
        return;

    using T = cross::Short4;

    const auto bufferSize = size / (sizeof(T) / sizeof(decltype(data[0])));
    std::vector<T> inData{};
    inData.resize(bufferSize);
    memcpy(inData.data(), reinterpret_cast<const T*>(data), bufferSize * sizeof(T));

    AddChannelData(cross::VertexSemantic::SemanticBlendIndex, static_cast<cross::VertexChannel>(channel), cross::VertexFormat::Short4, inData);
}

void MeshAssemble::AddVertexQuatTangent(const VertexChannel channel, float const* data, const std::uintmax_t size)
{
    assert(!mEndAssembled);

    if (!data || !size)
        return;

    if (channel < VertexChannel::QUATTAN0 || channel > VertexChannel::QUATTANLast)
        return;

    using T = CrossSchema::float4;
    const auto bufferSize = size / (sizeof(T) / sizeof(decltype(data[0])));
    std::vector<T> inData{};
    inData.resize(bufferSize);
    memcpy(inData.data(), reinterpret_cast<const T*>(data), bufferSize * sizeof(T));

    if (mSettings.mUseFullPrecisionTangent)
    {
        std::vector<cross::UShort4> normalized_data;
        TypeConverter(inData, normalized_data, [](const float& in, auto& out) { cross::FloatToSNORM(in, out); });
        AddChannelData(cross::VertexSemantic::SemanticQuatTan, static_cast<cross::VertexChannel>(channel), cross::VertexFormat::Short4_Norm, normalized_data);
    }
    else
    {
        std::vector<cross::UChar4> normalized_data;
        TypeConverter(inData, normalized_data, [](const float& in, auto& out) { cross::FloatToSNORM(in, out); });
        AddChannelData(cross::VertexSemantic::SemanticQuatTan, static_cast<cross::VertexChannel>(channel), cross::VertexFormat::Byte4_Norm, normalized_data);
    }
}

void MeshAssemble::AddBoxCollision(const BoxCollision* data, const std::uintmax_t size)
{
    for (auto i = 0; i < size; ++i)
    {
        auto newBox = std::make_unique<CrossSchema::PhysicsBoxCollisionT>();
        newBox->position = std::make_unique<CrossSchema::float3>(data[i].center[0], data[i].center[1], data[i].center[2]);
        newBox->rotate = std::make_unique<CrossSchema::float4>(data[i].rotation[0], data[i].rotation[1], data[i].rotation[2], data[i].rotation[3]);
        newBox->halfextents = std::make_unique<CrossSchema::float3>(data[i].extent[0], data[i].extent[1], data[i].extent[2]);

        mMesh.fphysicscollision->boxcollision.emplace_back(std::move(newBox));
    }
}

void MeshAssemble::AddSphereCollision(const SphereCollision* data, const std::uintmax_t size)
{
    for (auto i = 0; i < size; ++i)
    {
        auto newSphere = std::make_unique<CrossSchema::PhysicsSphereCollisionT>();
        newSphere->position = std::make_unique<CrossSchema::float3>(data[i].center[0], data[i].center[1], data[i].center[2]);
        newSphere->radius = data[i].radius;

        mMesh.fphysicscollision->spherecollision.emplace_back(std::move(newSphere));
    }
}

void MeshAssemble::AddCapsuleCollision(const CapsuleCollision* data, const std::uintmax_t size)
{
    for (auto i = 0; i < size; ++i)
    {
        auto newCapsule = std::make_unique<CrossSchema::PhysicsCapsuleCollisionT>();
        newCapsule->position = std::make_unique<CrossSchema::float3>(data[i].center[0], data[i].center[1], data[i].center[2]);
        newCapsule->rotate = std::make_unique<CrossSchema::float4>(data[i].rotation[0], data[i].rotation[1], data[i].rotation[2], data[i].rotation[3]);
        newCapsule->radius = data[i].radius;
        newCapsule->halfHeight = data[i].halfHeight;
        mMesh.fphysicscollision->capsulecollision.emplace_back(std::move(newCapsule));
    }
}

void MeshAssemble::AddConvexCollision(const ConvexCollision* data)
{
    cross::PhysicsEngine* physicsEngine = cross::EngineGlobal::GetPhysicsEngine();

    auto newConvex = std::make_unique<CrossSchema::PhysicsConvexCollisionT>();
    newConvex->position = std::make_unique<CrossSchema::float3>(data->center[0], data->center[1], data->center[2]);
    newConvex->rotate = std::make_unique<CrossSchema::float4>(data->rotation[0], data->rotation[1], data->rotation[2], data->rotation[3]);
    auto convexMesh = physicsEngine->GetCooker()->BuildConvexMesh(data->vertexData, data->vertexSize, data->vertexStride, data->indexData, data->indexSize, data->indexStride);
    newConvex->data = physicsEngine->GetCooker()->SerializeConvexMesh(convexMesh.get());
    mMesh.fphysicscollision->convexcollision.emplace_back(std::move(newConvex));
}

void MeshAssemble::AddTriMeshCollision(const TriMeshCollision* data) {
    auto newTriMesh = std::make_unique<CrossSchema::PhysicsMeshCollisionT>();
    newTriMesh->position = std::make_unique<CrossSchema::float3>(0.0f, 0.0f, 0.0f);
    newTriMesh->rotate = std::make_unique<CrossSchema::float4>(0.0f, 0.0f, 0.0f, 1.0f);
    cross::PhysicsEngine* physicsEngine = cross::EngineGlobal::GetPhysicsEngine();

    auto triMesh = physicsEngine->GetCooker()->BuildTriangleMesh(data->vertexData, data->vertexSize, data->vertexStride, data->indexData, data->indexSize, data->indexStride);
    newTriMesh->data = physicsEngine->GetCooker()->SerializeTriangleMesh(triMesh.get());
    mMesh.fphysicscollision->meshcollision.emplace_back(std::move(newTriMesh));
}
void MeshAssemble::SaveToFile()
{
    if (mSaved)
        return;

    if (mFile.empty())
        return;

    if (!mOverwrite && std::filesystem::exists(mFile))
    {
        mSaved = true;
        return;
    }

    DeleteUnusedBuffer();

    // generate default mesh part if no exist
    if (mMesh.fmeshpartinfo.size() == 0)
    {
        auto& subMesh = mMesh.fmeshpartinfo.emplace_back(std::make_unique<CrossSchema::ImportMeshPartAssetInfoT>());
        subMesh->fprimitivecount = mMesh.findexstream->fcount / 3;
        subMesh->fprimitivetype = static_cast<std::uint32_t>(cross::PrimitiveTopology::TriangleList);
        subMesh->fbindinginfo = std::make_unique<CrossSchema::MeshBoundT>();
        subMesh->fbindinginfo->fmax = std::make_unique<CrossSchema::float3>();
        subMesh->fbindinginfo->fmin = std::make_unique<CrossSchema::float3>();
        subMesh->findexstart = subMesh->fvertexstart = 0;
        subMesh->findexcount = mMesh.findexstream->fcount;

        subMesh->fnameindex = static_cast<std::int16_t>(mMesh.fmeshpartnames.size());
        mMesh.fmeshpartnames.emplace_back("default");

        auto result = mMesh.fvertexchanneldata.begin();
        for (; result != mMesh.fvertexchanneldata.end(); result++)
        {
            if (result->get()->fvertexchannel == static_cast<std::uint32_t>(VertexChannel::Position0))
                break;
        }

        if (result != mMesh.fvertexchanneldata.end())
            subMesh->fvertexcount = static_cast<std::uint32_t>(result->get()->fdata.size()) / sizeof(CrossSchema::float3);
    }

    // Process each mesh part to handle empty submeshes and adjust index starts
    if (mMesh.fmeshpartinfo.size() > 0)
    {
        auto* indexStream = mMesh.findexstream.get();
        std::uint32_t currentVertexOffset = 0;
        std::uint32_t currentIndexOffset = 0;

        // Find the position channel data
        CrossSchema::VertexChannelAssetDataT* positionChannelData = nullptr;
        auto posResult = mMesh.fvertexchanneldata.begin();
        for (; posResult != mMesh.fvertexchanneldata.end(); posResult++)
        {
            if (posResult->get()->fvertexchannel == static_cast<std::uint32_t>(VertexChannel::Position0))
            {
                positionChannelData = posResult->get();
                break;
            }
        }

        for (auto beg = mMesh.fmeshpartinfo.begin(); beg != mMesh.fmeshpartinfo.end(); beg++)
        {
            auto* subMesh = beg->get();

            if (subMesh->findexcount == 0)
            {
                // Handle empty submesh: add placeholder geometry
                const std::uint32_t placeholderVertexCount = 1;
                const std::uint32_t placeholderIndexCount = 3; // For a degenerate triangle

                // Add placeholder vertex data (position at origin)
                if (positionChannelData)
                {
                    const std::uint32_t positionStride = positionChannelData->fstride;
                    positionChannelData->fdata.resize(positionChannelData->fdata.size() + placeholderVertexCount * positionStride, 0);
                }
                // Add placeholder data for other channels if necessary (e.g., default UV, normal)
                // This would require iterating through other channels and appending default data.
                // For simplicity in this example, we only handle position.

                // Add placeholder index data (pointing to the placeholder vertex)
                const std::uint32_t indexStride = indexStream->fis16bitindex ? 2 : 4;
                indexStream->fdata.resize(indexStream->fdata.size() + placeholderIndexCount * indexStride);
                if (indexStream->fis16bitindex)
                {
                    std::uint16_t* indexHead = reinterpret_cast<std::uint16_t*>(indexStream->fdata.data()) + indexStream->fcount;
                    std::uint16_t placeholderIndex = static_cast<std::uint16_t>(mMesh.fvertexcount); // Index of the new placeholder vertex
                    indexHead[0] = placeholderIndex;
                    indexHead[1] = placeholderIndex;
                    indexHead[2] = placeholderIndex;
                }
                else
                {
                    std::uint32_t* indexHead = reinterpret_cast<std::uint32_t*>(indexStream->fdata.data()) + indexStream->fcount;
                    std::uint32_t placeholderIndex = mMesh.fvertexcount; // Index of the new placeholder vertex
                    indexHead[0] = placeholderIndex;
                    indexHead[1] = placeholderIndex;
                    indexHead[2] = placeholderIndex;
                }

                // Update submesh info
                subMesh->findexstart = indexStream->fcount;
                subMesh->findexcount = placeholderIndexCount;
                subMesh->fvertexstart = mMesh.fvertexcount;
                subMesh->fvertexcount = placeholderVertexCount;
                subMesh->fprimitivecount = 1; // One degenerate triangle

                // Update total counts
                mMesh.fvertexcount += placeholderVertexCount;
                indexStream->fcount += placeholderIndexCount;
                mMesh.fprimitivecount += 1;

                // Update offsets for subsequent submeshes
                currentVertexOffset += placeholderVertexCount;
                currentIndexOffset += placeholderIndexCount;
            }
            else
            {
                // Handle non-empty submesh: adjust index start
                if (indexStream->fis16bitindex)
                {
                    using T = std::uint16_t;
                    T* head = reinterpret_cast<T*>(indexStream->fdata.data()) + subMesh->findexstart;
                    std::vector<T> subIndices{head, head + subMesh->findexcount};

                    T minIndex = *std::min_element(subIndices.begin(), subIndices.end());
                    if (minIndex != 0)
                    {
                        std::for_each(subIndices.begin(), subIndices.end(), [&minIndex](T& v) { v -= minIndex; });
                        memcpy(head, subIndices.data(), subIndices.size() * sizeof(T));
                    }
                }
                else
                {
                    using T = std::uint32_t;
                    T* head = reinterpret_cast<T*>(indexStream->fdata.data()) + subMesh->findexstart;
                    std::vector<T> subIndices{head, head + subMesh->findexcount};

                    T minIndex = *std::min_element(subIndices.begin(), subIndices.end());
                    if (minIndex != 0)
                    {
                        std::for_each(subIndices.begin(), subIndices.end(), [&minIndex](T& v) { v -= minIndex; });
                        memcpy(head, subIndices.data(), subIndices.size() * sizeof(T));
                    }
                }

                // Update offsets for subsequent submeshes
                currentVertexOffset += subMesh->fvertexcount;
                currentIndexOffset += subMesh->findexcount;
            }
        }
    }

    // UE5 Nanite proxy mesh LOD level may be out-of-order
    if (mMesh.fmeshpartlodstartindex.size() > 1)
    {
        auto& LODFirstIndices = mMesh.fmeshpartlodstartindex;
        auto& sections = mMesh.fmeshpartinfo;

        struct LODRecord
        {
            std::uint32_t mNumIndex = 0;
            std::uint32_t mIndex = 0;
            std::vector<std::unique_ptr<CrossSchema::ImportMeshPartAssetInfoT>> mMeshParts{};
        };

        std::vector<LODRecord> LODLevel{};
        LODLevel.resize(LODFirstIndices.size());
        bool bNeedReOrder = false;

        for (std::size_t index = 0; index < LODFirstIndices.size(); index++)
        {
            const std::uint32_t sectionIdxEnd = index == LODFirstIndices.size() - 1 ? static_cast<std::uint32_t>(sections.size()) : LODFirstIndices[index + 1];

            LODLevel[index].mIndex = static_cast<std::uint32_t>(index);

            for (std::uint32_t sectionIdx = LODFirstIndices[index]; sectionIdx < sectionIdxEnd; sectionIdx++)
                LODLevel[index].mNumIndex += sections[sectionIdx]->findexcount;

            for (std::size_t J = 0; J < index; J++)
                bNeedReOrder = bNeedReOrder || (LODLevel[index].mNumIndex > LODLevel[J].mNumIndex);
        }

        if (bNeedReOrder)
        {
            for (std::size_t index = 0; index < LODFirstIndices.size(); index++)
            {
                const std::uint32_t sectionIdxEnd = index == LODFirstIndices.size() - 1 ? static_cast<std::uint32_t>(sections.size()) : LODFirstIndices[index + 1];
                for (std::uint32_t sectionIdx = LODFirstIndices[index]; sectionIdx < sectionIdxEnd; sectionIdx++)
                    LODLevel[index].mMeshParts.emplace_back(std::move(sections[sectionIdx]));
            }

            std::sort(LODLevel.begin(), LODLevel.end(), [](auto& a, auto& b) { return a.mNumIndex > b.mNumIndex; });

            LODFirstIndices[0] = 0;
            for (std::size_t index = 1, n = LODFirstIndices.size(); index < n; index++)
            {
                LODFirstIndices[index] = LODFirstIndices[index - 1] + static_cast<std::uint32_t>(LODLevel[index - 1].mMeshParts.size());
            }

            sections.clear();
            for (std::size_t index = 0; index < LODLevel.size(); index++)
            {
                for (std::size_t i = 0, n = LODLevel[index].mMeshParts.size(); i < n; i++)
                    sections.emplace_back(std::move(LODLevel[index].mMeshParts[i]));
            }
        }
    }

    // update bindings
    if (mMesh.fvertexchannelsemanticmask & static_cast<std::uint32_t>(cross::VertexSemantic::SemanticPosition))
    {
        auto result = mMesh.fvertexchanneldata.begin();
        for (; result != mMesh.fvertexchanneldata.end(); result++)
        {
            if (result->get()->fvertexchannel == static_cast<std::uint32_t>(VertexChannel::Position0))
                break;
        }

        if (result != mMesh.fvertexchanneldata.end())
        {
            mMesh.faabb->fmax->mutate_x(-INFINITY);
            mMesh.faabb->fmax->mutate_y(-INFINITY);
            mMesh.faabb->fmax->mutate_z(-INFINITY);

            mMesh.faabb->fmin->mutate_x(INFINITY);
            mMesh.faabb->fmin->mutate_y(INFINITY);
            mMesh.faabb->fmin->mutate_z(INFINITY);

            const auto* channel = result->get();
            for (auto beg = mMesh.fmeshpartinfo.begin(); beg != mMesh.fmeshpartinfo.end(); beg++)
            {
                auto* subMesh = beg->get();
                auto* aabb = subMesh->fbindinginfo.get();
                aabb->fmax->mutate_x(-INFINITY);
                aabb->fmax->mutate_y(-INFINITY);
                aabb->fmax->mutate_z(-INFINITY);

                aabb->fmin->mutate_x(INFINITY);
                aabb->fmin->mutate_y(INFINITY);
                aabb->fmin->mutate_z(INFINITY);
                const CrossSchema::float3* positionHead = reinterpret_cast<const CrossSchema::float3*>(channel->fdata.data()) + subMesh->fvertexstart;
                for (uint32_t i = 0; i < subMesh->fvertexcount; i++)
                {
                    aabb->fmax->mutate_x(std::max(aabb->fmax->x(), (positionHead + i)->x()));
                    aabb->fmax->mutate_y(std::max(aabb->fmax->y(), (positionHead + i)->y()));
                    aabb->fmax->mutate_z(std::max(aabb->fmax->z(), (positionHead + i)->z()));

                    aabb->fmin->mutate_x(std::min(aabb->fmin->x(), (positionHead + i)->x()));
                    aabb->fmin->mutate_y(std::min(aabb->fmin->y(), (positionHead + i)->y()));
                    aabb->fmin->mutate_z(std::min(aabb->fmin->z(), (positionHead + i)->z()));
                }

                mMesh.faabb->fmax->mutate_x(std::max(mMesh.faabb->fmax->x(), aabb->fmax->x()));
                mMesh.faabb->fmax->mutate_y(std::max(mMesh.faabb->fmax->y(), aabb->fmax->y()));
                mMesh.faabb->fmax->mutate_z(std::max(mMesh.faabb->fmax->z(), aabb->fmax->z()));

                mMesh.faabb->fmin->mutate_x(std::min(mMesh.faabb->fmin->x(), aabb->fmin->x()));
                mMesh.faabb->fmin->mutate_y(std::min(mMesh.faabb->fmin->y(), aabb->fmin->y()));
                mMesh.faabb->fmin->mutate_z(std::min(mMesh.faabb->fmin->z(), aabb->fmin->z()));
            }
        }
    }

    if (mSettings.mGenCollisionTree)
    {
        // CollisionTree
        for (size_t partIndex = 0; partIndex < mMesh.fmeshpartinfo.size(); ++partIndex)
        {
            auto& cTreeVec = mMesh.fmeshpartinfo[partIndex]->fcollisiontree;
            cTreeVec.clear();   // clear old data
            cross::editor::MeshPickBuilder::BuildCollisionTree(mMesh, *(mMesh.fmeshpartinfo[partIndex]), cTreeVec);
        }
    }

    cross::SerializeNode customNode, meshLODSettingNode;
    cross::SerializeNode lodLevelSettingNode = cross::SerializeNode::EmptyArray();
    for (const auto& levelSetting : mLODSetting.mLevelSettings)
    {
        cross::SerializeNode levelSettingNode = cross::SerializeNode::EmptyObject();
        levelSettingNode["fadeTransitionWidth"] = levelSetting.mFadeTransitionWidth;
        levelSettingNode["screenRelativeTransitionHeight"] = levelSetting.mScreenRelativeTransitionHeight;
        lodLevelSettingNode.PushBack(std::move(levelSettingNode));
    }
    meshLODSettingNode["lodLevelSetting"] = std::move(lodLevelSettingNode);
    customNode["MeshAssetLODSetting"] = std::move(meshLODSettingNode);
    customNode["culledHeight"] = mLODSetting.mCulledHeight;
    customNode["isStreamable"] = mLODSetting.mIsStreamable;

    cross::serialize::SerializeMesh(mFile.string().c_str(), &mMesh, CLASS_MeshAssetDataResource, mGUID.c_str(), std::move(customNode));

    mSaved = true;
}

void MeshAssemble::AddRefSkeleton(const MeshBoneNode* nodes, const std::uintmax_t numNodes, const char* name)
{
    mMesh.frefskeleton->skelteon.clear();
    mMesh.fbindposeinv.clear();
    mMesh.frefskeleton->name = std::string(name);
    for (size_t i = 0; i < numNodes; i++)
    {
        auto ImportBoneNode = std::make_unique<CrossSchema::ImportBoneNodeT>();

        ImportBoneNode->name = std::string(nodes[i].Name);
        ImportBoneNode->boneid = nodes[i].BoneId;
        ImportBoneNode->bonetype = 0;   // Bone type, default is 0
        ImportBoneNode->parentid = nodes[i].ParentId;
        ImportBoneNode->retarget = (CrossSchema::ImportBoneTransRetgtMode)nodes[i].Retarget;
        ImportBoneNode->bindposeinv.assign(nodes[i].BindposeInv, nodes[i].BindposeInv + 16);
        ImportBoneNode->bindposedef.assign(nodes[i].BindposeRef, nodes[i].BindposeRef + 16);
        ImportBoneNode->worldmatrix.assign(nodes[i].WorldMatrix, nodes[i].WorldMatrix + 16);
        mMesh.frefskeleton->skelteon.push_back(std::move(ImportBoneNode));

        auto BindposeInv = std::make_unique<CrossSchema::invmatrixT>();
        BindposeInv->val.assign(nodes[i].BindposeInv, nodes[i].BindposeInv + 16);
        mMesh.fbindposeinv.push_back(std::move(BindposeInv));
    }
}
}   // namespace CEAssetExchange
