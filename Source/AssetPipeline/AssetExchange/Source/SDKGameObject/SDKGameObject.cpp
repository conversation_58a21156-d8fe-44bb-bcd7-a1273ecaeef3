#include "CrossEngineSDK.h"
#include "GameFramework/GameObjects/GameObject.h"
#include "GameFramework/Components/Component.h"
#include <algorithm> // for std::min

namespace cesdk::cegf {

bool SDKGameObject::HasComponent(const char* componentName) const
{
    if (!gameObjectInstance || !componentName)
    {
        return false;
    }
    
    return gameObjectInstance->GetComponentByMetaClassName(componentName) != nullptr;
}

SDKGameObjectComponent SDKGameObject::GetComponentByName(const char* componentName) const
{
    if (!gameObjectInstance || !componentName)
    {
        return SDKGameObjectComponent(nullptr);
    }
    
    ::cegf::GameObjectComponent* component = gameObjectInstance->GetComponentByMetaClassName(componentName);
    return SDKGameObjectComponent(component);
}

size_t SDKGameObject::GetAllComponents(SDKGameObjectComponent* outComponents, size_t maxComponentCount) const
{
    if (!gameObjectInstance || !outComponents || maxComponentCount == 0)
    {
        return 0;
    }
    
    const auto& components = gameObjectInstance->GetAllComponents();
    size_t count = std::min(components.size(), maxComponentCount);
    
    for (size_t i = 0; i < count; ++i)
    {
        outComponents[i] = SDKGameObjectComponent(components[i].get());
    }
    
    return count;
}

size_t SDKGameObject::GetComponentCount() const
{
    if (!gameObjectInstance)
    {
        return 0;
    }
    
    return gameObjectInstance->GetAllComponents().size();
}

SDKGameObjectComponent SDKGameObject::AddComponent(const char* componentName)
{
    if (!gameObjectInstance || !componentName)
    {
        return SDKGameObjectComponent(nullptr);
    }
    
    ::cegf::GameObjectComponent* component = gameObjectInstance->AddComponent(componentName);
    return SDKGameObjectComponent(component);
}

bool SDKGameObject::RemoveComponent(SDKGameObjectComponent component)
{
    if (!gameObjectInstance || !component.componentInstance)
    {
        return false;
    }
    
    gameObjectInstance->RemoveComponent(component.componentInstance);
    return true;
}

SDKGameObjectComponent SDKGameObject::GetRootComponent() const
{
    if (!gameObjectInstance)
    {
        return SDKGameObjectComponent(nullptr);
    }
    
    ::cegf::GameObjectComponent* rootComponent = gameObjectInstance->GetRootComponent();
    return SDKGameObjectComponent(rootComponent);
}

void SDKGameObject::SetRootComponent(SDKGameObjectComponent component)
{
    if (!gameObjectInstance || !component.componentInstance)
    {
        return;
    }
    
    gameObjectInstance->SetRootComponent(component.componentInstance);
}

cross::TRSVector3Type SDKGameObject::GetLocalTranslation() const
{
    if (!gameObjectInstance)
    {
        return cross::TRSVector3Type{0.0, 0.0, 0.0};
    }
    
    ::cross::TRSVector3Type translation = gameObjectInstance->GetLocalTranslation();
    return { translation.x, translation.y, translation.z };
}

void SDKGameObject::SetLocalTranslation(const cross::TRSVector3Type& translation)
{
    if (!gameObjectInstance)
    {
        return;
    }
    
    gameObjectInstance->SetLocalTranslation({ translation.x, translation.y, translation.z });
}

cross::TRSQuaternionType SDKGameObject::GetLocalRotation() const
{
    if (!gameObjectInstance)
    {
        return cross::TRSQuaternionType{0.0, 0.0, 0.0, 1.0};
    }
    
    ::cross::TRSQuaternionType rotate = gameObjectInstance->GetLocalRotation();
    return { rotate.x, rotate.y, rotate.z, rotate.w };
}

void SDKGameObject::SetLocalRotation(const cross::TRSQuaternionType& rotation)
{
    if (!gameObjectInstance)
    {
        return;
    }
    
    gameObjectInstance->SetLocalRotation({ rotation.x, rotation.y, rotation.z, rotation.w });
}

cross::TRSVector3Type SDKGameObject::GetLocalScale() const
{
    if (!gameObjectInstance)
    {
        return cross::TRSVector3Type{1.0, 1.0, 1.0};
    }
    
    ::cross::TRSVector3Type scale = gameObjectInstance->GetLocalScale();
    return { scale.x, scale.y, scale.z };
}

void SDKGameObject::SetLocalScale(const cross::TRSVector3Type& scale)
{
    if (!gameObjectInstance)
    {
        return;
    }
    
    gameObjectInstance->SetLocalScale({ scale.x, scale.y, scale.z });
}

} // namespace cesdk::cegf
