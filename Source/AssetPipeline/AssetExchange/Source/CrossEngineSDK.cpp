#include "CrossEngineSDK.h"
#include "CEGameFramework/GameFramework/GameEngine.h"
#include "Runtime/GameWorld/CanvasSystemG.h"
#include "Runtime/GameWorld/EditorGlobalCallBackSystemG.h"
#include "Runtime/GameWorld/PhysicsSystemG.h"
#include "Runtime/GameWorld/VRViewSystemG.h"
#include "Runtime/GameWorld/WorldPartition/WorldLoadingSystemG.h"
#include "CrossBase/Log.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameParam.h"
#include "Resource/Resource.h"
#include "Resource/ResourceManager.h"
#include "Resource/MaterialParameterCollection.h"
#include "Resource/Animation/Composite/AnimatrixRes.h"
#include "Resource/Animation/Composite/AnimCompositeRes.h"
#include "Resource/TerrainResource.h"
#include "Resource/Animation/Animator/AnimatorResource.h"
#include "Resource/Animation/BlendSpace/AnimBlendSpaceRes.h"
#include "MaterialBP/material_expression.h"
#include <spdlog/logger.h>
#include "memoryhooker/Module.h"
IMPORT_MODULE
namespace cesdk::cegf {

void GameEngineWrap::Init()
{
    gameEngineInstance->Init();
}

void GameEngineWrap::ShutDown()
{
    gameEngineInstance->ShutDown();
}

void GameEngineWrap::OnBeginFrame(float deltaTime)
{
    gameEngineInstance->OnBeginFrame(deltaTime);
}

void GameEngineWrap::PreTick(float deltaTime)
{
    gameEngineInstance->PreTick(deltaTime);
}

void GameEngineWrap::Tick(float deltaTime)
{
    gameEngineInstance->Tick(deltaTime);
}

void GameEngineWrap::PostTick(float deltaTime)
{
    gameEngineInstance->PostTick(deltaTime);
}

void GameEngineWrap::NotifyStartGame()
{
    gameEngineInstance->NotifyStartGame();
}

SDKGameWorld GameEngineWrap::LoadGameWorld(const char* worldName)
{
    return gameEngineInstance->LoadGameWorld(worldName, ::cross::WorldTypeTag::SDKWorld);
}

SDKGameWorld GameEngineWrap::CreateGameWorld(const char* worldName)
{
    return gameEngineInstance->CreateGameWorld(worldName, ::cross::WorldTypeTag::SDKWorld);
}

SDKGameWorld GameEngineWrap::GetGameWorld(uint32_t worldID) const
{
    return SDKGameWorld(gameEngineInstance->GetGameWorld(worldID));
}

SDKGameWorld GameEngineWrap::GetCurrentGameWorld() const
{
    return SDKGameWorld(gameEngineInstance->GetCurrentGameWorld());
}

void GameEngineWrap::SetCurrentGameWorld(SDKGameWorld world)
{
    gameEngineInstance->SetCurrentGameWorld(world.gameWorldInstance);
}

void GameEngineWrap::OnCreateGameWorld(uint32_t worldRuntimeID)
{
    gameEngineInstance->OnCreateGameWorld(worldRuntimeID);
}

void GameEngineWrap::OnDestroyGameWorld(uint32_t worldRuntimeID)
{
    gameEngineInstance->OnDestroyGameWorld(worldRuntimeID);
}

void SDKGameWorld::BeginDestroy()
{
    gameWorldInstance->BeginDestroy();
}

void SDKGameWorld::Destroyed()
{
    gameWorldInstance->Destroyed();
}

void SDKGameWorld::OnBeginFrame(float deltaTime)
{
    gameWorldInstance->OnBeginFrame(deltaTime);
}

void SDKGameWorld::Tick(float deltaTime)
{
    gameWorldInstance->Tick(deltaTime);
}

void SDKGameWorld::StartGame()
{
    gameWorldInstance->StartGame();
}

void SDKGameWorld::EndGame()
{
    gameWorldInstance->EndGame();
}

bool SDKGameWorld::HasStarted() const
{
    return gameWorldInstance->HasStarted();
}

bool SDKGameWorld::IsLoaded() const
{
    return gameWorldInstance->IsLoaded();
}

uint32_t SDKGameWorld::GetRuntimeID() const
{
    return gameWorldInstance->GetRuntimeID();
}

const char* SDKGameWorld::GetWorldPath()
{
    return gameWorldInstance->GetWorldPath().c_str();
}

const char* SDKGameWorld::GetName()
{
    return gameWorldInstance->GetName().GetCString();
}

SDKGameObject SDKGameWorld::FindGameObject(const char* name) const
{
    return gameWorldInstance->FindGameObject(name);
}

size_t SDKGameWorld::GetRuntimeCreatedGameObjectCount() const
{
    return gameWorldInstance->GetRuntimeCreatedGameObjectCount();
}

SDKGameObject SDKGameWorld::GetRootGameObject() const
{
    return gameWorldInstance->GetRootGameObject();
}

SDKGameObject SDKGameWorld::CreateGameObject(const char* name, SDKGameObject parent, const cross::TRSVector3Type& localLocation, const cross::TRSQuaternionType& localRotation, const cross::TRSVector3Type& localScale)
{
    return gameWorldInstance->CreateGameObject(name, parent.gameObjectInstance, { localLocation.x, localLocation.y, localLocation.z }, { localRotation.x, localRotation.y, localRotation.z, localRotation.w }, { localScale.x,localScale.y,localScale.z });
}

bool SDKGameWorld::DestroyGameObject(SDKGameObject gameObj)
{
    return gameWorldInstance->DestroyGameObject(gameObj.gameObjectInstance);
}

uint32_t SDKGameWorld::GetNumControllers() const
{
    return gameWorldInstance->GetNumControllers();
}

uint32_t SDKGameWorld::GetNumPlayerControllers() const
{
    return gameWorldInstance->GetNumPlayerControllers();
}

bool SDKGameWorld::HasEndOfFrameDraw() const
{
    return gameWorldInstance->HasEndOfFrameDraw();
}

void SDKGameWorld::EndOfFrameDraw() const
{
    gameWorldInstance->EndOfFrameDraw();
}

bool SDKGameWorld::Save(const char* savePath) const
{
    return gameWorldInstance->Save(savePath);
}

SDKGameObject SDKGameWorld::CreateRootGameObject() const
{
    return gameWorldInstance->CreateRootGameObject();
}

GameEngineWrap GetGameEngine()
{
    return ::gGameEngine;
}


}   // namespace cesdk::cegf

namespace cesdk{

::cross::CrossEngine* gCrossEngine = nullptr;

void CreateGameEngine(const char* sdkAssetPath, const char* engineResourcePath, CrossEngineLogDelegate logDelegate)
{
    if (!gCrossEngine)
    {
        gCrossEngine = dynamic_cast<::cross::CrossEngine*>(CreateCrossEngine());

        ::cross::InitInfo info{
            .AssetPath = sdkAssetPath,
            .EngineResourcePath = engineResourcePath,
            .StartupType = ::cross::AppStartUpType::AppStartUpTypeHeadless,
        };
        info.LogDelegate = logDelegate;
        gCrossEngine->Init(info);
        gCrossEngine->PostInit();
        gCrossEngine->PreTick();
        ::cross::MaterialExpression::RegisterExpressions();
    }
}
void DeleteGameEngine()
{
    if (gCrossEngine)
    {
        gCrossEngine->ShutDown();
        gCrossEngine = nullptr;
    }
}

void ExportGameWorld(const char* worldSavePath, cesdk::CEWorldLoadedCallBack callback, void* customData)
{
    struct Task
    {
        struct promise_type
        {
            Task get_return_object() { return Task{this}; }
            std::suspend_never initial_suspend() noexcept { return {}; }
            std::suspend_always final_suspend() noexcept { return {}; }
            void unhandled_exception() {}
            void return_void() {}
        };

        std::coroutine_handle<promise_type> mHandle;

        Task(promise_type* promise)
            : mHandle{decltype(mHandle)::from_promise(*promise)}
        {}

        ~Task()
        {
            mHandle.destroy();
        }
    };

    struct WaitForWorldLoaded : std::suspend_always, ::cross::SystemEventReceiver
    {
        ::cross::WorldLoadingSystemG* mWorldLoadingSys = nullptr;
        std::coroutine_handle<> mHandle;

        WaitForWorldLoaded(::cross::GameWorld* world)
            : mWorldLoadingSys{world->GetGameSystem<::cross::WorldLoadingSystemG>()}
        { }

        void await_suspend(std::coroutine_handle<> handle) noexcept
        {
            mHandle = handle;
            mWorldLoadingSys->SubscribeEvent<::cross::WorldChangedEvent>(this, 0);
        }

        void NotifyEvent(const ::cross::SystemEventBase& event, UInt32& flag) override
        {
            if (event.mEventType == ::cross::WorldChangedEvent::sEventType)
            {
                const auto& e = static_cast<const ::cross::WorldChangedEvent&>(event);
                if (e.mData.mWorldLoadingStatus == ::cross::WorldLoadingStatus::Loaded)
                {
                    mHandle.resume();
                }
            }
        }

        void await_resume() noexcept { mWorldLoadingSys->Unsubscribe<::cross::WorldChangedEvent>(this); }
    };

    struct WaitForNextFrame : std::suspend_always
    {
        ::cross::EditorGlobalCallBackSystemG* mCallbackSys = nullptr;

        WaitForNextFrame() { mCallbackSys = gCrossEngine->GetGlobalSystem<::cross::EditorGlobalCallBackSystemG>(); }

        void await_suspend(std::coroutine_handle<> handle) const noexcept
        {
            mCallbackSys->mGlobalUpdateCallBack = [=]() { handle.resume(); };
        }

        void await_resume() const noexcept { mCallbackSys->mGlobalUpdateCallBack = nullptr; }
    };

    Task task = [&]() -> Task {
        co_await WaitForNextFrame{};

        // now we are in engine ticking
        auto ceWorld = dynamic_cast<::cross::GameWorld*>(gCrossEngine->CreateWorld("test.world", ::cross::ToUnderlying(::cross::WorldTypeTag::SDKWorld)));
        auto curWorld = gCrossEngine->GetCurrentWorld();
        gCrossEngine->SetCurrentWorld(ceWorld);

        static const char* gDisabledSystems[]{
            "cross::CanvasSystemG",
            "cross::PhysicsSystemG",
            "cross::VRViewSystemG",
            "cross::ScreenTerminalSystemG",
            "cross::SkeletonSystemG",
            "cross::PrimitiveRenderSystemG",
            "cross::ScriptSystemG",
        };

        for (auto sysName : gDisabledSystems)
        {
            auto sys = ceWorld->GetSystem(sysName);
            sys->SetEnable(false);
        }

        ceWorld->LoadWorld("EngineResource/World/Empty.world");

        co_await WaitForWorldLoaded{ceWorld};

        callback(customData, ::gGameEngine->GetGameWorld(ceWorld->GetRuntimeID()));

        co_await WaitForNextFrame{};

        auto gfWorld = ::gGameEngine->GetGameWorld(ceWorld->GetRuntimeID());
        gfWorld->Save(worldSavePath);

        co_await WaitForNextFrame{};

        gCrossEngine->Destroy(ceWorld);

        co_await WaitForNextFrame{};

        gCrossEngine->SetCurrentWorld(curWorld);
    }();

    while (!task.mHandle.done())
    {
        gCrossEngine->Tick();
    }
}
void ExportResource(cesdk::CEExportResourceCallBack callback, void* customData)
{
    auto callbackSys = gCrossEngine->GetGlobalSystem<::cross::EditorGlobalCallBackSystemG>();
    callbackSys->mGlobalUpdateCallBack = [&]() { callback(customData); };
    gCrossEngine->Tick();
    callbackSys->mGlobalUpdateCallBack = nullptr;
}
}

namespace cesdk::resource {

SDKResourceManager* gResourceManagerPtr = nullptr;

struct ResourceHolder
{
    ::cross::ResourcePtr mResource{};
};

SDKResource::SDKResource(std::string_view path, uint64_t classID) 
{
    mPath = path;
    mResHolder = GetResourceManager().CreateResourceHolder(classID, path.data());
}

SDKResource::~SDKResource() = default;
SDKResource::SDKResource(SDKResource&& other) noexcept = default;
SDKResource& SDKResource::operator=(SDKResource&& other) noexcept = default;

void SDKResource::Serialize() const
{
    if (mResHolder && mResHolder->mResource)
    {
        mResHolder->mResource->Serialize(::cross::SerializeNode(), mPath);
    }
}

const char* SDKResource::GetGuid() const
{
    return mResHolder->mResource->GetAsset()->GetGuid().c_str();
}

SDKResourceManager& SDKResourceManager::Instance()
{
    if (!gResourceManagerPtr)
    {
        static SDKResourceManager instance;
        instance.resourceManagerInstance = &::cross::ResourceManager::Instance();
        gResourceManagerPtr = &instance;
    }
    return *gResourceManagerPtr;
}

std::unique_ptr<ResourceHolder> SDKResourceManager::CreateResourceHolder(uint64_t classID, char const* path)
{
    std::unique_ptr<ResourceHolder> resHolder = std::make_unique<ResourceHolder>();
    resHolder->mResource = Instance().resourceManagerInstance->CreateResourceByClassID(classID, path);
    return resHolder;
}

std::unique_ptr<SDKResource> SDKResourceManager::ResourceGet(char const* path)
{
    ::cross::ResourcePtr tmpResourceInstance = ::cross::ResourcePtr{::cross::ResourceUtil::ResourceGet(path)};
    if (tmpResourceInstance != nullptr)
    {
        auto resource = std::make_unique<SDKResource>();
        resource->mResHolder = std::make_unique<ResourceHolder>();
        resource->mResHolder->mResource = std::move(tmpResourceInstance);
        resource->mPath = path;
        return resource;
    }
    return nullptr;
}

SDKResourceManager& GetResourceManager()
{
    return SDKResourceManager::Instance();
}

uint64_t SDKMaterialParameterCollection::GetClassID()
{
    return ::cross::resource::MaterialParameterCollection::GetClassIDStatic();
}

void SDKMaterialParameterCollection::ClearParameters()
{
    auto* mpcPtr = static_cast<::cross::resource::MaterialParameterCollection*>(mResHolder->mResource.get());
    mpcPtr->ClearScalerParameter();
    mpcPtr->ClearVectorParameter();
}

void SDKMaterialParameterCollection::AddScalerParameter(const char* name, float value)
{
    static_cast<::cross::resource::MaterialParameterCollection*>(mResHolder->mResource.get())->AddScalerParameter(std::string(name), value);
}

void SDKMaterialParameterCollection::AddVectorParameter(const char* name, const cross::TRSVector4Type& value)
{
    static_cast<::cross::resource::MaterialParameterCollection*>(mResHolder->mResource.get())->AddVectorParameter(name, ::cross::Float4(value.x, value.y, value.z, value.w));
}

uint64_t SDKAnimCompositeResource::GetClassID()
{
    return ::cross::anim::AnimCompositeRes::GetClassIDStatic();
}

void SDKAnimCompositeResource::Init(const char* json)
{
    ::cross::DeserializeNode node = ::cross::DeserializeNode::ParseFromJson(json);
    auto animRes = TypeCast<::cross::anim::AnimCompositeRes>(mResHolder->mResource);
    animRes->Deserialize(node);
}

uint64_t SDKAnimatrixResource::GetClassID()
{
    return ::cross::anim::AnimatrixRes::GetClassIDStatic();
}

void SDKAnimatrixResource::Init(const char* json)
{
    ::cross::DeserializeNode node = ::cross::DeserializeNode::ParseFromJson(json);
    auto animRes = TypeCast<::cross::anim::AnimatrixRes>(mResHolder->mResource);
    animRes->Deserialize(node);
}

SDKTerrainResource::SDKTerrainResource(std::string_view path) : SDKResource(path, GetClassID())
{
    auto resource = TypeCast<::cross::resource::TerrainResource>(mResHolder->mResource);
    resource->mTerrainInfo.mSurfaceType = ::cross::TerrainSurfaceType::Flat;
}

uint64_t SDKTerrainResource::GetClassID()
{
    return ::cross::resource::TerrainResource::GetClassIDStatic();
}

void SDKTerrainResource::SetTerrainSize(int32_t gridSizeX, int32_t gridSizeY, int32_t blockSize, int32_t tileSize, float texelDensity)
{
    auto resource = TypeCast<::cross::resource::TerrainResource>(mResHolder->mResource);
    resource->mTerrainInfo.mGridSizeX = gridSizeX;
    resource->mTerrainInfo.mGridSizeY = gridSizeY;
    resource->mTerrainInfo.mBlockSize = blockSize;
    resource->mTerrainInfo.mTileSize = tileSize;
    resource->mTerrainInfo.mTexelDensity = texelDensity;
}

void SDKTerrainResource::SetTerrainPath(const char* rootDataPath, const char* heightmapPrefix, const char* weightTexturePrefix, const char* materialPath, int32_t numLayers)
{
    auto resource = TypeCast<::cross::resource::TerrainResource>(mResHolder->mResource);
    resource->mTerrainInfo.mRootDataPath = rootDataPath;
    resource->mTerrainInfo.mHeightmapPrefix = heightmapPrefix;
    resource->mTerrainInfo.mWeightTexturePrefix = weightTexturePrefix;
    resource->mTerrainInfo.mMaterialPath = materialPath;
    for (auto i = 0; i != numLayers; i++)
    {
        resource->mTerrainInfo.mBaseColorTextures.emplace_back("EngineResource/Texture/DefaultWhiteGrid.nda");
    }
}

SDKInstancedStaticMeshResource::SDKInstancedStaticMeshResource(std::string_view path) : SDKResource(path, GetClassID())
{
    auto resource = TypeCast<::cross::resource::InstanceDataResource>(mResHolder->mResource);
}

uint64_t SDKInstancedStaticMeshResource::GetClassID()
{
    return ::cross::resource::InstanceDataResource::GetClassIDStatic();
}

void SDKInstancedStaticMeshResource::SetTransforms(const uint8_t* translation, const uint8_t* rotation, const uint8_t* scale, int32_t count)
{
    auto resource = TypeCast<::cross::resource::InstanceDataResource>(mResHolder->mResource);
    resource->SetInstanceCount(count);
    auto stride = sizeof(float) * 3;
    auto size = stride * count;
    resource->SetInstanceMemberData("Translation", 2, translation, size, stride);
    resource->SetInstanceMemberData("Rotation", 2, rotation, size, stride);
    resource->SetInstanceMemberData("Scale", 2, scale, size, stride);
}

void SDKInstancedStaticMeshResource::SetClusterNodes(const uint8_t* boundsMin, const uint8_t* boundsMax, const uint8_t* firstChild, const uint8_t* lastChild, const uint8_t* firstInstance, const uint8_t* lastInstance, int32_t count)
{
    auto resource = TypeCast<::cross::resource::InstanceDataResource>(mResHolder->mResource);
    resource->mClusterNodes.resize(count);
    for (int32_t i = 0; i != count; ++i)
    {
        auto& node = resource->mClusterNodes[i];
        node.BoundMin = *reinterpret_cast<const ::cross::Float3*>(boundsMin + i * sizeof(::cross::Float3));
        node.BoundMax = *reinterpret_cast<const ::cross::Float3*>(boundsMax + i * sizeof(::cross::Float3));
        node.FirstChild = *reinterpret_cast<const int32_t*>(firstChild + i * sizeof(int32_t));
        node.LastChild = *reinterpret_cast<const int32_t*>(lastChild + i * sizeof(int32_t));
        node.FirstInstance[0] = *reinterpret_cast<const int32_t*>(firstInstance + i * sizeof(int32_t));
        node.LastInstance[0] = *reinterpret_cast<const int32_t*>(lastInstance + i * sizeof(int32_t));
    }
}

uint64_t SDKAnimatorResource::GetClassID()
{
    return ::cross::anim::AnimatorRes::GetClassIDStatic();
}

void SDKAnimatorResource::SetDefault()
{
    std::string defaultStoryBoardNda = "EngineResource/Animation/Default.stb.nda";
    auto defaultRes = TypeCast<::cross::anim::AnimatorRes>(::gAssetStreamingManager->LoadSynchronously(defaultStoryBoardNda));
    auto animRes = TypeCast<::cross::anim::AnimatorRes>(mResHolder->mResource);
    ::cross::SerializeNode s;
    s["StoryBoard"] = defaultRes->GetStoryBoardContent().Clone();
    animRes->Deserialize(s);
}

void SDKAnimatorResource::SetStoryBoard(const char* json)
{
    std::string strJson(json);
    ::cross::DeserializeNode node = ::cross::DeserializeNode::ParseFromJson(strJson);

    auto animRes = TypeCast<::cross::anim::AnimatorRes>(mResHolder->mResource);

    animRes->Deserialize(node);
}

uint64_t SDKBlendSpaceResource::GetClassID()
{
    return ::cross::anim::AnimBlendSpaceRes::GetClassIDStatic();
}
void SDKBlendSpaceResource::Init(const char* json)
{
    ::cross::DeserializeNode node = ::cross::DeserializeNode::ParseFromJson(json);
    auto animRes = TypeCast<::cross::anim::AnimBlendSpaceRes>(mResHolder->mResource);
    animRes->Deserialize(node);
}

resource::SDKFxResource::SDKFxResource(std::string_view path)
{
    auto res = GetResourceManager().ResourceGet(path.data());
    if (res == nullptr)
    {
        mResHolder = GetResourceManager().CreateResourceHolder(GetClassID(), path.data());
        mPath = path;
    }
    else
    {
        mResHolder = std::move(res->mResHolder);
        mPath = std::move(res->mPath);

        auto* fxPtr = static_cast<::cross::resource::Fx*>(mResHolder->mResource.get());
        fxPtr->ResetResource();
    }
}

uint64_t SDKFxResource::GetClassID()
{
    return ::cross::resource::Fx::GetClassIDStatic();
}

void SDKFxResource::Init(const char* json)
{
    ::cross::DeserializeNode node = ::cross::DeserializeNode::ParseFromJson(json);
    auto fxRes = TypeCast<::cross::resource::Fx>(mResHolder->mResource);
    fxRes->Deserialize(node);
}

void SDKFxResource::AddReferenceResource(std::string resPath)
{
    mResHolder->mResource->AddReferenceResource(std::move(resPath));
}

resource::SDKMaterialFunctionResource::SDKMaterialFunctionResource(std::string_view path)
{
    auto res = GetResourceManager().ResourceGet(path.data());
    if (res == nullptr)
    {
        mResHolder = GetResourceManager().CreateResourceHolder(GetClassID(), path.data());
        mPath = path;
    }
    else
    {
        mResHolder = std::move(res->mResHolder);
        mPath = std::move(res->mPath);

        auto* materialFunctionPtr = static_cast<::cross::resource::MaterialFunction*>(mResHolder->mResource.get());
        materialFunctionPtr->ResetResource();
    }
}

uint64_t SDKMaterialFunctionResource::GetClassID()
{
    return ::cross::resource::MaterialFunction::GetClassIDStatic();
}

void SDKMaterialFunctionResource::Init(const char* json)
{
    ::cross::DeserializeNode node = ::cross::DeserializeNode::ParseFromJson(json);
    auto materialFunctionRes = TypeCast<::cross::resource::MaterialFunction>(mResHolder->mResource);
    materialFunctionRes->Deserialize(node);
}

void SDKMaterialFunctionResource::AddReferenceResource(std::string resPath)
{
    mResHolder->mResource->AddReferenceResource(std::move(resPath));
}

resource::SDKMaterialResource::SDKMaterialResource(std::string_view path)
{
    auto res = GetResourceManager().ResourceGet(path.data());
    if (res == nullptr)
    {
        mResHolder = GetResourceManager().CreateResourceHolder(GetClassID(), path.data());
        mPath = path;
    }
    else
    {
        mResHolder = std::move(res->mResHolder);
        mPath = std::move(res->mPath);

        auto* materialPtr = static_cast<::cross::resource::Material*>(mResHolder->mResource.get());
        materialPtr->ResetResource();
    }
}

uint64_t SDKMaterialResource::GetClassID()
{
    return ::cross::resource::Material::GetClassIDStatic();
}

void SDKMaterialResource::Init(const char* json)
{
    ::cross::DeserializeNode node = ::cross::DeserializeNode::ParseFromJson(json);
    auto materialRes = TypeCast<::cross::resource::Material>(mResHolder->mResource);
    materialRes->Deserialize(node);
}

void SDKMaterialResource::AddReferenceResource(std::string resPath)
{
    mResHolder->mResource->AddReferenceResource(std::move(resPath));
}

}   // namespace cesdk::resource
