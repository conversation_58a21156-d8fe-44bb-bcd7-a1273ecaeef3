#include <string>
#include <fstream>
#include <filesystem>
#include <algorithm>
#include <memory>
#include <sstream>
#include <iomanip>
#include "AssetPipeline/AssetExchange/Include/SDK.h"

#pragma warning(disable : 4244)

#include "MeshSimplifier.h"
#include "SimpVert.h"
#include "meshReduction/QudricSimplifierMeshReductionUsePtr.h"
#include "meshReduction/SkeletalReduction/SkeletalSimplifierVertex.h"
#include "meshReduction/SkeletalReduction/SkeletalSimplifierLinearAlgebra.h"
#include "meshReduction/SkeletalReduction/SkeletalSimplifierQuadrics.h"
#include "meshReduction/SkeletalReduction/SkeletalSimplifierMeshManager.h"
#include "meshReduction/SkeletalReduction/SkeletalMeshReductionSkinnedMesh.h"
#include "meshReduction/SkeletalReduction/SkeletalSimplifierQuadricCache.h"
#include "meshReduction/SkeletalReduction/SkeletalSimplifier.h"

#include "meshoptimizer/meshoptimizer.h"

// #define CE_MESH_SIMPLIFIER_DEBUG

#if 1
#    include <chrono>
#    define TICK(x) auto bench_##x = std::chrono::steady_clock::now();
#    define TOCK(x) std::cout << #    x ": " << std::chrono::duration_cast<std::chrono::duration<double>>(std::chrono::steady_clock::now() - bench_##x).count() << "s" << std::endl;
#else
#    define TICK(x)
#    define TOCK(x)
#endif

using namespace cross;
using namespace CrossSchema;
#include "memoryhooker/Module.h"
IMPORT_MODULE

std::shared_ptr<CEAssetExchange::IAssetExchangeSDK> CreateAssetExchangeSDKAutoPtr(const char* Staging)
{
    using namespace CEAssetExchange;
    return std::shared_ptr<IAssetExchangeSDK>{CreateAssetExchangeSDK(Staging, ""), [](IAssetExchangeSDK* This) { This->Release(); }};
}

namespace CEMeshSimplifier {

static constexpr uint32 MAX_UV = 8;
typedef TVertSimp<MAX_UV> VertType;
static constexpr int UEVerticesAtrributeCount = sizeof(VertType) / sizeof(float);

static void _MeshOptimize(std::vector<uint32_t>& ib, std::vector<VertType>& vb)
{
    meshopt_optimizeVertexCache(ib.data(), ib.data(), ib.size(), vb.size());
    meshopt_optimizeOverdraw(ib.data(), ib.data(), ib.size(), &vb[0].Position.x, vb.size(), sizeof(vb[0]), 1.05f);
    vb.resize(meshopt_optimizeVertexFetch(vb.data(), ib.data(), ib.size(), vb.data(), vb.size(), sizeof(vb[0])));
}

static void _MergeVertex(std::vector<uint32_t>& ib, std::vector<VertType>& vb, const FOverlappingThresholds t = {})
{
    std::unordered_map<uint32_t, uint32_t> hash;
    std::set<uint32_t, std::greater<uint32_t>> removeVbIdx;
    const int bits = CountZeroInDecimals(t.ThresholdPosition);

    for (int index = 0; index < ib.size(); index++)
    {
        const uint32_t vbIdx = ib[index];
        auto& vertex = vb[vbIdx];
        cross::Float3 PosKey;
        PosKey.x = Approximate(vertex.Position.x, bits);
        PosKey.y = Approximate(vertex.Position.y, bits);
        PosKey.z = Approximate(vertex.Position.z, bits);
        uint32_t hashKey = HashPosition(PosKey);

        if (hash.find(hashKey) == hash.end())
        {
            hash[hashKey] = vbIdx;
        }
        else if (hash[hashKey] != vbIdx && vb[hash[hashKey]].Equals(vb[vbIdx], t))
        {
            ib[index] = hash[hashKey];
            removeVbIdx.insert(vbIdx);
            // std::cout << vb[hash[hashKey]].ToString() << std::endl;
            // std::cout << vb[vbIdx].ToString() << std::endl;
        }
    }

    // MeshOptimize Will Do Such Work
    // for (const auto vbIdx : removeVbIdx)
    // {
    //     // std::cout << vbIdx << std::setw(5);
    //     vb.erase(vb.begin() + vbIdx);

    //     for (auto& idx : ib)
    //     {
    //         if (idx >= vbIdx)
    //             idx -= 1;
    //     }
    // }

    // remove degenerate triangle
    for (int idx = ib.size() - 1; idx >= 2; idx -= 3)
    {
        if (ib[idx] == ib[idx - 1] || ib[idx] == ib[idx - 2] || ib[idx - 1] == ib[idx - 2])
        {
            ib.erase(ib.begin() + idx - 2, ib.begin() + idx + 1);
        }
    }

    _MeshOptimize(ib, vb);
}

static void _SmoothNormal(std::vector<uint32_t>& ib, std::vector<VertType>& vb, const FOverlappingThresholds t = {}) 
{
    // smooth normal
    const int bits = CountZeroInDecimals(t.ThresholdPosition);
    std::unordered_map<uint32_t, std::vector<uint32_t>> vertHash;
    for (uint32_t i = 0; i < vb.size(); ++i)
    {
        auto& vertex = vb[i];
        vertex.Normal = Float3::Zero();

        uint32_t hashKey = HashPosition(vertex.Position, bits);
        vertHash[hashKey].push_back(i);
    }

    auto smoothFunc = [&vertHash, &vb, &bits](const cross::Float3& Position, Float3 normal) {
        uint32_t hashKey = HashPosition(Position, bits);
        if (vertHash.find(hashKey) != vertHash.end())
        {
            for (auto& vertIndex : vertHash[hashKey])
            {
                vb[vertIndex].Normal += normal;
            }
        }
    };

    uint32_t triangleCount = ib.size() / 3;
    for (uint32_t triIndex = 0; triIndex < triangleCount; triIndex++)
    {
        auto& v0 = vb[ib[3 * triIndex + 0]];
        auto& v1 = vb[ib[3 * triIndex + 2]];
        auto& v2 = vb[ib[3 * triIndex + 1]];

        Float3 normal = ((v2.Position - v0.Position).Cross(v1.Position - v0.Position)).Normalized();

        smoothFunc(v0.Position, normal);
        smoothFunc(v1.Position, normal);
        smoothFunc(v2.Position, normal);
    }

    for (int i = 0; i < vb.size(); ++i)
    {
        vb[i].Normal.Normalize();
    }
}

class SimplifyMO
{
public:
    std::vector<std::uint32_t> out_ib;
    std::vector<VertType> out_vb;

    float simplify(std::vector<uint32_t>& ib, std::vector<VertType>& vb, float threshold = 0.2f)
    {
        std::size_t target_index_count = static_cast<std::size_t>(ib.size() * threshold);
        float target_error = 1e-2f;
        float result_error = 0;

        out_ib.resize(ib.size());   // note: simplify needs space for index_count elements in the destination array, not target_index_count
        out_ib.resize(meshopt_simplify(&out_ib[0], &ib[0], ib.size(), &vb[0].Position.x, vb.size(), sizeof(vb[0]), target_index_count, target_error, 0, &result_error));
        // use sloppy methods if not satisfy criteria
        float radio = static_cast<float>(out_ib.size()) / ib.size();
        if (radio > 2.0f * threshold)
        {
            printf("use simplifySloppy because of %d triangles => %d triangles  \n", static_cast<int>(ib.size() / 3), static_cast<int>(out_ib.size() / 3));
            out_ib.resize(meshopt_simplifySloppy(&out_ib[0], &ib[0], ib.size(), &vb[0].Position.x, vb.size(), sizeof(vb[0]), target_index_count, target_error, &result_error));
        }

        out_vb.resize(out_ib.size() < vb.size() ? out_ib.size() : vb.size());   // note: this is just to reduce the cost of resize()
        out_vb.resize(meshopt_optimizeVertexFetch(&out_vb[0], &out_ib[0], out_ib.size(), &vb[0], vb.size(), sizeof(vb[0])));

        printf("%-9s: %d triangles => %d triangles (%.2f%% deviation) in \n", "SimplifyMO", static_cast<int>(ib.size() / 3), static_cast<int>(out_ib.size() / 3), result_error * 100);   //%.2f msec

        return result_error;
    }
};

static std::string logResult;

enum UEVerticesChannelStride : uint16_t
{
    PositionStride = cross::GetByteSize(cross::VertexFormat::Float3),
    NormalStride = cross::GetByteSize(cross::VertexFormat::Float3),
    TangentStride = cross::GetByteSize(cross::VertexFormat::Float3),
    BiTangentStride = cross::GetByteSize(cross::VertexFormat::Float3),
    ColorStride = cross::GetByteSize(cross::VertexFormat::Float4),
    UVStride = cross::GetByteSize(cross::VertexFormat::Float2),
};

enum ErrorCode : int
{
    FileNotExist = 1,
    OpenFileFailed,
    InvalidBinary,
    InvalidChannel,
    Any
};

void fillVertexBuffuer(std::vector<float>& vertices, int startIndex, uint16_t stride, const VertexChannelAssetDataT* chanel, int vertexIndex)
{
    if (!chanel)
        return;

    const int maxIndex = startIndex + stride / sizeof(float);

    auto writeFloat = [&](float v) {
        if (startIndex < maxIndex)
            vertices[startIndex++] = v;
    };

    auto writeUShort = [&](UInt16 v) {
        if (startIndex < maxIndex)
            SNormToFloat(v, vertices[startIndex++]);
    };

    auto writeShort = [&](SInt16 v) {
        if (startIndex < maxIndex)
            SNormToFloat(v, vertices[startIndex++]);
    };

    auto writeUChar = [&](UInt8 v) {
        if (startIndex < maxIndex)
            SNormToFloat(v, vertices[startIndex++]);
    };

    auto writeChar = [&](SInt8 v) {
        if (startIndex < maxIndex)
            SNormToFloat(v, vertices[startIndex++]);
    };

    switch (static_cast<cross::VertexFormat>(chanel->fdataformat))
    {
    case cross::VertexFormat::Float2:
    {
        const auto* float2P = reinterpret_cast<const float2*>(chanel->fdata.data() + vertexIndex * chanel->fstride);
        writeFloat(float2P->x()), writeFloat(float2P->y());
    }
    break;
    case cross::VertexFormat::Half2:
    {
        const auto* UInt16Vec2P = reinterpret_cast<const UShort2*>(chanel->fdata.data() + vertexIndex * chanel->fstride);
        cross::HalfToFloat(UInt16Vec2P->x, vertices[startIndex++]);
        cross::HalfToFloat(UInt16Vec2P->y, vertices[startIndex++]);
    }
    break;
    case cross::VertexFormat::Float3:
    {
        const auto* float3P = reinterpret_cast<const float3*>(chanel->fdata.data() + vertexIndex * chanel->fstride);
        writeFloat(float3P->x()), writeFloat(float3P->y()), writeFloat(float3P->z());
    }
    break;
    case cross::VertexFormat::UShort3_Norm:
    {
        const auto* ushort3P = reinterpret_cast<const UShort3*>(chanel->fdata.data() + vertexIndex * chanel->fstride);
        writeUShort(ushort3P->x), writeUShort(ushort3P->y), writeUShort(ushort3P->z);
    }
    break;
    case cross::VertexFormat::Short3_Norm:
    {
        const auto* short3P = reinterpret_cast<const Short3*>(chanel->fdata.data() + vertexIndex * chanel->fstride);
        writeShort(short3P->x), writeUShort(short3P->y), writeUShort(short3P->z);
    }
    break;
    case cross::VertexFormat::UByte3_Norm:
    {
        const auto* uchar3P = reinterpret_cast<const UChar3*>(chanel->fdata.data() + vertexIndex * chanel->fstride);
        writeUChar(uchar3P->x), writeUChar(uchar3P->y), writeUChar(uchar3P->z);
    }
    break;
    case cross::VertexFormat::Byte3_Norm:
    {
        const auto* char3P = reinterpret_cast<const Char3*>(chanel->fdata.data() + vertexIndex * chanel->fstride);
        writeChar(char3P->x), writeChar(char3P->y), writeChar(char3P->z);
    }
    break;
    case cross::VertexFormat::Float4:
    {
        const auto* float4P = reinterpret_cast<const float4*>(chanel->fdata.data() + vertexIndex * chanel->fstride);
        writeFloat(float4P->x()), writeFloat(float4P->y()), writeFloat(float4P->z()), writeFloat(float4P->w());
    }
    break;
    case cross::VertexFormat::UShort4_Norm:
    {
        const auto* ushort4P = reinterpret_cast<const UShort4*>(chanel->fdata.data() + vertexIndex * chanel->fstride);
        writeUShort(ushort4P->x), writeUShort(ushort4P->y), writeUShort(ushort4P->z), writeUShort(ushort4P->w);
    }
    break;
    case cross::VertexFormat::Short4_Norm:
    {
        const auto* short4P = reinterpret_cast<const Short4*>(chanel->fdata.data() + vertexIndex * chanel->fstride);
        writeShort(short4P->x), writeUShort(short4P->y), writeUShort(short4P->z), writeUShort(short4P->w);
    }
    break;
    case cross::VertexFormat::UByte4_Norm:
    {
        const auto* uchar4P = reinterpret_cast<const UChar4*>(chanel->fdata.data() + vertexIndex * chanel->fstride);
        writeUChar(uchar4P->x), writeUChar(uchar4P->y), writeUChar(uchar4P->z), writeUChar(uchar4P->w);
    }
    break;
    case cross::VertexFormat::Byte4_Norm:
    {
        const auto* char4P = reinterpret_cast<const Char4*>(chanel->fdata.data() + vertexIndex * chanel->fstride);
        writeChar(char4P->x), writeChar(char4P->y), writeChar(char4P->z), writeChar(char4P->w);
    }
    break;
    default:
        Assert(0);
        break;
    }
};

int fillInputMeshBuffer(InputMeshBuffer& meshBuffer)
{
    std::string logError;
    auto findVertexChannel = [](const ImportMeshAssetDataT* mesh, VertexChannel channel) -> VertexChannelAssetDataT* {
        auto& vertexChannelData = mesh->fvertexchanneldata;
        for (int i = 0; i < vertexChannelData.size(); i++)
        {
            using T = decltype(vertexChannelData[i].get()->fvertexchannel);
            if (vertexChannelData[i].get()->fvertexchannel == (T)channel)
                return vertexChannelData[i].get();
        }
        return nullptr;
    };

    auto fillSubMeshBuffers = [&](cross::VertexChannel channel, cross::VertexSemantic semantic, const VertexChannelAssetDataT** pp) {
        auto semanticMask = meshBuffer.mesh->fvertexchannelsemanticmask;
        auto vertextChannel = findVertexChannel(meshBuffer.mesh, channel);
        auto checkSemantic = [&]() { return !!(semanticMask & decltype(semanticMask)(semantic)); };
        auto checkDataSize = [&]() { return vertextChannel->fdata.size() / vertextChannel->fstride == meshBuffer.mesh->fvertexcount; };
        if (vertextChannel && checkSemantic() && checkDataSize())
            *pp = vertextChannel;
        else
        {
            meshBuffer.ignoredChannels.emplace_back(static_cast<CEAssetExchange::VertexChannel>(channel));
            return static_cast<int>(ErrorCode::InvalidChannel);
        }

        return 0;
    };

    int result = fillSubMeshBuffers(cross::VertexChannel::Position0, cross::VertexSemantic::SemanticPosition, &meshBuffer.position);

    if (result != 0)
    {
        LOG_ERROR("[MeshSimplifier]: Error, input file {}  has not a valid position chanel.", meshBuffer.sourceFile);
    }

    if (findVertexChannel(meshBuffer.mesh, cross::VertexChannel::Normal0))
    {
        fillSubMeshBuffers(cross::VertexChannel::Normal0, cross::VertexSemantic::SemanticNormal, &meshBuffer.normal);
        fillSubMeshBuffers(cross::VertexChannel::Tangent0, cross::VertexSemantic::SemanticTangent, &meshBuffer.tangent);
        fillSubMeshBuffers(cross::VertexChannel::BiNormal0, cross::VertexSemantic::SemanticBiNormal, &meshBuffer.bitangent);
    }
    else if (findVertexChannel(meshBuffer.mesh, cross::VertexChannel::QUATTAN0))
    {
        Assert(0);
        // fillSubMeshBuffers(cross::VertexChannel::BiNormal0, cross::VertexSemantic::SemanticBiNormal, &meshBuffer.quatTangent);
    }

    fillSubMeshBuffers(cross::VertexChannel::Color0, cross::VertexSemantic::SemanticColor, &meshBuffer.color);

    meshBuffer.uvs.resize(MAX_UV);
    for (auto i = 0; i < meshBuffer.uvs.size(); i++)
    {
        fillSubMeshBuffers(cross::VertexChannel::TexCoord0 + i, cross::VertexSemantic::SemanticTexCoord, &meshBuffer.uvs[i]);
    }

    meshBuffer.indices = meshBuffer.mesh->findexstream.get();
    return result;
}

void fillSubMeshParams(InputMeshBuffer& inputMesh, const int subMeshIndex, InputSubMeshBuffer* outSubMesh, bool noReusedVert)
{
    Assert(inputMesh.mesh);
    Assert(subMeshIndex >= 0 && subMeshIndex < inputMesh.mesh->fmeshpartinfo.size());
    outSubMesh->subMeshIndex = subMeshIndex;
    auto& vertices = outSubMesh->vertices;
    auto& indices = outSubMesh->indices;
    auto& materialGroup = outSubMesh->materialGroup;

    const auto* meshPart = inputMesh.mesh->fmeshpartinfo[subMeshIndex].get();
    outSubMesh->originalVertexCount = meshPart->fvertexcount;
    outSubMesh->originalTriangleCount = meshPart->findexcount / 3;

    const auto indicesCount = meshPart->findexcount;
    indices.resize(indicesCount);
    materialGroup.resize(indicesCount / 3, 0);

    if (inputMesh.indices->fis16bitindex)
    {
        const auto* head = reinterpret_cast<const std::uint16_t*>(inputMesh.indices->fdata.data()) + meshPart->findexstart;
        for (std::size_t i = 0; i < indicesCount; i++)
        {
            indices[i] = *(head + i);
            Assert(indices[i] >= 0 && indices[i] < static_cast<int>(meshPart->fvertexcount));
        }
    }
    else
    {
        const auto* head = reinterpret_cast<const int*>(inputMesh.indices->fdata.data()) + meshPart->findexstart;
        memcpy(indices.data(), head, indicesCount * sizeof(int));
    }

    auto fillVertexBuffuers = [&](auto index, auto vertexIndex) {
        auto offset = index * UEVerticesAtrributeCount;
        fillVertexBuffuer(vertices, offset, UEVerticesChannelStride::PositionStride, inputMesh.position, vertexIndex);
        offset += UEVerticesChannelStride::PositionStride / sizeof(float);
        fillVertexBuffuer(vertices, offset, UEVerticesChannelStride::NormalStride, inputMesh.normal, vertexIndex);
        offset += UEVerticesChannelStride::NormalStride / sizeof(float);
        fillVertexBuffuer(vertices, offset, UEVerticesChannelStride::TangentStride, inputMesh.tangent, vertexIndex);
        offset += UEVerticesChannelStride::TangentStride / sizeof(float);
        fillVertexBuffuer(vertices, offset, UEVerticesChannelStride::BiTangentStride, inputMesh.bitangent, vertexIndex);
        offset += UEVerticesChannelStride::BiTangentStride / sizeof(float);
        fillVertexBuffuer(vertices, offset, UEVerticesChannelStride::ColorStride, inputMesh.color, vertexIndex);
        offset += UEVerticesChannelStride::ColorStride / sizeof(float);

        for (auto i = 0; i < MAX_UV; i++)
        {
            fillVertexBuffuer(vertices, offset, UEVerticesChannelStride::UVStride, inputMesh.uvs[i], vertexIndex);
            offset += UEVerticesChannelStride::UVStride / sizeof(float);
        }
    };

    if (noReusedVert)
    {
        // no reused vertex
        const auto vertexCount = indices.size();
        vertices.resize(vertexCount * UEVerticesAtrributeCount);

        for (size_t index = 0; index < vertexCount; index++)
        {
            std::uint32_t vertexIndex = indices[index] + meshPart->fvertexstart;
            fillVertexBuffuers(index, vertexIndex);
            indices[index] = index;
        }
    }
    else
    {
        const auto vertexCount = meshPart->fvertexcount;
        vertices.resize(vertexCount * UEVerticesAtrributeCount);

        for (size_t index = 0; index < vertexCount; index++)
        {
            std::uint32_t vertexIndex = index + meshPart->fvertexstart;
            fillVertexBuffuers(index, vertexIndex);
        }
    }

#ifdef CE_MESH_SIMPLIFIER_DEBUG   // for debug input mesh
    {
        using namespace CEAssetExchange;
        std::string extension = ".submesh" + std::to_string(outSubMesh->subMeshIndex) + ".debug.nda";
        std::string debugFile = std::filesystem::path{inputMesh.sourceFile}.replace_extension(extension).string();

        IAssetExchangeSDK* sdk = CreateAssetExchangeSDK("");
        IMeshAssemble* meshAssemble = sdk->CreateMeshAssemble(debugFile.c_str());
        meshAssemble->AddIndexStream(reinterpret_cast<std::uint32_t*>(indices.data()), indices.size());
        MeshVertexBuffer vb;
        vb.mLayout = VertexBufferLayout::PF3_NF3_TF3_BiTF3_CF4_UVF2;
        vb.mData = reinterpret_cast<char*>(vertices.data());
        vb.mUVCount = MAX_UV;
        vb.mDataSize = vertices.size() * sizeof(float);
        vb.mIgnoreChannels = inputMesh.ignoredChannels.data();
        vb.mIgnoreChannelsNum = inputMesh.ignoredChannels.size();
        meshAssemble->AddVertexBuffer(&vb);
        sdk->Release();
    }
#endif
}

void normalizePath(MeshReduceInfo2& info, const std::filesystem::path& staging)
{
    std::string root = staging.empty() ? "" : (staging.string() + "/");
    PathHelper::Normalize(root);

    std::string inputFile = info.mMesh;

    if (!inputFile.empty())
        inputFile = root + inputFile;

    std::string outputFile = info.mOutPutMesh;

    if (!outputFile.empty())
        outputFile = root + outputFile;

    PathHelper::Normalize(inputFile);
    PathHelper::Normalize(outputFile);

    auto input = std::filesystem::path{inputFile};
    auto output = std::filesystem::path{outputFile};

    if (!std::filesystem::exists(output))
    {
        std::string rate = ".lod";

        if (info.mLODs.size() == 0)
        {
            constexpr const auto Reserve = 3;
            rate = std::to_string(info.mPercentTriangles);

            if (rate.length() < Reserve)
                rate += "000";

            const auto dotP = rate.find_last_of('.');

            if (dotP != std::string::npos)
                rate = rate.substr(0, dotP + Reserve);
        }

        output = std::filesystem::path{inputFile}.replace_extension(".simplify" + rate + input.extension().string());
    }

    info.mMeshFullPath = input.string();
    info.mOutPutMeshFullPath = output.string();
}

void toMeshReduceInfo2(const MeshReduceInfo* info, const std::filesystem::path& staging, MeshReduceInfo2& out)
{
    Assert(info->mInputFile);
    out.mMesh = info->mInputFile;
    out.mOutPutMesh = info->mOutputFile ? info->mOutputFile : info->mInputFile;
    out.mPercentTriangles = info->mPercentTriangles;
    out.mPercentVertices = info->mPercentVertices;
    out.mKeepOriginalMeshLOD0 = info->mKeepOriginalMeshLOD0;

    if (out.mOutPutMesh.empty())
        out.mOutPutMesh = info->mInputFile;

    PathHelper::Normalize(out.mMesh);
    PathHelper::Normalize(out.mOutPutMesh);

    for (std::uint32_t i = 0; i < info->mLODsNum; i++)
    {
        const MeshLODInfo* inLOD = info->mLODs + i;

        MeshLODInfo2 LOD{};
        LOD.mPercentTriangles = inLOD->mPercentTriangles;
        LOD.mPercentVertices = inLOD->mPercentVertices;
        LOD.mSubMeshIndices.resize(inLOD->mSubMeshIndicesNum);
        Assert(LOD.mPercentVertices >= 0.f && LOD.mPercentVertices <= 1.f);
        Assert(LOD.mPercentTriangles >= 0.f && LOD.mPercentTriangles <= 1.f);
        LOD.mPercentVertices = MathUtils::Clamp(LOD.mPercentVertices, 0.f, 1.0f);
        LOD.mPercentTriangles = MathUtils::Clamp(LOD.mPercentTriangles, 0.01f, 0.99f);
        memcpy(LOD.mSubMeshIndices.data(), inLOD->mSubMeshIndices, sizeof(std::uint32_t) * inLOD->mSubMeshIndicesNum);
        out.mLODs.emplace_back(std::move(LOD));
    }

    normalizePath(out, staging);

    if (out.mLODs.empty())
    {
        MeshLODInfo2 LOD{};
        LOD.mPercentTriangles = out.mPercentTriangles;
        LOD.mPercentVertices = out.mPercentVertices;
        out.mLODs.emplace_back(std::move(LOD));
    }
}

int getMeshDataFromPath(std::filesystem::path inputFile, std::vector<char>& buffer, std::string& GUID)
{
    if (!std::filesystem::exists(inputFile))
    {
        LOG_ERROR("[MeshSimplifier]: Error, failed to open file {}", inputFile.string());
        return ErrorCode::FileNotExist;
    }

    std::ifstream infile;
    infile.open(inputFile, std::ios::binary | std::ios::in);

    if (infile.is_open())
    {
        cross::resource::LoadNDAFileInfo ndaInfo;
        if (!gResourceAssetMgr.GetLoadNDAInfo(inputFile.string().c_str(), ndaInfo))
        {
            LOG_ERROR("[MeshSimplifier]: Error, failed to get file meta info {}", inputFile.string());
            return ErrorCode::Any;
        }

        if (ndaInfo.HasMetaHeader())
        {
            GUID = ndaInfo.GetMetaHeader().mGuid;
        }

        infile.seekg(0, std::ios::end);
        const auto bufSize = infile.tellg();
        const auto headerSize = ndaInfo.HasMetaHeader() ? ndaInfo.GetMetaHeader().mJsonStringLength : 0;
        const auto dataSize = bufSize - std::streampos{headerSize};

        buffer.resize(dataSize);

        infile.seekg(headerSize, std::ios::beg);
        infile.read(buffer.data(), dataSize);
        infile.close();
    }
    else
    {
        LOG_ERROR("[MeshSimplifier]: Error, failed to open file {}", inputFile.string());
        return ErrorCode::OpenFileFailed;
    }

    return 0;
}

void MeshSimplifier::SetMergeVertexThresholds(const MergeVertexThresholds* m)
{
    if (m)
        memcpy(&mMergeVertexThresholds, m, sizeof(MergeVertexThresholds));
}

int MeshSimplifier::MeshReduce(const MeshReduceInfo* info)
{
    logResult = "";
    TICK(MeshReduce_Task);
    MeshReduceInfo2 info2;
    toMeshReduceInfo2(info, mStaging, info2);
    int result = GenerateMeshLOD(info2);
    TOCK(MeshReduce_Task);
    return result;
}

int MeshSimplifier::SubMeshReduce(const InputSubMeshParams& params, const InputSubMeshBuffer& subMesh, OutSubMeshData* output)
{
    TICK(SubMeshReduce_Task);

    int newVertexCount = 0;
    int newFaceCount = 0;

    std::vector<float> vertices = subMesh.vertices;
    std::vector<uint32_t> indices = subMesh.indices;

    if (mEngine == EReduceEngine::UE)
    {
        std::vector<uint32_t> materialGroup = subMesh.materialGroup;
        std::vector<float> faceWeight;
        const auto vertexCount = indices.size();
        const auto indicesCount = indices.size();
        faceWeight.resize(indicesCount / 3, 1.f);

        int reducerIndex = 0;

        {
            std::lock_guard guard{mMutex};
            mReducers.emplace_back();
            reducerIndex = mReducers.size() - 1;
        }

        mReducers[reducerIndex].ReduceMeshDescription(vertices.data(),
                                                      vertexCount,
                                                      reinterpret_cast<int*>(indices.data()),
                                                      indicesCount / 3,
                                                      reinterpret_cast<int*>(materialGroup.data()),
                                                      faceWeight.data(),
                                                      &newVertexCount,
                                                      &newFaceCount,
                                                      params.percentTriangles,
                                                      UEVerticesAtrributeCount,
                                                      params.percentVertices,
                                                      MAX_UV);

        Assert(newFaceCount != 0 && newVertexCount != 0);
        output->indexCount = newFaceCount * 3;
        output->vertexCount = newVertexCount;
        output->indexBuffer = reinterpret_cast<std::uint32_t*>(mReducers[reducerIndex].GetIndices());
        output->vertexBuffer = mReducers[reducerIndex].GetVert();
    }
    else
    {
        static SimplifyMO mos;
        mos.simplify(indices, *reinterpret_cast<std::vector<VertType>*>(&vertices), params.percentTriangles);

        newFaceCount = mos.out_ib.size() / 3;
        newVertexCount = mos.out_vb.size();

        output->indexCount = newFaceCount * 3;
        output->vertexCount = newVertexCount;
        output->indexBuffer = mos.out_ib.data();
        output->vertexBuffer = reinterpret_cast<float*>(mos.out_vb.data());
    }

    TOCK(SubMeshReduce_Task);
    LOG_INFO("[MeshSimplifier]: Reduce SubMesh {} Finish, vertex: {} -> {} , triangle: {} -> {}", subMesh.subMeshIndex, subMesh.originalVertexCount, newVertexCount, subMesh.originalTriangleCount, newFaceCount);
    return 0;
}

int MeshSimplifier::GenerateMeshLOD(MeshReduceInfo2& info)
{
    Assert(info.mLODs.size());

    using namespace CEAssetExchange;
    auto sdk = CreateAssetExchangeSDKAutoPtr(mStaging.string().c_str());
    std::shared_ptr<ImportMeshAssetDataT> meshDataT = std::make_shared<ImportMeshAssetDataT>();
    IMeshAssemble* meshAssemble = nullptr;
    InputMeshBuffer meshBuffer{};
    std::uint32_t firstVertex = 0, firstIndex = 0;
    {
        std::string GUID = "";
        std::vector<char> buffer;
        int result = getMeshDataFromPath(info.mMeshFullPath, buffer, GUID);

        if (result != 0)
            return result;

        flatbuffers::Verifier verifier(reinterpret_cast<UInt8*>(buffer.data()), buffer.size());
        if (CrossSchema::VerifyResourceAssetBuffer(verifier))
        {
            auto* res = CrossSchema::GetResourceAsset(buffer.data());
            res->resource_as_ImportMeshAssetData()->UnPackTo(meshDataT.get());
        }
        else
        {
            LOG_ERROR("[MeshSimplifier]: Error, input file {} was not a valid mesh asset (flatbuffers).", info.mMesh);
            return ErrorCode::InvalidBinary;
        }

        meshBuffer.mesh = meshDataT.get();
        meshBuffer.sourceFile = info.mMesh.c_str();
        result = fillInputMeshBuffer(meshBuffer);

        if (result != 0)
            return result;

        if (info.mOutPutMesh == info.mMesh)
            sdk->AddResourceGUID(info.mOutPutMesh.c_str(), GUID.c_str());

        meshAssemble = sdk->CreateMeshAssemble(info.mOutPutMesh.c_str());

        if (info.mKeepOriginalMeshLOD0)
        {
            meshAssemble->SetRawFlatBuffers(reinterpret_cast<std::uint8_t*>(buffer.data()), buffer.size());

            if (meshAssemble->GetLODCount() > 1)
                meshAssemble->RemoveLODRange(1, meshAssemble->GetLODCount());

            firstVertex = meshAssemble->GetLODVertexCount(0);
            firstIndex = meshAssemble->GetLODIndexCount(0);
        }
    }

    LOG_INFO("[MeshSimplifier]: Start reduce mesh {}\n", info.mMeshFullPath.c_str());

    std::vector<int> LOD0Indices;
    {
        std::shared_ptr<ImportMeshAssetDataT> meshDataTLOD0 = std::make_shared<ImportMeshAssetDataT>();
        const int LOD0first = meshDataT->fmeshpartlodstartindex.size() > 0 ? meshDataT->fmeshpartlodstartindex[0] : 0;
        const int LOD1first = meshDataT->fmeshpartlodstartindex.size() > 1 ? meshDataT->fmeshpartlodstartindex[1] : meshDataT->fmeshpartinfo.size();
        Assert(LOD1first > LOD0first);
        for (int index = LOD0first; index < LOD1first; ++index)
        {
            LOD0Indices.emplace_back(index);
        }
    }

    std::vector<MeshSection> sections;
    std::map<int, std::unique_ptr<InputSubMeshBuffer>> inputSubMeshCache;
    std::vector<std::uint32_t> firstIndicesLOD;
    firstIndicesLOD.emplace_back(0);

    for (int lod = 0; lod < info.mLODs.size(); lod++)
    {
        TICK(GenerateLOD_Task);

        if (firstIndicesLOD.back() != sections.size())
            firstIndicesLOD.emplace_back(sections.size());

        const MeshLODInfo2& mLOD = info.mLODs[lod];
        std::vector<int> subMeshInThisLOD;

        if (mLOD.mSubMeshIndices.size() == 0)
        {
            subMeshInThisLOD = LOD0Indices;
        }
        else
        {
            for (int i = 0; i < mLOD.mSubMeshIndices.size(); i++)
                subMeshInThisLOD.emplace_back(mLOD.mSubMeshIndices[i]);
        }

        std::sort(subMeshInThisLOD.begin(), subMeshInThisLOD.end(), [](auto& a, auto& b) { return a < b; });
        for (int index = 0; index < subMeshInThisLOD.size(); index++)
        {
            if (inputSubMeshCache.find(index) == inputSubMeshCache.end())
            {
                inputSubMeshCache[index] = std::make_unique<InputSubMeshBuffer>();
                fillSubMeshParams(meshBuffer, subMeshInThisLOD[index], inputSubMeshCache[index].get(), mEngine == EReduceEngine::UE);
            }

            OutSubMeshData out{};
            InputSubMeshBuffer* subMeshBuffer = inputSubMeshCache[index].get();
            InputSubMeshParams subMeshParams{mLOD.mPercentTriangles, mLOD.mPercentVertices};
            SubMeshReduce(subMeshParams, *subMeshBuffer, &out);

            if (out.indexCount > 0 && out.vertexCount > 0)
            {
                std::vector<uint32_t> mergedIB;
                std::vector<VertType> mergedVB;
                mergedIB.resize(out.indexCount);
                mergedVB.resize(out.vertexCount);
                memcpy(mergedIB.data(), out.indexBuffer, static_cast<uintmax_t>(out.indexCount) * sizeof(uint32_t));
                memcpy(mergedVB.data(), out.vertexBuffer, static_cast<uintmax_t>(out.vertexCount) * sizeof(VertType));

                FOverlappingThresholds thresholds;
                thresholds.ThresholdUV = mMergeVertexThresholds.ThresholdUV;
                thresholds.ThresholdColor = mMergeVertexThresholds.ThresholdColor;
                thresholds.ThresholdPosition = mMergeVertexThresholds.ThresholdPosition;
                thresholds.ThresholdTangentNormal = mMergeVertexThresholds.ThresholdTangentNormal;

                _MergeVertex(mergedIB, mergedVB, thresholds);

                LOG_INFO("[MeshOptimizer]: Merge Vertex {} Finish, vertex: {} -> {} , triangle: {} -> {}", index, out.vertexCount, mergedVB.size(), out.indexCount / 3, mergedIB.size() / 3);

                MeshVertexBuffer vb{};
                vb.mLayout = VertexBufferLayout::PF3_NF3_TF3_BiTF3_CF4_UVF2;
                vb.mUVCount = MAX_UV;
                vb.mData = reinterpret_cast<char*>(mergedVB.data());
                vb.mDataSize = static_cast<uintmax_t>(mergedVB.size() * sizeof(VertType));
                vb.mIgnoreChannels = meshBuffer.ignoredChannels.data();
                vb.mIgnoreChannelsNum = meshBuffer.ignoredChannels.size();
                meshAssemble->AddVertexBuffer(&vb);
                meshAssemble->AddIndexStream(mergedIB.data(), mergedIB.size());

                MeshSection& section = sections.emplace_back();
                section.mMinVertexIndex = firstVertex;
                section.mFirstIndex = firstIndex;

                if (sections.size() > 1)
                {
                    const MeshSection& oldLast = sections[sections.size() - 2];
                    section.mMinVertexIndex = oldLast.mMaxVertexIndex + 1;
                    section.mFirstIndex = oldLast.mFirstIndex + oldLast.mNumTriangles * 3;
                }

                section.mMaxVertexIndex = section.mMinVertexIndex + mergedVB.size() - 1;
                section.mNumTriangles = mergedIB.size() / 3;

                const auto materialIndex = meshBuffer.mesh->fmeshpartinfo[index]->fmaterialindex;
                const auto& materialNames = meshBuffer.mesh->fmaterialnames;

                if (materialIndex >= 0 && materialIndex < materialNames.size())
                    section.mMaterial = materialNames[materialIndex].c_str();
            }

            mReducers.clear();
        }

        LOG_INFO("[MeshSimplifier]: Gen LOD {} Finish, faceRate {}, vertRate {}", info.mKeepOriginalMeshLOD0 ? lod + 1 : lod, mLOD.mPercentTriangles, mLOD.mPercentVertices);
        TOCK(GenerateLOD_Task);
    }

    MeshLODs meshLODs;
    meshLODs.mSections = sections.data();
    meshLODs.mSectionsNum = sections.size();
    meshLODs.mLODFirstIndices = firstIndicesLOD.data();
    meshLODs.mLODFirstIndicesNum = firstIndicesLOD.size();
    meshAssemble->AddMeshLODs(&meshLODs);

    return 0;
}

void normalizePath(MeshOptimizeInfo2& info, const std::filesystem::path& staging)
{
    std::string root = staging.empty() ? "" : (staging.string() + "/");
    PathHelper::Normalize(root);

    std::string inputFile = info.mMesh;

    if (!inputFile.empty())
        inputFile = root + inputFile;

    std::string outputFile = info.mOutPutMesh;

    if (!outputFile.empty())
        outputFile = root + outputFile;

    PathHelper::Normalize(inputFile);
    PathHelper::Normalize(outputFile);

    auto input = std::filesystem::path{inputFile};
    auto output = std::filesystem::path{outputFile};

    info.mMeshFullPath = input.string();
    info.mOutPutMeshFullPath = output.string();
}

void toMeshOptimizeInfo2(const MeshOptimizeInfo* info, const std::filesystem::path& staging, MeshOptimizeInfo2& out)
{
    Assert(info->mInputFile);
    out.mMesh = info->mInputFile;
    out.mOutPutMesh = info->mOutputFile ? info->mOutputFile : info->mInputFile;

    if (out.mOutPutMesh.empty())
        out.mOutPutMesh = info->mInputFile;

    PathHelper::Normalize(out.mMesh);
    PathHelper::Normalize(out.mOutPutMesh);

    normalizePath(out, staging);
}

int MeshSimplifier::MeshOptimize(const MeshOptimizeInfo* _info)
{
    logResult = "";
    TICK(MeshOptimize_Task);
    MeshOptimizeInfo2 info{};
    toMeshOptimizeInfo2(_info, mStaging, info);

    using namespace CEAssetExchange;
    auto sdk = CreateAssetExchangeSDKAutoPtr(mStaging.string().c_str());

    IMeshAssemble* meshAssemble = nullptr;

    std::string GUID = "";
    std::shared_ptr<ImportMeshAssetDataT> meshDataT = std::make_shared<ImportMeshAssetDataT>();
    {
        std::vector<char> buffer;
        int result = getMeshDataFromPath(info.mMeshFullPath, buffer, GUID);

        if (result != 0)
            return result;

        flatbuffers::Verifier verifier(reinterpret_cast<UInt8*>(buffer.data()), buffer.size());
        if (CrossSchema::VerifyResourceAssetBuffer(verifier))
        {
            auto* res = CrossSchema::GetResourceAsset(buffer.data());
            res->resource_as_ImportMeshAssetData()->UnPackTo(meshDataT.get());

            if (info.mOutPutMesh == info.mMesh)
                sdk->AddResourceGUID(info.mOutPutMesh.c_str(), GUID.c_str());

            meshAssemble = sdk->CreateMeshAssemble(info.mOutPutMesh.c_str());
            meshAssemble->SetRawFlatBuffers(reinterpret_cast<std::uint8_t*>(buffer.data()), buffer.size());
        }
        else
        {
            LOG_ERROR("[MeshSimplifier]: Error, input file {} was not a valid mesh asset (flatbuffers).", info.mMesh);
            return ErrorCode::InvalidBinary;
        }
    }

    InputMeshBuffer meshBuffer{};
    meshBuffer.mesh = meshDataT.get();
    meshBuffer.sourceFile = info.mMesh.c_str();
    if (int Result = fillInputMeshBuffer(meshBuffer))
    {
        return Result;   // errors happen
    }

    LOG_INFO("[MeshSimplifier]: Start optimize mesh {}\n", info.mMeshFullPath.c_str());

    // Optimize LOD

    std::vector<InputSubMeshBuffer> optimizedSubMesh;

    for (std::uint32_t LOD = 0; LOD < meshAssemble->GetLODCount(); LOD++)
    {
        std::uint32_t beginMeshPart = 0;
        std::uint32_t endMeshPart = 0;
        meshAssemble->GetLODMeshPartRange(LOD, &beginMeshPart, &endMeshPart);

        for (auto i = beginMeshPart; i < endMeshPart; i++)
        {
            LOG_INFO("[MeshOptimizer]: Optimize Mesh Part {}", i);

            optimizedSubMesh.emplace_back(InputSubMeshBuffer{});
            InputSubMeshBuffer* subMesh = &optimizedSubMesh.back();
            fillSubMeshParams(meshBuffer, i, subMesh, false);

            std::vector<uint32_t>& mergedIB = subMesh->indices;
            std::vector<VertType>& mergedVB = *reinterpret_cast<std::vector<VertType>*>(&subMesh->vertices);

            // mesh
            FOverlappingThresholds thresholds;
            thresholds.ThresholdUV = mMergeVertexThresholds.ThresholdUV;
            thresholds.ThresholdColor = mMergeVertexThresholds.ThresholdColor;
            thresholds.ThresholdPosition = mMergeVertexThresholds.ThresholdPosition;
            thresholds.ThresholdTangentNormal = mMergeVertexThresholds.ThresholdTangentNormal;

            _MergeVertex(mergedIB, mergedVB, thresholds);
            if (_info->mSmoothNormal)
            {
                _SmoothNormal(mergedIB, mergedVB, thresholds);
            }

            LOG_INFO("[MeshOptimizer]: Merge Vertex {} Finish, vertex: {} -> {} , triangle: {} -> {}\n", i, subMesh->originalVertexCount, mergedVB.size(), subMesh->originalTriangleCount, mergedIB.size() / 3);
        }
    }

    // Update Submesh

    for (auto& MeshPart : optimizedSubMesh)
    {
        InputSubMeshBuffer& submesh = MeshPart;

        MeshFlatBuffer mfb{};
        mfb.mIndex = submesh.indices.data();
        mfb.mIndexNum = submesh.indices.size();

        MeshVertexBuffer mvb;
        mvb.mLayout = VertexBufferLayout::PF3_NF3_TF3_BiTF3_CF4_UVF2;
        mvb.mUVCount = MAX_UV;
        mvb.mData = reinterpret_cast<char*>(submesh.vertices.data());
        mvb.mDataSize = static_cast<uintmax_t>(submesh.vertices.size() * sizeof(float));
        mvb.mIgnoreChannels = meshBuffer.ignoredChannels.data();
        mvb.mIgnoreChannelsNum = meshBuffer.ignoredChannels.size();

        mfb.mFlatVertexBuffer = &mvb;

        meshAssemble->UpdateMeshPart(submesh.subMeshIndex, &mfb);
    }

    meshAssemble->EndAssemble();
    meshAssemble->SaveToFile();

    TOCK(MeshOptimize_Task);
    return 0;
}

std::string MeshSimplifier::GetResultLog()
{
    return logResult;
}

CEMeshSimplifier::IMeshSimplifier* CreateMeshSimplifierSDK(const char* staging)
{
    return new MeshSimplifier(staging);
}

}   // namespace CEMeshSimplifier

#undef CE_MESH_SIMPLIFIER_DEBUG
