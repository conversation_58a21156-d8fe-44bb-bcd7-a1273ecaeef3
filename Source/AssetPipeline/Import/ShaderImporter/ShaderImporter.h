#pragma once
#include "AssetPipeline/Import/AssetImporter.h"
#include "AssetPipeline/Import/ShaderImporter/ShaderImportSettings.h"
#include "AssetPipeline/Import/ShaderImporter/IncludeHandler.h"
#include "CrossSchema/ShaderAsset_generated.h"
#include <vector>
#include <tuple>

#include "Resource/Shader.h"

namespace cross::editor {


class ShaderImporter : public AssetImporter
{
public:
    ShaderImporter(AssetType type);
    inline const static std::unordered_map<std::string_view, std::tuple<CrossSchema::ShaderVersion, std::vector<std::wstring>>> mShaderPlatform{
    {"d3d12",
     {
         {CrossSchema::ShaderCodeFormat::DXIL, 6, 6},
         {L"CROSS_NGI_D3D12"},
     }},
    {"vulkan",
     {
         {CrossSchema::ShaderCodeFormat::SPIR_V, 1, 4},
         {L"CROSS_NGI_VULKAN"},
     }},
    {"gles",
     {
         {CrossSchema::ShaderCodeFormat::ESSL, 3, 1},
         {L"CROSS_NGI_GLES3"},
     }},
    {"metal_ios",
     {
         {CrossSchema::ShaderCodeFormat::MSL_IOS, 2, 2},
         {L"CROSS_NGI_METAL_IOS"},
     }},
    {"metal_osx",
     {
         {CrossSchema::ShaderCodeFormat::MSL_OSX, 2, 2},
         {L"CROSS_NGI_METAL_OSX"},
     }},
    };
    
    using ShaderStageCodePtr = std::unique_ptr<CrossSchema::ShaderCodeT>(CrossSchema::GraphicsShaderCodeT::*);

    struct ShaderCompileCommand
    {
        struct UsageInfo
        {
            MaterialUsage Usage;
            std::vector<std::string> Keywords;
        };

        std::vector<std::tuple<CrossSchema::ShaderVersion, std::vector<std::wstring>>> curShaderFormats;
        std::vector<std::tuple<CrossSchema::ShaderStageBit, std::string, std::wstring>> stages;
        std::vector<std::string> keywords;
        std::vector<UsageInfo> usages;
        DebugMode debugMode;
        bool onDemandCompilation;
    };

    static void ParseShaderCompileCommand(std::shared_ptr<std::string> originalShaderStr, ShaderCompileCommand& cmd);
    static void ParseShaderCompileCommand(const std::string& originalShaderStr, ShaderCompileCommand& cmd);

protected:
    ShaderImportSettings* mImportSettings;
 };


class ASSET_API GraphicsShaderImporter : public ShaderImporter
{
public:
    GraphicsShaderImporter();

    void ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath, ImportSetting* setting) override {};

    threading::TaskEventPtr ImportAssetAsync(const std::string& assetFilename, const std::string& ndaSavePath = "") override;

    threading::TaskEventPtr CompileShaderAsync(const std::string& assetFilename, std::vector<UInt8>& outShaderBinary);

private:
    std::shared_ptr<threading::TaskEventArray> CompileShaderAsyncInner(const std::string& assetFilename, std::shared_ptr<IncludeHandler>& outHeaderDataBase, std::shared_ptr<CrossSchema::GraphicsShaderAssetT>& outGraphicsShaderAssetT);

    bool GenVariant(CrossSchema::GraphicsShaderCodeT* shaderVariant, const std::time_t mtime,
        const std::string& shaderCode, const CrossSchema::ShaderVersion&, 
        const std::vector<std::wstring>& defines, UInt64 keywordMask,
        const std::vector<std::tuple<CrossSchema::ShaderStageBit, std::string, std::wstring>>& stages,
        IncludeHandler*, DebugMode debugMode = DebugMode::None
    );

    bool CheckAssetName(const char* name) const override
    {
        return HasExtension(name, ".shader");
    }
};

class ASSET_API ComputeShaderImporter : public ShaderImporter
{
public:
    ComputeShaderImporter();

    void ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath, ImportSetting* setting) override{};
    threading::TaskEventPtr ImportAssetAsync(const std::string& assetFilename, const std::string& ndaSavePath = "") override;

    void ImportAssetOnDemand(const std::string& sourcePath, const std::string& ndaSavePath);
    void GenShaderOnDemand(const std::string& code, const std::string& ndaSavePath);

    bool GenKernels(CrossSchema::PlatformComputeShaderT* shader, const std::time_t mtime, 
        const std::string& shaderCode, const CrossSchema::ShaderVersion&, 
        const std::vector<std::tuple<CrossSchema::ShaderStageBit, std::string, std::wstring>> & entryPoints,
        IncludeHandler*, const DebugMode debugSymbol = DebugMode::None
    );

private:
    bool CheckAssetName(const char* name) const override
    {
        return HasExtension(name, ".compute");
    }
};

class ASSET_API RayTracingShaderImporter : public ShaderImporter
{
public:
    RayTracingShaderImporter();

    void ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath, ImportSetting* setting) override{}
    
    void ImportAssetOnDemand(const std::string& sourcePath, const std::string& ndaSavePath);
    
    bool GenKernels(CrossSchema::PlatformRayTracingShaderT* shader, std::time_t mtime,
        const std::string& shaderCode, const CrossSchema::ShaderVersion& version,
        const std::vector<std::tuple<CrossSchema::ShaderStageBit, std::string, std::wstring>>& entrys,
        IncludeHandler* includer, DebugMode debugSymbol = DebugMode::None);
    
    threading::TaskEventPtr ImportAssetAsync(const std::string& sourcePath, const std::string& ndaSavePath = "") override;

private:
    bool CheckAssetName(const char* name) const override
    {
        return HasExtension(name, ".raytracing");
    }
};

}   // namespace cross::editor