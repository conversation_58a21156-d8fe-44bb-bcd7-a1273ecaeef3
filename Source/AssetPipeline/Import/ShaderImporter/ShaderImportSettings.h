#pragma once
#include "EnginePrefix.h"
#include "CrossSchema/ShaderAsset_generated.h"
#include "AssetPipeline/Import/AssetImportSetting.h"

namespace cross::editor {

enum class CEMeta(Editor) ShaderCodeFormatE : uint32_t
{
    Unknown = 0,
    GLSL = 1,
    ESSL = 2,
    MSL_IOS = 3,
    MSL_OSX = 4,
    DXBC = 5,
    DXIL = 6,
    SPIR_V = 7,
    MTLLIB_IOS = 8,
    MTLLIB_OSX = 9,
    MIN = Unknown,
    MAX = MTLLIB_OSX
};

struct ShaderVersionE
{
    CE_Serialize_Deserialize;

    CEMeta(Serialize, Editor)  
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "The format of the shader")) 
    ShaderCodeFormatE Format;
    
    CEMeta(Serialize, Editor)  
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "The major version of the format")) 
    UInt32 Major;
    
    CEMeta(Serial<PERSON>, Editor)  
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "The minor version of the format")) 
    UInt32 Minor;
};
static_assert(sizeof(ShaderVersionE) == sizeof(CrossSchema::ShaderVersion), "The size must be equal");

struct ShaderDefinesE
{
    CE_Serialize_Deserialize

    CEMeta(Serialize, Editor) 
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "The defines of the shader keywords, used to generate variant"))
    std::vector<std::string> Defines;
};

struct ASSET_API ShaderImportSettings : ImportSetting
{
    CE_Virtual_Serialize_Deserialize;

    CEMeta(Serialize, Editor) 
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "The format and version of the shader")) 
    ShaderVersionE Version{ShaderCodeFormatE::SPIR_V, 1, 4};

    CEMeta(Serialize, Editor) 
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "The defines of the shader keywords, used to generate variant")) 
    std::vector<ShaderDefinesE> Variants;

    CEMeta(Serialize, Editor) 
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether to use on-demand compilation")) 
    bool UseOnDemandCompilation{true};

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether to ignore the nda file cache")) 
    bool IgnoreCache{false};

    CEMeta(Serialize, Editor) 
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether to gen all variants with the input shader format")) 
    bool GenAllVariants{false};
    
    CEMeta(Serialize, Editor) 
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether to check shader modules for auto import shaders")) 
    bool CheckShaderModules{true};
    
    CEMeta(Serialize, Editor) 
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether to generate shader maps for shader modules")) 
    bool GenShaderMaps{true};

    CEMeta(Serialize, Editor) 
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether to generate shader maps debug info")) 
    bool GenShaderMapsDebugInfo{false};

    static ShaderImportSettings gShaderImportSettings;
    static ShaderImportSettings gComputeShaderImportSettings;
    static ShaderImportSettings gRayTracingShaderImportSettings;
};

enum class CEMeta(Editor) ShaderFileOutdateLevelE
{
    MTIME = 0,
    CONTENT_HASH,
    ALL
};

enum class DebugMode
{
    None = 0,
    SourceCode,
    Compatible
};

}   // namespace cross::editor
