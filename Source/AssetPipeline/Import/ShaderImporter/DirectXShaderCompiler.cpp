#include "EnginePrefix.h"
#include "DirectXShaderCompiler.h"
#include "ShaderReflection.h"

#include "spirv_cross.hpp"
#include "spirv_glsl.hpp"
#include "spirv_msl.hpp"

#include <sstream>
#include <fstream>
#include <codecvt>
#include <fmt/xchar.h>
using CrossSchema::ShaderStageBit;

namespace {

inline void CHECK(HRESULT ret)
{
    if (ret != S_OK)
    {
        Assert(false);
        LOG_EDITOR_ERROR("Failure in shader compliation");
    }
}

const wchar_t* MapShaderProfileName(ShaderStageBit stage)
{
    switch (stage)
    {
    case ShaderStageBit::Vertex:
        return L"vs";
    case ShaderStageBit::Pixel:
        return L"ps";
    case ShaderStageBit::Geometry:
        return L"gs";
    case ShaderStageBit::Hull:
        return L"hs";
    case ShaderStageBit::Domain:
        return L"ds";
    case ShaderStageBit::Compute:
        return L"cs";
    case ShaderStageBit::RayGen:
    case ShaderStageBit::ClosestHit:
    case ShaderStageBit::AnyHit:
    case ShaderStageBit::Miss:
    case ShaderStageBit::Callable:
    case ShaderStageBit::InterSection:
    case ShaderStageBit::AllRayTracing:
        return L"lib";
    default:
        Assert(false);
        return nullptr;
    }
}

#if 0
class DXCIncluder : public IDxcIncludeHandler
{
public:
    DXCIncluder(cross::editor::IncludeHandler* handler, IDxcUtils* library)
        : mHandler{handler}
        , mLibrary{library}
    {}

private:
    HRESULT STDMETHODCALLTYPE LoadSource(LPCWSTR fileName, IDxcBlob** includeSource) override
    {
        if ((fileName[0] == L'.') && (fileName[1] == L'/'))
        {
            fileName += 2;
        }

        auto fileNameUTF8 = cross::editor::UTF16toUTF8(fileName);

        // The input file name may be converted to full path in DXC
        {
            cross::PathHelper::Normalize(fileNameUTF8);
            auto filePath = std::filesystem::path{fileNameUTF8};
            if (filePath.is_absolute())
            {
                // Check is relative to source file
                auto relativePath = filePath.lexically_proximate(mHandler->GetFileDirectoryPath());
                if (relativePath.is_relative()) 
                {
                    // Convert to relative path
                    fileNameUTF8 = relativePath.string();
                    cross::PathHelper::Normalize(fileNameUTF8);
                }
            }
        }

        if (auto [size, data] = mHandler->OpenPath(fileNameUTF8); data)
        {
            *includeSource = nullptr;
            return mLibrary->CreateBlobFromPinned(data, static_cast<UInt32>(size), CP_UTF8, reinterpret_cast<IDxcBlobEncoding**>(includeSource));
        }

        return E_FAIL;
    }

    ULONG STDMETHODCALLTYPE AddRef() override
    {
        ++m_ref;
        return m_ref;
    }

    ULONG STDMETHODCALLTYPE Release() override
    {
        --m_ref;
        ULONG result = m_ref;
        if (result == 0)
        {
            delete this;
        }
        return result;
    }

    HRESULT STDMETHODCALLTYPE QueryInterface(REFIID iid, void** object) override
    {
        if (IsEqualIID(iid, __uuidof(IDxcIncludeHandler)))
        {
            *object = dynamic_cast<IDxcIncludeHandler*>(this);
            this->AddRef();
            return S_OK;
        }
        else if (IsEqualIID(iid, __uuidof(IUnknown)))
        {
            *object = dynamic_cast<IUnknown*>(this);
            this->AddRef();
            return S_OK;
        }
        else
        {
            return E_NOINTERFACE;
        }
    }

    cross::editor::IncludeHandler* mHandler;

    IDxcUtils* mLibrary;

    std::atomic<ULONG> m_ref = 0;
};

static const std::regex halfTexturePattern{R"xx((Texture(?:[1-3]D|Cube)(?:MS)?(?:Array)?)\s*<\s*half([2-4]?)\s*>)xx"};

static const std::string halfTextureFormat{"$1<float$2>"};

static const std::regex halfPattern{R"( (?:\b|\()half([2-4]?)(\s+|\)|\() )"};

static const std::string halfFormat{"min16float$1$2"};
#endif
}   // namespace

cross::editor::DirectXShaderCompiler::DirectXShaderCompiler(IncludeHandler* pIncluder)
{
    CHECK(DxcCreateInstance(CLSID_DxcCompiler, IID_PPV_ARGS(&mCompiler)));
    CHECK(DxcCreateInstance(CLSID_DxcUtils, IID_PPV_ARGS(&mLibrary)));
    mLibrary->CreateDefaultIncludeHandler(&mIncluder);

    auto & includePaths = pIncluder->GetIncludePaths();
    for (auto& include : includePaths )
    {
        mIncluderPath.push_back(include.wstring());
    }
}

#pragma optimize("", off)
CComPtr<IDxcBlob> cross::editor::DirectXShaderCompiler::Compile(const std::string& sourceCode, const std::wstring& entry, const std::wstring& fileName, const std::vector<DxcDefine>& macros, ShaderStageBit stage,
                                                                const CrossSchema::ShaderVersion& version, bool debug, DebugMode debugMode)
{
    using namespace CrossSchema;   // safe

    std::wstring profile;

    std::vector<const wchar_t*> args;

    // it's quite complicated why we update to directxShaderCompiler v1.7.2308
    // first, we encoutered a perf validation error states that vertex shader wirtes to output slots that is not consumed by fragment shader
    // suggesting use VK_KHR_maintenance4. 
    // but lately we found that this is a validation suggestion bug, since this extention didnot handle this issue
    // https://github.com/KhronosGroup/Vulkan-ValidationLayers/issues/4579
    // and we cannot simply remove the unused attributs in librart  vertex xxxx.hlsl since it may be used by hand written surface shader.
    // previous dxc would simply remove the entry point in generated fragment spirv:
    // https://github.com/KhronosGroup/Vulkan-ValidationLayers/issues/4352
    // lately, we found that the newly released dxc [two day ago!!!!]add this ability -fspv-preserve-interface.
    // so we upgrade this library.
    // note that this version made HLSL 2021 as default, which has a lot of compatibility issue, so we add the -HV 2018 args


    switch (version.format())
    {
    case ShaderCodeFormat::DXIL:
        args = {
            L"-Zpc",
            //L"-HV",
            //L"2018",
        };
        profile = fmt::format(L"{}_{}_{}", MapShaderProfileName(stage), version.major_version(), version.minor_version());
        break;
    case ShaderCodeFormat::ESSL:
        args = {
            L"-Zpc",
            L"-spirv",
            L"-fspv-target-env=vulkan1.0",
            L"-fvk-use-gl-layout",
            //L"-HV",
            //L"2018",
        };
        profile = fmt::format(L"{}_6_3", MapShaderProfileName(stage));
        // sourceBlob = PreprocessSource(halfPattern, halfFormat, sourceBlob, fileName, macros);
        break;
    case ShaderCodeFormat::MSL_OSX:
    case ShaderCodeFormat::MSL_IOS:
        args = {
            L"-Zpc", 
            L"-enable-16bit-types", 
            L"-spirv", 
            L"-fspv-target-env=vulkan1.0",
            //L"-HV",
            //L"2018",
            // L"-fvk-use-scalar-layout",
        };
        profile = fmt::format(L"{}_6_3", MapShaderProfileName(stage));
        // sourceBlob = PreprocessSource(halfTexturePattern, halfTextureFormat, sourceBlob, fileName, macros);
        break;
    case ShaderCodeFormat::SPIR_V:
        args = {
            L"-Zpc",
            //L"-HV",
            //L"2018",
            // L"-Gis",
            // L"-Od",
            // L"-enable-16bit-types",
            L"-spirv",
            L"-fspv-target-env=vulkan1.3",
            L"-fspv-extension=SPV_EXT_descriptor_indexing",  // Enable Bindless
            L"-fspv-extension=SPV_KHR_ray_query",  // Enable Ray Query
            L"-fspv-extension=SPV_KHR_ray_tracing", // Enable Ray Tracing
            // L"-Zi",
            // L"-fspv-extension=SPV_KHR_non_semantic_info",
            // L"-fspv-debug=vulkan-with-source",
            // L"-fspv-debug=file",
            // L"-fvk-use-scalar-layout",
            L"-fvk-use-dx-position-w",
            //L"Oconfig=--strip-nonsemantic,--strip-debug",
            L"-fvk-auto-shift-bindings",
            //L"-fspv-preserve-interface"
        };
        switch (stage)
        {
        case ShaderStageBit::Vertex:
        case ShaderStageBit::Domain:
        case ShaderStageBit::Geometry:
            args.emplace_back(L"-fvk-invert-y");
            break;
        default:
            break;
        }
        if (debugMode != DebugMode::None)
        {
            args.emplace_back(L"-Zi");
            // args.emplace_back(L"-fspv-debug=line");
            // args.emplace_back(L"-fspv-print-all");
        }

        if (debugMode == DebugMode::SourceCode)
        {
            //sourceBlob = PreprocessSource(halfPattern, halfFormat, sourceBlob, fileName, macros);
            args.emplace_back(L"-fspv-extension=SPV_KHR_non_semantic_info");
            args.emplace_back(L"-fspv-debug=vulkan-with-source");
        }
        else
        {
            args.emplace_back(L"-fspv-debug=line");
        }
        profile = fmt::format(L"{}_6_3", MapShaderProfileName(stage));
        // sourceBlob = PreprocessSource(halfPattern, halfFormat, sourceBlob, fileName, macros);
        break;
    default:
        Assert(false);
        return {};
    }

    if (debug)
    {
        args.emplace_back(L"-Od");
    }
   
    for (auto& include : mIncluderPath)
    {
        args.emplace_back(L"-I");
        args.emplace_back(include.c_str());
    }

    CComPtr<IDxcCompilerArgs> compilerArgs;
    CHECK(mLibrary->BuildArguments(fileName.c_str(), entry.c_str(), profile.c_str(), args.data(), static_cast<UINT32>(args.size()), macros.data(), static_cast<UINT32>(macros.size()), &compilerArgs));

    DxcBuffer sourceBlob{sourceCode.data(), sourceCode.size(), DXC_CP_UTF8};

    CComPtr<IDxcBlob> preprocessed_result;

    // it seems different versions of renderdoc have different handling of debugsymbol
    // some would organize the preprocessed sourcecode more elegantly, that is, can jump through different included HLSL instead one big file
    if (debugMode == DebugMode::SourceCode)
    {
        CComPtr<IDxcCompilerArgs> preprocess_compileflags;
        CComPtr<IDxcResult> preprocess_result;
        std::vector<const wchar_t*> preprocess_args = args;
        preprocess_args.emplace_back(L"-P");
        CHECK(mLibrary->BuildArguments(fileName.c_str(), entry.c_str(), profile.c_str(), preprocess_args.data(), static_cast<UINT32>(preprocess_args.size()), macros.data(), static_cast<UINT32>(macros.size()), &preprocess_compileflags));

        CHECK(mCompiler->Compile(&sourceBlob, preprocess_compileflags->GetArguments(), preprocess_compileflags->GetCount(), mIncluder, IID_PPV_ARGS(&preprocess_result)));

        preprocess_result->GetResult(&preprocessed_result);

        sourceBlob = DxcBuffer(preprocessed_result->GetBufferPointer(), preprocessed_result->GetBufferSize(), DXC_CP_UTF8);
    }

    CComPtr<IDxcResult> result;
    CHECK(mCompiler->Compile(&sourceBlob,compilerArgs->GetArguments(), compilerArgs->GetCount(), mIncluder, IID_PPV_ARGS(&result)));





    HRESULT status;
    CHECK(result->GetStatus(&status));

    if (status != S_OK)
    {
        CComPtr<IDxcBlobEncoding> error;
        CHECK(result->GetErrorBuffer(&error));

        std::wstring defines;
        for (auto& itr : macros)
        {
            defines = L"{" + std::wstring(itr.Name) + L" " + std::wstring(itr.Value) + L"}";
        }

        std::wstring_convert<std::codecvt_utf8<wchar_t>, wchar_t> converters;
        std::string errors(reinterpret_cast<char*>(error->GetBufferPointer()));
        LOG_EDITOR_ERROR("defines {} {}", converters.to_bytes(defines), errors);
        std::cout << errors;
        return {};
    }

    CComPtr<IDxcBlob> resultBinary;
    CHECK(result->GetResult(&resultBinary));

    return resultBinary;
}

CComPtr<IDxcBlobEncoding> cross::editor::DirectXShaderCompiler::PreprocessSource(const std::regex& pattern, const std::string& fmt, CComPtr<IDxcBlobEncoding> sourceBlob, const std::wstring& fileNameUTF16,
                                                                                 const std::vector<DxcDefine>& defines)
{
    return {};
#if 0
    CComPtr<IDxcOperationResult> preprocessResult;
    if (mCompiler->Preprocess(sourceBlob, fileNameUTF16.c_str(), nullptr, 0, defines.data(), static_cast<UINT32>(defines.size()), mIncluder, &preprocessResult) == S_OK)
    {
        CComPtr<IDxcBlob> preprocessedSourceBlob;
        CComPtr<IDxcBlobEncoding> formattedSourceBlobEncoding;
        if (HRESULT ret; preprocessResult->GetStatus(&ret) == S_OK && ret == S_OK && preprocessResult->GetResult(&preprocessedSourceBlob) == S_OK)
        {
            CComPtr<IDxcBlobEncoding> preprocessedSourceBlobEncoding;
            preprocessedSourceBlob->QueryInterface(&preprocessedSourceBlobEncoding);

            BOOL known = FALSE;
            UINT32 encoding = 0;
            preprocessedSourceBlobEncoding->GetEncoding(&known, &encoding);
            Assert(known == TRUE && encoding == CP_UTF8);

            auto data = reinterpret_cast<char*>(preprocessedSourceBlob->GetBufferPointer());
            auto size = preprocessedSourceBlob->GetBufferSize();
            std::ostringstream oss;
            std::regex_replace(std::ostreambuf_iterator{oss}, data, data + size, pattern, fmt);
            auto formattedSourceCode = oss.str();
            mLibrary->CreateBlobWithEncodingOnHeapCopy(formattedSourceCode.c_str(), static_cast<UINT32>(formattedSourceCode.length()), CP_UTF8, &formattedSourceBlobEncoding);
            return formattedSourceBlobEncoding;
        }
    }
    CComPtr<IDxcBlobEncoding> errorMsg;
    preprocessResult->GetErrorBuffer(&errorMsg);
    LOG_EDITOR_ERROR("{}", reinterpret_cast<char*>(errorMsg->GetBufferPointer()));
    return {};
#endif
}

std::wstring cross::editor::UTF8toUTF16(const std::string& str)
{
#if !CROSSENGINE_OSX
    auto strUTF16Len = ::MultiByteToWideChar(CP_UTF8, MB_ERR_INVALID_CHARS, str.c_str(), static_cast<int>(str.length()), nullptr, 0);
    std::wstring strUTF16(strUTF16Len, L'\0');
    strUTF16Len = ::MultiByteToWideChar(CP_UTF8, MB_ERR_INVALID_CHARS, str.c_str(), static_cast<int>(str.length()), strUTF16.data(), static_cast<int>(strUTF16.length()));
    return strUTF16;
#else
    std::wstring_convert<std::codecvt_utf8<wchar_t> > converter;
    std::wstring ret = converter.from_bytes(str);
    return ret;
#endif
}

std::string cross::editor::UTF16toUTF8(const std::wstring& str)
{
#if !CROSSENGINE_OSX
    auto strUTF8Len = ::WideCharToMultiByte(CP_UTF8, 0, str.c_str(), static_cast<int>(str.length()), nullptr, 0, nullptr, nullptr);
    std::string strUTF8(strUTF8Len, '\0');
    strUTF8Len = ::WideCharToMultiByte(CP_UTF8, 0, str.c_str(), static_cast<int>(str.length()), strUTF8.data(), static_cast<int>(strUTF8.length()), nullptr, nullptr);
    return strUTF8;
#else
    std::wstring_convert<std::codecvt_utf8<wchar_t> > converter;
    std::string ret = converter.to_bytes(str);
    return ret;
#endif
}
