#pragma once
#include "PlatformDefs.h"
#include "dxc/dxcapi.h"
#if CROSSENGINE_WIN
#    include <atlbase.h>
#else
#    include "dxc/Support/WinAdapter.h"
#endif

#include "AssetPipeline/Import/ShaderImporter/ShaderImportSettings.h"
#include "IncludeHandler.h"
#include "CrossSchema/ShaderDefines_generated.h"
#include "CrossSchema/ShaderAsset_generated.h"

#include <string>
#include <regex>
#include <vector>

namespace cross::editor {

class DirectXShaderCompiler
{
public:
    DirectXShaderCompiler(IncludeHandler* pIncluder);

    CComPtr<IDxcBlob> Compile(const std::string& sourceCode, const std::wstring& entry, const std::wstring& fileName, const std::vector<DxcDefine>& macros, CrossSchema::ShaderStageBit stage, const CrossSchema::ShaderVersion& version,
                              bool debug, DebugMode debugMode = DebugMode::None);

private:
    CComPtr<IDxcBlobEncoding> PreprocessSource(const std::regex& pattern, const std::string& fmt, CComPtr<IDxcBlobEncoding> sourceBlob, const std::wstring& fileNameUTF16, const std::vector<DxcDefine>& defines);
    
    CComPtr<IDxcCompiler3> mCompiler;

    CComPtr<IDxcUtils> mLibrary;

    CComPtr<IDxcIncludeHandler> mIncluder;

    std::vector<std::wstring> mIncluderPath;
};

// temporarily used, waiting for bug fixed in UTF8.cpp
std::wstring UTF8toUTF16(const std::string& str);

std::string UTF16toUTF8(const std::wstring& str);

}   // namespace cross::editor