#pragma once
#include "Resource/IResourceInterface.h"
#include "AssetPipeline/Import/AssetImportSetting.h"
#include "AssetPipeline/Import/TextureImporter/TextureImportSetting.h"
#include "AssetPipeline/Import/ModelImporter/ModelImporter.h"
#include "AssetPipeline/Import/ModelImporter/MeshDescription.h"
#include "AssetPipeline/Import/ModelImporter/MaterialDescription.h"
#include "AssetPipeline/Import/ModelImporter/SkinDescription.h"
#include "AssetPipeline/Import/ModelImporter/FBXImporter/FBXImportUtility.h"
#include "AssetPipeline/AssetExchange/Include/SDK.h"
#include "AssetPipeline/Protocol/Model/ImportCurveData.h"
#include "Runtime/GameWorld/FFSWGS84SystemG.h"

namespace cross {
    struct HLODAssetDescription;

namespace editor {
    struct ImportScene;

    struct ASSET_API FbxUVs
    {
        FbxUVs(fbxsdk::FbxMesh* fbxMesh)
        {
            Init(fbxMesh);
        }

        ~FbxUVs()
        {
            UnInit();
        }

        void Init(fbxsdk::FbxMesh* fbxMesh)
        {
            SInt32 layerCount = fbxMesh->GetLayerCount();
            if (layerCount <= 0)
            {
                return;
            }

            mLayerElementUV.resize(layerCount);
            mUVReferenceMode.resize(layerCount);
            mUVMappingMode.resize(layerCount);

            for (SInt32 uvLayerIndex = 0; uvLayerIndex < layerCount; ++uvLayerIndex)
            {
                FbxLayer* layer = fbxMesh->GetLayer(uvLayerIndex);
                const SInt32 uvsetCount = layer->GetUVSetCount();
                if (uvsetCount > 0) {
                    FbxLayerElementUV* eleUVs = layer->GetUVs();
                    mLayerElementUV[uvLayerIndex] = eleUVs;
                    mUVReferenceMode[uvLayerIndex] = eleUVs->GetReferenceMode();
                    mUVMappingMode[uvLayerIndex] = eleUVs->GetMappingMode();
                    mUVCount++;
                }
            }
        }

        void UnInit()
        {
            mLayerElementUV.clear();
            mLayerElementUV.shrink_to_fit();
            mUVReferenceMode.clear();
            mUVReferenceMode.shrink_to_fit();
            mUVMappingMode.clear();
            mUVMappingMode.shrink_to_fit();
            mUVCount = 0;
        }

        std::vector<FbxLayerElementUV const*> mLayerElementUV;
        std::vector<FbxLayerElement::EReferenceMode> mUVReferenceMode;
        std::vector<FbxLayerElement::EMappingMode> mUVMappingMode;
        SInt32 mUVCount{0};
    };

    struct ASSET_API SharedFbxMeshInfo
    {
        // FbxMesh shared by these FbxNodes
        std::vector<FbxNode*> FbxNodes;
    };

    class ASSET_API FBXImporter : public ModelImporter
    {
        using MeshDataPtr = std::unique_ptr<CrossSchema::ImportMeshDataT>;
        using VertexID = MeshDescription::VertexID;
        using VertexInstanceID = MeshDescription::VertexInstanceID;
        using FbxMeshNodesMap = std::map<FbxMesh*, std::unique_ptr<SharedFbxMeshInfo>>;
        using MeshCollisions = std::vector<PhysicsMeshCollisionImport>;

    public:
        FBXImporter();
        virtual ~FBXImporter();

        virtual void ImportAsset(const std::string& readPath, const std::string& writePath, ImportSetting* setting) override;
        void ImportHLODAssets(const std::string& readPath, const std::string& writePath, ImportSetting* setting);
        void ImportHLODAsset(const std::string& baseDirectory, const std::string& parentEuid, const HLODAssetDescription& hlodAssetDesc, SerializeNode& hlodPackage, ImportSetting* setting);
        bool UpdateAsset(const std::string& readPath, const std::string& writePath) override;
        bool CheckAssetName(const char* name) const override;

        virtual void CleanUp() override;
        void ReleaseScene();
        void ConvertScene();
        void RecomputeNormals(MeshDescription& meshDescription);
    protected:
        bool OpenFile(const std::string& path);
        bool ImportFile(const std::string& path);
        void ExtractSceneInfo();
        virtual void PostImport(ImportScene& importScene);
        virtual void SerializeAllAsset(ImportScene& importScene, const std::string& readPath, const std::string& writePath);
        virtual void SerializeDefaultMaterials(const ImportScene& importScene, std::vector<MaterialDescription>& materialDescs, const std::string& assetFilePath, const std::string& ndaSavePath, std::vector<MaterialMeshBind>& bindings,
                                       std::vector<MeshDescription>& meshDescs);
        virtual void SerializeCombineMaterials(ImportScene& importScene, std::vector<MaterialDescription>& materialDescs, const std::string& assetFilePath, const std::string& ndaSavePath, std::vector<MaterialMeshBind>& bindings,
                                       std::vector<MeshDescription>& meshDescs);
        virtual void SerializeModel(const ImportScene& importScene, const std::string& writePath, bool importSkeleton);

        virtual bool TransferImportScene(ImportScene& outImportScene);
        bool TransferNodeHierarchy(ImportScene& importScene);
        void TransferGlobalMaterial(ImportScene& importScene);
        virtual bool TransferMesh(fbxsdk::FbxMesh* fbxMesh, const FbxAMatrix& totalMatrix, const MaterialDescription& materialDesc, MaterialMeshBind& matMeshBind, std::vector<MeshDescription>& outMeshDescs);
        //void TransferImportResult(const std::vector<MeshDescription>& meshDescs, );
        void TransferBlendShape(fbxsdk::FbxMesh* fbxMesh, const FbxAMatrix& totalMatrix, std::vector<MeshDescription>& outMeshDescs, UInt32 prevSize, UInt32 newSize);
        void AdjustMeshDescByFaceOffset(MeshDescription& outMeshDesc);
        void TransferSkin(ImportScene& importScene);
        void TransferAnimations(const SkeletonDesc& skeleton, std::vector<AnimationDesc>& outAnimations);
        void TransferCurveData(CurveDataDesc& outCurveData);
        void TransferCollider(const std::string& name, FbxNode* fbxNode, ImportScene& output);
        void TransferMaterial(const ImportScene& importScene, fbxsdk::FbxNode* fbxNode, fbxsdk::FbxMesh* fbxMesh, MaterialDescription& outMaterialDesc);

        void FindAllMeshNodeInCurrentLOD(FbxNode* lodGroupNode, SInt32 lodIndex, std::vector<FbxNode*>& output);
        void RecursiveGetAllMeshNode(FbxNode* fbxNode, std::vector<FbxNode*>& output);

        /*
         * Record the FbxMeshNodesMap(K[FbxMesh*] V[std::unique_ptr<SharedMeshInfo>]), FbxMesh* maybe shared for diffirent FbxNode
         * @params index : The index which indicated MeshDecription from ImportScene
         * @params fbxNode : The FbxNode* holds current shared FbxMesh*
         */
        void RecordMeshSharedInfo(FbxNode* fbxNode, FbxMesh* fbxMesh);

        virtual bool RecursiveImportNode(FbxNode* fbxNode, ImportScene& scene);

        bool BuildMeshDescription(fbxsdk::FbxMesh* fbxMesh, const MaterialDescription& materialDesc, const FbxAMatrix& totalMatrix, const std::vector<int>& smoothingGroups, const std::vector<UInt32>& materialPolygons,
                                  MeshDescription& meshDescription);
        bool BuildShapeDescription(const std::vector<Float3>& fbxShapePoints, MeshDescription& meshDescription, UInt32 channelIndex, UInt32 shapeIndex);

        FbxAMatrix ComputeTotalMatrix(FbxNode* node);

        virtual void RemeshTopologySmooth(const FbxMesh&, const FbxAMatrix&, const std::vector<UInt32>&, const FbxUVs&, const std::vector<int>&, const std::vector<SInt32>&, MeshDescription& output);

        void FillMeshRemappedVertex(const fbxsdk::FbxMesh& mesh, const FbxAMatrix& totalMatrix, MeshDescription& output, std::set<VertexID>& outVertices);
        void FillMeshBuildVertex(const FbxLayer& layer, const FbxUVs& uvs, const FbxAMatrix& totalMatrix, VertexID vertexID, SInt32 realIndex, const MaterialID& matId, bool hasVertexColor, MeshBuildVertex& output);

        fbxsdk::FbxMesh* PrepareFbxMesh(fbxsdk::FbxMesh* fbxMesh);

        // Texture
        virtual cross::TexturePtr ImportTexture(const FbxSurfaceMaterial* fbxMaterial, const std::string& texProperty, const std::string& savePath, const std::string& assetPath, TextureCompression compression);
        virtual cross::TexturePtr ImportTexture(const FbxFileTexture* fbxTexture, const std::string& savePath, cross::editor::TextureImportSetting settings /*= TextureImportSetting()*/);
        virtual cross::TexturePtr GenSolidTexture(const TextureInfo& info, const std::string& savePath, TextureCompression compression, UInt32 rgba, const std::string namePrefix, bool createNew = false);
        // Skeleton
        bool ImportSkeleton(const std::string meshName, SkeletonDesc& outSkeleton);
        void ImportSkeletonCluster(std::vector<FbxCluster*>& output);
        void ImportSkeletonHierarchy(SkeletonDesc& outSkeleton, std::vector<FbxNode*>& outSkeletonArray);
        void ImportSkeletonBindPose(const std::vector<FbxNode*>& skeletonArray, const std::vector<FbxCluster*>& clusterArray, SkeletonDesc& outSkeleton);
        // Skin
        void ImportSkinData(FbxMesh* pFbxMesh, MeshDescription& meshDescription);
        // Animation
        bool ValidateAnimStack(FbxAnimStack* curAnimStack, FbxTimeSpan& animTimeSpan);
        bool ImportAnimation(FbxAnimStack* animStack, const FbxTimeSpan& animTimeSpan, const SkeletonDesc& skeleton, AnimationDesc& outAnimation);
        void ImportBlendShapeCurves(FbxAnimStack* animStack, const FbxTimeSpan& animTimeSpan, AnimationDesc& outAnimation);
        void ImportCurveToAnimSequence(FbxAnimCurve* fbxAnimCurve, const std::string& curveName, const FbxTimeSpan& animTimeSpan, FloatCurveList& outCurveList,
                                       CurveUseType UseType = CurveUseType::BlendShape, bool removeDuplicateKey = false, bool keepStartTime = false);
        void ImportBoneTracks(SInt32 frameCount, SInt32 startFrame, const SkeletonDesc& skeleton, std::vector<AnimationDesc::Track>& outBoneTracks);
        void ConvertBonesGlobalTransformToTracks(const std::vector<std::vector<Float4x4>>& allBonesGlobalTransformations, const SkeletonDesc& skeleton, std::vector<AnimationDesc::Track>& outBoneTracks);
        void SplitBoneLocalTransformationsToTrack(const std::vector<Float4x4>& curBoneLocalTransformations, AnimationDesc::Track& outBoneTrack);
        SerializeNode LoadJsonFile(std::string filename, std::shared_ptr<TLSFDocument> doc = nullptr);
        void LoadNodeKeyframeAnimation(FbxNode* NodeToQuery, CurveDataDesc& data, CEAssetExchange::IWorldAssemble* prefab = nullptr);
        void LoadNodeKeyframeAnimationRecursively(CurveDataDesc& data, FbxNode* NodeToQuery, CEAssetExchange::IWorldAssemble* prefab = nullptr);
        void SetupTransformForNode(FbxNode* Node);

    public:
        static FbxAMatrix GetGeometryOffset(const FbxNode* pNode);

        static bool IsChildrenContain(fbxsdk::FbxNodeAttribute::EType searchType, fbxsdk::FbxNode* pRoot);
        static bool IsNodeAnimated(const FbxNode* pNode, const FbxAnimLayer* pAnimLayer);
        // Check if fullStr start with head
        static bool StartWith(const std::string& fullStr, const std::string& head)
        {
            return fullStr.compare(0, head.size(), head) == 0;
        }

        static bool ShouldImportCurve(const FbxAnimCurve* fbxAnimCurve, bool bDoNotImportCurveOnlyWithZero = true);
        // Import FbxCurve to FloatCurve
        static bool ImportCurve(const FbxAnimCurve* fbxAnimCurve, const FbxTimeSpan& animTimeSpan, FloatCurveTrack& outCurve, float valueScale = 1.0f, bool negative = false, bool removeDuplicateKey = false, bool keepStartTime = false);
        virtual void CollectMeshNodeRecursively(FbxNode* mergeRoot, FbxAMatrix rootMatix, SInt32 mergeRootIndex, ImportScene& scene, bool includeRoot = false);
        virtual void RecursiveMergeNodes(FbxNode* fbxNode, FbxAMatrix toMergeRootMatrix, SInt32 mergeRootIndex, ImportScene& scene);
        virtual void AddNodeWithNewMat(FbxSurfaceMaterial* fbxMaterialptr, FbxMesh* fbxMesh, FbxNode* fbxNode, MaterialDescription& materialDescCheck, FbxAMatrix toMergeRootMatrix, SInt32 mergeRootIndex, ImportScene& scene);
        virtual void MergeMeshDescription(FbxSurfaceMaterial* fbxMaterialptr, FbxMesh* fbxMesh, FbxNode* fbxNode, MaterialDescription& materialDescCheck, FbxAMatrix toMergeRootMatrix, SInt32 mergeRootIndex, ImportScene& scene);
        void SetWritePath(const std::string& writePath) 
        {
            mWritePath = writePath;
        }
    protected:
        FbxManager* mSdkManager = nullptr;
        FbxImporter* mImporter = nullptr;
        FbxScene* mScene = nullptr;
        std::unique_ptr<FbxGeometryConverter> mGeometryConverter;

        std::unordered_map<fbxsdk::FbxNode*, int> mBoneNodeToIDMap;
        std::vector<fbxsdk::FbxNode*> mSkeletonArray;
        std::unordered_set<FbxNode*> mClusterLinks;

        FbxMeshNodesMap mFbxMeshSharedInfoMap;
        // Each MeshDescription comes from a FbxMesh
        // A FbxMesh may create multi MeshDescriptions for SplitMeshByMaterial
        std::vector<FbxMesh*> mMeshDescSrcFbxMeshes;
        std::set<FbxMesh*> mUniqueLOD0FbxMeshes;
        std::unordered_map<FbxSurfaceMaterial*, SInt32> mdset;
        std::unordered_set<FbxSurfaceMaterial*> materialSet;
        std::unordered_map<std::string, SInt32> mLodMaterialNameMap;
        bool mLodMaterialCacheDirty{ false };
        std::unordered_map<FbxSurfaceMaterial*, std::string> mMaterailPathMap;
        bool needToBeMerged = false;
        std::string mWritePath;
    };
}}   // namespace cross::editor