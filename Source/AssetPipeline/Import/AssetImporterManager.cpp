#include "AssetPipeline/PCH/AssetPipelinePCH.h"
#include "AssetPipeline/Interface/AssetPipeline.h"
#include "AssetPipeline/Import/AssetImporterManager.h"
#include "AssetPipeline/Import/AssetImporter.h"
#include "AssetPipeline/Import/NDAImporter.h"
#include "AssetPipeline/Import/ModelImporter/FBXImporter/FBXImporter.h"
#include "AssetPipeline/Import/ModelImporter/OBJImporter/OBJImporter.h"
#include "AssetPipeline/Import/TextureImporter/TextureImporter.h"
#include "AssetPipeline/Import/ShaderImporter/ShaderImporter.h"
#include "AssetPipeline/Import/TerrainImporter/TerrainImporter.h"
#include "AssetPipeline/Import/FontImporter/FontImporter.h"
#include "AssetPipeline/Import/OSMImporter/OSMImporter.h"
#include "AssetPipeline/Import/SHPImporter/SHPImporter.h"
#include "AssetPipeline/Import/AnimAssetImporter/AnimAssetImporter.h"
#include "AssetPipeline/Import/TMapImporter/TMapImporter.h"
#include "AssetPipeline/Import/ModelImporter/FBXImporter/FBXCityImporter.h"
#include "Runtime/Interface/CrossEngineImp.h"
#include "FileSystem/filesystem.h"
#include "AssetPipeline/Import/ShaderImporter/ShaderMaps.h"
#include "PhysicsEngine/PhysicsEngine.h"
#include "PhysicsEngine/PhysicsCooker.h"
#include "PhysicsEngine/PhysicsShape.h"

namespace cross::editor
{
    extern AssetImporterManager gAssetImportManager;
    ModelImportSettings ModelImportSettings::gModelImportSettings;
    TextureImportSetting TextureImportSetting::gTextureImportSetting;
    FontImportSetting FontImportSetting::gFontImportSetting;
    ShaderImportSettings ShaderImportSettings::gShaderImportSettings;
    ShaderImportSettings ShaderImportSettings::gComputeShaderImportSettings;
    ShaderImportSettings ShaderImportSettings::gRayTracingShaderImportSettings;
    SHPImportSetting SHPImportSetting::gSHPImportSetting;
    OSMImportSetting OSMImportSetting::gOSMImportSetting;
    TMapImportSetting TMapImportSetting::gTMapImporterSetting;
    CityImportSettings CityImportSettings::gCityImporterSetting;

    AssetImporterManager::AssetImporterManager()
    {
        Init();
    }

    void AssetImporterManager::Init()
    {
        RegisterImporter(new FBXCityImporter());
        RegisterImporter(new FBXImporter());
        RegisterImporter(new TextureImporter());
        RegisterImporter(new GraphicsShaderImporter());
        RegisterImporter(new ComputeShaderImporter());
        RegisterImporter(new RayTracingShaderImporter());
        RegisterImporter(new NDAImporter());

        RegisterImporter(new TerrainImporter());
        RegisterImporter(new FontImporter());
        RegisterImporter(new OSMImporter());
        RegisterImporter(new SHPImporter());

        RegisterImporter(new AnimBlendSpaceImporter());
        RegisterImporter(new AnimCompositeImporter());
        RegisterImporter(new AnimatrixImporter());
        RegisterImporter(new TMapImporter());
    }

    void AssetImporterManager::RegisterImporter(AssetImporter* importer, bool override_sametype)
    {
        auto type = importer->GetType();
        Assert(type > AssetType::Unknown && type < AssetType::Count && "Unknown asset type!!");

        if (override_sametype)
        {
            mImporters.erase(type);
        }

        mImporters.emplace(type, std::move(std::unique_ptr<AssetImporter>{ importer }));
    }

    AssetImporter* AssetImporterManager::GetAssetImporter(const char* assetFilePath)
    {
        auto itr = std::find_if(mImporters.cbegin(), mImporters.cend(), [&](auto& elem)
        {
            auto&[type, imptr] = elem;
            return imptr && imptr->CheckAssetName(assetFilePath);
        });
        return itr == mImporters.end() ? nullptr : itr->second.get();
    }

    AssetImporter* AssetImporterManager::GetAssetImporter(AssetType type) {
        auto itr = mImporters.find(type);
        return itr == mImporters.end() ? nullptr : itr->second.get();
    }

    void AssetImporterManager::GenComputeShaderOnDemand(const std::string& code, const std::string& ndaSavePath)
    {
        static_cast<editor::ComputeShaderImporter*>(GetAssetImporter(cross::editor::AssetType::ComputeShader))->GenShaderOnDemand(code, ndaSavePath);
    }

    AssetType AssetImporterManager::GetAssetType(const char* assetFilePath)
    {
        auto* importer = AssetImporterManager::Instance().GetAssetImporter(assetFilePath);
        return importer ? importer->GetType() : AssetType::Unknown;
    }

    std::unique_ptr<AssetImportResult> AssetImporterManager::ImportAssetInternal(std::optional<SInt64> task, const char* name, const char* ndaSavePath /* = nullptr */) 
    {
        auto* assetImporter = GetAssetImporter(name);

        AssertMsg(assetImporter, "Unknown asset type!!");
        bool isAsyncImport = false;

        switch (assetImporter->GetType())
        {
        case cross::editor::AssetType::Default:
            break;
        case cross::editor::AssetType::Texture:
        {
            if (auto importer = static_cast<TextureImporter*>(assetImporter))
            {
                importer->ImportAsset(name, ndaSavePath, &TextureImportSetting::gTextureImportSetting);
            }
            break;
        }
        case cross::editor::AssetType::IsolatedPointCloud:
        case cross::editor::AssetType::WindsockPointCloud:
        case cross::editor::AssetType::FoliagePointCloud:
        case cross::editor::AssetType::Model:
        {
            if (auto importer = static_cast<ModelImporter*>(assetImporter))
            {
                importer->ImportAsset(name, ndaSavePath, &ModelImportSettings::gModelImportSettings);
                importer->CleanUp();
            }
            break;
        }
        case cross::editor::AssetType::Shader:
        {
            if (auto importer = static_cast<GraphicsShaderImporter*>(assetImporter))
            {
                auto completeEvent = importer->ImportAssetAsync(name, ndaSavePath);

                if (completeEvent)
                {
                    if (task)
                    {
                        isAsyncImport = true;
                        threading::Async({completeEvent}, [=](const auto&) {
                            const bool result = completeEvent->GetReturnValue<bool>();
                            gImportAssetCompleteCallback(task.value_or(-1), result, 0, nullptr);
                        });
                    }
                    else
                    {
                        completeEvent->WaitForCompletion();
                        const bool result = completeEvent->GetReturnValue<bool>();
                        return std::make_unique<AssetImportResult>(result ? AssetImportState::Success : AssetImportState::ImportFail);
                    }
                }
            }
            break;
        }
        case cross::editor::AssetType::ComputeShader:
        {
            if (auto importer = static_cast<ComputeShaderImporter*>(assetImporter)) {
                auto completeEvent = importer->ImportAssetAsync(name, ndaSavePath);
                if (task)
                {
                    isAsyncImport = true;
                    threading::Async({completeEvent}, [=](const auto&) {
                        const bool result = completeEvent->GetReturnValue<bool>();
                        gImportAssetCompleteCallback(task.value_or(-1), result, 0, nullptr);
                    });
                }
                else
                {
                    completeEvent->WaitForCompletion();
                    const bool result = completeEvent->GetReturnValue<bool>();
                    return std::make_unique<AssetImportResult>(result ? AssetImportState::Success : AssetImportState::ImportFail);
                }
            }
            break;
        }
        case cross::editor::AssetType::RayTracingShader:
        {
            if (auto importer = static_cast<RayTracingShaderImporter*>(assetImporter)) {
                auto completeEvent = importer->ImportAssetAsync(name, ndaSavePath);
                if (task)
                {
                    isAsyncImport = true;
                    threading::Async({completeEvent}, [=](const auto&) {
                        const bool result = completeEvent->GetReturnValue<bool>();
                        gImportAssetCompleteCallback(task.value_or(-1), result, 0, nullptr);
                    });
                }
                else
                {
                    completeEvent->WaitForCompletion();
                    const bool result = completeEvent->GetReturnValue<bool>();
                    return std::make_unique<AssetImportResult>(result ? AssetImportState::Success : AssetImportState::ImportFail);
                }
            }
            break;
        }
        case cross::editor::AssetType::Terrain:
        {
            if (auto importer = static_cast<TerrainImporter*>(assetImporter)) {
                importer->ImportAsset(name, ndaSavePath, &gTerrainImportSetting);
            }
            break;
        }
        case cross::editor::AssetType::Font:
        {
            if (auto importer = static_cast<FontImporter*>(assetImporter))
            {
                importer->ImportAsset(name, ndaSavePath, &FontImportSetting::gFontImportSetting);
            }
            break;
        }
        case cross::editor::AssetType::City:
            if (auto importer = static_cast<FBXCityImporter*>(assetImporter))
            {
                importer->ImportAsset(name, ndaSavePath, &CityImportSettings::gCityImporterSetting);
            }
            break;
        case cross::editor::AssetType::OSM:
            if (auto importer = static_cast<OSMImporter*>(assetImporter))
            {
                importer->ImportAsset(name, ndaSavePath, &OSMImportSetting::gOSMImportSetting);
            }
            break;
        case cross::editor::AssetType::SHP:
            if (auto importer = static_cast<SHPImporter*>(assetImporter))
            {
                importer->ImportAsset(name, ndaSavePath, &SHPImportSetting::gSHPImportSetting);
            }
            break;
        case cross::editor::AssetType::TMap:
            if (auto importer = static_cast<TMapImporter*>(assetImporter))
            {
                importer->ImportAsset(name, ndaSavePath, &TMapImportSetting::gTMapImporterSetting);
            }
            break;
        case cross::editor::AssetType::AnimBlendSpace:
            if (auto importer = static_cast<AnimBlendSpaceImporter*>(assetImporter))
            {
                importer->ImportAsset(name, ndaSavePath);
            }
            break;
        case cross::editor::AssetType::AnimComposite:
            if (auto importer = static_cast<AnimCompositeImporter*>(assetImporter))
            {
                importer->ImportAsset(name, ndaSavePath);
            }
            break;
        case cross::editor::AssetType::Animatrix:
            if (auto importer = static_cast<AnimatrixImporter*>(assetImporter))
            {
                importer->ImportAsset(name, ndaSavePath);
            }
            break;
        default:
            break;
        }

        auto importResult = std::make_unique<AssetImportResult>(assetImporter->GetImportResult());

        if (task && !isAsyncImport)
        {
            const int errorCount = static_cast<int>(importResult->ErrorCodes.size());
            int* errorData = reinterpret_cast<int*>(importResult->ErrorCodes.data());
            gImportAssetCompleteCallback(task.value_or(-1), importResult->bSuccess, errorCount, errorData);
        }

        return importResult;
    }

    void AssetImporterManager::ImportAssetAsync(SInt64 task, const char* name, const char* ndaSavePath)
    {
        auto* assetImporter = GetAssetImporter(name);
        AssertMsg(assetImporter, "Unknown asset type!!");
        {
            cross::threading::Dispatch([=, filePath = std::string{name}, savePath = std::string{ndaSavePath}](auto) 
            { 
                ImportAssetInternal(task, filePath.c_str(), savePath.c_str()); 
            });
        }
    }

    threading::TaskEventPtr AssetImporterManager::ImportAssetAsync(const char* name, const char* ndaSavePath)
    {
        auto* assetImporter = GetAssetImporter(name);
        AssertMsg(assetImporter, "Unknown asset type!!");
        {
            return cross::threading::Dispatch([=, filePath = std::string{name}, savePath = std::string{ndaSavePath}](auto) 
            { 
                ImportAssetInternal(std::nullopt, filePath.c_str(), savePath.c_str()); 
            });
        }
    }

    AssetImportResult AssetImporterManager::ImportAsset(const char* name, const char* ndaSavePath)
    {
        auto resultPtr = ImportAssetInternal(std::nullopt, name, ndaSavePath);
        return *resultPtr.get();
    }

    void AssetImporterManager::UpdateTextureAsset(const char* ndaSavePath, const TextureImportSetting setting, TextureResourceInfo& info)
    {
        auto importer = static_cast<TextureImporter*>(GetAssetImporter(AssetType::Texture));
        importer->UpdateTextureAsset(ndaSavePath, setting, info);
    }

    bool AssetImporterManager::GenerateCurveGradientTex(const std::string& fileName, const TextureImportSetting& setting, UInt32* stopColors, int n)
    {
        auto importer = static_cast<TextureImporter*>(GetAssetImporter(AssetType::Texture));
        bool flag = importer->GenerateCurveGradientTex(fileName, setting, stopColors, n);
        return flag;
    }

    bool AssetImporterManager::GetTextureAssetRawData(const char* ndaSavePath, TextureResourceInfo& info)
    {
        auto importer = static_cast<TextureImporter*>(GetAssetImporter(AssetType::Texture));
        return importer->GetTextureRawData(ndaSavePath);
    }

    void AssetImporterManager::GenerateSharpenedMip(const char* ndaSavePath, const TextureImportSetting setting, TextureResourceInfo& info) 
    {
        auto importer = static_cast<TextureImporter*>(GetAssetImporter(AssetType::Texture));
        importer->GenerateSharpenedMip(ndaSavePath, setting, info);
    }

    TextureImportSetting AssetImporterManager::GetTextureImportSettings(const char* ndaSavePath, TextureImportSetting setting, bool AutoFFS) 
    {
        auto importer = static_cast<TextureImporter*>(GetAssetImporter(AssetType::Texture));
        return importer->UpdateDefaultSetting(ndaSavePath, setting, AutoFFS);
    }

    bool AssetImporterManager::UpdateTerrainHeightmap(const std::string& terrainNdaPath, const std::string& updateDir)
    {
        auto importer = static_cast<TerrainImporter*>(GetAssetImporter(AssetType::Terrain));
        return importer->UpdateHeightmap(terrainNdaPath, updateDir);
    }

    bool AssetImporterManager::UpdateTerrainWeightTexture(const std::string& terrainNdaPath, const std::string& updateDir)
    {
        auto importer = static_cast<TerrainImporter*>(GetAssetImporter(AssetType::Terrain));
        return importer->UpdateWeightTexture(terrainNdaPath, updateDir);
    }

    bool AssetImporterManager::UpdateAsset(const char* assetPath, const char* ndaSavePath)
    {
        auto importer = GetAssetImporter(assetPath);
        return importer ? importer->UpdateAsset(assetPath, ndaSavePath) : false;
    }

    bool cross::editor::AssetImporterManager::CheckUpdateAsset(const char* assetPath, const char* ndaSavePath)
    {
        auto importer = GetAssetImporter(assetPath);
        return importer ? importer->CheckUpdateAsset(assetPath, ndaSavePath) : false;
    }
    void AssetImporterManager::SetFontImporterSettings(FontImportSetting assetImportSettings)
    {
        FontImportSetting::gFontImportSetting = assetImportSettings;
    }
    void AssetImporterManager::SetImportSettings(AssetType assetType, void* assetImportSettings)
    {
        Assert(assetType >= AssetType::Unknown && assetType < AssetType::Count && "Unknown asset type!!");

        switch (assetType)
        {
        case cross::editor::AssetType::Default:
            break;
        case cross::editor::AssetType::Texture:
        {
            int sizeofTextureImportSetting = sizeof(TextureImportSetting);
            TextureImportSetting* assetImportSettings1 = static_cast<TextureImportSetting*>(assetImportSettings);
            TextureImportSetting::gTextureImportSetting = *assetImportSettings1;
            break;
        }
        case cross::editor::AssetType::Model:
        {
            int sizeOfModelImportSettings = sizeof(ModelImportSettings);
            ModelImportSettings* assetImportSettings1 = static_cast<ModelImportSettings*>(assetImportSettings);
            ModelImportSettings::gModelImportSettings = *assetImportSettings1;
            break;
        }
        case cross::editor::AssetType::Shader:
        {
            int sizeOfShaderImportSettings = sizeof(ShaderImportSettings);
            ShaderImportSettings* assetImportSettings1 = static_cast<ShaderImportSettings*>(assetImportSettings);
            ShaderImportSettings::gShaderImportSettings = *assetImportSettings1;
            break;
        }
        case cross::editor::AssetType::ComputeShader:
        {
            int sizeOfComputeShaderImportSettings = sizeof(ShaderImportSettings);
            ShaderImportSettings* assetImportSettings2 = static_cast<ShaderImportSettings*>(assetImportSettings);
            ShaderImportSettings::gComputeShaderImportSettings = *assetImportSettings2;
            break;
        }
        case cross::editor::AssetType::RayTracingShader:
        {
            int sizeOfRayTracingShaderImportSettings = sizeof(ShaderImportSettings);
            ShaderImportSettings* assetImportSettings2 = static_cast<ShaderImportSettings*>(assetImportSettings);
            ShaderImportSettings::gRayTracingShaderImportSettings = *assetImportSettings2;
            break;
        }
        case cross::editor::AssetType::Terrain:
        {
            gTerrainImportSetting = *static_cast<TerrainImportSetting*>(assetImportSettings);
            break;
        }
        case cross::editor::AssetType::Font:
        {
            int sizeOfFontImportSetting = sizeof(FontImportSetting);
            FontImportSetting* assetImportSettings1 = static_cast<FontImportSetting*>(assetImportSettings);
            FontImportSetting::gFontImportSetting = *assetImportSettings1;
            break;
        }
        default:
            break;
        }
    }

    void AssetImporterManager::SetShaderImportSettings(const ShaderImportSettings settings) 
    {
        memcpy(&ShaderImportSettings::gShaderImportSettings.Version, &settings.Version, sizeof(ShaderVersionE));
        ShaderImportSettings::gShaderImportSettings.UseOnDemandCompilation = settings.UseOnDemandCompilation;
        ShaderImportSettings::gShaderImportSettings.IgnoreCache = settings.IgnoreCache;
        ShaderImportSettings::gShaderImportSettings.Variants = settings.Variants;
        ShaderImportSettings::gShaderImportSettings.GenAllVariants = settings.GenAllVariants;
        ShaderImportSettings::gShaderImportSettings.CheckShaderModules = settings.CheckShaderModules;
        ShaderImportSettings::gShaderImportSettings.GenShaderMaps = settings.GenShaderMaps;
        ShaderImportSettings::gShaderImportSettings.GenShaderMapsDebugInfo = settings.GenShaderMapsDebugInfo;
    }

    void AssetImporterManager::SetComputeShaderImportSettings(const ShaderImportSettings settings)
    {
        memcpy(&ShaderImportSettings::gComputeShaderImportSettings.Version, &settings.Version, sizeof(ShaderVersionE));
        ShaderImportSettings::gComputeShaderImportSettings.UseOnDemandCompilation = settings.UseOnDemandCompilation;
        ShaderImportSettings::gComputeShaderImportSettings.IgnoreCache = settings.IgnoreCache;
        ShaderImportSettings::gComputeShaderImportSettings.Variants = settings.Variants;
        ShaderImportSettings::gComputeShaderImportSettings.GenAllVariants = settings.GenAllVariants;
        ShaderImportSettings::gComputeShaderImportSettings.CheckShaderModules = settings.CheckShaderModules;
        ShaderImportSettings::gComputeShaderImportSettings.GenShaderMaps = settings.GenShaderMaps;
        ShaderImportSettings::gComputeShaderImportSettings.GenShaderMapsDebugInfo = settings.GenShaderMapsDebugInfo;
    }

    AssetImporterManager& AssetImporterManager::Instance() noexcept
    {
        return gAssetImportManager;
    }

    float AssetImporterManager::GetProgress(AssetType type)
    {
        auto importer = GetAssetImporter(type);
        return importer ? importer->GetProgress() : 0.f;
    }

    bool AssetImporterManager::GenerateCollision(const std::string& filePath, CollisionGenerateSetting setting)
    {
        resource::ResourceLoadError loadError = resource::ResourceLoadError::Succeeded;
        if (auto ptr = gResourceAssetMgr.LoadNDAFile(PathHelper::GetRelativePath(filePath).c_str(), loadError); ptr)
        {
            if (auto meshres = TypeCast<resource::MeshAssetDataResource>(ptr); meshres)
            {
                auto* meshData = meshres->GetAssetData();
                [[maybe_unused]] auto vertexCnt = meshData->GetVertexCount();
                auto* physicsCollision = meshData->GetPhysicsCollision();
                physicsCollision->Clear();

                auto rawData = meshData->GetRawVertexAndIndexData(0, VertexChannel::Position0);

                PhysicsEngine* physicsEngine = EngineGlobal::GetPhysicsEngine();
                std::shared_ptr<PhysicsTriangleMesh> trans = nullptr;

                std::vector<Float3> vertices;
                std::vector<UInt32> indices;

                if (setting.CollisionGenerationType == GenerateCollisionType::BoxCollision)
                {
                    auto boundingBox = meshData->GetBoundingBox();
                    physicsCollision->mBoxGeometry.clear();
                    physicsCollision->mBoxGeometry.emplace_back(boundingBox.GetCenter(), Quaternion::Identity(), boundingBox.GetExtent());
                }
                else if (setting.CollisionGenerationType == GenerateCollisionType::ComplexCollision)
                {
                    trans = physicsEngine->GetCooker()->BuildTriangleMesh(
                        reinterpret_cast<const UInt8*>(rawData.vertexData),
                        static_cast<UInt32>(rawData.vertexCount),
                        static_cast<UInt16>(rawData.vertexStride),
                        reinterpret_cast<const UInt8*>(rawData.indexData),
                        static_cast<UInt32>(rawData.indexCount),
                        static_cast<UInt16>(rawData.indexStride)
                    );
                }
                else if (setting.CollisionGenerationType == GenerateCollisionType::HeightFiled)
                {
                    LOG_ERROR("Not implemented!");
#if 0
                    auto aabb = meshData->GetAABB();
                    SInt32 minX = static_cast<SInt32>(std::round(aabb.Min.x / setting.GridSpacingX));
                    SInt32 minZ = static_cast<SInt32>(std::round(aabb.Min.z / setting.GridSpacingZ));
                    SInt32 maxX = static_cast<SInt32>(std::round(aabb.Max.x / setting.GridSpacingX));
                    SInt32 maxZ = static_cast<SInt32>(std::round(aabb.Max.z / setting.GridSpacingZ));
                    SInt32 rowCnt = static_cast<SInt32>(maxX - minX + 1);
                    SInt32 colCnt = static_cast<SInt32>(maxZ - minZ + 1);

                    std::vector<std::pair<double, UInt32>> grid(static_cast<size_t>(rowCnt * colCnt), {0.0, 0U});


                    for (UInt32 i = 0; i < rawData.vertexCount; i++)
                    {
                        auto& vertex = *reinterpret_cast<const Float3*>(rawData.vertexData + rawData.vertexStride * i);
                        SInt32 x = static_cast<SInt32>(std::round(vertex.x / setting.GridSpacingX));
                        SInt32 z = static_cast<SInt32>(std::round(vertex.z / setting.GridSpacingZ));
                        UInt32 idx = (x - minX) * colCnt + (z - minZ);

                        grid[idx].first += vertex.y;
                        grid[idx].second += 1;
                    }
                    vertices.reserve(grid.size());
                    std::set<UInt32> invalidBlock;
                    for (SInt32 x = minX; x <= maxX; x++)
                    {
                        for (SInt32 z = minZ; z <= maxZ; z++)
                        {
                            UInt32 idx = (x - minX) * colCnt + (z - minZ);
                            float y = 50000.0;
                            if (grid[idx].second > 0)
                            {
                                y = static_cast<float>(grid[idx].first / grid[idx].second);
                            }
                            else
                            {
                                invalidBlock.insert(idx);
                            }
                            vertices.emplace_back(x * setting.GridSpacingX, y, z * setting.GridSpacingZ);
                        }
                    }
                    for (auto idx : invalidBlock)
                    {
                        SInt32 i = static_cast<SInt32>(idx / colCnt);
                        SInt32 j = static_cast<SInt32>(idx % colCnt);
                        float yz = 0.0f;
                        {
                            SInt32 j0 = j - 1, j1 = j + 1;
                            while (j0 >= 0 && invalidBlock.count(i * colCnt + j0) != 0)
                                j0--;
                            while (j1 < colCnt && invalidBlock.count(i * colCnt + j1) != 0)
                                j1++;
                            yz = j0 >= 0 ? vertices[i * colCnt + j0].y : vertices[i * colCnt + j1].y;
                            if (j0 >= 0 && j1 < colCnt)
                            {
                                yz = std::min(vertices[i * colCnt + j0].y, vertices[i * colCnt + j1].y);
                                //yz = MathUtils::Lerp(vertices[i * colCnt + j0].y, vertices[i * colCnt + j1].y, static_cast<float>(j - j0) / static_cast<float>(j1 - j0));
                            }
                        }
                        float yx = 0.0f;
                        {
                            SInt32 i0 = i - 1, i1 = i + 1;
                            while (i0 >= 0 && invalidBlock.count(i0 * colCnt + j) != 0)
                                i0--;
                            while (i1 < rowCnt && invalidBlock.count(i1 * colCnt + j) != 0)
                                i1++;
                            yx = i0 >= 0 ? vertices[i0 * colCnt + j].y : vertices[i1 * colCnt + j].y;
                            if (i0 >= 0 && i1 < colCnt)
                            {
                                yz = std::min(vertices[i0 * colCnt + j].y, vertices[i1 * colCnt + j].y);
                                // yz = MathUtils::Lerp(vertices[i0 * colCnt + j].y, vertices[i1 * colCnt + j].y, static_cast<float>(i - i0) / static_cast<float>(i1 - i0));
                            }
                        }
                        vertices[idx].y = std::min(yz, yx);
                    }
                    indices = std::move(GenerateHeightmapIndicesClockwise(rowCnt, colCnt));
                    trans = physicsEngine->GetCooker()->BuildTriangleMesh(
                        reinterpret_cast<const UInt8*>(vertices.data()),
                        static_cast<UInt32>(vertices.size()),
                        static_cast<UInt16>(sizeof(vertices[0])),
                        reinterpret_cast<const UInt8*>(indices.data()),
                        static_cast<UInt32>(indices.size()),
                        static_cast<UInt16>(sizeof(indices[0]))
                    );
#endif
                }

                if (trans)
                {
                    PhysicsGeometryMesh coll(setting.LocalTranslation, setting.LocalRotation, trans);
                    physicsCollision->mMeshGeometry.clear();
                    physicsCollision->mMeshGeometry.push_back(std::move(coll));
                }
                return meshres->Serialize(filePath);
            }
        }
        return false;
    }

    std::string AssetImporterManager::GetNDAResourceFileName(const char* cszNDAName)const
    {
        if (cszNDAName == nullptr)
            return "";

        filesystem::FileSystem* fileSystem = cross::EngineGlobal::Inst().GetFileSystem();
        Assert(fileSystem);
        filesystem::IFilePtr file = fileSystem->Open(cszNDAName);
        Assert(file);

        UInt64 size = file->GetSize();
        SInt32 nTailInfo[2];
        file->Seek(static_cast<int>(size) - sizeof(nTailInfo));

        file->Read(reinterpret_cast<char*>(nTailInfo), sizeof(nTailInfo));
        if (nTailInfo[1] != NDA_TAIL_MAGIC)
            return "";
        file->Seek(-nTailInfo[0] - sizeof(nTailInfo));
        std::string name;
        name.resize(nTailInfo[0]);
        file->Read(name.data(), nTailInfo[0]);
        file->Close();

        return name;
    }

    void AssetImporterManager::BuildShaderMaps()
    {
        ShaderMaps::GetInstance().BuildShaderMaps();
    }

    void AssetImporterManager::CheckShaderModuleFile(const char* filePath, std::vector<std::string>& outShaders, ShaderFileOutdateLevelE Level)
    {
        return ShaderMaps::GetInstance().CheckShaderModuleFile(filePath, outShaders, Level);
    }
}
