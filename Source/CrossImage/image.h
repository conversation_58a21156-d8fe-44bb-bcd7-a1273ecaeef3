#pragma once

#if _WINDOWS
    #ifdef CrossImage_EXPORTS
        #define CROSS_IMAGE_API __declspec(dllexport)
    #else
        #define CROSS_IMAGE_API __declspec(dllimport)
    #endif
#else
    #ifdef __GNUC__
    #define CROSS_IMAGE_API __attribute__((visibility("default")))
    #else
    #define CROSS_IMAGE_API
    #endif
#endif

#include <cstdint>
#include <algorithm>
#include <cassert>
#include <vector>
#include <string>

#ifdef _WIN32
#define strcasecmp _stricmp
#endif

#define NOTE_UNUSED(x) (void)(x)
#define ARRAY_SIZE(x) (sizeof(x) / sizeof(x[0]))
#define NO_EQUALS_OR_COPY_CONSTRUCT(x) x(const x &) = delete; x& operator= (const x &) = delete;
#define ASSUME(x) static_assert(x, #x);
#define OFFSETOF(s, m) offsetof(s, m)
#define STRINGIZE(x) #x
#define STRINGIZE2(x) STRINGIZE(x)

#include "resampler.h"

#if _WINDOWS
#include "cmft/image.h"
#include "cmft/cubemapfilter.h"
#include "cmft/cubemaputils.h"
#endif

#include "nvtt/nvtt.h"
#include "apg_bmp.h"

#pragma warning(push)
#pragma warning(disable : 4201 4365 4324 4996)

namespace imageio
{
    template<typename T> inline void clear_vector(T &vec) { vec.erase(vec.begin(), vec.end()); }

    template <typename T> inline void clear_obj(T& obj) { memset(&obj, 0, sizeof(obj)); }

    template <typename T0, typename T1> inline T0 lerp(T0 a, T0 b, T1 c) { return a + (b - a) * c; }

    template <typename S> inline S maximum(S a, S b) { return (a > b) ? a : b; }
    template <typename S> inline S maximum(S a, S b, S c) { return maximum(maximum(a, b), c); }
    template <typename S> inline S maximum(S a, S b, S c, S d) { return maximum(maximum(maximum(a, b), c), d); }
    
    template <typename S> inline S minimum(S a, S b) {    return (a < b) ? a : b; }
    template <typename S> inline S minimum(S a, S b, S c) {    return minimum(minimum(a, b), c); }
    template <typename S> inline S minimum(S a, S b, S c, S d) { return minimum(minimum(minimum(a, b), c), d); }

    inline float clampf(float value, float low, float high) { if (value < low) value = low; else if (value > high) value = high;    return value; }
    inline float saturate(float value) { return clampf(value, 0, 1.0f); }
    inline uint8_t minimumub(uint8_t a, uint8_t b) { return (a < b) ? a : b; }
    inline uint32_t minimumu(uint32_t a, uint32_t b) { return (a < b) ? a : b; }
    inline int32_t minimumi(int32_t a, int32_t b) { return (a < b) ? a : b; }
    inline float minimumf(float a, float b) { return (a < b) ? a : b; }
    inline uint8_t maximumub(uint8_t a, uint8_t b) { return (a > b) ? a : b; }
    inline uint32_t maximumu(uint32_t a, uint32_t b) { return (a > b) ? a : b; }
    inline int32_t maximumi(int32_t a, int32_t b) { return (a > b) ? a : b; }
    inline float maximumf(float a, float b) { return (a > b) ? a : b; }
    inline int squarei(int i) { return i * i; }
    inline float squaref(float i) { return i * i; }
    template<typename T> inline T square(T a) { return a * a; }

    template <typename S> inline S clamp(S value, S low, S high) { return (value < low) ? low : ((value > high) ? high : value); }

    inline uint32_t iabs(int32_t i) { return (i < 0) ? static_cast<uint32_t>(-i) : static_cast<uint32_t>(i);    }
    inline uint64_t iabs64(int64_t i) {    return (i < 0) ? static_cast<uint64_t>(-i) : static_cast<uint64_t>(i); }

    inline uint8_t clamp255(int32_t i)
    {
        return (uint8_t)((i & 0xFFFFFF00U) ? (~(i >> 31)) : i);
    }

        inline uint64_t read_be64(const void *p)
    {
        uint64_t val = 0;
        for (uint32_t i = 0; i < 8; i++)
            val |= (static_cast<uint64_t>(static_cast<const uint8_t *>(p)[7 - i]) << (i * 8));
        return val;
    }

    inline void write_be64(void *p, uint64_t x)
    {
        for (uint32_t i = 0; i < 8; i++)
            static_cast<uint8_t *>(p)[7 - i] = static_cast<uint8_t>(x >> (i * 8));
    }

    static inline uint16_t byteswap16(uint16_t x) { return static_cast<uint16_t>((x << 8) | (x >> 8)); }
    static inline uint32_t byteswap32(uint32_t x) { return ((x << 24) | ((x << 8) & 0x00FF0000) | ((x >> 8) & 0x0000FF00) | (x >> 24)); }

    inline uint32_t floor_log2i(uint32_t v)
    {
        uint32_t b = 0;
        for (; v > 1U; ++b)
            v >>= 1;
        return b;
    }

    inline uint32_t ceil_log2i(uint32_t v)
    {
        uint32_t b = floor_log2i(v);
        if ((b != 32) && (v > (1U << b)))
            ++b;
        return b;
    }

    inline int posmod(int x, int y)
    {
        if (x >= 0)
            return (x < y) ? x : (x % y);
        int m = (-x) % y;
        return (m != 0) ? (y - m) : m;
    }

    static inline uint32_t read_le_dword(const uint8_t *pBytes)
    {
        return (pBytes[3] << 24U) | (pBytes[2] << 16U) | (pBytes[1] << 8U) | (pBytes[0]);
    }

    static inline void write_le_dword(uint8_t* pBytes, uint32_t val)
    {
        pBytes[0] = (uint8_t)val;
        pBytes[1] = (uint8_t)(val >> 8U);
        pBytes[2] = (uint8_t)(val >> 16U);
        pBytes[3] = (uint8_t)(val >> 24U);
    }

    inline int string_find_right(const std::string& filename, char c)
    {
        size_t result = filename.find_last_of(c);
        return (result == std::string::npos) ? -1 : (int)result;
    }


    inline std::string string_get_extension(const std::string &filename)
    {
        int sep = -1;
#ifdef _WIN32
        sep = string_find_right(filename, '\\');
#endif
        if (sep < 0)
            sep = string_find_right(filename, '/');

        int dot = string_find_right(filename, '.');
        if (dot <= sep)
            return "";

        std::string result(filename);
        result.erase(0, dot + 1);

        return result;
    }

    enum eNoClamp
    {
        cNoClamp = 0
    };

    template<uint32_t NumBytes>
    struct packed_uint
    {
        uint8_t m_bytes[NumBytes];

        inline packed_uint() { static_assert(NumBytes <= sizeof(uint64_t), "Invalid NumBytes"); }
        inline packed_uint(uint64_t v) { *this = v; }
        inline packed_uint(const packed_uint& other) { *this = other; }
                        
        inline packed_uint& operator= (uint64_t v) 
        { 
            for (uint32_t i = 0; i < NumBytes; i++) 
                m_bytes[i] = static_cast<uint8_t>(v >> (i * 8)); 
            return *this; 
        }

        inline packed_uint& operator= (const packed_uint& rhs) 
        { 
            memcpy(m_bytes, rhs.m_bytes, sizeof(m_bytes)); 
            return *this;
        }

        inline operator uint32_t() const
        {
            switch (NumBytes)
            {
                case 1:  
                {
                    return  m_bytes[0];
                }
                case 2:  
                {
                    return (m_bytes[1] << 8U) | m_bytes[0];
                }
                case 3:  
                {
                    return (m_bytes[2] << 16U) | (m_bytes[1] << 8U) | m_bytes[0];
                }
                case 4:  
                {
                    return read_le_dword(m_bytes);
                }
                case 5:
                {
                    uint32_t l = read_le_dword(m_bytes);
                    uint32_t h = m_bytes[4];
                    return static_cast<uint64_t>(l) | (static_cast<uint64_t>(h) << 32U);
                }
                case 6:
                {
                    uint32_t l = read_le_dword(m_bytes);
                    uint32_t h = (m_bytes[5] << 8U) | m_bytes[4];
                    return static_cast<uint64_t>(l) | (static_cast<uint64_t>(h) << 32U);
                }
                case 7:
                {
                    uint32_t l = read_le_dword(m_bytes);
                    uint32_t h = (m_bytes[6] << 16U) | (m_bytes[5] << 8U) | m_bytes[4];
                    return static_cast<uint64_t>(l) | (static_cast<uint64_t>(h) << 32U);
                }
                case 8:  
                {
                    uint32_t l = read_le_dword(m_bytes);
                    uint32_t h = read_le_dword(m_bytes + 4);
                    return static_cast<uint64_t>(l) | (static_cast<uint64_t>(h) << 32U);
                }
                default: 
                {
                    assert(0);
                    return 0;
                }
            }
        }
    };

    struct color32
    {
        union
        {
            struct
            {
                uint8_t r;
                uint8_t g;
                uint8_t b;
                uint8_t a;
            };

            uint8_t c[4];
            
            uint32_t m;
        };

        color32() { }

        color32(uint32_t vr, uint32_t vg, uint32_t vb, uint32_t va) { set(vr, vg, vb, va); }
        color32(eNoClamp unused, uint32_t vr, uint32_t vg, uint32_t vb, uint32_t va) { (void)unused; set_noclamp_rgba(vr, vg, vb, va); }

        void set(uint32_t vr, uint32_t vg, uint32_t vb, uint32_t va) { c[0] = static_cast<uint8_t>(vr); c[1] = static_cast<uint8_t>(vg); c[2] = static_cast<uint8_t>(vb); c[3] = static_cast<uint8_t>(va); }

        void set_noclamp_rgb(uint32_t vr, uint32_t vg, uint32_t vb) { c[0] = static_cast<uint8_t>(vr); c[1] = static_cast<uint8_t>(vg); c[2] = static_cast<uint8_t>(vb); }
        void set_noclamp_rgba(uint32_t vr, uint32_t vg, uint32_t vb, uint32_t va) { set(vr, vg, vb, va); }

        void set_clamped(int vr, int vg, int vb, int va) { c[0] = clamp255(vr); c[1] = clamp255(vg);    c[2] = clamp255(vb); c[3] = clamp255(va); }

        uint8_t operator[] (uint32_t idx) const { assert(idx < 4); return c[idx]; }
        uint8_t &operator[] (uint32_t idx) { assert(idx < 4); return c[idx]; }

        bool operator== (const color32&rhs) const { return m == rhs.m; }

        static color32 comp_min(const color32& a, const color32& b) { return color32(cNoClamp, minimum(a[0], b[0]), minimum(a[1], b[1]), minimum(a[2], b[2]), minimum(a[3], b[3])); }
        static color32 comp_max(const color32& a, const color32& b) { return color32(cNoClamp, maximum(a[0], b[0]), maximum(a[1], b[1]), maximum(a[2], b[2]), maximum(a[3], b[3])); }
    };

    class color_rgba
    {
    public:
        union
        {
            uint8_t m_comps[4];

            struct
            {
                uint8_t r;
                uint8_t g;
                uint8_t b;
                uint8_t a;
            };
        };

        inline color_rgba()
        {
            static_assert(sizeof(*this) == 4, "sizeof(*this) != 4");
            static_assert(sizeof(*this) == sizeof(color32), "sizeof(*this) != sizeof(color32)");
        }

        // Not too hot about this idea.
        inline color_rgba(const color32& other) :
            r(other.r),
            g(other.g),
            b(other.b),
            a(other.a)
        {
        }

        color_rgba& operator= (const color32& rhs)
        {
            r = rhs.r;
            g = rhs.g;
            b = rhs.b;
            a = rhs.a;
            return *this;
        }

        inline color_rgba(int y)
        {
            set(y);
        }

        inline color_rgba(int y, int na)
        {
            set(y, na);
        }

        inline color_rgba(int sr, int sg, int sb, int sa)
        {
            set(sr, sg, sb, sa);
        }

        inline color_rgba(eNoClamp, int sr, int sg, int sb, int sa)
        {
            set_noclamp_rgba((uint8_t)sr, (uint8_t)sg, (uint8_t)sb, (uint8_t)sa);
        }

        inline color_rgba& set_noclamp_y(int y)
        {
            m_comps[0] = (uint8_t)y;
            m_comps[1] = (uint8_t)y;
            m_comps[2] = (uint8_t)y;
            m_comps[3] = (uint8_t)255;
            return *this;
        }

        inline color_rgba &set_noclamp_rgba(int sr, int sg, int sb, int sa)
        {
            m_comps[0] = (uint8_t)sr;
            m_comps[1] = (uint8_t)sg;
            m_comps[2] = (uint8_t)sb;
            m_comps[3] = (uint8_t)sa;
            return *this;
        }

        inline color_rgba &set(int y)
        {
            m_comps[0] = static_cast<uint8_t>(clamp<int>(y, 0, 255));
            m_comps[1] = m_comps[0];
            m_comps[2] = m_comps[0];
            m_comps[3] = 255;
            return *this;
        }

        inline color_rgba &set(int y, int na)
        {
            m_comps[0] = static_cast<uint8_t>(clamp<int>(y, 0, 255));
            m_comps[1] = m_comps[0];
            m_comps[2] = m_comps[0];
            m_comps[3] = static_cast<uint8_t>(clamp<int>(na, 0, 255));
            return *this;
        }

        inline color_rgba &set(int sr, int sg, int sb, int sa)
        {
            m_comps[0] = static_cast<uint8_t>(clamp<int>(sr, 0, 255));
            m_comps[1] = static_cast<uint8_t>(clamp<int>(sg, 0, 255));
            m_comps[2] = static_cast<uint8_t>(clamp<int>(sb, 0, 255));
            m_comps[3] = static_cast<uint8_t>(clamp<int>(sa, 0, 255));
            return *this;
        }

        inline color_rgba &set_rgb(int sr, int sg, int sb)
        {
            m_comps[0] = static_cast<uint8_t>(clamp<int>(sr, 0, 255));
            m_comps[1] = static_cast<uint8_t>(clamp<int>(sg, 0, 255));
            m_comps[2] = static_cast<uint8_t>(clamp<int>(sb, 0, 255));
            return *this;
        }

        inline color_rgba &set_rgb(const color_rgba &other)
        {
            r = other.r;
            g = other.g;
            b = other.b;
            return *this;
        }

        inline const uint8_t &operator[] (uint32_t index) const { assert(index < 4); return m_comps[index]; }
        inline uint8_t &operator[] (uint32_t index) { assert(index < 4); return m_comps[index]; }
        
        inline void clear()
        {
            m_comps[0] = 0;
            m_comps[1] = 0;
            m_comps[2] = 0;
            m_comps[3] = 0;
        }

        inline bool operator== (const color_rgba &rhs) const
        {
            if (m_comps[0] != rhs.m_comps[0]) return false;
            if (m_comps[1] != rhs.m_comps[1]) return false;
            if (m_comps[2] != rhs.m_comps[2]) return false;
            if (m_comps[3] != rhs.m_comps[3]) return false;
            return true;
        }

        inline bool operator!= (const color_rgba &rhs) const
        {
            return !(*this == rhs);
        }

        inline bool operator<(const color_rgba &rhs) const
        {
            for (int i = 0; i < 4; i++)
            {
                if (m_comps[i] < rhs.m_comps[i])
                    return true;
                else if (m_comps[i] != rhs.m_comps[i])
                    return false;
            }
            return false;
        }

        inline int get_601_luma() const { return (19595U * m_comps[0] + 38470U * m_comps[1] + 7471U * m_comps[2] + 32768U) >> 16U; }
        inline int get_709_luma() const { return (13938U * m_comps[0] + 46869U * m_comps[1] + 4729U * m_comps[2] + 32768U) >> 16U; } 
        inline int get_luma(bool luma_601) const { return luma_601 ? get_601_luma() : get_709_luma(); }

        inline color32 get_color32() const
        {
            return color32(r, g, b, a);
        }

        static color_rgba comp_min(const color_rgba& a, const color_rgba& b) { return color_rgba(minimum(a[0], b[0]), minimum(a[1], b[1]), minimum(a[2], b[2]), minimum(a[3], b[3])); }
        static color_rgba comp_max(const color_rgba& a, const color_rgba& b) { return color_rgba(maximum(a[0], b[0]), maximum(a[1], b[1]), maximum(a[2], b[2]), maximum(a[3], b[3])); }
    };

    const color_rgba g_black_color(0, 0, 0, 255);
    const color_rgba g_black_trans_color(0, 0, 0, 0);
    const color_rgba g_white_color(255, 255, 255, 255);

    typedef std::vector<color_rgba> color_rgba_vec;
    typedef std::vector<uint8_t> uint8_vec;

#if _WINDOWS
    struct GPUImageData
    {
        int m_width = 0;
        int m_height = 0;
        int m_pitch = 0;
        int m_data_size = 0;
        void* m_data = nullptr;
        nvtt::Format m_format = nvtt::Format::Format_RGB;
    };


    struct GPUImage
    {
        int m_width = 0;
        int m_height = 0;
        int m_total_size = 0;

        uint32_t m_mip_offsets[CUBE_FACE_NUM][MAX_MIP_NUM];
        int m_mip_count = 1;
        bool m_cubemap = false;
        std::vector<GPUImageData> m_mipmaps;   // images[face][mipmaps]
    };
#endif

    class image
    {
    public:
        image() : 
            m_width(0), m_height(0), m_pitch(0)
        {
        }

        image(uint32_t w, uint32_t h, uint32_t p = UINT32_MAX) : 
            m_width(0), m_height(0), m_pitch(0)
        {
            resize(w, h, p);
        }

        image(const uint8_t *pImage, uint32_t width, uint32_t height, uint32_t comps) :
            m_width(0), m_height(0), m_pitch(0)
        {
            init(pImage, width, height, comps);
        }

        image(const image &other) :
            m_width(0), m_height(0), m_pitch(0)
        {
            *this = other;
        }

        image &swap(image &other)
        {
            std::swap(m_width, other.m_width);
            std::swap(m_height, other.m_height);
            std::swap(m_pitch, other.m_pitch);
            m_pixels.swap(other.m_pixels);
            return *this;
        }

        image &operator= (const image &rhs)
        {
            if (this != &rhs)
            {
                m_width = rhs.m_width;
                m_height = rhs.m_height;
                m_pitch = rhs.m_pitch;
                m_pixels = rhs.m_pixels;
            }
            return *this;
        }

        image &clear()
        {
            m_width = 0; 
            m_height = 0;
            m_pitch = 0;
            clear_vector(m_pixels);
            return *this;
        }

        image &resize(uint32_t w, uint32_t h, uint32_t p = UINT32_MAX, const color_rgba& background = g_black_color)
        {
            return crop(w, h, p, background);
        }

        image &set_all(const color_rgba &c)
        {
            for (uint32_t i = 0; i < m_pixels.size(); i++)
                m_pixels[i] = c;
            return *this;
        }

        void init(const uint8_t *pImage, uint32_t width, uint32_t height, uint32_t comps)
        {
            assert(comps >= 1 && comps <= 4);
            
            resize(width, height);

            for (uint32_t y = 0; y < height; y++)
            {
                for (uint32_t x = 0; x < width; x++)
                {
                    const uint8_t *pSrc = &pImage[(x + y * width) * comps];
                    color_rgba &dst = (*this)(x, y);

                    if (comps == 1)
                    {
                        dst.r = pSrc[0];
                        dst.g = pSrc[0];
                        dst.b = pSrc[0];
                        dst.a = 255;
                    }
                    else if (comps == 2)
                    {
                        dst.r = pSrc[0];
                        dst.g = pSrc[0];
                        dst.b = pSrc[0];
                        dst.a = pSrc[1];
                    }
                    else
                    {
                        dst.r = pSrc[0];
                        dst.g = pSrc[1];
                        dst.b = pSrc[2];
                        if (comps == 4)
                            dst.a = pSrc[3];
                        else
                            dst.a = 255;
                    }
                }
            }
        }

        image &fill_box(uint32_t x, uint32_t y, uint32_t w, uint32_t h, const color_rgba &c)
        {
            for (uint32_t iy = 0; iy < h; iy++)
                for (uint32_t ix = 0; ix < w; ix++)
                    set_clipped(x + ix, y + iy, c);
            return *this;
        }

        image& fill_box_alpha(uint32_t x, uint32_t y, uint32_t w, uint32_t h, const color_rgba& c)
        {
            for (uint32_t iy = 0; iy < h; iy++)
                for (uint32_t ix = 0; ix < w; ix++)
                    set_clipped_alpha(x + ix, y + iy, c);
            return *this;
        }

        image &crop_dup_borders(uint32_t w, uint32_t h)
        {
            const uint32_t orig_w = m_width, orig_h = m_height;

            crop(w, h);

            if (orig_w && orig_h)
            {
                if (m_width > orig_w)
                {
                    for (uint32_t x = orig_w; x < m_width; x++)
                        for (uint32_t y = 0; y < m_height; y++)
                            set_clipped(x, y, get_clamped(minimum(x, orig_w - 1U), minimum(y, orig_h - 1U)));
                }

                if (m_height > orig_h)
                {
                    for (uint32_t y = orig_h; y < m_height; y++)
                        for (uint32_t x = 0; x < m_width; x++)
                            set_clipped(x, y, get_clamped(minimum(x, orig_w - 1U), minimum(y, orig_h - 1U)));
                }
            }
            return *this;
        }

        image &crop(uint32_t w, uint32_t h, uint32_t p = UINT32_MAX, const color_rgba &background = g_black_color)
        {
            if (p == UINT32_MAX)
                p = w;

            if ((w == m_width) && (m_height == h) && (m_pitch == p))
                return *this;

            if ((!w) || (!h) || (!p))
            {
                clear();
                return *this;
            }

            color_rgba_vec cur_state;
            cur_state.swap(m_pixels);

            m_pixels.resize(p * h);
            
            for (uint32_t y = 0; y < h; y++)
            {
                for (uint32_t x = 0; x < w; x++)
                {
                    if ((x < m_width) && (y < m_height))
                        m_pixels[x + y * p] = cur_state[x + y * m_pitch];
                    else
                        m_pixels[x + y * p] = background;
                }
            }

            m_width = w;
            m_height = h;
            m_pitch = p;

            return *this;
        }

        inline const color_rgba &operator() (uint32_t x, uint32_t y) const { assert(x < m_width && y < m_height); return m_pixels[x + y * m_pitch]; }
        inline color_rgba &operator() (uint32_t x, uint32_t y) { assert(x < m_width && y < m_height); return m_pixels[x + y * m_pitch]; }

        inline const color_rgba &get_clamped(int x, int y) const { return (*this)(clamp<int>(x, 0, m_width - 1), clamp<int>(y, 0, m_height - 1)); }
        inline color_rgba &get_clamped(int x, int y) { return (*this)(clamp<int>(x, 0, m_width - 1), clamp<int>(y, 0, m_height - 1)); }

        inline const color_rgba &get_clamped_or_wrapped(int x, int y, bool wrap_u, bool wrap_v) const
        {
            x = wrap_u ? posmod(x, m_width) : clamp<int>(x, 0, m_width - 1);
            y = wrap_v ? posmod(y, m_height) : clamp<int>(y, 0, m_height - 1);
            return m_pixels[x + y * m_pitch];
        }

        inline color_rgba &get_clamped_or_wrapped(int x, int y, bool wrap_u, bool wrap_v)
        {
            x = wrap_u ? posmod(x, m_width) : clamp<int>(x, 0, m_width - 1);
            y = wrap_v ? posmod(y, m_height) : clamp<int>(y, 0, m_height - 1);
            return m_pixels[x + y * m_pitch];
        }
        
        inline image &set_clipped(int x, int y, const color_rgba &c) 
        {
            if ((static_cast<uint32_t>(x) < m_width) && (static_cast<uint32_t>(y) < m_height))
                (*this)(x, y) = c;
            return *this;
        }

        inline image& set_clipped_alpha(int x, int y, const color_rgba& c)
        {
            if ((static_cast<uint32_t>(x) < m_width) && (static_cast<uint32_t>(y) < m_height))
                (*this)(x, y).m_comps[3] = c.m_comps[3];
            return *this;
        }

        // Very straightforward blit with full clipping. Not fast, but it works.
        image &blit(const image &src, int src_x, int src_y, int src_w, int src_h, int dst_x, int dst_y)
        {
            for (int y = 0; y < src_h; y++)
            {
                const int sy = src_y + y;
                if (sy < 0)
                    continue;
                else if (sy >= (int)src.get_height())
                    break;

                for (int x = 0; x < src_w; x++)
                {
                    const int sx = src_x + x;
                    if (sx < 0)
                        continue;
                    else if (sx >= (int)src.get_height())
                        break;

                    set_clipped(dst_x + x, dst_y + y, src(sx, sy));
                }
            }

            return *this;
        }

        const image &extract_block_clamped(color_rgba *pDst, uint32_t src_x, uint32_t src_y, uint32_t w, uint32_t h) const
        {
            for (uint32_t y = 0; y < h; y++)
                for (uint32_t x = 0; x < w; x++)
                    *pDst++ = get_clamped(src_x + x, src_y + y);
            return *this;
        }

        image &set_block_clipped(const color_rgba *pSrc, uint32_t dst_x, uint32_t dst_y, uint32_t w, uint32_t h)
        {
            for (uint32_t y = 0; y < h; y++)
                for (uint32_t x = 0; x < w; x++)
                    set_clipped(dst_x + x, dst_y + y, *pSrc++);
            return *this;
        }

        inline uint32_t get_width() const { return m_width; }
        inline uint32_t get_height() const { return m_height; }
        inline uint32_t get_pitch() const { return m_pitch; }
        inline uint32_t get_total_pixels() const { return m_width * m_height; }

        inline uint32_t get_block_width(uint32_t w) const { return (m_width + (w - 1)) / w; }
        inline uint32_t get_block_height(uint32_t h) const { return (m_height + (h - 1)) / h; }
        inline uint32_t get_total_blocks(uint32_t w, uint32_t h) const { return get_block_width(w) * get_block_height(h); }

        inline const color_rgba_vec &get_pixels() const { return m_pixels; }
        inline color_rgba_vec &get_pixels() { return m_pixels; }

        inline const color_rgba *get_ptr() const { return &m_pixels[0]; }
        inline color_rgba *get_ptr() { return &m_pixels[0]; }

        bool has_alpha() const
        {
            for (uint32_t y = 0; y < m_height; ++y)
                for (uint32_t x = 0; x < m_width; ++x)
                    if ((*this)(x, y).a < 255)
                        return true;

            return false;
        }

        image &set_alpha(uint8_t a)
        {
            for (uint32_t y = 0; y < m_height; ++y)
                for (uint32_t x = 0; x < m_width; ++x)
                    (*this)(x, y).a = a;
            return *this;
        }

        image &flip_y()
        {
            for (uint32_t y = 0; y < m_height / 2; ++y)
                for (uint32_t x = 0; x < m_width; ++x)
                    std::swap((*this)(x, y), (*this)(x, m_height - 1 - y));
            return *this;
        }

    private:
        uint32_t m_width, m_height, m_pitch;  // all in pixels
        color_rgba_vec m_pixels;
    };

        // Image saving/loading/resampling
    
    CROSS_IMAGE_API bool load_png(const uint8_t* pBuf, size_t buf_size, image& img, const char* pFilename = nullptr);
    CROSS_IMAGE_API bool load_png(const char* pFilename, image& img);
    CROSS_IMAGE_API inline bool load_png(const std::string &filename, image &img) { return load_png(filename.c_str(), img); }

    CROSS_IMAGE_API bool load_bmp(const char* pFilename, image& img);
    CROSS_IMAGE_API inline bool load_bmp(const std::string &filename, image &img) { return load_bmp(filename.c_str(), img); }
        
    CROSS_IMAGE_API bool load_tga(const char* pFilename, image& img);
    CROSS_IMAGE_API inline bool load_tga(const std::string &filename, image &img) { return load_tga(filename.c_str(), img); }

    CROSS_IMAGE_API bool load_jpg(const char *pFilename, image& img);
    CROSS_IMAGE_API inline bool load_jpg(const std::string &filename, image &img) { return load_jpg(filename.c_str(), img); }
#if _WINDOWS
    CROSS_IMAGE_API bool load_tiff(const char* pFilename, cmft::Image &img);

    CROSS_IMAGE_API bool load_exr(const char* pFilename, cmft::Image& img, bool isSRGB);

    CROSS_IMAGE_API void save_exr(const char* pFilename, int width, int height, void* float4Pixels, bool isSRGB);

    // CROSS_IMAGE_API bool compress_exr(const char* pFilename, cmft::Image &img);
    CROSS_IMAGE_API bool compress_hdr(const char* pFilename, GPUImage &image, double shCoefs[SH_COEFF_NUM][3]);

    CROSS_IMAGE_API bool compress_hdr(cmft::Image &src, GPUImage &dst, double shCoeffs[SH_COEFF_NUM][3]);

    CROSS_IMAGE_API void flip_yz_cubemap(cmft::Image& src);
    
    CROSS_IMAGE_API bool compress2dxbcnvtt(const char* pFilename, GPUImage& image, bool cubemap, bool generate_mips, bool prefilter_mips, nvtt::Format dxformat);

    CROSS_IMAGE_API bool compress2dxbcnvtt(cmft::Image& src, GPUImage& dst, bool cubemap, bool generate_mips, bool prefilter_mips, nvtt::Format dxformat, bool FlipYZ = false, int glossBias = 1);

    CROSS_IMAGE_API uint32_t compute_compressed_size(nvtt::Format format, int width, int height);

    CROSS_IMAGE_API uint32_t compute_pitch(nvtt::Format format, int width);
#endif

   

    // Currently loads .BMP, .PNG, or .TGA.
    CROSS_IMAGE_API bool load_image(const char* pFilename, image& img);
    CROSS_IMAGE_API inline bool load_image(const std::string &filename, image &img) { return load_image(filename.c_str(), img); }

    CROSS_IMAGE_API uint8_t *read_tga(const uint8_t *pBuf, uint32_t buf_size, int &width, int &height, int &n_chans);
    CROSS_IMAGE_API uint8_t *read_tga(const char *pFilename, int &width, int &height, int &n_chans);
        
    enum
    {
        cImageSaveGrayscale = 1,
        cImageSaveIgnoreAlpha = 2,
        cImageSaveAll = 4,
    };

    CROSS_IMAGE_API bool save_png(const char* pFilename, const image& img, uint32_t image_save_flags = 0, uint32_t grayscale_comp = 0);
    CROSS_IMAGE_API inline bool save_png(const std::string &filename, const image &img, uint32_t image_save_flags = 0, uint32_t grayscale_comp = 0) { return save_png(filename.c_str(), img, image_save_flags, grayscale_comp); }

    CROSS_IMAGE_API bool save_png(std::vector<uint8_t> &out, const image& img, uint32_t image_save_flags = 0, uint32_t grayscale_comp = 0);

    CROSS_IMAGE_API inline unsigned int save_bmp(const char* filename, unsigned char* pixels_ptr, int w, int h, unsigned int n_chans){ return apg_bmp_write( filename, pixels_ptr, w, h, n_chans );}

    bool read_file_to_vec(const char* pFilename, uint8_vec& data);
    
    bool write_data_to_file(const char* pFilename, const void* pData, size_t len);
    
    inline bool write_vec_to_file(const char* pFilename, const uint8_vec& v) {    return v.size() ? write_data_to_file(pFilename, &v[0], v.size()) : write_data_to_file(pFilename, "", 0); }

    CROSS_IMAGE_API float linear_to_srgb(float l);
    CROSS_IMAGE_API float srgb_to_linear(float s);

    CROSS_IMAGE_API bool image_resample(const image &src, image &dst, bool srgb = true,
        const char *pFilter = "lanczos4", float filter_scale = 1.0f, 
        bool wrapping = false,
        uint32_t first_comp = 0, uint32_t num_comps = 4);

    CROSS_IMAGE_API bool generate_mipmaps(const image& img, std::vector<image>& mips, bool srgb);
    }

#pragma warning(pop)
