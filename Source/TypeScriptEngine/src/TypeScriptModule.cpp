#include "TypeScriptEngine/TypeScriptModule.h"
#include "JsEnv.h"
#include "Common/EngineGlobal.h"
#include "Common/SettingsManager.h"
#include "Profiling/Profiling.h"

#include "TimeDef.h"
#ifndef _MANAGED
#    include "tracy/Tracy.hpp"
#    include "tracy/TracyC.h"
#endif
#include "memoryhooker/Module.h"
IMPORT_MODULE
namespace cross {
struct TypeScriptModule::TypeScriptModuleParam
{
    bool bInitV8 = false;
#pragma region TypeScriptDebug
    bool bEnableDebug = false;
    int DebugPort = 9229;
    bool bWaitDebugger = false;
    std::string InFlags;     //flags like --expose-gc, --trace-gc .etc, please refer to V8 or puerts for detail explanation.
    bool ProfileGC = false; // profile GC
    bool IdleGC = true;     // whether notify typescript to do GC in idle time;
    float IdleSecondsThreshold = 0.002f; // how many idle seconds that is enough for a possible gc;
#pragma endregion TypeScriptDebug
};








static TracyCZoneCtx context;

void AddGCPrologueCallback(v8::Isolate* isolate, v8::GCType gc_type, v8::GCCallbackFlags flags, void* data)
{
    static constexpr ___tracy_source_location_data gc_location{"TypeScriptGC", TracyFunction, TracyFile, (uint32_t)TracyLine, 0};
    context = ___tracy_emit_zone_begin(&gc_location, true);
}


void AddGCEpilogueCallback(v8::Isolate* isolate, v8::GCType gc_type, v8::GCCallbackFlags flags, void* data)
{
    ___tracy_emit_zone_end(context);
}

TypeScriptModule::TypeScriptModule()
{
    mParam = std::make_unique<TypeScriptModuleParam>();


    auto settingManager = EngineGlobal::GetSettingMgr();
    if (settingManager)
    {
        settingManager->GetValue("TypeScriptDebug.enable", mParam->bEnableDebug);
        settingManager->GetValue("TypeScriptDebug.debugPort", mParam->DebugPort);
        settingManager->GetValue("TypeScriptDebug.bWaitDebugger", mParam->bWaitDebugger);
        settingManager->GetValue("TypeScriptDebug.InFlags", mParam->InFlags);
        settingManager->GetValue("TypeScriptDebug.ProfileGC", mParam->ProfileGC);
        settingManager->GetValue("TypeScriptDebug.IdleGC", mParam->IdleGC);
        settingManager->GetValue("TypeScriptDebug.IdleSecondsThreshold", mParam->IdleSecondsThreshold);

    }

    puerts::StartupJsEnvModule(mParam->InFlags);
    mParam->bInitV8 = true;
}

TypeScriptModule::~TypeScriptModule()
{
    Destroy();
}

void TypeScriptModule::Destroy()
{
    mJsEnv.reset();
    if (mParam->bInitV8)
    {
        puerts::ShutdownJsEnvModule();
        mParam->bInitV8 = false;
    }
}

TypeScriptModule* TypeScriptModule::Instance()
{
    static TypeScriptModule instance;
    return &instance;
}

void TypeScriptModule::Start(bool PIE)
{

    if (mParam->bEnableDebug)
    {
        mJsEnv = std::make_unique<PUERTS_NAMESPACE::FJsEnv>(std::make_unique<puerts::DefaultJSModuleLoader>("JavaScript"), std::make_shared<puerts::FDefaultLogger>(), mParam->DebugPort);
        if (mParam->bWaitDebugger && (PIE || EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpType::AppStartUpTypeStandAlone))
        {
            mJsEnv->WaitDebugger();
        }
    }
    else
    {
        mJsEnv = std::make_unique<PUERTS_NAMESPACE::FJsEnv>();
    }

    // for "import {xxx} form "cpp"" in .mts file, can work.
    // TODO how to don't need this code
    std::string cppType;
    ForeachRegisterClass([&cppType](const puerts::JSClassDefinition* ClassDefinition) { cppType += fmt::format("cpp.{};\n", ClassDefinition->ScriptName); });
    ForeachRegisterEnum([&cppType](const puerts::JSEnumDefinition* EnumDefinition) { cppType += fmt::format("cpp.{};\n", EnumDefinition->EnumName); });
    std::string startScript = fmt::format(R"((function() {{
    console.log("Cross Engine TypeScript Startup");
    const cpp = require("cpp");
{}
}})() )",
                                          cppType);
    mJsEnv->Start(startScript, {}, true);


    if(mParam->ProfileGC)
    {
        mJsEnv->AddGCPrologueCallback(AddGCPrologueCallback);
        mJsEnv->AddGCEpilogueCallback(AddGCEpilogueCallback);
    }
}

void TypeScriptModule::Stop()
{
    mJsEnv.reset();
}

TypeScriptObject TypeScriptModule::BindScriptObject(std::string_view model_name, const ClassTypeId* BindObjectTypeId, ClassObject* BindObject, std::string_view scriptEditorFieldsJson)
{
    return mJsEnv->CreateTypeScriptObject(FString(model_name), BindObjectTypeId, BindObject, scriptEditorFieldsJson);
}

bool TypeScriptModule::ReloadScriptModule(std::string_view model_name, const std::string& script_content)
{
    return mJsEnv->ReloadModule(FString(model_name), script_content);
}

void TypeScriptModule::Tick(float DeltaTime, float DurationTime)
{
    if (DeltaTime < DurationTime)
    {
        SCOPED_CPU_TIMING(GroupEngine, "SuggestGarbageCollect");
        SuggestGarbageCollect(DurationTime - DeltaTime);
    }

    mJsEnv->Tick(DeltaTime);
}

void TypeScriptModule::SuggestGarbageCollect(float time_budget_in_seconds)
{
    if(mParam->IdleGC)
    {
        // give GC some times to do real work
        if (time_budget_in_seconds > mParam->IdleSecondsThreshold)
        {
            mJsEnv->IdleNotificationDeadline(time_budget_in_seconds);
        }
        
    }
}

std::string TypeScriptModule::GetScriptEditorFieldsJson(const TypeScriptObject& typeScriptObject)
{
    return mJsEnv->GetScriptEditorFieldsJson(typeScriptObject);
}

bool TypeScriptModule::SetScriptDefaultValue(const TypeScriptObject& typeScriptObject, const std::string& scriptDefaultValueJson)
{
    return mJsEnv->SetScriptDefaultValue(typeScriptObject, scriptDefaultValueJson);
}

void TypeScriptModule::GetReferenceResource(const TypeScriptObject& typeScriptObject, const std::string& resourceGuid)
{
    return mJsEnv->GetReferenceResource(typeScriptObject, resourceGuid);
}

struct DeclarationGenerator
{
    std::vector<std::string> classes;
    std::vector<std::string> enumes;

    void GenArguments(const puerts::CFunctionInfo* Type, std::stringstream& ss)
    {
        for (unsigned int i = 0; i < Type->ArgumentCount(); i++)
        {
            if (i != 0)
                ss << ", ";
            auto argInfo = Type->Argument(i);

            ss << "p" << i;

            if (i >= Type->ArgumentCount() - Type->DefaultCount())
            {
                ss << "?";
            }

            ss << ": ";

            if (strcmp(argInfo->Name(), "cstring") != 0 && !argInfo->IsUEType() && !argInfo->IsObjectType() && argInfo->IsPointer())
            {
                ss << "ArrayBuffer";
            }
            else
            {
                bool IsReference = argInfo->IsRef();
                bool IsNullable = !IsReference && argInfo->IsPointer();
                if (IsNullable)
                {
                    ss << "$Nullable<";
                }
                if (IsReference)
                {
                    ss << "$Ref<";
                }

                const puerts::CTypeInfo* TypeInfo = Type->Argument(i);
                ss << TypeInfo->Name();

                if (IsNullable)
                {
                    ss << ">";
                }
                if (IsReference)
                {
                    ss << ">";
                }
            }
        }
    }

    void GenClassName(const char* name, std::stringstream& ss)
    {
        const char* pp = strchr(name, '.');
        while (pp)
        {
            const char* tmp = strchr(pp + 1, '.');
            if (tmp)
            {
                pp = tmp;
            }
            else   // no next .
            {
                break;
            }
        }
        if (pp)
        {
            ss << (pp + 1);
        }
        else
        {
            ss << name;
        }
    }

    void GenClass(const puerts::JSClassDefinition* ClassDefinition)
    {
        std::stringstream Output;

        Output << "    class ";
        GenClassName(ClassDefinition->ScriptName, Output);
        if (ClassDefinition->SuperTypeId)
        {
            Output << " extends ";
            GenClassName(FindClassByID(ClassDefinition->SuperTypeId)->ScriptName, Output);
        }
        Output << " {\n";

        std::set<std::string> AddedFunctions;

        puerts::NamedFunctionInfo* ConstructorInfo = ClassDefinition->ConstructorInfos;
        while (ConstructorInfo && ConstructorInfo->Name && ConstructorInfo->Type)
        {
            std::stringstream Tmp;
            Tmp << "        constructor(";
            GenArguments(ConstructorInfo->Type, Tmp);
            Tmp << ");\n";
            if (AddedFunctions.find(Tmp.str()) == AddedFunctions.end())
            {
                AddedFunctions.emplace(Tmp.str());
                Output << Tmp.str();
            }
            ++ConstructorInfo;
        }

        puerts::NamedPropertyInfo* PropertyInfo = ClassDefinition->PropertyInfos;
        while (PropertyInfo && PropertyInfo->Name && PropertyInfo->Type)
        {
            Output << "        " << PropertyInfo->Name << ": " << PropertyInfo->Type->Name() << ";\n";
            ++PropertyInfo;
        }

        puerts::NamedPropertyInfo* VariableInfo = ClassDefinition->VariableInfos;
        while (VariableInfo && VariableInfo->Name && VariableInfo->Type)
        {
            int Pos = VariableInfo - ClassDefinition->VariableInfos;
            Output << "        static " << (ClassDefinition->Variables[Pos].Setter ? "" : "readonly ") << VariableInfo->Name << ": " << VariableInfo->Type->Name() << ";\n";
            ++VariableInfo;
        }

        puerts::NamedFunctionInfo* FunctionInfo = ClassDefinition->FunctionInfos;
        while (FunctionInfo && FunctionInfo->Name && FunctionInfo->Type)
        {
            std::stringstream Tmp;
            Tmp << "        static " << FunctionInfo->Name;
            if (FunctionInfo->Type->Return())
            {
                Tmp << "(";
                GenArguments(FunctionInfo->Type, Tmp);
                const puerts::CTypeInfo* ReturnType = FunctionInfo->Type->Return();
                Tmp << ") :" << ReturnType->Name() << ";\n";
            }
            else
            {
                Tmp << FunctionInfo->Type->CustomSignature() << ";\n";
            }
            if (AddedFunctions.find(Tmp.str()) == AddedFunctions.end())
            {
                AddedFunctions.emplace(Tmp.str());
                Output << Tmp.str();
            }
            ++FunctionInfo;
        }

        puerts::NamedFunctionInfo* MethodInfo = ClassDefinition->MethodInfos;
        while (MethodInfo && MethodInfo->Name && MethodInfo->Type)
        {
            std::stringstream Tmp;
            Tmp << "        " << MethodInfo->Name;
            if (MethodInfo->Type->Return())
            {
                Tmp << "(";
                GenArguments(MethodInfo->Type, Tmp);
                const puerts::CTypeInfo* ReturnType = MethodInfo->Type->Return();
                Tmp << ") :" << ReturnType->Name() << ";\n";
            }
            else
            {
                Tmp << MethodInfo->Type->CustomSignature() << ";\n";
            }
            if (AddedFunctions.find(Tmp.str()) == AddedFunctions.end())
            {
                AddedFunctions.emplace(Tmp.str());
                Output << Tmp.str();
            }
            ++MethodInfo;
        }

        Output << "    }\n\n";

        classes.push_back(Output.str());
    }

    void GenEnum(const puerts::JSEnumDefinition* ClassDefinition)
    {
        std::stringstream Output;
        std::string_view fullEnumName = ClassDefinition->EnumName;
        std::string_view enumName = fullEnumName;
        std::string_view enumNameSpace;

        if (auto nameSpaceId = fullEnumName.find_last_of('.'); nameSpaceId != std::string_view::npos)
        {
            enumNameSpace = fullEnumName.substr(0, nameSpaceId);
            enumName = fullEnumName.substr(nameSpaceId + 1);
        }

        if (!enumNameSpace.empty())
            Output << "    export namespace " << enumNameSpace << "\n    {\n";

        Output << "        export enum " << enumName << " {\n";

        auto EnumFiled = ClassDefinition->EnumFileds;
        while (EnumFiled && EnumFiled->Name)
        {
            Output << "            " << EnumFiled->Name << " = " << EnumFiled->value << ",\n";
            ++EnumFiled;
        }

        Output << "        }\n\n";

        if (!enumNameSpace.empty())
            Output << "    }\n\n";

        enumes.push_back(Output.str());
    }

    std::string GetDeclarationOutput()
    {
        std::stringstream Output;

        {
            Output << R"(declare module "cpp" {
    import * as cpp from "cpp"
    import {$Ref, $Nullable, cstring} from "puerts"
)";

            for (int i = 0; i < enumes.size(); i++)
            {
                Output << enumes[i];
            }

            for (int i = 0; i < classes.size(); i++)
            {
                Output << classes[i];
            }
            Output << "}\n";
        }
        return Output.str();
    }

    std::string GetEnumImplOutput()
    {
        std::stringstream Output;
        for (int i = 0; i < enumes.size(); i++)
        {
            Output << enumes[i];
        }
        return Output.str();
    }
};

std::string TypeScriptModule::GenerateTypeScriptDeclaration(bool buildIn)
{
    DeclarationGenerator dg;

    ForeachRegisterEnum([&dg, buildIn](const puerts::JSEnumDefinition* enumDefinition) {
        if (enumDefinition->EnumName)
        {
            if (!buildIn || (buildIn && (enumDefinition->EnumRegisterType == puerts::RegisterType::Core)))
            {
                dg.GenEnum(enumDefinition);
            }
        }
    });

    ForeachRegisterClass([&dg, buildIn](const puerts::JSClassDefinition* ClassDefinition) {
        if (ClassDefinition->TypeId && ClassDefinition->ScriptName)
        {
            if (!buildIn || (buildIn && (ClassDefinition->ClassRegisterType == puerts::RegisterType::Core)))
            {
                dg.GenClass(ClassDefinition);
            }
        }
    });
    return dg.GetDeclarationOutput();
}

std::string TypeScriptModule::GenerateTypeScriptEnumImpl(bool buildIn)
{
    DeclarationGenerator dg;

    ForeachRegisterEnum([&dg, buildIn](const puerts::JSEnumDefinition* enumDefinition) {
        if (enumDefinition->EnumName)
        {
            if (!buildIn || (buildIn && (enumDefinition->EnumRegisterType == puerts::RegisterType::Core)))
            {
                dg.GenEnum(enumDefinition);
            }
        }
    });
    return dg.GetEnumImplOutput();
}

}   // namespace cross