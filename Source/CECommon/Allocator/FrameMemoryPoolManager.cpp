#include "PlatformPrefix.h"
#include <new>
#include <stdlib.h>
#include "FrameMemoryPoolManager.h"
#include "Log.h"
#include "memory/Module.h"
#include "CrossBase/Profiling/Profiling.h"
#include "tracy/Tracy.hpp"
#include "CrossBase/Template/TypeTraits.hpp"
namespace cross
{
constexpr size_t tlsfpoolsize = 1024 * 1024 * 64;
constexpr size_t tlsfpoolextendsize = 1024 * 1024 * 64;
constexpr size_t tlsfpoolsizeLarge = 1024 * 1024 * 512;
constexpr size_t tlsfpoolextendsizeLarge = 1024 * 1024 * 512;
gbf::allocator::RangeAllocator FrameMemoryPoolManager::mRangeAllocator(tlsfpoolsize, tlsfpoolextendsize);
gbf::allocator::RangeAllocator FrameMemoryPoolManager::mRangeAllocatorLarge(tlsfpoolsizeLarge, tlsfpoolextendsizeLarge);
MemoryBlock::MemoryBlock(const UInt32 size, const MemoryAlignmentSize align, void* buffer, UInt8 lifetime)
    : mSize(size), mAlign(align), mBuffer(buffer)
{
    QUICK_SCOPED_CPU_TIMING("MemoryPoolAllocate");
    Assert(size >= DefaultMemoryPageSize && size % DefaultMemoryPageSize == 0);
    if (!mBuffer)
    {
        mBuffer = FrameMemoryPoolManager::mRangeAllocator.Allocate(size, align).first;
        if (mBuffer == nullptr)
        {
            mBuffer = FrameMemoryPoolManager::mRangeAllocatorLarge.Allocate(size, align).first;
            mUseLargePool = true;
        }
        ALLOC_TAGED(mBuffer, size, "FrameMemory");
    }
}

MemoryBlock::~MemoryBlock()
{
    if (mBuffer)
    {
        QUICK_SCOPED_CPU_TIMING("MemoryPoolDelete");
        if (mUseLargePool)
        {
            FrameMemoryPoolManager::mRangeAllocatorLarge.Free(mBuffer);
        }
        else
        {
            FrameMemoryPoolManager::mRangeAllocator.Free(mBuffer);
        }
        FREE_TAGED(mBuffer, "FrameMemory");
        mBuffer = nullptr;

    }
}

bool MemoryBlock::Disable()
{
    return true;
}

bool MemoryBlock::Enable()
{
    return true;
}

MemoryBlock* FrameMemoryPoolManager::GetFrameMemoryBlock_threadsafe(const UInt32 frameParamIdx, const UInt8 times, const UInt8 lifetime)
{
    MemoryBlock* reservedBlock = nullptr;
    {
        std::lock_guard<std::mutex> lock(mMtx);
        {
            auto newBlock = new MemoryBlock(mBlockSizeUnit * times, mAlignment, nullptr, lifetime);
            RealMallocCountFrame++;
            return newBlock;
        }
    }
    if (reservedBlock->Enable())
    {
        return reservedBlock;
    }
    else
    {
        AssertMsg(false, "Memory block cannot be enabled, please check if it is out of memory. If not, it may have been freed incorrectly.");
        return nullptr;
    }
}
FrameMemoryPoolManager::FrameMemoryPoolManager() {}

bool FrameMemoryPoolManager::ReturnFrameMemoryBlock_threadsafe(MemoryBlock* block, const UInt32 frameParamIdx){
    return true;
}

void FrameMemoryPoolManager::ReleaseUnusedBlocks_threadsafe_force()
{
    for (UInt32 i = 0; i < MAX_FRAME_PARAM_COUNT; ++i)
    {
        ReleaseUnusedBlocks_threadsafe_force(i);
    }
}

void FrameMemoryPoolManager::ReleaseUnusedBlocks_threadsafe_force(const UInt32 frameParamId)
{
    {
        std::lock_guard<std::mutex> lock(mMtx);
        for (auto& [times, reservedBlocks] : mAvailableFrameBlocks)
        {
            for (MemoryBlock* block : reservedBlocks)
            {
                delete block;
            }
            reservedBlocks.clear();
        }
    }
}

void FrameMemoryPoolManager::ReleaseUnusedBlocks_threadsafe_soft(const UInt32 frameParamId)
{
    {
        std::lock_guard<std::mutex> lock(mMtx);
        for (auto& [times, reservedBlocks] : mAvailableFrameBlocks)
        {
            SInt32 i = ToSInt32(reservedBlocks.size()) - 1;
            for (; i >= 0; --i)
            {
                MemoryBlock* block = reservedBlocks[i];
                block->RecordUnusedFrames();
                if (block->GetUnusedFrames() >= FRAME_MEMORY_MAINTAIN_TIME)
                {
                    QUICK_SCOPED_CPU_TIMING("FrameAllocatorFreeBlocks");
                    RealFreeCountFrame++;
                    delete block;
                    reservedBlocks[i] = nullptr;
                }
            }
            reservedBlocks.erase(std::remove(reservedBlocks.begin(), reservedBlocks.end(), nullptr), reservedBlocks.end());
        }
    }
}

void FrameMemoryPoolManager::EndFrame(const UInt32 frameParamId)
{
    //LOG_INFO("System Malloc this frame : {}", RealMallocCountFrame);
    //LOG_INFO("System Free this frame : {}", RealFreeCountFrame);
}

FrameMemoryPoolManager::~FrameMemoryPoolManager() {}

std::vector<MemoryBlock*>& FrameMemoryPoolManager::GetAvailableBlocks(const UInt32 frameParamId, const UInt8 times)
{
    return mAvailableFrameBlocks[times];
}

}
