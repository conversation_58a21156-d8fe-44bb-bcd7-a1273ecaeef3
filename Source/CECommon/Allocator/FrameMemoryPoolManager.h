#pragma once

#include <stdint.h>
#include <vector>
#include <mutex>
#include <unordered_set>
#include <unordered_map>
#include <memory>
#include <array>
#include "MemoryMacros.h"
#include "CECommon/Common/FrameStage.h"
#include "CECommon/Common/CECommonForward.h"
#include "base/memory/allocator/range_allocator.hpp"
namespace cross
{
class MemoryBlock final
{
public:
    MemoryBlock(const UInt32 size, const MemoryAlignmentSize align, void* buffer = nullptr, UInt8 lifetime = 1);
    ~MemoryBlock();
    static std::vector<MemoryBlock*> GetContinuousMemoryBlocks(const UInt32 size, const MemoryAlignmentSize align, const UInt32 count);
    inline UInt32 GetSize()const { return mSize; }
    inline UInt32 GetAlignment()const { return mAlign; }
    inline void* GetBuffer()const { return mBuffer; }
    inline bool IsPointerInBuffer(void* pointer) const
    {
        if (mBuffer)
            return ((UInt8*)pointer >= (UInt8*)mBuffer) && ((UInt8*)pointer < ((UInt8*)mBuffer + mSize));
        return false;
    }
    bool Disable();
    bool Enable();
    UInt8 GetUnusedFrames() { return mUnusedFrames; }
    void RecordUnusedFrames() { ++mUnusedFrames; }

    UInt8 mAllocatedFrame{0};
private:
    MemoryBlock(const MemoryBlock&) = delete;
    MemoryBlock& operator=(const MemoryBlock&) = delete;

    void* mBuffer{ nullptr };
    uint32_t mSize{ 0 };
    uint32_t mAlign{ MEM_ALIGN_64 };
    UInt8 mUnusedFrames{ 0 };
    bool mUseLargePool = false;
};

class FrameMemoryPoolManager final
{
    friend class LinearAllocator;
public:
    static FrameMemoryPoolManager& Instance()
    {
        static FrameMemoryPoolManager sInstance;
        return sInstance;
    }
    static gbf::allocator::RangeAllocator mRangeAllocator;
    static gbf::allocator::RangeAllocator mRangeAllocatorLarge;
    UInt32 RealMallocCountFrame = 0;
    UInt32 RealFreeCountFrame = 0;
    CECOMMON_API MemoryBlock* GetFrameMemoryBlock_threadsafe(const UInt32 frameParamIdx, const UInt8 times = 1, const UInt8 lifetime = 1);
    CECOMMON_API bool ReturnFrameMemoryBlock_threadsafe(MemoryBlock* block, const UInt32 frameParamIdx);
    CECOMMON_API void ReleaseUnusedBlocks_threadsafe_force();
    CECOMMON_API void ReleaseUnusedBlocks_threadsafe_force(const UInt32 frameParamId);
    CECOMMON_API void ReleaseUnusedBlocks_threadsafe_soft(const UInt32 frameParamId);
    CECOMMON_API void EndFrame(const UInt32 frameParamId);
    UInt32 GetBlockSizeUnit() const { return mBlockSizeUnit; }
    ~FrameMemoryPoolManager();

private:
    struct FrameBlocksState
    {
        std::vector<MemoryBlock*> reservedBlocks; // enable before use
    };

    FrameMemoryPoolManager();
    FrameMemoryPoolManager(const FrameMemoryPoolManager&) = delete;
    FrameMemoryPoolManager& operator=(const FrameMemoryPoolManager&) = delete;

    const UInt32 mBlockSizeUnit{ FRAME_MEMORY_BLOCK_SIZE };
    const MemoryAlignmentSize mAlignment{ MEM_ALIGN_PAGE };

    std::vector<MemoryBlock*>& GetAvailableBlocks(const UInt32 frameParamId, const UInt8 times);
    std::unordered_map<UInt8, std::vector<MemoryBlock*>> mAvailableFrameBlocks;
    std::unordered_map<UInt8, std::vector<MemoryBlock*>> mAvailableFrameBlocksMultiFrame;
    std::mutex mMtx;
};
}