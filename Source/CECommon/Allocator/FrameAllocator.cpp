
#include "PlatformPrefix.h"
#include "MemoryMacros.h"
#include "FrameAllocator.h"
#include "FrameMemoryPoolManager.h"
#include "FrameAllocatorListener.h"
#include "CECommon/Common/CmdSettings.h"
#include "memoryhooker/MemoryProfile.h"
#include "Log.h"

namespace cross {
//////////////////////////////////////////////////////////////////////////
LinearAllocator::LinearMemoryBlock::LinearMemoryBlock(MemoryBlock* memBlock)
{
    mMemBlock = memBlock;
    mSize = mMemBlock->GetSize();
    mFreeSize = mSize;
    mAvailableBufferStart = memBlock->GetBuffer();
}

void* LinearAllocator::LinearMemoryBlock::Allocate(const UInt32 size, const MemoryAlignmentSize align)
{
    void* result = std::align(align, size, mAvailableBufferStart, mFreeSize);
    if (result)
    {
        mAvailableBufferStart = (uint8_t*)mAvailableBufferStart + size;   // TODO
        mFreeSize -= size;
    }
    return result;
}
void LinearAllocator::LinearMemoryBlock::Reset()
{
    mFreeSize = mSize;
    mAvailableBufferStart = mMemBlock->GetBuffer();
}
LinearAllocator::LinearAllocator(UInt32 frameParamIdx, UInt32 LifeTime, UInt32 cachedFrameParamId, FrameStage cachedStage, UInt8 cachedMultiFrameKey)
    : mFrameParamIdx(frameParamIdx)
    , mLifeTime(LifeTime)
    , mCachedFrameParamId(cachedFrameParamId)
    , mCachedStage(cachedStage)
    , mCachedMultiFrameKey(cachedMultiFrameKey)
    , mRangeAllocator(1024 * 1024 * 2)
{
    mMemBlocks.reserve(8);
}

LinearAllocator::~LinearAllocator()
{
    // Remove from thread_local caches
    if (mCachedStage != FRAME_STAGE_INVALID)
    {
        // Single-frame cache
        if (mCachedFrameParamId < FrameAllocator::mStageAllocatorsForFrames.size())
        {
            auto& heap = FrameAllocator::mStageAllocatorsForFrames[mCachedFrameParamId];
            for (auto& ptr : heap)
            {
                if (ptr == this)
                {
                    ptr = nullptr;
                }
            }
        }
    }
    else
    {
        // Multi-frame cache
        auto it = FrameAllocator::mStageAllocatorsForMultiFrames.find(mCachedMultiFrameKey);
        if (it != FrameAllocator::mStageAllocatorsForMultiFrames.end() && it->second == this)
        {
            FrameAllocator::mStageAllocatorsForMultiFrames.erase(it);
        }
    }
}

void* LinearAllocator::Allocate(const UInt32 size, const MemoryAlignmentSize align, IFrameAllocatorListener* listener)
{
    if (size == 0)
    {
        return nullptr;
    }
    if (listener)
    {
        mListeners.emplace_back(listener);
    }

    UInt32 maxRequireSize = size + align - 1;

    // 步骤 1: 检查提示块 (快速路径)
    if (mPrevAllocBlockIdx >= 0 && mMemBlocks[mPrevAllocBlockIdx].GetFreeSize() >= maxRequireSize)
    {
        mMemBlocks[mPrevAllocBlockIdx].GetMemoryBlock()->mAllocatedFrame = 0;
        return mMemBlocks[mPrevAllocBlockIdx].Allocate(size, align);
    }

    // 步骤 2: 如果提示块失败，则遍历所有块寻找可用空间 (慢速但正确的路径)
    for (int i = 0; i < mMemBlocks.size(); ++i)
    {
        if (mMemBlocks[i].GetFreeSize() >= maxRequireSize)
        {
            mPrevAllocBlockIdx = i; 
            mMemBlocks[i].GetMemoryBlock()->mAllocatedFrame = 0;
            return mMemBlocks[i].Allocate(size, align);
        }
    }

    // 步骤 3: 如果所有现有块都空间不足，则创建新块 (最终路径)
    const UInt32 blockSizeUnit = FrameMemoryPoolManager::Instance().GetBlockSizeUnit();
    UInt32 needBlocksCount = maxRequireSize / blockSizeUnit + (maxRequireSize % blockSizeUnit ? 1 : 0);
    AssertMsg(needBlocksCount < 256, "Blocks count is descriped in UInt8, as block cannot be too big.");

    void* buffer = mRangeAllocator.Allocate(blockSizeUnit * ToUInt8(needBlocksCount), MEM_ALIGN_PAGE).first;
    MemoryBlock* block = new MemoryBlock(blockSizeUnit * ToUInt8(needBlocksCount), MEM_ALIGN_PAGE, buffer, (UInt8)mLifeTime);
    ALLOC_TAGED(buffer, blockSizeUnit * ToUInt8(needBlocksCount), "FrameMemory");
    mMemBlocks.emplace_back(block);
    mPrevAllocBlockIdx = static_cast<int>(mMemBlocks.size()) - 1; 
    return mMemBlocks.back().Allocate(size, align);
}

int LinearAllocator::GetAllocatedSize() const
{
    UInt32 size = 0;
    for (auto& block : mMemBlocks)
    {
        size += block.GetAllocatedSize();
    }
    return size;
}

void LinearAllocator::Froze()
{
    if (mFrozen)
        return;
    for (auto& listener : mListeners)
    {
        listener->NotifyBeforeRecycleMemory();
    }
    mListeners.clear();
    for (auto& block : mMemBlocks)
    {
        [[maybe_unused]] bool isDiabled = block.GetMemoryBlock()->Disable();
        Assert(isDiabled);
    }
    mFrozen = true;
}
void LinearAllocator::Trim()
{
    for (int64_t i = mMemBlocks.size() - 1; i >= 0; --i)
    {
        if (mMemBlocks[i].GetFreeSize() == mMemBlocks[i].GetSize())
        {
            mRangeAllocator.Free(mMemBlocks[i].GetMemoryBlock()->GetBuffer());
            FREE_TAGED(mMemBlocks[i].GetMemoryBlock()->GetBuffer(), "FrameMemory");
            delete mMemBlocks[i].GetMemoryBlock();
            mMemBlocks.erase(mMemBlocks.begin() + i);
        }
    }
    mPrevAllocBlockIdx = (int)(mMemBlocks.size() - 1);
    
    if (mMemBlocks.empty())
    {
        mRangeAllocator.Reset();
    }
}
void LinearAllocator::Reset(bool doTrim)
{
    QUICK_SCOPED_CPU_TIMING("FrameAllocator::Reset");
    {
        if (!mFrozen)
            Froze();
        int bestCandidateIndex = -1;
        size_t maxFreeSpace = 0;

        for (int i = 0; i < mMemBlocks.size(); ++i)
        {
            auto& block = mMemBlocks[i];
            block.GetMemoryBlock()->mAllocatedFrame++;

            if (block.GetMemoryBlock()->mAllocatedFrame > mLifeTime)
            {
                block.Reset();
                block.GetMemoryBlock()->mAllocatedFrame = 0;
            }
            if (block.GetFreeSize() > maxFreeSpace)
            {
                maxFreeSpace = block.GetFreeSize();
                bestCandidateIndex = i;
            }
        }
        mPrevAllocBlockIdx = bestCandidateIndex;
        constexpr std::uint32_t TrimTimeThreshold = 120;

        if (doTrim)
        {
            Trim();
        }
        mFrozen = false;
    }
}

thread_local std::array<TaggedHeap, MAX_FRAME_PARAM_COUNT> FrameAllocator::mStageAllocatorsForFrames;
thread_local CEHashMap<UInt8, LinearAllocator*> FrameAllocator::mStageAllocatorsForMultiFrames;
FrameAllocator::FrameAllocator(UInt8 LifeTime, UInt32 InstanceCount)
    : mLifeTime(LifeTime)
    , mFrameParamId(InstanceCount)
{
    Assert(mFrameParamId < MAX_FRAME_PARAM_COUNT);   // frame allocator is reused as well as frame param is rotated
}
FrameAllocator::~FrameAllocator() {}
void* FrameAllocator::Allocate(const UInt32 size, const FrameStage frameStage, const MemoryAlignmentSize align)
{
    return Allocate(size, frameStage, align, nullptr);
}

void* FrameAllocator::Allocate(const UInt32 size, const FrameStage frameStage, const MemoryAlignmentSize align, IFrameAllocatorListener* listener)
{
    if (mLifeTime == 1)
    {
        auto index = frameStage - 1;
        auto allocator = mStageAllocatorsForFrames[mFrameParamId][index];

        if (allocator == nullptr)
        {
            // Create allocator for this thread/frameParam/stage
            allocator = new LinearAllocator(mFrameParamId, mLifeTime, mFrameParamId, frameStage, 0);
            mStageAllocatorsForFrames[mFrameParamId][index] = allocator;
            // Push allocator to recycle list and hand out the lifetime control by unique_ptr
            {
                std::lock_guard<std::mutex> lock(mRecycleLsitMtx);
                if (frameStage == FRAME_STAGE_GAME)
                {
                    mGameStageAllocators.emplace_back(allocator);
                }
                else if (frameStage == FRAME_STAGE_RENDER)
                {
                    mRenderStageAllocators.emplace_back(allocator);
                }
                else if (frameStage == FRAME_STAGE_GAME_RENDER)
                {
                    allocator->SetLifeTime(CmdSettings::Inst().gMaxQueuedFrame);
                    mRenderStageAllocators.emplace_back(allocator);
                }
                else
                    Assert(false);
            }
        }
        return allocator->Allocate(size, align, listener);
    }
    else
    {
        if (mStageAllocatorsForMultiFrames.count(mLifeTime - 1) == 0)
        {
            // Create allocator for this thread/frameParam/stage
            auto allocator = new LinearAllocator(static_cast<UInt32>(-1), mLifeTime, 0, FRAME_STAGE_INVALID, mLifeTime - 1);
            mStageAllocatorsForMultiFrames[mLifeTime - 1] = allocator;
            // Push allocator to recycle list and hand out the lifetime control by unique_ptr
            {
                std::lock_guard<std::mutex> lock(mRecycleLsitMtx2);
                mMultiFrameAllocators.emplace_back(allocator);
            }
            return allocator->Allocate(size, align, listener);
        }
        else
        {
            return mStageAllocatorsForMultiFrames[mLifeTime - 1]->Allocate(size, align, listener);
        }
    }
}

UInt32 FrameAllocator::GetAllocatedSize() const
{
    UInt32 size = 0;

    for (auto& allocator : mGameStageAllocators)
    {
        size += allocator->GetAllocatedSize();
    }
    for (auto& allocator : mRenderStageAllocators)
    {
        size += allocator->GetAllocatedSize();
    }
    for (auto& allocator : mMultiFrameAllocators)
    {
        size += allocator->GetAllocatedSize();
    }
    return size;
}

void FrameAllocator::BeginGameStage()
{
    mFrameStage = FRAME_STAGE_GAME;
}

void FrameAllocator::EndGameStage()
{
    mFrameStage &= (UInt8)~FRAME_STAGE_GAME;
    std::lock_guard<std::mutex> lock(mRecycleLsitMtx);
    for (auto& allocator : mGameStageAllocators)
    {
        allocator->Froze();
    }
}

void FrameAllocator::BeginRenderStage()
{
    mFrameStage |= FRAME_STAGE_RENDER;
}

void FrameAllocator::EndRenderStage(bool doTrim)
{
    mFrameStage &= (UInt8)~FRAME_STAGE_RENDER;

    auto& memoryPool = FrameMemoryPoolManager::Instance();
    {
        std::lock_guard<std::mutex> lock(mRecycleLsitMtx);
        for (auto& allocator : mRenderStageAllocators)
        {
            allocator->Reset(doTrim);
        }
        for (auto& allocator : mGameStageAllocators)
        {
            allocator->Reset(doTrim);
        }
    }
    {
        std::lock_guard<std::mutex> lock(mRecycleLsitMtx2);
        for (auto& allocator : mMultiFrameAllocators)
        {
            allocator->Reset(doTrim);
        }
    }
}

void* FrameAllocator::do_allocate(std::size_t bytes, std::size_t alignment)
{
    return Allocate(static_cast<UInt32>(bytes), static_cast<FrameStage>(mFrameStage.load()), static_cast<MemoryAlignmentSize>(alignment));
}

void FrameAllocator::do_deallocate(void* p, std::size_t bytes, std::size_t alignment) {}

bool FrameAllocator::do_is_equal(const memory_resource& that) const noexcept
{
    return dynamic_cast<const FrameAllocator*>(&that) == this;
}

}   // namespace cross
