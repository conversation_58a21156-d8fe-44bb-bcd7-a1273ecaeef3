#pragma once
#include "CrossBase/Platform/PlatformTypes.h"


/// @file information about NDA format
#define MAKE_DWORD(A,B,C,D)((A<<24)|(B<<16)|(C<<8)|D)
#define MAKE_QWORD(A,B,C,D,E,F,G,H)((A<<56)|(B<<48)|(C<<40)|(D<<32)|(E<<24)|(F<<16)|(G<<8)|H)
constexpr UInt32 ASSET_MAGIC_NUMBER = MAKE_DWORD('.', 'n', 'd', 'a');
constexpr UInt32 ASSET_MAGIC_NUMBER_JMETA = MAKE_DWORD('j', 'n', 'd', 'a');
constexpr UInt32 ASSET_MAGIC_NUMBER_BMETA = MAKE_DWORD('b', 'n', 'd', 'a');
//tail magic number only used in editor
constexpr UInt32 NDA_TAIL_MAGIC = ('E' | ('N' << 8) | ('D' << 16) | ('!' << 24));

namespace cross
{
	//Version 1: Original
	//Version 2: Refactor ModelImporter
	//Version 3：FlatBuffer
    struct AssetHeader
    {
        int mMagicNumber = ASSET_MAGIC_NUMBER;
        int mVersion = 0x3;
        int mClassID = -1;
        int mDataSize = 0;
        //int mCRC32;
        int mReserved = 0;
    };
}
