#pragma once

#include "CECommon/Common/CECommonForward.h"
#include "CrossBase/PCH/CrossBasePCHPublic.h"
#include "Resource/BaseClasses/ClassIDs.h"
#include "ImportMeshAssetData_generated.h"
#include "TextureAsset_generated.h"
#include "MaterialAsset_generated.h"

#ifndef Win32
#    define __stdcall
#endif

namespace cross::serialize {

extern "C" CECOMMON_API void __stdcall SerializeMesh(const char* file, const CrossSchema::ImportMeshAssetDataT* data, const ClassIDType id = CLASS_MeshAssetDataResource, const char* guid = nullptr, SerializeNode customNode = SerializeNode{});

extern "C" CECOMMON_API void __stdcall SerializeTexture(const char* file, const CrossSchema::TextureAssetT* data, const ClassIDType id = CLASS_Texture2D, const char* guid = nullptr);

extern "C" CECOMMON_API void __stdcall SerializeMaterial(const char* file, const CrossSchema::MaterialAssetT* data, const ClassIDType id = CLASS_Material, const char* guid = nullptr);

}   // namespace cross::serialize
