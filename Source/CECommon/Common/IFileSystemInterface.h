#pragma once
#include <vector>
#include "CECommon/Common/CECommonForward.h"

namespace cross
{
namespace filesystem
{
const std::string OPENER_KEY("opener");
const std::string ROOT_KEY("root");
const std::string DYNAMIC_KEY("dynamic");
const std::string DEPTHNUM_KEY("depth");
const std::string DISCRETE_KEY("discrete");

class FileLoaderConfig
{
public:
    std::string mOpener = "os";
    std::string mFileLoader = DISCRETE_KEY;
    std::string mRootPath = "%WORK_DIR%";
    UInt32      mDepthNum = 0;
    bool        mDynamic = false;
    FileLoaderConfig() = default;
    FileLoaderConfig(const std::string& inOpener, const std::string& inFileLoader,
        const std::string& inRootPath, UInt32 inDepthNum, bool inIsDynamic = false)
        : mOpener(inOpener), mFile<PERSON>oader(inFileLoader), mRootPath(inRootPath), mDepthNum(inDepthNum), mDynamic(inIsDynamic)
    {}
};

using FileLoaderConfigList = std::vector<FileLoaderConfig>;
class FileSystemConfig
{
public:
    FileLoaderConfigList mFileLoaderConfigs;
};
}

class IFileSystemInfrastructure
{
public:
    virtual bool Init(const filesystem::FileSystemConfig& inConfig) = 0; 
    virtual bool InitSimple(const char* inDir) = 0; 
    virtual bool ReplaceString(std::string& str, const std::string& from, const std::string& to) = 0;

    virtual const std::string& GetWorkDir() const = 0;

    virtual const std::string& GetDocDir() const = 0;
};

}
