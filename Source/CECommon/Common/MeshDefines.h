#pragma once
#include "EnginePrefix.h"
#include "PlatformDefs.h"
#include "CECommon/Allocator/MemoryMacros.h"
#include "NativeGraphicsInterface/RHI/DeviceTypes.h"
namespace cross {
enum class CEMeta(Editor) VertexFrequency
{
    PerVertex,
    PerInstance,
    PerPatch,
    PerPatchControlPoint,
    Constant
};


// Do NOT change element order in following enum
enum class CEMeta(Editor) VertexFormat
{
    Unknown,
    Float,
    Float2,
    Float3,
    Float4,

    Half2,
    Half4,

    Byte4,
    UByte4,
    Byte4_Norm,
    UByte4_Norm,
    Color = UByte4_Norm,

    Short2,
    Short4,
    UShort2,
    UShort4,
    Short2_Norm,
    Short4_Norm,
    UShort2_Norm,
    UShort4_Norm,

    UInt_R10G10B10A2,
    UInt_R10G10B10A2_Norm,
    UInt4,
    INT4,

    UInt,//32bit
    INT,//32bit

    Short3_Norm,
    UShort3_Norm,
    Half3,
    Byte<PERSON>_Nor<PERSON>,
    UByte3_Norm,
};

CEMeta(Editor) constexpr UInt32 GetByteSize(VertexFormat fmt)
{
    switch (fmt)
    {
    case VertexFormat::Float4:
    case VertexFormat::UInt4:
    case VertexFormat::INT4:
        return 16;
    case VertexFormat::Float3:
        return 12;
    case VertexFormat::Float2:
        return 8;
    case VertexFormat::Short4:
    case VertexFormat::UShort4:
    case VertexFormat::Short4_Norm:
    case VertexFormat::UShort4_Norm:
        return 8;
    case VertexFormat::UInt:
    case VertexFormat::INT:
    case VertexFormat::Float:
    case VertexFormat::Half2:
    case VertexFormat::Byte4:
    case VertexFormat::UByte4:
    case VertexFormat::Byte4_Norm:
    case VertexFormat::UByte4_Norm:
    case VertexFormat::Short2:
    case VertexFormat::UShort2:
    case VertexFormat::Short2_Norm:
    case VertexFormat::UShort2_Norm:
    case VertexFormat::UInt_R10G10B10A2:
    case VertexFormat::UInt_R10G10B10A2_Norm:
        return 4;
    case VertexFormat::Half3:
    case VertexFormat::UShort3_Norm:
    case VertexFormat::Short3_Norm:
        return 6;
    case VertexFormat::Byte3_Norm:
    case VertexFormat::UByte3_Norm:
        return 3;
    default:
        AssertMsg(false, "False VertexFormat!!!");
        return 0;
    }
}

// the align calculation is based on 
// https://www.khronos.org/registry/vulkan/specs/1.2-extensions/html/vkspec.html#fxvertex-input-extraction
// warning! not all format is supported
CEMeta(Editor) constexpr UInt16 GetAlign(VertexFormat fmt)
{
    switch (fmt)
    {
    case VertexFormat::Float4:
    case VertexFormat::UInt4:
    case VertexFormat::INT4:
    case VertexFormat::Float3:
    case VertexFormat::Float2:
    case VertexFormat::UInt:
    case VertexFormat::INT:
    case VertexFormat::Float:
        return 4;
    case VertexFormat::Short4:
    case VertexFormat::UShort4:
    case VertexFormat::Short4_Norm:
    case VertexFormat::UShort4_Norm:
    case VertexFormat::Half2:
    case VertexFormat::Short2:
    case VertexFormat::UShort2:
    case VertexFormat::Short2_Norm:
    case VertexFormat::UShort2_Norm:
    case VertexFormat::Half3:
    case VertexFormat::UShort3_Norm:
    case VertexFormat::Short3_Norm:
        return 2;

    case VertexFormat::Byte4:
    case VertexFormat::UByte4:
    case VertexFormat::Byte4_Norm:
    case VertexFormat::UByte4_Norm:
    case VertexFormat::Byte3_Norm:
    case VertexFormat::UByte3_Norm:
        return 1;

    case VertexFormat::UInt_R10G10B10A2:
    case VertexFormat::UInt_R10G10B10A2_Norm:
    default:
        AssertMsg(false, "False VertexFormat!!!");
        return 0;
    }
}

enum class CEMeta(Editor) VertexSemanticSerial : UInt32
{
    Position = 0,
    Color,
    Normal,
    Tangent,
    BiNormal,
    BlendIndex,
    BlendWeight,
    TexCoord,
    PtSize,
    PositionT,
    TessFactor,
    InstanceData,
    QuatTan,
    Count   // keep it last
};

const std::array<VertexSemanticSerial, 13> gAllVertexSemanticSerial{VertexSemanticSerial::Position,
                                                                    VertexSemanticSerial::Color,
                                                                    VertexSemanticSerial::Normal,
                                                                    VertexSemanticSerial::Tangent,
                                                                    VertexSemanticSerial::BiNormal,
                                                                    VertexSemanticSerial::BlendIndex,
                                                                    VertexSemanticSerial::BlendWeight,
                                                                    VertexSemanticSerial::TexCoord,
                                                                    VertexSemanticSerial::PtSize,
                                                                    VertexSemanticSerial::PositionT,
                                                                    VertexSemanticSerial::TessFactor,
                                                                    VertexSemanticSerial::InstanceData,
                                                                    VertexSemanticSerial::QuatTan
};

const std::array<std::string_view, 13> gAllVertexSemanticSerialName{
    "POSITION",
    "COLOR",
    "NORMAL",
    "TANGENT",
    "BINORMAL",
    "BLENDINDEX",
    "BLENDWEIGHT",
    "TEXCOORD",
    "PSIZE",
    "POSITIONT",
    "TESSFACTOR",
    "INSTANCEDATA",
    "QUATTAN"
};

enum CEMeta(Editor) VertexSemantic
{
    SemanticPosition = 1 << 5,
    SemanticColor = 1 << 6,
    SemanticNormal = 1 << 7,
    SemanticTangent = 1 << 8,
    SemanticBiNormal = 1 << 9,
    SemanticBlendIndex = 1 << 10,
    SemanticBlendWeight = 1 << 11,
    SemanticTexCoord = 1 << 12,
    SemanticPSize = 1 << 13,
    SemanticPositionT = 1 << 14,   // Transformed Vertex Position
    SemanticTessFactor = 1 << 15,
    SemanticInstance = 1 << 16,
    SemanticQuatTan = 1 << 17
};

enum class CEMeta(Editor) VertexChannel : UInt32
{
    Unkown = 0,

    Position0 = 32 | 0,
    Position1 = 32 | 1,
    Position2 = 32 | 2,
    Position3 = 32 | 3,
    PositionLast = Position3,

    Color0 = 64 | 0,
    Color1 = 64 | 1,
    Color2 = 64 | 2,
    Color3 = 64 | 3,
    ColorLast = Color3,

    Normal0 = 128 | 0,
    Normal1 = 128 | 1,
    Normal2 = 128 | 2,
    Normal3 = 128 | 3,
    NormalLast = Normal3,

    Tangent0 = 256 | 0,
    Tangent1 = 256 | 1,
    Tangent2 = 256 | 2,
    Tangent3 = 256 | 3,
    TangentLast = Tangent3,

    BiNormal0 = 512 | 0,
    BiNormal1 = 512 | 1,
    BiNormal2 = 512 | 2,
    BiNormal3 = 512 | 3,
    BiNormalLast = BiNormal3,

    BlendIndex0 = 1024 | 0,
    BlendIndex1 = 1024 | 1,
    BlendIndex2 = 1024 | 2,
    BlendIndex3 = 1024 | 3,
    BlendIndexLast = BlendIndex3,

    BlendWeight0 = 2048 | 0,
    BlendWeight1 = 2048 | 1,
    BlendWeight2 = 2048 | 2,
    BlendWeight3 = 2048 | 3,
    BlendWeightLast = BlendWeight3,

    TexCoord0 = 4096 | 0,
    TexCoord1 = 4096 | 1,
    TexCoord2 = 4096 | 2,
    TexCoord3 = 4096 | 3,
    TexCoord4 = 4096 | 4,
    TexCoord5 = 4096 | 5,
    TexCoord6 = 4096 | 6,
    TexCoord7 = 4096 | 7,
    TexCoord8 = 4096 | 8,
    TexCoord9 = 4096 | 9,
    TexCoord10 = 4096 | 10,
    TexCoord11 = 4096 | 11,
    TexCoord12 = 4096 | 12,
    TexCoord13 = 4096 | 13,
    TexCoord14 = 4096 | 14,
    TexCoord15 = 4096 | 15,
    TexCoordLast = TexCoord15,

    PSize0 = 8192 | 0,
    PSize1 = 8192 | 1,
    PSize2 = 8192 | 2,
    PSize3 = 8192 | 3,
    PSizeLast = PSize3,

    PositionT = 16384 | 0,
    PositionTLast = PositionT,

    TessFactor0 = 32768 | 0,
    TessFactor1 = 32768 | 1,
    TessFactor2 = 32768 | 2,
    TessFactor3 = 32768 | 3,
    TessFactorLast = TessFactor3,

    InstanceData = 65536,
    //InstanceData0 = 65536 | 0,
    //InstanceData1 = 65536 | 1,
    //InstanceData2 = 65536 | 2,
    //InstanceData3 = 65536 | 3,
    //InstanceData4 = 65536 | 4,
    //InstanceData5 = 65536 | 5,
    //InstanceData6 = 65536 | 6,
    //InstanceData7 = 65536 | 7,
    //InstanceDataLast = InstanceData7,
    
    QUATTAN0 = 1 << 17,
    QUATTAN1 = (1 << 17) | 1,
    QUATTAN2 = (1 << 17) | 2,
    QUATTAN3 = (1 << 17) | 3,
    QUATTANLast = QUATTAN3
    // Don't forget update GetVertexChannelMaskBit function when add or remove new value!!
};

const std::array<VertexChannel, 6> gSkinningVertexSentamtics = 
{   
    VertexChannel::Position0,       // cur_position in model space
    VertexChannel::Normal0, 
    VertexChannel::Tangent0, 
    VertexChannel::BlendIndex0, 
    VertexChannel::BlendWeight0, 
    VertexChannel::PositionT        // pre_position in model space 
};

// get a more compact value of VertexChannel
CEMeta(Editor)
constexpr uint32_t GetVertexChannelMaskBit(VertexChannel channelValue)
{
    switch (channelValue)
    {
    case cross::VertexChannel::Unkown:
        return 0;
    case cross::VertexChannel::Position0:
        return 1;
    case cross::VertexChannel::Position1:
        return 2;
    case cross::VertexChannel::Position2:
        return 3;
    case cross::VertexChannel::Position3:
        return 4;
    case cross::VertexChannel::Color0:
        return 5;
    case cross::VertexChannel::Color1:
        return 6;
    case cross::VertexChannel::Color2:
        return 7;
    case cross::VertexChannel::Color3:
        return 8;
    case cross::VertexChannel::Normal0:
        return 9;
    case cross::VertexChannel::Normal1:
        return 10;
    case cross::VertexChannel::Normal2:
        return 11;
    case cross::VertexChannel::Normal3:
        return 12;
    case cross::VertexChannel::Tangent0:
        return 13;
    case cross::VertexChannel::Tangent1:
        return 14;
    case cross::VertexChannel::Tangent2:
        return 15;
    case cross::VertexChannel::Tangent3:
        return 16;
    case cross::VertexChannel::BiNormal0:
        return 17;
    case cross::VertexChannel::BiNormal1:
        return 18;
    case cross::VertexChannel::BiNormal2:
        return 19;
    case cross::VertexChannel::BiNormal3:
        return 20;
    case cross::VertexChannel::BlendWeight0:
        return 21;
    case cross::VertexChannel::BlendWeight1:
        return 22;
    case cross::VertexChannel::BlendWeight2:
        return 23;
    case cross::VertexChannel::BlendWeight3:
        return 24;
    case cross::VertexChannel::BlendIndex0:
        return 25;
    case cross::VertexChannel::BlendIndex1:
        return 26;
    case cross::VertexChannel::BlendIndex2:
        return 27;
    case cross::VertexChannel::BlendIndex3:
        return 28;
    case cross::VertexChannel::TexCoord0:
        return 29;
    case cross::VertexChannel::TexCoord1:
        return 30;
    case cross::VertexChannel::TexCoord2:
        return 31;
    case cross::VertexChannel::TexCoord3:
        return 32;
    case cross::VertexChannel::TexCoord4:
        return 33;
    case cross::VertexChannel::TexCoord5:
        return 34;
    case cross::VertexChannel::TexCoord6:
        return 35;
    case cross::VertexChannel::TexCoord7:
        return 36;
    case cross::VertexChannel::TexCoord8:
        return 37;
    case cross::VertexChannel::TexCoord9:
        return 38;
    case cross::VertexChannel::TexCoord10:
        return 39;
    case cross::VertexChannel::TexCoord11:
        return 40;
    case cross::VertexChannel::TexCoord12:
        return 41;
    case cross::VertexChannel::TexCoord13:
        return 42;
    case cross::VertexChannel::TexCoord14:
        return 43;
    case cross::VertexChannel::TexCoord15:
        return 44;
    case cross::VertexChannel::PSize0:
        return 45;
    case cross::VertexChannel::PSize1:
        return 46;
    case cross::VertexChannel::PSize2:
        return 47;
    case cross::VertexChannel::PSize3:
        return 48;
    case cross::VertexChannel::PositionT:
        return 49;
    case cross::VertexChannel::TessFactor0:
        return 50;
    case cross::VertexChannel::TessFactor1:
        return 51;
    case cross::VertexChannel::TessFactor2:
        return 52;
    case cross::VertexChannel::TessFactor3:
        return 53;
    case cross::VertexChannel::InstanceData:
        return 54;
    case cross::VertexChannel::QUATTAN0:
        return 55;
    case cross::VertexChannel::QUATTAN1:
        return 56;
    case cross::VertexChannel::QUATTAN2:
        return 57;
    case cross::VertexChannel::QUATTAN3:
        return 58;
    default:
        assert(false);
        break;
    }
    return 0;
}

inline const UInt32 Align(UInt32 size, UInt32 align)
{
    return size % align ? (size + align -1)/align * align : size;
}

CEMeta(Editor)
constexpr VertexSemantic GetSemantic(VertexChannel channel)
{
    return VertexSemantic(ToUnderlying(channel) & (~15));
}

CEMeta(Editor)
constexpr UInt32 GetSemanticIndex(VertexChannel channel)
{
    return ToUnderlying(channel) & (15);
}

CEMeta(Editor)
constexpr VertexSemanticSerial GetSemanticSerial(VertexChannel channel)
{
    VertexSemantic semantic = GetSemantic(channel);
    for (UInt32 i = 0; i < ToUnderlying(VertexSemanticSerial::Count); i++)
    {
        if (ToUnderlying(semantic) & (1 << (i + 5)))
        {
            return VertexSemanticSerial(i);
        }
    }
    AssertMsg(false, "Invalid semantic!");
    return VertexSemanticSerial::Count;
}

CEMeta(Editor)
constexpr VertexSemantic GetSemantic(VertexSemanticSerial semantic)
{
    return VertexSemantic(1 << (ToUnderlying(semantic) + 5));
}

CEMeta(Editor)
constexpr UInt32 MaxSemanticIndex(VertexSemantic semantic)
{
    switch (semantic)
    {
    case cross::SemanticPosition:
        return ToUnderlying(VertexChannel::PositionLast) - ToUnderlying(VertexChannel::Position0);
    case cross::SemanticColor:
        return ToUnderlying(VertexChannel::ColorLast) - ToUnderlying(VertexChannel::Color0);
    case cross::SemanticNormal:
        return ToUnderlying(VertexChannel::NormalLast) - ToUnderlying(VertexChannel::Normal0);
    case cross::SemanticTangent:
        return ToUnderlying(VertexChannel::TangentLast) - ToUnderlying(VertexChannel::Tangent0);
    case cross::SemanticBiNormal:
        return ToUnderlying(VertexChannel::BiNormalLast) - ToUnderlying(VertexChannel::BiNormal0);
    case cross::SemanticBlendIndex:
        return ToUnderlying(VertexChannel::BlendIndexLast) - ToUnderlying(VertexChannel::BlendIndex0);
    case cross::SemanticBlendWeight:
        return ToUnderlying(VertexChannel::BlendWeightLast) - ToUnderlying(VertexChannel::BlendWeight0);
    case cross::SemanticTexCoord:
        return ToUnderlying(VertexChannel::TexCoordLast) - ToUnderlying(VertexChannel::TexCoord0);
    case cross::SemanticPSize:
        return ToUnderlying(VertexChannel::PSizeLast) - ToUnderlying(VertexChannel::PSize0);
    case cross::SemanticPositionT:
        return ToUnderlying(VertexChannel::PositionTLast) - ToUnderlying(VertexChannel::PositionT);
    case cross::SemanticTessFactor:
        return ToUnderlying(VertexChannel::TessFactorLast) - ToUnderlying(VertexChannel::TessFactor0);
    case cross::SemanticInstance:
        return 1;
    case cross::SemanticQuatTan:
        return ToUnderlying(VertexChannel::QUATTANLast) - ToUnderlying(VertexChannel::QUATTAN0);
    }
    AssertMsg(false, "Invalid Semantic!");
    return 0;
}

CEMeta(Editor)
constexpr VertexChannel MakeVertexChannel(VertexSemantic semantic, UInt32 index)
{
    UInt32 maxIndex = MaxSemanticIndex(semantic);
    Assert(index <= maxIndex);
    index = index < 0 ? 0 : index;
    index = index > maxIndex ? maxIndex : index;
    return VertexChannel(semantic | index);
}

// Position0 + 1 -> Position1
constexpr VertexChannel operator+(VertexChannel channel, UInt32 index)
{
    auto sindex = GetSemanticIndex(channel);
    auto s = GetSemantic(channel);

    return MakeVertexChannel(s, sindex + index);
}

// Position1 - 1 -> Position0
constexpr VertexChannel operator-(VertexChannel channel, UInt32 index)
{
    auto sindex = GetSemanticIndex(channel);
    auto s = GetSemantic(channel);

    return MakeVertexChannel(s, sindex - index);
}

struct VertexChannelLayout
{
    CEMeta(Editor)
    cross::VertexChannel mChannelName{VertexChannel::Unkown};
    CEMeta(Editor)
    cross::VertexFormat mFormat{0};
    CEMeta(Editor)
    UInt32 mOffset{0};
    CEMeta(Editor)
    UInt32 mFixedLocation{(std::numeric_limits<UInt32>::max)()};
    CEMeta(Editor)
    void Set(VertexChannel channel, VertexFormat fmt, UInt32 offset)
    {
        mChannelName = channel;
        mFormat = fmt;
        mOffset = offset;
    }

    CEMeta(Editor)
    void Clear()
    {
        static_assert(std::is_trivially_copyable_v<VertexChannelLayout>, "");
        memset(this, 0, sizeof(VertexChannelLayout));
    }
};

struct VertexStreamLayout
{
    static const uint32_t sChannelMaskSize = 64;
    using VertexChannelMask = std::bitset<sChannelMaskSize>;

    VertexStreamLayout()
        : mStride(0)
        , mInstanceStepRate(0)
        , mChannelCount(0)
        , mUsedChannelMask(0)
    {
        mHash.Clear();
    }

    VertexStreamLayout(std::initializer_list<VertexChannelLayout>&& channelLayouts)
        : VertexStreamLayout()
    {
        for (auto& channelLayout : channelLayouts)
        {
            this->AddVertexChannelLayout(channelLayout);
        }
    }

    VertexStreamLayout(VertexChannel channel, VertexFormat fmt): VertexStreamLayout()
    {
        AddVertexChannelLayout(channel, fmt);
    }

    void AddVertexChannelLayout(const VertexChannelLayout& channelDesc)
    {
        assert(mChannelCount < MaxVertexChannelPerStream);
        assert(channelDesc.mChannelName != VertexChannel::Unkown);

        uint32_t maskBit = GetVertexChannelMaskBit(channelDesc.mChannelName);
        assert(maskBit < sChannelMaskSize);
        //assert(!HasChannel(maskBit));

        if (mChannelCount > 1)
        {
            assert(mChannels[mChannelCount - 1].mOffset <= channelDesc.mOffset);
        }

        mUsedChannelMask.set(maskBit - 1, true);
        mChannels[mChannelCount] = channelDesc;
        mChannelCount++;

        mStride += GetByteSize(channelDesc.mFormat);
        mHash.Clear();
    }

    void SetFrequency(VertexFrequency freq) 
    {
        mFrequency = freq;
    }

    void AddVertexChannelLayout(VertexChannel channel, VertexFormat fmt, UInt8 offset)
    {
        VertexChannelLayout desc;
        desc.mChannelName = channel;
        desc.mFormat = fmt;
        desc.mOffset = offset;
        AddVertexChannelLayout(desc);
    }

    // automatically calcuate stride and offset; based on Vulkan specifictaion
    // see GetAlign 
    UInt32 AddVertexChannelLayout(VertexChannel channel, VertexFormat fmt)
    {
        VertexChannelLayout desc;
        desc.mChannelName = channel;
        desc.mFormat = fmt;
        desc.mOffset = Align(mStride, GetAlign(fmt));
        mStride = std::max(desc.mOffset, mStride);
        AddVertexChannelLayout(desc);

        return desc.mOffset;
    }

    void AddVertexChannelLayout(VertexChannel channel, VertexFormat fmt, UInt8 offset, UInt32 fixedLocation)
    {
        VertexChannelLayout desc;
        desc.mChannelName = channel;
        desc.mFormat = fmt;
        desc.mOffset = offset;
        desc.mFixedLocation = fixedLocation;
        AddVertexChannelLayout(desc);
    }

    const VertexChannelLayout& GetChannelLayout(uint32_t index) const
    {
        assert(index < mChannelCount);
        return mChannels[index];
    }

    void SetVertexStride(uint32_t stride)
    {
        mStride = stride;
    }

    uint32_t GetVertexStride() const 
    {
        return mStride;
    }

    // it's ugly to call this function ?
    // it's better to automatically calculated in GetVertexStride
    // but the const keyword make its hard to refactor.
    uint32_t GetVertexStride()
    {
        AlignStride();
        return mStride;
    }

    UInt16 GetChannelCount() const
    {
        return mChannelCount;
    }

    cross::VertexFrequency GetFrequency() const
    {
        return mFrequency;
    }

    UInt16 GetInstanceStepRate() const
    {
        return mInstanceStepRate;
    }

    void SetInstanceStepRate(UInt16 rate) 
    {
        mInstanceStepRate = rate;
    }

    void Clear()
    {
        static_assert(std::is_standard_layout<VertexChannelLayout>(), "");

        mStride = 0;
        mChannelCount = 0;
        mUsedChannelMask = 0;
        mInstanceStepRate = 0;
        memset(mChannels, 0, sizeof(mChannels));
        mHash.Clear();
    }

    bool HasChannel(uint32_t channelMaskBit) const
    {
        if (channelMaskBit < 1)
            return false;
        return mUsedChannelMask.test(channelMaskBit - 1);
    }

    const VertexChannelMask& GetChannelMask() const
    {
        return mUsedChannelMask;
    }

    DoubleHash GetHash() const
    {
        if (mHash.IsZero() && mChannelCount > 0)
            UpdateHash();
        return mHash;
    }

private:
    void AlignStride()
    {
        if(mChannelCount > 0)
            mStride = Align(mStride, GetAlign(mChannels[0].mFormat));
    }
    void UpdateHash() const
    {
        if (mChannelCount < 1)
        {
            mHash.Clear();
            return;
        }

        uint32_t h0 = HashFunction::Djb2((const char*)&mChannels[0], sizeof(VertexChannelLayout));
        uint32_t h1 = HashFunction::Sdbm((const char*)&mChannels[0], sizeof(VertexChannelLayout));

        for (uint8_t i = 1; i < mChannelCount; i++)
        {
            h0 = HashFunction::Djb2AppendData(h0, (const char*)&mChannels[i], sizeof(VertexChannelLayout));
            h1 = HashFunction::SdbmAppendData(h1, (const char*)&mChannels[i], sizeof(VertexChannelLayout));
        }

        mHash.SetHash(h0, h1);
    }

    UInt32 mStride{0};
    UInt16 mInstanceStepRate{0};
    UInt16 mChannelCount{0};
    VertexChannelLayout mChannels[MaxVertexChannelPerStream];
    cross::VertexFrequency mFrequency{cross::VertexFrequency::PerVertex};

    VertexChannelMask mUsedChannelMask;
    mutable DoubleHash mHash;
};

struct InputLayoutDesc
{
    InputLayoutDesc()
    {
        //memset(Streams, 0, sizeof(Streams));
    }

    void AddVertexStreamLayout(const VertexStreamLayout& desc)
    {
        assert(StreamCount < MaxVertexStreams);
        assert((mUsedChannelMask & desc.GetChannelMask()).none());
        mUsedChannelMask |= desc.GetChannelMask();
        Streams[StreamCount] = desc;
        StreamCount++;
        mHash.Clear();
    }

    const VertexStreamLayout& GetVertexStreamLayout(uint8_t index) const
    {
        assert(index < StreamCount);
        return Streams[index];
    }

    UInt8 GetStreamCount() const
    {
        return StreamCount;
    }

    bool HasVertexChannel(VertexChannel channel) const
    {
        uint32_t channelBit = GetVertexChannelMaskBit(channel);
        if (channelBit == 0)
            return false;
        return mUsedChannelMask.test(channelBit - 1);
    }

    void Clear()
    {
        for (UInt8 i = 0; i < StreamCount; i++)
        {
            Streams[i].Clear();
        }
        mUsedChannelMask = 0;
        StreamCount = 0;
        mHash.Clear();
    }

    void Clear(UInt32 index)
    {
        Streams[index].Clear();
        StreamCount--;
    }

    DoubleHash GetHash() const
    {
        if (mHash.IsZero() && StreamCount > 0)
            UpdateHash();
        return mHash;
    }

private:
    void UpdateHash() const
    {
        if (StreamCount < 1)
        {
            mHash.Clear();
            return;
        }

        DoubleHash streamHash[MaxVertexStreams];
        uint32_t streamIndexHash[MaxVertexStreams];

        for (uint8_t i = 0; i < StreamCount; i++)
        {
            streamHash[i] = Streams[i].GetHash();
            streamIndexHash[i] = 4111 * i;
        }

        uint32_t h0 = HashFunction::Djb2((const char*)streamHash, sizeof(DoubleHash) * StreamCount);
        uint32_t h1 = HashFunction::Sdbm((const char*)streamHash, sizeof(DoubleHash) * StreamCount);

        h0 = HashFunction::Djb2AppendData(h0, (const char*)streamIndexHash, sizeof(uint32_t) * StreamCount);
        h1 = HashFunction::Djb2AppendData(h1, (const char*)streamIndexHash, sizeof(uint32_t) * StreamCount);

        mHash.SetHash(h0, h1);
    }

    VertexStreamLayout Streams[MaxVertexStreams];
    VertexStreamLayout::VertexChannelMask mUsedChannelMask;
    mutable DoubleHash mHash;
    UInt8 StreamCount{0};

    VertexStreamLayout& operator[](UInt32 i)
    {
        if (i >= MaxVertexStreams)
        {
            Assert(false);
            return Streams[0];
        }
        return Streams[i];
    }

    const VertexStreamLayout& operator[](UInt32 i) const
    {
        if (i >= MaxVertexStreams)
        {
            Assert(false);
            return Streams[0];
        }
        return Streams[i];
    }
};
};   // namespace cross
