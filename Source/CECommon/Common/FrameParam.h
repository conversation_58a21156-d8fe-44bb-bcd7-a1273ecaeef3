#pragma once

#include <stdint.h>
#include <memory>
#include <chrono>
#include <array>
#include "CECommon/Common/FrameStage.h"
#include "CECommon/Utilities/Timer.h"
#include "CECommon/Common/CECommonForward.h"
#include "CrossBase/Math/CrossMath.h"
#include "CrossBase/Containers/HashMap/HashMap.hpp"
#include <iostream>
namespace cross {
    struct TimeSamples
    {
        TimeSamples(int MaxCount) :mMaxSamples(MaxCount) { mFrameDelta.resize(MaxCount); }
        std::vector<double> mFrameDelta{ 0.0 };
        double mFrameMean = 0.;
        double mFrameVariance = 0.;

        long long frameIndex = 0;

        int mMaxSamples;

        void Update(double sample)
        {
            mFrameDelta[frameIndex++ % mMaxSamples] = sample;
            auto variance = cross::math::MeanAndVariance(mFrameDelta);

            mFrameMean = variance.first;
            mFrameVariance = variance.second;
        }
    };

class FrameAllocator;
class FrameParam final
{
public:
    CECOMMON_API FrameParam();
    CECOMMON_API ~FrameParam();
    CECOMMON_API std::atomic<UInt32>& sInstanceCount();
    CECOMMON_API UInt32 GetFrameCount() const
    {
        return mFrameCount;
    }
    CECOMMON_API FrameAllocator* GetFrameAllocator() const;
    CECOMMON_API FrameAllocator* GetFrameAllocatorWithLifeTime(UInt8 lifetime);
    CECOMMON_API FrameStage GetRunningStage() const noexcept
    {
        return mRunningStage;
    }

    // system time
    CECOMMON_API const TimePoint& GetGameFrameStartTime() const
    {
        return mGameFrameStartTime;
    }
    CECOMMON_API const TimePoint& GetGameFrameEndTime() const
    {
        return mGameFrameEndTime;
    }
    CECOMMON_API const TimePoint& GetRenderFrameStartTime() const
    {
        return mRenderFrameStartTime;
    }
    CECOMMON_API const TimePoint& GetRenderFrameEndTime() const
    { 
        return mRenderEndTime;
    }
    // game time
    CECOMMON_API auto GetTime() const
    {
        return mTime;
    }
    CECOMMON_API auto GetDeltaTime() const
    {
        return mDeltaTime;
    }

    CECOMMON_API void SetTime(float time, float deltaTime)
    {
        mTime = time;
        mDeltaTime = deltaTime;
    }
    CECOMMON_API float GetRenderDeltaTime() const;
    CECOMMON_API float GetGameDeltaTime() const;
   
    CECOMMON_API void SetRenderStartTime() 
    {
        mRenderStartTime = ClockType::now();
    }
    CECOMMON_API void SetRenderEndTime();
    CECOMMON_API void SetGameStartTime();
    CECOMMON_API void SetGameEndTime();
    CECOMMON_API void SetGPUDeltaTime(float t) {
        mGPUDeltaTime = t;
    }
    CECOMMON_API float GetGPUDeltaTime() {
        return mGPUDeltaTime;
    }
    
 

private:
    void SetFrameID(UInt32 frameCount);
    void BeginFrameStage(FrameStage stage);
    void EndFrameStage(FrameStage stage);
    void Reset();

    UInt32 mFrameCount{0};
    FrameStage mRunningStage{FRAME_STAGE_INVALID};
    FrameStage mFinishedStage{FRAME_STAGE_INVALID};
    std::unique_ptr<FrameAllocator> mDefaultFrameAllocator;
    CEHashMap<UInt8, std::unique_ptr<FrameAllocator>> mMultiFrameAllocators;

private:
    TimePoint mGameFrameStartTime;
    TimePoint mGameFrameEndTime;
    TimePoint mRenderFrameStartTime;
    TimePoint mRenderFrameEndTime;
    float mTime;
    float mDeltaTime, mRenderDeltaTime, mGameDeltaTime, mGPUDeltaTime{0.0f};
    TimePoint mRenderStartTime, mRenderEndTime;
    TimePoint mGameStartTime, mGameEndTime;
    UInt32 mUniqueID;
    friend class FrameParamManager;
};

class FrameParamManager
{
public:
    CECOMMON_API ~FrameParamManager();

    // Following function only for engine infrastructure, do NOT invoke unless you
    // know what you're doing!!

    // protected:
    CECOMMON_API explicit FrameParamManager(UInt32 frameFreq = 0);
    FrameParamManager(const FrameParamManager&) = delete;
    FrameParamManager& operator=(FrameParamManager const&) = delete;

    // make sure do not call wrong param game in render
    // cannot add thread check, since tasks could be dispatchd to any thread
    CECOMMON_API FrameParam* GetCurrentGameFrameParam();

    CECOMMON_API FrameParam* GetCurrentRenderFrameParam();

    // FrameParam* GetCurrentNGIFrameParam() { return UpdateElapsedTime(&mAllFrameParams[mNGIParamIndex]); }

    CECOMMON_API void BeginGameFrame(float time, float deltaTime);

    CECOMMON_API void EndGameFrame();

    CECOMMON_API void BeginRenderFrame();

    CECOMMON_API void EndRenderFrame();
private:
    UInt32 mGameParamIndex{0};
    UInt32 mRenderParamIndex{0};
    UInt32 mFrameCount{0};
    std::array<FrameParam, MAX_FRAME_PARAM_COUNT> mAllFrameParams;

    FrameParam* mCurrentGameFrameParam{nullptr};
    FrameParam* mCurrentRenderFrameParam{nullptr};

    friend class CrossEngine;
};

}   // namespace cross
