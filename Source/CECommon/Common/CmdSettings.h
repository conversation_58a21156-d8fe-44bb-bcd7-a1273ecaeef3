#pragma once
#include "CECommon/Utilities/ValueContainer.h"
#pragma warning(push)
#pragma warning(disable : 4267)
#include "argparse/argparse.hpp"
#pragma warning(pop)
#include "meta/poly/poly.hpp"
#include "reflection/meta/meta_class.hpp"
namespace cross {
using ValueType = std::variant<bool*, int*, float*, UInt32*>;

template<typename V>
class NamedCmdObject
{
public:
    std::string name;
    std::string cmd;
    cross::ValueContainer<ValueType>* values = nullptr;
    NamedCmdObject(std::string n, std::string c, V* v, cross::ValueContainer<ValueType>* storage, argparse::ArgumentParser* args = nullptr)
        : name(n)
        , cmd(c)
        , values(storage)
    {
        values->SetKeyValue(cmd, v);
        if (args)
        {
            if constexpr (std::is_same_v<V, bool>)
                args->add_argument("--" + c).default_value(*v).implicit_value(true);
            else
                args->add_argument("--" + c).default_value(*v);
        }
    }
};
#define DeclNameObject(type, name, cmd, defaultV, store, args)                                                                                                                                                                                 \
public:\
    CEMeta(Reflect) type name = defaultV;                                                                                                                                                                                                                      \
    protected : NamedCmdObject<type> name##Object = NamedCmdObject<type>(#name, cmd, &name, store, args)

class CECOMMON_API ExtensibleSetting
{
public:
    virtual ~ExtensibleSetting() = default;

    auto& GetConfigValues()
    {
        return mConfigValues;
    }
    auto& GetCmdValues()
    {
        return mCmdValues;
    }
    template<class T>
    void ParseValueContainerExtensibleSetting(cross::ValueContainer<T>& container)
    {
        for (auto& itr : mConfigValues.mContext)
        {
            if (container.HasValidKey(itr.first))
            {
                std::visit(
                    [this, &itr, &container](auto&& arg) {
                        using V = std::decay_t<decltype(arg)>;
                        if constexpr (std::is_same_v<V, bool*>)
                            container.template GetValue<bool>(itr.first, *std::get<bool*>(itr.second));
                        else if constexpr (std::is_same_v<V, int*>)
                            container.template GetValue<int>(itr.first, *std::get<int*>(itr.second));
                        else if constexpr (std::is_same_v<V, float*>)
                            container.template GetValue<float>(itr.first, *std::get<float*>(itr.second));
                        else if constexpr (std::is_same_v<V, UInt32*>)
                            container.template GetValue<UInt32>(itr.first, *std::get<UInt32*>(itr.second));
                        // comment due to cannot pass CI, always report c++ error in server build, need some c++ expert
                        // else
                        //    static_assert(std::always_false_v<V>, "non-exhaustive visitor!");
                    },
                    itr.second);
            }
        }
    }
    void ParseExtensibleSetting(argparse::ArgumentParser& argsparser, std::vector<std::string>& args);

    bool HasProp(const char* classname, const char* name);
    template<typename T>
    T GetConfigValueByName(gbf::reflection::UserObject& uo, const char* name)
    {
        auto& cls = uo.GetClass();
        auto& prop = cls.GetProperty(name);
        return prop.Get(uo).Ref<T>();
    }
    ValueContainer<ValueType> mCmdValues;
    ValueContainer<ValueType> mConfigValues;
};
class CECOMMON_API CmdSettings
{
public:
    static CmdSettings& Inst();

    template<class V>
    void SetValue(std::string cmd, V value)
    {
        V* pointer = mCmdValues.GetKeyValue<V*>(cmd);
        *pointer = value;
    }

    template<class V>
    bool GetValue(std::string cmd, V& out)
    {
        V* pointer = mCmdValues.GetKeyValue<V*>(cmd);
        out = *pointer;
    }

    template<class V>
    V GetValue(std::string cmd)
    {
        V* pointer = mCmdValues.GetKeyValue<V*>(cmd);
        return *pointer;
    }

    cross::ValueContainer<ValueType>& GetConfigValues() { return mConfigValues; }

    template<class U, typename = std::enable_if_t<std::is_base_of_v<ExtensibleSetting, U>>>
    void RegisterExtensibleSetting()
    {
        const gbf::reflection::MetaClass* metaclass = gbf::reflection::query_meta_class<U>();
        if (metaclass)
        {
            HashString name = HashString(metaclass->name());
            if (mExtendSettings.find(name) == mExtendSettings.end())
            {
                auto ptr = std::make_shared<U>();
                mExtendSettings.emplace(name, std::make_pair(metaclass->GetUserObjectFromPointer(ptr.get()), ptr));
            }
        }
        else
        {
            LOG_ERROR("RegisterExtensibleSetting: metaclass not found for");
        }
    }
    template<class T>
    void ParseValueContainer(cross::ValueContainer<T>& container)
    {
        for (auto& itr : mConfigValues.mContext)
        {
            if (container.HasValidKey(itr.first))
            {
                std::visit(
                    [this, &itr, &container](auto&& arg) {
                        using V = std::decay_t<decltype(arg)>;
                        if constexpr (std::is_same_v<V, bool*>)
                            container.template GetValue<bool>(itr.first, *std::get<bool*>(itr.second));
                        else if constexpr (std::is_same_v<V, int*>)
                            container.template GetValue<int>(itr.first, *std::get<int*>(itr.second));
                        else if constexpr (std::is_same_v<V, float*>)
                            container.template GetValue<float>(itr.first, *std::get<float*>(itr.second));
                        else if constexpr (std::is_same_v<V, UInt32*>)
                            container.template GetValue<UInt32>(itr.first, *std::get<UInt32*>(itr.second));
                        // comment due to cannot pass CI, always report c++ error in server build, need some c++ expert
                        // else
                        //    static_assert(std::always_false_v<V>, "non-exhaustive visitor!");
                    },
                    itr.second);
            }
        }
        for (auto& [_, setting] : mExtendSettings)
        {
            setting.second->ParseValueContainerExtensibleSetting(container);
        }
    }

    template<typename T>
    bool GetConfigValueByName(const char* name, T& value)
    {
        gbf::reflection::UserObject uo = gbf::reflection::make_user_object(this, gbf::reflection::remote_storage_policy{});
        if (uo.GetClass().HasProperty(name))
        {
            auto& prop = uo.GetClass().GetProperty(name);
            value = prop.Get(uo).Ref<T>();
            return true;
        }
        else
        {
            for (auto& [classname, setting] : mExtendSettings)
            {
                if (setting.second->HasProp(classname.GetCString(), name))
                {
                    value = setting.second->GetConfigValueByName<T>(setting.first, name);
                    return true;
                }
            }
        }
        return false;
    }

    argparse::ArgumentParser& GetArgParser() { return gArgs; }

    void Parse(std::vector<std::string>& args)
    {
        if (args.size() > 1)
        {
            gArgs.parse_known_args(args);

            for (auto& itr : mCmdValues.mContext)
            {
                {
                    auto key = itr.first.GetCString();
                    std::visit(
                        [this, key, &itr](auto&& arg) {
                            using V = std::decay_t<decltype(arg)>;
                            if constexpr (std::is_same_v<V, bool*>)
                                *std::get<bool*>(itr.second) = gArgs.get<bool>(key);
                            else if constexpr (std::is_same_v<V, int*>)
                                *std::get<int*>(itr.second) = gArgs.get<int>(key);
                            else if constexpr (std::is_same_v<V, float*>)
                                *std::get<float*>(itr.second) = gArgs.get<float>(key);
                            else if constexpr (std::is_same_v<V, std::uint32_t*>)
                                *std::get<std::uint32_t*>(itr.second) = gArgs.get<std::uint32_t>(key);
                            // comment due to cannot pass CI, always report c++ error in server build, need some c++ expert
                            // else
                            //    static_assert(std::always_false_v<V>, "non-exhaustive visitor!");
                        },
                        itr.second);
                }
            }

        }
        for (auto& [_, setting] : mExtendSettings)
        {
            setting.second->ParseExtensibleSetting(gArgs, args);
        }
    }


private:
    void Init();

    ValueContainer<ValueType> mCmdValues;
    ValueContainer<ValueType> mConfigValues;
    std::unordered_map<HashString, std::pair<gbf::reflection::UserObject, std::shared_ptr<ExtensibleSetting>>> mExtendSettings;

public:
    argparse::ArgumentParser gArgs;

    DeclNameObject(bool, gGpuDump, "gpu_dump", false, &mCmdValues, &gArgs);
    DeclNameObject(bool, gShowFPS, "showfps", false, &mCmdValues, &gArgs);
    DeclNameObject(bool, gKeepMessage, "message_keep", false, &mCmdValues, &gArgs);

    DeclNameObject(bool, gShowFrameID, "showframeid", false, &mCmdValues, &gArgs);
    DeclNameObject(bool, gVsyncCheck, "vsynccheck", false, &mCmdValues, &gArgs);
    DeclNameObject(bool, gFrameProGPU, "frameprogpu", false, &mCmdValues, &gArgs);
    DeclNameObject(bool, gEnableLogToUdp, "log_to_udp", false, &mCmdValues, &gArgs);
    DeclNameObject(UInt32, gMaxQueuedFrame, "MaxQueuedFrame", 3, &mConfigValues, nullptr);
    
    DeclNameObject(bool, gVsync, "Vsync", true, &mConfigValues, nullptr);
    DeclNameObject(bool, gUseEngineStats, "UseEngineStats", false, &mConfigValues, nullptr);
    DeclNameObject(bool, gUseFullDump, "UseFullDump", false, &mConfigValues, nullptr);
    DeclNameObject(bool, gValidation, "validation", false, &mConfigValues, nullptr);

    // this is for adjusting the distance culling control, i.e the max drawing distance and the culled height
    // the max drawing distance, the mesh would be culled if distance/gCullingDistanceScale > maxCullingDistance
    // as for the culledHeight, since it is a projected radius pixel height,  so it would scale the mesh's distance between camera projectHeight(distance / gCullingDistanceScale ) < Cullheight
    DeclNameObject(float, gCullingDistanceScale, "CullingDistanceScale", 1.0, &mConfigValues, nullptr);
};
}

