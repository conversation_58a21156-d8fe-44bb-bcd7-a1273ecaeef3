#pragma once
#include "CrossBase/Serialization/SerializeNode.h"
#include "CrossBase/String/UniqueString.h"
#include "CrossBase/Math/CrossMath.h"
#include "CrossBase/NodeTransform/NodeTransform.h"
#include "CEAnimation/AnimationForward.h"

namespace cross {
/*
 *   Curve repeat type for pre-key range and post-key range
 */
enum CEMeta(Editor, Cli) CurveRepeatType
{
    CRT_CONSTANT,
    CRT_CYCLE,
    CRT_LINEAR,
    CRT_OSCILLATE
};

/*
 *   Curve keys smooth type for smooth keys.
 */
enum CEMeta(Editor, Script, Cli,Puerts) KeySmoothMode
{
    KSM_AUTO,
    KSM_FLAT,
    KSM_BREAK
};

/*
 *   Key interpolation type
 */
enum CEMeta(Editor, Script, <PERSON>li, Puerts) KeyInterpType
{
    KIT_LINEAR,
    KIT_CONSTANT,
    KIT_SMOOTH,
};

/*
 *   Key frame of float curve
 */
struct CECOMMON_API CEMeta(<PERSON><PERSON>, <PERSON><PERSON><PERSON>) FloatCurveKey
{
    /*
     *   Key's smooth type
     */
    CEMeta(<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>) KeySmoothMode SmoothType = KSM_AUTO;

    /*
     *   Key's smooth interpolation type.
     *   Key-pairs interpolation behavior is only determined by the left key. E.g, an interpolation between a cubic key and a linear key is performed as a cubic interpolation.
     */
    CEMeta(Serialize, Editor, Script) KeyInterpType InterpType = KIT_LINEAR;
    
    /*
     *   Key's time (x value).
     */
    CEMeta(Serialize, Editor, Script) float Time;

    /*
     *   Key's time (y value).
     */
    CEMeta(Serialize, Editor, Script) float Value;

    /*
     *   Key's value (y double value).
     */
    CEMeta(Serialize, Editor, Script)
    double DoubleValue;

    /*
     *   IsDouble.
     */
    CEMeta(Serialize, Editor, Script)
    bool IsDouble = false;

    template<class T>
    T QueryValue() const
    {
        if (IsDouble)
        {
            return static_cast<T>(DoubleValue);
        }
        else
        {
            return static_cast<T>(Value);
        }
    }

    /*
     *   Weights and tangents are valid only for smooth interpolation.
     *   Default value is used for non-smooth keys in a smooth interpolation.
     */ 
    CEMeta(Serialize, Editor, Script) bool AutoWeighted = 1;

    /*
     *   The arrive weight of the key.
     *   X value of the control point = Time - KeyDeltaTime * ArriveWeight;
     *       where KeyDeltaTime is the delta-time of the two keys when this key is at the right side. See the figure below.
     *
     *         o (ControlPointLeave)       o (ControlPointArrive)
     *        /                             \
     *       / LeaveTangent                  \    ArriveTangent
     *      /                                 \
     *     o(KeyN_1)                           o(KeyN)
     *     |<-------------Delta--------------->|
     *     |<->|                           |<->|
     *     LeaveWeight * Delta         ArriveWeight * delta
     */
    CEMeta(Serialize, Editor, Script) float ArriveWeight = 1.0f / 3.0f;

    /*
     *   Slope of the ControlPointArrive. See the figure above.
     */
    CEMeta(Serialize, Editor, Script) float ArriveTangent = 0.0f;

    /*
     *   The leave weight of the key.
     *   X value of the control point = Time + DeltaTime * LeaveWeight;
     *       where KDeltaTime is the delta-time of the two keys when this key is at the left side. See the figure above.
     */
    CEMeta(Serialize, Editor, Script) float LeaveWeight = 1.0f / 3.0f;

    /*
     *   Slope of the ControlPointLeave. See the figure above.
     */
    CEMeta(Serialize, Editor, Script) float LeaveTangent = 0.0f;

    CEMeta(Serialize, Editor, Script) std::string EventString;

    CE_Serialize_Deserialize;

    static constexpr float IS_SAME_THRESHOLD = 1.e-4f;

    bool operator==(const FloatCurveKey& that) 
    {
        return (Time == that.Time) || (
            Value == that.Value && SmoothType == that.SmoothType && InterpType == that.InterpType && AutoWeighted == that.AutoWeighted &&
            cross::Abs(ArriveWeight - that.ArriveWeight) < IS_SAME_THRESHOLD && 
            cross::Abs(ArriveTangent - that.ArriveTangent) < IS_SAME_THRESHOLD && 
            cross::Abs(LeaveWeight - that.LeaveWeight) < IS_SAME_THRESHOLD && 
            cross::Abs(LeaveTangent - that.LeaveTangent) < IS_SAME_THRESHOLD);
    }
    
    bool operator!=(const FloatCurveKey& that) 
    {
        return !(*this == that);
    }
};

enum class CEMeta(Editor, Cli) CurveUseType
{
    Common = 0,
    BlendShape,

    Count // Keep it last
};

/*
 *   Single float curve track defined by key frames.
 */
struct CECOMMON_API CEMeta(Cli, Puerts) FloatCurveTrack
{
public:
    /*
     *   Constructor
     */
    FloatCurveTrack() {}
    FloatCurveTrack(const UniqueString& inName) : Name(inName) {}

    /*
     *   Evaluation the curve using an O(LogN) algorithm where N is the number of keys.
     */
    CEMeta(Editor) float Eval(float inTime, float DefaultValue = 0.0f) const;

    double EvalDouble(float inTime, double DefaultValue = 0.0f) const;

    /*
     *   Get the number of keys
     */
    UInt32 GetNumKeys() const
    {
        return static_cast<UInt32>(Keys.size());
    }

    CEMeta(Editor) void RemoveDuplicateKeys();

    /*
     *   return 0 when num keys == 0
     *   return 1 when only keyLeft is set
     *   return 2 when only keyRight is set
     *   return 3 when keyLeft and keyRight are both set
     */
    UInt32 Query2Keys(float inTime, FloatCurveKey& keyLeft, FloatCurveKey& keyRight, float& outTime) const;

    /*
     *   Check the float numbers is not Nan.
     */
    CEMeta(Editor) bool Validate() const;

    /** Current index in track keys of input cursor */
    inline SInt32 IsRanked(float timeInSec, float tolerance = 0.01f) const
    {
        auto re = std::find_if(Keys.begin(), Keys.end(), [timeInSec, tolerance](auto& elem) {
            if (std::abs(timeInSec - elem.Time) < tolerance)
                return true;
            return false;
        });

        if (re == Keys.end())
            return -1;
        return static_cast<SInt32>(re - Keys.begin());
    }

private:
    /*
     *   Clamp Time to fit the keys range based on the leave type or enter type.
     */
    float ClampTime(float inTime) const;

public:
    /*
     *   Determine the curve repeat type for the range of (-inf, mKeys[0].time).
     */
    CEMeta(Serialize, Editor) CurveRepeatType EnterType = CRT_CONSTANT;

    /*
     *   Determine the repeat type for the range of [mKeys[N - 1].time, +inf).
     */
    CEMeta(Serialize, Editor) CurveRepeatType LeaveType = CRT_CONSTANT;

    /*
     *   Sorted key list.
     */
    CEMeta(Serialize, Editor) std::vector<FloatCurveKey> Keys;

    /*
     *   Name
     */
    CEMeta(Serialize, Editor) UniqueString Name;

    /*
     *   Use Type
     */
    CEMeta(Serialize, Editor) CurveUseType UseType{CurveUseType::Common};

    CE_Serialize_Deserialize;
};

/*
 *   A  vector container to store the curves.
 */
struct CECOMMON_API CEMeta(Cli, Puerts) FloatCurveList
{
public:
    /*
     *   Return true if it exists track with the specific name.
     */
    CEMeta(Editor) bool HasTrack(const UniqueString& inName) const;

    /*
     *   Find the track with the specific name and return the index in the list.
     */
    CEMeta(Editor) SInt32 FindTrackIndex(const UniqueString& inName) const;

    /*
     *   Find track by index.
     */
    CEMeta(Editor) inline const FloatCurveTrack& FindTrackByIndex(SInt32 index) const
    {
        Assert(index >= 0 && index < mTracks.size());
        return mTracks[index];
    }

    inline FloatCurveTrack& FindTrackByIndex(SInt32 index)
    {
        Assert(index >= 0 && index < mTracks.size());
        return mTracks[index];
    }

    /*
     *   Find track by name.
     */
    CEMeta(Editor) inline const FloatCurveTrack& FindTrackByName(const UniqueString& inName) const
    {
        Assert(HasTrack(inName));
        return FindTrackByIndex(FindTrackIndex(inName));
    }

    inline FloatCurveTrack& FindTrackByName(const UniqueString& inName)
    {
        Assert(HasTrack(inName));
        return FindTrackByIndex(FindTrackIndex(inName));
    }

    inline bool IsFullRanked(float timeInSec, float tolerance = 0.01f) const
    {
        auto notExistCurve = std::find_if(mTracks.begin(), mTracks.end(), [timeInSec, tolerance](auto& track) {
            return track.IsRanked(timeInSec, tolerance) < 0;
        });
        return notExistCurve == mTracks.end();
    }

#if CROSSENGINE_EDITOR

    inline SInt32 GetFullRankedCount() const { return static_cast<SInt32>(mAllRankedKeys.size()); }

    inline FloatCurveKey* GetFullRankedKey(SInt32 curveIndex, float timeInSec, float tolerance = 0.01f) const
    {
        auto& track = const_cast<FloatCurveTrack&>(mTracks[curveIndex]);
        auto itr = std::find_if(track.Keys.begin(), track.Keys.end(), [timeInSec, tolerance, this](auto& key) {
            if (std::abs(timeInSec - key.Time) < tolerance && IsFullRanked(timeInSec))
                return true;
            return false;
        });

        return itr == track.Keys.end() ? nullptr : &(*itr);
    }

    inline FloatCurveKey* GetNextFullRankedKey(SInt32 curveIndex, float timeInSec) const
    {
        float preFramePos = -1.0f;

        auto rankedItr = std::find_if(mAllRankedKeys.begin(), mAllRankedKeys.end(), [&preFramePos, timeInSec](auto& cursor) {
            if (timeInSec - cursor < 0.0001f)
                return true;

            preFramePos = cursor;
            return false;
        });

        if (rankedItr == mAllRankedKeys.end())
            return nullptr;

        rankedItr = rankedItr + 1;
        if (rankedItr == mAllRankedKeys.end())
            return nullptr;

        auto& track = const_cast<FloatCurveTrack&>(mTracks[curveIndex]);
        auto curveItr = std::find_if(track.Keys.begin(), track.Keys.end(), [rankedItr](auto& elem) { 
            if ((std::abs)(elem.Time - *rankedItr) < 0.0001f)
                return true;

            return false;
        });

        return curveItr == track.Keys.end() ? nullptr : &(*curveItr);
    }

    inline FloatCurveKey* GetNextFullRankedKey(SInt32 curveIndex, SInt32 rankedIndex) const
    {
        auto& track = mTracks[curveIndex];
        return GetNextFullRankedKey(curveIndex, track.Keys[rankedIndex].Time);
    }

    void CacheRankedKeys()
    {
        mAllRankedKeys.clear();

        // filter ranked key frames
        if (!mTracks.empty())
        {
            auto& track = mTracks[0];
            std::for_each(track.Keys.begin(), track.Keys.end(), [this, track](auto& key) {
                if (IsFullRanked(key.Time))
                    mAllRankedKeys.push_back(key.Time);
            });

            std::sort(mAllRankedKeys.begin(), mAllRankedKeys.end(), std::less<float>());
        }
    }

#endif

    /*
     *   Curve track number.
     */
    CEMeta(Editor) inline UInt32 GetCurveNum() const
    {
        return static_cast<UInt32>(mTracks.size());
    }

    /*
     *   Convert the json object gathered from CE_Serialize_Deserialize into a binary form and store it into a vector<UInt8> that will be used in flat-buffer.
     */
    void Serialize(std::vector<UInt8>& curveSet) const;

    /*
     *   Extract a json object from the flat-buffer binary data that will be used in CE_Serialize_Deserialize
     */
    bool Deserialize(const flatbuffers::Vector<uint8_t>* data);

public:
    /*
     *   Curve track list
     */
    CEMeta(Serialize, Editor) std::vector<FloatCurveTrack> mTracks;

    CE_Serialize_Deserialize;

    CEMeta(AdditionalDeserialize) void AdditionalDeserialize(const DeserializeNode& inNode, SerializeContext& context) 
    {
#if CROSSENGINE_EDITOR
        CacheRankedKeys();
#endif
    }

    friend class FloatCurveListModifier;

#if CROSSENGINE_EDITOR

protected:
    /* All keys sorted from min to max */
    std::vector<float> mAllRankedKeys;

#endif
};

struct CECOMMON_API Float1Curve : FloatCurveList
{
    using DataType = float;
    Float1Curve(const UniqueString& inName = "x");
    float Eval(float inTime, float DefaultValue = 0.0f) const;
};

struct CECOMMON_API Float2Curve : FloatCurveList
{
    using DataType = Float2;
    Float2Curve(const UniqueString& inNameX = "x", const UniqueString& inNameY = "y");
    Float2 Eval(float inTime, Float2 DefaultValue = {0.0f, 0.0f}) const;
};

struct CECOMMON_API Float3Curve : FloatCurveList
{
public:
    using DataType = Float3;
    Float3Curve(const UniqueString& inNameX = "x", const UniqueString& inNameY = "y", const UniqueString& inNameZ = "z");
    Float3 Eval(float inTime, Float3 DefaultValue = {0.0f, 0.0f, 0.0f}) const;
};

struct CECOMMON_API Double3Curve : FloatCurveList
{
public:
    using DataType = Double3;
    Double3Curve(const UniqueString& inNameX = "x", const UniqueString& inNameY = "y", const UniqueString& inNameZ = "z");
    Double3 Eval(float inTime, Double3 DefaultValue = {0.0f, 0.0f, 0.0f}) const;
};

struct CECOMMON_API Float4Curve : FloatCurveList
{
public:
    using DataType = Float4;
    Float4Curve(const UniqueString& inNameX = "x", const UniqueString& inNameY = "y", const UniqueString& inNameZ = "z", const UniqueString& inNameW = "w");
    Float4 Eval(float inTime, Float4 DefaultValue = {0.0f, 0.0f, 0.0f, 0.0f}) const;
};

struct CECOMMON_API RotEulerCurve : FloatCurveList
{
public:
    using DataType = Quaternion;
    RotEulerCurve(const UniqueString& inNameX = "x", const UniqueString& inNameY = "y", const UniqueString& inNameZ = "z", const UniqueString& inNameW = "w", const UniqueString& inNameT = "t");
    Quaternion Eval(float inTime, Quaternion DefaultValue = {0.0f, 0.0f, 0.0f, 1.0f}) const;
};

struct CECOMMON_API TransformCurve : FloatCurveList
{
public:
    using DataType = NodeTransform;
    TransformCurve(const UniqueString& inNameRY = "Rotation.Yaw", const UniqueString& inNameRP = "Rotation.Pitch", const UniqueString& inNameRR = "Rotation.Roll", const UniqueString& inNameTX = "Translation.X",
                  const UniqueString& inNameTY = "Translation.Y", const UniqueString& inNameTZ = "Translation.Z", const UniqueString& inNameSX = "Scale.X", const UniqueString& inNameSY = "Scale.Y",
                  const UniqueString& inNameSZ = "Scale.Z" );

    NodeTransform Eval(float inTime, NodeTransform DefaultValue = {}) const
    {
        return DefaultValue;
    }
};

struct CECOMMON_API EventCurve : FloatCurveList
{
    using DataType = UniqueString;
    EventCurve(const UniqueString& inName = "Event");

     std::string Eval(float inTime, float deltaTime) const;
};

class FloatCurveEval
{
public:
    FloatCurveEval(const std::shared_ptr<FloatCurveList>& CurveData)
        : CurveData(CurveData)
    {}

    template<typename T, typename D = typename T::DataType>
    D Eval(float inTime, const D& DefaultValue) const
    {
        const auto* curvePtr = static_cast<const T*>(CurveData.get());
        AssertMsg(curvePtr, "curvePtr is null");
        return curvePtr->Eval(inTime, DefaultValue);
    }
    template<typename T, typename D = typename T::DataType>
    D Eval(float inTime) const
    {
        const auto* curvePtr = static_cast<const T*>(CurveData.get());
        AssertMsg(curvePtr, "curvePtr is null");
        return curvePtr->Eval(inTime);
    }

    CEFunction(Reflect)
    float EvalFloat1(float inTime) const { return Eval<Float1Curve>(inTime); }

    CEFunction(Reflect)
    Float2 EvalFloat2(float inTime) const { return Eval<Float2Curve>(inTime); }

    CEFunction(Reflect)
    Float3 EvalFloat3(float inTime) const { return Eval<Float3Curve>(inTime); }

    CEFunction(Reflect)
    Double3 EvalDouble3(float inTime) const { return Eval<Double3Curve>(inTime); }

    CEFunction(Reflect) 
    Float4 EvalFloat4(float inTime) const  { return Eval<Float4Curve>(inTime); }

    CEFunction(Reflect)
    Quaternion EvalRotEuler(float inTime) const { return Eval<RotEulerCurve>(inTime); }

    std::shared_ptr<FloatCurveList> CurveData;
};


struct CEMeta(Cli, Puerts) FloatCurveListInfoItem
{
    CEMeta(Editor) UniqueString CurveName;
};

struct CEMeta(Cli, Puerts) FloatCurveListInfo
{
    CEMeta(Editor) std::vector<FloatCurveListInfoItem> Items;
};

class CECOMMON_API FloatCurveListModifier
{
public:
    FloatCurveListModifier(FloatCurveList& inCurveList)
        : mCurveList(inCurveList)
    {}

    /*
     *   Add a track into the list if not exists.
     */
    bool AddTrack(const FloatCurveTrack& inTrack);

    /*
     *   Remove a track of the specific name if exists.
     */
    bool RemoveTrack(const UniqueString& inName);

    /*
     *   Replace the track with the given one if exists.
     */
    bool SetTrack(const FloatCurveTrack& inTrack);

    /*
     *   Return true if it exists track with the specific name.
     */
    inline bool HasTrack(const UniqueString& inName) const
    {
        return mCurveList.HasTrack(inName);
    }

    /*
     *   Find the track with the specific name and return the index in the list.
     */
    inline SInt32 FindTrackIndex(const UniqueString& inName) const
    {
        return mCurveList.FindTrackIndex(inName);
    }

    /*
     *   Get internal CurveList object
     */
    inline const FloatCurveList& GetCurveList() const
    {
        return mCurveList;
    }

    /*
     *   Apply the given info data to the internal CurveList
     */
    void ApplyFloatCurveListInfo(const FloatCurveListInfo& inInfoData);

#if CROSSENGINE_EDITOR

    void ResortTrackKeys();

#endif

private:
    /*
     *   Internal CurveList object
     */
    FloatCurveList& mCurveList;
};

}   // namespace cross
