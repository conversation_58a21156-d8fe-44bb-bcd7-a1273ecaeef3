// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SHADERDEFINES_CROSSSCHEMA_H_
#define FLATBUFFERS_GENERATED_SHADERDEFINES_CROSSSCHEMA_H_

#include "flatbuffers/flatbuffers.h"

#include "CrossSchemaForward.h"

namespace CrossSchema {

struct ShaderVersion;

inline const flatbuffers::TypeTable *ShaderVersionTypeTable();

enum class CROSS_SCHEMA_API ShaderResourceType : uint32_t {
  Unknown = 0,
  ConstantBuffer = 1,
  TextureBuffer = 2,
  TexelBuffer = 3,
  RWTexelBuffer = 4,
  StructuredBuffer = 5,
  RWStructuredBuffer = 6,
  ByteAddressBuffer = 7,
  RWByteAddressBuffer = 8,
  Texture1D = 9,
  Texture1DArray = 10,
  Texture2D = 11,
  Texture2DArray = 12,
  Texture2DMS = 13,
  Texture2DMSArray = 14,
  Texture3D = 15,
  TextureCube = 16,
  TextureCubeArray = 17,
  RWTexture1D = 18,
  RWTexture1DArray = 19,
  RWTexture2D = 20,
  RWTexture2DArray = 21,
  RWTexture3D = 22,
  Sampler = 23,
  SubpassInput = 24,
  SubpassInputMS = 25,
  AccelStruct = 26,
  MIN = Unknown,
  MAX = AccelStruct
};

inline const ShaderResourceType (&EnumValuesShaderResourceType())[27] {
  static const ShaderResourceType values[] = {
    ShaderResourceType::Unknown,
    ShaderResourceType::ConstantBuffer,
    ShaderResourceType::TextureBuffer,
    ShaderResourceType::TexelBuffer,
    ShaderResourceType::RWTexelBuffer,
    ShaderResourceType::StructuredBuffer,
    ShaderResourceType::RWStructuredBuffer,
    ShaderResourceType::ByteAddressBuffer,
    ShaderResourceType::RWByteAddressBuffer,
    ShaderResourceType::Texture1D,
    ShaderResourceType::Texture1DArray,
    ShaderResourceType::Texture2D,
    ShaderResourceType::Texture2DArray,
    ShaderResourceType::Texture2DMS,
    ShaderResourceType::Texture2DMSArray,
    ShaderResourceType::Texture3D,
    ShaderResourceType::TextureCube,
    ShaderResourceType::TextureCubeArray,
    ShaderResourceType::RWTexture1D,
    ShaderResourceType::RWTexture1DArray,
    ShaderResourceType::RWTexture2D,
    ShaderResourceType::RWTexture2DArray,
    ShaderResourceType::RWTexture3D,
    ShaderResourceType::Sampler,
    ShaderResourceType::SubpassInput,
    ShaderResourceType::SubpassInputMS,
    ShaderResourceType::AccelStruct
  };
  return values;
}

inline const char * const *EnumNamesShaderResourceType() {
  static const char * const names[28] = {
    "Unknown",
    "ConstantBuffer",
    "TextureBuffer",
    "TexelBuffer",
    "RWTexelBuffer",
    "StructuredBuffer",
    "RWStructuredBuffer",
    "ByteAddressBuffer",
    "RWByteAddressBuffer",
    "Texture1D",
    "Texture1DArray",
    "Texture2D",
    "Texture2DArray",
    "Texture2DMS",
    "Texture2DMSArray",
    "Texture3D",
    "TextureCube",
    "TextureCubeArray",
    "RWTexture1D",
    "RWTexture1DArray",
    "RWTexture2D",
    "RWTexture2DArray",
    "RWTexture3D",
    "Sampler",
    "SubpassInput",
    "SubpassInputMS",
    "AccelStruct",
    nullptr
  };
  return names;
}

inline const char *EnumNameShaderResourceType(ShaderResourceType e) {
  if (flatbuffers::IsOutRange(e, ShaderResourceType::Unknown, ShaderResourceType::AccelStruct)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesShaderResourceType()[index];
}

enum class CROSS_SCHEMA_API ShaderVariableType : uint32_t {
  Unknown = 0,
  Bool = 1,
  UInt8 = 2,
  UInt16 = 3,
  UInt32 = 4,
  UInt64 = 5,
  Int8 = 6,
  Int16 = 7,
  Int32 = 8,
  Int64 = 9,
  Half = 10,
  Float = 11,
  Double = 12,
  Struct = 13,
  MIN = Unknown,
  MAX = Struct
};

inline const ShaderVariableType (&EnumValuesShaderVariableType())[14] {
  static const ShaderVariableType values[] = {
    ShaderVariableType::Unknown,
    ShaderVariableType::Bool,
    ShaderVariableType::UInt8,
    ShaderVariableType::UInt16,
    ShaderVariableType::UInt32,
    ShaderVariableType::UInt64,
    ShaderVariableType::Int8,
    ShaderVariableType::Int16,
    ShaderVariableType::Int32,
    ShaderVariableType::Int64,
    ShaderVariableType::Half,
    ShaderVariableType::Float,
    ShaderVariableType::Double,
    ShaderVariableType::Struct
  };
  return values;
}

inline const char * const *EnumNamesShaderVariableType() {
  static const char * const names[15] = {
    "Unknown",
    "Bool",
    "UInt8",
    "UInt16",
    "UInt32",
    "UInt64",
    "Int8",
    "Int16",
    "Int32",
    "Int64",
    "Half",
    "Float",
    "Double",
    "Struct",
    nullptr
  };
  return names;
}

inline const char *EnumNameShaderVariableType(ShaderVariableType e) {
  if (flatbuffers::IsOutRange(e, ShaderVariableType::Unknown, ShaderVariableType::Struct)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesShaderVariableType()[index];
}

enum class CROSS_SCHEMA_API ShaderStageBit : uint32_t {
  Unknown = 0,
  Vertex = 1,
  Hull = 2,
  Domain = 4,
  Geometry = 8,
  Pixel = 16,
  Compute = 32,
  Task = 64,
  Mesh = 128,
  RayGen = 256,
  ClosestHit = 512,
  AnyHit = 1024,
  Miss = 2048,
  Callable = 4096,
  InterSection = 8192,
  AllRayTracing = 16128,
  All = 4294967295,
  MIN = Unknown,
  MAX = All
};

inline const ShaderStageBit (&EnumValuesShaderStageBit())[17] {
  static const ShaderStageBit values[] = {
    ShaderStageBit::Unknown,
    ShaderStageBit::Vertex,
    ShaderStageBit::Hull,
    ShaderStageBit::Domain,
    ShaderStageBit::Geometry,
    ShaderStageBit::Pixel,
    ShaderStageBit::Compute,
    ShaderStageBit::Task,
    ShaderStageBit::Mesh,
    ShaderStageBit::RayGen,
    ShaderStageBit::ClosestHit,
    ShaderStageBit::AnyHit,
    ShaderStageBit::Miss,
    ShaderStageBit::Callable,
    ShaderStageBit::InterSection,
    ShaderStageBit::AllRayTracing,
    ShaderStageBit::All
  };
  return values;
}

inline const char *EnumNameShaderStageBit(ShaderStageBit e) {
  switch (e) {
    case ShaderStageBit::Unknown: return "Unknown";
    case ShaderStageBit::Vertex: return "Vertex";
    case ShaderStageBit::Hull: return "Hull";
    case ShaderStageBit::Domain: return "Domain";
    case ShaderStageBit::Geometry: return "Geometry";
    case ShaderStageBit::Pixel: return "Pixel";
    case ShaderStageBit::Compute: return "Compute";
    case ShaderStageBit::Task: return "Task";
    case ShaderStageBit::Mesh: return "Mesh";
    case ShaderStageBit::RayGen: return "RayGen";
    case ShaderStageBit::ClosestHit: return "ClosestHit";
    case ShaderStageBit::AnyHit: return "AnyHit";
    case ShaderStageBit::Miss: return "Miss";
    case ShaderStageBit::Callable: return "Callable";
    case ShaderStageBit::InterSection: return "InterSection";
    case ShaderStageBit::AllRayTracing: return "AllRayTracing";
    case ShaderStageBit::All: return "All";
    default: return "";
  }
}

enum class CROSS_SCHEMA_API ShaderCodeFormat : uint32_t {
  Unknown = 0,
  GLSL = 1,
  ESSL = 2,
  MSL_IOS = 3,
  MSL_OSX = 4,
  DXBC = 5,
  DXIL = 6,
  SPIR_V = 7,
  MTLLIB_IOS = 8,
  MTLLIB_OSX = 9,
  MIN = Unknown,
  MAX = MTLLIB_OSX
};

inline const ShaderCodeFormat (&EnumValuesShaderCodeFormat())[10] {
  static const ShaderCodeFormat values[] = {
    ShaderCodeFormat::Unknown,
    ShaderCodeFormat::GLSL,
    ShaderCodeFormat::ESSL,
    ShaderCodeFormat::MSL_IOS,
    ShaderCodeFormat::MSL_OSX,
    ShaderCodeFormat::DXBC,
    ShaderCodeFormat::DXIL,
    ShaderCodeFormat::SPIR_V,
    ShaderCodeFormat::MTLLIB_IOS,
    ShaderCodeFormat::MTLLIB_OSX
  };
  return values;
}

inline const char * const *EnumNamesShaderCodeFormat() {
  static const char * const names[11] = {
    "Unknown",
    "GLSL",
    "ESSL",
    "MSL_IOS",
    "MSL_OSX",
    "DXBC",
    "DXIL",
    "SPIR_V",
    "MTLLIB_IOS",
    "MTLLIB_OSX",
    nullptr
  };
  return names;
}

inline const char *EnumNameShaderCodeFormat(ShaderCodeFormat e) {
  if (flatbuffers::IsOutRange(e, ShaderCodeFormat::Unknown, ShaderCodeFormat::MTLLIB_OSX)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesShaderCodeFormat()[index];
}

FLATBUFFERS_MANUALLY_ALIGNED_STRUCT(4) CROSS_SCHEMA_API  ShaderVersion FLATBUFFERS_FINAL_CLASS {
 private:
  uint32_t format_;
  uint32_t major_version_;
  uint32_t minor_version_;

 public:
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  ShaderVersion();
  ShaderVersion(CrossSchema::ShaderCodeFormat _format, uint32_t _major_version, uint32_t _minor_version);
  CrossSchema::ShaderCodeFormat format() const;
  void mutate_format(CrossSchema::ShaderCodeFormat _format);
  uint32_t major_version() const;
  void mutate_major_version(uint32_t _major_version);
  uint32_t minor_version() const;
  void mutate_minor_version(uint32_t _minor_version);
};
FLATBUFFERS_STRUCT_END(ShaderVersion, 12);

inline const flatbuffers::TypeTable *ShaderResourceTypeTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ShaderResourceTypeTypeTable
  };
  static const char * const names[] = {
    "Unknown",
    "ConstantBuffer",
    "TextureBuffer",
    "TexelBuffer",
    "RWTexelBuffer",
    "StructuredBuffer",
    "RWStructuredBuffer",
    "ByteAddressBuffer",
    "RWByteAddressBuffer",
    "Texture1D",
    "Texture1DArray",
    "Texture2D",
    "Texture2DArray",
    "Texture2DMS",
    "Texture2DMSArray",
    "Texture3D",
    "TextureCube",
    "TextureCubeArray",
    "RWTexture1D",
    "RWTexture1DArray",
    "RWTexture2D",
    "RWTexture2DArray",
    "RWTexture3D",
    "Sampler",
    "SubpassInput",
    "SubpassInputMS",
    "AccelStruct"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_ENUM, 27, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ShaderVariableTypeTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ShaderVariableTypeTypeTable
  };
  static const char * const names[] = {
    "Unknown",
    "Bool",
    "UInt8",
    "UInt16",
    "UInt32",
    "UInt64",
    "Int8",
    "Int16",
    "Int32",
    "Int64",
    "Half",
    "Float",
    "Double",
    "Struct"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_ENUM, 14, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ShaderStageBitTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ShaderStageBitTypeTable
  };
  static const int64_t values[] = { 0, 1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192, 16128, 4294967295 };
  static const char * const names[] = {
    "Unknown",
    "Vertex",
    "Hull",
    "Domain",
    "Geometry",
    "Pixel",
    "Compute",
    "Task",
    "Mesh",
    "RayGen",
    "ClosestHit",
    "AnyHit",
    "Miss",
    "Callable",
    "InterSection",
    "AllRayTracing",
    "All"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_ENUM, 17, type_codes, type_refs, values, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ShaderCodeFormatTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ShaderCodeFormatTypeTable
  };
  static const char * const names[] = {
    "Unknown",
    "GLSL",
    "ESSL",
    "MSL_IOS",
    "MSL_OSX",
    "DXBC",
    "DXIL",
    "SPIR_V",
    "MTLLIB_IOS",
    "MTLLIB_OSX"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_ENUM, 10, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ShaderVersionTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ShaderCodeFormatTypeTable
  };
  static const int64_t values[] = { 0, 4, 8, 12 };
  static const char * const names[] = {
    "format",
    "major_version",
    "minor_version"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_STRUCT, 3, type_codes, type_refs, values, names
  };
  return &tt;
}

}  // namespace CrossSchema

#endif  // FLATBUFFERS_GENERATED_SHADERDEFINES_CROSSSCHEMA_H_
