include "BasicStruct.fbs";
include "ImportSkeleton.fbs";

namespace CrossSchema;

enum ImportAnimCompressType : byte { LinearReduction = 0, UniformSample, Raw }

enum ImportRootMotionLockType : byte { RefPose = 0, Anim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Zero}

enum ImportAdditiveSpaceType : byte { None = 0, LocalSpaceBase, RotationOffsetRootSpace }

enum ImportAdditiveBaseType : byte { None = 0, SkeletonRefPose, AnimScaled, AnimFrame }

table Notify_ScriptImpl
{
	callback_str:string;
}

table Notify_JumpSectionScriptImpl
{
	callback_str:string;
}

table Notify_JumpSectionFormulaImpl
{
	express_str:string;
}

union ImportAnimNotifyImpl { Notify_ScriptImpl, Notify_JumpSectionScriptImpl, Notify_JumpSectionFormulaImpl }

table ImportAnimNotify
{
	notify_type:string;
	notify_impl:ImportAnimNotifyImpl;
}

table ImportAnimNotifyEvent
{
	trigger_time:float;
	end_trigger_time:float;
	trigger_weight_thres:float;
	lod_thres:int;
	name:string;
	notify:ImportAnimNotify;
}

table ImportAnimSyncMarker
{
	marker_name:string;
	time:float;
}

table ImportAnimTrackPerBone
{
	pos:[float3];
	pos_t:[float];
	rot:[float4];
	rot_t:[float];
	scl:[float3];
	scl_t:[float];
}

table ImportAnimation
{
    name:string;
	ref_path:string;
    duration_sec:float;
    frame_num:int;
    skelt_num:int; 
	cpr_type: ImportAnimCompressType = LinearReduction;
    ref_skelt:ImportRefSkeleton;

	tracks_ani_buffer:[ImportAnimTrackPerBone]; 
	cpr_ani_buffer:[byte];

	notifies:[ImportAnimNotifyEvent];

	sync_markers:[ImportAnimSyncMarker];

	has_rootmotion:bool;
	root_lock_type:ImportRootMotionLockType = AnimFirstFrame;

	additive_path:string;
	additive_animspace:ImportAdditiveSpaceType;
	additive_basepose:ImportAdditiveBaseType;
	additive_baseframe:int;
	
	curve_set:[ubyte];
}

root_type ImportAnimation; 