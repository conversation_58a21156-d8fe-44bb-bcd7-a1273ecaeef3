namespace CrossSchema;

enum ImportBoneTransRetgtMode : byte { Animation = 0, Skeleton, AnimationScaled }

struct float3
{
	x:float;
	y:float;
	z:float;
}

struct float2
{
	x:float;
	y:float;
}

struct float4
{
	x:float;
	y:float;
	z:float; 
	w:float;
}

struct uint3
{
	x:uint;
	y:uint;
	z:uint;
}

struct uint4 
{
	x:uint;
	y:uint;
	z:uint;
	w:uint;
}

table matrix4x4
{
	matrix_value:[float];
}

table transform
{
	translate:float4;
	rotation:float4;
	scale:float4;
}

table ImportBoneNode
{
	name:string;
    boneid:uint;
	bonetype:int;
    parentid:uint;
	retarget:ImportBoneTransRetgtMode = Animation;
    bindposeinv:[float];
	bindposedef:[float];
    worldmatrix:[float];
}

struct GUID
{
	low:ulong;
	high:ulong;
}