include "BasicStruct.fbs";

namespace CrossSchema;

table ImportSlotGroup
{
    name:string;
    slot_names:[string];
}

table ImportTwinBone
{
	bonename:string;
	twinname:string;
}

table ImportSocket
{
    name:string;
    attached_bone:string;
}

table ImportRefSkeleton
{
    name:string;
    skelteon:[ImportBoneNode];
}

table ImportRunSkeleton
{
    fversion:uint = 0;
    fref_skelt:ImportRefSkeleton;
    fslots:[ImportSlotGroup];
    fsockets:[ImportSocket];
	ftwinbones:[ImportTwinBone];
}

root_type ImportRunSkeleton;