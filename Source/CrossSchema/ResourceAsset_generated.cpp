#include "ResourceAsset_generated.h"
namespace CrossSchema {
const flatbuffers::TypeTable* ResourceHeader::MiniReflectTypeTable(){
   return ResourceHeaderTypeTable();
  }
 ResourceHeader::ResourceHeader()
      : magicnumber_(0),
        version_(0),
        classid_(0),
        datasize_(0),
        reserved_(0) {
  }
ResourceHeader::ResourceHeader(int32_t _magicnumber, int32_t _version, int32_t _classid, int32_t _datasize, int32_t _reserved)
      : magicnumber_(flatbuffers::EndianScalar(_magicnumber)),
        version_(flatbuffers::EndianScalar(_version)),
        classid_(flatbuffers::EndianScalar(_classid)),
        datasize_(flatbuffers::EndianScalar(_datasize)),
        reserved_(flatbuffers::EndianScalar(_reserved)) {
  }
int32_t  ResourceHeader::magicnumber() const{
    return flatbuffers::EndianScalar(magicnumber_);
  }
void ResourceHeader::mutate_magicnumber(int32_t _magicnumber) {
    flatbuffers::WriteScalar(&magicnumber_, _magicnumber);
  }
int32_t  ResourceHeader::version() const{
    return flatbuffers::EndianScalar(version_);
  }
void ResourceHeader::mutate_version(int32_t _version) {
    flatbuffers::WriteScalar(&version_, _version);
  }
int32_t  ResourceHeader::classid() const{
    return flatbuffers::EndianScalar(classid_);
  }
void ResourceHeader::mutate_classid(int32_t _classid) {
    flatbuffers::WriteScalar(&classid_, _classid);
  }
int32_t  ResourceHeader::datasize() const{
    return flatbuffers::EndianScalar(datasize_);
  }
void ResourceHeader::mutate_datasize(int32_t _datasize) {
    flatbuffers::WriteScalar(&datasize_, _datasize);
  }
int32_t  ResourceHeader::reserved() const{
    return flatbuffers::EndianScalar(reserved_);
  }
void ResourceHeader::mutate_reserved(int32_t _reserved) {
    flatbuffers::WriteScalar(&reserved_, _reserved);
  }
 const flatbuffers::TypeTable * ResourceAsset::MiniReflectTypeTable(){
    return ResourceAssetTypeTable();
  }
const CrossSchema::ResourceHeader * ResourceAsset::header() const{
    return GetStruct<const CrossSchema::ResourceHeader *>(VT_HEADER);
  }
  CrossSchema::ResourceHeader * ResourceAsset::mutable_header() {
    return GetStruct<CrossSchema::ResourceHeader *>(VT_HEADER);
  }
const flatbuffers::String * ResourceAsset::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * ResourceAsset::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
CrossSchema::ResourceType  ResourceAsset::resource_type() const{
    return static_cast<CrossSchema::ResourceType>(GetField<uint8_t>(VT_RESOURCE_TYPE, 0));
  }
const void * ResourceAsset::resource() const{
    return GetPointer<const void *>(VT_RESOURCE);
  }
  const CrossSchema::TextureAsset * ResourceAsset::resource_as_TextureAsset() const{
    return resource_type() == CrossSchema::ResourceType::TextureAsset ? static_cast<const CrossSchema::TextureAsset *>(resource()) : nullptr;
  }
  const CrossSchema::MaterialAsset * ResourceAsset::resource_as_MaterialAsset() const{
    return resource_type() == CrossSchema::ResourceType::MaterialAsset ? static_cast<const CrossSchema::MaterialAsset *>(resource()) : nullptr;
  }
  const CrossSchema::ImportMeshes * ResourceAsset::resource_as_ImportMeshes() const{
    return resource_type() == CrossSchema::ResourceType::ImportMeshes ? static_cast<const CrossSchema::ImportMeshes *>(resource()) : nullptr;
  }
  const CrossSchema::ImportRunSkeleton * ResourceAsset::resource_as_ImportRunSkeleton() const{
    return resource_type() == CrossSchema::ResourceType::ImportRunSkeleton ? static_cast<const CrossSchema::ImportRunSkeleton *>(resource()) : nullptr;
  }
  const CrossSchema::ImportAnimation * ResourceAsset::resource_as_ImportAnimation() const{
    return resource_type() == CrossSchema::ResourceType::ImportAnimation ? static_cast<const CrossSchema::ImportAnimation *>(resource()) : nullptr;
  }
  const CrossSchema::GraphicsShaderAsset * ResourceAsset::resource_as_GraphicsShaderAsset() const{
    return resource_type() == CrossSchema::ResourceType::GraphicsShaderAsset ? static_cast<const CrossSchema::GraphicsShaderAsset *>(resource()) : nullptr;
  }
  const CrossSchema::ComputeShaderAsset * ResourceAsset::resource_as_ComputeShaderAsset() const{
    return resource_type() == CrossSchema::ResourceType::ComputeShaderAsset ? static_cast<const CrossSchema::ComputeShaderAsset *>(resource()) : nullptr;
  }
  const CrossSchema::ImportMeshAssetData * ResourceAsset::resource_as_ImportMeshAssetData() const{
    return resource_type() == CrossSchema::ResourceType::ImportMeshAssetData ? static_cast<const CrossSchema::ImportMeshAssetData *>(resource()) : nullptr;
  }
  const CrossSchema::RayTracingShaderAsset * ResourceAsset::resource_as_RayTracingShaderAsset() const{
    return resource_type() == CrossSchema::ResourceType::RayTracingShaderAsset ? static_cast<const CrossSchema::RayTracingShaderAsset *>(resource()) : nullptr;
  }
  void * ResourceAsset::mutable_resource() {
    return GetPointer<void *>(VT_RESOURCE);
  }
 bool ResourceAsset::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyField<CrossSchema::ResourceHeader>(verifier, VT_HEADER) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<uint8_t>(verifier, VT_RESOURCE_TYPE) &&
           VerifyOffset(verifier, VT_RESOURCE) &&
           VerifyResourceType(verifier, resource(), resource_type()) &&
           verifier.EndTable();
  }
template<> inline const CrossSchema::TextureAsset *ResourceAsset::resource_as<CrossSchema::TextureAsset>() const {
  return resource_as_TextureAsset();
}

template<> inline const CrossSchema::MaterialAsset *ResourceAsset::resource_as<CrossSchema::MaterialAsset>() const {
  return resource_as_MaterialAsset();
}

template<> inline const CrossSchema::ImportMeshes *ResourceAsset::resource_as<CrossSchema::ImportMeshes>() const {
  return resource_as_ImportMeshes();
}

template<> inline const CrossSchema::ImportRunSkeleton *ResourceAsset::resource_as<CrossSchema::ImportRunSkeleton>() const {
  return resource_as_ImportRunSkeleton();
}

template<> inline const CrossSchema::ImportAnimation *ResourceAsset::resource_as<CrossSchema::ImportAnimation>() const {
  return resource_as_ImportAnimation();
}

template<> inline const CrossSchema::GraphicsShaderAsset *ResourceAsset::resource_as<CrossSchema::GraphicsShaderAsset>() const {
  return resource_as_GraphicsShaderAsset();
}

template<> inline const CrossSchema::ComputeShaderAsset *ResourceAsset::resource_as<CrossSchema::ComputeShaderAsset>() const {
  return resource_as_ComputeShaderAsset();
}

template<> inline const CrossSchema::ImportMeshAssetData *ResourceAsset::resource_as<CrossSchema::ImportMeshAssetData>() const {
  return resource_as_ImportMeshAssetData();
}

template<> inline const CrossSchema::RayTracingShaderAsset *ResourceAsset::resource_as<CrossSchema::RayTracingShaderAsset>() const {
  return resource_as_RayTracingShaderAsset();
}

  void ResourceAssetBuilder::add_header(const CrossSchema::ResourceHeader *header) {
    fbb_.AddStruct(ResourceAsset::VT_HEADER, header);
  }
  void ResourceAssetBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(ResourceAsset::VT_NAME, name);
  }
  void ResourceAssetBuilder::add_resource_type(CrossSchema::ResourceType resource_type) {
    fbb_.AddElement<uint8_t>(ResourceAsset::VT_RESOURCE_TYPE, static_cast<uint8_t>(resource_type), 0);
  }
  void ResourceAssetBuilder::add_resource(flatbuffers::Offset<void> resource) {
    fbb_.AddOffset(ResourceAsset::VT_RESOURCE, resource);
  }
flatbuffers::Offset<ResourceAsset> CreateResourceAsset(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::ResourceHeader *header,
    flatbuffers::Offset<flatbuffers::String> name,
    CrossSchema::ResourceType resource_type,
    flatbuffers::Offset<void> resource){
    ResourceAssetBuilder builder_(_fbb);
  builder_.add_resource(resource);
  builder_.add_name(name);
  builder_.add_header(header);
  builder_.add_resource_type(resource_type);
  return builder_.Finish();
}

flatbuffers::Offset<ResourceAsset> CreateResourceAssetDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::ResourceHeader *header,
    const char *name,
    CrossSchema::ResourceType resource_type,
    flatbuffers::Offset<void> resource) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  return CrossSchema::CreateResourceAsset(
      _fbb,
      header,
      name__,
      resource_type,
      resource);
}

ResourceAssetT *ResourceAsset::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ResourceAssetT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ResourceAsset::UnPackTo(ResourceAssetT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = header(); if (_e) _o->header = std::unique_ptr<CrossSchema::ResourceHeader>(new CrossSchema::ResourceHeader(*_e)); }
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = resource_type(); _o->resource.type = _e; }
  { auto _e = resource(); if (_e) _o->resource.value = CrossSchema::ResourceTypeUnion::UnPack(_e, resource_type(), _resolver); }
}

CROSS_SCHEMA_API flatbuffers::Offset<ResourceAsset> ResourceAsset::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ResourceAssetT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateResourceAsset(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ResourceAsset> CreateResourceAsset(flatbuffers::FlatBufferBuilder &_fbb, const ResourceAssetT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ResourceAssetT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _header = _o->header ? _o->header.get() : 0;
  auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
  auto _resource_type = _o->resource.type;
  auto _resource = _o->resource.Pack(_fbb);
  return CrossSchema::CreateResourceAsset(
      _fbb,
      _header,
      _name,
      _resource_type,
      _resource);
}

bool VerifyResourceType(flatbuffers::Verifier &verifier, const void *obj, ResourceType type){
  switch (type) {
    case ResourceType::NONE: {
      return true;
    }
    case ResourceType::TextureAsset: {
      auto ptr = reinterpret_cast<const CrossSchema::TextureAsset *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case ResourceType::MaterialAsset: {
      auto ptr = reinterpret_cast<const CrossSchema::MaterialAsset *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case ResourceType::ImportMeshes: {
      auto ptr = reinterpret_cast<const CrossSchema::ImportMeshes *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case ResourceType::ImportRunSkeleton: {
      auto ptr = reinterpret_cast<const CrossSchema::ImportRunSkeleton *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case ResourceType::ImportAnimation: {
      auto ptr = reinterpret_cast<const CrossSchema::ImportAnimation *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case ResourceType::GraphicsShaderAsset: {
      auto ptr = reinterpret_cast<const CrossSchema::GraphicsShaderAsset *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case ResourceType::ComputeShaderAsset: {
      auto ptr = reinterpret_cast<const CrossSchema::ComputeShaderAsset *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case ResourceType::ImportMeshAssetData: {
      auto ptr = reinterpret_cast<const CrossSchema::ImportMeshAssetData *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case ResourceType::RayTracingShaderAsset: {
      auto ptr = reinterpret_cast<const CrossSchema::RayTracingShaderAsset *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

bool VerifyResourceTypeVector(flatbuffers::Verifier &verifier, const flatbuffers::Vector<flatbuffers::Offset<void>> *values, const flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyResourceType(
        verifier,  values->Get(i), types->GetEnum<ResourceType>(i))) {
      return false;
    }
  }
  return true;
}

void *ResourceTypeUnion::UnPack(const void *obj, ResourceType type, const flatbuffers::resolver_function_t *resolver) {
  switch (type) {
    case ResourceType::TextureAsset: {
      auto ptr = reinterpret_cast<const CrossSchema::TextureAsset *>(obj);
      return ptr->UnPack(resolver);
    }
    case ResourceType::MaterialAsset: {
      auto ptr = reinterpret_cast<const CrossSchema::MaterialAsset *>(obj);
      return ptr->UnPack(resolver);
    }
    case ResourceType::ImportMeshes: {
      auto ptr = reinterpret_cast<const CrossSchema::ImportMeshes *>(obj);
      return ptr->UnPack(resolver);
    }
    case ResourceType::ImportRunSkeleton: {
      auto ptr = reinterpret_cast<const CrossSchema::ImportRunSkeleton *>(obj);
      return ptr->UnPack(resolver);
    }
    case ResourceType::ImportAnimation: {
      auto ptr = reinterpret_cast<const CrossSchema::ImportAnimation *>(obj);
      return ptr->UnPack(resolver);
    }
    case ResourceType::GraphicsShaderAsset: {
      auto ptr = reinterpret_cast<const CrossSchema::GraphicsShaderAsset *>(obj);
      return ptr->UnPack(resolver);
    }
    case ResourceType::ComputeShaderAsset: {
      auto ptr = reinterpret_cast<const CrossSchema::ComputeShaderAsset *>(obj);
      return ptr->UnPack(resolver);
    }
    case ResourceType::ImportMeshAssetData: {
      auto ptr = reinterpret_cast<const CrossSchema::ImportMeshAssetData *>(obj);
      return ptr->UnPack(resolver);
    }
    case ResourceType::RayTracingShaderAsset: {
      auto ptr = reinterpret_cast<const CrossSchema::RayTracingShaderAsset *>(obj);
      return ptr->UnPack(resolver);
    }
    default: return nullptr;
  }
}

flatbuffers::Offset<void> ResourceTypeUnion::Pack(flatbuffers::FlatBufferBuilder &_fbb, const flatbuffers::rehasher_function_t *_rehasher) const {
  switch (type) {
    case ResourceType::TextureAsset: {
      auto ptr = reinterpret_cast<const CrossSchema::TextureAssetT *>(value);
      return CreateTextureAsset(_fbb, ptr, _rehasher).Union();
    }
    case ResourceType::MaterialAsset: {
      auto ptr = reinterpret_cast<const CrossSchema::MaterialAssetT *>(value);
      return CreateMaterialAsset(_fbb, ptr, _rehasher).Union();
    }
    case ResourceType::ImportMeshes: {
      auto ptr = reinterpret_cast<const CrossSchema::ImportMeshesT *>(value);
      return CreateImportMeshes(_fbb, ptr, _rehasher).Union();
    }
    case ResourceType::ImportRunSkeleton: {
      auto ptr = reinterpret_cast<const CrossSchema::ImportRunSkeletonT *>(value);
      return CreateImportRunSkeleton(_fbb, ptr, _rehasher).Union();
    }
    case ResourceType::ImportAnimation: {
      auto ptr = reinterpret_cast<const CrossSchema::ImportAnimationT *>(value);
      return CreateImportAnimation(_fbb, ptr, _rehasher).Union();
    }
    case ResourceType::GraphicsShaderAsset: {
      auto ptr = reinterpret_cast<const CrossSchema::GraphicsShaderAssetT *>(value);
      return CreateGraphicsShaderAsset(_fbb, ptr, _rehasher).Union();
    }
    case ResourceType::ComputeShaderAsset: {
      auto ptr = reinterpret_cast<const CrossSchema::ComputeShaderAssetT *>(value);
      return CreateComputeShaderAsset(_fbb, ptr, _rehasher).Union();
    }
    case ResourceType::ImportMeshAssetData: {
      auto ptr = reinterpret_cast<const CrossSchema::ImportMeshAssetDataT *>(value);
      return CreateImportMeshAssetData(_fbb, ptr, _rehasher).Union();
    }
    case ResourceType::RayTracingShaderAsset: {
      auto ptr = reinterpret_cast<const CrossSchema::RayTracingShaderAssetT *>(value);
      return CreateRayTracingShaderAsset(_fbb, ptr, _rehasher).Union();
    }
    default: return 0;
  }
}

ResourceTypeUnion::ResourceTypeUnion(const ResourceTypeUnion &u) : type(u.type), value(nullptr) {
  switch (type) {
    case ResourceType::TextureAsset: {
      FLATBUFFERS_ASSERT(false);  // CrossSchema::TextureAssetT not copyable.
      break;
    }
    case ResourceType::MaterialAsset: {
      FLATBUFFERS_ASSERT(false);  // CrossSchema::MaterialAssetT not copyable.
      break;
    }
    case ResourceType::ImportMeshes: {
      FLATBUFFERS_ASSERT(false);  // CrossSchema::ImportMeshesT not copyable.
      break;
    }
    case ResourceType::ImportRunSkeleton: {
      FLATBUFFERS_ASSERT(false);  // CrossSchema::ImportRunSkeletonT not copyable.
      break;
    }
    case ResourceType::ImportAnimation: {
      FLATBUFFERS_ASSERT(false);  // CrossSchema::ImportAnimationT not copyable.
      break;
    }
    case ResourceType::GraphicsShaderAsset: {
      FLATBUFFERS_ASSERT(false);  // CrossSchema::GraphicsShaderAssetT not copyable.
      break;
    }
    case ResourceType::ComputeShaderAsset: {
      FLATBUFFERS_ASSERT(false);  // CrossSchema::ComputeShaderAssetT not copyable.
      break;
    }
    case ResourceType::ImportMeshAssetData: {
      FLATBUFFERS_ASSERT(false);  // CrossSchema::ImportMeshAssetDataT not copyable.
      break;
    }
    case ResourceType::RayTracingShaderAsset: {
      FLATBUFFERS_ASSERT(false);  // CrossSchema::RayTracingShaderAssetT not copyable.
      break;
    }
    default:
      break;
  }
}

void ResourceTypeUnion::Reset() {
  switch (type) {
    case ResourceType::TextureAsset: {
      auto ptr = reinterpret_cast<CrossSchema::TextureAssetT *>(value);
      delete ptr;
      break;
    }
    case ResourceType::MaterialAsset: {
      auto ptr = reinterpret_cast<CrossSchema::MaterialAssetT *>(value);
      delete ptr;
      break;
    }
    case ResourceType::ImportMeshes: {
      auto ptr = reinterpret_cast<CrossSchema::ImportMeshesT *>(value);
      delete ptr;
      break;
    }
    case ResourceType::ImportRunSkeleton: {
      auto ptr = reinterpret_cast<CrossSchema::ImportRunSkeletonT *>(value);
      delete ptr;
      break;
    }
    case ResourceType::ImportAnimation: {
      auto ptr = reinterpret_cast<CrossSchema::ImportAnimationT *>(value);
      delete ptr;
      break;
    }
    case ResourceType::GraphicsShaderAsset: {
      auto ptr = reinterpret_cast<CrossSchema::GraphicsShaderAssetT *>(value);
      delete ptr;
      break;
    }
    case ResourceType::ComputeShaderAsset: {
      auto ptr = reinterpret_cast<CrossSchema::ComputeShaderAssetT *>(value);
      delete ptr;
      break;
    }
    case ResourceType::ImportMeshAssetData: {
      auto ptr = reinterpret_cast<CrossSchema::ImportMeshAssetDataT *>(value);
      delete ptr;
      break;
    }
    case ResourceType::RayTracingShaderAsset: {
      auto ptr = reinterpret_cast<CrossSchema::RayTracingShaderAssetT *>(value);
      delete ptr;
      break;
    }
    default: break;
  }
  value = nullptr;
  type = ResourceType::NONE;
}

const CrossSchema::ResourceAsset *GetResourceAsset(const void *buf){
 return flatbuffers::GetRoot<CrossSchema::ResourceAsset>(buf);
}

const CrossSchema::ResourceAsset *GetSizePrefixedResourceAsset(const void *buf) {
return flatbuffers::GetSizePrefixedRoot<CrossSchema::ResourceAsset>(buf);
}

ResourceAsset *GetMutableResourceAsset(void *buf) {
return flatbuffers::GetMutableRoot<ResourceAsset>(buf);
}

bool VerifyResourceAssetBuffer(flatbuffers::Verifier &verifier) {
return verifier.VerifyBuffer<CrossSchema::ResourceAsset>(nullptr);
}

bool VerifySizePrefixedResourceAssetBuffer(flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<CrossSchema::ResourceAsset>(nullptr);
}

void FinishResourceAssetBuffer(flatbuffers::FlatBufferBuilder &fbb,flatbuffers::Offset<CrossSchema::ResourceAsset> root) {
  fbb.Finish(root);
}

void FinishSizePrefixedResourceAssetBuffer(flatbuffers::FlatBufferBuilder &fbb,flatbuffers::Offset<CrossSchema::ResourceAsset> root) {
fbb.FinishSizePrefixed(root);
}

std::unique_ptr<CrossSchema::ResourceAssetT> UnPackResourceAsset(const void *buf,const flatbuffers::resolver_function_t *res) {
return std::unique_ptr<CrossSchema::ResourceAssetT>(GetResourceAsset(buf)->UnPack(res));
}

std::unique_ptr<CrossSchema::ResourceAssetT> UnPackSizePrefixedResourceAsset(const void *buf,const flatbuffers::resolver_function_t *res) {
return std::unique_ptr<CrossSchema::ResourceAssetT>(GetSizePrefixedResourceAsset(buf)->UnPack(res));
}

}  // namespace CrossSchema
