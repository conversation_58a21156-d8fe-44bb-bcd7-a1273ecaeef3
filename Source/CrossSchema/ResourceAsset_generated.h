// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_RESOURCEASSET_CROSSSCHEMA_H_
#define FLATBUFFERS_GENERATED_RESOURCEASSET_CROSSSCHEMA_H_

#include "flatbuffers/flatbuffers.h"

#include "CrossSchemaForward.h"
#include "BasicStruct_generated.h"
#include "ImportAnimation_generated.h"
#include "ImportMesh_generated.h"
#include "ImportMeshAssetData_generated.h"
#include "ImportSkeleton_generated.h"
#include "MaterialAsset_generated.h"
#include "ShaderAsset_generated.h"
#include "ShaderDefines_generated.h"
#include "TextureAsset_generated.h"

namespace CrossSchema {

struct ResourceHeader;

struct ResourceAsset;
struct ResourceAssetBuilder;
struct ResourceAssetT;

inline const flatbuffers::TypeTable *ResourceHeaderTypeTable();

inline const flatbuffers::TypeTable *ResourceAssetTypeTable();

enum class CROSS_SCHEMA_API ResourceType : uint8_t {
  NONE = 0,
  TextureAsset = 1,
  MaterialAsset = 2,
  ImportMeshes = 3,
  ImportRunSkeleton = 4,
  ImportAnimation = 5,
  GraphicsShaderAsset = 6,
  ComputeShaderAsset = 7,
  ImportMeshAssetData = 8,
  RayTracingShaderAsset = 9,
  MIN = NONE,
  MAX = RayTracingShaderAsset
};

inline const ResourceType (&EnumValuesResourceType())[10] {
  static const ResourceType values[] = {
    ResourceType::NONE,
    ResourceType::TextureAsset,
    ResourceType::MaterialAsset,
    ResourceType::ImportMeshes,
    ResourceType::ImportRunSkeleton,
    ResourceType::ImportAnimation,
    ResourceType::GraphicsShaderAsset,
    ResourceType::ComputeShaderAsset,
    ResourceType::ImportMeshAssetData,
    ResourceType::RayTracingShaderAsset
  };
  return values;
}

inline const char * const *EnumNamesResourceType() {
  static const char * const names[11] = {
    "NONE",
    "TextureAsset",
    "MaterialAsset",
    "ImportMeshes",
    "ImportRunSkeleton",
    "ImportAnimation",
    "GraphicsShaderAsset",
    "ComputeShaderAsset",
    "ImportMeshAssetData",
    "RayTracingShaderAsset",
    nullptr
  };
  return names;
}

inline const char *EnumNameResourceType(ResourceType e) {
  if (flatbuffers::IsOutRange(e, ResourceType::NONE, ResourceType::RayTracingShaderAsset)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesResourceType()[index];
}

template<typename T> struct ResourceTypeTraits {
  static const ResourceType enum_value = ResourceType::NONE;
};

template<> struct ResourceTypeTraits<CrossSchema::TextureAsset> {
  static const ResourceType enum_value = ResourceType::TextureAsset;
};

template<> struct ResourceTypeTraits<CrossSchema::MaterialAsset> {
  static const ResourceType enum_value = ResourceType::MaterialAsset;
};

template<> struct ResourceTypeTraits<CrossSchema::ImportMeshes> {
  static const ResourceType enum_value = ResourceType::ImportMeshes;
};

template<> struct ResourceTypeTraits<CrossSchema::ImportRunSkeleton> {
  static const ResourceType enum_value = ResourceType::ImportRunSkeleton;
};

template<> struct ResourceTypeTraits<CrossSchema::ImportAnimation> {
  static const ResourceType enum_value = ResourceType::ImportAnimation;
};

template<> struct ResourceTypeTraits<CrossSchema::GraphicsShaderAsset> {
  static const ResourceType enum_value = ResourceType::GraphicsShaderAsset;
};

template<> struct ResourceTypeTraits<CrossSchema::ComputeShaderAsset> {
  static const ResourceType enum_value = ResourceType::ComputeShaderAsset;
};

template<> struct ResourceTypeTraits<CrossSchema::ImportMeshAssetData> {
  static const ResourceType enum_value = ResourceType::ImportMeshAssetData;
};

template<> struct ResourceTypeTraits<CrossSchema::RayTracingShaderAsset> {
  static const ResourceType enum_value = ResourceType::RayTracingShaderAsset;
};

struct CROSS_SCHEMA_API ResourceTypeUnion {
  ResourceType type;
  void *value;

  ResourceTypeUnion() : type(ResourceType::NONE), value(nullptr) {}
  ResourceTypeUnion(ResourceTypeUnion&& u) FLATBUFFERS_NOEXCEPT :
    type(ResourceType::NONE), value(nullptr)
    { std::swap(type, u.type); std::swap(value, u.value); }
  ResourceTypeUnion(const ResourceTypeUnion &);
  ResourceTypeUnion &operator=(const ResourceTypeUnion &u)
    { ResourceTypeUnion t(u); std::swap(type, t.type); std::swap(value, t.value); return *this; }
  ResourceTypeUnion &operator=(ResourceTypeUnion &&u) FLATBUFFERS_NOEXCEPT
    { std::swap(type, u.type); std::swap(value, u.value); return *this; }
  ~ResourceTypeUnion() { Reset(); }

  void Reset();

#ifndef FLATBUFFERS_CPP98_STL
  template <typename T>
  void Set(T&& val) {
    using RT = typename std::remove_reference<T>::type;
    Reset();
    type = ResourceTypeTraits<typename RT::TableType>::enum_value;
    if (type != ResourceType::NONE) {
      value = new RT(std::forward<T>(val));
    }
  }
#endif  // FLATBUFFERS_CPP98_STL

  static void *UnPack(const void *obj, ResourceType type, const flatbuffers::resolver_function_t *resolver);
  flatbuffers::Offset<void> Pack(flatbuffers::FlatBufferBuilder &_fbb, const flatbuffers::rehasher_function_t *_rehasher = nullptr) const;

  CrossSchema::TextureAssetT *AsTextureAsset() {
    return type == ResourceType::TextureAsset ?
      reinterpret_cast<CrossSchema::TextureAssetT *>(value) : nullptr;
  }
  const CrossSchema::TextureAssetT *AsTextureAsset() const {
    return type == ResourceType::TextureAsset ?
      reinterpret_cast<const CrossSchema::TextureAssetT *>(value) : nullptr;
  }
  CrossSchema::MaterialAssetT *AsMaterialAsset() {
    return type == ResourceType::MaterialAsset ?
      reinterpret_cast<CrossSchema::MaterialAssetT *>(value) : nullptr;
  }
  const CrossSchema::MaterialAssetT *AsMaterialAsset() const {
    return type == ResourceType::MaterialAsset ?
      reinterpret_cast<const CrossSchema::MaterialAssetT *>(value) : nullptr;
  }
  CrossSchema::ImportMeshesT *AsImportMeshes() {
    return type == ResourceType::ImportMeshes ?
      reinterpret_cast<CrossSchema::ImportMeshesT *>(value) : nullptr;
  }
  const CrossSchema::ImportMeshesT *AsImportMeshes() const {
    return type == ResourceType::ImportMeshes ?
      reinterpret_cast<const CrossSchema::ImportMeshesT *>(value) : nullptr;
  }
  CrossSchema::ImportRunSkeletonT *AsImportRunSkeleton() {
    return type == ResourceType::ImportRunSkeleton ?
      reinterpret_cast<CrossSchema::ImportRunSkeletonT *>(value) : nullptr;
  }
  const CrossSchema::ImportRunSkeletonT *AsImportRunSkeleton() const {
    return type == ResourceType::ImportRunSkeleton ?
      reinterpret_cast<const CrossSchema::ImportRunSkeletonT *>(value) : nullptr;
  }
  CrossSchema::ImportAnimationT *AsImportAnimation() {
    return type == ResourceType::ImportAnimation ?
      reinterpret_cast<CrossSchema::ImportAnimationT *>(value) : nullptr;
  }
  const CrossSchema::ImportAnimationT *AsImportAnimation() const {
    return type == ResourceType::ImportAnimation ?
      reinterpret_cast<const CrossSchema::ImportAnimationT *>(value) : nullptr;
  }
  CrossSchema::GraphicsShaderAssetT *AsGraphicsShaderAsset() {
    return type == ResourceType::GraphicsShaderAsset ?
      reinterpret_cast<CrossSchema::GraphicsShaderAssetT *>(value) : nullptr;
  }
  const CrossSchema::GraphicsShaderAssetT *AsGraphicsShaderAsset() const {
    return type == ResourceType::GraphicsShaderAsset ?
      reinterpret_cast<const CrossSchema::GraphicsShaderAssetT *>(value) : nullptr;
  }
  CrossSchema::ComputeShaderAssetT *AsComputeShaderAsset() {
    return type == ResourceType::ComputeShaderAsset ?
      reinterpret_cast<CrossSchema::ComputeShaderAssetT *>(value) : nullptr;
  }
  const CrossSchema::ComputeShaderAssetT *AsComputeShaderAsset() const {
    return type == ResourceType::ComputeShaderAsset ?
      reinterpret_cast<const CrossSchema::ComputeShaderAssetT *>(value) : nullptr;
  }
  CrossSchema::ImportMeshAssetDataT *AsImportMeshAssetData() {
    return type == ResourceType::ImportMeshAssetData ?
      reinterpret_cast<CrossSchema::ImportMeshAssetDataT *>(value) : nullptr;
  }
  const CrossSchema::ImportMeshAssetDataT *AsImportMeshAssetData() const {
    return type == ResourceType::ImportMeshAssetData ?
      reinterpret_cast<const CrossSchema::ImportMeshAssetDataT *>(value) : nullptr;
  }
  CrossSchema::RayTracingShaderAssetT *AsRayTracingShaderAsset() {
    return type == ResourceType::RayTracingShaderAsset ?
      reinterpret_cast<CrossSchema::RayTracingShaderAssetT *>(value) : nullptr;
  }
  const CrossSchema::RayTracingShaderAssetT *AsRayTracingShaderAsset() const {
    return type == ResourceType::RayTracingShaderAsset ?
      reinterpret_cast<const CrossSchema::RayTracingShaderAssetT *>(value) : nullptr;
  }
};

bool VerifyResourceType(flatbuffers::Verifier &verifier, const void *obj, ResourceType type);
bool VerifyResourceTypeVector(flatbuffers::Verifier &verifier, const flatbuffers::Vector<flatbuffers::Offset<void>> *values, const flatbuffers::Vector<uint8_t> *types);

FLATBUFFERS_MANUALLY_ALIGNED_STRUCT(4) CROSS_SCHEMA_API  ResourceHeader FLATBUFFERS_FINAL_CLASS {
 private:
  int32_t magicnumber_;
  int32_t version_;
  int32_t classid_;
  int32_t datasize_;
  int32_t reserved_;

 public:
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  ResourceHeader();
  ResourceHeader(int32_t _magicnumber, int32_t _version, int32_t _classid, int32_t _datasize, int32_t _reserved);
  int32_t magicnumber() const;
  void mutate_magicnumber(int32_t _magicnumber);
  int32_t version() const;
  void mutate_version(int32_t _version);
  int32_t classid() const;
  void mutate_classid(int32_t _classid);
  int32_t datasize() const;
  void mutate_datasize(int32_t _datasize);
  int32_t reserved() const;
  void mutate_reserved(int32_t _reserved);
};
FLATBUFFERS_STRUCT_END(ResourceHeader, 20);

struct ResourceAssetT : public flatbuffers::NativeTable {
  typedef ResourceAsset TableType;
  std::unique_ptr<CrossSchema::ResourceHeader> header{};
  std::string name{};
  CrossSchema::ResourceTypeUnion resource{};
};

struct ResourceAsset FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ResourceAssetT NativeTableType;
  typedef ResourceAssetBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_HEADER = 4,
    VT_NAME = 6,
    VT_RESOURCE_TYPE = 8,
    VT_RESOURCE = 10
  };
CROSS_SCHEMA_API  const CrossSchema::ResourceHeader *header() const;
  CROSS_SCHEMA_API  CrossSchema::ResourceHeader *mutable_header();
CROSS_SCHEMA_API  const flatbuffers::String *name() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_name();
CROSS_SCHEMA_API  CrossSchema::ResourceType resource_type() const;
CROSS_SCHEMA_API  const void *resource() const;
  template<typename T>    CROSS_SCHEMA_API  const T *resource_as() const;
 CROSS_SCHEMA_API  const CrossSchema::TextureAsset * resource_as_TextureAsset() const;
 CROSS_SCHEMA_API  const CrossSchema::MaterialAsset * resource_as_MaterialAsset() const;
 CROSS_SCHEMA_API  const CrossSchema::ImportMeshes * resource_as_ImportMeshes() const;
 CROSS_SCHEMA_API  const CrossSchema::ImportRunSkeleton * resource_as_ImportRunSkeleton() const;
 CROSS_SCHEMA_API  const CrossSchema::ImportAnimation * resource_as_ImportAnimation() const;
 CROSS_SCHEMA_API  const CrossSchema::GraphicsShaderAsset * resource_as_GraphicsShaderAsset() const;
 CROSS_SCHEMA_API  const CrossSchema::ComputeShaderAsset * resource_as_ComputeShaderAsset() const;
 CROSS_SCHEMA_API  const CrossSchema::ImportMeshAssetData * resource_as_ImportMeshAssetData() const;
 CROSS_SCHEMA_API  const CrossSchema::RayTracingShaderAsset * resource_as_RayTracingShaderAsset() const;
  CROSS_SCHEMA_API  void *mutable_resource();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ResourceAssetT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ResourceAssetT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ResourceAsset> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ResourceAssetT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ResourceAssetBuilder {
  typedef ResourceAsset Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_header(const CrossSchema::ResourceHeader *header);
  void add_name(flatbuffers::Offset<flatbuffers::String> name);
  void add_resource_type(CrossSchema::ResourceType resource_type);
  void add_resource(flatbuffers::Offset<void> resource);
  explicit ResourceAssetBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ResourceAsset> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ResourceAsset>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ResourceAsset> CreateResourceAsset(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::ResourceHeader *header = 0,
    flatbuffers::Offset<flatbuffers::String> name = 0,
    CrossSchema::ResourceType resource_type = CrossSchema::ResourceType::NONE,
    flatbuffers::Offset<void> resource = 0);
struct ResourceAsset::Traits {
  using type = ResourceAsset;
  static auto constexpr Create = CreateResourceAsset;
};

CROSS_SCHEMA_API flatbuffers::Offset<ResourceAsset> CreateResourceAssetDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::ResourceHeader *header = 0,
    const char *name = nullptr,
    CrossSchema::ResourceType resource_type = CrossSchema::ResourceType::NONE,
    flatbuffers::Offset<void> resource = 0);
CROSS_SCHEMA_API flatbuffers::Offset<ResourceAsset> CreateResourceAsset(flatbuffers::FlatBufferBuilder &_fbb, const ResourceAssetT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

inline const flatbuffers::TypeTable *ResourceTypeTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_SEQUENCE, 0, 1 },
    { flatbuffers::ET_SEQUENCE, 0, 2 },
    { flatbuffers::ET_SEQUENCE, 0, 3 },
    { flatbuffers::ET_SEQUENCE, 0, 4 },
    { flatbuffers::ET_SEQUENCE, 0, 5 },
    { flatbuffers::ET_SEQUENCE, 0, 6 },
    { flatbuffers::ET_SEQUENCE, 0, 7 },
    { flatbuffers::ET_SEQUENCE, 0, 8 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::TextureAssetTypeTable,
    CrossSchema::MaterialAssetTypeTable,
    CrossSchema::ImportMeshesTypeTable,
    CrossSchema::ImportRunSkeletonTypeTable,
    CrossSchema::ImportAnimationTypeTable,
    CrossSchema::GraphicsShaderAssetTypeTable,
    CrossSchema::ComputeShaderAssetTypeTable,
    CrossSchema::ImportMeshAssetDataTypeTable,
    CrossSchema::RayTracingShaderAssetTypeTable
  };
  static const char * const names[] = {
    "NONE",
    "TextureAsset",
    "MaterialAsset",
    "ImportMeshes",
    "ImportRunSkeleton",
    "ImportAnimation",
    "GraphicsShaderAsset",
    "ComputeShaderAsset",
    "ImportMeshAssetData",
    "RayTracingShaderAsset"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_UNION, 10, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ResourceHeaderTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_INT, 0, -1 },
    { flatbuffers::ET_INT, 0, -1 },
    { flatbuffers::ET_INT, 0, -1 },
    { flatbuffers::ET_INT, 0, -1 },
    { flatbuffers::ET_INT, 0, -1 }
  };
  static const int64_t values[] = { 0, 4, 8, 12, 16, 20 };
  static const char * const names[] = {
    "magicnumber",
    "version",
    "classid",
    "datasize",
    "reserved"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_STRUCT, 5, type_codes, nullptr, values, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ResourceAssetTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_UTYPE, 0, 1 },
    { flatbuffers::ET_SEQUENCE, 0, 1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ResourceHeaderTypeTable,
    CrossSchema::ResourceTypeTypeTable
  };
  static const char * const names[] = {
    "header",
    "name",
    "resource_type",
    "resource"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 4, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

CROSS_SCHEMA_API const CrossSchema::ResourceAsset *GetResourceAsset(const void *buf);
CROSS_SCHEMA_API const CrossSchema::ResourceAsset *GetSizePrefixedResourceAsset(const void *buf);
CROSS_SCHEMA_API ResourceAsset *GetMutableResourceAsset(void *buf);
CROSS_SCHEMA_API bool VerifyResourceAssetBuffer(flatbuffers::Verifier &verifier);
CROSS_SCHEMA_API bool VerifySizePrefixedResourceAssetBuffer(flatbuffers::Verifier &verifier);
CROSS_SCHEMA_API void FinishResourceAssetBuffer(flatbuffers::FlatBufferBuilder &fbb,flatbuffers::Offset<CrossSchema::ResourceAsset> root);
CROSS_SCHEMA_API void FinishSizePrefixedResourceAssetBuffer(flatbuffers::FlatBufferBuilder &fbb,flatbuffers::Offset<CrossSchema::ResourceAsset> root);
CROSS_SCHEMA_API std::unique_ptr<CrossSchema::ResourceAssetT> UnPackResourceAsset(const void *buf,const flatbuffers::resolver_function_t *res = nullptr);
CROSS_SCHEMA_API std::unique_ptr<CrossSchema::ResourceAssetT> UnPackSizePrefixedResourceAsset(const void *buf,const flatbuffers::resolver_function_t *res = nullptr);
}  // namespace CrossSchema

#endif  // FLATBUFFERS_GENERATED_RESOURCEASSET_CROSSSCHEMA_H_
