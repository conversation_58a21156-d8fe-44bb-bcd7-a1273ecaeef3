include "BasicStruct.fbs";
include "ShaderDefines.fbs";

namespace CrossSchema;

table ShaderVariable
{
	name:string(key);
	type:ShaderVariableType;
	offset:uint;
	row_count:uint;
	col_count:uint;
	size:uint;
	array_size:uint;

	index:uint;
	stage_mask:uint;
}

table ShaderVariableEx
{
	name:string(key);
	offset:uint;
	size:uint;

	type:ShaderVariableType;
	row_count:uint = 1;
	col_count:uint = 1;
	members:[ShaderVariableEx];

	array_size:uint = 1;
	array_stride:uint = 0;

	index:uint;
	stage_mask:uint;
}

table ShaderStructType
{
	size:uint;
	members:[ShaderVariableEx];
}

table ShaderResource
{
	name:string(key);
	type:ShaderResourceType;
	array_size:uint;

	space:uint;
	index:uint;
	subpass_index:uint = 0;
	stage_mask:uint;

	return_type:ShaderVariableType = Float;
	depth_texture:bool = false;

	struct_type:ShaderStructType;
}

table ShaderConstantBuffer
{
	name:string(key);
	type:ShaderResourceType;
	size:uint;
	members:[ShaderVariable];

	space:uint;
	index:uint;
	stage_mask:uint;
	array_size:uint = 1;

	struct_type:ShaderStructType;
}

table CombinedSampler
{
	name:string;
	texture_name:string;
	sampler_name:string;
	stage_mask:uint;
}

table ShaderLayout
{
	constant_buffers:[ShaderConstantBuffer];
	resources:[ShaderResource];
	combined_samplers:[CombinedSampler];
	specialization_constants:ShaderConstantBuffer;
}

table ShaderCode
{
	stage:ShaderStageBit;
	entry_point:string;
	
	code_data:[ubyte];

	stage_inputs:[ShaderVariable];
	stage_outputs:[ShaderVariable];

	debug_symbol:bool = false;
}

table GraphicsShaderCode
{
	guid:GUID;
	active_keywords:ulong;
	layout:ShaderLayout;

	vertex_shader:ShaderCode;
	hull_shader:ShaderCode;
	domain_shader:ShaderCode;
	geometry_shader:ShaderCode;
	pixel_shader:ShaderCode;
	task_shader:ShaderCode;
	mesh_shader:ShaderCode;
	mtime:ulong;
}

table PlatformGraphicsShader
{
	version:ShaderVersion;
	keywords:[string];
	variants:[GraphicsShaderCode];
}

table GraphicsShaderAsset
{
	platform_shaders:[PlatformGraphicsShader];
	mtime:ulong;
}

table ComputeShaderCode
{
	guid:GUID;
	layout:ShaderLayout;

	group_size:uint3;
	compute_shader:ShaderCode;
	mtime:ulong;
}

table PlatformComputeShader
{
	version:ShaderVersion;
	code_list:[ComputeShaderCode];
}

table ComputeShaderAsset
{
	platform_shaders:[PlatformComputeShader];
}

table DXILReflectionCode
{
	group_size:uint3;
	layout:ShaderLayout;
}

table RayTracingShaderCode
{
    guid:GUID;
    layout:ShaderLayout;
    
    code:ShaderCode;

    raygen_shader_entry:string;
    closesthit_shader_entries:[string];
    anyhit_shader_entries:[string];
    miss_shader_entries:[string];
    callable_shader_entries:[string];
    intersection_shader_entries:[string];
    mtime:ulong;
}

table PlatformRayTracingShader
{
    version:ShaderVersion;
    shader_code:RayTracingShaderCode;
}

table RayTracingShaderAsset
{
    platform_shaders:[PlatformRayTracingShader];
}
