// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_IMPORTMESHASSETDATA_CROSSSCHEMA_H_
#define FLATBUFFERS_GENERATED_IMPORTMESHASSETDATA_CROSSSCHEMA_H_

#include "flatbuffers/flatbuffers.h"

#include "CrossSchemaForward.h"
#include "BasicStruct_generated.h"
#include "ImportMesh_generated.h"
#include "ImportSkeleton_generated.h"

namespace CrossSchema {

struct PhysicsBoxCollision;
struct PhysicsBoxCollisionBuilder;
struct PhysicsBoxCollisionT;

struct PhysicsSphereCollision;
struct PhysicsSphereCollisionBuilder;
struct PhysicsSphereCollisionT;

struct PhysicsCapsuleCollision;
struct PhysicsCapsuleCollisionBuilder;
struct PhysicsCapsuleCollisionT;

struct PhysicsConvexCollision;
struct PhysicsConvexCollisionBuilder;
struct PhysicsConvexCollisionT;

struct PhysicsMeshCollision;
struct PhysicsMeshCollisionBuilder;
struct PhysicsMeshCollisionT;

struct PhysicsCollision;
struct PhysicsCollisionBuilder;
struct PhysicsCollisionT;

struct UVRangeInfo;
struct UVRangeInfoBuilder;
struct UVRangeInfoT;

struct MeshClusterInfo;
struct MeshClusterInfoBuilder;
struct MeshClusterInfoT;

struct ClusterGroupInfo;
struct ClusterGroupInfoBuilder;
struct ClusterGroupInfoT;

struct IndirectDrawCMD;
struct IndirectDrawCMDBuilder;
struct IndirectDrawCMDT;

struct CustomAttributeInfo;
struct CustomAttributeInfoBuilder;
struct CustomAttributeInfoT;

struct VertexChannelAssetData;
struct VertexChannelAssetDataBuilder;
struct VertexChannelAssetDataT;

struct IndexStreamAssetData;
struct IndexStreamAssetDataBuilder;
struct IndexStreamAssetDataT;

struct MeshBound;
struct MeshBoundBuilder;
struct MeshBoundT;

struct ImportDeltaShapeInfo;
struct ImportDeltaShapeInfoBuilder;
struct ImportDeltaShapeInfoT;

struct ImportBlendShapeChannelInfo;
struct ImportBlendShapeChannelInfoBuilder;
struct ImportBlendShapeChannelInfoT;

struct ImportBlendShapeInfo;
struct ImportBlendShapeInfoBuilder;
struct ImportBlendShapeInfoT;

struct ImportMeshPartAssetInfo;
struct ImportMeshPartAssetInfoBuilder;
struct ImportMeshPartAssetInfoT;

struct ImportMeshAssetData;
struct ImportMeshAssetDataBuilder;
struct ImportMeshAssetDataT;

inline const flatbuffers::TypeTable *PhysicsBoxCollisionTypeTable();

inline const flatbuffers::TypeTable *PhysicsSphereCollisionTypeTable();

inline const flatbuffers::TypeTable *PhysicsCapsuleCollisionTypeTable();

inline const flatbuffers::TypeTable *PhysicsConvexCollisionTypeTable();

inline const flatbuffers::TypeTable *PhysicsMeshCollisionTypeTable();

inline const flatbuffers::TypeTable *PhysicsCollisionTypeTable();

inline const flatbuffers::TypeTable *UVRangeInfoTypeTable();

inline const flatbuffers::TypeTable *MeshClusterInfoTypeTable();

inline const flatbuffers::TypeTable *ClusterGroupInfoTypeTable();

inline const flatbuffers::TypeTable *IndirectDrawCMDTypeTable();

inline const flatbuffers::TypeTable *CustomAttributeInfoTypeTable();

inline const flatbuffers::TypeTable *VertexChannelAssetDataTypeTable();

inline const flatbuffers::TypeTable *IndexStreamAssetDataTypeTable();

inline const flatbuffers::TypeTable *MeshBoundTypeTable();

inline const flatbuffers::TypeTable *ImportDeltaShapeInfoTypeTable();

inline const flatbuffers::TypeTable *ImportBlendShapeChannelInfoTypeTable();

inline const flatbuffers::TypeTable *ImportBlendShapeInfoTypeTable();

inline const flatbuffers::TypeTable *ImportMeshPartAssetInfoTypeTable();

inline const flatbuffers::TypeTable *ImportMeshAssetDataTypeTable();

struct PhysicsBoxCollisionT : public flatbuffers::NativeTable {
  typedef PhysicsBoxCollision TableType;
  std::unique_ptr<CrossSchema::float3> position{};
  std::unique_ptr<CrossSchema::float4> rotate{};
  std::unique_ptr<CrossSchema::float3> halfextents{};
};

struct PhysicsBoxCollision FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef PhysicsBoxCollisionT NativeTableType;
  typedef PhysicsBoxCollisionBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_POSITION = 4,
    VT_ROTATE = 6,
    VT_HALFEXTENTS = 8
  };
CROSS_SCHEMA_API  const CrossSchema::float3 *position() const;
  CROSS_SCHEMA_API  CrossSchema::float3 *mutable_position();
CROSS_SCHEMA_API  const CrossSchema::float4 *rotate() const;
  CROSS_SCHEMA_API  CrossSchema::float4 *mutable_rotate();
CROSS_SCHEMA_API  const CrossSchema::float3 *halfextents() const;
  CROSS_SCHEMA_API  CrossSchema::float3 *mutable_halfextents();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API PhysicsBoxCollisionT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(PhysicsBoxCollisionT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<PhysicsBoxCollision> Pack(flatbuffers::FlatBufferBuilder &_fbb, const PhysicsBoxCollisionT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API PhysicsBoxCollisionBuilder {
  typedef PhysicsBoxCollision Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_position(const CrossSchema::float3 *position);
  void add_rotate(const CrossSchema::float4 *rotate);
  void add_halfextents(const CrossSchema::float3 *halfextents);
  explicit PhysicsBoxCollisionBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<PhysicsBoxCollision> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PhysicsBoxCollision>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<PhysicsBoxCollision> CreatePhysicsBoxCollision(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::float3 *position = 0,
    const CrossSchema::float4 *rotate = 0,
    const CrossSchema::float3 *halfextents = 0);
struct PhysicsBoxCollision::Traits {
  using type = PhysicsBoxCollision;
  static auto constexpr Create = CreatePhysicsBoxCollision;
};

CROSS_SCHEMA_API flatbuffers::Offset<PhysicsBoxCollision> CreatePhysicsBoxCollision(flatbuffers::FlatBufferBuilder &_fbb, const PhysicsBoxCollisionT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct PhysicsSphereCollisionT : public flatbuffers::NativeTable {
  typedef PhysicsSphereCollision TableType;
  std::unique_ptr<CrossSchema::float3> position{};
  float radius = 0.0f;
};

struct PhysicsSphereCollision FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef PhysicsSphereCollisionT NativeTableType;
  typedef PhysicsSphereCollisionBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_POSITION = 4,
    VT_RADIUS = 6
  };
CROSS_SCHEMA_API  const CrossSchema::float3 *position() const;
  CROSS_SCHEMA_API  CrossSchema::float3 *mutable_position();
CROSS_SCHEMA_API  float radius() const;
  CROSS_SCHEMA_API  bool mutate_radius(float _radius);
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API PhysicsSphereCollisionT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(PhysicsSphereCollisionT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<PhysicsSphereCollision> Pack(flatbuffers::FlatBufferBuilder &_fbb, const PhysicsSphereCollisionT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API PhysicsSphereCollisionBuilder {
  typedef PhysicsSphereCollision Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_position(const CrossSchema::float3 *position);
  void add_radius(float radius);
  explicit PhysicsSphereCollisionBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<PhysicsSphereCollision> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PhysicsSphereCollision>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<PhysicsSphereCollision> CreatePhysicsSphereCollision(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::float3 *position = 0,
    float radius = 0.0f);
struct PhysicsSphereCollision::Traits {
  using type = PhysicsSphereCollision;
  static auto constexpr Create = CreatePhysicsSphereCollision;
};

CROSS_SCHEMA_API flatbuffers::Offset<PhysicsSphereCollision> CreatePhysicsSphereCollision(flatbuffers::FlatBufferBuilder &_fbb, const PhysicsSphereCollisionT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct PhysicsCapsuleCollisionT : public flatbuffers::NativeTable {
  typedef PhysicsCapsuleCollision TableType;
  std::unique_ptr<CrossSchema::float3> position{};
  std::unique_ptr<CrossSchema::float4> rotate{};
  float radius = 0.0f;
  float halfHeight = 0.0f;
};

struct PhysicsCapsuleCollision FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef PhysicsCapsuleCollisionT NativeTableType;
  typedef PhysicsCapsuleCollisionBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_POSITION = 4,
    VT_ROTATE = 6,
    VT_RADIUS = 8,
    VT_HALFHEIGHT = 10
  };
CROSS_SCHEMA_API  const CrossSchema::float3 *position() const;
  CROSS_SCHEMA_API  CrossSchema::float3 *mutable_position();
CROSS_SCHEMA_API  const CrossSchema::float4 *rotate() const;
  CROSS_SCHEMA_API  CrossSchema::float4 *mutable_rotate();
CROSS_SCHEMA_API  float radius() const;
  CROSS_SCHEMA_API  bool mutate_radius(float _radius);
CROSS_SCHEMA_API  float halfHeight() const;
  CROSS_SCHEMA_API  bool mutate_halfHeight(float _halfHeight);
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API PhysicsCapsuleCollisionT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(PhysicsCapsuleCollisionT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<PhysicsCapsuleCollision> Pack(flatbuffers::FlatBufferBuilder &_fbb, const PhysicsCapsuleCollisionT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API PhysicsCapsuleCollisionBuilder {
  typedef PhysicsCapsuleCollision Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_position(const CrossSchema::float3 *position);
  void add_rotate(const CrossSchema::float4 *rotate);
  void add_radius(float radius);
  void add_halfHeight(float halfHeight);
  explicit PhysicsCapsuleCollisionBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<PhysicsCapsuleCollision> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PhysicsCapsuleCollision>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<PhysicsCapsuleCollision> CreatePhysicsCapsuleCollision(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::float3 *position = 0,
    const CrossSchema::float4 *rotate = 0,
    float radius = 0.0f,
    float halfHeight = 0.0f);
struct PhysicsCapsuleCollision::Traits {
  using type = PhysicsCapsuleCollision;
  static auto constexpr Create = CreatePhysicsCapsuleCollision;
};

CROSS_SCHEMA_API flatbuffers::Offset<PhysicsCapsuleCollision> CreatePhysicsCapsuleCollision(flatbuffers::FlatBufferBuilder &_fbb, const PhysicsCapsuleCollisionT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct PhysicsConvexCollisionT : public flatbuffers::NativeTable {
  typedef PhysicsConvexCollision TableType;
  std::unique_ptr<CrossSchema::float3> position{};
  std::unique_ptr<CrossSchema::float4> rotate{};
  std::vector<uint8_t> data{};
};

struct PhysicsConvexCollision FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef PhysicsConvexCollisionT NativeTableType;
  typedef PhysicsConvexCollisionBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_POSITION = 4,
    VT_ROTATE = 6,
    VT_DATA = 8
  };
CROSS_SCHEMA_API  const CrossSchema::float3 *position() const;
  CROSS_SCHEMA_API  CrossSchema::float3 *mutable_position();
CROSS_SCHEMA_API  const CrossSchema::float4 *rotate() const;
  CROSS_SCHEMA_API  CrossSchema::float4 *mutable_rotate();
CROSS_SCHEMA_API  const flatbuffers::Vector<uint8_t> *data() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<uint8_t> *mutable_data();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API PhysicsConvexCollisionT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(PhysicsConvexCollisionT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<PhysicsConvexCollision> Pack(flatbuffers::FlatBufferBuilder &_fbb, const PhysicsConvexCollisionT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API PhysicsConvexCollisionBuilder {
  typedef PhysicsConvexCollision Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_position(const CrossSchema::float3 *position);
  void add_rotate(const CrossSchema::float4 *rotate);
  void add_data(flatbuffers::Offset<flatbuffers::Vector<uint8_t>> data);
  explicit PhysicsConvexCollisionBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<PhysicsConvexCollision> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PhysicsConvexCollision>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<PhysicsConvexCollision> CreatePhysicsConvexCollision(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::float3 *position = 0,
    const CrossSchema::float4 *rotate = 0,
    flatbuffers::Offset<flatbuffers::Vector<uint8_t>> data = 0);
struct PhysicsConvexCollision::Traits {
  using type = PhysicsConvexCollision;
  static auto constexpr Create = CreatePhysicsConvexCollision;
};

CROSS_SCHEMA_API flatbuffers::Offset<PhysicsConvexCollision> CreatePhysicsConvexCollisionDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::float3 *position = 0,
    const CrossSchema::float4 *rotate = 0,
    const std::vector<uint8_t> *data = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<PhysicsConvexCollision> CreatePhysicsConvexCollision(flatbuffers::FlatBufferBuilder &_fbb, const PhysicsConvexCollisionT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct PhysicsMeshCollisionT : public flatbuffers::NativeTable {
  typedef PhysicsMeshCollision TableType;
  std::unique_ptr<CrossSchema::float3> position{};
  std::unique_ptr<CrossSchema::float4> rotate{};
  std::vector<uint8_t> data{};
};

struct PhysicsMeshCollision FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef PhysicsMeshCollisionT NativeTableType;
  typedef PhysicsMeshCollisionBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_POSITION = 4,
    VT_ROTATE = 6,
    VT_DATA = 8
  };
CROSS_SCHEMA_API  const CrossSchema::float3 *position() const;
  CROSS_SCHEMA_API  CrossSchema::float3 *mutable_position();
CROSS_SCHEMA_API  const CrossSchema::float4 *rotate() const;
  CROSS_SCHEMA_API  CrossSchema::float4 *mutable_rotate();
CROSS_SCHEMA_API  const flatbuffers::Vector<uint8_t> *data() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<uint8_t> *mutable_data();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API PhysicsMeshCollisionT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(PhysicsMeshCollisionT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<PhysicsMeshCollision> Pack(flatbuffers::FlatBufferBuilder &_fbb, const PhysicsMeshCollisionT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API PhysicsMeshCollisionBuilder {
  typedef PhysicsMeshCollision Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_position(const CrossSchema::float3 *position);
  void add_rotate(const CrossSchema::float4 *rotate);
  void add_data(flatbuffers::Offset<flatbuffers::Vector<uint8_t>> data);
  explicit PhysicsMeshCollisionBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<PhysicsMeshCollision> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PhysicsMeshCollision>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<PhysicsMeshCollision> CreatePhysicsMeshCollision(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::float3 *position = 0,
    const CrossSchema::float4 *rotate = 0,
    flatbuffers::Offset<flatbuffers::Vector<uint8_t>> data = 0);
struct PhysicsMeshCollision::Traits {
  using type = PhysicsMeshCollision;
  static auto constexpr Create = CreatePhysicsMeshCollision;
};

CROSS_SCHEMA_API flatbuffers::Offset<PhysicsMeshCollision> CreatePhysicsMeshCollisionDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::float3 *position = 0,
    const CrossSchema::float4 *rotate = 0,
    const std::vector<uint8_t> *data = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<PhysicsMeshCollision> CreatePhysicsMeshCollision(flatbuffers::FlatBufferBuilder &_fbb, const PhysicsMeshCollisionT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct PhysicsCollisionT : public flatbuffers::NativeTable {
  typedef PhysicsCollision TableType;
  std::vector<std::unique_ptr<CrossSchema::PhysicsBoxCollisionT>> boxcollision{};
  std::vector<std::unique_ptr<CrossSchema::PhysicsSphereCollisionT>> spherecollision{};
  std::vector<std::unique_ptr<CrossSchema::PhysicsCapsuleCollisionT>> capsulecollision{};
  std::vector<std::unique_ptr<CrossSchema::PhysicsConvexCollisionT>> convexcollision{};
  std::vector<std::unique_ptr<CrossSchema::PhysicsMeshCollisionT>> meshcollision{};
};

struct PhysicsCollision FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef PhysicsCollisionT NativeTableType;
  typedef PhysicsCollisionBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_BOXCOLLISION = 4,
    VT_SPHERECOLLISION = 6,
    VT_CAPSULECOLLISION = 8,
    VT_CONVEXCOLLISION = 10,
    VT_MESHCOLLISION = 12
  };
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsBoxCollision>> *boxcollision() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsBoxCollision>> *mutable_boxcollision();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsSphereCollision>> *spherecollision() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsSphereCollision>> *mutable_spherecollision();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsCapsuleCollision>> *capsulecollision() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsCapsuleCollision>> *mutable_capsulecollision();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsConvexCollision>> *convexcollision() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsConvexCollision>> *mutable_convexcollision();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsMeshCollision>> *meshcollision() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsMeshCollision>> *mutable_meshcollision();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API PhysicsCollisionT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(PhysicsCollisionT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<PhysicsCollision> Pack(flatbuffers::FlatBufferBuilder &_fbb, const PhysicsCollisionT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API PhysicsCollisionBuilder {
  typedef PhysicsCollision Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_boxcollision(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsBoxCollision>>> boxcollision);
  void add_spherecollision(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsSphereCollision>>> spherecollision);
  void add_capsulecollision(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsCapsuleCollision>>> capsulecollision);
  void add_convexcollision(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsConvexCollision>>> convexcollision);
  void add_meshcollision(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsMeshCollision>>> meshcollision);
  explicit PhysicsCollisionBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<PhysicsCollision> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PhysicsCollision>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<PhysicsCollision> CreatePhysicsCollision(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsBoxCollision>>> boxcollision = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsSphereCollision>>> spherecollision = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsCapsuleCollision>>> capsulecollision = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsConvexCollision>>> convexcollision = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PhysicsMeshCollision>>> meshcollision = 0);
struct PhysicsCollision::Traits {
  using type = PhysicsCollision;
  static auto constexpr Create = CreatePhysicsCollision;
};

CROSS_SCHEMA_API flatbuffers::Offset<PhysicsCollision> CreatePhysicsCollisionDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<CrossSchema::PhysicsBoxCollision>> *boxcollision = nullptr,
    const std::vector<flatbuffers::Offset<CrossSchema::PhysicsSphereCollision>> *spherecollision = nullptr,
    const std::vector<flatbuffers::Offset<CrossSchema::PhysicsCapsuleCollision>> *capsulecollision = nullptr,
    const std::vector<flatbuffers::Offset<CrossSchema::PhysicsConvexCollision>> *convexcollision = nullptr,
    const std::vector<flatbuffers::Offset<CrossSchema::PhysicsMeshCollision>> *meshcollision = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<PhysicsCollision> CreatePhysicsCollision(flatbuffers::FlatBufferBuilder &_fbb, const PhysicsCollisionT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct UVRangeInfoT : public flatbuffers::NativeTable {
  typedef UVRangeInfo TableType;
  std::vector<int32_t> mMin{};
  std::vector<int32_t> mGapStart{};
  std::vector<int32_t> mGapLength{};
  uint32_t mPrecision = 0;
  uint32_t mBitsU = 0;
  uint32_t mBitsV = 0;
};

struct UVRangeInfo FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef UVRangeInfoT NativeTableType;
  typedef UVRangeInfoBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MMIN = 4,
    VT_MGAPSTART = 6,
    VT_MGAPLENGTH = 8,
    VT_MPRECISION = 10,
    VT_MBITSU = 12,
    VT_MBITSV = 14
  };
CROSS_SCHEMA_API  const flatbuffers::Vector<int32_t> *mMin() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<int32_t> *mutable_mMin();
CROSS_SCHEMA_API  const flatbuffers::Vector<int32_t> *mGapStart() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<int32_t> *mutable_mGapStart();
CROSS_SCHEMA_API  const flatbuffers::Vector<int32_t> *mGapLength() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<int32_t> *mutable_mGapLength();
CROSS_SCHEMA_API  uint32_t mPrecision() const;
  CROSS_SCHEMA_API  bool mutate_mPrecision(uint32_t _mPrecision);
CROSS_SCHEMA_API  uint32_t mBitsU() const;
  CROSS_SCHEMA_API  bool mutate_mBitsU(uint32_t _mBitsU);
CROSS_SCHEMA_API  uint32_t mBitsV() const;
  CROSS_SCHEMA_API  bool mutate_mBitsV(uint32_t _mBitsV);
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API UVRangeInfoT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(UVRangeInfoT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<UVRangeInfo> Pack(flatbuffers::FlatBufferBuilder &_fbb, const UVRangeInfoT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API UVRangeInfoBuilder {
  typedef UVRangeInfo Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_mMin(flatbuffers::Offset<flatbuffers::Vector<int32_t>> mMin);
  void add_mGapStart(flatbuffers::Offset<flatbuffers::Vector<int32_t>> mGapStart);
  void add_mGapLength(flatbuffers::Offset<flatbuffers::Vector<int32_t>> mGapLength);
  void add_mPrecision(uint32_t mPrecision);
  void add_mBitsU(uint32_t mBitsU);
  void add_mBitsV(uint32_t mBitsV);
  explicit UVRangeInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<UVRangeInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<UVRangeInfo>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<UVRangeInfo> CreateUVRangeInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<int32_t>> mMin = 0,
    flatbuffers::Offset<flatbuffers::Vector<int32_t>> mGapStart = 0,
    flatbuffers::Offset<flatbuffers::Vector<int32_t>> mGapLength = 0,
    uint32_t mPrecision = 0,
    uint32_t mBitsU = 0,
    uint32_t mBitsV = 0);
struct UVRangeInfo::Traits {
  using type = UVRangeInfo;
  static auto constexpr Create = CreateUVRangeInfo;
};

CROSS_SCHEMA_API flatbuffers::Offset<UVRangeInfo> CreateUVRangeInfoDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<int32_t> *mMin = nullptr,
    const std::vector<int32_t> *mGapStart = nullptr,
    const std::vector<int32_t> *mGapLength = nullptr,
    uint32_t mPrecision = 0,
    uint32_t mBitsU = 0,
    uint32_t mBitsV = 0);
CROSS_SCHEMA_API flatbuffers::Offset<UVRangeInfo> CreateUVRangeInfo(flatbuffers::FlatBufferBuilder &_fbb, const UVRangeInfoT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct MeshClusterInfoT : public flatbuffers::NativeTable {
  typedef MeshClusterInfo TableType;
  uint32_t mIndexStart = 0;
  uint32_t mIndexCount = 0;
  uint32_t mVertexStart = 0;
  uint32_t mVertexCount = 0;
  std::unique_ptr<CrossSchema::float4> mBoundingSphere{};
  std::unique_ptr<CrossSchema::float4> mNormalCone{};
  float mApexOffset = 0.0f;
  std::vector<int32_t> mPosStart{};
  uint32_t mPosPrecision = 0;
  std::vector<uint32_t> mPosBits{};
  std::vector<std::unique_ptr<CrossSchema::UVRangeInfoT>> mUVInfo{};
  uint32_t mUVChannelCount = 0;
  uint32_t mBitsPerVertex = 0;
  uint32_t mIndexBit = 0;
  uint32_t mIndexOffset = 0;
  uint32_t mDataOffset = 0;
  std::vector<uint8_t> mEncodeData{};
};

struct MeshClusterInfo FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef MeshClusterInfoT NativeTableType;
  typedef MeshClusterInfoBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MINDEXSTART = 4,
    VT_MINDEXCOUNT = 6,
    VT_MVERTEXSTART = 8,
    VT_MVERTEXCOUNT = 10,
    VT_MBOUNDINGSPHERE = 12,
    VT_MNORMALCONE = 14,
    VT_MAPEXOFFSET = 16,
    VT_MPOSSTART = 18,
    VT_MPOSPRECISION = 20,
    VT_MPOSBITS = 22,
    VT_MUVINFO = 24,
    VT_MUVCHANNELCOUNT = 26,
    VT_MBITSPERVERTEX = 28,
    VT_MINDEXBIT = 30,
    VT_MINDEXOFFSET = 32,
    VT_MDATAOFFSET = 34,
    VT_MENCODEDATA = 36
  };
CROSS_SCHEMA_API  uint32_t mIndexStart() const;
  CROSS_SCHEMA_API  bool mutate_mIndexStart(uint32_t _mIndexStart);
CROSS_SCHEMA_API  uint32_t mIndexCount() const;
  CROSS_SCHEMA_API  bool mutate_mIndexCount(uint32_t _mIndexCount);
CROSS_SCHEMA_API  uint32_t mVertexStart() const;
  CROSS_SCHEMA_API  bool mutate_mVertexStart(uint32_t _mVertexStart);
CROSS_SCHEMA_API  uint32_t mVertexCount() const;
  CROSS_SCHEMA_API  bool mutate_mVertexCount(uint32_t _mVertexCount);
CROSS_SCHEMA_API  const CrossSchema::float4 *mBoundingSphere() const;
  CROSS_SCHEMA_API  CrossSchema::float4 *mutable_mBoundingSphere();
CROSS_SCHEMA_API  const CrossSchema::float4 *mNormalCone() const;
  CROSS_SCHEMA_API  CrossSchema::float4 *mutable_mNormalCone();
CROSS_SCHEMA_API  float mApexOffset() const;
  CROSS_SCHEMA_API  bool mutate_mApexOffset(float _mApexOffset);
CROSS_SCHEMA_API  const flatbuffers::Vector<int32_t> *mPosStart() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<int32_t> *mutable_mPosStart();
CROSS_SCHEMA_API  uint32_t mPosPrecision() const;
  CROSS_SCHEMA_API  bool mutate_mPosPrecision(uint32_t _mPosPrecision);
CROSS_SCHEMA_API  const flatbuffers::Vector<uint32_t> *mPosBits() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<uint32_t> *mutable_mPosBits();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::UVRangeInfo>> *mUVInfo() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::UVRangeInfo>> *mutable_mUVInfo();
CROSS_SCHEMA_API  uint32_t mUVChannelCount() const;
  CROSS_SCHEMA_API  bool mutate_mUVChannelCount(uint32_t _mUVChannelCount);
CROSS_SCHEMA_API  uint32_t mBitsPerVertex() const;
  CROSS_SCHEMA_API  bool mutate_mBitsPerVertex(uint32_t _mBitsPerVertex);
CROSS_SCHEMA_API  uint32_t mIndexBit() const;
  CROSS_SCHEMA_API  bool mutate_mIndexBit(uint32_t _mIndexBit);
CROSS_SCHEMA_API  uint32_t mIndexOffset() const;
  CROSS_SCHEMA_API  bool mutate_mIndexOffset(uint32_t _mIndexOffset);
CROSS_SCHEMA_API  uint32_t mDataOffset() const;
  CROSS_SCHEMA_API  bool mutate_mDataOffset(uint32_t _mDataOffset);
CROSS_SCHEMA_API  const flatbuffers::Vector<uint8_t> *mEncodeData() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<uint8_t> *mutable_mEncodeData();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API MeshClusterInfoT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(MeshClusterInfoT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<MeshClusterInfo> Pack(flatbuffers::FlatBufferBuilder &_fbb, const MeshClusterInfoT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API MeshClusterInfoBuilder {
  typedef MeshClusterInfo Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_mIndexStart(uint32_t mIndexStart);
  void add_mIndexCount(uint32_t mIndexCount);
  void add_mVertexStart(uint32_t mVertexStart);
  void add_mVertexCount(uint32_t mVertexCount);
  void add_mBoundingSphere(const CrossSchema::float4 *mBoundingSphere);
  void add_mNormalCone(const CrossSchema::float4 *mNormalCone);
  void add_mApexOffset(float mApexOffset);
  void add_mPosStart(flatbuffers::Offset<flatbuffers::Vector<int32_t>> mPosStart);
  void add_mPosPrecision(uint32_t mPosPrecision);
  void add_mPosBits(flatbuffers::Offset<flatbuffers::Vector<uint32_t>> mPosBits);
  void add_mUVInfo(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::UVRangeInfo>>> mUVInfo);
  void add_mUVChannelCount(uint32_t mUVChannelCount);
  void add_mBitsPerVertex(uint32_t mBitsPerVertex);
  void add_mIndexBit(uint32_t mIndexBit);
  void add_mIndexOffset(uint32_t mIndexOffset);
  void add_mDataOffset(uint32_t mDataOffset);
  void add_mEncodeData(flatbuffers::Offset<flatbuffers::Vector<uint8_t>> mEncodeData);
  explicit MeshClusterInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<MeshClusterInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<MeshClusterInfo>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<MeshClusterInfo> CreateMeshClusterInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t mIndexStart = 0,
    uint32_t mIndexCount = 0,
    uint32_t mVertexStart = 0,
    uint32_t mVertexCount = 0,
    const CrossSchema::float4 *mBoundingSphere = 0,
    const CrossSchema::float4 *mNormalCone = 0,
    float mApexOffset = 0.0f,
    flatbuffers::Offset<flatbuffers::Vector<int32_t>> mPosStart = 0,
    uint32_t mPosPrecision = 0,
    flatbuffers::Offset<flatbuffers::Vector<uint32_t>> mPosBits = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::UVRangeInfo>>> mUVInfo = 0,
    uint32_t mUVChannelCount = 0,
    uint32_t mBitsPerVertex = 0,
    uint32_t mIndexBit = 0,
    uint32_t mIndexOffset = 0,
    uint32_t mDataOffset = 0,
    flatbuffers::Offset<flatbuffers::Vector<uint8_t>> mEncodeData = 0);
struct MeshClusterInfo::Traits {
  using type = MeshClusterInfo;
  static auto constexpr Create = CreateMeshClusterInfo;
};

CROSS_SCHEMA_API flatbuffers::Offset<MeshClusterInfo> CreateMeshClusterInfoDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t mIndexStart = 0,
    uint32_t mIndexCount = 0,
    uint32_t mVertexStart = 0,
    uint32_t mVertexCount = 0,
    const CrossSchema::float4 *mBoundingSphere = 0,
    const CrossSchema::float4 *mNormalCone = 0,
    float mApexOffset = 0.0f,
    const std::vector<int32_t> *mPosStart = nullptr,
    uint32_t mPosPrecision = 0,
    const std::vector<uint32_t> *mPosBits = nullptr,
    const std::vector<flatbuffers::Offset<CrossSchema::UVRangeInfo>> *mUVInfo = nullptr,
    uint32_t mUVChannelCount = 0,
    uint32_t mBitsPerVertex = 0,
    uint32_t mIndexBit = 0,
    uint32_t mIndexOffset = 0,
    uint32_t mDataOffset = 0,
    const std::vector<uint8_t> *mEncodeData = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<MeshClusterInfo> CreateMeshClusterInfo(flatbuffers::FlatBufferBuilder &_fbb, const MeshClusterInfoT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ClusterGroupInfoT : public flatbuffers::NativeTable {
  typedef ClusterGroupInfo TableType;
  std::unique_ptr<CrossSchema::float4> boundingSphere{};
  uint32_t clusterStartIndex = 0;
  uint32_t clusterCount = 0;
  uint32_t mipLevel = 0;
  uint32_t parentIndex = 0;
  uint32_t childStartIndex = 0;
  uint32_t childCount = 0;
};

struct ClusterGroupInfo FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ClusterGroupInfoT NativeTableType;
  typedef ClusterGroupInfoBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_BOUNDINGSPHERE = 4,
    VT_CLUSTERSTARTINDEX = 6,
    VT_CLUSTERCOUNT = 8,
    VT_MIPLEVEL = 10,
    VT_PARENTINDEX = 12,
    VT_CHILDSTARTINDEX = 14,
    VT_CHILDCOUNT = 16
  };
CROSS_SCHEMA_API  const CrossSchema::float4 *boundingSphere() const;
  CROSS_SCHEMA_API  CrossSchema::float4 *mutable_boundingSphere();
CROSS_SCHEMA_API  uint32_t clusterStartIndex() const;
  CROSS_SCHEMA_API  bool mutate_clusterStartIndex(uint32_t _clusterStartIndex);
CROSS_SCHEMA_API  uint32_t clusterCount() const;
  CROSS_SCHEMA_API  bool mutate_clusterCount(uint32_t _clusterCount);
CROSS_SCHEMA_API  uint32_t mipLevel() const;
  CROSS_SCHEMA_API  bool mutate_mipLevel(uint32_t _mipLevel);
CROSS_SCHEMA_API  uint32_t parentIndex() const;
  CROSS_SCHEMA_API  bool mutate_parentIndex(uint32_t _parentIndex);
CROSS_SCHEMA_API  uint32_t childStartIndex() const;
  CROSS_SCHEMA_API  bool mutate_childStartIndex(uint32_t _childStartIndex);
CROSS_SCHEMA_API  uint32_t childCount() const;
  CROSS_SCHEMA_API  bool mutate_childCount(uint32_t _childCount);
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ClusterGroupInfoT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ClusterGroupInfoT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ClusterGroupInfo> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ClusterGroupInfoT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ClusterGroupInfoBuilder {
  typedef ClusterGroupInfo Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_boundingSphere(const CrossSchema::float4 *boundingSphere);
  void add_clusterStartIndex(uint32_t clusterStartIndex);
  void add_clusterCount(uint32_t clusterCount);
  void add_mipLevel(uint32_t mipLevel);
  void add_parentIndex(uint32_t parentIndex);
  void add_childStartIndex(uint32_t childStartIndex);
  void add_childCount(uint32_t childCount);
  explicit ClusterGroupInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ClusterGroupInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ClusterGroupInfo>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ClusterGroupInfo> CreateClusterGroupInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::float4 *boundingSphere = 0,
    uint32_t clusterStartIndex = 0,
    uint32_t clusterCount = 0,
    uint32_t mipLevel = 0,
    uint32_t parentIndex = 0,
    uint32_t childStartIndex = 0,
    uint32_t childCount = 0);
struct ClusterGroupInfo::Traits {
  using type = ClusterGroupInfo;
  static auto constexpr Create = CreateClusterGroupInfo;
};

CROSS_SCHEMA_API flatbuffers::Offset<ClusterGroupInfo> CreateClusterGroupInfo(flatbuffers::FlatBufferBuilder &_fbb, const ClusterGroupInfoT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct IndirectDrawCMDT : public flatbuffers::NativeTable {
  typedef IndirectDrawCMD TableType;
  uint32_t indexCount = 0;
  uint32_t instanceCount = 0;
  uint32_t firstIndex = 0;
  int32_t vertexOffset = 0;
  uint32_t firstInstance = 0;
  float apexOffset = 0.0f;
  std::unique_ptr<CrossSchema::float2> padding{};
  std::unique_ptr<CrossSchema::float4> boundingSphere{};
  std::unique_ptr<CrossSchema::float4> normalCone{};
};

struct IndirectDrawCMD FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef IndirectDrawCMDT NativeTableType;
  typedef IndirectDrawCMDBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_INDEXCOUNT = 4,
    VT_INSTANCECOUNT = 6,
    VT_FIRSTINDEX = 8,
    VT_VERTEXOFFSET = 10,
    VT_FIRSTINSTANCE = 12,
    VT_APEXOFFSET = 14,
    VT_PADDING = 16,
    VT_BOUNDINGSPHERE = 18,
    VT_NORMALCONE = 20
  };
CROSS_SCHEMA_API  uint32_t indexCount() const;
  CROSS_SCHEMA_API  bool mutate_indexCount(uint32_t _indexCount);
CROSS_SCHEMA_API  uint32_t instanceCount() const;
  CROSS_SCHEMA_API  bool mutate_instanceCount(uint32_t _instanceCount);
CROSS_SCHEMA_API  uint32_t firstIndex() const;
  CROSS_SCHEMA_API  bool mutate_firstIndex(uint32_t _firstIndex);
CROSS_SCHEMA_API  int32_t vertexOffset() const;
  CROSS_SCHEMA_API  bool mutate_vertexOffset(int32_t _vertexOffset);
CROSS_SCHEMA_API  uint32_t firstInstance() const;
  CROSS_SCHEMA_API  bool mutate_firstInstance(uint32_t _firstInstance);
CROSS_SCHEMA_API  float apexOffset() const;
  CROSS_SCHEMA_API  bool mutate_apexOffset(float _apexOffset);
CROSS_SCHEMA_API  const CrossSchema::float2 *padding() const;
  CROSS_SCHEMA_API  CrossSchema::float2 *mutable_padding();
CROSS_SCHEMA_API  const CrossSchema::float4 *boundingSphere() const;
  CROSS_SCHEMA_API  CrossSchema::float4 *mutable_boundingSphere();
CROSS_SCHEMA_API  const CrossSchema::float4 *normalCone() const;
  CROSS_SCHEMA_API  CrossSchema::float4 *mutable_normalCone();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API IndirectDrawCMDT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(IndirectDrawCMDT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<IndirectDrawCMD> Pack(flatbuffers::FlatBufferBuilder &_fbb, const IndirectDrawCMDT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API IndirectDrawCMDBuilder {
  typedef IndirectDrawCMD Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_indexCount(uint32_t indexCount);
  void add_instanceCount(uint32_t instanceCount);
  void add_firstIndex(uint32_t firstIndex);
  void add_vertexOffset(int32_t vertexOffset);
  void add_firstInstance(uint32_t firstInstance);
  void add_apexOffset(float apexOffset);
  void add_padding(const CrossSchema::float2 *padding);
  void add_boundingSphere(const CrossSchema::float4 *boundingSphere);
  void add_normalCone(const CrossSchema::float4 *normalCone);
  explicit IndirectDrawCMDBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<IndirectDrawCMD> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<IndirectDrawCMD>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<IndirectDrawCMD> CreateIndirectDrawCMD(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t indexCount = 0,
    uint32_t instanceCount = 0,
    uint32_t firstIndex = 0,
    int32_t vertexOffset = 0,
    uint32_t firstInstance = 0,
    float apexOffset = 0.0f,
    const CrossSchema::float2 *padding = 0,
    const CrossSchema::float4 *boundingSphere = 0,
    const CrossSchema::float4 *normalCone = 0);
struct IndirectDrawCMD::Traits {
  using type = IndirectDrawCMD;
  static auto constexpr Create = CreateIndirectDrawCMD;
};

CROSS_SCHEMA_API flatbuffers::Offset<IndirectDrawCMD> CreateIndirectDrawCMD(flatbuffers::FlatBufferBuilder &_fbb, const IndirectDrawCMDT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct CustomAttributeInfoT : public flatbuffers::NativeTable {
  typedef CustomAttributeInfo TableType;
  uint32_t fkeynamehash = 0;
  uint32_t fdataflag = 0;
  uint32_t fdatasizeinbyte = 0;
  uint32_t fdataoffset = 0;
};

struct CustomAttributeInfo FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef CustomAttributeInfoT NativeTableType;
  typedef CustomAttributeInfoBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FKEYNAMEHASH = 4,
    VT_FDATAFLAG = 6,
    VT_FDATASIZEINBYTE = 8,
    VT_FDATAOFFSET = 10
  };
CROSS_SCHEMA_API  uint32_t fkeynamehash() const;
  CROSS_SCHEMA_API  bool mutate_fkeynamehash(uint32_t _fkeynamehash);
CROSS_SCHEMA_API  uint32_t fdataflag() const;
  CROSS_SCHEMA_API  bool mutate_fdataflag(uint32_t _fdataflag);
CROSS_SCHEMA_API  uint32_t fdatasizeinbyte() const;
  CROSS_SCHEMA_API  bool mutate_fdatasizeinbyte(uint32_t _fdatasizeinbyte);
CROSS_SCHEMA_API  uint32_t fdataoffset() const;
  CROSS_SCHEMA_API  bool mutate_fdataoffset(uint32_t _fdataoffset);
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API CustomAttributeInfoT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(CustomAttributeInfoT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<CustomAttributeInfo> Pack(flatbuffers::FlatBufferBuilder &_fbb, const CustomAttributeInfoT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API CustomAttributeInfoBuilder {
  typedef CustomAttributeInfo Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_fkeynamehash(uint32_t fkeynamehash);
  void add_fdataflag(uint32_t fdataflag);
  void add_fdatasizeinbyte(uint32_t fdatasizeinbyte);
  void add_fdataoffset(uint32_t fdataoffset);
  explicit CustomAttributeInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<CustomAttributeInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<CustomAttributeInfo>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<CustomAttributeInfo> CreateCustomAttributeInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t fkeynamehash = 0,
    uint32_t fdataflag = 0,
    uint32_t fdatasizeinbyte = 0,
    uint32_t fdataoffset = 0);
struct CustomAttributeInfo::Traits {
  using type = CustomAttributeInfo;
  static auto constexpr Create = CreateCustomAttributeInfo;
};

CROSS_SCHEMA_API flatbuffers::Offset<CustomAttributeInfo> CreateCustomAttributeInfo(flatbuffers::FlatBufferBuilder &_fbb, const CustomAttributeInfoT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct VertexChannelAssetDataT : public flatbuffers::NativeTable {
  typedef VertexChannelAssetData TableType;
  uint16_t fstride = 0;
  uint16_t ffrequency = 1;
  int16_t fstream = -1;
  int16_t fstreamoffset = -1;
  uint16_t fmiscflag = 0;
  uint16_t freserve0 = 0;
  uint16_t freserve1 = 0;
  uint32_t fvertexchannel = 0;
  uint32_t fdataformat = 0;
  std::vector<uint8_t> fdata{};
};

struct VertexChannelAssetData FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef VertexChannelAssetDataT NativeTableType;
  typedef VertexChannelAssetDataBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FSTRIDE = 4,
    VT_FFREQUENCY = 6,
    VT_FSTREAM = 8,
    VT_FSTREAMOFFSET = 10,
    VT_FMISCFLAG = 12,
    VT_FRESERVE0 = 14,
    VT_FRESERVE1 = 16,
    VT_FVERTEXCHANNEL = 18,
    VT_FDATAFORMAT = 20,
    VT_FDATA = 22
  };
CROSS_SCHEMA_API  uint16_t fstride() const;
  CROSS_SCHEMA_API  bool mutate_fstride(uint16_t _fstride);
CROSS_SCHEMA_API  uint16_t ffrequency() const;
  CROSS_SCHEMA_API  bool mutate_ffrequency(uint16_t _ffrequency);
CROSS_SCHEMA_API  int16_t fstream() const;
  CROSS_SCHEMA_API  bool mutate_fstream(int16_t _fstream);
CROSS_SCHEMA_API  int16_t fstreamoffset() const;
  CROSS_SCHEMA_API  bool mutate_fstreamoffset(int16_t _fstreamoffset);
CROSS_SCHEMA_API  uint16_t fmiscflag() const;
  CROSS_SCHEMA_API  bool mutate_fmiscflag(uint16_t _fmiscflag);
CROSS_SCHEMA_API  uint16_t freserve0() const;
  CROSS_SCHEMA_API  bool mutate_freserve0(uint16_t _freserve0);
CROSS_SCHEMA_API  uint16_t freserve1() const;
  CROSS_SCHEMA_API  bool mutate_freserve1(uint16_t _freserve1);
CROSS_SCHEMA_API  uint32_t fvertexchannel() const;
  CROSS_SCHEMA_API  bool mutate_fvertexchannel(uint32_t _fvertexchannel);
CROSS_SCHEMA_API  uint32_t fdataformat() const;
  CROSS_SCHEMA_API  bool mutate_fdataformat(uint32_t _fdataformat);
CROSS_SCHEMA_API  const flatbuffers::Vector<uint8_t> *fdata() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<uint8_t> *mutable_fdata();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API VertexChannelAssetDataT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(VertexChannelAssetDataT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<VertexChannelAssetData> Pack(flatbuffers::FlatBufferBuilder &_fbb, const VertexChannelAssetDataT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API VertexChannelAssetDataBuilder {
  typedef VertexChannelAssetData Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_fstride(uint16_t fstride);
  void add_ffrequency(uint16_t ffrequency);
  void add_fstream(int16_t fstream);
  void add_fstreamoffset(int16_t fstreamoffset);
  void add_fmiscflag(uint16_t fmiscflag);
  void add_freserve0(uint16_t freserve0);
  void add_freserve1(uint16_t freserve1);
  void add_fvertexchannel(uint32_t fvertexchannel);
  void add_fdataformat(uint32_t fdataformat);
  void add_fdata(flatbuffers::Offset<flatbuffers::Vector<uint8_t>> fdata);
  explicit VertexChannelAssetDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<VertexChannelAssetData> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<VertexChannelAssetData>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<VertexChannelAssetData> CreateVertexChannelAssetData(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t fstride = 0,
    uint16_t ffrequency = 1,
    int16_t fstream = -1,
    int16_t fstreamoffset = -1,
    uint16_t fmiscflag = 0,
    uint16_t freserve0 = 0,
    uint16_t freserve1 = 0,
    uint32_t fvertexchannel = 0,
    uint32_t fdataformat = 0,
    flatbuffers::Offset<flatbuffers::Vector<uint8_t>> fdata = 0);
struct VertexChannelAssetData::Traits {
  using type = VertexChannelAssetData;
  static auto constexpr Create = CreateVertexChannelAssetData;
};

CROSS_SCHEMA_API flatbuffers::Offset<VertexChannelAssetData> CreateVertexChannelAssetDataDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t fstride = 0,
    uint16_t ffrequency = 1,
    int16_t fstream = -1,
    int16_t fstreamoffset = -1,
    uint16_t fmiscflag = 0,
    uint16_t freserve0 = 0,
    uint16_t freserve1 = 0,
    uint32_t fvertexchannel = 0,
    uint32_t fdataformat = 0,
    const std::vector<uint8_t> *fdata = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<VertexChannelAssetData> CreateVertexChannelAssetData(flatbuffers::FlatBufferBuilder &_fbb, const VertexChannelAssetDataT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct IndexStreamAssetDataT : public flatbuffers::NativeTable {
  typedef IndexStreamAssetData TableType;
  uint32_t fcount = 0;
  std::vector<uint8_t> fdata{};
  bool fis16bitindex = false;
};

struct IndexStreamAssetData FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef IndexStreamAssetDataT NativeTableType;
  typedef IndexStreamAssetDataBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FCOUNT = 4,
    VT_FDATA = 6,
    VT_FIS16BITINDEX = 8
  };
CROSS_SCHEMA_API  uint32_t fcount() const;
  CROSS_SCHEMA_API  bool mutate_fcount(uint32_t _fcount);
CROSS_SCHEMA_API  const flatbuffers::Vector<uint8_t> *fdata() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<uint8_t> *mutable_fdata();
CROSS_SCHEMA_API  bool fis16bitindex() const;
  CROSS_SCHEMA_API  bool mutate_fis16bitindex(bool _fis16bitindex);
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API IndexStreamAssetDataT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(IndexStreamAssetDataT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<IndexStreamAssetData> Pack(flatbuffers::FlatBufferBuilder &_fbb, const IndexStreamAssetDataT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API IndexStreamAssetDataBuilder {
  typedef IndexStreamAssetData Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_fcount(uint32_t fcount);
  void add_fdata(flatbuffers::Offset<flatbuffers::Vector<uint8_t>> fdata);
  void add_fis16bitindex(bool fis16bitindex);
  explicit IndexStreamAssetDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<IndexStreamAssetData> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<IndexStreamAssetData>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<IndexStreamAssetData> CreateIndexStreamAssetData(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t fcount = 0,
    flatbuffers::Offset<flatbuffers::Vector<uint8_t>> fdata = 0,
    bool fis16bitindex = false);
struct IndexStreamAssetData::Traits {
  using type = IndexStreamAssetData;
  static auto constexpr Create = CreateIndexStreamAssetData;
};

CROSS_SCHEMA_API flatbuffers::Offset<IndexStreamAssetData> CreateIndexStreamAssetDataDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t fcount = 0,
    const std::vector<uint8_t> *fdata = nullptr,
    bool fis16bitindex = false);
CROSS_SCHEMA_API flatbuffers::Offset<IndexStreamAssetData> CreateIndexStreamAssetData(flatbuffers::FlatBufferBuilder &_fbb, const IndexStreamAssetDataT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct MeshBoundT : public flatbuffers::NativeTable {
  typedef MeshBound TableType;
  std::unique_ptr<CrossSchema::float3> fmin{};
  std::unique_ptr<CrossSchema::float3> fmax{};
};

struct MeshBound FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef MeshBoundT NativeTableType;
  typedef MeshBoundBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FMIN = 4,
    VT_FMAX = 6
  };
CROSS_SCHEMA_API  const CrossSchema::float3 *fmin() const;
  CROSS_SCHEMA_API  CrossSchema::float3 *mutable_fmin();
CROSS_SCHEMA_API  const CrossSchema::float3 *fmax() const;
  CROSS_SCHEMA_API  CrossSchema::float3 *mutable_fmax();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API MeshBoundT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(MeshBoundT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<MeshBound> Pack(flatbuffers::FlatBufferBuilder &_fbb, const MeshBoundT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API MeshBoundBuilder {
  typedef MeshBound Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_fmin(const CrossSchema::float3 *fmin);
  void add_fmax(const CrossSchema::float3 *fmax);
  explicit MeshBoundBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<MeshBound> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<MeshBound>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<MeshBound> CreateMeshBound(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::float3 *fmin = 0,
    const CrossSchema::float3 *fmax = 0);
struct MeshBound::Traits {
  using type = MeshBound;
  static auto constexpr Create = CreateMeshBound;
};

CROSS_SCHEMA_API flatbuffers::Offset<MeshBound> CreateMeshBound(flatbuffers::FlatBufferBuilder &_fbb, const MeshBoundT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ImportDeltaShapeInfoT : public flatbuffers::NativeTable {
  typedef ImportDeltaShapeInfo TableType;
  uint32_t fvertexstart = 0;
  uint32_t fvertexcount = 0;
};

struct ImportDeltaShapeInfo FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ImportDeltaShapeInfoT NativeTableType;
  typedef ImportDeltaShapeInfoBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FVERTEXSTART = 4,
    VT_FVERTEXCOUNT = 6
  };
CROSS_SCHEMA_API  uint32_t fvertexstart() const;
  CROSS_SCHEMA_API  bool mutate_fvertexstart(uint32_t _fvertexstart);
CROSS_SCHEMA_API  uint32_t fvertexcount() const;
  CROSS_SCHEMA_API  bool mutate_fvertexcount(uint32_t _fvertexcount);
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ImportDeltaShapeInfoT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ImportDeltaShapeInfoT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ImportDeltaShapeInfo> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportDeltaShapeInfoT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ImportDeltaShapeInfoBuilder {
  typedef ImportDeltaShapeInfo Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_fvertexstart(uint32_t fvertexstart);
  void add_fvertexcount(uint32_t fvertexcount);
  explicit ImportDeltaShapeInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ImportDeltaShapeInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ImportDeltaShapeInfo>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportDeltaShapeInfo> CreateImportDeltaShapeInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t fvertexstart = 0,
    uint32_t fvertexcount = 0);
struct ImportDeltaShapeInfo::Traits {
  using type = ImportDeltaShapeInfo;
  static auto constexpr Create = CreateImportDeltaShapeInfo;
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportDeltaShapeInfo> CreateImportDeltaShapeInfo(flatbuffers::FlatBufferBuilder &_fbb, const ImportDeltaShapeInfoT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ImportBlendShapeChannelInfoT : public flatbuffers::NativeTable {
  typedef ImportBlendShapeChannelInfo TableType;
  std::string fname{};
  std::vector<float> fnormalizedfullweights{};
  std::vector<std::unique_ptr<CrossSchema::ImportDeltaShapeInfoT>> fdeltashapes{};
};

struct ImportBlendShapeChannelInfo FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ImportBlendShapeChannelInfoT NativeTableType;
  typedef ImportBlendShapeChannelInfoBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FNAME = 4,
    VT_FNORMALIZEDFULLWEIGHTS = 6,
    VT_FDELTASHAPES = 8
  };
CROSS_SCHEMA_API  const flatbuffers::String *fname() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_fname();
CROSS_SCHEMA_API  const flatbuffers::Vector<float> *fnormalizedfullweights() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<float> *mutable_fnormalizedfullweights();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportDeltaShapeInfo>> *fdeltashapes() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportDeltaShapeInfo>> *mutable_fdeltashapes();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ImportBlendShapeChannelInfoT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ImportBlendShapeChannelInfoT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ImportBlendShapeChannelInfo> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportBlendShapeChannelInfoT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ImportBlendShapeChannelInfoBuilder {
  typedef ImportBlendShapeChannelInfo Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_fname(flatbuffers::Offset<flatbuffers::String> fname);
  void add_fnormalizedfullweights(flatbuffers::Offset<flatbuffers::Vector<float>> fnormalizedfullweights);
  void add_fdeltashapes(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportDeltaShapeInfo>>> fdeltashapes);
  explicit ImportBlendShapeChannelInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ImportBlendShapeChannelInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ImportBlendShapeChannelInfo>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportBlendShapeChannelInfo> CreateImportBlendShapeChannelInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> fname = 0,
    flatbuffers::Offset<flatbuffers::Vector<float>> fnormalizedfullweights = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportDeltaShapeInfo>>> fdeltashapes = 0);
struct ImportBlendShapeChannelInfo::Traits {
  using type = ImportBlendShapeChannelInfo;
  static auto constexpr Create = CreateImportBlendShapeChannelInfo;
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportBlendShapeChannelInfo> CreateImportBlendShapeChannelInfoDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *fname = nullptr,
    const std::vector<float> *fnormalizedfullweights = nullptr,
    const std::vector<flatbuffers::Offset<CrossSchema::ImportDeltaShapeInfo>> *fdeltashapes = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<ImportBlendShapeChannelInfo> CreateImportBlendShapeChannelInfo(flatbuffers::FlatBufferBuilder &_fbb, const ImportBlendShapeChannelInfoT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ImportBlendShapeInfoT : public flatbuffers::NativeTable {
  typedef ImportBlendShapeInfo TableType;
  uint32_t fvertexchannelsemanticmask = 0;
  std::vector<std::unique_ptr<CrossSchema::VertexChannelAssetDataT>> fvertexchanneldata{};
  std::vector<std::unique_ptr<CrossSchema::ImportBlendShapeChannelInfoT>> fchannelinfos{};
};

struct ImportBlendShapeInfo FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ImportBlendShapeInfoT NativeTableType;
  typedef ImportBlendShapeInfoBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FVERTEXCHANNELSEMANTICMASK = 4,
    VT_FVERTEXCHANNELDATA = 6,
    VT_FCHANNELINFOS = 8
  };
CROSS_SCHEMA_API  uint32_t fvertexchannelsemanticmask() const;
  CROSS_SCHEMA_API  bool mutate_fvertexchannelsemanticmask(uint32_t _fvertexchannelsemanticmask);
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::VertexChannelAssetData>> *fvertexchanneldata() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::VertexChannelAssetData>> *mutable_fvertexchanneldata();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportBlendShapeChannelInfo>> *fchannelinfos() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportBlendShapeChannelInfo>> *mutable_fchannelinfos();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ImportBlendShapeInfoT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ImportBlendShapeInfoT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ImportBlendShapeInfo> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportBlendShapeInfoT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ImportBlendShapeInfoBuilder {
  typedef ImportBlendShapeInfo Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_fvertexchannelsemanticmask(uint32_t fvertexchannelsemanticmask);
  void add_fvertexchanneldata(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::VertexChannelAssetData>>> fvertexchanneldata);
  void add_fchannelinfos(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportBlendShapeChannelInfo>>> fchannelinfos);
  explicit ImportBlendShapeInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ImportBlendShapeInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ImportBlendShapeInfo>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportBlendShapeInfo> CreateImportBlendShapeInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t fvertexchannelsemanticmask = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::VertexChannelAssetData>>> fvertexchanneldata = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportBlendShapeChannelInfo>>> fchannelinfos = 0);
struct ImportBlendShapeInfo::Traits {
  using type = ImportBlendShapeInfo;
  static auto constexpr Create = CreateImportBlendShapeInfo;
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportBlendShapeInfo> CreateImportBlendShapeInfoDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t fvertexchannelsemanticmask = 0,
    const std::vector<flatbuffers::Offset<CrossSchema::VertexChannelAssetData>> *fvertexchanneldata = nullptr,
    const std::vector<flatbuffers::Offset<CrossSchema::ImportBlendShapeChannelInfo>> *fchannelinfos = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<ImportBlendShapeInfo> CreateImportBlendShapeInfo(flatbuffers::FlatBufferBuilder &_fbb, const ImportBlendShapeInfoT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ImportMeshPartAssetInfoT : public flatbuffers::NativeTable {
  typedef ImportMeshPartAssetInfo TableType;
  int16_t fnameindex = -1;
  int16_t fmaterialindex = -1;
  uint32_t fvertexstart = 0;
  uint32_t fvertexcount = 0;
  uint32_t findexstart = 0;
  uint32_t findexcount = 0;
  uint32_t fprimitivecount = 0;
  std::unique_ptr<CrossSchema::MeshBoundT> fbindinginfo{};
  uint64_t fmiscflag = 0;
  float fshadowbias = 0.0f;
  float fshadownormalbias = 0.0f;
  uint32_t fprimitivetype = 0;
  uint8_t frenderpriority = 0;
  std::vector<std::unique_ptr<CrossSchema::CollisionNodeT>> fcollisiontree{};
  std::unique_ptr<CrossSchema::CustomAttributeInfoT> fcustomattributeinfo{};
  std::vector<uint8_t> fcustomattributedata{};
  std::vector<std::unique_ptr<CrossSchema::MeshClusterInfoT>> mClusters{};
  std::vector<std::unique_ptr<CrossSchema::ClusterGroupInfoT>> mClusterGroups{};
  std::vector<std::unique_ptr<CrossSchema::IndirectDrawCMDT>> mIndirectDrawCMDs{};
  std::unique_ptr<CrossSchema::ImportBlendShapeInfoT> fblendshape{};
};

struct ImportMeshPartAssetInfo FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ImportMeshPartAssetInfoT NativeTableType;
  typedef ImportMeshPartAssetInfoBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FNAMEINDEX = 4,
    VT_FMATERIALINDEX = 6,
    VT_FVERTEXSTART = 8,
    VT_FVERTEXCOUNT = 10,
    VT_FINDEXSTART = 12,
    VT_FINDEXCOUNT = 14,
    VT_FPRIMITIVECOUNT = 16,
    VT_FBINDINGINFO = 18,
    VT_FMISCFLAG = 20,
    VT_FSHADOWBIAS = 22,
    VT_FSHADOWNORMALBIAS = 24,
    VT_FPRIMITIVETYPE = 26,
    VT_FRENDERPRIORITY = 28,
    VT_FCOLLISIONTREE = 30,
    VT_FCUSTOMATTRIBUTEINFO = 32,
    VT_FCUSTOMATTRIBUTEDATA = 34,
    VT_MCLUSTERS = 36,
    VT_MCLUSTERGROUPS = 38,
    VT_MINDIRECTDRAWCMDS = 40,
    VT_FBLENDSHAPE = 42
  };
CROSS_SCHEMA_API  int16_t fnameindex() const;
  CROSS_SCHEMA_API  bool mutate_fnameindex(int16_t _fnameindex);
CROSS_SCHEMA_API  int16_t fmaterialindex() const;
  CROSS_SCHEMA_API  bool mutate_fmaterialindex(int16_t _fmaterialindex);
CROSS_SCHEMA_API  uint32_t fvertexstart() const;
  CROSS_SCHEMA_API  bool mutate_fvertexstart(uint32_t _fvertexstart);
CROSS_SCHEMA_API  uint32_t fvertexcount() const;
  CROSS_SCHEMA_API  bool mutate_fvertexcount(uint32_t _fvertexcount);
CROSS_SCHEMA_API  uint32_t findexstart() const;
  CROSS_SCHEMA_API  bool mutate_findexstart(uint32_t _findexstart);
CROSS_SCHEMA_API  uint32_t findexcount() const;
  CROSS_SCHEMA_API  bool mutate_findexcount(uint32_t _findexcount);
CROSS_SCHEMA_API  uint32_t fprimitivecount() const;
  CROSS_SCHEMA_API  bool mutate_fprimitivecount(uint32_t _fprimitivecount);
CROSS_SCHEMA_API  const CrossSchema::MeshBound *fbindinginfo() const;
  CROSS_SCHEMA_API  CrossSchema::MeshBound *mutable_fbindinginfo();
CROSS_SCHEMA_API  uint64_t fmiscflag() const;
  CROSS_SCHEMA_API  bool mutate_fmiscflag(uint64_t _fmiscflag);
CROSS_SCHEMA_API  float fshadowbias() const;
  CROSS_SCHEMA_API  bool mutate_fshadowbias(float _fshadowbias);
CROSS_SCHEMA_API  float fshadownormalbias() const;
  CROSS_SCHEMA_API  bool mutate_fshadownormalbias(float _fshadownormalbias);
CROSS_SCHEMA_API  uint32_t fprimitivetype() const;
  CROSS_SCHEMA_API  bool mutate_fprimitivetype(uint32_t _fprimitivetype);
CROSS_SCHEMA_API  uint8_t frenderpriority() const;
  CROSS_SCHEMA_API  bool mutate_frenderpriority(uint8_t _frenderpriority);
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CollisionNode>> *fcollisiontree() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CollisionNode>> *mutable_fcollisiontree();
CROSS_SCHEMA_API  const CrossSchema::CustomAttributeInfo *fcustomattributeinfo() const;
  CROSS_SCHEMA_API  CrossSchema::CustomAttributeInfo *mutable_fcustomattributeinfo();
CROSS_SCHEMA_API  const flatbuffers::Vector<uint8_t> *fcustomattributedata() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<uint8_t> *mutable_fcustomattributedata();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::MeshClusterInfo>> *mClusters() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::MeshClusterInfo>> *mutable_mClusters();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ClusterGroupInfo>> *mClusterGroups() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ClusterGroupInfo>> *mutable_mClusterGroups();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::IndirectDrawCMD>> *mIndirectDrawCMDs() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::IndirectDrawCMD>> *mutable_mIndirectDrawCMDs();
CROSS_SCHEMA_API  const CrossSchema::ImportBlendShapeInfo *fblendshape() const;
  CROSS_SCHEMA_API  CrossSchema::ImportBlendShapeInfo *mutable_fblendshape();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ImportMeshPartAssetInfoT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ImportMeshPartAssetInfoT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ImportMeshPartAssetInfo> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportMeshPartAssetInfoT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ImportMeshPartAssetInfoBuilder {
  typedef ImportMeshPartAssetInfo Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_fnameindex(int16_t fnameindex);
  void add_fmaterialindex(int16_t fmaterialindex);
  void add_fvertexstart(uint32_t fvertexstart);
  void add_fvertexcount(uint32_t fvertexcount);
  void add_findexstart(uint32_t findexstart);
  void add_findexcount(uint32_t findexcount);
  void add_fprimitivecount(uint32_t fprimitivecount);
  void add_fbindinginfo(flatbuffers::Offset<CrossSchema::MeshBound> fbindinginfo);
  void add_fmiscflag(uint64_t fmiscflag);
  void add_fshadowbias(float fshadowbias);
  void add_fshadownormalbias(float fshadownormalbias);
  void add_fprimitivetype(uint32_t fprimitivetype);
  void add_frenderpriority(uint8_t frenderpriority);
  void add_fcollisiontree(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CollisionNode>>> fcollisiontree);
  void add_fcustomattributeinfo(flatbuffers::Offset<CrossSchema::CustomAttributeInfo> fcustomattributeinfo);
  void add_fcustomattributedata(flatbuffers::Offset<flatbuffers::Vector<uint8_t>> fcustomattributedata);
  void add_mClusters(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::MeshClusterInfo>>> mClusters);
  void add_mClusterGroups(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ClusterGroupInfo>>> mClusterGroups);
  void add_mIndirectDrawCMDs(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::IndirectDrawCMD>>> mIndirectDrawCMDs);
  void add_fblendshape(flatbuffers::Offset<CrossSchema::ImportBlendShapeInfo> fblendshape);
  explicit ImportMeshPartAssetInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ImportMeshPartAssetInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ImportMeshPartAssetInfo>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportMeshPartAssetInfo> CreateImportMeshPartAssetInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    int16_t fnameindex = -1,
    int16_t fmaterialindex = -1,
    uint32_t fvertexstart = 0,
    uint32_t fvertexcount = 0,
    uint32_t findexstart = 0,
    uint32_t findexcount = 0,
    uint32_t fprimitivecount = 0,
    flatbuffers::Offset<CrossSchema::MeshBound> fbindinginfo = 0,
    uint64_t fmiscflag = 0,
    float fshadowbias = 0.0f,
    float fshadownormalbias = 0.0f,
    uint32_t fprimitivetype = 0,
    uint8_t frenderpriority = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CollisionNode>>> fcollisiontree = 0,
    flatbuffers::Offset<CrossSchema::CustomAttributeInfo> fcustomattributeinfo = 0,
    flatbuffers::Offset<flatbuffers::Vector<uint8_t>> fcustomattributedata = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::MeshClusterInfo>>> mClusters = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ClusterGroupInfo>>> mClusterGroups = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::IndirectDrawCMD>>> mIndirectDrawCMDs = 0,
    flatbuffers::Offset<CrossSchema::ImportBlendShapeInfo> fblendshape = 0);
struct ImportMeshPartAssetInfo::Traits {
  using type = ImportMeshPartAssetInfo;
  static auto constexpr Create = CreateImportMeshPartAssetInfo;
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportMeshPartAssetInfo> CreateImportMeshPartAssetInfoDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    int16_t fnameindex = -1,
    int16_t fmaterialindex = -1,
    uint32_t fvertexstart = 0,
    uint32_t fvertexcount = 0,
    uint32_t findexstart = 0,
    uint32_t findexcount = 0,
    uint32_t fprimitivecount = 0,
    flatbuffers::Offset<CrossSchema::MeshBound> fbindinginfo = 0,
    uint64_t fmiscflag = 0,
    float fshadowbias = 0.0f,
    float fshadownormalbias = 0.0f,
    uint32_t fprimitivetype = 0,
    uint8_t frenderpriority = 0,
    const std::vector<flatbuffers::Offset<CrossSchema::CollisionNode>> *fcollisiontree = nullptr,
    flatbuffers::Offset<CrossSchema::CustomAttributeInfo> fcustomattributeinfo = 0,
    const std::vector<uint8_t> *fcustomattributedata = nullptr,
    const std::vector<flatbuffers::Offset<CrossSchema::MeshClusterInfo>> *mClusters = nullptr,
    const std::vector<flatbuffers::Offset<CrossSchema::ClusterGroupInfo>> *mClusterGroups = nullptr,
    const std::vector<flatbuffers::Offset<CrossSchema::IndirectDrawCMD>> *mIndirectDrawCMDs = nullptr,
    flatbuffers::Offset<CrossSchema::ImportBlendShapeInfo> fblendshape = 0);
CROSS_SCHEMA_API flatbuffers::Offset<ImportMeshPartAssetInfo> CreateImportMeshPartAssetInfo(flatbuffers::FlatBufferBuilder &_fbb, const ImportMeshPartAssetInfoT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ImportMeshAssetDataT : public flatbuffers::NativeTable {
  typedef ImportMeshAssetData TableType;
  uint32_t fversion = 0;
  std::string fname{};
  uint32_t fvertexcount = 0;
  uint32_t fprimitivecount = 0;
  uint32_t fvertexchannelsemanticmask = 0;
  std::unique_ptr<CrossSchema::IndexStreamAssetDataT> findexstream{};
  std::vector<std::unique_ptr<CrossSchema::VertexChannelAssetDataT>> fvertexchanneldata{};
  std::vector<std::unique_ptr<CrossSchema::ImportMeshPartAssetInfoT>> fmeshpartinfo{};
  std::vector<uint32_t> fmeshpartlodstartindex{};
  std::vector<std::string> fmaterialnames{};
  std::vector<std::string> fmeshpartnames{};
  std::unique_ptr<CrossSchema::MeshBoundT> faabb{};
  std::unique_ptr<CrossSchema::ImportRefSkeletonT> frefskeleton{};
  std::vector<std::unique_ptr<CrossSchema::invmatrixT>> fbindposeinv{};
  std::unique_ptr<CrossSchema::PhysicsCollisionT> fphysicscollision{};
  uint16_t fcustomattributeversion = 0;
  uint16_t fcustomattributeversionflag = 0;
  std::unique_ptr<CrossSchema::CustomAttributeInfoT> fcustomattributeinfo{};
  std::vector<uint8_t> fcustomattributedata{};
};

struct ImportMeshAssetData FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ImportMeshAssetDataT NativeTableType;
  typedef ImportMeshAssetDataBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FVERSION = 4,
    VT_FNAME = 6,
    VT_FVERTEXCOUNT = 8,
    VT_FPRIMITIVECOUNT = 10,
    VT_FVERTEXCHANNELSEMANTICMASK = 12,
    VT_FINDEXSTREAM = 14,
    VT_FVERTEXCHANNELDATA = 16,
    VT_FMESHPARTINFO = 18,
    VT_FMESHPARTLODSTARTINDEX = 20,
    VT_FMATERIALNAMES = 22,
    VT_FMESHPARTNAMES = 24,
    VT_FAABB = 26,
    VT_FREFSKELETON = 28,
    VT_FBINDPOSEINV = 30,
    VT_FPHYSICSCOLLISION = 32,
    VT_FCUSTOMATTRIBUTEVERSION = 34,
    VT_FCUSTOMATTRIBUTEVERSIONFLAG = 36,
    VT_FCUSTOMATTRIBUTEINFO = 38,
    VT_FCUSTOMATTRIBUTEDATA = 40
  };
CROSS_SCHEMA_API  uint32_t fversion() const;
  CROSS_SCHEMA_API  bool mutate_fversion(uint32_t _fversion);
CROSS_SCHEMA_API  const flatbuffers::String *fname() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_fname();
CROSS_SCHEMA_API  uint32_t fvertexcount() const;
  CROSS_SCHEMA_API  bool mutate_fvertexcount(uint32_t _fvertexcount);
CROSS_SCHEMA_API  uint32_t fprimitivecount() const;
  CROSS_SCHEMA_API  bool mutate_fprimitivecount(uint32_t _fprimitivecount);
CROSS_SCHEMA_API  uint32_t fvertexchannelsemanticmask() const;
  CROSS_SCHEMA_API  bool mutate_fvertexchannelsemanticmask(uint32_t _fvertexchannelsemanticmask);
CROSS_SCHEMA_API  const CrossSchema::IndexStreamAssetData *findexstream() const;
  CROSS_SCHEMA_API  CrossSchema::IndexStreamAssetData *mutable_findexstream();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::VertexChannelAssetData>> *fvertexchanneldata() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::VertexChannelAssetData>> *mutable_fvertexchanneldata();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportMeshPartAssetInfo>> *fmeshpartinfo() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportMeshPartAssetInfo>> *mutable_fmeshpartinfo();
CROSS_SCHEMA_API  const flatbuffers::Vector<uint32_t> *fmeshpartlodstartindex() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<uint32_t> *mutable_fmeshpartlodstartindex();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *fmaterialnames() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *mutable_fmaterialnames();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *fmeshpartnames() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *mutable_fmeshpartnames();
CROSS_SCHEMA_API  const CrossSchema::MeshBound *faabb() const;
  CROSS_SCHEMA_API  CrossSchema::MeshBound *mutable_faabb();
CROSS_SCHEMA_API  const CrossSchema::ImportRefSkeleton *frefskeleton() const;
  CROSS_SCHEMA_API  CrossSchema::ImportRefSkeleton *mutable_frefskeleton();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::invmatrix>> *fbindposeinv() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::invmatrix>> *mutable_fbindposeinv();
CROSS_SCHEMA_API  const CrossSchema::PhysicsCollision *fphysicscollision() const;
  CROSS_SCHEMA_API  CrossSchema::PhysicsCollision *mutable_fphysicscollision();
CROSS_SCHEMA_API  uint16_t fcustomattributeversion() const;
  CROSS_SCHEMA_API  bool mutate_fcustomattributeversion(uint16_t _fcustomattributeversion);
CROSS_SCHEMA_API  uint16_t fcustomattributeversionflag() const;
  CROSS_SCHEMA_API  bool mutate_fcustomattributeversionflag(uint16_t _fcustomattributeversionflag);
CROSS_SCHEMA_API  const CrossSchema::CustomAttributeInfo *fcustomattributeinfo() const;
  CROSS_SCHEMA_API  CrossSchema::CustomAttributeInfo *mutable_fcustomattributeinfo();
CROSS_SCHEMA_API  const flatbuffers::Vector<uint8_t> *fcustomattributedata() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<uint8_t> *mutable_fcustomattributedata();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ImportMeshAssetDataT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ImportMeshAssetDataT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ImportMeshAssetData> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportMeshAssetDataT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ImportMeshAssetDataBuilder {
  typedef ImportMeshAssetData Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_fversion(uint32_t fversion);
  void add_fname(flatbuffers::Offset<flatbuffers::String> fname);
  void add_fvertexcount(uint32_t fvertexcount);
  void add_fprimitivecount(uint32_t fprimitivecount);
  void add_fvertexchannelsemanticmask(uint32_t fvertexchannelsemanticmask);
  void add_findexstream(flatbuffers::Offset<CrossSchema::IndexStreamAssetData> findexstream);
  void add_fvertexchanneldata(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::VertexChannelAssetData>>> fvertexchanneldata);
  void add_fmeshpartinfo(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportMeshPartAssetInfo>>> fmeshpartinfo);
  void add_fmeshpartlodstartindex(flatbuffers::Offset<flatbuffers::Vector<uint32_t>> fmeshpartlodstartindex);
  void add_fmaterialnames(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> fmaterialnames);
  void add_fmeshpartnames(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> fmeshpartnames);
  void add_faabb(flatbuffers::Offset<CrossSchema::MeshBound> faabb);
  void add_frefskeleton(flatbuffers::Offset<CrossSchema::ImportRefSkeleton> frefskeleton);
  void add_fbindposeinv(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::invmatrix>>> fbindposeinv);
  void add_fphysicscollision(flatbuffers::Offset<CrossSchema::PhysicsCollision> fphysicscollision);
  void add_fcustomattributeversion(uint16_t fcustomattributeversion);
  void add_fcustomattributeversionflag(uint16_t fcustomattributeversionflag);
  void add_fcustomattributeinfo(flatbuffers::Offset<CrossSchema::CustomAttributeInfo> fcustomattributeinfo);
  void add_fcustomattributedata(flatbuffers::Offset<flatbuffers::Vector<uint8_t>> fcustomattributedata);
  explicit ImportMeshAssetDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ImportMeshAssetData> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ImportMeshAssetData>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportMeshAssetData> CreateImportMeshAssetData(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t fversion = 0,
    flatbuffers::Offset<flatbuffers::String> fname = 0,
    uint32_t fvertexcount = 0,
    uint32_t fprimitivecount = 0,
    uint32_t fvertexchannelsemanticmask = 0,
    flatbuffers::Offset<CrossSchema::IndexStreamAssetData> findexstream = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::VertexChannelAssetData>>> fvertexchanneldata = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportMeshPartAssetInfo>>> fmeshpartinfo = 0,
    flatbuffers::Offset<flatbuffers::Vector<uint32_t>> fmeshpartlodstartindex = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> fmaterialnames = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> fmeshpartnames = 0,
    flatbuffers::Offset<CrossSchema::MeshBound> faabb = 0,
    flatbuffers::Offset<CrossSchema::ImportRefSkeleton> frefskeleton = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::invmatrix>>> fbindposeinv = 0,
    flatbuffers::Offset<CrossSchema::PhysicsCollision> fphysicscollision = 0,
    uint16_t fcustomattributeversion = 0,
    uint16_t fcustomattributeversionflag = 0,
    flatbuffers::Offset<CrossSchema::CustomAttributeInfo> fcustomattributeinfo = 0,
    flatbuffers::Offset<flatbuffers::Vector<uint8_t>> fcustomattributedata = 0);
struct ImportMeshAssetData::Traits {
  using type = ImportMeshAssetData;
  static auto constexpr Create = CreateImportMeshAssetData;
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportMeshAssetData> CreateImportMeshAssetDataDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t fversion = 0,
    const char *fname = nullptr,
    uint32_t fvertexcount = 0,
    uint32_t fprimitivecount = 0,
    uint32_t fvertexchannelsemanticmask = 0,
    flatbuffers::Offset<CrossSchema::IndexStreamAssetData> findexstream = 0,
    const std::vector<flatbuffers::Offset<CrossSchema::VertexChannelAssetData>> *fvertexchanneldata = nullptr,
    const std::vector<flatbuffers::Offset<CrossSchema::ImportMeshPartAssetInfo>> *fmeshpartinfo = nullptr,
    const std::vector<uint32_t> *fmeshpartlodstartindex = nullptr,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *fmaterialnames = nullptr,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *fmeshpartnames = nullptr,
    flatbuffers::Offset<CrossSchema::MeshBound> faabb = 0,
    flatbuffers::Offset<CrossSchema::ImportRefSkeleton> frefskeleton = 0,
    const std::vector<flatbuffers::Offset<CrossSchema::invmatrix>> *fbindposeinv = nullptr,
    flatbuffers::Offset<CrossSchema::PhysicsCollision> fphysicscollision = 0,
    uint16_t fcustomattributeversion = 0,
    uint16_t fcustomattributeversionflag = 0,
    flatbuffers::Offset<CrossSchema::CustomAttributeInfo> fcustomattributeinfo = 0,
    const std::vector<uint8_t> *fcustomattributedata = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<ImportMeshAssetData> CreateImportMeshAssetData(flatbuffers::FlatBufferBuilder &_fbb, const ImportMeshAssetDataT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

inline const flatbuffers::TypeTable *PhysicsBoxCollisionTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_SEQUENCE, 0, 1 },
    { flatbuffers::ET_SEQUENCE, 0, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::float3TypeTable,
    CrossSchema::float4TypeTable
  };
  static const char * const names[] = {
    "position",
    "rotate",
    "halfextents"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 3, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *PhysicsSphereCollisionTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_FLOAT, 0, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::float3TypeTable
  };
  static const char * const names[] = {
    "position",
    "radius"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 2, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *PhysicsCapsuleCollisionTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_SEQUENCE, 0, 1 },
    { flatbuffers::ET_FLOAT, 0, -1 },
    { flatbuffers::ET_FLOAT, 0, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::float3TypeTable,
    CrossSchema::float4TypeTable
  };
  static const char * const names[] = {
    "position",
    "rotate",
    "radius",
    "halfHeight"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 4, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *PhysicsConvexCollisionTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_SEQUENCE, 0, 1 },
    { flatbuffers::ET_UCHAR, 1, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::float3TypeTable,
    CrossSchema::float4TypeTable
  };
  static const char * const names[] = {
    "position",
    "rotate",
    "data"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 3, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *PhysicsMeshCollisionTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_SEQUENCE, 0, 1 },
    { flatbuffers::ET_UCHAR, 1, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::float3TypeTable,
    CrossSchema::float4TypeTable
  };
  static const char * const names[] = {
    "position",
    "rotate",
    "data"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 3, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *PhysicsCollisionTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 1, 0 },
    { flatbuffers::ET_SEQUENCE, 1, 1 },
    { flatbuffers::ET_SEQUENCE, 1, 2 },
    { flatbuffers::ET_SEQUENCE, 1, 3 },
    { flatbuffers::ET_SEQUENCE, 1, 4 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::PhysicsBoxCollisionTypeTable,
    CrossSchema::PhysicsSphereCollisionTypeTable,
    CrossSchema::PhysicsCapsuleCollisionTypeTable,
    CrossSchema::PhysicsConvexCollisionTypeTable,
    CrossSchema::PhysicsMeshCollisionTypeTable
  };
  static const char * const names[] = {
    "boxcollision",
    "spherecollision",
    "capsulecollision",
    "convexcollision",
    "meshcollision"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 5, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *UVRangeInfoTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_INT, 1, -1 },
    { flatbuffers::ET_INT, 1, -1 },
    { flatbuffers::ET_INT, 1, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 }
  };
  static const char * const names[] = {
    "mMin",
    "mGapStart",
    "mGapLength",
    "mPrecision",
    "mBitsU",
    "mBitsV"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 6, type_codes, nullptr, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *MeshClusterInfoTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_FLOAT, 0, -1 },
    { flatbuffers::ET_INT, 1, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 1, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UCHAR, 1, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::float4TypeTable,
    CrossSchema::UVRangeInfoTypeTable
  };
  static const char * const names[] = {
    "mIndexStart",
    "mIndexCount",
    "mVertexStart",
    "mVertexCount",
    "mBoundingSphere",
    "mNormalCone",
    "mApexOffset",
    "mPosStart",
    "mPosPrecision",
    "mPosBits",
    "mUVInfo",
    "mUVChannelCount",
    "mBitsPerVertex",
    "mIndexBit",
    "mIndexOffset",
    "mDataOffset",
    "mEncodeData"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 17, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ClusterGroupInfoTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::float4TypeTable
  };
  static const char * const names[] = {
    "boundingSphere",
    "clusterStartIndex",
    "clusterCount",
    "mipLevel",
    "parentIndex",
    "childStartIndex",
    "childCount"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 7, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *IndirectDrawCMDTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_INT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_FLOAT, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_SEQUENCE, 0, 1 },
    { flatbuffers::ET_SEQUENCE, 0, 1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::float2TypeTable,
    CrossSchema::float4TypeTable
  };
  static const char * const names[] = {
    "indexCount",
    "instanceCount",
    "firstIndex",
    "vertexOffset",
    "firstInstance",
    "apexOffset",
    "padding",
    "boundingSphere",
    "normalCone"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 9, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *CustomAttributeInfoTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 }
  };
  static const char * const names[] = {
    "fkeynamehash",
    "fdataflag",
    "fdatasizeinbyte",
    "fdataoffset"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 4, type_codes, nullptr, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *VertexChannelAssetDataTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_USHORT, 0, -1 },
    { flatbuffers::ET_USHORT, 0, -1 },
    { flatbuffers::ET_SHORT, 0, -1 },
    { flatbuffers::ET_SHORT, 0, -1 },
    { flatbuffers::ET_USHORT, 0, -1 },
    { flatbuffers::ET_USHORT, 0, -1 },
    { flatbuffers::ET_USHORT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UCHAR, 1, -1 }
  };
  static const char * const names[] = {
    "fstride",
    "ffrequency",
    "fstream",
    "fstreamoffset",
    "fmiscflag",
    "freserve0",
    "freserve1",
    "fvertexchannel",
    "fdataformat",
    "fdata"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 10, type_codes, nullptr, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *IndexStreamAssetDataTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UCHAR, 1, -1 },
    { flatbuffers::ET_BOOL, 0, -1 }
  };
  static const char * const names[] = {
    "fcount",
    "fdata",
    "fis16bitindex"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 3, type_codes, nullptr, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *MeshBoundTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_SEQUENCE, 0, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::float3TypeTable
  };
  static const char * const names[] = {
    "fmin",
    "fmax"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 2, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ImportDeltaShapeInfoTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 }
  };
  static const char * const names[] = {
    "fvertexstart",
    "fvertexcount"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 2, type_codes, nullptr, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ImportBlendShapeChannelInfoTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_FLOAT, 1, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ImportDeltaShapeInfoTypeTable
  };
  static const char * const names[] = {
    "fname",
    "fnormalizedfullweights",
    "fdeltashapes"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 3, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ImportBlendShapeInfoTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 0 },
    { flatbuffers::ET_SEQUENCE, 1, 1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::VertexChannelAssetDataTypeTable,
    CrossSchema::ImportBlendShapeChannelInfoTypeTable
  };
  static const char * const names[] = {
    "fvertexchannelsemanticmask",
    "fvertexchanneldata",
    "fchannelinfos"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 3, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ImportMeshPartAssetInfoTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SHORT, 0, -1 },
    { flatbuffers::ET_SHORT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_ULONG, 0, -1 },
    { flatbuffers::ET_FLOAT, 0, -1 },
    { flatbuffers::ET_FLOAT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UCHAR, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 1 },
    { flatbuffers::ET_SEQUENCE, 0, 2 },
    { flatbuffers::ET_UCHAR, 1, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 3 },
    { flatbuffers::ET_SEQUENCE, 1, 4 },
    { flatbuffers::ET_SEQUENCE, 1, 5 },
    { flatbuffers::ET_SEQUENCE, 0, 6 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::MeshBoundTypeTable,
    CrossSchema::CollisionNodeTypeTable,
    CrossSchema::CustomAttributeInfoTypeTable,
    CrossSchema::MeshClusterInfoTypeTable,
    CrossSchema::ClusterGroupInfoTypeTable,
    CrossSchema::IndirectDrawCMDTypeTable,
    CrossSchema::ImportBlendShapeInfoTypeTable
  };
  static const char * const names[] = {
    "fnameindex",
    "fmaterialindex",
    "fvertexstart",
    "fvertexcount",
    "findexstart",
    "findexcount",
    "fprimitivecount",
    "fbindinginfo",
    "fmiscflag",
    "fshadowbias",
    "fshadownormalbias",
    "fprimitivetype",
    "frenderpriority",
    "fcollisiontree",
    "fcustomattributeinfo",
    "fcustomattributedata",
    "mClusters",
    "mClusterGroups",
    "mIndirectDrawCMDs",
    "fblendshape"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 20, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ImportMeshAssetDataTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_SEQUENCE, 1, 1 },
    { flatbuffers::ET_SEQUENCE, 1, 2 },
    { flatbuffers::ET_UINT, 1, -1 },
    { flatbuffers::ET_STRING, 1, -1 },
    { flatbuffers::ET_STRING, 1, -1 },
    { flatbuffers::ET_SEQUENCE, 0, 3 },
    { flatbuffers::ET_SEQUENCE, 0, 4 },
    { flatbuffers::ET_SEQUENCE, 1, 5 },
    { flatbuffers::ET_SEQUENCE, 0, 6 },
    { flatbuffers::ET_USHORT, 0, -1 },
    { flatbuffers::ET_USHORT, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 0, 7 },
    { flatbuffers::ET_UCHAR, 1, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::IndexStreamAssetDataTypeTable,
    CrossSchema::VertexChannelAssetDataTypeTable,
    CrossSchema::ImportMeshPartAssetInfoTypeTable,
    CrossSchema::MeshBoundTypeTable,
    CrossSchema::ImportRefSkeletonTypeTable,
    CrossSchema::invmatrixTypeTable,
    CrossSchema::PhysicsCollisionTypeTable,
    CrossSchema::CustomAttributeInfoTypeTable
  };
  static const char * const names[] = {
    "fversion",
    "fname",
    "fvertexcount",
    "fprimitivecount",
    "fvertexchannelsemanticmask",
    "findexstream",
    "fvertexchanneldata",
    "fmeshpartinfo",
    "fmeshpartlodstartindex",
    "fmaterialnames",
    "fmeshpartnames",
    "faabb",
    "frefskeleton",
    "fbindposeinv",
    "fphysicscollision",
    "fcustomattributeversion",
    "fcustomattributeversionflag",
    "fcustomattributeinfo",
    "fcustomattributedata"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 19, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

CROSS_SCHEMA_API const CrossSchema::ImportMeshAssetData *GetImportMeshAssetData(const void *buf);
CROSS_SCHEMA_API const CrossSchema::ImportMeshAssetData *GetSizePrefixedImportMeshAssetData(const void *buf);
CROSS_SCHEMA_API ImportMeshAssetData *GetMutableImportMeshAssetData(void *buf);
CROSS_SCHEMA_API bool VerifyImportMeshAssetDataBuffer(flatbuffers::Verifier &verifier);
CROSS_SCHEMA_API bool VerifySizePrefixedImportMeshAssetDataBuffer(flatbuffers::Verifier &verifier);
CROSS_SCHEMA_API void FinishImportMeshAssetDataBuffer(flatbuffers::FlatBufferBuilder &fbb,flatbuffers::Offset<CrossSchema::ImportMeshAssetData> root);
CROSS_SCHEMA_API void FinishSizePrefixedImportMeshAssetDataBuffer(flatbuffers::FlatBufferBuilder &fbb,flatbuffers::Offset<CrossSchema::ImportMeshAssetData> root);
CROSS_SCHEMA_API std::unique_ptr<CrossSchema::ImportMeshAssetDataT> UnPackImportMeshAssetData(const void *buf,const flatbuffers::resolver_function_t *res = nullptr);
CROSS_SCHEMA_API std::unique_ptr<CrossSchema::ImportMeshAssetDataT> UnPackSizePrefixedImportMeshAssetData(const void *buf,const flatbuffers::resolver_function_t *res = nullptr);
}  // namespace CrossSchema

#endif  // FLATBUFFERS_GENERATED_IMPORTMESHASSETDATA_CROSSSCHEMA_H_
