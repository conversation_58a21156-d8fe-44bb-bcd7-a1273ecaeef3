#include "EnginePrefix.h"
#include "PhysXShape.h"

#include "PhysicsEngine/PhysXImpl/PhysXUtility.h"
#include "PhysicsEngine/PhysXImpl/PhysXGeometry.h"
#include "PhysicsEngine/PhysXImpl/PhysXMaterial.h"
#include "PhysicsEngine/PhysXImpl/PhysXEngineImpl.h"

namespace cross
{
    PhysXShape::PhysXShape(physx::PxShape* shape, const Float3& scale)
        :mShape(shape)
        , mScale(scale)
    {
        mShape->userData = this;
    }

    PhysXShape::~PhysXShape()
    {
        mShape->release();
    }

    PhysXShape* PhysXShape::CreateTriangleMeshShape(const PhysicsGeometryMesh& meshGeo, const Float3& scale,
        PhysicsMaterial* material)
    {
        Assert(meshGeo.mesh);
        physx::PxTriangleMesh* pxMesh = static_cast<PhysXTriangleMesh*>(meshGeo.mesh.get())->GetUnderlay();
        Assert(pxMesh);
        physx::PxTriangleMeshGeometry geometry{pxMesh, physx::PxMeshScale{CrossVec3ToPxVec3(scale)}};
        physx::PxMaterial* physxMaterial = static_cast<PhysXMaterial*>(material)->GetUnderlay();
        physx::PxShape* shape = PxGetPhysics().createShape(geometry, *physxMaterial, true);
        Assert(shape != nullptr);
        if (shape)
        {
            shape->setLocalPose(physx::PxTransform{ CrossVec3ToPxVec3(meshGeo.position), CrossQuatToPxQuat(meshGeo.rotate) });
            return new PhysXShape{ shape, scale };
        }
        return nullptr;
    }

    PhysXShape* PhysXShape::CreateConvexMeshShape(const PhysicsGeometryConvex& convexGeo, const Float3& scale,
        PhysicsMaterial* material)
    {
        Assert(convexGeo.mesh);
        physx::PxConvexMesh* pxMesh = static_cast<PhysXConvexMesh*>(convexGeo.mesh.get())->GetUnderlay();
        Assert(pxMesh);
        physx::PxConvexMeshGeometry geometry{ pxMesh, physx::PxMeshScale{CrossVec3ToPxVec3(scale)} };
        physx::PxMaterial* physxMaterial = static_cast<PhysXMaterial*>(material)->GetUnderlay();
        physx::PxShape* shape = PxGetPhysics().createShape(geometry, *physxMaterial, true);
        Assert(shape != nullptr);
        if (shape)
        {
            shape->setLocalPose(physx::PxTransform{
                CrossVec3ToPxVec3(convexGeo.position), CrossQuatToPxQuat(convexGeo.rotate)
                });
            return new PhysXShape{ shape, scale };
        }
        return nullptr;
    }

    PhysXShape* PhysXShape::CreateBoxShape(const PhysicsGeometryBox& boxGeo, const Float3& scale,
        PhysicsMaterial* material)
    {
        physx::PxBoxGeometry geometry{ CrossVec3ToPxVec3(boxGeo.halfExtents * scale) };
        physx::PxMaterial* physxMaterial = static_cast<PhysXMaterial*>(material)->GetUnderlay();
        physx::PxShape* shape = PxGetPhysics().createShape(geometry, *physxMaterial, true);
        Assert(shape != nullptr);
        if (shape)
        {
            shape->setLocalPose(physx::PxTransform(CrossVec3ToPxVec3(boxGeo.position), CrossQuatToPxQuat(boxGeo.rotate)));
            return new PhysXShape{ shape, scale };
        }
        return nullptr;
    }

    PhysXShape* PhysXShape::CreateSphereShape(const PhysicsGeometrySphere& sphereGeo, const Float3& scale,
        PhysicsMaterial* material)
    {
        if (!(abs(scale.x - scale.y) < 0.001 && abs(scale.x - scale.z) < 0.001))
        {
            LOG_ERROR("PhysXShape::CreateSphereShape scale param error. You set a scale ({},{},{}), it's not sphere at all!", scale.x, scale.y, scale.z);
        }
        physx::PxSphereGeometry geometry{ sphereGeo.radius * scale.x };
        physx::PxMaterial* physxMaterial = static_cast<PhysXMaterial*>(material)->GetUnderlay();
        physx::PxShape* shape = PxGetPhysics().createShape(geometry, *physxMaterial, true);
        Assert(shape != nullptr);
        if (shape)
        {
            shape->setLocalPose(physx::PxTransform(CrossVec3ToPxVec3(sphereGeo.position), physx::PxIdentity));
            return new PhysXShape{ shape, scale };
        }
        return nullptr;
    }

    PhysXShape* PhysXShape::CreateCapsuleShape(const PhysicsGeometryCapsule& capsuleGeo, const Float3& scale,
        PhysicsMaterial* material)
    {
        if (!FloatEqual(scale.z, scale.x, 0.00001f))
        {
            //dango phy TODO: How to deal with wrong scale?
            LOG_ERROR("PhysXShape::CreateCapsuleShape scale param error. You set a scale ({},{},{}), it's not capsule at all!", scale.x, scale.y, scale.z);
        }
        physx::PxCapsuleGeometry geometry{ capsuleGeo.radius * scale.x, capsuleGeo.halfHeight * scale.y };
        physx::PxMaterial* physxMaterial = static_cast<PhysXMaterial*>(material)->GetUnderlay();
        physx::PxShape* shape = PxGetPhysics().createShape(geometry, *physxMaterial, true);
        Assert(shape != nullptr);
        if (shape)
        {
            static const auto z_rotation = physx::PxQuat(MathUtils::MathPiDiv2, physx::PxVec3(0, 0, 1));
            auto quat = CrossQuatToPxQuat(capsuleGeo.rotate) * z_rotation;
            shape->setLocalPose(physx::PxTransform(CrossVec3ToPxVec3(capsuleGeo.position), quat));
            //rotate 90 degrees around z, because px capsule is lying on ground
            return new PhysXShape{ shape, scale };
        }
        return nullptr;
    }

    PhysXShape* PhysXShape::CreatePlaneShape(const PhysicsGeometryPlane& planeGeo, const Float3& scale,
        PhysicsMaterial* material)
    {
        physx::PxPlaneGeometry geometry{ };
        physx::PxMaterial* physxMaterial = static_cast<PhysXMaterial*>(material)->GetUnderlay();
        physx::PxShape* shape = PxGetPhysics().createShape(geometry, *physxMaterial, true);
        if (shape)
        {
            Assert(shape != nullptr);
            return new PhysXShape{ shape, scale };
        }
        return nullptr;
    }
    PhysXShape* PhysXShape::CreateTerrainShape(const PhysicsGeometryTerrain& terrainGeo, const Float3& scale, PhysicsMaterial* material) {
        Assert(terrainGeo.heightMap);
        physx::PxHeightField* pxHeightField = static_cast<PhysXTerrainHeightMap*>(terrainGeo.heightMap.get())->GetUnderlay();
        Assert(pxHeightField);
        // the height scale is applied at reading height map, so the heightScale stay 1 here.
        physx::PxHeightFieldGeometry geometry{pxHeightField, physx::PxMeshGeometryFlags(), 1, scale.x, scale.z};
        physx::PxMaterial* physxMaterial = static_cast<PhysXMaterial*>(material)->GetUnderlay();
        physx::PxShape* shape = PxGetPhysics().createShape(geometry, *physxMaterial, true);
        Assert(shape != nullptr);
        if (shape)
        {
            return new PhysXShape{shape, scale};
        }
        return nullptr;
    }
    void PhysXShape::DestroyShape(PhysXShape* shape)
    {
        delete shape;
    }

    void PhysXShape::SetLocalPos(const Float3& localPos, const Quaternion& localRotate)
    {
        mShape->setLocalPose(physx::PxTransform{ CrossVec3ToPxVec3(localPos), CrossQuatToPxQuat(localRotate) });
    }

    std::pair<Float3, Quaternion> PhysXShape::GetLocalPos()
    {
        physx::PxTransform tran = mShape->getLocalPose();
        return { PxVec3ToCrossVec3(tran.p), PxQuatToCrossQuat(tran.q) };
    }

    void PhysXShape::SetScale(const Float3& scale)
    {
        if (mScale != scale)
        {
            auto geoHolder = mShape->getGeometry();
            switch (GetShapeType())
            {
            case PhysicsShapeType::Box:
                geoHolder.box().halfExtents.x *= scale.x / mScale.x;
                geoHolder.box().halfExtents.y *= scale.y / mScale.y;
                geoHolder.box().halfExtents.z *= scale.z / mScale.z;
                break;
            case PhysicsShapeType::Sphere:
                if (!(abs(scale.x - scale.y) < 0.001 && abs(scale.x - scale.z) < 0.001))
                {
                    LOG_ERROR("PhysXShape::SetScale scale param error. You set a scale ({},{},{}), it's not sphere at all!", scale.x, scale.y, scale.z);
                }
                geoHolder.sphere().radius *= scale.x / mScale.x;
                break;
            case PhysicsShapeType::Capsule:
                if (scale.z != scale.x)
                {
                    //dango phy TODO: How to deal with wrong scale?
                    LOG_ERROR("PhysXShape::SetScale scale param error. You set a scale ({},{},{}), it's not capsule at all!", scale.x, scale.y, scale.z);
                }
                geoHolder.capsule().radius *= scale.x / mScale.x;
                geoHolder.capsule().halfHeight *= scale.y / mScale.y;
                break;
            case PhysicsShapeType::Plane: break;
            case PhysicsShapeType::Convex:
                geoHolder.convexMesh().scale = CrossVec3ToPxVec3(scale);
                break;
            case PhysicsShapeType::Mesh:
                geoHolder.triangleMesh().scale = CrossVec3ToPxVec3(scale);
                break;
            default:
                break;
            }
            mShape->setGeometry(geoHolder.any());

            mScale = scale;
        }
    }

    Float3 PhysXShape::GetScale() const
    {
        return mScale;
    }

    PhysicsShapeType PhysXShape::GetShapeType() const
    {
        switch (mShape->getGeometryType())
        {
        case physx::PxGeometryType::eSPHERE: return PhysicsShapeType::Sphere;
        case physx::PxGeometryType::ePLANE: return PhysicsShapeType::Plane;
        case physx::PxGeometryType::eCAPSULE: return PhysicsShapeType::Capsule;
        case physx::PxGeometryType::eBOX: return PhysicsShapeType::Box;
        case physx::PxGeometryType::eCONVEXMESH: return PhysicsShapeType::Convex;
        case physx::PxGeometryType::eTRIANGLEMESH: return PhysicsShapeType::Mesh;
        case physx::PxGeometryType::eHEIGHTFIELD: break;
        default:break;
        }
        Assert(false);
        return {};
    }

    void PhysXShape::SetCollisionInfo(CollisionTypeValue collisionType, CollisionMask mask, bool isTrigger)
    {
        physx::PxFilterData simulationFilterData = mShape->getSimulationFilterData();
        simulationFilterData.word1 = EncodeCollisionWord(isTrigger, collisionType, mask);
        mShape->setSimulationFilterData(simulationFilterData);

        physx::PxFilterData queryFilterData = mShape->getQueryFilterData();
        queryFilterData.word1 = EncodeCollisionWord(isTrigger, collisionType, mask);
        mShape->setQueryFilterData(queryFilterData);
        mShape->setFlag(physx::PxShapeFlag::eSIMULATION_SHAPE, !isTrigger);
        mShape->setFlag(physx::PxShapeFlag::eSCENE_QUERY_SHAPE, true);
        mShape->setFlag(physx::PxShapeFlag::eTRIGGER_SHAPE, isTrigger);
    }

    void PhysXShape::SetEnableReportCollision(bool enable)
    {
        physx::PxFilterData simulationFilterData = mShape->getSimulationFilterData();

        if (enable)
            simulationFilterData.word0 |= static_cast<UInt32>(PhysXFilterBit::REPORT_COLLISION_POINT);
        else
            simulationFilterData.word0 &= ~static_cast<UInt32>(PhysXFilterBit::REPORT_COLLISION_POINT);

        mShape->setSimulationFilterData(simulationFilterData);
    }
}
