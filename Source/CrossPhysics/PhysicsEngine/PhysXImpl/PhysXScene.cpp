#include "EnginePrefix.h"
#include "PhysXScene.h"
#include "ECS/Develop/Framework/Types.h"

#include "PhysicsEngine/PhysXImpl/PhysXUtility.h"
#include "PhysicsEngine/PhysXImpl/PhysXActor.h"
#include "PhysicsEngine/PhysXImpl/PhysXGeometry.h"
#include "PhysicsEngine/PhysXImpl/PhysXEngineImpl.h"
#include "PhysicsEngine/PhysXImpl/PhysXCpuDispatcher.h"

namespace cross
{
    void PhysXSimulationEventCallback::onContact(const physx::PxContactPairHeader& pairHeader, const physx::PxContactPair* pairs, physx::PxU32 nbPairs)
    {
        std::array<PhysXActor*, 2> actors = {static_cast<PhysXActor*>(pairHeader.actors[0]->userData), static_cast<PhysXActor*>(pairHeader.actors[1]->userData)};
        //  either shape 0 or shape 1 is removed, do not notify any event;
        if (pairs[0].flags & (physx::PxContactPairFlag::Enum::eREMOVED_SHAPE_0 | physx::PxContactPairFlag::Enum::eREMOVED_SHAPE_1))
        {
            return;
        }

        physx::PxScene* pxScene = actors[0]->GetUnderlay()->getScene();
        PhysXScene* scene = static_cast<PhysXScene*>(pxScene->userData);
        {
            std::vector<ContactPoint>* contactPoints = nullptr;

            const std::shared_ptr<const std::vector<ContactPoint>> sharedContactPoints{contactPoints};
            for (UInt32 i = 0; i < nbPairs; i++)
            {
                const physx::PxContactPair& cp = pairs[i];

                if (cp.contactPoints)
                {
                    if (!contactPoints)
                    {
                        contactPoints = new std::vector<ContactPoint>;
                    }
                    physx::PxContactStreamIterator iter(cp.contactPatches, cp.contactPoints, cp.getInternalFaceIndices(), cp.patchCount, cp.contactCount);
                    UInt32 contactIndex = 0;
                    while (iter.hasNextPatch())
                    {
                        iter.nextPatch();
                        while (iter.hasNextContact())
                        {
                            iter.nextContact();
                            ContactPoint contactPoint{PxVec3ToCrossVec3(iter.getContactPoint()), PxVec3ToCrossVec3(iter.getContactNormal()), PxVec3ToCrossVec3(iter.getContactNormal() * cp.contactImpulses[contactIndex])};
                            contactPoints->emplace_back(contactPoint);
                            ++contactIndex;
                        }
                    }
                    Assert(contactIndex == cp.contactCount);
                }
                for (size_t actorIndex = 0; actorIndex < 2; ++actorIndex)
                {
                    if (cp.events & physx::PxPairFlag::Enum::eNOTIFY_TOUCH_FOUND)
                    {
                        if (actors[actorIndex]->GetCollisionEnter())
                        {
                            std::shared_ptr<CollisionInfo> sharedInfo{new CollisionInfo{actors[actorIndex], actors[actorIndex == 0 ? 1 : 0], CollisionFlag::NOTIFYTOUCHFOUND, sharedContactPoints}};

                            scene->PushCollisionEnterCmd(actors[actorIndex], std::move(sharedInfo));
                        }
                    }
                    else if (cp.events & physx::PxPairFlag::Enum::eNOTIFY_TOUCH_PERSISTS)
                    {
                        if (actors[actorIndex]->GetCollisionStay())
                        {
                            std::shared_ptr<CollisionInfo> sharedInfo{new CollisionInfo{actors[actorIndex], actors[actorIndex == 0 ? 1 : 0], CollisionFlag::NOTIFYTOUCHPERSISTS, sharedContactPoints}};

                            scene->PushCollisionStayCmd(actors[actorIndex], std::move(sharedInfo));
                        }
                    }
                    else
                    if (cp.events & physx::PxPairFlag::Enum::eNOTIFY_TOUCH_LOST)
                    {
                        if (actors[actorIndex]->GetCollisionExit())
                        {
                            std::shared_ptr<CollisionInfo> sharedInfo{new CollisionInfo{actors[actorIndex], actors[actorIndex == 0 ? 1 : 0], CollisionFlag::NOTIFYTOUCHLOST, sharedContactPoints}};

                            scene->PushCollisionExitCmd(actors[actorIndex], std::move(sharedInfo));
                        }
                    }
                }
            }
            
        }
    }

    void PhysXSimulationEventCallback::onTrigger(physx::PxTriggerPair* pairs, physx::PxU32 count) 
    {
        for (physx::PxU32 i = 0; i < count; i++)
        {
            const physx::PxTriggerPair& tp = pairs[i];
            if (tp.flags & (physx::PxTriggerPairFlag::eREMOVED_SHAPE_TRIGGER | physx::PxTriggerPairFlag::eREMOVED_SHAPE_OTHER))
                continue;
                        
            std::array<PhysXActor*, 2> actors = {static_cast<PhysXActor*>(tp.triggerActor->userData), static_cast<PhysXActor*>(tp.otherActor->userData)};
            physx::PxScene* pxScene = actors[0]->GetUnderlay()->getScene();
            Assert(actors[1]->GetUnderlay()->getScene() == pxScene);
            PhysXScene* scene = static_cast<PhysXScene*>(pxScene->userData);
            const std::shared_ptr<const std::vector<ContactPoint>> sharedContactPoints{};
            switch (tp.status)
            {
            case physx::PxPairFlag::Enum::eNOTIFY_TOUCH_FOUND:
            {
                for (size_t actorIndex = 0; actorIndex < actors.size(); ++actorIndex)
                {
                    if (actors[actorIndex]->GetIsTrigger())
                    {
                        if (actors[actorIndex]->GetCollisionEnter())
                        {
                            std::shared_ptr<CollisionInfo> sharedInfo{new CollisionInfo{actors[actorIndex], actors[actorIndex == 0 ? 1 : 0], CollisionFlag::NOTIFYTOUCHFOUND, sharedContactPoints}};

                            scene->PushCollisionEnterCmd(actors[actorIndex], std::move(sharedInfo));
                        }
                    }
                }
                break;
            }
            case physx::PxPairFlag::Enum::eNOTIFY_TOUCH_LOST:
            {
                for (size_t actorIndex = 0; actorIndex < actors.size(); ++actorIndex)
                {
                    if (actors[actorIndex]->GetIsTrigger())
                    {
                        if (actors[actorIndex]->GetCollisionExit())
                        {
                            std::shared_ptr<CollisionInfo> sharedInfo{new CollisionInfo{actors[actorIndex], actors[actorIndex == 0 ? 1 : 0], CollisionFlag::NOTIFYTOUCHLOST, sharedContactPoints}};

                            scene->PushCollisionExitCmd(actors[actorIndex], std::move(sharedInfo));
                        }
                    }
                }
                break;
            }
            default:
                break;
            }
            

        }
    }

    PhysXDebugVisualization::PhysXDebugVisualization(const physx::PxRenderBuffer& pxBuffer)
        :mBuffer(pxBuffer)
    {
    }

    const UInt32 PhysXDebugVisualization::GetNbPoints()
    {
        return mBuffer.getNbPoints();
    }

    const PhysicsDebugVisualization::Point* PhysXDebugVisualization::GetPoints() const
    {
        static_assert(sizeof(PhysicsDebugVisualization::Point) == sizeof(physx::PxDebugPoint));
        static_assert(alignof(PhysicsDebugVisualization::Point) == alignof(physx::PxDebugPoint));
        static_assert(offsetof(PhysicsDebugVisualization::Point, pos) == offsetof(physx::PxDebugPoint, pos));
        static_assert(offsetof(PhysicsDebugVisualization::Point, color) == offsetof(physx::PxDebugPoint, color));
        return reinterpret_cast<PhysicsDebugVisualization::Point*>(reinterpret_cast<void*>(const_cast<physx::PxDebugPoint*>(mBuffer.getPoints())));
    }

    const UInt32 PhysXDebugVisualization::GetNbLines()
    {
        return mBuffer.getNbLines();
    }

    const PhysicsDebugVisualization::Line* PhysXDebugVisualization::GetLines() const
    {
        static_assert(sizeof(PhysicsDebugVisualization::Line) == sizeof(physx::PxDebugLine));
        static_assert(alignof(PhysicsDebugVisualization::Line) == alignof(physx::PxDebugLine));
        static_assert(offsetof(PhysicsDebugVisualization::Line, p0.pos) == offsetof(physx::PxDebugLine, pos0));
        static_assert(offsetof(PhysicsDebugVisualization::Line, p0.color) == offsetof(physx::PxDebugLine, color0));
        static_assert(offsetof(PhysicsDebugVisualization::Line, p1.pos) == offsetof(physx::PxDebugLine, pos1));
        static_assert(offsetof(PhysicsDebugVisualization::Line, p1.color) == offsetof(physx::PxDebugLine, color1));
        return reinterpret_cast<PhysicsDebugVisualization::Line*>(reinterpret_cast<void*>(const_cast<physx::PxDebugLine*>(mBuffer.getLines())));
    }

    const UInt32 PhysXDebugVisualization::GetNbTriangles()
    {
        return mBuffer.getNbTriangles();
    }

    const PhysicsDebugVisualization::Triangle* PhysXDebugVisualization::GetTriangles() const
    {
        static_assert(sizeof(PhysicsDebugVisualization::Triangle) == sizeof(physx::PxDebugTriangle));
        static_assert(alignof(PhysicsDebugVisualization::Triangle) == alignof(physx::PxDebugTriangle));
        static_assert(offsetof(PhysicsDebugVisualization::Triangle, p0.pos) == offsetof(physx::PxDebugTriangle, pos0));
        static_assert(offsetof(PhysicsDebugVisualization::Triangle, p0.color) == offsetof(physx::PxDebugTriangle, color0));
        static_assert(offsetof(PhysicsDebugVisualization::Triangle, p1.pos) == offsetof(physx::PxDebugTriangle, pos1));
        static_assert(offsetof(PhysicsDebugVisualization::Triangle, p1.color) == offsetof(physx::PxDebugTriangle, color1));
        static_assert(offsetof(PhysicsDebugVisualization::Triangle, p2.pos) == offsetof(physx::PxDebugTriangle, pos2));
        static_assert(offsetof(PhysicsDebugVisualization::Triangle, p2.color) == offsetof(physx::PxDebugTriangle, color2));
        return reinterpret_cast<PhysicsDebugVisualization::Triangle*>(reinterpret_cast<void*>(const_cast<physx::PxDebugTriangle*>(mBuffer.getTriangles())));
    }

    //PhysXSceneWriteLocker::PhysXSceneWriteLocker(PhysXSceneWriteLocker&& other) noexcept
    //    :mScene(other.mScene)
    //{
    //    other.mScene = nullptr;
    //}

    //PhysXSceneWriteLocker::~PhysXSceneWriteLocker()
    //{
    //    if (mScene)
    //        mScene->UnlockWrite();
    //}

    //PhysXSceneWriteLocker& PhysXSceneWriteLocker::operator=(PhysXSceneWriteLocker&& other)
    //{
    //    if (mScene != other.mScene)
    //    {
    //        if (mScene)
    //            mScene->UnlockWrite();
    //        mScene = other.mScene;
    //        other.mScene = nullptr;
    //    }
    //    return *this;
    //}

    //PhysXSceneWriteLocker::PhysXSceneWriteLocker(PhysXScene* scene)
    //    :mScene(scene)
    //{
    //    if (scene)
    //        scene->LockWrite();
    //}

    physx::PxFilterFlags PhysXFilterShader(
        physx::PxFilterObjectAttributes attributes0, physx::PxFilterData filterData0,
        physx::PxFilterObjectAttributes attributes1, physx::PxFilterData filterData1,
        physx::PxPairFlags& pairFlags, const void* constantBlock, physx::PxU32 constantBlockSize)
    {
        // let triggers through
        //if (physx::PxFilterObjectIsTrigger(attributes0) || physx::PxFilterObjectIsTrigger(attributes1))
        //{
        //    pairFlags = physx::PxPairFlag::eTRIGGER_DEFAULT;
        //    return physx::PxFilterFlag::eDEFAULT;
        //}

        auto [isTrigger0, collisionType0, collisionMask0] = DecodeCollisionWord(filterData0.word1);
        auto [isTrigger1, collisionType1, collisionMask1] = DecodeCollisionWord(filterData1.word1);
        if (collisionMask0.IsSet(collisionType1) && collisionMask1.IsSet(collisionType0))
        {
            if (isTrigger0 ||isTrigger1)
            {
                pairFlags = physx::PxPairFlag::eTRIGGER_DEFAULT;
            }
            else
            {
                pairFlags = (physx::PxPairFlag::eCONTACT_DEFAULT | physx::PxPairFlag::eNOTIFY_TOUCH_FOUND | physx::PxPairFlag::eNOTIFY_TOUCH_LOST | 
                              physx::PxPairFlag::eNOTIFY_TOUCH_PERSISTS);   
                if ((filterData0.word0 & static_cast<UInt32>(PhysXFilterBit::REPORT_COLLISION_POINT)) || (filterData1.word0 & static_cast<UInt32>(PhysXFilterBit::REPORT_COLLISION_POINT)))
                {
                    pairFlags |= physx::PxPairFlag::eNOTIFY_CONTACT_POINTS;
                }
            }
            return physx::PxFilterFlag::eDEFAULT;
        }
        else
        {
            return physx::PxFilterFlag::eSUPPRESS;
        }
    }

    PhysXScene* PhysXScene::Create(bool requireRWLock)
    {
        PhysXScene* ret = new PhysXScene(requireRWLock);
        physx::PxSceneDesc sceneDesc(PxGetPhysics().getTolerancesScale());
        sceneDesc.gravity = physx::PxVec3(0.0f, -981.f, 0.0f);
        sceneDesc.cpuDispatcher = gPhysXEngineImpl->GetDispatcher();
        sceneDesc.simulationEventCallback = ret->GetEventCallback();
        sceneDesc.filterShader = PhysXFilterShader;
        sceneDesc.flags = physx::PxSceneFlag::eENABLE_ACTIVE_ACTORS | physx::PxSceneFlag::eEXCLUDE_KINEMATICS_FROM_ACTIVE_ACTORS | physx::PxSceneFlag::eENABLE_CCD | physx::PxSceneFlag::eENABLE_PCM |
                          physx::PxSceneFlag::eENABLE_STABILIZATION;
        if (requireRWLock)
            sceneDesc.flags |= physx::PxSceneFlag::eREQUIRE_RW_LOCK;
        //sceneDesc.staticKineFilteringMode = physx::PxPairFilteringMode::eKEEP; //allow request contact information between kinematic and static ,but still no physics collision
        //sceneDesc.kineKineFilteringMode = physx::PxPairFilteringMode::eKEEP;   //allow request contact information between kinematic and kinematic ,but still no physics collision
        physx::PxScene* scene = PxGetPhysics().createScene(sceneDesc);
        Assert(scene);
        ret->SetUnderlay(scene);
        if (gPhysXEngineImpl->GetEnablePVD())
        {
            physx::PxPvdSceneClient* pvdClient = scene->getScenePvdClient();
            if (pvdClient)
            {
                pvdClient->setScenePvdFlag(physx::PxPvdSceneFlag::eTRANSMIT_CONSTRAINTS, true);
                pvdClient->setScenePvdFlag(physx::PxPvdSceneFlag::eTRANSMIT_CONTACTS, true);
                pvdClient->setScenePvdFlag(physx::PxPvdSceneFlag::eTRANSMIT_SCENEQUERIES, true);
            }
        }

        return ret;
    }

    void PhysXScene::Destroy(PhysXScene* scene)
    {
        delete scene;
    }
    PhysXScene::PhysXScene(bool requireRWLock)

    {
        mEnableRWLock = requireRWLock;
    }

    PhysXScene::~PhysXScene()
    {
        if (mPxScene)
            mPxScene->release();
    }

    void PhysXScene::AddActor(PhysicsActor* actor)
    {
        actor->SetScene(this);
        physx::PxActor* pxActor = static_cast<PhysXActor*>(actor)->GetUnderlay();
        if (mEnableRWLock)
            mPxScene->lockWrite();
        mPxScene->addActor(*pxActor);
        if (mEnableRWLock)
            mPxScene->unlockWrite();
    }

    void PhysXScene::RemoveActor(PhysicsActor* actor)
    {
        physx::PxActor* pxActor = static_cast<PhysXActor*>(actor)->GetUnderlay();

        if (mRayCastCache.actor == pxActor)
            mRayCastCache = {};
        if (mSweepCache.actor == pxActor)
            mSweepCache = {};

        if (mEnableRWLock)
            mPxScene->lockWrite();
        mPxScene->removeActor(*pxActor);
        if (mEnableRWLock)
            mPxScene->unlockWrite();
    }

    void PhysXScene::TraverseAllActors(const std::function<void(PhysicsActor*)>& functor)
    {
        using namespace physx;
        PxActorTypeFlags flags = PxActorTypeFlag::eRIGID_STATIC | PxActorTypeFlag::eRIGID_DYNAMIC;
        PxU32 actorNum = mPxScene->getNbActors(flags);
        //PxActor** actors = new PxActor*[actorNum];
        std::vector<PxActor*> actors(actorNum);
        mPxScene->getActors(flags, actors.data(), actorNum);
        for (PxU32 i = 0; i < actorNum; i++)
        {
            PhysXActor* pxActor = static_cast<PhysXActor*>(actors[i]->userData);
            functor(pxActor);
        }
    }

    void PhysXScene::TraverseActiveActors(const std::function<void(PhysicsActor*)>& functor)
    {
        physx::PxU32 activeActorNum = 0;
        if (mEnableRWLock)
            mPxScene->lockRead();
        physx::PxActor** actors = mPxScene->getActiveActors(activeActorNum);
        if (mEnableRWLock)
            mPxScene->unlockRead();

        for (physx::PxU32 i = 0; i < activeActorNum; ++i)
        {
            physx::PxRigidDynamic* rigidDynamic = actors[i]->is<physx::PxRigidDynamic>();
            if (rigidDynamic)
            {
                PhysXActor* actor = static_cast<PhysXActor*>(rigidDynamic->userData);
                functor(actor);
            }
            else
            {
                Assert(false); //What actor is this?
            }
        }
    }

    void PhysXScene::Simulate(float step)
    {
        if (mEnableRWLock)
            mPxScene->lockWrite();

        mPxScene->simulate(step);
        if (mEnableRWLock)
            mPxScene->unlockWrite();
    }

    void PhysXScene::FetchResult()
    {
        SCOPED_CPU_TIMING(GroupPhysX, __FUNCTION__);
        if (mEnableRWLock)
            mPxScene->lockWrite();

        mPxScene->fetchResults(true);
        if (mEnableRWLock)
            mPxScene->unlockWrite();
    }

    void PhysXScene::BeforeUpdate()
    {
        ExecuteCollisionCmd();
    }

    UInt32 PhysXScene::RayCast(const Float3& origin, const Float3& unitDir, float maxDistance, CollisionMask mask, HitFlag flag, UInt32 maxHit , bool ignoreTrigger, PhysicsHitResult* outResults)
    {
        Assert(maxHit > 0);
        std::unique_ptr<physx::PxRaycastHit[]> touchBuffer = nullptr;

        physx::PxHitFlags pxFlag = CrossHitFlagToPxHitFlag(flag);
        if (maxHit == 1 && outResults == nullptr)
            pxFlag |= physx::PxHitFlag::eMESH_ANY;
        else if (maxHit > 1)
        {
            pxFlag |= physx::PxHitFlag::eMESH_MULTIPLE;
            touchBuffer = std::make_unique<physx::PxRaycastHit[]>(maxHit);
        }

        physx::PxQueryFilterData filterData;
        filterData.flags |= physx::PxQueryFlag::ePREFILTER;
        if (flag == HitFlag::None)
            filterData.flags |= physx::PxQueryFlag::eANY_HIT;
        auto filterCallbcak = PhysXQueryFilterCallback(mask, ignoreTrigger, maxHit > 1 );

        physx::PxRaycastBuffer hit(touchBuffer.get(), (maxHit == 1 ? 0 : maxHit));
        if (mEnableRWLock)
            mPxScene->lockRead();
        bool result = mPxScene->raycast(CrossVec3ToPxVec3(origin), CrossVec3ToPxVec3(unitDir), maxDistance, hit, pxFlag, filterData, &filterCallbcak, /*(maxHit == 1 && mRayCastCache.shape ? &mRayCastCache : nullptr)*/ nullptr);
        if (mEnableRWLock)
            mPxScene->unlockRead();

        if (maxHit == 1 && result)
        {
            mRayCastCache.actor = hit.block.actor;
            mRayCastCache.shape = hit.block.shape;
            mRayCastCache.faceIndex = hit.block.faceIndex;
        }

        if (outResults)
        {
            if (maxHit == 1)
            {
                if (result)
                {
                    outResults->flags = PxHitFlagToCrossHitFlag(hit.block.flags);
                    outResults->position = PxVec3ToCrossVec3(hit.block.position);
                    outResults->uv = Float2A{ hit.block.u, hit.block.v };
                    outResults->normal = PxVec3ToCrossVec3(hit.block.normal);
                    outResults->distance = hit.block.distance;
                    PhysXActor* actor = static_cast<PhysXActor*>(hit.block.actor->userData);
                    outResults->hitActor = actor;
                    PhysXShape* shape = static_cast<PhysXShape*>(hit.block.shape->userData);
                    outResults->hitShape = shape;
                }
            }
            else
            {
                for (UInt32 i = 0; i < hit.getNbTouches(); ++i)
                {
                    outResults[i].flags = PxHitFlagToCrossHitFlag(hit.touches[i].flags);
                    outResults[i].position = PxVec3ToCrossVec3(hit.touches[i].position);
                    outResults[i].uv = Float2A{ hit.touches[i].u, hit.touches[i].v };
                    outResults[i].normal = PxVec3ToCrossVec3(hit.touches[i].normal);
                    outResults[i].distance = hit.touches[i].distance;
                    PhysXActor* actor = static_cast<PhysXActor*>(hit.touches[i].actor->userData);
                    outResults[i].hitActor = actor;
                    PhysXShape* shape = static_cast<PhysXShape*>(hit.touches[i].shape->userData);
                    outResults[i].hitShape = shape;
                }
            }
        }

        return hit.getNbAnyHits();
    }

    UInt32 PhysXScene::Sweep(PhysicsGeometryBase* geometry, const Float3& position, const Quaternion& rotation, const Float3& scale, 
        const Float3& unitDir, float maxDistance, CollisionMask mask,
        HitFlag flag, UInt32 maxHit, bool ignoreTrigger, PhysicsHitResult* outResults)
    {
        static const Quaternion sRotationZ = Quaternion::CreateFromAxisAngle(Float3(0.f, 0.f, 1.0f), MathUtils::MathPiDiv2);

        Assert(maxHit > 0);
        std::unique_ptr<physx::PxGeometry> pxGeo = GenerateSweepGeo(geometry, position, rotation, scale);
        std::unique_ptr<physx::PxSweepHit[]> touchBuffer = nullptr;

        Quaternion realQua{rotation};
        // Rotate 90 degrees around z, because px capsule is lying on ground
        if (geometry->GetGeometryType() == PhysicsGeometryBase::Type::Capsule)
        {
            realQua = sRotationZ * realQua;
        }

        physx::PxHitFlags pxFlag = CrossHitFlagToPxHitFlag(flag);
        if (maxHit == 1 && outResults == nullptr)
            pxFlag |= physx::PxHitFlag::eMESH_ANY;
        else if (maxHit > 1)
        {
            pxFlag |= physx::PxHitFlag::eMESH_MULTIPLE;
            touchBuffer = std::make_unique<physx::PxSweepHit[]>(maxHit);
        }

        physx::PxQueryFilterData filterData;
        filterData.flags |= physx::PxQueryFlag::ePREFILTER;
        if (flag == HitFlag::None)
            filterData.flags |= physx::PxQueryFlag::eANY_HIT;
        auto filterCallbcak = PhysXQueryFilterCallback(mask, ignoreTrigger, maxHit >1);
     
        physx::PxSweepBuffer hit(touchBuffer.get(), (maxHit == 1 ? 0 : maxHit));
        if (mEnableRWLock)
            mPxScene->lockRead();
        bool result = mPxScene->sweep(*pxGeo, physx::PxTransform{ CrossVec3ToPxVec3(position), CrossQuatToPxQuat(realQua) },
                                      CrossVec3ToPxVec3(unitDir),
                                      maxDistance,
                                      hit,
                                      pxFlag,
                                      filterData,
                                      &filterCallbcak,
            /*(maxHit == 1 && mSweepCache.shape ? &mSweepCache : nullptr)*/ nullptr);
        if (mEnableRWLock)
            mPxScene->unlockRead();

        if (maxHit == 1 && result)
        {
            mSweepCache.actor = hit.block.actor;
            mSweepCache.shape = hit.block.shape;
            mSweepCache.faceIndex = hit.block.faceIndex;
        }

        if (outResults)
        {
            if (maxHit == 1)
            {
                if (result)
                {
                    outResults->flags = PxHitFlagToCrossHitFlag(hit.block.flags);
                    outResults->position = PxVec3ToCrossVec3(hit.block.position);
                    outResults->normal = PxVec3ToCrossVec3(hit.block.normal);
                    outResults->distance = hit.block.distance;
                    PhysXActor* actor = static_cast<PhysXActor*>(hit.block.actor->userData);
                    outResults->hitActor = actor;
                    PhysXShape* shape = static_cast<PhysXShape*>(hit.block.shape->userData);
                    outResults->hitShape = shape;
                }
            }
            else
            {
                for (UInt32 i = 0; i < hit.getNbTouches(); ++i)
                {
                    outResults[i].flags = PxHitFlagToCrossHitFlag(hit.touches[i].flags);
                    outResults[i].position = PxVec3ToCrossVec3(hit.touches[i].position);
                    outResults[i].normal = PxVec3ToCrossVec3(hit.touches[i].normal);
                    outResults[i].distance = hit.touches[i].distance;
                    PhysXActor* actor = static_cast<PhysXActor*>(hit.touches[i].actor->userData);
                    outResults[i].hitActor = actor;
                    PhysXShape* shape = static_cast<PhysXShape*>(hit.touches[i].shape->userData);
                    outResults[i].hitShape = shape;
                }
            }
        }

        return hit.getNbAnyHits();
    }

    UInt32 PhysXScene::Overlap(PhysicsGeometryBase* geometry, const Float3& position, const Quaternion& rotation, const Float3& scale, CollisionMask mask, HitFlag flag, UInt32 maxHit, PhysicsHitResult* outResults)
    {
        static const Quaternion sRotationZ = Quaternion::CreateFromAxisAngle(Float3(0.f, 0.f, 1.0f), MathUtils::MathPiDiv2);

        std::unique_ptr<physx::PxGeometry> pxGeo = GenerateSweepGeo(geometry, position, rotation, scale);

        Quaternion realQua{rotation};
        // Rotate 90 degrees around z, because px capsule is lying on ground
        if (geometry->GetGeometryType() == PhysicsGeometryBase::Type::Capsule)
        {
            realQua = sRotationZ * realQua;
        }

        physx::PxQueryFilterData filterData;
        filterData.flags |= physx::PxQueryFlag::ePREFILTER;
        if (flag == HitFlag::None)
            filterData.flags |= physx::PxQueryFlag::eANY_HIT;
        auto filterCallbcak = PhysXQueryFilterCallback(mask, false, maxHit > 1);

        std::unique_ptr<physx::PxOverlapHit[]> touchBuffer = std::make_unique<physx::PxOverlapHit[]>(maxHit);
        physx::PxOverlapBuffer hit(touchBuffer.get(), (maxHit == 1 ? 0 : maxHit));

        if (mEnableRWLock)
            mPxScene->lockRead();
        bool result = mPxScene->overlap(*pxGeo, physx::PxTransform{CrossVec3ToPxVec3(position), CrossQuatToPxQuat(realQua)}, hit, filterData);
        if (mEnableRWLock)
            mPxScene->unlockRead();

        if (outResults && result)
        {
            for (UInt32 i = 0; i < hit.getNbTouches(); ++i)
            {
                PhysXActor* actor = static_cast<PhysXActor*>(hit.touches[i].actor->userData);
                outResults[i].hitActor = actor;
                PhysXShape* shape = static_cast<PhysXShape*>(hit.touches[i].shape->userData);
                outResults[i].hitShape = shape;
            }
        }
        return hit.getNbAnyHits();
    }

    void PhysXScene::SetGravity(const Float3& gravity)
    {
        mPxScene->setGravity(CrossVec3ToPxVec3(gravity));
    }

    void PhysXScene::SetDebugViewOption(const PhysicsSceneDebugViewOption& option)
    {
        mDebugViewOption = option;
        if (mEnableRWLock)
            LockWrite();
        mPxScene->setVisualizationParameter(physx::PxVisualizationParameter::eSCALE, option.Enabled() ? 1.0f : 0.0f);
        mPxScene->setVisualizationParameter(physx::PxVisualizationParameter::eCOLLISION_SHAPES, option.showCollision ? 1.0f : 0.0f);
        mPxScene->setVisualizationParameter(physx::PxVisualizationParameter::eJOINT_LOCAL_FRAMES, option.showJointFrame ? 8.0f : 0.0f);
        mPxScene->setVisualizationParameter(physx::PxVisualizationParameter::eJOINT_LIMITS, option.showJointLimit ? 8.0f : 0.0f);
        if (mEnableRWLock)
            UnlockWrite();
    }

    std::unique_ptr<PhysicsDebugVisualization> PhysXScene::RefreshDebugVisualization()
    {
        if (mDebugViewOption.Enabled())
            return std::make_unique<PhysXDebugVisualization>(mPxScene->getRenderBuffer());
        return nullptr;
    }

    //PhysXSceneWriteLocker PhysXScene::LockWriteWithScopeLocker()
    //{
    //    return PhysXSceneWriteLocker{this};
    //}
    void PhysXScene::SetUnderlay(physx::PxScene* scene)
    {
        mPxScene = scene;
        mPxScene->userData = this;
    }
    Double3 PhysXScene::GetOrigin() const
    {
        return mOrigin;
    }

    void PhysXScene::SetOrigin(const Double3& origin)
    {
        mOrigin = origin;
    }
    void PhysXScene::LockWrite() {
        mPxScene->lockWrite();
    }
    void PhysXScene::UnlockWrite() {
        mPxScene->unlockWrite();
    }
    void PhysXScene::LockRead() {
        mPxScene->lockRead();
    }
    void PhysXScene::UnlockRead() {
        mPxScene->unlockRead();
    }
    void PhysXScene::ShiftOriginAssumeSceneLocked(const Double3& origin)
    {
        using namespace physx;
        Double3 posOffset = origin - mOrigin;
        PxActorTypeFlags flags = PxActorTypeFlag::eRIGID_STATIC | PxActorTypeFlag::eRIGID_DYNAMIC;
        PxU32 actorNum = mPxScene->getNbActors(flags);
        std::vector<PxActor*> actors(actorNum);
        mPxScene->getActors(flags, actors.data(), actorNum);
        for (PxU32 i = 0; i < actorNum; i++)
        {
            PxRigidActor* pxActor = actors[i]->is<PxRigidActor>();
            PxTransform pose = pxActor->getGlobalPose();
            pose.p.x = static_cast<float>(pose.p.x + posOffset.x);
            pose.p.y = static_cast<float>(pose.p.y + posOffset.y);
            pose.p.z = static_cast<float>(pose.p.z + posOffset.z);
            pxActor->setGlobalPose(pose);
        }
        mOrigin = origin;
    }

    void PhysXScene::PushCollisionEnterCmd(PhysXActor* actor, std::shared_ptr<CollisionInfo>&& info)
    {
        Assert(actor && info);
        mCollisionEnterCmds.emplace_back(std::pair<PhysXActor*, std::shared_ptr<CollisionInfo>>{actor, std::move(info)});
    }
    void PhysXScene::PushCollisionStayCmd(PhysXActor* actor, std::shared_ptr<CollisionInfo>&& info)
    {
        Assert(actor && info);
        mCollisionStayCmds.emplace_back(std::pair<PhysXActor*, std::shared_ptr<CollisionInfo>>{actor, std::move(info)});
    }
    void PhysXScene::PushCollisionExitCmd(PhysXActor* actor, std::shared_ptr<CollisionInfo>&& info)
    {
        Assert(actor && info);
        mCollisionExitCmds.emplace_back(std::pair<PhysXActor*, std::shared_ptr<CollisionInfo>>{actor, std::move(info)});
    }
    void PhysXScene::ExecuteCollisionCmd()
    {
        for (auto [actor, info] : mCollisionEnterCmds)
        {
            actor->GetCollisionEnter()(std::move(info));
        }
        for (auto [actor, info] : mCollisionStayCmds)
        {
            actor->GetCollisionStay()(std::move(info));
        }
        for (auto [actor, info] : mCollisionExitCmds)
        {
            actor->GetCollisionExit()(std::move(info));
        }
        mCollisionEnterCmds.clear();
        mCollisionStayCmds.clear();
        mCollisionExitCmds.clear();
    }

    std::unique_ptr<physx::PxGeometry> PhysXScene::GenerateSweepGeo(PhysicsGeometryBase* geometry, const Float3& position, const Quaternion& rotation, const Float3& scale)
    {
        std::unique_ptr<physx::PxGeometry> pxGeo = nullptr;

        switch (geometry->GetGeometryType())
        {
        case PhysicsGeometryBase::Type::Box:
        {
            PhysicsGeometryBox* box = static_cast<PhysicsGeometryBox*>(geometry);
            pxGeo = std::make_unique<physx::PxBoxGeometry>(CrossVec3ToPxVec3(box->halfExtents * scale));
            break;
        }
        case PhysicsGeometryBase::Type::Sphere:
        {
            PhysicsGeometrySphere* sphere = static_cast<PhysicsGeometrySphere*>(geometry);
            Assert(FloatEqual(scale.x, scale.y, 0.00001f) && FloatEqual(scale.y, scale.z, 0.00001f));
            pxGeo = std::make_unique<physx::PxSphereGeometry>(sphere->radius * scale.x);
            break;
        }
        case PhysicsGeometryBase::Type::Capsule:
        {
            PhysicsGeometryCapsule* capsule = static_cast<PhysicsGeometryCapsule*>(geometry);
            Assert(FloatEqual(scale.z, scale.x, 0.00001f));
            pxGeo = std::make_unique<physx::PxCapsuleGeometry>(capsule->radius * scale.x, capsule->halfHeight * scale.y);
            break;
        }
        case PhysicsGeometryBase::Type::Convex:
        {
            PhysicsGeometryConvex* convex = static_cast<PhysicsGeometryConvex*>(geometry);
            PhysXConvexMesh* mesh = static_cast<PhysXConvexMesh*>(convex->mesh.get());
            pxGeo = std::make_unique<physx::PxConvexMeshGeometry>(mesh->GetUnderlay(), physx::PxMeshScale{CrossVec3ToPxVec3(scale)});
            break;
        }
        case PhysicsGeometryBase::Type::Mesh:
        case PhysicsGeometryBase::Type::Plane:
        default:
            Assert(false);
        }
        return pxGeo;
    }
    PhysXQueryFilterCallback::PhysXQueryFilterCallback(CollisionMask mask, bool TriggerIgnore, bool hitBuffers)
    {
        mTriggerIgnore = TriggerIgnore;
        mMask = mask;
        mHitBuffers = hitBuffers;
    }

    physx::PxQueryHitType::Enum PhysXQueryFilterCallback::preFilter(const physx::PxFilterData& filterData, const physx::PxShape* shape, const physx::PxRigidActor* actor, physx::PxHitFlags& queryFlags)
    {
        const auto filterData0 = shape->getSimulationFilterData();
        auto [isTrigger0, collisionType0, collisionMask0] = DecodeCollisionWord(filterData0.word1);
        if (!mMask.IsSet(collisionType0))
        {
            return physx::PxQueryHitType::eNONE;
        }
        if (mTriggerIgnore)
        {
            if (isTrigger0)
            {
                return physx::PxQueryHitType::eNONE;
            }
        }
        return mHitBuffers? physx::PxQueryHitType::eTOUCH : physx::PxQueryHitType::eBLOCK;
    }
    physx::PxQueryHitType::Enum PhysXQueryFilterCallback::postFilter(const physx::PxFilterData& filterData, const physx::PxQueryHit& hit) {
        return physx::PxQueryHitType::eBLOCK;
    }
}
