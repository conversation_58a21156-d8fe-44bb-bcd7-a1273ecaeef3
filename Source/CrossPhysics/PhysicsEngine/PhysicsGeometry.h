#pragma once
#include "CrossPhysics.h"
#include "Math/CrossMath.h"
#include "CrossBase/Serialization/SerializeNode.h"

namespace cross
{
    struct CrossPhysics_API PhysicsConvexMesh
    {
    public:
        virtual ~PhysicsConvexMesh() = default;
    };
    struct CrossPhysics_API PhysicsTriangleMesh
    {
    public:
        virtual ~PhysicsTriangleMesh() = default;
    };
    struct CrossPhysics_API PhysicsTerrainHeightMap
    {
    public:
        virtual ~PhysicsTerrainHeightMap() = default;
    };
    struct CrossPhysics_API PhysicsGeometryBase
    {
        enum class CEMeta(Editor) Type
        {
            Box,
            Sphere,
            Capsule,
            Plane,
            Convex,
            Mesh,
            Terrain
        };

        CE_Serialize_Deserialize;

        CEFunction(Editor)
        virtual Type GetGeometryType() const = 0;

        virtual ~PhysicsGeometryBase() = default;
    };

    struct CrossPhysics_API PhysicsGeometryBox : public PhysicsGeometryBase
    {
        CEFunction(Editor, Script)
        PhysicsGeometryBox() = default;
        CEFunction(Editor, Script)
        PhysicsGeometryBox(const Float3& position, const Quaternion& rotate, const Float3& halfExtents)
            :position(position), rotate(rotate), halfExtents(halfExtents){}
        CEFunction(Editor, Script)
        Type GetGeometryType() const override { return Type::Box; }

        CEProperty(Editor, Script)
        Float3 position;
        CEProperty(Editor, Script)
        Quaternion rotate  = Quaternion::Identity();
        CEProperty(Editor, Script)
        Float3 halfExtents;

        CE_Serialize_Deserialize;
    };

    struct CrossPhysics_API PhysicsGeometrySphere : public PhysicsGeometryBase
    {
        CEFunction(Editor, Script)
        PhysicsGeometrySphere() = default;
        CEFunction(Editor, Script)
        PhysicsGeometrySphere(const Float3& position, float radius) :position(position), radius(radius) {}
        CEFunction(Editor, Script)
        Type GetGeometryType() const override { return Type::Sphere; }

        CEProperty(Editor, Script)
        Float3 position;
        CEProperty(Editor, Script)
        float radius;

        CE_Serialize_Deserialize;
    };

    struct CrossPhysics_API PhysicsGeometryCapsule : public PhysicsGeometryBase
    {
        CEFunction(Editor, Script)
        PhysicsGeometryCapsule() = default;
        CEFunction(Editor, Script)
        PhysicsGeometryCapsule(const Float3& position, const Quaternion& rotate, float radius, float halfHeight)
            :position(position), rotate(rotate), radius(radius), halfHeight(halfHeight) {}
        CEFunction(Editor, Script)
        Type GetGeometryType() const override { return Type::Capsule; }

        CEProperty(Editor, Script)
        Float3 position;
        CEProperty(Editor, Script)
        Quaternion rotate = Quaternion::Identity();
        CEProperty(Editor, Script)
        float radius;
        CEProperty(Editor, Script)
        float halfHeight;

        CE_Serialize_Deserialize;
    };

    struct CrossPhysics_API PhysicsGeometryPlane : public PhysicsGeometryBase
    {
        Type GetGeometryType() const override { return Type::Plane; }
    };

    struct CrossPhysics_API PhysicsGeometryConvex : public PhysicsGeometryBase
    {
        PhysicsGeometryConvex(const Float3& position, const Quaternion& rotate, const std::shared_ptr<PhysicsConvexMesh>& mesh)
            : position(position), rotate(rotate), mesh(mesh) {}
        Type GetGeometryType() const override { return Type::Convex; }
        Float3 position;
        Quaternion rotate = Quaternion::Identity();
        std::shared_ptr<PhysicsConvexMesh> mesh;
    };

    struct CrossPhysics_API PhysicsGeometryMesh : public PhysicsGeometryBase
    {
        PhysicsGeometryMesh(const Float3& position, const Quaternion& rotate, const std::shared_ptr<PhysicsTriangleMesh>& mesh)
            : position(position), rotate(rotate), mesh(mesh) {}
        Type GetGeometryType() const override { return Type::Mesh; }
        Float3 position;
        Quaternion rotate = Quaternion::Identity();
        std::shared_ptr<PhysicsTriangleMesh> mesh;
    };
    struct CrossPhysics_API PhysicsGeometryTerrain : public PhysicsGeometryBase
    {
        PhysicsGeometryTerrain(const std::shared_ptr<PhysicsTerrainHeightMap>& heightMap, UInt32 offsetx, UInt32 offsetz, UInt32 levelScale)
            : heightMap(heightMap)
            , offsetX(offsetx)
            , offsetZ(offsetz)
            , levelScale(levelScale)
        {}
        PhysicsGeometryTerrain()
            : heightMap(nullptr)
            , offsetX(0)
            , offsetZ(0)
            , levelScale(1)
        {}
        Type GetGeometryType() const override { return Type::Terrain; }
        UInt32 offsetX;
        UInt32 offsetZ;
        UInt32 levelScale;
        std::shared_ptr<PhysicsTerrainHeightMap> heightMap;
    };
    struct CrossPhysics_API PhysicsCollision
    {
        std::vector<PhysicsGeometryBox> mBoxGeometry;
        std::vector<PhysicsGeometrySphere> mSphereGeometry;
        std::vector<PhysicsGeometryCapsule> mCapsuleGeometry;
        std::vector<PhysicsGeometryPlane> mPlaneGeometry;
        std::vector<PhysicsGeometryConvex> mConvexGeometry;
        std::vector<PhysicsGeometryMesh> mMeshGeometry;

        void Clear();
        bool Empty() const;
        void Append(const PhysicsCollision& other);
    };

    struct CrossPhysics_API PhysicsSimpleCollision
    {
        CEProperty(Editor, Script)
        CECSAttribute(PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "BoxGeometry"))
        std::vector<PhysicsGeometryBox> mBoxGeometry;
        CEProperty(Editor, Script)
        CECSAttribute(PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "SphereGeometry"))
        std::vector<PhysicsGeometrySphere> mSphereGeometry;
        CEProperty(Editor, Script)
        CECSAttribute(PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "CapsuleGeometry"))
        std::vector<PhysicsGeometryCapsule> mCapsuleGeometry;

        std::vector<PhysicsGeometryConvex> mConvexGeometry;

        CE_Serialize_Deserialize;


        void Clear()
        {
            mBoxGeometry.clear();
            mSphereGeometry.clear();
            mCapsuleGeometry.clear();
            mConvexGeometry.clear();
        }
        CEFunction(AdditionalSerialize)
        void AdditionalSerialize(SerializeNode& inNode, SerializeContext& context) const;
        CEFunction(AdditionalDeserialize)
        void AdditionalDeserialize(const DeserializeNode& inNode, SerializeContext& context);
    };
}
