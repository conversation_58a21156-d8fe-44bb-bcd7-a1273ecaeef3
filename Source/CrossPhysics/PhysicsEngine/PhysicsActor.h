#pragma once
#include "CrossPhysics.h"
#include "PhysicsEngine/PhysicsGeometry.h"
#include "PhysicsEngine/PhysicsMaterial.h"
#include "PhysicsEngine/PhysicsShape.h"

namespace cross
{
    class PhysicsActor;
    class PhysicsScene; 
    enum class RigidDynamicFlag
    {
        Kinematic = (1 << 0),// Enable kinematic mode for the body.
        UseKinematicTargetForSceneQueries = (1 << 1),
    };

    enum class CollisionFlag
    {
        SOLVECONTACT = (1 << 0),
        MODIFYCONTACTS = (1 << 1),
        NOTIFYTOUCHFOUND = (1 << 2),
        NOTIFYTOUCHPERSISTS = (1 << 3),
        NOTIFYTOUCHLOST = (1 << 4),
        NOTIFYTOUCHCCD = (1 << 5),
        NOTIFYTHRESHOLDFORCEFOUND = (1 << 6),
        NOTIFYTHRESHOLDFORCEPERSISTS = (1 << 7),
        NOTIFYTHRESHOLDFORCELOST = (1 << 8),
        NOTIFYCONTACTPOINTS = (1 << 9),
        DETECTDISCRETECONTACT = (1 << 10),
        DETECTCCDCONTACT = (1 << 11),
        PRESOLVERVELOCITY = (1 << 12),
        POSTSOLVERVELOCITY = (1 << 13),
        CONTACTEVENTPOSE = (1 << 14),
        NEXTFREE = (1 << 15), 
        CONTACTDEFAULT = SOLVECONTACT | DETECTDISCRETECONTACT,
        TRIGGER_DEFAULT = NOTIFYTOUCHFOUND | NOTIFYTOUCHLOST | DETECTDISCRETECONTACT,
        Default
    };

    struct ContactPoint
    {
        Float3A contactPoint; //The position of the contact point between the shapes, in world space. 
        Float3A normal; //The normal of the contacting surfaces at the contact point. The normal direction points from other shape to self shape.
        Float3A impulse; //The impulse applied at the contact point, in world space. Divide by the simulation time step to get a force value.
    };
    struct CollisionInfo
    {
        PhysicsActor* self = nullptr;
        PhysicsActor* other = nullptr;

        CollisionFlag status = CollisionFlag::Default;
        const std::shared_ptr<const std::vector<ContactPoint>> contactPoints = nullptr; //Only available if you set reportCollisionPoint to true when calling SetOnCollision
    };
    using CollisionCallBack = std::function<void(std::shared_ptr<CollisionInfo>&&)>;

    struct PhysicsActorData
    {
        virtual ~PhysicsActorData() = default;
    };

    class CrossPhysics_API PhysicsActor
    {
    public:
        virtual ~PhysicsActor() = default;

        template<typename T>
        T* GetCustomData() const { return static_cast<T*>(mCustomData.get()); }
        template<typename T>
        void SetCustomData(std::unique_ptr<T>&& customData) { mCustomData = std::move(customData); }

        virtual bool IsDynamicActor() const = 0;

        virtual PhysicsShape* AddTriangleMeshShape(const PhysicsGeometryMesh& meshGeo, PhysicsMaterial* material) = 0;
        virtual PhysicsShape* AddConvexMeshShape(const PhysicsGeometryConvex& convexGeo, PhysicsMaterial* material) = 0;
        virtual PhysicsShape* AddBoxShape(const PhysicsGeometryBox& boxGeo, PhysicsMaterial* material) = 0;
        virtual PhysicsShape* AddSphereShape(const PhysicsGeometrySphere& sphereGeo, PhysicsMaterial* material) = 0;
        virtual PhysicsShape* AddCapsuleShape(const PhysicsGeometryCapsule& capsuleGeo, PhysicsMaterial* material) = 0;
        virtual PhysicsShape* AddPlaneShape(const PhysicsGeometryPlane& planeGeo, PhysicsMaterial* material) = 0;
        virtual PhysicsShape* AddTerrainShape(const PhysicsGeometryTerrain& terrainGeo, PhysicsMaterial* material) = 0;
        virtual void SetScene(PhysicsScene* scene)=0;
        virtual size_t GetShapeCount() const = 0;
        virtual PhysicsShape* GetShape(size_t index) = 0;
        virtual const PhysicsShape* GetShape(size_t index) const = 0;

        virtual void ClearShape(PhysicsShape* shape) = 0;
        virtual void ClearAllShapes() = 0;

        virtual void SetEnableGravity(bool enable) = 0;

        virtual void SetOnlyUsedForSceneQuery(bool onlyForSceneQuery) = 0;
        virtual bool GetOnlyUsedForSceneQuery() const = 0;

        virtual void SetDebugName(const char* name) = 0;
        virtual std::string GetDebugName() = 0;

        virtual void SetTransform(const Float3& pos, const Quaternion& rotation) = 0;
        virtual void SetTransformAssumeSceneLocked(const Float3& pos, const Quaternion& rotation) = 0;
        virtual std::pair<Float3, Quaternion> GetTransform() const = 0;

        virtual void SetScale(const Float3A& scale) = 0;
        virtual const Float3& GetScale() const = 0;

        //dynamic rigid
        virtual float GetLinearDamping() const = 0;
        virtual void SetLinearDamping(float linearDamping) = 0;

        virtual void SetIsKinematic(bool isKinematic, bool wakeUp = true) = 0; // It can only be moved by setting transform. It can hit other objects, and can receive callbacks.
        virtual bool GetIsKinematic() const = 0;

        virtual void SetIsTrigger(bool isTrigger) = 0; // It can only be moved by setting transform. It can not hit other objects, but can receive callbacks.
        virtual bool GetIsTrigger() const = 0;

        virtual void SetKinematicTarget(const Float3A& position, const Quaternion& rotation) = 0;
        virtual void SetKinematicTargetAssumeSceneLocked(const Float3A& position, const Quaternion& rotation) = 0;

        virtual void SetMass(float mass) = 0;
        virtual float GetMass() const = 0;

        virtual Float3A GetMassSpaceInertiaTensor() const = 0;;
        virtual void SetMassSpaceInertiaTensor(const Float3A& tensor) = 0;

        virtual void UpdateMassAndInertia(float density, const Float3A& massLocalPose = {0, 0, 0}, bool includeNonSimShapes = false) = 0;
        virtual void SetMassAndUpdateInertia(float mass, const Float3A& massLocalPose = {0, 0, 0}, bool includeNonSimShapes = false) = 0;

        virtual void SetRigidDynamicFlags(RigidDynamicFlag flags, bool value) = 0;

        virtual void AddForce(const Float3A& force) = 0;
        virtual void ClearForce() = 0;

        virtual void AddImpulse(const Float3A& impulse) = 0;
        virtual void ClearImpulse() = 0;

        virtual void AddLinearVelocity(const Float3A& velocity) = 0;
        virtual void ClearLinearVelocity() = 0;

        virtual void AddLinearAcceleration(const Float3A& acceleration) = 0;
        virtual void ClearLinearAcceleration() = 0;

        virtual void AddTorque(const Float3& torque) = 0;
        virtual void ClearTorque() = 0;

        virtual void AddImpulseTorque(const Float3& impulse) = 0;
        virtual void ClearImpulseTorque() = 0;

        virtual void AddAngularVelocity(const Float3& velocity) = 0;
        virtual void ClearAngularVelocity() = 0;

        virtual void AddAngularAcceleration(const Float3& acceleration) = 0;
        virtual void ClearAngularAcceleration() = 0;

        virtual Float3 GetAngularVelocity() const = 0;
        virtual Float3 GetLinearVelocity() const = 0;

        virtual void SetMaxDepenetrationVelocity(float velocity) = 0;
        virtual float GetMaxDepenetrationVelocity() const = 0;

        //reportContactPoints: Expensive. When set to true, contactPoints will be filled when calling callback
        virtual void SetCollisionEnter(CollisionCallBack callback, bool reportContactPoints = false) = 0;
        virtual void SetCollisionStay(CollisionCallBack callback, bool reportCollisionPoint = false) = 0;
        virtual void SetCollisionExit(CollisionCallBack callback, bool reportContactPoints = false) = 0;

        virtual CollisionTypeValue GetCollisionType() const { return mCollisionType; }
        virtual void SetCollisionType(CollisionTypeValue type) = 0;

        virtual CollisionMask GetCollisionMask() const { return mCollisionMask; }
        virtual void SetCollisionMask(CollisionMask mask) = 0;

        virtual void PutToSleep() = 0;
    protected:
        std::unique_ptr<PhysicsActorData> mCustomData;

        CollisionTypeValue mCollisionType{};
        CollisionMask mCollisionMask = CollisionMask::None();
    };

}
