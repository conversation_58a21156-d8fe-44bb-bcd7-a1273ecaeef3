#pragma once
#include "PhysicsEngine/CrossPhysics.h"
#include "CECommon/Common/FrameContainer.h"
#include "CrossBase/Math/CrossMath.h"
#include "CrossBase/Template/EnumClassFlags.h"
#include "PhysicsEngine/PhysicsShape.h"
#include "PhysicsEngine/PhysicsGeometry.h"
#include "PhysicsEngine/PhysicsActor.h"


namespace cross
{
    enum class HitFlag
    {
        None = 0,
        Position = (1 << 0),   //!< "position" member of #PxQueryHit is valid
        Normal = (1 << 1),     //!< "normal" member of #PxQueryHit is valid
        UV = (1 << 3),         //!< "u" and "v" barycentric coordinates of #PxQueryHit are valid. Not applicable to sweep queries.
        Default = Position | Normal,

        AssumeNoInitialOverlap = (1 << 4),   //!< Performance hint flag for sweeps when it is known upfront there's no initial overlap.
                                             //!< NOTE: using this flag may cause undefined results if shapes are initially overlapping.
        MeshBothSides = (1 << 7),            //!< Report hits with back faces of mesh triangles. Also report hits for raycast
                                             //!< originating on mesh surface and facing away from the surface normal. Not applicable to sweep queries.
                                             //!< Please refer to the user guide for heightfield-specific differences.

        MTD = (1 << 9),   //!< Report the minimum translation depth, normal and contact point.
    };
    ENUM_CLASS_FLAGS(HitFlag)

    struct CrossPhysics_API PhysicsHitResult
    {
        HitFlag flags = HitFlag::None;   //!< Hit flags specifying which members contain valid values.
        Float3 position;
        Float3A normal;
        Float2A uv;
        float distance = 0;   // distance to hit

        PhysicsActor* hitActor = nullptr;
        PhysicsShape* hitShape = nullptr;
        #ifdef CE_USE_DOUBLE_TRANSFORM 
        Double3 position_D;
        #endif
    };

    struct PhysicsSceneDebugViewOption
    {
        CEMeta(Editor)
        PhysicsSceneDebugViewOption() = default;
        CEMeta(Editor)
        PhysicsSceneDebugViewOption(bool showCollision, bool showJointFrame, bool showJointLimit)
            : showCollision(showCollision)
            , showJointFrame(showJointFrame)
            , showJointLimit(showJointLimit)
        {}
        CEMeta(Editor)
        bool showCollision  = false;
        CEMeta(Editor)
        bool showJointFrame = false;
        CEMeta(Editor)
        bool showJointLimit = false;

        CEMeta(Editor)
        bool Enabled() const
        {
            return showCollision || showJointFrame || showJointLimit;
        }
    };

    struct PhysicsHitResultDebug
    {
        CEProperty(Editor)
        double distance = 0;
        CEProperty(Editor)
        Double3 position;

        CEProperty(Editor)
        std::string entityName = "";

        CEProperty(Editor)
        std::string debugMsg;
    };

    struct CrossPhysics_API PhysicsDebugVisualization
    {
        virtual ~PhysicsDebugVisualization() = default;

        struct Point
        {
            Float3 pos;
            UInt32 color;
        };
        virtual const UInt32 GetNbPoints() = 0;
        virtual const Point* GetPoints() const = 0;

        struct Line
        {
            Point p0;
            Point p1;
        };
        virtual const UInt32 GetNbLines() = 0;
        virtual const Line* GetLines() const = 0;

        struct Triangle
        {
            Point p0;
            Point p1;
            Point p2;
        };
        virtual const UInt32 GetNbTriangles() = 0;
        virtual const Triangle* GetTriangles() const = 0;
    };

    class CrossPhysics_API PhysicsScene
    {
    public:
        virtual ~PhysicsScene() = default;
        virtual void AddActor(PhysicsActor* rigidStatic) = 0;
        virtual void RemoveActor(PhysicsActor* actor) = 0;
        virtual void TraverseActiveActors(const std::function<void(PhysicsActor*)>& functor) = 0;
        virtual void TraverseAllActors(const std::function<void(PhysicsActor*)>& functor) = 0;

        virtual void Simulate(float step) = 0;
        virtual void FetchResult() = 0;
        virtual void BeforeUpdate() = 0;

        // Cast a ray in the scene, the ray can hit for maxHit times.
        // outResults points to an array of PhysicsRayCastResult, and its size should not smaller than maxHit.
        // If outResults is nullptr and maxHit is 1, RayCast only test whether the ray can hit something or not. It's performance friendly.
        // return actual hit number
        virtual UInt32 RayCast(const Float3& origin, const Float3& unitDir, float maxDistance, CollisionMask mask, HitFlag flag = HitFlag::Default, UInt32 maxHit = 1, bool ignoreTrigger = false,
                               PhysicsHitResult* outResults = nullptr) = 0;

        // Sweep a geometry in the scene, can hit for maxHit times.
        // Similar to RayCast
        virtual UInt32 Sweep(PhysicsGeometryBase* geometry, const Float3& position, const Quaternion& rotation, const Float3& scale, const Float3& unitDir, float maxDistance, CollisionMask mask, HitFlag flag = HitFlag::Default,
                             UInt32 maxHit = 1, bool ignoreTrigger = false,PhysicsHitResult* outResults = nullptr) = 0;

        // Query searches a region enclosed by a specified shape for any overlapping objects in the scene. The region is
        // specified as a transformed box, sphere, capsule or convex geometry.
        virtual UInt32 Overlap(PhysicsGeometryBase* geometry, const Float3& position, const Quaternion& rotation, const Float3& scale, CollisionMask mask, HitFlag flag = HitFlag::Default, UInt32 maxHit = 1,
                               PhysicsHitResult* outResults = nullptr) = 0;


        virtual void SetGravity(const Float3& gravity) = 0;

        virtual void SetDebugViewOption(const PhysicsSceneDebugViewOption& option) = 0;

        virtual Double3 GetOrigin() const = 0;
        virtual void SetOrigin(const Double3& origin) = 0;

        virtual void LockWrite() = 0;
        virtual void UnlockWrite() = 0;
        virtual void LockRead() = 0;
        virtual void UnlockRead() = 0;
    public:
        virtual std::unique_ptr<PhysicsDebugVisualization> RefreshDebugVisualization() = 0;
        bool GetEnableRWLock() const;
        //physx::PxScene* GetUnderlay() const;
        //void SetUnderlay(physx::PxScene* scene);
    protected:
        bool mEnableRWLock = false;
        //
    };

}
