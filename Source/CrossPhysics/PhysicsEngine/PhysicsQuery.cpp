#include "EnginePrefix.h"

#include "PhysicsQuery.h"

#include "PhysicsEngine/PhysXImpl/PhysXUtility.h"
#include "PhysicsEngine/PhysXImpl/PhysXActor.h"
#include "PhysicsEngine/PhysXImpl/PhysXGeometry.h"
#include "PhysicsEngine/PhysXImpl/PhysXEngineImpl.h"
#include "PhysicsEngine/PhysXImpl/PhysXCpuDispatcher.h"

namespace cross {

template<typename RayCastResult>
    requires std::is_same_v<RayCastResult, PhysicsHitResult>
static void ConvertPxHitResultToCross(const physx::PxRaycastHit& pxHit, RayCastResult& outHit, const Double3& origin)
{
    outHit.flags = PxHitFlagToCrossHitFlag(pxHit.flags);
    if constexpr (std::is_same_v<RayCastResult, PhysicsHitResult>)
        outHit.position = Float3A(PxVec3ToCrossDouble3(pxHit.position) + origin);
    else
        outHit.position = PxVec3ToCrossDouble3(pxHit.position) + origin;
    outHit.uv = Float2{pxHit.u, pxHit.v};
    outHit.normal = PxVec3ToCrossVec3(pxHit.normal);
    outHit.distance = pxHit.distance;
    outHit.hitActor = static_cast<PhysXActor*>(pxHit.actor->userData);
    outHit.hitShape = static_cast<PhysXShape*>(pxHit.shape->userData);
}

template<typename RayCastResult>
    requires std::is_same_v<RayCastResult, PhysicsHitResult>
static void ConvertPxHitResultToCross(const physx::PxSweepHit& pxHit, RayCastResult& outHit, const Double3& origin)
{
    outHit.flags = PxHitFlagToCrossHitFlag(pxHit.flags);
    if constexpr (std::is_same_v<RayCastResult, PhysicsHitResult>)
        outHit.position = Float3A(PxVec3ToCrossDouble3(pxHit.position) + origin);
    else
        outHit.position = PxVec3ToCrossDouble3(pxHit.position) + origin;
    outHit.normal = PxVec3ToCrossVec3(pxHit.normal);
    outHit.distance = pxHit.distance;
    outHit.hitActor = static_cast<PhysXActor*>(pxHit.actor->userData);
    outHit.hitShape = static_cast<PhysXShape*>(pxHit.shape->userData);
}

template<typename RayCastResult>
    requires std::is_same_v<RayCastResult, PhysicsHitResult>
static void ConvertPxHitResultToCross(const physx::PxOverlapHit& pxHit, RayCastResult& outHit)
{
    outHit.hitActor = static_cast<PhysXActor*>(pxHit.actor->userData);
    outHit.hitShape = static_cast<PhysXShape*>(pxHit.shape->userData);
}

static std::unique_ptr<physx::PxGeometry> GeneratePhysXGeo(const PhysicsGeometryBase* geometry, const Float3& scale)
{
    std::unique_ptr<physx::PxGeometry> pxGeo = nullptr;

    switch (geometry->GetGeometryType())
    {
    case PhysicsGeometryBase::Type::Box:
    {
        auto* box = static_cast<const PhysicsGeometryBox*>(geometry);
        pxGeo = std::make_unique<physx::PxBoxGeometry>(CrossVec3ToPxVec3(box->halfExtents * scale));
        break;
    }
    case PhysicsGeometryBase::Type::Sphere:
    {
        auto* sphere = static_cast<const PhysicsGeometrySphere*>(geometry);
        Assert(FloatEqual(scale.x, scale.y, 0.00001f) && FloatEqual(scale.y, scale.z, 0.00001f));
        pxGeo = std::make_unique<physx::PxSphereGeometry>(sphere->radius * scale.x);
        break;
    }
    case PhysicsGeometryBase::Type::Capsule:
    {
        auto* capsule = static_cast<const PhysicsGeometryCapsule*>(geometry);
        Assert(FloatEqual(scale.z, scale.x, 0.00001f));
        pxGeo = std::make_unique<physx::PxCapsuleGeometry>(capsule->radius * scale.x, capsule->halfHeight * scale.y);
        break;
    }
    case PhysicsGeometryBase::Type::Convex:
    {
        auto* convex = static_cast<const PhysicsGeometryConvex*>(geometry);
        PhysXConvexMesh* mesh = static_cast<PhysXConvexMesh*>(convex->mesh.get());
        pxGeo = std::make_unique<physx::PxConvexMeshGeometry>(mesh->GetUnderlay(), physx::PxMeshScale{CrossVec3ToPxVec3(scale)});
        break;
    }
    case PhysicsGeometryBase::Type::Mesh:
    case PhysicsGeometryBase::Type::Plane:
    default:
        Assert(false);
    }
    return pxGeo;
}

static UInt32 RaycastInternal(PhysicsScene* scene, const Float3& origin, const Float3& unitDir, float maxDistance, CollisionMask blockMask, PhysicsHitResult* hitResult, HitFlag flags = HitFlag::Default, UInt32 maxHit = 1u,
                              bool ignoreTrigger = false)
{
    UInt32 result = scene->RayCast(origin, unitDir, maxDistance, blockMask, flags, maxHit, ignoreTrigger, hitResult);
    return result;
}

static bool SweepInternal(PhysicsScene* scene, PhysicsGeometryBase& geometry, const Float3& origin, const Quaternion& rotation, const Float3& scale, const Float3& unitDir, float maxDistance,CollisionMask blockMask,
                          PhysicsHitResult* hitResult, HitFlag flags = HitFlag::Default, UInt32 maxHit = 1u, bool ignoreTrigger = false)
{
    //static const Quaternion sRotationZ = Quaternion::CreateFromAxisAngle(Float3(0.f, 0.f, 1.0f), MathUtils::MathPiDiv2);
    //Quaternion realQua{rotation};
    //// Rotate 90 degrees around z, because px capsule is lying on ground
    //if (geometry.GetGeometryType() == PhysicsGeometryBase::Type::Capsule)
    //    realQua = sRotationZ * rotation;
    //auto pxGeo = GeneratePhysXGeo(&geometry, scale);
    //physx::PxTransform trans = physx::PxTransform(CrossVec3ToPxVec3(origin), CrossQuatToPxQuat(realQua));
    bool result = scene->Sweep(&geometry, origin, rotation, scale, unitDir, maxDistance, blockMask, flags, maxHit, ignoreTrigger, hitResult);

    return result;
}

static bool OverlapInternal(PhysicsScene* scene, PhysicsGeometryBase& geometry, const Float3& origin, const Quaternion& rotation, const Float3& scale, CollisionMask blockMask, PhysicsHitResult* hitResult, HitFlag flags = HitFlag::Default,
                            UInt32 maxHit = 1u)
{

    //static const Quaternion sRotationZ = Quaternion::CreateFromAxisAngle(Float3(0.f, 0.f, 1.0f), MathUtils::MathPiDiv2);
    //Quaternion realQua{rotation};
    //// Rotate 90 degrees around z, because px capsule is lying on ground
    //if (geometry.GetGeometryType() == PhysicsGeometryBase::Type::Capsule)
    //    realQua = sRotationZ * rotation;
    //auto pxGeo = GeneratePhysXGeo(&geometry, scale);
    //physx::PxTransform trans = physx::PxTransform(CrossVec3ToPxVec3(origin), CrossQuatToPxQuat(realQua));
    bool result = scene->Overlap(&geometry, origin, rotation, scale, blockMask, flags, maxHit, hitResult);

    return result;
}

PhysicsQuery::PhysicsQuery(PhysicsScene* scene)
    : mScene(scene)
{}

void PhysicsQuery::SetPhysicsScene(PhysicsScene* scene)
{
    mScene = scene;
}



bool PhysicsQuery::RayCastAny(const Float3& origin, const Float3& unitDir, float maxDistance, CollisionMask blockMask) const
{
    //physx::PxQueryFilterData filterData;
    //filterData.flags = physx::PxQueryFlag::eSTATIC | physx::PxQueryFlag::eDYNAMIC | physx::PxQueryFlag::eANY_HIT;
    //filterData.data.word1 = EncodeCollisionWord(false, CollisionTypeValue::NoCollision, blockMask);

    bool hasAnyHit = RaycastInternal(mScene, origin, unitDir, maxDistance, blockMask, nullptr, HitFlag::None);
    return hasAnyHit;
}

bool PhysicsQuery::RayCast(const Float3& origin, const Float3& unitDir, float maxDistance, CollisionMask blockMask, PhysicsHitResult& hitResult) const
{
    UInt32 hasAnyHit = RaycastInternal(mScene, origin, unitDir, maxDistance, blockMask, &hitResult);
    return hasAnyHit > 0;
}
#ifdef CE_USE_DOUBLE_TRANSFORM
bool PhysicsQuery::RayCast(const Double3& origin, const Float3& unitDir, float maxDistance, CollisionMask blockMask, PhysicsHitResult& hitResult) const
{
    Double3 sceneOrigin = mScene->GetOrigin();
    Float3 o = Float3(origin - sceneOrigin);

    UInt32 hasAnyHit = RaycastInternal(mScene, o, unitDir, maxDistance, blockMask, &hitResult);

    if (hasAnyHit>0)
    {
        hitResult.position_D = Double3(hitResult.position) + sceneOrigin;
    }
    return hasAnyHit>0;
}
#endif
UInt32 PhysicsQuery::RayCastMulti(const Float3& origin, const Float3& unitDir, float maxDistance, CollisionMask blockMask, PhysicsHitResult* hitResults, UInt32 maxHit) const
{
    UInt32 hitCount = RaycastInternal(mScene, origin, unitDir, maxDistance, blockMask, hitResults, HitFlag::Default, maxHit);

    return hitCount;
}
#ifdef CE_USE_DOUBLE_TRANSFORM
UInt32 PhysicsQuery::RayCastMulti(const Double3& origin, const Float3& unitDir, float maxDistance, CollisionMask blockMask, PhysicsHitResult* hitResults, UInt32 maxHit) const
{
    Double3 sceneOrigin = mScene->GetOrigin();
    Float3 o = Float3(origin - sceneOrigin);

    UInt32 hitCount = RaycastInternal(mScene, o, unitDir, maxDistance, blockMask, hitResults, HitFlag::Default, maxHit);

    for (UInt32 i = 0; i < hitCount; i++)
    {
        hitResults[i].position_D = Double3(hitResults[i].position) + sceneOrigin;
    }
    
    return hitCount;
}
#endif
bool PhysicsQuery::SweepAny(PhysicsGeometryBase& geo, const Transform& transform, const Float3& unitDir, float maxDistance, CollisionMask blockMask) const
{
    //physx::PxQueryFilterData filterData;
    //filterData.flags = physx::PxQueryFlag::eSTATIC | physx::PxQueryFlag::eDYNAMIC | physx::PxQueryFlag::eANY_HIT;
    //filterData.data.word1 = EncodeCollisionWord(false, CollisionTypeValue::NoCollision, blockMask);

    //HitFlag hitFlags = HitFlag::None;
    //physx::PxSweepBuffer hitBuffer(nullptr, 0);

    //Float3 origin = transform.translation - Float3(mScene->GetOrigin());
    bool hasAnyHit = SweepInternal(mScene, geo, transform.translation, transform.rotation, transform.scale, unitDir, maxDistance, blockMask, nullptr, HitFlag::None);
    return hasAnyHit;
}

bool PhysicsQuery::Sweep(PhysicsGeometryBase& geo, const Transform& transform, const Float3& unitDir, float maxDistance, CollisionMask blockMask, PhysicsHitResult& hitResult) const
{
    //physx::PxQueryFilterData filterData;
    //filterData.flags = physx::PxQueryFlag::eSTATIC | physx::PxQueryFlag::eDYNAMIC;
    //filterData.data.word1 = EncodeCollisionWord(false, CollisionTypeValue::NoCollision, blockMask);

    //HitFlag hitFlags = HitFlag::Default;
    //physx::PxSweepBuffer hitBuffer(nullptr, 0);

    //Float3 origin = transform.translation - Float3(mScene->GetOrigin());
    bool hasAnyHit = SweepInternal(mScene, geo, transform.translation, transform.rotation, transform.scale, unitDir, maxDistance, blockMask, &hitResult);

    return hasAnyHit;
}

bool PhysicsQuery::SweepMulti(PhysicsGeometryBase& geo, const Transform& transform, const Float3& unitDir, float maxDistance, CollisionMask blockMask, std::vector<PhysicsHitResult>& hitResults, UInt32 maxHit) const
{
    hitResults.resize(1);
    hitResults.reserve(maxHit); 
    //physx::PxQueryFilterData filterData;
    //filterData.flags = physx::PxQueryFlag::eSTATIC | physx::PxQueryFlag::eDYNAMIC;
    //filterData.data.word1 = EncodeCollisionWord(false, CollisionTypeValue::NoCollision, blockMask);

    //HitFlag hitFlags = HitFlag::Default;
    //auto touchBuffer = maxHit > 1 ? std::make_unique<physx::PxSweepHit[]>(maxHit) : nullptr;
    //physx::PxSweepBuffer hitBuffer(touchBuffer.get(), touchBuffer == nullptr ? 0 : maxHit);

    //Float3 origin = transform.translation - Float3(mScene->GetOrigin());
    bool hasAnyHit = SweepInternal(mScene, geo, transform.translation, transform.rotation, transform.scale, unitDir, maxDistance, blockMask, &(hitResults[0]), HitFlag::Default, maxHit);
    if (!hasAnyHit)
        hitResults.clear();
    return hasAnyHit;
}

bool PhysicsQuery::OverlapAny(PhysicsGeometryBase& geo, const Transform& transform, CollisionMask blockMask) const
{
    //physx::PxQueryFilterData filterData;
    //filterData.flags = physx::PxQueryFlag::eSTATIC | physx::PxQueryFlag::eDYNAMIC | physx::PxQueryFlag::eANY_HIT;
    //filterData.data.word1 = EncodeCollisionWord(false, CollisionTypeValue::NoCollision, blockMask);
    //physx::PxOverlapBuffer hitBuffer(nullptr, 0);

    //Float3 origin = transform.translation - Float3(mScene->GetOrigin());
    bool hasAnyHit = OverlapInternal(mScene, geo, transform.translation, transform.rotation, transform.scale, blockMask, nullptr,HitFlag::None);

    return hasAnyHit;
}

bool PhysicsQuery::OverlapMulti(PhysicsGeometryBase& geo, const Transform& transform, CollisionMask blockMask, std::vector<PhysicsHitResult>& hitResults, UInt32 maxHit) const
{
    hitResults.resize(1);
    hitResults.reserve(maxHit); 
    //physx::PxQueryFilterData filterData;
    //filterData.flags = physx::PxQueryFlag::eSTATIC | physx::PxQueryFlag::eDYNAMIC;
    //filterData.data.word1 = EncodeCollisionWord(false, CollisionTypeValue::NoCollision, blockMask);

    //physx::PxHitFlags hitFlags = physx::PxHitFlag::eDEFAULT;
    //maxHit = std::clamp(maxHit, 1U, OverlapMaxHits);
    //auto touchBuffer = std::make_unique<physx::PxOverlapHit[]>(maxHit);
    //physx::PxOverlapBuffer hitBuffer(touchBuffer.get(), maxHit);

    bool hasAnyHit = OverlapInternal(mScene, geo, transform.translation, transform.rotation, transform.scale, blockMask, &(hitResults[0]),HitFlag::Default,maxHit);
    if (!hasAnyHit)
        hitResults.clear();
    return hasAnyHit;
}
}   // namespace cross
