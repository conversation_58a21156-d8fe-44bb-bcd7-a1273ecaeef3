#pragma once
#include "CrossPhysics.h"
#include "Math/CrossMath.h"
#include "CrossBase/Template/EnumClassFlags.h"

namespace cross
{
    enum class CEMeta(Editor) PhysicsShapeType
    {
        Box,
        Sphere,
        Capsule,
        Plane,
        Convex,
        Mesh
    };

    enum class CEMeta(Editor) CollisionTypeValue : UInt16
    {
        NoCollision = 0,
        WorldStatic,
        WorldDynamic,
        Actor,
        Terrain,
        CustomType1,
        CustomType2,
        CustomType3,
        CustomType4,
    };

    constexpr UInt16 CollisionTypeToBit(CollisionTypeValue type)
    {
        return static_cast<UInt16>(type) == 0 ? 0 : 1 << (static_cast<UInt16>(type) - 1);
    }

    enum class CEMeta(Editor) CollisionChannelBit : UInt16   // It must match the rule of left shift in function DoesCollisionChannelMatch
    {
        NoneChannel     = CollisionTypeToBit(CollisionTypeValue::NoCollision),
        WorldStatic     = CollisionTypeToBit(CollisionTypeValue::WorldStatic),
        WorldDynamic    = CollisionTypeToBit(CollisionTypeValue::WorldDynamic),
        Actor           = CollisionTypeToBit(CollisionTypeValue::Actor),
        Terrain         = CollisionTypeToBit(CollisionTypeValue::Terrain),
        CustomChannel1  = CollisionTypeToBit(CollisionTypeValue::CustomType1),
        CustomChannel2  = CollisionTypeToBit(CollisionTypeValue::CustomType2),
        CustomChannel3  = CollisionTypeToBit(CollisionTypeValue::CustomType3),
        CustomChannel4  = CollisionTypeToBit(CollisionTypeValue::CustomType4),

        BlockAll        = 0xffff,
    };
    ENUM_CLASS_FLAGS(CollisionChannelBit)

    class CrossPhysics_API CollisionMask
    {
    public:
        CollisionMask() = default;
        explicit CollisionMask(UInt16 mask)
            : mBits(mask)
        {}
        CollisionMask(CollisionChannelBit mask)
            : mBits(static_cast<UInt16>(mask))
        {}
        CollisionMask(CollisionTypeValue type)
            : mBits(CollisionTypeToBit(type))
        {}
        CollisionMask(std::initializer_list<CollisionTypeValue> typies)
            : mBits(0)
        {
            for (auto t : typies)
                Set(t);
        }

        CEFunction(Editor)
        bool IsSet(CollisionTypeValue type) { return mBits & CollisionTypeToBit(type); }

        CEFunction(Editor)
        CollisionMask& Set(CollisionTypeValue type) { mBits |= CollisionTypeToBit(type); return *this; }
        CEFunction(Editor)
        CollisionMask& Clear(CollisionTypeValue type) { mBits &= ~CollisionTypeToBit(type); return *this; }

        CollisionMask& Set(CollisionTypeValue type, bool value) { return value ? Set(type) : Clear(type); }

        CEFunction(Editor)
        void SetAll() { mBits = 0xFFFF; }
        CEFunction(Editor)
        void ClearAll() { mBits = 0x0000; };

        CEFunction(Editor)
        UInt16 Value() const { return mBits; }
        CEFunction(Editor)
        void SetValue(UInt16 v) { mBits = v; }

        bool operator==(const CollisionMask& mask) const { return mBits == mask.mBits; }
        bool operator!=(const CollisionMask& mask) const { return mBits != mask.mBits; }

        operator CollisionChannelBit() { return static_cast<CollisionChannelBit>(mBits); }

        SerializeNode Serialize() const { return SerializeNode(mBits); }
        void Deserialize(const DeserializeNode& json) { mBits = json.AsUInt16(); }

    public:
        static CollisionMask None() { return CollisionMask(0x0000); }
        static CollisionMask All()  { return CollisionMask(0xFFFF); }

    private:
        UInt16 mBits = 0;
    };

    enum class CEMeta(Editor) MaterialType
    {
        None = 0,
        Metal,
        PartMetal,
        Stone,
        Mix,
        Ceramic,
        Water,
        Sand,
        Rock,
        Concrete,
        Soil,
        Marsh,
        Tree,
        Ice,
        Asphalt,
        Grass,
        AllCount
    };
    CrossPhysics_API std::string MaterialTypeToString(MaterialType e);

    struct PhysicsShapeData
    {
        virtual ~PhysicsShapeData() = default;
    };

    class CrossPhysics_API PhysicsShape
    {
    protected:
        PhysicsShape() = default;
        virtual ~PhysicsShape() = default;

    public:
        template<typename T>
        T* GetCustomData() const { return static_cast<T*>(mCustomData.get()); }
        template<typename T>
        void SetCustomData(std::unique_ptr<T>&& customData) { mCustomData = std::move(customData); }

        virtual void SetLocalPos(const Float3& localPos, const Quaternion& localRot) = 0;
        virtual std::pair<Float3, Quaternion> GetLocalPos() = 0;

        virtual void SetScale(const Float3& scale) = 0;
        virtual Float3 GetScale() const = 0;

        virtual PhysicsShapeType GetShapeType() const = 0;

        virtual void SetCollisionInfo(CollisionTypeValue collisionType, CollisionMask mask, bool isTrigger) = 0;
        virtual void SetEnableReportCollision(bool enable) = 0;

    protected:
        std::unique_ptr<PhysicsShapeData> mCustomData;
    };
   
}
