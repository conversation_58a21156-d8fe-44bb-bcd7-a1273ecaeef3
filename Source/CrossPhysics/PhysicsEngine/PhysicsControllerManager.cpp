#include "PhysicsControllerManager.h"
#include "PhysicsScene.h"
#include "PhysXImpl/PhysXScene.h"
#include "PhysXImpl/PhysXActor.h"
#include "PhysXImpl/PhysXUtility.h"
#include "PxPhysicsAPI.h"

namespace cross {
PhysicsCapsuleController::PhysicsCapsuleController(physx::PxCapsuleController* controller)
{
    mPxController = controller;
    mActor = new PhysXActor(controller->getActor(), false);
}

PhysicsCapsuleController::~PhysicsCapsuleController()
{
    if (mPxController)
        mPxController->release();
    mPxController = nullptr;
    if (mActor)
        delete mActor;
    mActor = nullptr;
}

PhysicsControllerCollisionFlag PhysicsCapsuleController::Move(const Float3& disp, float minDist, float elapsedTime, const physx::PxControllerFilters& filters)
{
    physx::PxControllerCollisionFlags flags = mPxController->move(CrossVec3ToPxVec3(disp), minDist, elapsedTime, filters);
    return static_cast<PhysicsControllerCollisionFlag>(static_cast<UInt16>(flags));
}

void PhysicsCapsuleController::SetPosition(const Double3& position)
{
    mPxController->setPosition({position.x, position.y, position.z});
}

Double3 PhysicsCapsuleController::GetPosition() const
{
    auto pos = mPxController->getPosition();
    return {pos.x, pos.y, pos.z};
}

Double3 PhysicsCapsuleController::GetFootPosition() const
{
    auto pos = mPxController->getFootPosition();
    return {pos.x, pos.y, pos.z};
}

void PhysicsCapsuleController::SetRadius(float radius)
{
    mPxController->setRadius(radius);
}

float PhysicsCapsuleController::GetRadius() const
{
    return mPxController->getRadius();
}

void PhysicsCapsuleController::SetHalfHeight(float halfHeight)
{
    mPxController->setHeight(halfHeight * 2.0f);
}

float PhysicsCapsuleController::GetHalfHeight() const
{
    return mPxController->getHeight() * 0.5f;
}

void PhysicsCapsuleController::SetUp(const Float3& up)
{
    mPxController->setUpDirection({up.x, up.y, up.z});
}

PhysicsActor* PhysicsCapsuleController::GetActor()
{
    return mActor;
}

void PhysicsCapsuleController::SetCollisionInfo(CollisionTypeValue collisionType, CollisionMask mask)
{
    physx::PxShape* shape = GetPxShape();

    physx::PxFilterData sFilterData = shape->getSimulationFilterData();
    sFilterData.word1 = EncodeCollisionWord(false, collisionType, mask);
    shape->setSimulationFilterData(sFilterData);

    physx::PxFilterData qFilterData = shape->getQueryFilterData();
    qFilterData.word1 = EncodeCollisionWord(false, CollisionTypeValue::NoCollision, {collisionType});
    shape->setQueryFilterData(qFilterData);

    shape->setFlag(physx::PxShapeFlag::eSIMULATION_SHAPE, true);
    shape->setFlag(physx::PxShapeFlag::eSCENE_QUERY_SHAPE, true);
}

physx::PxShape* PhysicsCapsuleController::GetPxShape()
{
    physx::PxShape* shape = nullptr;
    mPxController->getActor()->getShapes(&shape, 1);
    return shape;
}

PhysicsControllerManager::PhysicsControllerManager(PhysicsScene* scene)
{
    physx::PxScene* pxScene = dynamic_cast<PhysXScene*>(scene)->GetUnderlay();
    mPxManager = PxCreateControllerManager(*pxScene);
}

PhysicsControllerManager::~PhysicsControllerManager()
{
    if (mPxManager)
        mPxManager->release();
}

PhysicsCapsuleController* PhysicsControllerManager::CreateCapsuleController(PhysicsCapsuleControllerDesc& desc)
{
    auto* pxController = mPxManager->createController(desc);
    return new PhysicsCapsuleController(static_cast<physx::PxCapsuleController*>(pxController));
}
}   // namespace cross