#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) ExpressionOutputColor : public ExpressionOutput{};

class CEMeta(Cli) MaterialExpressionTextureSample : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "Texture Sample";
    }

    virtual void DoHandlePropertyChange(IMaterialEditor * editor) override;

public:
    CEProperty(Reflect,
               EditorPropertyInfo(
                   PropertyType = "StringAsResource", FileTypeDescriptor = "Texture Assets#nda", ObjectClassID1 = ClassIDType.CLASS_Texture, ObjectClassID2 = ClassIDType.CLASS_TextureUDIM, ObjectClassID3 = ClassIDType.CLASS_Texture2DArray))
    std::string m_DefaultTexture = "";

    CEProperty(Reflect, EditorPropertyInfo(PropertyType = "Struct"))
    SamplerState m_SamplerState;

    CEProperty(Reflect, EditorPropertyInfo(bHide = true))
    MaterialValueType m_DefaultTextureType = MaterialValueType::MCT_Texture2D;

    CEProperty(Reflect)
    ExpressionInput m_UV;

    CEProperty(Reflect)
    ExpressionInput m_Tex;

    CEProperty(Reflect)
    ExpressionInput m_Level;

    CEProperty(Reflect)
    ExpressionInput m_Bias;

    CEProperty(Reflect)
    ExpressionInput m_DDX;

    CEProperty(Reflect)
    ExpressionInput m_DDY;

    CEMeta(Reflect, meta(RedirectTo = m_RGBA, ColorMask = RGB))
    ExpressionOutput m_RGB;

    CEMeta(Reflect, meta(RedirectTo = m_RGBA, ColorMask = R))
    ExpressionOutput m_R;

    CEMeta(Reflect, meta(RedirectTo = m_RGBA, ColorMask = G))
    ExpressionOutput m_G;

    CEMeta(Reflect, meta(RedirectTo = m_RGBA, ColorMask = B))
    ExpressionOutput m_B;

    CEMeta(Reflect, meta(RedirectTo = m_RGBA, ColorMask = A))
    ExpressionOutput m_A;

    CEMeta(Reflect)
    ExpressionOutput m_RGBA;
};
}   // namespace cross