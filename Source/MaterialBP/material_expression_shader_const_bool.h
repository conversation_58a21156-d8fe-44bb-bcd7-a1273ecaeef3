#pragma once

#include "material_expression_shader_const.h"

namespace cross {
class CEMeta(Cli) Material_API MaterialExpressionShaderConstBool : public MaterialExpressionShaderConst
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetMenuName() const override
    {
        return "ShaderConst Bool";
    }

public:
    CEProperty(Reflect)
    bool m_Value = true;
};
}   // namespace cross