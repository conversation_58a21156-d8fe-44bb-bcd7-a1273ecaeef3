#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionCustomInterpolator : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * outputPin) override;

    virtual std::string GetCaption() const override
    {
        return "CustomInterpolator: " + m_Name;
    }

    virtual std::string GetMenuName() const override
    {
        return "CustomInterpolator";
    }

public:
    CEProperty(Reflect)
    std::string m_Name;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross