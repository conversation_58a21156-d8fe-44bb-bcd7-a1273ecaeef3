#pragma once

#include "material_expression_parameter.h"

namespace cross {
class CEMeta(Cli, Reflect) Material_API MaterialExpressionTextureParameter : public MaterialExpressionParameter
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetMenuName() const override
    {
        return "Texture Param";
    }

    virtual void DoHandlePropertyChange(IMaterialEditor * editor) override;

    bool IsVirtualTexture() const
    {
        return m_TextureType == MCT_TextureVirtual || m_TextureType == MaterialValueType::MCT_TextureVirtualNormal;
    }

public:
    CEProperty(Reflect, EditorPropertyInfo(bHide = true))
    MaterialValueType m_TextureType = MCT_Texture2D;

    CEMeta(Reflect)
    ExpressionOutput m_Result;

    CEProperty(Reflect,
               EditorPropertyInfo(
                   PropertyType = "StringAsResource", FileTypeDescriptor = "Texture Assets#nda", ObjectClassID1 = ClassIDType.CLASS_Texture, ObjectClassID2 = ClassIDType.CLASS_TextureVirtual))
    std::string m_TextureString;

    CEProperty(Reflect)
    std::string m_VirtualTextureLayer = "";
};
}   // namespace cross