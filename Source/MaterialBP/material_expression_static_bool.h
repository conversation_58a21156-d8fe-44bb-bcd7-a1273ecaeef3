#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionStaticBool : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "Static Bool";
    }

public:
    CEProperty(Reflect)
    bool m_Value = true;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross