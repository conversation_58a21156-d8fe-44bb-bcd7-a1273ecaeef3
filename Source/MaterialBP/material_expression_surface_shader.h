#pragma once

#include "material_expression.h"

namespace cross {
struct SurfaceShaderCompileResult
{
    std::optional<int32_t> baseColor;
    std::optional<int32_t> metallic;
    std::optional<int32_t> specular;
    std::optional<int32_t> roughness;
    std::optional<int32_t> opacity;
    std::optional<int32_t> opacityMask;
    std::optional<int32_t> normal;
    std::optional<int32_t> ambientOcclusion;
    std::optional<int32_t> emissiveColor;
    std::optional<int32_t> subsurfaceColor;
    std::optional<int32_t> debugColor;
    std::optional<int32_t> temporalReactive;

    bool IsValid()
    {
        return !(baseColor && baseColor == CODE_CHUNK_INDEX_NONE || 
                 metallic && metallic == CODE_CHUNK_INDEX_NONE || 
                 specular && specular == CODE_CHUNK_INDEX_NONE || 
                 roughness && roughness == CODE_CHUNK_INDEX_NONE ||
                 opacity && opacity == CODE_CHUNK_INDEX_NONE || 
                 opacityMask && opacityMask == CODE_CHUNK_INDEX_NONE || 
                 normal && normal == CODE_CHUNK_INDEX_NONE || 
                 ambientOcclusion && ambientOcclusion == CODE_CHUNK_INDEX_NONE ||
                 emissiveColor && emissiveColor == CODE_CHUNK_INDEX_NONE || 
                 subsurfaceColor && subsurfaceColor == CODE_CHUNK_INDEX_NONE || 
                 debugColor && debugColor == CODE_CHUNK_INDEX_NONE ||
                 temporalReactive && temporalReactive == CODE_CHUNK_INDEX_NONE);
    }

    void Clear()
    {
        baseColor.reset();
        metallic.reset();
        specular.reset();
        roughness.reset();
        opacity.reset();
        opacityMask.reset();
        normal.reset();
        ambientOcclusion.reset();
        emissiveColor.reset();
        subsurfaceColor.reset();
        debugColor.reset();
        temporalReactive.reset();
    }
};

class CEMeta(Cli) Material_API MaterialExpressionSurfaceShader : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    MaterialExpressionSurfaceShader()
    {
        m_MaterialAttributes.m_Enable = false;
    }

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * outputPin) override;

    virtual std::string GetCaption() const override
    {
        return "Surface Shader";
    }

    void UpdatePinEnables(const MaterialDefines& defines);

    virtual ImColor GetTitleColor() const override
    {
        return ImColor(1.0f, 0.0f, 0.0f, 1.0f);
    }

public:
    SurfaceShaderCompileResult m_CompileResult;

public:
    CEProperty(Reflect)
    ExpressionInput m_BaseColor;
    CEProperty(Reflect, meta(OverrideInputProperty = m_BaseColor))
    Float4 m_BaseColorConstant = {1.f, 1.f, 1.f, 0.f};

    CEProperty(Reflect)
    ExpressionInput m_Metallic;
    CEProperty(Reflect, meta(OverrideInputProperty = m_Metallic))
    float m_MetallicConstant = 0.f;
    
    CEProperty(Reflect)
    ExpressionInput m_Specular;
    CEProperty(Reflect, meta(OverrideInputProperty = m_Specular))
    float m_SpecularConstant = .5f;

    CEProperty(Reflect)
    ExpressionInput m_Roughness;
    CEProperty(Reflect, meta(OverrideInputProperty = m_Roughness))
    float m_RoughnessConstant = .5f;

    CEProperty(Reflect)
    ExpressionInput m_Opacity;
    CEProperty(Reflect, meta(OverrideInputProperty = m_Opacity))
    float m_OpacityConstant = 0.f;

    CEProperty(Reflect)
    ExpressionInput m_OpacityMask;

    CEProperty(Reflect)
    ExpressionInput m_Normal;

    CEProperty(Reflect)
    ExpressionInput m_AmbientOcclusion;

    CEProperty(Reflect)
    ExpressionInput m_EmissiveColor;

    CEProperty(Reflect)
    ExpressionInput m_SubsurfaceColor;

    CEProperty(Reflect)
    ExpressionInput m_DebugColor;

    CEProperty(Reflect)
    ExpressionInput m_TemporalReactive;

    CEProperty(Reflect)
    ExpressionAttributesInput m_MaterialAttributes;
};
}   // namespace cross