#pragma once

#include "material_expression.h"

namespace cross {
struct CEMeta(Cli) CustomInterpolator
{
    CE_Serialize_Deserialize;

    CEProperty(Reflect)
    std::string Name;

    CEProperty(Reflect)
    MaterialCustomInterpolatorType Type = MaterialCustomInterpolatorType::Float2;

    CEProperty()
    ExpressionInput Input;
};

struct VertexShaderCompileResult
{
    std::optional<int32_t> WorldPositionOffset;
    std::vector<int32_t> CustomInterpolators;

    bool IsValid()
    {
        if (WorldPositionOffset && WorldPositionOffset == CODE_CHUNK_INDEX_NONE)
        {
            return false;
        }

        for (auto value : CustomInterpolators)
        {
            if (value == CODE_CHUNK_INDEX_NONE)
            {
                return false;
            }
        }

        return true;
    }

    void Clear()
    {
        WorldPositionOffset.reset();
        CustomInterpolators.clear();
    }
};

class CEMeta(Cli) Material_API MaterialExpressionVertexShader : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * outputPin) override;

    virtual std::string GetCaption() const override
    {
        return "Vertex Shader";
    }

    virtual ImColor GetTitleColor() const override
    {
        return ImColor(1.0f, 0.0f, 0.0f, 1.0f);
    }

    virtual void OnPropertyChange(IMaterialEditor * editor) override;

    CEFunction(AdditionalDeserialize)
    void AdditionalDeserialize(const cross::DeserializeNode& node, SerializeContext& context);

public:
    VertexShaderCompileResult m_CompileResult;

public:
    CEProperty(Reflect, EditorPropertyInfo(PropertyType = "List", ChildPropertyType = "Struct"))
    std::vector<CustomInterpolator> m_CustomInterpolators;

    CEProperty(Reflect)
    ExpressionAttributesInput m_WorldPositionOffset;
};
}   // namespace cross