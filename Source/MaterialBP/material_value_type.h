#pragma once

#include "CrossBase/Math/CrossMath.h"
#include "reflection/objects/rtti_base.hpp"
#include "reflection/objects/value.hpp"

namespace cross {

#define CODE_CHUNK_INDEX_NONE -1

#define SUPPORTED_MATERIAL_ATTRIBUTES 11

// !!! copy from ue UnrealEngine/Engine/Source/Runtime/Engine/Public/MaterialValueType.h !!!
enum MaterialValueType : UInt64
{
    /**
     * A scalar float type.
     * Note that MCT_Float1 will not auto promote to any other float types,
     * So use MCT_Float instead for scalar expression return types.
     */
    MCT_Float1 = 1,
    MCT_Float2 = MCT_Float1 << 1,
    MCT_Float3 = MCT_Float2 << 1,
    MCT_Float4 = MCT_Float3 << 1,

    /**
     * Any size float type by definition, but this is treated as a scalar which can auto convert (by replication) to any other size float vector.
     * Use this as the type for any scalar expressions.
     */
    MCT_Float = MCT_Float1 | MCT_Float2 | MCT_Float3 | MCT_Float4,
    MCT_Texture2D = MCT_Float4 << 1,
    MCT_Texture2DNormal = MCT_Texture2D << 1,
    MCT_TextureCube = MCT_Texture2DNormal << 1,
    MCT_Texture2DArray = MCT_TextureCube << 1,
    MCT_TextureCubeArray = MCT_Texture2DArray << 1,
    MCT_VolumeTexture = MCT_TextureCubeArray << 1,
    MCT_StaticBool = MCT_VolumeTexture << 1,
    MCT_Unknown = MCT_StaticBool << 1,
    MCT_MaterialAttributes = MCT_Unknown << 1,
    MCT_TextureExternal = MCT_MaterialAttributes << 1,
    MCT_TextureVirtual = MCT_TextureExternal << 1,
    MCT_TextureVirtualNormal = MCT_TextureVirtual << 1,
    MCT_Texture = MCT_Texture2D | MCT_Texture2DNormal | MCT_TextureCube | MCT_Texture2DArray | MCT_TextureCubeArray | MCT_VolumeTexture | MCT_TextureExternal | MCT_TextureVirtual | MCT_TextureVirtualNormal,

    /** Used internally when sampling from virtual textures */
    MCT_VTPageTableResult = MCT_TextureVirtualNormal << 1,

    MCT_ShadingModel = MCT_VTPageTableResult << 1,

    MCT_Strata = MCT_ShadingModel << 1,

    MCT_LWCScalar = MCT_Strata << 1,
    MCT_LWCVector2 = MCT_LWCScalar << 1,
    MCT_LWCVector3 = MCT_LWCVector2 << 1,
    MCT_LWCVector4 = MCT_LWCVector3 << 1,
    MCT_LWCType = MCT_LWCScalar | MCT_LWCVector2 | MCT_LWCVector3 | MCT_LWCVector4,

    MCT_Execution = MCT_LWCVector4 << 1,

    /** Used for code chunks that are statements with no value, rather than expressions */
    MCT_VoidStatement = MCT_Execution << 1,

    /** Non-static bool, only used in new HLSL translator */
    MCT_Bool = MCT_VoidStatement << 1,

    /** Unsigned int types */
    MCT_UInt1 = MCT_Bool << 1,
    MCT_UInt2 = MCT_UInt1 << 1,
    MCT_UInt3 = MCT_UInt2 << 1,
    MCT_UInt4 = MCT_UInt3 << 1,
    MCT_UInt = MCT_UInt1 | MCT_UInt2 | MCT_UInt3 | MCT_UInt4,

    /** int types */
    MCT_SInt1 = MCT_UInt4 << 1,
    MCT_SInt2 = MCT_SInt1 << 1,
    MCT_SInt3 = MCT_SInt2 << 1,
    MCT_SInt4 = MCT_SInt3 << 1,
    MCT_SInt = MCT_SInt1 | MCT_SInt2 | MCT_SInt3 | MCT_SInt4,

    MCT_Numeric = MCT_Float | MCT_LWCType | MCT_Bool | MCT_UInt | MCT_SInt,
};

inline UInt32 GetNumericComponentCount(MaterialValueType type)
{
    assert(type & MCT_Numeric);

    switch (type)
    {
    case MCT_Float1:
    case MCT_UInt1:
    case MCT_SInt1:
        return 1u;
    case MCT_Float2:
    case MCT_UInt2:
    case MCT_SInt2:
        return 2u;
    case MCT_Float3:
    case MCT_UInt3:
    case MCT_SInt3:
        return 3u;
    case MCT_Float4:
    case MCT_UInt4:
    case MCT_SInt4:
        return 4u;
    default:
        assert(false);
        return 0u;
    }
}

inline MaterialValueType MakeMaterialValueType(MaterialValueType baseType, UInt32 componentCount)
{
    if (baseType & MCT_Float)
    {
        if (componentCount == 1u)
            return MCT_Float1;
        if (componentCount == 2u)
            return MCT_Float2;
        if (componentCount == 3u)
            return MCT_Float3;
        if (componentCount == 4u)
            return MCT_Float4;
    }
    if (baseType & MCT_UInt)
    {
        if (componentCount == 1u)
            return MCT_UInt1;
        if (componentCount == 2u)
            return MCT_UInt2;
        if (componentCount == 3u)
            return MCT_UInt3;
        if (componentCount == 4u)
            return MCT_UInt4;
    }
    if (baseType & MCT_SInt)
    {
        if (componentCount == 1u)
            return MCT_SInt1;
        if (componentCount == 2u)
            return MCT_SInt2;
        if (componentCount == 3u)
            return MCT_SInt3;
        if (componentCount == 4u)
            return MCT_SInt4;
    }
    return MCT_Unknown;
}

enum class CEMeta(Cli) MaterialCustomInterpolatorType : uint32_t
{
    Float1,
    Float2,
    Float3,
    Float4,
    UInt1,
    UInt2,
    UInt3,
    UInt4,
};

enum class MaterialParameterType : uint8_t
{
    Scalar = 0u,
    Vector,         // float4
    DoubleVector,   // unused
    Texture2D,
    TextureCube,
    VirtualTexture,
    Num,
    None = 0xff,
};

inline bool IsTexture(MaterialParameterType type)
{
    return type == MaterialParameterType::Texture2D || type == MaterialParameterType::TextureCube || type == MaterialParameterType::VirtualTexture;
}

enum class MaterialShaderConstType : uint8_t
{
    Bool,
    Float,
};

enum class MaterialInstanceDataType : uint8_t
{
    Float1,
    Float2,
    Float3,
    Float4,
    Int1,
    Int2,
    Int3,
    Int4,
    UInt1,
    UInt2,
    UInt3,
    UInt4,
    Matrix,
};

enum ShaderFrequency : uint8_t
{
    SF_WorldPositionOffset,
    SF_GenerateCutsomInterpolators,
    SF_Surface,
    SF_NumFrequencies,
};

enum class CEMeta(Reflect, Editor, Serialize, Cli) EMaterialProperty : uint8_t
{
    None = 0,
    BaseColor = 1,
    Metallic = 2,
    Specular = 3,
    Roughness = 4,
    Opacity = 5,
    OpacityMask = 6,
    Normal = 7,
    AmbientOcclusion = 8,
    EmissiveColor = 9,
    SubsurfaceColor = 10,
    WorldPositionOffset = 11,
    ShadingModel = 12,
    Anisotropy = 13,
    Tangent = 14,
    ClearCoat = 15,
    ClearCoatRoughness = 16,
    Refraction = 17,
    PixelDepthOffset = 18,
    Displacement = 19,
};

inline MaterialValueType GetMaterialValueType(MaterialCustomInterpolatorType type)
{
    switch (type)
    {
    case MaterialCustomInterpolatorType::Float1:
        return MCT_Float;
    case MaterialCustomInterpolatorType::Float2:
        return MCT_Float2;
    case MaterialCustomInterpolatorType::Float3:
        return MCT_Float3;
    case MaterialCustomInterpolatorType::Float4:
        return MCT_Float4;
    case MaterialCustomInterpolatorType::UInt1:
        return MCT_UInt;
    case MaterialCustomInterpolatorType::UInt2:
        return MCT_UInt2;
    case MaterialCustomInterpolatorType::UInt3:
        return MCT_UInt3;
    case MaterialCustomInterpolatorType::UInt4:
        return MCT_UInt4;
    default:
        return MCT_Unknown;
    }
}

inline MaterialValueType GetMaterialValueType(MaterialParameterType type)
{
    switch (type)
    {
    case MaterialParameterType::Scalar:
        return MCT_Float;
    case MaterialParameterType::Vector:
        return MCT_Float4;
    case MaterialParameterType::DoubleVector:
        return MCT_LWCVector4;
    case MaterialParameterType::Texture2D:
        return MCT_Texture2D;
    case MaterialParameterType::TextureCube:
        return MCT_TextureCube;
    case MaterialParameterType::VirtualTexture:
        return MCT_TextureVirtual;
    default:
        return MCT_Unknown;
    }
}

inline MaterialValueType GetMaterialValueType(MaterialShaderConstType type)
{
    switch (type)
    {
    case MaterialShaderConstType::Bool:
        return MCT_Bool;
    case MaterialShaderConstType::Float:
        return MCT_Float;
    default:
        return MCT_Unknown;
    }
}
}   // namespace cross