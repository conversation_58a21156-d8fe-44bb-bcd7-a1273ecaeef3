#pragma once

#include "material_value_type.h"
#include <map>

namespace cross {
class MaterialExpressionFunctionCall;

inline uint32_t GetNumComponents(MaterialValueType type)
{
    switch (type)
    {
    case MCT_UInt:
    case MCT_UInt1:
    case MCT_Float:
    case MCT_Float1:
        return 1;
    case MCT_UInt2:
    case MCT_Float2:
        return 2;
    case MCT_UInt3:
    case MCT_Float3:
        return 3;
    case MCT_UInt4:
    case MCT_Float4:
        return 4;
    case MCT_LWCScalar:
        return 1;
    case MCT_LWCVector2:
        return 2;
    case MCT_LWCVector3:
        return 3;
    case MCT_LWCVector4:
        return 4;
    case MCT_StaticBool:
        return 1;
    case MCT_Bool:
        return 1;
    default:
        return 0;
    }
}

inline bool IsMaterialValueType(MaterialValueType InType, MaterialValueType InCompare)
{
    return (InType & InCompare) != 0;
}

inline bool IsLWCType(MaterialValueType InType)
{
    return IsMaterialValueType(InType, MCT_LWCType);
}

inline bool IsFloatNumericType(MaterialValueType inType)
{
    return (inType & MCT_Float) /*|| IsLWCType(inType)*/;
}
inline bool IsFloatNumericOrVectorType(MaterialValueType inType) 
{
    return (inType & MCT_Float) || (inType & MCT_Float2) || (inType & MCT_Float3) || (inType & MCT_Float4);
}
inline bool IsUIntNumericType(MaterialValueType inType)
    {
    return (inType & MCT_UInt);
}

inline bool IsNumericType(MaterialValueType inType)
{
    return IsFloatNumericType(inType) || inType == MCT_ShadingModel || IsUIntNumericType(inType);
}

struct MaterialExpressionKey
{
    class MaterialExpression* expression;
    class ExpressionOutput* output;
    EMaterialProperty attribute;

    friend bool operator<(const MaterialExpressionKey& x, const MaterialExpressionKey& y)
    {
        if (x.expression == y.expression)
        {
            if (x.output == y.output)
            {
                return x.attribute < y.attribute;
            }
            return x.output < y.output;
        }
        return x.expression < y.expression;
    }

    friend bool operator==(const MaterialExpressionKey& x, const MaterialExpressionKey& y) { return x.expression == y.expression && x.output == y.output && x.attribute == y.attribute; }
};

class MaterialFunctionCompileState
{
public:
    MaterialFunctionCompileState(MaterialExpressionFunctionCall* functionCall)
        : m_FunctionCall(functionCall)
    {}

    ~MaterialFunctionCompileState();

    MaterialFunctionCompileState* FindOrAddSharedFunctionState(MaterialExpressionKey expressionKey, MaterialExpressionFunctionCall* functionCall);

public:
    std::map<MaterialExpressionKey, int32_t> m_ExpressionCodeMap;
    std::vector<MaterialExpressionKey> m_ExpressionStack;

    MaterialExpressionFunctionCall* m_FunctionCall;

private:
    // cache of MaterialFunctionCall CodeChunks
    std::map<MaterialExpressionKey, MaterialFunctionCompileState*> m_SharedFunctionStates;
};

// CustomInterpolator

struct MaterialCustomInterpolatorInfo
{
    MaterialCustomInterpolatorType Type;
    std::string Name;
    std::string InnerName;
};

class MaterialCustomInterpolatorSet
{
public:
    MaterialCustomInterpolatorInfo* FindInterpolator(std::string_view name);

    void AddInterpolator(MaterialCustomInterpolatorType type, std::string_view name);

    const auto& GetCustomInterpolators() const { return m_CustomInterpolators; }

    void Clear()
    {
        m_CustomInterpolators.clear();
        m_CIIndex = 0;
    }

private:
    std::vector<MaterialCustomInterpolatorInfo> m_CustomInterpolators;
    int m_CIIndex;
};

// Parameter

using MaterialParameterValue = std::variant<float, Float4, std::string>;

struct MaterialParameterInfo
{
    MaterialParameterType m_Type;
    std::string m_ParameterName;
    MaterialParameterValue m_Value;
};

class UniformExpressionSet
{
public:
    int32_t FindOrAddParameter(MaterialParameterType type, std::string_view name, MaterialParameterValue value);

    const auto& GetParamters() const { return m_UniformParameters; }

    void Clear() { m_UniformParameters.clear(); }

private:
    std::vector<MaterialParameterInfo> m_UniformParameters;
};

// ShaderConst

using MaterialShaderConstValue = std::variant<bool, float>;

struct MaterialShaderConstInfo
{
    MaterialShaderConstType Type;
    std::string ShaderConstName;
    std::string ShaderConstDisplayName;
    MaterialShaderConstValue Value;
};

class MaterialCompilerShaderConstSet
{
public:
    int32_t FindOrAddShaderConst(MaterialShaderConstType type, std::string_view name, std::string_view displayName, MaterialShaderConstValue value);

    const auto& GetShaderConsts() const { return m_ShaderConsts; }

    void Clear() { m_ShaderConsts.clear(); }

private:
    std::vector<MaterialShaderConstInfo> m_ShaderConsts;
};

// Macro

class MaterialCompileMacroSet
{
public:
    void AddMacro(const std::string& key, const std::string& value = "");

    const auto& GetMacros() const { return m_Macros; }

    void Clear() { m_Macros.clear(); }

private:
    std::map<std::string, std::string> m_Macros;
};

enum ExposedViewProperty : int
{
    WorldSpaceCameraPosition,
    TileCameraPosition,
    CameraVectorWS,
    ViewPropertyMax,
};

enum class CEMeta(Cli) GPUSceneDataLevel
{
    Object,
    Primitive,
};

enum class CEMeta(Cli) MaterialCommonBasis
{
    /** Tangent space (relative to the surface) */
    Tangent,   //(DisplayName = "Tangent Space"),

    /** Local space (relative to the rendered object, = object space) */
    Local,   //(DisplayName = "Local Space"),

    /** World space, a unit is 1cm */
    World,   //(DisplayName = "World Space"),

    /** View space (relative to the camera/eye, = camera space, differs from camera space in the shadow passes) */
    // View, //(DisplayName = "View Space"),

    /** Camera space */
    Camera,   //(DisplayName = "Camera Space"),

    /** Instance space (used to provide per instance transform, i.e. for Instanced Static Mesh / Particles). */
    Instance,   //(DisplayName = "Instance & Particle Space"),
};

enum class CEMeta(Cli) TransformElementType
{
    Vector,
    Normal,
    Position,
};

enum class BoundsOutputIndex
{
    BoundsHalfExtent = 0,
    BoundsExtent = 1,
    BoundsMax = 2,
    BoundsMin = 3,
};
}   // namespace cross