#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionTilePosition : public MaterialExpression
{
public:
    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "TilePosition";
    }

public:
    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross