#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) Material_API MaterialExpressionParameter : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual std::string GetCaption() const override
    {
        return m_Name;
    }

    virtual void OnPropertyChange(IMaterialEditor * editor) override;

public:
    CEProperty(Reflect, EditorPropertyInfo(bHide = true))
    std::string m_ParameterName;

    CEProperty(Reflect, EditorPropertyInfo(bHide = true))
    bool m_IsVisibleInMaterialInstanceEditor{true};

    CEProperty(Reflect)
    std::string m_Name;

    CEProperty(Reflect)
    std::string m_Group = "Default";

    CEProperty(Reflect)
    float m_SortPriority = 1.0f;

};
}   // namespace cross