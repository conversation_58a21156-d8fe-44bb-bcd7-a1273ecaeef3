#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) Material_API MaterialExpressionBreakMaterialAttributes : public MaterialExpression
{
public:

    CE_Virtual_Serialize_Deserialize;

    MaterialExpressionBreakMaterialAttributes();

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "BreakMaterialAttributes";
    }

    EMaterialProperty GetPinProperty(ExpressionOutput * output) const
    {
        auto it = m_OutputMap.find(output);
        if (it != m_OutputMap.end())
        {
            return it->second;
        }
        return EMaterialProperty::None;
    }

public:
    CEProperty(Reflect)
    ExpressionAttributesInput m_MaterialAttributes;

    CEMeta(Reflect)
    ExpressionOutput m_BaseColor;

    CEMeta(Reflect)
    ExpressionOutput m_Metallic;

    CEMeta(Reflect)
    ExpressionOutput m_Specular;

    CEMeta(Reflect)
    ExpressionOutput m_Roughness;

    CEMeta(Reflect)
    ExpressionOutput Anisotropy;

    CEMeta(Reflect)
    ExpressionOutput m_EmissiveColor;

    CEMeta(Reflect)
    ExpressionOutput m_Opacity;

    CEMeta(Reflect)
    ExpressionOutput m_OpacityMask;

    CEMeta(Reflect)
    ExpressionOutput m_Normal;

    CEMeta(Reflect)
    ExpressionOutput m_Tangent;

    CEMeta(Reflect)
    ExpressionOutput m_WorldPositionOffset;

    CEMeta(Reflect)
    ExpressionOutput m_SubsurfaceColor;

    CEMeta(Reflect)
    ExpressionOutput m_AmbientOcclusion;

    CEMeta(Reflect)
    ExpressionOutput m_ShadingModel;

private:
    std::map<ExpressionOutput*, EMaterialProperty> m_OutputMap;
};
}   // namespace cross