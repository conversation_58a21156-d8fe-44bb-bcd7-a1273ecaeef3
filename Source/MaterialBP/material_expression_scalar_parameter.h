#pragma once

#include "material_expression_parameter.h"

namespace cross {
class CEMeta(Cli) Material_API MaterialExpressionScalarParameter : public MaterialExpressionParameter
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetMenuName() const override
    {
        return "ScalarParam";
    }

public:
    CEMeta(Reflect)
    ExpressionOutput m_Result;

    CEProperty(Reflect)
    float m_DefaultValue;

    CEMeta(Serialize, Reflect)
    float m_SliderMin;

    CEMeta(Serialize, Reflect)
    float m_SliderMax;
};
}   // namespace cross