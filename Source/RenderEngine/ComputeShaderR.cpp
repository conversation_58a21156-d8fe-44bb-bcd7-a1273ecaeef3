#include "EnginePrefix.h"
#include "ComputeShaderR.h"
#include "CECommon/Common/EngineGlobal.h"
#include "RenderEngine/RenderContext.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderEngine.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "Resource/Shader.h"

cross::ComputeShaderR::~ComputeShaderR()
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    for (auto& [_, pso] : mPipelineStates)
    {
        rendererSystem->DestroyNGIObject(std::move(pso));
    }
}

void cross::ComputeShaderR::Reset(resource::ComputeShader* cs) {
    mGameObject = cs;
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    for (auto& [_, pso] : mPipelineStates)
    {
        rendererSystem->DestroyNGIObject(std::move(pso));
    }
    mPipelineStates.clear();
}

std::tuple<cross::NGIComputePipelineState*, cross::resource::ComputeProgramDesc*> cross::ComputeShaderR::GetPipelineState(PropertySet& context, const NameID& kernel)
{
    using namespace CrossSchema;

    auto* programDesc = mGameObject->GetProgramDesc(kernel);
    Assert(programDesc);

    std::vector<UInt8> shaderConsts;
    if (programDesc->ShaderConstantLayout)
    {
        auto& scLayout = *programDesc->ShaderConstantLayout;
        shaderConsts.resize(scLayout.ByteSize);
        for (auto& m : scLayout.Members)
        {
            auto* dataPtr = shaderConsts.data() + m.Offset;

            if (auto* prop = context.GetNumericProperty(m.Name); prop)
            {
                std::visit(Overloaded{
                    [&](const bool& v)
                    {
                        if (CheckType<bool>(m))
                        {
                             memset(dataPtr, v ? 0xffffffff : 0, m.Size);
                        }
                        else
                        {
                            Assert(false);
                        }
                    },
                    [&](const FrameStdVector<UInt8>& v)
                    {
                        memcpy(dataPtr, v.data(), std::min<size_t>(v.size(), m.Size));
                    },
                    [&](const auto& v)
                    {
                        if (CheckType<std::decay_t<decltype(v)>>(m))
                        {
                            memcpy(dataPtr, &v, sizeof(v));
                        }
                        else
                        {
                            Assert(false);
                        }
                    } 
                    }, *prop);
            }
        }
    }

    NGIComputePipelineStateDesc desc{
        {
            programDesc->GUID.low,
            programDesc->GUID.high,
        },
        &programDesc->ProgramDesc,
        programDesc->PipelineLayout,
        static_cast<UInt32>(shaderConsts.size()),
        shaderConsts.data(),
    };
    {
        std::shared_lock lock(mPipelineStatesMutex);
        if (auto ret = mPipelineStates.find(&desc); ret != mPipelineStates.end())
        {
            return { ret->second.get(), programDesc };
        }
    }


    {
        std::scoped_lock lock(mPipelineStatesMutex);
        auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto* pso = rdrSys->GetGraphicsPipelineStatePool()->CreateComputePipelineState(desc);
        pso->SetDebugName(programDesc->ProgramDesc.FilePath);
        mPipelineStates.emplace(std::piecewise_construct, std::forward_as_tuple(&pso->GetDesc()), std::forward_as_tuple(pso));
        return {pso, programDesc};
    }
}

void cross::ComputeShaderR::GetThreadGroupSize(const NameID& kernel, UInt32& x, UInt32& y, UInt32& z)
{
    if (auto* programDesc = mGameObject->GetProgramDesc(kernel); programDesc)
    {
        x = programDesc->GroupSize.x();
        y = programDesc->GroupSize.y();
        z = programDesc->GroupSize.z();
    }
}
