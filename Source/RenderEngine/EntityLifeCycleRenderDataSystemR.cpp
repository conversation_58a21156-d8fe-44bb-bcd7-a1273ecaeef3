#include "EntityLifeCycleRenderDataSystemR.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/RenderNodeSystemR.h"
#include "RenderEngine/AABBSystemR.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/RenderPipelineSystemR.h"
#include "RenderEngine/SkeletonSystemR.h"

namespace cross {

EntityLifeCycleRenderDataSystemR* EntityLifeCycleRenderDataSystemR::CreateInstance()
{
    return new EntityLifeCycleRenderDataSystemR();
}

void EntityLifeCycleRenderDataSystemR::OnBeginFrame(FrameParam* frameParam)
{
    mRenderEntityCreateListMap.clear();
    mRenderEntityVSMUpdateListMap.clear();
    mRenderEntityChangeListMap.clear();
    mRenderEntityCreateList.clear();
    mRenderEntityVSMUpdateList.clear();
    mRenderEntityChangeList.clear();
}

void EntityLifeCycleRenderDataSystemR::OnFirstUpdate(FrameParam* frameParam)
{
    auto* transformSys = mRenderWorld->GetRenderSystem<TransformSystemR>();
    auto* renderNodeSys = mRenderWorld->GetRenderSystem<RenderNodeSystemR>();

    mRenderWorld->SubscribeRemainedEvent<EntityDestroyEvent>(this, true);
    transformSys->SubscribeEvent<TransformChangeEvent>(this);
    renderNodeSys->SubscribeEvent<RenderMeshChangeBeforeEvent>(this);
    renderNodeSys->SubscribeEvent<RenderMeshChangeEvent>(this);
    renderNodeSys->SubscribeEvent<RenderMaterialChangeEvent>(this);
    renderNodeSys->SubscribeEvent<MaterialUpdateEvent>(this);
}

void EntityLifeCycleRenderDataSystemR::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    RenderSystemBase::NotifyEvent(event, flag);

    // hack
    if (!mGPUScene)
    {
        auto* renderPipelineSys = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>();
        auto* worldRenderPipeline = renderPipelineSys->GetWorldRenderPipeline();

        if (worldRenderPipeline)
        {
            mGPUScene = worldRenderPipeline->GetGPUScene();
            mRayTracingScene = worldRenderPipeline->GetRayTracingScene();
        }
    }

    if (event.mEventType == RemainedEventUpdatedEvent::sEventType)
    {
        const RemainedEventUpdatedEvent& e = static_cast<const RemainedEventUpdatedEvent&>(event);

        // gather entity removed
        if (e.mData.mRemainedEventType == EntityDestroyEvent::sEventType)
        {
            for (UInt32 i = e.mData.mFirstIndex; i <= e.mData.mLastIndex; i++)
            {
                auto entity = mRenderWorld->GetRemainedEvent<EntityDestroyEvent>(i).mData.mEntityID;

                if (!IsRenderEntity(entity))
                {
                    continue;
                }

                mRayTracingScene->RemoveEntity(entity);

                AddRenderEntityUpdateVSM(entity);
            }
        }
    }
    else if (event.mEventType == TransformChangeEvent::sEventType)
    {
        // gather entity moved
        const TransformChangeEvent& e = static_cast<const TransformChangeEvent&>(event);
        auto& data = e.mData;
        auto entity = data.mEntityID;

        if (!IsAliveRenderEntity(entity))
        {
            return;
        }

        AddRenderEntityChanged(entity, RenderEntityChangeData::ChangeType::Transform);
    }
    else if (event.mEventType == RenderMeshChangeBeforeEvent::sEventType)
    {
        // gather entity mesh changed
        const RenderMeshChangeBeforeEvent& e = static_cast<const RenderMeshChangeBeforeEvent&>(event);
        auto& data = e.mData;
        auto entity = data.mEntityID;

        if (!IsAliveRenderEntity(entity))
        {
            return;
        }

        AddRenderEntityUpdateVSM(entity);
    }
    else if (event.mEventType == RenderMeshChangeEvent::sEventType)
    {
        // gather entity mesh changed
        const RenderMeshChangeEvent& e = static_cast<const RenderMeshChangeEvent&>(event);
        auto& data = e.mData;
        auto entity = data.mEntityID;

        if (!IsAliveRenderEntity(entity))
        {
            return;
        }

        AddRenderEntityCreated(entity);
    }
    else if (event.mEventType == RenderMaterialChangeEvent::sEventType)
    {
        const RenderMaterialChangeEvent& e = static_cast<const RenderMaterialChangeEvent&>(event);
        auto& data = e.mData;
        auto entity = data.mEntityID;

        if (!IsAliveRenderEntity(entity))
        {
            return;
        }

        AddRenderEntityCreated(entity);
    }
    else if (event.mEventType == MaterialUpdateEvent::sEventType)
    {
        const MaterialUpdateEvent& e = static_cast<const MaterialUpdateEvent&>(event);
        auto& data = e.mData;
        auto entity = data.mEntityID;

        if (!IsAliveRenderEntity(entity))
        {
            return;
        }

        AddRenderEntityUpdateVSM(entity);
    }
}

void EntityLifeCycleRenderDataSystemR::Release()
{
    delete this;
}

void EntityLifeCycleRenderDataSystemR::OnBuildUpdateTasks(FrameParam* frameParam)
{
    CreateTaskFunction(FrameTickStage::Update, {}, [&] {
        SCOPED_CPU_TIMING(GroupRendering, "EntityLifeCycleRenderDataSystemRUpdate");

        auto* renderPropertySys = mRenderWorld->GetRenderSystem<RenderPropertySystemR>();

        // gather entity created
        {
            const UInt32 entityCreateCount = mRenderWorld->GetRemainedEventCount<EntityCreateEvent>(true);
            for (UInt32 i = 0; i < entityCreateCount; i++)
            {
                auto entity = mRenderWorld->GetRemainedEvent<EntityCreateEvent>(i).mData.mEntityID;

                if (!IsAliveRenderEntity(entity))
                {
                    continue;
                }

                AddRenderEntityCreated(entity);
            }
        }

        // gather skeleton entity
        {
            auto* modelSys = mRenderWorld->GetRenderSystem<ModelSystemR>();

            auto queryResult = mRenderWorld->Query<SkeletonComponentR, ModelComponentR>();
            for (auto&& [skComp, modelComp] : queryResult)
            {
                // for now, we assume model is always enabled
                if (!skComp.Read()->mEnable)
                {
                    continue;
                }

                if (!IsAliveRenderEntity(skComp.GetEntityID()))
                {
                    continue;
                }

                if ((UInt32)(modelSys->GetBatchInfo(modelComp.Read()).GetBatchFlag()) == 0)
                    continue;

                // static mesh should not come into here
                if (!modelSys->IsSkeletalModel(modelComp.Read()))
                    continue;

                AddRenderEntityUpdateVSM(skComp.GetEntityID());
            }
        }

        // handle RenderProperty change
        {
            const auto& changeList = renderPropertySys->GetChangeListCullingData();
            UInt32 renderProeprtyChangeCount = changeList.GetCount();
            for (UInt32 i = 0; i < renderProeprtyChangeCount; i++)
            {
                auto [entity, cullData] = changeList.GetData(i);

                if (!IsAliveRenderEntity(entity))
                {
                    continue;
                }

                AddRenderEntityChanged(entity, RenderEntityChangeData::ChangeType::RenderProperty);
            }
        }

        // handle RenderEffect change
        {
            const auto& changeListeffect = renderPropertySys->GetChangeListRenderEffect();
            UInt32 renderEffectChangeCount = changeListeffect.GetCount();
            for (UInt32 i = 0; i < renderEffectChangeCount; i++)
            {
                auto [entity, effectData] = changeListeffect.GetData(i);

                if (!IsAliveRenderEntity(entity))
                {
                    continue;
                }

                AddRenderEntityChanged(entity, RenderEntityChangeData::ChangeType::RenderEffect);
            }
        }
    });
}

bool EntityLifeCycleRenderDataSystemR::IsAliveRenderEntity(ecs::EntityID entity)
{
    return mRenderWorld->IsEntityAlive(entity) && mRenderWorld->HasComponent<RenderNodeComponentR>(entity) && mRenderWorld->HasComponent<AABBComponentR>(entity);
}

bool EntityLifeCycleRenderDataSystemR::IsRenderEntity(ecs::EntityID entity)
{
    return mRenderWorld->IsEntityAliveInner(entity) && mRenderWorld->HasComponent<RenderNodeComponentR>(entity) && mRenderWorld->HasComponent<AABBComponentR>(entity);
}

void EntityLifeCycleRenderDataSystemR::AddRenderEntityCreated(ecs::EntityID entity)
{
    std::lock_guard guard(mRenderEntityCreateListMapMutex);

    auto* transformSys = mRenderWorld->GetRenderSystem<TransformSystemR>();
    auto* aabbSys = mRenderWorld->GetRenderSystem<AABBSystemR>();

    auto iter = mRenderEntityCreateListMap.find(entity);
    if (iter == mRenderEntityCreateListMap.end())
    {
        auto aabbComp = mRenderWorld->GetComponent<AABBComponentR>(entity);
        auto tilePositionComp = mRenderWorld->GetComponent<TilePositionComponentR>(entity);

        BoundingBox boundingBox = aabbSys->GetWorldAABB(aabbComp.Read());
        Float3 tilePosition = transformSys->GetTilePosition(tilePositionComp.Read());

        mRenderEntityCreateListMap.insert({entity, mRenderEntityCreateList.size()});
        mRenderEntityCreateList.push_back(RenderEntityCreateData{
            entity,
        });
    }
}

void EntityLifeCycleRenderDataSystemR::AddRenderEntityUpdateVSM(ecs::EntityID entity)
{
    std::lock_guard guard(mRenderEntityVSMUpdateListMapMutex);

    auto iter = mRenderEntityVSMUpdateListMap.find(entity);
    if (iter == mRenderEntityVSMUpdateListMap.end())
    {
        auto* transformSys = mRenderWorld->GetRenderSystem<TransformSystemR>();
        auto* aabbSys = mRenderWorld->GetRenderSystem<AABBSystemR>();
        auto* renderNodeSys = mRenderWorld->GetRenderSystem<RenderNodeSystemR>();

        auto [aabbComp, tilePositionComp, renderNodeComp] = mRenderWorld->GetComponent<AABBComponentR, TilePositionComponentR, RenderNodeComponentR>(entity);

        auto renderNodeCompReader = renderNodeComp.Read();

        BoundingBox boundingBox = aabbSys->GetWorldAABB(aabbComp.Read());
        Float3 tilePosition = transformSys->GetTilePosition(tilePositionComp.Read());

        auto* renderNode = renderNodeSys->GetRenderNode(renderNodeComp.Read());

        mRenderEntityVSMUpdateListMap.insert({entity, mRenderEntityVSMUpdateList.size()});
        mRenderEntityVSMUpdateList.push_back(RenderEntityVSMUpdateData{
            entity,
            boundingBox,
            tilePosition,
            renderNode->IsCastShadow(),
        });
    }
}

void EntityLifeCycleRenderDataSystemR::AddRenderEntityChanged(ecs::EntityID entity, RenderEntityChangeData::ChangeType changeType)
{
    SizeType changeDataIndex = 0;

    auto iter = mRenderEntityChangeListMap.find(entity);
    if (iter == mRenderEntityChangeListMap.end())
    {
        changeDataIndex = mRenderEntityChangeList.size();

        mRenderEntityChangeListMap.insert({entity, changeDataIndex});
        mRenderEntityChangeList.push_back({});
    }
    else
    {
        changeDataIndex = iter->second;
    }

    auto* transformSys = mRenderWorld->GetRenderSystem<TransformSystemR>();
    auto* aabbSys = mRenderWorld->GetRenderSystem<AABBSystemR>();
    auto* renderNodeSys = mRenderWorld->GetRenderSystem<RenderNodeSystemR>();

    auto transformComp = mRenderWorld->GetComponent<TransformComponentR>(entity);
    auto aabbComp = mRenderWorld->GetComponent<AABBComponentR>(entity);
    auto tilePositionComp = mRenderWorld->GetComponent<TilePositionComponentR>(entity);
    auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(entity);

    BoundingBox boundingBox = aabbSys->GetWorldAABB(aabbComp.Read());
    Float3 tilePosition = transformSys->GetTilePosition(tilePositionComp.Read());
    Float3 worldPosition = transformSys->GetWorldTranslation(transformComp.Read());
    auto trsTransform = renderNodeSys->GetRenderNode(renderNodeComp.Read())->GetWorldTransform();

    auto& changeData = mRenderEntityChangeList[changeDataIndex];
    changeData.entity = entity;
    changeData.type |= changeType;
    changeData.prevBoundingBox = boundingBox;
    changeData.prevTilePosition = trsTransform.TilePosition;
    changeData.currBoundingBox = BoundingBox(worldPosition, boundingBox.GetExtent());
    changeData.currTilePosition = tilePosition;
}
}   // namespace cross