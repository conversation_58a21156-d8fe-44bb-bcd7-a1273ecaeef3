#include "EnginePrefix.h"
#include "RenderWindowR.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "NativeGraphicsInterface/NGI.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/RenderPipelineSetting.h"
#include "CECommon/Common/CmdSettings.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "CrossBase/Threading/RenderingThread.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"

cross::RenderWindowR::RenderWindowR(const RenderWindowInitInfo& info)
    : mSwapchain(nullptr)
{
    DispatchRenderingCommandWithToken([=] {
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
         
        cross::NGISwapchainDesc desc;
        desc.Window = static_cast<NativeWindow>(info.handle);
        desc.Width = info.width;
        desc.Height = info.height;
        desc.ScreenMode = EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeStandAlone ? static_cast<NGIScreenMode>(EngineGlobal::GetSettingMgr()->GetFullScreen()) : NGIScreenMode::Windowed;
        desc.BufferCount = CmdSettings::Inst().gMaxQueuedFrame;
        desc.PresentMode = CmdSettings::Inst().gVsync ? NGIPresentMode::FIFO : NGIPresentMode::Immediate;
        desc.GraphicsCommandQueue = rendererSystem->GetDefaultCommandQueue();
        desc.CommandQueue = rendererSystem->GetDefaultCommandQueue();
        
        switch (EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting()->BackBufferFormat)
        {
        case BackBufferPixelFormat::R10G10B10A2:
            desc.Format = mHDRFormat;
            mSwapchain.reset(GetNGIDevice().CreateSwapchain(desc));
            if (mSwapchain->GetBuffer(0))
            {
                break;
            }
        default:
            desc.Format = mDefaultFormat;
            mSwapchain.reset(GetNGIDevice().CreateSwapchain(desc));
            break;
        }

        mUndefinedBackbuffers = desc.BufferCount;
    });
}

cross::RenderWindowR::~RenderWindowR()
{
    EngineGlobal::GetRenderEngine()->GetGlobalSystem<cross::RendererSystemR>()->Flush();
}

bool cross::RenderWindowR::Present()
{
    if (!mMinimized)
    {
        return mSwapchain->Present();
    }
    return false;
}

bool cross::RenderWindowR::Acquire(NGIFence* toSingalFence)
{
    if (!mMinimized)
    {
        return mSwapchain->Acquire(toSingalFence);
    }
    return false;
}

#if CROSSENGINE_ANDROID
void cross::RenderWindowR::ReCreateSurface(NativeWindow nativeWindow)
{
    if (mSwapchain)
    {
        mSwapchain->ReCreateSurface(nativeWindow);
    }
}
#endif

void cross::RenderWindowR::Resize(UInt32 width, UInt32 height, cross::NGIScreenMode screenMode)
{
    if (width == 0 && height == 0)
    {
        mMinimized = true;
    }
    else
    {
        if (mMinimized)
        {
            mMinimized = false;
            EngineGlobal::GetRenderEngine()->GetGlobalSystem<cross::RendererSystemR>()->Flush();
        }
        mSwapchain->ResizeBuffers(width, height,screenMode);
        mUndefinedBackbuffers = mSwapchain->GetDesc().BufferCount;
    }
}

bool cross::RenderWindowR::WaitForPresent(UInt64 presentid, UInt64 timeout)
{
    auto* vkswapchain = dynamic_cast<VulkanSwapchain*>(mSwapchain.get());
    return vkswapchain->WaitForPresent(presentid, timeout);
}

std::tuple<UInt32, UInt32> cross::RenderWindowR::GetSize()
{
    auto& desc = mSwapchain->GetDesc();
    return { desc.Width, desc.Height, };
}

cross::REDTexture* cross::RenderWindowR::PrepareBackbuffer(NGICommandList* cmdList)
{
    auto* RED = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor();
    
    UInt32 index = mSwapchain->GetCurrentBackBufferIndex();
    if (EngineGlobal::GetSettingMgr()->GetUseSeparatePresentThread()) 
    {
        if (index == UINT_MAX)
            index = 0;

        mBackIndex = mBackIndex % CmdSettings::Inst().gMaxQueuedFrame;
        if (mBackIndex != index)
            index = mBackIndex;

        mBackIndex++;
    }
    auto* backbuffer = mSwapchain->GetBuffer(index);

    if (mUndefinedBackbuffers)
    {
        NGITextureBarrier barrier
        {
            backbuffer,
            0,
            NGIResourceState::Undefined,
            NGIResourceState::Present,
            true,
        };
        cmdList->ResourceBarrier(0, nullptr, 1, &barrier);
        mUndefinedBackbuffers--;
    }

    return RED->AllocateTexture("Backbuffer", backbuffer, NGIResourceState::Present);
}
