#pragma once
#include "NativeGraphicsInterface/NGI.h"
#include <unordered_map>
#include "RenderEngine/RenderEngineForward.h"
#include "Resource/IComputerShaderR.h"
#include "Resource/Shader.h"

namespace cross
{

struct PropertySet;

class ComputeShaderR : public IComputeShaderR
{
public:
    RENDER_ENGINE_API ComputeShaderR(resource::ComputeShader* gameObject) : mGameObject{ gameObject } {}

    RENDER_ENGINE_API ~ComputeShaderR();

    virtual void Reset(resource::ComputeShader*);

    RENDER_ENGINE_API std::tuple<NGIComputePipelineState*, resource::ComputeProgramDesc*> GetPipelineState(PropertySet& context, const NameID& kernel);

    RENDER_ENGINE_API void GetThreadGroupSize(const NameID& kernel, UInt32& x, UInt32& y, UInt32& z);
private:
    std::shared_mutex mPipelineStatesMutex;
	std::unordered_map<const NGIComputePipelineStateDesc*, std::unique_ptr<NGIComputePipelineState>, NGIObjectDescHasher, NGIObjectDescHasher> mPipelineStates;
	
    resource::ComputeShader* mGameObject = nullptr;
};

}
