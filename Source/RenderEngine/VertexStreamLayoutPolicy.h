#pragma once
#include "CECommon/Common/FrameContainer.h"
#include "CrossBase/Math/CrossMath.h"
#include "ECS/Develop/Framework/Types.h"
#include "Resource/MeshAssetData.h"

#include "RenderEngine/AnimCpuSkin.h"
#include "RenderEngine/MeshBlendShapeUtil.h"
#include "RenderEngine/RenderGeometry.h"
#include "RenderEngine/RenderFactoryNGIResPtr.h"
#include "RenderEngine/IVertexStreamLayoutPolicy.h"
#include "RenderEngine/ModelSystemR.h"
#include "RenderEngine/SkeletonGpuSkin.h"
#include "RenderEngine/RenderMesh.h"

namespace cross {

struct NGIStagingBuffer;
class MaterialR;

//struct VertexUploadBufferBinding
//{
//    const UInt8* mSrc{nullptr};
//    UInt32 mStride{0};
//    UInt32 mChannelOffset{0};
//    UInt32 mSize{ 0 };
//};

struct VertexUploadBufferSpace
{
    std::vector<VertexUploadBufferBinding> Bindings;
    UInt32 Location;
};

class VertexStreamLayoutBlendShapeParameter : public IVertexStreamLayoutParameter
{
public:
    bool HasBlendShape {false};
    const BlendShapeVertexData* ModelBlendShapeVertexDataPtr{nullptr};
};

class VertexStreamLayoutSkinParameter : public VertexStreamLayoutBlendShapeParameter
{
public:
    // Corresponding entity's specify model skin mode
    GeomertySkinnedMode::Type SkinMode              = GeomertySkinnedMode::Dummy;
    // Corresponding entity which need be skinned 
    ecs::EntityID Entity                            = ecs::EntityID::InvalidHandle();
    // Corresponding model index within this entity's ModelComponentR
    cross::PrimaryModelCompH ModelIndex             = cross::PrimaryModelCompH::InvalidHandle();
    // Corresponding model's all sub model materials
    FrameVector<MaterialR*>* AllSubModelMaterials   = nullptr;
    // Corresponding model's skin matrices which = bind_pose_inv * anim_pose
    FrameVector<SIMDMatrix>* SkinningMatrices       = nullptr;
    // Corresponding model's mesh channels data ptr
    cross::anim::SkeltMeshCPUSkinUtil::InputVertexSkinningInfo VertexSkinningInfo;
    // Corresponding model's skin channels data ptr
    cross::anim::SkeltMeshCPUSkinUtil::SkinWeightInfo SkinningWeightInfo;

    /*=============================================================================
    CPU SKIN concerns: Staging buffer properties
    =============================================================================*/

    NGIBufferPtr VB{nullptr};
    StagingBufferWrap VBStagingBufferWrap;
    UInt32 VBSize{0};
 
    UInt32 mCurLodVertexStart{0};
    UInt32 mCurLodVertexEnd{0};
    SkinLevel mCurSkinLevel{SkinLevel::All};

    VertexUploadBufferBinding UploadBufferBinding[MaxVertexChannelCount];
    UInt32 BufferBindingCount{0};
};

class VertexStreamLayoutStaticMeshParameter : public VertexStreamLayoutBlendShapeParameter
{
public:
    using MeshAssetDataHolder = std::variant<MeshAssetDataPtr, MeshAssetDataResourcePtr>;
public:
    const MeshAssetData* MeshAsset = nullptr;
    MeshAssetDataHolder MeshAssetHolder;
};

class VertexStreamLayoutUtil
{
public:
    static void SetIndexBufferForGeometryPacket(const MeshAssetData* meshAsset, GeometryPacketPtr geoPak, UInt32 skipIndexCount = 0);
    static void SetRenderGeometryDataFromGeometryPacket(MeshR* renderMesh, const MeshAssetData* meshAsset, GeometryPacketPtr geoPak, UInt32 skipVertexCount = 0, UInt32 skipIndexCount = 0);
    static void SetRenderGeometryDataFromLODGeometryPacket(MeshR* renderMesh, const MeshAssetData* meshAsset, GeometryPacketPtr geoPak, UInt8 selectedLOD);

    static void CollectVertexChannelDataForStaticMesh(
        const MeshAssetData* meshAsset, 
        VertexUploadBufferBinding (&outUploadBufferBinding)[MaxVertexChannelCount], 
        UInt32& outBufferBindingCount, 
        std::vector<VertexStreamLayout>& outStreamLayouts,
        VertexStreamLayoutBlendShapeParameter* meshParam = nullptr);

    static UInt32 CollectVertexChannelDataForCPUSkinnedMesh(
        const MeshAssetData* meshAsset, 
        VertexUploadBufferBinding (&outUploadBufferBinding)[MaxVertexChannelCount], 
        UInt32& outBufferBindingCount,
        std::vector<VertexStreamLayout>& outStreamLayouts,
        VertexStreamLayout& outStreamDesc,
        VertexStreamLayoutSkinParameter* skinParam = nullptr,
        VertexChannel PrePoistionChannel = VertexChannel::PositionT);

    static void CollectVertexChannelDataSeparatingSkinningAndStatic(
        const MeshAssetData*                              inMeshAsset, 
        std::array<std::vector<VertexStreamLayout>, 2>&   outStreamLayouts, // 0 for skinned mesh, 1 for static mesh
        std::array<VertexUploadBufferSpace, 2>&           outUploadBufferSpaces,
        VertexStreamLayoutSkinParameter*                  outSkinningParam = nullptr);

    static inline const UInt32 skinnedBufferSpaceIndex = 0;
    static inline const UInt32 staticBufferSpaceIndex = 1;
    static inline constexpr UInt32 skinnedVertexBufferCount = 2; 
};

class VertexStreamLayoutSkinningAndStaticParameter : public IVertexStreamLayoutParameter
{
public:
    /*
     * Holds vertex channels' VertexUploadBufferBinding VertexBufferSpaces[0].Bindings[streamIndex] corresponding to VertexBufferLayouts[0][streamIndex]
     * 0 for Skinned vertex channels
     * 1 for Static vertex channels
     */
    std::array<VertexUploadBufferSpace, VertexStreamLayoutUtil::skinnedVertexBufferCount> VertexBufferSpaces;
    /*
     * Element 0 is a collection of SkinnedMesh Channels, consists of n streams, each stream stores one channel for bindless access
     * Element 1 is a collection of StaticMesh Channels
     */
    std::array<std::vector<VertexStreamLayout>, VertexStreamLayoutUtil::skinnedVertexBufferCount> VertexBufferLayouts;
    const MeshAssetData* MeshAsset = nullptr;
    MeshAssetDataResourcePtr MeshAssetHolder;
};

// Used for static mesh and GPUSkinned skeletal mesh without blend shape.
class VertexStreamLayoutPolicyStaticAllInOne : public IVertexStreamLayoutPolicy
{
public:
    virtual void AssembleGpuResource(MeshR* renderMesh, const MeshAssetData* meshAsset, IVertexStreamLayoutParameter* meshParameter = nullptr) override;

    virtual void AssembleMergedGpuResource(std::vector<MeshR*>& renderMeshes, const std::vector<MeshAssetDataResourcePtr>& meshAssets, MergedGeometryPacket& mergedGeometryPacket) override;
};

/** Used for skinned mesh separate POS NORMAL TANGENT & other static channels
 *  @param outRenderGeometryList that this Policy could switched from cpuskin, remaining the previous render geometries
*/
class VertexStreamLayoutPolicySkinningAndStatic : public IVertexStreamLayoutPolicy
{
public:
    virtual void AssembleGpuResource(MeshR* renderMesh, const MeshAssetData* meshAsset, IVertexStreamLayoutParameter* meshParameter = nullptr) override;
};

// Used for static mesh and GPUSkinned skeletal mesh with blend shape.
class VertexStreamLayoutPolicyStaticBlendShape : public IVertexStreamLayoutPolicy
{
public:
    virtual void AssembleGpuResource(MeshR* renderMesh, const MeshAssetData* meshAsset, IVertexStreamLayoutParameter* meshParameter = nullptr) override;
};

// Used for CPUSkinned skeletal mesh with/without blend shape
class VertexStreamLayoutPolicyCPUSkin : public IVertexStreamLayoutPolicy
{
public:
    virtual void AssembleGpuResource(MeshR* renderMesh, const MeshAssetData* meshAsset, IVertexStreamLayoutParameter* meshParameter = nullptr) override;
};

class VertexStreamLayoutStaticLODStreamingParameter : public IVertexStreamLayoutParameter
{
public:
    bool EnableStreaming = false;
    SInt8 SelectedLOD = -1; // -1 means all, but invlid ?
};

class VertexStreamLayoutPolicyStaticLODStreaming : public IVertexStreamLayoutPolicy
{
public:
    virtual void AssembleGpuResource(MeshR* renderMesh, const MeshAssetData* meshAsset, IVertexStreamLayoutParameter* meshParameter = nullptr) override;
};
};   // namespace cross
