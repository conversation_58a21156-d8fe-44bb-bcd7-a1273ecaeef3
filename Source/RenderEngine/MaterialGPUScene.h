#pragma once
#include "GPUScene/ObjectIndexAllocator.h"
#include "RenderMaterial.h"
#include "GPUScene/GPUScene.h"

namespace cross {

class MaterialGPUScene
{
public:
    UInt32 Allocate(UInt32 byteStride);

    void Free(UInt32 byteStride, UInt32 materialIndexStart);

    void* UploadData(UInt32 sizeInByte, UInt32 dstBufferOffsetInByte, bool clearMemory = true);

    void ResizeGPUBuffer();

    void UploadDataToGPUBuffer();
    
    void MarkMaterialCacheDirty(MaterialR* dirtyMaterial);

    void UpdateMaterialCache();

    void AddMaterialCacheByteSize(int byteSizeChange);

private:
    int mMtlScratchByte = 0;
    int mMtlVarientByte = 0;
    std::mutex mMtlAddMutex;
    std::mutex mMarkMaterialCacheByteSizeMutex;
    std::mutex mMarkMaterialDirtyMutex;
    std::set<MaterialR*> mDirtyCacheMaterial;
    
    ObjectIndexAllocator mGPUMaterialBufferIndexAllocator;
    std::mutex mGPUMaterialBufferIndexAllocatorMutex;
    ScatterBytesUploadBuffer mGPUMaterialUploadBuffer;
};

}  // namespace cross
