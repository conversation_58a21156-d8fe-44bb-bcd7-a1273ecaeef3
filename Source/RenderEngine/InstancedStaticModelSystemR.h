#pragma once

#include "CECommon/Common/FrameStdContainer.h"
#include "CrossBase/Math/CrossMath.h"
#include "ECS/Develop/Framework/Types.h"
#include "CECommon/Common/RenderSystemBase.h"
#include "RenderEngine/RenderWorldConst.h"
#include "RenderEngine/RenderNode/InstancedStaticModelRenderNode.h"
#include "RenderEngine/RenderGeometry.h"
#include "Resource/MeshAssetDataResource.h"
#include "Resource/InstanceDataResource.h"
#include "RenderEngine/MeshBlendShapeUtil.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDCulling.h"
#include "memory/allocator/range_allocator.hpp"
#include "RenderEngine/RenderNode/StaticModelRenderNode.h"
#include "RenderEngine/RenderMesh.h"
#include <deque>

namespace cross {
struct ComponentDesc;
class MaterialR;

template<typename T>
struct InstancedStaticModelChangeList
{
public:
    InstancedStaticModelChangeList()
        : mResource(16 * 1024 * 1024, 16 * 1024 * 1024)
    {
        mQueue = std::pmr::set<T>(&mResource);
    }
    void EmplaceChangeData(const T& ID)
    {
        std::unique_lock<std::shared_mutex> lock(mMtx);
        mQueue.insert(ID);
    }
    void EmplaceChangeData(T&& ID)
    {
        std::unique_lock<std::shared_mutex> lock(mMtx);
        mQueue.insert(ID);
    }
    void EmplaceChangeData(T& ID)
    {
        std::unique_lock<std::shared_mutex> lock(mMtx);
        mQueue.insert(ID);
    }
    bool HasChangeData(const T& ID)
    {
        std::shared_lock<std::shared_mutex> lock(mMtx);
        return mQueue.count(ID) > 0;
    }
    T PopFirstChangeData()
    {
        std::unique_lock<std::shared_mutex> lock(mMtx);
        T ret = *mQueue.begin();
        mQueue.erase(mQueue.begin());
        return ret;
    }
    UInt32 GetCount() const
    {
        std::shared_lock<std::shared_mutex> lock(mMtx);
        return static_cast<UInt32>(mQueue.size());
    }
    auto& GetContainer() { return mQueue; }

private:
    std::pmr::set<T> mQueue;
    mutable std::shared_mutex mMtx;
    gbf::allocator::RangeAllocatorResource mResource;
};

struct InstancedStaticModelComponentR : ecs::IComponent
{
    //struct SubModelProperty
    //{
    //    bool mVisible = true;
    //    std::vector<MaterialR*> mMaterials;   // for each LOD
    //};

    struct StaticSingleLODModelProperty
    {
        struct StaticSubModelProperty
        {
            MaterialR* mMaterial{};

            bool mVisible{true};
        };

        std::vector<StaticSubModelProperty> mSubModelProperties{};
    };

    CEFunction(Reflect)
    RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();

public:
    bool mVisible = true;
    bool mReceiveDecals = true;
    bool mIsStaticBuilt = false;
    bool mRenderMeshDirty = true;
    bool mNeedRebuildClusterTree = false;

    MeshAssetDataResourcePtr mAsset;
    std::vector<StaticSingleLODModelProperty> mLODModelProperties;
    EntityDistanceCulling mDistanceCulling;
    float mGlobalScale = 1.0f;
    std::shared_ptr<ModelRenderNode> mRenderNode = std::make_shared<InstancedStaticModelRenderNode>();
    MeshR* mRenderMesh = nullptr;

    InstanceDataResourcePtr mInstanceDataResource;
};

using RenderInstancedStaticModelComponentHandle = ecs::ComponentHandle<InstancedStaticModelComponentR>;
using RenderInstancedStaticModelComponentReader = ecs::ScopedComponentRead<InstancedStaticModelComponentR>;
using RenderInstancedStaticModelComponentWriter = ecs::ScopedComponentWrite<InstancedStaticModelComponentR>;

struct RenderMesh;

class InstancedStaticModelSystemR : public RenderSystemBase
{
    CEMetaInternal(Reflect)
public:
    RENDER_ENGINE_API static InstancedStaticModelSystemR* CreateInstance();

    virtual void Release() override;

    CEFunction(Reflect)
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

protected:
    virtual void OnBeginFrame(FrameParam* frameParam) override;

    virtual void OnEndFrame(FrameParam* frameParam) override;

    virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

public:
    const MeshAssetDataResourcePtr& GetModelAsset(const RenderInstancedStaticModelComponentReader& reader) const { return reader->mAsset; }
    MeshAssetDataResourcePtr& GetModelAsset(const RenderInstancedStaticModelComponentWriter& writer) { return writer->mAsset; }
    const MeshAssetData* GetMeshAssetData(const RenderInstancedStaticModelComponentReader& reader) const { return reader->mAsset->GetAssetData(); }
    RENDER_ENGINE_API void SetModelAsset(ecs::EntityID entity, MeshAssetDataResourcePtr asset);

    bool GetRenderGeometryListDirty(const RenderInstancedStaticModelComponentReader& reader) const { return reader->mRenderMeshDirty; }
    void SetRenderGeometryListDirty(const RenderInstancedStaticModelComponentWriter& writer, bool dirty) { writer->mRenderMeshDirty = dirty; }

    MeshR* GetModelRenderGeometryList(const RenderInstancedStaticModelComponentWriter& writer) const { return writer->mRenderMesh; }
    void SetModelRenderGeometryList(const RenderInstancedStaticModelComponentWriter& writer, MeshR* renderMesh) { writer->mRenderMesh = renderMesh; }

    bool IsModelStaticBuilt(const RenderInstancedStaticModelComponentReader& reader) const { return reader->mIsStaticBuilt; }
    void SetModelStaticBuilt(const RenderInstancedStaticModelComponentWriter& writer, bool isBuilt) { writer->mIsStaticBuilt = isBuilt; }

    UInt32 GetSubModelCount(const RenderInstancedStaticModelComponentReader& reader) const { return static_cast<UInt32>(reader->mLODModelProperties[0].mSubModelProperties.size()); }

    MaterialR* GetModelMaterial(const RenderInstancedStaticModelComponentReader& modelH, UInt32 subModelIndex, UInt32 lodIndex);
    RENDER_ENGINE_API void SetModelMaterial(ecs::EntityID entity, MaterialR* material, SInt32 subModelIndex, SInt32 lodIndex);

    RENDER_ENGINE_API EntityDistanceCulling GetModelEntityDistanceCulling(ecs::EntityID entity);
    RENDER_ENGINE_API void SetModelEntityDistanceCulling(ecs::EntityID entity, const EntityDistanceCulling& entityCulling);

    RENDER_ENGINE_API void SetGlobalScale(ecs::EntityID entity, const float globalScale, bool rebuildClusterTree);
    bool GetModelVisible(ecs::EntityID entity);
    RENDER_ENGINE_API void SetModelVisible(ecs::EntityID entity, bool isVisible);

    RENDER_ENGINE_API void SetModelReceiveDecals(ecs::EntityID entity, bool value);

    RENDER_ENGINE_API void SetSubModelVisible(ecs::EntityID entity, bool isVisible, UInt32 lodIndex, SInt32 subModelIndex);

    RENDER_ENGINE_API void SetModelDirty(ecs::EntityID entity);

    RENDER_ENGINE_API void SetInstanceDataResource(ecs::EntityID entity, InstanceDataResourcePtr instanceDataResource);

    RENDER_ENGINE_API void NotifyInstanceDataResourceChange(const resource::InstanceDataResource* instanceDataResource);

    // Method for mesh builder
    RenderInstancedStaticModelComponentHandle GetModelHandle(ecs::EntityID entity);

    auto& GetChangeList() { return mChangeList; }

protected:
    InstancedStaticModelChangeList<ecs::EntityID> mChangeList;
};

}   // namespace cross
