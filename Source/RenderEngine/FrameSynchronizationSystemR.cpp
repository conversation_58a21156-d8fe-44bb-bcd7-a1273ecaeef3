#include "EnginePrefix.h"
#include "RenderEngine/FrameSynchronizationSystemR.h"
#include "RenderEngine/WindowSystemR.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderWindowR.h"
#include "NativeGraphicsInterface/NGI.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "NativeGraphicsInterface/SLWrapper.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/CmdSettings.h"
#include "CrossBase/Globals/Globals.h"
namespace cross
{
    RENDER_ENGINE_API threading::StallEvent gVBlankStallEvent;
    RENDER_ENGINE_API threading::StallEvent gPresentVBlankStallEvent;
    RENDER_ENGINE_API std::list<std::chrono::steady_clock::time_point> gVBlankTimestamps; 
    RENDER_ENGINE_API std::mutex gVBlanksMutex;


    const GlobalSystemDesc& FrameSynchronizationSystemR::GetDesc() 
    {
        static const GlobalSystemDesc* sDesc = nullptr;
        if (!sDesc)
        {
            auto* descSystem = EngineGlobal::GetGlobalSystemDescSystem();
            sDesc = descSystem->CreateOrGetGlobalSystemDesc("FrameSynchronizationSystemR", false);
        }
        return *sDesc;
    }

    FrameSynchronizationSystemR* FrameSynchronizationSystemR::CreateInstance(FrameRestriction* inFrameRstr)
    {
        auto sysPtr = new FrameSynchronizationSystemR();
        sysPtr->mFrameRestrictionPtr = inFrameRstr;
        bool value = false;
        if (CmdSettings::Inst().GetConfigValueByName<bool>("gUseEngineStats", value) && value)
        {
            sysPtr->mFrameStats = std::move(std::make_unique<cross::FrameStats>());
        }

        auto* device = &GetNGIDevice();
        sysPtr->mAcquireReadyFence.reset(device->CreateTimeLineFence(CmdSettings::Inst().gMaxQueuedFrame));
        sysPtr->mNGIResourceReadyFence.reset(device->CreateTimeLineFence(0));
        return sysPtr;
    }

    void FrameSynchronizationSystemR::Release()
    {
        delete this;
    }

    void FrameSynchronizationSystemR::OnFirstUpdate(FrameParam* frameParam)
    {
    }

    void FrameSynchronizationSystemR::OnUpdate(FrameParam* frameParam)
    {
        auto& presentRstr = mFrameRestrictionPtr->GrabFrameRestriction(frameParam->GetFrameCount());
        presentRstr.OnRenderUpdateFrame(frameParam);    
    }

    void FrameSynchronizationSystemR::OnBeginFrame(FrameParam* frameParam)
    {
        auto& presentRstr = mFrameRestrictionPtr->GrabFrameRestriction(frameParam->GetFrameCount());
        presentRstr.OnRenderBeginFrame(frameParam);
    }

    void FrameSynchronizationSystemR::OnEndFrame(FrameParam* frameParam)
    {
        auto& presentRstr = mFrameRestrictionPtr->GrabFrameRestriction(frameParam->GetFrameCount());
        presentRstr.OnRenderEndFrame(frameParam);
        if (bShowTimeStats) 
        {
            LOG_INFO("FrameID : {}, Game: {} ms, Render : {} ms. GPU : {} ms", frameParam->GetFrameCount(), frameParam->GetGameDeltaTime(), frameParam->GetRenderDeltaTime(), frameParam->GetGPUDeltaTime());
        }
        FrameMarkNamed("RenderFrame");
    }
    FramePresentRestriction::FramePresentRestriction(threading::FrameFrequencyRestriction const& inRstr)//, UInt32 inFramePresentCount, UInt32 inFramePresentIndex)
        : mFrequenceySync(inRstr)
        //, mGPUStallFence(nullptr)
        //, mFramePresentCount{inFramePresentCount}
        //, mFramePresentIndex{inFramePresentIndex}
    {
        // First time begin frame, ignore all cpu side stall fence
        //mCPUStallEvent.Trigger();

        auto setting_manager = EngineGlobal::GetSettingMgr();
        if (setting_manager)
        {
            setting_manager->GetValue("TargetLatency", mTargetLatency);
            setting_manager->GetValue("MaxLatencyFilter", mMaxLatencyFilter);
        }
    }

    void FramePresentRestriction::OnRenderUpdateFrame(FrameParam* frameParam)
    {
        SCOPED_CPU_TIMING(GroupEngine, "FramePresentR OnRenderUpdateFrame");
            
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

        // cause vulkan validation error of buffer life time
        if (EngineGlobal::GetSettingMgr()->GetUseSeparatePresentThread())
        {
            rendererSystem->GetTransientResourceManager()->OnBeginFrame(frameParam);
        }
    }

    void FramePresentRestriction::OnRenderBeginFrame(FrameParam* frameParam)
    {
        // should be really careful use of the begin dynamic function
        // it new and delete string all the time.
        // causing app slow down after long time run,
        //auto profiler = dynamic_cast<gbf::IProfilerModule*>(GameFramework()->QueryModule(gbf::kModuleProfilerName));

        //auto FrameIDString = std::string("FRAME_").append(std::to_string(frameParam->GetFrameCount()));
        //profiler->BeginSampleDynamic(FrameIDString.c_str(), "");

        {
            SCOPED_CPU_TIMING(GroupEngine, "FramePresentR BeginFrame");
            Assert(threading::TaskSystem::IsInRenderingThread());

            // comment by yazhenyuan 2023/9/6
            // since we have fix the bugs, and make sure dispatch rendercommand after game is executed
            //// step1. First time in begin frame create GPU fence instead of wait device trigger actually
            //if (mGPUStallFence == nullptr)
            //{
            //    Assert(frameParam->GetFrameCount() < (mFramePresentCount + 1));

            //    auto device = GetNGIDevicePtr();
            //    mGPUStallFence = device->CreateFence(frameParam->GetFrameCount() - 1);   // Attention, frame parameter start from 1, not 0
            //}
            //else
            //{
            //    // Time to wait game thread trigger corresponding framebuffer cv wait
            //    mCPUStallEvent.Wait();
            //    mCPUStallEvent.Reset();
            //}

            {
                QUICK_SCOPED_CPU_TIMING("WaitForCurrentFrame");
                if (frameParam->GetFrameCount() >= CmdSettings::Inst().gMaxQueuedFrame)
                {
                    auto* frameResourceReady = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetFrameReadyFence();

                        frameResourceReady->Wait(frameParam->GetFrameCount(), UINT64_MAX);
                }
            }
            
            // set mRenderStart timestamp
            bool value = false;
            if (CmdSettings::Inst().GetConfigValueByName<bool>("gUseEngineStats", value) && value)
            {
                auto& curFrameStats = EngineGlobal::GetRenderEngine()->GetGlobalSystem<FrameSynchronizationSystemR>()->mFrameStats;
                curFrameStats->GetFrameTimer(frameParam->GetFrameCount()).mRenderStart = std::chrono::high_resolution_clock::now();
            }

            if (EngineGlobal::GetSettingMgr()->GetUseSeparatePresentThread())    
            {
                threading::DispatchPresentCommand([=, curFrame = frameParam->GetFrameCount()] {
                    // step2. Acquire next image singal corresponding framebuffer fence
                    auto* windowsSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<WindowSystemR>();
                    for (auto* window : windowsSystem->GetWindows())
                    {
                        if (!window->Acquire(nullptr))
                            LOG_ERROR("Fail to acquire backbuffer");
                    }

                    EngineGlobal::GetRenderEngine()->GetGlobalSystem<FrameSynchronizationSystemR>()->mAcquireReadyFence->Signal(curFrame);
                });
            }
            else
            {
                auto* windowsSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<WindowSystemR>();
                for (auto* window : windowsSystem->GetWindows())
                {
                    // auto* swapchain = window->GetSwapchain();
                    if (!window->Acquire(nullptr))
                        // if (!swapchain->Acquire(/*firstFrame ? nullptr : mGPUStallFence*/ nullptr))
                        LOG_ERROR("Fail to acquire backbuffer");
                }
            }
        }

        //profiler->EndSample();
    }

    void FramePresentRestriction::OnRenderEndFrame(FrameParam* frameParam)
    {
        SCOPED_CPU_TIMING(GroupEngine, "FramePresentR EndFrame");
        Assert(threading::TaskSystem::IsInRenderingThread());
        //frameParam->SetRenderEndTime();
        
        // set mRenderEnd timestamp
        bool value = false;
        bool UseStats = CmdSettings::Inst().GetConfigValueByName<bool>("gUseEngineStats", value) && value;
        if (UseStats)
        {
            auto& curFrameStats = EngineGlobal::GetRenderEngine()->GetGlobalSystem<FrameSynchronizationSystemR>()->mFrameStats;
            curFrameStats->GetFrameTimer(frameParam->GetFrameCount()).mRenderEnd = std::chrono::high_resolution_clock::now();
        }

        // Time to notify render thread unlock corresponding wait device fence
        auto renderEngine = EngineGlobal::GetRenderEngine();
        auto* windowsSystem = renderEngine->GetGlobalSystem<WindowSystemR>();

        // set mCPUPresentStart timestamp
        if (UseStats)
        {
            auto& curFrameStats = EngineGlobal::GetRenderEngine()->GetGlobalSystem<FrameSynchronizationSystemR>()->mFrameStats;
            curFrameStats->GetFrameTimer(frameParam->GetFrameCount()).mCPUPresentStart = std::chrono::high_resolution_clock::now();
        }
        if (!EngineGlobal::GetSettingMgr()->GetUseSeparatePresentThread())
        {
#ifdef WIN32
            if (SLWrapper::Get().GetReflexAvailable())
            {
                SLWrapper::Get().ReflexCallback_PresentStart(frameParam->GetFrameCount());
            }
#endif


            for (auto* window : windowsSystem->GetWindows())
            {
                // auto* swapchain = window->GetSwapchain();

                // if (!swapchain->Present())
                if (!window->Present())
                    LOG_ERROR("Swapchain present failed");
            }
            {
                std::scoped_lock lock(RendererSystemR::sCallbackMutex);
                std::for_each(RendererSystemR::sQueuePresentTriggerCbs.begin(), RendererSystemR::sQueuePresentTriggerCbs.end(), 
                    [=](auto& cb) { cb.second(frameParam->GetFrameCount() + CmdSettings::Inst().gMaxQueuedFrame); });
            }   
#ifdef WIN32
            if (SLWrapper::Get().GetReflexAvailable())
            {
                SLWrapper::Get().ReflexCallback_PresentEnd(frameParam->GetFrameCount());
            }
#endif
        }
        // set mCPUPresentEnd timestamp
        if (UseStats)
        {
            auto& curFrameStats = EngineGlobal::GetRenderEngine()->GetGlobalSystem<FrameSynchronizationSystemR>()->mFrameStats;
            curFrameStats->GetFrameTimer(frameParam->GetFrameCount()).mCPUPresentEnd = std::chrono::high_resolution_clock::now();
        }
    }

    double FramePresentRestriction::Throttle(FrameParam* frameParam)
    {
        SCOPED_CPU_TIMING(GroupEngine, "FramePresentG BeginFrame");
        Assert(threading::TaskSystem::IsInGameThread());

        //  Time to wait corresponding GPU fence which signal after device
        auto renderEngine = EngineGlobal::GetRenderEngine();
        auto* rendererSys = renderEngine->GetGlobalSystem<RendererSystemR>();

        // set mGameSyncGPUStart timestamp
        bool value = false;
        bool UseStats = CmdSettings::Inst().GetConfigValueByName<bool>("gUseEngineStats", value) && value;
        if (UseStats)
        {
            auto& curFrameStats = EngineGlobal::GetRenderEngine()->GetGlobalSystem<FrameSynchronizationSystemR>()->mFrameStats;
            curFrameStats->GetFrameTimer(frameParam->GetFrameCount()).mGameSyncGPUStart = std::chrono::high_resolution_clock::now();
        }

        if (CmdSettings::Inst().gVsync && !EngineGlobal::GetSettingMgr()->GetUseSeparatePresentThread())
        {
            SCOPED_CPU_TIMING(GroupEngine, "FramePresentG Waitvblank");
            gVBlankStallEvent.Wait();
            gVBlankStallEvent.Reset();
        }

        if (mFrequenceySync.GetFrameRate() > 0)
        {
            QUICK_SCOPED_CPU_TIMING("FrequenceySync");
            mFrequenceySync.Sync();
        }

        // manually throttle for latency

        if (UseStats)
        {
            auto & frameStats = EngineGlobal::GetRenderEngine()->GetGlobalSystem<FrameSynchronizationSystemR>()->mFrameStats;

            // plus one to make sure all data is ready
            auto& prveTimer = frameStats->GetFrameTimer(frameParam->GetFrameCount() + 1);

            const float currentRatio = 0.5f;

            mGameRenderSlop = mGameRenderSlop * (1.0f - currentRatio) + static_cast<float>(frameStats->MapTimerMode(prveTimer, TimeStatsMode::GameRenderSlop) * currentRatio);
            mRenderGPUSlop = mRenderGPUSlop * (1.0f - currentRatio) + static_cast<float>(frameStats->MapTimerMode(prveTimer, TimeStatsMode::RenderGPUSlop) * currentRatio);
            mGpuPresentSlop = mGpuPresentSlop * (1.0f - currentRatio) + static_cast<float>(frameStats->MapTimerMode(prveTimer, TimeStatsMode::GPUPresentSlop) * currentRatio);

            auto all_slops = std::max(0.0f, mGameRenderSlop) + std::max(0.0f, mRenderGPUSlop) + std::max(0.0f, mGpuPresentSlop);
            mTotalSlop = mTotalSlop * (1.0f - currentRatio) + all_slops * currentRatio;
            mGPULatency = mGPULatency * (1.0f - currentRatio) + static_cast<float>(frameStats->MapTimerMode(prveTimer, TimeStatsMode::GPUDuration) * currentRatio);
            
            mTotalLatency = mTotalLatency * (1.0f - currentRatio) + static_cast<float>(frameStats->MapTimerMode(prveTimer, TimeStatsMode::FrameDuration) * currentRatio);

            mLatencyThrottle = 0.0f;
            // use 100 to mask out large value to avoid wait inifinite during loading/or some task.
            if (mEnableLatencyThrottle && mTotalSlop > mTargetLatency && mTotalSlop < mMaxLatencyFilter)
            {
                SCOPED_CPU_TIMING(GroupEngine, "Latency Throttle");
                timerSleep((mTotalSlop - mTargetLatency) / 1000.0);
                mLatencyThrottle = (mTotalSlop - mTargetLatency);
            }
        }

        if (EngineGlobal::GetSettingMgr()->GetUseSeparatePresentThread())
        {
            if (frameParam->GetFrameCount() > CmdSettings::Inst().gMaxQueuedFrame)
            {
                auto* frameCommandReady = rendererSys->GetFrameCommandReadyFence();
                frameCommandReady->Wait(frameParam->GetFrameCount(), UINT64_MAX);
            }
        }

        {
            QUICK_SCOPED_CPU_TIMING("WaitForCurrentFrame");
            if (frameParam->GetFrameCount() >= CmdSettings::Inst().gMaxQueuedFrame)
            {
                auto* frameResourceReady = rendererSys->GetFrameReadyFence();
                frameResourceReady->Wait(frameParam->GetFrameCount(), UINT64_MAX);
            }
        }

        // todo frameRestrictionSync

        // todo timeStamp for vsync
        return 0.0;
    }

    void FramePresentRestriction::OnGameBeginFrame(FrameParam* frameParam)
    {
        QUICK_SCOPED_CPU_TIMING("OnGameBeginFrame");
        // set mGameSyncGPUEnd timestamp
        bool value = false;
        bool UseStats = CmdSettings::Inst().GetConfigValueByName<bool>("gUseEngineStats", value) && value;
        if (UseStats)
        {
             auto& curFrameStats = EngineGlobal::GetRenderEngine()->GetGlobalSystem<FrameSynchronizationSystemR>()->mFrameStats;
             curFrameStats->GetFrameTimer(frameParam->GetFrameCount()).mGameSyncGPUEnd = std::chrono::high_resolution_clock::now();
        }

        //mCPUStallEvent.Trigger();
        frameParam->SetGameStartTime();
        // set mGameTriggerRender timestamp
        if (UseStats)
        {
             auto& curFrameStats = EngineGlobal::GetRenderEngine()->GetGlobalSystem<FrameSynchronizationSystemR>()->mFrameStats;
             curFrameStats->GetFrameTimer(frameParam->GetFrameCount()).mGameTriggerRender = std::chrono::high_resolution_clock::now();
        }
    }

    void FramePresentRestriction::OnGameEndFrame(FrameParam* frameParam)
    {
        SCOPED_CPU_TIMING(GroupEngine, "FramePresentG EndFrame");
        Assert(threading::TaskSystem::IsInGameThread());
        frameParam->SetGameEndTime();
        bool value = false;
        bool UseStats = CmdSettings::Inst().GetConfigValueByName<bool>("gUseEngineStats", value) && value;
        // set game delta time
        if (UseStats)
        {
             auto& curFrameStats = EngineGlobal::GetRenderEngine()->GetGlobalSystem<FrameSynchronizationSystemR>()->mFrameStats;
             curFrameStats->GetFrameTimer(frameParam->GetFrameCount()).mGameDeltaTime = frameParam->GetGameDeltaTime();
        }
    }

    //void FramePresentRestriction::MakeCurrentFrameCpuSyncOffset(std::chrono::duration<double, std::micro> offset) 
    //{
    //    // mFrameSyncOffset = offset;
    //}

    FrameRestriction::FrameRestriction(UInt32 N)
        : mFrequencySync(60.0)
    {
        double framteRate = 60.0;
        if (EngineGlobal::GetSettingMgr()->GetValue("FrameRate", framteRate))
        {
            mFrequencySync = threading::FrameFrequencyRestriction(framteRate);
        }

        mPresentSync = {N, {mFrequencySync}};
        mCursor = {0};
    } 

    FramePresentRestriction& FrameRestriction::GrabFrameRestriction(UInt32 frameCount)
    {
        return mPresentSync[frameCount % Size()];
    }

    FramePresentRestriction& FrameRestriction::GrabFrameRestriction_GameThread()
    {
        return mPresentSync[mCursor % Size()];
    }

    FrameStats::FrameStats()
    {
        bool value = false;
        bDisplayLatency = CmdSettings::Inst().GetConfigValueByName<bool>("gUseEngineStats", value) && value;
        TimeStatsMode mode{
            TimeStatsMode::GameDuration   | TimeStatsMode::RenderDuration | TimeStatsMode::GPUDuration | 
            TimeStatsMode::GameGPUSyncSlop | TimeStatsMode::RenderStart | TimeStatsMode::GPUStart | TimeStatsMode::FrameDuration,
        };
        CreateLineInfo(mode);
        mInited = true;
        mFrameTimerSet.resize(10);
        // mSetSize = CmdSettings::Inst().gMaxQueuedFrame;
    }

    FrameStats::~FrameStats() {}

    void FrameStats::CreateLineInfo(TimeStatsMode mode)
    {
       /* auto profiler = dynamic_cast<gbf::IProfilerModule*>(GameFramework()->QueryModule(gbf::kModuleProfilerName));


        if (EnumHasAnyFlags(mode, TimeStatsMode::GameGPUSyncSlop))
        {
            FrameProLine curLine{};
            curLine.mTsm = TimeStatsMode::GameGPUSyncSlop;
            curLine.mLineInfo = profiler->CreateCustomLineInfo("GameWaitGPUEnd", "GameWaitGPUEnd", "ms", 5);
            mLineInfoSet.emplace_back(std::move(curLine));
        }


        if (EnumHasAnyFlags(mode, TimeStatsMode::GameDuration))
        {
            FrameProLine curLine{};
            curLine.mTsm = TimeStatsMode::GameDuration;
            curLine.mLineInfo = profiler->CreateCustomLineInfo("GameEnd", "GameEnd", "ms", 2);
            mLineInfoSet.emplace_back(std::move(curLine));
        }

        if (EnumHasAnyFlags(mode, TimeStatsMode::RenderStart))
        {
            FrameProLine curLine{};
            curLine.mTsm = TimeStatsMode::RenderStart;
            curLine.mLineInfo = profiler->CreateCustomLineInfo("RenderStart", "RenderStart", "ms", 6);
            mLineInfoSet.emplace_back(std::move(curLine));
        }

        if (EnumHasAnyFlags(mode, TimeStatsMode::RenderDuration))
        {
            FrameProLine curLine{};
            curLine.mTsm = TimeStatsMode::RenderDuration;
            curLine.mLineInfo = profiler->CreateCustomLineInfo("RenderEnd", "RenderEnd", "ms", 3);
            mLineInfoSet.emplace_back(std::move(curLine));
        }


        if (EnumHasAnyFlags(mode, TimeStatsMode::GPUStart))
        {
            FrameProLine curLine{};
            curLine.mTsm = TimeStatsMode::GPUStart;
            curLine.mLineInfo = profiler->CreateCustomLineInfo("GPUStart", "GPUStart", "ms", 7);
            mLineInfoSet.emplace_back(std::move(curLine));
        }

        if (EnumHasAnyFlags(mode, TimeStatsMode::GPUDuration))
        {
            FrameProLine curLine{};
            curLine.mTsm = TimeStatsMode::GPUDuration;
            curLine.mLineInfo = profiler->CreateCustomLineInfo("GPUEnd", "GPUEnd", "ms", 4);
            mLineInfoSet.emplace_back(std::move(curLine));
        }

        if (EnumHasAnyFlags(mode, TimeStatsMode::FrameDuration))
        {
            FrameProLine curLine{};
            curLine.mTsm = TimeStatsMode::FrameDuration;
            curLine.mLineInfo = profiler->CreateCustomLineInfo("FramePresent", "FramePresent", "ms", 1);
            mLineInfoSet.emplace_back(std::move(curLine));
        }

        if (EnumHasAnyFlags(mode, TimeStatsMode::GPUPresentSlop))
        {
            FrameProLine curLine{};
            curLine.mTsm = TimeStatsMode::GPUPresentSlop;
            curLine.mLineInfo = profiler->CreateCustomLineInfo("GPUPresentStart", "GPUPresentStart", "ms", 9);
            mLineInfoSet.emplace_back(std::move(curLine));
        }
        LOG_INFO("Create Line Info");*/
    }

    void FrameStats::UpdateInfos(UInt32 framecount)
    {
        auto fc = framecount - CmdSettings::Inst().gMaxQueuedFrame;
        auto profiler = dynamic_cast<gbf::IProfilerModule*>(GameFramework()->QueryModule(gbf::kModuleProfilerName));
        auto& curOutputFrameTimer = mFrameTimerSet[fc % mFrameTimerSet.size()];
        for (auto& lineinfo : mLineInfoSet)
        {
            profiler->SetCustomLineValue(lineinfo.mLineInfo, MapTimerMode(curOutputFrameTimer, lineinfo.mTsm));
            //LOG_INFO("The Line name is {}, cost time is : {}", lineinfo.mLineInfo.line_name.c_str(), MapTimerMode(lineinfo.mTsm, fc));
        }

        mUpadtedFrameCount = framecount;
    }

    double FrameStats::MapTimerMode(const FrameTimer & timer, const TimeStatsMode mode)
    {
        auto& curOutputFrameTimer = timer;//
        std::chrono::duration<double, std::milli> deltaObserved;
        switch (mode)
        {
        case TimeStatsMode::FrameDuration:
            deltaObserved = curOutputFrameTimer.mGPUPresentStart - curOutputFrameTimer.mGameStart;
            return deltaObserved.count();
        case TimeStatsMode::GameDuration:
            deltaObserved = curOutputFrameTimer.mGameEnd - curOutputFrameTimer.mGameStart;
            return deltaObserved.count();
        case TimeStatsMode::RenderDuration:
            deltaObserved = curOutputFrameTimer.mRenderEnd - curOutputFrameTimer.mGameStart;
            return deltaObserved.count();
        case TimeStatsMode::GPUDuration:
            deltaObserved = curOutputFrameTimer.mGPUEnd - curOutputFrameTimer.mGameStart;
            return deltaObserved.count();
        case TimeStatsMode::GameGPUSyncSlop:
            deltaObserved = curOutputFrameTimer.mGameSyncGPUEnd - curOutputFrameTimer.mGameStart;
            return deltaObserved.count();
        case TimeStatsMode::RenderStart:
            deltaObserved = curOutputFrameTimer.mRenderStart - curOutputFrameTimer.mGameStart;
            return deltaObserved.count();
        case TimeStatsMode::GPUStart:
            deltaObserved = curOutputFrameTimer.mGPUEnd - curOutputFrameTimer.mGameStart;
            return deltaObserved.count() - curOutputFrameTimer.mGPUDeltaTime;
        case TimeStatsMode::GameRenderSlop:
            deltaObserved = curOutputFrameTimer.mRenderStart - curOutputFrameTimer.mGameEnd;
            return deltaObserved.count();
        case TimeStatsMode::RenderGPUSlop:
            deltaObserved = curOutputFrameTimer.mGPUEnd - curOutputFrameTimer.mRenderEnd;
            return deltaObserved.count() - curOutputFrameTimer.mGPUDeltaTime;
        case TimeStatsMode::GPUPresentSlop:
            deltaObserved = curOutputFrameTimer.mGPUPresentStart - curOutputFrameTimer.mGPUEnd;
            return deltaObserved.count();
        default:
            Assert(false);
            break;
        }
        return 0.0;
    }


    void FrameStats::PushVsyncInterval(std::chrono::steady_clock::time_point begin, std::chrono::steady_clock::time_point end)
    {
        bool value = false;
        bool bUseStats = CmdSettings::Inst().GetConfigValueByName<bool>("gUseEngineStats", value) && value;
        if (bUseStats && CmdSettings::Inst().gVsyncCheck)
        {
            auto fc = (mUpadtedFrameCount - CmdSettings::Inst().gMaxQueuedFrame) ;

            std::shared_lock lock(mPresentMutex);
            for (int i = 0; i < mFrameTimerSet.size(); i++)
            {
                auto& frameTimer = mFrameTimerSet[(fc - i) % mFrameTimerSet.size()];
                if (frameTimer.mGPUPresentStart >= begin && frameTimer.mGPUPresentStart <= end)
                {
                    MissedCount = 0;
                    return;
                }
            }

            // print info for debug
            MissedCount++;

            LOG_WARN("Missed {}", MissedCount.load());

            for (int i = 0; i < mFrameTimerSet.size() /2; i++)
            {
                //Begin at
                auto& frameTimer = mFrameTimerSet[(fc - i) % mFrameTimerSet.size()];

                std::string deltas;
                deltas += " " + std::to_string(MapTimerMode(frameTimer, TimeStatsMode::GameGPUSyncSlop));
                deltas += " " + std::to_string(MapTimerMode(frameTimer, TimeStatsMode::GameDuration));
                deltas += " " + std::to_string(MapTimerMode(frameTimer, TimeStatsMode::RenderStart));
                deltas += " " + std::to_string(MapTimerMode(frameTimer, TimeStatsMode::RenderDuration));
                deltas += " " + std::to_string(MapTimerMode(frameTimer, TimeStatsMode::GPUStart));
                deltas += " " + std::to_string(MapTimerMode(frameTimer, TimeStatsMode::GPUDuration));
                deltas += " " + std::to_string(MapTimerMode(frameTimer, TimeStatsMode::FrameDuration));

                LOG_WARN("Frame {} begin {} {}", fc - i, time::GetHourMinutesSecondMilli(frameTimer.mGameStart), deltas);
            }
        }
    }
}
