#include "EnginePrefix.h"
#include <array>
#include "VertexStreamLayoutPolicy.h"
#include "CECommon/Common/MeshDefines.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameParam.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderFactory.h"
#include "RenderPipeline/Effects/GPass.h"
#include "NGIManager.h"
#include "NativeGraphicsInterface/Statistics.h"

namespace cross {
template<class T1, class T2, class TypeConvFunc>
void TypeConverter(const T1& inNormal, T2& out, TypeConvFunc func)
{
    //out.resize(inNormalVec.size());
    constexpr size_t element_dim = T2::Dim();

    for (int j = 0; j < element_dim; j++)
    {
        func(inNormal.data()[j], out.data()[j]);
    }
}

//////////////////////////////////////////////////////////////////////////
// VertexStreamLayoutUtil
//
//////////////////////////////////////////////////////////////////////////
void VertexStreamLayoutUtil::SetIndexBufferForGeometryPacket(const MeshAssetData* meshAsset, GeometryPacketPtr geoPak, UInt32 skipIndexCount /* = 0*/)
{
    SCOPED_CPU_TIMING(GroupRendering, "SetIndexBufferForGeometryPacket");
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    const IndexStreamAssetData& indexAsset = meshAsset->GetIndexStream();
    UInt32 sizeBytes = indexAsset.mIs16BitIndex ? sizeof(UInt16) : sizeof(UInt32);
    UInt32 ibSize = static_cast<UInt32>(indexAsset.mData.size()) - skipIndexCount * sizeBytes;
    IndexBufferFormat idxFmt = indexAsset.mIs16BitIndex ? IndexFormat_UInt16 : IndexFormat_UInt32;
    NGIBufferPtr ib = RenderFactory::Instance().CreateIndexBuffer(ibSize, idxFmt);
    geoPak->SetIndexStream(ib.get(), ibSize, indexAsset.mCount - skipIndexCount);
    ib->SetDebugName(meshAsset->GetName().c_str());

    UInt8* destBuffer{nullptr};
    const UInt8* srcData = indexAsset.mData.data() + skipIndexCount * sizeBytes;

    if (ib->Mappable())
    {
        destBuffer = static_cast<UInt8*>(ib->Map());
        memcpy(destBuffer, srcData, ibSize);
        ib->Unmap();
    }
    else
    {
        NGIBufferDesc desc{ibSize, NGIBufferUsage::CopySrc};
        NGIStagingBuffer* stagingBuffer = GetNGIDevice().CreateStagingBuffer(desc);
        destBuffer = static_cast<UInt8*>(stagingBuffer->MapRange(NGIBufferUsage::CopySrc, 0, ibSize));

        memcpy(destBuffer, srcData, ibSize);

        stagingBuffer->UnmapRange(0, ibSize);
        NGICopyBuffer region{0, 0, ibSize};
        rendererSystem->UpdateBuffer(ib.get(), stagingBuffer, region, NGIResourceState::Undefined, NGIResourceState::IndexBuffer);
        rendererSystem->DestroyNGIObjectInAsyncThread(stagingBuffer);
    }
}

void VertexStreamLayoutUtil::SetRenderGeometryDataFromGeometryPacket(MeshR* renderMesh, const MeshAssetData* meshAsset, GeometryPacketPtr geoPak, UInt32 skipVertexCount /* = 0*/, UInt32 skipIndexCount/* = 0*/)
{
    SCOPED_CPU_TIMING(GroupRendering, "SetRenderGeometryDataFromGeometryPacket");
    UInt32 totalMeshPartCount = meshAsset->GetAllLodMeshPartCount();
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    {
        std::scoped_lock setDataLock(renderMesh->GetBuildMutex());
        renderMesh->ClearAndResize(totalMeshPartCount);
        UInt32 meshPartStartIndex, meshPartCount;
        meshAsset->GetMeshLodInfo(0, meshPartStartIndex, meshPartCount);
        // UInt32 uvCount = std::min<UInt32>(meshAsset->GetUVChannelCount(), MAX_UV_NUM);
        for (UInt32 p = 0; p < totalMeshPartCount; p++)
        {
            auto& partInfo = meshAsset->GetMeshPartInfo(p);
            auto& geometry = renderMesh->GetRenderGeometry(p);
            geometry.SetData(
                geoPak.get(), partInfo.mVertexCount, partInfo.mVertexStart - skipVertexCount, partInfo.mIndexCount, partInfo.mIndexStart - skipIndexCount, partInfo.mPrimitiveCount, partInfo.mPrimitiveType, static_cast<UInt16>(p));

            const auto* name = meshAsset->GetMeshPartName(p);
            StringHash32 nameHash = HashFunction::HashString32(name->c_str());
            renderMesh->SetNameHash(p, nameHash);

            geometry.mAssetGUID = meshAsset->GetAssetGUID();
        }
    }
}

void VertexStreamLayoutUtil::SetRenderGeometryDataFromLODGeometryPacket(MeshR* renderMesh, const MeshAssetData* meshAsset, GeometryPacketPtr geoPak, UInt8 selectedLOD)
{
    SCOPED_CPU_TIMING(GroupRendering, "SetRenderGeometryDataFromLODGeometryPacket");
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    {
        std::scoped_lock setDataLock(renderMesh->GetBuildMutex());

        // Only fully resize geometries on the first time
        UInt32 totalMeshPartCount = meshAsset->GetAllLodMeshPartCount();
        if (renderMesh->IsGeometryEmpty() || totalMeshPartCount != renderMesh->GetGeometryCount())
        {
            renderMesh->ClearAndResize(totalMeshPartCount);
        }

        auto* lodRenderMesh = renderMesh->GetLODMesh(selectedLOD);
        Assert(lodRenderMesh);
        lodRenderMesh->GeometrySections.clear();

        UInt32 meshPartStartIndex, meshPartCount;
        meshAsset->GetMeshLodInfo(selectedLOD, meshPartStartIndex, meshPartCount);

        auto& firtPartInfo = meshAsset->GetMeshPartInfo(meshPartStartIndex);
        UInt32 vertexOffset = firtPartInfo.mVertexStart;
        UInt32 indexOffset = firtPartInfo.mIndexStart;

        for (UInt32 p = meshPartStartIndex; p < meshPartStartIndex + meshPartCount; p++)
        {
            auto& partInfo = meshAsset->GetMeshPartInfo(p);
            auto& geometry = renderMesh->GetRenderGeometry(p);
            geometry.SetData(geoPak.get(), partInfo.mVertexCount, partInfo.mVertexStart - vertexOffset, partInfo.mIndexCount, partInfo.mIndexStart - indexOffset, partInfo.mPrimitiveCount, partInfo.mPrimitiveType, static_cast<UInt16>(p));

            const auto* name = meshAsset->GetMeshPartName(p);
            StringHash32 nameHash = HashFunction::HashString32(name->c_str());
            renderMesh->SetNameHash(p, nameHash);

            geometry.mAssetGUID = meshAsset->GetAssetGUID();
            lodRenderMesh->GeometrySections.emplace_back(&geometry);
        }
    }
}

void VertexStreamLayoutUtil::CollectVertexChannelDataForStaticMesh(
    const MeshAssetData* meshAsset,
    VertexUploadBufferBinding(&outUploadBufferBinding)[MaxVertexChannelCount],
    UInt32& outBufferBindingCount,
    std::vector<VertexStreamLayout>& outStreamLayouts,
    VertexStreamLayoutBlendShapeParameter* meshParam)
{
    UInt32 channelCount = meshAsset->GetVertexChannelCount();
    Assert(channelCount < MaxVertexChannelCount);

    bool HasBlendShape = false;
    const BlendShapeVertexData* ModelBlendShapeVertexDataPtr = nullptr;
    if (meshParam)
    {
        HasBlendShape = meshParam->HasBlendShape;
        ModelBlendShapeVertexDataPtr = meshParam->ModelBlendShapeVertexDataPtr;
    }

    if (HasBlendShape)
        Assert(ModelBlendShapeVertexDataPtr != nullptr);

    // Collect vertex channel information
    for (UInt32 s = 0; s < gAllVertexSemanticSerial.size(); s++)
    {
        VertexSemantic semantic = GetSemantic(gAllVertexSemanticSerial[s]);
        if (!meshAsset->HasVertexSemantic(semantic))
            continue;

        UInt32 indexCount = semantic != SemanticTexCoord ? 4 : std::max<int>(4, meshAsset->GetUVChannelCount());
        for (UInt32 i = 0; i < indexCount; i++)
        {
            VertexChannel channel = MakeVertexChannel(semantic, i);

            const VertexChannelAssetData* channelData = nullptr;

            if ((channel >= VertexChannel::Position0 && channel <= VertexChannel::PositionLast) ||
                (channel >= VertexChannel::Normal0 && channel <= VertexChannel::NormalLast) ||
                (channel >= VertexChannel::Tangent0 && channel <= VertexChannel::TangentLast))
            {
                channelData = HasBlendShape ? ModelBlendShapeVertexDataPtr->GetVertexChannelData(channel) : meshAsset->GetVertexChannelData(channel);
            }
            else
            {
                channelData = meshAsset->GetVertexChannelData(channel);
            }

            if (channelData)
            {
                outStreamLayouts.emplace_back(channelData->mVertexChannel, channelData->mDataFormat);
                
                outUploadBufferBinding[outBufferBindingCount].mSrc = channelData->mData.data();
                outUploadBufferBinding[outBufferBindingCount].mStride = channelData->mStride;
                outUploadBufferBinding[outBufferBindingCount].mChannelOffset = 0;  // channel offset is always 0 since we only store one channel per stream
                outUploadBufferBinding[outBufferBindingCount].mSize = static_cast<UInt32>(channelData->mData.size());

                if (channelData->mData.size() / channelData->mStride < meshAsset->GetVertexCount())
                {
                    LOG_ERROR("Found {} missing vertex data {} {}, {} required but {} existed", meshAsset->GetName(), gAllVertexSemanticSerialName[s], i, meshAsset->GetVertexCount(), channelData->mData.size() / channelData->mStride);
                }

                outBufferBindingCount++;
               

                channelCount--;
                if (channelCount == 0)
                    break;
            }
        }

        if (channelCount == 0)
            break;
    }
    Assert(outBufferBindingCount == outStreamLayouts.size());
    Assert(channelCount == 0);
}

UInt32 VertexStreamLayoutUtil::CollectVertexChannelDataForCPUSkinnedMesh(
    const MeshAssetData* meshAsset,
    VertexUploadBufferBinding(&outUploadBufferBinding)[MaxVertexChannelCount],
    UInt32& outBufferBindingCount,
    std::vector<VertexStreamLayout>& outStreamLayouts,
    VertexStreamLayout& outStreamDesc,
    VertexStreamLayoutSkinParameter* skinParam,
    VertexChannel PrePoistionChannel)
{
    AssertMsg(false, "CPU Skin is deprecated");
    
    cross::anim::SkeltMeshCPUSkinUtil::InputVertexSkinningInfo& VertexSkinningInfo = skinParam->VertexSkinningInfo;
    cross::anim::SkeltMeshCPUSkinUtil::SkinWeightInfo& SkinningWeightInfo = skinParam->SkinningWeightInfo;

    UInt32 channelOffset = 0;
    UInt32 channelCount = meshAsset->GetVertexChannelCount();
    Assert(channelCount < MaxVertexChannelCount);

    bool HasBlendShape = false;
    const BlendShapeVertexData* ModelBlendShapeVertexDataPtr = nullptr;
    if (skinParam)
    {
        HasBlendShape = skinParam->HasBlendShape;
        ModelBlendShapeVertexDataPtr = skinParam->ModelBlendShapeVertexDataPtr;
    }

    if (HasBlendShape)
        Assert(ModelBlendShapeVertexDataPtr != nullptr);

    // Collect vertex channel information
    for (UInt32 s = 0; s < gAllVertexSemanticSerial.size(); s++)
    {
        VertexSemantic semantic = GetSemantic(gAllVertexSemanticSerial[s]);
        if (!meshAsset->HasVertexSemantic(semantic))
            continue;

        UInt32 indexCount = semantic != SemanticTexCoord ? 4 : meshAsset->GetUVChannelCount();
        for (UInt32 i = 0; i < indexCount; i++)
        {
            VertexChannel channel = MakeVertexChannel(semantic, i);
            Assert(PrePoistionChannel != channel);

            const VertexChannelAssetData* channelData = nullptr;

            if (channel >= VertexChannel::Position0 && channel <= VertexChannel::PositionLast)
            {
                channelData = HasBlendShape ? ModelBlendShapeVertexDataPtr->GetVertexChannelData(channel) : meshAsset->GetVertexChannelData(channel);
                if (channelData)
                {
                    VertexSkinningInfo.InPositions = reinterpret_cast<const Float3*>(channelData->mData.data());
                    VertexSkinningInfo.PosOffsets = channelOffset;
                    outStreamDesc.AddVertexChannelLayout(channelData->mVertexChannel, channelData->mDataFormat, static_cast<UInt8>(channelOffset));
                    channelOffset += channelData->mStride;
                    channelCount--;
                }
            }
            else if (channel >= VertexChannel::Normal0 && channel <= VertexChannel::NormalLast)
            {
                channelData = HasBlendShape ? ModelBlendShapeVertexDataPtr->GetVertexChannelData(channel) : meshAsset->GetVertexChannelData(channel);
                if (channelData)
                {
                    VertexSkinningInfo.InNormals = reinterpret_cast<const Float3*>(channelData->mData.data());
                    VertexSkinningInfo.NormalOffsets = channelOffset;
                    outStreamDesc.AddVertexChannelLayout(channelData->mVertexChannel, channelData->mDataFormat, static_cast<UInt8>(channelOffset));
                    channelOffset += channelData->mStride;
                    channelCount--;
                }
            }
            else if (channel >= VertexChannel::Tangent0 && channel <= VertexChannel::TangentLast)
            {
                channelData = HasBlendShape ? ModelBlendShapeVertexDataPtr->GetVertexChannelData(channel) : meshAsset->GetVertexChannelData(channel);
                if (channelData)
                {
                    VertexSkinningInfo.InTangents = reinterpret_cast<const Float4*>(channelData->mData.data());
                    VertexSkinningInfo.TangentOffsets = channelOffset;
                    outStreamDesc.AddVertexChannelLayout(channelData->mVertexChannel, channelData->mDataFormat, static_cast<UInt8>(channelOffset));
                    channelOffset += channelData->mStride;
                    channelCount--;
                }
            }
            // Get vertex data from Raw mesh
            else
            {
                channelData = meshAsset->GetVertexChannelData(channel);
                if (channelData)
                {
                    if ((channel >= VertexChannel::BlendIndex0 && channel <= VertexChannel::BlendIndexLast))
                    {
                        // There is no need to copy BlendIndex to vb where CPU-skinning has been applied.
                        SkinningWeightInfo.InfluenceBones = reinterpret_cast<const Short4*>(channelData->mData.data());
                        SkinningWeightInfo.InfulenceBoneCount = static_cast<SInt32>(channelData->mStride / (sizeof(SInt16)));
                    }
                    else if ((channel >= VertexChannel::BlendWeight0 && channel <= VertexChannel::BlendWeightLast))
                    {
                        // There is no need to copy BlendWeight to vb where CPU-skinning has been applied.
                        SkinningWeightInfo.InfluenceWeights = reinterpret_cast<const Float4*>(channelData->mData.data());
                    }
                    else
                    {
                        outStreamDesc.AddVertexChannelLayout(channelData->mVertexChannel, channelData->mDataFormat, static_cast<UInt8>(channelOffset));
                        outUploadBufferBinding[outBufferBindingCount].mSrc = channelData->mData.data();
                        outUploadBufferBinding[outBufferBindingCount].mStride = channelData->mStride;
                        outUploadBufferBinding[outBufferBindingCount].mChannelOffset = channelOffset;
                        outBufferBindingCount++;
                        channelOffset += channelData->mStride;
                    }
                    channelCount--;
                }
            }

            if (channelCount == 0)
                break;
        }

        if (channelCount == 0)
            break;
    }
    
    // add PrePoistionChannel for cpu skin
    VertexSkinningInfo.PrePosOffsets = channelOffset;
    outStreamDesc.AddVertexChannelLayout(PrePoistionChannel, VertexFormat::Float3, static_cast<UInt8>(channelOffset));
    channelOffset += sizeof(Float3);

    return channelOffset;
}

void VertexStreamLayoutUtil::CollectVertexChannelDataSeparatingSkinningAndStatic(
    const MeshAssetData*                              inMeshAsset, 
    std::array<std::vector<VertexStreamLayout>, 2>&   outStreamLayouts,
    std::array<VertexUploadBufferSpace, 2>&           outUploadBufferSpaces,
    VertexStreamLayoutSkinParameter*                  outSkinningParam /*= nullptr*/)
{
    SCOPED_CPU_TIMING(GroupRendering, "CollectVertexChannelDataSeparatingSkinningAndStatic");
    
    std::vector<VertexStreamLayout>& skinVertexBufferLayout = outStreamLayouts[skinnedBufferSpaceIndex];
    std::vector<VertexStreamLayout>& staticVertexBufferLayout = outStreamLayouts[staticBufferSpaceIndex];
    
    VertexUploadBufferSpace& skinVertexBufferSpace = outUploadBufferSpaces[skinnedBufferSpaceIndex];
    VertexUploadBufferSpace& staticVertexBufferSpace = outUploadBufferSpaces[staticBufferSpaceIndex];

    skinVertexBufferSpace.Location = skinnedBufferSpaceIndex;
    staticVertexBufferSpace.Location = staticBufferSpaceIndex;

    // Check input channel is skinning involved or not
    auto checkSkinChannelLambda = [=](VertexChannel& inChannel) {
        auto itr = std::find_if(gSkinningVertexSentamtics.begin(), gSkinningVertexSentamtics.end(), [&](auto& elem) {
            if (elem ==inChannel)
                return true;
            return false;
        });
        return itr != gSkinningVertexSentamtics.end();
    };

    // Grab channel count from Primary Model AssetgAllVertexSemanticSerial
    UInt32 channelCount = inMeshAsset->GetVertexChannelCount();
    Assert(channelCount < MaxVertexChannelCount);

    // Any vertex candidate channel should be filtered from Primary Model Asset
    for (UInt32 s = 0; s < gAllVertexSemanticSerial.size(); s++)
    {
        VertexSemantic semantic = GetSemantic(gAllVertexSemanticSerial[s]);
        if (!inMeshAsset->HasVertexSemantic(semantic))
            continue;

        // Any vertex channels data except TEXCOORD got only one channel for Skinned Mesh
        UInt32 semtanicChannelCount = semantic != SemanticTexCoord ? 1 : 
            std::min<UInt32>((UInt32)VertexChannel::TexCoord15 - (UInt32)VertexChannel::TexCoord0, inMeshAsset->GetUVChannelCount());
        for (UInt32 channelIndex = 0; channelIndex < semtanicChannelCount; channelIndex++)
        {
            VertexChannel vertexChannel = MakeVertexChannel(semantic, channelIndex);
            const VertexChannelAssetData* vertexChannelData = inMeshAsset->GetVertexChannelData(vertexChannel);
            const bool isSkinChannel = checkSkinChannelLambda(vertexChannel);

            if (vertexChannelData == nullptr)
                continue;

            auto& vertexBufferSpace  = isSkinChannel ? skinVertexBufferSpace : staticVertexBufferSpace;
            auto& vertexBufferLayout = isSkinChannel ? skinVertexBufferLayout : staticVertexBufferLayout;

            if (vertexChannelData->mData.size() / vertexChannelData->mStride < inMeshAsset->GetVertexCount())
            {
                LOG_ERROR("Found {} missing vertex data {} {}, {} required but {} existed", 
                    inMeshAsset->GetName(), gAllVertexSemanticSerialName[s], channelIndex, inMeshAsset->GetVertexCount(), vertexChannelData->mData.size() / vertexChannelData->mStride);                
                continue;
            }

            vertexBufferLayout.emplace_back(vertexChannelData->mVertexChannel, vertexChannelData->mDataFormat);
            
            // Persist channel runtime data into vertex buffer space
            auto& vertexBufferBinding   = vertexBufferSpace.Bindings.emplace_back();
            vertexBufferBinding.mSrc    = vertexChannelData->mData.data();
            vertexBufferBinding.mStride = vertexChannelData->mStride;
            vertexBufferBinding.mSize   = static_cast<UInt32>(vertexChannelData->mData.size());
            vertexBufferBinding.mChannelOffset = 0;  // channel offset is always 0 since we only store one channel per stream
        }
    }

    outSkinningParam->VertexSkinningInfo.InOutPrePositions = const_cast<Float3*>((Float3*)inMeshAsset->GetVertexChannelData(VertexChannel::Tangent0)->mData.data());

    // Add pre_position channel depends on previous collect result
    // pre_position channel data grabbed from float4 channel due to structed_buffer align of 16 bytes
    const VertexChannelAssetData* tangentChannelData = inMeshAsset->GetVertexChannelData(VertexChannel::Tangent0);
    if (tangentChannelData)
    {
        skinVertexBufferLayout.emplace_back(VertexChannel::PositionT, VertexFormat::Float4);

        // Persist pre_position channel runtime data into vertex buffer space
        auto& vertexBufferBinding = skinVertexBufferSpace.Bindings.emplace_back();
        vertexBufferBinding.mSrc = tangentChannelData->mData.data();
        vertexBufferBinding.mStride = tangentChannelData->mStride;
        vertexBufferBinding.mSize = static_cast<UInt32>(tangentChannelData->mData.size());
        vertexBufferBinding.mChannelOffset = 0;  // channel offset is always 0 since we only store one channel per stream
    }

    Assert(skinVertexBufferLayout.size() == skinVertexBufferSpace.Bindings.size() && staticVertexBufferLayout.size() == staticVertexBufferSpace.Bindings.size());
    AssertMsg(skinVertexBufferSpace.Bindings.size() == gSkinningVertexSentamtics.size(), 
        "Skinnned mesh could draw failed for not all essential channels data founded in MESH ASSERT, PATH = {}", inMeshAsset->GetName());
}

//////////////////////////////////////////////////////////////////////////
// VertexStreamLayoutPolicyStaticAllInOne
//
//////////////////////////////////////////////////////////////////////////
void VertexStreamLayoutPolicyStaticAllInOne::AssembleGpuResource(MeshR* renderMesh, const MeshAssetData* meshAsset, IVertexStreamLayoutParameter* meshParameter)
{
    SCOPED_CPU_TIMING(GroupRendering, "VertexStreamLayoutPolicyStaticAllInOne::AssembleGpuResource");
    VertexStreamLayoutBlendShapeParameter* meshParam = static_cast<VertexStreamLayoutBlendShapeParameter*>(meshParameter);

    UInt32 totalMeshPartCount = meshAsset->GetAllLodMeshPartCount();

    UInt32 skipVertexCount = 0;
    UInt32 skipIndexCount = 0;

    // Apply LOD bias and skip data outside the range
    auto* pipelineSetting = EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting();
    UInt8 lodBias = static_cast<UInt8>(pipelineSetting->GlobalLODBias);
    UInt8 minLod = std::clamp(lodBias, static_cast<UInt8>(0), static_cast<UInt8>(meshAsset->GetLodCount() - 1));
    UInt8 maxLod = std::clamp(static_cast<UInt8>(meshAsset->GetLodCount() - 1 + lodBias), static_cast<UInt8>(0), static_cast<UInt8>(meshAsset->GetLodCount() - 1));
    UInt32 skipLodVertexCount = 0;
    UInt32 skipLodIndexCount = 0;
    MeshStatistics meshStat;
    meshStat.DebugName = meshAsset->GetName();
    for (UInt8 lodIndex = 0; lodIndex < meshAsset->GetLodCount(); lodIndex++)
    {
        UInt32 startIndex = 0;
        UInt32 count = 0;
        meshAsset->GetMeshLodInfo(lodIndex, startIndex, count);
        bool inLodRange = lodIndex >= minLod && lodIndex <= maxLod;

        for (UInt32 partIndex = startIndex; partIndex < startIndex + count; partIndex++)
        {
            auto& partInfo = meshAsset->GetMeshPartInfo(partIndex);
            if (!inLodRange)
            {
                skipLodVertexCount += partInfo.mVertexCount;
                skipLodIndexCount += partInfo.mIndexCount;
            }
            else
            {
                meshStat.LODStats[lodIndex].VertexBufferSize += partInfo.mVertexCount;
                meshStat.TotalVertexBufferSize += partInfo.mVertexCount;
                meshStat.LODStats[lodIndex].IndexBufferSize += partInfo.mIndexCount;
                meshStat.TotalIndexBufferSize += partInfo.mIndexCount;
            }
        }
    }

    skipVertexCount = skipLodVertexCount;
    skipIndexCount = skipLodIndexCount;

    // Collect vertex channel information
    VertexUploadBufferBinding uploadBufferBinding[MaxVertexChannelCount];
    UInt32 bufferBindingCount = 0;
    
    std::vector<VertexStreamLayout> vertexStreamLayouts;
    VertexStreamLayoutUtil::CollectVertexChannelDataForStaticMesh(meshAsset, uploadBufferBinding, bufferBindingCount, vertexStreamLayouts, meshParam);

    const UInt32 vtxCount = meshAsset->GetVertexCount() - skipVertexCount;

    UInt32 sizeBytes = 0;
    if (meshAsset->HasIndexStream())
    {
        const IndexStreamAssetData& indexAsset = meshAsset->GetIndexStream();
        sizeBytes = indexAsset.mIs16BitIndex ? sizeof(UInt16) : sizeof(UInt32);
    }

    UInt32 vertexStride = 0;
    for (UInt8 streamIndex = 0; streamIndex < vertexStreamLayouts.size(); streamIndex++)
    {
        vertexStride += vertexStreamLayouts[streamIndex].GetVertexStride();
    }

    for (UInt8 index = 0; index < 8; index++)
    {
        meshStat.LODStats[index].VertexBufferSize *= vertexStride / 1024.0f / 1024.0f;
        meshStat.LODStats[index].IndexBufferSize *= sizeBytes / 1024.0f / 1024.0f;
    }
    meshStat.TotalVertexBufferSize *= vertexStride / 1024.0f / 1024.0f;
    meshStat.TotalIndexBufferSize *= sizeBytes / 1024.0f / 1024.0f;
    //EngineMeshStatistics::GetInstance().AddMeshStat(meshAsset->GetName(), meshStat);

    // Grab RendererSystem for allocate following gpu buffer
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    // Create geometry packet for input Primary Model
    GeometryPacketPtr geoPak = RenderFactory::Instance().CreateGeometryPacket();

    {
        Assert(bufferBindingCount == vertexStreamLayouts.size());
        for (UInt32 streamIndex = 0; streamIndex < vertexStreamLayouts.size(); streamIndex++)
        {
            const VertexStreamLayout& streamLayout = vertexStreamLayouts[streamIndex];
            // Assert only one channel per stream for bindless access
            Assert(streamLayout.GetChannelCount() == 1);
            
            UInt32 streamVBSize = vtxCount * streamLayout.GetVertexStride();
            NGIBufferPtr streamVB = RenderFactory::Instance().CreateVertexBuffer(streamVBSize);
            std::string debugName = meshAsset->GetName() + " Channel" + std::to_string(streamIndex);
            streamVB->SetDebugName(debugName.c_str());
            geoPak->AddVertexStream(streamVB.get(), streamVBSize, 0, streamLayout);

            NGIBufferDesc desc{streamVBSize, NGIBufferUsage::CopySrc};
            NGIStagingBuffer* stagingBuffer = GetNGIDevice().CreateStagingBuffer(desc);

            auto& curBinding = uploadBufferBinding[streamIndex];
            UInt32 skipSize = skipVertexCount * streamLayout.GetVertexStride();
            Assert(curBinding.mSize == streamVBSize + skipSize);
            UInt8* dst = static_cast<UInt8*>(stagingBuffer->MapRange(NGIBufferUsage::CopySrc, 0, streamVBSize));
            memcpy(dst, curBinding.mSrc + skipSize, curBinding.mSize);
            
            stagingBuffer->UnmapRange(0, streamVBSize);
            NGICopyBuffer region{
                .SrcOffset = 0,
                .DstOffset = 0,
                .NumBytes = streamVBSize};
            rendererSystem->UpdateBuffer(streamVB.get(), stagingBuffer, region, NGIResourceState::Undefined, NGIResourceState::VertexBuffer);
            rendererSystem->DestroyNGIObjectInAsyncThread(stagingBuffer);
        }

        if (meshAsset->HasIndexStream())
        {
            VertexStreamLayoutUtil::SetIndexBufferForGeometryPacket(meshAsset, geoPak, skipIndexCount);
        }
    }

    VertexStreamLayoutUtil::SetRenderGeometryDataFromGeometryPacket(renderMesh, meshAsset, geoPak, skipVertexCount, skipIndexCount);
}

void VertexStreamLayoutPolicyStaticAllInOne::AssembleMergedGpuResource(std::vector<MeshR*>& renderMeshes, const std::vector<MeshAssetDataResourcePtr>& meshAssets, MergedGeometryPacket& mergedGeometryPacket)
{
    UInt32 totalVertexCount = 0;
    UInt32 totalVbSize = 0;
    UInt32 totalIndexCount = 0;
    UInt32 totalIbSize = 0;
    typedef std::array<VertexUploadBufferBinding, MaxVertexChannelCount> VertexUploadBufferBindingArray;
    std::vector<VertexUploadBufferBindingArray> uploadBufferBindings;
    std::vector<VertexStreamLayout> initVertexStreamLayouts;
    std::vector<std::vector<VertexStreamLayout>> meshAssetStreamLayouts(meshAssets.size()); 
    uploadBufferBindings.resize(meshAssets.size());
    std::string packName;
    
    // forcing use UInt32 format
    //IndexBufferFormat initIdxFmt = IndexFormat_UInt32;
    std::set<int> skipMeshIndex;

    if (mergedGeometryPacket.geoPack)
    {
        for (UInt8 stream = 0; stream < mergedGeometryPacket.geoPack->GetStreamCount(); ++stream)
        {
            const VertexStreamLayout& streamLayout = mergedGeometryPacket.geoPack->GetInputLayout().GetVertexStreamLayout(stream);
            Assert(streamLayout.GetChannelCount() == 1);
            initVertexStreamLayouts.push_back(streamLayout);
        }
        
        // initStreamDesc = mergedGeometryPacket.geoPack->GetInputLayout().GetVertexStreamLayout(0);
    }
    else
    {
        auto uvFormat = VertexFormat::Half2;
        //const MeshAssetData* mesh = meshAssets[0];
        //auto channelData = mesh->GetVertexChannelData(VertexChannel::TexCoord0);
        //if (channelData)
        //{
        //    uvFormat = channelData->mDataFormat;
        //}

        initVertexStreamLayouts.push_back(VertexStreamLayout(VertexChannel::Position0, VertexFormat::Float3));
        initVertexStreamLayouts.push_back(VertexStreamLayout(VertexChannel::Normal0, VertexFormat::Byte4_Norm));
        initVertexStreamLayouts.push_back(VertexStreamLayout(VertexChannel::Normal2, VertexFormat::Byte4));
        initVertexStreamLayouts.push_back(VertexStreamLayout(VertexChannel::Tangent0, VertexFormat::Byte4_Norm));
        initVertexStreamLayouts.push_back(VertexStreamLayout(VertexChannel::TexCoord0, uvFormat));
        
        // initStreamDesc.AddVertexChannelLayout(VertexChannel::Position0, VertexFormat::Float3);
        // initStreamDesc.AddVertexChannelLayout(VertexChannel::Normal0, VertexFormat::Byte3_Norm);
        // initStreamDesc.AddVertexChannelLayout(VertexChannel::Normal2, VertexFormat::Byte4);
        // initStreamDesc.AddVertexChannelLayout(VertexChannel::Tangent0, VertexFormat::Byte4_Norm);
        // initStreamDesc.AddVertexChannelLayout(VertexChannel::TexCoord0, uvFormat);
        
        //initStreamDesc.AddVertexChannelLayout(VertexChannel::TexCoord1, uvFormat);
        //initStreamDesc.AddVertexChannelLayout(VertexChannel::QUATTAN0, VertexFormat::Byte4_Norm);
    }

    for (int i = 0; i < meshAssets.size(); ++i)
    {
        if (meshAssets[i]->GetAssetData()->GetIndexStream().mData.empty())
        {
            meshAssets[i]->ReloadMesh();
            meshAssets[i]->SetResidentInCpu(true);
        }

        const MeshAssetData* meshAsset = meshAssets[i]->GetAssetData();

        packName += meshAsset->GetName();
        VertexUploadBufferBinding uploadBufferBinding[MaxVertexChannelCount];
        UInt32 bufferBindingCount = 0;

        // Collect vertex channel information
        std::vector<VertexStreamLayout> vertexStreamLayouts;
        VertexStreamLayoutUtil::CollectVertexChannelDataForStaticMesh(meshAsset, uploadBufferBinding, bufferBindingCount, vertexStreamLayouts, nullptr);
        
        if (initVertexStreamLayouts.size() == 0)
        {
            initVertexStreamLayouts = vertexStreamLayouts;
        }
        else  // check if all init vertex channels are included in the vertexStreamLayouts
        {
            bool sameVertexLayout = true;
            for (int originStream = 0; originStream < initVertexStreamLayouts.size(); originStream++)
            {
                bool hasChannel = false;
                auto initChannelLayout = initVertexStreamLayouts[originStream].GetChannelLayout(0);
                for (int vertexStream = 0; vertexStream < vertexStreamLayouts.size(); vertexStream++)
                {
                    auto vertexChannelLayout = vertexStreamLayouts[vertexStream].GetChannelLayout(0);
                    if (initChannelLayout.mChannelName == vertexChannelLayout.mChannelName)
                    {
                        hasChannel = true;
                        break;
                    }
                }
                sameVertexLayout &= hasChannel;
                if (!sameVertexLayout)
                {
                    break;
                }
            }
        }

        meshAssetStreamLayouts[i] = std::move(vertexStreamLayouts);
        
        // if (initStreamDesc.GetChannelCount() == 0) 
        // {
        //     initStreamDesc = streamDesc;
        // }
        // else
        // {
        //     bool sameVertexFormat = true; 
        //     for (int channel = 0; channel < initStreamDesc.GetChannelCount(); ++channel)
        //     {
        //         bool hasChannel = false;
        //         auto& channelLayout = initStreamDesc.GetChannelLayout(channel);
        //         for (int j = 0; j < streamDesc.GetChannelCount(); ++j)
        //         {
        //             auto& srcChannelLayout = streamDesc.GetChannelLayout(j);
        //             if (srcChannelLayout.mChannelName == channelLayout.mChannelName/* && srcChannelLayout.mFormat == channelLayout.mFormat*/)
        //             {
        //                 hasChannel = true;
        //                 break;
        //             }
        //         }
        //         sameVertexFormat &= hasChannel;
        //         if (!sameVertexFormat)
        //         {
        //             break;
        //         }
        //     }
        //     //bool sameVertexFormat = initStreamDesc.GetVertexStride() == streamDesc.GetVertexStride() && initStreamDesc.GetChannelCount() == streamDesc.GetChannelCount();
        //     // todo: need implementation. not support different vertex format now, skip merging this mesh data
        //     //if (!sameVertexFormat)
        //     //{
        //     //    skipMeshIndex.insert(i);
        //     //    continue;
        //     //}
        // }

        
        VertexUploadBufferBindingArray uploadBufferBindingArr;
        std::move(std::begin(uploadBufferBinding), std::end(uploadBufferBinding), uploadBufferBindingArr.begin());
        uploadBufferBindings[i] = std::move(uploadBufferBindingArr);

        const UInt32 vtxCount = meshAsset->GetVertexCount();
        UInt32 vertexStride = 0;
        for (int streamIndex = 0; streamIndex < initVertexStreamLayouts.size(); streamIndex++)
        {
            vertexStride += initVertexStreamLayouts[streamIndex].GetVertexStride();
        }
        const UInt32 vbSize = vtxCount * vertexStride;
        totalVertexCount += vtxCount;
        totalVbSize += vbSize;
        
        if (meshAsset->HasIndexStream())
        {
            const IndexStreamAssetData& indexAsset = meshAsset->GetIndexStream();
            //UInt32 ibSize = static_cast<UInt32>(indexAsset.mData.size());
            UInt32 ibSize = indexAsset.mCount * sizeof(UInt32);
            totalIbSize += ibSize;
            totalIndexCount += indexAsset.mCount;
            //IndexBufferFormat idxFmt = indexAsset.mIs16BitIndex ? IndexFormat_UInt16 : IndexFormat_UInt32;
            //if (i == 0)
            //{
            //    initIdxFmt = idxFmt;
            //}
            //else
            //{
            //    Assert(initIdxFmt == idxFmt);
            //}
        }
    }

    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    GeometryPacketPtr geoPack;
    // Create geometry packet
    packName = "mergedGeometry";
    if (mergedGeometryPacket.geoPack.get() == nullptr)
    {
        geoPack = RenderFactory::Instance().CreateGeometryPacket();
        mergedGeometryPacket.geoPack = geoPack;
        mergedGeometryPacket.maxVertexCount = std::max<UInt32>(50000, totalVertexCount);
        mergedGeometryPacket.maxIndexCount = std::max<UInt32>(150000, totalIndexCount);
        mergedGeometryPacket.vertexCount = 0;
        mergedGeometryPacket.indexCount = 0;

        // create vb for per channel stream
        for (int streamIndex = 0; streamIndex < initVertexStreamLayouts.size(); ++streamIndex)
        {
            auto& streamLayout = initVertexStreamLayouts[streamIndex];
            Assert(streamLayout.GetChannelCount() == 1);
            
            UInt32 streamVBSize = mergedGeometryPacket.maxVertexCount * streamLayout.GetVertexStride();
            NGIBufferPtr streamVB = NGIBufferPtr{
                GetNGIDevicePtr()->CreateBuffer(
                {
                    streamVBSize,
                    NGIBufferUsage::VertexBuffer | NGIBufferUsage::CopyDst | NGIBufferUsage::CopySrc,
                }, (packName + std::to_string(streamIndex)).c_str())
            };
            geoPack->AddVertexStream(streamVB.get(), streamVBSize, 0, streamLayout);
        }

        // create index buffer
        UInt32 maxIbSize = mergedGeometryPacket.maxIndexCount * sizeof(UInt32);
        NGIBufferPtr ib = NGIBufferPtr{GetNGIDevice().CreateBuffer({
            maxIbSize,
            NGIBufferUsage::IndexBuffer | NGIBufferUsage::CopyDst | NGIBufferUsage::CopySrc,
            },
            packName.c_str())};

        geoPack->SetIndexStream(ib.get(), maxIbSize, mergedGeometryPacket.maxIndexCount);
        
        // // Create vertex buffer
        // UInt32 maxVbSize = mergedGeometryPacket.maxVertexCount * initStreamDesc.GetVertexStride();
        // NGIBufferPtr vb = NGIBufferPtr{GetNGIDevice().CreateBuffer({
        //     maxVbSize,
        //     NGIBufferUsage::VertexBuffer | NGIBufferUsage::CopyDst | NGIBufferUsage::CopySrc,
        //     },
        //     packName.c_str())};
        // // Add vertex buffer into geometry packet
        // geoPack->AddVertexStream(vb.get(), maxVbSize, 0, initStreamDesc);
        //
        // // create index buffer
        // UInt32 maxIbSize = mergedGeometryPacket.maxIndexCount * sizeof(UInt32);
        // NGIBufferPtr ib = NGIBufferPtr{GetNGIDevice().CreateBuffer({
        //     maxIbSize,
        //     NGIBufferUsage::IndexBuffer | NGIBufferUsage::CopyDst | NGIBufferUsage::CopySrc,
        //     },
        //     packName.c_str())};
        //
        // geoPack->SetIndexStream(ib.get(), maxIbSize, mergedGeometryPacket.maxIndexCount);
    }
    else
    {
        geoPack = mergedGeometryPacket.geoPack;

        // extend vb and ib
        UInt32 needVertexCount = mergedGeometryPacket.vertexCount + totalVertexCount;
        if (needVertexCount > mergedGeometryPacket.maxVertexCount)
        {
            UInt32 maxVertexCount = mergedGeometryPacket.maxVertexCount * 2;
            while (needVertexCount > maxVertexCount)
            {
                maxVertexCount *= 2;
            }
            mergedGeometryPacket.maxVertexCount = maxVertexCount;

            // create vb for per channel stream
            for (int streamIndex = 0; streamIndex < initVertexStreamLayouts.size(); streamIndex++)
            {
                auto& streamLayout = initVertexStreamLayouts[streamIndex];
                Assert(streamLayout.GetChannelCount() == 1);
            
                UInt32 streamVBSize = mergedGeometryPacket.maxVertexCount * streamLayout.GetVertexStride();
                NGIBufferPtr newStreamVB = NGIBufferPtr{
                    GetNGIDevicePtr()->CreateBuffer(
                    {
                        streamVBSize,
                        NGIBufferUsage::VertexBuffer | NGIBufferUsage::CopyDst | NGIBufferUsage::CopySrc,
                    }, (packName + std::to_string(streamIndex)).c_str())
                };

                // Copy pre VB to new VB
                auto preVB = geoPack->GetVertexStream(static_cast<UInt8>(streamIndex));
                NGICopyBuffer region(0, 0, preVB->GetStreamSize());
                rendererSystem->UpdateBuffer(newStreamVB.get(), preVB->GetGpuBuffer(), region,
                    NGIResourceState::VertexBuffer, NGIResourceState::VertexBuffer, NGIResourceState::VertexBuffer, NGIResourceState::Undefined);

                geoPack->ClearVertexStream(streamIndex);
                geoPack->AddVertexStream(newStreamVB.get(), streamVBSize, 0, streamLayout);
            }
            
            // UInt32 vertexStride = 0;
            // for (int streamIndex = 0; streamIndex < initVertexStreamLayouts.size(); streamIndex++)
            // {
            //     vertexStride += initVertexStreamLayouts[streamIndex].GetVertexStride();
            // }
            // UInt32 maxVbSize = mergedGeometryPacket.maxVertexCount * vertexStride;
            // NGIBufferPtr vb = NGIBufferPtr{GetNGIDevice().CreateBuffer({
            //     maxVbSize,
            //     NGIBufferUsage::VertexBuffer | NGIBufferUsage::CopyDst | NGIBufferUsage::CopySrc,
            // })};
            // vb->SetDebugName(packName.c_str());
            //
            // auto prevVB = geoPack->GetVertexStream(0);
            // NGICopyBuffer region{
            //     0,
            //     0,
            //     prevVB->GetStreamSize(),
            // };
            // rendererSystem->UpdateBuffer(vb.get(), prevVB->GetGpuBuffer(), region, NGIResourceState::VertexBuffer, NGIResourceState::VertexBuffer, NGIResourceState::VertexBuffer, NGIResourceState::Undefined);
            //
            // geoPack->ClearVertexStream();
            // geoPack->AddVertexStream(vb.get(), maxVbSize, 0, initStreamDesc);
        }

        UInt32 needIndexCount = mergedGeometryPacket.indexCount + totalIndexCount;
        if (mergedGeometryPacket.indexCount + totalIndexCount > mergedGeometryPacket.maxIndexCount)
        {
            UInt32 maxIndexCount = mergedGeometryPacket.maxIndexCount * 2;
            while (needIndexCount > maxIndexCount)
            {
                maxIndexCount *= 2;
            }
            mergedGeometryPacket.maxIndexCount = maxIndexCount;
            UInt32 maxIbSize = mergedGeometryPacket.maxIndexCount * sizeof(UInt32);
            NGIBufferPtr ib = NGIBufferPtr{GetNGIDevice().CreateBuffer({
                maxIbSize,
                NGIBufferUsage::IndexBuffer | NGIBufferUsage::CopyDst | NGIBufferUsage::CopySrc,
            })};
            ib->SetDebugName(packName.c_str());

            auto prevIB = geoPack->GetIndexStream();
            NGICopyBuffer region{
                0,
                0,
                prevIB->GetStreamSize(),
            };
            rendererSystem->UpdateBuffer(ib.get(), prevIB->GetGpuBuffer(), region, NGIResourceState::IndexBuffer, NGIResourceState::IndexBuffer, NGIResourceState::IndexBuffer, NGIResourceState::Undefined);

            geoPack->SetIndexStream(ib.get(), maxIbSize, mergedGeometryPacket.maxIndexCount);
        }
    }

    NGIBufferPtr vb{geoPack->GetVertexStream(0)->GetGpuBuffer()};
    NGIBufferPtr ib{geoPack->GetIndexStream()->GetGpuBuffer()};

    // UInt32 vertexBufferOffset = mergedGeometryPacket.vertexCount * initStreamDesc.GetVertexStride();
    auto vbStagingBufferWrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, totalVbSize);
    vbStagingBufferWrap.MemSet(0, 0, totalVbSize);

    auto FindChannelByName = [](const VertexStreamLayout& streamDesc, const VertexChannelLayout& channel, int& outIndex) -> bool
    {
        for (int j = 0; j < streamDesc.GetChannelCount(); ++j)
        {
            auto& srcChannelLayout = streamDesc.GetChannelLayout(j);
            if (srcChannelLayout.mChannelName == channel.mChannelName)
            {
                outIndex = j;
                return true;
            }
        }
        return false;
    };

    auto GetTargetChannelStreamIndex = [](const VertexChannelLayout& targetChannel, const std::vector<VertexStreamLayout>& streamLayouts, UInt32& outStreamIndex) -> bool
    {
        for (UInt32 streamIndex = 0; streamIndex < streamLayouts.size(); streamIndex++)
        {
            Assert(streamLayouts[streamIndex].GetChannelCount() == 1);
            if (streamLayouts[streamIndex].GetChannelLayout(0).mChannelName == targetChannel.mChannelName)
            {
                outStreamIndex = streamIndex;
                return true;
            }
        }
        return false;
    };

    UInt32 curVertex = 0;
    for (int meshIndex = 0; meshIndex < meshAssets.size(); ++meshIndex)
    {
        if (skipMeshIndex.count(meshIndex) != 0)
        {
            continue;
        }
        
        const MeshAssetData* meshAsset = meshAssets[meshIndex]->GetAssetData();
        const UInt32 curMeshVertexCount = meshAsset->GetVertexCount();
        VertexUploadBufferBindingArray& uploadBufferBinding = uploadBufferBindings[meshIndex];
        UInt32 streamCount = static_cast<UInt32>(initVertexStreamLayouts.size());

        auto& meshStreamLayout = meshAssetStreamLayouts[meshIndex];
        
        for (UInt32 initStreamIndex = 0; initStreamIndex < streamCount; ++initStreamIndex)
        {
            auto& initChannelStream = initVertexStreamLayouts[initStreamIndex];
            Assert(initChannelStream.GetChannelCount() == 1);
            const VertexChannelLayout& srcChannelLayout = initChannelStream.GetChannelLayout(0);
            VertexFormat srcChannelFormat = srcChannelLayout.mFormat;
            
            auto& meshChannelStream = meshStreamLayout[initStreamIndex];
            Assert(meshChannelStream.GetChannelCount() == 1);
            UInt32 channelStreamIndex = 0;
            if (GetTargetChannelStreamIndex(srcChannelLayout, meshStreamLayout, channelStreamIndex))
            {
                const VertexChannelLayout& dstChannelLayout = meshStreamLayout[channelStreamIndex].GetChannelLayout(0);
                VertexFormat dstChannelFormat = dstChannelLayout.mFormat;
                auto& curBinding = uploadBufferBinding[channelStreamIndex];

                UInt32 channelStride = initChannelStream.GetVertexStride();
                UInt32 streamVBSize = totalVertexCount * channelStride;
                StagingBufferWrap streamVBStaging =
                    rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, streamVBSize);
                
                if (srcChannelFormat == dstChannelFormat)
                {
                    streamVBStaging.MemWrite(0u, curBinding.mSrc, streamVBSize);
                }
                else  // Convert format
                {
                    switch (srcChannelLayout.mChannelName)
                    {
                    case VertexChannel::TexCoord0:
                    {
                        for (UInt32 vertexIndex = 0; vertexIndex < curMeshVertexCount; vertexIndex++)
                        {
                            UShort2 normalizeData;
                            Float2 inData;
                            cross::FloatToHalfConverter converter;
                            memcpy(&inData, curBinding.mSrc + vertexIndex * curBinding.mStride, curBinding.mStride);
                            TypeConverter(inData, normalizeData, [&converter](const auto& in, auto& out) { converter.Convert(in, out); });
                            Assert(srcChannelLayout.mOffset == 0);
                            streamVBStaging.MemWrite(vertexIndex * channelStride + srcChannelLayout.mOffset, &normalizeData, sizeof(UShort2));
                        }
                        break;
                    }
                    case VertexChannel::Normal0:
                    {
                        for (UInt32 vertexIndex = 0; vertexIndex < curMeshVertexCount; vertexIndex++)
                        {
                            UChar3 normalizeData;
                            UShort3 inData;
                            memcpy(&inData, curBinding.mSrc + vertexIndex * curBinding.mStride, curBinding.mStride);
                            TypeConverter(inData, normalizeData, [](const auto& in, auto& out) {
                                float data;
                                SNormToFloat(in, data);
                                FloatToSNORM(data, out);
                            });
                            Assert(srcChannelLayout.mOffset == 0);
                            streamVBStaging.MemWrite(vertexIndex * channelStride + srcChannelLayout.mOffset, &normalizeData, sizeof(UChar3));
                        }
                        break;
                    }
                    case VertexChannel::Normal2:
                    {
                        for (UInt32 vertexIndex = 0; vertexIndex < curMeshVertexCount; vertexIndex++)
                        {
                            UChar4 normalizeData;
                            Float4 inData;
                            memcpy(&inData, curBinding.mSrc + vertexIndex * curBinding.mStride, curBinding.mStride);
                            TypeConverter(inData, normalizeData, [](const auto& in, auto& out) {
                                out = static_cast<UInt8>(in);
                            });
                            Assert(srcChannelLayout.mOffset == 0);
                            streamVBStaging.MemWrite(vertexIndex * channelStride + srcChannelLayout.mOffset, &normalizeData, sizeof(UChar4));
                        }
                        break;
                    }
                    case VertexChannel::Tangent0:
                    {
                        for (UInt32 vertexIndex = 0; vertexIndex < curMeshVertexCount; vertexIndex++)
                        {
                            UChar4 normalizeData;
                            UShort4 inData;
                            memcpy(&inData, curBinding.mSrc + vertexIndex * curBinding.mStride, curBinding.mStride);
                            TypeConverter(inData, normalizeData, [](const auto& in, auto& out) {
                                float data;
                                SNormToFloat(in, data);
                                FloatToSNORM(data, out);
                            });
                            Assert(srcChannelLayout.mOffset == 0);
                            streamVBStaging.MemWrite(vertexIndex * channelStride + srcChannelLayout.mOffset, &normalizeData, sizeof(UChar4));
                        }
                        break;
                    }
                    default:
                        break;
                    }
                }

                //                           origin vertex count    +   traversed vertex count
                UInt32 streamVBOffset = (mergedGeometryPacket.vertexCount + curVertex) * initChannelStream.GetVertexStride();
                NGICopyBuffer region{
                    streamVBStaging.GetNGIOffset(),
                    streamVBOffset,
                    streamVBSize,
                };
                rendererSystem->UpdateBuffer(geoPack.get()->GetVertexStream(static_cast<UInt8>(initStreamIndex))->GetGpuBuffer(), streamVBStaging.GetNGIBuffer(), region,
                    NGIResourceState::Undefined | NGIResourceState::VertexBuffer, NGIResourceState::VertexBuffer);
            }
        }

        curVertex += curMeshVertexCount;
    }
    


    // UInt32 curVertex = 0;
    // for (int i = 0; i < meshAssets.size(); ++i)
    // {
    //     if (skipMeshIndex.count(i) != 0)
    //         continue;
    //
    //     const MeshAssetData* meshAsset = meshAssets[i]->GetAssetData();
    //     const UInt32 vtxCount = meshAsset->GetVertexCount();
    //     VertexUploadBufferBindingArray& uploadBufferBinding = uploadBufferBindings[i];
    //     UInt32 bufferBindingCount = initStreamDesc.GetChannelCount();
    //     auto& streamDesc = initVertexStreamLayouts[i];
    //     // VB Layout: Vert0_Pos, Vert0_Color, Vert0_Normal, Vert0_Tangent, Vert0_TexCoord, Vert1_Pos, Vert1_Color ......
    //     UInt32 stride = initStreamDesc.GetVertexStride();
    //     for (UInt32 bindingIdx = 0; bindingIdx < bufferBindingCount; bindingIdx++)
    //     {
    //         int outIndex;
    //         auto& channelLayout = initStreamDesc.GetChannelLayout(bindingIdx);
    //         if (FindChannelByName(streamDesc, channelLayout, outIndex))
    //         {
    //             auto srcFormat = streamDesc.GetChannelLayout(outIndex).mFormat;
    //             auto& curBinding = uploadBufferBinding[outIndex];
    //             for (UInt32 j = 0; j < vtxCount; ++j)
    //             {
    //                 if (channelLayout.mFormat == srcFormat)
    //                 {
    //                     vbStagingBufferWrap.MemWrite((curVertex + j) * stride + channelLayout.mOffset, curBinding.mSrc + j * curBinding.mStride, curBinding.mStride);
    //                 }
    //                 else
    //                 {
    //                     // convert diff vertex format
    //                     switch (channelLayout.mChannelName)
    //                     {
    //                     case VertexChannel::TexCoord0:
    //                     {
    //                         UShort2 normalizeData;
    //                         Float2 inData;
    //                         cross::FloatToHalfConverter converter;
    //                         memcpy(&inData, curBinding.mSrc + j * curBinding.mStride, curBinding.mStride);
    //                         TypeConverter(inData, normalizeData, [&converter](const auto& in, auto& out) { converter.Convert(in, out); });
    //                         vbStagingBufferWrap.MemWrite((curVertex + j) * stride + channelLayout.mOffset, &normalizeData, sizeof(UShort2));
    //                         break;
    //                     }
    //                     case VertexChannel::Normal0:
    //                     {
    //                         UChar3 normalizeData;
    //                         UShort3 inData;
    //                         memcpy(&inData, curBinding.mSrc + j * curBinding.mStride, curBinding.mStride);
    //                         TypeConverter(inData, normalizeData, [](const auto& in, auto& out) {
    //                             float data;
    //                             SNormToFloat(in, data);
    //                             FloatToSNORM(data, out);
    //                         });
    //                         vbStagingBufferWrap.MemWrite((curVertex + j) * stride + channelLayout.mOffset, &normalizeData, sizeof(UChar3));
    //                         break;
    //                     }
    //                     case VertexChannel::Normal2:
    //                     {
    //                         UChar4 normalizeData;
    //                         Float4 inData;
    //                         memcpy(&inData, curBinding.mSrc + j * curBinding.mStride, curBinding.mStride);
    //                         TypeConverter(inData, normalizeData, [](const auto& in, auto& out) {
    //                             out = static_cast<UInt8>(in);
    //                         });
    //                         vbStagingBufferWrap.MemWrite((curVertex + j) * stride + channelLayout.mOffset, &normalizeData, sizeof(UChar4));
    //                         break;
    //                     }
    //                     case VertexChannel::Tangent0:
    //                     {
    //                         UChar4 normalizeData;
    //                         UShort4 inData;
    //                         memcpy(&inData, curBinding.mSrc + j * curBinding.mStride, curBinding.mStride);
    //                         TypeConverter(inData, normalizeData, [](const auto& in, auto& out) {
    //                             float data;
    //                             SNormToFloat(in, data);
    //                             FloatToSNORM(data, out);
    //                         });
    //                         vbStagingBufferWrap.MemWrite((curVertex + j) * stride + channelLayout.mOffset, &normalizeData, sizeof(UChar4));
    //                         break;
    //                     }
    //                     default:
    //                         break;
    //                     }
    //                 }
    //             }
    //         }
    //     }
    //     curVertex += vtxCount;
    // }


    // {
    //     
    //     NGICopyBuffer region{
    //         vbStagingBufferWrap.GetNGIOffset(),
    //         vertexBufferOffset,
    //         totalVbSize,
    //     };
    //     rendererSystem->UpdateBuffer(vb.get(), vbStagingBufferWrap.GetNGIBuffer(), region, NGIResourceState::Undefined, NGIResourceState::VertexBuffer);
    // }

    
    auto ibStagingBufferWrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, totalIbSize);
    

    UInt16 geopakSubIndex = 0;
    UInt32 vertexBase = mergedGeometryPacket.vertexCount;
    UInt32 indexBase = mergedGeometryPacket.indexCount;
    UInt32 idxOffset = 0;
    for (int i = 0; i < meshAssets.size(); ++i) 
    {
        if (skipMeshIndex.count(i) != 0)
            continue;

        const MeshAssetData* meshAsset = meshAssets[i]->GetAssetData();
        MeshR* renderGeometryList = renderMeshes[i];
        const IndexStreamAssetData& indexAsset = meshAsset->GetIndexStream();
        UInt32 ibSize = static_cast<UInt32>(indexAsset.mData.size());

        //UInt32 idxSize = initIdxFmt == IndexFormat_UInt32 ? 4 : 2;
        //auto data = indexAsset.mData.data();
        //for (UInt32 j = 0; j < indexAsset.mCount; ++j) 
        //{
        //    UInt32 offset = j * idxSize;
        //    if (initIdxFmt == IndexFormat_UInt32)
        //    {
        //        UInt32 idx = data[offset] + vertexBase;
        //        memcpy(destBuffer + offset, &idx, sizeof(UInt32));
        //    }
        //    else
        //    {
        //        UInt16 idx = data[offset] + (UInt16)vertexBase;
        //        memcpy(destBuffer + offset, &idx, sizeof(UInt16));
        //    }
        //}

        if (indexAsset.mIs16BitIndex) 
        {
            auto data = indexAsset.mData.data();
            for (UInt32 j = 0; j < indexAsset.mCount; ++j)
            {
                UInt32 offset = j * sizeof(UInt16);
                UInt32 idx = *(reinterpret_cast<UInt16*>(const_cast<unsigned char*>(data + offset)));
                ibStagingBufferWrap.MemWrite(idxOffset, &idx, sizeof(UInt32));
                idxOffset += sizeof(UInt32);
            }
        }
        else
        {
            ibStagingBufferWrap.MemWrite(idxOffset, indexAsset.mData.data(), ibSize);
            idxOffset += ibSize;
        }

        
        UInt32 totalMeshPartCount = meshAsset->GetAllLodMeshPartCount();
        renderGeometryList->ClearAndResize(totalMeshPartCount);
        for (UInt32 p = 0; p < totalMeshPartCount; p++)
        {
            auto& partInfo = meshAsset->GetMeshPartInfo(p);
            auto& geometry = renderGeometryList->GetRenderGeometry(p);
            geometry.SetData(geoPack.get(), partInfo.mVertexCount, partInfo.mVertexStart + vertexBase, partInfo.mIndexCount, partInfo.mIndexStart + indexBase, partInfo.mPrimitiveCount, partInfo.mPrimitiveType, geopakSubIndex);

            const auto* name = meshAsset->GetMeshPartName(p);
            StringHash32 nameHash = HashFunction::HashString32(name->c_str());
            renderGeometryList->SetNameHash(p, nameHash);
            ++geopakSubIndex;
        }

        vertexBase += meshAsset->GetVertexCount();
        indexBase += indexAsset.mCount;
        renderGeometryList->SetState(MeshR::State::Initialized);
    }


    {
        UInt32 indexBufferOffset = mergedGeometryPacket.indexCount * sizeof(UInt32);
        NGICopyBuffer region{
            ibStagingBufferWrap.GetNGIOffset(),
            indexBufferOffset,
            totalIbSize,
        };
        rendererSystem->UpdateBuffer(ib.get(), ibStagingBufferWrap.GetNGIBuffer(), region, NGIResourceState::Undefined, NGIResourceState::IndexBuffer);
    }

    mergedGeometryPacket.vertexCount += totalVertexCount;
    mergedGeometryPacket.indexCount += totalIndexCount;
}

//////////////////////////////////////////////////////////////////////////
// VertexStreamLayoutPolicySkinningAndStatic
//
//////////////////////////////////////////////////////////////////////////
void VertexStreamLayoutPolicySkinningAndStatic::AssembleGpuResource(MeshR* renderMesh, const MeshAssetData* meshAsset, IVertexStreamLayoutParameter* meshParameter /*= nullptr*/)
{
    SCOPED_CPU_TIMING(GroupRendering, "VertexStreamLayoutPolicySkinningAndStatic::AssembleGpuResource");
    UInt32 totalMeshPartCount = meshAsset->GetAllLodMeshPartCount();
    if (totalMeshPartCount == 0)
        return;

    auto* meshParam = static_cast<VertexStreamLayoutSkinningAndStaticParameter*>(meshParameter);

    // Collect input Primary Model vertex channels information
    auto vertexBufferSpaces = meshParam->VertexBufferSpaces;
    auto vertexBufferLayouts = meshParam->VertexBufferLayouts;

    // Collect skinned vertex buffer (Pos, Nor, Tan, SKIN_ID, SKIN_WT, PrePos) size & remaining vertex buffer 
    const UInt32 vtxCount = meshAsset->GetVertexCount();
    
    // Create geometry packet for input Primary Model
    GeometryPacketPtr geoPacket = RenderFactory::Instance().CreateGeometryPacket();

    // Create vertex buffer persisting Model's all sub model vertices
    Assert(vertexBufferLayouts.size() == 2 && vertexBufferSpaces.size() == 2);

    // Grab RendererSystem for allocate following gpu buffer
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    
    std::string vbSkinName = fmt::format("{}_skin", meshAsset->GetName());
    for (UInt8 skinStreamIndex = 0; skinStreamIndex < vertexBufferLayouts[VertexStreamLayoutUtil::skinnedBufferSpaceIndex].size(); skinStreamIndex++)
    {
        const VertexStreamLayout& streamLayout = vertexBufferLayouts[VertexStreamLayoutUtil::skinnedBufferSpaceIndex][skinStreamIndex];
        // One channel per stream for bindless access
        Assert(streamLayout.GetChannelCount() == 1);

        UInt32 streamVBSize = vtxCount * streamLayout.GetVertexStride();
        NGIBufferPtr streamVB = RenderFactory::Instance().CreateVertexBuffer(streamVBSize, NGIBufferUsage::RWTexelBuffer | NGIBufferUsage::RWStructuredBuffer);
        std::string debugName = vbSkinName + " Channel" + std::to_string(skinStreamIndex);
        streamVB->SetDebugName(debugName.c_str());
        geoPacket->AddVertexStream(streamVB.get(), streamVBSize, 0, streamLayout);

        auto& curBinding = vertexBufferSpaces[VertexStreamLayoutUtil::skinnedBufferSpaceIndex].Bindings[skinStreamIndex];
        Assert(curBinding.mSize == streamVBSize);

        // Upload vertex buffer data
        auto stagingBufferWrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, streamVBSize);
        stagingBufferWrap.MemWrite(0, curBinding.mSrc, curBinding.mSize);
        NGICopyBuffer region{
            stagingBufferWrap.GetNGIOffset(),
            0,
            streamVBSize,
        };
        rendererSystem->UpdateBuffer(streamVB.get(), stagingBufferWrap.GetNGIBuffer(), region, NGIResourceState::Undefined, NGIResourceState::VertexBuffer);
    }

    std::string vbStaticName = fmt::format("{}_static", meshAsset->GetName());
    for (UInt8 staticStreamIndex = 0; staticStreamIndex < vertexBufferLayouts[VertexStreamLayoutUtil::staticBufferSpaceIndex].size(); staticStreamIndex++)
    {
        const VertexStreamLayout& streamLayout = vertexBufferLayouts[VertexStreamLayoutUtil::staticBufferSpaceIndex][staticStreamIndex];
        // One channel per stream for bindless access
        Assert(streamLayout.GetChannelCount() == 1);

        UInt32 streamVBSize = vtxCount * streamLayout.GetVertexStride();
        NGIBufferPtr streamVB = RenderFactory::Instance().CreateVertexBuffer(streamVBSize, NGIBufferUsage::RWTexelBuffer | NGIBufferUsage::RWStructuredBuffer);
        std::string debugName = vbStaticName + " Channel" + std::to_string(staticStreamIndex);
        streamVB->SetDebugName(debugName.c_str());
        geoPacket->AddVertexStream(streamVB.get(), streamVBSize, 0, streamLayout);

        auto& curBinding = vertexBufferSpaces[VertexStreamLayoutUtil::staticBufferSpaceIndex].Bindings[staticStreamIndex];
        Assert(curBinding.mSize == streamVBSize);

        // Upload vertex buffer data
        auto stagingBufferWrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, streamVBSize);
        stagingBufferWrap.MemWrite(0, curBinding.mSrc, curBinding.mSize);
        NGICopyBuffer region{
            stagingBufferWrap.GetNGIOffset(),
            0,
            streamVBSize,
        };
        rendererSystem->UpdateBuffer(streamVB.get(), stagingBufferWrap.GetNGIBuffer(), region, NGIResourceState::Undefined, NGIResourceState::VertexBuffer);
    }
    
    // Upload index buffer
    if (meshAsset->HasIndexStream())
        VertexStreamLayoutUtil::SetIndexBufferForGeometryPacket(meshAsset, geoPacket, 0);

    VertexStreamLayoutUtil::SetRenderGeometryDataFromGeometryPacket(renderMesh, meshAsset, geoPacket, 0, 0);
}

//////////////////////////////////////////////////////////////////////////
// VertexStreamLayoutPolicyStaticBlendShape
//
//////////////////////////////////////////////////////////////////////////
void VertexStreamLayoutPolicyStaticBlendShape::AssembleGpuResource(MeshR* renderMesh, const MeshAssetData* meshAsset, IVertexStreamLayoutParameter* meshParameter)
{
    SCOPED_CPU_TIMING(GroupRendering, "VertexStreamLayoutPolicyStaticBlendShape::AssembleGpuResource");
    VertexStreamLayoutBlendShapeParameter* meshParam = static_cast<VertexStreamLayoutBlendShapeParameter*>(meshParameter);

    VertexUploadBufferBinding uploadBufferBinding[MaxVertexChannelCount];
    UInt32 bufferBindingCount = 0;

    // Collect vertex channel information
    std::vector<VertexStreamLayout> vertexStreamLayouts;
    VertexStreamLayoutUtil::CollectVertexChannelDataForStaticMesh(meshAsset, uploadBufferBinding, bufferBindingCount, vertexStreamLayouts, meshParam);

    const UInt32 vtxCount = meshAsset->GetVertexCount();

    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    GeometryPacketPtr geoPak{nullptr};
    // GeometryPak not exist, this means current mesh is firstly built.
    if (renderMesh->GetRenderGeometries().empty())
    {
        // Create geometry packet
        geoPak = RenderFactory::Instance().CreateGeometryPacket();
        // Create vertex buffer
        for (UInt32 streamIndex = 0; streamIndex < vertexStreamLayouts.size(); streamIndex++)
        {
            auto& streamLayout = vertexStreamLayouts[streamIndex];
            // Assert only one channel per stream for bindless access
            Assert(streamLayout.GetChannelCount() == 1);
            
            UInt32 streamVBSize = vtxCount * streamLayout.GetVertexStride();
            NGIBufferPtr streamVB = RenderFactory::Instance().CreateVertexBuffer(streamVBSize);
            std::string debugName = meshAsset->GetName() + " Channel" + std::to_string(streamIndex);
            streamVB->SetDebugName(debugName.c_str());
            geoPak->AddVertexStream(streamVB.get(), streamVBSize, 0, streamLayout);
        }

        // Create index buffer
        if (meshAsset->HasIndexStream())
        {
            VertexStreamLayoutUtil::SetIndexBufferForGeometryPacket(meshAsset, geoPak);
        }

        VertexStreamLayoutUtil::SetRenderGeometryDataFromGeometryPacket(renderMesh, meshAsset, geoPak);
    }
    // GeometryPak already exist, we just need to update data of it
    else
    {
        auto& renderGeo = renderMesh->GetRenderGeometry(0);
        geoPak.reset(renderGeo.GetGeometryPacket());

        Assert(geoPak->GetStreamCount() > 0);
    }

    for (UInt32 streamIndex = 0; streamIndex < vertexStreamLayouts.size(); streamIndex++)
    {
        auto& curBinding = uploadBufferBinding[streamIndex];
        UInt32 streamVBSize = vtxCount * vertexStreamLayouts[streamIndex].GetVertexStride();
        Assert(streamVBSize == curBinding.mSize);
        
        auto stagingBufferWrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, streamVBSize);
        size_t stagingBufferOffset = 0;
        stagingBufferWrap.MemWrite(stagingBufferOffset, curBinding.mSrc, streamVBSize);

        NGICopyBuffer region{
            .SrcOffset = stagingBufferWrap.GetNGIOffset(),
            .DstOffset = 0,
            .NumBytes = streamVBSize};
        rendererSystem->UpdateBuffer(geoPak->GetVertexStream(static_cast<UInt8>(streamIndex))->GetGpuBuffer(), stagingBufferWrap.GetNGIBuffer(),
            region, NGIResourceState::Undefined, NGIResourceState::VertexBuffer);
    }
}

//////////////////////////////////////////////////////////////////////////
// VertexStreamLayoutPolicyCPUSkin
//
//////////////////////////////////////////////////////////////////////////
void VertexStreamLayoutPolicyCPUSkin::AssembleGpuResource(MeshR* renderMesh, const MeshAssetData* meshAsset, IVertexStreamLayoutParameter* meshParameter)
{
    AssertMsg(false, "CPU Skin is deprecated");
    
    SCOPED_CPU_TIMING(GroupRendering, "VertexStreamLayoutPolicyCPUSkin::AssembleGpuResource");
    VertexStreamLayoutSkinParameter* skinParam = static_cast<VertexStreamLayoutSkinParameter*>(meshParameter);
    cross::anim::SkeltMeshCPUSkinUtil::InputVertexSkinningInfo& VertexSkinningInfo = skinParam->VertexSkinningInfo;
    cross::anim::SkeltMeshCPUSkinUtil::SkinWeightInfo& SkinningWeightInfo = skinParam->SkinningWeightInfo;

    // Collect vertex channel information
    // TODO(mindalv): 
    // Note
    // Now we support vertex attributes with difference level of compression
    // that is, normal, tanget 's format can be different R8G8B8_SNORM R16B16G16_SNORM etc
    // and channelOffsets need alignment according vulkan specs.
    // please refer the VertexStreamLayoutPolicyStaticAllInOne
    VertexStreamLayout streamDesc;
    std::vector<VertexStreamLayout> vertexStreamLayouts;
    UInt32 channelOffset = VertexStreamLayoutUtil::CollectVertexChannelDataForCPUSkinnedMesh(meshAsset, skinParam->UploadBufferBinding, skinParam->BufferBindingCount, vertexStreamLayouts, streamDesc, skinParam);

    const UInt32 vtxCount = meshAsset->GetVertexCount();
    SkinningWeightInfo.InfulenceVertCount = vtxCount;

    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    const UInt32 vbSize = vtxCount * streamDesc.GetVertexStride();

    GeometryPacketPtr geoPak{nullptr};
    NGIBufferPtr vb{nullptr};
    // GeometryPak not exist, this means current mesh is firstly built.
    if (renderMesh->GetRenderGeometries().empty())
    {
        // Create geometry packet
        geoPak = RenderFactory::Instance().CreateGeometryPacket();
        // Create vertex buffer
        vb = RenderFactory::Instance().CreateVertexBuffer(vbSize);
        vb->SetDebugName(meshAsset->GetName().c_str());
        // Add vertex buffer into geometry packet
        geoPak->AddVertexStream(vb.get(), vbSize, 0, streamDesc);

        // Create index buffer
        if (meshAsset->HasIndexStream())
        {
            VertexStreamLayoutUtil::SetIndexBufferForGeometryPacket(meshAsset, geoPak);
        }

        VertexStreamLayoutUtil::SetRenderGeometryDataFromGeometryPacket(renderMesh, meshAsset, geoPak);
        
        std::vector<Float3> initPrePositions(vtxCount);
        memcpy(initPrePositions.data(), VertexSkinningInfo.InPositions, sizeof(Float3) * vtxCount);
        geoPak->SetPrePositions(std::move(initPrePositions));
    }
    // GeometryPak already exist, we just need to update data of it
    else
    {
        auto& renderGeo = renderMesh->GetRenderGeometry(0);
        geoPak.reset(renderGeo.GetGeometryPacket());

        Assert(geoPak->GetStreamCount() > 0);
        auto vertexStream = geoPak->GetVertexStream(0);
        auto vbPtr = vertexStream->GetGpuBuffer();
        vb.reset(vbPtr);
        
        VertexSkinningInfo.InOutPrePositions = geoPak->GetPrePositions().data();
    }

    VertexSkinningInfo.PerVertexStride = channelOffset;

    // Determine VB info for skin
    skinParam->VB = vb;
    skinParam->VBSize = static_cast<UInt32>(vbSize);

    skinParam->VBStagingBufferWrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, vbSize);
}

//////////////////////////////////////////////////////////////////////////
// VertexStreamLayoutPolicyStaticLODStreaming
//
//////////////////////////////////////////////////////////////////////////
void VertexStreamLayoutPolicyStaticLODStreaming::AssembleGpuResource(MeshR* renderMesh, const MeshAssetData* meshAsset, IVertexStreamLayoutParameter* meshParameter)
{
    SCOPED_CPU_TIMING(GroupRendering, "VertexStreamLayoutPolicyStaticLODStreaming::AssembleGpuResource");
    VertexStreamLayoutStaticLODStreamingParameter* meshParam = static_cast<VertexStreamLayoutStaticLODStreamingParameter*>(meshParameter);

    // Grab RendererSystem for allocate following gpu buffer
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    // Streaming is disabled, only calculate buffer data
    if (!meshParam->EnableStreaming)
    {
        // Initialize LOD meshes
        UInt32 lodMeshCount = renderMesh->GetLODMeshCount();
        if (lodMeshCount == 0 || lodMeshCount != meshAsset->GetLodCount())
        {
            renderMesh->ClearAndResizeLODMeshes(meshAsset->GetLodCount());
        }

        // Collect vertex channel information
        VertexUploadBufferBinding uploadBufferBinding[MaxVertexChannelCount];
        UInt32 bufferBindingCount = 0;
        // TODO: Blend shape is not considered now
        std::vector<VertexStreamLayout> vertexStreamLayouts;
        VertexStreamLayoutUtil::CollectVertexChannelDataForStaticMesh(meshAsset, uploadBufferBinding, bufferBindingCount, vertexStreamLayouts, nullptr);

        Assert(meshAsset->HasIndexStream());
        const IndexStreamAssetData& indexAsset = meshAsset->GetIndexStream();
        UInt32 indexSizeBytes = indexAsset.mIs16BitIndex ? sizeof(UInt16) : sizeof(UInt32);
        IndexBufferFormat indexFormat = indexAsset.mIs16BitIndex ? IndexFormat_UInt16 : IndexFormat_UInt32;

        for (UInt32 lodIndex = 0; lodIndex < meshAsset->GetLodCount(); lodIndex++)
        {
            auto* lodRenderMesh = renderMesh->GetLODMesh(static_cast<UInt8>(lodIndex));
            Assert(lodRenderMesh);

            GeometryPacketPtr& geoPack = lodRenderMesh->GeometryPacket;
            if (!geoPack)
            {
                // Create geometry packet for each LOD model
                geoPack = RenderFactory::Instance().CreateGeometryPacket();
            }

            UInt32 vertexCount = 0;
            UInt32 indexCount = 0;

            UInt32 startIndex = 0;
            UInt32 partCount = 0;

            UInt32 vertexStartIndex = 0;
            UInt32 indexStartIndex = 0;

            meshAsset->GetMeshLodInfo(lodIndex, startIndex, partCount);
            for (UInt32 partIndex = startIndex; partIndex < startIndex + partCount; partIndex++)
            {
                auto& partInfo = meshAsset->GetMeshPartInfo(partIndex);

                // Start index is the start index of the first mesh part of this LOD level
                if (vertexStartIndex == 0 && partIndex == startIndex)
                {
                    vertexStartIndex = partInfo.mVertexStart;
                }

                if (indexStartIndex == 0 && partIndex == startIndex)
                {
                    indexStartIndex = partInfo.mIndexStart;
                }

                vertexCount += partInfo.mVertexCount;
                indexCount += partInfo.mIndexCount;
            }
            
            UInt32 ibSize = indexCount * indexSizeBytes;

            // Create render geometry for each mesh part
            VertexStreamLayoutUtil::SetRenderGeometryDataFromLODGeometryPacket(renderMesh, meshAsset, geoPack, static_cast<UInt8>(lodIndex));

            // Store data in render mesh
            lodRenderMesh->VertexStartIndex = vertexStartIndex;
            lodRenderMesh->VertexCount = vertexCount;
            lodRenderMesh->VertexStreamLayouts = vertexStreamLayouts;
            memcpy(lodRenderMesh->UploadBufferBinding, uploadBufferBinding, sizeof(uploadBufferBinding));
            lodRenderMesh->BufferBindingCount = bufferBindingCount;
            for (UInt32 streamIndex = 0; streamIndex < lodRenderMesh->VertexStreamLayouts.size(); streamIndex++)
            {
                // Only one channel for stream for bindless access
                Assert(lodRenderMesh->VertexStreamLayouts[streamIndex].GetChannelCount() == 1);
                UInt32 streamVBSize = vertexCount * lodRenderMesh->VertexStreamLayouts[streamIndex].GetVertexStride();
                lodRenderMesh->StreamVertexBufferSize.push_back(streamVBSize);
            }

            lodRenderMesh->IndexStartIndex = indexStartIndex;
            lodRenderMesh->IndexCount = indexCount;
            lodRenderMesh->IndexFormat = indexFormat;
            lodRenderMesh->IndexBufferSize = ibSize;
        }
    }
    // Streaming is enabled, use data collected before to upload them to GPU
    // TODO: Will frequently create buffers cause memory fragments?
    else
    {
        UInt8 lodLevel = meshParam->SelectedLOD;
        Assert(lodLevel >= 0 && lodLevel < meshAsset->GetLodCount());

        auto* lodRenderMesh = renderMesh->GetLODMesh(static_cast<UInt8>(lodLevel));
        Assert(lodRenderMesh);

        std::string debugName = fmt::format("{}_LOD_{}", meshAsset->GetName(), lodLevel);
        std::string vbDebugName = fmt::format("{}_Vertex", debugName);
        std::string ibDebugName = fmt::format("{}_Index", debugName);

        // Create vertex buffer for each LOD models
        const auto& geoPack = lodRenderMesh->GeometryPacket;
        geoPack->Clear();
        UInt32 skipVertexCount = lodRenderMesh->VertexStartIndex;
        // Create and Upload VertexBuffers
        for (UInt32 streamIndex = 0; streamIndex < lodRenderMesh->VertexStreamLayouts.size(); streamIndex++)
        {
            UInt32 streamVBSize = lodRenderMesh->StreamVertexBufferSize[streamIndex];
            NGIBufferPtr streamVB = RenderFactory::Instance().CreateVertexBuffer(streamVBSize);
            geoPack->AddVertexStream(streamVB.get(), streamVBSize, 0, lodRenderMesh->VertexStreamLayouts[streamIndex]);
            streamVB->SetDebugName((vbDebugName + " Channel" + std::to_string(streamIndex)).c_str());

            const VertexStreamLayout& streamLayout = lodRenderMesh->VertexStreamLayouts[streamIndex];
            const VertexUploadBufferBinding& curBinding = lodRenderMesh->UploadBufferBinding[streamIndex];
                
            NGIBufferDesc desc{streamVBSize, NGIBufferUsage::CopySrc};
            NGIStagingBuffer* stagingBuffer = GetNGIDevice().CreateStagingBuffer(desc);
                
            UInt32 skipSize = skipVertexCount * streamLayout.GetVertexStride();
            Assert(curBinding.mSize == streamVBSize + skipSize);

            UInt8* dst = static_cast<UInt8*>(stagingBuffer->MapRange(NGIBufferUsage::CopySrc, 0, streamVBSize));
            memcpy(dst, curBinding.mSrc, curBinding.mSize);

            stagingBuffer->UnmapRange(0, streamVBSize);
            NGICopyBuffer region{
                .SrcOffset = 0,
                .DstOffset = 0,
                .NumBytes = streamVBSize};
            rendererSystem->UpdateBuffer(streamVB.get(), stagingBuffer, region, NGIResourceState::Undefined, NGIResourceState::VertexBuffer);
            rendererSystem->DestroyNGIObjectInAsyncThread(stagingBuffer);
        }

        // Create index buffer for each LOD models
        UInt32 ibSize = lodRenderMesh->IndexBufferSize;
        NGIBufferPtr ib = RenderFactory::Instance().CreateIndexBuffer(ibSize, lodRenderMesh->IndexFormat);
        ib->SetDebugName(ibDebugName.c_str());
        geoPack->SetIndexStream(ib.get(), ibSize, lodRenderMesh->IndexCount);
        // Upload index buffer
        {
            const IndexStreamAssetData& indexAsset = meshAsset->GetIndexStream();

            UInt8* destBuffer{nullptr};
            UInt32 indexSizeBytes = indexAsset.mIs16BitIndex ? sizeof(UInt16) : sizeof(UInt32);
            const UInt8* srcData = indexAsset.mData.data() + lodRenderMesh->IndexStartIndex * indexSizeBytes;

            if (ib->Mappable())
            {
                destBuffer = static_cast<UInt8*>(ib->Map());
                memcpy(destBuffer, srcData, ibSize);
                ib->Unmap();
            }
            else
            {
                NGIBufferDesc desc{ibSize, NGIBufferUsage::CopySrc};
                NGIStagingBuffer* stagingBuffer = GetNGIDevice().CreateStagingBuffer(desc);
                destBuffer = static_cast<UInt8*>(stagingBuffer->MapRange(NGIBufferUsage::CopySrc, 0, ibSize));

                memcpy(destBuffer, srcData, ibSize);

                stagingBuffer->UnmapRange(0, ibSize);
                NGICopyBuffer region{0, 0, ibSize};
                rendererSystem->UpdateBuffer(ib.get(), stagingBuffer, region, NGIResourceState::Undefined, NGIResourceState::IndexBuffer);
                rendererSystem->DestroyNGIObjectInAsyncThread(stagingBuffer);
            }
        }
    }
}

}   // namespace cross
