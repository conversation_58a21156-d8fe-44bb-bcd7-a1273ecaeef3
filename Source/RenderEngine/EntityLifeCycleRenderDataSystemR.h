#pragma once
#include "CECommon/Common/RenderSystemBase.h"
#include "RenderEngine/RenderEngineForward.h"
#include "ECS/Develop/Framework/Types.h"
#include "RenderEngine/GPUScene/GPUScene.h"

namespace cross {
struct RenderEntityCreateData
{
    ecs::EntityID entity;
};

struct RenderEntityVSMUpdateData
{
    ecs::EntityID entity;
    BoundingBox boundingBox;   // world space
    Float3 tilePosition;
    bool isCastShadow;
};

struct RenderEntityChangeData
{
    enum class ChangeType : UInt32
    {
        None = 0,
        Transform = 1,
        RenderProperty = Transform << 1,
        RenderEffect = RenderProperty << 1,
        
        All = Transform | RenderProperty | RenderEffect,
    };

    ecs::EntityID entity;
    ChangeType type;

    BoundingBox prevBoundingBox;   // world space
    Float3 prevTilePosition;
    BoundingBox currBoundingBox;   // world space
    Float3 currTilePosition;
};

ENUM_CLASS_FLAGS(RenderEntityChangeData::ChangeType);

class EntityLifeCycleRenderDataSystemR : public RenderSystemBase
{
    CEMetaInternal(Reflect)
public:
    RENDER_ENGINE_API static EntityLifeCycleRenderDataSystemR* CreateInstance();

    virtual void Release() override;

    const auto& GetRenderEntityCreateList() const
    {
        return mRenderEntityCreateList;
    }

    const auto& GetRenderEntityVSMUpdateList() const
    {
        return mRenderEntityVSMUpdateList;
    }

    const auto& GetRenderEntityChangeList() const
    {
        return mRenderEntityChangeList;
    }

    void AddRenderEntityUpdateVSM(ecs::EntityID entity);

    CEFunction(Reflect)
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

protected:
    virtual void OnBeginFrame(FrameParam* frameParam) override;
    virtual void OnFirstUpdate(FrameParam* frameParam) override;
    virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

private:
    bool IsAliveRenderEntity(ecs::EntityID entity);
    bool IsRenderEntity(ecs::EntityID entity);
    void AddRenderEntityCreated(ecs::EntityID entity);
    void AddRenderEntityChanged(ecs::EntityID entity, RenderEntityChangeData::ChangeType changeType);

private:
    std::unordered_map<ecs::EntityID, SizeType> mRenderEntityCreateListMap;
    std::vector<RenderEntityCreateData> mRenderEntityCreateList;

    std::unordered_map<ecs::EntityID, SizeType> mRenderEntityVSMUpdateListMap;
    std::vector<RenderEntityVSMUpdateData> mRenderEntityVSMUpdateList;

    std::unordered_map<ecs::EntityID, SizeType> mRenderEntityChangeListMap;
    std::vector<RenderEntityChangeData> mRenderEntityChangeList;

    std::mutex mRenderEntityCreateListMapMutex;
    std::mutex mRenderEntityVSMUpdateListMapMutex;

    GPUScene* mGPUScene = nullptr;
    RayTracingScene* mRayTracingScene = nullptr;
};

}   // namespace cross
