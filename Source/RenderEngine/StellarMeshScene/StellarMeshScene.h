#pragma once
#include "CrossBase/Math/CrossMath.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "RenderEngine/StellarMeshScene/StellarMeshShared.h"

#ifndef _MANAGED
#include <span>
#endif

namespace cross {
struct FFSRenderPipelineSetting;

struct StellarSceneData
{
    alignas(16) Float4x4 mWorldMatrix;
    //alignas(16) Float4x4 mPrevWorldMatrix;
    alignas(16) Float3   mLocalBoundsCenter;
    alignas(4)  UInt32   mPadding0;
    alignas(16) Float3   mLocalBoundsExtent;
    alignas(4)  UInt32   mMeshID;
};

class StellarMeshScene
{
public:
    StellarMeshScene();
    ~StellarMeshScene();
    
    void Initialize(RenderWorld* renderWorld, RenderingExecutionDescriptor* red);

    void SetFFSRenderPipelineSetting(const FFSRenderPipelineSetting* renderPipelineSetting);

    void AddOrUpdate(ecs::EntityID entityID, StellarSceneData& data);

    void MarkNeedUpdataDataFor(ecs::EntityID entityID);

#ifndef _MANAGED
    void Add(ecs::EntityID entityID, std::span<StellarSceneData> data, std::span<MaterialR*> mtls);
#endif

    void Remove(ecs::EntityID entityID);
    
    void Update();

    void PostUpdate();
    
    REDResidentBufferView* GetStellarSceneBufferView();
    
    UInt32 GetStellarMeshSceneCount();

    const StellarMeshRasterPipelines& GetStellarMeshRasterPipelines();
    
    // void Reset() { mIndexAllocator.Reset(); mUploadBuffer.Reset(); }
    
private:
    struct Impl;
    std::unique_ptr<Impl> pImpl;
};

} // namespace cross
