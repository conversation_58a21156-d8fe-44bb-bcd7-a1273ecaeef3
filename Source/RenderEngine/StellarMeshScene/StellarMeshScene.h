#pragma once
#include "CrossBase/Math/CrossMath.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"

namespace cross {
struct FFSRenderPipelineSetting;

struct StellarSceneData
{
    alignas(16) Float4x4 mWorldMatrix;
    //alignas(16) Float4x4 mPrevWorldMatrix;
    alignas(16) Float3   mLocalBoundsCenter;
    alignas(16) Float3   mLocalBoundsExtent;
    alignas(4)  UInt32   mMeshID;
};

class StellarMeshScene
{
public:
    StellarMeshScene();
    ~StellarMeshScene();
    
    void Initialize(RenderWorld* renderWorld, RenderingExecutionDescriptor* red);

    void SetFFSRenderPipelineSetting(const FFSRenderPipelineSetting* renderPipelineSetting);
    
    void Add(ecs::EntityID entityID, UInt32 count, StellarSceneData& data);

    void Remove(ecs::EntityID entityID, UInt32 count);
    
    void Update();

    void PostUpdate();
    
    REDResidentBufferView* GetStellarSceneBufferView();
    
    UInt32 GetStellarMeshSceneCount();
    
    // void Reset() { mIndexAllocator.Reset(); mUploadBuffer.Reset(); }
    
private:
    struct Impl;
    std::unique_ptr<Impl> pImpl;
};

} // namespace cross
