#include "StellarMeshScene.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/GPUScene/GPUScene.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"

namespace cross {

struct StellarMeshScene::Impl
{
    Impl() = default;
    ~Impl() = default;
    
    RenderWorld* mRenderWorld{};
    RenderingExecutionDescriptor* mRED{};
    const FFSRenderPipelineSetting* mFFSRenderPipelineSetting{};

    std::mutex updateEntitySetMutex{};
    std::unordered_set<ecs::EntityID> mUpdateEntities{};
    std::unordered_set<ecs::EntityID> mLastFrameMovedEntities{};
    
    std::mutex mIndexAllocatorMutex{};
    ObjectIndexAllocator mIndexAllocator{};

    REDUniquePtr<REDResidentBuffer> mStellarSceneBuffer{};
    REDUniquePtr<REDResidentBufferView> mStellarSceneBufferView{};

    ScatterBytesUploadBuffer mStellarMeshSceneUploadBuffer{};

    StellarMeshRasterPipelines mStellarMeshRasterPipelines;

    std::mutex mEntityIndexMutex{};
    std::unordered_map<ecs::EntityID, std::pair<UInt32, UInt32>> mStellarSceneIndexMap{};
    std::unordered_map<ecs::EntityID, std::vector<MaterialR*>> mEntityToMaterials;
    
    constexpr static NGIBufferUsage StellarSceneBufferUsage = NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopySrc | NGIBufferUsage::CopyDst;

    const StellarSceneData InvalidStellarSceneData = StellarSceneData{ 
        Float4x4::Identity(),
        Float3::Zero(),
        0,
        Float3::Zero(), 
        0xFFFFFFFFu,
    };

    UInt32 Allocate(ecs::EntityID entityID, UInt32 count)
    {
        std::lock_guard lock{mIndexAllocatorMutex};
        auto id = mIndexAllocator.Allocate(sizeof(StellarSceneData), count);
        mStellarSceneIndexMap.insert_or_assign(entityID, std::make_pair(id, count));
        return id;
    }

    std::tuple<UInt32, UInt32> Release(ecs::EntityID entityID)
    {
        std::lock_guard lock{mIndexAllocatorMutex};
        auto [id, count] = mStellarSceneIndexMap[entityID];
        mIndexAllocator.Free(sizeof(StellarSceneData), count, id);
        mStellarSceneIndexMap.erase(entityID);
        return {id, count};
    }

    void AddOrUpdate(ecs::EntityID entityID, StellarSceneData& data)
    {
        UInt32 stellarSceneIndex;
        {
            std::lock_guard lock{mEntityIndexMutex};
            if (mStellarSceneIndexMap.contains(entityID))
            {
                stellarSceneIndex = mStellarSceneIndexMap.at(entityID).first;
            }
            else
            {
                stellarSceneIndex = Allocate(entityID, 1);
            }
        }
        //LOG_DEBUG("StellarMeshScene: AddOrUpdate EntityID: {}, InstanceID: {}.", entityID.GetValue(), stellarSceneIndex);

        UInt32 sizeInBytes = sizeof(StellarSceneData);
        UInt32 dstOffsetInBytes = stellarSceneIndex * sizeof(StellarSceneData);

        auto dstData = mStellarMeshSceneUploadBuffer.AddScatterData(sizeInBytes, dstOffsetInBytes);
        memcpy(dstData, &data, sizeof(StellarSceneData));
    }

    void Add(ecs::EntityID entityID, std::span<StellarSceneData> data, std::span<MaterialR*> mtls)
    {
        // TODO

        //auto stellarSceneIndex = Allocate(entityID, static_cast<UInt32>(data.size()));
        //mMaxUsedIndex = std::max(mMaxUsedIndex, stellarSceneIndex + static_cast<UInt32>(data.size()));

        //UInt32 sizeInBytes = static_cast<UInt32>(data.size()) * sizeof(StellarSceneData);
        //UInt32 dstOffsetInBytes = stellarSceneIndex * sizeof(StellarSceneData);

        //auto dstData = mStellarMeshSceneUploadBuffer.AddScatterData(sizeInBytes, dstOffsetInBytes);
        //memcpy(dstData, data.data(), sizeof(StellarSceneData) * data.size());
        //for (auto mtl : mtls)
        //{
        //     if (auto [itr, ret] = mStellarMeshRasterPipelines.PipelineMap.try_emplace(mtl, StellarMeshRasterPipeline{mtl}); ret)
        //     {
        //         mStellarMeshRasterPipelines.Pipelines.emplace_back(&itr->second);
        //     }
        //}

        //mEntityToMaterials[entityID] = std::vector(mtls.begin(), mtls.end());
    }

    void Remove(ecs::EntityID entityID)
    {
        if (!mStellarSceneIndexMap.contains(entityID))
        {
            return;
        }

        auto [stellarSceneIndex, count] = Release(entityID);
        //LOG_DEBUG("StellarMeshScene: Remove EntityID: {}, InstanceID: {}.", entityID.GetValue(), stellarSceneIndex);

        UInt32 sizeInBytes = count * sizeof(StellarSceneData);
        UInt32 dstOffsetInBytes = stellarSceneIndex * sizeof(StellarSceneData);

        auto dstData = mStellarMeshSceneUploadBuffer.AddScatterData(sizeInBytes, dstOffsetInBytes);
        memcpy(dstData, &InvalidStellarSceneData, sizeof(StellarSceneData) * count);

        for (auto* mtl : mEntityToMaterials[entityID])
        {
            if (auto ret = mStellarMeshRasterPipelines.PipelineMap.find(mtl); ret != mStellarMeshRasterPipelines.PipelineMap.end())
            {
                std::erase(mStellarMeshRasterPipelines.Pipelines, &ret->second);
                mStellarMeshRasterPipelines.PipelineMap.erase(mtl);
            }
        }
        mEntityToMaterials.erase(entityID);
    }

    void ResizeStellarSceneBuffer()
    {
        UInt32 newSize = std::bit_ceil(std::max(mIndexAllocator.GetUsedSize(), 1u));
        newSize = std::max(newSize, 1024u);

        if (!mStellarSceneBuffer || mStellarSceneBuffer->GetDesc().Size != newSize)
        {
            auto newBuffer = mRED->CreateBuffer("StellarMeshScene.Buffer", NGIBufferDesc{newSize, StellarSceneBufferUsage});

            if (mStellarSceneBuffer)
            {
                NGICopyBuffer region{0, 0, std::min<SizeType>(mStellarSceneBuffer->GetDesc().Size, newSize)};
                mRED->AllocatePass("CopyStellarSceneBuffer")->CopyBufferToBuffer(newBuffer.get(), mStellarSceneBuffer.get(), 1, &region);
            }

            mStellarSceneBuffer = std::move(newBuffer);

            NGIBufferViewDesc viewDesc{NGIBufferUsage::StructuredBuffer, 0, newSize, GraphicsFormat::Unknown, sizeof(StellarSceneData)};

            mStellarSceneBufferView = mRED->CreateBufferView(mStellarSceneBuffer.get(), viewDesc);
        }
    }

    void UploadDataToGPU(StellarMeshScene& stellarMeshScene)
    {
        if (!mUpdateEntities.empty())
        {
            QUICK_SCOPED_CPU_TIMING("upload data");
            threading::ParallelFor(mUpdateEntities.begin(), mUpdateEntities.end(), [&](auto iter) {
                auto& entity = *iter;
                if (mRenderWorld->IsEntityAlive(entity))
                {
                    auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(entity);
                    auto* renderNode = renderNodeComp.Read()->mRenderNode.get();
                    renderNode->UploadStellarMeshScene(stellarMeshScene, mRenderWorld, entity);
                }
            });

            mUpdateEntities.clear();
        }
        
        ResizeStellarSceneBuffer();

        {
            QUICK_SCOPED_CPU_TIMING("assemble upload pass");
            mStellarMeshSceneUploadBuffer.UploadToDstBuffer(mRED, mFFSRenderPipelineSetting, mStellarSceneBuffer.get());
        }
    }

    void Update(StellarMeshScene& stellarMeshScene)
    {
        QUICK_SCOPED_CPU_TIMING("StellarMeshScene::Update");

        std::lock_guard lock{updateEntitySetMutex};
        auto* entityLifeCycleRenderDataSys = mRenderWorld->GetRenderSystem<EntityLifeCycleRenderDataSystemR>();

        // handle entity create
        {
            QUICK_SCOPED_CPU_TIMING("handle entity create");

            for (const auto& data : entityLifeCycleRenderDataSys->GetRenderEntityCreateList())
            {
                auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(data.entity);
                auto renderNodeCompWriter = renderNodeComp.Write();

                if (mRenderWorld->IsEntityAlive(data.entity) && mRenderWorld->HasComponent<RenderNodeComponentR>(data.entity) && mRenderWorld->HasComponent<AABBComponentR>(data.entity))
                {
                    mUpdateEntities.insert(data.entity);
                }
            }
        }

        // handle entity change
        {
            QUICK_SCOPED_CPU_TIMING("handle entity change");

            // if an entity moved in the last frame, we need also update GPUScene.
            for (auto entity : mLastFrameMovedEntities)
            {
                if (mRenderWorld->IsEntityAlive(entity) && mRenderWorld->HasComponent<RenderNodeComponentR>(entity) && mRenderWorld->HasComponent<AABBComponentR>(entity))
                {
                    mUpdateEntities.insert(entity);

                    auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(entity);
                    auto renderNodeCompWriter = renderNodeComp.Write();
                    //renderNodeCompWriter->mRenderNode->ClearTransformState();
                }
            }

            mLastFrameMovedEntities.clear();

            for (const auto& data : entityLifeCycleRenderDataSys->GetRenderEntityChangeList())
            {
                if (!mRenderWorld->IsEntityAlive(data.entity))
                    continue;

                auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(data.entity);

                mUpdateEntities.insert(data.entity);

                if (data.type == RenderEntityChangeData::ChangeType::Transform)
                {
                    // store moved entity for the next frame
                    mLastFrameMovedEntities.insert(data.entity);

                    auto renderNodeCompWriter = renderNodeComp.Write();
                    //renderNodeCompWriter->mRenderNode->MarkAsTransformed();
                }
            }
        }

        UploadDataToGPU(stellarMeshScene);
    }

    void PostUpdate()
    {
        if (mStellarSceneBuffer)
        {
            mRED->FlushState(mStellarSceneBuffer.get(), NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
        }
    }
};

StellarMeshScene::StellarMeshScene()
    : pImpl(std::make_unique<Impl>())
{
}

StellarMeshScene::~StellarMeshScene() = default;

void StellarMeshScene::Initialize(RenderWorld* renderWorld, RenderingExecutionDescriptor* red)
{
    pImpl->mRenderWorld = renderWorld;
    pImpl->mRED = red;
}

void StellarMeshScene::SetFFSRenderPipelineSetting(const FFSRenderPipelineSetting* renderPipelineSetting)
{
    pImpl->mFFSRenderPipelineSetting = renderPipelineSetting;
}

void StellarMeshScene::AddOrUpdate(ecs::EntityID entityID, StellarSceneData& data)
{
    pImpl->AddOrUpdate(entityID, data);
}

void StellarMeshScene::MarkNeedUpdataDataFor(ecs::EntityID entityID)
{
    std::lock_guard lock{pImpl->updateEntitySetMutex};
    pImpl->mUpdateEntities.insert(entityID);
}

void StellarMeshScene::Add(ecs::EntityID entityID, std::span<StellarSceneData> data, std::span<MaterialR*> mtls)
{
    pImpl->Add(entityID, data, mtls);
}

void StellarMeshScene::Remove(ecs::EntityID entityID)
{
    // mUploadBuffer.AddData(dstOffsetInBytes, sizeInBytes, InvalidStellarSceneData);
    pImpl->Remove(entityID);
}

void StellarMeshScene::Update()
{
    pImpl->Update(*this);
}

void StellarMeshScene::PostUpdate()
{
    pImpl->PostUpdate();
}

REDResidentBufferView* StellarMeshScene::GetStellarSceneBufferView()
{
    return pImpl->mStellarSceneBufferView.get();
}

UInt32 StellarMeshScene::GetStellarMeshSceneCount()
{
    return pImpl->mIndexAllocator.GetUsedSize() / sizeof(StellarMeshScene);
}

const StellarMeshRasterPipelines& StellarMeshScene::GetStellarMeshRasterPipelines()
{
    return pImpl->mStellarMeshRasterPipelines;
}

} // namespace cross
