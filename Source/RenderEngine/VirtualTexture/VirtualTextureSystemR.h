#pragma once
#include "CECommon/Common/GlobalSystemBase.h"
#include "RenderEngine/RenderEngineForward.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "NativeGraphicsInterface/NGI.h"
#include "VirtualTexturing.h"
#include "TexturePageLocks.h"
#include "VirtualTextureProducer.h"
#include "VirtualTexturePhysicalSpace.h"
#include "VirtualTextureFeedback.h"

namespace cross {

class ComputeShaderR;
class VTUniqueRequestList;
class VTUniquePageList;
class AllocatedVirtualTexture;
class VTPhysicalSpace;
class VirtualTextureProducer;
class VTSpace;
class VTFeedbackAnalysisTask;
struct VTSpaceDescription;
struct VTPhysicalSpaceDescription;
union PhysicalSpaceIDAndAddress;
struct VTGatherRequestsParameters;
struct VTPageUpdateBuffer;
struct VTFeedbackBufferDesc;




class VirtualTextureSystemR : public GlobalRenderSystemBase
{
    friend struct VTImageChunk;
    friend class VTPhysicalSpace;
    friend class VTPageMap;
public:
    struct VTFeedbackAnalysisParameters
    {
        VirtualTextureSystemR* system = nullptr;
        const UInt32* feedbackBuffer = nullptr;
        VTUniquePageList* uniquePageList = nullptr;
        UInt32 feedbackSize = 0u;
    };


    struct VTPageUpdateBuffer
    {
        static const UInt32 PageCapacity = 128u;
        VTPageUpdateBuffer()
        {
            memset(physicalAddresses, 0, PageCapacity * sizeof(UInt16));
        }
        UInt16 physicalAddresses[PageCapacity];
        UInt32 prevPhysicalAddress = ~0u;
        UInt32 numPages = 0u;
        UInt32 numPageUpdates = 0u;
    };

    struct VTGatherRequestsParameters
    {
        VirtualTextureSystemR* system = nullptr;
        const VTUniquePageList* uniquePageList = nullptr;
        std::vector<VTPageUpdateBuffer> pageUpdateBuffers;
        VTUniqueRequestList* requestList = nullptr;
        UInt32 pageUpdateFlushCount = 0u;
        UInt32 pageStartIndex = 0u;
        UInt32 numPages = 0u;
        UInt32 frameRequested;
    };

public:
    RENDER_ENGINE_API static const GlobalSystemDesc& GetDesc();
    RENDER_ENGINE_API static VirtualTextureSystemR* CreateInstance();

    virtual void Release() override;
    virtual void OnFirstUpdate(FrameParam* frameParam) override;
    virtual void OnBeginFrame(FrameParam* frameParam) override;
    virtual void OnEndFrame(FrameParam* frameParam) override;
    virtual void NotifyShutdownEngine() override;
    virtual const GlobalSystemDesc& GetSystemDesc() const override
    {
        return GetDesc();
    }
    RENDER_ENGINE_API void FlushVirtualTextureCache();
    RENDER_ENGINE_API void AllocateResources();
    void Update(RenderingExecutionDescriptor* RED, RenderWorld* world, ComputeShaderR* VTCS, UInt32 currentframe, VTFeedbackManager* feedbackManager);
    void FeedbackAnalysisTask(VTFeedbackAnalysisParameters& parameters);
    void GatherRequestsTask(VTGatherRequestsParameters& parameters);
    IAllocatedVirtualTexture* AllocateVirtualTexture(const FAllocatedVTDescription& desc);
    VTProducerHandle RegisterProducer(const VTProducerDescription& inDesc, IVirtualTexture* inProducer);
    void ReleaseProducer(const UInt32& handle);
    VTPhysicalSpace* AcquirePhysicalSpace(const VTPhysicalSpaceDescription& inDesc);
    VTSpace* AcquireSpace(VTSpaceDescription& inSpaceDesc, UInt8 forceSpaceID, AllocatedVirtualTexture* allocatedVT);
    VirtualTextureProducer* FindProducer(const VTProducerHandle& handle);
    
    VTSpace* GetSpace(UInt8 id) const
    {
        Assert(id < MaxSpaces);
        return mSpaces[id].get();
    }
    VTPhysicalSpace* GetPhysicalSpace(UInt16 id) const
    {
        Assert(mPhysicalSpaces[id]);
        return mPhysicalSpaces[id];
    }

    void ReleaseSpace(VTSpace* space);
    void ReleasePhysicalSpace(UInt16 id);
    void DestoryVirtualTexture(IAllocatedVirtualTexture* allocatedVT);

    void LockTile(const VTLocalTile& tile);
    void UnlockTile(const VTLocalTile& tile, const VirtualTextureProducer* producer);
    void RequestTiles(const UInt2& inScreenSpaceSize, SInt32 inMipLevel = -1);
    void End(NGIStagingBuffer* stagingBuffer, VTFeedbackBufferDesc desc, UInt32 frameIndex, VTFeedbackManager* feedbackManager);
    auto& GetAllocatedVTs()
    {
        return mAllocatedVTs;
    }
    auto& GetAllocatedVTsToMap() 
    {
        return mAllocatedVTsToMap;
    }

private:
    VirtualTextureSystemR();
    ~VirtualTextureSystemR();

    void DestroyPendingVirtualTextures(bool bForceDestoryAll); 
    void ReleasePendingPhysicalSpaces();
    void ReleaseResource();

    void GatherRequests(UInt32 frameRequested);
    void AddPageUpdate(std::vector<VTPageUpdateBuffer>& buffers, UInt32 flushCount, UInt16 physicalSpaceID, UInt16 pAddress);

    void SubmitRequestsFromLocalTileList(std::vector<VTLocalTile>& outDeferredTiles, const std::set<VTLocalTile>& localTileList, EVTProducePageFlags flags);
    void SubmitPreMappedRequests();
    void SubmitRequests(RenderingExecutionDescriptor* RED, RenderWorld* world, ComputeShaderR* VTCS, VTUniqueRequestList* requestList, bool bAsync);

    void RequestTilesInternal(const IAllocatedVirtualTexture* allocatedVT, SInt32 inMipLevel);
    void RequestTilesInternal(const IAllocatedVirtualTexture* allocatedVT, const UInt2& inScreenSpaceSize, SInt32 inMipLevel);

    UInt32 mFrame;
    bool bFlushCaches;
    static const UInt32 MaxNumTasks = 64;
    static const UInt8 MaxSpaces = 16;
    std::unique_ptr<VTSpace> mSpaces[MaxSpaces];

    VTFeedbackAnalysisParameters feedbackAnalysisParameters[MaxNumTasks];
    //VTFeedbackAnalysisTask* mFeedbackAnalysisTasks[MaxNumTasks] = {nullptr};


    VTGatherRequestsParameters gatherRequestsParameters[MaxNumTasks];

    VTUniquePageList* mMergedUniquePageList = nullptr;
    VTUniqueRequestList* mMergedRequestList = nullptr; 

    VTProducerCollection mProducers;
    std::vector<VTPhysicalSpace*> mPhysicalSpaces;
    std::vector<UInt32> mRequestedPackedTiles;

    std::vector<VTLocalTile> mTilesToLock;
    std::vector<VTLocalTile> mTransientCollectedPages;
    std::set<VTLocalTile> mContinuousUpdateTilesToProduce;
    std::set<VTLocalTile> mMappedTilesToProduce;
    std::vector<IAllocatedVirtualTexture*> mPendingDeleteAllocatedVTs;
    std::unordered_map<FAllocatedVTDescription, AllocatedVirtualTexture*, FAllocatedVTDescription::hash> mAllocatedVTs;
    std::vector<IAllocatedVirtualTexture*> mAllocatedVTsToMap;
    // this implementation seems problematic, lifetime is not considered
    FrameVector<NGIStagingBuffer*>* mPendingDeleteBuffers;
    VTPageLocks mTileLocks;
    //std::vector<FProducePageDataPrepareTask> mTopLevelTileTask;

    std::shared_mutex mPhysicalSpaceMutex;
};
}
