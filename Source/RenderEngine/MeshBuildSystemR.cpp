#include "EnginePrefix.h"
#include "ECS/Develop/Framework.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "RenderEngine/MeshBuildSystemR.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "VertexStreamLayoutPolicy.h"
#include "RenderEngine/ModelSystemR.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/SkinnedMeshBuilderR.h"
#include "RenderEngine/SkeletonComponentR.h"
#include "RenderEngine/SkeletonSystemR.h"
#include "RenderEngine/SkeletonGpuSkin.h"
#include "RenderEngine/ProceduralModelSystemR.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderEngine.h"

namespace cross
{

MeshBuildSystemR* MeshBuildSystemR::CreateInstance()
{
    return new MeshBuildSystemR();
}

void MeshBuildSystemR::Release()
{
    delete this;
}

void MeshBuildSystemR::OnBeginFrame(FrameParam* frameParam)
{
    FrameParam* fp = TYPE_CAST(FrameParam*, frameParam);
    for (UInt32 i = 0; i < (UInt32)mMeshBuilders.size(); i++)
    {
        mMeshBuilders[i].mBuilder->BeginFrame(fp);
    }
}

void MeshBuildSystemR::OnBuildUpdateTasks(FrameParam* frameParam)
{
    CreateTaskFunction<threading::ThreadID::RenderingThreadLocal>(FrameTickStage::Update, {}, [this, frameParam]
    {
        SCOPED_CPU_TIMING(GroupRendering, "MeshBuilderUpdateR");

        if (mNothingToBuild)
            return;
        mNothingToBuild = true;

        // STEP1. Refresh model component's pose buffer happened here if entity compute skin settled
        FrameParam* fp = TYPE_CAST(FrameParam*, frameParam);
        for (UInt32 i = 0; i < (UInt32)mMeshBuilders.size(); i++)
        {
            mMeshBuilders[i].mBuilder->Tick(fp, this);
            mNothingToBuild = mNothingToBuild && !mMeshBuilders[i].mBuilder->HasUnbuiltMesh();
        }

        // STEP2.
        auto skeletonSys = GetSkeletonSystem();
        skeletonSys->UploadPoseStroagingffer();

        // STEP3. Dispatch skinning cmds for each entity with skeleton component
        auto renderSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto red = renderSys->GetRenderingExecutionDescriptor();

        auto const& poseOffsetBufferMap = skeletonSys->GetPoseOffsetFromPoseBuffer();
        auto modelSys = mRenderWorld->GetRenderSystem<ModelSystemR>();

        auto skinningShader = skeletonSys->GetSkinningComputeShader();
        auto skinningPrePosShader = skeletonSys->GetSkinningComputeShader(true);

        UInt3 skinComputePassSize;
        skinningShader->GetThreadGroupSize(Skin_Senmatic_Main_Function.GetCString(), skinComputePassSize.x, skinComputePassSize.y, skinComputePassSize.z);
        Assert(skinComputePassSize.x > 0 && skinComputePassSize.y > 0 && skinComputePassSize.z > 0);

        mPrimarySkinningRedBuffers.clear();
        mPrimarySkinningRedBuffers.resize(poseOffsetBufferMap.size());

        UInt32 poseOffetBufferMapIndex = 0;
        for (auto itr = poseOffsetBufferMap.cbegin(); itr != poseOffsetBufferMap.cend(); ++itr, ++poseOffetBufferMapIndex)
        {
            SCOPED_CPU_TIMING(GroupRendering, "dispatchGpuSkinPass");
            auto poseParam = itr->first.SkinParam;
            auto poseOffsetInBytes = itr->second * sizeof(float);

            if (mRenderWorld->IsEntityAlive(poseParam->Entity) == false)
                continue;

            // Create a Transient ssbo buffer view holding pose array for later dispatch
            NGIBufferView* poseBufferView = PoseMatricesFrameBuffer::AssembleComputeSkinnedPoseBufferView(poseParam, poseOffsetInBytes, skeletonSys->GetPoseStorageBuffer());

            auto&& [skComp, modelComp] = mRenderWorld->GetComponent<SkeletonComponentR, ModelComponentR>(poseParam->Entity);

            // Get primary & static geometry list for grabbing next skinning vertex buffers
            auto primaryGeometryList = modelSys->GetModelPrivateRenderMesh(modelComp.Write(), static_cast<UInt32>(poseParam->ModelIndex));
            if (primaryGeometryList == nullptr || primaryGeometryList->GetState() == MeshR::State::Initializing)
                continue;

            auto staticGeometryList = modelSys->GetModelSharedRenderMesh(modelComp.Write(), static_cast<UInt32>(poseParam->ModelIndex));
            if (staticGeometryList == nullptr || staticGeometryList->GetState() == MeshR::State::Initializing)
                continue;

            // Get primary model asset for grabbing specify lod level's secondary model index
            auto primaryGeometryData = modelSys->GetModelAsset(modelComp.Write(), static_cast<UInt32>(poseParam->ModelIndex));
            UInt8 curLod = modelSys->GetModelLODIndex(modelComp.Read(), static_cast<UInt32>(poseParam->ModelIndex));

            // Warning: In the first skin pass for skinning all levels, AnimCompositeInstance::mRefTrack.Advance has not been called in AnimCompositeInstance::Update,
            // because the corresponding animation has not started.
            // Consequently, when the animation starts playing, the pose buffer will be different from that in the previous frames.

            UInt32 meshPartStartIndex = 0;
            UInt32 meshPartLodCount = 0;
            if (modelSys->GetModelSkinLevel(modelComp.Read(), 0) == SkinLevel::All)
            {
                meshPartStartIndex = 0;
                meshPartLodCount = primaryGeometryData->GetAssetData()->GetAllLodMeshPartCount();
                modelSys->SetModelSkinLevel(modelComp.Write(), 0, SkinLevel::CurLod);
            }
            else
            {
                primaryGeometryData->GetAssetData()->GetMeshLodInfo(curLod, meshPartStartIndex, meshPartLodCount);
            }

            // Each secondary geometry got an opportunity to skin by its visible & lod property
            ModelComponentR::IndividualModel tIndividualModel;
            tIndividualModel.mAsset = primaryGeometryData;
            tIndividualModel.mPrivateRenderMesh = primaryGeometryList;

            ModelComponentR::IndividualModel sIndividualModel;
            sIndividualModel.mAsset = primaryGeometryData;
            sIndividualModel.mSharedRenderMesh = staticGeometryList;

            // STEP3.1
            auto poseBufferHintName = fmt::format("{}_pose", primaryGeometryData->GetAssetData()->GetName());
            auto vertexOutBufferHintName = fmt::format("{}_vertexOut", primaryGeometryData->GetAssetData()->GetName());
            auto vertexInBufferHintName = fmt::format("{}_vertexIn", primaryGeometryData->GetAssetData()->GetName());

            if (primaryGeometryList->GetGeometryCount() && staticGeometryList->GetGeometryCount())
            {
                auto primaryGeomPacketPtr = primaryGeometryList->GetRenderGeometry(0).GetGeometryPacket();
                auto primaryVertexStream = primaryGeomPacketPtr->GetVertexStream(VertexStreamLayoutUtil::skinnedBufferSpaceIndex);

                auto staticGeomPacketPtr = staticGeometryList->GetRenderGeometry(0).GetGeometryPacket();
                auto staticVertexStream = staticGeomPacketPtr->GetVertexStream(VertexStreamLayoutUtil::skinnedBufferSpaceIndex);

                REDBuffer* redVertexInBuffer = red->FindExternalBuffer(staticVertexStream->GetGpuBuffer());
                if (redVertexInBuffer == nullptr)
                    redVertexInBuffer = red->AllocateBuffer(vertexInBufferHintName, staticVertexStream->GetGpuBuffer(), NGIResourceState::VertexBuffer);

                REDBuffer* redVertexOutBuffer = red->FindExternalBuffer(primaryVertexStream->GetGpuBuffer());
                if (redVertexOutBuffer == nullptr)
                    redVertexOutBuffer = red->AllocateBuffer(vertexOutBufferHintName, primaryVertexStream->GetGpuBuffer(), NGIResourceState::VertexBuffer);

                REDBuffer* redPoseBuffer = red->FindExternalBuffer(poseBufferView->GetBuffer());
                if (redPoseBuffer == nullptr)
                    redPoseBuffer = red->AllocateBuffer(poseBufferHintName, poseBufferView->GetBuffer(), NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);

                mPrimarySkinningRedBuffers[poseOffetBufferMapIndex].redVertexInBuffer = redVertexInBuffer;
                mPrimarySkinningRedBuffers[poseOffetBufferMapIndex].redVertexOutBuffer = redVertexOutBuffer;

                // STEP3.2
                //for (UInt32 iSubModel = meshPartStartIndex; iSubModel < meshPartStartIndex + meshPartLodCount; iSubModel++)
                {
                    // Create a Transient ssbo buffer view holding vertex for later dispatch
                    //SecondaryModelCompH secondModelH = {iSubModel};  
                    auto vertexChannelBufferViewsIn = PoseMatricesFrameBuffer::AssembleComputeSkinnedVertexBufferView(&tIndividualModel,
                        meshPartStartIndex, meshPartLodCount, true);
                    auto vertexChannelBufferViewsOut = PoseMatricesFrameBuffer::AssembleComputeSkinnedVertexBufferView(&sIndividualModel,
                        meshPartStartIndex, meshPartLodCount, true);

                    // Get specify mesh part's vertex count
                    UInt32 vertexCount = static_cast<UInt32>(vertexChannelBufferViewsIn[0]->GetDesc().SizeInBytes) / sizeof(Float3);

                    // Dispatch skinning task
                    auto* skinComputePass = red->AllocatePass("skinningPass");
                    bool isFirstSkinning = !modelSys->HasModelBeenSkinned(modelComp.Read(), static_cast<UInt32>(poseParam->ModelIndex));

                    skinComputePass->SetProperty(Skin_Semantic_Compute_VertexPosInBuffer.GetCString(), vertexChannelBufferViewsIn[0]);
                    skinComputePass->SetProperty(Skin_Semantic_Compute_VertexNormalInBufer.GetCString(), vertexChannelBufferViewsIn[1]);
                    skinComputePass->SetProperty(Skin_Semantic_Compute_VertexTangentInBuffer.GetCString(), vertexChannelBufferViewsIn[2]);
                    skinComputePass->SetProperty(Skin_Semantic_Compute_VertexSkeletonIDInBuffer.GetCString(), vertexChannelBufferViewsIn[3]);
                    skinComputePass->SetProperty(Skin_Semantic_Compute_VertexSkeletonWtInBuffer.GetCString(), vertexChannelBufferViewsIn[4]);
                    skinComputePass->SetProperty(Skin_Semantic_Compute_VertexPosOutBuffer.GetCString(), vertexChannelBufferViewsOut[0]);
                    skinComputePass->SetProperty(Skin_Semantic_Compute_VertexNormalOutBuffer.GetCString(), vertexChannelBufferViewsOut[1]);
                    skinComputePass->SetProperty(Skin_Semantic_Compute_VertexTangentOutBuffer.GetCString(), vertexChannelBufferViewsOut[2]);
                    skinComputePass->SetProperty(Skin_Semantic_Compute_VertexSkeletonIDOutBuffer.GetCString(), vertexChannelBufferViewsOut[3]);
                    skinComputePass->SetProperty(Skin_Semantic_Compute_VertexSkeletonWtOutBuffer.GetCString(), vertexChannelBufferViewsOut[4]);

                    skinComputePass->SetProperty(Skin_Senamtic_Graphic_PoseStorageBufferOffset.GetCString(), 0);
                    skinComputePass->SetProperty(Skin_Senamtic_Graphic_PoseStorageBufferRange.GetCString(), vertexCount);
                    skinComputePass->SetProperty(Skin_Senmatic_Compute_PoseStorageBuffer.GetCString(), poseBufferView);
                    skinComputePass->SetProperty(skin_senmatic_compute_IS_FIRST_SKINNING.GetCString(), isFirstSkinning);

                    auto currentSkinningShader = modelSys->IsRenderGeometryAdditiveVertexChannel(poseParam->Entity, static_cast<UInt32>(poseParam->ModelIndex), VertexSemantic::SemanticPositionT)
                        ? skinningPrePosShader : skinningShader;
                    skinComputePass->Dispatch(
                        currentSkinningShader, 
                        Skin_Senmatic_Main_Function.GetCString(), 
                        (UInt32)std::ceil(vertexCount * 1.0 / skinComputePassSize.x), 1, 1);

                    skinComputePass->AddBufferReference(redVertexOutBuffer, REDResourceState{NGIResourceState::ComputeShaderUnorderedAccess});
                    skinComputePass->AddBufferReference(redPoseBuffer, REDResourceState{NGIResourceState::ComputeShaderUnorderedAccess});

                    modelSys->SetModelSkinned(modelComp.Write(), static_cast<UInt32>(poseParam->ModelIndex));
                }

                // STEP3.3
                auto transCompleteComputePass = red->AllocatePass("SkinningVertexBufferTransition", true);
                transCompleteComputePass->AddBufferReference(redVertexOutBuffer, REDResourceState{NGIResourceState::VertexBuffer});
                transCompleteComputePass->AddBufferReference(redVertexInBuffer, REDResourceState{NGIResourceState::VertexBuffer});
            }
        }

        for (UInt32 i = 0; i < (UInt32)mMeshBuilders.size(); i++)
        {
            mMeshBuilders[i].mBuilder->PostTick(fp, this);
        }
    });
}

void MeshBuildSystemR::OnEndFrame(FrameParam* frameParam)
{
	FrameParam* fp = TYPE_CAST(FrameParam*, frameParam);

    for (UInt32 i = 0; i < (UInt32)mMeshBuilders.size(); i++)
    {
        mMeshBuilders[i].mBuilder->EndFrame(fp);
    }
}

void MeshBuildSystemR::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    RenderSystemBase::NotifyEvent(event, flag);
    
    if (event.mEventType == RemainedEventUpdatedEvent::sEventType)
    {
        const RemainedEventUpdatedEvent& e = static_cast<const RemainedEventUpdatedEvent&>(event);
        if (e.mData.mRemainedEventType == EntityDestroyEvent::sEventType)
        {
            for (UInt32 eventIndex = e.mData.mFirstIndex; eventIndex <= e.mData.mLastIndex; ++eventIndex)
            {
                const EntityDestroyEvent& entityLifeCycleEvent = mRenderWorld->GetRemainedEvent<EntityDestroyEvent>(eventIndex);
                if (ecs::HasComponentMask<SkeletonComponentR>(entityLifeCycleEvent.mData.mChangedComponentMask) 
                    && ecs::HasComponentMask<ModelComponentR>(entityLifeCycleEvent.mData.mChangedComponentMask))
                {
                    ModelSystemR* modelSys = mRenderWorld->GetRenderSystem<ModelSystemR>();
                    auto modelComp = mRenderWorld->GetComponent<ModelComponentR>(entityLifeCycleEvent.mData.mEntityID);
                    auto* meshBuilder = GetBuilderByBatchFlag(modelSys->GetBatchInfo(modelComp.Read()).GetBatchFlag());
                    auto* skinnedMeshBuilder = TYPE_CAST(SkinnedMeshBuilderR*, meshBuilder);

                    if (skinnedMeshBuilder)
                        mNothingToBuild = false;
                }
            }
        }
    }
    else if (event.mEventType == OnSystemAddToRenderWorldEvent::sEventType)
    {
        mRenderWorld->SubscribeRemainedEvent<EntityDestroyEvent>(this, true);
    }
}

void MeshBuildSystemR::BuildMesh(ecs::EntityID modelEntity, MeshBatchInfo batchInfo)
{
    if (batchInfo.GetMeshBuildPolicy() == MESH_BUILD_POLICY_CUSTOM)
        return;

    auto* meshBuilder = GetBuilderByBatchFlag(batchInfo.GetBatchFlag());
    if (meshBuilder)
    {
        mNothingToBuild = false;
        meshBuilder->AddMesh(modelEntity);
        return;
    }

#if CROSSENGINE_DEBUG
        LOG_WARN("Missing mesh builder for batch flag:{}", (UInt32)batchInfo.GetBatchFlag());
#endif
}


void MeshBuildSystemR::AddMeshBuilder(MeshBuilderBaseR* meshBuilder)
{
    if (meshBuilder == nullptr)
        return;

    auto flag = meshBuilder->GetBatchFlag();
    for (UInt32 i = 0; i < (UInt32)mMeshBuilders.size(); i++)
    {
        auto& builderFlag = mMeshBuilders[i].mBatchFlag;
        if (builderFlag == flag)
        {
            mMeshBuilders[i].mBuilder.reset(meshBuilder);
            return;
        }
    }

    mMeshBuilders.emplace_back(meshBuilder);
}

void MeshBuildSystemR::DestroyMeshBuilder(const char* name)
{
    if (name)
    {
        StringHash32 hash = HashFunction::HashString32(name);
        for (UInt32 i = 0; i < mMeshBuilders.size(); i++)
        {
            if (mMeshBuilders[i].mBuilder->GetName().GetHash32() == hash)
            {
                mMeshBuilders.erase(mMeshBuilders.begin() + i);
                return;
            }
        }
    }
}

MeshBuilderBaseR* MeshBuildSystemR::GetBuilderByBatchFlag(MeshBatchFlag flag)
{
    MeshBuilderBaseR* candidateBuilder = nullptr;
    for (UInt32 i = 0; i < (UInt32)mMeshBuilders.size(); i++)
    {
        if (mMeshBuilders[i].mBatchFlag == flag)
        {
            // Find the best fit builder
            return mMeshBuilders[i].mBuilder.get();
        }
        else if (mMeshBuilders[i].mBatchFlag & flag)
        {
            // find a candidate builder
            candidateBuilder = mMeshBuilders[i].mBuilder.get();
        }
    }
    return candidateBuilder;
}

ModelSystemR* MeshBuildSystemR::GetModelSystem()
{
    return (ModelSystemR*)mRenderWorld->GetRenderSystem<ModelSystemR>();
}

ProceduralModelSystemR* MeshBuildSystemR::GetProceduralModelSystem()
{
    return dynamic_cast<ProceduralModelSystemR*>(mRenderWorld->GetRenderSystem<ProceduralModelSystemR>());
}

SkeletonSystemR* MeshBuildSystemR::GetSkeletonSystem()
{
    return (SkeletonSystemR*)mRenderWorld->GetRenderSystem<SkeletonSystemR>();
}

MeshBuildSystemR::BuilderItem::BuilderItem(MeshBuilderBaseR* builder)
    : mBatchFlag(builder->GetBatchFlag())
    , mBuilder(builder)
{}
}   // namespace cross
