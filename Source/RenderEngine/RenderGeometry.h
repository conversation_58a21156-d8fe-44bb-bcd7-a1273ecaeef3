#pragma once
#include "RenderEngine/RenderFactoryNGIResPtr.h"
#include "CECommon/Resource/IRuntimeObject.h"
#include "CECommon/Common/MeshDefines.h"
#include "CECommon/Common/RenderStateDefines.h"
#include "Resource/MeshAssetData.h"
#include "Resource/Texture/TextureUDIM.h"

namespace cross
{
class GInputLayout;

class BufferStream final
{
public:
    RENDER_ENGINE_API void Clear();

    void SetGpuBuffer(NGIBuffer* buffer, UInt32 size, UInt32 offset = 0);

    inline NGIBuffer* GetGpuBuffer() const { return mGpuBuffer; }
    
    inline uint32_t GetStreamOffset()const { return mDataOffset; }

    inline uint32_t GetStreamSize()const { return mStreamSize; }

private: 
    uint32_t mStreamSize{ 0 };
    uint32_t mDataOffset{ 0 }; 
    uint32_t mElementNum{ 0 };

    NGIBuffer* mGpuBuffer{ nullptr };
    NGIBufferPtr mGpuBufferPtr;
};

class VertexBufferGroup
{
public:
    VertexBufferGroup() = default;

    RENDER_ENGINE_API void AddVertexStream(NGIBuffer* vertexBuffer, UInt32 vertexCount, UInt32 dataOffset, const VertexStreamLayout& layoutDesc);

    const BufferStream* GetVertexStream(uint8_t index) const
    {
        if (index < mStreamCount)
            return &mVertexStreams[index];
        return nullptr;
    }

    const auto& GetInputLayout()const { return mInputLayoutDesc; }

    uint8_t GetStreamCount()const { return mStreamCount; }

    void Clear();

    void Clear(UInt32 index);

private:
    VertexBufferGroup(VertexBufferGroup&) = delete;
    VertexBufferGroup& operator=(VertexBufferGroup&) = delete;

    BufferStream mVertexStreams[MaxVertexStreams];
    InputLayoutDesc mInputLayoutDesc;
    UInt8 mStreamCount{ 0 };
};

class GeometryPacket
{
public:
    void AddVertexStream(NGIBuffer* vertexBuffer, UInt32 vbSize, UInt32 dataOffset, const VertexStreamLayout& layoutDesc)
    {
        mVertexBufferGroup.AddVertexStream(vertexBuffer, vbSize, dataOffset, layoutDesc);
    }

    inline UInt8 GetStreamCount()const { return mVertexBufferGroup.GetStreamCount(); }

    inline const BufferStream* GetVertexStream(UInt8 index) const { return mVertexBufferGroup.GetVertexStream(index); }

    RENDER_ENGINE_API void SetIndexStream(NGIBuffer* indexBuffer, UInt32 size, UInt32 indexCount, UInt32 offset = 0);

    inline bool HasIndexStream()const { return mIndexStream.GetGpuBuffer () != nullptr; }

    inline const BufferStream* GetIndexStream() const { return &mIndexStream; }

    inline auto& GetInputLayout()const { return mVertexBufferGroup.GetInputLayout(); }

    RENDER_ENGINE_API void Clear();

    RENDER_ENGINE_API void ClearVertexStream();

    RENDER_ENGINE_API void ClearVertexStream(UInt32 index);
    
    bool Is16BitIndex() const { return mIndexFormat == GraphicsFormat::R16_UInt; }

    GraphicsFormat GetIndexFormat()const { return mIndexFormat; }
    
    void SetPrePositions(const std::vector<Float3>&& prepos)
    {
        mPrePosition = prepos;
    }

    std::vector<Float3>& GetPrePositions()
    {
        return mPrePosition;
    }

    GeometryPacket();
    ~GeometryPacket();
private:
    GeometryPacket(GeometryPacket&) = delete;
    GeometryPacket& operator=(GeometryPacket&) = delete;

    void IncreaseRefCount() { mRefCount++; }

    void DecreaseRefCount() { mRefCount--; }

    int32_t GetRefCount() { return mRefCount; }

    VertexBufferGroup mVertexBufferGroup;
    BufferStream mIndexStream;
    uint32_t mRefCount{ 0 };
    GraphicsFormat mIndexFormat{ GraphicsFormat::Unknown };

    // cpu skin pre positions
    std::vector<Float3> mPrePosition{};

    friend class RenderFactory;
    friend class NSharedPtr<GeometryPacket>;
};

template <>
inline void NSharedPtr<GeometryPacket>::NotifyZeroReference()
{
    RenderFactoryHelper::ReleaseGeometryPacket(mRawPtr);
    //Assert(0);
}

using GeometryPacketPtr = NSharedPtr<GeometryPacket>;

class InstancingBufferGroup
{
private:
    VertexBufferGroup mInstancingBufferGroup;
    //uint32_t mInstanceCount{ 0 };
};

#define CE_BINDLESS_INVALID_INDEX 0xffffffff

struct GeometryData
{
    UInt32 PosBufferIndex{CE_BINDLESS_INVALID_INDEX};
    UInt32 ColorBufferIndex{CE_BINDLESS_INVALID_INDEX};
    UInt32 NormalBufferIndex{CE_BINDLESS_INVALID_INDEX};
    UInt32 TangentBufferIndex{CE_BINDLESS_INVALID_INDEX};
    UInt32 BinormalBufferIndex{CE_BINDLESS_INVALID_INDEX};
    UInt32 UVBufferIndex{CE_BINDLESS_INVALID_INDEX};
    UInt32 UV1BufferIndex{CE_BINDLESS_INVALID_INDEX};
    UInt32 IndexBufferIndex{CE_BINDLESS_INVALID_INDEX};
};

class RenderGeometry final
{
public:
    RenderGeometry() = default;

    RenderGeometry(GeometryPacket* geoPak, UInt32 vertexCount, UInt32 vertexStart, UInt32 indexCount, UInt32 indexStart, UInt32 primCount, PrimitiveTopology primType, UInt16 geopakSubIdx = 0)
        : mGeoPacket(geoPak)
        , mVertexCount(vertexCount)
        , mVertexStart(vertexStart)
        , mIndexCount(indexCount)
        , mIndexStart(indexStart)
        , mPrimitiveCount(primCount)
        , mPrimitiveType(primType)
        , mGeopakSubIndex(geopakSubIdx)
    {}

    inline void SetData(GeometryPacket* geoPak, UInt32 vertexCount, UInt32 vertexStart, UInt32 indexCount, UInt32 indexStart,
        UInt32 primCount, PrimitiveTopology primType, UInt16 geopakSubIdx = 0)
    {
        mGeoPacket.reset(geoPak);
        mVertexCount = vertexCount;
        mVertexStart = vertexStart;
        mIndexCount = indexCount;
        mIndexStart = indexStart;
        mPrimitiveCount = primCount;
        mPrimitiveType = primType;
        mGeopakSubIndex = geopakSubIdx;
    }

    inline GeometryPacket* GetGeometryPacket()const { return mGeoPacket.get(); }

    inline UInt16 GetGeopakSubIndex()const { return mGeopakSubIndex; }

    inline void SetGeometryPacket(GeometryPacketPtr geoPacket) { mGeoPacket.reset(geoPacket.get()); }

    inline UInt32 GetVertexStart()const { return mVertexStart; }

    inline UInt32 GetVertexCount()const { return mVertexCount; }

    inline void SetVertexInfo(UInt32 vertexCount, UInt32 vertexStart = 0)
    {
        mVertexCount = vertexCount;
        mVertexStart = vertexStart;
    }

    inline UInt32 GetIndexStart()const { return mIndexStart; }

    inline UInt32 GetIndexCount()const { return mIndexCount; }

    inline void SetIndexInfo(UInt32 indexCount, UInt32 indexStart = 0) 
    { 
        mIndexCount = indexCount;
        mIndexStart = indexStart; 
    }

    inline UInt32 GetPrimitiveCount() const { return mPrimitiveCount; }

    inline PrimitiveTopology GetPrimitiveType() const { return mPrimitiveType; }

    inline void SetPrimitiveInfo(UInt32 primCount, PrimitiveTopology primType)
    {
        mPrimitiveCount = primCount;
        mPrimitiveType = primType;
    }

    inline void Reset()
    {
        mGeoPacket.reset();
        mVertexCount = 0;
        mVertexStart = 0;
        mIndexStart = 0;
        mIndexCount = 0;
        mPrimitiveCount = 0;
        mPrimitiveType = PrimitiveTopology::Invalid;
        mGeopakSubIndex = 0;
    }

    UInt32 mMeshInfOffset{0};

    CrossUUID mAssetGUID;

    UInt32 GetGeometryIndex()const
    {
        return mGeometryIndex;
    }

    void SetGeometryIndex(UInt32 geoIndex)
    {
        mGeometryIndex = geoIndex;
    }

    const GeometryData& GetGeometryData() const
    {
        return mGeoData;
    }

    void SetGeometryData(const GeometryData& geoData)
    {
        mGeoData = geoData;
    }

private:

    GeometryPacketPtr mGeoPacket;
    UInt32 mVertexCount{ 0 };
    UInt32 mVertexStart{ 0 };
    UInt32 mIndexCount{ 0 };
    UInt32 mIndexStart{ 0 };
    UInt32 mPrimitiveCount{ 0 };
    //float mDepthBias{ 0 };
    //float mNormalBias{ 0 };
    PrimitiveTopology mPrimitiveType{ 0 };
    UInt16 mGeopakSubIndex{ 0 };

    // Global geometry index
    UInt32 mGeometryIndex = 0xffffffff;
    GeometryData mGeoData;  // stores bindless buffer indices

    friend class RenderFactory;
};

using ActiveStreamMask = std::bitset<16>;

//class RenderGeometryList : public IRuntimeObject
//{
//public:
//    enum State
//    {
//        Uninitialized,
//        Initializing,
//        Initialized,
//        bNeedRecreate
//    };
//
//public:
//    virtual void Release();
//
//    bool IsEmpty()const { return mGeometries.empty(); }
//
//    const std::vector<RenderGeometry>& GetGeometries()const { return mGeometries; }
//
//    UInt32 GetGeometryCount()const { return (UInt32)mGeometries.size(); }
//
//    const RenderGeometry& GetRenderGeometry(UInt32 index) const { return mGeometries[index]; }
//
//    RenderGeometry& GetRenderGeometry(UInt32 index) { return mGeometries[index]; }
//
//    const std::vector<StringHash32>& GetNameHashes()const { return mGeometryNameHashes; }
//
//    void SetNameHash(UInt32 index, StringHash32 hash) { mGeometryNameHashes[index] = hash; }
//
//    bool TryUpdate(UInt32 curFrameId){ return mVersion.exchange(curFrameId) != curFrameId; }
//
//    void ClearAndResize(UInt32 geometryCount)
//    {
//        mGeometries.clear();
//        mGeometries.resize(geometryCount);
//        mGeometryNameHashes.clear();
//        mGeometryNameHashes.resize(geometryCount);
//    }
//    auto& GetBuildMutex()
//    {
//        return mBuildMutex;
//    }
//    int32_t GetRefCount() const { return mRefCount; }
//    void SetState(State s) { mState.store(s, std::memory_order_relaxed); }
//    State GetState() { return mState.load(std::memory_order_relaxed); }
//
//private:
//    void IncreaseRefCount() { mRefCount++; }
//
//    void DecreaseRefCount() { mRefCount--; }
//
//    std::vector<RenderGeometry> mGeometries;
//    std::vector<StringHash32> mGeometryNameHashes;
//    std::atomic<UInt32> mVersion{ 0xffffffff };
//    std::atomic<int32_t> mRefCount{ 0 };
//    std::atomic<State> mState = Uninitialized;
//    mutable std::mutex mBuildMutex;
//    friend class NSharedPtr<RenderGeometryList>;
//};
//
//using RenderGeometryListPtr = NSharedPtr<RenderGeometryList>;

struct MergedGeometryPacket
{
    GeometryPacketPtr geoPack;
    UInt32 maxVertexCount = 0;
    UInt32 maxIndexCount = 0;
    UInt32 vertexCount = 0;
    UInt32 indexCount = 0;
};

}
