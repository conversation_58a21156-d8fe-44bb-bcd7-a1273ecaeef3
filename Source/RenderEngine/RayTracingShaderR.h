#pragma once
#include "NativeGraphicsInterface/NGI.h"
#include <unordered_map>
#include "RenderEngine/RenderEngineForward.h"
#include "Resource/IRayTracingShaderR.h"
#include "Resource/Shader.h"


namespace cross {

struct PropertySet;

class RayTracingShaderR : public IRayTracingShaderR
{
public:
    RENDER_ENGINE_API RayTracingShaderR(resource::RayTracingShader* gameObject) : mGameObject{ gameObject } {}

    RENDER_ENGINE_API ~RayTracingShaderR() override;

    void Reset(resource::RayTracingShader*) override { Assert(false); }

private:
    std::shared_mutex mPipelineStatesMutex;
    std::unordered_map<const NGIRayTracingPipelineStateDesc*, std::unique_ptr<NGIRayTracingPipelineState>, NGIObjectDescHasher, NGIObjectDescHasher> mPipelineStates;
    resource::RayTracingShader* mGameObject = nullptr;
};

}
