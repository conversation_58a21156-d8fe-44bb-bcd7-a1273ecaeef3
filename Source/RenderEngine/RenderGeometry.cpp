

#include "EnginePrefix.h"
#include "RenderEngine/RenderGeometry.h"
//#include "Runtime/RenderSystem/GPUContext/GPUState/GGraphicState.h"
#include "RenderEngine/RenderFactory.h"

namespace cross
{
//////////////////////////////////////////////////////////////////////////
//VertexStream
void BufferStream::Clear()
{
    mDataOffset = 0;
    mStreamSize = 0;
    mGpuBuffer = nullptr;
    mGpuBufferPtr.reset();
}

void BufferStream::SetGpuBuffer(NGIBuffer* gpuBuffer, UInt32 size, UInt32 offset)
{
    mGpuBuffer = gpuBuffer;
    if (mGpuBuffer && mGpuBuffer->IsEnableReferenceCount())
    {
        mGpuBufferPtr.reset(gpuBuffer);
    }
    else
    {
        mGpuBufferPtr.reset();
    }
    mStreamSize = size;
    mDataOffset = offset;
}

//////////////////////////////////////////////////////////////////////////
//VertexBufferGroup
void VertexBufferGroup::AddVertexStream(NGIBuffer* vertexBuffer, UInt32 streamSize, UInt32 dataOffset, const VertexStreamLayout& layoutDesc)
{
    assert(mStreamCount <= MaxVertexStreams);
    mVertexStreams[mStreamCount].SetGpuBuffer(vertexBuffer, streamSize, dataOffset);
    mStreamCount++;

    mInputLayoutDesc.AddVertexStreamLayout(layoutDesc);
}

void VertexBufferGroup::Clear()
{
    for (uint8_t i = 0; i < MaxVertexStreams; i++)
    {
        mVertexStreams[i].Clear();
    }

    mStreamCount = 0;
    mInputLayoutDesc.Clear();
}

void VertexBufferGroup::Clear(UInt32 index)
{
    mVertexStreams[index].Clear();
    mStreamCount--;
    mInputLayoutDesc.Clear(index);
}

//////////////////////////////////////////////////////////////////////////
//GeometryPacket
GeometryPacket::GeometryPacket()
{
}

GeometryPacket::~GeometryPacket()
{
}

void GeometryPacket::SetIndexStream(NGIBuffer* indexBuffer, UInt32 size, UInt32 indexCount, UInt32 offset)
{
    mIndexStream.SetGpuBuffer(indexBuffer, size, offset);
    UInt32 sizeOfIndex = size / indexCount;
    if (sizeOfIndex == 2)
        mIndexFormat = GraphicsFormat::R16_UInt;
    else if (sizeOfIndex == 4)
        mIndexFormat = GraphicsFormat::R32_UInt;
    else
        assert(false);
}

void GeometryPacket::Clear()
{
    mVertexBufferGroup.Clear();
    mIndexStream.Clear();
    mIndexFormat = GraphicsFormat::Unknown;
}

void GeometryPacket::ClearVertexStream()
{
    mVertexBufferGroup.Clear();
}

void GeometryPacket::ClearVertexStream(UInt32 index)
{
    Assert(index < mVertexBufferGroup.GetStreamCount());
    mVertexBufferGroup.Clear(index);
}

//void RenderGeometryList::Release() 
//{
//    delete this;
//}

}   // namespace cross