#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameParam.h"
#include "RenderEngine/RenderFactory.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderGeometry.h"
#include "RenderEngine/VertexStreamLayoutPolicy.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "RenderEngine/RendererSystemR.h"

namespace cross
{
//////////////////////////////////////////////////////////////////////////
//RenderFactory
RenderFactory::RenderFactory()
{
    SetVertexStreamLayoutPolicy(STREAM_LAYOUT_POLICY_STATIC, new VertexStreamLayoutPolicyStaticAllInOne());
    
    SetVertexStreamLayoutPolicy(STREAM_LAYOUT_POLICY_GPU_GRAPHIC_SKIN, new VertexStreamLayoutPolicyStaticAllInOne());
    SetVertexStreamLayoutPolicy(STREAM_LAYOUT_POLICY_GPU_COMPUTE_SKIN, new VertexStreamLayoutPolicySkinningAndStatic());

    SetVertexStreamLayoutPolicy(STREAM_LAYOUT_POLICY_STATIC_BLEND_SHAPE, new VertexStreamLayoutPolicyStaticBlendShape());
    SetVertexStreamLayoutPolicy(STREAM_LAYOUT_POLICY_CPU_SKIN, new VertexStreamLayoutPolicyCPUSkin());
    SetVertexStreamLayoutPolicy(STREAM_LAYOUT_POLICY_CPUSKIN_BLEND_SHAPE, new VertexStreamLayoutPolicyCPUSkin());

    SetVertexStreamLayoutPolicy(STREAM_LAYOUT_POLICY_STATIC_LOD_STREAMING, new VertexStreamLayoutPolicyStaticLODStreaming());
}

RenderFactory::~RenderFactory()
{
}

void RenderFactory::BeginRenderFrame(FrameParam* fp)
{
    if (GetNGIDevice().IsMemoryBudgetEnabled())
    {
        GetNGIDevice().SetFrameIndex(fp->GetFrameCount());
    }
}

void RenderFactory::EndRenderFrame(FrameParam* fp)
{
}

void RenderFactory::ReleaseNGIResource(NGIObject* rhiObj)
{
	if (rhiObj)
	{
		auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
		rendererSystem->DestroyNGIObject(std::unique_ptr<NGIObject>{ rhiObj });
	}
}

GeometryPacketPtr RenderFactory::CreateGeometryPacket()
{
	return GeometryPacketPtr(new GeometryPacket());
}

void RenderFactory::ReleaseGeometryPacket(GeometryPacket* geopack)
{
	delete geopack;
}

NGIBufferPtr RenderFactory::CreateVertexBuffer(uint32_t sizeInByte)
{
	NGIBufferDesc desc
	{
		sizeInByte,
		NGIBufferUsage::VertexBuffer | NGIBufferUsage::CopyDst | NGIBufferUsage::TexelBuffer,
	};
    desc.Priority = 0.1f;
    return NGIBufferPtr{GetNGIDevice().CreateBuffer(desc, "RenderFactory VertexBuffer")};
}

NGIBufferPtr RenderFactory::CreateVertexBuffer(uint32_t sizeInByte, NGIBufferUsage externalUsages)
{
    NGIBufferDesc desc{
        sizeInByte,
        NGIBufferUsage::VertexBuffer | NGIBufferUsage::CopyDst | NGIBufferUsage::TexelBuffer | externalUsages,
    };
    desc.Priority = 0.1f;
    return NGIBufferPtr{GetNGIDevice().CreateBuffer(desc, "RenderFactory VertexBuffer")};
}

NGIBufferPtr RenderFactory::CreateIndexBuffer(uint32_t sizeInByte, IndexBufferFormat indexFmt)
{
	NGIBufferDesc desc
	{
		sizeInByte,
		NGIBufferUsage::IndexBuffer | NGIBufferUsage::CopyDst | NGIBufferUsage::TexelBuffer,
    };
    desc.Priority = 0.1f;
    return NGIBufferPtr{GetNGIDevice().CreateBuffer(desc, "RenderFactory IndexBuffer")};
}

}
