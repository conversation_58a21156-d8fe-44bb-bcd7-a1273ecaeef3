#include "EnginePrefix.h"
#include "ParticleSimulationSystemR.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderNodeSystemR.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderFactory.h"
#include "RenderEngine/RenderPipelineSystemR.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/RenderMesh.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "CrossFX/ParticleSystem/Modules/ParticleBaseRenderer.h"

namespace cross
{
    
DECLARE_CPU_TIMING_GROUP(GroupParticleSystemR);

ecs::ComponentDesc* ParticleSystemComponentR::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetRenderComponentDesc<ParticleSystemComponentR>(false);
}

ParticleSimulationSystemR::ParticleSimulationSystemR()
{
    auto rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    mRED = rendererSystem->GetRenderingExecutionDescriptor();
}

ParticleSimulationSystemR* ParticleSimulationSystemR::CreateInstance()
{
    return new ParticleSimulationSystemR();
}

void ParticleSimulationSystemR::Release()
{
    delete this;
}

void ParticleSimulationSystemR::OnBeginFrame(FrameParam* frameParam)
{
    mChangeList.BeginFrame(frameParam, FRAME_STAGE_RENDER);
    mCurrentFrame = frameParam->GetFrameCount();

    while (!mGpuSimulationResults.empty())
    {
        const auto& result = mGpuSimulationResults.front();
        if (mCurrentFrame - result.frameIndex >= CmdSettings::Inst().gMaxQueuedFrame)
        {
            auto* src = result.buffer->MapRange(NGIBufferUsage::CopyDst, 0, result.range);
            memcpy(result.dst, src, result.range);
            result.buffer->UnmapRange(0, result.range);
            mGpuSimulationResults.pop();
        }
        else
        {
            break;
        }
    }
}

void ParticleSimulationSystemR::OnEndFrame(FrameParam* frameParam)
{
}

void ParticleSimulationSystemR::OnFirstUpdate(FrameParam* frameParam)
{
}

void ParticleSimulationSystemR::OnBuildUpdateTasks(FrameParam* frameParam)
{
    CreateTaskFunction<threading::ThreadID::RenderingThreadLocal>(
        FrameTickStage::Update,
        {},
        [this, frameParam]
        {
            SCOPED_CPU_TIMING(GroupRendering, "ParticleSimulationUpdateR");

            RenderPropertySystemR* renderPropertySys = mRenderWorld->GetRenderSystem<RenderPropertySystemR>();
            auto* gpuScene = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>()->GetWorldRenderPipeline()->GetGPUScene();

            UInt32 eventCount = mRenderWorld->GetRemainedEventCount<EntityCreateEvent>(true);
            for (UInt32 i = 0; i < eventCount; ++i)
            {
                auto& lifeEvent = mRenderWorld->GetRemainedEvent<EntityCreateEvent>(i);
                if (ecs::HasComponentMask<ParticleSystemComponentR>(lifeEvent.mData.mChangedComponentMask) &&
                    EnumHasAnyFlags(lifeEvent.mData.mEventFlag, EntityLifeCycleEventFlag::CreateComponent | EntityLifeCycleEventFlag::MoveComponent))
                {
                    auto& entity = lifeEvent.mData.mEntityID;
                    auto [particleSystemComp, renderNodeComp] = mRenderWorld->GetComponent<ParticleSystemComponentR, RenderNodeComponentR>(entity);
                    renderPropertySys->SetCullingProperty(entity, CullingProperty::CULLING_PROPERTY_ALWAYS_VISIBLE);

                    std::shared_ptr<ParticleSystemRenderNode> renderNode = particleSystemComp.Write()->GetRenderNodePtr();
                    renderNodeComp.Write()->SetRenderNode(renderNode);

                    if (renderNode->CheckGpuSceneCapacity())
                    {
                        renderNode->AllocateGpuScene(*gpuScene);
                        gpuScene->SetGPUSceneDirty(entity);
                    }
                }
            }

            const UInt32 changeCount = mChangeList.GetCount();
            for (auto index = 0u; index < changeCount; ++index)
            {
                ecs::EntityID entity = (mChangeList.GetData(index));
                if (!mRenderWorld->IsEntityAlive(entity))
                {
                    continue;
                }
                auto [particleSystemComp, renderNodeComp] = mRenderWorld->GetComponent<ParticleSystemComponentR, RenderNodeComponentR>(entity);
                std::shared_ptr<ParticleSystemRenderNode> renderNode = particleSystemComp.Read()->GetRenderNodePtr();
                renderNodeComp.Write()->SetRenderNode(renderNode);

                if (renderNode->CheckGpuSceneCapacity())
                {
                    //renderNode->FreeGpuScene(*gpuScene);
                    renderNode->AllocateGpuScene(*gpuScene);
                    gpuScene->SetGPUSceneDirty(entity);
                }
            }
        });
}

void ParticleSimulationSystemR::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    RenderSystemBase::NotifyEvent(event, flag);
    if (event.mEventType == OnSystemAddToRenderWorldEvent::sEventType)
    {
        mRenderWorld->SubscribeRemainedEvent<EntityDestroyEvent>(this, true);
    }
    else if (event.mEventType == RemainedEventUpdatedEvent::sEventType)
    {
        const RemainedEventUpdatedEvent& e = static_cast<const RemainedEventUpdatedEvent&>(event);
        if (e.mData.mRemainedEventType == EntityDestroyEvent::sEventType)
        {
            auto* gpuScene = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>()->GetWorldRenderPipeline()->GetGPUScene();
            for (UInt32 i = e.mData.mFirstIndex; i <= e.mData.mLastIndex; i++)
            {
                auto& lifeEvent = mRenderWorld->GetRemainedEvent<EntityDestroyEvent>(i);
                auto entity = lifeEvent.mData.mEntityID;

                if (ecs::HasComponentMask<ParticleSystemComponentR>(lifeEvent.mData.mChangedComponentMask) && EnumHasAnyFlags(lifeEvent.mData.mEventFlag, EntityLifeCycleEventFlag::DestroyComponent))
                {
                    auto particleSystemComp = mRenderWorld->GetComponent<ParticleSystemComponentR>(entity);
                    std::shared_ptr<ParticleSystemRenderNode> renderNode = particleSystemComp.Read()->GetRenderNodePtr();
                    renderNode->FreeGpuScene(*gpuScene);
                }
            }
        }
    }
}

ParticleSystemRenderNode& ParticleSimulationSystemR::GetRenderNode(ecs::EntityID entity)
{
    auto writer = mRenderWorld->GetComponent<ParticleSystemComponentR>(entity).Write();
    return writer->GetRenderNode();
}

void ParticleSimulationSystemR::PrepareEmitterVertexView(const fx::ParticleVertexView& view, ParticleRenderUnit& output)
{
    output.mGeometryList.clear();
    if (MeshAssetDataResourcePtr meshAsset = view.meshAsset)
    {
        const MeshAssetData* meshAssetData = meshAsset->GetAssetData();
        MeshR* renderMesh = static_cast<MeshR*>(meshAssetData->GetRenderMesh());

        if (renderMesh->GetState() == MeshR::State::Uninitialized || renderMesh->GetState() == MeshR::State::NeedReinitialized)
            renderMesh->BuildStaticMesh();

        const UInt32 meshPartNum = meshAssetData->GetMeshPartMaxCount();
        //const UInt8 meshLodNum = std::min(resource::MAX_MESH_LOD_NUM, meshAssetData->GetLodCount());
        for (auto meshPartIndex = 0u; meshPartIndex < meshPartNum; ++meshPartIndex)
        {
            const UInt32 lodIndex = 0u;
            UInt32 meshPartStartIndex, currentLodMeshPartCnt;
            meshAssetData->GetMeshLodInfo(lodIndex, meshPartStartIndex, currentLodMeshPartCnt);
            Assert(currentLodMeshPartCnt <= meshPartNum);

            output.mGeometryList.emplace_back(renderMesh->GetRenderGeometry(meshPartStartIndex + meshPartIndex));
        }
    }
    else
    {
        if (view.vertexBufferSize < 1 && view.indexBufferSize < 1)
        {
            return;
        }
        GeometryPacketPtr geometryPack = RenderFactory::Instance().CreateGeometryPacket();
        auto& ngiDevice = GetNGIDevice();
        // TODO(jihuixu) : Maybe sprite or ribbon, beam particle's vertex data with procedural generation
        RendererSystemR* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        const UInt32 vertexSize = view.vertexBufferSize;
        auto vertexBufferNGIWrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, vertexSize);
        vertexBufferNGIWrap.MemWrite(0, view.vertexData.data(), vertexSize);
       
        NGIBufferDesc vertexBufferDesc{ vertexSize, NGIBufferUsage::VertexBuffer | NGIBufferUsage::CopyDst };
        auto vertexBuffer = ngiDevice.CreateBuffer(vertexBufferDesc, "ParticleSpriteVertexBuffer");
        rendererSystem->InitializeBuffer(vertexBuffer, vertexBufferNGIWrap.GetNGIBuffer(), NGICopyBuffer{vertexBufferNGIWrap.GetNGIOffset(), 0u, vertexSize}, NGIResourceState::VertexBuffer);
        geometryPack->AddVertexStream(vertexBuffer, vertexSize, 0u, view.layout);

        const UInt32 indexSize = view.indexBufferSize;
        auto indexBufferNGIWrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, indexSize);
        indexBufferNGIWrap.MemWrite(0, view.indexData.data(), indexSize);

        NGIBufferDesc indexBufferDesc{indexSize, NGIBufferUsage::IndexBuffer | NGIBufferUsage::CopyDst};
        auto indexBuffer = ngiDevice.CreateBuffer(indexBufferDesc, "ParticleSpriteIndexBuffer");
        rendererSystem->InitializeBuffer(indexBuffer, indexBufferNGIWrap.GetNGIBuffer(), NGICopyBuffer{indexBufferNGIWrap.GetNGIOffset(), 0u, indexSize}, NGIResourceState::IndexBuffer);
        geometryPack->SetIndexStream(indexBuffer, indexSize, view.indexNum); 

        const UInt32 vertexNum = view.vertexNum;
        const UInt32 indexNum = view.indexNum;
        const UInt32 primitiveNum = view.primitiveNum;
        output.mGeometryList.emplace_back(geometryPack.get(), vertexNum, 0, indexNum, 0, primitiveNum, PrimitiveTopology::TriangleList);
    }
}

void ParticleSimulationSystemR::PrepareEmitterMaterial(const fx::ParticleMaterialView& view, ParticleRenderUnit& output)
{
    output.mMaterials = view.mMaterials;
    output.SetParticleVariables(view.mVariables);
}

void ParticleSimulationSystemR::UpdateRenderNode(const fx::ParticleRenderData& renderData)
{
    SCOPED_CPU_TIMING(GroupParticleSystemR, "UpdateRenderNode");
    auto particleSystemComp = mRenderWorld->GetComponent<ParticleSystemComponentR>(renderData.Entity);
    std::shared_ptr<ParticleSystemRenderNode> renderNode = particleSystemComp.Write()->GetRenderNodePtr();
    bool renderDirty = renderData.DataSets.size() > 0 || renderData.ComputeContexts.size() > 0;
    particleSystemComp.Write()->SetComputeContexts(std::move(renderData.ComputeContexts));
    const auto& computeContexts = particleSystemComp.Read()->GetComputeContexts();

    for (auto emitterIndex = 0u; emitterIndex < renderData.EmitterNum; ++emitterIndex)
    {
        if (emitterIndex >= renderNode->GetRenderUnits().size())
            continue;
        
        auto pipeline = dynamic_cast<FFSWorldRenderPipeline*>(mRenderWorld->GetRenderSystem<RenderPipelineSystemR>()->GetWorldRenderPipeline());
        auto sim = renderData.Simulation[emitterIndex] ? SimulationType::CPU : SimulationType::GPU;
        renderNode->UpdateRenderUnit(emitterIndex, sim, pipeline->GetParticleSystemGpuDriven());
        auto renderUnit = renderNode->GetRenderUnit(emitterIndex);
        Assert(renderUnit && emitterIndex < computeContexts.size());
        renderUnit->Update(emitterIndex, renderData, renderDirty, &computeContexts[emitterIndex]);
    }

    renderNode->PostUpdate();
    auto* gpuScene = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>()->GetWorldRenderPipeline()->GetGPUScene();
    gpuScene->SetGPUSceneDirty(renderData.Entity);
}

}   // namespace cross
