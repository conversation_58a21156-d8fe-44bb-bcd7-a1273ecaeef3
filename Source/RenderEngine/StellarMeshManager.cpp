#include "StellarMeshManager.h"
#include "NativeGraphicsInterface/NGI.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderEngine.h"
#include "CECommon/Common/EngineGlobal.h"

namespace cross::StellarMesh {

StellarMeshManager::StellarMeshManager()
{
    mMeshConstants.reserve(1024);
    mClusters.reserve(4096);

    ResizeBuffer<MeshConstant>(mMeshConstantBuffer, mMeshConstantBufferView, mMeshConstants, "StellarMeshManager.MeshConstantBuffer");
    ResizeBuffer<GPUCluster>(mClusterBuffer, mClusterBufferView, mClusters, "StellarMeshManager.ClusterBuffer");
}

void StellarMeshManager::AddRenderMesh(MeshR* meshR, std::vector<GPUCluster> clusters)
{
    if (!meshR)
        return;

    UInt32 meshIndex = meshR->GetMeshIndex();

    // Use exclusive lock for writing to shared data
    std::lock_guard<std::mutex> lock(mMutex);

    if (meshIndex >= mMeshConstants.size())
    {
        mMeshConstants.resize(meshIndex + 1);
    }

    UInt32 clusterOffset = static_cast<UInt32>(mClusters.size());
    UInt32 clusterCount = static_cast<UInt32>(clusters.size());

    MeshConstant& meshConstant = mMeshConstants[meshIndex];
    meshConstant.mClusterOffset = clusterOffset;
    meshConstant.mClusterCount = clusterCount;

    auto const& resource = meshR->GetStellarMeshResource();

    meshConstant.mIndexBufferIndex = gBindlessResourceManager.AllocateBindlessBufferView(resource.mIndexBuffer.get(), GraphicsFormat::R32_UInt, resource.mIndexBuffer->GetSize());
    for (auto& [attribute, buffer] : resource.mVertexAttributesBuffer)
    {
        switch (attribute)
        {
        case StellarMeshVertexAttribute::mPosition:
            meshConstant.mPositionBufferIndex = gBindlessResourceManager.AllocateBindlessBufferView(buffer.get(), GraphicsFormat::R32G32B32_SFloat, buffer->GetSize());
            break;
        case StellarMeshVertexAttribute::mNormal:
            meshConstant.mNormalBufferIndex = gBindlessResourceManager.AllocateBindlessBufferView(buffer.get(), GraphicsFormat::R32G32B32_SFloat, buffer->GetSize());
            break;
        case StellarMeshVertexAttribute::mTangent:
            meshConstant.mTangetBufferIndex = gBindlessResourceManager.AllocateBindlessBufferView(buffer.get(), GraphicsFormat::R32G32B32A32_SFloat, buffer->GetSize());
            break;
        case StellarMeshVertexAttribute::mColor:
            meshConstant.mColorBufferIndex = gBindlessResourceManager.AllocateBindlessBufferView(buffer.get(), GraphicsFormat::R32G32B32A32_SFloat, buffer->GetSize());
            break;
        case StellarMeshVertexAttribute::mUV0:
            meshConstant.mUV0BufferIndex = gBindlessResourceManager.AllocateBindlessBufferView(buffer.get(), GraphicsFormat::R32G32_SFloat, buffer->GetSize());
            break;
        case StellarMeshVertexAttribute::mUV1:
            meshConstant.mUV1BufferIndex = gBindlessResourceManager.AllocateBindlessBufferView(buffer.get(), GraphicsFormat::R32G32_SFloat, buffer->GetSize());
            break;
        default:
            Assert(false);
        }
    }

    mClusters.insert(mClusters.end(), clusters.begin(), clusters.end());

    UpdateGPUBufferFor(meshIndex);
}

void StellarMeshManager::RemoveRenderMesh(MeshR* meshR)
{
    if (!meshR)
        return;

    UInt32 meshIndex = meshR->GetMeshIndex();

    // Use exclusive lock for writing to shared data
    std::lock_guard<std::mutex> lock(mMutex);

    // Check if index is valid
    if (meshIndex >= mMeshConstants.size())
        return;

    // Get cluster information
    MeshConstant& meshConstant = mMeshConstants[meshIndex];
    UInt32 clusterOffset = meshConstant.mClusterOffset;
    UInt32 clusterCount = meshConstant.mClusterCount;

    // If no clusters, return directly
    if (clusterCount == 0)
        return;

    // Clear mesh constant data
    gBindlessResourceManager.ReleaseBindlessBufferView(meshConstant.mIndexBufferIndex);
    gBindlessResourceManager.ReleaseBindlessBufferView(meshConstant.mPositionBufferIndex);
    gBindlessResourceManager.ReleaseBindlessBufferView(meshConstant.mNormalBufferIndex);
    gBindlessResourceManager.ReleaseBindlessBufferView(meshConstant.mTangetBufferIndex);
    gBindlessResourceManager.ReleaseBindlessBufferView(meshConstant.mColorBufferIndex);
    gBindlessResourceManager.ReleaseBindlessBufferView(meshConstant.mUV0BufferIndex);
    gBindlessResourceManager.ReleaseBindlessBufferView(meshConstant.mUV1BufferIndex);

    meshConstant.mClusterOffset       = 0;
    meshConstant.mClusterCount        = 0;
    meshConstant.mIndexBufferIndex    = CE_BINDLESS_INVALID_INDEX;
    meshConstant.mPositionBufferIndex = CE_BINDLESS_INVALID_INDEX;
    meshConstant.mNormalBufferIndex   = CE_BINDLESS_INVALID_INDEX;
    meshConstant.mTangetBufferIndex   = CE_BINDLESS_INVALID_INDEX;
    meshConstant.mColorBufferIndex    = CE_BINDLESS_INVALID_INDEX;
    meshConstant.mUV0BufferIndex      = CE_BINDLESS_INVALID_INDEX;
    meshConstant.mUV1BufferIndex      = CE_BINDLESS_INVALID_INDEX;

    // Mark as dirty, needs update
    //mDirtyMeshIndices.push(meshIndex);
    //mMeshConstantBufferDirty = true;
    //UploadDataToGPUFor(meshIndex);
}

void StellarMeshManager::UpdateGPUBufferFor(UInt32 meshIndex)
{
    // Resize buffers if needed
    if (!mMeshConstants.empty())
    {
        ResizeBuffer<MeshConstant>(mMeshConstantBuffer, mMeshConstantBufferView, mMeshConstants, "StellarMeshManager.MeshConstantBuffer");
    }

    if (!mClusters.empty())
    {
        ResizeBuffer<GPUCluster>(mClusterBuffer, mClusterBufferView, mClusters, "StellarMeshManager.ClusterBuffer");
    }

    UploadDataToGPUFor(meshIndex);
}

NGIBuffer* StellarMeshManager::GetMeshConstantBuffer() const
{
    // Use shared lock for read-only access
    std::shared_lock<std::shared_mutex> lock(mSharedMutex);
    return mMeshConstantBuffer.get();
}

NGIBuffer* StellarMeshManager::GetClusterBuffer() const
{
    // Use shared lock for read-only access
    std::shared_lock<std::shared_mutex> lock(mSharedMutex);
    return mClusterBuffer.get();
}

NGIBufferView* StellarMeshManager::GetMeshConstantBufferView() const
{
    // Use shared lock for read-only access
    std::shared_lock<std::shared_mutex> lock(mSharedMutex);
    return mMeshConstantBufferView.get();
}

NGIBufferView* StellarMeshManager::GetClusterBufferView() const
{
    // Use shared lock for read-only access
    std::shared_lock<std::shared_mutex> lock(mSharedMutex);
    return mClusterBufferView.get();
}

template<typename T>
void StellarMeshManager::ResizeBuffer(std::unique_ptr<NGIBuffer>& buffer,
                                     std::unique_ptr<NGIBufferView>& bufferView,
                                     const std::vector<T>& data,
                                     const char* bufferName)
{
    // Calculate required buffer size
    UInt32 requiredSize = static_cast<UInt32>(data.size() * sizeof(T));

    // Calculate new buffer size (round up to power of 2)
    UInt32 newSize = 1;
    while (newSize < requiredSize)
    {
        newSize <<= 1;
    }

    // Ensure minimum capacity based on type
    constexpr UInt32 minSize = (sizeof(T) == sizeof(MeshConstant)) ? 1024u : 4096u;
    newSize = std::max(newSize, minSize);

    if (!buffer || buffer->GetDesc().Size < newSize)
    {
        NGIBufferDesc desc{newSize, BufferUsage};
        auto newBuffer = std::unique_ptr<NGIBuffer>(GetNGIDevice().CreateBuffer(desc, bufferName));

        // Copy data from old buffer if exists
        if (buffer)
        {
            // Create a staging buffer for the copy
            auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

            SizeType copySize = std::min<SizeType>(buffer->GetDesc().Size, newSize);
            auto asycStagingbuffer = rendererSystem->GetScratchBuffer()->AllocateStaging_Async(NGIBufferUsage::CopySrc, copySize);
            auto [stagingbuffer, offset, dstData] = asycStagingbuffer.Raw();

            // Copy old data to staging buffer
            void* mappedData = buffer->Map();
            if (mappedData)
            {
                //stagingBufferWrap.MemWrite(0, mappedData, copySize);
                std::copy_n(static_cast<const UInt8*>(mappedData), copySize, std::next(static_cast<UInt8*>(dstData), offset));
                buffer->Unmap();
            }
            stagingbuffer->UnmapRange(offset, copySize);

            // Copy from staging buffer to new buffer
            rendererSystem->UpdateBuffer(newBuffer.get(), stagingbuffer, NGICopyBuffer{offset, 0, copySize}, NGIResourceState::Undefined, NGIResourceState::ShaderResourceBit);
            asycStagingbuffer.Free();
        }

        buffer = std::move(newBuffer);

        // Create buffer view
        NGIBufferViewDesc viewDesc{
            NGIBufferUsage::StructuredBuffer,
            0,
            newSize,
            GraphicsFormat::Unknown,
            sizeof(T)
        };

        bufferView.reset(GetNGIDevice().CreateBufferView(buffer.get(), viewDesc));
    }
}

// Explicit template instantiations
template void StellarMeshManager::ResizeBuffer<MeshConstant>(
    std::unique_ptr<NGIBuffer>&,
    std::unique_ptr<NGIBufferView>&,
    const std::vector<MeshConstant>&,
    const char*);

template void StellarMeshManager::ResizeBuffer<GPUCluster>(
    std::unique_ptr<NGIBuffer>&,
    std::unique_ptr<NGIBufferView>&,
    const std::vector<GPUCluster>&,
    const char*);

void StellarMeshManager::UploadDataToGPUFor(UInt32 meshIndex)
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    // Upload MeshConstant data
    if (!mMeshConstants.empty())
    {
        UInt32 sizeInBytes = static_cast<UInt32>(sizeof(MeshConstant));

        // Allocate staging buffer
        auto asycStagingbuffer = rendererSystem->GetScratchBuffer()->AllocateStaging_Async(NGIBufferUsage::CopySrc, sizeInBytes);
        auto [stagingbuffer, stagingOffset, stagingData] = asycStagingbuffer.Raw();

        auto meshData = reinterpret_cast<UInt8*>(&mMeshConstants[meshIndex]);
        auto meshOffset = meshIndex * sizeInBytes;

        // Copy data to staging buffer
        memcpy(stagingData, meshData, sizeInBytes);
        stagingbuffer->UnmapRange(stagingOffset, sizeInBytes);

        // Update the buffer
        rendererSystem->UpdateBuffer(mMeshConstantBuffer.get(), stagingbuffer, NGICopyBuffer{stagingOffset, meshOffset, sizeInBytes}, NGIResourceState::Undefined, NGIResourceState::ShaderResourceBit);
        asycStagingbuffer.Free();
    }

    // Upload Cluster data
    if (!mClusters.empty())
    {
        if (mMeshConstants[meshIndex].mClusterCount == 0)
            return;

        auto uploadClusterCount = mMeshConstants[meshIndex].mClusterCount;
        UInt32 sizeInBytes = static_cast<UInt32>(uploadClusterCount * sizeof(GPUCluster));

        auto asycStagingbuffer = rendererSystem->GetScratchBuffer()->AllocateStaging_Async(NGIBufferUsage::CopySrc, sizeInBytes);
        auto [stagingbuffer, stagingOffset, stagingData] = asycStagingbuffer.Raw();

        auto clusterData = reinterpret_cast<UInt8*>(&mClusters[mMeshConstants[meshIndex].mClusterOffset]);
        auto clusterOffset = mMeshConstants[meshIndex].mClusterOffset * sizeof(GPUCluster);

        memcpy(stagingData, clusterData, sizeInBytes);
        stagingbuffer->UnmapRange(stagingOffset, sizeInBytes);

        rendererSystem->UpdateBuffer(mClusterBuffer.get(), stagingbuffer, NGICopyBuffer{stagingOffset, clusterOffset, sizeInBytes}, NGIResourceState::Undefined, NGIResourceState::ShaderResourceBit);
        asycStagingbuffer.Free();
    }
}

} // namespace cross::StellarMesh