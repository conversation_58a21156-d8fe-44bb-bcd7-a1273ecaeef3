#include "InstanceCulling.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "RenderEngine/RenderPipelineSystemR.h"

namespace cross {
std::tuple<REDPass*, REDBufferView*> InstanceCulling::AssembleInstanceCullingPass(const GameContext& gameContext, REDDrawUnitList* drawUnitList, REDTextureView* depthHiZView, const NameID& passName, const NameID& tagName, bool enableHiZ,
                                                                                  bool useCurrFrameHiZ)
{
    struct VisibleInstanceCommand
    {
        UInt32 instanceId;
        UInt32 indirectArgIndex;
    };

    auto* gpuScene = gameContext.mRenderPipeline->GetWorldRenderPipeline()->GetGPUScene();
    auto* RED = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
    auto* renderPipeline = static_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
    auto* renderPipelineSetting = static_cast<const FFSRenderPipelineSetting*>(gameContext.mRenderPipeline->GetSetting());

    auto objectIndexBufferTuple = CreateUAVBuffer(RED, "ObjectIndexBuffer", 0, sizeof(UInt32));
    auto* objectIndexBuffer = std::get<0>(objectIndexBufferTuple);
    auto* objectIndexBufferUAV = std::get<1>(objectIndexBufferTuple);
    auto* objectIndexBufferSRV = std::get<2>(objectIndexBufferTuple);

    auto* renderCamera = renderPipeline->GetRenderCamera();

    REDPass* cullingPass = nullptr;
    auto& foliageGpuDriven = renderPipeline->GetFoliageGpuDriven();

    // InstanceCulling
    //if (renderPipelineSetting->InstanceCulling)
    {
        auto visibleObjectCommandsBufferTuple = CreateUAVBuffer(RED, "VisibleObjectCommandsBuffer", 0, sizeof(VisibleInstanceCommand));
        auto* visibleObjectCommandsBuffer = std::get<0>(visibleObjectCommandsBufferTuple);
        auto* visibleObjectCommandsBufferUAV = std::get<1>(visibleObjectCommandsBufferTuple);
        auto* visibleObjectCommandsBufferSRV = std::get<2>(visibleObjectCommandsBufferTuple);

        auto [visibleObjectCommandCountBuffer, visibleObjectCommandCountBufferUAV, visibleObjectCommandCountBufferSRV] = CreateUAVBuffer(RED, "VisibleObjectCommandCountBuffer", 1, sizeof(UInt32));
        ClearRWStructuredBuffer(RED, renderPipelineSetting, visibleObjectCommandCountBufferUAV, 0);

        auto* drawIndirectArgsBuffer = RED->AllocateBuffer("DrawIndirectBuffer", NGIBufferDesc{0, NGIBufferUsage::IndirectBuffer | NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst | NGIBufferUsage::CopySrc});
        auto* drawIndirectArgsBufferSRV = RED->AllocateBufferView(drawIndirectArgsBuffer, NGIBufferViewDesc{NGIBufferUsage::StructuredBuffer, 0, 0, GraphicsFormat::Unknown, sizeof(UInt32)});
        auto* drawIndirectArgsBufferUAV = RED->AllocateBufferView(drawIndirectArgsBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, 0, GraphicsFormat::Unknown, sizeof(UInt32)});
        drawUnitList->SetIndirectBuffer(drawIndirectArgsBuffer);

        auto [objectIndexOffsetBuffer, objectIndexOffsetBufferUAV, objectIndexOffsetBufferSRV] = CreateUAVBuffer(RED, "ObjectIndexOffsetBuffer", 0, sizeof(UInt32), NGIBufferUsage::VertexBuffer);
        drawUnitList->SetObjectIndexOffsetsBuffer(objectIndexOffsetBuffer);

        auto [tempObjectIndexOffsetBuffer, tempObjectIndexOffsetBufferUAV, tempObjectIndexOffsetBufferSRV] = CreateUAVBuffer(RED, "TempObjectIndexOffsetBuffer", 0, sizeof(UInt32));

        auto [offsetBufferCountBuffer, offsetBufferCountBufferUAV, offsetBufferCountBufferSRV] = CreateUAVBuffer(RED, "OffsetBufferCountBuffer", 1, sizeof(UInt32));
        ClearRWStructuredBuffer(RED, renderPipelineSetting, offsetBufferCountBufferUAV, 0);

        auto* outputPassIndirectArgs = RED->AllocateBuffer("OutputPassIndirectArgs", NGIBufferDesc{3u * sizeof(UInt32), NGIBufferUsage::IndirectBuffer | NGIBufferUsage::RWStructuredBuffer});
        auto* outputPassIndirectArgsUAV = RED->AllocateBufferView(outputPassIndirectArgs, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, 3u * sizeof(UInt32), GraphicsFormat::Unknown, sizeof(UInt32)});

        auto [objectPayloadIndexAndObjectOffsetAndMutex, objectPayloadIndexAndObjectOffsetAndMutexUAV, objectPayloadIndexAndObjectOffsetAndMutexSRV] = CreateUAVBuffer(RED, "_ObjectPayloadIndexAndObjectOffsetAndMutex", 3, sizeof(UInt32));
        auto* rangePayloadSetPass = ClearRWStructuredBuffer(RED, renderPipelineSetting, objectPayloadIndexAndObjectOffsetAndMutexUAV, 0, 1, 0);
        ClearRWStructuredBuffer(RED, renderPipelineSetting, objectPayloadIndexAndObjectOffsetAndMutexUAV, 1, 3, 0);
        //ClearRWStructuredBuffer(RED, renderPipelineSetting, objectPayloadIndexAndObjectOffsetAndMutexUAV,0);

        std::shared_ptr<UInt32> visibleObjectCommandBufferMaxNum;
        visibleObjectCommandBufferMaxNum.reset(new UInt32(0));

        //bool runInstanceCulling2 = renderPipelineSetting->InstanceCulling2;

        // CullPerPageDrawUnits(Normal)
        {
            auto objectPayloadDataBufferTuple = CreateUAVBuffer(RED, "ObjectPayloadDataBuffer", 0, sizeof(ObjectPayloadData), NGIBufferUsage::CopyDst);
            auto* objectPayloadDataBuffer = std::get<0>(objectPayloadDataBufferTuple);
            auto* objectPayloadDataBufferUAV = std::get<1>(objectPayloadDataBufferTuple);
            auto* objectPayloadDataBufferSRV = std::get<2>(objectPayloadDataBufferTuple);

            UInt32 engineFrameCount = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();

            auto* particleInstanceCountCopyPass = RED->AllocatePass("ParticleInstanceCountCopy");
            auto* pass = RED->AllocatePass("CullPerPageDrawUnits(Normal)");
            {
                pass->SetProperty(NAME_ID("_CurrSceneFrameCount"), engineFrameCount);
                pass->SetProperty(NAME_ID("_GPUScenePrimitiveCullingDatas"), gpuScene->GetPrimitiveCullingDataBufferSRV());
                pass->SetProperty(NAME_ID("_GPUSceneObjectCullingDatas"), gpuScene->GetObjectCullingDataBufferSRV());
                pass->SetProperty(NAME_ID("_ObjectPayloadDatas"), objectPayloadDataBufferSRV);
                pass->SetProperty(NAME_ID("_ObjectPayloadIndexAndObjectOffsetAndMutex"), objectPayloadIndexAndObjectOffsetAndMutexUAV);

                if (depthHiZView != nullptr)
                {
                    pass->SetProperty(NAME_ID("_DepthHiZ"), depthHiZView);
                    pass->SetProperty(NAME_ID("_HiZDepthTextureSize"), Float2(static_cast<float>(depthHiZView->GetWidth()), static_cast<float>(depthHiZView->GetHeight())));
                    pass->SetProperty(NAME_ID("_DepthHiZMipCount"), depthHiZView->GetTexture()->GetDesc().MipCount);
                }
                else
                {
                    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
                    pass->SetProperty(NAME_ID("_DepthHiZ"), rendererSystem->GetRenderPrimitives()->mDefaultBlackTexture2DView.get());
                }
                pass->SetProperty(NAME_ID("_UseCurrHiZ"), useCurrFrameHiZ);

                pass->SetProperty(NAME_ID("_OutVisibleObjectCommands"), visibleObjectCommandsBufferUAV);
                pass->SetProperty(NAME_ID("_OutVisibleObjectCommandCount"), visibleObjectCommandCountBufferUAV);
                pass->SetProperty(NAME_ID("_OutDrawIndirectArgs"), drawIndirectArgsBufferUAV);

                std::tuple<REDBuffer*, REDBufferView*, REDBufferView*> globalInstanceCounterBufferOffsetTuple = {nullptr, nullptr, nullptr};
                std::tuple<REDBuffer*, REDBufferView*, REDBufferView*> payloadDataOffsetTuple = {nullptr, nullptr, nullptr};
                REDBufferView* globalInstanceCounterBufferView = nullptr;
                REDComputeExecutionPayload *computeExecutionPayload, *particleInstanceCountCopyComputeExecutionPayload;

                //if (runInstanceCulling2)
                {
                    globalInstanceCounterBufferOffsetTuple = CreateUAVBuffer(RED, "globalInstanceCounterBufferOffsetBuffer", 1, sizeof(SInt32), NGIBufferUsage::CopyDst);
                    payloadDataOffsetTuple = CreateUAVBuffer(RED, "payloadDataOffsetBuffer", 1, sizeof(SInt32), NGIBufferUsage::CopyDst);
                    globalInstanceCounterBufferView =
                        dynamic_cast<FFSWorldRenderPipeline*>(gameContext.mRenderWorld->GetRenderSystem<RenderPipelineSystemR>()->GetWorldRenderPipeline())->GetParticleSystemGpuDriven()->GetGlobalInstanceCounterBufferView();
                    if (globalInstanceCounterBufferView)
                        particleInstanceCountCopyPass->SetProperty(NameID("_ParticleGlobalInstanceCounterBuffer"), globalInstanceCounterBufferView, NGIResourceState::ComputeShaderShaderResource);
                    particleInstanceCountCopyPass->SetProperty(NameID("_ObjectPayloadDatas"), objectPayloadDataBufferUAV, NGIResourceState::ComputeShaderUnorderedAccess);
                    particleInstanceCountCopyPass->SetProperty(NameID("_GlobalInstanceCounterBufferOffsets"), std::get<2>(globalInstanceCounterBufferOffsetTuple), NGIResourceState::ComputeShaderShaderResource);
                    particleInstanceCountCopyPass->SetProperty(NameID("_PayloadDataOffsets"), std::get<2>(payloadDataOffsetTuple), NGIResourceState::ComputeShaderShaderResource);
                    particleInstanceCountCopyComputeExecutionPayload = particleInstanceCountCopyPass->Dispatch(renderPipelineSetting->ParticleGPUSimuateInstanceCountCopyComputeShaderR, NameID("InstanceCountCopy"), 0, 0, 0);

                    computeExecutionPayload = pass->Dispatch(renderPipelineSetting->InstanceCulling2ComputeShaderR, NameID("CullPerPageDrawUnits"), 64, 1, 1);
                }
                //else
                //{
                //    computeExecutionPayload = pass->Dispatch(renderPipelineSetting->InstanceCullingComputeShaderR, NameID("CullPerPageDrawUnits"), 0, 0, 0);
                //}

                pass->OnCulling([=,
                                 objectIndexOffsetBuffer = objectIndexOffsetBuffer,
                                 objectIndexOffsetBufferUAV = objectIndexOffsetBufferUAV,
                                 tempObjectIndexOffsetBuffer = tempObjectIndexOffsetBuffer,
                                 tempObjectIndexOffsetBufferUAV = tempObjectIndexOffsetBufferUAV,
                                 visibleObjectCommandBufferMaxNum = visibleObjectCommandBufferMaxNum](REDPass* pass) {
                    auto* drawUnits = drawUnitList->GetDrawUnits();
                    //if (runInstanceCulling2)
                    {
                        FrameStdVector<SInt32> globalInstanceCounterBufferOffsets;
                        FrameStdVector<SInt32> payloadDataOffsets;
                        //drawUnits have been sorted according to distance
                        for (UInt32 drawUnitIndex = 0; drawUnitIndex < drawUnits->GetSize(); drawUnitIndex++)
                        {
                            const REDDrawUnit& drawUnit = drawUnits->At(drawUnitIndex);

                            if (drawUnit.mType == RenderNodeType::Particle)
                            {
                                SInt32 offset = static_cast<SInt32>(reinterpret_cast<uintptr_t>(drawUnit.mCustumData));
                                // offset < 0 if CPU particle enabled
                                if (offset >= 0)
                                {
                                    const_cast<REDDrawUnit&>(drawUnit).mCustumData = reinterpret_cast<const void*>(static_cast<uintptr_t>(globalInstanceCounterBufferOffsets.size()));
                                    globalInstanceCounterBufferOffsets.push_back(offset);
                                    payloadDataOffsets.push_back(static_cast<SInt32>(drawUnitIndex));
                                }
                            }
                        }

                        if (!globalInstanceCounterBufferOffsets.empty())
                        {
                            UInt32 payloadDataSize = static_cast<UInt32>(drawUnitList->GetObjectPayloadDatas2().size());
                            UInt32 cnt = 0;
                            for (UInt32 payloadDataIndex = 0; payloadDataIndex < payloadDataSize; payloadDataIndex++)
                            {
                                if (cnt == payloadDataOffsets.size())
                                    break;
                                const REDDrawUnit& drawUnit = drawUnits->At(drawUnitList->GetObjectPayloadDrawUnitIndex(payloadDataIndex));
                                SInt32 offset = static_cast<SInt32>(reinterpret_cast<uintptr_t>(drawUnit.mCustumData));
                                if (drawUnit.mType == RenderNodeType::Particle && offset >= 0)
                                {
                                    cnt++;
                                    Assert(static_cast<UInt32>(payloadDataOffsets.at(offset)) == drawUnitList->GetObjectPayloadDrawUnitIndex(payloadDataIndex));
                                    payloadDataOffsets.at(offset) = static_cast<SInt32>(payloadDataIndex);
                                }
                            }
                            Assert(cnt == payloadDataOffsets.size());
                            Assert(globalInstanceCounterBufferView);
                            auto GPUEmitterCount = static_cast<UInt32>(globalInstanceCounterBufferOffsets.size());
                            auto bufferSize = static_cast<UInt32>(GPUEmitterCount * sizeof(SInt32));
                            particleInstanceCountCopyComputeExecutionPayload->ModifyThreadGroupCount((GPUEmitterCount + 63) >> 6, 1, 1);
                            get<0>(globalInstanceCounterBufferOffsetTuple)->ModifySize(bufferSize);
                            get<0>(payloadDataOffsetTuple)->ModifySize(bufferSize);
                            get<2>(globalInstanceCounterBufferOffsetTuple)->ModifyRange(0, bufferSize);
                            get<2>(payloadDataOffsetTuple)->ModifyRange(0, bufferSize);

                            RED->QueueBufferUpload(get<0>(globalInstanceCounterBufferOffsetTuple), 0, globalInstanceCounterBufferOffsets.data(), bufferSize);
                            RED->QueueBufferUpload(get<0>(payloadDataOffsetTuple), 0, payloadDataOffsets.data(), bufferSize);
                            particleInstanceCountCopyPass->SetProperty(NameID("_GPUEmitterCount"), GPUEmitterCount);
                        }
                    }
                    // ObjectPayloadData (for normal DrawUnits)

                    UInt32 normalObjectCount = 0;
                    //if (!runInstanceCulling2)
                    //{
                    //    const auto& objectPayloadDatas = drawUnitList->GetObjectPayloadDatas();
                    //    normalObjectCount = static_cast<UInt32>(objectPayloadDatas.size());

                    //    UInt32 objectPayloadDataBufferDataSize = static_cast<UInt32>(normalObjectCount * sizeof(ObjectPayloadData));
                    //    UInt32 objectPayloadDataBufferSize = std::max(objectPayloadDataBufferDataSize, 1u);
                    //    objectPayloadDataBuffer->ModifySize(objectPayloadDataBufferSize);
                    //    objectPayloadDataBufferSRV->ModifyRange(0, objectPayloadDataBufferSize);

                    //    RED->QueueBufferUpload(objectPayloadDataBuffer, 0, objectPayloadDatas.data(), objectPayloadDataBufferDataSize);
                    //}
                    //else
                    {
                        const auto& objectPayloadDatas = drawUnitList->GetObjectPayloadDatas2();
                        normalObjectCount = static_cast<UInt32>(objectPayloadDatas.size());

                        UInt32 objectPayloadDataBufferDataSize = static_cast<UInt32>(normalObjectCount * sizeof(ObjectPayloadData2));
                        UInt32 objectPayloadDataBufferSize = std::max(objectPayloadDataBufferDataSize, 1u);
                        objectPayloadDataBuffer->ModifySize(objectPayloadDataBufferSize);
                        objectPayloadDataBufferSRV->ModifyRange(0, objectPayloadDataBufferSize);
                        objectPayloadDataBufferUAV->ModifyRange(0, objectPayloadDataBufferSize);

                        RED->QueueBufferUpload(objectPayloadDataBuffer, 0, objectPayloadDatas.data(), objectPayloadDataBufferDataSize);
                    }

                    // VisibleObjectsBuffer (for all DrawUnits)
                    std::unordered_set<const void*> renderNodeMap;

                    UInt32 maxObjectsPerPass = 0;
                    for (UInt32 drawUnitIndex = 0; drawUnitIndex < drawUnits->GetSize(); drawUnitIndex++)
                    {
                        const REDDrawUnit& drawUnit = drawUnits->At(drawUnitIndex);

                        if (drawUnit.mType == RenderNodeType::Foliage)
                        {
                            auto* foliageInfo = reinterpret_cast<const FoliageInfo*>(drawUnit.mCustumData);
                            auto* renderNode = foliageInfo->RenderNode;

                            if (renderNodeMap.find(renderNode) == renderNodeMap.end())
                            {
                                renderNodeMap.insert(renderNode);

                                maxObjectsPerPass += drawUnit.mInstanceCount;
                            }
                        }
                        else
                        {
                            maxObjectsPerPass += drawUnit.mInstanceCount;
                        }
                    }

                    *visibleObjectCommandBufferMaxNum = maxObjectsPerPass;

                    UInt32 visibleObjectsBufferDataSize = static_cast<UInt32>(maxObjectsPerPass * sizeof(VisibleInstanceCommand));
                    UInt32 visibleObjectsBufferSize = std::max(visibleObjectsBufferDataSize, 1u);
                    visibleObjectCommandsBuffer->ModifySize(visibleObjectsBufferSize);
                    visibleObjectCommandsBufferUAV->ModifyRange(0, visibleObjectsBufferSize);
                    visibleObjectCommandsBufferSRV->ModifyRange(0, visibleObjectsBufferSize);

                    // IndirectBuffer (for all DrawUnits)
                    auto pool = RED->GetREDFrameAllocator();
                    FrameStdVector<CompactDrawCMD> indirectDrawCommands(pool);
                    indirectDrawCommands.resize(drawUnits->GetSize());

                    for (UInt32 index = 0; index < drawUnits->GetSize(); index++)
                    {
                        const REDDrawUnit& drawUnit = (*drawUnits)[index];
                        const RenderGeometry* geometry = drawUnit.mGeometry;

                        // TEMP
                        const bool isSupportAutoInstancing = drawUnit.mTypedObjectDataBufferView;
                        const bool isFoliage = drawUnit.mType == RenderNodeType::Foliage;

                        indirectDrawCommands[index] = CompactDrawCMD{
                            geometry->GetIndexCount(),
                            (isSupportAutoInstancing || isFoliage) ? 0u : 1u,
                            geometry->GetIndexStart(),
                            static_cast<SInt32>(geometry->GetVertexStart()),
                            0,
                        };

                        drawUnit.mIndirectBufferOffset = index * sizeof(CompactDrawCMD);
                        drawUnit.mIndirectCount = 1u;
                        drawUnit.mIndirectStride = sizeof(CompactDrawCMD);
                    }

                    UInt32 indirectBufferDataSize = static_cast<UInt32>(drawUnits->GetSize() * sizeof(CompactDrawCMD));
                    UInt32 indirectBufferSize = std::max(indirectBufferDataSize, 1u);
                    drawIndirectArgsBuffer->ModifySize(indirectBufferSize);
                    drawIndirectArgsBufferSRV->ModifyRange(0, indirectBufferSize);
                    drawIndirectArgsBufferUAV->ModifyRange(0, indirectBufferSize);

                    RED->QueueBufferUpload(drawIndirectArgsBuffer, 0, indirectDrawCommands.data(), indirectBufferDataSize);

                    // ObjectIndexOffsetBuffer
                    UInt32 objectIndexOffsetBufferSize = std::max(static_cast<UInt32>(drawUnits->GetSize() * sizeof(UInt32)), 1u);
                    objectIndexOffsetBuffer->ModifySize(objectIndexOffsetBufferSize);
                    objectIndexOffsetBufferUAV->ModifyRange(0, objectIndexOffsetBufferSize);

                    // TempObjectIndexOffsetBuffer
                    tempObjectIndexOffsetBuffer->ModifySize(objectIndexOffsetBufferSize);
                    tempObjectIndexOffsetBufferUAV->ModifyRange(0, objectIndexOffsetBufferSize);

                    // ObjectIndexBuffer
                    UInt32 objectIndexBufferSize = std::max(static_cast<UInt32>(maxObjectsPerPass * sizeof(UInt32)), 1u);
                    objectIndexBuffer->ModifySize(objectIndexBufferSize);
                    objectIndexBufferUAV->ModifyRange(0, objectIndexBufferSize);
                    objectIndexBufferSRV->ModifyRange(0, objectIndexBufferSize);

                    // Dispatch
                    pass->SetProperty(NAME_ID("_ObjectCount"), normalObjectCount);
                    pass->SetProperty(NAME_ID("_PayloadCount"), normalObjectCount);
                    pass->SetProperty(NAME_ID("_RangePayloadOffset"), drawUnitList->GetRangePayloadOffset());
                    pass->SetProperty(NAME_ID("_RangePayloadCount"), drawUnitList->GetLargePayloadOffset() - drawUnitList->GetRangePayloadOffset());
                    pass->SetProperty(NAME_ID("_LargePayloadCount"), normalObjectCount - drawUnitList->GetLargePayloadOffset());
                    
                    pass->SetProperty(NAME_ID("_VisibleObjectCommandBufferMaxNum"), maxObjectsPerPass);
                    rangePayloadSetPass->SetProperty(NAME_ID("_ClearParams"), UInt4(0, 1, drawUnitList->GetLargePayloadOffset(), 0));

                    //if (!runInstanceCulling2)
                    //{
                    //    UInt3 groupSize;
                    //    renderPipelineSetting->InstanceCullingComputeShaderR->GetThreadGroupSize("CullPerPageDrawUnits", groupSize.x, groupSize.y, groupSize.z);
                    //    computeExecutionPayload->ModifyThreadGroupCount(math::DivideAndRoundUp(normalObjectCount, groupSize.x), 1, 1);
                    //}
                });
            }

            cullingPass = pass;
        }

        if (renderPipelineSetting->mFoliageGpuDrivenSettings.enable && renderPipelineSetting->EnableFoliageDrawing)
        {
            cullingPass = foliageGpuDriven.AssembleCullingPass(
                gameContext, passName, tagName, renderCamera, enableHiZ ? gameContext.mRenderPipeline->GetDepthPyramid() : nullptr, drawUnitList, visibleObjectCommandsBufferUAV, visibleObjectCommandCountBufferUAV);
        }

        // AllocateCommandInstanceOutputSpace
        {
            auto* pass = RED->AllocatePass("AllocateCommandInstanceOutputSpace");
            {
                pass->SetProperty(NAME_ID("_DrawIndirectArgs"), drawIndirectArgsBufferSRV);
                pass->SetProperty(NAME_ID("_OutOffsetBufferCount"), offsetBufferCountBufferUAV);
                pass->SetProperty(NAME_ID("_OutObjectIndexOffsetBuffer"), objectIndexOffsetBufferUAV);
                pass->SetProperty(NAME_ID("_OutTempObjectIndexOffsetBuffer"), tempObjectIndexOffsetBufferUAV);
                auto* computeExecutionPayload = pass->Dispatch(renderPipelineSetting->InstanceCullingComputeShaderR, NameID("AllocateCommandInstanceOutputSpace"), 0, 0, 0);

                pass->OnCulling([=](REDPass* pass) {
                    auto* drawUnits = drawUnitList->GetDrawUnits();

                    pass->SetProperty("_IndirectArgCount", drawUnits->GetSize());

                    UInt3 groupSize;
                    renderPipelineSetting->InstanceCullingComputeShaderR->GetThreadGroupSize("AllocateCommandInstanceOutputSpace", groupSize.x, groupSize.y, groupSize.z);
                    computeExecutionPayload->ModifyThreadGroupCount(math::DivideAndRoundUp(drawUnits->GetSize(), groupSize.x), 1, 1);
                });
            }
        }

        // InitIndirectArgs1D
        {
            UInt3 groupSize;
            renderPipelineSetting->InstanceCullingComputeShaderR->GetThreadGroupSize("OutputCommandInstanceLists", groupSize.x, groupSize.y, groupSize.z);

            auto* pass = RED->AllocatePass("InitIndirectArgs1D");
            {
                pass->SetProperty(NAME_ID("_UIntParams0"), UInt4(1, groupSize.x, 0, 0));
                pass->SetProperty(NAME_ID("_InputCountBuffer"), visibleObjectCommandCountBufferSRV);
                pass->SetProperty(NAME_ID("_OutIndirectDispatchArgs"), outputPassIndirectArgsUAV);
                pass->Dispatch(renderPipelineSetting->mVirtualShadowMapSettings.InitIndirectArgs1DR, "InitIndirectArgs1D", 1, 1, 1);
            }
        }

        // OutputCommandInstanceLists
        {
            auto* pass = RED->AllocatePass("OutputCommandInstanceLists");
            {
                pass->SetProperty(NAME_ID("_VisibleObjectCommands"), visibleObjectCommandsBufferSRV);
                pass->SetProperty(NAME_ID("_VisibleObjectCommandCount"), visibleObjectCommandCountBufferSRV);
                pass->SetProperty(NAME_ID("_OutTempObjectIndexOffsetBuffer"), tempObjectIndexOffsetBufferUAV);
                pass->SetProperty(NAME_ID("_OutObjectIndexBuffer"), objectIndexBufferUAV);
                pass->DispatchIndirect(renderPipelineSetting->InstanceCullingComputeShaderR, NameID("OutputCommandInstanceLists"), outputPassIndirectArgs, 0);
            }
        }
    }

    return std::make_tuple(cullingPass, objectIndexBufferSRV);
}

void InstanceCulling::AddReadBackDrawCulling(REDPass* cullingPass, REDDrawUnitList* drawUnitList, UInt64 frameId, bool feedBackDebug)
{
    if (!cullingPass)
        return;

    cullingPass->OnCulling([=](REDPass* pass) {
        auto UniqueDrawUnitKeyGen = [](const REDDrawUnit& drawUnit) {
            if (drawUnit.mType != RenderNodeType::Foliage  && drawUnit.mCustumData == nullptr)
            {
                return drawUnit.mStateBucketID;
            }

            UInt64 hashValue64 = 0;
            if (drawUnit.mType != RenderNodeType::Foliage)
            {
                hashValue64 = HashState(&drawUnit.mCustumData);
            }
            else
            {
                auto* foliageInfo = reinterpret_cast<const FoliageInfo*>(drawUnit.mCustumData);

                hashValue64 = HashState(&foliageInfo->RenderNode);
            }


            UInt32 hashValue32 = static_cast<UInt32>((hashValue64 >> 32u) ^ (hashValue64 & 0xFFFFFFFF));
            return hashValue32;
        };

        if (!drawUnitList->RecordDrawUnitMetaInfo(frameId, UniqueDrawUnitKeyGen))
        {
            return;
        }
        auto& PrevFrameCache = *drawUnitList->GetReadBackBuffer(frameId + 1);

        auto* drawUnits = drawUnitList->GetDrawUnits();

        int num_discarded = 0;
        int num_should_instance = 0;

        // Assert(PrevFrameCache.mReadbackData.size() / sizeof(CompactDrawCMD) == PrevFrameCache.mIndexMap.size());

        // for (UInt32 index = 0; index < PrevFrameCache.mReadbackData.size() / sizeof(CompactDrawCMD); index++)
        //{
        //     auto & cmd = reinterpret_cast<CompactDrawCMD*>(PrevFrameCache.mReadbackData.data())[index];
        //     LOG_INFO("frameID {} index{} {}", frameId + 1 - 3, index, cmd.instanceCount);
        // }

        for (UInt32 index = 0; index < drawUnits->GetSize(); index++)
        {
            const REDDrawUnit& drawUnit = (*drawUnits)[index];

            std::string value = "";
            auto key = UniqueDrawUnitKeyGen(drawUnit);
            if (PrevFrameCache.mIndexMap.count(key) != 0)
            {

                if (drawUnit.mType != RenderNodeType::Foliage && PrevFrameCache.mIndexMap[key].size() > 1)
                {
                    num_should_instance++;
                }

                size_t total_instance = 0;
                for (auto& itr : PrevFrameCache.mIndexMap[key])
                {
                    // auto itr = PrevFrameCache.mIndexMap[drawUnit.mStateBucketID][0];
                    if (PrevFrameCache.mReadbackData.size() > itr * sizeof(CompactDrawCMD))
                    {
                        auto& prev_draw_cmd = (reinterpret_cast<CompactDrawCMD*>(PrevFrameCache.mReadbackData.data()))[itr];
                        
                        total_instance += prev_draw_cmd.instanceCount;

                        // add this check, since if the indexCount ==0, means readback is invalid somehow (not be culled or correctly uploaded)
                        // TODO: should check why upload is incorect, all zero cases.
                        // so we should not rely on the instanceCount to check visible
                        // total_instance += prev_draw_cmd.indexCount == 0;
                    }
                }
                drawUnit.mFeedBackVisible = total_instance > 0;   
                if (total_instance <= 0)
                {
                    // LOG_INFO("Index{} {} Discarded, {} size {}", index, drawUnit.mStateBucketID, PrevFrameCache.mIndexMap[key][0], PrevFrameCache.mIndexMap[key].size());
                    num_discarded++;
                }

                // for (auto& itr : PrevFrameCache.mIndexMap[key])
                //{
                //     value += std::to_string(itr) + " ";
                // }
            }
        }

        //if (num_discarded == static_cast<int>(drawUnits->GetSize()))
        //{
        //    for (UInt32 index = 0; index < drawUnits->GetSize(); index++)
        //    {
        //        auto& prev_draw_cmd = (reinterpret_cast<CompactDrawCMD*>(PrevFrameCache.mReadbackData.data()))[index];
        //        LOG_INFO("Value {} {} {} {} {}", prev_draw_cmd.indexCount, prev_draw_cmd.instanceCount, prev_draw_cmd.firstIndex, prev_draw_cmd.vertexOffset, prev_draw_cmd.firstInstance);
        //    }
        //}

        if (feedBackDebug)
        {
            LOG_INFO("pass{} frameId {} Discard {}/{} ShouldInstance {} Index{} readBackSize {}",
                     cullingPass->GetName(),
                     frameId,
                     num_discarded,
                     drawUnits->GetSize(),
                     num_should_instance,
                     PrevFrameCache.mIndexMap.size(),
                     PrevFrameCache.mReadbackData.size() / sizeof(CompactDrawCMD));
        }

    });
}

void InstanceCulling::RequestForReadbackDraw(REDDrawUnitList* drawUnitList, UInt64 frameId)
{
    auto& drawUnitFeedBack = drawUnitList->GetReadBackBuffer(frameId)->mReadbackData;
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    rendererSystem->CopyReadBackBufferDynamic(drawUnitList->GetIndirectBuffer<REDBuffer*>(), {0, 0, 0}, &drawUnitFeedBack);
}

std::tuple<REDBuffer*, REDBufferView*, REDBufferView*> InstanceCulling::CreateUAVBuffer(RenderingExecutionDescriptor* RED, std::string_view name, UInt32 elementCount, UInt32 elementBytes, NGIBufferUsage additionalBufferUsage)
{
    NGIBufferUsage usage = NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer | additionalBufferUsage;

    REDBuffer* buffer = RED->AllocateBuffer(name, NGIBufferDesc{elementCount * elementBytes, usage});

    REDBufferView* bufferUAV = RED->AllocateBufferView(buffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0u, elementCount * elementBytes, GraphicsFormat::Unknown, elementBytes});
    REDBufferView* bufferSRV = RED->AllocateBufferView(buffer, NGIBufferViewDesc{NGIBufferUsage::StructuredBuffer, 0u, elementCount * elementBytes, GraphicsFormat::Unknown, elementBytes});

    return {buffer, bufferUAV, bufferSRV};
}

void InstanceCulling::ClearRWStructuredBuffer(RenderingExecutionDescriptor* RED, const FFSRenderPipelineSetting* renderPipelineSetting, REDBufferView* bufferView, UInt32 value)
{
    const SizeType bufferSize = bufferView->GetDesc().SizeInBytes;

    UInt4 clearParams(0, (UInt32)bufferSize / sizeof(UInt32), value, 0);

    auto* pass = RED->AllocatePass("ClearUAVBuffer");
    {
        pass->SetProperty(NameID("_ClearResource"), bufferView);
        pass->SetProperty(NameID("_ClearParams"), clearParams);

        UInt3 groupSize;
        renderPipelineSetting->mVirtualShadowMapSettings.ClearBufferUIntComputeShaderR->GetThreadGroupSize(NameID("ClearCS"), groupSize.x, groupSize.y, groupSize.z);   // temp
        pass->Dispatch(renderPipelineSetting->mVirtualShadowMapSettings.ClearBufferUIntComputeShaderR, NameID("ClearCS"), ((UInt32)bufferSize / sizeof(UInt32) + groupSize.x - 1) / groupSize.x, 1, 1);
    }
}

REDPass* InstanceCulling::ClearRWStructuredBuffer(RenderingExecutionDescriptor* RED, const FFSRenderPipelineSetting* renderPipelineSetting, REDBufferView* bufferView, UInt32 minOffset, UInt32 maxOffset, UInt32 value)
{
    UInt4 clearParams(minOffset, maxOffset, value, 0);

    auto* pass = RED->AllocatePass("ClearUAVBuffer");
    {
        pass->SetProperty(NameID("_ClearResource"), bufferView);
        pass->SetProperty(NameID("_ClearParams"), clearParams);

        UInt3 groupSize;
        renderPipelineSetting->mVirtualShadowMapSettings.ClearBufferUIntComputeShaderR->GetThreadGroupSize(NameID("ClearCS"), groupSize.x, groupSize.y, groupSize.z);   // temp
        pass->Dispatch(renderPipelineSetting->mVirtualShadowMapSettings.ClearBufferUIntComputeShaderR, NameID("ClearCS"), ((maxOffset - minOffset) + groupSize.x - 1) / groupSize.x, 1, 1);
    }

    return pass;
}
}   // namespace cross