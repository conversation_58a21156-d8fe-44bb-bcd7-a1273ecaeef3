#include "RayTracingScene.h"
#include "RenderEngine/EntityLifeCycleRenderDataSystemR.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/GPUScene/GPUSceneData.h"
#include "RenderEngine/AABBSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"
#include "RenderEngine/RenderMaterial.h"
#include "Resource/Shader.h"
#include "RenderEngine/RenderMesh.h"
#include "RenderEngine/ModelSystemR.h"
#include <ranges>
#include "CECommon/Common/FrameTickManager.h"
#include "Runtime/GameWorld/EntityMetaSystem.h"
#include "CameraSystemR.h"

#ifdef NGI_ENABLE_RAY_TRACING
namespace cross {

#pragma optimize("", off)
RayTracingScene::~RayTracingScene()
{
    EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(mAccelStruct);
    EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(mSingleTriangleBLAS);
    // NOTE(scolu): Assert mMeshBLASMap is empty since all entities are destroyed when the world is destroyed, and their corresponding BLASes are released via RemoveEntity.
    //Assert(mMeshBLASMap.empty());
    if (!mMeshBLASMap.empty())
    {
        LOG_WARN("%d entities haven't be destroyed with world, forcing cleanup!", mMeshBLASMap.size());
        for (auto& blas : mMeshBLASMap | std::views::values)
        {
            EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(blas);
        }
    }
}

void RayTracingScene::Initialize(RenderWorld* renderWorld, RenderingExecutionDescriptor* red, GPUScene* gpuScene)
{
    mRenderWorld = renderWorld;
    mRED = red;
    mGPUScene = gpuScene;
}

void RayTracingScene::SetFFSRenderPipelineSetting(const FFSRenderPipelineSetting* renderPipelineSetting)
{
    mFFSRenderPipelineSetting = renderPipelineSetting;
}

void RayTracingScene::Update()
{
    QUICK_SCOPED_CPU_TIMING("RayTracingScene::Update");
    
    // Step 1: Set up prerequisites
    {
        mRenderSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        mModelSystem = mRenderWorld->GetRenderSystem<ModelSystemR>();
        mTransformSystem = mRenderWorld->GetRenderSystem<TransformSystemR>();
        mEntityLifeCycleRenderDataSystem = mRenderWorld->GetRenderSystem<EntityLifeCycleRenderDataSystemR>();
        mMainCameraTilePosition = Float3(0.f, 0.f, 0.f);
        auto mainCamera = mRenderWorld->GetRenderSystem<CameraSystemR>()->GetMainCamera();
        if (mainCamera != ecs::EntityID::InvalidHandle())
        {
            mMainCameraTilePosition = mTransformSystem->GetTilePosition(mainCamera);
        }
        mCmd = mRenderSystem->GetAccelStructCmd();
    }
    
    // Step 2: Handle events
    {
        // Step 2.1: Handle entity create
        for (const auto& data :  mEntityLifeCycleRenderDataSystem->GetRenderEntityCreateList())
        {
            // TODO(scolu): cull sky sphere since it has huge impact on performance
            if (IsEntityValidForRayTracing(data.entity))
            {
                AddUpdateEntity(data.entity);
            }
        }

        // TODO(scolu): Handle skinned mesh and rebuilt it's blas
        
        // Step 2.2: Handle entity change
        // for (const auto& data : mEntityLifeCycleRenderDataSystem->GetRenderEntityChangeList())
        // {
        //     bool isCreateEvent = data.type == RenderEntityChangeData::ChangeType::All;
        //     if (IsEntityValidForRayTracing(data.entity))
        //     {
        //         AddUpdateEntity(data.entity);
        //     }
        // }
        
        // Step 2.3: Handle entity destroy
        // UInt32 destroyEventCount = mRenderWorld->GetRemainedEventCount<EntityDestroyEvent>(true);
        // for (UInt32 destroyEventIndex = 0; destroyEventIndex < destroyEventCount; destroyEventIndex++)
        // {
        //     const EntityDestroyEvent& destroyEvent = mRenderWorld->GetRemainedEvent<EntityDestroyEvent>(destroyEventIndex);
        //     if (static_cast<UInt32>(destroyEvent.mData.mEventFlag) & static_cast<UInt32>(EntityLifeCycleEventFlag::DestroyEntity) &&
        //         IsEntityValidForRayTracing(destroyEvent.mData.mEntityID))
        //     {
        //         RemoveEntity(destroyEvent.mData.mEntityID);
        //     }
        // }
    }
}

void RayTracingScene::PostUpdate() {}

void RayTracingScene::AddUpdateEntity(ecs::EntityID entity)
{
    mFrameUpdateEntitySet.insert(entity);
}

void RayTracingScene::RemoveEntity(ecs::EntityID entity)
{
    if (!ShouldReleaseBLAS(entity))
    {
        return;
    }
    MeshR* mesh = GetRenderMesh(entity);
    if (mMeshBLASMap.contains(mesh))
    {
        ReleaseMeshBLAS(mesh);
        mRayTracingInstances.erase(entity);
    }
}

void RayTracingScene::SetBindlessResources(REDPass* pass) const
{
    pass->SetProperty(BuiltInProperty::ce_AccelerationStructure, GetTopLevelAccelStruct());
    pass->SetProperty(BuiltInProperty::ce_SubInstanceData, mSubInstanceBufferView.get());
}

MeshR* RayTracingScene::GetRenderMesh(ecs::EntityID entity) const
{
    auto modelComp = mRenderWorld->GetComponent<ModelComponentR>(entity);
    if (!modelComp.IsValid())
    {
        LOG_ERROR("ModelComponentR is not available\n");
    }
    auto modelCompReader = modelComp.Read();
    constexpr UInt32 modelIndex = 0;
    const ModelComponentR::IndividualModel& model = mModelSystem->GetModel(modelCompReader, modelIndex);

    return mModelSystem->GetRenderMeshInternal(model);
}

bool RayTracingScene::IsEntityValidForRayTracing(ecs::EntityID entity) const
{
    if (!mRenderWorld->IsEntityAlive(entity) ||
        !mRenderWorld->HasComponent<ModelComponentR>(entity) ||
        !mRenderWorld->HasComponent<RenderNodeComponentR>(entity) ||
        !mRenderWorld->HasComponent<TransformComponentR>(entity))
    {
        return false;
    }

    auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(entity);
    if (renderNodeComp.Read()->mRenderNode->GetType() != RenderNodeType::Model)
    {
        return false;
    }

    MeshR* mesh = GetRenderMesh(entity);
    if (!mesh || mesh->GetState() != MeshR::State::Initialized)
    {
        return false;
    }

    return true;
}

bool RayTracingScene::ShouldReleaseBLAS(ecs::EntityID entity) const
{
    if (!mRenderWorld->HasComponent<ModelComponentR>(entity) || 
        !mRenderWorld->HasComponent<RenderNodeComponentR>(entity) || 
        !mRenderWorld->HasComponent<TransformComponentR>(entity))
    {
        return false;
    }

    auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(entity);
    if (renderNodeComp.Read()->mRenderNode->GetType() != RenderNodeType::Model)
    {
        return false;
    }

    MeshR* mesh = GetRenderMesh(entity);
    return mesh && mMeshRefCountMap.contains(mesh);
}

std::string_view RayTracingScene::GetEntityName(ecs::EntityID entity) const
{
    return "BLASDataBuffer";
}

void RayTracingScene::ReleaseMeshBLAS(MeshR* mesh)
{
    --mMeshRefCountMap[mesh];
    UInt32 refCount = mMeshRefCountMap[mesh];
    if (refCount == 0)
    {
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(mMeshBLASMap[mesh]);
        mMeshBLASMap.erase(mesh);
        mMeshRefCountMap.erase(mesh);
        LOG_INFO("Release BLAS");
    }
}

void RayTracingScene::UpdateSubInstanceData()
{
    mSubInstanceData.reserve(mSubInstanceCount);
    for (auto& entity : mRayTracingInstances)
    {
        MeshR* mesh = GetRenderMesh(entity);
        for (auto& renderGeometry : mesh->GetRenderGeometries())
        {
            SubInstanceData subInstanceData;
            // TODO(scolu): Assign geometry index and material index, for geometry could assign unique index when build mesh
            const GeometryData& geoData = renderGeometry.GetGeometryData();
            subInstanceData.PosBufferIndex = geoData.PosBufferIndex;
            subInstanceData.ColorBufferIndex = geoData.ColorBufferIndex;
            subInstanceData.NormalBufferIndex = geoData.NormalBufferIndex;
            subInstanceData.TangentBufferIndex = geoData.TangentBufferIndex;
            subInstanceData.BinormalBufferIndex = geoData.BinormalBufferIndex;
            subInstanceData.UVBufferIndex = geoData.UVBufferIndex;
            subInstanceData.UV1BufferIndex = geoData.UV1BufferIndex;
            subInstanceData.IndexBufferIndex = geoData.IndexBufferIndex;
            subInstanceData.MaterialIndex = 0;  // TODO(scolu)
            mSubInstanceData.push_back(subInstanceData);
        }
    }
    Assert(mSubInstanceCount == 1 || mSubInstanceCount == mSubInstanceData.size());

    // Upload SubInstanceBuffer
    {
        UInt32 subInstanceBufferSize = mSubInstanceCount * sizeof(SubInstanceData);
        NGIBufferDesc subInstanceBufferDesc{
            .Size = subInstanceBufferSize,
            .Usage = NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst,
        };
        mSubInstanceBuffer = mRenderSystem->GetRenderingExecutionDescriptor()->CreateBuffer("SubInstanceData Buffer", subInstanceBufferDesc);
        
        auto stagingBuffer = GetNGIDevicePtr()->CreateStagingBuffer(subInstanceBufferDesc);
        UInt8* dst = static_cast<UInt8*>(stagingBuffer->MapRange(NGIBufferUsage::CopyDst, 0, subInstanceBufferSize));
        memcpy(dst, mSubInstanceData.data(), subInstanceBufferSize);
        stagingBuffer->UnmapRange(0, subInstanceBufferSize);

        NGICopyBuffer region{
            .SrcOffset = 0,
            .DstOffset = 0,
            .NumBytes = subInstanceBufferSize};
        mRenderSystem->UpdateBuffer(mSubInstanceBuffer->GetNativeBuffer(), stagingBuffer, region, NGIResourceState::Undefined, NGIResourceState::RayTracingShaderShaderResource | NGIResourceState::ShaderStageBitMask);
        mRenderSystem->DestroyNGIObjectInAsyncThread(stagingBuffer);

        NGIBufferViewDesc subInstanceBufferViewDesc{
            .Usage = NGIBufferUsage::StructuredBuffer,
            .BufferLocation = 0,
            .SizeInBytes = subInstanceBufferSize,
            .StructureByteStride = sizeof(SubInstanceData)
        };
        mSubInstanceBufferView = mRenderSystem->GetRenderingExecutionDescriptor()->CreateBufferView(mSubInstanceBuffer.get(), subInstanceBufferViewDesc);
    }
}

void RayTracingScene::UpdateAccelStructs()
{
    // TODO(scolu): Rebuild Skinned Mesh BLAS
    BuildBLASes();
    // TODO(scolu): Compact BLASes
    BuildTLAS();
    mFrameUpdateEntitySet.clear();
}

void RayTracingScene::BuildTLAS()
{
    QUICK_SCOPED_CPU_TIMING("RayTracingScene::BuildTLAS");

    mCmd->BeginDebugRegion("BuildTLAS");
    
    std::vector<NGIInstanceDesc> instanceDescs;
    UInt32 subInstanceCount = 0;
    if (!mRayTracingInstances.empty())
    {
        for (auto& entity: mRayTracingInstances)
        {
            MeshR* mesh = GetRenderMesh(entity);
            subInstanceCount += mesh->GetGeometryCount();
        
            // continue if blas is not built
            if (!mMeshBLASMap.contains(mesh))
            {
                // TODO(scolu): since we haven't rebuilt skinned mesh per frame, may encounter this
                LOG_ERROR("Trying to use a inexistent BLAS");
                continue;
            }

            NGIInstanceDesc instanceDesc{};
            // Do preview translation for higher precision near camera
            auto transformComp = mRenderWorld->GetComponent<TransformComponentR>(entity);
            Float4x4 transform = mTransformSystem->GetWorldAbsoMatrixMatrix(transformComp.Read())->Transpose();
            Float3 previewTranslation = Float3(0.f, 0.f, 0.f);
#ifdef CE_USE_DOUBLE_TRANSFORM
            previewTranslation = mMainCameraTilePosition * LENGTH_PER_TILE;
            transform[3] -= previewTranslation.x;
            transform[7] -= previewTranslation.y;
            transform[11] -= previewTranslation.z;
#endif
            memcpy(instanceDesc.Transform, &transform, sizeof(float) * 12);
            instanceDesc.InstanceID = subInstanceCount;
            instanceDesc.InstanceMask = 0xff;
            instanceDesc.InstanceContribToHitGroupIndex = 0;  // TODO(scolu): set to determine hit group index
            instanceDesc.Flag = NGIInstanceFlag::ForceOpaque;
            instanceDesc.BottomLevelAS = mMeshBLASMap[mesh].get();
            instanceDescs.push_back(instanceDesc);
        }
    }
    else  // Build TLAS with a simple triangle when no valid instances are available
    {
        if (!mSingleTriangleBLAS)
        {
            mSingleTriangleIndexBuffer = mRED->CreateBuffer("SingleTriangleIndexBuffer",
                NGIBufferDesc{sizeof(UInt16) * 3, NGIBufferUsage::IndexBuffer});
            mSingleTriangleVertexBuffer = mRED->CreateBuffer("SingleTriangleVertexBuffer",
                NGIBufferDesc{sizeof(float) * 9, NGIBufferUsage::VertexBuffer});
            
            NGIAccelStructDesc blasDesc;
            blasDesc.IsTopLevel = false;
            blasDesc.BuildFlag = NGIAccelStructBuildFlag::PreferFastTrace;
            blasDesc.DebugName = "SingleTriangleBLAS";

            NGIGeometryDesc geoDesc{};
            geoDesc.GeometryType = NGIGeometryType::Triangle;
            NGIGeometryTriangle& triangles = geoDesc.GeometryData.Triangle;
            
            triangles.IndexBuffer = mSingleTriangleIndexBuffer->GetNativeBuffer();
            triangles.IndexFormat = IndexFormat_UInt16;
            triangles.IndexOffset = 0;
            triangles.IndexCount = 3;
            triangles.VertexBuffer = mSingleTriangleVertexBuffer->GetNativeBuffer();
            triangles.VertexFormat = GraphicsFormat::R32G32B32_SFloat;
            triangles.VertexOffset = 0;
            triangles.VertexStride = 12;
            triangles.VertexCount = 3;
            geoDesc.UseTransform = false;
            geoDesc.Flag = NGIGeometryFlag::Opaque;

            blasDesc.BottomLevelGeometries.push_back(geoDesc);

            NGIAccelStruct* as = mCmd->CreateAccelStruct(blasDesc);
            
            UInt64 scratchBufferSize = as->mSizeInfo.BuildScratchSize;
            NGIBufferDesc scratchBufferDesc{
                .Size = scratchBufferSize,
                .Usage = NGIBufferUsage::RayTracingScratchBuffer
            };
            auto [scratchBuffer, scratchBufferState] =
                mRenderSystem->GetTransientResourceManager()->AllocateBuffer(scratchBufferDesc, "BLASBuildScratchBuffer", true, false);
            mCmd->BuildBottomLevelAccelStruct(as,
                                              blasDesc.BottomLevelGeometries.data(),
                                              blasDesc.BottomLevelGeometries.size(),
                                              blasDesc.BuildFlag,
                                              scratchBuffer);
        }
        
        NGIInstanceDesc instanceDesc{};
        instanceDesc.InstanceID = 0;
        instanceDesc.InstanceMask = 0xff;
        instanceDesc.InstanceContribToHitGroupIndex = 0;
        instanceDesc.Flag = NGIInstanceFlag::ForceOpaque;
        instanceDesc.BottomLevelAS = mSingleTriangleBLAS.get();
        instanceDescs.push_back(instanceDesc);
        subInstanceCount = 1;
    }
    
    if (!instanceDescs.empty())
    {
        NGIAccelStructBuildFlag buildFlags = NGIAccelStructBuildFlag::PreferFastTrace | NGIAccelStructBuildFlag::AllowUpdate;

        UInt64 scratchBufferSize;
        if (!mAccelStruct || mAccelStruct->GetTopLevelMaxInstanceCount() != instanceDescs.size())
        {
            DestroyTLAS();
            CreateTLAS();
            scratchBufferSize = mAccelStruct->mSizeInfo.BuildScratchSize;
        }
        else
        {
            buildFlags |= NGIAccelStructBuildFlag::PerformUpdate;
            scratchBufferSize = mAccelStruct->mSizeInfo.UpdateScratchSize;
        }

        if (subInstanceCount != mSubInstanceCount)
        {
            LOG_ERROR("Mismatch {} != {}", subInstanceCount, mSubInstanceCount);
            // Assert(subInstanceCount == mSubInstanceCount);
        }
        
        NGIBufferDesc scratchBufferDesc{
            .Size = scratchBufferSize,
            .Usage = NGIBufferUsage::RayTracingScratchBuffer
        };
        auto [scratchBuffer, scratchBufferState] =
            mRenderSystem->GetTransientResourceManager()->AllocateBuffer(scratchBufferDesc,
                                                                     mAccelStruct ? "TLAS Update Scratch Buffer" : "TLAS Build Scratch Buffer",
                                                                     true,
                                                                     false);

        ScratchBufferWrap uploadInstanceBuffer =
            mRenderSystem->GetScratchBuffer()->AllocateScratch(NGIBufferUsage::AccelStructBuildInputBuffer, mAccelStruct->GetTopLevelUploadBufferSize());

        // Wait for blas build finish
        NGIMemoryBarrier barrier{.StateBefore = NGIResourceState::AccelStructWrite,
            .StateAfter = NGIResourceState::AccelStructBuildBLASBit | NGIResourceState::ShaderStageBitMask};
        mCmd->MemBarrier(&barrier);

        mCmd->BuildTopLevelAccelStructFromBuffer(mAccelStruct.get(),
                                                uploadInstanceBuffer,
                                                uploadInstanceBuffer.GetNGIOffset(),
                                                instanceDescs.data(),
                                                instanceDescs.size(),
                                                buildFlags,
                                                scratchBuffer);
        mCmd->TLASBarrier(mAccelStruct.get());

        // LOG_INFO("Instance Count {}", mInstanceCount);
        // std::cout << "SubmitTLASBuildCommand\n";
    }

    mCmd->EndDebugRegion();
}

void RayTracingScene::CreateTLAS()
{
    NGIAccelStructDesc tlasDesc;
    tlasDesc.IsTopLevel = true;
    tlasDesc.TopLevelMaxInstance = mInstanceCount;
    tlasDesc.BuildFlag = NGIAccelStructBuildFlag::AllowUpdate | NGIAccelStructBuildFlag::PreferFastTrace;
    tlasDesc.DebugName = "TLASDataBuffer";
    mAccelStruct.reset(mCmd->CreateAccelStruct(tlasDesc));
    
    UpdateSubInstanceData();
}

void RayTracingScene::DestroyTLAS()
{
    if (mAccelStruct)
    {
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(mAccelStruct);
        mAccelStruct = nullptr;
    }
    mSubInstanceData.clear();
}

void RayTracingScene::BuildBLASes()
{
    QUICK_SCOPED_CPU_TIMING("RayTracingScene::BuildBLASes");

    mCmd->BeginDebugRegion("BuildBLASes");
    
    for (auto& entity : mFrameUpdateEntitySet)
    {
        MeshR* mesh = GetRenderMesh(entity);

        if (!mRayTracingInstances.contains(entity))
        {
            mRayTracingInstances.insert(entity);
            // If mesh blas is already created, increase ref count and continue
            if (mMeshBLASMap.contains(mesh))
            {
                mMeshRefCountMap[mesh]++;
                continue;
            }
            mMeshRefCountMap[mesh] = 1;
        }
        else  // entity already processed
        {
            // TODO(scolu): if skinned Mesh, rebuilt without increase ref count
            continue;
        }

        NGIAccelStructDesc blasDesc;
        blasDesc.IsTopLevel = false;
        blasDesc.BuildFlag = NGIAccelStructBuildFlag::PreferFastTrace;
        blasDesc.DebugName = GetEntityName(entity);

        for (auto& geo : mesh->GetRenderGeometries())
        {
            NGIGeometryDesc geoDesc{};
            geoDesc.GeometryType = NGIGeometryType::Triangle;
            NGIGeometryTriangle& triangles = geoDesc.GeometryData.Triangle;
            
            GeometryPacket* geoPacket = geo.GetGeometryPacket();
            if (!geoPacket)
            {
                Assert(false);
                continue;
            }
            triangles.IndexBuffer = geoPacket->GetIndexStream()->GetGpuBuffer();
            triangles.IndexFormat = geoPacket->GetIndexFormat();
            UInt32 indexSizeInBytes = triangles.IndexFormat == IndexFormat_UInt16 ? 2 : 4;
            triangles.IndexOffset = geoPacket->GetIndexStream()->GetStreamOffset() + geo.GetIndexStart() * indexSizeInBytes;
            triangles.IndexCount = geo.GetIndexCount();

            // Traverse to find position vertex buffer stream
            bool findPosStream = false;
            for (UInt8 streamIndex = 0; streamIndex < geoPacket->GetStreamCount(); streamIndex++)
            {
                const VertexStreamLayout& streamLayout = geoPacket->GetInputLayout().GetVertexStreamLayout(streamIndex);
                // One channel per stream for bindless access
                Assert(streamLayout.GetChannelCount() == 1);
                const VertexChannelLayout& channelLayout = streamLayout.GetChannelLayout(0);
                if (channelLayout.mChannelName == VertexChannel::Position0 && channelLayout.mFormat == VertexFormat::Float3)
                {
                    triangles.VertexBuffer = geoPacket->GetVertexStream(streamIndex)->GetGpuBuffer();
                    triangles.VertexFormat = GraphicsFormat::R32G32B32_SFloat;
                    triangles.VertexOffset = geo.GetVertexStart() * streamLayout.GetVertexStride();
                    triangles.VertexStride = streamLayout.GetVertexStride();
                    triangles.VertexCount = geo.GetVertexCount();
                    findPosStream = true;
                    break;
                }
            }
            geoDesc.UseTransform = false;
            geoDesc.Flag = NGIGeometryFlag::Opaque;
            
            if (findPosStream)
            {
                blasDesc.BottomLevelGeometries.push_back(geoDesc);
            }
            else
            {
                LOG_WARN("Can't find position channel in GeoPacket, skip submehs blas build!");
            }
        }

        if (!blasDesc.BottomLevelGeometries.empty())
        {
            NGIAccelStruct* as = mCmd->CreateAccelStruct(blasDesc);

            // Build BLAS
            //  TODO(scolu): further pack blases build
            {
                UInt64 scratchBufferSize = as->mSizeInfo.BuildScratchSize;
                NGIBufferDesc scratchBufferDesc{
                    .Size = scratchBufferSize,
                    .Usage = NGIBufferUsage::RayTracingScratchBuffer
                };
                auto [scratchBuffer, scratchBufferState] =
                    mRenderSystem->GetTransientResourceManager()->AllocateBuffer(scratchBufferDesc, "BLAS Build Scratch Buffer", true, false);
                mCmd->BuildBottomLevelAccelStruct(as,
                                                  blasDesc.BottomLevelGeometries.data(),
                                                  blasDesc.BottomLevelGeometries.size(),
                                                  blasDesc.BuildFlag,
                                                  scratchBuffer);
            }
            
            mMeshBLASMap[mesh].reset(as);
            mesh->SetBLAS(mMeshBLASMap[mesh].get());
        }

        // std::cout << "SubmitBLASBuildCommand\n";
    }

    {
        mInstanceCount = static_cast<UInt32>(mRayTracingInstances.size());
        mSubInstanceCount = 0;
        if (mRayTracingInstances.empty())
        {
            mInstanceCount = 1;
            mSubInstanceCount = 1;
        }
        else
        {
            for (auto& entity : mRayTracingInstances)
            {
                MeshR* mesh = GetRenderMesh(entity);
                mSubInstanceCount += mesh->GetGeometryCount();
            }
        }
    }

    mCmd->EndDebugRegion();

    // TODO(scolu): Optimize BLAS construction - either parallelize build commands or batch multiple builds (preferred).
    //   Validation needed for both approaches.
    // threading::ParallelFor(static_cast<SInt32>(mGPUScene->GetUpdateEntities().size()), [&](auto i)
    // {
    //     auto& entity = mGPUScene->GetUpdateEntities()[i];
    //     auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(entity);
    //     RenderNode* renderNode = renderNodeComp.Read()->mRenderNode.get();
    //     
    //     if (ModelRenderNode* modelRenderNode = dynamic_cast<ModelRenderNode*>(renderNode); modelRenderNode)
    //     {
    //         ModelRenderNode::SingleLODModel& rayTracingModel = modelRenderNode->GetRenderModel().mLODModels[0];
    //         
    //         NGIAccelStructDesc blasDesc;
    //         blasDesc.IsTopLevel = false;
    //         blasDesc.BuildFlag = NGIAccelStructBuildFlag::PreferFastTrace;
    //         // TODO(scolu): Give a debug name
    // //        blasDesc.DebugName = modelCompReader.
    //         
    //         for (auto& subModel : rayTracingModel.mSubModels)
    //         {
    //             NGIGeometryDesc geoDesc{};
    //             geoDesc.GeometryType = NGIGeometryType::Triangle;
    //             NGIGeometryTriangle& triangles = geoDesc.GeometryData.Triangle;
    //
    //             RenderGeometry* geo = subModel.mGeometry;
    //
    //             constexpr UInt32 stream = 0;
    //             GeometryPacket* geoPacket = geo->GetGeometryPacket();
    //             if (!geoPacket)
    //             {
    //                 continue;
    //             }
    //             triangles.IndexBuffer = geoPacket->GetIndexStream()->GetGpuBuffer();
    //             triangles.IndexFormat = geoPacket->GetIndexFormat();
    //             UInt32 indexSizeInBytes = triangles.IndexFormat == IndexFormat_UInt16 ? 2 : 4;
    //             triangles.IndexOffset = geoPacket->GetIndexStream()->GetStreamOffset() + geo->GetIndexStart() * indexSizeInBytes;
    //             triangles.IndexCount = geo->GetIndexCount();
    //
    //             const VertexStreamLayout& vertexStreamLayout = geoPacket->GetInputLayout().GetVertexStreamLayout(stream);
    //             triangles.VertexBuffer = geoPacket->GetVertexStream(stream)->GetGpuBuffer();
    //             triangles.VertexFormat = GraphicsFormat::R32G32B32_SFloat;
    //             triangles.VertexOffset = geoPacket->GetVertexStream(stream)->GetStreamOffset() +
    //                                      geo->GetVertexStart() * vertexStreamLayout.GetVertexStride();
    //             triangles.VertexStride = vertexStreamLayout.GetVertexStride();
    //             triangles.VertexCount = geo->GetVertexCount();
    //             
    //             geoDesc.UseTransform = false;
    //             geoDesc.Flag = NGIGeometryFlag::Opaque;
    //
    //             blasDesc.BottomLevelGeometries.push_back(geoDesc);
    //         }
    //         
    //         NGIAccelStruct* as = cmd->CreateAccelStruct(blasDesc);
    //         cmd->BuildBottomLevelAccelStruct(as, blasDesc.BottomLevelGeometries.data(), blasDesc.BottomLevelGeometries.size(),
    //                                          blasDesc.BuildFlag);
    //         // mesh->SetBLAS(as);
    //     }
    // });
}
#pragma optimize("", on)

}
#endif