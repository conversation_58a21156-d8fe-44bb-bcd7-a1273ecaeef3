#include "BindlessResourceManager.h"
#include "Resource/IResourceInterface.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "CECommon/Common/EngineGlobal.h"
#include <algorithm>


namespace cross {

BindlessResourceManager::BindlessResourceManager()
{
    mMeshIndexAllocator            = std::make_shared<ThreadSafeIndexAllocator>("MeshIndexAllocator");
    mGeometryIndexAllocator        = std::make_shared<ThreadSafeIndexAllocator>("GeometryIndexAllocator");
    mMaterialIndexAllocator        = std::make_shared<ThreadSafeIndexAllocator>("MaterialIndexAllocator");
    mTextureIndexAllocator         = std::make_shared<ThreadSafeIndexAllocator>("TextureIndexAllocator");
    mBindlessBufferIndexAllocator  = std::make_shared<ThreadSafeIndexAllocator>("BindlessBufferIndexAllocator");
    
    CreateBindlessResourceGroup(8192);
}

BindlessResourceManager& BindlessResourceManager::Instance()
{
    static BindlessResourceManager sInstance;
    return sInstance;
}

#pragma optimize("", off)
void BindlessResourceManager::OnMeshBuild(IMeshR* mesh)
{
    auto meshR = dynamic_cast<MeshR*>(mesh);
    if (meshR)
    {
#ifdef NGI_ENABLE_RAY_TRACING
        UInt32 subMeshCount = meshR->GetGeometryCount();
        if (subMeshCount > 0)
        {
            // Allocate n continuous geometry index
            UInt32 firstGeoIndex = mGeometryIndexAllocator->Allocate(subMeshCount);
            for (UInt32 localGeoIndex = 0; localGeoIndex < subMeshCount; localGeoIndex++)
            {
                auto& geo = meshR->GetRenderGeometry(localGeoIndex);
                UInt32 globalGeoIndex = firstGeoIndex + localGeoIndex;
                geo.SetGeometryIndex(globalGeoIndex);
            }
        }

        for (UInt32 localGeoIndex = 0; localGeoIndex < meshR->GetGeometryCount(); localGeoIndex++)
        {
            auto& geo = meshR->GetRenderGeometry(localGeoIndex);
            GeometryPacket* geoPacket = geo.GetGeometryPacket();
            CONTINUE_IF(geoPacket);
            
            GeometryData geoData{};
            for (UInt8 streamIndex = 0; streamIndex < geoPacket->GetStreamCount(); streamIndex++)
            {
                auto streamLayout = geoPacket->GetInputLayout().GetVertexStreamLayout(streamIndex);
                // Assert(streamLayout.GetChannelCount() == 1);
                CONTINUE_IF(streamLayout.GetChannelCount() != 1);
                const BufferStream* bufferStream = geoPacket->GetVertexStream(streamIndex);
                Assert(bufferStream->GetGpuBuffer() && bufferStream->GetStreamOffset() == 0);
                ProcessVertexChannel(streamLayout, bufferStream, geoData);
            }
            
            auto indexStream = geoPacket->GetIndexStream();
            NGIBuffer* indexBuffer = geoPacket->GetIndexStream()->GetGpuBuffer();
            NGIBufferViewDesc indexBufferViewDesc{
                .Usage = NGIBufferUsage::TexelBuffer,
                .BufferLocation = indexStream->GetStreamOffset(),
                .SizeInBytes = indexStream->GetStreamSize(),
                .Format = geoPacket->Is16BitIndex() ? GraphicsFormat::R16_UInt : GraphicsFormat::R32_UInt,
            };
            UInt32 indexBufferIndex = mBindlessBufferIndexAllocator->Allocate();
            geoData.IndexBufferIndex = indexBufferIndex;
            std::unique_ptr<NGIBufferView> indexBufferView = std::unique_ptr<NGIBufferView>{GetNGIDevicePtr()->CreateBufferView(indexBuffer, indexBufferViewDesc)};
            Assert(indexBufferView);
            {
                std::lock_guard locker(mUpdateBindlessResourceGroupMutex);
                auto rawIndexBufferView = indexBufferView.get();
                mBindlessResourceGroup->SetBuffers(BuiltInProperty::ce_BindlessUIntBuffers, indexBufferIndex, 1, &rawIndexBufferView);
                Assert(!mTrackedBindlessBufferViews.contains(indexBufferIndex));
                mTrackedBindlessBufferViews[indexBufferIndex] = std::move(indexBufferView);
            }

            geo.SetGeometryData(std::move(geoData));
        }
#endif
    }
}

UInt32 BindlessResourceManager::AllocateMeshIndex(UInt32 count)
{
    // TODO: why mMeshIndex allocated may be useless?
    auto id = 0xFFFFFFFFu;
    do
    {
        id = mMeshIndexAllocator->Allocate(count);
    } while (id == 0xFFFFFFFFu);
    Assert(id != 0xFFFFFFFFu);
    return id;
}

void BindlessResourceManager::ReleaseMeshIndex(UInt32 index, UInt32 count)
{
    return mMeshIndexAllocator->Release(index, count);
}

UInt32 BindlessResourceManager::AllocateBindlessBufferView(NGIBuffer* buffer, GraphicsFormat format, SizeType size, UInt32 offset)
{
    if (size == 0)
    {
        return CE_BINDLESS_INVALID_INDEX;
    }

    NGIBufferViewDesc vertexBufferViewDesc{
        .Usage = NGIBufferUsage::TexelBuffer,
        .BufferLocation = offset,
        .SizeInBytes = size,
        .Format = format,
    };

    UInt32 index = mBindlessBufferIndexAllocator->Allocate();
    std::unique_ptr<NGIBufferView> bufferView = std::unique_ptr<NGIBufferView>{GetNGIDevicePtr()->CreateBufferView(buffer, vertexBufferViewDesc)};
    {
        std::lock_guard locker(mUpdateBindlessResourceGroupMutex);
        auto rawBufferView = bufferView.get();
        switch (format & (GraphicsFormat::CompTypeUInt | GraphicsFormat::CompTypeSInt | GraphicsFormat::CompTypeSFloat | GraphicsFormat::CompTypeUNorm | GraphicsFormat::CompTypeSNorm))
        {
        case GraphicsFormat::CompTypeUInt:
            mBindlessResourceGroup->SetBuffers(BuiltInProperty::ce_BindlessUIntBuffers, index, 1, &rawBufferView);
            break;
        case GraphicsFormat::CompTypeSInt:
            mBindlessResourceGroup->SetBuffers(BuiltInProperty::ce_BindlessIntBuffers, index, 1, &rawBufferView);
            break;
        case GraphicsFormat::CompTypeSFloat:
        case GraphicsFormat::CompTypeUNorm:
        case GraphicsFormat::CompTypeSNorm:
            mBindlessResourceGroup->SetBuffers(BuiltInProperty::ce_BindlessFloatBuffers, index, 1, &rawBufferView);
            break;
        default:
            Assert(false);
        }
        Assert(!mTrackedBindlessBufferViews.contains(index));
        mTrackedBindlessBufferViews[index] = std::move(bufferView);
    }

    return index;
}

void BindlessResourceManager::ReleaseBindlessBufferView(UInt32 bufferIndex, UInt32 count)
{
    if (bufferIndex == CE_BINDLESS_INVALID_INDEX)
        return;
    Assert(mTrackedBindlessBufferViews.contains(bufferIndex));
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    {
        std::lock_guard locker(mReleaseTrackedBindlessBufferViewMutex);
        rendererSystem->DestroyNGIObject(std::move(mTrackedBindlessBufferViews[bufferIndex]));
        mTrackedBindlessBufferViews.erase(bufferIndex);
    }
    mBindlessBufferIndexAllocator->Release(bufferIndex, count);
}

void BindlessResourceManager::OnMeshRelease(IMeshR* mesh)
{
#ifdef NGI_ENABLE_RAY_TRACING
    MeshR* meshR = dynamic_cast<MeshR*>(mesh);
    if (meshR && meshR->GetGeometryCount() > 0)
    {
        UInt32 subMeshCount = meshR->GetGeometryCount();
        UInt32 firstGeoIndex = meshR->GetRenderGeometry(0).GetGeometryIndex();

        // Skip mesh render geometries that are not created by GetMeshR e.g.PrimitiveRenderSystem
        if (firstGeoIndex == 0xffffffff)
        {
            return;
        }
        // Release geometry data index and buffer views
        for (UInt32 localGeoIndex = 0; localGeoIndex < subMeshCount; localGeoIndex++)
        {
            const GeometryData& geoData = meshR->GetRenderGeometry(localGeoIndex).GetGeometryData();

            for (UInt32 bufferIndex : {
                geoData.PosBufferIndex,
                geoData.ColorBufferIndex,
                geoData.NormalBufferIndex,
                geoData.TangentBufferIndex,
                geoData.BinormalBufferIndex,
                geoData.UVBufferIndex,
                geoData.UV1BufferIndex,
                geoData.IndexBufferIndex
            })
            {
                if (bufferIndex != CE_BINDLESS_INVALID_INDEX)
                {
                    // ReleaseBindlessBufferView(bufferIndex);
                }
            }
        }
        
        // Release geometry index
        mGeometryIndexAllocator->Release(firstGeoIndex, subMeshCount);
    }
#endif
}
#pragma optimize("", on)

void BindlessResourceManager::AddMaterial(IMaterialR* material)
{
    UInt32 matIndex = mMaterialIndexAllocator->Allocate();
    material->SetMaterialIndex(matIndex);

    //if (mMaterials.size() <= matIndex)
    //{
    //    size_t newSize = std::max(matIndex + 1, static_cast<UInt32>(mMaterials.size() * 2));
    //    mMaterials.resize(newSize);
    //}
    //// TODO(scolu): Read material
    //mMaterials[matIndex] = {
    //    .BaseColor = {.5f, .5f, .5f},
    //};
}

void BindlessResourceManager::AddTexture(IGPUTexture* tex)
{
    UInt32 texIndex = mTextureIndexAllocator->Allocate();

    //if (mTextureResources.size() <= texIndex)
    //{
    //    size_t newSize = std::max(texIndex + 1, static_cast<UInt32>(mTextureResources.size() * 2));
    //    mTextureResources.resize(newSize);
    //}
    //mTextureResources[texIndex] = tex;
}

void BindlessResourceManager::CreateBindlessResourceGroup(UInt32 maxBindlessResourceCount)
{
    constexpr int BindlessResourcesCount = 3;
    std::array<NGIResourceDesc, BindlessResourcesCount> bindlessResources;

    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* groupPool = rendererSystem->GetResourceGroupPool();

    bindlessResources[0] = {
        .ID = BuiltInProperty::ce_BindlessFloatBuffers,
        .Type = CrossSchema::ShaderResourceType::TexelBuffer,
        .Space = ShaderParamGroup_Bindless,
        .ArraySize = 0,  // 0 indicates for bindless
        .StageMask = ToUnderlying(CrossSchema::ShaderStageBit::All)
    };
    bindlessResources[1] = {
        .ID = BuiltInProperty::ce_BindlessUIntBuffers,
        .Type = CrossSchema::ShaderResourceType::TexelBuffer,
        .Space = ShaderParamGroup_Bindless,
        .ArraySize = 0,  // 0 indicates for bindless
        .StageMask = ToUnderlying(CrossSchema::ShaderStageBit::All)
    };
    bindlessResources[2] = {
        .ID = BuiltInProperty::ce_BindlessIntBuffers,
        .Type = CrossSchema::ShaderResourceType::TexelBuffer,
        .Space = ShaderParamGroup_Bindless,
        .ArraySize = 0,  // 0 indicates for bindless
        .StageMask = ToUnderlying(CrossSchema::ShaderStageBit::All)
    };

    // Create resource group layout(descriptor set layout)
    NGIResourceGroupLayoutDesc bindlessResourceGroupLayoutDesc{
        .ResourceCount = BindlessResourcesCount,
        .Resources = bindlessResources.data()
    };
    mBindlessResourceGroupLayout.reset(gResourceMgr.mCreateRenderObjectMgr->AllocateResourceGroupLayout(bindlessResourceGroupLayoutDesc));
    // Create resource group(descriptor set)
    // TODO(scolu): Set a reasonable max resource count
    mBindlessResourceGroup.reset(groupPool->CreateBindlessResourceGroup(mBindlessResourceGroupLayout.get(), NGIBindlessResourceType::Buffer, maxBindlessResourceCount));
}

void BindlessResourceManager::ProcessVertexChannel(const VertexStreamLayout& streamLayout, const BufferStream* bufferStream, GeometryData& geoData)
{
    UInt32 bufferIndex = CE_BINDLESS_INVALID_INDEX;
    NGIBufferViewDesc bufferViewDesc{
        .Usage = NGIBufferUsage::TexelBuffer,
        .BufferLocation = 0,
        .SizeInBytes = bufferStream->GetStreamSize()
    };
    NGIDevice* device = GetNGIDevicePtr();
    NGIBuffer* gpuBuffer = bufferStream->GetGpuBuffer();
    auto vertexChannel = streamLayout.GetChannelLayout(0);
    std::unique_ptr<NGIBufferView> bufferView = nullptr;
    
    switch (vertexChannel.mChannelName)
    {
    case VertexChannel::Position0:
    {
        bufferIndex = mBindlessBufferIndexAllocator->Allocate();
        geoData.PosBufferIndex = bufferIndex;
        break;
    }
    case VertexChannel::Color0:
    {
        bufferIndex = mBindlessBufferIndexAllocator->Allocate();
        geoData.ColorBufferIndex = bufferIndex;
        break;
    }
    case VertexChannel::Normal0:
    {
        bufferIndex = mBindlessBufferIndexAllocator->Allocate();
        geoData.NormalBufferIndex = bufferIndex;
        break;
    }
    case VertexChannel::Tangent0:
    {
        bufferIndex = mBindlessBufferIndexAllocator->Allocate();
        geoData.TangentBufferIndex = bufferIndex;
        break;
    }
    case VertexChannel::BiNormal0:
    {
        bufferIndex = mBindlessBufferIndexAllocator->Allocate();
        geoData.BinormalBufferIndex = bufferIndex;
        break;
    }
    case VertexChannel::TexCoord0:
    {
        bufferIndex = mBindlessBufferIndexAllocator->Allocate();
        geoData.UVBufferIndex = bufferIndex;
        break;
    }
    case VertexChannel::TexCoord1:
    {
        bufferIndex = mBindlessBufferIndexAllocator->Allocate();
        geoData.UV1BufferIndex = bufferIndex;
        break;
    }
    default:
    {
        // Pass untracked channels
        break;
    }
    }

    if (bufferIndex == CE_BINDLESS_INVALID_INDEX)
    {
        return;
    }

    auto threadSafeUpdateBindlessBufferViews = [this, &bufferIndex, &bufferView](const NameID& Prop) {
        std::lock_guard locker(mUpdateBindlessResourceGroupMutex);
        Assert(!mTrackedBindlessBufferViews.contains(bufferIndex) && bufferView);
        auto rawBufferView = bufferView.get();
        mBindlessResourceGroup->SetBuffers(Prop, bufferIndex, 1, &rawBufferView);
        mTrackedBindlessBufferViews[bufferIndex] = std::move(bufferView);
    };
    
    switch (vertexChannel.mFormat)
    {
    case VertexFormat::Float:
    {
        bufferViewDesc.Format = GraphicsFormat::R32_SFloat;
        bufferView = std::unique_ptr<NGIBufferView>{device->CreateBufferView(gpuBuffer, bufferViewDesc)};
        threadSafeUpdateBindlessBufferViews(BuiltInProperty::ce_BindlessFloatBuffers);
        break;
    }
    case VertexFormat::Float2:
    {
        bufferViewDesc.Format = GraphicsFormat::R32G32_SFloat;
        bufferView = std::unique_ptr<NGIBufferView>{device->CreateBufferView(gpuBuffer, bufferViewDesc)};
        threadSafeUpdateBindlessBufferViews(BuiltInProperty::ce_BindlessFloatBuffers);
        break;
    }
    case VertexFormat::Float3:
    {
        bufferViewDesc.Format = GraphicsFormat::R32G32B32_SFloat;
        bufferView = std::unique_ptr<NGIBufferView>{device->CreateBufferView(gpuBuffer, bufferViewDesc)};
        threadSafeUpdateBindlessBufferViews(BuiltInProperty::ce_BindlessFloatBuffers);
        break;
    }
    case VertexFormat::Float4:
    {
        bufferViewDesc.Format = GraphicsFormat::R32G32B32A32_SFloat;
        bufferView = std::unique_ptr<NGIBufferView>{device->CreateBufferView(gpuBuffer, bufferViewDesc)};
        threadSafeUpdateBindlessBufferViews(BuiltInProperty::ce_BindlessFloatBuffers);
        break;
    }
    case VertexFormat::Byte3_Norm:
    {
        Assert(false);  // Texel buffer doesn't support GraphicsFormat::R8G8B8_SNorm, convert to Byte4_Norm instead
        break;
    }
    case VertexFormat::Byte4_Norm:
    {
        bufferViewDesc.Format = GraphicsFormat::R8G8B8A8_SNorm;
        bufferView = std::unique_ptr<NGIBufferView>{device->CreateBufferView(gpuBuffer, bufferViewDesc)};
        threadSafeUpdateBindlessBufferViews(BuiltInProperty::ce_BindlessFloatBuffers);
        break;
    }
    case VertexFormat::UByte3_Norm:
    {
        Assert(false);
        break;
    }
    case VertexFormat::UByte4_Norm:
    {
        bufferViewDesc.Format = GraphicsFormat::R8G8B8A8_UNorm;
        bufferView = std::unique_ptr<NGIBufferView>{device->CreateBufferView(gpuBuffer, bufferViewDesc)};
        threadSafeUpdateBindlessBufferViews(BuiltInProperty::ce_BindlessFloatBuffers);
        break;
    }
    case VertexFormat::Half2:
    {
        bufferViewDesc.Format = GraphicsFormat::R16G16_UNorm;
        bufferView = std::unique_ptr<NGIBufferView>{device->CreateBufferView(gpuBuffer, bufferViewDesc)};
        threadSafeUpdateBindlessBufferViews(BuiltInProperty::ce_BindlessFloatBuffers);
        break;
    }
    case VertexFormat::Short2_Norm:
    {
        bufferViewDesc.Format = GraphicsFormat::R16G16_SNorm;
        bufferView = std::unique_ptr<NGIBufferView>{device->CreateBufferView(gpuBuffer, bufferViewDesc)};
        threadSafeUpdateBindlessBufferViews(BuiltInProperty::ce_BindlessFloatBuffers);
        break;
    }
    default:
    {
        Assert(false);
        break;
    }
    }
}

}
