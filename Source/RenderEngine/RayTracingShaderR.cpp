#include "RayTracingShaderR.h"
#include "ComputeShaderR.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderEngine.h"
#include <ranges>


namespace cross {

RayTracingShaderR::~RayTracingShaderR()
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    for (auto& pso : mPipelineStates | std::views::values)
    {
        rendererSystem->DestroyNGIObject(std::move(pso));
    }
}

}
