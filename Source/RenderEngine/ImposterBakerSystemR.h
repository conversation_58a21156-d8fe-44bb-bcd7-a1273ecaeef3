#pragma once
#include "CECommon/Common/RenderSystemBase.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RenderEngineForward.h"

namespace cross {
struct ImposterBakerComponentR final : ecs::IComponent
{
    CEFunction(Reflect) RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();

protected:
    friend class ImposterBakerSystemR;
};

typedef std::array<NGITexture*, 2> ImposterTextureArray;
class RenderCamera;

class ImposterBakerSystemR : public RenderSystemBase
{
    CEMetaInternal(Reflect)
        
    using ImposterBakerComponentH = ecs::ComponentHandle<ImposterBakerComponentR>;

public:
    RENDER_ENGINE_API static ImposterBakerSystemR* CreateInstance();

    virtual void Release() override;

    virtual void OnBeginFrame(FrameParam* frameParam) override;

    virtual void OnEndFrame(FrameParam* frameParam) override;

    CEFunction(Reflect)
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

    RENDER_ENGINE_API UInt32 GetCurrentBakeFrame() const
    {
        return mCurrentBakeFrame2;
    }

    RENDER_ENGINE_API void SetCurrentBakeFrame(UInt32 currentBakeFrame)
    {
        mCurrentBakeFrame = currentBakeFrame;
        ++mCurrentBakeFrame2;
    }

    RENDER_ENGINE_API bool GetBakeTextureEnable() const 
    {
        return mEnableBakeTexture;
    }

    RENDER_ENGINE_API void SetBakeTextureEnable(bool enable)
    {
        mEnableBakeTexture = enable;
        if (!enable)
        {
            mCurrentBakeFrame2 = 0;
        }
    }

    RENDER_ENGINE_API ImposterTextureArray& GetImposterRenderTexture()
    {
        return mImposterTextures;
    }

    RENDER_ENGINE_API void SetImposterRenderTexture(ImposterTextureArray imposterTextures)
    {
        mImposterTextures = std::move(imposterTextures);
    }

    RENDER_ENGINE_API ecs::EntityID GetRenderCameraID()
    {
        return mRenderCameraID;
    }

    RENDER_ENGINE_API const RenderCamera* GetRenderCamera()
    {
        return mRenderCamera;
    }

    RENDER_ENGINE_API void SetRenderCamera(ecs::EntityID renderCamera);

    RENDER_ENGINE_API const std::vector<Float3>& GetViewCaptureVec()
    {
        return mViewCaptureVec;
    }

    RENDER_ENGINE_API void SetViewCaptureVec(std::vector<Float3> viewCaptureVec) 
    {
        mViewCaptureVec = viewCaptureVec;
    }

    RENDER_ENGINE_API const Float3& GetCenter()
    {
        return mCenter;
    }

    RENDER_ENGINE_API void SetCenter(Float3 center)
    {
        mCenter = center;
    }

    RENDER_ENGINE_API float GetRadius()
    {
        return mRadius;
    }

    RENDER_ENGINE_API void SetRadius(float radius)
    {
        mRadius = radius;
    }

public:
protected:
    ImposterBakerSystemR();

    ~ImposterBakerSystemR();

private:
    ImposterTextureArray mImposterTextures;
    UInt32 mCurrentBakeFrame;
    UInt32 mCurrentBakeFrame2 = 0;
    std::vector<Float3> mViewCaptureVec;
    Float3 mCenter;
    float mRadius;
    bool mEnableBakeTexture = false;
    ecs::EntityID mRenderCameraID;
    const RenderCamera* mRenderCamera = nullptr;
};

}   // namespace cross
