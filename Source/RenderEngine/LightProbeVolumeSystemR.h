#pragma once
#include "CECommon/Common/RenderSystemBase.h"
#include "CrossBase/Math/CrossMath.h"
#include "RenderEngine/RenderWorldConst.h"
#include "RenderEngine/RenderWorld.h"
#include "CECommon/Common/FrameContainer.h"
#include "RenderEngine/RenderGeometry.h"
#include "RenderEngine/RenderContext.h"
#include "RenderEngine/LightProbeCache.h"

namespace cross {
class MaterialR;
class FrameAllocator;
class LightProbeVolumeSystemR;

struct LightProbeVolumeComponentR : ecs::IComponent
{
    CEFunction(Reflect)
    RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();

private:
    std::string mCacheFile;
    std::unique_ptr<LightProbeCache> mCacheData{nullptr};
    std::unique_ptr<VolumetricLightmapData> mVLMData{nullptr};
    bool mEnable{true};
    bool mUseVolumetricLightMap{false};
    float mVLMReflectionProbeIntensity{0.0f};
    float mVLMReflectionProbeAOIntensity{0.0f};

    friend class LightProbeVolumeSystemR;
};

class LightProbeVolumeSystemR : public RenderSystemBase
{
    CEMetaInternal(Reflect) 
    using LightProbeVolumeComponentH = ecs::ComponentHandle<LightProbeVolumeComponentR>;
    using LightProbeVolumeComponentReader = ecs::ScopedComponentRead<LightProbeVolumeComponentR>;

public:
    RENDER_ENGINE_API static LightProbeVolumeSystemR* CreateInstance();

    RENDER_ENGINE_API virtual void Release() override;

    CEFunction(Reflect)
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

public:
    bool QueryLightProbeNode(const cross::Float3A& worldPos, LightProbeNode& outBlendNode);
    const VolumetricLightmapData* QueryVLMData(const cross::Float3A& worldPos, float& VLMReflectionProbeIntensity, float& VLMReflectionProbeAOIntensity);

public:
#define LIGHT_PROBE_VOLUME_COMPONENT_R_PROPERTY(PROP, TYPE)                                                                                                                                                                                    \
    inline void SetLightProbeVolume##PROP(ecs::EntityID entity, const TYPE& val)                                                                                                                                                               \
    {                                                                                                                                                                                                                                          \
        auto comp = mRenderWorld->GetComponent<LightProbeVolumeComponentR>(entity);                                                                                                                                                            \
        comp.Write()->m##PROP = val; mVolumeDirty = true;                                                                                                                                                                                      \
    }                                                                                                                                                                                                                                          \
    inline const TYPE GetLightProbeVolume##PROP(const LightProbeVolumeComponentReader& comp) const                                                                                                                                             \
    {                                                                                                                                                                                                                                          \
        return comp->m##PROP;                                                                                                                                                                                                                  \
    }

    LIGHT_PROBE_VOLUME_COMPONENT_R_PROPERTY(Enable, bool);
    LIGHT_PROBE_VOLUME_COMPONENT_R_PROPERTY(UseVolumetricLightMap, bool);
    LIGHT_PROBE_VOLUME_COMPONENT_R_PROPERTY(VLMReflectionProbeIntensity, float);
    LIGHT_PROBE_VOLUME_COMPONENT_R_PROPERTY(VLMReflectionProbeAOIntensity, float);

    RENDER_ENGINE_API void SetLightProbeVolumeCacheFile(ecs::EntityID entity, const std::string& filename);

    RENDER_ENGINE_API bool SetVolumetricLightMap(ecs::EntityID entityID, const TexturePtr& indirectionTex, const TexturePtr& ambientVector, const std::vector<TexturePtr>& shCoefficients, const TexturePtr& skyBentNormal,
                                                 const BoundingBox& volumeBounds, int brickSize);

protected:
    LightProbeVolumeSystemR();

    ~LightProbeVolumeSystemR();

    virtual void OnEndFrame(FrameParam* frameParam) override;

private:
    bool mVolumeDirty;

public:
    inline bool GetVolumeDirty()
    {
        return mVolumeDirty;
    }

    inline void SetVolumeDirty(bool dirty)
    {
        mVolumeDirty = dirty;
    }
};
}   // namespace cross