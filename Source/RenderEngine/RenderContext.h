#pragma once
#include "CrossBase/String/NameID.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDResource.h"
#include "CrossBase/Math/CrossMath.h"
#include "Resource/Shader.h"
#include "ECS/Develop/Framework/Types.h"
#include "RenderEngine/RenderEngineForward.h"
#include "RenderEngine/RenderPrimitives.h"
#include "CECommon/Common/FrameContainer.h"
#include "CECommon/Common/FrameStdContainer.h"

namespace cross {

#define NUMERIC_PROPERTY_TYPES         bool, Float4x4, Float4, Float3, Float2, float, UInt4, UInt3, UInt2, UInt32, Int4, Int3, Int2, SInt32
#define ALIGNED_NUMERIC_PROPERTY_TYPES Float4x4A, Float4A, Float3A, Float2A
#define RESOURCE_PROPERTY_TYPES        NGISampler*, REDTextureView*, REDBufferView*, NGIBufferView*, NGITextureView*, NGIAccelStruct*

template<typename T>
bool CheckType(const NGIVariableDesc& desc)
{
    using namespace CrossSchema;

    if constexpr (std::is_same_v<T, bool>)
    {
        return desc.Type == ShaderVariableType::Bool && desc.RowCount == 1 && desc.ColCount == 1;
    }
    else if constexpr (std::is_same_v<T, Float4x4>)
    {
        return desc.Type == ShaderVariableType::Float && desc.RowCount == 4 && desc.ColCount == 4;
    }
    else if constexpr (std::is_same_v<T, Float4>)
    {
        return desc.Type == ShaderVariableType::Float && desc.RowCount == 1 && desc.ColCount == 4;
    }
    else if constexpr (std::is_same_v<T, Float3>)
    {
        return desc.Type == ShaderVariableType::Float && desc.RowCount == 1 && desc.ColCount == 3;
    }
    else if constexpr (std::is_same_v<T, Float2>)
    {
        return desc.Type == ShaderVariableType::Float && desc.RowCount == 1 && desc.ColCount == 2;
    }
    else if constexpr (std::is_same_v<T, float>)
    {
        return desc.Type == ShaderVariableType::Float && desc.RowCount == 1 && desc.ColCount == 1;
    }
    else if constexpr (std::is_same_v<T, UInt4>)
    {
        return desc.Type == ShaderVariableType::UInt32 && desc.RowCount == 1 && desc.ColCount == 4;
    }
    else if constexpr (std::is_same_v<T, UInt3>)
    {
        return desc.Type == ShaderVariableType::UInt32 && desc.RowCount == 1 && desc.ColCount == 3;
    }
    else if constexpr (std::is_same_v<T, UInt2>)
    {
        return desc.Type == ShaderVariableType::UInt32 && desc.RowCount == 1 && desc.ColCount == 2;
    }
    else if constexpr (std::is_same_v<T, UInt32>)
    {
        return desc.Type == ShaderVariableType::UInt32 && desc.RowCount == 1 && desc.ColCount == 1;
    }
    else if constexpr (std::is_same_v<T, Int4>)
    {
        return desc.Type == ShaderVariableType::Int32 && desc.RowCount == 1 && desc.ColCount == 4;
    }
    else if constexpr (std::is_same_v<T, Int3>)
    {
        return desc.Type == ShaderVariableType::Int32 && desc.RowCount == 1 && desc.ColCount == 3;
    }
    else if constexpr (std::is_same_v<T, Int2>)
    {
        return desc.Type == ShaderVariableType::Int32 && desc.RowCount == 1 && desc.ColCount == 2;
    }
    else if constexpr (std::is_same_v<T, SInt32>)
    {
        return desc.Type == ShaderVariableType::Int32 && desc.RowCount == 1 && desc.ColCount == 1;
    }
    else
    {
#if CROSSENGINE_WIN
        // below line does not compile
        // static_assert(false, "No such type");
#endif
        return false;
    }
}

struct RENDER_ENGINE_API PropertySet
{
    using NumericProperty = std::variant<NUMERIC_PROPERTY_TYPES, FrameStdVector<UInt8>>;

    struct ResourceProperty
    {
        std::variant<std::monostate, RESOURCE_PROPERTY_TYPES> Value;
        NGIResourceBindingFlags Flags;
    };
#if CROSSENGINE_WIN
    class ResourcePropVector
    {
    public:
        ResourcePropVector() = default;
        ResourcePropVector(FrameAllocatorPool* framalloc);
        ~ResourcePropVector();
        std::pmr::vector<ResourceProperty> mData;
    };
#else
    class ResourcePropVector
    {
    public:
        ResourcePropVector() = default;
        ResourcePropVector(FrameAllocatorPool* framalloc);
        std::vector<ResourceProperty> mData;
    };
#endif

    PropertySet(FrameAllocatorPool* framalloc, const PropertySet* parent = nullptr)
        : mFramePool{framalloc}
        , mParent{parent}
    {
        if (framalloc)
        {
            mResourceSet = std::move(CEFrameHashMap<NameID, ResourcePropVector, std::hash<NameID>>(framalloc));
            mNumericSet = std::move(CEFrameHashMap<NameID, NumericProperty, std::hash<NameID>>(framalloc));
        }
        else if (parent && parent->mFramePool)
        {
            mResourceSet = std::move(CEFrameHashMap<NameID, ResourcePropVector, std::hash<NameID>>(parent->mFramePool));
            mNumericSet = std::move(CEFrameHashMap<NameID, NumericProperty, std::hash<NameID>>(parent->mFramePool));
        }
    }

    PropertySet(const PropertySet&) = default;

    PropertySet& operator=(const PropertySet&) = default;

    void SetParent(const PropertySet* parent) { mParent = parent; }

    void SetProperty(const NameID& name, const void* data, size_t dataSize);

    void* GetPropertyPtr(const NameID& name, size_t dataSize);

    template<typename T, typename = typename std::enable_if_t<IsOneOf<T, NUMERIC_PROPERTY_TYPES, ALIGNED_NUMERIC_PROPERTY_TYPES>>>
    void SetProperty(const NameID& name, T&& value)
    {
        mNumericSet[name] = std::forward<T>(value);
    }

    void SetProperty(const NameID& name, const NumericProperty& value) { mNumericSet[name] = value; }

    template<typename T, typename = typename std::enable_if_t<IsOneOf<T, RESOURCE_PROPERTY_TYPES>>>
    void SetProperty(const NameID& name, T&& value, NGIResourceBindingFlags flags = NGIResourceBindingFlags{0})
    {
        if constexpr (std::is_same_v<std::remove_cv_t<std::remove_reference_t<T>>, NGIBufferView*>)
            Assert(value);
        
        ResourcePropVector vec(mFramePool);
        ResourceProperty prop;
        prop.Value = std::forward<T>(value);
        prop.Flags = NGIResourceBindingFlags{0};
        vec.mData.push_back(prop);

        mResourceSet[name] = std::move(vec);
    }

    void SetProperty(const NameID& name, NGIAccelStruct* accel)
    {
        ResourcePropVector vec(mFramePool);
        ResourceProperty prop{
            accel,
            NGIResourceBindingFlags{0}
        };
        vec.mData.push_back(prop);

        mResourceSet[name] = std::move(vec);
    }

    // careful, will remove all exsiting props under the name
    void SetProperty(const NameID& name, const ResourcePropVector& props) { mResourceSet[name] = props; }

    // careful, will remove all exsiting props under the name
    template<typename T, typename = typename std::enable_if_t<IsOneOf<T, RESOURCE_PROPERTY_TYPES>>>
    void SetProperty(const NameID& name, UInt32 num, T* values)
    {
        ResourcePropVector vec(mFramePool);
        vec.mData.reserve(num);
        for (UInt32 i = 0; i < num; ++i)
        {
            vec.mData.push_back({values[i], NGIResourceBindingFlags{0}});
        }

        mResourceSet[name] = std::move(vec);
    }

    const NumericProperty* GetNumericProperty(const NameID& name) const;

    template<typename T, typename = typename std::enable_if_t<IsOneOf<T, NUMERIC_PROPERTY_TYPES>>>
    const T* GetNumericProperty(const NameID& name) const
    {
        if (auto* prop = GetNumericProperty(name); prop)
        {
            if (auto* p = std::get_if<T>(prop); p)
            {
                return p;
            }
        }
        return nullptr;
    }

    // careful, no type checking
    bool GetNumericProperty(const NameID& name, size_t size, void* data) const;

    const ResourceProperty* GetResourceProperty(const NameID& name, UInt32 arrayIndex = 0) const;

    // careful, will remove all exsiting props under the name
    template<typename T, typename = typename std::enable_if_t<IsOneOf<T, RESOURCE_PROPERTY_TYPES>>>
    const T GetResourceProperty(const NameID& name, UInt32 arrayIndex = 0) const
    {
        if (auto* prop = GetResourceProperty(name, arrayIndex); prop)
        {
            if (auto* p = std::get_if<T>(&prop->Value); p)
            {
                return *p;
            }
        }
        return nullptr;
    }

    auto& GetNumericPropertySet() const { return mResourceSet; }

    auto& GetResourcePropertySet() const { return mResourceSet; }

    void RemoveProperty(const NameID& name)
    {
        mNumericSet.erase(name);
        mResourceSet.erase(name);
    }

    void Clear();

    // !!! Deprecated, use GetResourceGroup instead
    NGIResourceGroup* GetPassResourceGroup(const resource::ShaderResourceGroupLayout& layout, NGIPipelineLayout* ngiLayout, ShaderParamGroup group) const { return GetResourceGroup(layout, ngiLayout->GetDesc().ResourceGroupLayouts[group]); }

    NGIResourceGroup* GetResourceGroup(const resource::ShaderResourceGroupLayout& layout, NGIResourceGroupLayout* ngiLayout) const;

    FrameStdVector<NGIResourceBinding> GetResourceBindings(const resource::ShaderResourceGroupLayout& layout, NGIResourceGroupLayout* ngiLayout) const;

    void FillBuffer(const resource::ShaderBufferLayout& layout, void* target, bool transposeMatrix = false) const;

    void FillBuffer(const resource::ShaderBufferLayout& layout, StagingBufferWrap bufferWrap, SizeType bufferWrapOffset = 0, bool transposeMatrix = false) const;

    bool HasShaderConstant(const resource::ShaderBufferLayout& layout) const;

    void FillShaderKey(resource::ShaderVariationKey& key) const;

    template<typename TFunc>
    void VisitProperty(TFunc&& accesser) const
    {
        if (mParent)
        {
            mParent->VisitProperty(accesser);
        }
        for (auto& [key, value] : mNumericSet)
        {
            accesser(key, value);
        }
        for (auto& [key, value] : mResourceSet)
        {
            accesser(key, value);
        }
    }

    bool IsEmpty() const
    {
        if (!mNumericSet.empty() || !mResourceSet.empty())
        {
            return false;
        }

        if (mParent)
        {
            return mParent->IsEmpty();
        }
        return true;
    }

    bool IsNumericSetEmpty() const
    {
        if (!mNumericSet.empty())
        {
            return false;
        }

        if (mParent)
        {
            return mParent->IsNumericSetEmpty();
        }
        return true;
    }

public:
    FrameAllocatorPool* mFramePool = nullptr;
protected:
    const PropertySet* mParent = nullptr;
    CEFrameHashMap<NameID, NumericProperty, std::hash<NameID>> mNumericSet;
    CEFrameHashMap<NameID, ResourcePropVector, std::hash<NameID>> mResourceSet;

};

struct RENDER_ENGINE_API ReadOnlyPropertySet : public PropertySet
{
    ReadOnlyPropertySet(FrameAllocatorPool* framalloc, const PropertySet* parent = nullptr)
        : PropertySet(framalloc, parent)
    {
    }
    void SetProperty(const NameID& name, const void* data, size_t dataSize);

    template<typename T, typename = typename std::enable_if_t<IsOneOf<T, NUMERIC_PROPERTY_TYPES, ALIGNED_NUMERIC_PROPERTY_TYPES>>>
    void SetProperty(const NameID& name, T&& value)
    {
        auto [itr, ret] = mNumericSet.try_emplace(name, std::forward<T>(value));
        // AssertMsg(ret, "Modify after init was not allowed");
    }

    void SetProperty(const NameID& name, const NumericProperty& value)
    {
        auto [itr, ret] = mNumericSet.try_emplace(name, value);
        AssertMsg(ret, "Modify after init was not allowed");
    }

    template<typename T, typename = typename std::enable_if_t<IsOneOf<T, RESOURCE_PROPERTY_TYPES>>>
    void SetProperty(const NameID& name, T&& value, NGIResourceBindingFlags flags = NGIResourceBindingFlags{0})
    {
        ResourcePropVector vec(mFramePool);
        ResourceProperty prop;
        prop.Value = std::forward<T>(value);
        prop.Flags = NGIResourceBindingFlags{0};
        vec.mData.push_back(prop);

        auto [itr, ret] = mResourceSet.try_emplace(name, std::move(vec));
        AssertMsg(ret, "Modify after init was not allowed");
    }

    // careful, will remove all exsiting props under the name
    void SetProperty(const NameID& name, const ResourcePropVector& props)
    {
        auto [itr, ret] = mResourceSet.try_emplace(name, props);
        AssertMsg(ret, "Modify after init was not allowed");
    }

    // careful, will remove all exsiting props under the name
    template<typename T, typename = typename std::enable_if_t<IsOneOf<T, RESOURCE_PROPERTY_TYPES>>>
    void SetProperty(const NameID& name, UInt32 num, T* values)
    {
        ResourcePropVector vec(mFramePool);
        vec.mData.reserve(num);
        for (UInt32 i = 0; i < num; ++i)
        {
            vec.mData.push_back({values[i], NGIResourceBindingFlags{0}});
        }

        auto [itr, ret] = mResourceSet.try_emplace(name, std::move(vec));
        AssertMsg(ret, "Modify after init was not allowed");
    }
};

using RenderContext = PropertySet;

}   // namespace cross
