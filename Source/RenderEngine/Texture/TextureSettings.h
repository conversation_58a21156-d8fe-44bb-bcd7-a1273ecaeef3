#pragma once

#include "Resource/Texture/TextureFormatConvert.h"
#include "NativeGraphicsInterface/NGI.h"

//struct NGITexture;

// Texture filter, anisotropy, wrap mode settings.
struct TextureSettings
{
	TextureFilterMode mFilterMode;
	TextureWrapMode	mWrapMode;
	int mAnisoLevel;
	float mMipBias;						// A bias to the index of the top mip level to use
	ColorSpace mColorSpace;

	TextureSettings();

	void Reset();

	void Apply(cross::NGITexture* texture, TextureDimension dimension, bool hasMipMap, ColorSpace colorSpace, bool isNPOT = false) const;
};
