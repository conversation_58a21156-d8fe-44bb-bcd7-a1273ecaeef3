#pragma once
#include "EnginePrefix.h"
#include "RenderEngine/RenderEngineForward.h"
#include "Resource/Texture/DataProvider.h"
#include "NativeGraphicsInterface/NGI.h"
#include "RenderEngine/RenderFactoryNGIResPtr.h"
#include "CrossBase/String/NameID.h"
#include "Resource/Texture/Texture.h"
#include "Resource/Texture/TextureUDIM.h"
#include "RenderEngine/VirtualTexture/VirtualTexturing.h"

namespace cross
{
    struct VTBuiltData;
    class MaterialR;
}

cross::NGITextureType GetNGITextureType(TextureDimension d);

class RENDER_ENGINE_API GPUTexture : public cross::IGPUTexture
{
public:
    cross::NGITexture* GetNGITexture() override { return mTextureInfo.Proxy ? mProxyTexture : mTexture.get(); }
    cross::NGITextureView* GetNGITextureView() override { return mTextureInfo.Proxy ? mProxyTextureView : mTextureView.get(); }
    virtual bool UploadImage(UInt32 arrayIndex, UInt32 faceIndex, UInt32 mipIndex, UInt8* data, UInt32 dataSize, UInt32 rowPitch = 0, UInt32 slicePitch = 0) override;
    virtual bool UploadImageRect(UInt32 arrayIndex, UInt32 faceIndex, UInt32 mipIndex, const cross::Rect3u& uploadRect, UInt8* data, UInt32 dataSize, UInt32 rowPitch = 0, UInt32 slicePitch = 0) override;
    void ClearTextureDataProvider(){ }
    virtual void Initialize(const TextureInfo& info, const char* pDebugName = "") override;
    virtual void Initialize(std::shared_ptr<TextureDataProvider> data) override;
    virtual void Initialize(const TextureInfo& info, const std::vector<IGPUTexture*>& textures, const char* pDebugName = "") override;
    
    virtual void Initialize(const TextureInfo& info, cross::NGITexture* proxyTexture, cross::NGITextureView* proxyTextureView);

    virtual cross::StreamingScratchBuffer::Range PrepareStreamingData(const TextureDataProvider* dataProvider, std::vector<cross::NGICopyBufferTexture>& updates) override;

    virtual void FinalizeStreamingData(const TextureDataProvider* dataProvider, const std::vector<cross::NGICopyBufferTexture>& updates) override;

    bool IsVTTexture()    { return mTextureInfo.EnableVirtualTextureStreaming;}
    cross::VTBuiltData* GetVTBuildData()    { return vtData;}
    UInt32 GetVTProducerHandle()   { return mVTProducerHandle;}
    TextureFormat GetTextureFormat()    { return mTextureInfo.Format;}
    //void SetAllocatedVT(cross::MaterialR* renderMaterial, cross::IAllocatedVirtualTexture* allocatedVT) { 
    //    std::unique_lock lock(mVTMutex);    
    //    mAllocatedVT[renderMaterial] = allocatedVT; 
    //}
    //cross::IAllocatedVirtualTexture* GetAllocatedVT(cross::MaterialR* renderMaterial){ 
    //    std::shared_lock lock(mVTMutex);
    //    if (mAllocatedVT.count(renderMaterial))
    //    {
    //        return mAllocatedVT.at(renderMaterial);
    //    }
    //    return nullptr;
    //}
    
    GPUTexture();
    ~GPUTexture();

protected:
    void Initialize(cross::NGITextureDesc& desc, const char* pDebugName = "");
    // VT Begin
    //void InitializeVTResource(cross::IVirtualTexture* InVirtualTexture, cross::VTBuiltData* VTData);
    UInt32 mVTProducerHandle = 0;
    //std::unordered_map<cross::MaterialR*, cross::IAllocatedVirtualTexture*> mAllocatedVT;
    cross::VTBuiltData* vtData = nullptr;
    // VT End
   
    //std::shared_mutex mVTMutex;
    std::unique_ptr<cross::NGITexture> mTexture;
    std::unique_ptr<cross::NGITextureView> mTextureView;
    
    // hacks
    cross::NGITexture* mProxyTexture  = nullptr;
    cross::NGITextureView* mProxyTextureView = nullptr; 

    TextureInfo mTextureInfo;
};


class RENDER_ENGINE_API VirtualGPUTexture : public GPUTexture
{
public:
    VirtualGPUTexture() = default;
    virtual void Initialize(std::shared_ptr<TextureDataProvider> data) override;
    virtual void Initialize(const std::vector<std::shared_ptr<TextureDataProvider>>& datas, bool enableMerge = false) override;
    virtual cross::GPUTextureType GetType() override { return mEnabelMerge ? cross::GPUTextureType::MVT : cross::GPUTextureType::VT; };

    virtual void GenerateReprentativeTexture();

protected:
    void GenerateRepresentativeTetxureImp(const TextureInfo& textureinfo, TextureDataProvider* textureProvider);

    virtual void InitVirtualTexture();
    TextureDataProvider* mTextureProvider = nullptr;


private:
    bool mEnabelMerge = false;
};

class RENDER_ENGINE_API VirtualUDIMGPUTexture : public VirtualGPUTexture
{
public:
    void Initialize(const cross::resource::UDIMs& udims) override;
    void GenerateReprentativeTexture() override;
    virtual cross::GPUTextureType GetType() override { return cross::GPUTextureType::UDIM; };
};