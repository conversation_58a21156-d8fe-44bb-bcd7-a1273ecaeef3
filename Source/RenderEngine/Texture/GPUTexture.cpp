#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameCounter.h"
#include "GPUTexture.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "NativeGraphicsInterface/NGIUtils.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/VirtualTexture/VirtualTextureSystemR.h"
#include "RenderEngine/VirtualTexture/VirtualTextureMath.h"
#include "RenderEngine/VirtualTexture/VirtualTexturing.h"
#include "RenderEngine/VirtualTexture/VirtualTextureBuildData.h"
#include "RenderEngine/VirtualTexture/UploadingVirtualTexture.h"
#include "RenderEngine/BindlessResource/BindlessResourceManager.h"

#undef max
#undef min


cross::NGITextureType GetNGITextureType(TextureDimension d)
{
    using namespace cross;   // safe

    switch (d)
    {
    case TextureDimension::Tex2D:
        return NGITextureType::Texture2D;
    case TextureDimension::Tex3D:
        return NGITextureType::Texture3D;
    case TextureDimension::TexCube:
        return NGITextureType::TextureCube;
    case TextureDimension::Tex2DArray:
        return NGITextureType::Texture2DArray;
    case TextureDimension::TexCubeArray:
        return NGITextureType::TextureCubeArray;
    default:
        Assert(false /*, "Illegal value !!! "*/);
        return NGITextureType::Unknown;
    }
}

void GPUTexture::Initialize(std::shared_ptr<TextureDataProvider> data)
{
    using namespace cross;   // safe

    //std::unique_lock writerLock(mInitMutex);
    auto info = data->GetTextureInfo();
    auto format = GetGraphicsFormat(info.Format, info.ColorSpace);
    Assert(format != GraphicsFormat::Unknown);
    mTextureInfo = std::move(info);
    UInt8 mipcount = static_cast<UInt8>(info.MipCount);
    UInt8 realmipbias = 0;
    if (info.MipCount > info.MipBias)
    {
        mipcount = static_cast<UInt8>(info.MipCount - info.MipBias);
        realmipbias = info.MipBias;
    }
    UInt32 realwidth;
    UInt32 realheight;
    UInt32 realdepth;
    data->GetImageSize(realmipbias, realwidth, realheight, realdepth);

    {
        NGITextureDesc desc{
            format,
            GetNGITextureType(info.Dimension),
            mipcount,
            1,
            realwidth,
            realheight,
            info.Dimension == TextureDimension::Tex3D ? info.Depth : 1,
            info.ArraySize,
            NGITextureUsage::CopySrc | NGITextureUsage::CopyDst | NGITextureUsage::ShaderResource,
        };

        Initialize(desc, data->GetPath().c_str());

        auto texelBlock = GetFormatTexelBlockProperty(format);

        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();


        for (UInt32 m = realmipbias; m < data->GetImageCount(); m++)
        {
            UInt32 Wide, High, Depth;
            data->GetImageSize(m, Wide, High, Depth);

            UInt32 mipLevel, faceIndex, arrayIndex;
            data->GetImageLevels(m, mipLevel, faceIndex, arrayIndex);
            mipLevel = mipLevel - realmipbias;
            auto* Data = data->GetImageData(m);
            auto DataByteSize = data->GetImageDataByteSize(m);



            auto ArrayIndex = IsCubeType(info.Dimension) ? 6 * arrayIndex + faceIndex : arrayIndex;

            auto RowUpdateSize = data->GetImagePitchByteSize(m);
            AsyncUsedStageBuffer stagingBuffer = rendererSystem->GetScratchBuffer()->AllocateStaging_Async(NGIBufferUsage::CopySrc, DataByteSize);
            auto [buffer, offset, dstData] = stagingBuffer.Raw();
            memcpy(dstData, Data, DataByteSize);
            buffer->UnmapRange(offset, DataByteSize);

            // image mipmap num is calculate from max(width, height), width or height may be zero
            Wide = std::max(mTexture->GetDesc().Width >> mipLevel, 1u);
            High = std::max(mTexture->GetDesc().Height >> mipLevel, 1u);

            cross::threading::DispatchRenderingCommand([=]()mutable {
                //Assert(mTexture.get());
                // duplicate initialize
                if (mTexture.get())
                {
                    NGICopyBufferTexture region{
                        offset,
                        DataByteSize,
                        RowUpdateSize,

                        NGICalcSubresource(mipLevel, ArrayIndex, 0, desc.MipCount, desc.ArraySize),
                        {
                            0,
                            0,
                            0,
                        },
                        {
                            Wide,
                            High,
                            std::max(Depth, 1u),
                        },
                    };
                    rendererSystem->UpdateTexture(mTexture.get(), buffer, region, NGIResourceState::Undefined, NGIResourceState::PixelShaderShaderResource);
                }
                stagingBuffer.Free();
            });
        }
    }
    data->OnDataUploaded();

    auto provider = std::dynamic_pointer_cast<resource::TextureResourceDataProvider>(data);
    if (provider)
    {
        provider->ReleaseFileHandle();
    }
}

void GPUTexture::Initialize(const TextureInfo& info, cross::NGITexture* proxyTexture, cross::NGITextureView* proxyTextureView)
{
    mProxyTexture = proxyTexture;
    mProxyTextureView = proxyTextureView;
    mTextureInfo = info;
}

cross::StreamingScratchBuffer::Range GPUTexture::PrepareStreamingData(const TextureDataProvider* dataProvider, std::vector<cross::NGICopyBufferTexture>& updates)
{
    using namespace cross;

    QUICK_SCOPED_CPU_TIMING("PrepareStreamingData");

    const auto textureInfo = dataProvider->GetTextureInfo();
    AssertMsg(!IsCubeType(textureInfo.Dimension), "Streaming for cube texture hasn't been implemented yet.");
    AssertMsg(textureInfo.ArraySize == 1, "Streaming for texture array hasn't been implemented yet.");

    const auto format = GetGraphicsFormat(textureInfo.Format, textureInfo.ColorSpace);
    const auto lowestMipLevel = textureInfo.MaxMips - textureInfo.RequestedMips;

    auto textureDataSize = 0U;
    for (auto i = lowestMipLevel; i != dataProvider->GetImageCount(); i++)
    {
        textureDataSize += dataProvider->GetImageDataByteSize(i) + 16; // to-do: alignment
    }

    const auto range = StreamingScratchBuffer::Get()->Allocate(textureDataSize);
    if (range.mDestination)
    {
        auto bufferOffset = range.mOffset;
        updates.reserve(dataProvider->GetImageCount() - lowestMipLevel);
        for (auto i = lowestMipLevel; i != dataProvider->GetImageCount(); i++)
        {
            const auto dataSize = dataProvider->GetImageDataByteSize(i);
            bufferOffset = StreamingScratchBuffer::Get()->GetBuffer()->AlignOffset(NGIBufferUsage::CopySrc, bufferOffset, dataSize);
            std::memcpy(static_cast<UInt8*>(range.mDestination) + bufferOffset - range.mOffset, dataProvider->GetImageData(i), dataSize);

            UInt32 imageWidth, imageHeight, imageDepth;
            dataProvider->GetImageSize(i, imageWidth, imageHeight, imageDepth);
            UInt32 mipLevel, faceIndex, arrayIndex;
            dataProvider->GetImageLevels(i, mipLevel, faceIndex, arrayIndex); // seems not supporting texture array

            const auto texelBlock = GetFormatTexelBlockProperty(format);
            const auto imagePitch = dataProvider->GetImagePitchByteSize(i);
            const auto rowPitch = ((imageWidth - 1) / texelBlock.Width + 1) * texelBlock.Size;
            Assert(rowPitch == imagePitch);

            const auto width = static_cast<UInt32>(std::ceil(textureInfo.Width / std::pow(2, mipLevel)));
            const auto height = static_cast<UInt32>(std::ceil(textureInfo.Height / std::pow(2, mipLevel)));
            NGICopyBufferTexture region
            {
                bufferOffset,
                dataSize,
                imagePitch,
                NGICalcSubresource(mipLevel - lowestMipLevel, arrayIndex, 0, textureInfo.RequestedMips, textureInfo.ArraySize),
                {},
                { std::min(imageWidth, width), std::min(imageHeight, height), std::max(imageDepth, 1U) },
            };
            updates.emplace_back(region);

            bufferOffset += dataSize;
        }
    }

    return range;
}

void GPUTexture::FinalizeStreamingData(const TextureDataProvider* dataProvider, const std::vector<cross::NGICopyBufferTexture>& updates)
{
    using namespace cross;

    QUICK_SCOPED_CPU_TIMING("FinalizeStreamingData");

    mTextureInfo = dataProvider->GetTextureInfo();
    const auto format = GetGraphicsFormat(mTextureInfo.Format, mTextureInfo.ColorSpace);

    NGITextureDesc desc
    {
        format,
        GetNGITextureType(mTextureInfo.Dimension),
        static_cast<UInt8>(updates.size()),
        1,
        static_cast<UInt16>(updates[0].TextureExtent.Width),
        static_cast<UInt16>(updates[0].TextureExtent.Height),
        static_cast<UInt16>(updates[0].TextureExtent.Depth),
        static_cast<UInt16>(mTextureInfo.ArraySize),
        NGITextureUsage::CopySrc | NGITextureUsage::CopyDst | NGITextureUsage::ShaderResource,
    };
    Initialize(desc, dataProvider->GetPath().c_str());

    auto rendererSystemR = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    for (const auto& region : updates)
    {
        Assert(mTexture.get());
        rendererSystemR->UpdateTexture(mTexture.get(), StreamingScratchBuffer::Get()->GetBuffer(), region, NGIResourceState::Undefined, NGIResourceState::PixelShaderShaderResource);
    }
}

void GPUTexture::Initialize(const TextureInfo& info, const char* pDebugName)
{
    using namespace cross;   // safe

    auto format = GetGraphicsFormat(info.Format, info.ColorSpace);
    Assert(format != GraphicsFormat::Unknown);
    mTextureInfo = info;
    UInt8 mipcount = static_cast<UInt8>(mTextureInfo.MipCount);
    UInt8 realmipbias = 0;
    if (info.MipCount > info.MipBias)
    {
        mipcount = static_cast<UInt8>(info.MipCount - info.MipBias);
        realmipbias = info.MipBias;
    }
    UInt16 realwidth = static_cast<UInt16>(std::ceil(info.Width / (std::pow(2, realmipbias))));
    UInt16 realheight = static_cast<UInt16>(std::ceil(info.Height / (std::pow(2, realmipbias))));
    if (mipcount == 0)
    {
        mipcount = static_cast<UInt8>(std::max(log2(info.Width) + 1, std::max(log2(info.Height) + 1, log2(info.Depth) + 1)) + 0.5);
    }
    NGITextureDesc desc{
        format,
        GetNGITextureType(info.Dimension),
        mipcount,
        1,
        realwidth,
        realheight,
        (UInt16)info.Depth,
        (UInt16)info.ArraySize,
        NGITextureUsage::CopySrc | NGITextureUsage::CopyDst | NGITextureUsage::ShaderResource,
    };

    Initialize(desc, pDebugName);

    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    auto count = NGICalcSubresourceCount(desc);
    for (UInt32 i = 0; i < count; ++i)
    {
        DispatchRenderingCommandWithToken([=] {
            NGICopyBufferTexture region{
                0,
                0,
                0,
                i,
                {},
                {},
            };
            if (mTexture.get())
            {
                rendererSystem->UpdateTexture(mTexture.get(), nullptr, region, NGIResourceState::Undefined, NGIResourceState::PixelShaderShaderResource);
            }
        });
    }
}

GPUTexture::GPUTexture()
{
    // TODO(scolu): Move to Initialize()?
#ifdef NGI_ENABLE_RAY_TRACING
    if (cross::EngineGlobal::GetSettingMgr()->GetAppStartUpType() != cross::AppStartUpTypeHeadless)
    {
        gBindlessResourceManager.AddTexture(this);
    }
#endif
}

GPUTexture::~GPUTexture()
{
    using namespace cross;   // safe
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    rendererSystem->DestroyNGIObject(std::move(mTexture));
    rendererSystem->DestroyNGIObject(std::move(mTextureView));
    if (vtData)
    {
       delete vtData;
       vtData = nullptr;
    }
}

void GPUTexture::Initialize(cross::NGITextureDesc& desc, const char* pDebugName)
{
    using namespace cross;   // safe

    //std::unique_lock<std::shared_mutex> lock{mTextureResetMutex};
    if (mTexture || mTextureView)
    {
        PostTickUpdates::Get()->Add([texture = mTexture.release(), textureView = mTextureView.release(), frameNumber = frame::GetRenderingFrameNumber()]
        {
            auto release = [texture, textureView, frameNumber](auto self) -> void
            {
                if (frameNumber + CmdSettings::Inst().gMaxQueuedFrame < cross::frame::GetRenderingFrameNumber())
                {
                    DispatchRenderingCommandWithToken([texture, textureView]
                    {
                        QUICK_SCOPED_CPU_TIMING("ReleaseGPUTexture");

                        delete textureView;
                        delete texture;
                    });
                }
                else
                {
                    cross::PostTickUpdates::Get()->Add([texture, textureView, frameNumber, self]
                    {
                        self(self);
                    });
                }
            };

            release(release);
        });
    }

    mTexture.reset(GetNGIDevice().CreateTexture(desc, pDebugName));
    NGITextureViewDesc viewDesc{NGITextureUsage::CopySrc | NGITextureUsage::CopyDst | NGITextureUsage::ShaderResource,
                                desc.Format,
                                desc.Dimension,
                                {
                                    NGITextureAspect::Color,
                                    0,
                                    desc.MipCount,
                                    0,
                                    desc.ArraySize,
                                }};
    mTextureView.reset(GetNGIDevice().CreateTextureView(mTexture.get(), viewDesc));
}

void GPUTexture::Initialize(const TextureInfo& info, const std::vector<IGPUTexture*>& textures, const char* pDebugName)
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);

    using namespace cross;   // safe
    auto format = GetGraphicsFormat(info.Format, info.ColorSpace);
    Assert(format != GraphicsFormat::Unknown);
    mTextureInfo = info;
    UInt8 mipcount = static_cast<UInt8>(info.MipCount);
    UInt8 realmipbias = 0;
    if (info.MipCount > info.MipBias)
    {
        mipcount = static_cast<UInt8>(info.MipCount - info.MipBias);
        realmipbias = info.MipBias;
    }
    if (mipcount == 0)
    {
        mipcount = static_cast<UInt8>(std::max(log2(info.Width) + 1, std::max(log2(info.Height) + 1, log2(info.Depth) + 1)) + 0.5);
    }
    UInt16 realwidth = static_cast<UInt16>(std::ceil(info.Width / (std::pow(2, realmipbias))));
    UInt16 realheight = static_cast<UInt16>(std::ceil(info.Height / (std::pow(2, realmipbias))));
    NGITextureDesc desc{
        format,
        GetNGITextureType(info.Dimension),
        mipcount,
        1,
        realwidth,
        realheight,
        (UInt16)info.Depth,
        (UInt16)info.ArraySize,
        NGITextureUsage::CopySrc | NGITextureUsage::CopyDst | NGITextureUsage::ShaderResource,
    };
    Initialize(desc, pDebugName);

    auto* rendererSystem = cross::EngineGlobal::GetRenderEngine()->GetGlobalSystem<cross::RendererSystemR>();
    UInt8 faceNum = IsCubeType(mTextureInfo.Dimension) ? 6u : 1u;
    UInt32 arraySize = mTextureInfo.ArraySize / faceNum;
    for (UInt16 arrayIdx = 0; arrayIdx < arraySize; arrayIdx++)
    {
        for (UInt8 faceIdx = 0; faceIdx < faceNum; faceIdx++)
        {
            for (UInt8 mipIdx = 0; mipIdx < mipcount; mipIdx++)
            {
            DispatchRenderingCommandWithToken([=] {
                    auto Width = (std::max)(static_cast<UInt32>(realwidth >> mipIdx), 1u);
                    auto Height = (std::max)(static_cast<UInt32>(realheight >> mipIdx), 1u);
                    auto Depth = (std::max)(mTextureInfo.Depth >> mipIdx, 1u);
                    cross::NGICopyTexture region = {cross::NGICalcSubresource(mipIdx, faceIdx, 0, mipcount, faceNum), {}, cross::NGICalcSubresource(mipIdx, faceIdx, arrayIdx, mipcount, faceNum), {}, {Width, Height, Depth}};

                    if (mTexture.get())
                        rendererSystem->UpdateTexture(mTexture.get(),
                                                      textures[arrayIdx]->GetNGITexture(),
                                                      region,
                                                      cross::NGIResourceState::Undefined,
                                                      cross::NGIResourceState::ShaderResourceBit | cross::NGIResourceState::ShaderStageBitMask,
                                                      cross::NGIResourceState::ShaderResourceBit | cross::NGIResourceState::ShaderStageBitMask,
                                                      cross::NGIResourceState::ShaderResourceBit | cross::NGIResourceState::ShaderStageBitMask);
                });
            }
        }
    }
}

bool GPUTexture::UploadImage(UInt32 arrayIndex, UInt32 faceIndex, UInt32 mipIndex, UInt8* data, UInt32 dataSize, UInt32 rowPitch /*= 0*/, UInt32 slicePitch /*= 0*/)
{
    SCOPED_CPU_TIMING(GroupRendering, "UploadImageRenderthread");
    using namespace cross;   // safe

    if (mTextureInfo.MipCount <= mipIndex || faceIndex > 5)
        return false;

    UInt32 updateArray = IsCubeType(mTextureInfo.Dimension) ? arrayIndex * 6 + faceIndex : arrayIndex;

    UInt32 rowNum = mTextureInfo.Height >> mipIndex;
    auto blockSize = GetBlockSize(mTextureInfo.Format);
    auto pixelSize = GetPixelByteSize(mTextureInfo.Format);
    UInt32 rowUpdate = pixelSize * (mTextureInfo.Width / blockSize.x);
    if (rowPitch == 0)
        rowPitch = rowUpdate;
    rowNum /= blockSize.y;

    auto Width = (std::max)(mTextureInfo.Width >> mipIndex, 1u);
    auto Height = (std::max)(mTextureInfo.Height >> mipIndex, 1u);
    auto Depth = (std::max)(mTextureInfo.Depth >> mipIndex, 1u);
    auto SlicePitch = slicePitch == 0 ? rowPitch * rowNum : slicePitch;
    auto DataSize = dataSize == 0 ? SlicePitch * Depth : dataSize;

    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto stagingBuffer = rendererSystem->GetScratchBuffer()->AllocateStaging_Async(NGIBufferUsage::CopySrc, DataSize);
    auto [stageBuffer, offset, dstData] = stagingBuffer.Raw();
            
    //auto [stageBuffer, offset, dstData] = rendererSystem->GetScratchBuffer()->Allocate(NGIBufferUsage::CopySrc, DataSize);
        
    memcpy(dstData, data, DataSize);
    stageBuffer->UnmapRange(offset, DataSize);

    NGICopyBufferTexture region{
        offset,
        DataSize,
        rowPitch,

        NGICalcSubresource(mipIndex, updateArray, 0, mTexture->GetDesc().MipCount, mTexture->GetDesc().ArraySize),
        {0, 0, 0},
        {
            Width,
            Height,
            Depth,
        },
    };

    Assert(mTexture.get());
    rendererSystem->UpdateTexture(mTexture.get(), stageBuffer, region, NGIResourceState::PixelShaderShaderResource, NGIResourceState::PixelShaderShaderResource);
    stagingBuffer.Free();
    return true;
}

bool GPUTexture::UploadImageRect(UInt32 arrayIndex, UInt32 faceIndex, UInt32 mipIndex, const cross::Rect3u& uploadRect, UInt8* data, UInt32 dataSize, UInt32 rowPitch /*= 0*/, UInt32 slicePitch /*= 0*/)
{
    using namespace cross;   // safe

    if (mTextureInfo.MipCount <= mipIndex || faceIndex > 5)
        return false;

    UInt32 updateArray = IsCubeType(mTextureInfo.Dimension) ? arrayIndex * 6 + faceIndex : arrayIndex;

    UInt32 rowNum = uploadRect.size.y;
    auto blockSize = GetBlockSize(mTextureInfo.Format);
    auto pixelSize = GetPixelByteSize(mTextureInfo.Format);
    UInt32 rowUpdate = uploadRect.size.x * pixelSize;
    if (rowPitch == 0)
        rowPitch = rowUpdate;

    auto SlicePitch = slicePitch == 0 ? rowPitch * rowNum : slicePitch;
    auto DataByteSize = dataSize == 0 ? SlicePitch * uploadRect.size.z : dataSize;

    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto stagingBuffer = rendererSystem->GetScratchBuffer()->AllocateStaging_Async(NGIBufferUsage::CopySrc, DataByteSize);
    auto [stageBuffer, offset, dstData] = stagingBuffer.Raw();
    
    //auto [stageBuffer, offset, dstData] = rendererSystem->GetScratchBuffer()->Allocate(NGIBufferUsage::CopySrc, DataByteSize);
    memcpy(dstData, data, DataByteSize);

    stageBuffer->UnmapRange(offset, DataByteSize);

    NGICopyBufferTexture region{
        offset,
        DataByteSize,
        rowPitch,

        NGICalcSubresource(mipIndex, updateArray, 0, mTexture->GetDesc().MipCount, mTexture->GetDesc().ArraySize),
        {static_cast<SInt32>(uploadRect.start.x), static_cast<SInt32>(uploadRect.start.y), static_cast<SInt32>(uploadRect.start.z)},
        {uploadRect.size.x, uploadRect.size.y, uploadRect.size.z},
    };

    Assert(mTexture.get());
    rendererSystem->UpdateTexture(mTexture.get(), stageBuffer, region, NGIResourceState::PixelShaderShaderResource, NGIResourceState::PixelShaderShaderResource);
    stagingBuffer.Free();
    return true;
}

void VirtualGPUTexture::Initialize(std::shared_ptr<TextureDataProvider> data)
{
    auto info = data->GetTextureInfo();
    auto format = GetGraphicsFormat(info.Format, info.ColorSpace);
    Assert(format != cross::GraphicsFormat::Unknown);
    mTextureInfo = std::move(info);

    Assert(mTextureInfo.Width >= VIRTUALTEXTURE_TILE_SIZE && mTextureInfo.Height >= VIRTUALTEXTURE_TILE_SIZE);


    vtData = new cross::SingleImageVTBuiltinData();
    vtData->Serialize(mTextureInfo, data.get());

    InitVirtualTexture();
    mTextureProvider = data.get();
}

void VirtualGPUTexture::Initialize(const std::vector<std::shared_ptr<TextureDataProvider>>& datas, bool enableMerge)
{
    mEnabelMerge = enableMerge;

    using namespace cross;
    auto info = datas[0]->GetTextureInfo();
    auto format = GetGraphicsFormat(info.Format, info.ColorSpace);
    Assert(format != GraphicsFormat::Unknown);
    mTextureInfo = std::move(info);

    if (enableMerge) {
        auto vt_data = new cross::MergeVTBuiltinData();
        vt_data->Serialize(mTextureInfo, datas);
        vtData = vt_data;
    }
    else
    {
        auto vt_data = new cross::MutilImageVTBuiltinData();
        vt_data->Serialize(mTextureInfo, datas);
        vtData = vt_data;
    }
   
    InitVirtualTexture();
}

void VirtualGPUTexture::GenerateReprentativeTexture() 
{
    using namespace cross;   // safe

    if (mTexture || mTextureView)
    {
        return;
    }

    GenerateRepresentativeTetxureImp(mTextureInfo, mTextureProvider);

}
void VirtualGPUTexture::GenerateRepresentativeTetxureImp(const TextureInfo& textureinfo, TextureDataProvider* textureProvider)
{
    using namespace cross;   // safe
    auto format = GetGraphicsFormat(textureinfo.Format, textureinfo.ColorSpace);


    auto texelBlock = GetFormatTexelBlockProperty(format);

    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    std::vector<TileID> outTileIDs;
    // get the possible largest mip that larger or equal to 128
    UInt8 level = std::min(static_cast<UInt8>(log2(textureinfo.Width / VIRTUALTEXTURE_TILE_SIZE)), static_cast<UInt8> (textureProvider->GetImageCount() - 1));

    UInt32 Wide = textureinfo.Width >> level;
    UInt32 High = textureinfo.Height >> level;


        NGITextureDesc desc{
        format,
        GetNGITextureType(textureinfo.Dimension),
        1,
        1,
        Wide,
        High,
        1,
        1,
        NGITextureUsage::CopySrc | NGITextureUsage::CopyDst | NGITextureUsage::ShaderResource,
    };

    GPUTexture::Initialize(desc, textureProvider->GetPath().c_str());

    auto* textureResourceProvider = dynamic_cast<cross::resource::TextureResourceDataProvider*>(textureProvider);
    auto loadded_mip_data = textureResourceProvider->StreaminRequestIO(level);

    auto DataByteSize = loadded_mip_data.mData.size();
    auto Data = loadded_mip_data.mData.data();
    auto RowUpdateSize = textureProvider->GetImagePitchByteSize(level);



    AsyncUsedStageBuffer stagingBuffer = rendererSystem->GetScratchBuffer()->AllocateStaging_Async(NGIBufferUsage::CopySrc, DataByteSize);
    auto [buffer, offset, dstData] = stagingBuffer.Raw();
    memcpy(dstData, Data, DataByteSize);
    buffer->UnmapRange(offset, DataByteSize);

    NGICopyBufferTexture region{
        offset,
        static_cast<SizeType>(DataByteSize),
        RowUpdateSize,

        NGICalcSubresource(0, 0, 0, desc.MipCount, desc.ArraySize),
        {
            0,
            0,
            0,
        },
        {
            Wide,
            High,
            1,
        },
    };
    rendererSystem->UpdateTexture(mTexture.get(), buffer, region, NGIResourceState::Undefined, NGIResourceState::PixelShaderShaderResource);

    stagingBuffer.Free();
    textureResourceProvider->ReleaseFileHandle();
}

void VirtualGPUTexture::InitVirtualTexture()
{
    using namespace cross;
    auto* vtSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<VirtualTextureSystemR>();
    VTProducerDescription producerDesc;
    producerDesc.bContinuousUpdate = false;
    producerDesc.dimensions = 2;
    producerDesc.tileSize = vtData->tileSize;
    producerDesc.tileBorderSize = vtData->tileBorderSize;
    producerDesc.blockWidthInTiles = VTMath::DivideAndRoundUp<UInt32>(VTMath::DivideAndRoundUp(vtData->width, vtData->tileSize), vtData->widthInBlocks);
    producerDesc.blockHeightInTiles = VTMath::DivideAndRoundUp<UInt32>(VTMath::DivideAndRoundUp(vtData->height, vtData->tileSize), vtData->heightInBlocks);
    producerDesc.widthInBlocks = static_cast<UInt16>(vtData->widthInBlocks);
    producerDesc.heightInBlocks = static_cast<UInt16>(vtData->heightInBlocks);
    producerDesc.depthInTiles = 1u;
    producerDesc.maxLevel = vtData->GetMaxLevel();
    producerDesc.numTextureLayers = static_cast<UInt8>(vtData->GetNumLayers());
    producerDesc.numPhysicalGroups = 1;
    producerDesc.arrayX = vtData->arrayX;
    producerDesc.arrayY = vtData->arrayY;

    for (UInt32 layerIndex = 0u; layerIndex < vtData->GetNumLayers(); ++layerIndex)
    {
        producerDesc.layerFormat[layerIndex] = vtData->layerTypes[layerIndex];
        producerDesc.colorSpaces[layerIndex] = vtData->colorSpaces[layerIndex];
        producerDesc.layerFallbackColor[layerIndex] = vtData->fallbackColor[layerIndex];
        producerDesc.physicalGroupIndex[layerIndex] = 0;
    }

    UploadingVirtualTexture* virtualTexture = new UploadingVirtualTexture(vtData);
    VTProducerHandle producerHandle = vtSystem->RegisterProducer(producerDesc, virtualTexture);
    mVTProducerHandle = producerHandle.packedValue;
}

void VirtualUDIMGPUTexture::Initialize(const cross::resource::UDIMs& udims)
{
    using namespace cross;
    auto data = udims.mImages.begin()->second.mTextureResourceProvider;
    auto info = data->GetTextureInfo();
    auto format = GetGraphicsFormat(info.Format, info.ColorSpace);
    Assert(format != GraphicsFormat::Unknown);
    mTextureInfo = std::move(info);

    Int2 bounds = { 1, 0 };
    Int2 maxImage = { 0, 0 };
    
    std::map<std::pair<UInt32, UInt32>, TextureDataProvider*> images;
    for (auto& itr : udims.mImages)
    {
        bounds.x = std::max<int>(itr.first.first, bounds.x);
        bounds.y = std::max<int>(itr.first.second, bounds.y);
        images[itr.first] = itr.second.mTextureResourceProvider.get();
    }

    bounds.y += 1;
    // modify the texture info to fake a large texture
    // may be stupid;
    // and how to handle the UDIM with different size;
    // not supported

    //mTextureInfo.Width *= bounds.x;
    //mTextureInfo.Height *= bounds.y;


    auto udim_vt_data = new cross::UDIMVTBuiltinData();
    udim_vt_data->Serialize(mTextureInfo, images, udims.mips, bounds);
    vtData = udim_vt_data;

    InitVirtualTexture();
}

void VirtualUDIMGPUTexture::GenerateReprentativeTexture() {
    if (mTexture || mTextureView)
    {
        return;
    }
    // get the first udim texture as representative;

    auto& textureInfo = mTextureInfo;

    std::vector<cross::TileID> out;

    auto chunks = vtData->GetImageChunk(0, 0, 0, out);
    if (chunks.size() > 0)
    {
        auto chunk = chunks[0];

        GenerateRepresentativeTetxureImp(textureInfo, chunk->mTextureDataProvider.get());
    }
    

}

