#include "EnginePrefix.h"
#include "TerrainRenderNode.h"
#include "RenderEngine/GPUScene/GPUSceneData.h"
#include "RenderEngine/GPUScene/GPUScene.h"
#include "RenderEngine/TerrainSystemR.h"
#include "RenderEngine/CameraSystemR.h"

namespace cross
{
    void TerrainRenderNode::GenerateDrawUnits(const GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const
    {
        const auto cameraSystemR = params.renderWorld->GetRenderSystem<CameraSystemR>();
        const auto mainCamera = cameraSystemR->GetMainCamera();
        if (params.renderWorld->IsEntityAlive(mainCamera))
        {
            const auto& drawUnitDesc = params.drawUnitsDesc;
            const auto cameraComponentR = params.renderWorld->GetComponent<CameraComponentR>(mainCamera);
            const auto renderCamera = cameraSystemR->GetRenderCamera(cameraComponentR.Read());

            // the else code path is for a temporary optimization that, for other camera other than main camera, terrain should use the largest LOD to optimize performance
            // which cause many troubles, especially in sequencer recording
            // we now disable, and such optimization should use more standard methods to achieve.
            if (true)
            {
                if (!mVisibleDrawData.empty())
                {
                    const auto& sharedDrawData = mVisibleDrawData.front();
                    MaterialR* material{};
                    UInt16 renderGroup{};
                    if (IsDrawableTerrainPass(drawUnitDesc.TagName, sharedDrawData.mMaterial, material) && params.IsDrawable(sharedDrawData.mMaterial, material, renderGroup))
                    {
                        const auto stateBucketID = REDCulling::CalculateDrawUnitStateBucketID(sharedDrawData.mGeometry, material, sharedDrawData.mProperties, drawUnitDesc.TagName, REDDrawUnitFlag::ReceiveDecal, 0);
                        for (UInt32 i = 0U; i != mVisibleDrawData.size(); i++)
                        {
                            const auto& drawData = mVisibleDrawData[i];
                            const auto& objectIndexStarts = mAllocationInfo.mObjectIndexStarts;
                            if (auto iter = objectIndexStarts.find(drawUnitDesc.TagName); iter != objectIndexStarts.end())
                            {
                                collector.AddOpaqueBatchableDrawUnit(renderGroup,
                                                                     sharedDrawData.mGeometry,
                                                                     material,
                                                                     stateBucketID,
                                                                     1U,
                                                                     mObjectCullingGUIDStart + i,
                                                                     iter->second.mObjectIndexStart + i,
                                                                     iter->second.mTypedObjectDataBufferView,
                                                                     nullptr,
                                                                     REDDrawUnitFlag::ReceiveDecal,
                                                                     nullptr,
                                                                     &drawData.mProperties);
                            }
                        }
                    }
                }
            }
            else
            {
                if (!mAlwaysVisibleDrawData.empty())
                {
                    const auto& sharedDrawData = mAlwaysVisibleDrawData.front();
                    MaterialR* material{};
                    UInt16 renderGroup{};
                    if (IsDrawableTerrainPass(drawUnitDesc.TagName, sharedDrawData.mMaterial, material) && params.IsDrawable(sharedDrawData.mMaterial, material, renderGroup))
                    {
                        const auto stateBucketID = REDCulling::CalculateDrawUnitStateBucketID(sharedDrawData.mGeometry, material, sharedDrawData.mProperties, drawUnitDesc.TagName, REDDrawUnitFlag::ReceiveDecal, 0);
                        for (UInt32 i = 0U; i != mAlwaysVisibleDrawData.size(); i++)
                        {
                            const auto& drawData = mAlwaysVisibleDrawData[i];
                            const auto& objectIndexStarts = mAllocationInfo.mObjectIndexStarts;
                            if (auto iter = objectIndexStarts.find(drawUnitDesc.TagName); iter != objectIndexStarts.end())
                            {
                                const auto index = static_cast<UInt32>(mVisibleDrawData.size() + i);
                                collector.AddOpaqueBatchableDrawUnit(renderGroup,
                                                                     sharedDrawData.mGeometry,
                                                                     material,
                                                                     stateBucketID,
                                                                     1U,
                                                                     mObjectCullingGUIDStart + index,
                                                                     iter->second.mObjectIndexStart + index,
                                                                     iter->second.mTypedObjectDataBufferView,
                                                                     nullptr,
                                                                     REDDrawUnitFlag::ReceiveDecal,
                                                                     nullptr,
                                                                     &drawData.mProperties);
                            }
                        }
                    }
                }
            }

            Assert(mCustomTerrainDrawData.size() <= mCustomTerrainAllocationInfo.size());
            for (UInt32 i = 0U; i != mCustomTerrainDrawData.size(); i++)
            {
                const auto& drawData = mCustomTerrainDrawData[i];
                MaterialR* material{};
                UInt16 renderGroup{};
                if (IsDrawableTerrainPass(drawUnitDesc.TagName, drawData.mMaterial, material) && params.IsDrawable(drawData.mMaterial, material, renderGroup))
                {
                    const auto& objectIndexStarts = mCustomTerrainAllocationInfo[i].mObjectIndexStarts;
                    if (auto iter = objectIndexStarts.find(drawUnitDesc.TagName); iter != objectIndexStarts.end())
                    {
                        const auto stateBucketID = REDCulling::CalculateDrawUnitStateBucketID(drawData.mGeometry, material, drawData.mProperties, drawUnitDesc.TagName, REDDrawUnitFlag::ReceiveDecal, 0);
                        collector.AddOpaqueBatchableDrawUnit(renderGroup,
                                                             drawData.mGeometry,
                                                             material,
                                                             stateBucketID,
                                                             1U,
                                                             mObjectCullingGUIDStart,
                                                             iter->second.mObjectIndexStart,
                                                             iter->second.mTypedObjectDataBufferView,
                                                             nullptr,
                                                             REDDrawUnitFlag::ReceiveDecal,
                                                             nullptr,
                                                             &mObjectProperties);
                    }
                }
            }
        }
    }

    void TerrainRenderNode::UploadGPUScene(GPUScene& GPUScene, RenderWorld* renderWorld, ecs::EntityID entity)
    {
        QUICK_SCOPED_CPU_TIMING("TerrainRenderNode::UploadGPUScene");

        if (mPrimitiveCullingGUIDStart != -1)
        {
            PrimitiveCullingData primitiveCullingData
            {
                {},
                EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount(),
                {},
                1U,
                {},
                0u,
            };

            ObjectCullingData objectCullingData{
                Float4x4::Identity(),
                static_cast<UInt32>(mPrimitiveCullingGUIDStart),
            };

            auto uploadBufferDataPtr = GPUScene.UploadData(static_cast<UInt32>(sizeof(PrimitiveCullingData)), static_cast<UInt32>(mPrimitiveCullingGUIDStart * sizeof(PrimitiveCullingData)));
            std::memcpy(static_cast<std::byte*>(uploadBufferDataPtr), &primitiveCullingData, sizeof(PrimitiveCullingData));

            const auto count = mVisibleDrawData.size() + mAlwaysVisibleDrawData.size() + mCustomTerrainDrawData.size();
            uploadBufferDataPtr = GPUScene.UploadData(static_cast<UInt32>(count * sizeof(ObjectCullingData)), static_cast<UInt32>(mObjectCullingGUIDStart * sizeof(ObjectCullingData)));
            for (UInt32 i = 0U; i != count; i++)
            {
                std::memcpy(static_cast<std::byte*>(uploadBufferDataPtr) + i * sizeof(ObjectCullingData), &objectCullingData, sizeof(ObjectCullingData));
            }
        }

        if (!mAlwaysVisibleDrawData.empty())
        {
            const auto material = mAlwaysVisibleDrawData.front().mMaterial;
            const bool FxFromMaterialEditor = material->GetFx()->IsCreatedByMaterialEditor();
            for (auto [passName, passObjectIndexStart] : mAllocationInfo.mObjectIndexStarts)
            {
                if (const auto protoType =
                        FxFromMaterialEditor ? material->GetShader(passName)->GetProtoTypeContainer().GetObjectProtoType(MaterialUsage::USED_WITH_TERRAIN) : material->GetShader(passName)->GetObjectProtoType(); protoType)
                {
                    const auto objectDataByteSize = protoType->Layout.ByteSize;
                    const auto objectIndex = passObjectIndexStart.mObjectIndexStart;
                    const auto count = static_cast<UInt32>(mVisibleDrawData.size() + mAlwaysVisibleDrawData.size());
                    auto uploadBufferDataPtr = GPUScene.UploadData(count * objectDataByteSize, objectIndex * objectDataByteSize);
                    for (UInt32 i = 0U; i != mVisibleDrawData.size(); i++)
                    {
                        mVisibleDrawData[i].mProperties.FillBuffer(protoType->Layout, static_cast<std::byte*>(uploadBufferDataPtr) + i * objectDataByteSize);
                    }
                    for (UInt32 i = 0U; i != mAlwaysVisibleDrawData.size(); i++)
                    {
                        mAlwaysVisibleDrawData[i].mProperties.FillBuffer(protoType->Layout, static_cast<std::byte*>(uploadBufferDataPtr) + (mVisibleDrawData.size() + i) * objectDataByteSize);
                    }
                }
                else
                {
                    Assert(false);
                }
            }
        }

        for (UInt32 i = 0U; i != mCustomTerrainDrawData.size(); i++)
        {
            const auto& drawData = mCustomTerrainDrawData[i];
            const auto material = drawData.mMaterial;
            const bool FxFromMaterialEditor = material->GetFx()->IsCreatedByMaterialEditor();
            for (auto [passName, passObjectIndexStart] : mCustomTerrainAllocationInfo[i].mObjectIndexStarts)
            {
                if (const auto protoType = FxFromMaterialEditor ? material->GetShader(passName)->GetProtoTypeContainer().GetObjectProtoType(MaterialUsage::USED_WITH_TERRAIN) : material->GetShader(passName)->GetObjectProtoType(); protoType)
                {
                    const auto objectDataByteSize = protoType->Layout.ByteSize;
                    const auto objectIndex = passObjectIndexStart.mObjectIndexStart;
                    auto uploadBufferDataPtr = GPUScene.UploadData(objectDataByteSize, objectIndex * objectDataByteSize);
                    mCustomTerrainDrawData[i].mProperties.FillBuffer(protoType->Layout, uploadBufferDataPtr);
                }
                else
                {
                    Assert(false);
                }
            }
        }
    }

    void TerrainRenderNode::AllocateGPUScene(GPUScene& gpuScene)
    {
        Assert(mPrimitiveCullingGUIDStart == -1 && mObjectCullingGUIDStart == -1);

        mObjectCount = static_cast<UInt32>(std::max(mDrawData.size(), mAlwaysVisibleDrawData.size()) + mAlwaysVisibleDrawData.size());

        std::tie(mPrimitiveCullingGUIDStart, mObjectCullingGUIDStart) = gpuScene.AllocateCullingData(mObjectCount + static_cast<UInt32>(mCustomTerrainDrawData.size()));

        if (!mDrawData.empty())
        {
            const auto fx = mDrawData.front().mMaterial->GetFx();
            const bool FxFromMaterialEditor = fx->IsCreatedByMaterialEditor();
            for (const auto& [passName, pass] : fx->GetAllPass())
            {
                if (pass.mShaderPtr)
                {
                    if (const auto protoType = FxFromMaterialEditor ? pass.mShaderPtr->GetProtoTypeContainer().GetObjectProtoType(MaterialUsage::USED_WITH_TERRAIN) : pass.mShaderPtr->GetObjectProtoType(); protoType)
                    {
                        const auto byteStride = protoType->Layout.ByteSize;
                        const auto [objectIndexStart, objectDataBufferView] = gpuScene.Allocate(byteStride, mObjectCount);
                        mAllocationInfo.mObjectIndexStarts[passName] =
                        {
                            static_cast<SInt32>(objectIndexStart),
                            byteStride,
                            objectDataBufferView
                        };
                    }
                }
            }
        }

        mCustomTerrainAllocationInfo.reserve(mCustomTerrainDrawData.size());
        for (const auto& drawData : mCustomTerrainDrawData)
        {
            const auto fx = drawData.mMaterial->GetFx();
            const bool FxFromMaterialEditor = fx->IsCreatedByMaterialEditor();
            for (const auto& [passName, pass] : fx->GetAllPass())
            {
                if (pass.mShaderPtr)
                {
                    if (const auto protoType = FxFromMaterialEditor ? pass.mShaderPtr->GetProtoTypeContainer().GetObjectProtoType(MaterialUsage::USED_WITH_TERRAIN) : pass.mShaderPtr->GetObjectProtoType(); protoType)
                    {
                        const auto byteStride = protoType->Layout.ByteSize;
                        const auto [objectIndexStart, objectDataBufferView] = gpuScene.Allocate(byteStride, mObjectCount);
                        mCustomTerrainAllocationInfo.emplace_back().mObjectIndexStarts[passName] =
                        {
                            static_cast<SInt32>(objectIndexStart),
                            byteStride,
                            objectDataBufferView
                        };
                    }
                }
            }
        }
    }

    void TerrainRenderNode::FreeGPUScene(GPUScene& gpuScene)
    {
        if (mPrimitiveCullingGUIDStart != -1)
        {
            gpuScene.FreeCullingData(mPrimitiveCullingGUIDStart, mObjectCullingGUIDStart, mObjectCount + static_cast<UInt32>(mCustomTerrainDrawData.size()));

            for (const auto& [_, passObjectIndexStart] : mAllocationInfo.mObjectIndexStarts)
            {
                gpuScene.Free(passObjectIndexStart.mByteStride, passObjectIndexStart.mObjectIndexStart, mObjectCount);
            }
            mAllocationInfo.mObjectIndexStarts.clear();

            for (const auto& customTerrainAllocationInfo : mCustomTerrainAllocationInfo)
            {
                for (const auto& [_, passObjectIndexStart] : customTerrainAllocationInfo.mObjectIndexStarts)
                {
                    gpuScene.Free(passObjectIndexStart.mByteStride, passObjectIndexStart.mObjectIndexStart, 1U);
                }
            }
            mCustomTerrainAllocationInfo.clear();

            mPrimitiveCullingGUIDStart = mObjectCullingGUIDStart = - 1;
            mObjectCount = 0U;
        }
    }

    bool TerrainRenderNode::IsDrawableTerrainPass(const NameID passName, const MaterialR* renderMtl, const MaterialR* overrideMtl)
    {
        const MaterialR* finalMtl = (overrideMtl != nullptr) ? overrideMtl : renderMtl;

        if (finalMtl->GetFx()->IsCreatedByMaterialEditor())
        {
            const auto& defines = finalMtl->GetFx()->GetMaterialDefines();
            Assert(defines.UsedWithTerrain);
            switch (defines.TerrainMode)
            {
            case TerrainMode::Land:
            case TerrainMode::WeightBlend:
                return (passName == NAME_ID("gpass"));
            case TerrainMode::Ocean:
                if (defines.BlendMode == MaterialBlendMode::Opaque)
                    return (passName == NAME_ID("gpass"));
                else
                    return (passName == NAME_ID("forward"));
            default:
                Assert(false);
                return false;
            }
        }
        else
        {
            return true;
        }
    }
}
