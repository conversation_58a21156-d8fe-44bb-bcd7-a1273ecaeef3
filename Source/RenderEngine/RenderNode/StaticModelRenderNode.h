#pragma once

#include "ModelRenderNode.h"

namespace cross {

// Use AABB in Mesh for culling
class StaticModelRenderNode : public ModelRenderNode
{
public:
    StaticModelRenderNode(const EntityDistanceCulling* distanceCulling)
        : mDistanceCulling{distanceCulling}
    {
        mObjectProperties.SetProperty(magic_enum::enum_name<MaterialUsage::USED_WITH_DEFAULT>(), true);
    }

    virtual void AllocateGPUScene(GPUScene& GPUScene) override;

    virtual void GenerateDrawUnits(const GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const override;

    virtual void UploadGPUScene(GPUScene& GPUScene, RenderWorld* renderWorld, ecs::EntityID entity) override;

    // ---------------------------------------------For RayTracing---------------------------------------------
    UInt32 GetSubMeshCount() const override
    {
        return mRenderModel.mLODModels.size() == 0 ? 0 : static_cast<UInt32>(mRenderModel.mLODModels[0].mSubModels.size());
    }
    void BuildAccelStruct(RayTracingScene& RayTracingScene, RenderWorld* renderWorld, ecs::EntityID entity) override;
    bool IsValidForRayTracing() const override { return true; }
    NGIAccelStruct* GetAccelStruct() const override;
    RenderGeometry* GetRenderGeometry(UInt32 index) override { return mRenderModel.mLODModels[0].mSubModels[index].mGeometry; }

private:
    const EntityDistanceCulling* mDistanceCulling;
};
}   // namespace cross