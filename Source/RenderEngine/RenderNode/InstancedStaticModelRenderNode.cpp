#include "EnginePrefix.h"
#include "InstancedStaticModelRenderNode.h"
#include "RenderEngine/GPUScene/GPUScene.h"
#include "RenderEngine/GPUScene/GPUSceneData.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/AABBSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"

namespace cross {

void InstancedStaticModelRenderNode::AllocateGPUScene(GPUScene& GPUScene)
{
    if (mInstanceCount != 0)
    {
        AllocateCullingData(GPUScene, mInstanceCount);

        AllocateObjectAndPrimitiveData(GPUScene, mInstanceCount);
    }
}

void InstancedStaticModelRenderNode::FreeGPUScene(GPUScene& GPUScene)
{
    // Free CullingData
    {
        // for each SubModel
        for (auto& singleLODModel : mRenderModel.mLODModels)
        {
            for (auto& subModel : singleLODModel.mSubModels)
            {
                if (subModel.mPrimitiveCullingGUIDStart != -1)
                {
                    GPUScene.FreeCullingData(subModel.mPrimitiveCullingGUIDStart, subModel.mObjectCullingGUIDStart, mInstanceCount);

                    subModel.mPrimitiveCullingGUIDStart = subModel.mObjectCullingGUIDStart = -1;
                }
            }
        }
    }

    ModelRenderNode::FreeGPUScene(GPUScene);
}

void InstancedStaticModelRenderNode::GenerateDrawUnits(const GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const
{
    if (mInstanceCount == 0)
    {
        return;
    }

    auto& entityData = params.entityData;
    auto& cameraView = params.camera->GetCameraView();
    auto& cameraViewLODSel = params.cameraLODSel->GetCameraView();

    for (auto& subModel : mRenderModel.mLODModels[0].mSubModels)
    {
        Float2 radiusAndDistacne = GetBoundingBoxScreenRadiusAndDistance(subModel.mBoundingBox, params);
        //const auto lodIndex = GetLODIndex(&radiusAndDistacne.x, subModel.GetLODCount(), mLodSetting);
        auto distanceToCamera = radiusAndDistacne.y;

        // TODO(peterwjma): move distance culling to GPU
        // smaller than culledHeight, do not generate drawUnit;
        //if (lodIndex == UINT8_MAX)
        //{
        //    continue;
        //}

        auto* geometry = subModel.mGeometry;
        if (!geometry)
        {
            continue;
        }

        auto* material = subModel.mMaterial;
        auto& drawUnitsDesc = params.drawUnitsDesc;
        const auto& passName = drawUnitsDesc.TagName;

        MaterialR* finalMaterial;
        UInt16 renderGroup;
        if (params.IsDrawable(material, finalMaterial, renderGroup))
        {
            //auto& subModelLOD = subModel.mLODs[lodIndex];
            Assert(subModel.GPUSceneAlloc.PrimitiveAlloc && subModel.GPUSceneAlloc.ObjectAlloc);

            REDDrawUnitFlag flags{};

            if (mRenderModel.mReceiveDecals)
            {
                flags |= REDDrawUnitFlag::ReceiveDecal;
            }

            if (mNeedReverseCullingFace)
            {
                flags |= REDDrawUnitFlag::ReverseFaceOrder;
            }

            UInt32 stateBucketID = REDCulling::CalculateDrawUnitStateBucketID(geometry, finalMaterial, mObjectProperties, passName, flags, 0);

            if (math::InBetween(gRenderGroupOpaque, renderGroup, static_cast<UInt16>(gRenderGroupTransparent - 1u)))
            {
                collector.AddOpaqueBatchableDrawUnit(renderGroup,
                                                     geometry,
                                                     finalMaterial,
                                                     stateBucketID,
                                                     mInstanceCount,
                                                     subModel.mObjectCullingGUIDStart,
                                                     subModel.GPUSceneAlloc.ObjectAlloc->IndexStart,
                                                     subModel.GPUSceneAlloc.ObjectAlloc->BufferView,
                                                     subModel.GPUSceneAlloc.PrimitiveAlloc->BufferView,
                                                     flags);
            }
            else if (math::InBetween(gRenderGroupTransparent, renderGroup, static_cast<UInt16>(gRenderGroupUI - 1u)) || math::InBetween(gRenderGroupGizmoWithSceneDepth, renderGroup, static_cast<UInt16>(gRenderGroupOverlay - 1u)))
            {
                collector.AddTransparentBatchableDrawUnit(distanceToCamera,
                                                          renderGroup,
                                                          geometry,
                                                          finalMaterial,
                                                          stateBucketID,
                                                          mInstanceCount,
                                                          subModel.mObjectCullingGUIDStart,
                                                          subModel.GPUSceneAlloc.ObjectAlloc->IndexStart,
                                                          subModel.GPUSceneAlloc.ObjectAlloc->BufferView,
                                                          subModel.GPUSceneAlloc.PrimitiveAlloc->BufferView,
                                                          flags);
            }
            else if (math::InBetween(gRenderGroupAfterFSR, renderGroup, static_cast<UInt16>(7000 - 1u)))
            {
                collector.AddTransparentBatchableDrawUnit(distanceToCamera,
                                                          renderGroup,
                                                          geometry,
                                                          finalMaterial,
                                                          stateBucketID,
                                                          mInstanceCount,
                                                          subModel.mObjectCullingGUIDStart,
                                                          subModel.GPUSceneAlloc.ObjectAlloc->IndexStart,
                                                          subModel.GPUSceneAlloc.ObjectAlloc->BufferView,
                                                          subModel.GPUSceneAlloc.PrimitiveAlloc->BufferView,
                                                          flags);
            }
        }
    }
}

void InstancedStaticModelRenderNode::UploadGPUScene(GPUScene& GPUScene, RenderWorld* renderWorld, ecs::EntityID entity)
{
    QUICK_SCOPED_CPU_TIMING("InstancedStaticModelRenderNode::UploadGPUScene");

    if (mInstanceCount == 0)
    {
        return;
    }

    auto* aabbSys = renderWorld->GetRenderSystem<AABBSystemR>();
    auto* renderPropertySys = renderWorld->GetRenderSystem<RenderPropertySystemR>();
    auto allocator = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameAllocator();

    auto [renderPropertyComp, aabbComp] = renderWorld->GetComponent<RenderPropertyComponentR, AABBComponentR>(entity);

    UInt32 currEngineFrameCount = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();

    // Cache WorldMatrix per instance
    std::pmr::vector<Float4x4> worldMatricesCaches(allocator);
    {
        worldMatricesCaches.resize(mInstanceCount);

        const Float3* tranlationDataPtr = reinterpret_cast<const Float3*>(mInstanceDataResource->GetInstanceMemberData("Translation").mData.data());

        const Float3* rotationDataPtr = reinterpret_cast<const Float3*>(mInstanceDataResource->GetInstanceMemberData("Rotation").mData.data());

        const Float3* scaleDataPtr = reinterpret_cast<const Float3*>(mInstanceDataResource->GetInstanceMemberData("Scale").mData.data());

        // for each instance
        for (UInt32 instanceIndex = 0; instanceIndex < mInstanceCount; instanceIndex++)
        {
            Float3 translation(reinterpret_cast<const float*>(tranlationDataPtr));
            tranlationDataPtr++;

            Float3 rotation(reinterpret_cast<const float*>(rotationDataPtr));
            rotationDataPtr++;

            Float3 scale(reinterpret_cast<const float*>(scaleDataPtr));
            scaleDataPtr++;

            Float4x4 instanceToLocal = Float4x4::Compose(scale*mGlobalScale, Quaternion::EulerToQuaternion(rotation), translation);
            Float4x4 instanceToWorld = instanceToLocal * mWorldTransform.RelativeMatrix;

            worldMatricesCaches[instanceIndex] = instanceToWorld;
        }
    }

    // Upload PrimitiveCullingData
    {
        CullingProperty cullingProperty = renderPropertySys->GetCullingProperty(renderPropertyComp.Read());
        bool isAlwaysVisible = cullingProperty == CullingProperty::CULLING_PROPERTY_ALWAYS_VISIBLE;
        UInt32 flag = isAlwaysVisible ? 1u : 0u;

        // for each SubModel
        for (auto& singleLODModel : mRenderModel.mLODModels)
        {
            for (auto& subModel : singleLODModel.mSubModels)
            {
                PrimitiveCullingData cullingData{
                    subModel.mBoundingBox.GetCenter(),
                    currEngineFrameCount,
                    subModel.mBoundingBox.GetExtent(),
                    flag,
                    mWorldTransform.TilePosition,
                };

                void* copyDstBufferPtr = GPUScene.UploadData(sizeof(PrimitiveCullingData), subModel.mPrimitiveCullingGUIDStart * sizeof(PrimitiveCullingData), false);
                memcpy(copyDstBufferPtr, &cullingData, sizeof(PrimitiveCullingData));
            }
        }
    }

    // Upload ObjectCullingData
    {
        // for each SubModel
        for (auto& singleLODModel : mRenderModel.mLODModels)
        {
            for (auto& subModel : singleLODModel.mSubModels)
            {
                Assert(subModel.mPrimitiveCullingGUIDStart != -1 && subModel.mObjectCullingGUIDStart != -1);

                // Allocate CopyDst Buffer
                UInt8* copyDstBufferPtr = reinterpret_cast<UInt8*>(GPUScene.UploadData(sizeof(ObjectCullingData) * mInstanceCount, subModel.mObjectCullingGUIDStart * sizeof(ObjectCullingData), false));

                // for each instance
                for (UInt32 instanceIndex = 0; instanceIndex < mInstanceCount; instanceIndex++)
                {
                    ObjectCullingData cullingData{
                        worldMatricesCaches[instanceIndex],
                        static_cast<UInt32>(subModel.mPrimitiveCullingGUIDStart),
                    };

                    memcpy(copyDstBufferPtr, &cullingData, sizeof(ObjectCullingData));
                    copyDstBufferPtr += sizeof(ObjectCullingData);
                }
            }
        }
    }

    // Upload ObjectData
    {
        auto pool = EngineGlobal::Inst().GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator();
        PropertySet objProps{pool, & mObjectProperties};

        objProps.SetProperty(BuiltInProperty::ce_TilePosition, mWorldTransform.TilePosition);
        objProps.SetProperty(BuiltInProperty::ce_PreTilePosition, mWorldTransform.PreTilePosition);
        objProps.SetProperty(NAME_ID("ce_RootToWorld"), mWorldTransform.RelativeMatrix);
        objProps.SetProperty(NAME_ID("ce_WorldToRoot"), mWorldTransform.RelativeMatrix.Inverted());
        objProps.SetProperty(NAME_ID("ce_LocalBoundsCenter"), mRenderModel.mBoundingBox.GetCenter());
        objProps.SetProperty(NAME_ID("ce_LocalBoundsExtent"), mRenderModel.mBoundingBox.GetExtent());

        for (auto& [_, alloc] : mPrimitiveAllocs)
        {
            auto byteStride = alloc.ProtoType->Layout.ByteSize;
            auto* data = GPUScene.UploadData(byteStride * alloc.IndexCount, byteStride * alloc.IndexStart, false);
            objProps.FillBuffer(alloc.ProtoType->Layout, data);
        }

        for (auto& [key, alloc] : mObjectAllocs)
        {
            auto byteStride = alloc.ProtoType->Layout.ByteSize;
            UInt8* data = reinterpret_cast<UInt8*>(GPUScene.UploadData(byteStride * alloc.IndexCount, byteStride * alloc.IndexStart, false));
            Assert(mPrimitiveAllocs.count(std::get<0>(key)));
            auto ce_PrimitiveIndex = static_cast<UInt32>(mPrimitiveAllocs[std::get<0>(key)].IndexStart);
            objProps.SetProperty(BuiltInProperty::ce_PrimitiveIndex, ce_PrimitiveIndex);

            struct LocalCache
            {
                const UInt8* dataPtr = nullptr;
                void* propertyPtr = nullptr;
                UInt64 stride = 0;
                UInt32 size = 0;
            };

            std::pmr::vector<LocalCache> cache(allocator);
            cache.reserve(alloc.ProtoType->Layout.Members.size());
            for (auto& objectBufferElement : alloc.ProtoType->Layout.Members)
            {
                std::string elementName = objectBufferElement.Name.GetName();
                auto itr = std::ranges::find(mInstanceDataResource->mInstanceMembers, elementName, &resource::InstanceMemberData::mName);
                if (itr != mInstanceDataResource->mInstanceMembers.end())
                {
                    auto& instanceElementData = *itr;
                    auto stride = instanceElementData.mData.size() / mInstanceDataResource->mInstanceCount;
                    auto propertySize = objectBufferElement.Size * objectBufferElement.ArraySize;
                    Assert(propertySize <= stride);
                    cache.emplace_back(reinterpret_cast<const UInt8*>(instanceElementData.mData.data()), objProps.GetPropertyPtr(elementName, propertySize), stride, propertySize);
                }
            }
            // TODO(peterwjma): just for  refector needed
            // for each instance
            for (UInt32 instanceIndex = 0; instanceIndex < mInstanceDataResource->mInstanceCount; instanceIndex++)
            {
                auto invWorld = worldMatricesCaches[instanceIndex].Inverted();
                objProps.SetProperty(BuiltInProperty::ce_World, worldMatricesCaches[instanceIndex]);
                objProps.SetProperty(BuiltInProperty::ce_PreWorld, worldMatricesCaches[instanceIndex]);
                objProps.SetProperty(BuiltInProperty::ce_InvWorld, invWorld);
                objProps.SetProperty(BuiltInProperty::ce_InvTransposeWorld, invWorld.Transpose());
                objProps.SetProperty(BuiltInProperty::ce_InvTransposeInvWorld, worldMatricesCaches[instanceIndex].Transpose());

                // for each ObjectBufferElement
                for (auto& element : cache)
                {
                    memcpy(element.propertyPtr, element.dataPtr + element.stride * instanceIndex, element.size);
                }

                objProps.FillBuffer(alloc.ProtoType->Layout, data);
                data += byteStride;
            }
        }
    }
}

void InstancedStaticModelRenderNode::SetRenderModelInstancedImpl(RenderModel&& renderModel, InstanceDataResourcePtr instanceDataResource)
{
    mRenderModel = std::move(renderModel);

    mInstanceDataResource = instanceDataResource;
    mInstanceCount = mInstanceDataResource ? mInstanceDataResource->mInstanceCount : 0;
}
void InstancedStaticModelRenderNode::SetDistnaceCulling(const EntityDistanceCulling& distanceCulling) {
    mDistanceCulling = distanceCulling;
}
void InstancedStaticModelRenderNode::SetGlobalScale(const float value) {
    mGlobalScale = value;
}
}   // namespace cross