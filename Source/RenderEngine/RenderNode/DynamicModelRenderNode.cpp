#include "EnginePrefix.h"
#include "DynamicModelRenderNode.h"
#include "RenderEngine/GPUScene/GPUScene.h"
#include "RenderEngine/GPUScene/GPUSceneData.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/AABBSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"

namespace cross {

void DynamicModelRenderNode::GenerateDrawUnits(const GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const
{
    auto& entityData = params.entityData;
    auto& cameraView = params.camera->GetCameraView();
    auto& cameraViewLODSel = params.cameraLODSel->GetCameraView();

    Float2 radiusAndDistacne = GetEntityScreenRadiusAndDistance(params);
    auto selectedLODIndex = GetLODIndex(&radiusAndDistacne.x, static_cast<UInt8>(mRenderModel.mLODModels.size()), mLodSetting);

    // smaller than culledHeight, do not generate drawUnit;
    if (selectedLODIndex == UINT8_MAX)
        return;

    if (selectedLODIndex >= static_cast<UInt8>(mRenderModel.mLODModels.size()))
        return;

    // for each render node in entity
    for (auto& subModel : mRenderModel.mLODModels[selectedLODIndex].mSubModels)
        //for (auto& subModel : mSubModels)
    {
        auto distanceToCamera = GetBoundingBoxScreenRadiusAndDistance(subModel.mBoundingBox, params).y;

        if (mDistanceCulling && mDistanceCulling->IsCulled(GetScaledCulledDistance(distanceToCamera)))
        {
            continue;
        }

        auto* geometry = subModel.mGeometry;
        if (!geometry)
        {
            continue;
        }

        auto* material = subModel.mMaterial;
        const auto& passAllocs = subModel.PassAllocs;

        auto& drawUnitsDesc = params.drawUnitsDesc;
        const auto& passName = drawUnitsDesc.TagName;

        MaterialR* finalMaterial;
        UInt16 renderGroup;
        if (params.IsDrawable(material, finalMaterial, renderGroup))
        {
            REDDrawUnitFlag flags{};

            if (mRenderModel.mReceiveDecals)
            {
                flags |= REDDrawUnitFlag::ReceiveDecal;
            }

            if (mNeedReverseCullingFace)
            {
                flags |= REDDrawUnitFlag::ReverseFaceOrder;
            }

            UInt32 stateBucketID = REDCulling::CalculateDrawUnitStateBucketID(geometry, finalMaterial, mObjectProperties, passName, flags, selectedLODIndex);

            const GPUScenePassAlloc* GPUSceneAlloc = nullptr;
            const bool isMaterialVersion2 = subModel.GPUSceneAlloc.ObjectAlloc != nullptr;

            if (isMaterialVersion2)
            {
                GPUSceneAlloc = &subModel.GPUSceneAlloc;
            }
            else
            {
                if (auto passAllocItr = passAllocs.find(passName); passAllocItr != passAllocs.end())
                {
                    GPUSceneAlloc = &passAllocItr->second;
                }
            }

            if (math::InBetween(gRenderGroupOpaque, renderGroup, static_cast<UInt16>(gRenderGroupTransparent - 1u)))
            {
                if (GPUSceneAlloc)
                {
                    Assert(GPUSceneAlloc->ObjectAlloc);
                    auto* primitiveBufferView = GPUSceneAlloc->PrimitiveAlloc ? GPUSceneAlloc->PrimitiveAlloc->BufferView : nullptr;

                    collector.AddOpaqueBatchableDrawUnit(
                        renderGroup, geometry, finalMaterial, stateBucketID, 1, mObjectCullingGUIDStart, GPUSceneAlloc->ObjectAlloc->IndexStart, GPUSceneAlloc->ObjectAlloc->BufferView, primitiveBufferView, flags);
                }
                else
                {
                    collector.AddOpaqueIsolatedDrawUnit(renderGroup, geometry, finalMaterial, flags, selectedLODIndex, stateBucketID);
                }
            }
            else if (math::InBetween(gRenderGroupTransparent, renderGroup, static_cast<UInt16>(gRenderGroupUI - 1u)) || math::InBetween(gRenderGroupGizmoWithSceneDepth, renderGroup, static_cast<UInt16>(gRenderGroupOverlay - 1u)))
            {
                if (GPUSceneAlloc)
                {
                    Assert(GPUSceneAlloc->ObjectAlloc);
                    auto* primitiveBufferView = GPUSceneAlloc->PrimitiveAlloc ? GPUSceneAlloc->PrimitiveAlloc->BufferView : nullptr;

                    collector.AddTransparentBatchableDrawUnit(
                        distanceToCamera, renderGroup, geometry, finalMaterial, stateBucketID, 1, mObjectCullingGUIDStart, GPUSceneAlloc->ObjectAlloc->IndexStart, GPUSceneAlloc->ObjectAlloc->BufferView, primitiveBufferView, flags);
                }
                else
                {
                    collector.AddTransparentIsolatedDrawUnit(distanceToCamera, renderGroup, geometry, finalMaterial, flags, selectedLODIndex, stateBucketID);
                }
            }
        }
    }
}

void DynamicModelRenderNode::AllocateGPUScene(GPUScene& GPUScene)
{
    SCOPED_CPU_TIMING(GroupRendering, "AllocateGPUScene");

    // Allocate CullingGUID
    {
        Assert(mPrimitiveCullingGUIDStart == -1 && mObjectCullingGUIDStart == -1);

        std::tie(mPrimitiveCullingGUIDStart, mObjectCullingGUIDStart) = GPUScene.AllocateCullingData(1);
    }

    AllocateObjectAndPrimitiveData(GPUScene, 1);
}

void DynamicModelRenderNode::FreeGPUScene(GPUScene& GPUScene)
{
    // Free CullingData
    {
        if (mPrimitiveCullingGUIDStart != -1)
        {
            GPUScene.FreeCullingData(mPrimitiveCullingGUIDStart, mObjectCullingGUIDStart, 1);

            mPrimitiveCullingGUIDStart = mObjectCullingGUIDStart = -1;
        }
    }

    ModelRenderNode::FreeGPUScene(GPUScene);
}

void DynamicModelRenderNode::UploadGPUScene(GPUScene& GPUScene, RenderWorld* renderWorld, ecs::EntityID entity)
{
    QUICK_SCOPED_CPU_TIMING("DynamicModelRenderNode::UploadGPUScene");

    auto* aabbSys = renderWorld->GetRenderSystem<AABBSystemR>();
    auto* renderPropertySys = renderWorld->GetRenderSystem<RenderPropertySystemR>();

    auto [renderPropertyComp, aabbComp] = renderWorld->GetComponent<RenderPropertyComponentR, AABBComponentR>(entity);

    UInt32 currEngineFrameCount = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();

    // Upload CullingData
    if (mPrimitiveCullingGUIDStart != -1)
    {
        CullingProperty cullingProperty = renderPropertySys->GetCullingProperty(renderPropertyComp.Read());
        bool isAlwaysVisible = cullingProperty == CullingProperty::CULLING_PROPERTY_ALWAYS_VISIBLE;
        UInt32 flag = isAlwaysVisible ? 1u : 0u;

        BoundingBox localBoundingBox;
        aabbSys->GetWorldAABB(aabbComp.Read()).Transform(localBoundingBox, mWorldTransform.RelativeMatrix.Inverted());

        PrimitiveCullingData cullingData{
            localBoundingBox.GetCenter(),
            currEngineFrameCount,
            localBoundingBox.GetExtent(),
            flag,
            mWorldTransform.TilePosition,
        };

        void* copyDstBufferPtr = GPUScene.UploadData(sizeof(PrimitiveCullingData), mPrimitiveCullingGUIDStart * sizeof(PrimitiveCullingData));
        memcpy(copyDstBufferPtr, &cullingData, sizeof(PrimitiveCullingData));
    }

    // Upload ObjectCullingData
    if (mObjectCullingGUIDStart != -1)
    {
        ObjectCullingData cullingData{
            mWorldTransform.RelativeMatrix,
            static_cast<UInt32>(mPrimitiveCullingGUIDStart),
        };

        void* copyDstBufferPtr = GPUScene.UploadData(sizeof(ObjectCullingData), mObjectCullingGUIDStart * sizeof(ObjectCullingData));
        memcpy(copyDstBufferPtr, &cullingData, sizeof(ObjectCullingData));
    }

    // Upload ObjectData
    {
        auto pool = EngineGlobal::Inst().GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator();
        PropertySet objProps{pool, &mObjectProperties};

        objProps.SetProperty(BuiltInProperty::ce_World, mWorldTransform.RelativeMatrix);
        objProps.SetProperty(NAME_ID("ce_RootToWorld"), mWorldTransform.RelativeMatrix);
        objProps.SetProperty(BuiltInProperty::ce_PreWorld, mWorldTransform.PreRelativeMatrix);
        objProps.SetProperty(BuiltInProperty::ce_TilePosition, mWorldTransform.TilePosition);
        objProps.SetProperty(BuiltInProperty::ce_PreTilePosition, mWorldTransform.PreTilePosition);
        objProps.SetProperty(BuiltInProperty::ce_InvWorld, mWorldTransform.RelativeMatrix.Inverted());
        objProps.SetProperty(NAME_ID("ce_WorldToRoot"), mWorldTransform.RelativeMatrix.Inverted());
        objProps.SetProperty(BuiltInProperty::ce_InvTransposeWorld, mWorldTransform.RelativeMatrix.Inverted().Transpose());
        objProps.SetProperty(BuiltInProperty::ce_InvTransposeInvWorld, mWorldTransform.RelativeMatrix.Inverted().Transpose().Inverted());
        objProps.SetProperty(NAME_ID("ce_LocalBoundsCenter"), mRenderModel.mBoundingBox.GetCenter());
        objProps.SetProperty(NAME_ID("ce_LocalBoundsExtent"), mRenderModel.mBoundingBox.GetExtent());

        for (auto& [_, alloc] : mPrimitiveAllocs)
        {
            auto byteStride = alloc.ProtoType->Layout.ByteSize;
            auto* date = GPUScene.UploadData(byteStride * alloc.IndexCount, byteStride * alloc.IndexStart);
            objProps.FillBuffer(alloc.ProtoType->Layout, date);
        }

        for (auto& [key, alloc] : mObjectAllocs)
        {
            auto byteStride = alloc.ProtoType->Layout.ByteSize;
            auto* date = GPUScene.UploadData(byteStride * alloc.IndexCount, byteStride * alloc.IndexStart);
            Assert(mPrimitiveAllocs.count(std::get<0>(key)));
            auto ce_PrimitiveIndex = static_cast<UInt32>(mPrimitiveAllocs[std::get<0>(key)].IndexStart);
            objProps.SetProperty(BuiltInProperty::ce_PrimitiveIndex, ce_PrimitiveIndex);
            objProps.FillBuffer(alloc.ProtoType->Layout, date);
        }
    }
}

void DynamicModelRenderNode::SetRenderEffect(RenderEffectTag renderEffect)
{
    RenderNode::SetRenderEffect(renderEffect);

    const auto isStatic = IsStatic();
    mObjectProperties.SetProperty(NAME_ID("WRITES_VELOCITY_TO_GBUFFER"), !isStatic);
}

}   // namespace cross