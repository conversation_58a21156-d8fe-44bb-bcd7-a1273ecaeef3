#include "EnginePrefix.h"
#include "StaticModelRenderNode.h"
#include "RenderEngine/GPUScene/GPUScene.h"
#include "RenderEngine/GPUScene/GPUSceneData.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"
#include "NativeGraphicsInterface/Statistics.h"

#include "Resource/ResourceManager.h"

namespace cross {
void StaticModelRenderNode::AllocateGPUScene(GPUScene& GPUScene)
{
    AllocateCullingData(GPUScene, 1);

    AllocateObjectAndPrimitiveData(GPUScene, 1);
}

void StaticModelRenderNode::GenerateDrawUnits(const GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const
{
    SCOPED_CPU_TIMING(GroupRendering ,"StaticModel::GenerateDrawUnits");
    auto& entityData = params.entityData;
    auto& cameraView = params.camera->GetCameraView();
    auto& cameraViewLODSel = params.cameraLODSel->GetCameraView();
    
    // FIXME: Seems like mRenderModel.mBoundingBox cannot match to params.entityData.boundingBox in the camera tile space
    // Float2 radiusAndDistacne = GetBoundingBoxScreenRadiusAndDistance(mRenderModel.mBoundingBox, params);
    Float2 radiusAndDistance = GetEntityScreenRadiusAndDistance(params);
    auto selectedLODIndex = GetLODIndex(&radiusAndDistance.x, static_cast<UInt8>(mRenderModel.mLODModels.size()), mLodSetting);

    // smaller than culledHeight, do not generate drawUnit;
    if (selectedLODIndex == UINT8_MAX)
        return;

    if (selectedLODIndex >= static_cast<UInt8>(mRenderModel.mLODModels.size()))
        return;

    auto* streamingManager = gResourceMgr.mStreamingMgr;
    if (streamingManager->IsStreamingEnabled())
    {
        if (mRenderModel.mReadyForStreaming)
        {
            int desiredLODCount = static_cast<int>(mRenderModel.mLODModels.size() - selectedLODIndex);
            Assert(desiredLODCount <= mRenderModel.mLODModels.size());
            desiredLODCount = std::clamp(desiredLODCount, 0, static_cast<int>(mRenderModel.mLODModels.size()));
            streamingManager->UpdateDesiredLODCount(params.entityData.entity, desiredLODCount);
            int residentLODCount = streamingManager->GetResidentLODCount(params.entityData.entity);
            if (residentLODCount >= 0)
            {
                selectedLODIndex = std::max(static_cast<UInt8>(mRenderModel.mLODModels.size() - residentLODCount), selectedLODIndex);
                selectedLODIndex = std::clamp(selectedLODIndex, static_cast<UInt8>(0), static_cast<UInt8>(mRenderModel.mLODModels.size() - 1));
            }
        }
    }

    // for each render node in entity
    for (auto& subModel : mRenderModel.mLODModels[selectedLODIndex].mSubModels)
    {
        auto distanceToCamera = GetBoundingBoxScreenRadiusAndDistance(subModel.mBoundingBox, params).y;

#ifdef ENABLE_SCENE_OCTREE
        // Skip the invisible SubModel
        if (entityData.invisibleSubModelSet != nullptr && (*entityData.invisibleSubModelSet).find(subModel.mObjectCullingGUIDStart) != (*entityData.invisibleSubModelSet).end())
        {
            continue;
        }
#endif        

        if (mDistanceCulling && mDistanceCulling->IsCulled(GetScaledCulledDistance(distanceToCamera)))
        {
            continue;
        }

        auto* geometry = subModel.mGeometry;
        if (!geometry)
        {
            continue;
        }

        auto* material = subModel.mMaterial;
        const auto& passAllocs = subModel.PassAllocs;

        auto& drawUnitsDesc = params.drawUnitsDesc;
        const auto& passName = drawUnitsDesc.TagName;

        MaterialR* finalMaterial;
        UInt16 renderGroup;
        if (params.IsDrawable(material, finalMaterial, renderGroup))
        {
            //UInt32 frameCount = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();
            //std::string name = geometry->GetGeometryPacket()->GetVertexStream(0)->GetGpuBuffer()->GetDebugName();
            //EngineMeshStatistics::GetInstance().SetMeshLOD(frameCount, name, selectedLODIndex);

            REDDrawUnitFlag flags{};

            if (mRenderModel.mReceiveDecals)
            {
                flags |= REDDrawUnitFlag::ReceiveDecal;
            }

            if (mNeedReverseCullingFace)
            {
                flags |= REDDrawUnitFlag::ReverseFaceOrder;
            }

            UInt32 stateBucketID = REDCulling::CalculateDrawUnitStateBucketID(geometry, finalMaterial, mObjectProperties, passName, flags, selectedLODIndex);

            const GPUScenePassAlloc* GPUSceneAlloc = nullptr;
            const bool isMaterialVersion2 = params.drawUnitsDesc.OverrideMaterial == nullptr && subModel.GPUSceneAlloc.ObjectAlloc != nullptr;

            if (isMaterialVersion2)
            {
                GPUSceneAlloc = &subModel.GPUSceneAlloc;
            }
            else
            {
                if (auto passAllocItr = passAllocs.find(passName); passAllocItr != passAllocs.end())
                {
                    GPUSceneAlloc = &passAllocItr->second;
                }
            }

            if (math::InBetween(gRenderGroupOpaque, renderGroup, static_cast<UInt16>(gRenderGroupTransparent - 1u)))
            {
                if (GPUSceneAlloc)
                {
                    Assert(GPUSceneAlloc->ObjectAlloc);
                    auto* primitiveBufferView = GPUSceneAlloc->PrimitiveAlloc ? GPUSceneAlloc->PrimitiveAlloc->BufferView : nullptr;

                    auto& drawUnit = collector.AddOpaqueBatchableDrawUnit(renderGroup, 
                                                                          geometry, 
                                                                          finalMaterial, 
                                                                          stateBucketID, 
                                                                          GPUSceneAlloc->ObjectAlloc->BufferView, 
                                                                          primitiveBufferView, 
                                                                          flags);
                    drawUnit.mSingleRun = {1, subModel.mObjectCullingGUIDStart, GPUSceneAlloc->ObjectAlloc->IndexStart};
                }
                else
                {
                    collector.AddOpaqueIsolatedDrawUnit(renderGroup, geometry, finalMaterial, flags, selectedLODIndex, stateBucketID);
                }
            }
            else if (math::InBetween(gRenderGroupTransparent, renderGroup, static_cast<UInt16>(gRenderGroupUI - 1u)) || math::InBetween(gRenderGroupGizmoWithSceneDepth, renderGroup, static_cast<UInt16>(gRenderGroupOverlay - 1u)))
            {
                if (GPUSceneAlloc)
                {
                    Assert(GPUSceneAlloc->ObjectAlloc);
                    auto* primitiveBufferView = GPUSceneAlloc->PrimitiveAlloc ? GPUSceneAlloc->PrimitiveAlloc->BufferView : nullptr;

                    auto& drawUnit = collector.AddTransparentBatchableDrawUnit(distanceToCamera, 
                                                              renderGroup, 
                                                              geometry, 
                                                              finalMaterial, 
                                                              stateBucketID, 
                                                              GPUSceneAlloc->ObjectAlloc->BufferView, 
                                                              primitiveBufferView, flags);
                    drawUnit.mSingleRun = {1, subModel.mObjectCullingGUIDStart, GPUSceneAlloc->ObjectAlloc->IndexStart};
                }
                else
                {
                    collector.AddTransparentIsolatedDrawUnit(distanceToCamera, renderGroup, geometry, finalMaterial, flags, selectedLODIndex, stateBucketID);
                }
            }
        }
    }
}

void StaticModelRenderNode::UploadGPUScene(GPUScene& GPUScene, RenderWorld* renderWorld, ecs::EntityID entity)
{
    QUICK_SCOPED_CPU_TIMING("StaticModelRenderNode::UploadGPUScene");

    auto* renderPropertySys = renderWorld->GetRenderSystem<RenderPropertySystemR>();
    auto renderPropertyComp = renderWorld->GetComponent<RenderPropertyComponentR>(entity);

    UInt32 currEngineFrameCount = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();

    // Upload PrimitiveCullingData
    {
        CullingProperty cullingProperty = renderPropertySys->GetCullingProperty(renderPropertyComp.Read());
        bool isAlwaysVisible = cullingProperty == CullingProperty::CULLING_PROPERTY_ALWAYS_VISIBLE;
        UInt32 flag = isAlwaysVisible ? 1u : 0u;

        // for each SubModel
        for (auto& singleLODModel : mRenderModel.mLODModels)
        {
            for (auto& subModel : singleLODModel.mSubModels)
            {
                PrimitiveCullingData cullingData{
                    subModel.mBoundingBox.GetCenter(),
                    currEngineFrameCount,
                    subModel.mBoundingBox.GetExtent(),
                    flag,
                    mWorldTransform.TilePosition,
                };

                void* copyDstBufferPtr = GPUScene.UploadData(sizeof(PrimitiveCullingData), subModel.mPrimitiveCullingGUIDStart * sizeof(PrimitiveCullingData));
                memcpy(copyDstBufferPtr, &cullingData, sizeof(PrimitiveCullingData));
            }
        }
    }

    // Upload ObjectCullingData
    {
        // for each SubModel
        for (auto& singleLODModel : mRenderModel.mLODModels)
        {
            for (auto& subModel : singleLODModel.mSubModels)
            {
                ObjectCullingData cullingData{
                    mWorldTransform.RelativeMatrix,
                    static_cast<UInt32>(subModel.mPrimitiveCullingGUIDStart),
                };

                void* copyDstBufferPtr = GPUScene.UploadData(sizeof(ObjectCullingData), subModel.mObjectCullingGUIDStart * sizeof(ObjectCullingData));
                memcpy(copyDstBufferPtr, &cullingData, sizeof(ObjectCullingData));
            }
        }
    }

    // Upload ObjectData
    {
        auto pool = EngineGlobal::Inst().GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator();
        PropertySet objProps{pool};

        objProps.SetProperty(BuiltInProperty::ce_World, mWorldTransform.RelativeMatrix);
        objProps.SetProperty(NAME_ID("ce_RootToWorld"), mWorldTransform.RelativeMatrix);
        objProps.SetProperty(BuiltInProperty::ce_PreWorld, mWorldTransform.PreRelativeMatrix);
        objProps.SetProperty(BuiltInProperty::ce_TilePosition, mWorldTransform.TilePosition);
        objProps.SetProperty(BuiltInProperty::ce_PreTilePosition, mWorldTransform.PreTilePosition);
        objProps.SetProperty(BuiltInProperty::ce_InvWorld, mWorldTransform.RelativeMatrix.Inverted());
        objProps.SetProperty(NAME_ID("ce_WorldToRoot"), mWorldTransform.RelativeMatrix.Inverted());
        objProps.SetProperty(BuiltInProperty::ce_InvTransposeWorld, mWorldTransform.RelativeMatrix.Inverted().Transpose());
        objProps.SetProperty(BuiltInProperty::ce_InvTransposeInvWorld, mWorldTransform.RelativeMatrix.Inverted().Transpose().Inverted());
        objProps.SetProperty(NAME_ID("ce_LocalBoundsCenter"), mRenderModel.mBoundingBox.GetCenter());
        objProps.SetProperty(NAME_ID("ce_LocalBoundsExtent"), mRenderModel.mBoundingBox.GetExtent());

        for (auto& [_, alloc] : mPrimitiveAllocs)
        {
            auto byteStride = alloc.ProtoType->Layout.ByteSize;
            auto* date = GPUScene.UploadData(byteStride * alloc.IndexCount, byteStride * alloc.IndexStart);
            objProps.FillBuffer(alloc.ProtoType->Layout, date);
        }

        for (auto& [key, alloc] : mObjectAllocs)
        {
            auto byteStride = alloc.ProtoType->Layout.ByteSize;
            auto* date = GPUScene.UploadData(byteStride * alloc.IndexCount, byteStride * alloc.IndexStart);
            Assert(mPrimitiveAllocs.count(std::get<0>(key)));
            auto ce_PrimitiveIndex = static_cast<UInt32>(mPrimitiveAllocs[std::get<0>(key)].IndexStart);
            objProps.SetProperty(BuiltInProperty::ce_PrimitiveIndex, ce_PrimitiveIndex);
            objProps.FillBuffer(alloc.ProtoType->Layout, date);
        }
    }
}

void StaticModelRenderNode::BuildAccelStruct(RayTracingScene& RayTracingScene, RenderWorld* renderWorld, ecs::EntityID entity)
{
    if (mRenderModel.mLODModels.size() == 0)
    {
        return;
    }
    
    SingleLODModel& targetModel = mRenderModel.mLODModels[0];
    MeshR* parentMesh = targetModel.mParentMesh;
    Assert(parentMesh);

    RendererSystemR* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    NGICommandList* cmd = rendererSystem->GetAccelStructCmd();

    if (parentMesh->GetAccelStruct())
    {
        return;
    }
    
    std::vector<NGIGeometryDesc> geometries(targetModel.mSubModels.size(), NGIGeometryDesc{});

    NGIAccelStructDesc blasDesc;
    blasDesc.DebugName = "";
    blasDesc.IsTopLevel = false;
    blasDesc.BuildFlag = NGIAccelStructBuildFlag::PreferFastTrace;
    for (UInt32 index = 0; index < targetModel.mSubModels.size(); index++)
    {
        auto& geoDesc = geometries[index];
        auto& subModel = targetModel.mSubModels[index];

        geoDesc.GeometryType = NGIGeometryType::Triangle;
        geoDesc.UseTransform = false;
        geoDesc.Flag = NGIGeometryFlag::Opaque;

        auto& triangles = geoDesc.GeometryData.Triangle;
        RenderGeometry* renderGeometry = subModel.mGeometry;
        GeometryPacket* geoPacket = subModel.mGeometry->GetGeometryPacket();
        triangles.IndexBuffer = geoPacket->GetIndexStream()->GetGpuBuffer();
        triangles.IndexFormat = geoPacket->GetIndexFormat();
        triangles.IndexCount = renderGeometry->GetIndexCount();
        UInt32 indexSizeInBytes = triangles.IndexFormat == IndexFormat_UInt16 ? 2 : 4;
        triangles.IndexOffset = geoPacket->GetIndexStream()->GetStreamOffset() + renderGeometry->GetIndexStart() * indexSizeInBytes;

        bool findPosStream = false;
        for (UInt8 streamIndex = 0; streamIndex < geoPacket->GetStreamCount(); streamIndex++)
        {
            const VertexStreamLayout& streamLayout = geoPacket->GetInputLayout().GetVertexStreamLayout(streamIndex);
            // One channel per stream for bindless access
            Assert(streamLayout.GetChannelCount() == 1);
            const VertexChannelLayout& channelLayout = streamLayout.GetChannelLayout(0);
            if (channelLayout.mChannelName == VertexChannel::Position0 && channelLayout.mFormat == VertexFormat::Float3)
            {
                triangles.VertexBuffer = geoPacket->GetVertexStream(streamIndex)->GetGpuBuffer();
                triangles.VertexFormat = GraphicsFormat::R32G32B32_SFloat;
                triangles.VertexOffset = renderGeometry->GetVertexStart() * streamLayout.GetVertexStride();
                triangles.VertexStride = streamLayout.GetVertexStride();
                triangles.VertexCount = renderGeometry->GetVertexCount();
                findPosStream = true;
                break;
            }
        }
        Assert(triangles.IndexBuffer && triangles.VertexBuffer);
    }
    blasDesc.BottomLevelGeometries = geometries;
    
    NGIAccelStruct* accel = cmd->CreateAccelStruct(blasDesc);
    if (parentMesh->GetAccelStruct())
    {
        delete accel;
        return;
    }
    parentMesh->SetAccelStruct(accel);

    UInt64 scratchBufferSize = accel->mSizeInfo.BuildScratchSize;
    NGIBufferDesc scratchBufferDesc{
        .Size = scratchBufferSize,
        .Usage = NGIBufferUsage::RayTracingScratchBuffer
    };
    auto [scratchBuffer, scratchBufferState] =
        rendererSystem->GetTransientResourceManager()->AllocateBuffer(scratchBufferDesc, "BLAS Build Scratch Buffer", true, false);
    cmd->BuildBottomLevelAccelStruct(accel,
        blasDesc.BottomLevelGeometries.data(),
        blasDesc.BottomLevelGeometries.size(),
        blasDesc.BuildFlag,
        scratchBuffer);
}

NGIAccelStruct* StaticModelRenderNode::GetAccelStruct() const
{
    return mRenderModel.mLODModels[0].mParentMesh->GetAccelStruct();
}

}   // namespace cross