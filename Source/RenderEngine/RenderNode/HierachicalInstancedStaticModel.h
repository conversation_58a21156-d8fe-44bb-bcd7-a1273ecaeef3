#pragma once
#include "EnginePrefix.h"
#include "Resource/InstanceDataResource.h"

namespace cross {

struct ClusterTree
{
    std::vector<ClusterNode> Nodes;
    std::vector<SInt32> SortedInstances;
    std::vector<SInt32> InstanceReorderTable;
};

class ClusterBuilder
{
public:
    ClusterBuilder(InstanceDataResourcePtr instanceDataResource, const BoundingBox& instBox);

    void BuildTreeAndResource(float globalScale, SInt32 partitionCount = 1);

private:
    void Init(float globalScale);

    void BuildTree(float globalScale, const SInt32 partitionCount = 1);

    void OverrideInstanceDataResource();

    void Split(SInt32 num);

    void Split(SInt32 start, SInt32 end);

private:
    std::unique_ptr<ClusterTree> mResult;
    InstanceDataResourcePtr mInstanceDataResource;

    std::vector<Float4x4> mInstanceTransforms;

    std::vector<SInt32> mSortIndex;
    std::vector<Float3> mSortPoints;

    struct RunPair
    {
        SInt32 Start;
        SInt32 Num;

        bool operator<(const RunPair& o) const { return Start < o.Start; }
    };
    std::vector<RunPair> mClusters;

    struct SortPair
    {
        float d;
        SInt32 Index;

        bool operator<(const SortPair& o) const { return d < o.d; }
    };
    std::vector<SortPair> mSortPairs;

    SInt32 mMaxInstancesPerLeaf = 16;
    SInt32 mBranchingFactor;
    SInt32 mNumRoot;
    BoundingBox mInstBox;
    SInt32 mInternalNodeBranchingFactor = 16;
    SInt32 mNum;
    static constexpr UInt32 MAX_PARTITION_COUNT = 16;
};
}   // namespace cross