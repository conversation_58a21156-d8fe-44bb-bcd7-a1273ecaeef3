#include "EnginePrefix.h"
#include "ModelRenderNode.h"
#include "RenderEngine/GPUScene/GPUScene.h"
#include "RenderEngine/RenderNodeSystemR.h"

namespace cross {

Float2 ModelRenderNode::GetBoundingBoxScreenRadiusAndDistance(const BoundingBox& boundingBox, const GenerateDrawUnitsParams& params) const
{
    auto& entityData = params.entityData;
    auto& cameraView = params.camera->GetCameraView();
    auto& cameraViewLODSel = params.cameraLODSel->GetCameraView();

    BoundingBox worldBoundingBox;
    boundingBox.Transform(worldBoundingBox, mWorldTransform.RelativeMatrix);
    Float3 center = worldBoundingBox.GetCenter();
    const Float3& extent = worldBoundingBox.GetExtent();

    Float3 cameraWorldPos{cameraView.mInvertViewMatrix.m30, cameraView.mInvertViewMatrix.m31, cameraView.mInvertViewMatrix.m32};
    Float3 distanceToCamera = center - cameraWorldPos;

#ifdef CE_USE_DOUBLE_TRANSFORM
    center += (mWorldTransform.TilePosition - cameraViewLODSel.mCameraTilePosition) * LENGTH_PER_TILE_F;
    distanceToCamera += (mWorldTransform.TilePosition - cameraViewLODSel.mCameraTilePosition) * LENGTH_PER_TILE_F;
#endif

    Float2 outSize;
    CameraUtility::GetScreenSize(outSize, BoundingSphere(center, extent.Length()), params.cameraLODSel->GetCameraView(), params.cameraLODSel->GetProjectionMode());

    return Float2(outSize.y, distanceToCamera.Length());
}

Float2 ModelRenderNode::GetEntityScreenRadiusAndDistance(const GenerateDrawUnitsParams& params) const
{
    auto& entityData = params.entityData;
    auto& cameraView = params.camera->GetCameraView();
    auto& cameraViewLODSel = params.cameraLODSel->GetCameraView();

    Float3 cameraWorldPos{cameraView.mInvertViewMatrix.m30, cameraView.mInvertViewMatrix.m31, cameraView.mInvertViewMatrix.m32};
    Float3 distanceToCamera = entityData.boundingBox.GetCenter() - cameraWorldPos;

#ifdef CE_USE_DOUBLE_TRANSFORM
    distanceToCamera += (mWorldTransform.TilePosition - cameraViewLODSel.mCameraTilePosition) * LENGTH_PER_TILE_F;
#endif

    Float2 outSize;
    CameraUtility::GetScreenSize(outSize, BoundingSphere(entityData.boundingSphereCenter, entityData.boundingSphereRadius), params.cameraLODSel->GetCameraView(), params.cameraLODSel->GetProjectionMode());

    return Float2(outSize.y, distanceToCamera.Length());
}

void ModelRenderNode::SetRenderModelImpl(RenderModel&& renderModel)
{
    mRenderModel = std::move(renderModel);
}

void ModelRenderNode::AllocateCullingData(GPUScene& GPUScene, UInt32 instanceCount)
{
    SCOPED_CPU_TIMING(GroupRendering, "AllocateGPUScene Culling Date");

    // for each SubModel
    for (auto& singleLODModel : mRenderModel.mLODModels)
    {
        for (auto& subModel : singleLODModel.mSubModels)
        {
            Assert(subModel.mPrimitiveCullingGUIDStart == -1 && subModel.mObjectCullingGUIDStart == -1);

            std::tie(subModel.mPrimitiveCullingGUIDStart, subModel.mObjectCullingGUIDStart) = GPUScene.AllocateCullingData(instanceCount);
        }
    }
}

void ModelRenderNode::AllocateObjectAndPrimitiveData(GPUScene& GPUScene, UInt32 instanceCount)
{
    SCOPED_CPU_TIMING(GroupRendering, "AllocateGPUScene Object and Primitive Date");

    for (auto& singleLODModel : mRenderModel.mLODModels)
    {
        for (auto& subModel : singleLODModel.mSubModels)
        {
            auto& allocs = subModel.PassAllocs;

            if (!subModel.mMaterial)
                continue;

            auto* fx = subModel.mMaterial->GetFx();
            if (!fx) continue;

            // TODO(peterwjma): fix it later
            // Assert(fx->GetAllPass().size() == renderNodeLOD.mMaterial->GetPasses().size());

            if (fx->GetVersion() <= 1)
            {
                // for each Pass
                for (auto& [passName, pass] : fx->GetAllPass())
                {
                    if (pass.mShaderPtr)
                    {
                        if (auto* objectProtoType = pass.mShaderPtr->GetObjectProtoType(); objectProtoType)
                        {
                            GPUScenePassAlloc alloc{};
                            if (auto* primitiveProtoType = pass.mShaderPtr->GetPrimitiveProtoType(); primitiveProtoType)
                            {
                                alloc.ObjectAlloc = AllocateObjectGPUScene(GPUScene, primitiveProtoType, objectProtoType, instanceCount);
                                alloc.PrimitiveAlloc = AllocatePrimitiveGPUScene(GPUScene, primitiveProtoType);
                            }
                            else
                            {
                                alloc.ObjectAlloc = AllocatePrimitiveGPUScene(GPUScene, objectProtoType);
                            }
                            allocs.emplace(passName, alloc);
                        }
                    }
                }
            }
            else if (fx->GetVersion() == 2)
            {
                auto [primitiveProtoType, objectProtoType] = fx->GetPrimitiveAndObjectProtoType(MaterialUsage::USED_WITH_DEFAULT);

                if (primitiveProtoType && objectProtoType)
                {
                    subModel.GPUSceneAlloc.PrimitiveAlloc = AllocatePrimitiveGPUScene(GPUScene, primitiveProtoType);
                    subModel.GPUSceneAlloc.ObjectAlloc = AllocateObjectGPUScene(GPUScene, primitiveProtoType, objectProtoType, instanceCount);
                }
            }
        }
    }
}

void ModelRenderNode::FreeGPUScene(GPUScene& GPUScene)
{
    // Free CullingData
    {
        // for each SubModel
        for (auto& singleLODModel : mRenderModel.mLODModels)
        {
            for (auto& subModel : singleLODModel.mSubModels)
            {
                if (subModel.mPrimitiveCullingGUIDStart != -1)
                {
                    GPUScene.FreeCullingData(subModel.mPrimitiveCullingGUIDStart, subModel.mObjectCullingGUIDStart, 1);

                    subModel.mPrimitiveCullingGUIDStart = subModel.mObjectCullingGUIDStart = -1;
                }
            }
        }
    }

    // Free ObjectData
    {
        for (auto& singleLODModel : mRenderModel.mLODModels)
        {
            for (auto& subModel : singleLODModel.mSubModels)
            {
                // for each Pass
                for (auto& [_, passAlloc] : subModel.PassAllocs)
                {
                    passAlloc.PrimitiveAlloc = nullptr;
                    passAlloc.ObjectAlloc = nullptr;
                }

                subModel.PassAllocs.clear();

                subModel.GPUSceneAlloc.PrimitiveAlloc = nullptr;
                subModel.GPUSceneAlloc.ObjectAlloc = nullptr;
            }
        }
    }

    for (auto& [_, alloc] : mPrimitiveAllocs)
    {
        GPUScene.Free(alloc.ProtoType->Layout.ByteSize, alloc.IndexStart, alloc.IndexCount);
    }
    mPrimitiveAllocs.clear();

    for (auto& [_, alloc] : mObjectAllocs)
    {
        GPUScene.Free(alloc.ProtoType->Layout.ByteSize, alloc.IndexStart, alloc.IndexCount);
    }
    mObjectAllocs.clear();
}

//void ModelRenderNode::SetRenderModel(RenderModel&& renderModel)
//{
//    mSubModels = std::vector<SubModel>(renderMeshes.size());   // Attention: RenderNode objects are changed
//
//    for (size_t i = 0; i < mSubModels.size(); ++i)
//    {
//        auto& renderNode = mSubModels[i];
//        auto& renderMesh = renderMeshes[i];
//        renderNode.mLODs.resize(renderMesh.mLODCount);
//
//        for (UInt32 lodIdx = 0; lodIdx < renderNode.mLODs.size(); ++lodIdx)
//        {
//            auto& lod = renderNode.mLODs[lodIdx];
//            lod.mGeometry = renderMesh.mLODGeometries[lodIdx];
//            lod.mMaterial = (0 < lodIdx && lodIdx <= renderMesh.mLODMaterials.size() && renderMesh.mLODMaterials[lodIdx - 1]) ? renderMesh.mLODMaterials[lodIdx - 1] : renderMesh.mDefaultMaterial;
//        }
//
//        renderNode.mReceiveDecals = renderMesh.mReceiveDecals;
//        renderNode.mBoundingBox = renderMesh.mBoundingBox;
//    }
//}

BoundingBox ModelRenderNode::GetSubModelBoundingBox(UInt32 submodelIndex, UInt32 lodIndex) const
{
    if (lodIndex < mRenderModel.mLODModels.size() && submodelIndex < mRenderModel.mLODModels[lodIndex].mSubModels.size())
    {
        BoundingBox retBox;
        mRenderModel.mLODModels[lodIndex].mSubModels[submodelIndex].mBoundingBox.Transform(retBox, mWorldTransform.RelativeMatrix);
        return retBox;
    }

    return BoundingBox();
}

SInt32 ModelRenderNode::GetSubModelObjectGUID(UInt32 submodelIndex, UInt32 lodIndex) const
{
    if (lodIndex < mRenderModel.mLODModels.size() && submodelIndex < mRenderModel.mLODModels[lodIndex].mSubModels.size())
    {
        return mRenderModel.mLODModels[lodIndex].mSubModels[submodelIndex].mObjectCullingGUIDStart;
    }
    return 0;
}

void ModelRenderNode::NotifyMaterialChange(GPUScene& GPUScene, MaterialR* mtlChanged, RenderNodeSystemR* renderNodeSys, ecs::EntityID entity)
{
    for (auto& singleLODModel : mRenderModel.mLODModels)
    {
        for (auto& subModel : singleLODModel.mSubModels)
        {
            if (subModel.mMaterial == mtlChanged)
            {
                FreeGPUScene(GPUScene);
                AllocateGPUScene(GPUScene);
                GPUScene.SetGPUSceneDirty(entity);

                MaterialUpdateEvent e{entity};
                renderNodeSys->DispatchImmediateEvent(e);
                return;
            }
        }
    }
}

ModelRenderNode::GPUSceneAlloc* ModelRenderNode::AllocatePrimitiveGPUScene(GPUScene& GPUScene, const resource::Shader::ProtoType* protoType)
{
    if (auto ret = mPrimitiveAllocs.find(protoType->ID); ret == mPrimitiveAllocs.end())
    {
        auto [indexStart, bufferView] = GPUScene.Allocate(protoType->Layout.ByteSize, 1);

        GPUSceneAlloc alloc{
            static_cast<SInt32>(indexStart),
            1,
            bufferView,
            protoType,
        };

        auto [itr, _] = mPrimitiveAllocs.emplace(protoType->ID, alloc);
        return &itr->second;
    }
    else
    {
        return &ret->second;
    }
}

ModelRenderNode::GPUSceneAlloc* ModelRenderNode::AllocateObjectGPUScene(GPUScene& GPUScene, const resource::Shader::ProtoType* primitiveProtoType, const resource::Shader::ProtoType* objectProtoType, UInt32 count)
{
    auto key = std::make_tuple(primitiveProtoType->ID, objectProtoType->ID);
    if (auto ret = mObjectAllocs.find(key); ret == mObjectAllocs.end())
    {
        auto [indexStart, bufferView] = GPUScene.Allocate(objectProtoType->Layout.ByteSize, count);

        GPUSceneAlloc alloc{
            static_cast<SInt32>(indexStart),
            count,
            bufferView,
            objectProtoType,
        };

        auto [itr, _] = mObjectAllocs.emplace(key, alloc);
        return &itr->second;
    }
    else
    {
        return &ret->second;
    }
}
}   // namespace cross