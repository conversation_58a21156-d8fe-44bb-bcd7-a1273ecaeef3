#include "StellarMeshRenderNode.h"

namespace cross
{

void StellarMeshRenderNode::AllocateGPUScene(GPUScene& GPUScene)
{
    // TODO: how to alloc material here?
}

void StellarMeshRenderNode::FreeGPUScene(GPUScene& GPUScene)
{
    
}

void StellarMeshRenderNode::GenerateDrawUnits(const GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const
{
    
}

void StellarMeshRenderNode::UploadGPUScene(GPUScene& GPUScene, RenderWorld* renderWorld, ecs::EntityID entity)
{
    
}

void StellarMeshRenderNode::FreeStellarMeshScene(StellarMeshScene& stellarMeshScene, ecs::EntityID entity)
{
    stellarMeshScene.Remove(entity);
}

void StellarMeshRenderNode::UploadStellarMeshScene(StellarMeshScene& stellarMeshScene, RenderWorld* renderWorld, ecs::EntityID entity)
{
    // Upload StellarMeshScene Data
    StellarSceneData data{
        mWorldTransform.RelativeMatrix,
        mRenderStellarMesh.mBoundingBox.GetCenter(),
        0,
        mRenderStellarMesh.mBoundingBox.GetExtent(),
        mRenderStellarMesh.mMeshConstantID,
    };

    //LOG_DEBUG("AddOrUpdate Entity: {}, RenderWorld: {}", entity.GetValue(), renderWorld->GetName().GetString());
    stellarMeshScene.AddOrUpdate(entity, data);
}

void StellarMeshRenderNode::SetRenderStellarMesh(RenderStellarMesh renderStellarMesh)
{
    mRenderStellarMesh = std::move(renderStellarMesh);
}

}

