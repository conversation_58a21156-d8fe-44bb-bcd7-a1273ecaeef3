#pragma once

#include "InstancedStaticModelSystemR.h"
#include "HierachicalInstancedStaticModel.h"

namespace cross {
class HierarchicalInstancedStaticModelRenderNode : public InstancedStaticModelRenderNode
{
public:
    virtual void GenerateDrawUnits(const GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const override;
    void BuildTree();

    void SetPartitionCount(int count)
    {
        mPartitionCount = count;
    }

    void SetDensityLodDistanceScalar(float value)
    {
        mDensityLodDistanceScalar = value;
    }

    void UpdateNodeInstanceCountCache();

protected:
    virtual void SetRenderModelInstancedImpl(RenderModel&& renderModel, InstanceDataResourcePtr instanceDataResource) override;


private:
    struct TraverseNode
    {
        SInt32 index;
        SInt32 minLOD;
        SInt32 maxLOD;
        SInt32 minDensityLOD;
        SInt32 maxDensityLOD;
    };

    struct HISMCullInstanceParam
    {
        std::variant<BoundingFrustum, BoundingOrientedBox> bounding;
        CameraProjectionMode cameraMode;
        std::vector<UInt32> LODRuns[resource::MAX_MESH_LOD_NUM];
        std::vector<float> LODDistancePlanes;
        std::vector<float> DensityLODDistancePlanes;
        Float3 CameraRelPosition;

        void AddRun(UInt32 lod, UInt32 firstInstance, UInt32 lastInstance)
        {
            if (lod < resource::MAX_MESH_LOD_NUM)
            {
                AddRun(LODRuns[lod], firstInstance, lastInstance);
            }
        }

        void AddRun(std::vector<UInt32>& vec, UInt32 firstInstance, UInt32 lastInstance)
        {
            if (vec.size() && vec.back() + 1 == firstInstance)
            {
                vec.back() = lastInstance;
            }
            else
            {
                vec.push_back(firstInstance);
                vec.push_back(lastInstance);
            }
        }
    };

    void Traverse(HISMCullInstanceParam& param, SInt32 index, SInt32 minLOD, SInt32 maxLOD, SInt32 minDensityLOD, SInt32 maxDensityLOD, bool isFullyContain) const;

    void TraverseLoop(HISMCullInstanceParam& param, TraverseNode rootNode, bool isFullyContain) const;

private:
    BoundingBox mInstanceBoundingBox;
    SInt32 mPartitionCount = 1;
    float mDensityLodDistanceScalar = 8;
    std::vector<UInt32> mNodeInstanceCountCache;
    size_t mStackSize = 0;
};
}   // namespace cross