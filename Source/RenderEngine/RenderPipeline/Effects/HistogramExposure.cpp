#include "HistogramExposure.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderCamera.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "RenderEngine/RendererSystemR.h"
#include "Resource/AssetStreaming.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "RenderEngine/PostProcessVolumeSystemR.h"

namespace cross {

void ExposurePassInput::GenerateInputData(const GameContext& gameContext) 
{
    inSceneColor = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastPassColor>();
}

void ExposurePassOutput::SetOutputData(const GameContext& gameContext)
{
    
}

void ExposurePass::FillInput(const GameContext& gameContext)
{
    if (auto ffsPipeline = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline); ffsPipeline)
    {
        mSetting = gameContext.mRenderPipeline->GetPostProcessVolumeSetting()->BaseSettings.mPostProcessExposureSetting;
        mInput.GenerateInputData(gameContext);
        mHistogramExposureComputeShader = ffsPipeline->GetSetting()->HistogramExposureComputeShaderR;
        mManualExposureComputeShader = ffsPipeline->GetSetting()->ManualExposureComputeShaderR;
    }
}

void ExposurePass::Execute(const GameContext& gameContext)
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__)
    
    auto& HistogramExposureSetting = mSetting.mHistogramExposureSettings;
    auto& ManualExposureSetting = mSetting.mManualExposureSettings;

    bool EnableHistogramExposure = mSetting.enable && mSetting.mExposureType == ExposureType::Histogram && HistogramExposureSetting.enable;
    bool EnableManualExposure = mSetting.enable && mSetting.mExposureType == ExposureType::Manual && ManualExposureSetting.enable;
    
    auto RED = gameContext.mRenderPipeline->RED();
    if (EnableHistogramExposure)
    {
        float preExposure = GetPreExposure(gameContext);
        RED->SetProperty(BuiltInProperty::ce_PreExposure, preExposure);
        
        auto Width = mInput.inSceneColor->GetWidth();
        auto Height = mInput.inSceneColor->GetHeight();
        
        constexpr UInt16 HistogramSize = 64;
        constexpr UInt16 BucketsPerTexel = 4;
        constexpr UInt16 ThreadGroupSizeX = 64;
        auto* histogramScatterView = IRenderPipeline::CreateTextureView2D("Histogram Texture", HistogramSize * 2, 1, GraphicsFormat::R32_UInt, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);
        auto* histogramClearPass = RED->AllocatePass("histogram_clear");
        histogramClearPass->SetProperty(NAME_ID("_HistogramScatter32Output"), histogramScatterView);
        UInt3 groupSize_pass1;
        mHistogramExposureComputeShader->GetThreadGroupSize("KEyeHistogramClear", groupSize_pass1.x, groupSize_pass1.y, groupSize_pass1.z);
        histogramClearPass->Dispatch(mHistogramExposureComputeShader, "KEyeHistogramClear", HistogramSize / BucketsPerTexel, 1, 1);
        
        Float4 scaleOffsetRes;
        {
            scaleOffsetRes = Float4(0, 0, Width * 1.0f, Height * 1.0f);
        }
        auto expoparam = CalculateHistogramExposureParams(gameContext.mRenderPipeline->GetBuiltInValue<PassSemanticName::ExposureTexResult>());
        
        auto exposureTexView = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::ExposureTexView>().get();
        
        //---------pass 2: accumulate Histogram----------
        auto* histogramComputePass = RED->AllocatePass("HistogramAtomic");
        histogramComputePass->SetProperty(NAME_ID("_HistogramScatter32Output"), histogramScatterView, NGIResourceState::ComputeShaderUnorderedAccess);
        SetPassParameters(expoparam, histogramComputePass);
        histogramComputePass->SetProperty(NAME_ID("_Source"), mInput.inSceneColor, NGIResourceState::ComputeShaderShaderResource);
        histogramComputePass->SetProperty(NAME_ID("_ScaleOffsetRes"), scaleOffsetRes);
        UInt3 groupSize_pass2;
        mHistogramExposureComputeShader->GetThreadGroupSize("MainAtomicCS", groupSize_pass2.x, groupSize_pass2.y, groupSize_pass2.z);
        histogramComputePass->Dispatch(mHistogramExposureComputeShader, "MainAtomicCS", Height, 1, 1);

        //-----------Histogram Convert-------------
        auto* histogramConvertView = IRenderPipeline::CreateTextureView2D("Histogram Convert", HistogramSize / BucketsPerTexel, 2, GraphicsFormat::R32G32B32A32_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);

        auto* histogramConvertPass = RED->AllocatePass("HistogramConvert");
        histogramConvertPass->SetProperty(NAME_ID("_HistogramScatter32Texture"), histogramScatterView, NGIResourceState::ComputeShaderShaderResource);
        histogramConvertPass->SetProperty(NAME_ID("_EyeAdaptationTextureHistory"), exposureTexView, NGIResourceState::ComputeShaderShaderResource);
        histogramConvertPass->SetProperty(NAME_ID("_HistogramOutput"), histogramConvertView, NGIResourceState::ComputeShaderUnorderedAccess);
        histogramConvertPass->SetProperty(NAME_ID("_ScaleOffsetRes"), scaleOffsetRes);
        UInt32 NumGroupsRequired = HistogramSize / ThreadGroupSizeX;
        mHistogramExposureComputeShader->GetThreadGroupSize("HistogramConvertCS", groupSize_pass2.x, groupSize_pass2.y, groupSize_pass2.z);
        histogramConvertPass->Dispatch(mHistogramExposureComputeShader, "HistogramConvertCS", NumGroupsRequired, 1, 1);

        //------------Eye Adaptation pass---------
        float deltaTime = EngineGlobal::Inst().GetRenderEngine()->GetAverageFrameTime();
        auto* eyeadaptationPass = RED->AllocatePass("EyeAdaptation");
        eyeadaptationPass->SetProperty(NAME_ID("_HistogramOutputTexture"), histogramConvertView, NGIResourceState::ComputeShaderShaderResource);
        SetPassParameters(expoparam, eyeadaptationPass);
        eyeadaptationPass->SetProperty(NAME_ID("EyeAdaptation_DeltaWorldTime"), deltaTime);
        eyeadaptationPass->SetProperty(NAME_ID("_EyeAdaptationTenxture"), exposureTexView);
        eyeadaptationPass->SetProperty(NAME_ID("InSceneColor"), mInput.inSceneColor);
        mHistogramExposureComputeShader->GetThreadGroupSize("EyeAdaptationCS", groupSize_pass2.x, groupSize_pass2.y, groupSize_pass2.z);
        eyeadaptationPass->Dispatch(mHistogramExposureComputeShader, "EyeAdaptationCS", UInt3(1, 1, 1));
    }
    else
    {
        // NOTE(scolu): Actually we execute a ManualExposure Pass even if we don't enable ManualExposure,
        //   We write a fixed exposure value 1.f for tone mapping pass and BuiltInValue::ExposureResult
        float exposure = 1.f;
        if (EnableManualExposure)
        {
            exposure = ManualExposureSetting.Exposure;
        }
        
        auto* pass = RED->AllocatePass("fixed_exposure", true);
        auto& exposureTexView = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::ExposureTexView>();
        pass->SetProperty(NAME_ID("exposure_texture"), exposureTexView.get());
        pass->SetProperty(NAME_ID("fixedExposure"), exposure);
        UInt3 threadGroupSize;
        mManualExposureComputeShader->GetThreadGroupSize("ManualExposureCS", threadGroupSize.x, threadGroupSize.y, threadGroupSize.z);
        pass->Dispatch(mManualExposureComputeShader, "ManualExposureCS", UInt3(1, 1, 1));
    }

    if (EnableHistogramExposure)
    {
        ReadBackExposureTexture(gameContext);
    }
    else
    {
        float exposure = 1.f;
        if (EnableManualExposure)
        {
            exposure = ManualExposureSetting.Exposure;
        }
        gameContext.mRenderPipeline->GetBuiltInValue<PassSemanticName::ExposureTexResult>().x = exposure;
    }
    
    mOutput.SetOutputData(gameContext);
}

ExposureParam ExposurePass::CalculateHistogramExposureParams(const Float4& exposureResult)
{
    auto& HistogramExposureSetting = mSetting.mHistogramExposureSettings;
    
    ExposureParam params;
    const bool bExtendedLuminanceRange = false;
    const float PercentToScale = 0.01f;
    params.ExposureHighPercent.mValue = clamp(HistogramExposureSetting.HighPercent, 1.f, 99.f) * PercentToScale;
    params.ExposureLowPercent.mValue = clamp(HistogramExposureSetting.LowPercent, 1.f, 99.f) * PercentToScale;
    float MinWhitePointLuminance = 1.0f;
    float MaxWhitePointLuminance = 1.0f;
    MinWhitePointLuminance = HistogramExposureSetting.MinBrightness;
    MaxWhitePointLuminance = HistogramExposureSetting.MaxBrightness;
    MinWhitePointLuminance = std::min(MinWhitePointLuminance, MaxWhitePointLuminance);

    const float kFrameTimeEps = 1.0f / 60.0f;
    const float StartDistance = 1.5f;
    const float StartTimeUp = StartDistance / std::max(HistogramExposureSetting.SpeedUp, 0.001f);
    const float StartTimeDown = StartDistance / std::max(HistogramExposureSetting.SpeedDown, 0.001f);
    params.ExposureSpeedUp.mValue = HistogramExposureSetting.SpeedUp;
    params.ExposureSpeedDown.mValue = HistogramExposureSetting.SpeedDown;
    params.ExponentialUpM.mValue = kFrameTimeEps / ((1.0f - exp2(-kFrameTimeEps * HistogramExposureSetting.SpeedUp)) * StartTimeUp);
    params.ExponentialDownM.mValue = kFrameTimeEps / ((1.0f - exp2(-kFrameTimeEps * HistogramExposureSetting.SpeedDown)) * StartTimeDown);

    // const float LuminanceMax = 1.0f; 
    const float HistogramLogMax = HistogramExposureSetting.HistogramLogMax;
    const float HistogramLogMin = std::min(HistogramExposureSetting.HistogramLogMin, HistogramLogMax - 1);
    const float HistogramLogDelta = HistogramLogMax - HistogramLogMin;
    params.HistogramScale.mValue = 1.0f / HistogramLogDelta;
    params.HistogramBias.mValue = -HistogramLogMin * params.HistogramScale.mValue;
    constexpr float kMiddleGrey = 0.18f;
    params.MinAverageLuminance.mValue = MinWhitePointLuminance * kMiddleGrey;
    params.MaxAverageLuminance.mValue = MaxWhitePointLuminance * kMiddleGrey;
    const float LuminanceMin = exp2(HistogramLogMin);
    params.LuminanceMin.mValue = LuminanceMin;
    float AutoExposureBias = HistogramExposureSetting.AutoExposureBias;

    auto sceneLuminance = exposureResult.z;
    auto compensationCurve = HistogramExposureSetting.ExposureCompensationCurve.Eval(sceneLuminance);
    params.ExposureCompensationSettings.mValue = pow(2.0f, AutoExposureBias);
    params.ExposureCompensationCurve.mValue = pow(2.0f, compensationCurve);
    return params;
}

float ExposurePass::GetLastEyeAdaptationExposure(const GameContext& gameContext) const
{
    auto& exposureTexResult = gameContext.mRenderPipeline->GetBuiltInValue<PassSemanticName::ExposureTexResult>();
    return exposureTexResult.x;
}

float ExposurePass::GetLastAverageSceneLuminance(const GameContext& gameContext) const
{
    auto& exposureTexResult = gameContext.mRenderPipeline->GetBuiltInValue<PassSemanticName::ExposureTexResult>();
    return exposureTexResult.z;
}

float ExposurePass::GetPreExposure(const GameContext& gameContext) const
{
    float preExposure = 1.0f;
    const float SceneColorTint = 1.0f;
    const float VignetteMask = 1.0f;
    const float LocalExposure = 1.0f;
    float GlobalExposure = 1.0;
    const float LastExposure = GetLastEyeAdaptationExposure(gameContext);
    auto enableManualExposure = gameContext.mRenderPipeline->GetPostProcessVolumeSetting()->BaseSettings.mPostProcessExposureSetting.mManualExposureSettings.enable;
    if (enableManualExposure)
        GlobalExposure = gameContext.mRenderPipeline->GetPostProcessVolumeSetting()->BaseSettings.mPostProcessExposureSetting.mManualExposureSettings.Exposure;
    else if (LastExposure > 0.0)
        GlobalExposure = LastExposure;

    const float FinalPreExposure = SceneColorTint * GlobalExposure * VignetteMask * LocalExposure;
    preExposure = FinalPreExposure;

    return preExposure;
}

void ExposurePass::ReadBackExposureTexture(const GameContext& gameContext)
{
    auto& exposureTexResult = gameContext.mRenderPipeline->GetBuiltInValue<PassSemanticName::ExposureTexResult>();
    auto RED = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
    auto renderSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto& exposureTex = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::ExposureTex>();
    auto size = sizeof(exposureTexResult);
    NGIExtent3D extent = {1, 1, 1};

    renderSystem->ReadBackREDTexture(exposureTex.get(), 0, {0, 0, 0}, extent, exposureTexResult.data(), size, gameContext.mRenderPipeline->GetReadBackSession());
}

void LocalExposure::FillInput(const GameContext& gameContext) 
{
    auto ffssetting = dynamic_cast<const FFSRenderPipelineSetting*>(gameContext.mRenderPipeline->GetSetting());
    mLocalExposureComputeShader = ffssetting->LocalExposureComputeShaderR;
    mLocalSetting = gameContext.mRenderPipeline->GetPostProcessVolumeSetting()->BaseSettings.mPostProcessLocalExposureSetting;
    mExecuteLocalExposure = gameContext.mRenderPipeline->GetPostProcessVolumeSetting()->BaseSettings.mPostProcessLocalExposureSetting.enable;
    ManualExposureVal = gameContext.mRenderPipeline->GetPostProcessVolumeSetting()->BaseSettings.mPostProcessExposureSetting.mManualExposureSettings.Exposure;
    mInput.GenerateInputData(gameContext);
    mPostProcessMtl = gameContext.mRenderPipeline->GetPostProcessMtl();
    mHistogramLogMax = gameContext.mRenderPipeline->GetPostProcessVolumeSetting()->BaseSettings.mPostProcessExposureSetting.mHistogramExposureSettings.HistogramLogMax;
    mHistogramLogMin = gameContext.mRenderPipeline->GetPostProcessVolumeSetting()->BaseSettings.mPostProcessExposureSetting.mHistogramExposureSettings.HistogramLogMin;
}


void LocalExposure::Execute(const GameContext& gameContext) 
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
    
    halfRes = nullptr;
    mOutput.manualExposuredView = mInput.inputcolor;
    
    if (mExecuteLocalExposure)
    {
        auto RED = gameContext.mRenderPipeline->RED();
        
        REDTextureView* Input = mInput.inputcolor;
        const auto& desc = Input->mTexture->mDesc;
        if (!halfRes)
            halfRes = IRenderPipeline::CreateTextureView2D("Scene(1/2)", math::DivideAndRoundUp(desc.Width, 2u), math::DivideAndRoundUp(desc.Height, 2u), desc.Format, desc.Usage);

        IRenderPipeline::PostProcessUtil(
            [&](REDPass* pass) {
                Float2 Input_Extent = Float2{static_cast<float>(Input->GetWidth()), static_cast<float>(Input->GetHeight())};
                Float2 Input_ExtentInverse = Float2{1.0f / Input_Extent.x, 1.0f / Input_Extent.y};
                Float2 Input_UVViewportBilinearMin = 0.5f * Input_ExtentInverse;
                Float2 Input_UVViewportBilinearMax = Input_Extent - 0.5f * Input_ExtentInverse;
                Float2 Output_ExtentInverse = {1.0f / halfRes->GetWidth(), 1.0f / halfRes->GetHeight()};
                pass->SetProperty(NAME_ID("InputTexture"), Input, NGIResourceState::PixelShaderShaderResource);
                pass->SetProperty(NAME_ID("Input_UVViewportBilinearMin"), Input_UVViewportBilinearMin);
                pass->SetProperty(NAME_ID("Input_UVViewportBilinearMax"), Input_UVViewportBilinearMax);
                pass->SetProperty(NAME_ID("Input_ExtentInverse"), Input_ExtentInverse);
                pass->SetProperty(NAME_ID("Output_ExtentInverse"), Output_ExtentInverse);
            },
            mPostProcessMtl,
            gameContext.mRenderPipeline->GetRenderingExecutionDescriptor(),
            "PostProcessDownsample",
            true,
            halfRes);
        
        RED->BeginRegion("LocalExposure");

        auto LEP = CalculateLocalExposureParams(mHistogramLogMax, mHistogramLogMin);

        // Local Exposure - Details
        {
            RED->BeginRegion("LocalExposure - Histogram");

            static constexpr UInt32 BilateralGridDepth = 32;
            static constexpr UInt2 TexelsPerThreadGroup{64, 64};
            const UInt2 NumTiles = math::DivideAndRoundUp(halfRes->GetSize(), TexelsPerThreadGroup);
            const UInt2 ThreadGroupCount = math::DivideAndRoundUp(halfRes->GetSize(), TexelsPerThreadGroup);

            if (mOutput.LumBilateralGrid == nullptr)
                mOutput.LumBilateralGrid =
                    gameContext.mRenderPipeline->CreateTextureView3D("LumBilateralGrid", NumTiles.x, NumTiles.y, BilateralGridDepth, GraphicsFormat::R32G32_SFloat, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess);

            auto pass = RED->AllocatePass("FLocalExposure");
            pass->SetProperty("Input_ViewportMin", UInt2{0, 0});
            pass->SetProperty("Input_ViewportMax", halfRes->GetSize());
            pass->SetProperty("Input_ViewportSizeInverse", Float2{1.0f / halfRes->GetWidth(), 1.0f / halfRes->GetHeight()});
            pass->SetProperty("EyeAdaptation_HistogramScale", LEP.HistogramScale);
            pass->SetProperty("EyeAdaptation_HistogramBias", LEP.HistogramBias);
            pass->SetProperty("EyeAdaptation_LuminanceMin", LEP.LuminanceMin);
            pass->SetProperty("InputTexture", halfRes, NGIResourceState::ComputeShaderShaderResource);
            pass->SetProperty("BilateralGridRWTexture", mOutput.LumBilateralGrid, NGIResourceState::ComputeShaderUnorderedAccess);

            // if (true)
            // {
            //     auto DebugRT = gameContext.mRenderPipeline->CreateTextureView2D("Debug RT", NumTiles.x * 8, NumTiles.y * 8, GraphicsFormat::R32G32B32A32_SFloat, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess);
            //     pass->SetProperty("DebugRWTexture", DebugRT, NGIResourceState::ComputeShaderUnorderedAccess);
            // }

            pass->Dispatch(mLocalExposureComputeShader, NAME_ID("MainCS"), ThreadGroupCount.x, ThreadGroupCount.y, 1);
            RED->EndRegion();
        }

        // Scene DownSample 1/64
        REDTextureView* Scene64_1 = nullptr;
        {
            static constexpr std::string_view SceneDownSampleNames[5] = {
                "Scene(1/4)",
                "Scene(1/8)",
                "Scene(1/16)",
                "Scene(1/32)",
                "Scene(1/64)",
            };

            auto Input2 = halfRes;

            for (int i = 0; i < 5; i++)
            {
                const auto& desc2 = Input2->mTexture->mDesc;
                auto out = gameContext.mRenderPipeline->CreateTextureView2D(SceneDownSampleNames[i], math::DivideAndRoundUp(desc2.Width, 2u), math::DivideAndRoundUp(desc2.Height, 2u), desc2.Format, desc2.Usage);
                IRenderPipeline::PostProcessUtil(
                    [&](REDPass* pass) {
                        Float2 Input_Extent = Float2{static_cast<float>(Input2->GetWidth()), static_cast<float>(Input2->GetHeight())};
                        Float2 Input_ExtentInverse = Float2{1.0f / Input_Extent.x, 1.0f / Input_Extent.y};
                        Float2 Input_UVViewportBilinearMin = 0.5f * Input_ExtentInverse;
                        Float2 Input_UVViewportBilinearMax = Input_Extent - 0.5f * Input_ExtentInverse;
                        Float2 Output_ExtentInverse = {1.0f / out->GetWidth(), 1.0f / out->GetHeight()};
                        pass->SetProperty(NAME_ID("InputTexture"), Input, NGIResourceState::PixelShaderShaderResource);
                        // pass->SetProperty(NAME_ID("InputSampler"), );
                        pass->SetProperty(NAME_ID("Input_UVViewportBilinearMin"), Input_UVViewportBilinearMin);
                        pass->SetProperty(NAME_ID("Input_UVViewportBilinearMax"), Input_UVViewportBilinearMax);
                        pass->SetProperty(NAME_ID("Input_ExtentInverse"), Input_ExtentInverse);
                        pass->SetProperty(NAME_ID("Output_ExtentInverse"), Output_ExtentInverse);
                        // pass->SetProperty(NAME_ID("Output_ViewportMin"), UInt2{0, 0});
                    },
                    gameContext.mRenderPipeline->GetPostProcessMtl(),
                    RED,
                    "PostProcessDownsample",
                    true,
                    out);

                Input2 = out;
            }

            Scene64_1 = Input2;
        }

        // Local Exposure - Blurred luminance helps preserve image appearance and specular highlights, and reduce ringing.
        {
            constexpr UInt2 ThreadGroupSize{8, 8};

            const auto& desc3 = Scene64_1->mTexture->mDesc;
            auto tempLogLum = gameContext.mRenderPipeline->CreateTextureView2D("LogLum Tex (1/64)", desc3.Width, desc3.Height, desc3.Format, desc3.Usage | NGITextureUsage::UnorderedAccess);
            {
                UInt2 ThreadGroupCount = math::DivideAndRoundUp(Scene64_1->GetSize(), ThreadGroupSize);

                auto setupPass = RED->AllocatePass("SetupLogLuminanceCS");
                setupPass->SetProperty("Input_ViewportMin", UInt2{0, 0});
                setupPass->SetProperty("Input_ViewportSize", UInt2{Scene64_1->GetWidth(), Scene64_1->GetHeight()});
                setupPass->SetProperty("EyeAdaptation_LuminanceMin", LEP.LuminanceMin);
                setupPass->SetProperty("InputTexture", Scene64_1, NGIResourceState::ComputeShaderShaderResource);
                setupPass->SetProperty("Output2DFRW", tempLogLum, NGIResourceState::ComputeShaderUnorderedAccess);
                setupPass->Dispatch(mLocalExposureComputeShader, NAME_ID("SetupLogLuminanceCS"), ThreadGroupCount.x, ThreadGroupCount.y, 1);
            }

            // auto tempBlurH = gameContext.mRenderPipeline->CreateTextureView2D("LocalExposureGaussianX", desc.Width, desc.Height, GraphicsFormat::R16_SFloat, desc.Usage);
            auto tempBlurH = gameContext.mRenderPipeline->CreateTextureView2D("LocalExposureGaussianX", desc.Width / 2, desc.Height, GraphicsFormat::R16_SFloat, desc.Usage | NGITextureUsage::UnorderedAccess);
            {
                Float2 Output_ExtentInverse = {1.0f / tempBlurH->GetWidth(), 1.0f / tempBlurH->GetHeight()};
                UInt2 ThreadGroupCount = math::DivideAndRoundUp(tempBlurH->GetSize(), ThreadGroupSize);

                auto GBlurHPass = RED->AllocatePass("GaussianBlurFilterCS H");
                GBlurHPass->SetProperty("BLUR_VERTICAL", false);
                GBlurHPass->SetProperty("Output_ExtentInverse", Output_ExtentInverse);
                GBlurHPass->SetProperty("InputTexture", tempLogLum, NGIResourceState::ComputeShaderShaderResource);
                GBlurHPass->SetProperty("RWOutputTexture", tempBlurH, NGIResourceState::ComputeShaderUnorderedAccess);
                GBlurHPass->Dispatch(mLocalExposureComputeShader, NAME_ID("GaussianBlurFilterCS"), ThreadGroupCount.x, ThreadGroupCount.y, 1);
            }

            if (mOutput.BlurredLogLum == nullptr) {
                mOutput.BlurredLogLum = gameContext.mRenderPipeline->CreateTextureView2D("LocalExposureGaussianY", desc.Width, desc.Height, GraphicsFormat::R16_SFloat, desc.Usage | NGITextureUsage::UnorderedAccess);
                NGIClearValue clearValue{{0, 0, 0, 0}};
                RED->AllocatePass("Clear LocalExposureGaussianY Texture")->ClearTexture(mOutput.BlurredLogLum, clearValue);
            }

            {
                Float2 Output_ExtentInverse = {1.0f / mOutput.BlurredLogLum->GetWidth(), 1.0f / mOutput.BlurredLogLum->GetHeight()};
                UInt2 ThreadGroupCount = math::DivideAndRoundUp(mOutput.BlurredLogLum->GetSize(), ThreadGroupSize);

                auto GBlurVPass = RED->AllocatePass("GaussianBlurFilterCS V");
                GBlurVPass->SetProperty("BLUR_VERTICAL", true);
                GBlurVPass->SetProperty("Output_ExtentInverse", Output_ExtentInverse);
                GBlurVPass->SetProperty("InputTexture", tempBlurH, NGIResourceState::ComputeShaderShaderResource);
                GBlurVPass->SetProperty("RWOutputTexture", mOutput.BlurredLogLum, NGIResourceState::ComputeShaderUnorderedAccess);
                GBlurVPass->Dispatch(mLocalExposureComputeShader, NAME_ID("GaussianBlurFilterCS"), ThreadGroupCount.x, ThreadGroupCount.y, 1);
            }
        }
        RED->EndRegion();
    }
    
    mOutput.manualExposuredView = mInput.inputcolor;
    mOutput.SetOutputData(gameContext);
}

void LocalExposureOutput::SetOutputData(const GameContext& gameContext) 
{
    gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastPassColor>() = manualExposuredView;
}

void LocalExposureInput::GenerateInputData(const GameContext& gameContext) 
{
    inputcolor = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastPassColor>();
}
LocalExpoureParam LocalExposure::CalculateLocalExposureParams(float _HistogramLogMax, float _HistogramLogMin)
{
    LocalExpoureParam LEP;
    //const float LuminanceMax = 1.0f; 
    const float HistogramLogMax = _HistogramLogMax;
    const float HistogramLogMin = std::min(_HistogramLogMin, HistogramLogMax - 1);
    const float HistogramLogDelta = HistogramLogMax - HistogramLogMin;
    LEP.HistogramScale = 1.0f / HistogramLogDelta;
    LEP.HistogramBias = -HistogramLogMin * LEP.HistogramScale;
    LEP.LuminanceMin = exp2(HistogramLogMin);
    return LEP;
}

}   // namespace cross

