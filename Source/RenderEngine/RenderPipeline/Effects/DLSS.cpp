#ifdef WIN32
#include "DLSS.h"
#include "CECommon/Common/EngineGlobal.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "RenderEngine/CloudSystemR.h"
#include "Resource/AssetStreaming.h"
#include "RenderEngine/RenderCamera.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include"Vulkan/VulkanInclude.h"

namespace cross 
{

void DLSSPass::FillInput(const GameContext& gameContext)
{
    {
        bool bCopyFromFrameDepth = true;
        if (bCopyFromFrameDepth)
        {
            auto RED = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
            UInt32 DepthStencilWidth = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>()->GetDesc().Width;
            UInt32 DepthStencilHeight = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>()->GetDesc().Height;

            auto format = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>()->GetDesc().Format;
            NGITextureDesc depthStencilDesc{
                format,
                NGITextureType::Texture2D,
                1,
                1,
                DepthStencilWidth,
                DepthStencilHeight,
                1,
                1,
                NGITextureUsage::DepthStencil | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst,
            };
            gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastFrameDepthStencil>() = RED->AllocateTexture("LastFrameDepthStencil", depthStencilDesc);
            gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastFrameDepthStencil>()->ExtendLifetime();

            NGICopyTexture region1 = {0};
            region1.SrcSubresource = NGICalcSubresource(0, 0, 0, 1, 1);
            region1.SrcOffset = NGIOffset3D{0, 0, 0};
            region1.DstSubresource = NGICalcSubresource(0, 0, 0, 1, 1);
            region1.DstOffset = NGIOffset3D{0, 0, 0};
            region1.Extent = NGIExtent3D{DepthStencilWidth, DepthStencilHeight, 1};

            auto* pass = RED->AllocatePass("CopyDepthStencil");
            pass->CopyTextureToTexture(
                gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastFrameDepthStencil>(), gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>(), 1, &region1);
        }
        else
        {
            gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>()->ExtendLifetime();
            gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastFrameDepthStencil>() = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>();
        }
        //successCheck(SLWrapper::Get().pslGetNewFrameToken(SLWrapper::Get().m_currentFrame, nullptr), "SL_GetFrameToken");
        auto ffspipeline = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
        if (!ffspipeline)
            return;
        mSetting = ffspipeline->GetSetting()->mDLSSSetting;
    }
}



 sl::DLSSMode GetDLSSMode(DLSSMode DLSSMode)
{
    switch (DLSSMode)
    {
    case DLSSMode::eBalanced:
        return sl::DLSSMode::eBalanced;
        break;
    case DLSSMode::eMaxQuality:
        return sl::DLSSMode::eMaxQuality;
        break;
    case DLSSMode::eUltraPerformance:
        return sl::DLSSMode::eUltraPerformance;
        break;
    case DLSSMode::eMaxPerformance:
        return sl::DLSSMode::eMaxPerformance;
        break;
    case DLSSMode::eDLAA:
        return sl::DLSSMode::eDLAA;
        break;
    default:
        return sl::DLSSMode::eOff;
        break;
    }
}

sl::DLSSPreset GetPreset(DLSSPreset preset) 
{
    switch (preset)
    {
    case DLSSPreset::ePresetA:
        return sl::DLSSPreset::ePresetA;
    case DLSSPreset::ePresetB:
        return sl::DLSSPreset::ePresetB;
    case DLSSPreset::ePresetC:
        return sl::DLSSPreset::ePresetC;
    case DLSSPreset::ePresetD:
        return sl::DLSSPreset::ePresetD;
    case DLSSPreset::ePresetE:
        return sl::DLSSPreset::ePresetE;
    case DLSSPreset::ePresetF:
        return sl::DLSSPreset::ePresetF;
    case DLSSPreset::ePresetG:
        return sl::DLSSPreset::ePresetG;
    default:
        return sl::DLSSPreset::eDefault;
    }
}



 void DLSSPass::Init(const GameContext& gameContext)
 {
     // DLSS Options
     if (SLWrapper::Get().GetDLSSAvailable())
     {
         if (SLWrapper::Get().m_viewports.find(gameContext.mRenderPipeline) == SLWrapper::Get().m_viewports.end())
         {
             SLWrapper::Get().m_viewports[gameContext.mRenderPipeline] = sl::ViewportHandle(SLWrapper::Get().viewportBase++);
         }
         m_DisplaySize = gameContext.mRenderPipeline->GetDisplaySize();
         if (mSetting.bBoostMode)
         {
             // Coresponding to max upscale of DLSS, will be downsampled in postprocess pass
             m_DisplaySize = gameContext.mRenderPipeline->GetRenderSize();
             m_DisplaySize.x *= 3;
             m_DisplaySize.y *= 3;
         }
         sl::DLSSOptions dlssConstants = {};
         // DLSS Open or Close Check
         //if ((DLSS_Last_Mode != DLSSMode::eOff && mSetting.DLSS_Mode == DLSSMode::eOff) || (lastEnable == true && mSetting.enable == false))
         if (DLSS_Last_Mode != DLSSMode::eOff && mSetting.DLSS_Mode == DLSSMode::eOff)
         {
             DLSS_Last_Mode = DLSSMode::eOff;
             DLSS_Last_DisplaySize = {0, 0};
             SLWrapper::Get().CleanUpDLSS(SLWrapper::Get().m_viewports[gameContext.mRenderPipeline] ,true);
         }
         lastEnable = mSetting.enable;
         
        //  else if (lastEnable == false && mSetting.enable == true)
        //  {
        //      DLSS_Last_Mode = DLSSMode::eBalanced;
        //      mSetting.DLSS_Mode = DLSSMode::eBalanced;
        //      DLSS_Last_DisplaySize = {0, 0};
        //  }
     
         if (mSetting.enable && mSetting.DLSS_Mode != DLSSMode::eOff)
         {
             dlssConstants.mode = GetDLSSMode(mSetting.DLSS_Mode);
             if (mSetting.bBoostMode)
             {
                 dlssConstants.mode = sl::DLSSMode::eUltraPerformance;
             }

             if (CheckDLSSPresentChanged(mSetting))
             {
                 SLWrapper::Get().CleanUpDLSS(SLWrapper::Get().m_viewports[gameContext.mRenderPipeline], true);
             }
            /* dlssConstants.outputWidth = gameContext.mRenderPipeline->GetDisplaySize().x;
             dlssConstants.outputHeight = gameContext.mRenderPipeline->GetDisplaySize().y;*/
             dlssConstants.outputWidth = m_DisplaySize.x;
             dlssConstants.outputHeight = m_DisplaySize.y;
             dlssConstants.colorBuffersHDR = mSetting.IsColorBuffersHDR();
             dlssConstants.useAutoExposure = mSetting.IsUseAutoExposure();
             dlssConstants.sharpness = recommendedDLSSSettings.sharpness;
             dlssConstants.dlaaPreset = GetPreset(mSetting.dlaaPreset);
             dlssConstants.qualityPreset = GetPreset(mSetting.qualityPreset);
             dlssConstants.balancedPreset = GetPreset(mSetting.balancedPreset);
             dlssConstants.performancePreset = GetPreset(mSetting.performancePreset);
             dlssConstants.ultraPerformancePreset = GetPreset(mSetting.ultraPerformancePreset);
             SLWrapper::Get().m_dlss_consts = dlssConstants;
             successCheck(pslDLSSSetOptions(SLWrapper::Get().m_viewports[gameContext.mRenderPipeline], SLWrapper::Get().m_dlss_consts), "slDLSSSetOptions");
             // Check DLSS Mode Change
             bool DLSS_resizeRequired = (mSetting.DLSS_Mode != DLSS_Last_Mode) || (m_DisplaySize.x != DLSS_Last_DisplaySize.x) || (m_DisplaySize.y != DLSS_Last_DisplaySize.y);
             if (DLSS_resizeRequired)
             {
                 SLWrapper::Get().QueryDLSSOptimalSettings(recommendedDLSSSettings);

                 if (recommendedDLSSSettings.optimalRenderSize.x <= 0 || recommendedDLSSSettings.optimalRenderSize.y <= 0)
                 {
                     //useless
                     mSetting.enable = false;
                     mSetting.DLSS_Mode = DLSSMode::eOff;
                     m_RenderingRectSize = m_DisplaySize;
                    
                 }
                 else
                 {
                     DLSS_Last_Mode = mSetting.DLSS_Mode;
                     DLSS_Last_DisplaySize = m_DisplaySize;
                 }
                 SLWrapper::Get().m_device->WaitIdle();
             }
           /*  UInt2 maxSize = recommendedDLSSSettings.maxRenderSize;
             UInt2 minSize = recommendedDLSSSettings.minRenderSize;*/

             //float texLodXDimension;

             
             if (mSetting.DLSS_Mode != DLSSMode::eOff)
             {
                 m_RenderingRectSize = recommendedDLSSSettings.optimalRenderSize;
                 //texLodXDimension = (float)m_RenderingRectSize.x;
             }
             //float lodBias = std::log2f(texLodXDimension / m_DisplaySize.x) - 1;
             // TODO : Send Lod Bias to Engine
             // Engine.loadbias=lodbias;


         }
         else
         {
             dlssConstants.mode = sl::DLSSMode::eOff;
             SLWrapper::Get().m_dlss_consts = dlssConstants;
             successCheck(pslDLSSSetOptions(SLWrapper::Get().m_viewports[gameContext.mRenderPipeline], SLWrapper::Get().m_dlss_consts), "slDLSSSetOptions");
             m_RenderingRectSize = m_DisplaySize;
         }

        /* successCheck(pslDLSSSetOptions(SLWrapper::Get().m_viewports[gameContext.mRenderPipeline], SLWrapper::Get().m_dlss_consts),
                      "slDLSSSetOptions");*/
         sl::Constants slConstants = {};
         auto camera = gameContext.mRenderPipeline->GetRenderCamera();
         slConstants.cameraAspectRatio = camera->GetAspectRatio();
         slConstants.cameraFOV = camera->GetFOV();
         slConstants.cameraFar = camera->GetFarPlane();
         slConstants.cameraMotionIncluded = sl::Boolean::eTrue;
         slConstants.cameraNear = camera->GetNearPlane();
         
         slConstants.cameraPinholeOffset = {0.f, 0.f};
         slConstants.cameraPos = make_sl_float3(camera->GetCameraOrigin());
         // from matrix
         slConstants.cameraFwd = make_sl_float3(Float3(camera->GetInvertViewMatrix().m20, camera->GetInvertViewMatrix().m21, camera->GetInvertViewMatrix().m22));
         slConstants.cameraUp = make_sl_float3(Float3(camera->GetInvertViewMatrix().m10, camera->GetInvertViewMatrix().m11, camera->GetInvertViewMatrix().m12));
         slConstants.cameraRight = make_sl_float3(Float3(camera->GetInvertViewMatrix().m00, camera->GetInvertViewMatrix().m01, camera->GetInvertViewMatrix().m02));
         //slConstants.cameraRight = make_sl_float3(Float3(-1.0, 0.0, 0.0));
         // no jitter
         slConstants.cameraViewToClip = make_sl_float4x4(camera->GetProjMatrix());
         slConstants.clipToCameraView = make_sl_float4x4(camera->GetInvertProjMatrix());
         slConstants.clipToPrevClip = make_sl_float4x4(camera->GetReprojectionClipToPrevClip());
         slConstants.depthInverted = sl::Boolean::eTrue;

         slConstants.jitterOffset = make_sl_float2(gameContext.mRenderPipeline->GetJitterData()->jitter);
         slConstants.mvecScale = {1.0f, 1.0f};   // This are scale factors used to normalize mvec (to -1,1) and donut has mvec in pixel space
         slConstants.prevClipToClip = make_sl_float4x4(camera->GetReprojectionPrevClipToClip());
         slConstants.reset = sl::Boolean::eFalse;
         slConstants.motionVectors3D = sl::Boolean::eFalse;
         slConstants.motionVectorsInvalidValue = FLT_MIN;

         SLWrapper::Get().SetSLConsts(slConstants,gameContext);
     }
 }

 void ClearTextureDLSS(RenderingExecutionDescriptor* RED, REDTextureView* texture, NGIClearValue color)
 {
     RED->AllocatePass("Clear " + texture->mTexture->GetName(), true)->ClearTexture(texture, color);
 }

 //void restorePipeline(VkCommandBuffer cmdBuffer)
 //{
 //    VulkanThreadContext* thread = (VulkanThreadContext*)m_getThreadContext();

 //    if (thread->PipelineBindPoint != VK_PIPELINE_BIND_POINT_MAX_ENUM)
 //    {
 //        vkCmdBindPipeline(cmdBuffer, thread->PipelineBindPoint, thread->Pipeline);
 //    }
 //    if (thread->PipelineBindPointDesc != VK_PIPELINE_BIND_POINT_MAX_ENUM)
 //    {
 //        vkCmdBindDescriptorSets(cmdBuffer, thread->PipelineBindPointDesc, thread->Layout, thread->FirstSet, thread->DescriptorCount, thread->DescriptorSets, thread->DynamicOffsetCount, thread->DynamicOffsets);
 //    }

 //    return ComputeStatus::eOk;
 //}

 void AssembleJitterCancel(const GameContext& gameContext,  REDTextureView* input, REDTextureView*& output, bool noCulling)
 {
     Assert(input);

     if (!output)
     {
         const auto& desc = input->mTexture->mDesc;
         const auto size = gameContext.mRenderPipeline->GetDisplaySize();   // display size by default
         output = gameContext.mRenderPipeline->CreateTextureView2D("FSR2 Jitter Cancel Output", size.x, size.y, desc.Format, desc.Usage);
     }

     REDPass* pass = nullptr;
     NGIClearValue clearValue{{0, 0, 0, 0}};

     auto* RED = gameContext.mRenderPipeline->RED();
     if (FormatHasDepth(output->mTexture->mDesc.Format))
     {
         clearValue.depthStencil = { 0.f , 0};
         REDDepthStencilTargetDesc depthStencilTarget{output, NGILoadOp::Clear, NGIStoreOp::Store, NGILoadOp::DontCare, NGIStoreOp::DontCare, clearValue};

         RED->BeginRenderPass("FFX_FSR2_DEPTH_JITTER_CANCEL", 0, nullptr, &depthStencilTarget, noCulling);
         pass = RED->AllocateSubRenderPass("FFX_FSR2_DEPTH_JITTER_CANCEL", 0, nullptr, 0, nullptr, REDPassFlagBit::NeedDepth);
         pass->SetProperty(NAME_ID("FFX_FSR2_OPTION_UPSCALE_SPATIAL_DEPTH"), true);
     }
     else
     {
         REDColorTargetDesc renderTarget{output, NGILoadOp::Clear, NGIStoreOp::Store, clearValue};
         NGIRenderPassTargetIndex renderTargetIndex = static_cast<NGIRenderPassTargetIndex>(0);

         RED->BeginRenderPass("FFX_FSR2_COLOR_JITTER_CANCEL", 1, &renderTarget, nullptr, noCulling);
         pass = RED->AllocateSubRenderPass("FFX_FSR2_COLOR_JITTER_CANCEL", 0, nullptr, 1, &renderTargetIndex, REDPassFlagBit{0});
         pass->SetProperty(NAME_ID("FFX_FSR2_OPTION_UPSCALE_SPATIAL_DEPTH"), false);
     }

     pass->SetProperty(NAME_ID("FFX_FSR2_OPTION_JITTER_CANCEL"), true);
     pass->SetProperty(NAME_ID("r_input"), input, NGIResourceState::PixelShaderShaderResource);
     pass->SetProperty(NAME_ID("fUVJitterOffset"), gameContext.mRenderPipeline->GetJitterData()->GetOffsetInUVSpace());

     REDDrawScreenQuad drawInfo{gameContext.mRenderPipeline->GetPostProcessMtl(), "ffx_fsr2_spatial_scale"};
     pass->DrawScreenQuad(drawInfo);
     RED->EndRenderPass();
 }

 void DLSSPass::Execute(const GameContext& gameContext)
 {
     {
         Init(gameContext);
         if (mSetting.enable && mSetting.DLSS_Mode != DLSSMode::eOff)
         {

             auto* red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
             auto gbuffer = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::GBufferViews>();
             REDTextureView* motionVectorView = nullptr;
             red->BeginRegion("DLSS");
             const UInt2 renderSize = gameContext.mRenderPipeline->GetRenderSize();
             // 1. Prepare motion vector texture for DLSS
             {
                 motionVectorView = gameContext.mRenderPipeline->CreateTextureView2D(
                     "MotionVectorForDLSS", renderSize.x, renderSize.y, GraphicsFormat::R16G16_SFloat, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess | NGITextureUsage::CopyDst);
                 ClearTextureDLSS(red, motionVectorView, {0.f, 0.f, 0.f, 0.f});

                 const auto jitterData = gameContext.mRenderPipeline->GetJitterData();
                 auto* velocity_pass = red->AllocatePass("MotionVectorPassForDLSS", true);
                 velocity_pass->SetProperty(NAME_ID("ViewSize"), renderSize);
                 velocity_pass->SetProperty(NAME_ID("ViewInvSize"), Float2(1.0f / renderSize.x, 1.0f / renderSize.y));
                 velocity_pass->SetProperty(NAME_ID("fReprojectionMat"), jitterData->GetReprojectionMatrixNoJitter());
                 velocity_pass->SetProperty(NAME_ID("InputDepth"), gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthMapAfterGPassView>(), NGIResourceState::ComputeShaderShaderResource);
                 velocity_pass->SetProperty(NAME_ID("InputVelocity"), gbuffer[3]);
                 velocity_pass->SetProperty(NAME_ID("OutputTexture"), motionVectorView, NGIResourceState::ComputeShaderUnorderedAccess);
                 velocity_pass->Dispatch(mSetting.DLSSComputeShaderR, "ConvertVelocityCS", (renderSize.x + 8 - 1) / 8, (renderSize.y + 8 - 1) / 8, 1);
             }
             // Original SceneDepth
             REDTextureView* depthView = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthMapAfterGPassView>();
             
             auto* pass = red->AllocatePass("DLSS", true);
            
             REDTexture* motionVectorTex = motionVectorView->GetTexture();
             REDTexture* depthTex = depthView->GetTexture();

             REDTextureView* colorInView = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastPassColor>();
             REDTexture* colorInTex = colorInView->GetTexture();

             // REDTexture* exposureTex = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::ExposureTex>();

             const auto& desc = colorInTex->mDesc;
             //auto size = gameContext.mRenderPipeline->GetDisplaySize();

             REDTextureView* colorOutView =
                 gameContext.mRenderPipeline->CreateTextureView2D("DLSS Out Color", m_DisplaySize.x, m_DisplaySize.y, desc.Format, desc.Usage | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::UnorderedAccess);

             REDTexture* colorOutTex = colorOutView->GetTexture();

             pass->AddTextureReference(motionVectorView, REDResourceState(NGIResourceState::ShaderResourceBit | NGIResourceState::ComputeShaderBit));
             pass->AddTextureReference(depthView, REDResourceState(NGIResourceState::ShaderResourceBit | NGIResourceState::ComputeShaderBit));
             pass->AddTextureReference(colorInView, REDResourceState(NGIResourceState::ShaderResourceBit | NGIResourceState::ComputeShaderBit));
             pass->AddTextureReference(colorOutView, REDResourceState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ComputeShaderBit));

             auto lambda = [=](REDPass* pass, NGICommandList* cmdList) {

                 std::scoped_lock<std::mutex> dlss_lock(SLWrapper::Get().GetDLSSExclusiveMutex());

                 sl::Extent renderExtent{0, 0, depthTex->GetDesc().Width, depthTex->GetDesc().Height};
                 sl::Extent fullExtent{0, 0, colorOutTex->GetDesc().Width, colorOutTex->GetDesc().Height};
                 sl::Resource motionVector = {sl::ResourceType::eTex2d,
                                              reinterpret_cast<void*>(motionVectorTex->GetNativeTexture()->GetNativeHandle()),
                                              reinterpret_cast<void*>(motionVectorTex->GetNativeTexture()->GetMemoryNativeHandle()),
                                              reinterpret_cast<void*>(motionVectorView->GetNativeTextureView()->GetNativeHandle()),
                                              VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL};
                 motionVector.width = motionVectorTex->GetDesc().Width;
                 motionVector.height = motionVectorTex->GetDesc().Height;
                 motionVector.mipLevels = motionVectorTex->GetDesc().MipCount;
                 motionVector.arrayLayers = motionVectorTex->GetDesc().ArraySize;
                 motionVector.nativeFormat = SLWrapper::Get().MapGraphicsFormat(motionVectorTex->GetDesc().Format);
                 sl::ResourceTag motionVectorTag = sl::ResourceTag{&motionVector, sl::kBufferTypeMotionVectors, sl::ResourceLifecycle::eValidUntilPresent, &renderExtent};

                 sl::Resource depth = {sl::ResourceType::eTex2d,
                                       reinterpret_cast<void*>(depthTex->GetNativeTexture()->GetNativeHandle()),
                                       reinterpret_cast<void*>(depthTex->GetNativeTexture()->GetMemoryNativeHandle()),
                                       reinterpret_cast<void*>(depthView->GetNativeTextureView()->GetNativeHandle()),
                                       VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL};
                 depth.width = depthTex->GetDesc().Width;
                 depth.height = depthTex->GetDesc().Height;
                 depth.mipLevels = depthTex->GetDesc().MipCount;
                 depth.arrayLayers = depthTex->GetDesc().ArraySize;
                 depth.nativeFormat = SLWrapper::Get().MapGraphicsFormat(depthTex->GetDesc().Format);
                 sl::ResourceTag depthTag = sl::ResourceTag{&depth, sl::kBufferTypeDepth, sl::ResourceLifecycle::eValidUntilPresent, &renderExtent};

                 sl::Resource colorIn = {sl::ResourceType::eTex2d,
                                         reinterpret_cast<void*>(colorInTex->GetNativeTexture()->GetNativeHandle()),
                                         reinterpret_cast<void*>(colorInTex->GetNativeTexture()->GetMemoryNativeHandle()),
                                         reinterpret_cast<void*>(colorInView->GetNativeTextureView()->GetNativeHandle()),
                                         VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL};
                 colorIn.width = colorInTex->GetDesc().Width;
                 colorIn.height = colorInTex->GetDesc().Height;
                 colorIn.mipLevels = colorInTex->GetDesc().MipCount;
                 colorIn.arrayLayers = colorInTex->GetDesc().ArraySize;
                 colorIn.nativeFormat = SLWrapper::Get().MapGraphicsFormat(colorInTex->GetDesc().Format);
                 sl::ResourceTag colorInTag = sl::ResourceTag{&colorIn, sl::kBufferTypeScalingInputColor, sl::ResourceLifecycle::eOnlyValidNow, &renderExtent};

                 sl::Resource colorOut = {sl::ResourceType::eTex2d,
                                          reinterpret_cast<void*>(colorOutTex->GetNativeTexture()->GetNativeHandle()),
                                          reinterpret_cast<void*>(colorOutTex->GetNativeTexture()->GetMemoryNativeHandle()),
                                          reinterpret_cast<void*>(colorOutView->GetNativeTextureView()->GetNativeHandle()),
                                          VK_IMAGE_LAYOUT_GENERAL};
                 colorOut.width = colorOutTex->GetDesc().Width;
                 colorOut.height = colorOutTex->GetDesc().Height;
                 colorOut.mipLevels = colorOutTex->GetDesc().MipCount;
                 colorOut.arrayLayers = colorOutTex->GetDesc().ArraySize;
                 colorOut.nativeFormat = SLWrapper::Get().MapGraphicsFormat(colorOutTex->GetDesc().Format);
                 sl::ResourceTag colorOutTag = sl::ResourceTag{&colorOut, sl::kBufferTypeScalingOutputColor, sl::ResourceLifecycle::eValidUntilPresent, &fullExtent};

                 auto vp = SLWrapper::Get().m_viewports[gameContext.mRenderPipeline];
                 
                 //const sl::BaseStructure* inputs[] = {&vp, &depthTag, &motionVectorTag, &colorInTag, &colorOutTag};
                 //Tag Globally
                 sl::ResourceTag inputs[] = { depthTag, motionVectorTag};
                 successCheck(SLWrapper::Get().pslSetTag(vp, inputs, _countof(inputs), reinterpret_cast<VkCommandBuffer_T*>(cmdList->GetNativeHandle())), "slSetTag_General");

                 sl::ResourceTag inputs2[] = {colorInTag, colorOutTag};
                 successCheck(SLWrapper::Get().pslSetTag(vp, inputs2, _countof(inputs2), reinterpret_cast<VkCommandBuffer_T*>(cmdList->GetNativeHandle())), "slSetTag_dlss_nis");

                 const sl::BaseStructure* inputsDLSS[] = {&vp};
                 successCheck(SLWrapper::Get().pslEvaluateFeature(sl::kFeatureDLSS, *SLWrapper::Get().m_currentFrame, inputsDLSS, _countof(inputsDLSS), reinterpret_cast<VkCommandBuffer_T*>(cmdList->GetNativeHandle())),
                              "slEvaluateFeature_DLSS");
             };
             AssembleJitterCancel(gameContext, depthView, gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DisplayDepthStencil>(), false);
             pass->OnExecute(lambda);
            
             red->EndRegion();
             gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastPassColor>() = colorOutView;
         }
     }
 }
 }   // namespace cross
#endif
