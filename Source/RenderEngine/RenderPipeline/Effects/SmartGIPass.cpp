#include "SmartGIPass.h"
#include "CECommon/Common/EngineGlobal.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "Resource/AssetStreaming.h"
#include "RenderEngine/RenderCamera.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"
#include "RenderEngine/SkyLightSystemR.h"
#include "RenderEngine/PrimitiveGenerator.h"
#include "RenderEngine/PrimitiveRenderSystemR.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "RenderPipeline/FFSRenderPipeline.h"

namespace cross
{
    void SmartGIPassSettings::Initialize()
    {
        LOAD_RENDER_PIPELINE_MATERIAL(SmartVisualizeSceneMtl);
        LOAD_RENDER_PIPELINE_MATERIAL(SmartVisualizeSHCoefsMtl);
        LOAD_RENDER_PIPELINE_MATERIAL(SmartVisualizeCubeMtl);
        LOAD_RENDER_PIPELINE_TEXTURE(EnvBRDFTexture);

        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(UpdateVoxelizeTextureShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(ClearReplacementShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartSceneRadiosityShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartSceneLightingCompositeShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartVisualizeVoxelsShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartVoxelizerUpdateShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartCompactTracesShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartFinalGatherTraceVoxelsShader);

        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartVisualizeSHComputeShader);

        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HiZGeneratorShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartFGTraceHZBShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartImportanceSamplingShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartImportanceSamplingHalfResShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartFinalGatherFilteringShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartFinalGatherFilteringHalfResShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartFinalGatherFilteringProbeShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartFinalGatherIntegrateShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartFinalGatherTemporalShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartFinalGatherScreenProbeComputeShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartFinalGatherRayDebugShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartVoxelizerCompactComputeShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartVoxelizerDirectLightingComputeShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SmartVoxelLightCullingComputeShader);
    }

    const cross::SmartGIPostProcessSetting& SmartGIPassSettings::GetGIPostProcess(const GameContext& gameContext)
    {
        static const cross::SmartGIPostProcessSetting defaultSetting;
        return defaultSetting;
        //return gameContext.mRenderPipeline->GetPostProcessVolumeSetting()->BaseSettings.GISetting;
    }

    PassDesc SmartGIPass::GetPassDesc()
    {
        return PassDesc("SmartGIPass", "this is a compute based smartGI");
    }

    SmartGIPass::~SmartGIPass() 
    {
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        if (mRayDebugFeedbackBuffer[0])
        {
            for (int i = 0; i < RAY_DEBUG_MAX_FLYING_FRAME; i++)
            {
                rendererSystem->DestroyNGIObject(mRayDebugFeedbackBuffer[i]);
            }
        }
        if (mRayDebugFreezeDatas)
        {
            free(mRayDebugFreezeDatas);
        }
    }
    void SmartGIPass::InitializeParams(const GameContext& gameContext)
    {
        auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
        InitializeSmartFGCommon(gameContext);
        InitializeViewParams(gameContext);
        InitializeDebugState();
        if (mSetting.IsEnableVoxelGI(gameContext))
        {
            mVoxelRenderer.InitializeVoxelLightingState(red, mVisualize);
        }
        InitializeOctahedralSolidAngleTexture(gameContext);
        if (mSetting.mEnableDebugRay)
        {
            mRayDebugRayDataSize = sizeof(Float4) * static_cast<UInt32>(std::pow(mSmartFGCommon.mScreenProbeTracingOctahedronResolution, 2)) * 3 + 1;
            if (mRayDebugFeedbackBuffer[0] == nullptr)
            {
                auto desc = NGIBufferDesc{mRayDebugRayDataSize, NGIBufferUsage::CopyDst};
                for (int i = 0; i < RAY_DEBUG_MAX_FLYING_FRAME; i++)
                {
                    mRayDebugFeedbackBuffer[i] = GetNGIDevice().CreateStagingBuffer(desc);
                }
            }
            if (mRayDebugFreezeDatas == nullptr)
            {
                UInt32 bufferSize = mRayDebugRayDataSize;
                mRayDebugFreezeDatas = static_cast<float*>(cross::Memory::Malloc(bufferSize));
            }
        }
    }

    void SmartGIPass::UpdateSkyLightContext(const GameContext& gameContext)
    {
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto* red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
        const auto* skyLightSys = gameContext.mRenderWorld->GetRenderSystem<SkyLightSystemR>();
        if (auto skyTex = skyLightSys->GetSkyLightTexture(); skyTex)
        {
            red->SetProperty(BuiltInProperty::ce_SkyLightTexture, skyTex);
        }
        else
        {
            red->SetProperty(BuiltInProperty::ce_SkyLightTexture, rendererSystem->GetDefaultTextureCubeView());
        }
        if (auto buffer = skyLightSys->GetDiffuseProbe(); buffer)
        {
            red->SetProperty(BuiltInProperty::ce_UE4AmbientProbeSH, buffer);
        }
        red->SetProperty(NAME_ID("ENABLE_SKY_LIGHT_SH"), mSetting.mDebugTraceSkyLightSH);

        Float3 skyColor = skyLightSys->GetColor();
        red->SetProperty(BuiltInProperty::ce_SkyLightColor, skyColor);
        float skyLightIntensity = skyLightSys->GetSkyLightIntensity();
        red->SetProperty(BuiltInProperty::ce_SkyLightIntensity, skyLightIntensity);
    }

    void SmartGIPass::UpdateSmartGIRenderContext(const GameContext& gameContext)
    {
        UpdateContextSmartFGCommon();
        UpdateContextViewParams();
        UpdateSkyLightContext(gameContext);

        auto* red = mRenderPipeline->GetRenderingExecutionDescriptor();

        auto* camera = gameContext.mRenderCamera;
        Double4x4 OffsetMat = Double4x4::Identity();
#ifdef CE_USE_DOUBLE_TRANSFORM
        OffsetMat = Double4x4::CreateTranslation(static_cast<Double3>(camera->GetTilePosition() - camera->GetTilePosition<true>()) * LENGTH_PER_TILE);
#endif
        Float4x4 ClipToPrevClip = static_cast<Float4x4>(Double4x4(camera->GetViewProjMatrix()).Inverted() * OffsetMat * Double4x4(camera->GetLastFrameViewProjMatrix()));
        red->SetProperty(NAME_ID("_View_ClipToPrevClip"), ClipToPrevClip);
    }

    void SmartGIPass::InitializeDebugState()
    {
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto* RED = rendererSystem->GetRenderingExecutionDescriptor();

        auto viewWidth = input_targetView->GetWidth();
        auto viewHeight = input_targetView->GetHeight();

        mDebugState.mDebugSceneColor = IRenderPipeline::CreateTextureView2D(
            "Smart.DebugSceneColor", viewWidth, viewHeight, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::RenderTarget | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst | NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);

        NGIClearValue clearValue{{0, 0, 0, 0}};
        RED->AllocatePass("Clear DebugSceneColor")->ClearTexture(mDebugState.mDebugSceneColor, clearValue);

        if (mSetting.mDebugShowSH3)
        {
            auto shBufSize = sizeof(float) * mDebugState.mDebugSHCoefsSize;
            for (auto i = 0; i < cDebugProbeNum; i++)
            {
                auto* redBuffer = RED->AllocateBuffer("Smart.DebugSHCoefs", NGIBufferDesc{shBufSize, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::CopyDst});
                mDebugState.mDebugSHCoefs[i] = RED->AllocateBufferView(redBuffer, NGIBufferViewDesc{redBuffer->mDesc.Usage, 0, redBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(UInt32)});
                RED->AllocatePass("Clear DebugSHCoefs")->ClearBuffer(mDebugState.mDebugSHCoefs[i], 0);
                if (!mDebugState.mReadBackSHData[i])
                {
                    mDebugState.mReadBackSHData[i] = std::shared_ptr<float>(new float[shBufSize](), std::default_delete<float[]>());
                }
            }
        }
    }

    void cross::SmartGIPass::InitializeSmartFGCommon(const GameContext& gameContext)
    {
        Assert(input_targetView != nullptr && mInitialized == false);
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto* RED = rendererSystem->GetRenderingExecutionDescriptor();
        int GSmartDownsampleFactor = mSetting.GetDownsampleFactor(gameContext);
        float GSmartFinalGatherAdaptiveProbeAllocationFraction = 0.5;
        int GSmartImportanceSamplingNumLevels = 1;
        int GSmartFinalGatherTracingOctahedronResolution = mSetting.IsProbeOctHalfResolution(gameContext) ? 4 : 8;
        int GScreenProbeBRDFOctahedronResolution = mSetting.IsProbeOctHalfResolution(gameContext) ? 4 : 8;
        int GSmartFixedJitterIndex = -1;
        int GSmartFinalGatherNumMips = 1;

        auto viewWidth = input_targetView->GetWidth();
        auto viewHeight = input_targetView->GetHeight();

        UInt2 viewSize(viewWidth, viewHeight);

        auto& out = mSmartFGCommon;
        out.mScreenProbeViewSize = UInt2(static_cast<UInt32>((viewSize.x + GSmartDownsampleFactor - 1) / GSmartDownsampleFactor), static_cast<UInt32>((viewSize.y + GSmartDownsampleFactor - 1) / GSmartDownsampleFactor));
        out.mScreenProbeAtlasViewSize = UInt2(out.mScreenProbeViewSize.x, static_cast<UInt32>(out.mScreenProbeViewSize.y * (1 + GSmartFinalGatherAdaptiveProbeAllocationFraction)));
        out.mScreenProbeAtlasBufferSize = out.mScreenProbeAtlasViewSize;

        out.mNumUniformScreenProbes = out.mScreenProbeViewSize.x * out.mScreenProbeViewSize.y;
        out.mMaxNumAdaptiveProbes = out.mScreenProbeAtlasViewSize.x * (out.mScreenProbeAtlasViewSize.y - out.mScreenProbeViewSize.y);
        out.mFixedJitterIndex = GSmartFixedJitterIndex;

        out.mScreenProbeDownsampleFactor = GSmartDownsampleFactor;
        out.mScreenProbeTracingOctahedronResolution = GSmartFinalGatherTracingOctahedronResolution;
        out.mScreenProbeGatherOctahedronResolution = out.mScreenProbeTracingOctahedronResolution;
        out.mScreenProbeGatherOctahedronResolutionWithBorder = out.mScreenProbeGatherOctahedronResolution + 2 * (1 << (GSmartFinalGatherNumMips - 1));
        out.mScreenProbeGatherMaxMip = static_cast<float>(GSmartFinalGatherNumMips - 1);
        out.mMaxImportanceSamplingOctahedronResolution = out.mScreenProbeTracingOctahedronResolution * (1 << GSmartImportanceSamplingNumLevels);
        out.mScreenProbeBRDFOctahedronResolution = GScreenProbeBRDFOctahedronResolution;

        out.mScreenProbeSceneDepth = IRenderPipeline::CreateTextureView2D(
            "Smart.ScreenProbeSceneDepth", out.mScreenProbeAtlasBufferSize.x, out.mScreenProbeAtlasBufferSize.y, GraphicsFormat::R32_UInt, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        out.mScreenProbeWorldNormal = IRenderPipeline::CreateTextureView2D(
            "Smart.ScreenProbeWorldNormal", out.mScreenProbeAtlasBufferSize.x, out.mScreenProbeAtlasBufferSize.y, GraphicsFormat::R8G8_UNorm, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        out.mScreenProbeWorldSpeed = IRenderPipeline::CreateTextureView2D(
            "Smart.ScreenProbeWorldSpeed", out.mScreenProbeAtlasBufferSize.x, out.mScreenProbeAtlasBufferSize.y, GraphicsFormat::R16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        out.mScreenProbeTranslatedWorldPosition = IRenderPipeline::CreateTextureView2D(
            "Smart.ScreenProbeTranslatedWorldPosition", out.mScreenProbeAtlasBufferSize.x, out.mScreenProbeAtlasBufferSize.y, GraphicsFormat::R32G32B32A32_SFloat, 
            NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);

        auto* redBuffer = RED->AllocateBuffer("Smart.NumAdaptiveScreenProbes", NGIBufferDesc{sizeof(UInt32), NGIBufferUsage::StructuredBuffer | NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::CopyDst});
        out.mNumAdaptiveScreenProbes = RED->AllocateBufferView(redBuffer, NGIBufferViewDesc{redBuffer->mDesc.Usage, 0, redBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(UInt32)});
        redBuffer = RED->AllocateBuffer("Smart.daptiveScreenProbeData", NGIBufferDesc{sizeof(UInt32) * out.mMaxNumAdaptiveProbes, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::CopyDst});
        out.mAdaptiveScreenProbeData = RED->AllocateBufferView(redBuffer, NGIBufferViewDesc{redBuffer->mDesc.Usage, 0, redBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(UInt32)});
        out.mScreenTileAdaptiveProbeHeader = IRenderPipeline::CreateTextureView2D(
            "Smart.ScreenTileAdaptiveProbeHeader", out.mScreenProbeViewSize.x, out.mScreenProbeViewSize.y, GraphicsFormat::R32_UInt, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        out.mScreenTileAdaptiveProbeIndices = IRenderPipeline::CreateTextureView2D(
            "Smart.ScreenTileAdaptiveProbeIndices", viewSize.x, viewSize.y, GraphicsFormat::R16_UInt, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);

        UInt2 traceViewSize = out.GetScreenProbeOctahedronViewSize();
        out.mTraceRadiance =
            IRenderPipeline::CreateTextureView2D("Smart.TraceRadiance", traceViewSize.x, traceViewSize.y, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        out.mTraceHit = IRenderPipeline::CreateTextureView2D("Smart.TraceHit", traceViewSize.x, traceViewSize.y, GraphicsFormat::R32_UInt, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        out.mTraceRadianceFiltered = IRenderPipeline::CreateTextureView2D(
            "Smart.TraceRadianceFiltered", traceViewSize.x, traceViewSize.y, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        out.mTraceHitFiltered =
            IRenderPipeline::CreateTextureView2D("Smart.TraceHitFiltered", traceViewSize.x, traceViewSize.y, GraphicsFormat::R8_UNorm, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
//         out.mTraceMovingFiltered =
//             IRenderPipeline::CreateTextureView2D("Smart.TraceMovingFiltered", traceViewSize.x, traceViewSize.y, GraphicsFormat::R8_UNorm, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        RED->AllocatePass("Clear Smart TraceRadiance")->ClearTexture(out.mTraceRadiance, NGIClearValue({0, 0, 0, 0}));
        RED->AllocatePass("Clear Smart TraceHit")->ClearTexture(out.mTraceHit, NGIClearValue({0, 0, 0, 0}));
        RED->AllocatePass("Clear Smart TraceRadianceFiltered")->ClearTexture(out.mTraceRadianceFiltered, NGIClearValue({0, 0, 0, 0}));
        RED->AllocatePass("Clear Smart TraceHitFiltered")->ClearTexture(out.mTraceHitFiltered, NGIClearValue({0, 0, 0, 0}));
        //RED->AllocatePass("Clear Smart TraceMovingFiltered")->ClearTexture(out.mTraceMovingFiltered, NGIClearValue({0, 0, 0, 0}));

        out.mDummyBlackTexture = IRenderPipeline::CreateTextureView2D("Smart.DummyTexture", 4, 4, GraphicsFormat::R8G8B8A8_SNorm, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        RED->AllocatePass("Clear DummyBlackTexture")->ClearTexture(out.mDummyBlackTexture, NGIClearValue({0, 0, 0, 0}));
        if (mSetting.mIrradianceFormatUseSH3)
        {
            out.mScreenProbeRadianceSHAmbient = IRenderPipeline::CreateTextureView2D("Smart.ScreenProbeRadianceSHAmbient",
                                                                                     out.mScreenProbeAtlasBufferSize.x,
                                                                                     out.mScreenProbeAtlasBufferSize.y,
                                                                                     GraphicsFormat::R11G11B10_UFloatPack32,
                                                                                     NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
            RED->AllocatePass("Clear Smart ScreenProbeRadianceSHAmbient")->ClearTexture(out.mScreenProbeRadianceSHAmbient, NGIClearValue({0, 0, 0, 0}));
            out.mScreenProbeRadianceSHDirectional = IRenderPipeline::CreateTextureView2D("Smart.ScreenProbeRadianceSHDirectional",
                                                                                         out.mScreenProbeAtlasBufferSize.x * 6,
                                                                                         out.mScreenProbeAtlasBufferSize.y,
                                                                                         GraphicsFormat::R16G16B16A16_SFloat,
                                                                                         NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
            RED->AllocatePass("Clear Smart ScreenProbeRadianceSHDirectional")->ClearTexture(out.mScreenProbeRadianceSHDirectional, NGIClearValue({0, 0, 0, 0}));
        }
        else
        {
            out.mScreenProbeIrradianceWithBorder = IRenderPipeline::CreateTextureView2D(
                "Smart.ScreenProbeIrradianceWithBorder", traceViewSize.x, traceViewSize.y, GraphicsFormat::R11G11B10_UFloatPack32, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
            RED->AllocatePass("Clear Smart ScreenProbeIrradianceWithBorder")->ClearTexture(out.mScreenProbeIrradianceWithBorder, NGIClearValue({0, 0, 0, 0}));
        }

        UInt2 octWithBorderSize = out.GetScreenProbeOctahedronWithBorderViewSize();
        out.mScreenProbeFilterRadianceWithBorder = IRenderPipeline::CreateTextureView2D(
            "Smart.ScreenProbeFilterRadianceWithBorder", octWithBorderSize.x, octWithBorderSize.y, GraphicsFormat::R11G11B10_UFloatPack32, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        RED->AllocatePass("Clear Smart ScreenProbeFilterRadianceWithBorder")->ClearTexture(out.mScreenProbeFilterRadianceWithBorder, NGIClearValue({0, 0, 0, 0}));

        out.mDiffuseIndirect = IRenderPipeline::CreateTextureView2D("Smart.DiffuseIndirect", viewWidth, viewHeight, GraphicsFormat::R16G16B16A16_SFloat, 
            NGITextureUsage::RenderTarget | NGITextureUsage::CopyDst | NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);
        out.mRoughSpecularIndirect =
            IRenderPipeline::CreateTextureView2D("Smart.RoughSpecularIndirect", viewWidth, viewHeight, GraphicsFormat::R11G11B10_UFloatPack32, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        RED->AllocatePass("Clear Smart DiffuseIndirect")->ClearTexture(out.mDiffuseIndirect, NGIClearValue({0, 0, 0, 0}));
        RED->AllocatePass("Clear Smart RoughSpecularIndirect")->ClearTexture(out.mRoughSpecularIndirect, NGIClearValue({0, 0, 0, 0}));

        NGIClearValue clearValue{{0, 0, 0, 0}};
        RED->AllocatePass("Clear Smart ScreenProbeSceneDepth")->ClearTexture(out.mScreenProbeSceneDepth, clearValue);
        RED->AllocatePass("Clear Smart ScreenProbeWorldNormal")->ClearTexture(out.mScreenProbeWorldNormal, clearValue);
        RED->AllocatePass("Clear Smart ScreenProbeWorldSpeed")->ClearTexture(out.mScreenProbeWorldSpeed, clearValue);
        RED->AllocatePass("Clear Smart ScreenProbeTranslatedWorldPosition")->ClearTexture(out.mScreenProbeTranslatedWorldPosition, clearValue);
        RED->AllocatePass("Clear Smart ScreenTileAdaptiveProbeHeader")->ClearTexture(out.mScreenTileAdaptiveProbeHeader, clearValue);
        RED->AllocatePass("Clear Smart ScreenTileAdaptiveProbeIndices")->ClearTexture(out.mScreenTileAdaptiveProbeIndices, clearValue);
        RED->AllocatePass("Clear Smart NumAdaptiveScreenProbes")->ClearBuffer(out.mNumAdaptiveScreenProbes, 0);
        RED->AllocatePass("Clear Smart AdaptiveScreenProbeData")->ClearBuffer(out.mAdaptiveScreenProbeData, 0);
    }

    void cross::SmartGIPass::InitializeViewParams(const GameContext& gameContext)
    {
        Assert(input_targetView != nullptr && mInitialized == false);
        auto viewWidth = input_targetView->GetWidth();
        auto viewHeight = input_targetView->GetHeight();

        UInt2 viewSize(viewWidth, viewHeight);
        auto& outViewParams = mSmartViewParams;
        outViewParams.mRectMin = UInt2(0, 0);
        outViewParams.mSizeAndInvSize = Float4(static_cast<float>(viewSize.x), static_cast<float>(viewSize.y), 1.0f / viewSize.x, 1.0f / viewSize.y);
        outViewParams.mScreenPositionScaleBias = Float4(0.5, -0.5, 0.5, 0.5);
        if (mSetting.mFixFrameIndex >= 0)
        {
            outViewParams.mFrameIndex = mSetting.mFixFrameIndex;
        }
        else
        {
            outViewParams.mFrameIndex++;
        }
        outViewParams.mPreExposure = 1.0f;

        const RenderCamera* renderCamera = gameContext.mRenderCamera;
        const auto& invViewMatrix = renderCamera->GetInvertViewMatrix();
        const auto& prevInvViewMatrix = renderCamera->GetLastFrameInvertViewMatrix();
        const auto& projMatrix = renderCamera->GetProjMatrix();
        const Float3 preViewTranslation = Float3(-invViewMatrix.m30, -invViewMatrix.m31, -invViewMatrix.m32);
        const auto& invViewProjectMatrix = renderCamera->GetInvertProjMatrix() * renderCamera->GetInvertViewMatrix();
        auto screenToTranslateWorld = Float4x4(
            1.0f, 0.0f, 0.0f, 0.0f,
            0.0f, 1.0f, 0.0f, 0.0f,
            0.0f, 0.0f, projMatrix.m22, 1,
            0.0f, 0.0f, projMatrix.m32, 0.0f)
            * invViewProjectMatrix 
            * Float4x4(
            1.0f, 0.0f, 0.0f, 0.0f,
            0.0f, 1.0f, 0.0f, 0.0f,
            0.0f, 0.0f, 1.0f, 0.0f,
            preViewTranslation.x, preViewTranslation.y, preViewTranslation.z, 1.0f);

        outViewParams.mScreenToTranslatedWorld = screenToTranslateWorld;
        outViewParams.mPreViewTranslation = preViewTranslation;
        outViewParams.mCameraOrigin = -preViewTranslation;
        outViewParams.mPrevPreViewTranslation = Float3(-prevInvViewMatrix.m30, -prevInvViewMatrix.m31, -prevInvViewMatrix.m32);
#ifdef CE_USE_DOUBLE_TRANSFORM
        outViewParams.mPrevPreViewTranslation = outViewParams.mPrevPreViewTranslation + (-renderCamera->GetTilePosition<true>() + renderCamera->GetTilePosition()) * LENGTH_PER_TILE_F;
#endif
        outViewParams.mCameraVelocityOffset = outViewParams.mCameraOrigin + outViewParams.mPrevPreViewTranslation;
    }

    void cross::SmartGIPass::UpdateContextSmartFGCommon()
    {
        auto* red = mRenderPipeline->GetRenderingExecutionDescriptor();

        const auto& context = mSmartFGCommon;
        red->SetProperty(NAME_ID("_ScreenProbeViewSize"), context.mScreenProbeViewSize);
        red->SetProperty(NAME_ID("_ScreenProbeAtlasViewSize"), context.mScreenProbeAtlasViewSize);
        red->SetProperty(NAME_ID("_ScreenProbeAtlasBufferSize"), context.mScreenProbeAtlasBufferSize);
        red->SetProperty(NAME_ID("_ScreenProbeDownsampleFactor"), context.mScreenProbeDownsampleFactor);

        red->SetProperty(NAME_ID("_NumUniformScreenProbes"), context.mNumUniformScreenProbes);
        red->SetProperty(NAME_ID("_MaxNumAdaptiveProbes"), context.mMaxNumAdaptiveProbes);
        red->SetProperty(NAME_ID("_FixedJitterIndex"), context.mFixedJitterIndex);

        red->SetProperty(NAME_ID("_ScreenProbeTracingOctahedronResolution"), context.mScreenProbeTracingOctahedronResolution);
        red->SetProperty(NAME_ID("_ScreenProbeGatherOctahedronResolution"), context.mScreenProbeGatherOctahedronResolution);
        red->SetProperty(NAME_ID("_ScreenProbeGatherOctahedronResolutionWithBorder"), context.mScreenProbeGatherOctahedronResolutionWithBorder);
        red->SetProperty(NAME_ID("_ScreenProbeGatherMaxMip"), context.mScreenProbeGatherMaxMip);
        red->SetProperty(NAME_ID("_OctahedralSolidAngleTextureResolutionSq"), context.mOctahedralSolidAngleTextureResolutionSq);
        red->SetProperty(NAME_ID("_MaxImportanceSamplingOctahedronResolution"), context.mMaxImportanceSamplingOctahedronResolution);
        red->SetProperty(NAME_ID("_ScreenProbeBRDFOctahedronResolution"), context.mScreenProbeBRDFOctahedronResolution);
    }

    void cross::SmartGIPass::UpdateContextViewParams()
    {
        auto* red = mRenderPipeline->GetRenderingExecutionDescriptor();

        red->SetProperty(NAME_ID("_View_RectMin"), mSmartViewParams.mRectMin);
        red->SetProperty(NAME_ID("_View_SizeAndInvSize"), mSmartViewParams.mSizeAndInvSize);
        // TODO(timllpan): difference with _View_SizeAndInvSize???
        red->SetProperty(NAME_ID("_View_BufferSizeAndInvSize"), mSmartViewParams.mSizeAndInvSize);
        red->SetProperty(NAME_ID("_View_ScreenPositionScaleBias"), mSmartViewParams.mScreenPositionScaleBias);
        red->SetProperty(NAME_ID("_View_ScreenToTranslatedWorld"), mSmartViewParams.mScreenToTranslatedWorld);
        red->SetProperty(NAME_ID("_View_StateFrameIndex"), mSmartViewParams.mFrameIndex);
        red->SetProperty(NAME_ID("_View_StateFrameIndexMod8"), mSmartViewParams.mFrameIndex % 8);
        red->SetProperty(NAME_ID("_View_PreExposure"), mSmartViewParams.mPreExposure);
        red->SetProperty(NAME_ID("_View_PrevPreViewTranslation"), mSmartViewParams.mPrevPreViewTranslation);
        red->SetProperty(NAME_ID("_View_UseReverseZ"), true);
    }

    void cross::SmartGIPass::InitializeOctahedralSolidAngleTexture(const GameContext& gameContext)
    {
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto* RED = rendererSystem->GetRenderingExecutionDescriptor();
        auto* SmartFinalGatherFilteringShader = mSetting.GetSmartFinalGatherFilteringShader(gameContext);

        static UInt32 GSmartOctahedralSolidAngleTextureSize = 16;
        if (!mSmartFGCommon.mOctahedralSolidAngleTex || !RED->Validate(mSmartFGCommon.mOctahedralSolidAngleTex))
        {
            auto texSize = GSmartOctahedralSolidAngleTextureSize;
            mSmartFGCommon.mOctahedralSolidAngleTextureSize = texSize;
            mSmartFGCommon.mOctahedralSolidAngleTextureResolutionSq = static_cast<float>(texSize) * texSize;
            mSmartFGCommon.mOctahedralSolidAngleTexture = IRenderPipeline::CreateTextureView2D("OctahedralSolidAngleTexture", texSize, texSize, GraphicsFormat::R16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);
            auto* pass = RED->AllocatePass("OctahedralSolidAngleCS");
            mSmartFGCommon.SetREDProperty(pass);
            pass->SetProperty(NAME_ID("RWOctahedralSolidAngleTexture"), mSmartFGCommon.mOctahedralSolidAngleTexture);
            pass->SetProperty(NAME_ID("OctahedralSolidAngleTextureSize"), texSize);

            UInt3 groupSize;
            SmartFinalGatherFilteringShader->GetThreadGroupSize("OctahedralSolidAngleCS", groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(SmartFinalGatherFilteringShader, "OctahedralSolidAngleCS", (texSize + groupSize.x - 1) / groupSize.x, (texSize + groupSize.y - 1) / groupSize.y, 1);
            mSmartFGCommon.mOctahedralSolidAngleTex = mSmartFGCommon.mOctahedralSolidAngleTexture->mTexture;
        }
        else
        {
            mSmartFGCommon.mOctahedralSolidAngleTexture = RED->AllocateTextureView(
                mSmartFGCommon.mOctahedralSolidAngleTex, TextureCreateHelper::GetTexture2DViewDesc(mSmartFGCommon.mOctahedralSolidAngleTex->mDesc.Usage, mSmartFGCommon.mOctahedralSolidAngleTex->mDesc.Format, NGITextureAspect::Color));
        }
        mSmartFGCommon.mOctahedralSolidAngleTex->ExtendLifetime();
    }

    cross::REDTextureView* cross::SmartGIPass::AssembleSmartGIDepthPyramid(RenderingExecutionDescriptor* RED, REDTextureView* depthView)
    {
        ComputeShaderR* HiZGeneratorShader = mSetting.HiZGeneratorShaderR;
        auto gameViewWidth = depthView->mTexture->mDesc.Width;
        auto gameViewHeight = depthView->mTexture->mDesc.Height;
        const SInt32 numMipsX = std::max(static_cast<SInt32>(std::ceil(std::log2f(static_cast<float>(gameViewWidth)))) - 1, 1);
        const SInt32 numMipsY = std::max(static_cast<SInt32>(std::ceil(std::log2f(static_cast<float>(gameViewHeight)))) - 1, 1);
        const SInt32 HZBSizeX = 1 << numMipsX;
        const SInt32 HZBSizeY = 1 << numMipsY;

        UInt16 mipLevelNum = std::min(static_cast<UInt16>(numMipsX), static_cast<UInt16>(numMipsY));

        REDTextureView* depthSRV = RED->AllocateTextureView(depthView->mTexture,
                                                             NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                depthView->mTexture->mDesc.Format,
                                                                                NGITextureType::Texture2D,
                                                                                {
                                                                                    NGITextureAspect::Depth,
                                                                                    0,
                                                                                    1,
                                                                                    0,
                                                                                    1,
                                                                                }});

        auto* texture = RED->AllocateTexture(
            "depth pyrimad smartGI",
            NGITextureDesc{GraphicsFormat::R32_SFloat, NGITextureType::Texture2D, mipLevelNum, 1, static_cast<UInt16>(HZBSizeX), static_cast<UInt16>(HZBSizeY), 1, 1, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource});

        REDTextureView* depthPyramid = RED->AllocateTextureView(texture,
                                                                 NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                    GraphicsFormat::R32_SFloat,
                                                                                    NGITextureType::Texture2D,
                                                                                    {
                                                                                        NGITextureAspect::Color,
                                                                                        0,
                                                                                        mipLevelNum,
                                                                                        0,
                                                                                        1,
                                                                                    }});

        UInt3 groupSize_pass1;
        HiZGeneratorShader->GetThreadGroupSize("DownsampleDepth", groupSize_pass1.x, groupSize_pass1.y, groupSize_pass1.z);

        UInt32 width = HZBSizeX * 2;
        UInt32 height = HZBSizeY * 2;

        for (UInt16 i = 0; i < mipLevelNum; i++)
        {
            Float4 dispatchThreadIdToBufferUV;
            Float2 invSrcSize;
            Float2 inputViewportMaxBound{1.0f, 1.0f};

            Float2 srcSize;
            if (i == 0)
            {
                srcSize = Float2(static_cast<float>(gameViewWidth), static_cast<float>(gameViewHeight));
                dispatchThreadIdToBufferUV.z = 0.f / srcSize.x;
                dispatchThreadIdToBufferUV.w = 0.f / srcSize.y;
                inputViewportMaxBound = (srcSize - 0.5f) / srcSize;
            }
            else
            {
                srcSize = Float2(static_cast<float>(HZBSizeX), static_cast<float>(HZBSizeY)) / static_cast<float>(1 << (i - 1));
                dispatchThreadIdToBufferUV.z = dispatchThreadIdToBufferUV.w = 0;
            }
            dispatchThreadIdToBufferUV.x = 2.0f / srcSize.x;
            dispatchThreadIdToBufferUV.y = 2.0f / srcSize.y;
            invSrcSize = Float2(1.0f, 1.0f) / srcSize;

            width = (std::max)(width / 2, 1u);
            height = (std::max)(height / 2, 1u);
            auto* targetView = RED->AllocateTextureView(texture,
                                                         NGITextureViewDesc{NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource,
                                                                            GraphicsFormat::R32_SFloat,
                                                                            NGITextureType::Texture2D,
                                                                            {
                                                                                NGITextureAspect::Color,
                                                                                i,
                                                                                1,
                                                                                0,
                                                                                1,
                                                                            }});
            // targetView->SetExternalState(NGIResourceState::TargetReadWrite);
            // downsample
            auto* downSample1Pass = RED->AllocatePass("depth pyramid SmartGI");
            mSmartFGCommon.SetREDProperty(downSample1Pass);
            downSample1Pass->SetProperty(NAME_ID("_DepthSrc"), depthSRV);
            downSample1Pass->SetProperty(NAME_ID("_DepthDst"), targetView);
            downSample1Pass->SetProperty(NAME_ID("_DispatchThreadIdToBufferUV"), dispatchThreadIdToBufferUV);
            downSample1Pass->SetProperty(NAME_ID("_InvSrcSize"), invSrcSize);
            downSample1Pass->SetProperty(NAME_ID("_InputViewportMaxBound"), inputViewportMaxBound);
            // downSample1Pass->SetProperty(NAME_ID("_MaxSampler"),);

            downSample1Pass->Dispatch(HiZGeneratorShader, "DownsampleDepth", (width + groupSize_pass1.x - 1) / groupSize_pass1.x, (height + groupSize_pass1.y - 1) / groupSize_pass1.y, 1);
            depthSRV = targetView;
        }
        return depthPyramid;
    }

    void cross::SmartGIPass::AssembleSmartGIScreenProbe(const GameContext& gameContext, RenderingExecutionDescriptor* RED, const GBufferType& gBufferViews, REDTextureView* sceneColorView, REDTextureView* depthView)
    {
        auto SmartFinalGatherScreenProbeComputeShader = mSetting.SmartFinalGatherScreenProbeComputeShaderR;
        UInt2 viewSize(depthView->mTexture->mDesc.Width, depthView->mTexture->mDesc.Height);

        REDTextureView* depthSRV = RED->AllocateTextureView(depthView->mTexture,
                                                             NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                depthView->mTexture->mDesc.Format,
                                                                                NGITextureType::Texture2D,
                                                                                {
                                                                                    NGITextureAspect::Depth,
                                                                                    0,
                                                                                    1,
                                                                                    0,
                                                                                    1,
                                                                                }});

        auto passSetupCommon = [&](cross::REDPass* pass) {
            // screen textures
            pass->SetProperty(NAME_ID("_GBuffer0"), gBufferViews[0]);
            pass->SetProperty(NAME_ID("_GBuffer1"), gBufferViews[1]);
            pass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2]);
            pass->SetProperty(NAME_ID("_DepthMap"), depthSRV);
            pass->SetProperty(NAME_ID("_RWScreenProbeSceneDepth"), mSmartFGCommon.mScreenProbeSceneDepth);
            pass->SetProperty(NAME_ID("_RWScreenProbeWorldNormal"), mSmartFGCommon.mScreenProbeWorldNormal);
            pass->SetProperty(NAME_ID("_RWScreenProbeWorldSpeed"), mSmartFGCommon.mScreenProbeWorldSpeed);
            pass->SetProperty(NAME_ID("_RWScreenProbeTranslatedWorldPosition"), mSmartFGCommon.mScreenProbeTranslatedWorldPosition);
        };

        UInt3 groupSize;

        // Uniform  screen probe pass.
        auto* uniformProbePass = RED->AllocatePass("Uniform Screen Probe");
        mSmartFGCommon.SetREDProperty(uniformProbePass);
        SmartFinalGatherScreenProbeComputeShader->GetThreadGroupSize("DownsampleDepthUniformCS", groupSize.x, groupSize.y, groupSize.z);
        passSetupCommon(uniformProbePass);
        uniformProbePass->Dispatch(
            SmartFinalGatherScreenProbeComputeShader, "DownsampleDepthUniformCS", (mSmartFGCommon.mScreenProbeViewSize.x + groupSize.x - 1) / groupSize.x, (mSmartFGCommon.mScreenProbeViewSize.y + groupSize.y - 1) / groupSize.y, 1);

        // Adpative screen probe pass.
        UInt32 placementDownsampleFactor = mSmartFGCommon.mScreenProbeDownsampleFactor;
        if (placementDownsampleFactor >= 2)
        {
            do
            {
                placementDownsampleFactor /= 2;
                UInt2 downsampleSize((viewSize.x + placementDownsampleFactor - 1) / placementDownsampleFactor, (viewSize.y + placementDownsampleFactor - 1) / placementDownsampleFactor);
                auto* adaptiveProbePass = RED->AllocatePass("Adpative Screen Probe");
                mSmartFGCommon.SetREDProperty(adaptiveProbePass);
                SmartFinalGatherScreenProbeComputeShader->GetThreadGroupSize("AdaptivePlacementCS", groupSize.x, groupSize.y, groupSize.z);
                passSetupCommon(adaptiveProbePass);
                adaptiveProbePass->SetProperty(NAME_ID("_RWNumAdaptiveScreenProbes"), mSmartFGCommon.mNumAdaptiveScreenProbes);
                adaptiveProbePass->SetProperty(NAME_ID("_RWAdaptiveScreenProbeData"), mSmartFGCommon.mAdaptiveScreenProbeData);
                adaptiveProbePass->SetProperty(NAME_ID("_RWScreenTileAdaptiveProbeHeader"), mSmartFGCommon.mScreenTileAdaptiveProbeHeader);
                adaptiveProbePass->SetProperty(NAME_ID("_RWScreenTileAdaptiveProbeIndices"), mSmartFGCommon.mScreenTileAdaptiveProbeIndices);
                adaptiveProbePass->SetProperty(NAME_ID("_PlacementDownsampleFactor"), placementDownsampleFactor);
                adaptiveProbePass->SetProperty(NAME_ID("PROBE_INTERPOLATION_WITH_NORMAL"), mSetting.mProbeInterpolationWithNormal);
                adaptiveProbePass->Dispatch(SmartFinalGatherScreenProbeComputeShader, "AdaptivePlacementCS", (downsampleSize.x + groupSize.x - 1) / groupSize.x, (downsampleSize.y * 2 + groupSize.y - 1) / groupSize.y, 1);
            } while (placementDownsampleFactor > static_cast<UInt32>(std::max(mSetting.GetAdaptiveProbeMinDownsampleFactor(gameContext), 1)));
        }
    }

    cross::REDTextureView* SmartGIPass::AssembleSmartGenerateRays(const GameContext& gameContext, RenderingExecutionDescriptor* RED, const GBufferType& gBufferViews, REDTextureView* sceneColorView, REDTextureView* depthView)
    {
        auto SmartImportanceSamplingShader = mSetting.GetImportanceSamplingShader(gameContext);
        REDTextureView* depthSRV = RED->AllocateTextureView(depthView->mTexture,
                                                             NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                depthView->mTexture->mDesc.Format,
                                                                                NGITextureType::Texture2D,
                                                                                {
                                                                                    NGITextureAspect::Depth,
                                                                                    0,
                                                                                    1,
                                                                                    0,
                                                                                    1,
                                                                                }});

        REDTextureView* pdfView = gameContext.mRenderPipeline->CreateTextureView2D("Smart.BRDFProbabilityDensityFunction",
                                                      mSmartFGCommon.mScreenProbeAtlasBufferSize.x * mSmartFGCommon.mScreenProbeBRDFOctahedronResolution,
                                                      mSmartFGCommon.mScreenProbeAtlasBufferSize.y * mSmartFGCommon.mScreenProbeBRDFOctahedronResolution,
                                                      GraphicsFormat::R16_SFloat,
                                                      NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);

        const UInt32 BRDF_SHBufferSize = mSmartFGCommon.mScreenProbeAtlasBufferSize.x * mSmartFGCommon.mScreenProbeAtlasBufferSize.y * 9;
        auto* redBuffer = RED->AllocateBuffer("Smart.BRDFProbabilityDensityFunctionSH", NGIBufferDesc{sizeof(float) * BRDF_SHBufferSize, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::CopyDst});
        REDBufferView* bufferView = RED->AllocateBufferView(redBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::CopyDst, 0, redBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(float)});
        NGIClearValue clearValue{{0, 0, 0, 0}};
        RED->AllocatePass("Clear Smart BRDFProbabilityDensityFunction")->ClearTexture(pdfView, clearValue);
        RED->AllocatePass("Clear Smart BRDFProbabilityDensityFunctionSH")->ClearBuffer(bufferView, 0);

        auto passSetupCommon = [&](cross::REDPass* pass) {
            mSmartFGCommon.SetREDProperty(pass);
            // screen textures
            pass->SetProperty(NAME_ID("_GBuffer0"), gBufferViews[0]);
            pass->SetProperty(NAME_ID("_GBuffer1"), gBufferViews[1]);
            pass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2]);
            pass->SetProperty(NAME_ID("_DepthMap"), depthSRV);
        };
        UInt3 groupSize;

        // Compute BRDF PDF
        auto* pdfPass = RED->AllocatePass("ComputeBRDF_PDF");
        passSetupCommon(pdfPass);
        pdfPass->SetProperty(NAME_ID("_RWBRDFProbabilityDensityFunctionSH"), bufferView);
        //pdfPass->SetProperty(NAME_ID("_RWBRDFProbabilityDensityFunction"), pdfView);

        SmartImportanceSamplingShader->GetThreadGroupSize("ComputeBRDFProbabilityDensityFunctionCS", groupSize.x, groupSize.y, groupSize.z);
        pdfPass->Dispatch(SmartImportanceSamplingShader, "ComputeBRDFProbabilityDensityFunctionCS", mSmartFGCommon.mScreenProbeAtlasBufferSize.x, mSmartFGCommon.mScreenProbeAtlasBufferSize.y, 1);


        // Importance Sampling Lighting
        bool bImportanceSampleLighting = true;
        REDTextureView* LightingProbabilityDensityFunction = nullptr;
        if (bImportanceSampleLighting) {
             LightingProbabilityDensityFunction = gameContext.mRenderPipeline->CreateTextureView2D("Smart.LightingProbabilityDensityFunction",
                                                                                                  mSmartFGCommon.mScreenProbeAtlasBufferSize.x * mSmartFGCommon.mScreenProbeTracingOctahedronResolution,
                                                                                                  mSmartFGCommon.mScreenProbeAtlasBufferSize.y * mSmartFGCommon.mScreenProbeTracingOctahedronResolution,
                                                                                                  GraphicsFormat::R16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
            RED->AllocatePass("Clear Smart LightingProbabilityDensityFunction")->ClearTexture(LightingProbabilityDensityFunction, {0, 0, 0, 0});

            auto* lightingPdfPass = RED->AllocatePass("ComputeLightingPDF");
            passSetupCommon(lightingPdfPass);
            lightingPdfPass->SetProperty(NAME_ID("_RWLightingProbabilityDensityFunction"), LightingProbabilityDensityFunction);
            lightingPdfPass->SetProperty(NAME_ID("OctahedralSolidAngleTexture"), mSmartFGCommon.mOctahedralSolidAngleTexture);
            SmartImportanceSamplingShader->GetThreadGroupSize("ComputeLightingProbabilityDensityFunctionCS", groupSize.x, groupSize.y, groupSize.z);
            lightingPdfPass->Dispatch(SmartImportanceSamplingShader, "ComputeLightingProbabilityDensityFunctionCS", mSmartFGCommon.mScreenProbeAtlasBufferSize.x, mSmartFGCommon.mScreenProbeAtlasBufferSize.y, 1);
        }


        // Generate Rays
        float GSmartImportanceSamplingMinPDFToTrace = .1f;
        UInt2 rayInfosTexSize = mSmartFGCommon.GetScreenProbeOctahedronViewSize();
        REDTextureView* importanceSampleRayInfosForTracing =
            IRenderPipeline::CreateTextureView2D("Smart.RayInfosForTracing", rayInfosTexSize.x, rayInfosTexSize.y, GraphicsFormat::R16_UInt, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        RED->AllocatePass("Clear Smart importanceSampleRayInfosForTracing")->ClearTexture(importanceSampleRayInfosForTracing, {0, 0, 0, 0});

        auto* pass = RED->AllocatePass("GenerateRays 8x8");
        mSmartFGCommon.SetREDProperty(pass);
        pass->SetProperty(NAME_ID("_MinPDFToTrace"), GSmartImportanceSamplingMinPDFToTrace);
        //pass->SetProperty(NAME_ID("_BRDFProbabilityDensityFunction"), pdfView);
        pass->SetProperty(NAME_ID("_BRDFProbabilityDensityFunctionSH"), bufferView);
        pass->SetProperty(NAME_ID("_LightingProbabilityDensityFunction"), LightingProbabilityDensityFunction);
        pass->SetProperty(NAME_ID("_RWStructuredImportanceSampledRayInfosForTracing"), importanceSampleRayInfosForTracing);

        pass->Dispatch(SmartImportanceSamplingShader, "GenerateRaysCS", mSmartFGCommon.mScreenProbeAtlasBufferSize.x, mSmartFGCommon.mScreenProbeAtlasBufferSize.y, 1);
        return importanceSampleRayInfosForTracing;
    }

    void cross::SmartGIPass::AssembleSmartFGTraceHZB(const GameContext& gameContext, RenderingExecutionDescriptor* RED, GBufferType& gBufferViews, REDTextureView* depthView, REDTextureView* depthPyramid,
                                                     REDTextureView* sceneColorView,
                                                     REDTextureView* rayInfosForTracing)
    {
        auto SmartFGTraceHZBShader = mSetting.SmartFGTraceHZBShaderR;

        auto viewWidth = sceneColorView->mTexture->mDesc.Width;
        auto viewHeight = sceneColorView->mTexture->mDesc.Height;

        REDTextureView* preSceneColorView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mPreSceneColorRT, sceneColorView);
        REDTextureView* depthSRV = RED->AllocateTextureView(depthView->mTexture,
                                                             NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                depthView->mTexture->mDesc.Format,
                                                                                NGITextureType::Texture2D,
                                                                                {
                                                                                    NGITextureAspect::Depth,
                                                                                    0,
                                                                                    1,
                                                                                    0,
                                                                                    1,
                                                                                }});
        REDTextureView* depthHistoryTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mDepthHistoryRT, depthSRV, NGITextureAspect::Color);
        auto ffsRdrPipe = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
        auto [prevEmissiveColorRT, curEmissiveColorRTView] = ffsRdrPipe->GetEmissiveColorForSmartGI();
        REDTextureView* emissiveColorTexView = TextureCreateHelper::GetHistoryTextureView(RED, prevEmissiveColorRT, curEmissiveColorRTView);

        UInt2 traceViewSize = mSmartFGCommon.GetScreenProbeOctahedronViewSize();

        UInt3 groupThreadSize;
        SmartFGTraceHZBShader->GetThreadGroupSize("TraceHZBCS", groupThreadSize.x, groupThreadSize.y, groupThreadSize.z);

        Float2 dispatchThreadIdToScreenUV(1.0f / viewWidth, 1.0f / viewHeight);

        Float2 HZBUvFactor(viewWidth / static_cast<float>(2 * depthPyramid->GetWidth()), viewHeight / static_cast<float>(2 * depthPyramid->GetHeight()));
        Float4 HZBUvFactorAndInvFactor(HZBUvFactor.x, HZBUvFactor.y, 1.0f / HZBUvFactor.x, 1.0f / HZBUvFactor.y);

        auto* csPass = RED->AllocatePass("SmartFGTraceHZB");
        mSmartFGCommon.SetREDProperty(csPass);
        
        auto* camera = gameContext.mRenderCamera;
        csPass->SetProperty(NAME_ID("_LastFrame_View"), camera->GetLastFrameViewMatrix());
        csPass->SetProperty(NAME_ID("_LastFrame_Projection"), camera->GetLastFrameProjMatrix());

        csPass->SetProperty(NAME_ID("EmissiveColorTexture"), emissiveColorTexView);

        csPass->SetProperty(NAME_ID("TRACE_SKY_LIGHTING"), !mSetting.IsEnableVoxelGI(gameContext));

        csPass->SetProperty(NAME_ID("StructuredImportanceSampledRayInfosForTracing"), rayInfosForTracing);
        csPass->SetProperty(NAME_ID("ScreenProbeSceneDepth"), mSmartFGCommon.mScreenProbeSceneDepth);
        csPass->SetProperty(NAME_ID("ScreenProbeWorldNormal"), mSmartFGCommon.mScreenProbeWorldNormal);
        csPass->SetProperty(NAME_ID("SceneDepthTexture"), depthSRV);
        csPass->SetProperty(NAME_ID("HistorySceneDepthTexture"), depthHistoryTexView);
        csPass->SetProperty(NAME_ID("FurthestHZBTexture"), depthPyramid);
        csPass->SetProperty(NAME_ID("PrevSceneColorTexture"), preSceneColorView);
        csPass->SetProperty(NAME_ID("RWTraceRadiance"), mSmartFGCommon.mTraceRadiance);
        csPass->SetProperty(NAME_ID("ENABLE_SKY_LIGHT_REALTIME_CAPTURE"), gameContext.mRenderWorld->GetRenderSystem<SkyLightSystemR>()->IsSkyLightRealtimeCapture());

        REDTextureView* TraceHitUAV =
            RED->AllocateTextureView(mSmartFGCommon.mTraceHit->mTexture,
                                     NGITextureViewDesc{NGITextureUsage::UnorderedAccess, mSmartFGCommon.mTraceHit->mTexture->mDesc.Format, mSmartFGCommon.mTraceHit->GetDesc().ViewDimension, mSmartFGCommon.mTraceHit->GetDesc().SubRange});
        csPass->SetProperty(NAME_ID("RWTraceHit"), TraceHitUAV);
        csPass->SetProperty(NAME_ID("_GBuffer1"), gBufferViews[1]);
        csPass->SetProperty(NAME_ID("VelocityBuffer"), gBufferViews[3]);

        UInt2 viewSize(input_targetView->GetWidth(), input_targetView->GetHeight());
        Float4 HistoryUVMinMax{0.5f / viewSize.x, 0.5f / viewSize.y, (viewSize.x - 0.5f) / viewSize.x, (viewSize.y - 0.5f) / viewSize.y};
        csPass->SetProperty(NAME_ID("HistoryUVMinMax"), HistoryUVMinMax);

        csPass->SetProperty(NAME_ID("_MaxTraceDistance"), mSetting.mMaxScreenTraceDistance);
        csPass->SetProperty(NAME_ID("_MaxScreenTraceFraction"), mSetting.mMaxScreenTraceFraction);
        csPass->SetProperty(NAME_ID("_NumRayCastSteps"), mSetting.mNumRayCastSteps);
        csPass->SetProperty(NAME_ID("_HZBUvFactorAndInvFactor"), HZBUvFactorAndInvFactor);
        csPass->SetProperty(NAME_ID("_DispatchThreadIdToScreenUV"), dispatchThreadIdToScreenUV);
        csPass->SetProperty(NAME_ID("_PrevSceneColorExposureScale"), mSetting.mSceneColorWithEmissiveExposureScale);
        csPass->SetProperty(NAME_ID("_EmissiveGIIntensity"), mSetting.mEmissiveGIIntensity);
        csPass->SetProperty("DEBUG_TRACE_SKY_LIGHTING_ONLY", mSetting.mDebugTraceSkyLightingOnly);

        csPass->SetProperty(NAME_ID("HIERARCHICAL_SCREEN_TRACING"), mSetting.mUseHiZRayTracing);
        csPass->SetProperty(NAME_ID("ClosestHZBTexture"), output_ClosestHiZView);
        csPass->SetProperty(NAME_ID("_HZBBaseTexelSize"), Float2(1.0f / output_ClosestHiZView->GetWidth(), 1.0f / output_ClosestHiZView->GetHeight()));

        csPass->Dispatch(SmartFGTraceHZBShader, "TraceHZBCS", (traceViewSize.x + groupThreadSize.x - 1) / groupThreadSize.x, (traceViewSize.y + groupThreadSize.y - 1) / groupThreadSize.y, 1);
    }
    void cross::SmartGIPass::AssembleSmartFilteringProbe(const GameContext& gameContext, RenderingExecutionDescriptor* RED)
    {
        float GSmartTemporalFilterProbesHistoryWeight = 0.85f;
        float GSmartTemporalFilterProbesHistoryDistanceThreshold = 100;

        float GSmartFilterMaxRadianceHitAngle = 10.0f;
        float GSmartScreenFilterPositionWeightScale = 1000.0f;
        SInt32 GSmartSpatialFilterHalfKernelSize = 1;

        UInt2 probeOctViewSize = mSmartFGCommon.GetScreenProbeOctahedronViewSize();

        auto SmartFinalGatherFilteringProbeShader = mSetting.SmartFinalGatherFilteringProbeShaderR;
        if (mSetting.mEnableProbeTemporalFilter)
        {
            auto traceRadiance = IRenderPipeline::CreateTextureView2D(
                "Smart.ScreenProbeTemporallyFilteredRadiance", probeOctViewSize.x, probeOctViewSize.y, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
            RED->AllocatePass("Clear Smart ScreenProbeTemporallyFilteredRadiance")->ClearTexture(traceRadiance, NGIClearValue({0, 0, 0, 0}));

            REDTextureView* probeHitDistanceHistoryTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mScreenProbeHitDistanceHistoryRT, mSmartFGCommon.mTraceHitFiltered);
            REDTextureView* probeRadianceHistoryTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mScreenProbeRadianceHistoryRT, mSmartFGCommon.mTraceRadianceFiltered);
            REDTextureView* probeSceneDepthHistoryTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mScreenProbeSceneDepthHistoryRT, mSmartFGCommon.mScreenProbeSceneDepth);
            REDTextureView* probeTranslatedWorldPositionHistoryTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mScreenProbeTranslatedWorldPositionHistoryRT, mSmartFGCommon.mScreenProbeTranslatedWorldPosition);

            auto* pass = RED->AllocatePass("TemporallyAccumulateRadiance");
            mSmartFGCommon.SetREDProperty(pass);
            auto* camera = gameContext.mRenderCamera;
            pass->SetProperty(NAME_ID("_LastFrame_View"), camera->GetLastFrameViewMatrix());
            pass->SetProperty(NAME_ID("_LastFrame_Projection"), camera->GetLastFrameProjMatrix());
            pass->SetProperty(NAME_ID("VelocityBuffer"), input_gBufferViews[3]);

            pass->SetProperty(NAME_ID("ScreenProbeRadiance"), mSmartFGCommon.mTraceRadianceFiltered);
            pass->SetProperty(NAME_ID("RWScreenProbeRadiance"), traceRadiance);

            pass->SetProperty(NAME_ID("PROBE_TEMPORAL_FILTER_WITH_HIT_DISTANCE"), mSetting.mProbeTemporalFilterWithHitDistance);
            pass->SetProperty(NAME_ID("HistoryScreenProbeHitDistance"), probeHitDistanceHistoryTexView);
            pass->SetProperty(NAME_ID("ScreenProbeHitDistance"), mSmartFGCommon.mTraceHitFiltered);
            pass->SetProperty(NAME_ID("SpatialFilterMaxRadianceHitAngle"), GSmartFilterMaxRadianceHitAngle * PI / 180.0f);

            pass->SetProperty(NAME_ID("HistoryScreenProbeRadiance"), probeRadianceHistoryTexView);
            pass->SetProperty(NAME_ID("HistoryScreenProbeSceneDepth"), probeSceneDepthHistoryTexView);
            pass->SetProperty(NAME_ID("HistoryScreenProbeTranslatedWorldPosition"), probeTranslatedWorldPositionHistoryTexView);

            pass->SetProperty(NAME_ID("ProbeTemporalFilterHistoryWeight"), GSmartTemporalFilterProbesHistoryWeight);
            pass->SetProperty(NAME_ID("HistoryDistanceThreshold"), GSmartTemporalFilterProbesHistoryDistanceThreshold);
            pass->SetProperty(NAME_ID("PrevInvPreExposure"), 1.0f);
            pass->SetProperty(NAME_ID("HistoryScreenPositionScaleBias"), mSmartViewParams.mScreenPositionScaleBias);
            UInt2 viewSize(input_targetView->GetWidth(), input_targetView->GetHeight());
            Float4 HistoryUVMinMax{0.5f / viewSize.x, 0.5f / viewSize.y, (viewSize.x - 0.5f) / viewSize.x, (viewSize.y - 0.5f) / viewSize.y};
            pass->SetProperty(NAME_ID("HistoryUVMinMax"), HistoryUVMinMax);

            UInt3 groupSize;
            SmartFinalGatherFilteringProbeShader->GetThreadGroupSize("TemporalFilteringCS", groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(SmartFinalGatherFilteringProbeShader, "TemporalFilteringCS", (probeOctViewSize.x + groupSize.x - 1) / groupSize.x, (probeOctViewSize.y + groupSize.y - 1) / groupSize.y, 1);
            mSmartFGCommon.mTraceRadianceFiltered = traceRadiance;
        }
        if (mSetting.mEnableProbeSpatialFilter)
        {
            REDTextureView* screenProbeRadiance = mSmartFGCommon.mTraceRadianceFiltered;

            auto* filteredScreenProbeRadiance = IRenderPipeline::CreateTextureView2D(
                "Smart.TraceRadianceProbeFiltered", probeOctViewSize.x, probeOctViewSize.y, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
            RED->AllocatePass("Clear Smart TraceRadianceProbeFiltered", true)->ClearTexture(filteredScreenProbeRadiance, NGIClearValue({0, 0, 0, 0}));

            REDTextureView* screenProbeRadianceTexArray[2] = {screenProbeRadiance, filteredScreenProbeRadiance};
            for (auto passIndex = 0; passIndex < mSetting.mSpatialFilterNumPasses; passIndex++)
            {
                auto* pass = RED->AllocatePass("FilterRadianceWithGather");
                mSmartFGCommon.SetREDProperty(pass);
                pass->SetProperty(NAME_ID("ScreenProbeRadiance"), screenProbeRadianceTexArray[0]);
                pass->SetProperty(NAME_ID("ScreenProbeHitDistance"), mSmartFGCommon.mTraceHitFiltered);
                pass->SetProperty(NAME_ID("RWScreenProbeRadiance"), screenProbeRadianceTexArray[1]);

                pass->SetProperty(NAME_ID("SpatialFilterMaxRadianceHitAngle"), GSmartFilterMaxRadianceHitAngle * PI / 180.0f);
                pass->SetProperty(NAME_ID("SpatialFilterPositionWeightScale"), GSmartScreenFilterPositionWeightScale);
                pass->SetProperty(NAME_ID("SpatialFilterHalfKernelSize"), GSmartSpatialFilterHalfKernelSize);

                UInt3 groupSize;
                SmartFinalGatherFilteringProbeShader->GetThreadGroupSize("SpatialFilteringCS", groupSize.x, groupSize.y, groupSize.z);
                pass->Dispatch(SmartFinalGatherFilteringProbeShader, "SpatialFilteringCS", (probeOctViewSize.x + groupSize.x - 1) / groupSize.x, (probeOctViewSize.y + groupSize.y - 1) / groupSize.y, 1);

                screenProbeRadiance = screenProbeRadianceTexArray[1];
                screenProbeRadianceTexArray[1] = screenProbeRadianceTexArray[0];
                screenProbeRadianceTexArray[0] = screenProbeRadiance;
            }
            mSmartFGCommon.mTraceRadianceFiltered = screenProbeRadiance;
        }

        mTemporalState.mScreenProbeHitDistanceHistoryRT = mSmartFGCommon.mTraceHitFiltered->mTexture;
        mTemporalState.mScreenProbeRadianceHistoryRT = mSmartFGCommon.mTraceRadianceFiltered->mTexture;
        mTemporalState.mScreenProbeSceneDepthHistoryRT = mSmartFGCommon.mScreenProbeSceneDepth->mTexture;
        mTemporalState.mScreenProbeTranslatedWorldPositionHistoryRT = mSmartFGCommon.mScreenProbeTranslatedWorldPosition->mTexture;
        mTemporalState.mScreenProbeHitDistanceHistoryRT->ExtendLifetime();
        mTemporalState.mScreenProbeRadianceHistoryRT->ExtendLifetime();
        mTemporalState.mScreenProbeSceneDepthHistoryRT->ExtendLifetime();
        mTemporalState.mScreenProbeTranslatedWorldPositionHistoryRT->ExtendLifetime();
    }

    void cross::SmartGIPass::AssembleSmartConvertToIrradiance(const GameContext& gameContext, RenderingExecutionDescriptor* RED, REDTextureView* rayInfosForTracing)
    {
        auto* SmartFinalGatherFilteringShader = mSetting.GetSmartFinalGatherFilteringShader(gameContext);
        if(mSetting.mEnableCompositeTraces)
        {
            auto* pass = RED->AllocatePass("CompositeTraces");
            mSmartFGCommon.SetREDProperty(pass);
            pass->SetProperty(NAME_ID("StructuredImportanceSampledRayInfosForTracing"), rayInfosForTracing);
            pass->SetProperty(NAME_ID("OctahedralSolidAngleTexture"), mSmartFGCommon.mOctahedralSolidAngleTexture);
            pass->SetProperty(NAME_ID("MaxRayIntensity"), 20.f);
            pass->SetProperty(NAME_ID("TraceRadiance"), mSmartFGCommon.mTraceRadiance);
            //REDTextureView* TraceHitSRV = RED->AllocateTextureView(
            //    mSmartFGCommon.mTraceHit->mTexture,
            //    NGITextureViewDesc{NGITextureUsage::ShaderResource, mSmartFGCommon.mTraceHit->mTexture->mDesc.Format, mSmartFGCommon.mTraceHit->GetDesc().ViewDimension, mSmartFGCommon.mTraceHit->GetDesc().SubRange});
            //pass->SetProperty(NAME_ID("TraceHit"), TraceHitSRV);
            pass->SetProperty(NAME_ID("RWScreenProbeRadiance"), mSmartFGCommon.mTraceRadianceFiltered);
            pass->SetProperty(NAME_ID("RWScreenProbeHitDistance"), mSmartFGCommon.mTraceHitFiltered);
            //pass->SetProperty(NAME_ID("RWScreenProbeTraceMoving"), mSmartFGCommon.mTraceMovingFiltered);

            UInt2 probeOctViewSize = mSmartFGCommon.GetScreenProbeOctahedronViewSize();
            UInt3 groupSize;
            SmartFinalGatherFilteringShader->GetThreadGroupSize("CompositeTracesCS", groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(SmartFinalGatherFilteringShader, "CompositeTracesCS", (probeOctViewSize.x + groupSize.x - 1) / groupSize.x, (probeOctViewSize.y + groupSize.y - 1) / groupSize.y, 1);
        }
        else 
        {
            mSmartFGCommon.mTraceRadianceFiltered = mSmartFGCommon.mTraceRadiance;
        }

        AssembleSmartFilteringProbe(gameContext, RED);

        {
            auto* pass = RED->AllocatePass("SmartConvertToIrradiance");
            mSmartFGCommon.SetREDProperty(pass);
            pass->SetProperty(NAME_ID("OctahedralSolidAngleTexture"), mSmartFGCommon.mOctahedralSolidAngleTexture);
            pass->SetProperty(NAME_ID("ScreenProbeRadiance"), mSmartFGCommon.mTraceRadianceFiltered);

            pass->SetProperty(NAME_ID("PROBE_IRRADIANCE_FORMAT"), mSetting.GetProbeIrradianceFormat());
            if (mSetting.mIrradianceFormatUseSH3)
            {
                pass->SetProperty(NAME_ID("RWScreenProbeRadianceSHAmbient"), mSmartFGCommon.mScreenProbeRadianceSHAmbient);
                pass->SetProperty(NAME_ID("RWScreenProbeRadianceSHDirectional"), mSmartFGCommon.mScreenProbeRadianceSHDirectional);

                pass->SetProperty(NAME_ID("RWScreenProbeIrradianceWithBorder"), mSmartFGCommon.mDummyBlackTexture);
            }
            else
            {
                pass->SetProperty(NAME_ID("RWScreenProbeIrradianceWithBorder"), mSmartFGCommon.mScreenProbeIrradianceWithBorder);

                pass->SetProperty(NAME_ID("RWScreenProbeRadianceSHAmbient"), mSmartFGCommon.mDummyBlackTexture);
                pass->SetProperty(NAME_ID("RWScreenProbeRadianceSHDirectional"), mSmartFGCommon.mDummyBlackTexture);
            }

            UInt2 probeOctViewSize = mSmartFGCommon.GetScreenProbeOctahedronViewSize();
            UInt3 groupSize;
            SmartFinalGatherFilteringShader->GetThreadGroupSize("ConvertToIrradianceCS", groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(SmartFinalGatherFilteringShader, "ConvertToIrradianceCS", (probeOctViewSize.x + groupSize.x - 1) / groupSize.x, (probeOctViewSize.y + groupSize.y - 1) / groupSize.y, 1);
        }
        {
            auto* pass = RED->AllocatePass("FixupBorders");
            mSmartFGCommon.SetREDProperty(pass);
            pass->SetProperty(NAME_ID("ScreenProbeRadiance"), mSmartFGCommon.mTraceRadianceFiltered);
            pass->SetProperty(NAME_ID("RWScreenProbeRadiance"), mSmartFGCommon.mScreenProbeFilterRadianceWithBorder);

            UInt2 probeOctWithBorderViewSize = mSmartFGCommon.GetScreenProbeOctahedronWithBorderViewSize();
            UInt3 groupSize;
            SmartFinalGatherFilteringShader->GetThreadGroupSize("FixupBordersCS", groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(SmartFinalGatherFilteringShader, "FixupBordersCS", (probeOctWithBorderViewSize.x + groupSize.x - 1) / groupSize.x, (probeOctWithBorderViewSize.y + groupSize.y - 1) / groupSize.y, 1);
        }
    }

    void cross::SmartGIPass::AssembleSmartIntegrate(const GameContext& gameContext, RenderingExecutionDescriptor* RED, const GBufferType& gBufferViews, REDTextureView* sceneColorView, REDTextureView* depthView)
    {
        auto* SmartFinalGatherIntegrateShader = mSetting.GetSmartFinalGatherIntegrateShader();
        UInt2 viewSize(input_targetView->GetWidth(), input_targetView->GetHeight());

        REDTextureView* depthSRV = RED->AllocateTextureView(depthView->mTexture,
                                                             NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                depthView->mTexture->mDesc.Format,
                                                                                NGITextureType::Texture2D,
                                                                                {
                                                                                    NGITextureAspect::Depth,
                                                                                    0,
                                                                                    1,
                                                                                    0,
                                                                                    1,
                                                                                }});

        auto* pass = RED->AllocatePass("SmartIntegrate(" + std::to_string(viewSize.x) + "," + std::to_string(viewSize.y) + ")");
        mSmartFGCommon.SetREDProperty(pass);
        Float2 ScreenProbeGatherOctahedronResolutionWithBorder = Float2(static_cast<float>(mSmartFGCommon.mScreenProbeGatherOctahedronResolutionWithBorder), static_cast<float>(mSmartFGCommon.mScreenProbeGatherOctahedronResolutionWithBorder));
        Float2 ScreenProbeGatherOctahedronResolution = Float2(static_cast<float>(mSmartFGCommon.mScreenProbeGatherOctahedronResolution), static_cast<float>(mSmartFGCommon.mScreenProbeGatherOctahedronResolution));

        Float2 InvAtlasWithBorderBufferSize =
            Float2(1.0f, 1.0f) / (ScreenProbeGatherOctahedronResolutionWithBorder * Float2(static_cast<float>(mSmartFGCommon.mScreenProbeAtlasBufferSize.x), static_cast<float>(mSmartFGCommon.mScreenProbeAtlasBufferSize.y)));
        Float2 SampleRadianceProbeUVMul = ScreenProbeGatherOctahedronResolution * InvAtlasWithBorderBufferSize;
        Float2 SampleRadianceProbeUVAdd = std::exp2(mSmartFGCommon.mScreenProbeGatherMaxMip) * InvAtlasWithBorderBufferSize;
        Float2 SampleRadianceAtlasUVMul = Float2(ScreenProbeGatherOctahedronResolutionWithBorder) * InvAtlasWithBorderBufferSize;
        pass->SetProperty(NAME_ID("SampleRadianceProbeUVMul"), SampleRadianceProbeUVMul);
        pass->SetProperty(NAME_ID("SampleRadianceProbeUVAdd"), SampleRadianceProbeUVAdd);
        pass->SetProperty(NAME_ID("SampleRadianceAtlasUVMul"), SampleRadianceAtlasUVMul);

        pass->SetProperty(NAME_ID("MaxRoughnessToTrace"), mSetting.GetMaxRoughnessToTrace(gameContext));
        pass->SetProperty(NAME_ID("RoughnessFadeLength"), mSetting.GetInvRoughnessFadeLength(gameContext));

        pass->SetProperty(NAME_ID("_GBuffer0"), gBufferViews[0]);
        pass->SetProperty(NAME_ID("_GBuffer1"), gBufferViews[1]);
        pass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2]);
        pass->SetProperty(NAME_ID("_DepthMap"), depthSRV);

        if (input_aoView)
        {
            pass->SetProperty(NAME_ID("ao_texture"), input_aoView);
            pass->SetProperty(NAME_ID("ENABLE_AO"), true);
        }
        else
        {
            pass->SetProperty(NAME_ID("ENABLE_AO"), false);
        }

        pass->SetProperty(NAME_ID("PROBE_INTERPOLATION_WITH_NORMAL"), mSetting.mProbeInterpolationWithNormal);
        pass->SetProperty(NAME_ID("PROBE_IRRADIANCE_FORMAT"), mSetting.GetProbeIrradianceFormat());
        if (mSetting.mIrradianceFormatUseSH3)
        {
            pass->SetProperty(NAME_ID("ScreenProbeRadianceSHAmbient"), mSmartFGCommon.mScreenProbeRadianceSHAmbient);
            pass->SetProperty(NAME_ID("ScreenProbeRadianceSHDirectional"), mSmartFGCommon.mScreenProbeRadianceSHDirectional);

            pass->SetProperty(NAME_ID("ScreenProbeIrradianceWithBorder"), mSmartFGCommon.mDummyBlackTexture);
        }
        else
        {
            pass->SetProperty(NAME_ID("ScreenProbeIrradianceWithBorder"), mSmartFGCommon.mScreenProbeIrradianceWithBorder);

            pass->SetProperty(NAME_ID("ScreenProbeRadianceSHAmbient"), mSmartFGCommon.mDummyBlackTexture);
            pass->SetProperty(NAME_ID("ScreenProbeRadianceSHDirectional"), mSmartFGCommon.mDummyBlackTexture);
        }
        pass->SetProperty(NAME_ID("ScreenProbeRadianceWithBorder"), mSmartFGCommon.mScreenProbeFilterRadianceWithBorder);

        pass->SetProperty(NAME_ID("RWDiffuseIndirect"), mSmartFGCommon.mDiffuseIndirect);
        pass->SetProperty(NAME_ID("RWRoughSpecularIndirect"), mSmartFGCommon.mRoughSpecularIndirect);

        UInt3 groupSize;
        SmartFinalGatherIntegrateShader->GetThreadGroupSize("LightingIntegrateCS", groupSize.x, groupSize.y, groupSize.z);
        pass->Dispatch(SmartFinalGatherIntegrateShader, "LightingIntegrateCS", (viewSize.x + groupSize.x - 1) / groupSize.x, (viewSize.y + groupSize.y - 1) / groupSize.y, 1);
    }

    void SmartGIPass::AssembleDebugSHReadPass(const GameContext& gameContext, RenderingExecutionDescriptor* RED, int probeIndex, Float2 debugScreenPos, REDTextureView* sceneColorView)
    {
        auto* SmartVisualizeSHComputeShader = mSetting.SmartVisualizeSHComputeShaderR;
        auto* pass = RED->AllocatePass("SmartDebugReadProbeSH", true);
        mSmartFGCommon.SetREDProperty(pass);

        REDTextureView* depthSRV = RED->AllocateTextureView(input_depthView->mTexture,
                                                            NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                               input_depthView->mTexture->mDesc.Format,
                                                                               NGITextureType::Texture2D,
                                                                               {
                                                                                   NGITextureAspect::Depth,
                                                                                   0,
                                                                                   1,
                                                                                   0,
                                                                                   1,
                                                                               }});

        Float2 ScreenProbeGatherOctahedronResolutionWithBorder =
            Float2(static_cast<float>(mSmartFGCommon.mScreenProbeGatherOctahedronResolutionWithBorder), static_cast<float>(mSmartFGCommon.mScreenProbeGatherOctahedronResolutionWithBorder));
        Float2 ScreenProbeGatherOctahedronResolution = Float2(static_cast<float>(mSmartFGCommon.mScreenProbeGatherOctahedronResolution), static_cast<float>(mSmartFGCommon.mScreenProbeGatherOctahedronResolution));

        Float2 InvAtlasWithBorderBufferSize =
            Float2(1.0f, 1.0f) / (ScreenProbeGatherOctahedronResolutionWithBorder * Float2(static_cast<float>(mSmartFGCommon.mScreenProbeAtlasBufferSize.x), static_cast<float>(mSmartFGCommon.mScreenProbeAtlasBufferSize.y)));
        Float2 SampleRadianceProbeUVMul = ScreenProbeGatherOctahedronResolution * InvAtlasWithBorderBufferSize;
        Float2 SampleRadianceProbeUVAdd = std::exp2(mSmartFGCommon.mScreenProbeGatherMaxMip) * InvAtlasWithBorderBufferSize;
        Float2 SampleRadianceAtlasUVMul = Float2(ScreenProbeGatherOctahedronResolutionWithBorder) * InvAtlasWithBorderBufferSize;
        pass->SetProperty(NAME_ID("SampleRadianceProbeUVMul"), SampleRadianceProbeUVMul);
        pass->SetProperty(NAME_ID("SampleRadianceProbeUVAdd"), SampleRadianceProbeUVAdd);
        pass->SetProperty(NAME_ID("SampleRadianceAtlasUVMul"), SampleRadianceAtlasUVMul);
        pass->SetProperty(NAME_ID("ScreenPosition"), UInt2(static_cast<UInt32>(debugScreenPos.x * input_depthView->GetWidth()), static_cast<UInt32>(debugScreenPos.y * input_depthView->GetHeight())));

        pass->SetProperty(NAME_ID("RWDebugSHCoefs"), mDebugState.mDebugSHCoefs[probeIndex]);
        pass->SetProperty(NAME_ID("_GBuffer0"), input_gBufferViews[0]);
        pass->SetProperty(NAME_ID("_GBuffer1"), input_gBufferViews[1]);
        pass->SetProperty(NAME_ID("_GBuffer2"), input_gBufferViews[2]);
        pass->SetProperty(NAME_ID("_DepthMap"), depthSRV);

        pass->SetProperty(NAME_ID("PROBE_INTERPOLATION_WITH_NORMAL"), mSetting.mProbeInterpolationWithNormal);
        pass->SetProperty(NAME_ID("PROBE_IRRADIANCE_FORMAT"), mSetting.GetProbeIrradianceFormat());
        if (mSetting.mIrradianceFormatUseSH3)
        {
            pass->SetProperty(NAME_ID("ScreenProbeRadianceSHAmbient"), mSmartFGCommon.mScreenProbeRadianceSHAmbient);
            pass->SetProperty(NAME_ID("ScreenProbeRadianceSHDirectional"), mSmartFGCommon.mScreenProbeRadianceSHDirectional);

            pass->SetProperty(NAME_ID("ScreenProbeIrradianceWithBorder"), mSmartFGCommon.mDummyBlackTexture);
        }
        else
        {
            pass->SetProperty(NAME_ID("ScreenProbeIrradianceWithBorder"), mSmartFGCommon.mScreenProbeIrradianceWithBorder);

            pass->SetProperty(NAME_ID("ScreenProbeRadianceSHAmbient"), mSmartFGCommon.mDummyBlackTexture);
            pass->SetProperty(NAME_ID("ScreenProbeRadianceSHDirectional"), mSmartFGCommon.mDummyBlackTexture);
        }
        pass->SetProperty(NAME_ID("ScreenProbeRadianceWithBorder"), mSmartFGCommon.mScreenProbeFilterRadianceWithBorder);

        pass->Dispatch(SmartVisualizeSHComputeShader, "ReadProbeSHCoefsCS", 1, 1, 1);

        auto bufSize = mDebugState.mDebugSHCoefs[probeIndex]->GetDesc().SizeInBytes;
        NGICopyBuffer copyRegion{0, 0, bufSize};
        auto *debugSHCoefsStagingBuffer = GetNGIDevice().CreateStagingBuffer(NGIBufferDesc{bufSize, NGIBufferUsage::CopyDst});
        auto* stagingBufferRED = RED->AllocateBuffer("SmartCopySH", debugSHCoefsStagingBuffer);
        auto* copyPass = RED->AllocatePass("SmartCopySH");
        copyPass->CopyBufferToBuffer(stagingBufferRED, mDebugState.mDebugSHCoefs[probeIndex]->mBuffer, 1, &copyRegion);
        stagingBufferRED->SetExternalState(NGIResourceState::HostRead);

        auto frameCount = EngineGlobal::Inst().GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();
        mDebugState.mPendingCopyBackTasks.emplace(frameCount, debugSHCoefsStagingBuffer, mDebugState.mReadBackSHData[probeIndex].get(), bufSize);
    }

    void SmartGIPass::UpdateReadBackBuffer(const GameContext& gameContext, RenderingExecutionDescriptor* RED) 
    {
        while (!mDebugState.mPendingCopyBackTasks.empty()) 
        {
            auto frameCount = EngineGlobal::Inst().GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();
            auto& [frame, stagingBuffer, dst, range] = mDebugState.mPendingCopyBackTasks.front();
            if (frameCount - frame >= CmdSettings::Inst().gMaxQueuedFrame)
            {
                auto* src = stagingBuffer->MapRange(NGIBufferUsage::CopyDst, 0, range);
                memcpy(dst, src, range);
                stagingBuffer->UnmapRange(0, range);
                mDebugState.mPendingCopyBackTasks.pop();
            }
            else
            {
                break;
            }
        }
    }

    void SmartGIPass::AssembleDebugSHDrawPass(const GameContext& gameContext, RenderingExecutionDescriptor* RED, REDTextureView* outputRT, REDTexture* depthView)
    {
        auto viewWidth = outputRT->GetWidth();
        auto viewHeight = outputRT->GetHeight();

        auto colorAttachmentRT =
            RED->AllocateTextureView(outputRT->mTexture, NGITextureViewDesc{NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource, outputRT->mDesc.Format, NGITextureType::Texture2D, {NGITextureAspect::Color, 0, 1, 0, 1}});
        REDColorTargetDesc colorTargetDesc{colorAttachmentRT, NGILoadOp::Load, NGIStoreOp::Store, NGIClearValue{}};

        NGITextureViewDesc viewDesc{NGITextureUsage::DepthStencil, GraphicsFormat::D24_UNorm_S8_UInt, NGITextureType::Texture2D, {NGITextureAspect::Depth | NGITextureAspect::Stencil, 0, 1, 0, 1}};
        REDTextureView* depthStencilView = RED->AllocateTextureView(depthView, viewDesc);

        NGIClearValue clearValue;
        clearValue.depthStencil = {0, 0};
        REDDepthStencilTargetDesc depthStencilTargetDesc{depthStencilView, NGILoadOp::Load, NGIStoreOp::Store, NGILoadOp::Load, NGIStoreOp::DontCare, clearValue};

        NGIViewport viewport{0, 0, static_cast<float>(viewWidth), static_cast<float>(viewHeight), 0, 1};
        NGIScissor scissor{0, 0, viewWidth, viewHeight};

        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        MaterialR* material = mSetting.SmartVisualizeSHCoefsMtlR;
        auto shMtlState = material->GetMaterialRenderState("BasePass");

        MaterialR* cubeMat = mSetting.SmartVisualizeCubeMtlR;
        auto cubeMtlState = cubeMat->GetMaterialRenderState("BasePass");
        
        auto GetPipelineDesc = [](REDPass* pass, MaterialR::MaterialRenderState& mtlState, InputLayoutDesc& inputLayoutDesc, PrimitiveTopology topology, const void* shaderConst) {
            NGIGraphicsPipelineStateDesc pipelineDesc{pass->GetRenderPass(),
                                                      pass->GetSubpass(),
                                                      {mtlState.mProgram->GUID.low, mtlState.mProgram->GUID.high},
                                                      &mtlState.mProgram->GraphicsProgramDesc,
                                                      mtlState.mProgram->PipelineLayout,
                                                      inputLayoutDesc.GetHash().GetHash(),
                                                      &inputLayoutDesc,
                                                      topology,
                                                      *mtlState.mRaterizationState,
                                                      *mtlState.mBlendState,
                                                      *mtlState.mDepthStencilState,
                                                      0,
                                                      shaderConst};
            return pipelineDesc;
        };

        auto GetSphereInputLayoutDesc = []() {
            InputLayoutDesc sphereInputLayoutDesc{};
            VertexStreamLayout streamLayout{};
            streamLayout.AddVertexChannelLayout({VertexChannel::Position0, VertexFormat::Float3, 0});
            streamLayout.AddVertexChannelLayout({VertexChannel::TexCoord0, VertexFormat::Float2, sizeof(Float3)});
            sphereInputLayoutDesc.AddVertexStreamLayout(streamLayout);
            return sphereInputLayoutDesc;
        };

        auto GetCubeInputLayoutDesc = []() {
            InputLayoutDesc cubeInputLayoutDesc{};
            VertexStreamLayout streamLayout{};
            streamLayout.AddVertexChannelLayout({VertexChannel::Position0, VertexFormat::Float4, 0});
            streamLayout.AddVertexChannelLayout({VertexChannel::Normal0, VertexFormat::Float4, sizeof(Float3)});
            cubeInputLayoutDesc.AddVertexStreamLayout(streamLayout);
            return cubeInputLayoutDesc;
        };
        InputLayoutDesc sphereInputLayoutDesc = GetSphereInputLayoutDesc();
        InputLayoutDesc cubeInputLayoutDesc = GetCubeInputLayoutDesc();

        RED->BeginRenderPass("SmartDebugShowSH", 1, &colorTargetDesc, &depthStencilTargetDesc);
        auto colorTargetIndex = NGIRenderPassTargetIndex::Target0;
        auto* subPass = RED->AllocateSubRenderPass("SmartDebugShowSH", 0, nullptr, 1, &colorTargetIndex, REDPassFlagBit::NeedDepth);

        for (auto i = 0; i < cDebugProbeNum; i++)
        {
            float* shData = mDebugState.mReadBackSHData[i].get();
            Float3 worldPos(shData[120], shData[121], shData[122]);
            worldPos = worldPos + mSetting.mDebugSHOffset;
            Float4x4 worldMat = Float4x4::Compose(mSetting.mDebugSHScale, Quaternion(), worldPos);

            Float3 normalWS(shData[124], shData[125], shData[126]);
            Float4 color(shData[128], shData[129], shData[130], 1.0f);
            float lineScale = mSetting.mDebugSHScale.x;
            Float4x4 shLineWorldMat = Float4x4::Compose(Float3(lineScale * 3.f, lineScale * 0.1f, lineScale * 0.1f), Quaternion::CreateFrom2Vectors(Float3(1, 0, 0), normalWS), worldPos);

            subPass->Execute([=](REDPass* pass, NGIBundleCommandList* cmdList) mutable {
                auto* renderPrimitives = rendererSystem->GetRenderPrimitives();

                cmdList->SetViewports(1, &viewport);
                cmdList->SetScissors(1, &scissor);

                {
                    subPass->SetProperty(BuiltInProperty::ce_World, worldMat);
                    subPass->SetProperty(NAME_ID("_GIProbeSH"), shData, sizeof(Float4) * 7);

                    NGIBuffer* vbs[]{renderPrimitives->GetUnitSphere()};
                    NGIBuffer* ibs{renderPrimitives->GetUnitSphereIndex()};
                    cmdList->SetVertexBuffers(1, vbs, nullptr);
                    cmdList->SetIndexBuffer(ibs, 0, GraphicsFormat::R16_UInt);

                    const void* shaderConst = nullptr;
                    NGIGraphicsPipelineStateDesc pipelineDesc = GetPipelineDesc(pass, shMtlState, sphereInputLayoutDesc, PrimitiveTopology::TriangleList, shaderConst);
                    auto* pipeline = rendererSystem->GetGraphicsPipelineStatePool()->AllocateGraphicsPipelineState(pipelineDesc);
                    cmdList->SetGraphicsPipelineState(pipeline);

                    auto* pg = pass->GetContext().GetPassResourceGroup(shMtlState.mProgram->ResourceGroupLayouts[ShaderParamGroup_Pass], shMtlState.mProgram->PipelineLayout, ShaderParamGroup_Pass);
                    if (pg)
                    {
                        cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Pass, pg);
                    }

                    auto* mg = shMtlState.GetResourceBinding();
                    if (mg)
                    {
                        cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Material, mg);
                    }
                    cmdList->DrawIndexedInstanced(renderPrimitives->GetUnitSphereIndexCount(), 1, 0, 0, 0);
                }

                {
                    subPass->SetProperty(BuiltInProperty::ce_World, shLineWorldMat);
                    subPass->SetProperty(NAME_ID("_BaseColor"), color);

                    NGIBuffer* vbs[]{renderPrimitives->GetUnitCube()};
                    NGIBuffer* ibs{renderPrimitives->GetUnitCubeIndex()};
                    cmdList->SetVertexBuffers(1, vbs, nullptr);
                    cmdList->SetIndexBuffer(ibs, 0, GraphicsFormat::R16_UInt);

                    const void* shaderConst = nullptr;
                    NGIGraphicsPipelineStateDesc pipelineDesc = GetPipelineDesc(pass, cubeMtlState, cubeInputLayoutDesc, PrimitiveTopology::TriangleList, shaderConst);
                    auto* pipeline = rendererSystem->GetGraphicsPipelineStatePool()->AllocateGraphicsPipelineState(pipelineDesc);
                    cmdList->SetGraphicsPipelineState(pipeline);

                    auto* pg = pass->GetContext().GetPassResourceGroup(cubeMtlState.mProgram->ResourceGroupLayouts[ShaderParamGroup_Pass], cubeMtlState.mProgram->PipelineLayout, ShaderParamGroup_Pass);
                    if (pg)
                    {
                        cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Pass, pg);
                    }

                    auto* mg = cubeMtlState.GetResourceBinding();
                    if (mg)
                    {
                        cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Material, mg);
                    }
                    cmdList->DrawIndexedInstanced(renderPrimitives->GetUnitCubeIndexCount(), 1, 0, 0, 0);
                }
            });
        }

        RED->EndRenderPass();
    }

    NGITextureViewDesc TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage usage, GraphicsFormat format, NGITextureAspect aspect)
    {
        return NGITextureViewDesc{usage, format, NGITextureType::Texture2D, {aspect, 0, 1, 0, 1}};
    }

    NGITextureDesc TextureCreateHelper::GetTexture2DDesc(GraphicsFormat format, UInt32 width, UInt32 height, NGITextureUsage usage)
    {
        return NGITextureDesc{format, NGITextureType::Texture2D, 1, 1, width, height, 1, 1, usage};
    }

    REDTextureView* TextureCreateHelper::GetHistoryTextureView(RenderingExecutionDescriptor* RED, REDTexture* historyTex, REDTextureView* currentTexView, NGITextureAspect aspect)
    {
        REDTextureView* ret;
        if (RED->Validate(historyTex) && historyTex->mDesc.Width == currentTexView->GetWidth() && historyTex->mDesc.Height == currentTexView->GetHeight())
        {
            ret = RED->AllocateTextureView(historyTex, GetTexture2DViewDesc(historyTex->mDesc.Usage, historyTex->mDesc.Format, aspect));
        }
        else
        {
            ret = currentTexView;
        }
        Assert(ret);
        return ret;
    }

    cross::REDTexture* TextureCreateHelper::AllocateNewTemporalTexture(RenderingExecutionDescriptor* RED, std::string_view name, NGITextureUsage usage, GraphicsFormat format, UInt32 width, UInt32 height) 
    {
        REDTexture* newTex = RED->AllocateTexture(name, GetTexture2DDesc(format, width, height, usage));
        newTex->ExtendLifetime();
        Assert(newTex);
        return newTex;
    }

    void SmartGIPass::AssembleSmartScreenTemporalFilter(const GameContext& gameContext, RenderingExecutionDescriptor* RED, REDTextureView*& diffuseIndirect, REDTextureView*& specularIndirect)
    {
        auto SmartFinalGatherTemporalShader = mSetting.SmartFinalGatherTemporalShaderR;

        UInt2 viewSize(input_targetView->GetWidth(), input_targetView->GetHeight());

        REDTextureView* depthSRV = RED->AllocateTextureView(input_depthView->mTexture,
                                                            NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                               input_depthView->mTexture->mDesc.Format,
                                                                               NGITextureType::Texture2D,
                                                                               {
                                                                                   NGITextureAspect::Depth,
                                                                                   0,
                                                                                   1,
                                                                                   0,
                                                                                   1,
                                                                               }});
        REDTextureView* depthHistoryTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mDepthHistoryRT, depthSRV, NGITextureAspect::Color);
        auto depthFormat = GraphicsFormat::R16_UNorm;
        mTemporalState.mDepthHistoryRT = TextureCreateHelper::AllocateNewTemporalTexture(RED, "Smart.DepthHistoryRT", NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource, depthFormat, viewSize.x, viewSize.y);
        REDTextureView* newDepthHistoryTexView = RED->AllocateTextureView(mTemporalState.mDepthHistoryRT, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::UnorderedAccess, depthFormat));
        
        auto* normalTexView = input_gBufferViews[1];
        REDTextureView* normalHistoryTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mNormalHistoryRT, normalTexView);
        mTemporalState.mNormalHistoryRT = normalTexView->mTexture;
        mTemporalState.mNormalHistoryRT->ExtendLifetime();

        auto halfFloat = GraphicsFormat::R16G16B16A16_SFloat;

        REDTextureView* diffuseIndirectHistoryTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mDiffuseIndirectHistoryRT, diffuseIndirect);
        REDTexture* RWNewHistoryDiffuseTex = TextureCreateHelper::AllocateNewTemporalTexture(
            RED, "Smart.DiffuseIndirect", NGITextureUsage::RenderTarget | NGITextureUsage::CopyDst | NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource, halfFloat, viewSize.x, viewSize.y);
        mTemporalState.mDiffuseIndirectHistoryRT = RWNewHistoryDiffuseTex;
        REDTextureView* newHistoryDiffuseIndirectTexView = RED->AllocateTextureView(RWNewHistoryDiffuseTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::UnorderedAccess, halfFloat));

        REDTextureView* specularIndirectHistoryTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mRoughSpecularIndirectHistoryRT, specularIndirect);
        REDTexture* RWNewHistorySpecularTex = TextureCreateHelper::AllocateNewTemporalTexture(RED, "Smart.RoughSpecularIndirect", NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource, halfFloat, viewSize.x, viewSize.y);
        mTemporalState.mRoughSpecularIndirectHistoryRT = RWNewHistorySpecularTex;
        REDTextureView* newHistorySpecularIndirectTexView = RED->AllocateTextureView(RWNewHistorySpecularTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::UnorderedAccess, halfFloat));

        {
            mTemporalState.mDefaultR8RT = RED->AllocateTexture("Smart.DefaultR8", TextureCreateHelper::GetTexture2DDesc(GraphicsFormat::R8_UNorm, viewSize.x, viewSize.y, NGITextureUsage::ShaderResource));
            mTemporalState.mDefaultR8View = RED->AllocateTextureView(mTemporalState.mDefaultR8RT, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::ShaderResource, GraphicsFormat::R8_UNorm));
        }

        REDTexture* RWNewNumFramesAccumulatedTex =
            TextureCreateHelper::AllocateNewTemporalTexture(RED, "Smart.NumHistoryFramesAccumulated", NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource, GraphicsFormat::R8_UNorm, viewSize.x, viewSize.y);
        REDTextureView* newNumFramesAccumulatedTexView = RED->AllocateTextureView(RWNewNumFramesAccumulatedTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::UnorderedAccess, GraphicsFormat::R8_UNorm));
        REDTextureView* historyNumFramesAccumulatedTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mNumFramesAccumulatedRT, mTemporalState.mDefaultR8View);
        mTemporalState.mNumFramesAccumulatedRT = RWNewNumFramesAccumulatedTex;

        REDTexture* RWNewFastUpdateModeTex = TextureCreateHelper::AllocateNewTemporalTexture(RED, "Smart.FastUpdateMode", NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource, GraphicsFormat::R8_UNorm, viewSize.x, viewSize.y);
        REDTextureView* newFastUpdateModeTexView = RED->AllocateTextureView(RWNewFastUpdateModeTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::UnorderedAccess, GraphicsFormat::R8_UNorm));
        REDTextureView* historyFastUpdateModeTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mFastUpdateModeHistoryRT, mTemporalState.mDefaultR8View);
        mTemporalState.mFastUpdateModeHistoryRT = RWNewFastUpdateModeTex;

        auto* pass = RED->AllocatePass("SmartScreenTemporalFilter");
        mSmartFGCommon.SetREDProperty(pass);
        auto* camera = gameContext.mRenderCamera;
        pass->SetProperty(NAME_ID("_LastFrame_View"), camera->GetLastFrameViewMatrix());
        pass->SetProperty(NAME_ID("_LastFrame_Projection"), camera->GetLastFrameProjMatrix());
        pass->SetProperty(NAME_ID("VelocityBuffer"), input_gBufferViews[3]);

        pass->SetProperty(NAME_ID("HistoryScreenPositionScaleBias"), mSmartViewParams.mScreenPositionScaleBias);
        Float4 HistoryUVMinMax{0.5f / viewSize.x, 0.5f / viewSize.y, (viewSize.x - 0.5f) / viewSize.x, (viewSize.y - 0.5f) / viewSize.y};
        pass->SetProperty(NAME_ID("HistoryUVMinMax"), HistoryUVMinMax);
        pass->SetProperty(NAME_ID("PrevSceneColorPreExposureCorrection"), 1.0f);
        pass->SetProperty(NAME_ID("InvFractionOfLightingMovingForFastUpdateMode"), mSetting.mFastUpdateScale);
        pass->SetProperty(NAME_ID("MaxFastUpdateModeAmount"), 0.9f); // seems useless
        pass->SetProperty(NAME_ID("MaxFramesAccumulated"), mSetting.mMaxFramesAccumulated);
        pass->SetProperty(NAME_ID("NumHistoryAccumulateThres"), mSetting.mNumHistoryAccumulateThres);
        pass->SetProperty(NAME_ID("HistoryNormalCosThreshold"), 0.707f); // cos(45degree)
        pass->SetProperty(NAME_ID("HistoryDistanceThreshold"), mSetting.mHistoryDistanceThreshold);
        pass->SetProperty(NAME_ID("DisocclusionDistanceThreshold"), mSetting.mDisocclusionDistanceThreshold);

        pass->SetProperty(NAME_ID("SceneDepthTexture"), depthSRV);
        pass->SetProperty(NAME_ID("DiffuseIndirectDepthHistory"), depthHistoryTexView);
        pass->SetProperty(NAME_ID("RWDepthHistory"), newDepthHistoryTexView);

        pass->SetProperty(NAME_ID("_GBuffer1"), normalTexView);
        pass->SetProperty(NAME_ID("NormalHistory"), normalHistoryTexView);

        pass->SetProperty(NAME_ID("DiffuseIndirect"), diffuseIndirect);
        pass->SetProperty(NAME_ID("DiffuseIndirectHistory"), diffuseIndirectHistoryTexView);
        pass->SetProperty(NAME_ID("RWNewHistoryDiffuseIndirect"), newHistoryDiffuseIndirectTexView);

        pass->SetProperty(NAME_ID("RoughSpecularIndirect"), specularIndirect);
        pass->SetProperty(NAME_ID("RoughSpecularIndirectHistory"), specularIndirectHistoryTexView);
        pass->SetProperty(NAME_ID("RWNewHistoryRoughSpecularIndirect"), newHistorySpecularIndirectTexView);

        pass->SetProperty(NAME_ID("HistoryNumFramesAccumulated"), historyNumFramesAccumulatedTexView);
        pass->SetProperty(NAME_ID("RWNumHistoryFramesAccumulated"), newNumFramesAccumulatedTexView);

        pass->SetProperty(NAME_ID("FastUpdateModeHistory"), historyFastUpdateModeTexView);
        pass->SetProperty(NAME_ID("RWNewHistoryFastUpdateMode"), newFastUpdateModeTexView);

        UInt3 groupSize;
        SmartFinalGatherTemporalShader->GetThreadGroupSize("TemporalReprojectionCS", groupSize.x, groupSize.y, groupSize.z);
        pass->Dispatch(SmartFinalGatherTemporalShader, "TemporalReprojectionCS", (viewSize.x + groupSize.x - 1) / groupSize.x, (viewSize.y + groupSize.y - 1) / groupSize.y, 1);

        diffuseIndirect = RED->AllocateTextureView(RWNewHistoryDiffuseTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::ShaderResource, halfFloat));
        specularIndirect = RED->AllocateTextureView(RWNewHistorySpecularTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::ShaderResource, halfFloat));
    }

    bool SmartGIPass::ExecuteImp(const GameContext& gameContext)
    {
        auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
        //bool needCompositeLighting = !mSetting.mDebugShowVoxels || !mSetting.IsEnableVoxelGI(gameContext);

        if (mStageStatus == StageStatus::SMART_GI_STAGE)
        {
            red->BeginRegion("SmartGIInit");
            InitializeParams(gameContext);
            red->EndRegion();

            red->BeginRegion("SmartGI");
            UpdateSmartGIRenderContext(gameContext);

            if (mSetting.IsEnableVoxelGI(gameContext))
            {
                QUICK_SCOPED_CPU_TIMING("AssembleVoxelLighting");
                REDTextureView* preSceneColor = TextureCreateHelper::GetHistoryTextureView(red, mTemporalState.mPreSceneColorRT, input_sceneColorView, NGITextureAspect::Color);
                REDTextureView* depthSRV = red->AllocateTextureView(input_depthView->mTexture,
                    NGITextureViewDesc{NGITextureUsage::ShaderResource,
                        input_depthView->mTexture->mDesc.Format,
                        NGITextureType::Texture2D,
                        {
                            NGITextureAspect::Depth,
                            0,
                            1,
                            0,
                            1
                        }});
                REDTextureView* preDepth = TextureCreateHelper::GetHistoryTextureView(red, mTemporalState.mDepthHistoryRT, depthSRV, NGITextureAspect::Color);
                REDTextureView* preDiffuse = TextureCreateHelper::GetHistoryTextureView(red, mTemporalState.mDiffuseIndirectHistoryRT, input_sceneColorView, NGITextureAspect::Color);
                if (!mTemporalState.mDiffuseIndirectHistoryRT)
                {
                    preDiffuse = nullptr;
                }
                mVoxelRenderer.AssembleVoxelLighting(gameContext, red, input_depthView->mTexture, input_shadowProperties, mDebugState.mDebugSceneColor,
                    preSceneColor, preDepth, preDiffuse, input_gBufferViews);
            }
            AssembleSmartGIScreenProbe(gameContext, red, input_gBufferViews, input_sceneColorView, input_depthView);
            REDTextureView* rayInfosForTracing = AssembleSmartGenerateRays(gameContext, red, input_gBufferViews, input_sceneColorView, input_depthView);

            // Default enable HZB trace
            AssembleSmartFGTraceHZB(gameContext, red, input_gBufferViews, input_depthView, output_HiZView, input_sceneColorView, rayInfosForTracing);

            if (mSetting.IsEnableVoxelGI(gameContext))
            {
                QUICK_SCOPED_CPU_TIMING("AssembleScreenProbeTraceVoxels");
                mVoxelRenderer.AssembleScreenProbeTraceVoxels(gameContext, red, rayInfosForTracing);
            }
            if(mSetting.mEnableDebugRay)
            {
                AssembleRayDebug(gameContext, red, input_gBufferViews, input_depthView, rayInfosForTracing);
            }
            AssembleSmartConvertToIrradiance(gameContext, red, rayInfosForTracing);
            if (mSetting.mDebugShowSH3 && !mSetting.mLockShowSH3)
            {
                AssembleDebugSHReadPass(gameContext, red, 0, mSetting.mDebugScreenPosition, input_sceneColorView);
                AssembleDebugSHReadPass(gameContext, red, 1, mSetting.mDebugScreenPosition2, input_sceneColorView);
            }
            AssembleSmartIntegrate(gameContext, red, input_gBufferViews, input_sceneColorView, input_depthView);
            if (mSetting.mEnableTemporalFilter)
            {
                AssembleSmartScreenTemporalFilter(gameContext, red, mSmartFGCommon.mDiffuseIndirect, mSmartFGCommon.mRoughSpecularIndirect);
            }

            output_diffuseIndirectView = red->AllocateTextureView(mSmartFGCommon.mDiffuseIndirect->mTexture,
                                                                   NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                      mSmartFGCommon.mDiffuseIndirect->mTexture->mDesc.Format,
                                                                                      NGITextureType::Texture2D,
                                                                                      {
                                                                                          NGITextureAspect::Color,
                                                                                          0,
                                                                                          1,
                                                                                          0,
                                                                                          1,
                                                                                      }});
            output_specularIndirectView = red->AllocateTextureView(mSmartFGCommon.mRoughSpecularIndirect->mTexture,
                                                                   NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                      mSmartFGCommon.mRoughSpecularIndirect->mTexture->mDesc.Format,
                                                                                      NGITextureType::Texture2D,
                                                                                      {
                                                                                          NGITextureAspect::Color,
                                                                                          0,
                                                                                          1,
                                                                                          0,
                                                                                          1,
                                                                                      }});

            if (mSetting.mDebugShowSH3)
            {
                UpdateReadBackBuffer(gameContext, red);
                AssembleDebugSHDrawPass(gameContext, red, output_compositeIndirectView, input_depthView->mTexture);
            }

            red->EndRegion();
        }
        else if (mStageStatus == StageStatus::SMART_GI_COMPLETED_STAGE)
        {
            ExtendPreSceneColorRT(input_sceneColorView->mTexture);
            SwapEmissiveColorRT();
        }
        
        return true;
    }

    void SmartGIPass::SwapEmissiveColorRT() 
    {
        //mTemporalState.mPreEmissiveColorRT = mTemporalState.mCurEmissiveColorRT;
        //mTemporalState.mCurEmissiveColorRT = nullptr;
    }

    //void SmartGIPass::CopyEmissiveAfterGPass(const GameContext& gameContext, REDTextureView* sceneColorView)
    //{
    //    auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
    //    // Always decoupling sceneColor and emmisiveColor
    //    {
    //        auto width = sceneColorView->GetWidth();
    //        auto height = sceneColorView->GetHeight();
    //        auto emissiveColorCopy = gameContext.mRenderPipeline->CreateTextureView2D("EmissiveColor", width, height, sceneColorView->GetDesc().Format, NGITextureUsage::CopyDst | NGITextureUsage::ShaderResource);
    //        NGICopyTexture region{0, {}, 0, {}, {width, height, 1}};
    //        red->AllocatePass("CopyEmissiveColorForGI")->CopyTextureToTexture(emissiveColorCopy->mTexture, sceneColorView->mTexture, 1, &region);
    //        mTemporalState.mCurEmissiveColorRTView = emissiveColorCopy;
    //        // Stash and extend this Texture's life time to use in next frame
    //        mTemporalState.mPreEmissiveColorRT = emissiveColorCopy->mTexture;
    //        if (mTemporalState.mPreEmissiveColorRT->mType != REDResourceType::ExtendedToNext) 
    //        {
    //            mTemporalState.mPreEmissiveColorRT->ExtendLifetime();
    //        }
    //    }
    //}

    void SmartGIPass::ExtendPreSceneColorRT(REDTexture* sceneColorRT)
    {
        mTemporalState.mPreSceneColorRT = sceneColorRT;
        if (mTemporalState.mPreSceneColorRT->mType != REDResourceType::ExtendedToNext)
        {
            mTemporalState.mPreSceneColorRT->ExtendLifetime();
        }
    }

    void SmartGIPass::AssembleRayDebug(const GameContext& gameContext, RenderingExecutionDescriptor* RED, const GBufferType& gBufferViews, REDTextureView* depthView, REDTextureView* rayInfosForTracing) 
    {
        auto* SmartFinalGatherRayDebugShader = mSetting.GetSmartFinalGatherRayDebugShader();
        {
            /*buffer structure, | Ray Origin | Ray End + Color |...*/
            UInt32 bufferSize = mRayDebugRayDataSize;
            auto* redBuffer = RED->AllocateBuffer("Smart.RayDebugOutputBuffer", NGIBufferDesc{bufferSize, NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::CopySrc});
            auto* redBufferView = RED->AllocateBufferView(redBuffer, NGIBufferViewDesc{redBuffer->mDesc.Usage, 0, redBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(Float4)});
            REDTextureView* depthSRV = RED->AllocateTextureView(depthView->mTexture,
                                                                NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                   depthView->mTexture->mDesc.Format,
                                                                                   NGITextureType::Texture2D,
                                                                                   {
                                                                                       NGITextureAspect::Depth,
                                                                                       0,
                                                                                       1,
                                                                                       0,
                                                                                       1,
                                                                                   }});

            auto passSetupCommon = [&](cross::REDPass* pass) {
                // screen textures
                pass->SetProperty(NAME_ID("_GBuffer0"), gBufferViews[0]);
                pass->SetProperty(NAME_ID("_GBuffer1"), gBufferViews[1]);
                pass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2]);
                pass->SetProperty(NAME_ID("_DepthMap"), depthSRV);
                pass->SetProperty(NAME_ID("_RWScreenProbeSceneDepth"), mSmartFGCommon.mScreenProbeSceneDepth);
                pass->SetProperty(NAME_ID("_RWScreenProbeWorldNormal"), mSmartFGCommon.mScreenProbeWorldNormal);
                pass->SetProperty(NAME_ID("_RWScreenProbeWorldSpeed"), mSmartFGCommon.mScreenProbeWorldSpeed);
                pass->SetProperty(NAME_ID("_RWScreenProbeTranslatedWorldPosition"), mSmartFGCommon.mScreenProbeTranslatedWorldPosition);
                pass->SetProperty(NAME_ID("PROBE_INTERPOLATION_WITH_NORMAL"), mSetting.mProbeInterpolationWithNormal);
            };

            // Generate Ray Debug Info
            {
                auto* pass = RED->AllocatePass("GenerateRayDebug", true);
                mSmartFGCommon.SetREDProperty(pass);
                passSetupCommon(pass);
                pass->SetProperty(NAME_ID("PROBE_INTERPOLATION_WITH_NORMAL"), mSetting.mProbeInterpolationWithNormal);
                pass->SetProperty(NAME_ID("StructuredImportanceSampledRayInfosForTracing"), rayInfosForTracing);
                pass->SetProperty(NAME_ID("TraceRadiance"), mSmartFGCommon.mTraceRadiance);
                REDTextureView* TraceHitSRV = RED->AllocateTextureView(
                    mSmartFGCommon.mTraceHit->mTexture,
                    NGITextureViewDesc{NGITextureUsage::ShaderResource, mSmartFGCommon.mTraceHit->mTexture->mDesc.Format, mSmartFGCommon.mTraceHit->GetDesc().ViewDimension, mSmartFGCommon.mTraceHit->GetDesc().SubRange});
                pass->SetProperty(NAME_ID("TraceHit"), TraceHitSRV);
                pass->SetProperty(NAME_ID("DebugRayInfoOutput"), redBufferView);
                pass->SetProperty(NAME_ID("RayScreenPosition"), UInt2(static_cast<UInt32>(mSetting.mDebugRayPosition.x * input_depthView->GetWidth()), static_cast<UInt32>(mSetting.mDebugRayPosition.y * input_depthView->GetHeight())));
                pass->Dispatch(SmartFinalGatherRayDebugShader, "GenerateRayDebugInfo", 1, 1, 1);
            }

            auto frameCount = EngineGlobal::Inst().GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();
            UInt32 currentWrite = frameCount % RAY_DEBUG_MAX_FLYING_FRAME;
            UInt32 currentRead = frameCount >= RAY_DEBUG_MAX_FLYING_FRAME ? (frameCount - RAY_DEBUG_MAX_FLYING_FRAME + 1) % RAY_DEBUG_MAX_FLYING_FRAME : 0;

            // Read back to Feedback buffer.
            {
                auto stagingBufferRED = RED->AllocateBuffer("FeedbackbufferCopy", mRayDebugFeedbackBuffer[currentWrite]);
                auto* vtFeedbackBufferCopyPass = RED->AllocatePass("VirtualTextureFeedbackCopy");
                const NGICopyBuffer region{
                    0,
                    0,
                    bufferSize,
                };
                vtFeedbackBufferCopyPass->CopyBufferToBuffer(stagingBufferRED, redBuffer, 1, &region);
                stagingBufferRED->SetExternalState(NGIResourceState::CopyDst);
            }

            // Draw Primitive next frame.
            {
                if (mRayDebugCurrentFreezeState != mSetting.mEnableDebugRayFreeze)
                {
                    mRayDebugCurrentFreezeState = mSetting.mEnableDebugRayFreeze;
                    if (mRayDebugCurrentFreezeState)
                    {
                        float* data = static_cast<float*>(mRayDebugFeedbackBuffer[currentRead]->MapRange(NGIBufferUsage::CopyDst, 0, bufferSize));
                        memcpy(mRayDebugFreezeDatas, data, bufferSize);
                    }
                }
                float* data = nullptr;
                if (mRayDebugCurrentFreezeState)
                {
                    data = mRayDebugFreezeDatas;
                }
                else
                {
                    data = static_cast<float*>(mRayDebugFeedbackBuffer[currentRead]->MapRange(NGIBufferUsage::CopyDst, 0, bufferSize));
                }
                {
                    UInt32 count = static_cast<UInt32>(std::pow(mSmartFGCommon.mScreenProbeTracingOctahedronResolution, 2));
                    std::vector<Float3> starts;
                    std::vector<Float3> ends;
                    std::vector<Float2> traceCoords;
                    std::vector<Float3> colors;
                    starts.resize(count);
                    ends.resize(count);
                    colors.resize(count);
                    traceCoords.resize(count);
                    for (UInt32 i = 0; i < count; i++)
                    {
                        starts[i] = Float3(data[0], data[1], data[2]);
                    }
                    UInt32 debugInfoStrip = 4*3;
                    static int preDebugRayIndex = -1;
                    for (UInt32 i = 0; i < count; i++)
                    {
                        ends[i] = Float3(data[4 + i * debugInfoStrip + 0], data[4 + i * debugInfoStrip + 1], data[4 + i * debugInfoStrip + 2]);
                        float distance = data[4 + i * debugInfoStrip + 3];
                        Float3 color = Float3(data[4 + i * debugInfoStrip + 4], data[4 + i * debugInfoStrip + 5], data[4 + i * debugInfoStrip + 6]);
                        colors[i] = color;
                        bool isSkyTrace = std::abs(mSetting.mMaxVoxelTraceDistance - distance) < 1 || std::abs(mSetting.mMaxScreenTraceDistance - distance) < 1;
                        if (isSkyTrace)
                        {
                            //ends[i] = starts[i];
                        }
                        traceCoords[i] = Float2(data[4 + i * debugInfoStrip + 8], data[4 + i * debugInfoStrip + 9]);
                        if (static_cast<UInt32>(mSetting.mDebugRayIndex) == i)
                        {
                            if (preDebugRayIndex != mSetting.mDebugRayIndex)
                            {
                                LOG_ERROR("DebugRayIndex:{}, traceCoords:({}, {}), color:({}, {}, {}), distance:{}, isSkyTrace:{}, startPos=({}, {}, {}), endPos=({}, {}, {})",
                                          mSetting.mDebugRayIndex,
                                          traceCoords[i].x,
                                          traceCoords[i].y,
                                          color.x,
                                          color.y,
                                          color.z,
                                          distance,
                                          isSkyTrace,
                                          starts[i].x,
                                          starts[i].y,
                                          starts[i].z,
                                          ends[i].x,
                                          ends[i].y,
                                          ends[i].z);
                                preDebugRayIndex = mSetting.mDebugRayIndex;
                            }
                            colors[i] = Float3(0.0, 1.0, 0.0);
                        }
                    }
                    PrimitiveGenerator::GenerateRays(&primitive[currentRead], starts.data(), ends.data(), colors.data(), count);
                    // Set TilePosition info
                    primitive[currentRead].SetTilePosition(gameContext.mRenderCamera->GetTilePosition());

                    auto primitiveSystem = gameContext.mRenderWorld->GetRenderSystem<PrimitiveRenderSystemR>();
                    MaterialR* material = primitiveSystem->GetBeforeToneMappingPureVertexMaterial();
                    primitiveSystem->BatchPrimitive(&primitive[currentRead], material, false, true);
                }
            }
        }
    }

    void SmartGIPass::SetupSkyLightingGameContext(const GameContext& gameContext, REDPass* pass)
    {
        if (mSetting.IsEnableVoxelGI(gameContext))
        {
            QUICK_SCOPED_CPU_TIMING("AssembleScreenProbeTraceVoxels_ForSkyLighting");
            auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
            mVoxelRenderer.AssembleScreenProbeTraceVoxels_ForSkyLighting(gameContext, red, pass);
        }
    }

    void SmartGIPostProcessSetting::Initialize() {}

    namespace ComputeShaderUtils
    {
        UInt3 GetGroupCount(ComputeShaderR* shader, const NameID& kernel, UInt3 dispatchSize)
        {
            UInt3 groupSize;
            shader->GetThreadGroupSize(kernel, groupSize.x, groupSize.y, groupSize.z);

            return GetGroupCount(dispatchSize, groupSize);
        }

        void SetCameraShaderParameters(REDPass* pass, RenderCamera* camera)
        {
            // This Frame View
            pass->SetProperty(NAME_ID("ce_CameraPos"), camera->GetCameraOrigin());
            pass->SetProperty(NAME_ID("ce_View"), camera->GetViewMatrix());
            pass->SetProperty(NAME_ID("ce_Projection"), camera->GetProjMatrix());
            pass->SetProperty(NAME_ID("ce_InvView"), camera->GetInvertViewMatrix());
            pass->SetProperty(NAME_ID("ce_InvProjection"), camera->GetInvertProjMatrix());
            pass->SetProperty(NAME_ID("ce_InvViewProjMatrix"), camera->GetInvertProjMatrix() * camera->GetInvertViewMatrix());

            // Last Frame View
            const Float4x4A& LastFrameViewMatrix = camera->GetLastFrameViewMatrix();
            pass->SetProperty(NAME_ID("_LastFrame_View"), camera->GetLastFrameViewMatrix());
            pass->SetProperty(NAME_ID("_LastFrame_Projection"), camera->GetLastFrameProjMatrix());
            pass->SetProperty(NAME_ID("ce_PreViewMatrix"), LastFrameViewMatrix);
            pass->SetProperty(NAME_ID("ce_PreProjMatrix"), camera->GetLastFrameProjMatrix());
            pass->SetProperty(NAME_ID("ce_PreInvViewMatrix"), camera->GetLastFrameInvertViewMatrix());
            pass->SetProperty(NAME_ID("ce_PreInvProjMatrix"), camera->GetLastFrameInvertProjMatrix());
            pass->SetProperty(NAME_ID("ce_PreInvViewProjMatrix"), camera->GetLastFrameInvertProjMatrix() * camera->GetLastFrameInvertViewMatrix());

            // TODO: LastFrameCameraPos is incorrect
            Float3 LastFrameCameraPos = -Float3(
                LastFrameViewMatrix.m03, LastFrameViewMatrix.m13, LastFrameViewMatrix.m23
            );
            pass->SetProperty(NAME_ID("ce_PreCameraPos"), LastFrameCameraPos);
            Float4x4A OffsetMat = Float4x4::CreateTranslation(camera->GetTilePosition() - camera->GetTilePosition<true>() * LENGTH_PER_TILE);
            Float4x4 ClipToPrevClip = static_cast<Float4x4>(camera->GetViewProjMatrix()).Inverted() * OffsetMat * camera->GetLastFrameViewProjMatrix();
            pass->SetProperty(NAME_ID("_View_ClipToPrevClip"), ClipToPrevClip);
            pass->SetProperty(NAME_ID("ce_ClipToPrevClipMat"), ClipToPrevClip);
            // Not Used now ?
            pass->SetProperty(NAME_ID("ce_ClipToPrevClipMatNoJitter"), ClipToPrevClip);
            pass->SetProperty(NAME_ID("ce_ProjMatrixJitter"), Float2(camera->GetJitterIntensity(), camera->GetJitterIntensity()));

            // Use Reverse Z
            pass->SetProperty(NAME_ID("_View_UseReverseZ"), true);
        }

        void SetCommonTextureShaderParameters(REDPass* pass, std::optional<REDTextureView*> sceneColor, std::optional<REDTextureView*> preSceneColor,
            std::optional<std::array<REDTextureView*, 4>> gBuffer, std::optional<REDTextureView*> depthMap, std::optional<REDTextureView*> preDepthMap)
        {
            // SceneColor
            if (sceneColor.has_value())
            {
                pass->SetProperty("_SceneColor", sceneColor.value());

                UInt2 ViewSize = sceneColor.value()->GetSize();
                Float4 ViewSizeAndInvSize;
                ViewSizeAndInvSize.x = static_cast<float>(ViewSize.x);
                ViewSizeAndInvSize.y = static_cast<float>(ViewSize.y);
                ViewSizeAndInvSize.z = 1.f / static_cast<float>(ViewSize.x);
                ViewSizeAndInvSize.w = 1.f / static_cast<float>(ViewSize.y);
                pass->SetProperty("_View_SizeAndInvSize", ViewSizeAndInvSize);
            }

            // Last Frame SceneColor
            if (sceneColor.has_value())
            {
                pass->SetProperty("_PrevSceneColor", preSceneColor.value());
            }

            // GBuffer
            if (gBuffer.has_value())
            {
                pass->SetProperty(NAME_ID("_GBuffer0"), gBuffer.value()[0]);
                pass->SetProperty(NAME_ID("_GBuffer1"), gBuffer.value()[1]);
                pass->SetProperty(NAME_ID("_GBuffer2"), gBuffer.value()[2]);
                pass->SetProperty(NAME_ID("_GBuffer3"), gBuffer.value()[3]);

                UInt2 gBufferSize = gBuffer.value()[0]->GetSize();
                Float4 BufferSizeAndInvSize;
                BufferSizeAndInvSize.x = static_cast<float>(gBufferSize.x);
                BufferSizeAndInvSize.y = static_cast<float>(gBufferSize.y);
                BufferSizeAndInvSize.z = 1.f / static_cast<float>(gBufferSize.x);
                BufferSizeAndInvSize.w = 1.f / static_cast<float>(gBufferSize.y);
                pass->SetProperty(NAME_ID("_View_BufferSizeAndInvSize"), BufferSizeAndInvSize);
            }

            // Depth Map
            if (depthMap.has_value())
            {
                pass->SetProperty(NAME_ID("_DepthMap"), depthMap.value());
            }

            // Last Frame Depth Map
            if (preDepthMap.has_value())
            {
                pass->SetProperty(NAME_ID("_PrevDepthMap"), preDepthMap.value());
            }

            pass->SetProperty(NAME_ID("_View_ScreenPositionScaleBias"), Float4(0.5, -0.5, 0.5, 0.5));
        }

        void SetReprojectPassShaderParameters(REDPass* pass, RenderCamera* camera, std::optional<REDTextureView*> sceneColor, std::optional<REDTextureView*> preSceneColor,
            std::optional<std::array<REDTextureView*, 4>> gBuffer, std::optional<REDTextureView*> depthMap, std::optional<REDTextureView*> preDepthMap)
        {
            SetCameraShaderParameters(pass, camera);
            SetCommonTextureShaderParameters(pass, sceneColor, preSceneColor, gBuffer, depthMap, preDepthMap);
        }
    }
}
