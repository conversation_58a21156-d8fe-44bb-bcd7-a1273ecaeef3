#pragma once
#include "PassBase.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "AOPass.h"

namespace cross {

    enum class CEMeta(Reflect, Editor) AOType
    {
        NONE = 0,
        GTAO
    };

class CEMeta(Editor) RENDER_ENGINE_API AmbientOcclusionSetting : public PassSetting
{
public:
    CE_Virtual_Serialize_Deserialize;

    CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Ambient Occlusion Type"))
    AOType mAOType = AOType::GTAO;

    // GTAO setting
    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "GTAO Settings", ToolTips = "GTAO"))
    GTAOSettings mGTAOSetting;

    virtual void Initialize() override;
};

class RENDER_ENGINE_API AmbientOcclusion : public PassBase<AmbientOcclusionSetting, AmbientOcclusion>
{
public:
    RenderWorld* mWorld;
private:
    RenderingExecutionDescriptor* mRED;

protected:

    friend PassBase<AmbientOcclusionSetting, AmbientOcclusion>;
};
}