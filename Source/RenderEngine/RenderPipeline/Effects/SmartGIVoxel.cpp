#include "SmartGIVoxel.h"
#include "RenderEngine/AABBSystemR.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "RenderEngine/SkyLightSystemR.h"
#include "SmartGIPass.h"
#include <NativeGraphicsInterface/NGIManager.h>
#include <RenderEngine/LightSystemR.h>
#include <RenderEngine/TransformSystemR.h>
#include <RenderEngine/VisibilitySystemR.h>
#include "../FFSRenderPipeline.h"
// #include "RenderEngine/CullingGatherSystemR.h"
#include "ModelSystemR.h"
#include "RenderEngine/RendererVoxelizeHelper.h"
#include "RenderEngine/VoxelizeSystemR.h"
#include "RenderEngine/RenderPipeline/Effects/FoliageGpuDriven.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "Runtime/GameWorld/AABBSystemG.h"

namespace cross {

bool GSmartVoxelLightingEnable = true;

int32_t GSmartSceneClipmapResolution = 64;
int32_t GSmartSceneClipmapZResolutionDivisor = 1;
int32_t GSmartSceneNumClipmapLevels = 4;
//float GSmartSceneFirstClipmapWorldExtent = 2500.0f;
int32_t GSmartSceneVoxelLightingForceUpdateClipmapIndex = -1;
int32_t GSmartDrawClipmapBounds = 0;
int32_t GSmartDebugIncrementUpdate = 0;
float GDiffuseTraceStepFactor = 1;
float GSmartDiffuseMinTraceDistance = 0;
float GSmartSurfaceBias = 5.0f;
float GSmartRadiosityDecayRate = 0.7f;

Float3 Int3ToFloat3(Int3 input)
{
    return Float3(static_cast<float>(input.x), static_cast<float>(input.y), static_cast<float>(input.z));
}

float SmartGI::GetFirstClipmapWorldExtent()
{
    FFSRenderPipelineSetting* rpSettings = dynamic_cast<FFSRenderPipelineSetting*>(EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting());
    return std::max(rpSettings->mIndirectLightingCompositeSettings.mSmartGISetting.mSmartSceneFirstClipmapWorldExtent, 1.f);
}

Float3 SmartGI::GetFirstClipmapTexelSize()
{
    float worldSize = GetFirstClipmapWorldExtent() * 2;
    UInt32 clipmapResolutionXY = GetClipmapResolutionXY();
    UInt32 clipmapResolutionZ = GetClipmapResolutionZ();
    return Float3(worldSize / clipmapResolutionXY, worldSize / clipmapResolutionXY, worldSize / clipmapResolutionZ);
}

bool SmartGI::ShouldRenderVoxelLighting()
{
    return GSmartVoxelLightingEnable;
}

uint32_t SmartGI::GetClipmapResolutionXY()
{
    return std::clamp(GSmartSceneClipmapResolution, 4, 512);
}

uint32_t SmartGI::GetClipmapResolutionZ()
{
    return GetClipmapResolutionXY() / std::clamp(GSmartSceneClipmapZResolutionDivisor, 1, 8);
}

Int3 SmartGI::GetClipmapResolution()
{
    return Int3(GetClipmapResolutionXY(), GetClipmapResolutionXY(), GetClipmapResolutionZ());
}

Int3 SmartGI::GetUpdateGridResolution()
{
    const Int3 ClipmapResolution = GetClipmapResolution();
    return Int3((ClipmapResolution.x + GVisBufferTileSize) / GVisBufferTileSize, (ClipmapResolution.y + GVisBufferTileSize) / GVisBufferTileSize, (ClipmapResolution.z + GVisBufferTileSize) / GVisBufferTileSize);
}

Float3 SmartGI::GetSmartVoxelClipmapExtent(int32_t ClipmapIndex)
{
    const Float3 FirstClipmapWorldExtent(SmartGI::GetFirstClipmapWorldExtent(), SmartGI::GetFirstClipmapWorldExtent(), SmartGI::GetFirstClipmapWorldExtent() / static_cast<float>(GSmartSceneClipmapZResolutionDivisor));
    const auto ClipmapWorldScale = static_cast<float>(1 << ClipmapIndex);
    return FirstClipmapWorldExtent * ClipmapWorldScale;
}

int32_t SmartGI::GetNumSmartVoxelClipmaps(float SmartSceneViewDistance)
{
    int32_t WantedClipmaps = GSmartSceneNumClipmapLevels;

    if (GetSmartVoxelClipmapExtent(WantedClipmaps + 1).x <= SmartSceneViewDistance)
    {
        WantedClipmaps += 2;
    }
    else if (GetSmartVoxelClipmapExtent(WantedClipmaps).x <= SmartSceneViewDistance)
    {
        WantedClipmaps += 1;
    }

    return std::clamp(WantedClipmaps, 1, GMaxVoxelClipmapLevels);
}

Float3 SmartGI::GetSmartSceneViewOrigin(Float3 CameraOrigin, Float3 CameraVelocityOffset, int32_t ClipmapIndex)
{
    if (ClipmapIndex > 0)
    {
        const Float3 ClipmapExtent = GetSmartVoxelClipmapExtent(ClipmapIndex);
        constexpr float MaxCameraDriftFraction = .75f;
        CameraVelocityOffset.x = std::clamp<float>(CameraVelocityOffset.x, -ClipmapExtent.x * MaxCameraDriftFraction, ClipmapExtent.x * MaxCameraDriftFraction);
        CameraVelocityOffset.y = std::clamp<float>(CameraVelocityOffset.y, -ClipmapExtent.y * MaxCameraDriftFraction, ClipmapExtent.y * MaxCameraDriftFraction);
        CameraVelocityOffset.z = std::clamp<float>(CameraVelocityOffset.z, -ClipmapExtent.z * MaxCameraDriftFraction, ClipmapExtent.z * MaxCameraDriftFraction);
    }

    //CameraOrigin += CameraVelocityOffset;
    return CameraOrigin;
}

bool SmartGI::ShouldUpdateVoxelClipmap(int32_t ClipmapIndex, int32_t NumClipmaps, int32_t FrameNumber)
{
    if (GSmartSceneVoxelLightingForceUpdateClipmapIndex >= 0 && GSmartSceneVoxelLightingForceUpdateClipmapIndex < NumClipmaps)
    {
        return ClipmapIndex == GSmartSceneVoxelLightingForceUpdateClipmapIndex;
    }

    if (NumClipmaps == 1)
    {
        return true;
    }
    else if (ClipmapIndex == 0)
    {
        return FrameNumber % 2 == 0;
    }
    else if (ClipmapIndex == 1)
    {
        return FrameNumber % 8 == 1 || FrameNumber % 8 == 5;
    }
    else if (ClipmapIndex == 2)
    {
        return FrameNumber % 8 == 3;
    }
    else if (NumClipmaps > 4)
    {
        if (ClipmapIndex == 3)
        {
            return FrameNumber % 16 == 7;
        }
        else if (ClipmapIndex == 4)
        {
            return FrameNumber % 32 == 15;
        }
        else
        {
            return FrameNumber % 32 == 31;
        }
    }
    else
    {
        if (ClipmapIndex == 3)
        {
            return FrameNumber % 8 == 7;
        }
        else
        {
            return FrameNumber % 8 == 1;
        }
    }
}

inline BoundingBox GetClipmapTiledBounds(const BoundingBox& ClipmapBounds, const Float3 UpdateTileWorldSize, const BoundingBox& ModifiedBounds, bool bUseWrapMode = true)
{
    Float3 MinValue, MaxValue;
    ModifiedBounds.GetMinMax(&MinValue, &MaxValue);
    Float3 ClipmapBoundsMin, ClipmapBoundsMax;
    ClipmapBounds.GetMinMax(&ClipmapBoundsMin, &ClipmapBoundsMax);

    if (!bUseWrapMode)
    {
        MinValue -= ClipmapBoundsMin;
        MaxValue -= ClipmapBoundsMin;
    }

    MinValue.x = std::floor(MinValue.x / UpdateTileWorldSize.x);
    MinValue.y = std::floor(MinValue.y / UpdateTileWorldSize.y);
    MinValue.z = std::floor(MinValue.z / UpdateTileWorldSize.z);

    MaxValue.x = std::ceil(MaxValue.x / UpdateTileWorldSize.x);
    MaxValue.y = std::ceil(MaxValue.y / UpdateTileWorldSize.y);
    MaxValue.z = std::ceil(MaxValue.z / UpdateTileWorldSize.z);

    MinValue = MinValue * UpdateTileWorldSize;
    MaxValue = MaxValue * UpdateTileWorldSize;

    if (!bUseWrapMode)
    {
        MinValue += ClipmapBoundsMin;
        MaxValue += ClipmapBoundsMin;
    }

    return {(MinValue + MaxValue) / 2, (MaxValue - MinValue) / 2};
}

void AddUpdateBoundsForAxis(
    Int3 MovementInTiles,
    const BoundingBox& ClipmapBounds,
    float UpdateTileWorldSize,
    int32_t ComponentIndex,
    std::vector<BoundingBox>& UpdateBounds)
{
    auto MovementInWorldPosition = Int3ToFloat3(MovementInTiles) * UpdateTileWorldSize;
    auto AxisUpdateBoundsNew = ClipmapBounds;

    Float3 ClipmapBoundsCenter, ClipmapBoundsExtent, AxisUpdateBoundsOldMin, AxisUpdateBoundsOldMax, AxisUpdateBoundsNewMin, AxisUpdateBoundsNewMax;
    ClipmapBounds.GetCenter(&ClipmapBoundsCenter);
    ClipmapBounds.GetExtent(&ClipmapBoundsExtent);
    const auto AxisUpdateBoundsOld = BoundingBox(ClipmapBoundsCenter - MovementInWorldPosition, ClipmapBoundsExtent);

    AxisUpdateBoundsNew.GetMinMax(&AxisUpdateBoundsNewMin, &AxisUpdateBoundsNewMax);
    AxisUpdateBoundsOld.GetMinMax(&AxisUpdateBoundsOldMin, &AxisUpdateBoundsOldMax);

    if (MovementInTiles.data()[ComponentIndex] > 0)
    {
        AxisUpdateBoundsNewMin.data()[ComponentIndex] =
            std::max(AxisUpdateBoundsNewMax.data()[ComponentIndex] - MovementInWorldPosition.data()[ComponentIndex], AxisUpdateBoundsNewMin.data()[ComponentIndex]);
        //AxisUpdateBoundsOldMax.data()[ComponentIndex] = std::max(AxisUpdateBoundsOldMin.data()[ComponentIndex] + MovementInWorldPosition.data()[ComponentIndex], ClipmapBoundsMin.data()[ComponentIndex]);
    }
    else if (MovementInTiles.data()[ComponentIndex] < 0)
    {
        AxisUpdateBoundsNewMax.data()[ComponentIndex] =
            std::min(AxisUpdateBoundsNewMin.data()[ComponentIndex] - MovementInWorldPosition.data()[ComponentIndex], AxisUpdateBoundsNewMax.data()[ComponentIndex]);
        //AxisUpdateBoundsOldMin.data()[ComponentIndex] = std::min(AxisUpdateBoundsOldMax.data()[ComponentIndex] + MovementInWorldPosition.data()[ComponentIndex], ClipmapBoundsMax.data()[ComponentIndex]);
    }

    if (std::abs(MovementInTiles.data()[ComponentIndex]) > 0)
    {
        Float3 newExtent = (AxisUpdateBoundsNewMax - AxisUpdateBoundsNewMin) / 2.0;
        AxisUpdateBoundsNew = BoundingBox(AxisUpdateBoundsNewMin + newExtent, newExtent);
        UpdateBounds.push_back(AxisUpdateBoundsNew);
        //UpdateBounds.push_back(AxisUpdateBoundsOld);
    }
}

SmartVoxelLightingState::SmartVoxelLightingState() {}

SmartVoxelLightingState::~SmartVoxelLightingState()
{
    if (!mInited)
    {
        return;
    }
    mInited = false;
    const auto ClampedNumClipmapLevels = SmartGI::GetNumSmartVoxelClipmaps(GSmartSceneViewDistance);
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    // delete resources
    for (int Index = 0; Index < GNumVoxelLightingTextures; Index++)
    {
        rendererSystem->DestroyNGIObject(std::unique_ptr<NGIObject>(LightingTextures[Index].release()));
    }
    rendererSystem->DestroyNGIObject(std::unique_ptr<NGIObject>(OpacityTexture.release()));
    rendererSystem->DestroyNGIObject(std::unique_ptr<NGIObject>(OpacityCompactTexture.release()));
    rendererSystem->DestroyNGIObject(std::unique_ptr<NGIObject>(AlbedoTexture.release()));
    rendererSystem->DestroyNGIObject(std::unique_ptr<NGIObject>(NormalTexture.release()));
    rendererSystem->DestroyNGIObject(std::unique_ptr<NGIObject>(EmissiveTexture.release()));
    rendererSystem->DestroyNGIObject(std::unique_ptr<NGIObject>(VoxelVisBufferAllocator.release()));
    for (int32_t ClipmapIndex = 0; ClipmapIndex < ClampedNumClipmapLevels; ++ClipmapIndex)
    {
        rendererSystem->DestroyNGIObject(std::unique_ptr<NGIObject>(VoxelVisBuffers[ClipmapIndex].release()));
    }
}

void SmartVoxelLightingState::Init()
{
    if (mInited)
    {
        return;
    }
    mInited = true;
    auto& device = GetNGIDevice();
    const auto ClipmapResolution = SmartGI::GetClipmapResolution();
    const auto ClampedNumClipmapLevels = SmartGI::GetNumSmartVoxelClipmaps(GSmartSceneViewDistance);
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    for (int Index = 0; Index < GNumVoxelLightingTextures; Index++)
    {
        const NGITextureDesc desc{GraphicsFormat::R16G16B16A16_SFloat,
                                  NGITextureType::Texture3D,
                                  1,
                                  1,
                                  static_cast<UInt32>(ClipmapResolution.x),
                                  static_cast<UInt32>(ClipmapResolution.y * ClampedNumClipmapLevels),
                                  static_cast<UInt16>(ClipmapResolution.z * GNumVoxelDirections),
                                  1,
                                  NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst};
        LightingTextures[Index].reset(device.CreateTexture(desc, "SmartGI.LightingTexture"));
        rendererSystem->InitializeTexture(LightingTextures[Index].get(), NGIResourceState::ShaderStageBitMask | NGIResourceState::ShaderResourceBit);
    }

    {
        const NGITextureDesc descOpacity{GraphicsFormat::R8_UNorm,
                                         NGITextureType::Texture3D,
                                         1,
                                         1,
                                         static_cast<UInt32>(ClipmapResolution.x),
                                         static_cast<UInt32>(ClipmapResolution.y * ClampedNumClipmapLevels),
                                         static_cast<UInt16>(ClipmapResolution.z),
                                         1,
                                         NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst};
        OpacityTexture.reset(device.CreateTexture(descOpacity, "SmartGI.OpacityTexture"));
        rendererSystem->InitializeTexture(OpacityTexture.get(), NGIResourceState::ShaderStageBitMask | NGIResourceState::ShaderResourceBit);

        const NGITextureDesc descOpacityCompact{GraphicsFormat::R8_UInt,
                                                NGITextureType::Texture3D,
                                                1,
                                                1,
                                                static_cast<UInt32>(ClipmapResolution.x / 2),
                                                static_cast<UInt32>(ClipmapResolution.y * ClampedNumClipmapLevels / 2),
                                                static_cast<UInt16>(ClipmapResolution.z / 2),
                                                1,
                                                NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst};
        OpacityCompactTexture.reset(device.CreateTexture(descOpacityCompact, "SmartGI.OpacityCompact"));
        rendererSystem->InitializeTexture(OpacityCompactTexture.get(), NGIResourceState::ShaderStageBitMask | NGIResourceState::ShaderResourceBit);
    }

    {
        const NGITextureDesc desc{GraphicsFormat::R32_UInt,
                                  NGITextureType::Texture3D,
                                  1,
                                  1,
                                  static_cast<UInt32>(ClipmapResolution.x),
                                  static_cast<UInt32>(ClipmapResolution.y * ClampedNumClipmapLevels),
                                  static_cast<UInt16>(ClipmapResolution.z * GNumVoxelDirections),
                                  1,
                                  NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst};
        AlbedoTexture.reset(device.CreateTexture(desc, "SmartGI.Albedo"));
        NormalTexture.reset(device.CreateTexture(desc, "SmartGI.Normal"));
        EmissiveTexture.reset(device.CreateTexture(desc, "SmartGI.Emissive"));
        rendererSystem->InitializeTexture(AlbedoTexture.get(), NGIResourceState::ShaderStageBitMask | NGIResourceState::ShaderResourceBit);
        rendererSystem->InitializeTexture(NormalTexture.get(), NGIResourceState::ShaderStageBitMask | NGIResourceState::ShaderResourceBit);
        rendererSystem->InitializeTexture(EmissiveTexture.get(), NGIResourceState::ShaderStageBitMask | NGIResourceState::ShaderResourceBit);
    }

    {
        const NGIBufferDesc desc{sizeof(uint32_t) * ClampedNumClipmapLevels, NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer};
        VoxelVisBufferAllocator.reset(GetNGIDevice().CreateBuffer(desc, "VoxelVis"));
    }

    for (int32_t ClipmapIndex = 0; ClipmapIndex < ClampedNumClipmapLevels; ++ClipmapIndex)
    {
        NGIBufferDesc desc{sizeof(uint32_t) * ClipmapResolution.x * ClipmapResolution.y * ClipmapResolution.z * 6, NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer};
        VoxelVisBuffers[ClipmapIndex].reset(GetNGIDevice().CreateBuffer(desc, "VoxelVisClip"));
    }
}

void SmartVoxelRenderer::InitTracingInputs(SmartTracingInputs& inputs) const
{
    const int32_t ClampedNumClipmapLevels = SmartGI::GetNumSmartVoxelClipmaps(GSmartSceneViewDistance);
    for (int32_t ClipmapIndex = 0; ClipmapIndex < ClampedNumClipmapLevels; ++ClipmapIndex)
    {
        const auto& Clipmap = mVoxelLightingClipmapState[ClipmapIndex];

        inputs.ClipmapWorldToUVScale[ClipmapIndex] = Float4(Float3(1.0f, 1.0f, 1.0f) / (2.0f * Clipmap.Extent), 0.0f);
        inputs.ClipmapWorldToUVBias[ClipmapIndex] = -Float4((Clipmap.Center - Clipmap.Extent), 0.0f) * inputs.ClipmapWorldToUVScale[ClipmapIndex];
        inputs.ClipmapVoxelSizeAndRadius[ClipmapIndex] = Float4((Float3)Clipmap.VoxelSize, Clipmap.VoxelRadius);
        inputs.ClipmapWorldCenter[ClipmapIndex] = Float4(Clipmap.Center, 0.0f);
        inputs.ClipmapWorldExtent[ClipmapIndex] = Float4(Clipmap.Extent, 0.0f);
    }

    inputs.VoxelGridResolution = SmartGI::GetClipmapResolution();
    inputs.NumClipmapLevels = ClampedNumClipmapLevels;
}

void SmartVoxelRenderer::AssembleVoxelLighting(const GameContext& gameContext, RenderingExecutionDescriptor* RED, REDTexture* depthTexture, const ShadowProperties* shadowProperties, REDTextureView*& outDebugView,
                                               REDTextureView* preSceneColorView, REDTextureView* preDepthView, REDTextureView* preDiffuse, const std::array<REDTextureView*, 4>& gBufferViews)
{
    UpdateClipmapBounds(gameContext);
    InitTracingInputs(mTracingInputs);

    const int32_t ClampedNumClipmapLevels = SmartGI::GetNumSmartVoxelClipmaps(GSmartSceneViewDistance);
    bool ShouldResetLighting = mVoxelLightingReset; // The init value of mVoxelLightingReset is true, i.e. Reset Lighting for the first frame
    mVoxelLightingReset = false;

    std::vector<int32_t> ClipmapsToUpdate;
    for (int32_t ClipmapIndex = 0; ClipmapIndex < ClampedNumClipmapLevels; ClipmapIndex++)
    {
        if (ShouldResetLighting || SmartGI::ShouldUpdateVoxelClipmap(ClipmapIndex, ClampedNumClipmapLevels, static_cast<int32_t>(mSmartViewParams.mFrameIndex)))
        {
            ClipmapsToUpdate.push_back(ClipmapIndex);
        }
        // TODO(jackjhu): DrawClipmap
        // DrawClipmap(GraphBuilder, SceneContext, View, mTracingInputs, ClipmapIndex);
    }

    bool ShouldVoxelizeWholeClipmap = ShouldResetLighting || mPassSettings.mVoxelsForceFullVoxelize;

    // TODO(timllpan): just for test
    // ClipmapsToUpdate = {0};
    if (ClipmapsToUpdate.size() > 0)
    {
        if (mPassSettings.mVoxelsForceUpdateLighting)
        {
            for (const int32_t ClipmapIndex : ClipmapsToUpdate)
            {
                SetupClipmapParameters(mTracingInputs, ClipmapIndex, &ShouldVoxelizeWholeClipmap);

                RED->BeginRegion("SmartVoxelizeScene");
                VoxelizeScene(gameContext, RED, mTracingInputs, ClipmapIndex, ShouldVoxelizeWholeClipmap);
                RED->EndRegion();

                CompactVisVoxels(gameContext, RED, mTracingInputs, ClipmapIndex);
                RenderRadiosity(gameContext, RED, mTracingInputs, ClipmapIndex, preSceneColorView, preDepthView, preDiffuse, gBufferViews);
                RenderDirectLighting(gameContext, RED, mTracingInputs, shadowProperties, ClipmapIndex);
            }
        }
        else
        {
            bool VoxelDataChanged = mPassSettings.mVoxelsForceFullVoxelize;
            for (const int32_t ClipmapIndex : ClipmapsToUpdate)
            {
                SetupClipmapParameters(mTracingInputs, ClipmapIndex, &ShouldVoxelizeWholeClipmap);

                VoxelDataChanged = VoxelDataChanged || mVoxelLightingClipmapState[ClipmapIndex].UpdateBounds.size() != 0;
            }

            for (const int32_t ClipmapIndex : ClipmapsToUpdate)
            {
                RED->BeginRegion("SmartVoxelizeScene");
                VoxelizeScene(gameContext, RED, mTracingInputs, ClipmapIndex, ShouldVoxelizeWholeClipmap);
                RED->EndRegion();
            }

            LightSystemR* lightSys = gameContext.mRenderWorld->GetRenderSystem<LightSystemR>();
            bool LightChanged = lightSys->GetChangeList().GetCount() != 0;
            // TODO(scolu): Deal with LightDelete
            bool LightDelete = false;
            // TODO(scolu): Deal with mVoxelRadiosity or mInjectLightingFromPrevFrame state change
            bool SmartGIStateChanged = false;

            bool NeedReCalculateLighting = LightChanged || VoxelDataChanged || ShouldResetLighting || LightDelete || SmartGIStateChanged;

            // Mark All Clipmaps Need Update
            if (NeedReCalculateLighting)
            {
                for (int32_t ClipmapIndex = 0; ClipmapIndex < SmartGI::GetNumSmartVoxelClipmaps(GSmartSceneViewDistance); ClipmapIndex++)
                {
                    ClipmapLightingToUpdate.insert(ClipmapIndex);
                }
            }

            // Inject Direct Lighting and Trace Indirect Lighting if current update frame need update lighting
            for (const int32_t ClipmapIndex : ClipmapsToUpdate)
            {
                auto it = ClipmapLightingToUpdate.find(ClipmapIndex);
                if (it != ClipmapLightingToUpdate.end())
                {
                    CompactVisVoxels(gameContext, RED, mTracingInputs, ClipmapIndex);
                    RenderRadiosity(gameContext, RED, mTracingInputs, ClipmapIndex, preSceneColorView, preDepthView, preDiffuse, gBufferViews);
                    RenderDirectLighting(gameContext, RED, mTracingInputs, shadowProperties, ClipmapIndex);
                    ClipmapLightingToUpdate.erase(it);
                }
            }
        }
    }

    if (mPassSettings.mDebugShowVoxels)
    {
        AssembleDebugVisualizeVoxels(gameContext, RED, mPassSettings.SmartVisualizeVoxelsShaderR, outDebugView, depthTexture);
        RenderVisualizeScene(gameContext, RED, outDebugView, depthTexture);

        outDebugView = RED->AllocateTextureView(outDebugView->mTexture,
                                                NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                   outDebugView->mDesc.Format,
                                                                   NGITextureType::Texture2D,
                                                                   {
                                                                       NGITextureAspect::Color,
                                                                       0,
                                                                       1,
                                                                       0,
                                                                       1,
                                                                   }});

        auto ffsRdrPipe = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
        if (ffsRdrPipe)
        {
            mVisualize = ViewModeVisualizeType::DebugColor;
            ffsRdrPipe->GetViewModeVisualization().SetRenderDebugTex(outDebugView);
        }
    }
}

void SmartVoxelRenderer::UpdateClipmapBounds(const GameContext& gameContext)
{
    const int32_t ClampedNumClipmapLevels = SmartGI::GetNumSmartVoxelClipmaps(GSmartSceneViewDistance);

    for (int32_t ClipmapIndex = 0; ClipmapIndex < ClampedNumClipmapLevels; ClipmapIndex++)
    {
        SmartVoxelLightingClipmapState& Clipmap = mVoxelLightingClipmapState[ClipmapIndex];

        AABBSystemR* aabbSys = gameContext.mRenderWorld->GetRenderSystem<AABBSystemR>();
        RenderPropertySystemR* rpSys = gameContext.mRenderWorld->GetRenderSystem<RenderPropertySystemR>();

        const auto& aabbInvalidLists = aabbSys->GetInvalidVoxelizeAABBZoneList();
        const auto& listSize = aabbInvalidLists.GetCount();
        for (UInt32 i = 0; i < listSize; ++i)
        {
            const auto& [entityID, bbox] = aabbInvalidLists.GetData(i);

            Clipmap.PrimitiveModifiedBounds.push_back(bbox);
        }
    }
}

void SmartVoxelRenderer::SetupClipmapParameters(SmartTracingInputs& TracingInputs, int32_t ClipmapIndex, bool* bForceFullUpdate)
{
    const Int3 ClipmapResolution = TracingInputs.VoxelGridResolution;
    SmartVoxelLightingClipmapState& Clipmap = mVoxelLightingClipmapState[ClipmapIndex];

    const Int3 UpdateGridResolution = SmartGI::GetUpdateGridResolution();
    const Float3 SmartSceneCameraOrigin = SmartGI::GetSmartSceneViewOrigin(mSmartViewParams.mCameraOrigin, mSmartViewParams.mCameraVelocityOffset, ClipmapIndex);
    const Float3 ClipmapExtent = SmartGI::GetSmartVoxelClipmapExtent(ClipmapIndex);

    const Float3 UpdateTileWorldSize = (2.0f * ClipmapExtent) / Int3ToFloat3(UpdateGridResolution);
    Int3 UpdateTileCenter;
    UpdateTileCenter.x = static_cast<int>(std::floor(SmartSceneCameraOrigin.x / UpdateTileWorldSize.x));
    UpdateTileCenter.y = static_cast<int>(std::floor(SmartSceneCameraOrigin.y / UpdateTileWorldSize.y));
    UpdateTileCenter.z = static_cast<int>(std::floor(SmartSceneCameraOrigin.z / UpdateTileWorldSize.z));

    Clipmap.Center = Float3(
        static_cast<float>(UpdateTileCenter.x),
        static_cast<float>(UpdateTileCenter.y),
        static_cast<float>(UpdateTileCenter.z)
        ) * UpdateTileWorldSize;
    Clipmap.Extent = ClipmapExtent;
    Clipmap.VoxelSize = 2.0f * Clipmap.Extent / Int3ToFloat3(ClipmapResolution);
    Clipmap.VoxelRadius = Clipmap.VoxelSize.Length();

    TracingInputs.ClipmapWorldToUVScale[ClipmapIndex] = Float4(Float3(1.0f, 1.0f, 1.0f) / (2.0f * Clipmap.Extent), 0.0f);
    TracingInputs.ClipmapWorldToUVBias[ClipmapIndex] = -Float4((Clipmap.Center - Clipmap.Extent), 0.0f) * TracingInputs.ClipmapWorldToUVScale[ClipmapIndex];
    TracingInputs.ClipmapVoxelSizeAndRadius[ClipmapIndex] = Float4((Float3)Clipmap.VoxelSize, Clipmap.VoxelRadius);
    TracingInputs.ClipmapWorldCenter[ClipmapIndex] = Float4(Clipmap.Center, 0.0f);
    TracingInputs.ClipmapWorldExtent[ClipmapIndex] = Float4(Clipmap.Extent, 0.0f);

    // Update clipmap bounds
    auto& PrimitiveModifiedBounds = Clipmap.PrimitiveModifiedBounds;
    const BoundingBox ClipmapBounds(Clipmap.Center, Clipmap.Extent);
    std::vector<BoundingBox> UpdateBounds;

    if (*bForceFullUpdate)
    {
        UpdateBounds.push_back(ClipmapBounds);
    }
    else
    {
        Float3 max, min;
        ClipmapBounds.GetMinMax(&min, &max);
        const Float3 UpdateGridWorldSize = (max - min) / Int3ToFloat3(ClipmapResolution);

        for (int32_t BoundsIndex = 0; BoundsIndex < PrimitiveModifiedBounds.size(); BoundsIndex++)
        {
            const auto& ModifiedBounds = PrimitiveModifiedBounds[BoundsIndex];

            if (ModifiedBounds.Intersects(ClipmapBounds))
            {
                UpdateBounds.push_back(GetClipmapTiledBounds(ClipmapBounds, UpdateGridWorldSize, ModifiedBounds));
            }
        }

        // Add an update region for each potential axis of camera movement
        const auto MovementInTiles = Int3(UpdateTileCenter.x - Clipmap.LastPartialUpdateOriginInTiles.x,
                              UpdateTileCenter.y - Clipmap.LastPartialUpdateOriginInTiles.y,
                              UpdateTileCenter.z - Clipmap.LastPartialUpdateOriginInTiles.z);
        AddUpdateBoundsForAxis(MovementInTiles, ClipmapBounds, UpdateTileWorldSize.x, 0, UpdateBounds);
        AddUpdateBoundsForAxis(MovementInTiles, ClipmapBounds, UpdateTileWorldSize.y, 1, UpdateBounds);
        AddUpdateBoundsForAxis(MovementInTiles, ClipmapBounds, UpdateTileWorldSize.z, 2, UpdateBounds);
    }

    // If bounds num is too large, it will cost a huge time for culling. PrimitiveNum * BoundsNum + VoxelsSize * BoundsNum.
    if (UpdateBounds.size() > GMaxIncrementalUpdateBounds)
    {
        UpdateBounds.clear();
        UpdateBounds.push_back(ClipmapBounds);
        *bForceFullUpdate = true;
    }

    if (!GSmartDebugIncrementUpdate)
    {
        PrimitiveModifiedBounds.clear();
    }
    Clipmap.UpdateBounds = UpdateBounds;
    Clipmap.LastPartialUpdateOriginInTiles = UpdateTileCenter;
}


void SmartVoxelRenderer::AssembleDebugVisualizeVoxels(const GameContext& gameContext, RenderingExecutionDescriptor* RED, ComputeShaderR* shader, REDTextureView* outputRT, REDTexture* depthView)
{
    if (mPassSettings.mDebugShowVoxelType < 3)
    {
        auto SmartVisualizeVoxelsShader = shader;

        UInt2 viewSize(depthView->mDesc.Width, depthView->mDesc.Height);

        REDTextureView* depthSRV = RED->AllocateTextureView(depthView,
                                                            NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                               depthView->mDesc.Format,
                                                                               NGITextureType::Texture2D,
                                                                               {
                                                                                   NGITextureAspect::Depth,
                                                                                   0,
                                                                                   1,
                                                                                   0,
                                                                                   1,
                                                                               }});

        auto* pass = RED->AllocatePass("DebugShowVoxels");
        mFGCommon.SetREDProperty(pass);
        pass->SetProperty("AlbedoTexture", mVoxelLightingState.AlbedoTextureViewRead);
        pass->SetProperty("NormalTexture", mVoxelLightingState.NormalTextureViewRead);
        pass->SetProperty("LightingTexture", mVoxelLightingState.GetReadLightingTexture());
        pass->SetProperty("VISUALIZE_VOXEL_TYPE", mPassSettings.mDebugShowVoxelType);

        bool visualizeAllClipmapLevels = mPassSettings.mDebugShowClipmap < 0 || mPassSettings.mDebugShowClipmap >= SmartGI::GetNumSmartVoxelClipmaps(GSmartSceneViewDistance);
        pass->SetProperty("VISUALIZE_ALL_CLIPMAP_LEVELS", visualizeAllClipmapLevels);

        pass->SetProperty("DummyTexture", GetVoxelLightingState().mVoxelizeDummy);
        pass->SetProperty("SceneDepthTexture", depthSRV);
        pass->SetProperty("RWSceneColor", outputRT);
        auto clipmapIndex = mPassSettings.mDebugShowClipmap;

        UpdateContextVoxelCommon(RED, pass, mTracingInputs, clipmapIndex, false);

        const UInt3 groupCount = ComputeShaderUtils::GetGroupCount(SmartVisualizeVoxelsShader, "VisualizeSmartVoxelsCS", UInt3(viewSize.x, viewSize.y, 1));
        pass->Dispatch(SmartVisualizeVoxelsShader, "VisualizeSmartVoxelsCS", groupCount);
    }
}

void SmartVoxelRenderer::RenderVisualizeScene(const GameContext& gameContext, RenderingExecutionDescriptor* RED,
                                              REDTextureView* outputRT, REDTexture* depthView)
{
    if (mPassSettings.mDebugShowVoxelType >= 3 && mPassSettings.mDebugShowVoxelType <= 6)
    {
        auto DebugShowVoxelType = mPassSettings.mDebugShowVoxelType - 3;

        auto viewWidth = outputRT->GetWidth();
        auto viewHeight = outputRT->GetHeight();

        auto* colorAttachmentRT = RED->AllocateTextureView(outputRT->mTexture,
                                                           NGITextureViewDesc{NGITextureUsage::RenderTarget | NGITextureUsage::CopySrc,
                                                                              outputRT->mDesc.Format,
                                                                              NGITextureType::Texture2D,
                                                                              {
                                                                                  NGITextureAspect::Color,
                                                                                  0,
                                                                                  1,
                                                                                  0,
                                                                                  1,
                                                                              }});
        REDColorTargetDesc colorTargetDesc{
            colorAttachmentRT,
            NGILoadOp::Clear,
            NGIStoreOp::Store,
            NGIClearValue{{0, 0, 0, 0}},
        };

        NGITextureViewDesc viewDesc{
            NGITextureUsage::DepthStencil | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst,
            GraphicsFormat::D24_UNorm_S8_UInt,
            NGITextureType::Texture2D,
            {
                NGITextureAspect::Depth | NGITextureAspect::Stencil,
                0,
                1,
                0,
                1,
            },
        };
        REDTextureView* depthStencilView = RED->AllocateTextureView(depthView, viewDesc);

        NGIClearValue clearValue{{0, 0, 0, 0}};
        bool useReverseZ = true;
        clearValue.depthStencil = {useReverseZ ? 0.f : 1.f, 0};
        REDDepthStencilTargetDesc depthStencilTargetDesc{
            depthStencilView,
            NGILoadOp::Clear,
            NGIStoreOp::Store,
            NGILoadOp::Clear,
            NGIStoreOp::Store,
            clearValue,
        };

        NGIViewport viewport{0, 0, static_cast<float>(viewWidth), static_cast<float>(viewHeight), 0, 1};
        NGIScissor scissor{0, 0, viewWidth, viewHeight};

        PrimitiveData cubePrim;
        PrimitiveGenerator::GenerateCube(&cubePrim);

        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        MaterialR* material = mPassSettings.SmartVisualizeSceneMtlR;
        auto mtlState = material->GetMaterialRenderState("SmartVisualizeScenePass");

        InputLayoutDesc inputLayoutDesc{};
        VertexStreamLayout streamLayout{};
        streamLayout.AddVertexChannelLayout({VertexChannel::Position0, VertexFormat::Float4, 0});
        streamLayout.AddVertexChannelLayout({VertexChannel::Normal0, VertexFormat::Float4, sizeof(float) * 4});
        inputLayoutDesc.AddVertexStreamLayout(streamLayout);

        SInt32 clipmapIndex = mPassSettings.mDebugShowClipmap;
        const SInt32 NumInstancesPerClipmap = mTracingInputs.VoxelGridResolution.x * mTracingInputs.VoxelGridResolution.y * mTracingInputs.VoxelGridResolution.z;

        RED->BeginRenderPass("VisualizeVoxelScene", 1, &colorTargetDesc, &depthStencilTargetDesc);
        {
            auto colorTargetIndex = NGIRenderPassTargetIndex::Target0;
            auto* subPass = RED->AllocateSubRenderPass("VisualizeVoxelScene", 0, nullptr, 1, &colorTargetIndex, REDPassFlagBit::NeedDepth);

            Float4x4 worldMat = Float4x4::CreateScale(100, 100, 100);
            subPass->SetProperty(BuiltInProperty::ce_World, worldMat);

            UpdateContextVoxelCommon(RED, subPass, mTracingInputs, clipmapIndex, false);
            if (DebugShowVoxelType == 0)
            {
                subPass->SetProperty(NAME_ID("VisualizeTexture"), mVoxelLightingState.AlbedoTextureViewRead);
            }
            else if (DebugShowVoxelType == 1)
            {
                subPass->SetProperty(NAME_ID("VisualizeTexture"), mVoxelLightingState.NormalTextureViewRead);
            }
            else if (DebugShowVoxelType == 2)
            {
                subPass->SetProperty(NAME_ID("IS_TEXTURE_3D_FLOAT"), true);
                subPass->SetProperty(NAME_ID("VisualizeTextureFloat"), mVoxelLightingState.GetReadLightingTexture());
            }
            else if (DebugShowVoxelType == 3)
            {
                auto textureView = RED->AllocateTextureView(mVoxelLighting->mTexture,
                                                            NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                               mVoxelLighting->mDesc.Format,
                                                                               NGITextureType::Texture3D,
                                                                               {
                                                                                   NGITextureAspect::Color,
                                                                                   0,
                                                                                   1,
                                                                                   0,
                                                                                   1,
                                                                               }});
                subPass->SetProperty(NAME_ID("VisualizeTextureFloat"), textureView);
                subPass->SetProperty(NAME_ID("IS_TEXTURE_3D_FLOAT"), true);
            }
            subPass->SetProperty(NAME_ID("NormalTexture"), mVoxelLightingState.NormalTextureViewRead);
            subPass->SetProperty(NAME_ID("OpacityTexture"), mVoxelLightingState.OpacityTextureViewRead);
            subPass->SetProperty(NAME_ID("OpacityCompactTexture"), mVoxelLightingState.OpacityCompactTextureViewRead);
            subPass->SetProperty(NAME_ID("ENABLE_VOXEL_OPACITY_TEX"), mPassSettings.mEnableVoxelOpacityTex);
            subPass->SetProperty(NAME_ID("ENABLE_VOXEL_OPACITY_COMPACT_TEX"), mPassSettings.mEnableVoxelOpacityCompactTex);

            subPass->Execute([=](REDPass* pass, NGIBundleCommandList* cmdList) mutable {
                cmdList->SetViewports(1, &viewport);
                cmdList->SetScissors(1, &scissor);

                auto* renderPrimitives = rendererSystem->GetRenderPrimitives();
                NGIBuffer* vbs[]{renderPrimitives->GetUnitCube()};
                NGIBuffer* ibs{renderPrimitives->GetUnitCubeIndex()};
                cmdList->SetVertexBuffers(1, vbs, nullptr);
                cmdList->SetIndexBuffer(ibs, 0, GraphicsFormat::R16_UInt);

                const void* shaderConst = nullptr;
                if (mtlState.mProgram->ShaderConstantLayout && mtlState.mProgram->ShaderConstantLayout->ByteSize > 0)
                {
                    shaderConst = mtlState.mShaderConstants;
                    auto pasOverride = pass->GetContext().HasShaderConstant(*mtlState.mProgram->ShaderConstantLayout);
                    if (pasOverride)
                    {
                        auto* frameAllocator = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameAllocator();
                        void* tmpShaderConsts = frameAllocator->Allocate<UInt8>(mtlState.mProgram->ShaderConstantLayout->ByteSize, FrameStage::FRAME_STAGE_RENDER);
                        pass->GetContext().FillBuffer(*mtlState.mProgram->ShaderConstantLayout, tmpShaderConsts);
                        shaderConst = tmpShaderConsts;
                    }
                }

                NGIGraphicsPipelineStateDesc pipelineDesc{
                    pass->GetRenderPass(),
                    pass->GetSubpass(),

                    {mtlState.mProgram->GUID.low, mtlState.mProgram->GUID.high},
                    &mtlState.mProgram->GraphicsProgramDesc,
                    mtlState.mProgram->PipelineLayout,

                    inputLayoutDesc.GetHash().GetHash(),
                    &inputLayoutDesc,

                    PrimitiveTopology::TriangleList,
                    *mtlState.mRaterizationState,
                    *mtlState.mBlendState,
                    *mtlState.mDepthStencilState,

                    mtlState.mProgram->ShaderConstantLayout ? mtlState.mProgram->ShaderConstantLayout->ByteSize : 0,
                    shaderConst,
                };

                auto* pipeline = rendererSystem->GetGraphicsPipelineStatePool()->AllocateGraphicsPipelineState(pipelineDesc);
                cmdList->SetGraphicsPipelineState(pipeline);

                auto* pg = pass->GetContext().GetPassResourceGroup(mtlState.mProgram->ResourceGroupLayouts[ShaderParamGroup_Pass], mtlState.mProgram->PipelineLayout, ShaderParamGroup_Pass);
                if (pg)
                {
                    cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Pass, pg);
                }

                auto* mg = mtlState.GetResourceBinding();
                if (mg)
                {
                    cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Material, mg);
                }
                cmdList->DrawIndexedInstanced(renderPrimitives->GetUnitCubeIndexCount(), NumInstancesPerClipmap, 0, 0, 0);
            });
        }
        RED->EndRenderPass();
    }
}

void SmartVoxelRenderer::VoxelizeScene(const GameContext& gameContext, RenderingExecutionDescriptor* RED, SmartTracingInputs& TracingInputs, SInt32 clipmapIndex, bool bForceFullUpdate)
{
    const auto& UpdateBounds = mVoxelLightingClipmapState[clipmapIndex].UpdateBounds;
    if (UpdateBounds.size() == 0)
    {
        return;
    }
    if (bForceFullUpdate)
    {
        ClearVoxelizeTexture(RED, TracingInputs, clipmapIndex);
    }
    else
    {
        if (!mPassSettings.mVoxelsKeepNotClear)
        {
            ClearVoxelizeTextureGridTiles(RED, TracingInputs, clipmapIndex);
        }
    }

    auto* renderWorld = gameContext.mRenderWorld;

    const Int3 ClipmapResolution = TracingInputs.VoxelGridResolution;
    SmartVoxelLightingClipmapState& Clipmap = mVoxelLightingClipmapState[clipmapIndex];
    const BoundingBox ClipmapBounds(Clipmap.Center, Clipmap.Extent);
    Float3 max, min;
    ClipmapBounds.GetMinMax(&min, &max);
    const Float3 UpdateGridWorldSize = (max - min) / Int3ToFloat3(ClipmapResolution);

    REDColorTargetDesc colorTargetDesc{
        mVoxelLightingState.mVoxelizeDummy,
        NGILoadOp::Clear, //TODO(timllpan): DontCare
        NGIStoreOp::Store,
        NGIClearValue{0, 0, 0, 0},
    };

    mClipmapCullingCameras[clipmapIndex].reset(
        new CustomCullingCamera(gameContext.mRenderCamera,CullingType::Voxel,
                                [=](const BoundingBox& box) {
                                    for (const auto& modifiedBound : UpdateBounds)
                                    {
                                        if (modifiedBound.Intersects(GetClipmapTiledBounds(ClipmapBounds, UpdateGridWorldSize, box)))
                                        {
                                            return false;
                                        }
                                    }
                                    return true;
                                }));

    for (auto directionIdx = 0; directionIdx < 3; directionIdx++)
    {
        //cross::REDDrawUnitProxy* proxy = nullptr;
        REDPass* foliagePass = nullptr;
        REDPass* cullingPass = nullptr;

        {
            std::vector<Float4> UpdateBoundsData;
            UpdateBoundsData.resize(UpdateBounds.size() * 2);
            for (int index = 0; index < UpdateBounds.size(); index++)
            {
                const auto& bound = UpdateBounds[index];
                Float3 out;
                bound.GetCenter(&out);
                UpdateBoundsData[index] = Float4(out, 0.0f);
                bound.GetExtent(&out);
                UpdateBoundsData[index + UpdateBounds.size()] = Float4(out, 0.0f);
            }
            //auto* rp = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
            //if (rp->GetSetting()->mFoliageGpuDrivenSettings.enable && rp->GetSetting()->EnableFoliageDrawing)
            //{
            //    //rp->mFoliageGpuDriven.Execute(rp->mGameContext, "VoxelVegetationCulling", NameID("VoxelizePass"), mClipmapCullingCameras[clipmapIndex].get(), proxy, rp->mGPassDepthPyramidReprojectionView, foliagePass);
            //    foliagePass->SetProperty("isClipmapCulling", true);
            //    foliagePass->SetProperty("aabbCount", static_cast<int>(UpdateBounds.size()));
            //    foliagePass->SetProperty("aabbCenters", &UpdateBoundsData[0], sizeof(Float4) * UpdateBounds.size());
            //    foliagePass->SetProperty("aabbExtents", &UpdateBoundsData[UpdateBounds.size()], sizeof(Float4) * UpdateBounds.size());
            //}
        }

        //RED->AllocatePass("ClearSmartVoxelizeDummpy", true)->ClearTexture(mVoxelLightingState.mVoxelizeDummy, NGIClearValue{0, 0, 0, 0});
        RED->BeginRenderPass("VoxelizeScenePass_Axis_" + std::to_string(directionIdx) + "_clipmap_" + std::to_string(clipmapIndex), 1, &colorTargetDesc, nullptr);
        {
            auto colorTargetIndex = NGIRenderPassTargetIndex::Target0;
            auto* voxelizePass = RED->AllocateSubRenderPass("VoxelizeScenePass", 0, nullptr, 1, &colorTargetIndex, REDPassFlagBit::StencilReadOnly);
            dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline)->UpdateVTContext(voxelizePass);

            auto* numCullBuffer = RED->AllocateBuffer("DummyGridCullNumBuffer", NGIBufferDesc{sizeof(UInt32), NGIBufferUsage::RWTexelBuffer});
            auto* numCulledLightsGrid = RED->AllocateBufferView(numCullBuffer, NGIBufferViewDesc{NGIBufferUsage::RWTexelBuffer, 0, sizeof(UInt32), GraphicsFormat::R32_UInt, sizeof(UInt32)});
            auto* cullDataBuffer = RED->AllocateBuffer("DummyGridCullDataBuffer", NGIBufferDesc{sizeof(UInt32), NGIBufferUsage::RWTexelBuffer});
            auto* culledLightDataGrid = RED->AllocateBufferView(cullDataBuffer, NGIBufferViewDesc{NGIBufferUsage::RWTexelBuffer, 0, sizeof(UInt32), GraphicsFormat::R32_UInt, sizeof(UInt32)});
            {
                voxelizePass->SetProperty("_NumCulledLightsGrid", numCulledLightsGrid, NGIResourceState::PixelShaderUnorderedAccess);
                voxelizePass->SetProperty("_CulledLightsDataGrid", culledLightDataGrid, NGIResourceState::PixelShaderUnorderedAccess);
            }
            
            RendererVoxelizeHelper::InitVoxelPassRenderContext(&voxelizePass->GetContext(),
                                                               false,
                                                               mVoxelLightingState.AlbedoTextureView,
                                                               mVoxelLightingState.NormalTextureView,
                                                               mVoxelLightingState.EmissiveTextureView,
                                                               mVoxelLightingState.OpacityTextureView,
                                                               directionIdx,
                                                               clipmapIndex,
                                                               mRenderPipeline->GetRenderCamera()->GetTilePosition(),
                                                               TracingInputs.ClipmapWorldToUVScale[clipmapIndex],
                                                               TracingInputs.ClipmapWorldToUVBias[clipmapIndex],
                                                               TracingInputs.ClipmapWorldCenter[clipmapIndex],
                                                               TracingInputs.ClipmapWorldExtent[clipmapIndex],
                                                               ClipmapResolution,
                                                               mPassSettings.mDiffuseBoost,
                                                               mRenderPipeline->GetRenderCamera());

            {
                auto* cullingResult = RED->Cull(REDCullingDesc{renderWorld, mClipmapCullingCameras[clipmapIndex].get()});
                // Note(scolu): Cull by if material has VoxelizePass here
                auto* drawUnitList = cullingResult->GenerateDrawUnitList(REDDrawUnitsDesc{NAME_ID("VoxelizePass"), 0, gRenderGroupUI - 1});
                voxelizePass->OnCulling([=](REDPass* pass) {
                    if (drawUnitList->GetDefaultObjectIndexBufferView())
                        pass->SetProperty(NAME_ID("_ObjectIndexBuffer"), drawUnitList->GetDefaultObjectIndexBufferView());
                });
                voxelizePass->SetProperty(BuiltInProperty::CE_INSTANCING, true);

                REDRenderDrawUnitsDesc renderDrawUnitDesc{
                    drawUnitList,
                    std::nullopt,
                    std::nullopt,
                    std::nullopt,
                    std::nullopt,
                    std::nullopt,
                    std::nullopt,
                    false,
                };
                voxelizePass->RenderDrawUnits(renderDrawUnitDesc);
            }
        }
        RED->EndRenderPass();
    }
}

void SmartVoxelRenderer::CompactVisVoxels(const GameContext& gameContext, RenderingExecutionDescriptor* RED, SmartTracingInputs& TracingInputs, int32_t ClipmapIndex)
{
    ComputeShaderR* voxelizerCompactComputeShader = mPassSettings.SmartVoxelizerCompactComputeShaderR;
    {
        auto* pass = RED->AllocatePass("ClearVisBuffer Clipmap" + std::to_string(ClipmapIndex));
        mFGCommon.SetREDProperty(pass);
        pass->SetProperty(NAME_ID("ClipmapIndex"), ClipmapIndex);
        pass->SetProperty(NAME_ID("RWVoxelVisBufferAllocator"), mVoxelLightingState.VoxelVisBufferAllocatorView);
        pass->Dispatch(voxelizerCompactComputeShader, "ClearVoxelizeVisBufferCS", 1, 1, 1);
    }

    {
        Int3 threadCount(TracingInputs.VoxelGridResolution.x, TracingInputs.VoxelGridResolution.y, TracingInputs.VoxelGridResolution.z * GNumVoxelDirections);
        auto* pass = RED->AllocatePass("CompactVisVoxel Clipmap" + std::to_string(ClipmapIndex));
        mFGCommon.SetREDProperty(pass);
        pass->SetProperty(NAME_ID("ClipmapResolution"), threadCount);
        pass->SetProperty(NAME_ID("ClipmapIndex"), ClipmapIndex);
        pass->SetProperty(NAME_ID("AlbedoTexture"), mVoxelLightingState.AlbedoTextureViewRead);
        pass->SetProperty(NAME_ID("RWLightingTexture"), mVoxelLightingState.GetWriteLightingTexture());
        pass->SetProperty(NAME_ID("RWVoxelVisBuffer"), mVoxelLightingState.VoxelVisBuffersView[ClipmapIndex]);
        pass->SetProperty(NAME_ID("RWVoxelVisBufferAllocator"), mVoxelLightingState.VoxelVisBufferAllocatorView);
        pass->SetProperty(NAME_ID("RWOpacityCompactTexture"), mVoxelLightingState.OpacityCompactTextureView);
        UInt3 groupSize;
        voxelizerCompactComputeShader->GetThreadGroupSize("CompactVoxelizeSceneCS", groupSize.x, groupSize.y, groupSize.z);
        pass->Dispatch(voxelizerCompactComputeShader, "CompactVoxelizeSceneCS", (threadCount.x + groupSize.x - 1) / groupSize.x, (threadCount.y + groupSize.y - 1) / groupSize.y, (threadCount.z + groupSize.z - 1) / groupSize.z);
    }

    if (mPassSettings.mEnableVoxelOpacityTex && mPassSettings.mEnableVoxelOpacityCompactTex)
    {
        Int3 threadCount(TracingInputs.VoxelGridResolution.x / 2, TracingInputs.VoxelGridResolution.y / 2, TracingInputs.VoxelGridResolution.z / 2);
        auto* pass = RED->AllocatePass("CompactOpacityTex Clipmap" + std::to_string(ClipmapIndex));
        pass->SetProperty(NAME_ID("ClipmapResolution"), TracingInputs.VoxelGridResolution);
        pass->SetProperty(NAME_ID("ClipmapIndex"), ClipmapIndex);
        pass->SetProperty(NAME_ID("OpacityTexture"), mVoxelLightingState.OpacityTextureViewRead);
        pass->SetProperty(NAME_ID("AlbedoTexture"), mVoxelLightingState.AlbedoTextureViewRead);
        pass->SetProperty(NAME_ID("RWOpacityCompactTexture"), mVoxelLightingState.OpacityCompactTextureView);
        UInt3 groupSize;
        voxelizerCompactComputeShader->GetThreadGroupSize("CompactOpacityVoxelCS", groupSize.x, groupSize.y, groupSize.z);
        pass->Dispatch(voxelizerCompactComputeShader, "CompactOpacityVoxelCS", (threadCount.x + groupSize.x - 1) / groupSize.x, (threadCount.y + groupSize.y - 1) / groupSize.y, (threadCount.z + groupSize.z - 1) / groupSize.z);
    }
}

REDBufferView* SmartVoxelRenderer::SetupVoxelizeIndirectArgs(const GameContext& gameContext, RenderingExecutionDescriptor* RED, int32_t ClipmapIndex)
{
    ComputeShaderR* voxelizerCompactComputeShader = mPassSettings.SmartVoxelizerCompactComputeShaderR;
    auto* redBuffer = RED->AllocateBuffer("SmartGI.CompactVoxelIndirectArgs", NGIBufferDesc{sizeof(int32_t) * 2 * 3, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::IndirectBuffer});
    REDBufferView* bufferView = RED->AllocateBufferView(redBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, redBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(float)});

    auto* pass = RED->AllocatePass("SetupVoxelizeIndirectArgs Clipmap" + std::to_string(ClipmapIndex));
    mFGCommon.SetREDProperty(pass);
    pass->SetProperty("ClipmapIndex", ClipmapIndex);
    pass->SetProperty("RWVoxelizeIndirectArgs", bufferView);
    pass->SetProperty("VoxelVisBufferAllocator", mVoxelLightingState.VoxelVisBufferAllocatorView);
    pass->Dispatch(voxelizerCompactComputeShader, "SetupVoxelizeIndirectArgsCS", 1, 1, 1);

    return bufferView;
}

void SmartVoxelRenderer::CompositeLighting(const GameContext& gameContext, RenderingExecutionDescriptor* RED, const REDBufferView* voxelizeIndirectArgs, const SmartTracingInputs& TracingInputs, int32_t ClipmapIndex, bool bOverrideLighting)
{
    ComputeShaderR* SmartSceneLightingCompositeShader = mPassSettings.SmartSceneLightingCompositeShaderR;
    auto* pass = RED->AllocatePass("LightingComposite_Clipmap_" + std::to_string(ClipmapIndex));
    mFGCommon.SetREDProperty(pass);
    UpdateContextVoxelCommon(RED, pass, TracingInputs, ClipmapIndex);
    pass->SetProperty("OVERRIDE_LIGHTING", bOverrideLighting);
    pass->SetProperty("LightingTexture", mVoxelLighting);
    pass->SetProperty("RWLightingTexture", mVoxelLightingState.GetWriteLightingTexture());
    pass->DispatchIndirect(SmartSceneLightingCompositeShader, "SmartLightingCompositeCS", voxelizeIndirectArgs->mBuffer, 0);
}

void SmartVoxelRenderer::RenderRadiosity(const GameContext& gameContext, RenderingExecutionDescriptor* RED, SmartTracingInputs& TracingInputs, int32_t ClipmapIndex,
                                         REDTextureView* preSceneColorView, REDTextureView* preDepthView, REDTextureView* preDiffuseIndirect, const std::array<REDTextureView*, 4>& gBufferViews)
{
    bool coneTraceRadiosity = mPassSettings.IsVoxelRadiosityEnable(gameContext) && !mPassSettings.mInjectLightingFromPrevFrame;
    bool reprojectRadiosity = mPassSettings.mInjectLightingFromPrevFrame;

    if (!coneTraceRadiosity && !reprojectRadiosity)
    {
        return;
    }

    AllocateVoxelLightingTexture(RED, mTracingInputs.VoxelGridResolution);

    const auto* indirectArgs = SetupVoxelizeIndirectArgs(gameContext, RED, ClipmapIndex);
    ComputeShaderR* SmartSceneRadiosityShader = mPassSettings.SmartSceneRadiosityShaderR;

    if (reprojectRadiosity && preDiffuseIndirect)
    {
        int ResolutionDownsampleFactor = 4;
        auto* pass = RED->AllocatePass("InjectLightingFromPreviousFrame_Clipmap_" + std::to_string(ClipmapIndex));
        pass->SetProperty("ResolutionDownsampleFactor", ResolutionDownsampleFactor);
        pass->SetProperty("ClipmapIndex", ClipmapIndex);
        pass->SetProperty("_PrevDiffuseOutput", preDiffuseIndirect);
        pass->SetProperty("FrameIndex", mSmartViewParams.mFrameIndex);
        RenderCamera* renderCamera = gameContext.mRenderCamera;

        ComputeShaderUtils::SetReprojectPassShaderParameters(pass,
                                                             renderCamera,
                                                             std::nullopt,
                                                             preSceneColorView,
                                                             gBufferViews,
                                                             std::nullopt,
                                                             preDepthView);

        UpdateContextVoxelLightingState(RED, pass);
        UpdateContextVoxelCommon(RED, pass, TracingInputs, ClipmapIndex);


        pass->SetProperty(NAME_ID("USE_LOCAL_LIGHT_SHADOW_CACHE"), mPassSettings.mUseLocalLightShadowCache);
        pass->SetProperty(NAME_ID("USE_CACHED_LOCAL_LIGHT_SHADOW"), mPassSettings.mUseLocalLightShadowCache); // Note(scolu): Compact for LocalLightShadowCache
        pass->SetProperty(NAME_ID("RWLightingTexture"), mVoxelLighting);
        pass->SetProperty(NAME_ID("ClipmapIndex"), ClipmapIndex);
        pass->SetProperty(NAME_ID("ENABLE_FIRST_VOXEL_UNHIT"), mPassSettings.mEnableFirstVoxelUnHit);
        pass->SetProperty(NAME_ID("ce_CameraTilePosition"), mRenderPipeline->GetRenderCamera()->GetTilePosition());
        pass->SetProperty(NAME_ID("ce_View"), mRenderPipeline->GetRenderCamera()->GetViewMatrix());

        REDTextureView* DebugTex = IRenderPipeline::CreateTextureView2D("DebugTex",
                                                                        preSceneColorView->GetSize().x,
                                                                        preSceneColorView->GetSize().y,
                                                                        GraphicsFormat::R16G16B16A16_SFloat,
                                                                        NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);
        pass->SetProperty("DebugTex", DebugTex);

        UInt3 GroupCount = ComputeShaderUtils::GetGroupCount(SmartSceneRadiosityShader,
                                                             "SmartInjectLightingFromPrevFrameCS",
                                                             UInt3(gameContext.TargetScreenSize().x / ResolutionDownsampleFactor, gameContext.TargetScreenSize().y / ResolutionDownsampleFactor, 1));
        pass->Dispatch(SmartSceneRadiosityShader, "SmartInjectLightingFromPrevFrameCS", GroupCount);
    }

    if (coneTraceRadiosity)
    {
        auto* pass = RED->AllocatePass("TraceVoxels_Radiosity_Clipmap_" + std::to_string(ClipmapIndex));
        mFGCommon.SetREDProperty(pass);
        pass->SetProperty(NAME_ID("LightingTexture"), mVoxelLightingState.GetReadLightingTexture());
        pass->SetProperty(NAME_ID("RWLightingTexture"), mVoxelLighting);
        pass->SetProperty(NAME_ID("ENABLE_VOXEL_OPACITY_TEX"), mPassSettings.mEnableVoxelOpacityTex);
        pass->SetProperty(NAME_ID("ENABLE_VOXEL_OPACITY_COMPACT_TEX"), mPassSettings.mEnableVoxelOpacityCompactTex);

        pass->SetProperty(NAME_ID("StepFactor"), clamp(GDiffuseTraceStepFactor, 0.1f, 10.0f));
        pass->SetProperty(NAME_ID("MinTraceDistance"), clamp(mPassSettings.mMinVoxelTraceDistance, 0.01f, 1000.0f));
        pass->SetProperty(NAME_ID("MaxTraceDistance"), mPassSettings.mMaxVoxelTraceDistance);
        pass->SetProperty(NAME_ID("ENABLE_FIRST_VOXEL_UNHIT"), mPassSettings.mEnableFirstVoxelUnHit);
        pass->SetProperty(NAME_ID("SurfaceBias"), clamp(mPassSettings.mVoxelTraceSurfaceBias, 0.01f, 1.0f));
        pass->SetProperty(NAME_ID("DecayRate"), mPassSettings.GetRadiosityDecayRate(gameContext));

        UInt32 RadiosityRaysPerVoxel2D = static_cast<UInt32>(ceil(sqrt(mPassSettings.mRadiosityRaysPerVoxel)));
        pass->SetProperty("RaysPerVoxel2D", RadiosityRaysPerVoxel2D);

        UpdateContextVoxelLightingState(RED, pass);
        UpdateContextVoxelCommon(RED, pass, TracingInputs, ClipmapIndex);
        pass->DispatchIndirect(SmartSceneRadiosityShader, "SmartRadiosityCS", indirectArgs->mBuffer, 3 * sizeof(UInt32));
    }

    CompositeLighting(gameContext, RED, indirectArgs, TracingInputs, ClipmapIndex, true);
}

void SmartVoxelRenderer::RenderDirectLighting(const GameContext& gameContext, RenderingExecutionDescriptor* RED, SmartTracingInputs& TracingInputs, const ShadowProperties* shadowProperties, int32_t ClipmapIndex)
{
    const auto& indirectArgs = SetupVoxelizeIndirectArgs(gameContext, RED, ClipmapIndex);

    AllocateVoxelLightingTexture(RED, mTracingInputs.VoxelGridResolution);

    // Emissive Lighting
    {
        ComputeShaderR* voxelizerCompactComputeShader = mPassSettings.SmartVoxelizerDirectLightingComputeShaderR;
        auto* pass = RED->AllocatePass("Emissive Lighting");
        mFGCommon.SetREDProperty(pass);
        UpdateContextVoxelLightingState(RED, pass);
        UpdateContextVoxelCommon(RED, pass, TracingInputs, ClipmapIndex);
        pass->SetProperty("RWLightingTexture", mVoxelLighting);
        pass->DispatchIndirect(voxelizerCompactComputeShader, "SmartEmissiveLightingCS", indirectArgs->mBuffer, 0);
    }

    const auto& lightList = mRenderPipeline->GetLightList();
    // reuse LightCullingSystem's ce_lights in GPU, just dispatch once HERE! including directional/point/spot
    // too many local lights with too many light info gathering consume too many CPU resources
    int lightCnt = static_cast<int>(lightList.size());
    if (lightCnt <= 0)
    {
        CompositeLighting(gameContext, RED, indirectArgs, TracingInputs, ClipmapIndex, (mPassSettings.IsVoxelRadiosityEnable(gameContext) || mPassSettings.mInjectLightingFromPrevFrame) ? false : true);
        return;
    }

    if (mPassSettings.mEnableVoxelLightCulling)
    {
        uint32_t ChunkSize1D = mPassSettings.GetVoxelChunkSize();
        UInt3 ChunkSize3D = UInt3(ChunkSize1D, ChunkSize1D, ChunkSize1D);
        UInt3 ChunkCount3D = ComputeShaderUtils::GetGroupCount(UInt3(TracingInputs.VoxelGridResolution),
                                                               ChunkSize3D);
        const uint32_t ChunkCount = ChunkCount3D.x * ChunkCount3D.y * ChunkCount3D.z;

        struct LightIndex
        {
            uint32_t StartIndex;
            uint32_t EndIndex;
        };
        REDBuffer* ChunkLightIndexStartEnd = RED->AllocateBuffer(
            "ChunkLightIndexStartEnd",
            NGIBufferDesc(sizeof(LightIndex) * ChunkCount, NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer)
            );
        REDBufferView* ChunkLightIndexStartEndView = RED->AllocateBufferView(
            ChunkLightIndexStartEnd,
            NGIBufferViewDesc(NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer,
                              0,
                              ChunkLightIndexStartEnd->mDesc.Size,
                              GraphicsFormat::Unknown,
                              sizeof(LightIndex)
                ));
        REDPass* clearChunkLightIndexStartEndPass = RED->AllocatePass("ClearChunkLightIndex");
        clearChunkLightIndexStartEndPass->SetProperty(NAME_ID("RWChunkLightIndexStartEnd"), ChunkLightIndexStartEndView);
        clearChunkLightIndexStartEndPass->SetProperty(NAME_ID("ChunkCount"), ChunkCount);
        const UInt3 clearChunkLightIndexStartEndGroupCount = ComputeShaderUtils::GetGroupCount(
            mPassSettings.SmartVoxelLightCullingComputeShaderR,
            "ClearChunkLightIndexCS",
            UInt3(ChunkCount, 1, 1)
            );
        clearChunkLightIndexStartEndPass->Dispatch(mPassSettings.SmartVoxelLightCullingComputeShaderR,
                                                   "ClearChunkLightIndexCS",
                                                   clearChunkLightIndexStartEndGroupCount);

        REDPass* lightCullingPass = RED->AllocatePass("Voxel Light Culling");
        UpdateContextVoxelCommon(RED, lightCullingPass, TracingInputs, ClipmapIndex);
        lightCullingPass->SetProperty(NAME_ID("LightCnt"), lightCnt);
        lightCullingPass->SetProperty(NAME_ID("ClipmapIndex"), ClipmapIndex);
        lightCullingPass->SetProperty(NAME_ID("ChunkCount3D"), ChunkCount3D);
        lightCullingPass->SetProperty(NAME_ID("ChunkCount"), ChunkCount);
        lightCullingPass->SetProperty(NAME_ID("ChunkSize3D"), ChunkSize3D);
        lightCullingPass->SetProperty(NAME_ID("ce_Lights"), mRenderPipeline->GetLightsInfoBufferView());

        lightCullingPass->SetProperty(NAME_ID("RWChunkLightIndexStartEnd"), ChunkLightIndexStartEndView);

        const UInt3 lightCullingGroupCount = ComputeShaderUtils::GetGroupCount(
            mPassSettings.SmartVoxelLightCullingComputeShaderR,
            "VoxelLightCullingCS",
            ChunkCount3D);
        lightCullingPass->Dispatch(mPassSettings.SmartVoxelLightCullingComputeShaderR, "VoxelLightCullingCS", lightCullingGroupCount);

        REDPass* culledLightInjectionPass = RED->AllocatePass("Culled Direct Lighting Injection");

        mFGCommon.SetREDProperty(culledLightInjectionPass);
        UpdateContextVoxelLightingState(RED, culledLightInjectionPass);
        UpdateContextVoxelCommon(RED, culledLightInjectionPass, TracingInputs, ClipmapIndex);

        culledLightInjectionPass->SetProperty(NAME_ID("USE_LOCAL_LIGHT_SHADOW_CACHE"), mPassSettings.mUseLocalLightShadowCache);
        culledLightInjectionPass->SetProperty(NAME_ID("USE_CACHED_LOCAL_LIGHT_SHADOW"), mPassSettings.mUseLocalLightShadowCache); // Note(scolu): Compact for LocalLightShadowCache
        culledLightInjectionPass->SetProperty(NAME_ID("RWLightingTexture"), mVoxelLighting);
        culledLightInjectionPass->SetProperty(NAME_ID("ClipmapIndex"), ClipmapIndex);
        culledLightInjectionPass->SetProperty(NAME_ID("MinTraceDistance"), clamp(mPassSettings.mMinVoxelTraceDistance, 0.01f, 1000.0f));
        culledLightInjectionPass->SetProperty(NAME_ID("MaxTraceDistance"), mPassSettings.mMaxVoxelTraceDistance);
        culledLightInjectionPass->SetProperty(NAME_ID("ENABLE_VOXEL_OPACITY_TEX"), mPassSettings.mEnableVoxelOpacityTex);
        culledLightInjectionPass->SetProperty(NAME_ID("ENABLE_VOXEL_OPACITY_COMPACT_TEX"), mPassSettings.mEnableVoxelOpacityCompactTex);
        culledLightInjectionPass->SetProperty(NAME_ID("ENABLE_FIRST_VOXEL_UNHIT"), mPassSettings.mEnableFirstVoxelUnHit);
        culledLightInjectionPass->SetProperty(NAME_ID("ce_CameraTilePosition"), mRenderPipeline->GetRenderCamera()->GetTilePosition());
        culledLightInjectionPass->SetProperty(NAME_ID("ce_View"), mRenderPipeline->GetRenderCamera()->GetViewMatrix());
        if (mPassSettings.mUseLocalLightShadowCache)
        {
            gameContext.mRenderPipeline->UpdateShadowContext(culledLightInjectionPass, shadowProperties);
        }

        culledLightInjectionPass->SetProperty(NAME_ID("ChunkCount3D"), ChunkCount3D);
        culledLightInjectionPass->SetProperty(NAME_ID("ChunkCount"), ChunkCount);
        culledLightInjectionPass->SetProperty(NAME_ID("ChunkLightIndexStartEnd"), ChunkLightIndexStartEndView);
        culledLightInjectionPass->SetProperty(NAME_ID("ChunkSize3D"), ChunkSize3D);

        culledLightInjectionPass->DispatchIndirect(mPassSettings.SmartVoxelLightCullingComputeShaderR,
                                                   "CulledDirectLightingInjectionCS",
                                                   indirectArgs->mBuffer,
                                                   0);
    }
    else
    {
        ComputeShaderR* voxelizerDirectLightingCS = mPassSettings.SmartVoxelizerDirectLightingComputeShaderR;
        auto* pass = RED->AllocatePass("Direct Lighting Injection");

        mFGCommon.SetREDProperty(pass);
        UpdateContextVoxelLightingState(RED, pass);
        UpdateContextVoxelCommon(RED, pass, TracingInputs, ClipmapIndex);

        pass->SetProperty(NAME_ID("ce_Lights"), mRenderPipeline->GetLightsInfoBufferView());
        pass->SetProperty(NAME_ID("USE_LOCAL_LIGHT_SHADOW_CACHE"), mPassSettings.mUseLocalLightShadowCache);
        pass->SetProperty(NAME_ID("USE_CACHED_LOCAL_LIGHT_SHADOW"), mPassSettings.mUseLocalLightShadowCache); // Note(scolu): Compact for LocalLightShadowCache
        pass->SetProperty(NAME_ID("RWLightingTexture"), mVoxelLighting);
        pass->SetProperty(NAME_ID("ClipmapIndex"), ClipmapIndex);
        pass->SetProperty(NAME_ID("lightCnt"), lightCnt); // LightCullingSystem's light count
        pass->SetProperty(NAME_ID("MinTraceDistance"), clamp(mPassSettings.mMinVoxelTraceDistance, 0.01f, 1000.0f));
        pass->SetProperty(NAME_ID("MaxTraceDistance"), mPassSettings.mMaxVoxelTraceDistance);
        pass->SetProperty(NAME_ID("ENABLE_VOXEL_OPACITY_TEX"), mPassSettings.mEnableVoxelOpacityTex);
        pass->SetProperty(NAME_ID("ENABLE_VOXEL_OPACITY_COMPACT_TEX"), mPassSettings.mEnableVoxelOpacityCompactTex);
        pass->SetProperty(NAME_ID("ENABLE_FIRST_VOXEL_UNHIT"), mPassSettings.mEnableFirstVoxelUnHit);
        pass->SetProperty(NAME_ID("ce_CameraTilePosition"), mRenderPipeline->GetRenderCamera()->GetTilePosition());
        pass->SetProperty(NAME_ID("ce_View"), mRenderPipeline->GetRenderCamera()->GetViewMatrix());
        if (mPassSettings.mUseLocalLightShadowCache)
        {
            gameContext.mRenderPipeline->UpdateShadowContext(pass, shadowProperties);
        }

        pass->DispatchIndirect(voxelizerDirectLightingCS, "SmartDirectLightingCS", indirectArgs->mBuffer, 0);
    }

    CompositeLighting(gameContext, RED, indirectArgs, TracingInputs, ClipmapIndex, mPassSettings.IsVoxelRadiosityEnable(gameContext) || mPassSettings.mInjectLightingFromPrevFrame ? false : true);
}

void SmartVoxelRenderer::AllocateVoxelLightingTexture(RenderingExecutionDescriptor* RED, const Int3& mVoxelGridResolution)
{
    auto textureSize = mVoxelGridResolution;
    textureSize.z *= GNumVoxelDirections;
    mVoxelLighting = IRenderPipeline::CreateTextureView3D("Smart.VoxelLighting",
                                                          textureSize.x,
                                                          textureSize.y,
                                                          textureSize.z,
                                                          GraphicsFormat::R16G16B16A16_SFloat,
                                                          NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);

    if (mPassSettings.mClearTextureUseCS)
    {
        auto* ClearReplacementShader = mPassSettings.ClearReplacementShaderR;
        auto* pass = RED->AllocatePass("ClearVoxelLighting");
        mFGCommon.SetREDProperty(pass);

        UInt3 threadCount(static_cast<UInt32>(textureSize.x), static_cast<UInt32>(textureSize.y), static_cast<UInt32>(textureSize.z));

        pass->SetProperty("_ClearValue", Float4(0, 0, 0, 0));
        pass->SetProperty("_TextureSize", textureSize);
        pass->SetProperty("ClearResource", mVoxelLighting);

        UInt3 groupSize;
        ClearReplacementShader->GetThreadGroupSize("ClearCS", groupSize.x, groupSize.y, groupSize.z);
        pass->Dispatch(ClearReplacementShader, "ClearCS", (threadCount.x + groupSize.x - 1) / groupSize.x, (threadCount.y + groupSize.y - 1) / groupSize.y, (threadCount.z + groupSize.z - 1) / groupSize.z);
    }
    else
    {
        RED->AllocatePass("Clear VoxelLighting", true)->ClearTexture(mVoxelLighting, {0, 0, 0, 0});
    }
}

void SmartVoxelRenderer::InitializeVoxelLightingState(RenderingExecutionDescriptor* RED, ViewModeVisualizeType visualize)
{
    mVoxelLightingState.Init();
    mVisualize = visualize;
    //GSmartSceneFirstClipmapWorldExtent = mPassSettings.mSmartSceneFirstClipmapWorldExtent;
    auto voxelizeWidth = static_cast<uint32_t>(SmartGI::GetClipmapResolution().x * std::max(mPassSettings.mVoxelizeNewSizeScale, 1.0f));
    auto voxelizeHeight = static_cast<uint32_t>(SmartGI::GetClipmapResolution().y * std::max(mPassSettings.mVoxelizeNewSizeScale, 1.0f));
    if (!mPassSettings.mVoxelizeNewSize)
    {
        voxelizeWidth = static_cast<uint32_t>(mSmartViewParams.mSizeAndInvSize.x);
        voxelizeHeight = static_cast<uint32_t>(mSmartViewParams.mSizeAndInvSize.y);
    }
    mVoxelLightingState.mVoxelizeDummy = IRenderPipeline::CreateTextureView2D("Smart.voxelizeDummy",
                                                                              voxelizeWidth,
                                                                              voxelizeHeight,
                                                                              GraphicsFormat::R8G8B8A8_UNorm,
                                                                              NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);

    UInt2 traceViewSize = mFGCommon.GetScreenProbeOctahedronViewSize();
    UInt32 traceRaysNum = traceViewSize.x * traceViewSize.y;

    {
        auto* redBuffer = RED->AllocateBuffer("SmartGI.CompactedTraceTexelAllocator", NGIBufferDesc{sizeof(UInt32), NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst});
        mCompactedTraceParam.mCompactedTraceTexelAllocatorRW = RED->AllocateBufferView(redBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, redBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(UInt32)});
        mCompactedTraceParam.mCompactedTraceTexelAllocator = RED->AllocateBufferView(redBuffer, NGIBufferViewDesc{NGIBufferUsage::StructuredBuffer, 0, redBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(UInt32)});
        RED->AllocatePass("Clear Smart CompactedTraceTexelAllocator")->ClearBuffer(mCompactedTraceParam.mCompactedTraceTexelAllocator, 0);
    }

    if (true)
    {
        auto uint2Size = sizeof(UInt2);
        auto* redBuffer = RED->AllocateBuffer("SmartGI.CompactedTraceTexelData", NGIBufferDesc{uint2Size * traceRaysNum, NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst});
        mCompactedTraceParam.mCompactedTraceTexelDataRW = RED->AllocateBufferView(redBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, redBuffer->mDesc.Size, GraphicsFormat::Unknown, uint2Size});
        mCompactedTraceParam.mCompactedTraceTexelData = RED->AllocateBufferView(redBuffer, NGIBufferViewDesc{NGIBufferUsage::StructuredBuffer, 0, redBuffer->mDesc.Size, GraphicsFormat::Unknown, uint2Size});
        RED->AllocatePass("Clear Smart mCompactedTraceTexelData")->ClearBuffer(mCompactedTraceParam.mCompactedTraceTexelData, 0);
    }

    if (false)
    {
        mCompactedTraceParam.mCompactedTraceTexelDataTex =
            IRenderPipeline::CreateTextureView2D("SmartGI.CompactedTraceTexelData", traceViewSize.x, traceViewSize.y, GraphicsFormat::R32G32_UInt, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);
        RED->AllocatePass("Clear Smart CompactedTraceTexelData")->ClearTexture(mCompactedTraceParam.mCompactedTraceTexelDataTex, NGIClearValue{0, 0, 0, 0});
    }

    {
        auto* redBuffer = RED->AllocateBuffer("SmartGI.CompactTraceIndirectArgs", NGIBufferDesc{sizeof(int32_t) * 2 * 3, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::IndirectBuffer});
        mCompactedTraceParam.mCompactedTracingIndirectArgs = RED->AllocateBufferView(redBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, redBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(float)});
    }

    {
        auto* REDOpacityTexture = RED->AllocateTexture("OpacityTexture", mVoxelLightingState.OpacityTexture.get());
        auto* REDOpacityCompactTexture = RED->AllocateTexture("OpacityCompactTexture", mVoxelLightingState.OpacityCompactTexture.get());
        auto* REDNormalTexture = RED->AllocateTexture("NormalTexture", mVoxelLightingState.NormalTexture.get());
        auto* REDAlbedoTexture = RED->AllocateTexture("AlbedoTexture", mVoxelLightingState.AlbedoTexture.get());
        auto* REDEmissiveTexture = RED->AllocateTexture("EmissiveTexture", mVoxelLightingState.EmissiveTexture.get());

        mVoxelLightingState.OpacityTextureView = RED->AllocateTextureView(REDOpacityTexture,
                                                                          NGITextureViewDesc{NGITextureUsage::UnorderedAccess,
                                                                                             REDOpacityTexture->mDesc.Format,
                                                                                             NGITextureType::Texture3D,
                                                                                             {
                                                                                                 NGITextureAspect::Color,
                                                                                                 0,
                                                                                                 1,
                                                                                                 0,
                                                                                                 1,
                                                                                             }});
        mVoxelLightingState.OpacityTextureView->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);
        mVoxelLightingState.OpacityTextureViewRead = RED->AllocateTextureView(REDOpacityTexture,
                                                                              NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                                 REDOpacityTexture->mDesc.Format,
                                                                                                 NGITextureType::Texture3D,
                                                                                                 {
                                                                                                     NGITextureAspect::Color,
                                                                                                     0,
                                                                                                     1,
                                                                                                     0,
                                                                                                     1,
                                                                                                 }});
        mVoxelLightingState.OpacityTextureViewRead->SetExternalState(NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);

        mVoxelLightingState.OpacityCompactTextureView = RED->AllocateTextureView(REDOpacityCompactTexture,
                                                                                 NGITextureViewDesc{NGITextureUsage::UnorderedAccess,
                                                                                                    REDOpacityCompactTexture->mDesc.Format,
                                                                                                    NGITextureType::Texture3D,
                                                                                                    {
                                                                                                        NGITextureAspect::Color,
                                                                                                        0,
                                                                                                        1,
                                                                                                        0,
                                                                                                        1,
                                                                                                    }});
        mVoxelLightingState.OpacityCompactTextureView->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);
        mVoxelLightingState.OpacityCompactTextureViewRead = RED->AllocateTextureView(REDOpacityCompactTexture,
                                                                                     NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                                        REDOpacityCompactTexture->mDesc.Format,
                                                                                                        NGITextureType::Texture3D,
                                                                                                        {
                                                                                                            NGITextureAspect::Color,
                                                                                                            0,
                                                                                                            1,
                                                                                                            0,
                                                                                                            1,
                                                                                                        }});
        mVoxelLightingState.OpacityCompactTextureViewRead->SetExternalState(NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);

        mVoxelLightingState.NormalTextureView = RED->AllocateTextureView(REDNormalTexture,
                                                                         NGITextureViewDesc{NGITextureUsage::UnorderedAccess,
                                                                                            REDNormalTexture->mDesc.Format,
                                                                                            NGITextureType::Texture3D,
                                                                                            {
                                                                                                NGITextureAspect::Color,
                                                                                                0,
                                                                                                1,
                                                                                                0,
                                                                                                1,
                                                                                            }});
        mVoxelLightingState.NormalTextureView->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);
        mVoxelLightingState.NormalTextureViewRead = RED->AllocateTextureView(REDNormalTexture,
                                                                             NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                                REDNormalTexture->mDesc.Format,
                                                                                                NGITextureType::Texture3D,
                                                                                                {
                                                                                                    NGITextureAspect::Color,
                                                                                                    0,
                                                                                                    1,
                                                                                                    0,
                                                                                                    1,
                                                                                                }});
        mVoxelLightingState.NormalTextureViewRead->SetExternalState(NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);

        mVoxelLightingState.AlbedoTextureView = RED->AllocateTextureView(REDAlbedoTexture,
                                                                         NGITextureViewDesc{NGITextureUsage::UnorderedAccess,
                                                                                            REDAlbedoTexture->mDesc.Format,
                                                                                            NGITextureType::Texture3D,
                                                                                            {
                                                                                                NGITextureAspect::Color,
                                                                                                0,
                                                                                                1,
                                                                                                0,
                                                                                                1,
                                                                                            }});
        mVoxelLightingState.AlbedoTextureView->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);
        mVoxelLightingState.AlbedoTextureViewRead = RED->AllocateTextureView(REDAlbedoTexture,
                                                                             NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                                REDAlbedoTexture->mDesc.Format,
                                                                                                NGITextureType::Texture3D,
                                                                                                {
                                                                                                    NGITextureAspect::Color,
                                                                                                    0,
                                                                                                    1,
                                                                                                    0,
                                                                                                    1,
                                                                                                }});
        mVoxelLightingState.AlbedoTextureViewRead->SetExternalState(NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);

        mVoxelLightingState.EmissiveTextureView = RED->AllocateTextureView(REDEmissiveTexture,
                                                                           NGITextureViewDesc{NGITextureUsage::UnorderedAccess,
                                                                                              REDEmissiveTexture->mDesc.Format,
                                                                                              NGITextureType::Texture3D,
                                                                                              {
                                                                                                  NGITextureAspect::Color,
                                                                                                  0,
                                                                                                  1,
                                                                                                  0,
                                                                                                  1,
                                                                                              }});
        mVoxelLightingState.EmissiveTextureView->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);
        mVoxelLightingState.EmissiveTextureViewRead = RED->AllocateTextureView(REDEmissiveTexture,
                                                                               NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                                  REDEmissiveTexture->mDesc.Format,
                                                                                                  NGITextureType::Texture3D,
                                                                                                  {
                                                                                                      NGITextureAspect::Color,
                                                                                                      0,
                                                                                                      1,
                                                                                                      0,
                                                                                                      1,
                                                                                                  }});
        mVoxelLightingState.EmissiveTextureViewRead->SetExternalState(NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
    }
    for (int Index = 0; Index < GNumVoxelLightingTextures; Index++)
    {
        auto* REDTexture = RED->AllocateTexture("LightingTexture-" + std::to_string(Index), mVoxelLightingState.LightingTextures[Index].get());
        mVoxelLightingState.LightingTexturesView[Index] = RED->AllocateTextureView(REDTexture,
                                                                                   NGITextureViewDesc{NGITextureUsage::UnorderedAccess,
                                                                                                      REDTexture->mDesc.Format,
                                                                                                      NGITextureType::Texture3D,
                                                                                                      {
                                                                                                          NGITextureAspect::Color,
                                                                                                          0,
                                                                                                          1,
                                                                                                          0,
                                                                                                          1,
                                                                                                      }});
        mVoxelLightingState.LightingTexturesView[Index]->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);
        mVoxelLightingState.LightingTexturesViewRead[Index] = RED->AllocateTextureView(REDTexture,
                                                                                       NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                                          REDTexture->mDesc.Format,
                                                                                                          NGITextureType::Texture3D,
                                                                                                          {
                                                                                                              NGITextureAspect::Color,
                                                                                                              0,
                                                                                                              1,
                                                                                                              0,
                                                                                                              1,
                                                                                                          }});
        mVoxelLightingState.LightingTexturesViewRead[Index]->SetExternalState(NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
    }
    {
        auto* REDBuffer = RED->AllocateBuffer("VoxelVisBufferAllocator", mVoxelLightingState.VoxelVisBufferAllocator.get());
        REDBuffer->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);
        mVoxelLightingState.VoxelVisBufferAllocatorView = RED->AllocateBufferView(REDBuffer,
                                                                                  NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer, 0, REDBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(UInt32)});
    }
    const int32_t ClampedNumClipmapLevels = SmartGI::GetNumSmartVoxelClipmaps(GSmartSceneViewDistance);
    for (int32_t ClipmapIndex = 0; ClipmapIndex < ClampedNumClipmapLevels; ++ClipmapIndex)
    {
        auto* REDBuffer = RED->AllocateBuffer("VoxelVisBuffers-" + std::to_string(ClipmapIndex), mVoxelLightingState.VoxelVisBuffers[ClipmapIndex].get());
        REDBuffer->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);
        mVoxelLightingState.VoxelVisBuffersView[ClipmapIndex] = RED->AllocateBufferView(REDBuffer,
                                                                                        NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer, 0, REDBuffer->mDesc.Size, GraphicsFormat::Unknown,
                                                                                                          sizeof(UInt32)});
    }

    //AllocateVoxelLightingTexture(RED, SmartGI::GetClipmapResolution());
}

void SmartVoxelRenderer::AssembleScreenProbeTraceVoxels(const GameContext& gameContext, RenderingExecutionDescriptor* RED, REDTextureView* rayInfosForTracing)
{
    AssembleCompactTraces(gameContext, RED);

    auto* TraceVoxelsShader = mPassSettings.SmartFinalGatherTraceVoxelsShaderR;
    {
        SmartTracingInputs TracingInputs;
        InitTracingInputs(TracingInputs);
        auto* pass = RED->AllocatePass("FinalGatherTraceVoxels");
        mFGCommon.SetREDProperty(pass);
        UpdateContextVoxelCommon(RED, pass, TracingInputs, 0);
        UpdateContextVoxelLightingState(RED, pass);
        pass->SetProperty(NAME_ID("StructuredImportanceSampledRayInfosForTracing"), rayInfosForTracing);
        pass->SetProperty(NAME_ID("LightingTexture"), mVoxelLightingState.GetReadLightingTexture());
        pass->SetProperty(NAME_ID("ENABLE_VOXEL_OPACITY_TEX"), mPassSettings.mEnableVoxelOpacityTex);
        pass->SetProperty(NAME_ID("ENABLE_VOXEL_OPACITY_COMPACT_TEX"), mPassSettings.mEnableVoxelOpacityCompactTex);

        pass->SetProperty(NAME_ID("CompactedTraceTexelAllocator"), mCompactedTraceParam.mCompactedTraceTexelAllocator);
        pass->SetProperty(NAME_ID("CompactedTraceTexelData"), mCompactedTraceParam.mCompactedTraceTexelData);
        pass->SetProperty(NAME_ID("RWTraceRadiance"), mFGCommon.mTraceRadiance);
        pass->SetProperty(NAME_ID("RWTraceHit"), mFGCommon.mTraceHit);

        pass->SetProperty(NAME_ID("StepFactor"), clamp(GDiffuseTraceStepFactor, 0.1f, 10.0f));
        pass->SetProperty(NAME_ID("MinTraceDistance"), clamp(mPassSettings.mMinVoxelTraceDistance, 0.01f, 1000.0f));
        pass->SetProperty(NAME_ID("MaxTraceDistance"), mPassSettings.mMaxVoxelTraceDistance);
        pass->SetProperty(NAME_ID("ENABLE_FIRST_VOXEL_UNHIT"), mPassSettings.mEnableFirstVoxelUnHit);
        pass->SetProperty(NAME_ID("SurfaceBias"), clamp(mPassSettings.mVoxelTraceSurfaceBias, 0.01f, 1.0f));

        //SmartGIPass::UpdateSkyLightContext(gameContext);

        pass->DispatchIndirect(TraceVoxelsShader, "TraceVoxelsCS", mCompactedTraceParam.mCompactedTracingIndirectArgs->mBuffer, 0);
    }
}

void SmartVoxelRenderer::AssembleScreenProbeTraceVoxels_ForSkyLighting(const GameContext& gameContext, RenderingExecutionDescriptor* RED, REDPass* pass)
{
    if (mVoxelLighting == nullptr)
    {
        return;
    }

    if (mVoxelLightingReset)
    {
        return;
    }

    SmartTracingInputs TracingInputs;
    InitTracingInputs(TracingInputs);

    UpdateContextVoxelCommon(RED, pass, TracingInputs, 0, false);

    auto* REDOpacityTexture = RED->AllocateTexture("OpacityTexture_ForSkyLighting", mVoxelLightingState.OpacityTexture.get());
    auto* REDOpacityCompactTexture = RED->AllocateTexture("OpacityCompactTexture_ForSkyLighting", mVoxelLightingState.OpacityCompactTexture.get());
    auto* REDAlbedoTexture = RED->AllocateTexture("AlbedoTexture_ForSkyLighting", mVoxelLightingState.AlbedoTexture.get());

    auto* OpacityTextureView = RED->AllocateTextureView(REDOpacityTexture,
                                                        NGITextureViewDesc{NGITextureUsage::UnorderedAccess,
                                                                           REDOpacityTexture->mDesc.Format,
                                                                           NGITextureType::Texture3D,
                                                                           {
                                                                               NGITextureAspect::Color,
                                                                               0,
                                                                               1,
                                                                               0,
                                                                               1,
                                                                           }});
    OpacityTextureView->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);
    auto* OpacityTextureViewRead = RED->AllocateTextureView(REDOpacityTexture,
                                                            NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                               REDOpacityTexture->mDesc.Format,
                                                                               NGITextureType::Texture3D,
                                                                               {
                                                                                   NGITextureAspect::Color,
                                                                                   0,
                                                                                   1,
                                                                                   0,
                                                                                   1,
                                                                               }});
    OpacityTextureViewRead->SetExternalState(NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);

    auto* OpacityCompactTextureView = RED->AllocateTextureView(REDOpacityCompactTexture,
                                                               NGITextureViewDesc{NGITextureUsage::UnorderedAccess,
                                                                                  REDOpacityCompactTexture->mDesc.Format,
                                                                                  NGITextureType::Texture3D,
                                                                                  {
                                                                                      NGITextureAspect::Color,
                                                                                      0,
                                                                                      1,
                                                                                      0,
                                                                                      1,
                                                                                  }});
    OpacityCompactTextureView->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);
    auto* OpacityCompactTextureViewRead = RED->AllocateTextureView(REDOpacityCompactTexture,
                                                                   NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                      REDOpacityCompactTexture->mDesc.Format,
                                                                                      NGITextureType::Texture3D,
                                                                                      {
                                                                                          NGITextureAspect::Color,
                                                                                          0,
                                                                                          1,
                                                                                          0,
                                                                                          1,
                                                                                      }});
    OpacityCompactTextureViewRead->SetExternalState(NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);

    auto* AlbedoTextureView = RED->AllocateTextureView(REDAlbedoTexture,
                                                       NGITextureViewDesc{NGITextureUsage::UnorderedAccess,
                                                                          REDAlbedoTexture->mDesc.Format,
                                                                          NGITextureType::Texture3D,
                                                                          {
                                                                              NGITextureAspect::Color,
                                                                              0,
                                                                              1,
                                                                              0,
                                                                              1,
                                                                          }});
    AlbedoTextureView->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);
    auto* AlbedoTextureViewRead = RED->AllocateTextureView(REDAlbedoTexture,
                                                           NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                              REDAlbedoTexture->mDesc.Format,
                                                                              NGITextureType::Texture3D,
                                                                              {
                                                                                  NGITextureAspect::Color,
                                                                                  0,
                                                                                  1,
                                                                                  0,
                                                                                  1,
                                                                              }});
    AlbedoTextureViewRead->SetExternalState(NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);

    pass->SetProperty("AlbedoTexture", AlbedoTextureViewRead);
    pass->SetProperty("OpacityTexture", OpacityTextureViewRead);
    pass->SetProperty("OpacityCompactTexture", OpacityCompactTextureViewRead);

    //pass->SetProperty(NAME_ID("LightingTexture"), mVoxelLightingState.GetReadLightingTexture());

    pass->SetProperty(NAME_ID("ENABLE_VOXEL_OPACITY_TEX"), mPassSettings.mEnableVoxelOpacityTex);
    pass->SetProperty(NAME_ID("ENABLE_VOXEL_OPACITY_COMPACT_TEX"), mPassSettings.mEnableVoxelOpacityCompactTex);

    pass->SetProperty(NAME_ID("StepFactor"), clamp(GDiffuseTraceStepFactor, 0.1f, 10.0f));
    pass->SetProperty(NAME_ID("MinTraceDistance"), clamp(mPassSettings.mMinVoxelTraceDistance, 0.01f, 1000.0f));
    pass->SetProperty(NAME_ID("MaxTraceDistance"), mPassSettings.mMaxVoxelTraceDistance);
    pass->SetProperty(NAME_ID("ENABLE_FIRST_VOXEL_UNHIT"), mPassSettings.mEnableFirstVoxelUnHit);
    pass->SetProperty(NAME_ID("SurfaceBias"), clamp(mPassSettings.mVoxelTraceSurfaceBias, 0.01f, 1.0f));
}

void SmartVoxelRenderer::AssembleCompactTraces(const GameContext& gameContext, RenderingExecutionDescriptor* RED)
{
    constexpr float WORLD_MAX = 2097152.0; /* Maximum size of the world */

    UInt2 traceViewSize = mFGCommon.GetScreenProbeOctahedronViewSize();

    auto* CompactTracesShader = mPassSettings.SmartCompactTracesShaderR;

    {
        auto* pass = RED->AllocatePass("CompactTraces");
        mFGCommon.SetREDProperty(pass);
        pass->SetProperty("CompactionTracingEndDistanceFromCamera", 0.5f * WORLD_MAX);
        pass->SetProperty("CompactionMaxTraceDistance", 0.5f * WORLD_MAX);

        pass->SetProperty("RWCompactedTraceTexelAllocator", mCompactedTraceParam.mCompactedTraceTexelAllocatorRW);
        if (mCompactedTraceParam.mCompactedTraceTexelDataRW)
        {
            pass->SetProperty("RWCompactedTraceTexelData", mCompactedTraceParam.mCompactedTraceTexelDataRW);
        }
        if (mCompactedTraceParam.mCompactedTraceTexelDataTex)
        {
            pass->SetProperty("RWCompactedTraceTexelDataTex", mCompactedTraceParam.mCompactedTraceTexelDataTex);
        }

        UInt3 groupSize;
        CompactTracesShader->GetThreadGroupSize("CompactTracesCS", groupSize.x, groupSize.y, groupSize.z);
        pass->Dispatch(CompactTracesShader, "CompactTracesCS", (traceViewSize.x + groupSize.x - 1) / groupSize.x, (traceViewSize.y + groupSize.y - 1) / groupSize.y, 1);
    }
    {
        auto* pass = RED->AllocatePass("SetupCompactedTracesIndirectArgs");
        mFGCommon.SetREDProperty(pass);
        pass->SetProperty("CompactedTraceTexelAllocator", mCompactedTraceParam.mCompactedTraceTexelAllocator);
        pass->SetProperty("RWScreenProbeCompactTracingIndirectArgs", mCompactedTraceParam.mCompactedTracingIndirectArgs);
        pass->Dispatch(CompactTracesShader, "SetupCompactedTracesIndirectArgsCS", 1, 1, 1);
    }
}

void SmartVoxelRenderer::AssembleUpdateVoxelizeTexture(const GameContext& gameContext, RenderingExecutionDescriptor* RED, const SmartTracingInputs& inputs, SInt32 ClipmapIndex,
                                                       ecs::EntityID eID, REDTextureView* albedoTex, REDTextureView* normalTex, REDTextureView* emissiveTex)
{
    auto* voxelizeSystemR = gameContext.mRenderWorld->GetRenderSystem<VoxelizeSystemR>();

    auto* UpdateVoxelizeTextureShader = mPassSettings.UpdateVoxelizeTextureShaderR;
    UInt3 groupSize;
    UpdateVoxelizeTextureShader->GetThreadGroupSize("UpdateVoxelizeTexture", groupSize.x, groupSize.y, groupSize.z);
    UInt32 threadGroupX = (albedoTex->GetWidth() + groupSize.x - 1) / groupSize.x;
    UInt32 threadGroupY = (albedoTex->GetHeight() + groupSize.y - 1) / groupSize.y;
    UInt32 threadGroupZ = (albedoTex->mTexture->mDesc.Depth / GNumVoxelDirections + groupSize.z - 1) / groupSize.z;

    auto* pass = RED->AllocatePass("UpdateVoxelizeTextureCS");
    mFGCommon.SetREDProperty(pass);
    RendererVoxelizeHelper::InitVoxelPassRenderContext(&pass->GetContext(),
                                                       false,
                                                       mVoxelLightingState.AlbedoTextureView,
                                                       mVoxelLightingState.NormalTextureView,
                                                       mVoxelLightingState.EmissiveTextureView,
                                                       nullptr,
                                                       0,
                                                       ClipmapIndex,
                                                       mRenderPipeline->GetRenderCamera()->GetTilePosition(),
                                                       inputs.ClipmapWorldToUVScale[ClipmapIndex],
                                                       inputs.ClipmapWorldToUVBias[ClipmapIndex],
                                                       inputs.ClipmapWorldCenter[ClipmapIndex],
                                                       inputs.ClipmapWorldExtent[ClipmapIndex],
                                                       inputs.VoxelGridResolution,
                                                       mPassSettings.mDiffuseBoost,
                                                       mRenderPipeline->GetRenderCamera());

    pass->SetProperty("SrcNormalTexture", normalTex);
    pass->SetProperty("SrcAlbedoTexture", albedoTex);
    pass->SetProperty("SrcEmissiveTexture", emissiveTex);
    voxelizeSystemR->UpdateVoxelizeTexturePassContext(eID, pass->GetContext());
    pass->Dispatch(UpdateVoxelizeTextureShader, "UpdateVoxelizeTexture", threadGroupX, threadGroupY, threadGroupZ);
}

void SmartVoxelRenderer::UpdateContextVoxelLightingState(RenderingExecutionDescriptor* RED, REDPass* pass) const
{
    UpdateContextVoxelLightingStateWriter(pass);
    UpdateContextVoxelLightingStateReader(pass);
}

void SmartVoxelRenderer::UpdateContextVoxelLightingStateWriter(REDPass* pass) const
{
    pass->SetProperty(NAME_ID("RWNormalTexture"), mVoxelLightingState.NormalTextureView);
    pass->SetProperty(NAME_ID("RWAlbedoTexture"), mVoxelLightingState.AlbedoTextureView);
    pass->SetProperty(NAME_ID("RWEmissiveTexture"), mVoxelLightingState.EmissiveTextureView);
    pass->SetProperty(NAME_ID("RWOpacityTexture"), mVoxelLightingState.OpacityTextureView);
    pass->SetProperty(NAME_ID("RWOpacityCompactTexture"), mVoxelLightingState.OpacityCompactTextureView);
}

void SmartVoxelRenderer::UpdateContextVoxelLightingStateReader(REDPass* pass) const
{
    pass->SetProperty(NAME_ID("NormalTexture"), mVoxelLightingState.NormalTextureViewRead);
    pass->SetProperty(NAME_ID("AlbedoTexture"), mVoxelLightingState.AlbedoTextureViewRead);
    pass->SetProperty(NAME_ID("EmissiveTexture"), mVoxelLightingState.EmissiveTextureViewRead);
    pass->SetProperty(NAME_ID("OpacityTexture"), mVoxelLightingState.OpacityTextureViewRead);
    pass->SetProperty(NAME_ID("OpacityCompactTexture"), mVoxelLightingState.OpacityCompactTextureViewRead);
}

void SmartVoxelRenderer::UpdateContextVoxelCommon(RenderingExecutionDescriptor* RED, REDPass* pass, const SmartTracingInputs& inputs, int32_t ClipmapIndex, bool includeVisBuffer) const
{
    const auto& ClipmapState = mVoxelLightingClipmapState[ClipmapIndex];
    auto ClipmapToWorldCenterBias = ClipmapState.Center - ClipmapState.Extent;
    auto ClipmapToWorldCenterScale = ClipmapState.VoxelSize;

    pass->SetProperty("ClipmapToWorldCenterScale", ClipmapToWorldCenterScale);
    pass->SetProperty("ClipmapToWorldCenterBias", ClipmapToWorldCenterBias);

    pass->SetProperty("ClipmapIndex", ClipmapIndex);
    pass->SetProperty("VoxelSize", ClipmapState.VoxelSize.x);

    pass->SetProperty("ClipmapResolution", inputs.VoxelGridResolution);
    pass->SetProperty("NumClipmapLevels", inputs.NumClipmapLevels);

    constexpr auto dataSize = sizeof(inputs.ClipmapWorldToUVScale);
    pass->SetProperty("ClipmapWorldToUVScale", inputs.ClipmapWorldToUVScale.data(), dataSize);
    pass->SetProperty("ClipmapWorldToUVBias", inputs.ClipmapWorldToUVBias.data(), dataSize);
    pass->SetProperty("ClipmapWorldCenter", inputs.ClipmapWorldCenter.data(), dataSize);
    pass->SetProperty("ClipmapWorldExtent", inputs.ClipmapWorldExtent.data(), dataSize);
    pass->SetProperty("ClipmapVoxelSizeAndRadius", inputs.ClipmapVoxelSizeAndRadius.data(), dataSize);

    if (includeVisBuffer)
    {
        pass->SetProperty("VoxelVisBuffer", mVoxelLightingState.VoxelVisBuffersView[ClipmapIndex]);
    }
}

void SmartVoxelRenderer::ClearVoxelizeTexture(RenderingExecutionDescriptor* RED, SmartTracingInputs& TracingInputs, int clipmapIndex)
{
    auto* SmartVoxelizerUpdateShader = mPassSettings.SmartVoxelizerUpdateShaderR;
    auto* pass = RED->AllocatePass("ClearVoxelizeSceneCS_Clipmap_" + std::to_string(clipmapIndex));
    mFGCommon.SetREDProperty(pass);

    UInt3 threadCount(static_cast<UInt32>(TracingInputs.VoxelGridResolution.x),
                      static_cast<UInt32>(TracingInputs.VoxelGridResolution.y),
                      static_cast<UInt32>(TracingInputs.VoxelGridResolution.z * GNumVoxelDirections));

    UpdateContextVoxelLightingState(RED, pass);
    pass->SetProperty("ClipmapIndex", clipmapIndex);
    pass->SetProperty("ClipmapResolution", threadCount);

    UInt3 groupSize;
    SmartVoxelizerUpdateShader->GetThreadGroupSize("ClearVoxelizeSceneCS", groupSize.x, groupSize.y, groupSize.z);
    pass->Dispatch(SmartVoxelizerUpdateShader,
                   "ClearVoxelizeSceneCS",
                   (threadCount.x + groupSize.x - 1) / groupSize.x,
                   (threadCount.y + groupSize.y - 1) / groupSize.y,
                   (threadCount.z + groupSize.z - 1) / groupSize.z);
}

void SmartVoxelRenderer::ClearVoxelizeTextureGridTiles(RenderingExecutionDescriptor* RED, SmartTracingInputs& TracingInputs, int clipmapIndex)
{
    ComputeShaderR* SmartVoxelizerUpdateShader = mPassSettings.SmartVoxelizerUpdateShaderR;
    auto* redBuffer = RED->AllocateBuffer("Smart.UpdateIndirectArgBuffer", NGIBufferDesc{sizeof(int32_t) * 13 * 3, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::IndirectBuffer});
    REDBufferView* indirectBuffer = RED->AllocateBufferView(redBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, redBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(float)});
    {
        auto* pass = RED->AllocatePass("ClearIndirectArgBuffer");
        mFGCommon.SetREDProperty(pass);
        pass->SetProperty("RWClearVisBufferIndirectArgBuffer", indirectBuffer);
        pass->Dispatch(SmartVoxelizerUpdateShader, "ClearVisBuffersCS", 1, 1, 1);
    }

    const auto& UpdateBounds = mVoxelLightingClipmapState[clipmapIndex].UpdateBounds;
    const auto NumUpdateBounds = UpdateBounds.size();
    auto* BoundsBuffer = RED->AllocateBuffer("Smart.UpdateBoundsBuffer", NGIBufferDesc{sizeof(Float4) * 2 * UpdateBounds.size(), NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst});
    REDBufferView* BoundsBufferView = RED->AllocateBufferView(BoundsBuffer, NGIBufferViewDesc{NGIBufferUsage::StructuredBuffer, 0, BoundsBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(float) * 4});
    {
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto* scratchBuffer = rendererSystem->GetScratchBuffer();

        std::vector<Float4> UpdateBoundsData;
        UpdateBoundsData.resize(UpdateBounds.size() * 2);
        const auto UpdateBoundsDataBufferSize = UpdateBoundsData.size() * sizeof(Float4);
        for (int index = 0; index < UpdateBounds.size(); index++)
        {
            const auto& bound = UpdateBounds[index];
            Float3 out;
            bound.GetCenter(&out);
            UpdateBoundsData[index * 2 + 0] = Float4(out, 0.0f);
            bound.GetExtent(&out);
            UpdateBoundsData[index * 2 + 1] = Float4(out, 0.0f);
        }

        {
            auto bufferWrap = scratchBuffer->AllocateStaging(NGIBufferUsage::CopySrc, UpdateBoundsDataBufferSize);
            bufferWrap.MemWrite(0, UpdateBoundsData.data(), UpdateBoundsDataBufferSize);

            const NGICopyBuffer region{
                bufferWrap.GetNGIOffset(),
                0,
                UpdateBoundsDataBufferSize,
            };
            auto* uploadPass = RED->AllocatePass("Upload Bounds Buffer");
            uploadPass->CopyBufferToBuffer(BoundsBuffer, bufferWrap.GetNGIBuffer(), 1, &region);
        }
    }

    const auto Clipmap = mVoxelLightingClipmapState[clipmapIndex];
    const auto UpdateGridResolution = TracingInputs.VoxelGridResolution;
    const auto UpdateTileWorldExtent = 0.5f * Clipmap.VoxelSize;
    const auto UpdateGridCoordToWorldCenterScale = (2.0f * Clipmap.Extent) / Int3ToFloat3(UpdateGridResolution);
    const auto UpdateGridCoordToWorldCenterBias = Clipmap.Center - Clipmap.Extent;
    const auto UpdateGridSize = UpdateGridResolution.x * UpdateGridResolution.y * UpdateGridResolution.z;

    auto* UpdateTileBuffer = RED->AllocateBuffer("Smart.UpdateTileBuffer", NGIBufferDesc{static_cast<size_t>(UpdateGridSize), NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer});
    REDBufferView* UpdateTileBufferView = RED->AllocateBufferView(UpdateTileBuffer,
                                                                  NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer, 0, UpdateTileBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(float)});
    {
        auto* pass = RED->AllocatePass("BuildUpdateGridTiles: " + std::to_string(clipmapIndex) + " NumUpdateBounds: " + std::to_string(NumUpdateBounds));
        mFGCommon.SetREDProperty(pass);
        const UInt3 threadCount(static_cast<UInt32>(UpdateGridResolution.x), static_cast<UInt32>(UpdateGridResolution.y), static_cast<UInt32>(UpdateGridResolution.z));
        UpdateContextVoxelLightingState(RED, pass);
        pass->SetProperty("RWGridTileBuffer", UpdateTileBufferView);
        pass->SetProperty("RWClearVisBufferIndirectArgBuffer", indirectBuffer);
        pass->SetProperty("UpdateBoundsBuffer", BoundsBufferView);
        pass->SetProperty("NumUpdateBounds", static_cast<UInt32>(NumUpdateBounds));
        pass->SetProperty("ClipmapIndex", clipmapIndex);
        pass->SetProperty("ClipmapResolution", UpdateGridResolution);
        pass->SetProperty("ClipmapToWorldCenterScale", UpdateGridCoordToWorldCenterScale);
        pass->SetProperty("ClipmapToWorldCenterBias", UpdateGridCoordToWorldCenterBias);
        pass->SetProperty("GridCenter", Clipmap.Center);
        pass->SetProperty("GridExtent", Clipmap.Extent);
        pass->SetProperty("TileWorldExtent", UpdateTileWorldExtent);
        pass->Dispatch(SmartVoxelizerUpdateShader, "BuildUpdateGridTilesCS", threadCount.x, threadCount.y, threadCount.z);
    }

    {
        auto* pass = RED->AllocatePass("ClearVoxelizeSceneCS_Clipmap_" + std::to_string(clipmapIndex));
        mFGCommon.SetREDProperty(pass);
        UInt3 threadCount(static_cast<UInt32>(TracingInputs.VoxelGridResolution.x), static_cast<UInt32>(TracingInputs.VoxelGridResolution.y), static_cast<UInt32>(TracingInputs.VoxelGridResolution.z * GNumVoxelDirections));
        UpdateContextVoxelLightingState(RED, pass);
        pass->SetProperty("ClipmapIndex", clipmapIndex);
        pass->SetProperty("ClipmapResolution", threadCount);
        pass->SetProperty("UpdateTileBuffer", UpdateTileBufferView);

        pass->DispatchIndirect(SmartVoxelizerUpdateShader, "ClearVoxelizeSceneCSWithTileMask", indirectBuffer->mBuffer, 0);
    }
}
}
