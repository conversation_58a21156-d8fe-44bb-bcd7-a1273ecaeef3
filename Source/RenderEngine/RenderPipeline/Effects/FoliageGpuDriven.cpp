#include "EnginePrefix.h"
#include "FoliageGpuDriven.h"
#include "Shadow.h"
#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/EngineGlobal.h"
#include "Resource/AssetStreaming.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/FoliageSystemR.h"
#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/ShadowCamera.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "RenderEngine/IVertexStreamLayoutPolicy.h"
#include "RenderEngine/RenderFactory.h"
#include "RenderEngine/RenderPipeline/WorldRenderPipeline/FFSWorldRenderPipeline.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/SkyAtmosphereSystemR.h"
#include "RenderEngine/RenderMesh.h"

#include "NativeGraphicsInterface/NGIManager.h"

#define DEBUG_SHOW_SHADOW_FRUSTUM false

#define LOAD_RENDER_PIPELINE_MERGE_GEOMETRY(Name, RenderMesh, MeshAssets) \
{ \
    Name##Res = TypeCast<resource::MeshAssetDataResource>(gAssetStreamingManager->LoadSynchronously(Name)); \
    Name##R = static_cast<MeshAssetData*>(Name##Res->GetAssetData()); \
    if (Name##R->HasPreDefinedLayout()) \
    { \
        Assert(false); \
    } \
    else \
    { \
        RenderMesh.push_back(static_cast<MeshR*>(Name##R->GetRenderMesh())); \
        MeshAssets.push_back(Name##Res); \
    } \
}

#define CREATE_RED_BUFFER_VIEW(BUFFER_NAME, RED, NAME, SIZE, USAGE, STRIDE) \
    REDBuffer* BUFFER_NAME; \
    REDBufferView* BUFFER_NAME##View; \
    std::tie(BUFFER_NAME##View, BUFFER_NAME) = CreateREDBufferView(RED, NAME, NGIBufferDesc{SIZE, USAGE}, STRIDE);

#define CREATE_RED_BUFFER_VIEW_FROM_NATIVE(RED, BUFFER_NAME, NATIVE_BUFFER, STATE, USAGE, STRIDE) \
    REDBuffer* BUFFER_NAME##REDBuffer = AllocateREDBuffer(RED, #BUFFER_NAME, NATIVE_BUFFER, STATE); \
    REDBufferView* BUFFER_NAME##View = RED->AllocateBufferView(BUFFER_NAME##REDBuffer, NGIBufferViewDesc{USAGE, 0, BUFFER_NAME##REDBuffer->GetDesc().Size, GraphicsFormat::Unknown, STRIDE}); \

namespace cross {
struct alignas(16) LODInfo
{
    UInt32 indexCount;
    UInt32 firstIndex;
    SInt32 vertexOffset;
    float screenReleativeTransitionHeight;
};

struct BatchDrawUnitInfo
{
    UInt32 countBufferOffset;
    UInt32 instanceCount;
    UInt32 startSection;
    UInt32 sectionCount;
    UInt32 separateLOD;
};

struct alignas(16) InstanceSection
{
    Float4 boundingSphere;
    UInt32 batchDrawUnitIndex;
    UInt32 instanceOffset;
    UInt32 drawCountOffset;
    float culledHeight;
    SInt32 isPCGFoliage;
    UInt32 startLodOffset;
    UInt32 lodCount;
    UInt32 startInstance;
    UInt32 instanceCount;
};

struct alignas(16) DrawUnitInfo
{
    Float4 boundingSphere;
    float culledHeight;
    float maxRandomCulling;
    UInt32 startLodOffset;
    UInt32 lodCount;
    UInt32 LodBias;
    UInt32 startInstance;
    UInt32 instanceCount;
    UInt32 indirectArgIndexOffset;
};

struct VisibleInstance
{
    UInt32 instanceId;
    UInt32 indirectArgIndex;
};

struct MapData
{
    UInt32 drawUnitId;
    UInt32 firstInstance;
};

const inline static NameID tileBasedDeferredLighingBackPassID = "TileBasedDeferredLighting";

const UInt32 FOLIAGE_DEFAULT_BLOCK_SIZE = 4 * 1024 * 1024;
const float BUFFER_PRIORITY = 1.0f;
UInt32 FoliageGpuDriven::mMaxInstanceCount = 1500000;
UInt32 FoliageGpuDriven::mMaxDrawCount = 3000000;
UInt32 FoliageGpuDriven::mMaxCount = 2000;
NGIBufferPtr FoliageGpuDriven::mFoliageIndirectBuffer;
NGIBufferPtr FoliageGpuDriven::mFoliageInstanceBuffer;
NGIBufferPtr FoliageGpuDriven::mLightInstanceBuffer;
NGIBufferPtr FoliageGpuDriven::mFoliageInstanceCountBuffer;
NGIBufferPtr FoliageGpuDriven::mFoliageEntityBuffer;

std::unique_ptr<NGIBufferView> FoliageGpuDriven::mFoliageIndirectRWBufferView;
std::unique_ptr<NGIBufferView> FoliageGpuDriven::mFoliageIndirectBufferView;

void GetFrustumPlanes(const RenderCamera* camera, Float4* planes, bool& useHiz)
{
    if (camera->GetProjectionMode() == CameraProjectionMode::Perspective)
    {
        const BoundingFrustum& frustum = camera->GetFrustum();
        const auto& worldMatrix = camera->GetInvertViewMatrix();
        BoundingFrustum newFrustum;
        frustum.Transform(newFrustum, worldMatrix);
        newFrustum.GetPlanes(&planes[0], &planes[1], &planes[2], &planes[3], &planes[4], &planes[5]);

//        auto& lastFrameWorldMat = camera->GetLastFrameInvertViewMatrix(CameraProjectionMode::Perspective);
//        Float3 scale, lastFrameScale;
//        Quaternion rotation, lastFrameRotation;
//        Float3 translation, lastFrameTranslation;
//        worldMatrix.Decompose(scale, rotation, translation);
//        lastFrameWorldMat.Decompose(lastFrameScale, lastFrameRotation, lastFrameTranslation);
//#ifdef CE_USE_DOUBLE_TRANSFORM
//        lastFrameTranslation = lastFrameTranslation + (camera->GetTilePosition() - camera->GetTilePosition<true>()) * LENGTH_PER_TILE_F;
//#endif
//        float distance = Float3::Distance(translation, lastFrameTranslation);
//        float rotationDis = rotation.AngularDistance(lastFrameRotation);
        //if (distance > 50.0f || rotationDis > 5.0f)
        //{
        //    useHiz = false;
        //}
        }
    else
    {
        // Orthogonal camera, use corner points to construct 6 planes
        Float3 corners[8];
        camera->GetOrthBox().GetCorners(corners);
        Plane orthogonalPlanes[6];
        orthogonalPlanes[0].Set3Points(corners[0], corners[1], corners[2]);
        orthogonalPlanes[1].Set3Points(corners[6], corners[5], corners[4]);
        orthogonalPlanes[2].Set3Points(corners[2], corners[1], corners[5]);
        orthogonalPlanes[3].Set3Points(corners[0], corners[3], corners[7]);
        orthogonalPlanes[4].Set3Points(corners[3], corners[2], corners[6]);
        orthogonalPlanes[5].Set3Points(corners[4], corners[5], corners[1]);
        for (int i = 0; i < 6; ++i)
        {
            planes[i].x = orthogonalPlanes[i].a();
            planes[i].y = orthogonalPlanes[i].b();
            planes[i].z = orthogonalPlanes[i].c();
            planes[i].w = orthogonalPlanes[i].d();
        }
    }
}

std::tuple<NGIBufferView*, cross::NGIBuffer*> cross::FoliageGpuDriven::CreateDeviceBufferView(NGIBufferDesc desc, SizeType stride, std::string_view name)
{
    auto rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto transientMgr = rendererSystem->GetTransientResourceManager();
    auto [buf, resState] = transientMgr->AllocateBuffer(desc, name);

    auto* bufView = rendererSystem->GetTransientResourceManager()->AllocateBufferView({desc.Usage, 0, desc.Size, GraphicsFormat::Unknown, stride}, buf);

    return std::make_tuple(bufView, buf);
}

std::tuple<REDBufferView*, cross::REDBuffer*> cross::FoliageGpuDriven::CreateREDBufferView(RenderingExecutionDescriptor* RED, std::string_view name, NGIBufferDesc desc, SizeType stride)
{
    REDBuffer* buffer = RED->AllocateBuffer(name, desc);

    REDBufferView* bufferView = RED->AllocateBufferView(buffer, NGIBufferViewDesc{desc.Usage, 0u, desc.Size, GraphicsFormat::Unknown, stride});

    return std::make_tuple(bufferView, buffer);
}

REDBuffer* AllocateREDBuffer(RenderingExecutionDescriptor* RED, std::string_view name, NGIBuffer* externalBuffer, NGIResourceState state) 
{
    REDBuffer* REDBuffer = nullptr;
#ifndef CROSSENGINE_RELEASE
    auto ret = RED->FindExternalBuffer(externalBuffer);
    if (ret)
    {
        REDBuffer = ret;
    }
    else
#endif
    {
        REDBuffer = RED->AllocateBuffer(name, externalBuffer);
    }
    REDBuffer->SetExternalState(state);
    return REDBuffer;
}

}   // namespace cross

void cross::FoliageGpuDrivenSettings::Initialize()
{
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(FoliageCullingShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(ClearBufferShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(LightTileCullingShader);
    LOAD_RENDER_PIPELINE_MATERIAL(PointLightMaterial);
    LOAD_RENDER_PIPELINE_MATERIAL(SpotLightMaterial);
    LOAD_RENDER_PIPELINE_MATERIAL(TiledbaseShadingMtl);
    LOAD_RENDER_PIPELINE_MATERIAL(DeferredShadingMtl);
    LOAD_RENDER_PIPELINE_MATERIAL(DeferredShadingAGAAMtl);

    // Assemble Merged MeshAssetData
    std::vector<MeshR*> renderMesh;
    std::vector<MeshAssetDataResourcePtr> meshAssets;
    LOAD_RENDER_PIPELINE_MERGE_GEOMETRY(PointLightMesh, renderMesh, meshAssets);
    LOAD_RENDER_PIPELINE_MERGE_GEOMETRY(SpotLightMesh, renderMesh, meshAssets);

    IVertexStreamLayoutPolicy* packStreamLayoutPolicy = RenderFactory::Instance().GetVertexStreamLayoutPolicy(STREAM_LAYOUT_POLICY_STATIC);
    if (packStreamLayoutPolicy)
    {
        DispatchRenderingCommandWithToken(
            [renderMesh = renderMesh, packStreamLayoutPolicy = packStreamLayoutPolicy, meshAssets = meshAssets, this]() mutable { packStreamLayoutPolicy->AssembleMergedGpuResource(renderMesh, meshAssets, MergedGeometryPacket); });
    }
}

cross::FoliageGpuDriven::FoliageGpuDriven()
{
    constexpr static NGITargetBlendStateDesc addBlendStateDesc{
        true,
        false,
        BlendFactor::One,
        BlendFactor::One,
        NGIBlendOp::Add,
        BlendFactor::One,
        BlendFactor::One,
        NGIBlendOp::Add,
        LogicOp::NoOp,
        ColorMask::All,
    };

    constexpr static NGIBlendStateDesc directionalBlendStateDesc{
        false,
        false,
        2,
        {
            addBlendStateDesc,
            addBlendStateDesc,
        },
    };
    constexpr static NGIDepthStencilStateDesc directionalDepthStencilStateDesc{false, false, ComparisonOp::Always, false};

    mDirectionalLightBackLightingStateOverride = {directionalBlendStateDesc, directionalDepthStencilStateDesc};
}

void cross::FoliageGpuDriven::DestoryBuffer() 
{
     auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
     if (mFoliageIndirectRWBufferView)
    {
         rendererSystem->DestroyNGIObject(mFoliageIndirectRWBufferView.release());
     }
     if (mFoliageIndirectBufferView)
    {
         rendererSystem->DestroyNGIObject(mFoliageIndirectBufferView.release());
     }
}

cross::FoliageGpuDriven::~FoliageGpuDriven()
{
}

void cross::FoliageGpuDriven::PrepareBuffer()
{
    if (mSetting.enable)
    {
        auto& NGIDevice = GetNGIDevice();
        if (!mFoliageInstanceBuffer)
        {
            auto newBuff = NGIDevice.CreateBuffer(NGIBufferDesc{FOLIAGE_DEFAULT_BLOCK_SIZE, NGIBufferUsage::CopySrc | NGIBufferUsage::CopyDst | NGIBufferUsage::VertexBuffer | NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer, BUFFER_PRIORITY}, "FoliageInstanceBuffer");
            mFoliageInstanceBuffer.reset(newBuff);
        }

        if (!mLightInstanceBuffer)
        {
            auto lightBuff = NGIDevice.CreateBuffer(NGIBufferDesc{FOLIAGE_DEFAULT_BLOCK_SIZE, NGIBufferUsage::CopySrc | NGIBufferUsage::CopyDst | NGIBufferUsage::VertexBuffer | NGIBufferUsage::RWStructuredBuffer, BUFFER_PRIORITY}, "LightInstanceBuffer");
            mLightInstanceBuffer.reset(lightBuff);
        }

        if (!mFoliageIndirectBuffer)
        {
            auto indirecBuff = NGIDevice.CreateBuffer(NGIBufferDesc{FOLIAGE_DEFAULT_BLOCK_SIZE, NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::IndirectBuffer, BUFFER_PRIORITY}, "FoliageIndirectBuffer");
            mFoliageIndirectBuffer.reset(indirecBuff);
        }

        if (!mFoliageInstanceCountBuffer)
        {
            auto countBuff = NGIDevice.CreateBuffer(NGIBufferDesc{mMaxCount * sizeof(UInt32), NGIBufferUsage::RWStructuredBuffer}, "FoliageInstanceCountBuffer");
            mFoliageInstanceCountBuffer.reset(countBuff);
        }

        if (!mFoliageEntityBuffer)
        {
            auto entityBuff = NGIDevice.CreateBuffer(NGIBufferDesc{FOLIAGE_DEFAULT_BLOCK_SIZE, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst, BUFFER_PRIORITY}, "FoliageEntityBuffer");
            mFoliageEntityBuffer.reset(entityBuff);
        }
    }
}

void cross::FoliageGpuDriven::TryShrinkBuffer(NGIBufferPtr& buffer, UInt64 size, NGIResourceState resState)
{
    if (buffer)
    {
        UInt64 bufSize = buffer->GetSize();
        UInt64 shrinkSize = bufSize;
        if (size < FOLIAGE_DEFAULT_BLOCK_SIZE)
        {
            size = FOLIAGE_DEFAULT_BLOCK_SIZE;
        }
        while (size < (shrinkSize >> 1))
        {
            shrinkSize >>= 1;
        }
        if (shrinkSize != bufSize)
        {
            auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
            auto& NGIDevice = GetNGIDevice();
            auto destBuff = NGIDevice.CreateBuffer(NGIBufferDesc{shrinkSize, buffer->GetDesc().Usage, BUFFER_PRIORITY});

            NGICopyBuffer region{0, 0, size};
            rendererSystem->UpdateBuffer(destBuff, buffer.get(), region, NGIResourceState::Undefined, resState, resState, resState);
            buffer.reset(destBuff);
        }
    }
}

void cross::FoliageGpuDriven::PreAllocateBuffer(NGIBufferPtr& buffer, NGIBufferUsage Usage, UInt64 offset, UInt64 size, NGIResourceState resState, bool copyIfReallocate)
{
    auto& NGIDevice = GetNGIDevice();
    if (!buffer)
    {
        auto buff = NGIDevice.CreateBuffer(NGIBufferDesc{FOLIAGE_DEFAULT_BLOCK_SIZE, Usage, BUFFER_PRIORITY}, "FoliageGpuDriven::PreAllocateBuffer buffer");
        buffer.reset(buff);
    }

    UInt64 bufSize = buffer->GetSize();
    UInt64 needSize = offset + size;
    if (needSize > bufSize)
    {
        UInt64 newSize = bufSize * 2;
        while (needSize > newSize)
        {
            newSize *= 2;
        }

        auto destBuff = NGIDevice.CreateBuffer(NGIBufferDesc{newSize, Usage, BUFFER_PRIORITY}, "FoliageGpuDriven::PreAllocateBuffer copy dst");

        if (copyIfReallocate && offset > 0)
        {
            NGICopyBuffer region{0, 0, offset};
            auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
            rendererSystem->UpdateBuffer(destBuff, buffer.get(), region, NGIResourceState::Undefined, resState, NGIResourceState::Undefined, resState);
        }
        buffer.reset(destBuff);
    }
}

cross::NGIBufferView* cross::FoliageGpuDriven::GetLightInstanceView()
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto transientResourceMgr = rendererSystem->GetTransientResourceManager();
    auto* lightInstanceView = transientResourceMgr->AllocateBufferView({ NGIBufferUsage::StructuredBuffer, 0, mSetting.MaxLightInstanceCount * sizeof(LightInstanceData),
        GraphicsFormat::Unknown, sizeof(LightInstanceData) }, mLightInstanceBuffer.get());
    return lightInstanceView;
}

void cross::FoliageGpuDriven::SetRenderPass(REDPass* cullPass, REDPass* renderPass)
{
    if (!cullPass)
        return;
    mRefRenderPassMap[cullPass] = renderPass;

    renderPass->AddBufferReference(indirectREDBuffer, REDResourceState{NGIResourceState::IndirectArgument});

    renderPass->SetProperty("LIGHT_CULLING", false);

    auto& passName = cullPass->GetName();
    bool lightCulling = passName == "GPULightCulling";
    if (lightCulling)
    {
        // regenerate pass context for light drawUnit
        renderPass->SetProperty("lightInstanceData", mLightInstanceREDView);
    }
    else
    {
        renderPass->SetProperty("_FoliageObjectSceneDatas", mFoliageInstanceREDView);
        renderPass->SetProperty("_FoliageEntityBuffer", mFoliageEntityREDView);
    }
}

//bool cross::FoliageGpuDriven::ExecuteImp(const GameContext& gameContext, const NameID& passName, const NameID& passTag, const RenderCamera* camera, cross::REDDrawUnitProxy*& proxy, REDTextureView* hi_z, REDPass*& outPass) 
//{
//    outPass = InjectFoliageCullingPass(gameContext, passName, passTag, camera, proxy, hi_z);
//    return true;
//}

cross::REDPass* cross::FoliageGpuDriven::AssembleCullingPass(const GameContext& gameContext, const NameID& passName, const NameID& passTag, const RenderCamera* camera, REDTextureView* hi_z, 
    REDDrawUnitList* drawUnitList, REDBufferView* visibleObjectCommandsBufferUAV, REDBufferView* visibleObjectCommandCountBufferUAV)
{
    PrepareBuffer();

    auto* foliageSystem = gameContext.mRenderPipeline->mWorld->GetRenderSystem<FoliageSystemR>();
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    auto RED = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
    PropertySet renderContext(RED->GetREDFrameAllocator());
    if (!indirectREDBuffer)
    {
        indirectREDBuffer = AllocateREDBuffer(RED, "foliageIndirectBuffer", mFoliageIndirectBuffer.get(), NGIResourceState::ComputeShaderUnorderedAccess);
    }

    bool isShadowPass = passTag == "shadow" || passTag == "shadow_all";
    bool useHiz = EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting()->EnableHiZ;
    Float4 HZBUvFactorAndInvFactor(1.0f, 1.0f, 1.0f, 1.0f);
    auto targetView = gameContext.mRenderPipeline->GetTargetView();
    auto displaySize = gameContext.mRenderPipeline->GetDisplaySize();
    float displayWidth = static_cast<float>(displaySize.x);
    float displayHeight = static_cast<float>(displaySize.y);
    float viewWidth = static_cast<float>(targetView->mTexture->mDesc.Width);
    float viewHeight = static_cast<float>(targetView->mTexture->mDesc.Height);
    Float4 ce_ScreenParams(viewWidth, viewHeight, 1.0f / viewWidth, 1.0f / viewHeight);
    Float4 displayScreenSize(displayWidth, displayHeight, 1.0f / displayWidth, 1.0f / displayHeight);
    Float4 ce_HZBParams;

    if (hi_z)
    {
        renderContext.SetProperty("hzb", hi_z);
        ce_HZBParams.x = static_cast<float>(hi_z->GetWidth());
        ce_HZBParams.y = static_cast<float>(hi_z->GetHeight());
        ce_HZBParams.z = static_cast<float>(hi_z->GetDesc().SubRange.MipLevels);
    }
    renderContext.SetProperty("isShadowPass", isShadowPass);
    renderContext.SetProperty("lodScaleFactor", mSetting.LODScaleFactor);
    renderContext.SetProperty("ce_ScreenParams", ce_ScreenParams);
    renderContext.SetProperty("ce_HZBParams", ce_HZBParams);
    renderContext.SetProperty("displayScreenSize", displayScreenSize);
    renderContext.SetProperty("_RandomCullingMin", mSetting.RandomCullingMin);
    renderContext.SetProperty("_RandomCullingMax", mSetting.RandomCullingMax);

    // Update Frustum
    Float4 planes[6];
    GetFrustumPlanes(isShadowPass ? gameContext.mRenderPipeline->GetRenderCamera() : camera, planes, useHiz);
    renderContext.SetProperty("_Frustums", &planes[0], sizeof(Float4) * 6);
    renderContext.SetProperty("useHiz", useHiz && hi_z != nullptr);
    bool useDoubleTransform = false;
#if defined(CE_USE_DOUBLE_TRANSFORM)
    useDoubleTransform = true;
#endif
    renderContext.SetProperty("useDoubleTransform", useDoubleTransform);

    bool useReverseZ = gameContext.mRenderPipeline->UseReverseZ();
    if (isShadowPass)
    {
        renderContext.SetProperty("shadowCamView", camera->GetViewMatrix());
        renderContext.SetProperty("shadowCamProjection", camera->GetProjMatrix());

        GetFrustumPlanes(camera, planes, useHiz);
        renderContext.SetProperty("shadowFrustums", &planes[0], sizeof(Float4) * 6);
        useReverseZ = false;
        HZBUvFactorAndInvFactor = {1.0f, 1.0f, 1.0f, 1.0f};

        Float4x4A lightToCamera = (camera->GetViewMatrix() * camera->GetProjMatrix()).Transpose().Inverted() * gameContext.mRenderPipeline->GetRenderCamera()->GetViewProjMatrix().Transpose();
        renderContext.SetProperty("lightToCamera", lightToCamera);
        REDTextureView* hzbShadowVolume = gameContext.mRenderPipeline->GetDepthPyramid();
        renderContext.SetProperty("hzbShadowVolume", hzbShadowVolume);
        renderContext.SetProperty("shadowVolumeCulling", EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting()->EnableShadowVolumeCulling && hzbShadowVolume != nullptr);
        renderContext.SetProperty("ce_ShadowCameraTilePosition", camera->GetTilePosition());

#if DEBUG_SHOW_SHADOW_FRUSTUM
        RenderWorld* renderWorld = gameContext.mRenderPipeline->GetRenderWorld();
        auto cameraSystem = renderWorld->GetRenderSystem<CameraSystemR>();
        cameraSystem->SetShadowFrustumShow(camera, true);
#endif
    }
    renderContext.SetProperty("reverseZ", useReverseZ);
    renderContext.SetProperty("hizUVFactorAndInv", HZBUvFactorAndInvFactor);
    bool lightCulling = passName == "GPULightCulling";
    renderContext.SetProperty("LIGHT_CULLING", lightCulling);
    renderContext.SetProperty("tileBasedCullHeight", mSetting.TileBasedCullHeight);

    gameContext.mRenderPipeline->UpdateContext(renderContext, gameContext.mRenderPipeline->mWorld);

    // Set buffer view
    indirectREDBufferView =
        RED->AllocateBufferView(indirectREDBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::IndirectBuffer, 0, indirectREDBuffer->GetDesc().Size, GraphicsFormat::Unknown, sizeof(CompactDrawCMD)});

    CREATE_RED_BUFFER_VIEW(drawUnitInfoBuffer, RED, "drawUnitInfoBuffer", 1, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst, sizeof(DrawUnitInfo))
    CREATE_RED_BUFFER_VIEW(countBuffer, RED, "countBuffer", 1, NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::IndirectBuffer, sizeof(UInt32))
    CREATE_RED_BUFFER_VIEW(mapDataBuffer, RED, "mapData", 1, NGIBufferUsage::RWStructuredBuffer, sizeof(UInt32))
    CREATE_RED_BUFFER_VIEW(srcDrawBuffer, RED, "srcDrawBuffer", 1, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst, sizeof(LODInfo))
    CREATE_RED_BUFFER_VIEW(indirectArgIndexBuffer, RED, "indirectArgIndexBuffer", 1, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst, sizeof(UInt32))
    CREATE_RED_BUFFER_VIEW(tilebasedLightID, RED, "tilebasedLightID", sizeof(UInt32), NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::CopyDst, sizeof(UInt32))
    CREATE_RED_BUFFER_VIEW(sourceBufferLight, RED, "sourceBufferLight", 1, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst, sizeof(LightInstanceData))
    CREATE_RED_BUFFER_VIEW(sourceBufferShadowIndex, RED, "sourceBufferShadowIndex", 1, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst, sizeof(int))

    UInt32 entityDataSize = std::max(1u, static_cast<UInt32>(sizeof(FoliageEntityData) * foliageSystem->mFoliageEntityOffset));
    CREATE_RED_BUFFER_VIEW(entityData, RED, "entityData", entityDataSize, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst, sizeof(FoliageEntityData))

    CREATE_RED_BUFFER_VIEW_FROM_NATIVE(RED, foliageInstance, mFoliageInstanceBuffer.get(), NGIResourceState::ComputeShaderShaderResource, NGIBufferUsage::StructuredBuffer, sizeof(FoliageInstanceCompactData));
    CREATE_RED_BUFFER_VIEW_FROM_NATIVE(RED, foliageEntity, mFoliageEntityBuffer.get(), NGIResourceState::ComputeShaderShaderResource, NGIBufferUsage::StructuredBuffer, sizeof(FoliageEntityData));
    CREATE_RED_BUFFER_VIEW_FROM_NATIVE(RED, lightInstance, mLightInstanceBuffer.get(), NGIResourceState::ComputeShaderShaderResource, NGIBufferUsage::RWStructuredBuffer, sizeof(LightInstanceData));
    mFoliageInstanceREDView = foliageInstanceView;
    mFoliageEntityREDView = foliageEntityView;
    mLightInstanceREDView = lightInstanceView;

    auto indirectBuffer = drawUnitList->GetIndirectBuffer<REDBuffer*>();
    auto indirectBufferView = RED->AllocateBufferView(indirectBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, indirectBuffer->GetDesc().Size, GraphicsFormat::Unknown, sizeof(CompactDrawCMD)});

    if (lightCulling)
    {
        mTilebasedLightIDREDView = tilebasedLightIDView;
        UInt32 value = 0;
        RED->QueueBufferUpload(tilebasedLightID, 0, &value, sizeof(UInt32));
    }

    renderContext.SetProperty("_SrcDrawBuffer", srcDrawBufferView);
    renderContext.SetProperty("_DstDrawBuffer", indirectREDBufferView);
    renderContext.SetProperty("_DrawUnitInfoBuffer", drawUnitInfoBufferView);
    renderContext.SetProperty("_FoliageEntityBuffer", foliageEntityView);
    renderContext.SetProperty("_CountBuffer", countBufferView);
    renderContext.SetProperty("_MapData", mapDataBufferView);
    renderContext.SetProperty("_InstanceBuffer", foliageInstanceView);
    renderContext.SetProperty("_IndirectArgIndexBuffer", indirectArgIndexBufferView);
    renderContext.SetProperty("_OutDrawIndirectArgs", indirectBufferView);
    renderContext.SetProperty("_OutVisibleObjectCommands", visibleObjectCommandsBufferUAV);
    renderContext.SetProperty("_OutVisibleObjectCommandCount", visibleObjectCommandCountBufferUAV);

    renderContext.SetProperty("_InstanceBufferLight", mLightInstanceREDView);
    renderContext.SetProperty("_TilebasedLightID", tilebasedLightIDView);

    renderContext.SetProperty("_SourceBufferLight", sourceBufferLightView);
    renderContext.SetProperty("_SourceBufferShadowIndex", sourceBufferShadowIndexView);

    NameID kernelInitDrawCommandBuffer = NameID("SetupMapData");
    auto* initDrawCommandPass = RED->AllocatePass(kernelInitDrawCommandBuffer.GetName());
    initDrawCommandPass->SetRenderContext(renderContext);
    auto payloadInitDrawCommandBuffer = initDrawCommandPass->Dispatch(mSetting.FoliageCullingShaderR, kernelInitDrawCommandBuffer, 0, 0, 0);

    NameID kernelVegetationCulling = NameID("FoliageCulling");
    auto* foliageCullingPass = RED->AllocatePass(passName.GetName());
    foliageCullingPass->SetRenderContext(renderContext);
    auto payloadVegetationCulling = foliageCullingPass->Dispatch(mSetting.FoliageCullingShaderR, kernelVegetationCulling, 0, 0, 0);

    foliageCullingPass->OnCulling([=](REDPass* pass) {
        QUICK_SCOPED_CPU_TIMING("DispatchFoliageCulling");

        auto* redDrawUnits = drawUnitList->GetDrawUnits();
        RenderWorld* world = gameContext.mRenderPipeline->mWorld;
        auto* foliageSystem = world->GetRenderSystem<FoliageSystemR>();
        // auto* lightSystem = world->GetRenderSystem<LightSystemR>();
        //bool lightCulling = pass->GetName() == "ForwardTransparentLightCulling";
        //if (lightCulling)
        //{
        //    foliageSystem->UpdateLightList();
        //}

        UInt32 bufferSize = 0;
        UInt32 totalInstancesCount = 0;
        UInt32 maxFoliageInstanceOffset = 0;
        UInt32 maxLightInstanceOffset = 0;

        int indirectCMDOffset = 0;
        UInt32 indirectDrawUnitCount = 0;
        UInt32 totalLodCount = 0;
        UInt32 copyLightInstanceCount = 0;
        for (UInt32 drawUnitIndex = 0; drawUnitIndex < redDrawUnits->GetSize(); drawUnitIndex++)
        {
            const auto& drawUnit = redDrawUnits->At(drawUnitIndex);
            if (drawUnit.mType == RenderNodeType::Foliage )
            {
                auto* foliageInfo = reinterpret_cast<const FoliageInfo*>(drawUnit.mCustumData);
                auto& instanceInfo = foliageSystem->mClusterOffsetMap[foliageInfo->mFoliageClusterKey];
                if (gameContext.mRenderWorld->IsEntityAliveInner(instanceInfo.entityID))
                {
                    auto lodIndex = foliageInfo->LODIndex;
                    auto lodCount = foliageInfo->LODCount;
                    if (lodIndex == 0 && lodCount > 0)
                    {
                        totalInstancesCount += drawUnit.mInstanceCount;
                        ++indirectDrawUnitCount;

                        totalLodCount += lodCount;

                        bool isLightType = instanceInfo.instanceDataType == InstanceDataType::Light;
                        if (instanceInfo.dataDirty && isLightType && !instanceInfo.firstUpload)
                        {
                            copyLightInstanceCount += instanceInfo.uploadInstanceCount;
                        }
                    }
                }
            }
        }

        if (totalInstancesCount)
        {
            indirectBufferView->ModifyRange(0, indirectBuffer->GetDesc().Size);

            int dataCmdOffset = 0;
            int sectionDataCmdOffset = 0;
            bufferSize = totalLodCount * static_cast<UInt32>(sizeof(LODInfo));
            //auto [stagingBuffer, bufferOffset, drawData] = rendererSystem->GetScratchBuffer()->Allocate(NGIBufferUsage::CopySrc, bufferSize);
            auto stagingBufferWrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, bufferSize);


            UInt32 drawUnitInfoSize = indirectDrawUnitCount * static_cast<UInt32>(sizeof(DrawUnitInfo));
            auto drawUnitBufferWrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, drawUnitInfoSize);

            auto entityScratchBufferWrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, entityDataSize);

            UInt32 copyDataOffset = 0;
            UInt32 copyLightSize = copyLightInstanceCount * static_cast<UInt32>(sizeof(LightInstanceData));

            StagingBufferWrap copyLightBufferWrap;
            if (copyLightInstanceCount > 0)
            {
                copyLightBufferWrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, copyLightSize);
            }
            auto pool = RED->GetREDFrameAllocator();
            FrameStdVector<REDDrawUnit*> drawUnits(pool);
            CEHashMap<const void*, UInt32> renderNodeMap;
            FrameStdVector<SInt32> indirectArgIndexData(pool);

            indirectDrawUnitCount = 0;
            UInt32 startLodOffset = 0;
            for (UInt32 drawUnitIndex = 0; drawUnitIndex < redDrawUnits->GetSize(); drawUnitIndex++)
            {
                REDDrawUnit& drawUnit = const_cast<REDDrawUnit&>(redDrawUnits->At(drawUnitIndex));
                if (drawUnit.mType == RenderNodeType::Foliage)
                {
                    auto* foliageInfo = reinterpret_cast<const FoliageInfo*>(drawUnit.mCustumData);
                    auto lodIndex = foliageInfo->LODIndex;
                    UInt8 lodCount = static_cast<UInt8>(foliageInfo->LODCount);
                    auto lodBias = foliageInfo->LODBias;
                    auto foliageClusterKey = foliageInfo->mFoliageClusterKey;
                    auto lodSetting = foliageInfo->LODSetting;
                    if (lodCount > 0 && gameContext.mRenderWorld->IsEntityAliveInner(foliageSystem->mClusterOffsetMap[foliageClusterKey].entityID))
                    {
                        if (renderNodeMap.find(foliageInfo->RenderNode) == renderNodeMap.end())
                        {
                            UInt32 indirectArgIndexOffset = static_cast<UInt32>(indirectArgIndexData.size());
                            renderNodeMap[foliageInfo->RenderNode] = indirectArgIndexOffset;

                            drawUnits.push_back(&drawUnit);

                            auto& instancesCount = drawUnit.mInstanceCount;


                            const RenderGeometry* renderGeometry = nullptr;

                            auto& instanceInfo = foliageSystem->mClusterOffsetMap[foliageClusterKey];
                            const UInt32 instanceOffset = static_cast<UInt32>(instanceInfo.dataOffset);
                            bool isFoliage = instanceInfo.instanceDataType == InstanceDataType::Foliage;
                            UInt32 instanceStride = isFoliage ? sizeof(FoliageInstanceCompactData) : sizeof(LightInstanceData);
                            if (instanceInfo.dataDirty && !isFoliage && !instanceInfo.firstUpload)
                            {
                                // collect copy data
                                UInt32 copySize = instanceInfo.uploadInstanceCount * instanceStride;
                                copyLightBufferWrap.MemWrite(copyDataOffset, instanceInfo.instanceData, copySize);
                                copyDataOffset += copySize;
                                instanceInfo.dataDirty = false;
                            }

                            float cullenHeight = (lodSetting) ? lodSetting->mCulledHeight : 0;
                            for (UInt8 i = 0; i < lodCount; ++i)
                            {
                                UInt8 lodIdx = i;
                                lodIdx = lodIdx < lodCount ? lodIdx : lodCount - 1;
                                float screenReleativeTransitionHeight = (lodSetting && lodIdx < lodCount) ? lodSetting->mLevelSettings[lodIdx].mScreenReleativeTransitionHeight : 0;
                                renderGeometry = foliageInfo->RenderNode->GetRenderGeometry(lodIdx);   // LOD2 can be just 1 submesh
                                LODInfo lodInfo = {
                                    renderGeometry ? renderGeometry->GetIndexCount() : 0,
                                    renderGeometry ? renderGeometry->GetIndexStart() : 0,
                                    renderGeometry ? static_cast<SInt32>(renderGeometry->GetVertexStart()) : 0,
                                    screenReleativeTransitionHeight,
                                };
                                UInt32 cmdSize = static_cast<UInt32>(sizeof(LODInfo));
                                stagingBufferWrap.MemWrite(dataCmdOffset, &lodInfo, cmdSize);
                                dataCmdOffset += cmdSize;
                            }

                            {
                                DrawUnitInfo drawUnitInfo = {
                                    foliageInfo->BoundingSphere,
                                    cullenHeight,
                                    instanceInfo.maxRandomCulling,
                                    startLodOffset,
                                    lodCount,
                                    lodBias,
                                    instanceInfo.startInstance,
                                    instancesCount,
                                    indirectArgIndexOffset
                                };
                                UInt32 secSize = static_cast<UInt32>(sizeof(DrawUnitInfo));
                                drawUnitBufferWrap.MemWrite(sectionDataCmdOffset, &drawUnitInfo, secSize);
                                sectionDataCmdOffset += secSize;
                                startLodOffset += lodCount;
                            }

                            if (!lightCulling)
                            {
                                if (gameContext.mRenderPipeline->mWorld->IsEntityAliveInner(instanceInfo.entityID))
                                {
                                    if (auto renderNodeComp = gameContext.mRenderPipeline->mWorld->GetComponent<RenderNodeComponentR>(instanceInfo.entityID); renderNodeComp.IsValid())
                                    {
                                        auto renderNodeWriter = renderNodeComp.Write(ecs::ComponentAccessFlag::SubComponentAccess2);
                                        auto& worldTransform = renderNodeWriter->GetRenderNode()->GetWorldTransform();
                                        auto& relativeMatrix = worldTransform.RelativeMatrix;
                                        auto& preRelativeMatrix = worldTransform.PreRelativeMatrix;

                                        FoliageEntityData entityInfoData;
                                        entityInfoData.world = relativeMatrix;
                                        entityInfoData.preWorld = preRelativeMatrix;
                                        entityInfoData.invTransposeWorld = relativeMatrix.Inverted().Transpose();
                                        entityInfoData.tilePosition = worldTransform.TilePosition;
                                        entityInfoData.preTilePosition = worldTransform.PreTilePosition;

                                        UInt32 entitySize = static_cast<UInt32>(sizeof(FoliageEntityData));
                                        entityScratchBufferWrap.MemWrite((entitySize * instanceInfo.entityIndex), &entityInfoData, entitySize);
                                    }
                                }
                            }

                            ++indirectDrawUnitCount;
                            indirectCMDOffset += instancesCount;
                            if (isFoliage)
                            {
                                maxFoliageInstanceOffset = std::max(maxFoliageInstanceOffset, instanceOffset + instancesCount * instanceStride);
                            }
                            else
                            {
                                maxLightInstanceOffset = std::max(maxLightInstanceOffset, instanceOffset + instancesCount * instanceStride);
                            }

                            // add IndirectArgIndexData
                            indirectArgIndexData.insert(indirectArgIndexData.end(), lodCount, -1);

                            indirectArgIndexData[indirectArgIndexOffset + lodIndex] = drawUnitIndex;
                        }
                        else
                        {
                            UInt32 indirectArgIndexOffset = renderNodeMap[foliageInfo->RenderNode];

                            indirectArgIndexData[indirectArgIndexOffset + lodIndex] = drawUnitIndex;
                        }
                    }
                }
            }

            UInt32 countBufferSize = (2 * indirectDrawUnitCount + 1) * sizeof(UInt32);

            // UInt32 batchDrawUnitInfoBufferSize = indirectDrawUnitCount * sizeof(BatchDrawUnitInfo);
            // auto [batchDrawUnitInfoStagingBuffer, batchDrawUnitInfoBufferDataOffset, batchDrawUnitInfoBufferData] = rendererSystem->GetScratchBuffer()->Allocate(NGIBufferUsage::RWStructuredBuffer, batchDrawUnitInfoBufferSize);

            UInt32 indirectBufferSize = static_cast<UInt32>(indirectCMDOffset * sizeof(CompactDrawCMD));

            // use maxInstanceOffset to cover all instance data used
            if (maxFoliageInstanceOffset == 0)
                maxFoliageInstanceOffset = sizeof(FoliageInstanceCompactData);

            if (maxLightInstanceOffset == 0)
                maxLightInstanceOffset = sizeof(LightInstanceData);

            UInt32 mapDataSize = 2 * totalInstancesCount * sizeof(UInt32);

            if (lightCulling)
            {
                UInt32 lightIDsBufferSize = (totalInstancesCount + 1) * static_cast<UInt32>(sizeof(UInt32));
                tilebasedLightID->ModifySize(lightIDsBufferSize);
                mTilebasedLightIDREDView->ModifyRange(0, lightIDsBufferSize);
            }

            // resize buffer view size
            UInt32 indirectArgIndexBufferDataSize = static_cast<UInt32>(indirectArgIndexData.size() * sizeof(UInt32));

            drawUnitInfoBuffer->ModifySize(drawUnitInfoSize);
            countBuffer->ModifySize(countBufferSize);
            mapDataBuffer->ModifySize(mapDataSize);
            srcDrawBuffer->ModifySize(bufferSize);
            indirectArgIndexBuffer->ModifySize(indirectArgIndexBufferDataSize);

            drawUnitInfoBufferView->ModifyRange(0, drawUnitInfoSize);
            countBufferView->ModifyRange(0, countBufferSize);
            mapDataBufferView->ModifyRange(0, mapDataSize);
            srcDrawBufferView->ModifyRange(0, bufferSize);
            indirectArgIndexBufferView->ModifyRange(0, indirectArgIndexBufferDataSize);
            foliageInstanceView->ModifyRange(0, maxFoliageInstanceOffset);
            indirectREDBufferView->ModifyRange(0, indirectBufferSize);
            //mLightInstanceREDView->ModifyRange(0, maxLightInstanceOffset);

            RED->QueueBufferUpload(drawUnitInfoBuffer, drawUnitBufferWrap.GetNGIBuffer(), NGICopyBuffer{drawUnitBufferWrap.GetNGIOffset(), 0, drawUnitInfoSize});
            RED->QueueBufferUpload(srcDrawBuffer, stagingBufferWrap.GetNGIBuffer(), NGICopyBuffer{stagingBufferWrap.GetNGIOffset(), 0, bufferSize});
            RED->QueueBufferUpload(indirectArgIndexBuffer, 0, indirectArgIndexData.data(), indirectArgIndexBufferDataSize);
            RED->QueueBufferUpload(entityData, entityScratchBufferWrap.GetNGIBuffer(), NGICopyBuffer{entityScratchBufferWrap.GetNGIOffset(), 0, entityDataSize});

            pass->SetProperty("_DrawCMD_Count", static_cast<float>(totalInstancesCount));
            pass->SetProperty("_DrawUnitCount", static_cast<float>(indirectDrawUnitCount));

            initDrawCommandPass->SetProperty("_DrawCMD_Count", static_cast<float>(totalInstancesCount));
            initDrawCommandPass->SetProperty("_DrawUnitCount", static_cast<float>(indirectDrawUnitCount));

            // initial indirect buffer
            {
                UInt3 groupSize;
                mSetting.FoliageCullingShaderR->GetThreadGroupSize(kernelInitDrawCommandBuffer, groupSize.x, groupSize.y, groupSize.z);
                payloadInitDrawCommandBuffer->ModifyThreadGroupCount(math::DivideAndRoundUp(totalInstancesCount, groupSize.x), 1, 1);
            }

            // foliage culling
            {
                UInt3 groupSize;
                mSetting.FoliageCullingShaderR->GetThreadGroupSize(kernelVegetationCulling, groupSize.x, groupSize.y, groupSize.z);
                payloadVegetationCulling->ModifyThreadGroupCount(math::DivideAndRoundUp(totalInstancesCount, groupSize.x), 1, 1);
            }
        }
    });

    REDResourceState state{NGIResourceState::ComputeShaderShaderResource};
    if (hi_z)
    {
        foliageCullingPass->AddTextureReference(hi_z, state);
    }
    foliageCullingPass->AddBufferReference(indirectREDBuffer, REDResourceState{NGIResourceState::ComputeShaderUnorderedAccess});

    return foliageCullingPass;
}

cross::Float3 cross::FoliageGpuDriven::GetLightGridZParams(float NearPlane, float FarPlane, UInt32 gridSizeZ)
{
    // S = distribution scale
    // B, O are solved for given the z distances of the first+last slice, and the # of slices.
    //
    // slice = log2(z*B + O) * S

    // Don't spend lots of resolution right in front of the near plane
    float NearOffset = 0.f;   // .095f * 100.0f;
    // Space out the slices so they aren't all clustered at the near plane
    float S = mSetting.GridSplitS;   // 4.05f;

    float N = NearPlane + NearOffset;
    float F = FarPlane;

    float O = (F - N * std::exp2((gridSizeZ - 1) / S)) / (F - N);
    float B = (1 - O) / N;

    return cross::Float3(B, O, S);
}

void cross::FoliageGpuDriven::AssembleLightTileCulling(const GameContext& gameContext)
{
    auto targetView = gameContext.mRenderPipeline->GetTargetView();
    UInt32 width = targetView->mTexture->mDesc.Width;
    UInt32 height = targetView->mTexture->mDesc.Height;
    auto camera = gameContext.mRenderPipeline->GetRenderCamera();

    mLightGridSizeX = (width + static_cast<UInt32>(mSetting.GridPixelSize) - 1) / static_cast<UInt32>(mSetting.GridPixelSize);
    mLightGridSizeY = (height + static_cast<UInt32>(mSetting.GridPixelSize) - 1) / static_cast<UInt32>(mSetting.GridPixelSize);
    mLightGridSizeZ = static_cast<UInt32>(mSetting.GridZSizeNum);
    UInt32 lightGridNum = mLightGridSizeX * mLightGridSizeY * mLightGridSizeZ;

    // RWTexture Create
    IRenderPipeline* renderPipeline = gameContext.mRenderPipeline;
    auto RED = renderPipeline->GetRenderingExecutionDescriptor();

    auto* nextCulledLightLinkBuffer = RED->AllocateBuffer("NextCulledLightLinkBuffer", NGIBufferDesc{sizeof(UInt32), NGIBufferUsage::RWTexelBuffer});
    REDBufferView* nextCulledLightLinkBufferView = RED->AllocateBufferView(nextCulledLightLinkBuffer, NGIBufferViewDesc{NGIBufferUsage::RWTexelBuffer, 0, sizeof(UInt32), GraphicsFormat::R32_UInt, sizeof(UInt32)});
    auto* nextCulledLightDataBuffer = RED->AllocateBuffer("NextCulledLightDataBuffer", NGIBufferDesc{sizeof(UInt32), NGIBufferUsage::RWTexelBuffer});
    REDBufferView* nextCulledLightDataBufferView = RED->AllocateBufferView(nextCulledLightDataBuffer, NGIBufferViewDesc{NGIBufferUsage::RWTexelBuffer, 0, sizeof(UInt32), GraphicsFormat::R32_UInt, sizeof(UInt32)});
    auto* startOffsetGridBuffer = RED->AllocateBuffer("StartOffsetGridBuffer", NGIBufferDesc{sizeof(UInt32) * lightGridNum * 2, NGIBufferUsage::RWTexelBuffer});
    REDBufferView* startOffsetGridBufferView = RED->AllocateBufferView(startOffsetGridBuffer, NGIBufferViewDesc{NGIBufferUsage::RWTexelBuffer, 0, sizeof(UInt32) * lightGridNum * 2, GraphicsFormat::R32_UInt, sizeof(UInt32)});
    auto* culledLightLinksBuffer = RED->AllocateBuffer("CulledLightLinksBuffer", NGIBufferDesc{sizeof(UInt32) * lightGridNum * static_cast<UInt32>(mSetting.MaxCullSizePerCell), NGIBufferUsage::RWTexelBuffer});
    REDBufferView* culledLightLinksBufferView =
        RED->AllocateBufferView(culledLightLinksBuffer, NGIBufferViewDesc{NGIBufferUsage::RWTexelBuffer, 0, sizeof(UInt32) * lightGridNum * static_cast<UInt32>(mSetting.MaxCullSizePerCell), GraphicsFormat::R32_UInt, sizeof(UInt32)});
    auto* numCullBuffer = RED->AllocateBuffer("GridCullNumBuffer", NGIBufferDesc{sizeof(UInt32) * lightGridNum * 4, NGIBufferUsage::RWTexelBuffer});
    mNumCulledLightsGrid = RED->AllocateBufferView(numCullBuffer, NGIBufferViewDesc{NGIBufferUsage::RWTexelBuffer, 0, sizeof(UInt32) * lightGridNum * 4, GraphicsFormat::R32_UInt, sizeof(UInt32)});
    auto* cullDataBuffer = RED->AllocateBuffer("GridCullDataBuffer", NGIBufferDesc{sizeof(UInt32) * lightGridNum * static_cast<UInt32>(mSetting.MaxCullSizePerCell), NGIBufferUsage::RWTexelBuffer});
    mCulledLightDataGrid =
        RED->AllocateBufferView(cullDataBuffer, NGIBufferViewDesc{NGIBufferUsage::RWTexelBuffer, 0, sizeof(UInt32) * lightGridNum * static_cast<UInt32>(mSetting.MaxCullSizePerCell), GraphicsFormat::R32_UInt, sizeof(UInt32)});

    Float4x4 viewMat = camera->GetViewMatrix();
    Float4x4 projMat = camera->GetProjMatrix();
    Float4x4 invProjMat = camera->GetInvertProjMatrix();
    Float4x4 invViewMat = camera->GetInvertViewMatrix();

    Float4 viewSizeAndInvView = Float4(static_cast<float>(width), static_cast<float>(height), 1.f / static_cast<float>(width), 1.f / static_cast<float>(height));
    mLightGridPixelSizeShift = static_cast<UInt32>(std::log2(static_cast<UInt32>(mSetting.GridPixelSize)));
    mBOS = GetLightGridZParams(camera->GetNearPlane(), camera->GetFarPlane(), static_cast<UInt16>(mSetting.GridZSizeNum));

    {
        auto* passClear = RED->AllocatePass("ClearBuffer");
        passClear->SetProperty(NAME_ID("_CulledGridSizeX"), mLightGridSizeX);
        passClear->SetProperty(NAME_ID("_CulledGridSizeY"), mLightGridSizeY);
        passClear->SetProperty(NAME_ID("_CulledGridSizeZ"), mLightGridSizeZ);
        passClear->SetProperty(NAME_ID("_MaxCulledLightsPerCell"), static_cast<UInt32>(mSetting.MaxCullSizePerCell));
        passClear->SetProperty(NAME_ID("_NumGridCells"), lightGridNum);
        passClear->SetProperty(NAME_ID("RWNextCulledLightLink"), nextCulledLightLinkBufferView);
        passClear->SetProperty(NAME_ID("RWNextCulledLightData"), nextCulledLightDataBufferView);
        passClear->SetProperty(NAME_ID("RWStartOffsetGrid"), startOffsetGridBufferView);
        passClear->SetProperty(NAME_ID("RWCulledLightLinks"), culledLightLinksBufferView);
        passClear->SetProperty(NAME_ID("RWNumCulledLightsGrid"), mNumCulledLightsGrid);
        passClear->SetProperty(NAME_ID("RWCulledLightDataGrid"), mCulledLightDataGrid);

        UInt3 groupSizeClearPass;
        mSetting.LightTileCullingShaderR->GetThreadGroupSize("ClearBufferCS", groupSizeClearPass.x, groupSizeClearPass.y, groupSizeClearPass.z);
        passClear->Dispatch(mSetting.LightTileCullingShaderR,
                            "ClearBufferCS",
                            (mLightGridSizeX + groupSizeClearPass.x - 1) / groupSizeClearPass.x,
                            (mLightGridSizeY + groupSizeClearPass.y - 1) / groupSizeClearPass.y,
                            (mLightGridSizeZ + groupSizeClearPass.z - 1) / groupSizeClearPass.z);
    }

    {
        auto* pass = RED->AllocatePass("BuildGridCulling");
        pass->SetProperty(NAME_ID("_CulledGridSizeX"), mLightGridSizeX);
        pass->SetProperty(NAME_ID("_CulledGridSizeY"), mLightGridSizeY);
        pass->SetProperty(NAME_ID("_CulledGridSizeZ"), mLightGridSizeZ);
        pass->SetProperty(NAME_ID("_NumGridCells"), lightGridNum);
        pass->SetProperty(NAME_ID("_LightGridZParamsB"), mBOS.x);
        pass->SetProperty(NAME_ID("_LightGridZParamsO"), mBOS.y);
        pass->SetProperty(NAME_ID("_LightGridZParamsS"), mBOS.z);
        pass->SetProperty(NAME_ID("_ViewSizeAndInvSize"), viewSizeAndInvView);
        pass->SetProperty(NAME_ID("_LightGridPixelSizeShift"), mLightGridPixelSizeShift);
        pass->SetProperty(NAME_ID("_InvProjMat"), invProjMat);
        pass->SetProperty(NAME_ID("_InvViewMat"), invViewMat);
        pass->SetProperty(NAME_ID("_ProjMat"), projMat);
        pass->SetProperty(NAME_ID("_ViewMat"), viewMat);
        pass->SetProperty(NAME_ID("_MaxCulledLightsPerCell"), static_cast<UInt32>(mSetting.MaxCullSizePerCell));
        pass->SetProperty(NAME_ID("RWNextCulledLightLink"), nextCulledLightLinkBufferView);
        pass->SetProperty(NAME_ID("RWStartOffsetGrid"), startOffsetGridBufferView);
        pass->SetProperty(NAME_ID("RWCulledLightLinks"), culledLightLinksBufferView);

        pass->SetProperty(NAME_ID("_TilebasedLightID"), mTilebasedLightIDREDView);
        pass->SetProperty(NAME_ID("_InstanceBufferLight"), mLightInstanceREDView);

#if defined(CE_USE_DOUBLE_TRANSFORM)
        pass->SetProperty(NAME_ID("CE_USE_DOUBLE_TRANSFORM"), true);
        pass->SetProperty(NAME_ID("ce_CameraTilePosition"), camera->GetTilePosition());
#else
        pass->SetProperty(NAME_ID("CE_USE_DOUBLE_TRANSFORM"), false);
#endif
        UInt3 groupSize;
        mSetting.LightTileCullingShaderR->GetThreadGroupSize("GridCullCS", groupSize.x, groupSize.y, groupSize.z);
        pass->Dispatch(mSetting.LightTileCullingShaderR, "GridCullCS", (mLightGridSizeX + groupSize.x - 1) / groupSize.x, (mLightGridSizeY + groupSize.y - 1) / groupSize.y, (mLightGridSizeZ + groupSize.z - 1) / groupSize.z);
    }

    {
        auto* passCompact = RED->AllocatePass("CompactRevert");
        passCompact->SetProperty(NAME_ID("_CulledGridSizeX"), mLightGridSizeX);
        passCompact->SetProperty(NAME_ID("_CulledGridSizeY"), mLightGridSizeY);
        passCompact->SetProperty(NAME_ID("_CulledGridSizeZ"), mLightGridSizeZ);
        passCompact->SetProperty(NAME_ID("_NumGridCells"), lightGridNum);
        passCompact->SetProperty(NAME_ID("RWNextCulledLightData"), nextCulledLightDataBufferView);
        passCompact->SetProperty(NAME_ID("RWStartOffsetGrid"), startOffsetGridBufferView);
        passCompact->SetProperty(NAME_ID("RWCulledLightLinks"), culledLightLinksBufferView);
        passCompact->SetProperty(NAME_ID("RWNumCulledLightsGrid"), mNumCulledLightsGrid);
        passCompact->SetProperty(NAME_ID("RWCulledLightDataGrid"), mCulledLightDataGrid);
        passCompact->SetProperty(NAME_ID("_TilebasedLightID"), mTilebasedLightIDREDView);
        UInt3 groupSizeCompactPass;
        mSetting.LightTileCullingShaderR->GetThreadGroupSize("GridCompactCS", groupSizeCompactPass.x, groupSizeCompactPass.y, groupSizeCompactPass.z);
        passCompact->Dispatch(mSetting.LightTileCullingShaderR,
                              "GridCompactCS",
                              (mLightGridSizeX + groupSizeCompactPass.x - 1) / groupSizeCompactPass.x,
                              (mLightGridSizeY + groupSizeCompactPass.y - 1) / groupSizeCompactPass.y,
                              (mLightGridSizeZ + groupSizeCompactPass.z - 1) / groupSizeCompactPass.z);
    }
}

void cross::FoliageGpuDriven::AssmbleTiledbaseShading(const GameContext& gameContext, const ShadowProperties* shadowData, REDTextureView* ShadowMaskView, std::array<REDTextureView*, 4> gBufferView, REDTextureView* depthStencilView,
                    REDTextureView* depthOnlyView, REDTextureView* contactShadowView, REDTextureView* SceneColor,
                    bool contactShadowTransmissionEnable, REDTextureView* depthPyramid, REDTextureView* GPassDepthPyramid, ViewModeVisualizeType visulaize, std::function<void(REDPass *)> contextCallback)
{

    FFSRenderPipeline* pipeline = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
    auto* skySys = gameContext.mRenderWorld->GetRenderSystem<SkyAtmosphereSystemR>();
    auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* rdrPrim = rdrSys->GetRenderPrimitives();

    bool multiPassesForVisualize = true;
    
    REDColorTargetDesc LightingPassColorTargets[]{{
        SceneColor,
        NGILoadOp::Load,
        NGIStoreOp::Store,
        NGIClearValue{0, 0, 0, 0},
    }};

    REDDepthStencilTargetDesc depthStencilTarget{
        depthStencilView,
        NGILoadOp::Load,
        NGIStoreOp::Store,
        NGILoadOp::Load,
        NGIStoreOp::Store,
    };

    auto RED = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();

    RED->BeginRenderPass("TilebasedLightingPass", static_cast<UInt32>(ArrayCount(LightingPassColorTargets)), LightingPassColorTargets, &depthStencilTarget);
    {
        NGIRenderPassTargetIndex colorTargets[]{NGIRenderPassTargetIndex::Target0};
        auto* lightingPass =
            RED->AllocateSubRenderPass("TilebasedLightingPass", 0, nullptr, static_cast<UInt32>(ArrayCount(colorTargets)), colorTargets, REDPassFlagBit::NeedStencil | REDPassFlagBit::NeedDepth | REDPassFlagBit::DepthReadOnly);

        REDTextureView* depthPyramid1 = depthPyramid;
        {
            depthPyramid1 = GPassDepthPyramid;
        }

        auto& subSampleShadingSetting = pipeline->GetSetting()->mSubSampleShadingSettings;
        const bool enableMSAA = pipeline->IsRenderPipelineSuportMSAA() && subSampleShadingSetting.enable;
        bool enableAGAA = enableMSAA && subSampleShadingSetting.EnableAGAA;
        SubSampleShading& SubSpamleShadingData = pipeline->GetSubSampleShading();
        // Properties
        lightingPass->SetProperty(NAME_ID("_HiZBuffer"), depthPyramid1);
        lightingPass->SetProperty(NAME_ID("_GBuffer0"), gBufferView[0], NGIResourceState::PixelShaderShaderResource);
        lightingPass->SetProperty(NAME_ID("_GBuffer1"), gBufferView[1], NGIResourceState::PixelShaderShaderResource);
        lightingPass->SetProperty(NAME_ID("_GBuffer2"), gBufferView[2], NGIResourceState::PixelShaderShaderResource);
        lightingPass->SetProperty(NAME_ID("_GBuffer3"), gBufferView[3], NGIResourceState::PixelShaderShaderResource);
        lightingPass->SetProperty(NAME_ID("_DepthMap"), depthOnlyView);
        if (enableAGAA)
        {
            auto aggregateDepthView = SubSpamleShadingData.GetSecondAggregateDepth();
            auto aggregateCoverageView = SubSpamleShadingData.GetAggregateCoverageView();
            auto secondAggregateGbuffer = SubSpamleShadingData.GetSecondAggregateGBuffer();
            lightingPass->SetProperty(NAME_ID("_SecondaryGBuffer0"), SubSpamleShadingData.IsEnableAggregatedBaseColor() ? secondAggregateGbuffer[0] : gBufferView[0], NGIResourceState::PixelShaderShaderResource);
            lightingPass->SetProperty(NAME_ID("_SecondaryGBuffer1"), secondAggregateGbuffer[1], NGIResourceState::PixelShaderShaderResource);
            lightingPass->SetProperty(NAME_ID("_SecondaryGBuffer2"), secondAggregateGbuffer[2], NGIResourceState::PixelShaderShaderResource);
            lightingPass->SetProperty(NAME_ID("_SecondAggregateCoverage"), aggregateCoverageView, NGIResourceState::PixelShaderShaderResource);
            lightingPass->SetProperty(NAME_ID("_SecondaryDepth"), aggregateDepthView, NGIResourceState::PixelShaderShaderResource);
        }
        auto& lightingMat = enableAGAA ? mSetting.DeferredShadingAGAAMtlR : mSetting.DeferredShadingMtlR;

        if (contactShadowView != nullptr)
        {
            lightingPass->SetProperty(NAME_ID("_ContactShadowMap"), contactShadowView, NGIResourceState::PixelShaderShaderResource);
        }
        else
        {
            lightingPass->SetProperty(NAME_ID("_ContactShadowMap"), rdrPrim->mDefaultUIntTexture2DView.get());
        }
        if (ShadowMaskView != nullptr)
        {
            lightingPass->SetProperty(NAME_ID("_ShadowMaskMap"), ShadowMaskView, NGIResourceState::PixelShaderShaderResource);
        }

        UpdateShadowContext(lightingPass, gameContext, shadowData);
        gameContext.mRenderPipeline->UpdateCloudShadowContext(lightingPass);

        contextCallback(lightingPass);

        lightingPass->SetProperty(NAME_ID("VISUALIZE"), static_cast<float>(visulaize));
        lightingPass->SetProperty(NAME_ID("ENABLE_CONTACT_SHADOW_TRANSMISSION"), contactShadowTransmissionEnable);
        lightingPass->SetProperty(NAME_ID("USE_SIMPLE_SPOT"), mSetting.SimpleSpotLight);

        // Draw
        if (multiPassesForVisualize)
        {
            // DirectionalLight
            const NameID stencilDeferredLighingBackPassID = "StencilDeferredLightingBack";
            auto* lightSystemR = TYPE_CAST(const LightSystemR*, gameContext.mRenderWorld->GetRenderSystem<LightSystemR>());
            auto* transformSystemR = TYPE_CAST(const TransformSystemR*, gameContext.mRenderWorld->GetRenderSystem<TransformSystemR>());
            //auto& LightLists = pipeline->GetDirectionalLightList();
            auto& LightLists = pipeline->GetLightList();
            for (UInt32 lightIdx = 0; lightIdx < LightLists.size(); ++lightIdx)
            {
                auto light = (LightLists[lightIdx]);
                auto [lightComp, transComp, tilePositionComponent] = gameContext.mRenderWorld->GetComponent<LightComponentR, TransformComponentR, TilePositionComponentR>(light);
                auto lightType = lightSystemR->GetLightType(lightComp.Read());

                if (lightType == LightType::Directional)
                {
                    Float4 lightDirPos;
                    Float4 lightTilePos;
                    Float4 lightSpotDir;
                    Float4 lightAttenuation;
                    lightDirPos = transformSystemR->GetWorldRotation(transComp.Read()).Float4Rotate(Float4(0, 0, 1, 0));
                    lightDirPos.w = 0.0f;
                    lightTilePos = Float4(transformSystemR->GetTilePosition(tilePositionComponent.Read()), 0.0f);
                    lightAttenuation = Float4{0.0f, 0.0f, 0.00000001f, 100000000.0f};
                    
                    Float4 lightColor = Float4(lightSystemR->GetLightColor(lightComp.Read()) * lightSystemR->GetLightIntensity(lightComp.Read()), 0.0f);
                    if (lightSystemR->GetEnableTransmittance(lightComp.Read()))
                    {
                        Float3 trans = skySys->GetTransmittanceTowardsSunAtRenderCamera(-lightDirPos.XYZ(), gameContext.mRenderCamera);
                        lightColor *= Float4(trans, 1);
                    }

                    PropertySet objProps{RED->GetREDFrameAllocator()};

                    objProps.SetProperty(BuiltInProperty::ce_LightIndex, static_cast<float>(lightIdx));
                    objProps.SetProperty(NAME_ID("ce_ShadowDataIndex"), lightSystemR->GetLightShadowDataIndex(lightComp.Read()));
                    objProps.SetProperty(NAME_ID("ce_SingleLightDirPos"), lightDirPos);
                    objProps.SetProperty(NAME_ID("ce_SingleLightTilePos"), lightTilePos);
                    objProps.SetProperty(NAME_ID("ce_SingleLightAttenuation"), lightAttenuation);
                    objProps.SetProperty(NAME_ID("ce_SingleLightColor"), lightColor);
                    objProps.SetProperty(NAME_ID("ce_SingleLightSpotDirection"), lightSpotDir);
                    objProps.SetProperty(NAME_ID("SKY_LIGHT"), (lightIdx == 0));
                    objProps.SetProperty(NAME_ID("DIRECTION_LIGHT"), true);
                    objProps.SetProperty(NAME_ID("POINT_LIGHT"), false);
                    objProps.SetProperty(NAME_ID("SPOT_LIGHT"), false);

                    REDDrawScreenQuad lightingDrawInfo{
                        lightingMat,
                        stencilDeferredLighingBackPassID,
                        std::nullopt,
                        std::get<0>(mDirectionalLightBackLightingStateOverride),
                        std::get<1>(mDirectionalLightBackLightingStateOverride),
                        std::nullopt,
                        std::move(objProps),
                    };
                    lightingPass->DrawScreenQuad(lightingDrawInfo);
                }
            }
            // LocalLight
            lightingPass->SetProperty(NAME_ID("_LightGridZParamsB"), mBOS.x);
            lightingPass->SetProperty(NAME_ID("_LightGridZParamsO"), mBOS.y);
            lightingPass->SetProperty(NAME_ID("_LightGridZParamsS"), mBOS.z);
            lightingPass->SetProperty(NAME_ID("_CulledGridSizeX"), mLightGridSizeX);
            lightingPass->SetProperty(NAME_ID("_CulledGridSizeY"), mLightGridSizeY);
            lightingPass->SetProperty(NAME_ID("_CulledGridSizeZ"), mLightGridSizeZ);
            lightingPass->SetProperty(NAME_ID("_LightGridPixelSizeShift"), mLightGridPixelSizeShift);
            //lightingPass->SetProperty(NAME_ID("_NumLocalLights"), mLocalLightsNum);
            lightingPass->SetProperty(NAME_ID("_NumCulledLightsGrid"), mNumCulledLightsGrid, NGIResourceState::PixelShaderUnorderedAccess);
            lightingPass->SetProperty(NAME_ID("_CulledLightsDataGrid"), mCulledLightDataGrid, NGIResourceState::PixelShaderUnorderedAccess);
            lightingPass->SetProperty(NAME_ID("_TilebasedLightID"), mTilebasedLightIDREDView);
            lightingPass->SetProperty(NAME_ID("_InstanceBufferLight"), mLightInstanceREDView);

            REDDrawScreenQuad localLightingDrawInfo{
                mSetting.TiledbaseShadingMtlR,
                tileBasedDeferredLighingBackPassID,
                std::nullopt,
                std::get<0>(mDirectionalLightBackLightingStateOverride),
                std::get<1>(mDirectionalLightBackLightingStateOverride),
            };
            lightingPass->DrawScreenQuad(localLightingDrawInfo);
        }   // MultiPasses
        //else
        //{
        //    lightingPass->SetProperty("DIRECTION_LIGHT", true);
        //    lightingPass->SetProperty("POINT_LIGHT", false);
        //    lightingPass->SetProperty("SPOT_LIGHT", false);

        //    REDDrawScreenQuad lightingDrawInfo{
        //        mStencilDeferredMat,
        //        stencilDeferredLighingBackPassID,
        //        std::nullopt,
        //        std::get<0>(mDirectionalLightBackLightingStateOverride),
        //        std::get<1>(mDirectionalLightBackLightingStateOverride),
        //    };
        //    lightingPass->DrawScreenQuad(lightingDrawInfo);
        //}   // SinglePassForVisualize
    }
    RED->EndRenderPass();
}

void cross::FoliageGpuDriven::AssembleGPUDrivenLight(const GameContext& gameContext, const std::array<REDTextureView*, 4>& gBufferViews,
                                                     REDTextureView* depthStencilView, REDTextureView* depthOnlyView, REDTextureView* sceneColorView, REDTextureView* depthPyramid, REDTextureView* GPassDepthPyramid,
                                                     REDBufferView* clustersBufferView, ViewModeVisualizeType visulaize, std::function<void(REDPass*)> contextCallback, std::function<void()> rayDebugCallback)
{
    FFSRenderPipeline* pipeline = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
    auto pipelineSetting = pipeline->GetSetting();
    auto RED = pipeline->GetRenderingExecutionDescriptor();
    if (!pipelineSetting->GPUDrivenLight || !mSetting.enable)
        return;

    NameID tagName = "GPULightingPass";
    RED->BeginRegion(tagName.GetName());

    auto* renderCamera = pipeline->GetRenderCamera();
    auto* cullingResult = RED->Cull(REDCullingDesc{gameContext.mRenderWorld, const_cast<RenderCamera*>(renderCamera)});
    auto* drawUnitList = cullingResult->GenerateDrawUnitList(REDDrawUnitsDesc{tagName, gRenderGroupTransparent, gRenderGroupUI - 2});

    auto [gpuLightPass, objectIndexBufferView] = AssembleCommonPass(gameContext, drawUnitList, "GPULightCulling", tagName);

    REDColorTargetDesc LightingPassColorTargets[]{{
        sceneColorView,
        NGILoadOp::Load,
        NGIStoreOp::Store,
        NGIClearValue{0, 0, 0, 0},
    }};

    REDDepthStencilTargetDesc depthStencilTarget{
        depthStencilView,
        NGILoadOp::Load,
        NGIStoreOp::Store,
        NGILoadOp::Load,
        NGIStoreOp::Store,
    };

    RED->BeginRenderPass("GPULightingPass", static_cast<UInt32>(ArrayCount(LightingPassColorTargets)), LightingPassColorTargets, &depthStencilTarget);
    {
        NGIRenderPassTargetIndex colorTargets[]{NGIRenderPassTargetIndex::Target0};
        auto* lightingPass = RED->AllocateSubRenderPass("GPULightingPass", 0, nullptr, static_cast<UInt32>(ArrayCount(colorTargets)), colorTargets, REDPassFlagBit::NeedStencil | REDPassFlagBit::NeedDepth | REDPassFlagBit::DepthReadOnly);

        lightingPass->SetProperty(NAME_ID("_GBuffer0"), gBufferViews[0], NGIResourceState::PixelShaderShaderResource);
        lightingPass->SetProperty(NAME_ID("_GBuffer1"), gBufferViews[1], NGIResourceState::PixelShaderShaderResource);
        lightingPass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2], NGIResourceState::PixelShaderShaderResource);
        lightingPass->SetProperty(NAME_ID("_GBuffer3"), gBufferViews[3], NGIResourceState::PixelShaderShaderResource);
        lightingPass->SetProperty(NAME_ID("_DepthMap"), depthOnlyView);
        lightingPass->SetProperty(NAME_ID("USE_SIMPLE_SPOT"), mSetting.SimpleSpotLight);
        lightingPass->SetProperty(NAME_ID("_ObjectIndexBuffer"), objectIndexBufferView);

        if (contextCallback)
            contextCallback(lightingPass);

        lightingPass->SetProperty(BuiltInProperty::CE_INSTANCING, true);

        if (gpuLightPass)
        {
            SetRenderPass(gpuLightPass, lightingPass);
        }

        REDRenderDrawUnitsDesc renderDrawUnitDesc{
            drawUnitList,
            std::nullopt,
            std::nullopt,
            std::nullopt,
            std::nullopt,
            std::nullopt,
            std::nullopt,
            true,
        };
        lightingPass->RenderDrawUnits(renderDrawUnitDesc);
    }
    rayDebugCallback();
    RED->EndRenderPass();

    RED->EndRegion();
}

std::tuple<cross::REDPass*, cross::REDBufferView*> cross::FoliageGpuDriven::AssembleCommonPass(const GameContext& gameContext, REDDrawUnitList* drawUnitList, const NameID& passName, const NameID& tagName)
{
    FFSRenderPipeline* pipeline = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
    auto pipelineSetting = pipeline->GetSetting();
    auto RED = pipeline->GetRenderingExecutionDescriptor();
    auto* renderCamera = pipeline->GetRenderCamera();

    CREATE_RED_BUFFER_VIEW(objectIndexBuffer, RED, "ObjectIndexBuffer", 1, NGIBufferUsage::RWStructuredBuffer, sizeof(UInt32))
    CREATE_RED_BUFFER_VIEW(visibleObjectCommandsBuffer, RED, "VisibleObjectCommandsBuffer", 1, NGIBufferUsage::RWStructuredBuffer, sizeof(VisibleInstance))
    CREATE_RED_BUFFER_VIEW(visibleObjectCommandCountBuffer, RED, "VisibleObjectCommandCountBuffer", sizeof(UInt32), NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::CopyDst, sizeof(UInt32))
    CREATE_RED_BUFFER_VIEW(drawIndirectArgsBuffer, RED, "DrawIndirectBuffer", 1, NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::IndirectBuffer | NGIBufferUsage::CopyDst, sizeof(UInt32))
    CREATE_RED_BUFFER_VIEW(objectIndexOffsetBuffer, RED, "ObjectIndexOffsetBuffer", 1, NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::VertexBuffer, sizeof(UInt32))
    CREATE_RED_BUFFER_VIEW(tempObjectIndexOffsetBuffer, RED, "TempObjectIndexOffsetBuffer", 1, NGIBufferUsage::RWStructuredBuffer, sizeof(UInt32))
    CREATE_RED_BUFFER_VIEW(offsetBufferCountBuffer, RED, "OffsetBufferCountBuffer", sizeof(UInt32), NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::CopyDst, sizeof(UInt32))
    CREATE_RED_BUFFER_VIEW(outputPassIndirectArgs, RED, "OutputPassIndirectArgs", 3 * sizeof(UInt32), NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::IndirectBuffer, sizeof(UInt32))

    UInt32 value = 0;
    RED->QueueBufferUpload(visibleObjectCommandCountBuffer, 0, &value, sizeof(UInt32));
    RED->QueueBufferUpload(offsetBufferCountBuffer, 0, &value, sizeof(UInt32));

    drawUnitList->SetIndirectBuffer(drawIndirectArgsBuffer);
    drawUnitList->SetObjectIndexOffsetsBuffer(objectIndexOffsetBuffer);

    std::shared_ptr<UInt32> visibleObjectCommandBufferMaxNum;
    visibleObjectCommandBufferMaxNum.reset(new UInt32(0));

    REDPass* cullingPass = nullptr;
    {
        auto clearPassName = NameID("ClearBuffer");
        auto* clearPass = RED->AllocatePass(clearPassName.GetName());
        clearPass->SetProperty(NAME_ID("_OutVisibleObjectCommands"), visibleObjectCommandsBufferView);
        auto* computeExecutionPayload = clearPass->Dispatch(mSetting.ClearBufferShaderR, clearPassName, 0, 0, 0);

        clearPass->OnCulling([=](REDPass* pass) {
            auto* drawUnits = drawUnitList->GetDrawUnits();

            // VisibleObjectsBuffer (for all DrawUnits)
            std::unordered_set<const void*> renderNodeMap;

            UInt32 maxObjectsPerPass = 0;
            for (UInt32 drawUnitIndex = 0; drawUnitIndex < drawUnits->GetSize(); drawUnitIndex++)
            {
                const REDDrawUnit& drawUnit = drawUnits->At(drawUnitIndex);

                if (drawUnit.mType == RenderNodeType::Foliage)
                {
                    auto* foliageInfo = reinterpret_cast<const FoliageInfo*>(drawUnit.mCustumData);
                    auto* renderNode = foliageInfo->RenderNode;

                    if (renderNodeMap.find(renderNode) == renderNodeMap.end())
                    {
                        renderNodeMap.insert(renderNode);

                        maxObjectsPerPass += drawUnit.mInstanceCount;
                    }
                }
                else
                {
                    maxObjectsPerPass += drawUnit.mIndirectCount;
                }
            }
            *visibleObjectCommandBufferMaxNum = maxObjectsPerPass;

            UInt32 visibleObjectsBufferDataSize = static_cast<UInt32>(maxObjectsPerPass * sizeof(VisibleInstance));
            UInt32 visibleObjectsBufferSize = std::max(visibleObjectsBufferDataSize, 1u);
            visibleObjectCommandsBuffer->ModifySize(visibleObjectsBufferSize);
            visibleObjectCommandsBufferView->ModifyRange(0, visibleObjectsBufferSize);

            // IndirectBuffer (for all DrawUnits)
            std::vector<CompactDrawCMD> indirectDrawCommands;
            indirectDrawCommands.resize(drawUnits->GetSize());

            for (UInt32 index = 0; index < drawUnits->GetSize(); index++)
            {
                const REDDrawUnit& drawUnit = (*drawUnits)[index];
                const RenderGeometry* geometry = drawUnit.mGeometry;

                indirectDrawCommands[index] = CompactDrawCMD{
                    geometry->GetIndexCount(),
                    0u,
                    geometry->GetIndexStart(),
                    static_cast<SInt32>(geometry->GetVertexStart()),
                    0,
                };

                drawUnit.mIndirectBufferOffset = index * sizeof(CompactDrawCMD);
                drawUnit.mIndirectCount = 1u;
                drawUnit.mIndirectStride = sizeof(CompactDrawCMD);
            }

            UInt32 indirectBufferDataSize = static_cast<UInt32>(drawUnits->GetSize() * sizeof(CompactDrawCMD));
            UInt32 indirectBufferSize = std::max(indirectBufferDataSize, 1u);
            drawIndirectArgsBuffer->ModifySize(indirectBufferSize);
            drawIndirectArgsBufferView->ModifyRange(0, indirectBufferSize);

            RED->QueueBufferUpload(drawIndirectArgsBuffer, 0, indirectDrawCommands.data(), indirectBufferDataSize);

            // ObjectIndexOffsetBuffer
            UInt32 objectIndexOffsetBufferSize = std::max(static_cast<UInt32>(drawUnits->GetSize() * sizeof(UInt32)), 1u);
            objectIndexOffsetBuffer->ModifySize(objectIndexOffsetBufferSize);
            objectIndexOffsetBufferView->ModifyRange(0, objectIndexOffsetBufferSize);

            // TempObjectIndexOffsetBuffer
            tempObjectIndexOffsetBuffer->ModifySize(objectIndexOffsetBufferSize);
            tempObjectIndexOffsetBufferView->ModifyRange(0, objectIndexOffsetBufferSize);

            // ObjectIndexBuffer
            UInt32 objectIndexBufferSize = std::max(static_cast<UInt32>(maxObjectsPerPass * sizeof(UInt32)), 1u);
            objectIndexBuffer->ModifySize(objectIndexBufferSize);
            objectIndexBufferView->ModifyRange(0, objectIndexBufferSize);

            // Dispatch
            pass->SetProperty(NAME_ID("_VisibleObjectCommandBufferMaxNum"), maxObjectsPerPass);

            UInt3 groupSize;
            mSetting.ClearBufferShaderR->GetThreadGroupSize(clearPassName, groupSize.x, groupSize.y, groupSize.z);
            computeExecutionPayload->ModifyThreadGroupCount(math::DivideAndRoundUp(maxObjectsPerPass, groupSize.x), 1, 1);
        });

        if (mSetting.enable)
        {
            cullingPass = AssembleCullingPass(gameContext, passName, tagName, renderCamera, pipeline->GetDepthPyramid(), drawUnitList, visibleObjectCommandsBufferView, visibleObjectCommandCountBufferView);
        }
    }

    // AllocateCommandInstanceOutputSpace
    {
        auto* pass = RED->AllocatePass("AllocateCommandInstanceOutputSpace");
        {
            pass->SetProperty(NAME_ID("_DrawIndirectArgs"), drawIndirectArgsBufferView);
            pass->SetProperty(NAME_ID("_OutOffsetBufferCount"), offsetBufferCountBufferView);
            pass->SetProperty(NAME_ID("_OutObjectIndexOffsetBuffer"), objectIndexOffsetBufferView);
            pass->SetProperty(NAME_ID("_OutTempObjectIndexOffsetBuffer"), tempObjectIndexOffsetBufferView);
            auto* computeExecutionPayload = pass->Dispatch(pipelineSetting->InstanceCullingComputeShaderR, NameID("AllocateCommandInstanceOutputSpace"), 0, 0, 0);

            pass->OnCulling([=](REDPass* pass) {
                auto* drawUnits = drawUnitList->GetDrawUnits();

                pass->SetProperty("_IndirectArgCount", drawUnits->GetSize());

                UInt3 groupSize;
                pipelineSetting->InstanceCullingComputeShaderR->GetThreadGroupSize("AllocateCommandInstanceOutputSpace", groupSize.x, groupSize.y, groupSize.z);
                computeExecutionPayload->ModifyThreadGroupCount(math::DivideAndRoundUp(drawUnits->GetSize(), groupSize.x), 1, 1);
            });
        }
    }

    // InitIndirectArgs1D
    {
        UInt3 groupSize;
        pipelineSetting->InstanceCullingComputeShaderR->GetThreadGroupSize("OutputCommandInstanceLists", groupSize.x, groupSize.y, groupSize.z);

        auto* pass = RED->AllocatePass("InitIndirectArgs1D");
        {
            pass->SetProperty(NAME_ID("_UIntParams0"), UInt4(1, groupSize.x, 0, 0));
            pass->SetProperty(NAME_ID("_InputCountBuffer"), visibleObjectCommandCountBufferView);
            pass->SetProperty(NAME_ID("_OutIndirectDispatchArgs"), outputPassIndirectArgsView);
            pass->Dispatch(pipelineSetting->mVirtualShadowMapSettings.InitIndirectArgs1DR, "InitIndirectArgs1D", 1, 1, 1);
        }
    }

    // OutputCommandInstanceLists
    {
        auto* pass = RED->AllocatePass("OutputCommandInstanceLists");
        {
            pass->SetProperty(NAME_ID("_VisibleObjectCommands"), visibleObjectCommandsBufferView);
            pass->SetProperty(NAME_ID("_VisibleObjectCommandCount"), visibleObjectCommandCountBufferView);
            pass->SetProperty(NAME_ID("_OutTempObjectIndexOffsetBuffer"), tempObjectIndexOffsetBufferView);
            pass->SetProperty(NAME_ID("_OutObjectIndexBuffer"), objectIndexBufferView);
            pass->DispatchIndirect(pipelineSetting->InstanceCullingComputeShaderR, NameID("OutputCommandInstanceLists"), outputPassIndirectArgs, 0);
        }
    }

    return std::make_tuple(cullingPass, objectIndexBufferView);
}

void cross::FoliageGpuDriven::UpdateShadowContext(cross::REDPass* lightingPass, const GameContext& gameContext, const ShadowProperties* shadowProperties)
{
    auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto pipelineSetting = gameContext.mRenderPipeline->GetSetting();
    auto localLightShadowCacheSetting = gameContext.mRenderPipeline->GetLocalLightShadowCacheSetting();
    if (localLightShadowCacheSetting && localLightShadowCacheSetting->enable)
    {
        lightingPass->SetProperty(NAME_ID("USE_CACHED_LOCAL_LIGHT_SHADOW"), true);
        if (shadowProperties->cachedSpotShadowMapsView)
        {
            lightingPass->SetProperty(NAME_ID("_LocalLightMultiTargetShadowMap"), shadowProperties->cachedSpotShadowMapsView, NGIResourceState::PixelShaderShaderResource);
            float width = static_cast<float>(shadowProperties->cachedSpotShadowMapsView->GetWidth());
            float height = static_cast<float>(shadowProperties->cachedSpotShadowMapsView->GetHeight());
            lightingPass->SetProperty(NAME_ID("_LocalLightShadowMapSize"), Float4(width, height, 1.f / width, 1.f / height));
        }   
    }

    // Set structured buffers property
    {
        NGIBufferView* shadowDataView;
        NGIBufferView* shadowMatricesView;
        NGIBufferView* spotShadowMapRangeView;
        NGIBufferView* pointShadowMapRangeView;

        auto CreateEmptyBuffer = [&](NGIBufferView*& dataView, SizeType byteSize) {
            std::vector<UInt32> Datas(byteSize, 0);
            auto* scratchBuffer = rdrSys->GetScratchBuffer();
            auto shadowDataBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, byteSize);
            shadowDataBufferWrap.MemWrite(0, Datas.data(), byteSize);
            dataView = rdrSys->GetTransientResourceManager()->AllocateBufferView(
                NGIBufferViewDesc{
                    NGIBufferUsage::StructuredBuffer,
                    shadowDataBufferWrap.GetNGIOffset(),
                    byteSize,
                    GraphicsFormat::Unknown,
                    byteSize,
                },
                shadowDataBufferWrap.GetNGIBuffer());
        };

        if (shadowProperties->shadowMatricesBufferView)
        {
            lightingPass->SetProperty(NAME_ID("_ShadowMatrices"), shadowProperties->shadowMatricesBufferView);
        }
        else
        {
            CreateEmptyBuffer(shadowMatricesView, sizeof(Float4x4));
            lightingPass->SetProperty(NAME_ID("_ShadowMatrices"), shadowMatricesView);
        }

        if (shadowProperties->cachedSpotShadowMapRangesView)
        {
            lightingPass->SetProperty(NAME_ID("_SpotLightShadowRanges"), shadowProperties->cachedSpotShadowMapRangesView);
        }
        else
        {
            CreateEmptyBuffer(spotShadowMapRangeView, sizeof(SubMapInfo));
            lightingPass->SetProperty(NAME_ID("_SpotLightShadowRanges"), spotShadowMapRangeView);
        }

        if (shadowProperties->cachedPointShadowMapRangesView)
        {
            lightingPass->SetProperty(NAME_ID("_PointLightShadowRanges"), shadowProperties->cachedPointShadowMapRangesView);
        }
        else
        {
            CreateEmptyBuffer(pointShadowMapRangeView, sizeof(SubMapInfo));
            lightingPass->SetProperty(NAME_ID("_PointLightShadowRanges"), pointShadowMapRangeView);
        }

        if (shadowProperties->shadowDatasBufferView)
        {
            lightingPass->SetProperty(NAME_ID("_ShadowDatas"), shadowProperties->shadowDatasBufferView);
        }
        else
        {
            CreateEmptyBuffer(shadowDataView, 44);
            lightingPass->SetProperty(NAME_ID("_ShadowDatas"), shadowDataView);
        }

        lightingPass->SetProperty(NAME_ID("ENABLE_SHADOW_MAP"), shadowProperties->shadowDatasBufferView != nullptr);
    }
}