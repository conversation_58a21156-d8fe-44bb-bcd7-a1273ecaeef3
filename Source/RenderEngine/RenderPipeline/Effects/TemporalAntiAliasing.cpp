#include "TemporalAntiAliasing.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include <RenderEngine/RenderPipeline/FFSRenderPipeline.h>

namespace cross
{
    void TemporalAntiAliasingInput::GenerateInputData(const GameContext& gameContext) 
    {
        color = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastPassColor>();
        depth = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DepthMapAfterGPassView>();
        auto gbuffer = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::GBufferViews>();
        vbuffer = gbuffer[3];
        eyeadaptation = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastFrameEyeAdpatationTex>();
    }

    void TemporalAntiAliasingOutput::SetOutputData(const GameContext& gameContext) 
    {
        gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastPassColor>() = outputcolor;
    }

    void TemporalAntiAliasing::FillInput(const GameContext& gameContext) 
    {
        auto ffspipeline = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
        if (!ffspipeline)
            return;
        mSetting = ffspipeline->GetSetting()->mTemporalAntiAliasingSetting;
        mContext = &ffspipeline->mTemporalAntiAliasingContext;
        mInput.GenerateInputData(gameContext);
        mSceneColorTextureWidth = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DisplayColor>()->mTexture->mDesc.Width;
        mSceneColorTextureHeight = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DisplayColor>()->mTexture->mDesc.Height;
        mDepthStencilFormat = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DisplayDepthStencil>()->mDesc.Format;
        mPostProcessMaterial = gameContext.mRenderPipeline->GetPostProcessMtl();
    }

    void TemporalAntiAliasing::Execute(const GameContext& gameContext)
    {
        QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
        if (mSetting.enable)
        {
            auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();

            const NGITextureViewDesc historyBufferViewDesc{NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource,
                                                           GraphicsFormat::R16G16B16A16_SFloat,
                                                           NGITextureType::Texture2D,
                                                           {
                                                               NGITextureAspect::Color,
                                                               0U,
                                                               1U,
                                                               0U,
                                                               1U,
                                                           }};

            const NGITextureDesc historyBufferDesc{
                GraphicsFormat::R16G16B16A16_SFloat,
                NGITextureType::Texture2D,
                1U,
                1U,
                mSceneColorTextureWidth,
                mSceneColorTextureHeight,
                1U,
                1U,
                NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource,
            };

            REDTextureView* historyBufferView = mInput.color;
            if (red->Validate(mContext->mHistoryBuffer))
            {
                if (mContext->mHistoryBuffer->mDesc.Width != mSceneColorTextureWidth || mContext->mHistoryBuffer->mDesc.Height != mSceneColorTextureHeight)
                {
                    historyBufferView = mInput.color;
                }
                else
                {
                    historyBufferView = red->AllocateTextureView(mContext->mHistoryBuffer, historyBufferViewDesc);
                }
            }

           mContext->mHistoryBuffer = red->AllocateTexture("TAAHistoryBuffer", historyBufferDesc);
            mContext->mHistoryBufferRTView = red->AllocateTextureView(mContext->mHistoryBuffer, historyBufferViewDesc);
           mContext->mHistoryBuffer->ExtendLifetime();

            const auto jitterData = gameContext.mRenderPipeline->GetJitterData();
            auto reprojectionMat = jitterData->GetReprojectionMatrixNoJitter();

            gameContext.mRenderPipeline->PostProcess(
                [&](auto pass) {
                    pass->SetProperty(NAME_ID("SceneColor"), mInput.color, NGIResourceState::PixelShaderShaderResource);
                    pass->SetProperty(NAME_ID("DepthMap"), mInput.depth, NGIResourceState::PixelShaderShaderResource);
                    pass->SetProperty(NAME_ID("VelocityBuffer"), mInput.vbuffer, NGIResourceState::PixelShaderShaderResource);
                    pass->SetProperty(NAME_ID("HistoryBuffer"), historyBufferView, NGIResourceState::PixelShaderShaderResource);

                    if (mInput.eyeadaptation)
                    {
                        pass->SetProperty(NAME_ID("ENABLE_AUTO_EXPOSURE"), true);
                        pass->SetProperty(NAME_ID("ExposureTexture"), mInput.eyeadaptation, NGIResourceState::PixelShaderShaderResource);
                    }
                    else
                    {
                        pass->SetProperty(NAME_ID("ENABLE_AUTO_EXPOSURE"), false);
                    }

                    if (mSetting.WithoutMotionVector)
                    {
                        pass->SetProperty(NAME_ID("MOTION_VECTOR"), false);
                    }
                    else
                    {
                        pass->SetProperty(NAME_ID("MOTION_VECTOR"), true);
                    }

                    pass->SetProperty(NAME_ID("ReprojectionMat"), reprojectionMat);
                    pass->SetProperty(NAME_ID("JitterOffset"), jitterData->GetLastOffsetInUVSpace());
                    pass->SetProperty(NAME_ID("FrameTime"), EngineGlobal::Inst().GetRenderEngine()->GetAverageFrameTime());
                    pass->SetProperty(NAME_ID("CurrentFrameWeight"), mSetting.CurrentFrameWeight);
                },
                mPostProcessMaterial,
                "TAAMain",
                true,
                mContext->mHistoryBufferRTView);

            if (mSetting.EnableSharpenPass)
            {
                auto sharpenOutputTexture = red->AllocateTexture("TAASharpenOutput", historyBufferDesc);
                auto sharpenOutputView = red->AllocateTextureView(sharpenOutputTexture, historyBufferViewDesc);

                gameContext.mRenderPipeline->PostProcess([&](auto pass) { pass->SetProperty(NAME_ID("SceneColor"), mContext->mHistoryBufferRTView); }, mPostProcessMaterial, "TAASharpen", true, sharpenOutputView);

                mContext->mHistoryBufferRTView = sharpenOutputView;
            }

            mOutput.outputcolor = mContext->mHistoryBufferRTView;
        }
        else
        {
            mOutput.outputcolor = mInput.color;
        }
        mOutput.SetOutputData(gameContext);
    }
}   // namespace cross

