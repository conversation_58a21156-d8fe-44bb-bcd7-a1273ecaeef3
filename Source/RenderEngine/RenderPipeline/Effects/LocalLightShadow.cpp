#include "LocalLightShadow.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "RenderEngine/RenderPipeline/WorldRenderPipeline/FFSWorldRenderPipeline.h"
#include "RenderEngine/RenderPipelineSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/LightSystemR.h"
#include "Resource/AssetStreaming.h"
#include "Shadow.h"

namespace cross {
static const UInt16 MAX_LOCAL_SHADOW_LEVEL = 3;

template<class T>
NGIBufferView* CreateStructuredBuffer(const FrameStdVector<T>& data)
{
    auto* rendererSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    SizeType dataByteSize = sizeof(T) * data.size();

    auto* scratchBuffer = rendererSys->GetScratchBuffer();
    auto bufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, dataByteSize);
    bufferWrap.MemWrite(0, data.data(), dataByteSize);

    return rendererSys->GetTransientResourceManager()->AllocateBufferView(
        NGIBufferViewDesc{
            NGIBufferUsage::StructuredBuffer,
            bufferWrap.GetNGIOffset(),
            dataByteSize,
            GraphicsFormat::Unknown,
            sizeof(T),
        },
        bufferWrap.GetNGIBuffer());
}

NGIViewport LocalLightShadowCache::GetViewport(const SubMapInfo* info)
{
    return NGIViewport{static_cast<float>(info->posX), static_cast<float>(info->posY), static_cast<float>(info->width), static_cast<float>(info->height), 0, 1};
}

NGIScissor LocalLightShadowCache::GetScissor(const SubMapInfo* info)
{
    return NGIScissor{info->posX, info->posY, info->width, info->height};
}

LocalLightShadowCache::LocalLightShadowCache()
{
    mPoolWidth = 0;
    mPoolHeight = 0;
    mStaticTex = nullptr;
    mDynamicTex = nullptr;
    mStaticTexView = nullptr;
    mDynamicTexView = nullptr;
}

LocalLightShadowCache::~LocalLightShadowCache() {}

void LocalLightShadowCache::UpdateRenderWorld(RenderWorld* renderWorld)
{
    if (renderWorld != mRenderWorld)
    {
        mRenderWorld = renderWorld;
        mPoolWidth = 0;
        mPoolHeight = 0;
        mStaticTex = nullptr;
        mDynamicTex = nullptr;
        mStaticTexView = nullptr;
        mDynamicTexView = nullptr;
    }

    CleanInvalidLightCache();
}

void LocalLightShadowCache::UpdateLocalLightShadowSetting(LocalLightShadowCacheSettings* settings)
{
    mEnableDynamicShadow = settings->EnableDynamicShadow;
    mMinScreenRatio = settings->MinScreenRatio;
    bAllowCopyCache = settings->AllowCopyShadowCache;
    if (settings->enable == false)
    {
        mPoolWidth = 0;
        mPoolHeight = 0;
        mStaticTex = nullptr;
        mDynamicTex = nullptr;
        mStaticTexView = nullptr;
        mDynamicTexView = nullptr;
        return;
    }
    if (settings->LocalLightCacheSize != mPoolWidth || settings->LocalLightCacheSize != mPoolHeight || settings->CacheMode != mMode)
    {
        mPoolWidth = settings->LocalLightCacheSize;
        mPoolHeight = settings->LocalLightCacheSize;
        mSubMapHeight = settings->MaxLightShadowMapSize;
        mSubMapWidth = settings->MaxLightShadowMapSize;
        mMode = settings->CacheMode;
        bMultiLevelCache = mMode != LocalLightShadowCacheMode::SingleLevel;
        bOnePassShadow = (mMode == LocalLightShadowCacheMode::OnePass_Fast) || (mMode == LocalLightShadowCacheMode::OnePass_Stable);
        if (bOnePassShadow)
        {
            mSliceHeight = mSubMapHeight;
            mSliceWidth = mSubMapHeight * 2;
            mTexArraySize = (mPoolWidth / mSliceWidth) * (mPoolHeight / mSliceHeight);
        }
        else
        {
            mTexArraySize = 1;
        }

        Init();
        bInitialized = true;
    }
    else
    {
        bInitialized = false;
    }
}

void LocalLightShadowCache::Init()
{
    // Free Texture, Allocate Texture
    CreateResidentResources();

    // clear history pool
    mSubMapPool.clear();
    mLightCache.clear();
    mDeprecatedSubMapPool.clear();

    // Divide texture into small pieces
    mSubMapPool = std::vector<std::vector<DividedShadowMap>>(MAX_LOCAL_SHADOW_LEVEL, std::vector<DividedShadowMap>(0));
    mPoolRest = std::vector<UInt16>(MAX_LOCAL_SHADOW_LEVEL, 0);
    mPoolTotal = std::vector<UInt16>(MAX_LOCAL_SHADOW_LEVEL, 0);
    mDeprecatedSubMapPool = std::vector<std::map<ecs::EntityID, LightCacheMap>>(MAX_LOCAL_SHADOW_LEVEL, std::map<ecs::EntityID, LightCacheMap>());
    mCopyDeprecatedSubMapList = std::vector<std::vector<LightCacheCopyInfo>>(MAX_LOCAL_SHADOW_LEVEL, std::vector<LightCacheCopyInfo>());

    if (bOnePassShadow)
    {
        DividePoolInMultiViewportMultiLevel();
    }
    else if (bMultiLevelCache)
    {
        DividePoolInMultiLevel();
    }
    else
    {
        DividePoolInSingleLevel();
    }

    mSumPoolSize = GetCachePoolSize();
}

void LocalLightShadowCache::CleanInvalidLightCache()
{
    for (auto it = mLightCache.begin(); it != mLightCache.end();)
    {
        if (!mRenderWorld->IsEntityAlive(it->first))
        {
            UInt16 lv = it->second.cacheLevel;
            mPoolRest[lv] += static_cast<UInt16>(it->second.cacheIdxList.size());
            for (auto idx : it->second.cacheIdxList)
            {
                mSubMapPool[lv][idx].used = false;
            }
            it = mLightCache.erase(it);
        }
        else
        {
            it++;
        }
    }
    for (int lv = 0; lv < mDeprecatedSubMapPool.size(); lv++)
    {
        auto& deprecatedSubMaps = mDeprecatedSubMapPool[lv];
        for (auto it = deprecatedSubMaps.begin(); it != deprecatedSubMaps.end();)
        {
            if (!mRenderWorld->IsEntityAlive(it->first))
            {
                mPoolRest[lv] += static_cast<UInt16>(it->second.cacheIdxList.size());
                for (auto idx : it->second.cacheIdxList)
                {
                    mSubMapPool[lv][idx].used = false;
                }
                it = deprecatedSubMaps.erase(it);
            }
            else
            {
                it++;
            }
        }
    }
}

UInt32 LocalLightShadowCache::GetCachePoolSize()
{
    return UInt32(mPoolTotal[0] + mPoolTotal[1] + mPoolTotal[2]);
}

Float4 LocalLightShadowCache::GetUvRange(UInt32 x, UInt32 y, UInt32 width, UInt32 height, UInt32 maxWidth, UInt32 maxHeight)
{
    float uMin = (static_cast<float>(x) + 0.5f) / static_cast<float>(maxWidth);
    float vMin = (static_cast<float>(y) + 0.5f) / static_cast<float>(maxHeight);
    float uMax = (static_cast<float>(x + width) + 0.5f) / static_cast<float>(maxWidth);
    float vMax = (static_cast<float>(y + height) + 0.5f) / static_cast<float>(maxHeight);

    return Float4(uMin, vMin, uMax, vMax);
}

void LocalLightShadowCache::AddViewportScissorsSubmap(const SubMapInfo* submapInfo)
{
    mViewports.emplace_back(GetViewport(submapInfo));
    mScissors.emplace_back(GetScissor(submapInfo));
}

void LocalLightShadowCache::DividePoolInSingleLevel()
{
    for (UInt32 x = 0; x + mSubMapWidth <= mPoolWidth; x += mSubMapWidth)
    {
        for (UInt32 y = 0; y + mSubMapHeight <= mPoolHeight; y += mSubMapHeight)
        {
            auto uvRange = GetUvRange(x, y, mSubMapWidth, mSubMapHeight, mPoolWidth, mPoolHeight);
            SubMapInfo newSubMap{static_cast<SInt32>(x), static_cast<SInt32>(y), mSubMapWidth, mSubMapHeight, uvRange, 0, 0};
            mSubMapPool[0].push_back({newSubMap, false, 0});
            mPoolTotal[0]++;
        }
    }
    mPoolRest[0] = mPoolTotal[0];
}

void LocalLightShadowCache::DividePoolInMultiLevel()
{
    // Divide pool in 3 levels
    // Level 0 shadows use a half of total pool
    // Level 1 shadows use quarter of total pool
    // Level 2 shadows use the rest quarter of total pool
    // Level 0 = 4 x Level 1 = 16 x Level 2

    // Level 0:
    UInt32 subMapWidth = mSubMapWidth;
    UInt32 subMapHeight = mSubMapHeight;
    for (UInt32 x = 0; x + subMapWidth <= mPoolWidth / 2; x += subMapWidth)
    {
        for (UInt32 y = 0; y + subMapHeight <= mPoolHeight; y += subMapHeight)
        {
            auto uvRange = GetUvRange(x, y, subMapWidth, subMapHeight, mPoolWidth, mPoolHeight);
            SubMapInfo newSubMap{static_cast<SInt32>(x), static_cast<SInt32>(y), subMapWidth, subMapHeight, uvRange, 0, 0};
            mSubMapPool[0].push_back({newSubMap, false, 0});
            mPoolTotal[0]++;
        }
    }
    mPoolRest[0] = mPoolTotal[0];

    // Level 1:
    subMapWidth /= 2;
    subMapHeight /= 2;
    for (UInt32 x = mPoolWidth / 2; x + subMapWidth <= mPoolWidth / 4 * 3; x += subMapWidth)
    {
        for (UInt32 y = 0; y + subMapHeight <= mPoolHeight; y += subMapHeight)
        {
            auto uvRange = GetUvRange(x, y, subMapWidth, subMapHeight, mPoolWidth, mPoolHeight);
            SubMapInfo newSubMap{static_cast<SInt32>(x), static_cast<SInt32>(y), subMapWidth, subMapHeight, uvRange, 0, 0};
            mSubMapPool[1].push_back({newSubMap, false, 1});
            mPoolTotal[1]++;
        }
    }
    mPoolRest[1] = mPoolTotal[1];

    // Level 2:
    subMapWidth /= 2;
    subMapHeight /= 2;
    for (UInt32 x = mPoolWidth / 4 * 3; x + subMapWidth <= mPoolWidth; x += subMapWidth)
    {
        for (UInt32 y = 0; y + subMapHeight <= mPoolHeight; y += subMapHeight)
        {
            auto uvRange = GetUvRange(x, y, subMapWidth, subMapHeight, mPoolWidth, mPoolHeight);
            SubMapInfo newSubMap{static_cast<SInt32>(x), static_cast<SInt32>(y), subMapWidth, subMapHeight, uvRange, 0, 0};
            mSubMapPool[2].push_back({newSubMap, false, 2});
            mPoolTotal[2]++;
        }
    }
    mPoolRest[2] = mPoolTotal[2];
}

void LocalLightShadowCache::DividePoolInMultiViewportMultiLevel()
{
    UInt32 subMapWidth = mSubMapWidth;
    UInt32 subMapHeight = mSubMapHeight;
    mViewports.clear();
    mScissors.clear();

    for (UInt32 targetIdx = 0; targetIdx < mTexArraySize; targetIdx++)
    {
        UInt32 viewportId = 0u;
        // Level 0:
        {
            UInt32 levelSize = mSubMapHeight;
            auto uvRange = GetUvRange(0, 0, levelSize, levelSize, mSliceWidth, mSliceHeight);
            SubMapInfo newSubMap{static_cast<SInt32>(0), static_cast<SInt32>(0), subMapWidth, subMapHeight, uvRange, targetIdx, viewportId++};
            mSubMapPool[0].push_back({newSubMap, false, 0});
            mPoolTotal[0]++;
            if (targetIdx == 0)
            {
                AddViewportScissorsSubmap(&newSubMap);
            }
        }

        // Level 1:
        {
            UInt32 levelSize = mSubMapHeight / 2;
            UInt32 x = mSliceWidth / 2;
            for (UInt32 y = 0; y + levelSize <= mSliceHeight; y += levelSize)
            {
                auto uvRange = GetUvRange(x, y, levelSize, levelSize, mSliceWidth, mSliceHeight);
                SubMapInfo newSubMap{static_cast<SInt32>(x), static_cast<SInt32>(y), levelSize, levelSize, uvRange, targetIdx, viewportId++};
                mSubMapPool[1].push_back({newSubMap, false, 1});
                mPoolTotal[1]++;
                if (targetIdx == 0)
                {
                    AddViewportScissorsSubmap(&newSubMap);
                }
            }
        }

        // Level 2:
        {
            UInt32 levelSize = mSubMapHeight / 4;
            for (UInt32 x = mSliceWidth / 4 * 3; x + levelSize <= mSliceWidth; x += levelSize)
            {
                for (UInt32 y = 0; y + levelSize <= mSliceHeight; y += levelSize)
                {
                    auto uvRange = GetUvRange(x, y, levelSize, levelSize, mSliceWidth, mSliceHeight);
                    SubMapInfo newSubMap{static_cast<SInt32>(x), static_cast<SInt32>(y), levelSize, levelSize, uvRange, targetIdx, viewportId++};
                    mSubMapPool[2].push_back({newSubMap, false, 2});
                    mPoolTotal[2]++;
                    if (targetIdx == 0)
                    {
                        AddViewportScissorsSubmap(&newSubMap);
                    }
                }
            }
        }
    }

    for (int lv = 0; lv < 3; lv++)
    {
        mPoolRest[lv] = mPoolTotal[lv];
    }
}

void LocalLightShadowCache::CreateResidentResources()
{
    UInt32 createWidth = bOnePassShadow ? mSliceWidth : mPoolWidth;
    UInt32 createHeight = bOnePassShadow ? mSliceHeight : mPoolHeight;

    NGITextureDesc wholeTexDesc{
        GraphicsFormat::D32_SFloat, NGITextureType::Texture2DArray, 1, 1, createWidth, createHeight, 1, mTexArraySize, NGITextureUsage::ShaderResource | NGITextureUsage::DepthStencil | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst};
    NGITextureViewDesc wholeTexViewDesc{NGITextureUsage::ShaderResource | NGITextureUsage::DepthStencil | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst,
                                        GraphicsFormat::D32_SFloat,
                                        NGITextureType::Texture2DArray,
                                        NGITextureSubRange{NGITextureAspect::Depth, 0, 1, 0, mTexArraySize}};

    NGITextureDesc copyTexDesc{GraphicsFormat::D32_SFloat, NGITextureType::Texture2DArray, 1, 1, mSubMapWidth / 2, mSubMapHeight / 2, 1, mTexArraySize, NGITextureUsage::CopySrc | NGITextureUsage::DepthStencil};
    NGITextureViewDesc copyTexViewDesc{NGITextureUsage::CopySrc | NGITextureUsage::DepthStencil, GraphicsFormat::D32_SFloat, NGITextureType::Texture2DArray, NGITextureSubRange{NGITextureAspect::Depth, 0, 1, 0, mTexArraySize}};

    mStaticTex = RED()->CreateTexture("Local Light Static Shadow", wholeTexDesc);
    mStaticTexView = RED()->CreateTextureView(mStaticTex.get(), wholeTexViewDesc);

    mDownSampleTex = RED()->CreateTexture("Local Light Cache Down Sample Copy", copyTexDesc);
    mDownSampleTexView = RED()->CreateTextureView(mDownSampleTex.get(), copyTexViewDesc);

    mDynamicTex = RED()->CreateTexture("Local Light Dynamic Shadow", wholeTexDesc);
    mDynamicTexView = RED()->CreateTextureView(mDynamicTex.get(), wholeTexViewDesc);

    NGIClearValue clearValue{};
    clearValue.depthStencil.depth = 1.f;
    clearValue.depthStencil.stencil = 0;

    RED()->BeginRegion("Clear local light shadow cache");
    auto* clearPass = RED()->AllocatePass("Clear pass", true);
    clearPass->ClearTexture(mStaticTexView.get(), clearValue);
    clearPass->ClearTexture(mDynamicTexView.get(), clearValue);
    RED()->EndRegion();
}

void LocalLightShadowCache::FreeLightCache(ecs::EntityID light)
{
    auto it = mLightCache.find(light);

    mDeprecatedSubMapPool[it->second.cacheLevel].insert({it->first, it->second});

    mLightCache.erase(it);
}

int LocalLightShadowCache::TryPreAllocateOneSubMap()
{
    for (int i = 0; i < MAX_LOCAL_SHADOW_LEVEL; i++)
    {
        if (mPreAllocatePoolUseNum[i] + 1 <= mPoolTotal[i])
        {
            mPreAllocatePoolUseNum[i]++;
            return i;
        }
    }
    return -1;
}

int LocalLightShadowCache::TryPreAllocateSubMap(UInt16 size)
{
    for (int i = 0; i < MAX_LOCAL_SHADOW_LEVEL; i++)
    {
        if (mPreAllocatePoolUseNum[i] + size <= mPoolTotal[i])
        {
            mPreAllocatePoolUseNum[i] += size;
            return i;
        }
    }
    return -1;
}

UInt16 LocalLightShadowCache::AllocateLights(std::vector<ecs::EntityID>& lightList)
{
    QUICK_SCOPED_CPU_TIMING("AllocateLocalLightsShadowMaps");
    auto* lightSys = mRenderWorld->GetRenderSystem<LightSystemR>();
    mThisFrameLightList.clear();
    mLightLevelMap.clear();
    mPreAllocatePoolUseNum = std::vector<UInt16>(MAX_LOCAL_SHADOW_LEVEL, 0);
    mThisFrameLightSet.clear();
    mCopySrcCache.clear();
    for (auto& copyDeprecatedSubMap : mCopyDeprecatedSubMapList)
    {
        copyDeprecatedSubMap.clear();
    }

    // Make clear shadow level of each light, store result in mLightLevelMap.
    // Store necessary light list in mThisFrameLightList and thisFrameLightSet
    {
        for (int i = 0; i < lightList.size(); i++)
        {
            auto light = lightList[i];
            auto lightComp = mRenderWorld->GetComponent<LightComponentR>(light);

            if (lightSys->GetLightType(lightComp.Read()) == LightType::Spot)
            {
                int level = TryPreAllocateSubMap(1);
                if (level < 0)   // if cannot allocate size 1 sub-map, we can break
                    break;
                mLightLevelMap[light] = static_cast<UInt16>(level);
            }
            else if (lightSys->GetLightType(lightComp.Read()) == LightType::Point)
            {
                int level = TryPreAllocateSubMap(6);
                if (level < 0)
                    continue;
                mLightLevelMap[light] = static_cast<UInt16>(level);
            }
            else
            {
                // do not add directional light to light list
                continue;
            }

            mThisFrameLightList.push_back(light);
            mThisFrameLightSet.insert(light);
        }
    }

    // Try copy from low level if not dirty
    if (bMultiLevelCache && bAllowCopyCache)
    {
        for (auto [light, aimLevel] : mLightLevelMap)
        {
            auto lightComp = mRenderWorld->GetComponent<LightComponentR>(light).Read();
            bool dirtyLight = lightSys->GetLightDirty_ShadowMap(lightComp);
            if (dirtyLight)
                continue;

            // Has high quality cache
            if (auto cacheIt = mLightCache.find(light); cacheIt != mLightCache.end())
            {
                if (cacheIt->second.cacheLevel < aimLevel)
                {
                    mCopySrcCache.insert({light, cacheIt->second});
                }
                continue;
            }

            // Has high quality deprecated cache
            for (int i = 0; i < aimLevel - 1 && i < mDeprecatedSubMapPool.size(); i++)
            {
                auto& deprecatedSubMapPool = mDeprecatedSubMapPool[i];
                if (auto depretedCacheIt = deprecatedSubMapPool.find(light); depretedCacheIt != deprecatedSubMapPool.end())
                {
                    mCopySrcCache.insert({light, depretedCacheIt->second});
                    break;
                }
            }
        }
    }

    {
        std::vector<ecs::EntityID> freeLightList(0);
        for (auto it = mLightCache.begin(); it != mLightCache.end(); it++)
        {
            auto light = it->first;
            auto& cachedMap = it->second;
            if (mThisFrameLightSet.find(light) == mThisFrameLightSet.end())
            {
                // 1. Lights not in this frame can be released
                freeLightList.push_back(light);
            }
            else
            {
                // 2. Lights need different level cache can be released
                UInt16 thisFrameLevel = mLightLevelMap[light];
                if (cachedMap.cacheLevel != thisFrameLevel)
                {
                    freeLightList.push_back(light);
                }
            }
        }

        // Release after iterator loop
        for (auto light : freeLightList)
            FreeLightCache(light);
    }

    // Allocate
    for (auto light : mThisFrameLightList)
    {
        auto lightComp = mRenderWorld->GetComponent<LightComponentR>(light).Read();
        UInt16 aimLevel = mLightLevelMap[light];
        UInt16 faceNum = 1;
        if (lightSys->GetLightType(lightComp) == LightType::Point)
        {
            faceNum = 6;
        }

        bool dirtyLight = lightSys->GetLightDirty_ShadowMap(lightComp);

        if (auto cacheIt = mLightCache.find(light); cacheIt != mLightCache.end())
        {
            if (dirtyLight)
            {
                cacheIt->second.needRefresh = true;
                lightSys->SetLightUndirty_ShadowMap(light);
            }
            continue;
        }

        if (TryUseDeprecatedCache(light, aimLevel))
        {
            auto cacheIt = mLightCache.find(light);
            if (dirtyLight)
            {
                cacheIt->second.needRefresh = true;
                lightSys->SetLightUndirty_ShadowMap(light);
            }
            continue;
        }

        // not cached
        if (mPoolRest[aimLevel] < faceNum)
        {
            ReleaseDeprecatedCache(aimLevel, faceNum - mPoolRest[aimLevel]);
        }
        Assert(mPoolRest[aimLevel] >= faceNum);

        auto allocateResult = LightCacheMap{std::vector<UInt16>(faceNum, 0xffff), aimLevel, true};

        UInt16 allocatedNum = 0;
        bool successAllocate = false;
        for (int i = 0; i < mSubMapPool[aimLevel].size(); i++)
        {
            if (mSubMapPool[aimLevel][i].used == false)
            {
                mSubMapPool[aimLevel][i].used = true;
                mPoolRest[aimLevel]--;
                allocateResult.cacheIdxList[allocatedNum] = static_cast<UInt16>(i);
                allocatedNum++;

                if (allocatedNum >= faceNum)
                {
                    successAllocate = true;
                    break;
                }
            }
        }
        Assert(successAllocate);

        if (bMultiLevelCache && bAllowCopyCache)
        {
            if (auto it = mCopySrcCache.find(light); it != mCopySrcCache.end() && !dirtyLight)
            {
                allocateResult.needRefresh = false;
                for (int i = 0; i < allocateResult.cacheIdxList.size(); i++)
                {
                    LocalLightShadowMap src{mSubMapPool[it->second.cacheLevel][it->second.cacheIdxList[i]].range, mStaticTexView.get()};
                    LocalLightShadowMap dst{mSubMapPool[aimLevel][allocateResult.cacheIdxList[i]].range, mStaticTexView.get()};
                    mCopyDeprecatedSubMapList[aimLevel].emplace_back(LightCacheCopyInfo(src, dst));
                }
            }
        }

        mLightCache[light] = allocateResult;
        lightSys->SetLightUndirty_ShadowMap(light);
    }

    return static_cast<UInt16>(mThisFrameLightList.size());
}

bool LocalLightShadowCache::GetShadowMap_SpotLight(ecs::EntityID light, LocalLightShadowMap& staticMap, LocalLightShadowMap& dynamicMap, bool& needRefresh)
{
    Assert(mThisFrameLightSet.find(light) != mThisFrameLightSet.end());

    auto it = mLightCache.find(light);
    Assert(it != mLightCache.end());

    SubMapInfo retSubMapInfo = mSubMapPool[it->second.cacheLevel][it->second.cacheIdxList[0]].range;
    staticMap = LocalLightShadowMap{retSubMapInfo, mStaticTexView.get()};
    dynamicMap = LocalLightShadowMap{retSubMapInfo, mDynamicTexView.get()};
    needRefresh = it->second.needRefresh;
    it->second.needRefresh = false;

    return true;
}

bool LocalLightShadowCache::GetShadowMap_PointLight(ecs::EntityID light, std::vector<LocalLightShadowMap>& staticMaps, std::vector<LocalLightShadowMap>& dynamicMaps, bool& needRefresh)
{
    auto retSubMapInfo = std::vector<SubMapInfo>(6);
    staticMaps = std::vector<LocalLightShadowMap>(6);
    dynamicMaps = std::vector<LocalLightShadowMap>(6);

    auto it = mLightCache.find(light);
    Assert(it != mLightCache.end());

    for (int i = 0; i < 6; i++)
    {
        retSubMapInfo[i] = mSubMapPool[it->second.cacheLevel][it->second.cacheIdxList[i]].range;
        staticMaps[i] = LocalLightShadowMap{retSubMapInfo[i], mStaticTexView.get()};
        dynamicMaps[i] = LocalLightShadowMap{retSubMapInfo[i], mDynamicTexView.get()};
    }

    needRefresh = it->second.needRefresh;
    it->second.needRefresh = false;

    return true;
}

UInt32 LocalLightShadowCache::GetMinSubMapSize() const
{
    if (bMultiLevelCache)
    {
        // For multi-level local shadow cache, return min sub map size (Level 2 size).
        return mSubMapHeight / 4;
    }
    else
    {
        // For single-level local shadow cache, all caches are the same size.
        return mSubMapHeight;
    }
}

bool LocalLightShadowCache::ReleaseSubmap(UInt16 level, int subMapIdx)
{
    mSubMapPool[level][subMapIdx].used = false;
    mPoolRest[level]++;
    return true;
}

bool LocalLightShadowCache::ReleaseDeprecatedCache(UInt16 level, int releaseSize)
{
    while (releaseSize > 0)
    {
        Assert(!mDeprecatedSubMapPool[level].empty());
        releaseSize -= static_cast<int>(mDeprecatedSubMapPool[level].begin()->second.cacheIdxList.size());
        for (auto idx : mDeprecatedSubMapPool[level].begin()->second.cacheIdxList)
        {
            ReleaseSubmap(level, idx);
        }
        mDeprecatedSubMapPool[level].erase(mDeprecatedSubMapPool[level].begin());
    }

    Assert(releaseSize <= 0);
    return true;
}

bool LocalLightShadowCache::TryUseDeprecatedCache(ecs::EntityID light, UInt16 level)
{
    auto cacheIt = mDeprecatedSubMapPool[level].find(light);
    if (cacheIt == mDeprecatedSubMapPool[level].end())
        return false;

    mLightCache.insert({light, cacheIt->second});
    mDeprecatedSubMapPool[level].erase(cacheIt);
    return true;
}

inline RenderingExecutionDescriptor* LocalLightShadowCache::RED()
{
    auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    return rdrSys->GetRenderingExecutionDescriptor();
}

void LocalLightShadowMapProxy::InitContext(RenderWorld* renderWorld, LocalLightShadowCacheSettings* settings)
{
    mRenderWorld = renderWorld;
    mShadowSettings = settings;

    auto* shadowSys = mRenderWorld->GetRenderSystem<ShadowSystemR>();
    mCache = shadowSys->GetLocalLightShadowCache();
    mCache->UpdateRenderWorld(renderWorld);
    mCache->UpdateLocalLightShadowSetting(mShadowSettings);
}

void LocalLightShadowMapProxy::RenderLocalLightsShadowMaps(FFSRenderPipeline* pipe, LightCullingResult* lightCullingResult, std::vector<ShadowData>& shadowData, std::vector<Float4x4>& shadowMatrices,
                                                           std::vector<SubMapInfo>& spotShadowRange, std::vector<SubMapInfo>& pointShadowRange)
{
    QUICK_SCOPED_CPU_TIMING("RenderLocalLightsShadowMaps");
    mRenderPipeline = pipe;
    mWorldRenderPipeline = static_cast<FFSWorldRenderPipeline*>(pipe->GetWorldRenderPipeline());
    mRED = pipe->GetRenderingExecutionDescriptor();
    mLightCullingResult = lightCullingResult;

    auto* lightSys = mRenderWorld->GetRenderSystem<LightSystemR>();
    auto* shadowSys = mRenderWorld->GetRenderSystem<ShadowSystemR>();
    tempLightIntensityList.clear();
    mDebugRenderNum = 0;

    auto lightList = mLightCullingResult->GetLightList();
    auto lightScreenRatio = mLightCullingResult->GetLightScreenRatio();
    auto lightAABBs = mLightCullingResult->GetlightAABB();
    float minScreenRatio = mCache->GetMinScreenRatio();

    std::vector<ecs::EntityID> renderLightList(0);
    std::vector<std::pair<Float3, Float3>> renderLightAABB(0);
    for (int i = 0; i < lightList.size(); i++)
    {
        auto lightEntity = lightList[i];
        auto lightComp = mRenderWorld->GetComponent<LightComponentR>(lightEntity);
        if (!shadowSys->IsShadowEnable(mRenderPipeline->GetCamera(), lightEntity))
        {
            continue;
        }
        if (lightSys->GetLightCastShadow(lightComp.Read()) && lightScreenRatio[i] > minScreenRatio)
        {
            renderLightList.push_back(lightEntity);
            renderLightAABB.push_back(lightAABBs[i]);
        }
    }

    mMaxLightThisFrame = mCache->AllocateLights(renderLightList);

    // lightList --> mSpotLights + mPointLights
    PrepareClassifiedLightListAndLightBounding(renderLightList, renderLightAABB);

    FetchLightShadowMap();

    FillUpShadowData(shadowData, shadowMatrices, spotShadowRange, pointShadowRange);

    if (mMaxLightThisFrame == 0)
        return;

    if (mCache->GetCacheLevel() > 1 && mCache->AllowCopyCache())
        CopyShadowMapFromOtherLevel();

    RenderLightsShadows();
}

void LocalLightShadowMapProxy::RenderDebugLocalShadowCacheTexture(REDTextureView*& debugTextureView)
{
    auto sys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    mRED->BeginRegion("Shadow.LocalLightShadowCache");

    NGITextureDesc debugTextureDesc = mRenderPipeline->mTargetView->mTexture->mDesc;
    debugTextureDesc.Format = GraphicsFormat::R8G8B8A8_UNorm;
    debugTextureDesc.Usage |= NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource;
    REDTexture* mDebugTexture = mRED->AllocateTexture("DebugTexture", debugTextureDesc);

    NGITextureViewDesc debugTextureViewDesc = mRenderPipeline->mTargetView->mDesc;
    debugTextureViewDesc.Format = GraphicsFormat::R8G8B8A8_UNorm;
    debugTextureViewDesc.Usage = NGITextureUsage::ShaderResource;
    debugTextureView = mRED->AllocateTextureView(mDebugTexture, debugTextureViewDesc);

    debugTextureViewDesc.Usage = NGITextureUsage::UnorderedAccess;
    REDTextureView* debugTextureViewUAV = mRED->AllocateTextureView(mDebugTexture, debugTextureViewDesc);

    const UInt2 screenSize = mRenderPipeline->GetRenderSize();
    const Float4 screenSizeAndInvSize{static_cast<float>(screenSize.x), static_cast<float>(screenSize.y), 1.0f / static_cast<float>(screenSize.x), 1.0f / static_cast<float>(screenSize.y)};

    UInt32 unitSize = mCache->GetMinSubMapSize();
    UInt2 poolTexSize = mCache->GetPoolTexSize();
    UInt2 sliceSize = mCache->GetTexSize();
    int rowNum = (poolTexSize.y + unitSize - 1) / unitSize;
    int colNum = (poolTexSize.x + unitSize - 1) / unitSize;
    int unitPerSliceCol = (sliceSize.x + unitSize - 1) / unitSize;
    int unitPerSliceRow = (sliceSize.y + unitSize - 1) / unitSize;

    NGIBufferView* bufferToShader;
    {
        // data bit: [used(1), level(2), deprecated(1), lightId(28)]
        UInt32 initData = 0x80000000 + ((mCache->GetCacheLevel() - 1) << 29) + 0x0fffffff;
        std::vector<UInt32> localShadowCache(rowNum * colNum, initData);

        auto FillUpDebugDataArray = [&](SubMapInfo& range, UInt32 fillData) {
            for (UInt32 i = range.posX; i < range.posX + range.width; i += unitSize)
            {
                int col = i / unitSize;
                for (UInt32 j = range.posY; j < range.posY + range.height; j += unitSize)
                {
                    int row = j / unitSize;
                    int offset = row * colNum + col;
                    if (mCache->bOnePassShadow)
                    {
                        int sliceColNum = poolTexSize.x / sliceSize.x;
                        int sliceRowNum = poolTexSize.y / sliceSize.y;
                        int sliceCol = range.arrayIdx / sliceRowNum;
                        int sliceRow = range.arrayIdx % sliceRowNum;

                        int finalCol = col + unitPerSliceCol * sliceCol;
                        int finalRow = row + unitPerSliceRow * sliceRow;
                        offset = finalRow * colNum + finalCol;
                    }
                    localShadowCache[offset] = fillData;
                }
            }
        };

        {
            const auto& lightShadowAllocate = mCache->GetLightCache();
            const auto& shadowCachePool = mCache->GetSubMapPool();
            const auto& deprecatedCaches = mCache->GetDeprecatedSubMpaPool();
            int levelNum = mCache->GetCacheLevel();

            for (auto it = lightShadowAllocate.begin(); it != lightShadowAllocate.end(); it++)
            {
                UInt32 lightId = static_cast<UInt32>(it->first.GetHandle() & 0x0fffffff);
                LightCacheMap lightCacheMap = it->second;
                for (auto cacheIdx : lightCacheMap.cacheIdxList)
                {
                    SubMapInfo range = shadowCachePool[lightCacheMap.cacheLevel][cacheIdx].range;
                    bool used = shadowCachePool[lightCacheMap.cacheLevel][cacheIdx].used;
                    UInt32 debugRenderData = lightId + (used ? 0u : 0x80000000u) + (static_cast<UInt32>(lightCacheMap.cacheLevel) << 29);
                    FillUpDebugDataArray(range, debugRenderData);
                }
            }

            for (int lv = 0; lv < levelNum; lv++)
            {
                const auto& deprecatedCache = deprecatedCaches[lv];
                for (auto it = deprecatedCache.begin(); it != deprecatedCache.end(); it++)
                {
                    UInt32 lightId = static_cast<UInt32>(it->first.GetHandle() & 0x0fffffffu);
                    LightCacheMap lightCacheMap = it->second;
                    for (auto cacheIdx : lightCacheMap.cacheIdxList)
                    {
                        SubMapInfo range = shadowCachePool[lightCacheMap.cacheLevel][cacheIdx].range;
                        bool used = shadowCachePool[lightCacheMap.cacheLevel][cacheIdx].used;
                        UInt32 debugRenderData = lightId + (used ? 0u : 0x80000000u) + (static_cast<UInt32>(lightCacheMap.cacheLevel) << 29) + 0x10000000;
                        FillUpDebugDataArray(range, debugRenderData);
                    }
                }
            }
        }

        UInt32 debugDataByteSize = static_cast<UInt32>(sizeof(UInt32) * localShadowCache.size());
        UInt32 debugDataBufferSize = std::max(debugDataByteSize, 1u);

        auto* scratchBuffer = sys->GetScratchBuffer();
        auto debugDataBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, debugDataBufferSize);
        debugDataBufferWrap.MemWrite(0, localShadowCache.data(), debugDataByteSize);

        bufferToShader = sys->GetTransientResourceManager()->AllocateBufferView(
            NGIBufferViewDesc{
                NGIBufferUsage::StructuredBuffer,
                debugDataBufferWrap.GetNGIOffset(),
                debugDataBufferSize,
                GraphicsFormat::Unknown,
                sizeof(UInt32),
            },
            debugDataBufferWrap.GetNGIBuffer());
    }

    auto* pass = mRED->AllocatePass("LocalLightShadowCacheDebug");
    {
        int poolNum = mCache->GetCachePoolSize();
        pass->SetProperty(NAME_ID("_LocalShadowCacheData"), bufferToShader);
        pass->SetProperty(NAME_ID("_OutDebugTex"), debugTextureViewUAV);

        pass->SetProperty(NAME_ID("_CacheSize"), Int2(rowNum, colNum));
        pass->SetProperty(NAME_ID("_MaxLevel"), mCache->GetCacheLevel());

        pass->SetProperty(NAME_ID("_RenderRatio"), static_cast<float>(mDebugRenderNum) / static_cast<float>(poolNum));
        pass->SetProperty(NAME_ID("_CopyRatio"), static_cast<float>(mDebugCopyNum) / static_cast<float>(poolNum));

        UInt3 groupSize;
        mShadowSettings->LocalShadowCacheDebugComputeShaderR->GetThreadGroupSize("LocalShadowDebugCS", groupSize.x, groupSize.y, groupSize.z);
        pass->Dispatch(mShadowSettings->LocalShadowCacheDebugComputeShaderR, NAME_ID("LocalShadowDebugCS"), math::DivideAndRoundUp(screenSize.x, groupSize.x), math::DivideAndRoundUp(screenSize.y, groupSize.y), 1);
    }

    mRED->EndRegion();
}

UInt32 LocalLightShadowMapProxy::GetSpotLightNum() const
{
    return static_cast<UInt32>(mSpotLights.size());
}

UInt32 LocalLightShadowMapProxy::GetPointLightNum() const
{
    return static_cast<UInt32>(mPointLights.size());
}

REDTextureView* LocalLightShadowMapProxy::GetLocalLightShadowMapView()
{
    return mCache->GetDynamicShadowTextureView();
}

LocalLightShadowMap LocalLightShadowMapProxy::GetSpotLightShadowMapResult(ecs::EntityID light)
{
    for (int i = 0; i < mSpotLights.size(); i++)
    {
        if (mSpotLights[i] == light)
            return mSpotLightDynamicShadowMaps[i];
    }
    Assert(false);
    return LocalLightShadowMap();
}

std::vector<LocalLightShadowMap> LocalLightShadowMapProxy::GetPointLightShadowMapResult(ecs::EntityID light)
{
    for (int i = 0; i < mPointLights.size(); i++)
    {
        if (mPointLights[i] == light)
            return mPointLightDynamicShadowMaps[i];
    }
    Assert(false);
    return std::vector<LocalLightShadowMap>();
}

std::tuple<REDBuffer*, REDBufferView*, REDBufferView*> LocalLightShadowMapProxy::CreateUAVBuffer(std::string_view name, UInt32 elementCount, UInt32 elementBytes, NGIBufferUsage additionalBufferUsage)
{
    NGIBufferUsage usage = NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer | additionalBufferUsage;

    REDBuffer* buffer = mRED->AllocateBuffer(name, NGIBufferDesc{elementCount * elementBytes, usage});

    REDBufferView* bufferUAV = mRED->AllocateBufferView(buffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0u, elementCount * elementBytes, GraphicsFormat::Unknown, elementBytes});
    REDBufferView* bufferSRV = mRED->AllocateBufferView(buffer, NGIBufferViewDesc{NGIBufferUsage::StructuredBuffer, 0u, elementCount * elementBytes, GraphicsFormat::Unknown, elementBytes});

    return {buffer, bufferUAV, bufferSRV};
}

void LocalLightShadowMapProxy::ClearRWStructuredBuffer(REDBufferView* bufferView, UInt32 value)
{
    const SizeType bufferSize = bufferView->GetDesc().SizeInBytes;

    UInt4 clearParams(0, (UInt32)bufferSize / sizeof(UInt32), value, 0);

    auto* pass = mRED->AllocatePass("ClearUAVBuffer", mIsNoCulling);
    {
        pass->SetProperty(NameID("_ClearResource"), bufferView);
        pass->SetProperty(NameID("_ClearParams"), clearParams);

        UInt3 groupSize;
        mShadowSettings->ClearBufferUIntComputeShaderR->GetThreadGroupSize(NameID("ClearCS"), groupSize.x, groupSize.y, groupSize.z);
        pass->Dispatch(mShadowSettings->ClearBufferUIntComputeShaderR, NameID("ClearCS"), ((UInt32)bufferSize / sizeof(UInt32) + groupSize.x - 1) / groupSize.x, 1, 1);
    }
}

REDPass* LocalLightShadowMapProxy::ClearRWStructuredBuffer(REDBufferView* bufferView, UInt32 minOffset, UInt32 maxOffset, UInt32 value)
{
    UInt4 clearParams(minOffset, maxOffset, value, 0);

    auto* pass = mRED->AllocatePass("ClearUAVBuffer");
    {
        pass->SetProperty(NAME_ID("_ClearResource"), bufferView);
        pass->SetProperty(NAME_ID("_ClearParams"), clearParams);

        UInt3 groupSize;
        mShadowSettings->ClearBufferUIntComputeShaderR->GetThreadGroupSize(NAME_ID("ClearCS"), groupSize.x, groupSize.y, groupSize.z);
        pass->Dispatch(mShadowSettings->ClearBufferUIntComputeShaderR, NAME_ID("ClearCS"), ((maxOffset - minOffset) + groupSize.x - 1) / groupSize.x, 1, 1);
    }

    return pass;
}

void LocalLightShadowMapProxy::PrepareClassifiedLightListAndLightBounding(std::vector<ecs::EntityID>& lightList, std::vector<std::pair<Float3, Float3>>& lightAABBs)
{
    auto* lightSys = mRenderWorld->GetRenderSystem<LightSystemR>();

    mAllLights.clear();
    mSpotLights.clear();
    mPointLights.clear();
    UInt16 allocatedNum = 0;

    float floatMax = std::numeric_limits<float>::max();
    float floatMin = std::numeric_limits<float>::min();
    Float3 aabbMin{floatMax, floatMax, floatMax};
    Float3 aabbMax{floatMin, floatMin, floatMin};
    auto expandBoundingAABB = [&](std::pair<Float3, Float3>& bound) {
        aabbMin = Float3::Min(aabbMin, bound.first);
        aabbMax = Float3::Max(aabbMax, bound.second);
    };

    for (int i = 0; i < lightList.size(); i++)
    {
        auto lightEntity = lightList[i];
        auto lightComp = mRenderWorld->GetComponent<LightComponentR>(lightEntity);
        auto lightType = lightSys->GetLightType(lightComp.Read());

        if (allocatedNum >= mMaxLightThisFrame)
            break;

        if (lightType == LightType::Spot)
        {
            mSpotLights.push_back(lightEntity);
            tempLightIntensityList.push_back(lightSys->GetLightIntensity(lightComp.Read()));
        }
        else if (lightType == LightType::Point)
        {
            mPointLights.push_back(lightEntity);
        }
        else
        {
            continue;
        }
        mAllLights.push_back(lightEntity);
        allocatedNum++;
        expandBoundingAABB(lightAABBs[i]);
    }

    mObjCullingBounding.CreateFromPoints(mObjCullingBounding, aabbMin, aabbMax);

    UInt16 numSpotLight = static_cast<UInt16>(mSpotLights.size());
    mSpotLightDirty = std::vector<bool>(numSpotLight);
    mSpotLightStaticShadowMaps = std::vector<LocalLightShadowMap>(numSpotLight);
    mSpotLightDynamicShadowMaps = std::vector<LocalLightShadowMap>(numSpotLight);

    UInt16 numPointLight = static_cast<UInt16>(mPointLights.size());
    mPointLightDirty = std::vector<bool>(numPointLight);
    mPointLightStaticShadowMaps = std::vector<std::vector<LocalLightShadowMap>>(numPointLight, std::vector<LocalLightShadowMap>(6));
    mPointLightDynamicShadowMaps = std::vector<std::vector<LocalLightShadowMap>>(numPointLight, std::vector<LocalLightShadowMap>(6));
}

void LocalLightShadowMapProxy::FetchLightShadowMap()
{
    bool initialized = mCache->IsInitialized();
    auto* lightSys = mRenderWorld->GetRenderSystem<LightSystemR>();

    for (int i = 0, j = 0, k = 0; i < mAllLights.size(); i++)
    {
        auto lightEntity = mAllLights[i];
        auto lightComp = mRenderWorld->GetComponent<LightComponentR>(lightEntity);
        auto lightType = lightSys->GetLightType(lightComp.Read());

        if (lightType == LightType::Spot)
        {
            bool needRefresh;
            mCache->GetShadowMap_SpotLight(mSpotLights[j], mSpotLightStaticShadowMaps[j], mSpotLightDynamicShadowMaps[j], needRefresh);
            mSpotLightDirty[j] = needRefresh || initialized;
            j++;
        }
        else if (lightType == LightType::Point)
        {
            bool needRefresh;
            mCache->GetShadowMap_PointLight(mPointLights[k], mPointLightStaticShadowMaps[k], mPointLightDynamicShadowMaps[k], needRefresh);
            mPointLightDirty[k] = needRefresh || initialized;
            k++;
        }
    }
}

SubMapInfo LocalLightShadowMapProxy::GetShrinkSampleRange(const SubMapInfo& range, float shrinkPixel)
{
    SubMapInfo ret = range;
    UInt2 texSize = mCache->GetTexSize();
    ret.uvRange.x += 1.f / static_cast<float>(texSize.x) * shrinkPixel;
    ret.uvRange.y += 1.f / static_cast<float>(texSize.y) * shrinkPixel;
    ret.uvRange.z -= 1.f / static_cast<float>(texSize.x) * shrinkPixel;
    ret.uvRange.w -= 1.f / static_cast<float>(texSize.y) * shrinkPixel;
    return std::move(ret);
}

void LocalLightShadowMapProxy::FillUpShadowData(std::vector<ShadowData>& shadowData, std::vector<Float4x4>& shadowMatrices, std::vector<SubMapInfo>& spotShadowRange, std::vector<SubMapInfo>& pointShadowRange)
{
    auto* lightSys = mRenderWorld->GetRenderSystem<LightSystemR>();
    auto* shadowSys = mRenderWorld->GetRenderSystem<ShadowSystemR>();

    shadowData.reserve(shadowData.size() + mSpotLights.size() + mPointLights.size());
    shadowMatrices.reserve(shadowMatrices.size() + mSpotLights.size());
    spotShadowRange.reserve(mSpotLights.size());
    pointShadowRange.reserve(mPointLights.size() * 6);

    for (int i = 0; i < mSpotLights.size(); i++)
    {
        auto light = mSpotLights[i];
        auto shadowCameraComp = mRenderWorld->GetComponent<ShadowCameraComponentR>(light);
        const auto& punctualShadowCameras = *shadowSys->GetPunctualLightShadowCameras(shadowCameraComp.Read());
        const auto& shadowCamera = punctualShadowCameras[0];
        auto farPlane = punctualShadowCameras[0].GetFarPlane();

        auto lightComp = mRenderWorld->GetComponent<LightComponentR>(light);
        ShadowData newShadowData(lightSys, lightComp.Read(), static_cast<SInt32>(i), static_cast<SInt32>(shadowMatrices.size()), farPlane);
        lightSys->SetLightShadowDataIndex(lightComp.Write(), static_cast<int>(shadowData.size()));

        shadowData.push_back(newShadowData);
        shadowMatrices.push_back(shadowCamera.GetViewProjMatrix());
        spotShadowRange.push_back(mSpotLightStaticShadowMaps[i].range);
    }

    for (int i = 0; i < mPointLights.size(); i++)
    {
        auto light = mPointLights[i];
        auto shadowCameraComp = mRenderWorld->GetComponent<ShadowCameraComponentR>(light);
        const auto& punctualShadowCameras = *shadowSys->GetPunctualLightShadowCameras(shadowCameraComp.Read());
        auto lightComp = mRenderWorld->GetComponent<LightComponentR>(light);
        // const auto& shadowCamera = punctualShadowCameras[0];
        auto farPlane = punctualShadowCameras[0].GetFarPlane();

        ShadowData newShadowData(lightSys, lightComp.Read(), i, 0, farPlane);
        lightSys->SetLightShadowDataIndex(lightComp.Write(), static_cast<int>(shadowData.size()));

        shadowData.push_back(newShadowData);
        auto ranges = GetPointLightShadowMapResult(light);
        for (UInt16 face = 0; face < 6; face++)
        {
            pointShadowRange.push_back(GetShrinkSampleRange(ranges[face].range, 1.f));
        }
    }
}

void LocalLightShadowMapProxy::CopyShadowMapFromOtherLevel()
{
    mRED->BeginRegion("CopyLocalShadowCacheFromLowerLevel");

    const auto& copyLists = mCache->GetCopyLists();
    mDebugCopyNum = 0;

    for (auto it = copyLists.rbegin(); it != copyLists.rend(); ++it)
    {
        for (auto& copyInfo : *it)
        {
            mDebugCopyNum++;
            const LocalLightShadowMap& src = copyInfo.src;
            const LocalLightShadowMap& dst = copyInfo.dst;
            const auto copyTexView = mCache->GetSubMapDownSampleTextureView();

            Int4 srcPosInfo{src.range.posX, src.range.posY, static_cast<int>(src.range.width), static_cast<int>(src.range.height)};
            Int4 dstPosInfo{dst.range.posX, dst.range.posY, static_cast<int>(dst.range.width), static_cast<int>(dst.range.height)};

            float poolTexWidth = static_cast<float>(src.tex->GetWidth());
            float poolTexHeight = static_cast<float>(src.tex->GetHeight());
            Float4 poolTexSize{poolTexWidth, poolTexHeight, 1.f / poolTexWidth, 1.f / poolTexHeight};

            float texWidth = static_cast<float>(copyTexView->GetWidth());
            float texHeight = static_cast<float>(copyTexView->GetHeight());
            Float4 texSize{texWidth, texHeight, 1.f / texWidth, 1.f / texHeight};

            // Down sample from source (tex2D array) to copy texture (tex2D)
            mRenderPipeline->PostProcessDepthOnly(
                [&](REDPass* pass) {
                    pass->SetProperty(NAME_ID("_SrcTex"), src.tex);
                    pass->SetProperty(NAME_ID("_SrcPosInfo"), srcPosInfo);
                    pass->SetProperty(NAME_ID("_DstPosInfo"), dstPosInfo);
                    pass->SetProperty(NAME_ID("_PoolTexSize"), poolTexSize);
                    pass->SetProperty(NAME_ID("_TexSize"), texSize);
                    pass->SetProperty(NAME_ID("_SrcArrayIdx"), src.range.arrayIdx);
                },
                mRenderPipeline->GetPostProcessMtl(),
                "down_sample_sub_map",
                true,
                true,
                copyTexView);

            // Copy from copy texture (tex2D) to destination (tex2D array)
            UInt32 copySubResource = NGICalcSubresource(0u, 0, 0u, copyTexView->GetDesc().SubRange.MipLevels, copyTexView->GetDesc().SubRange.ArraySize);
            UInt32 dstSubResource = NGICalcSubresource(0u, dst.range.arrayIdx, 0u, dst.tex->GetDesc().SubRange.MipLevels, dst.tex->GetDesc().SubRange.ArraySize);
            NGICopyTexture region{copySubResource, {0, 0, 0}, dstSubResource, {dst.range.posX, dst.range.posY, 0}, {dst.range.width, dst.range.height, 1}};
            auto* copyPass = mRED->AllocatePass("Copy pass", true);
            copyPass->CopyTextureToTexture(dst.tex->mTexture, copyTexView->mTexture, 1, &region);
        }
    }

    mRED->EndRegion();
}

struct FoliagePayloadData
{
    Float4 boundingSphere;
    UInt32 lodDataStart;
    UInt32 lodDataCount;
    UInt32 objectIndexStart;
    UInt32 objectIndexCount;
    UInt32 indirectArgIndexOffset;
    float culledHeight;
    float _pad[2];
};

struct VisibleInstanceCommand
{
    UInt32 lightViewIndex;
    UInt32 instanceId;
    UInt32 indirectArgIndex;
};

void LocalLightShadowMapProxy::RenderOneShadowCamera(const RenderCamera* shadowCamera, const std::string& cameraTag, REDTextureView* shadowView, SubMapInfo* subMapInfo, REDObjectType objMask)
{
    mRED->BeginRegion("ShadowCaster" + cameraTag);

    bool isPunctualLight = true;

    REDPass* shadowPass;
    NGIClearValue clearValue{};
    clearValue.depthStencil.depth = 1.f;
    clearValue.depthStencil.stencil = 0;
    REDDepthStencilTargetDesc depthStencilTargetDesc{
        shadowView,
        NGILoadOp::Load,
        NGIStoreOp::Store,
        NGILoadOp::Load,
        NGIStoreOp::DontCare,
        clearValue,
    };

    auto* cullingResult = mRED->CullShadowCaster(REDCullingDesc{mRenderWorld, const_cast<cross::RenderCamera*>(shadowCamera), objMask});
    auto* drawUnitList = cullingResult->GenerateDrawUnitList(REDDrawUnitsDesc{NAME_ID("shadow_all"), 0, UINT16_MAX, RenderEffectTag::CastShadow});
    // Render shadow depth
    {
        mRED->BeginRenderPass("ShadowCaster" + cameraTag, 0, nullptr, &depthStencilTargetDesc, mIsNoCulling);
        shadowPass = mRED->AllocateSubRenderPass("shadow", 0, nullptr, 0, nullptr, REDPassFlagBit::NeedDepth);

        // override the global context;
        auto invViewMatrix = shadowCamera->GetViewMatrix().Inverted();
        Float3 cameraPos = Float3(invViewMatrix.m30, invViewMatrix.m31, invViewMatrix.m32);
        shadowPass->SetProperty(BuiltInProperty::ce_CameraPos, cameraPos);
#if defined(CE_USE_DOUBLE_TRANSFORM)
        shadowPass->SetProperty(BuiltInProperty::ce_CameraTilePosition, shadowCamera->GetTilePosition());
#endif
        shadowPass->SetProperty(BuiltInProperty::ce_View, shadowCamera->GetViewMatrix());
        shadowPass->SetProperty(BuiltInProperty::ce_Projection, shadowCamera->GetProjMatrix());
        shadowPass->SetProperty(NAME_ID("_NormalizeFactor"), shadowCamera->GetFarPlane());

        shadowPass->SetProperty(NAME_ID("PUNCTUAL_LIGHT"), isPunctualLight);   // PUCTUAL_LIGHT here means "local light", including spot & point lights
        shadowPass->SetProperty(NAME_ID("SOFT_SHADOW"), false);
        shadowPass->SetProperty(BuiltInProperty::CE_INSTANCING, true);
        shadowPass->SetProperty(NAME_ID("LOCAL_SHADOW"), true);
        shadowPass->SetProperty(NAME_ID("_FoliageObjectSceneDatas"), mRenderPipeline->GetFoliageGpuDriven().mFoliageInstanceREDView);
        shadowPass->SetProperty(NAME_ID("_FoliageEntityBuffer"), mRenderPipeline->GetFoliageGpuDriven().mFoliageEntityREDView);
        auto drawUnitsDesc = REDRenderDrawUnitsDesc{drawUnitList,
                                                    NGIRasterizationStateDesc{
                                                        FillMode::Solid,
                                                        CullMode::None,
                                                        FaceOrder::CW,
                                                        false,
                                                        false,
                                                        false,
                                                        0,
                                                        0,
                                                        0,
                                                        0,
                                                        0,
                                                        RasterizationMode::DefaultRaster,
                                                        0,
                                                    }};

        shadowPass->OnCulling([=](REDPass* pass) { 
            if (drawUnitList->GetDefaultObjectIndexBufferView())
                shadowPass->SetProperty(NAME_ID("_ObjectIndexBuffer"), drawUnitList->GetDefaultObjectIndexBufferView()); 
        });
        drawUnitsDesc.InitViewport = std::array<NGIViewport, 16>{LocalLightShadowCache::GetViewport(subMapInfo)};
        drawUnitsDesc.InitScissor = std::array<NGIScissor, 16>{LocalLightShadowCache::GetScissor(subMapInfo)};

        shadowPass->RenderDrawUnits(drawUnitsDesc);

        mRED->EndRenderPass();
    }

    mRED->EndRegion();
}

void LocalLightShadowMapProxy::RenderShadowForEachLight(REDObjectType objType)
{
    auto* shadowCameraSys = mRenderWorld->GetRenderSystem<ShadowSystemR>();
    bool renderStatic = objType == REDObjectType::Static;
    Assert(objType != REDObjectType::All);

    for (UInt16 i = 0; i < mSpotLights.size(); i++)
    {
        if (bool lightDirty = mSpotLightDirty[i]; lightDirty || !renderStatic)
        {
            auto shadowCameraComp = mRenderWorld->GetComponent<ShadowCameraComponentR>(mSpotLights[i]);
            const auto& punctualShadowCameras = *shadowCameraSys->GetPunctualLightShadowCameras(shadowCameraComp.Read());
            const auto& shadowCamera = punctualShadowCameras[0];
            REDTextureView* texView = renderStatic ? mSpotLightStaticShadowMaps[i].tex : mSpotLightDynamicShadowMaps[i].tex;
            RenderOneShadowCamera(&shadowCamera, "_SpotLight_" + std::to_string(i), texView, &mSpotLightStaticShadowMaps[i].range, objType);
        }
    }
    for (UInt16 i = 0; i < mPointLights.size(); i++)
    {
        if (bool lightDirty = mPointLightDirty[i]; lightDirty || !renderStatic)
        {
            auto shadowCameraComp = mRenderWorld->GetComponent<ShadowCameraComponentR>(mPointLights[i]);
            const auto& punctualShadowCameras = *shadowCameraSys->GetPunctualLightShadowCameras(shadowCameraComp.Read());
            for (UInt16 face = 0; face < 6; face++)
            {
                const auto& shadowCamera = punctualShadowCameras[face];
                REDTextureView* texView = renderStatic ? mPointLightStaticShadowMaps[i][face].tex : mPointLightDynamicShadowMaps[i][face].tex;
                RenderOneShadowCamera(&shadowCamera, "_PointLight_" + std::to_string(i) + "_face_" + std::to_string(face), texView, &mPointLightStaticShadowMaps[i][face].range, objType);
            }
        }
    }
}

void LocalLightShadowMapProxy::CopyStaticToDynamic()
{
    struct CopyData
    {
        REDTextureView* src;
        REDTextureView* dst;
        SInt32 minX, minY, maxX, maxY;
        UInt32 arrayIdx;
    };
    std::vector<CopyData> copyList(0);

    std::vector<UInt32> arrayIndices(0);
    std::set<UInt32> arrayIdxSet;
    for (UInt16 i = 0; i < mSpotLights.size(); i++)
    {
        auto arrayIdx = mSpotLightStaticShadowMaps[i].range.arrayIdx;
        if (arrayIdxSet.find(arrayIdx) == arrayIdxSet.end())
        {
            arrayIdxSet.insert(arrayIdx);
            LocalLightShadowMap& src = mSpotLightStaticShadowMaps[i];
            LocalLightShadowMap& dst = mSpotLightDynamicShadowMaps[i];
            auto texSize = mCache->GetTexSize();
            copyList.emplace_back(CopyData{src.tex, dst.tex, 0, 0, static_cast<int>(texSize.x), static_cast<int>(texSize.y), arrayIdx});
        }
    }

    for (UInt16 i = 0; i < mPointLights.size(); i++)
    {
        for (UInt16 face = 0; face < 6; face++)
        {
            auto arrayIdx = mPointLightStaticShadowMaps[i][face].range.arrayIdx;
            if (arrayIdxSet.find(arrayIdx) == arrayIdxSet.end())
            {
                arrayIdxSet.insert(arrayIdx);
                LocalLightShadowMap& src = mPointLightStaticShadowMaps[i][face];
                LocalLightShadowMap& dst = mPointLightDynamicShadowMaps[i][face];
                auto texSize = mCache->GetTexSize();
                copyList.emplace_back(CopyData{src.tex, dst.tex, 0, 0, static_cast<int>(texSize.x), static_cast<int>(texSize.y), arrayIdx});
            }
        }
    }

    mRED->BeginRegion("CopyStaticShadowToDynamic");
    for (auto& copyData : copyList)
    {
        SubMapInfo subMapInfo{copyData.minX, copyData.minY, static_cast<UInt32>(copyData.maxX - copyData.minX), static_cast<UInt32>(copyData.maxY - copyData.minY), Float4(), copyData.arrayIdx};
        CopyStaticTexToDynamicTex(copyData.src, copyData.dst, subMapInfo);
    }
    mRED->EndRegion();
}

void LocalLightShadowMapProxy::ClearLocalLightShadowMap(LocalLightShadowMap& shadowMap)
{
    // auto onConfig = [&](auto pass) { pass->SetProperty(NAME_ID("uvRange"), tempRange); };
    auto onConfig = [&](auto pass) {
        pass->SetProperty(NAME_ID("uvRange"), shadowMap.range.uvRange);
        pass->SetProperty(NAME_ID("arrayIdx"), shadowMap.range.arrayIdx);
    };
    mRenderPipeline->PostProcessDepthOnly<decltype(onConfig), PrimitiveMeshType::FullScreenQuad>(std::move(onConfig), mRenderPipeline->GetPostProcessMtl(), "clear_sub_map", false, false, shadowMap.tex);
}

void LocalLightShadowMapProxy::CleanStaticSubmaps()
{
    mRED->BeginRegion("Clear static shadow map");
    auto* shadowCameraSys = mRenderWorld->GetRenderSystem<ShadowSystemR>();

    for (UInt16 i = 0; i < mSpotLights.size(); i++)
    {
        if (bool lightDirty = mSpotLightDirty[i]; lightDirty)
        {
            ClearLocalLightShadowMap(mSpotLightStaticShadowMaps[i]);
            mDebugRenderNum++;
        }
    }
    for (UInt16 i = 0; i < mPointLights.size(); i++)
    {
        if (bool lightDirty = mPointLightDirty[i]; lightDirty)
        {
            for (UInt16 face = 0; face < 6; face++)
            {
                ClearLocalLightShadowMap(mPointLightStaticShadowMaps[i][face]);
                mDebugRenderNum += 6;
            }
        }
    }
    mRED->EndRegion();
}

void LocalLightShadowMapProxy::CopyStaticTexToDynamicTex(REDTextureView* staticSubMap, REDTextureView* dynamicSubMap, SubMapInfo& copyRange)
{
    UInt32 srcSubResource = NGICalcSubresource(0u, copyRange.arrayIdx, 0u, staticSubMap->GetDesc().SubRange.MipLevels, staticSubMap->GetDesc().SubRange.ArraySize);
    UInt32 dstSubResource = NGICalcSubresource(0u, copyRange.arrayIdx, 0u, dynamicSubMap->GetDesc().SubRange.MipLevels, dynamicSubMap->GetDesc().SubRange.ArraySize);
    NGICopyTexture region{srcSubResource, {copyRange.posX, copyRange.posY, 0}, dstSubResource, {copyRange.posX, copyRange.posY, 0}, {copyRange.width, copyRange.height, 1}};

    auto* copyPass = mRED->AllocatePass("Copy pass", true);
    copyPass->CopyTextureToTexture(dynamicSubMap->mTexture, staticSubMap->mTexture, 1, &region);
}

void LocalLightShadowMapProxy::GenerateLightViewDatas(FrameStdVector<LocalLightViewData>& viewDatas, bool renderStatic)
{
    UInt32 viewNum = GetLightCameraNum(renderStatic);
    viewDatas.resize(viewNum);

    auto* lightSys = mRenderWorld->GetRenderSystem<LightSystemR>();
    auto* shadowSys = mRenderWorld->GetRenderSystem<ShadowSystemR>();

    UInt32 nowIdx = 0;
    for (UInt16 i = 0; i < mSpotLights.size(); i++)
    {
        if (bool lightDirty = mSpotLightDirty[i]; lightDirty || !renderStatic)
        {
            auto light = mSpotLights[i];
            auto shadowCameraComp = mRenderWorld->GetComponent<ShadowCameraComponentR>(light);
            const auto& punctualShadowCameras = *shadowSys->GetPunctualLightShadowCameras(shadowCameraComp.Read());
            const auto& shadowCamera = punctualShadowCameras[0];
            const SubMapInfo& submapInfo = renderStatic ? mSpotLightStaticShadowMaps[i].range : mSpotLightDynamicShadowMaps[i].range;
            Float3 tilePos = shadowCamera.GetCameraView().mCameraTilePosition;
            Float4 tileAndRange = Float4(tilePos.x, tilePos.y, tilePos.z, shadowCamera.GetFarPlane());

            auto invViewMatrix = shadowCamera.GetViewMatrix().Inverted();
            Float4 cameraPos = Float4(invViewMatrix.m30, invViewMatrix.m31, invViewMatrix.m32, 1.f);

            viewDatas[nowIdx++] = LocalLightViewData{shadowCamera.GetViewProjMatrix(), cameraPos, tileAndRange, submapInfo.arrayIdx, submapInfo.viewportIdx, UInt2(0, 0)};
        }
    }
    for (UInt16 i = 0; i < mPointLights.size(); i++)
    {
        if (bool lightDirty = mPointLightDirty[i]; lightDirty)
        {
            auto light = mPointLights[i];
            auto shadowCameraComp = mRenderWorld->GetComponent<ShadowCameraComponentR>(light);
            const auto& punctualShadowCameras = *shadowSys->GetPunctualLightShadowCameras(shadowCameraComp.Read());
            for (UInt16 face = 0; face < 6; face++)
            {
                const auto& shadowCamera = punctualShadowCameras[face];
                const SubMapInfo& submapInfo = renderStatic ? mPointLightStaticShadowMaps[i][face].range : mPointLightDynamicShadowMaps[i][face].range;
                Float3 tilePos = shadowCamera.GetCameraView().mCameraTilePosition;
                Float4 tileAndRange = Float4(tilePos.x, tilePos.y, tilePos.z, shadowCamera.GetFarPlane());

                auto invViewMatrix = shadowCamera.GetViewMatrix().Inverted();
                Float4 cameraPos = Float4(invViewMatrix.m30, invViewMatrix.m31, invViewMatrix.m32, 1.f);

                viewDatas[nowIdx++] = LocalLightViewData{shadowCamera.GetViewProjMatrix(), cameraPos, tileAndRange, submapInfo.arrayIdx, submapInfo.viewportIdx, UInt2(0, 0)};
            }
        }
    }
}

void LocalLightShadowMapProxy::GenerateDrawUnitListDrawIndirectCmdBuffer(REDDrawUnitList* drawUnitList, NGIBufferView* viewDataBufferSRV, UInt32 viewCnt, REDBufferView*& outObjectIndexBufferView, REDBufferView*& outLightViewIndexBufferView)
{
    mRED->SetProperty(NAME_ID("_LightViewCount"), viewCnt);
    UInt32 allocateLightNum = (mShadowSettings->BufferLimitFactor == 0) ? viewCnt : mShadowSettings->BufferLimitFactor;
    mRED->SetProperty(NAME_ID("_IndirectArgCount"), allocateLightNum);

    auto* gpuScene = mWorldRenderPipeline->GetGPUScene();

    auto objectIndexBufferTuple = CreateUAVBuffer("ObjectIndexBuffer", 0, sizeof(UInt32));
    auto* objectIndexBuffer = std::get<0>(objectIndexBufferTuple);
    auto* objectIndexBufferUAV = std::get<1>(objectIndexBufferTuple);
    auto* objectIndexBufferSRV = std::get<2>(objectIndexBufferTuple);

    auto lightViewIndexBufferTuple = CreateUAVBuffer("LightIndexBuffer", 0, sizeof(UInt32));
    auto* LightIndexBuffer = std::get<0>(lightViewIndexBufferTuple);
    auto* LightIndexBufferUAV = std::get<1>(lightViewIndexBufferTuple);
    auto* LightIndexBufferSRV = std::get<2>(lightViewIndexBufferTuple);

    auto visibleObjectCommandsBufferTuple = CreateUAVBuffer("VisibleObjectCommandsBuffer", 0, sizeof(VisibleInstanceCommand));
    auto* visibleObjectCommandsBuffer = std::get<0>(visibleObjectCommandsBufferTuple);
    auto* visibleObjectCommandsBufferUAV = std::get<1>(visibleObjectCommandsBufferTuple);
    auto* visibleObjectCommandsBufferSRV = std::get<2>(visibleObjectCommandsBufferTuple);

    auto [visibleObjectCommandCountBuffer, visibleObjectCommandCountBufferUAV, visibleObjectCommandCountBufferSRV] = CreateUAVBuffer("VisibleObjectCommandCountBuffer", 1, sizeof(UInt32));
    ClearRWStructuredBuffer(visibleObjectCommandCountBufferUAV, 0);

    auto* drawIndirectArgsBuffer = mRED->AllocateBuffer("DrawIndirectBuffer", NGIBufferDesc{0, NGIBufferUsage::IndirectBuffer | NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst});
    auto* drawIndirectArgsBufferSRV = mRED->AllocateBufferView(drawIndirectArgsBuffer, NGIBufferViewDesc{NGIBufferUsage::StructuredBuffer, 0, 0, GraphicsFormat::Unknown, sizeof(UInt32)});
    auto* drawIndirectArgsBufferUAV = mRED->AllocateBufferView(drawIndirectArgsBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, 0, GraphicsFormat::Unknown, sizeof(UInt32)});
    drawUnitList->SetIndirectBuffer(drawIndirectArgsBuffer);

    auto [objectIndexOffsetBuffer, objectIndexOffsetBufferUAV, objectIndexOffsetBufferSRV] = CreateUAVBuffer("ObjectIndexOffsetBuffer", 0, sizeof(UInt32), NGIBufferUsage::VertexBuffer);
    drawUnitList->SetObjectIndexOffsetsBuffer(objectIndexOffsetBuffer);

    auto [tempObjectIndexOffsetBuffer, tempObjectIndexOffsetBufferUAV, tempObjectIndexOffsetBufferSRV] = CreateUAVBuffer("TempObjectIndexOffsetBuffer", 0, sizeof(UInt32));

    auto [offsetBufferCountBuffer, offsetBufferCountBufferUAV, offsetBufferCountBufferSRV] = CreateUAVBuffer("OffsetBufferCountBuffer", 1, sizeof(UInt32));
    ClearRWStructuredBuffer(offsetBufferCountBufferUAV, 0);

    auto* outputPassIndirectArgs = mRED->AllocateBuffer("OutputPassIndirectArgs", NGIBufferDesc{3u * sizeof(UInt32), NGIBufferUsage::IndirectBuffer | NGIBufferUsage::RWStructuredBuffer});
    auto* outputPassIndirectArgsUAV = mRED->AllocateBufferView(outputPassIndirectArgs, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, 3u * sizeof(UInt32), GraphicsFormat::Unknown, sizeof(UInt32)});

    std::shared_ptr<UInt32> visibleObjectCommandBufferMaxNum;
    visibleObjectCommandBufferMaxNum.reset(new UInt32(0));


    auto [objectPayloadIndexAndObjectOffsetAndMutex, objectPayloadIndexAndObjectOffsetAndMutexUAV, objectPayloadIndexAndObjectOffsetAndMutexSRV] = CreateUAVBuffer("_ObjectPayloadIndexAndObjectOffsetAndMutex", 3, sizeof(UInt32));
    auto* rangePayloadSetPass = ClearRWStructuredBuffer(objectPayloadIndexAndObjectOffsetAndMutexUAV, 0, 1, 0);
    ClearRWStructuredBuffer(objectPayloadIndexAndObjectOffsetAndMutexUAV, 1, 3, 0);

    bool fastMode = (mShadowSettings->CacheMode == LocalLightShadowCacheMode::OnePass_Fast);

    // GPU instance culling pass
    {
        auto [objectPayloadDataBuffer, objectPayloadDataBufferUAV, objectPayloadDataBufferSRV] = CreateUAVBuffer("ObjectPayloadDataBuffer", 0, sizeof(ObjectPayloadData), NGIBufferUsage::CopyDst);

        if (fastMode)
        {
            auto* pass = mRED->AllocatePass("CullPerLightDrawUnits(Fast)");

            pass->SetProperty(NAME_ID("_GPUScenePrimitiveCullingDatas"), gpuScene->GetPrimitiveCullingDataBufferSRV());
            pass->SetProperty(NAME_ID("_GPUSceneObjectCullingDatas"), gpuScene->GetObjectCullingDataBufferSRV());
            pass->SetProperty(NAME_ID("_ObjectPayloadDatas"), objectPayloadDataBufferSRV);
            pass->SetProperty(NAME_ID("_LocalLightViewDatas"), viewDataBufferSRV);

            pass->SetProperty(NAME_ID("_OutVisibleObjectCommands"), visibleObjectCommandsBufferUAV);
            pass->SetProperty(NAME_ID("_OutVisibleObjectCommandCount"), visibleObjectCommandCountBufferUAV);
            pass->SetProperty(NAME_ID("_OutDrawIndirectArgs"), drawIndirectArgsBufferUAV);

            auto computeExecutionPayload = pass->Dispatch(mShadowSettings->LocalShadowMapInstanceCulling2ComputeShaderR, "CullPerLightDrawUnitsFast", 32, 1, 1);

            pass->OnCulling([=,
                                 objectIndexOffsetBuffer = objectIndexOffsetBuffer,
                                 objectIndexOffsetBufferUAV = objectIndexOffsetBufferUAV,
                                 tempObjectIndexOffsetBuffer = tempObjectIndexOffsetBuffer,
                                 tempObjectIndexOffsetBufferUAV = tempObjectIndexOffsetBufferUAV,
                                 visibleObjectCommandBufferMaxNum = visibleObjectCommandBufferMaxNum](REDPass* pass) {
                auto* drawUnits = drawUnitList->GetDrawUnits();

                // ObjectPayloadData (for normal DrawUnits)
                const auto& objectPayloadDatas = drawUnitList->GetObjectPayloadDatas2();
                auto normalObjectCount = static_cast<UInt32>(objectPayloadDatas.size());

                UInt32 objectPayloadDataBufferDataSize = static_cast<UInt32>(normalObjectCount * sizeof(ObjectPayloadData2));
                UInt32 objectPayloadDataBufferSize = std::max(objectPayloadDataBufferDataSize, 1u);
                objectPayloadDataBuffer->ModifySize(objectPayloadDataBufferSize);
                objectPayloadDataBufferSRV->ModifyRange(0, objectPayloadDataBufferSize);

                mRED->QueueBufferUpload(objectPayloadDataBuffer, 0, objectPayloadDatas.data(), objectPayloadDataBufferDataSize);

                // VisibleObjectsBuffer (for all DrawUnits)
                std::unordered_set<const void*> renderNodeMap;

                UInt32 maxObjectsPerPass = 0;
                for (UInt32 drawUnitIndex = 0; drawUnitIndex < drawUnits->GetSize(); drawUnitIndex++)
                {
                    const REDDrawUnit& drawUnit = drawUnits->At(drawUnitIndex);
                    maxObjectsPerPass += drawUnit.mInstanceCount * allocateLightNum;
                }

                *visibleObjectCommandBufferMaxNum = maxObjectsPerPass;

                UInt32 visibleObjectsBufferDataSize = static_cast<UInt32>(maxObjectsPerPass * sizeof(VisibleInstanceCommand));
                UInt32 visibleObjectsBufferSize = std::max(visibleObjectsBufferDataSize, 1u);
                visibleObjectCommandsBuffer->ModifySize(visibleObjectsBufferSize);
                visibleObjectCommandsBufferUAV->ModifyRange(0, visibleObjectsBufferSize);
                visibleObjectCommandsBufferSRV->ModifyRange(0, visibleObjectsBufferSize);

                // IndirectBuffer (for all DrawUnits)
                std::vector<CompactDrawCMD> indirectDrawCommands;
                indirectDrawCommands.resize(drawUnits->GetSize());

                for (UInt32 index = 0; index < drawUnits->GetSize(); index++)
                {
                    const REDDrawUnit& drawUnit = (*drawUnits)[index];
                    const RenderGeometry* geometry = drawUnit.mGeometry;

                    indirectDrawCommands[index] = CompactDrawCMD{
                        geometry->GetIndexCount(),
                        0,
                        geometry->GetIndexStart(),
                        static_cast<SInt32>(geometry->GetVertexStart()),
                        0,
                    };

                    drawUnit.mIndirectBufferOffset = index * sizeof(CompactDrawCMD);
                    drawUnit.mIndirectCount = 1u;
                    drawUnit.mIndirectStride = sizeof(CompactDrawCMD);
                }

                UInt32 indirectBufferDataSize = static_cast<UInt32>(drawUnits->GetSize() * sizeof(CompactDrawCMD));
                UInt32 indirectBufferSize = std::max(indirectBufferDataSize, 1u);
                drawIndirectArgsBuffer->ModifySize(indirectBufferSize);
                drawIndirectArgsBufferSRV->ModifyRange(0, indirectBufferSize);
                drawIndirectArgsBufferUAV->ModifyRange(0, indirectBufferSize);

                mRED->QueueBufferUpload(drawIndirectArgsBuffer, 0, indirectDrawCommands.data(), indirectBufferDataSize);

                // ObjectIndexOffsetBuffer
                UInt32 objectIndexOffsetBufferSize = std::max(static_cast<UInt32>(drawUnits->GetSize() * sizeof(UInt32)), 1u);
                objectIndexOffsetBuffer->ModifySize(objectIndexOffsetBufferSize);
                objectIndexOffsetBufferUAV->ModifyRange(0, objectIndexOffsetBufferSize);

                // TempObjectIndexOffsetBuffer
                tempObjectIndexOffsetBuffer->ModifySize(objectIndexOffsetBufferSize);
                tempObjectIndexOffsetBufferUAV->ModifyRange(0, objectIndexOffsetBufferSize);

                // ObjectIndexBuffer
                UInt32 objectIndexBufferSize = std::max(static_cast<UInt32>(maxObjectsPerPass * sizeof(UInt32)), 1u);
                objectIndexBuffer->ModifySize(objectIndexBufferSize);
                objectIndexBufferUAV->ModifyRange(0, objectIndexBufferSize);
                objectIndexBufferSRV->ModifyRange(0, objectIndexBufferSize);

                // LightIndexBuffer
                UInt32 lightIndexBufferSize = std::max(static_cast<UInt32>(maxObjectsPerPass * sizeof(UInt32)), 1u);
                LightIndexBuffer->ModifySize(lightIndexBufferSize);
                LightIndexBufferUAV->ModifyRange(0, lightIndexBufferSize);
                LightIndexBufferSRV->ModifyRange(0, lightIndexBufferSize);

                // Dispatch
            	pass->SetProperty("_UIntParams0", UInt4(viewCnt, normalObjectCount, maxObjectsPerPass, allocateLightNum));
                pass->SetProperty("_RangePayloadOffset", drawUnitList->GetRangePayloadOffset());
                rangePayloadSetPass->SetProperty(NAME_ID("_ClearParams"), UInt4(0, 1, drawUnitList->GetRangePayloadOffset(), 0));
            });
        }
        else
        {
            auto [DrawUnitsCountPerLight, DrawUnitsCountPerLightUVA, DrawUnitsCountPerLightSRV] = CreateUAVBuffer("DrawUnitsCountPerLight", viewCnt, sizeof(SInt32), NGIBufferUsage::StructuredBuffer | NGIBufferUsage::RWStructuredBuffer);
            auto [UseableDrawUnitsCountPerLight, UseableDrawUnitsCountPerLightUVA, UseableDrawUnitsCountPerLightSRV] = CreateUAVBuffer("UseableDrawUnitsCountPerLight", viewCnt, sizeof(SInt32), NGIBufferUsage::StructuredBuffer | NGIBufferUsage::RWStructuredBuffer);

            // Count Draw Untis for each light
            auto* CountPerLightDrawUnits = mRED->AllocatePass("CountPerLightDrawUnits");
            CountPerLightDrawUnits->SetProperty(NAME_ID("_GPUScenePrimitiveCullingDatas"), gpuScene->GetPrimitiveCullingDataBufferSRV());
            CountPerLightDrawUnits->SetProperty(NAME_ID("_GPUSceneObjectCullingDatas"), gpuScene->GetObjectCullingDataBufferSRV());
            CountPerLightDrawUnits->SetProperty(NAME_ID("_ObjectPayloadDatas"), objectPayloadDataBufferSRV);
            CountPerLightDrawUnits->SetProperty(NAME_ID("_LocalLightViewDatas"), viewDataBufferSRV);
            CountPerLightDrawUnits->SetProperty(NAME_ID("_OutDrawUnitsCountPerLight"), DrawUnitsCountPerLightUVA);
            CountPerLightDrawUnits->Dispatch(mShadowSettings->LocalShadowMapInstanceCulling2ComputeShaderR, "CountPerLightDrawUnits", 32, 1, 1);

            ClearRWStructuredBuffer(objectPayloadIndexAndObjectOffsetAndMutexUAV, 0, 3, 0);

            // Distribute Command PerLight
            auto DistributeCommandPerLight = mRED->AllocatePass("DistributeCommandPerLight");
            DistributeCommandPerLight->SetProperty(NAME_ID("_InDrawUnitsCountPerLight"), DrawUnitsCountPerLightSRV);
            DistributeCommandPerLight->SetProperty(NAME_ID("_UseableDrawUnitsPerLight"), UseableDrawUnitsCountPerLightUVA);
            DistributeCommandPerLight->Dispatch(mShadowSettings->LocalShadowMapInstanceCulling2ComputeShaderR, "DistributeCommandPerLight", 1, 1, 1);

            // CullPerLightDrawUnits
            auto* CullPerLightDrawUnits = mRED->AllocatePass("CullPerLightDrawUnits(Stable)");
            CullPerLightDrawUnits->SetProperty(NAME_ID("_GPUScenePrimitiveCullingDatas"), gpuScene->GetPrimitiveCullingDataBufferSRV());
            CullPerLightDrawUnits->SetProperty(NAME_ID("_GPUSceneObjectCullingDatas"), gpuScene->GetObjectCullingDataBufferSRV());
            CullPerLightDrawUnits->SetProperty(NAME_ID("_ObjectPayloadDatas"), objectPayloadDataBufferSRV);
            CullPerLightDrawUnits->SetProperty(NAME_ID("_LocalLightViewDatas"), viewDataBufferSRV);
            CullPerLightDrawUnits->SetProperty(NAME_ID("_OutVisibleObjectCommands"), visibleObjectCommandsBufferUAV);
            CullPerLightDrawUnits->SetProperty(NAME_ID("_OutVisibleObjectCommandCount"), visibleObjectCommandCountBufferUAV);
            CullPerLightDrawUnits->SetProperty(NAME_ID("_OutDrawIndirectArgs"), drawIndirectArgsBufferUAV);
            CullPerLightDrawUnits->SetProperty(NAME_ID("_UseableDrawUnitsPerLight"), UseableDrawUnitsCountPerLightUVA);

            auto computeExecutionPayload = CullPerLightDrawUnits->Dispatch(mShadowSettings->LocalShadowMapInstanceCulling2ComputeShaderR, "CullPerLightDrawUnitsFast", 32, 1, 1);

            CullPerLightDrawUnits->OnCulling([=,
                                              objectIndexOffsetBuffer = objectIndexOffsetBuffer,
                                              objectIndexOffsetBufferUAV = objectIndexOffsetBufferUAV,
                                              tempObjectIndexOffsetBuffer = tempObjectIndexOffsetBuffer,
                                              tempObjectIndexOffsetBufferUAV = tempObjectIndexOffsetBufferUAV,
                                              visibleObjectCommandBufferMaxNum = visibleObjectCommandBufferMaxNum](REDPass* pass) {

                auto* drawUnits = drawUnitList->GetDrawUnits();

                // ObjectPayloadData (for normal DrawUnits)
                const auto& objectPayloadDatas = drawUnitList->GetObjectPayloadDatas2();
                auto normalObjectCount = static_cast<UInt32>(objectPayloadDatas.size());

                UInt32 objectPayloadDataBufferDataSize = static_cast<UInt32>(normalObjectCount * sizeof(ObjectPayloadData2));
                UInt32 objectPayloadDataBufferSize = std::max(objectPayloadDataBufferDataSize, 1u);
                objectPayloadDataBuffer->ModifySize(objectPayloadDataBufferSize);
                objectPayloadDataBufferSRV->ModifyRange(0, objectPayloadDataBufferSize);

                mRED->QueueBufferUpload(objectPayloadDataBuffer, 0, objectPayloadDatas.data(), objectPayloadDataBufferDataSize);

                // VisibleObjectsBuffer (for all DrawUnits)
                std::unordered_set<const void*> renderNodeMap;

                UInt32 maxObjectsPerPass = 0;
                for (UInt32 drawUnitIndex = 0; drawUnitIndex < drawUnits->GetSize(); drawUnitIndex++)
                {
                    const REDDrawUnit& drawUnit = drawUnits->At(drawUnitIndex);
                    maxObjectsPerPass += drawUnit.mInstanceCount * allocateLightNum;
                }

                *visibleObjectCommandBufferMaxNum = maxObjectsPerPass;

                UInt32 visibleObjectsBufferDataSize = static_cast<UInt32>(maxObjectsPerPass * sizeof(VisibleInstanceCommand));
                UInt32 visibleObjectsBufferSize = std::max(visibleObjectsBufferDataSize, 1u);
                visibleObjectCommandsBuffer->ModifySize(visibleObjectsBufferSize);
                visibleObjectCommandsBufferUAV->ModifyRange(0, visibleObjectsBufferSize);
                visibleObjectCommandsBufferSRV->ModifyRange(0, visibleObjectsBufferSize);

                // IndirectBuffer (for all DrawUnits)
                std::vector<CompactDrawCMD> indirectDrawCommands;
                indirectDrawCommands.resize(drawUnits->GetSize());

                for (UInt32 index = 0; index < drawUnits->GetSize(); index++)
                {
                    const REDDrawUnit& drawUnit = (*drawUnits)[index];
                    const RenderGeometry* geometry = drawUnit.mGeometry;

                    indirectDrawCommands[index] = CompactDrawCMD{
                        geometry->GetIndexCount(),
                        0,
                        geometry->GetIndexStart(),
                        static_cast<SInt32>(geometry->GetVertexStart()),
                        0,
                    };

                    drawUnit.mIndirectBufferOffset = index * sizeof(CompactDrawCMD);
                    drawUnit.mIndirectCount = 1u;
                    drawUnit.mIndirectStride = sizeof(CompactDrawCMD);
                }

                UInt32 indirectBufferDataSize = static_cast<UInt32>(drawUnits->GetSize() * sizeof(CompactDrawCMD));
                UInt32 indirectBufferSize = std::max(indirectBufferDataSize, 1u);
                drawIndirectArgsBuffer->ModifySize(indirectBufferSize);
                drawIndirectArgsBufferSRV->ModifyRange(0, indirectBufferSize);
                drawIndirectArgsBufferUAV->ModifyRange(0, indirectBufferSize);

                mRED->QueueBufferUpload(drawIndirectArgsBuffer, 0, indirectDrawCommands.data(), indirectBufferDataSize);

                // ObjectIndexOffsetBuffer
                UInt32 objectIndexOffsetBufferSize = std::max(static_cast<UInt32>(drawUnits->GetSize() * sizeof(UInt32)), 1u);
                objectIndexOffsetBuffer->ModifySize(objectIndexOffsetBufferSize);
                objectIndexOffsetBufferUAV->ModifyRange(0, objectIndexOffsetBufferSize);

                // TempObjectIndexOffsetBuffer
                tempObjectIndexOffsetBuffer->ModifySize(objectIndexOffsetBufferSize);
                tempObjectIndexOffsetBufferUAV->ModifyRange(0, objectIndexOffsetBufferSize);

                // ObjectIndexBuffer
                UInt32 objectIndexBufferSize = std::max(static_cast<UInt32>(maxObjectsPerPass * sizeof(UInt32)), 1u);
                objectIndexBuffer->ModifySize(objectIndexBufferSize);
                objectIndexBufferUAV->ModifyRange(0, objectIndexBufferSize);
                objectIndexBufferSRV->ModifyRange(0, objectIndexBufferSize);

                // LightIndexBuffer
                UInt32 lightIndexBufferSize = std::max(static_cast<UInt32>(maxObjectsPerPass * sizeof(UInt32)), 1u);
                LightIndexBuffer->ModifySize(lightIndexBufferSize);
                LightIndexBufferUAV->ModifyRange(0, lightIndexBufferSize);
                LightIndexBufferSRV->ModifyRange(0, lightIndexBufferSize);

                // Dispatch
                CountPerLightDrawUnits->SetProperty("_UIntParams0", UInt4(viewCnt, normalObjectCount, maxObjectsPerPass, allocateLightNum));
                CountPerLightDrawUnits->SetProperty("_RangePayloadOffset", drawUnitList->GetRangePayloadOffset());
                DistributeCommandPerLight->SetProperty("_UIntParams0", UInt4(viewCnt, normalObjectCount, maxObjectsPerPass, allocateLightNum));
                DistributeCommandPerLight->SetProperty("_RangePayloadOffset", drawUnitList->GetRangePayloadOffset());
                CullPerLightDrawUnits->SetProperty("_UIntParams0", UInt4(viewCnt, normalObjectCount, maxObjectsPerPass, allocateLightNum));
                CullPerLightDrawUnits->SetProperty("_RangePayloadOffset", drawUnitList->GetRangePayloadOffset());

                rangePayloadSetPass->SetProperty(NAME_ID("_ClearParams"), UInt4(0, 1, drawUnitList->GetRangePayloadOffset(), 0));
            });
        }
    }

    // AllocateCommandInstanceOutputSpace
    {
        auto* pass = mRED->AllocatePass("AllocateCommandInstanceOutputSpace", mIsNoCulling);
        {
            pass->SetProperty(NAME_ID("_DrawIndirectArgs"), drawIndirectArgsBufferSRV);
            pass->SetProperty(NAME_ID("_OutOffsetBufferCount"), offsetBufferCountBufferUAV);
            pass->SetProperty(NAME_ID("_OutObjectIndexOffsetBuffer"), objectIndexOffsetBufferUAV);
            pass->SetProperty(NAME_ID("_OutTempObjectIndexOffsetBuffer"), tempObjectIndexOffsetBufferUAV);
            auto* computeExecutionPayload = pass->Dispatch(mShadowSettings->LocalShadowMapInstanceCullingComputeShaderR, NameID("AllocateCommandInstanceOutputSpace"), 0, 0, 0);

            pass->OnCulling([=](REDPass* pass) {
                auto* drawUnits = drawUnitList->GetDrawUnits();

                pass->SetProperty("_UIntParams0", UInt4(0, 0, 0, drawUnits->GetSize()));

                UInt3 groupSize;
                mShadowSettings->LocalShadowMapInstanceCullingComputeShaderR->GetThreadGroupSize("AllocateCommandInstanceOutputSpace", groupSize.x, groupSize.y, groupSize.z);
                computeExecutionPayload->ModifyThreadGroupCount(math::DivideAndRoundUp(drawUnits->GetSize(), groupSize.x), 1, 1);
            });
        }
    }

    // InitIndirectArgs1D
    {
        UInt3 groupSize;
        mShadowSettings->LocalShadowMapInstanceCullingComputeShaderR->GetThreadGroupSize("OutputCommandInstanceLists", groupSize.x, groupSize.y, groupSize.z);

        auto* pass = mRED->AllocatePass("InitIndirectArgs1D", mIsNoCulling);
        {
            pass->SetProperty(NAME_ID("_UIntParams0"), UInt4(1, groupSize.x, 0, 0));
            pass->SetProperty(NAME_ID("_InputCountBuffer"), visibleObjectCommandCountBufferSRV);
            pass->SetProperty(NAME_ID("_OutIndirectDispatchArgs"), outputPassIndirectArgsUAV);
            pass->Dispatch(mShadowSettings->InitIndirectArgs1DR, "InitIndirectArgs1D", 1, 1, 1);
        }
    }

    // OutputCommandInstanceLists
    {
        auto* pass = mRED->AllocatePass("OutputCommandInstanceLists", mIsNoCulling);
        {
            pass->SetProperty(NAME_ID("_VisibleObjectCommands"), visibleObjectCommandsBufferSRV);
            pass->SetProperty(NAME_ID("_VisibleObjectCommandCount"), visibleObjectCommandCountBufferSRV);
            pass->SetProperty(NAME_ID("_OutTempObjectIndexOffsetBuffer"), tempObjectIndexOffsetBufferUAV);
            pass->SetProperty(NAME_ID("_OutLightInfoBuffer"), LightIndexBufferUAV);
            pass->SetProperty(NAME_ID("_OutObjectIndexBuffer"), objectIndexBufferUAV);
            pass->DispatchIndirect(mShadowSettings->LocalShadowMapInstanceCullingComputeShaderR, NameID("OutputCommandInstanceLists"), outputPassIndirectArgs, 0);
        }
    }

    outObjectIndexBufferView = objectIndexBufferSRV;
    outLightViewIndexBufferView = LightIndexBufferSRV;
}

void LocalLightShadowMapProxy::GenerateDrawViewportsArray(std::array<NGIViewport, 16>& arrayViewport, std::array<NGIScissor, 16>& arrayScissor)
{
    const auto& viewports = mCache->mViewports;
    const auto& scissors = mCache->mScissors;

    for (int i = 0; i < 16 && i < viewports.size(); i++)
    {
        arrayViewport[i] = viewports[i];
        arrayScissor[i] = scissors[i];
    }
}

void LocalLightShadowMapProxy::RenderShadowInOnePass(REDObjectType objType)
{
    bool renderStatic = objType == REDObjectType::Static;
    const std::string regionName = renderStatic ? "LocalLightShadow_Static" : "LocalLightShadow_Dynamic";
    QUICK_SCOPED_CPU_TIMING("LocalLightShadow_StaticOrDynamic");

    // 0. Generate light view data
    FrameStdVector<LocalLightViewData> viewDatas;
    GenerateLightViewDatas(viewDatas, renderStatic);
    if (viewDatas.empty())
        return;
    NGIBufferView* viewDataBufferSRV = CreateStructuredBuffer(viewDatas);

    mRED->BeginRegion(regionName);

    // 1. Prepare gpu culling data
    REDCullingResult* roughCullingResult = mRED->CullLocalLightShadowCaster(mRenderWorld, const_cast<RenderCamera*>(mRenderPipeline->GetRenderCamera()), &mObjCullingBounding, objType);
    auto* drawUnitList = roughCullingResult->GenerateDrawUnitList(REDDrawUnitsDesc{NAME_ID("shadow_all"), 0, UINT16_MAX, RenderEffectTag::CastShadow, RenderNodeType{0}, nullptr, mRenderPipeline->GetRenderCamera()});

    // 2. Prepare draw unit list buffers (gpu culling and generate draw indirect buffer to draw unit list ptr)
    REDBufferView* objectIndexBufferViewSRV = nullptr;
    REDBufferView* lightViewIndexBufferViewSRV = nullptr;
    GenerateDrawUnitListDrawIndirectCmdBuffer(drawUnitList, viewDataBufferSRV, static_cast<UInt32>(viewDatas.size()), objectIndexBufferViewSRV, lightViewIndexBufferViewSRV);

    // 3. Get viewports and scissors
    std::array<NGIViewport, 16> viewports;
    std::array<NGIScissor, 16> scissors;
    GenerateDrawViewportsArray(viewports, scissors);

    // 4. Render shadow depth
    bool isPunctualLight = true;
    REDTextureView* shadowTexView = renderStatic ? mCache->GetStaticShadowTextureView() : mCache->GetDynamicShadowTextureView();
    NGIClearValue clearValue{};
    clearValue.depthStencil.depth = 1.f;
    clearValue.depthStencil.stencil = 0;
    REDDepthStencilTargetDesc depthStencilTargetDesc{
        shadowTexView,
        NGILoadOp::Load,
        NGIStoreOp::Store,
        NGILoadOp::Load,
        NGIStoreOp::DontCare,
        clearValue,
    };

    // Render shadow depth
    mRED->BeginRenderPass("LocalLightShadowDepth", 0, nullptr, &depthStencilTargetDesc, mIsNoCulling);
    {

        REDPass* shadowPass = mRED->AllocateSubRenderPass("shadow", 0, nullptr, 0, nullptr, REDPassFlagBit::NeedDepth);

        shadowPass->SetProperty(NAME_ID("PUNCTUAL_LIGHT"), isPunctualLight);   // PUCTUAL_LIGHT here means "local light", including spot & point lights
        shadowPass->SetProperty(NAME_ID("SOFT_SHADOW"), false);
        shadowPass->SetProperty(BuiltInProperty::CE_INSTANCING, true);
        shadowPass->SetProperty(NAME_ID("USE_MULTI_VIEWPORT"), true);
        shadowPass->SetProperty(NAME_ID("_LocalLightViewDatas"), viewDataBufferSRV);

        auto drawUnitsDesc = REDRenderDrawUnitsDesc{drawUnitList,
                                                    NGIRasterizationStateDesc{
                                                        FillMode::Solid,
                                                        CullMode::None,
                                                        FaceOrder::CW,
                                                        false,
                                                        false,
                                                        false,
                                                        0,
                                                        0,
                                                        0,
                                                        0,
                                                        0,
                                                        RasterizationMode::DefaultRaster,
                                                        0,
                                                    }};

        shadowPass->OnCulling([=](REDPass* pass) {
            shadowPass->SetProperty(NAME_ID("_ObjectIndexBuffer"), objectIndexBufferViewSRV);
            shadowPass->SetProperty(NAME_ID("_LightViewIndexBuffer"), lightViewIndexBufferViewSRV);
        });
        drawUnitsDesc.InitViewport = viewports;
        drawUnitsDesc.InitScissor = scissors;

        shadowPass->RenderDrawUnits(drawUnitsDesc);

        mRED->EndRenderPass();
    }

    mRED->EndRegion();
}

UInt32 LocalLightShadowMapProxy::GetLightCameraNum(bool renderStatic)
{
    if (!renderStatic)
        return static_cast<UInt32>(mSpotLights.size() + mPointLights.size() * 6);

    UInt32 viewCnt = 0;
    for (UInt16 i = 0; i < mSpotLights.size(); i++)
    {
        if (bool lightDirty = mSpotLightDirty[i]; lightDirty)
        {
            viewCnt++;
        }
    }
    for (UInt16 i = 0; i < mPointLights.size(); i++)
    {
        if (bool lightDirty = mPointLightDirty[i]; lightDirty)
        {
            viewCnt += 6;
        }
    }
    return viewCnt;
}

void LocalLightShadowMapProxy::RenderLightsShadows()
{
    CleanStaticSubmaps();

    if (mCache->bOnePassShadow)
    {
        RenderShadowInOnePass(REDObjectType::Static);
    }
    else
    {
        RenderShadowForEachLight(REDObjectType::Static);
    }

    CopyStaticToDynamic();

    if (mCache && mCache->mEnableDynamicShadow)
    {
        if (mCache->bOnePassShadow)
        {
            RenderShadowInOnePass(REDObjectType::Dynamic);
        }
        else
        {
            RenderShadowForEachLight(REDObjectType::Dynamic);
        }
    }
}
}   // namespace cross
