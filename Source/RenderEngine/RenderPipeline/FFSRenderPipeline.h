#pragma once
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "FFSRenderPipelineSetting.h"
#include "Effects/GPass.h"
#include "RenderEngine/VirtualTexture/VirtualTextureFeedback.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.generated.h"
#include "RenderEngine/RenderPipeline/Effects/ViewModeVisualization.h"

namespace cross {
class CEMeta(Reflect) RENDER_ENGINE_API FFSRenderPipeline : public IRenderPipeline
{
    CEGeneratedCode(FFSRenderPipeline)
    friend struct ParticlePass;
public:
    ~FFSRenderPipeline();

    virtual void Initialize(WorldRenderPipeline* worldRenderPipeline, RenderWorld* world, RenderingExecutionDescriptor* RED) override;

    virtual void UpdatePipelineSharedContext() override;

    virtual void PrepareRenderData() override;

    virtual void FreeFGRes() override;

    virtual REDTextureView* GetDepthPyramid() const override
    {
        return mGPassDepthPyramidReprojectionView;
    };

    void UpdateVTContext(REDPass* pass);
    void AllocateVTResourceAndFeedBackBuffer();

    bool ShouldRenderSkyAtmosphere();
    bool ShouldRenderScreenSpaceFog() const;
    bool ShouldUseMultiLayerFog() const;
    bool ShouldRenderVolumetricFog() const;
    bool ShouldComputeDistantSkyLight();
    void UpdateSkyAtmosphereAdvancedVars();

    virtual void UpdateCloudShadowContext(REDPass * pass) const override;

   /* void UpdateSkyAtmosphereContext(REDPass* pass);*/
    // Make screen black on FFS IOS repositioning, only used by FfsSystemG
    void SetPureColor(bool enable, Float4 color = {0.0f, 0.0f, 0.0f, 1.0f})
    {
        mEnablePureColor = enable;
        mPureColor = color;
    };

    bool IsPureColorEnabled() const
    {
        return mEnablePureColor;
    }

    Float4 GetPureColor() const {
        return mPureColor;
    }

    void SetFSR2Enabled(bool enable) {
        mUpscale = enable;
        auto setting = dynamic_cast<FFSRenderPipelineSetting*>(const_cast<RenderPipelineSetting*>(mSetting));
        setting->mFSR2Setting.enable = enable;
    }

     void SetShadowEnabled(bool enable)
     {
        auto setting = dynamic_cast<FFSRenderPipelineSetting*>(const_cast<RenderPipelineSetting*>(mSetting));
        mCacheShaodwBool = setting->Shadow;
        setting->Shadow = enable;
     }

     void RollbackShadowEnabled()
     {
        auto setting = dynamic_cast<FFSRenderPipelineSetting*>(const_cast<RenderPipelineSetting*>(mSetting));
        setting->Shadow = mCacheShaodwBool;
     }

     void SetFoliageDrawingEnabled(bool enable)
     {
        auto setting = dynamic_cast<FFSRenderPipelineSetting*>(const_cast<RenderPipelineSetting*>(mSetting));
        mCacheFoliageBool = setting->EnableFoliageDrawing;
        setting->EnableFoliageDrawing = enable;
     }
     void SetSmartGIEnabled(bool enable)
     {
         auto setting = dynamic_cast<FFSRenderPipelineSetting*>(const_cast<RenderPipelineSetting*>(mSetting));
         setting->mIndirectLightingCompositeSettings.mSmartGISetting.enable = enable;
     }
     void RollbackFoliageDrawingEnabled()
     {
        auto setting = dynamic_cast<FFSRenderPipelineSetting*>(const_cast<RenderPipelineSetting*>(mSetting));
        setting->EnableFoliageDrawing = mCacheFoliageBool;
     }

    virtual const FFSRenderPipelineSetting* GetSetting() const override
    {
        return dynamic_cast<const FFSRenderPipelineSetting*>(mSetting);
    }
    virtual const LocalLightShadowCacheSettings* GetLocalLightShadowCacheSetting() const override
    {
        return &(dynamic_cast<const FFSRenderPipelineSetting*>(mSetting))->mLocalLightShadowMapSettings;
    }
    void CompilePassGraph();
    void CompilePassGraph_MaterialEditorPreview();
    void ExecutePasses();

    REDBufferView* GetScreenBlurMask() override
    {
        return nullptr;// mCloudRes.mUnderCloudStat;
    }

    void RegisterPassAfterTonemap(GeneralPass pass);

    FoliageGpuDriven& GetFoliageGpuDriven()
    {
        return mFoliageGpuDriven;
    }

    GPass& GetGPass();
    auto& GetGPassDepthPyramidLastFrameView()
    {
        return mGPassDepthPyramidLastFrameView;
    }
    auto& GetGPassDepthPyramidCurrFrameView()
    {
        return mGPassDepthPyramidCurrFrameView;
    }
    auto& GetOutlineMat()
    {
        return mOutlineMtl;
    }
    auto& GetWireFrameMat()
    {
        return mWireframeMtl;
    }
    auto& GetViewModeVisualizationMtl()
    {
        return mViewModeVisualizationMtl;
    }
    auto& GetSceneColorPreserveTex()
    {
        return mSceneColorTex;
    }
    SubSampleShading& GetSubSampleShading() 
    {
        return mSubSampleShading;
    }
    std::vector<std::pair<ecs::EntityID, UInt32>>& GetDirectionalLightList() 
    {
        return mBuildTileCulling.mDirectionalLightList;
    }

    bool IsRenderPipelineSuportMSAA() const
    {
        return mIsSupportMSAA && !IsReflectionProbePipeline();
    }

    bool IsViewModeNeedPostProcessing() const
    {
        auto& viewMode = GetSetting()->viewModeVisualizeType;
        return viewMode == ViewModeVisualizeType::Lit ||
            viewMode == ViewModeVisualizeType::LightingOnly ||
            viewMode == ViewModeVisualizeType::DetailLighting ||
            viewMode == ViewModeVisualizeType::Reflections ||
            viewMode == ViewModeVisualizeType::GlobalIllumination ||
            viewMode == ViewModeVisualizeType::SeparateTranslucencyRGB ||
            viewMode == ViewModeVisualizeType::SceneColor ||
            viewMode == ViewModeVisualizeType::GILighting;

    }

    auto& GetAOView()
    {
        return mAOView;
    }

    auto& GetReflectionIndirectView()
    {
        return mReflectionIndirectView;
    }

    auto& GetEmissiveColorView()
    {
        return mCurEmissiveColorRTView;
    }
    auto GetEmissiveColorForSmartGI()
    {
        return std::make_tuple(mPreEmissiveColorRT, mCurEmissiveColorRTView);
    }

    auto& GetGIColorView()
    {
        return mGIColorView;
    }

    auto GetSmartGIViews()
    {
        return std::make_tuple(mSmartGIDiffuseView, mSmartGISpecularView);
    }

    auto& GetViewModeVisualization()
    {
        return mViewModeVisualization;
    }

    auto& GetCloudResources()
    {
        return mCloudRes;
    }

protected:
    void UpdateSetting(const RenderPipelineSetting* setting) override;
    void InitializePassSettings();

    void Assemble(REDTextureView* targetView) override;

    void AssemblePreDepth(REDTextureView*& depthView);
    void AssembleShadow(REDTextureView* depthView, std::array<REDTextureView*, 4>& gBufferViews, ShadowProperties* shadowData, REDTextureView*& shadowMaskView);
    void AssembleGPass(std::array<REDTextureView*, 4>& gBufferViews, REDTextureView*& sceneColorView, REDTextureView* depthStencilView, REDTextureView* depthOnlyView, REDTextureView* depthPyramid,
                       REDTextureView * &manuallyResolvedDepth, REDTextureView * &reactiveMask);
    void AssembleDeferredLighting(const ShadowProperties& shadowData, REDTextureView* shadowMaskView, const std::array<REDTextureView*, 4>& gBufferViews, REDTextureView* depthStencilView, REDTextureView* depthOnlyView,
                          REDTextureView* contactShadowView, REDTextureView* sceneColorView,
                          bool contactShadowTransmissionEnable);
    void AssembleGPUDrivenLight(const std::array<REDTextureView*, 4>& gBufferViews, REDTextureView* depthStencilView, REDTextureView* depthOnlyView,
                                REDTextureView* sceneColorView,REDBufferView* clustersBufferView);
    void AssembleForwardTransparent(const ShadowProperties& shadowData, REDTextureView* shadowMaskView, REDTextureView* depthStencilView, REDTextureView* depthOnlyView, REDTextureView* contactShadowView, REDTextureView* aoView,
                                    REDTextureView* colorLastFrame, REDTextureView* sceneColorReadOnly, REDTextureView* sceneColorView, REDTextureView* ssprView, REDTextureView* integratedFogView, REDTextureView*& separateTranslucencyView,
                                    REDTextureView*& reactiveView);
    void AssembleSkyPass(REDTextureView* depthStencilView, REDTextureView* depthOnlyView, REDTextureView* sceneColorView, REDTextureView* integratedFogView, REDTextureView*& separateTranslucencyView, REDTextureView*& reactiveView);

    void AssembleForwardImpl(std::string_view subPassName, NameID forwardGroupID, const ShadowProperties& shadowData, REDTextureView* shadowMaskView, REDTextureView* depthStencilView, REDTextureView* depthOnlyView,
                                 REDTextureView* contactShadowView, REDTextureView* aoView,
                                    REDTextureView* colorLastFrame, REDTextureView* sceneColorReadOnly, REDTextureView* sceneColorView, REDTextureView* ssprView, REDTextureView* integratedFogView, REDTextureView*& separateTranslucencyView,
                                 REDTextureView*& reactiveView, bool transparent, bool use_msaa = true, bool gpuCulling = false);
    void AssembleFFTOcean(REDTextureView* depthStencilView, REDTextureView* sceneColorReadOnly, REDTextureView* sceneColorView);

    
    /*
     * @param isForSkyLightRealtimeCapture: Used for capture sky light cubemap, since pp height fog and skylight cubemap capture render exp fog use quiet different logic
     */
    void AssembleScreenSpaceFogPass(REDTextureView * sceneView, REDTextureView * depthOnlyView, REDTextureView * volumetricFogView, bool isForSkyLightRealtimeCapture = false);
    void AssembleScreenSpaceFogPass(ScreenSpaceFog & fogPass, REDTextureView * sceneView, REDTextureView * depthOnlyView, REDTextureView * volumetricFogView, bool isForSkyLightRealtimeCapture = false);
    void AssembleVoxelVolumetricFogPass(REDTextureView* depthOnlyView, const ShadowProperties& shadowData, REDTextureView*& integratedView, REDTextureView*& volumetricFogView);
    void AssembleFogAndCloudApply(REDTextureView* depthOnlyView, REDTextureView* volumetricFogView, REDTextureView*& sceneView, bool isSkyLight);
    void AssembleGTAO(RenderWorld* world, REDTextureView* depthStencilView, std::array<REDTextureView*, 4>& gBufferViews);
    bool AssembleContactShadow(REDTextureView * depthView, REDTextureView * gBuffer1MapView, REDTextureView * gBuffer2MapView, REDTextureView * &contactShadowView);
    void AssembleMassiveLightsShadow(REDTextureView * depthOnlyView, REDTextureView * gBuffer1MapView, REDTextureView * gBuffer2MapView, 
        cross::NGIBufferView* lightInstanceView, REDBufferView*& clustersBufferView, REDBufferView*& shadowBufferView);
    REDTextureView* AssembleDepthPyramid(REDTextureView * depthView, const RenderCamera* camera, bool reverseZ = true, bool extendToNextFrame = false, UInt16 firstArraySlice = 0, bool closestDepth = false);
    void AssembleDrawRenderTextureToAtlasPass();
    void AssembleVolumetricLight(ShadowProperties* shadowData, REDTextureView* sceneDepth, REDTextureView* dstColor);
    void AssembleSkyAtmosphereDistantSkyLightPass();
    void AssembleSkyAtmospherePass(ShadowProperties* shadowData, REDTextureView* color, REDTextureView*& sceneDepthStencilView);
    void AssembleDecalPass(std::array<REDTextureView*, 4>& gBufferViews, REDTextureView* color, REDTextureView*& sceneDepthStencilView);
    void AssembleCloudPass(REDTextureView*& depth, CLOUD_PIPE_METHOD method = CLOUD_PIPE_METHOD::SPATIAL_AND_TEMPORAL, UInt2 overrideScreenSize = {0, 0});

    void AssembleSSPR(REDTextureView*& sceneColorView, REDTextureView*& sceneDepthStencilView);
    void AssembleSSR(std::array<REDTextureView*, 4>& gBufferViews, REDTextureView* sceneViewLastFrame, REDTextureView* sceneDepthStencilView, REDTextureView* depthPyramid);
    void AssembleBuildTileCulling();
    // Handle low roughness specular indirect reflection
    void AssembleReflectionIndirect(std::array<REDTextureView*, 4>& gBufferViews, REDTextureView*& reflecIndirectViewOut, REDTextureView* sceneDepthStencilView);
    REDTextureView* AllocateColorViewFromLastFrame(UInt32 viewWidth, UInt32 viewHeight);

    // Handle diffuse indirect gi and high roughness specular indirect gi
    // SmartGI Begin
    void AssembleSmartGI(const std::array<REDTextureView*, 4>& gBufferViews, REDTextureView* depthView, REDTextureView* hizDepthView, REDTextureView* sceneColorView, REDTextureView* aoView, const ShadowProperties* shadowProperties);
    void AssembleSmartGIFinish(REDTextureView* sceneColorView);
    // SmartGI End

    // To composite ReflectionIndirect and SmartGI indirect lighting
    void AssembleIndirectLightingComposite(const std::array<REDTextureView*, 4>& gBufferViews, REDTextureView* depthView, REDTextureView*& sceneColorViewPrev, REDTextureView* sceneColorViewAfter);

    void AssembleAmbientOcclusionPass(RenderWorld * world, REDTextureView * depthStencilView, std::array<REDTextureView*, 4> & gBufferViews);
    
    // Handle all kinds of Visualization in one pass
    void AssembleViewModeVisualization(REDTextureView* visOutputView, REDTextureView* sceneColorView);
    void CopyEmissiveAfterGPass(const GameContext& gameContext, REDTextureView* sceneColorView);

    void AssembleSeparateTranslucencyBlendPass();
#ifdef WIN32
    void AssembleDLSS();
    void AssembleFSR3();
    void AssembleDLSSG(bool isFirst=true);
#endif
public:
    static bool sTODChanging;
    static bool sEnableSkyLightVoxel;

private:
    const static inline NameID gDeferredID = "gpass";

    REDBuffer* mVTFeedbackBuffer = nullptr;
    REDBufferView* mVTFeedbackBufferView = nullptr;
    REDBufferView* mNumCulledLightsGrid = nullptr;
    REDBufferView* mCulledLightDataGrid = nullptr;
    REDBufferView* mScreenBlurMaskView  = nullptr;

    CloudResources mCloudRes;

    MaterialR* mCombineLightingMtl = nullptr;
    MaterialR* mOutlineMtl = nullptr;
    MaterialR* mWireframeMtl = nullptr;
    MaterialR* mViewModeVisualizationMtl = nullptr;
    ComputeShaderR* mCollectShadowMaskShader = nullptr;

    ComputeShaderR* mVTFeedbackComputeShader = nullptr;
    
    void PrepareAtmosphereSunLight();
    void PreComputeSkyAtmosphere(const RenderCamera* Camera);
    void ProcessPerPixelFogTranmittance(REDTextureView * sceneView);
    

    void UpdateReflectionProbe();
    void UpdateBuffer(NGIBuffer* buffer, void* data, UInt32 size, NGIResourceState resourceState = NGIResourceState::ComputeShaderShaderResource | NGIResourceState::PixelShaderShaderResource);
    // the functionality is replace by update Light and update SkyLight
    // void UpdatePrecomputeLightContext(RenderContext& context);
    void UpdateShadowContext(REDPass* pass, const ShadowProperties* shadowInfo) const override;

    void UpdateMassiveLightsShadowContext(REDPass * lightingPass, REDBufferView * clustersBufferView, REDBufferView * shadowBufferView, REDTextureView * gBuffer2MapView);

    Float4x4 windData0, windData1, windData2;

    bool mIsSupportMSAA = false;

    bool mIsSplitReflectionProcess = false;

    bool mEnablePureColor = false;
    Float4 mPureColor = {0.0f, 0.0f, 0.0f, 1.0f};

    REDTexture* mCloudNoiseTex = nullptr;
    REDTextureView* mCloudSpreadView = nullptr;
    REDTexture* mSceneColorTex = nullptr;

    REDTexture* mGPassDepthPyramidLastFrame = nullptr;
    REDTextureView* mGPassDepthPyramidLastFrameView = nullptr;
    REDTextureView* mGPassDepthPyramidCurrFrameView = nullptr;
    REDTextureView* mGPassDepthPyramidReprojectionView = nullptr;

    // AO Textures, xyz = bent normal, w = [0,1] aoValue
    REDTextureView* mAOView{nullptr};

    // Screen Reflection Textures
    REDTextureView* mSSPRView{nullptr};
    REDTextureView* mSSRView{nullptr};
    REDTextureView* mReflectionIndirectView{nullptr};

    // SmartGI Textures
    REDTextureView* mSmartGIDiffuseView{nullptr};
    REDTextureView* mSmartGISpecularView{nullptr};

    // For ViewModeVisualization
    REDTextureView* mGIColorView{nullptr};

    // Emissive Color GI
    REDTexture* mPreEmissiveColorRT{nullptr};
    REDTextureView* mCurEmissiveColorRTView{nullptr};
#ifdef WIN32
    DLSSPass mDLSSPass;
    DLSSGPass mDLSSGPass;
    FSR3SRPass mFSR3SRPass;
#endif
    SeparateTranslucencyBlendPass mSeparateTranslucencyBlendPass;
    GPass mGPass;
    MultiScaleVolumetricOcclusion mMultiScaleVoPass;
    AOPass mAOPass;
    TileBasedDefferedLighting mTileBasedDeferredLighting;
    SmartGIPass mSmartGIPass{this};
    ShadowPass mShadowPass;
    ShadowProperties mShadowProperties;
    ContactShadow mContactShadow;
    MassiveLightsShadow mMassiveLightsShadow;
    REDBuffer* mRayDebugBuffer = nullptr;
    VolumetricFog mVolumetricFog;
    ExponentialFog mExponentialFog;
    MultiLayerFog mMultiLayerFog;
    SkyCloud mSkyCloud;
    ScreenSpacePlaneReflection mScreenSpacePlaneReflection;
    ScreenSpaceReflection mSSR;
    DepthPyramidPass mDepthPyramidPass;
    DrawRenderTextureToAtlasPass mDrawRenderTextureToAtlasPass;
    ApplyDBufferDataPass mApplyDBufferDataPass;
    FoliageGpuDriven mFoliageGpuDriven;
    BuildTileCulling mBuildTileCulling;
    ReflectionIndirect mReflectionIndirect;
    IndirectLightingComposite mIndirectLightingComposite;
    AmbientOcclusion mAmbientOcclusion;
    ViewModeVisualization mViewModeVisualization;

    SubSampleShading mSubSampleShading;
    std::unique_ptr<VTFeedbackManager> mVTFeedBackManager;
    friend class ShadowPass;
    friend class VolumetricFog;
    friend class SmartVoxelRenderer;
    friend class SkyLightSystemR;

    bool mCacheShaodwBool = false;
    bool mCacheFoliageBool = false;

    FrameStdHashMap<HashString, GeneralPass> mPassAfterTonemap;
};

}   // namespace cross

extern "C" RENDER_ENGINE_API void FFSRenderPipeline_SetTODChanging(bool bTODChanging);

extern "C" RENDER_ENGINE_API void FFSRenderPipeline_SetEnableSkyLightVoxel(bool bEnableSkyLightVoxel);