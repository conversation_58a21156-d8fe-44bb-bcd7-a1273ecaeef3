#pragma once
#include "WorldRenderPipeline.h"
#include "RenderEngine/StellarMeshScene/StellarMeshScene.h"
#include "RenderEngine/VirtualShadowMap/VirtualShadowMapPhysicalPagePool.h"
#include "RenderEngine/RenderPipeline/Effects/ParticleSystemGpuDriven.h"

namespace cross {
// this class effectively a counterpart of UE's FScene, ie hold variables for render a scene
// the Pipeline inside the world renderpipeline is more like per-view;
class RENDER_ENGINE_API FFSWorldRenderPipeline : public WorldRenderPipeline
{
public:
    virtual void Initialize(RenderWorld* world, RenderingExecutionDescriptor* RED) override;

    virtual void Assemble(FrameParam* frameParam) override;

    virtual void UpdateSetting(const RenderPipelineSetting* setting) override;

    virtual GPUScene* GetGPUScene() override
    {
        return &mGPUScene;
    }

    StellarMeshScene* GetStellarMeshScene() override
    {
        return &mStellarMeshScene;
    }

#ifdef NGI_ENABLE_RAY_TRACING
    RayTracingScene* GetRayTracingScene() override
    {
        return &mRayTracingScene;
    }
#endif

    VirtualShadowMapPhysicalPagePool* GetVirtualShadowMapPhysicalPagePool()
    {
        return &mVirtualShadowMapPhysicalPagePool;
    }
    ParticleSystemGpuDriven* GetParticleSystemGpuDriven()
    {
        return &mParticleSystemGpuDriven;
    }

    /*
     *  precompute Skyatmosphere and skylight textures and buffers (if SkyLightComponent->RealtimeCapture is on)
     */
    void PreComputeSkyTextures();

    void UpdateSkyLightContext();

private:

    void UpdateWorldCameraContext() const;

    GPUScene mGPUScene;

#ifdef NGI_ENABLE_RAY_TRACING
    RayTracingScene mRayTracingScene;
#endif
    
    StellarMeshScene mStellarMeshScene;
    VirtualShadowMapPhysicalPagePool mVirtualShadowMapPhysicalPagePool;
    ParticleSystemGpuDriven mParticleSystemGpuDriven;
};
}   // namespace cross