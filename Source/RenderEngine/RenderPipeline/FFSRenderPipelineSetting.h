#pragma once
#include "RenderEngine/RenderEngineForward.h"
#include "CECommon/Common/RenderPipelineSetting.h"
#include "Resource/Resource.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/ComputeShaderR.h"
#include "RenderEngine/RenderWorldConst.h"

#include "RenderEngine/RenderPipeline/RenderPipelineBaseSetting.h"
#include "RenderEngine/RenderPipeline/Effects/MultiScaleVolumetricOcclusion.h"
#include "RenderEngine/RenderPipeline/Effects/AOPass.h"
#include "RenderEngine/RenderPipeline/Effects/TileBasedDefferedLighting.h"
#include "RenderEngine/RenderPipeline/Effects/SmartGIPass.h"
#include "RenderEngine/RenderPipeline/Effects/IndirectLightingComposite.h"
#include "RenderEngine/RenderPipeline/Effects/Shadow.h"
#include "RenderEngine/RenderPipeline/Effects/ContactShadow.h"
#include "RenderEngine/RenderPipeline/Effects/TemporalAntiAliasing.h"
#include "RenderEngine/RenderPipeline/Effects/FSR2.h"
#include "RenderEngine/RenderPipeline/Effects/DLSS.h"
#include "RenderEngine/RenderPipeline/Effects/DLSSG.h"
#include "RenderEngine/RenderPipeline/Effects/SeparateTranslucencyBlend.h"
#include "RenderEngine/RenderPipeline/Effects/FSR3.h"
#include "RenderEngine/RenderPipeline/Effects/DepthPyramidPass.h"
#include "RenderEngine/RenderPipeline/Effects/DrawRenderTextureToAtlas.h"
#include "RenderEngine/RenderPipeline/Effects/ApplyDBufferData.h"
#include "RenderEngine/RenderPipeline/Effects/FoliageGpuDriven.h"
#include "RenderEngine/RenderPipeline/Effects/SkyCloud.h"
#include "RenderEngine/RenderPipeline/Effects/ScreenSpacePlaneReflection.h"
#include "RenderEngine/RenderPipeline/Effects/DepthOfField.h"
#include "RenderEngine/RenderPipeline/Effects/BuildTileCulling.h"
#include "RenderEngine/RenderPipeline/Effects/MassiveLightsShadow.h"
#include "RenderEngine/RenderPipeline/Effects/SkyAtmosphereSettings.h"
#include "RenderEngine/RenderPipeline/Effects/SubSampleShading.h"
#include "RenderEngine/RenderPipeline/Effects/IndirectLightingComposite.h"
#include "RenderEngine/RenderPipeline/Effects/StellarMesh/StellarMeshRenderPipeline.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.generated.h"
namespace cross
{
struct CEMeta(Reflect) RENDER_ENGINE_API FFSRenderPipelineSetting : public RenderPipelineBaseSetting
{
    CEGeneratedCode(FFSRenderPipelineSetting)
    CEMeta(Reflect)
    FFSRenderPipelineSetting();

    RenderPipelineSetting* Clone() const override;

    void Initialize() override;

    bool UseFSRorDLSS() const;

    float GetUpScaleValue(ViewType type) const;

    CE_Virtual_Serialize_Deserialize;

    RENDER_PIPELINE_VALUE(SInt32, GBufferDebugMode, 0, "GBuffer Debug Mode", "", "General Settings");
    RENDER_PIPELINE_VALUE(bool, VolumetricLight, true, "Volumetric Light", "", "General Settings");
    RENDER_PIPELINE_VALUE(bool, EnableDeferredLighting, true, "Enable DeferredLighting", "", "DeferredLighting Settings")
    RENDER_PIPELINE_VALUE(bool, EnablePredepth, true, "Enable Predepth", "", "General Settings")
    RENDER_PIPELINE_VALUE(bool, PerPixelFogTransmittance, false, "Enable Per Pixel Fog Transmittance", "", "General Settings")
    RENDER_PIPELINE_VALUE(bool, EnableBlackScreen, false, "Enable Black Screen", "", "Black Screen Settings");

    //"Enable Feedback for drawunit optimiztaion for gpass  & forward"
    RENDER_PIPELINE_VALUE_INVISIBLE(bool, FeedbackDirectDrawOptimization, false);
    //"Enable Feedback for drawunit optimiztaion for shadow"
    RENDER_PIPELINE_VALUE_INVISIBLE(bool, FeedbackDirectShadowOptimization, false);

    RENDER_PIPELINE_VALUE_INVISIBLE(bool, FeedBackDebug, false);
    RENDER_PIPELINE_VALUE_INVISIBLE(bool, EnableTranslucentPassDepthWrite, false);

    CEMeta(Serialize, Editor)
    CECSAttribute(JsonProperty("UseLightBoundCulling"))
    CECSAttribute(PropertyInfo(PropertyType = "Auto", DisplayName = "Use Light Bound Culling", ToolTips = "", Category = "DeferredLighting Settings"))
    bool UseLightBoundCulling = false;

    CEMeta(Serialize, Editor)
    CECSAttribute(JsonProperty("LinkMode"))
    CECSAttribute(PropertyInfo(PropertyType = "Auto", DisplayName = "Link Mode", ToolTips = "", Category = "DeferredLighting Settings"))
    bool LinkMode = true;

    CEMeta(Editor) CECSAttribute(JsonProperty("ViewModeVisualizeType"))
    CECSAttribute(PropertyInfo(PropertyType = "Auto", DisplayName = "Scene Visualize Type", ToolTips = "", Category = "DeferredLighting Settings")) 
    ViewModeVisualizeType viewModeVisualizeType = ViewModeVisualizeType::Lit;
    
    RENDER_PIPELINE_RESOURCE_INVISIBLE(Material, PostProcessMtl, "Material/PostProcess.nda", "Post Process Material", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(Material, CombineLightingMtl, "Material/CombineLighting.nda", "Combine Lighting Material", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(Material, OutlineEditorMtl, "Material/OutLineEditor.nda", "Outline Editor Material", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(Material, WireframeMtl, "Material/Wireframe.nda", "Wireframe Editor Material", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(Material, ViewModeVisualizationMtl, "Material/ViewModeVisualizationMtl.nda", "ViewMode Visualization Material", "", "General Settings");

    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, CollectShadowMaskShader, "Shader/Lighting/Shadow/CollectShadowMask.compute.nda", "Collect ShadowMask Shader", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, SubsurfaceScatteringComputeShader, "Shader/Material/SubsurfaceScattering/SubsurfaceScattering.compute.nda", "Subsurface Scattering Compute Shader", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HistogramExposureComputeShader, "ComputeShader/auto_exposure.compute.nda", "Histogram Exposure Compute Shader", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, ManualExposureComputeShader, "Shader/Features/PostProcess/fixed_exposure.compute.nda", "Mannual Exposure", "", "")
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, VolumetricFogComputeShader, "Shader/Features/Fog/VoxelVolumetricFog.compute.nda", "Volumetric Fog Compute Shader", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, VFogLightInjectionComputeShader, "Shader/Features/Fog/VFogLightInjection.compute.nda", "Volumetric Fog Light Injection Compute Shader", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, VFogDispatchSizeComputeShader, "Shader/Features/Fog/VFogDispatchSize.compute.nda", "Volumetric Fog Dispatch Size Compute Shader", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, VTComputeShader, "ComputeShader/VTFeedback.compute.nda", "VT Compute Shader", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, AOComputeShader, "Contents/PipelineResource/Shader/Features/AO/MultiScaleVO.compute.nda", "AO Compute Shader", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, VelocityFlattenComputeShader, "Shader/Features/MotionBlur/velocity_flatten.compute.nda", "Motion Blur Velocity Flatten Compute Shader ", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, VelocityNeighborMaxComputeShader, "Shader/Features/MotionBlur/velocity_neighbor_max.compute.nda", "Motion Blur Velocity Neighbor Filter Compute Shader ", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, MotionBlurComputeShader, "Shader/Features/MotionBlur/motion_blur.compute.nda", "Motion Blur Recounstruct Compute Shader ", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, BloomDownsampleShader, "ComputeShader/bloomdownsample.compute.nda", "One Pass Down Sample Compute Shader ", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, LocalExposureComputeShader, "Shader/Features/LocalExposure/LocalExposure.compute.nda", "Local Exposure Compute Shader ", "", "General Settings");

    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, ScatterCopyComputeShader, "Shader/Features/GPUScene/ScatterCopy.compute.nda", "ScatterCopy", "", "GPUScene");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, ScatterByteCopyComputeShader, "Shader/Features/GPUScene/ScatterByteCopy.compute.nda", "ScatterByteCopy", "", "GPUScene");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, InstanceCullingComputeShader, "Shader/Features/InstanceCulling/InstanceCulling.compute.nda", "InstanceCulling", "", "GPUScene");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, InstanceCulling2ComputeShader, "Shader/Features/InstanceCulling/InstanceCulling2.compute.nda", "InstanceCulling2", "", "GPUScene");

    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, ParticleIndirectArgsGenShader, "Shader/Features/VFX/ParticleIndirectArgsGen.compute.nda", "ParticleIndirectArgsGen", "", "ParticleGPU");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, ParticleGPUSimuateInstanceCountCopyComputeShader, "Shader/Features/VFX/ParticleGPUSimuateInstanceCountCopy.compute.nda", "InstanceCountCopy", "", "ParticleGPU");
    
    CEMeta(Serialize, Editor) 
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "Foliage GPU Driven Settings", ToolTips = "Foliage GPU Driven Settings"))
    FoliageGpuDrivenSettings mFoliageGpuDrivenSettings;

    CEMeta(Serialize, Editor) 
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "DepthPyramid Settings", ToolTips = "DepthPyramid"))
    DepthPyramidPassSettings mDepthPyramidSettings;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "DrawRenderTextureToAtlas Settings", ToolTips = "DrawRenderTextureToAtlas"))
    DrawRenderTextureToAtlasPassSettings mDrawRenderTextureToAtlasPassSettings;

    CEMeta(Serialize, Editor) 
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "ApplyDBufferDataPass Settings", ToolTips = "ApplyDBufferData"))
    ApplyDBufferDataPassSettings mApplyDBufferDataPassSettings;

    RENDER_PIPELINE_RESOURCE_INVISIBLE(Material, DeferSSRMtl, "Shader/Features/PostProcess/SSR/DeferSSR.nda", "Defer SSR Material", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, StochasticReproject, "Shader/Features/PostProcess/SSR/StochasticReproject.compute.nda", "Reproject", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, StochasticPrefilter, "Shader/Features/PostProcess/SSR/StochasticPrefilter.compute.nda", "Reproject", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, StochasticResolveTemporal, "Shader/Features/PostProcess/SSR/StochasticResolveTemporal.compute.nda", "Reproject", "", "General Settings");

    RENDER_PIPELINE_RESOURCE_INVISIBLE(Texture, NoiseTexture, "Texture/PostProcess/Noise.nda", "Noise Texture", "", "");

    RENDER_PIPELINE_RESOURCE(Texture, MPCDI_AlphaTexture, "EngineResource/Texture/mpcdi_alpha0.nda", "MPCDI Alpha Texture", "", "MPCDI Settings");
    RENDER_PIPELINE_RESOURCE(Texture, MPCDI_LutTexture, "EngineResource/Texture/mpcdi_lut0.nda", "MPCDI LUT Texture", "", "MPCDI Settings");
    //RENDER_PIPELINE_RESOURCE(Texture, MPCDI_BetaTexture, "Texture/black.nda", "MPCDI Beta Texture", "", "MPCDI Settings");
    RENDER_PIPELINE_GEOMETRY(MPCDI_WarpMesh, "EngineResource/Geometry/mpcdi_mesh0.nda", "MPCDI Warp Mesh", "", "MPCDI Settings");
    //RENDER_PIPELINE_RESOURCE(Texture, ScreenEffect_Texture, "EngineResource/Texture/ScreenEffect.nda", "Screen Effect Texture", "", "Screen Effect");
    RENDER_PIPELINE_GEOMETRY(MPCDI_ScreenEffect_Mesh, "EngineResource/Geometry/mpcdi_screeneffect_mesh0.nda", "MPCDI Screen Effect Mesh", "", "MPCDI Screen Effect");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(Material, MPCDI_ScreenEffect_Mtl, "EngineResource/Material/MPCDIScreenEffectMat.nda", "MPCDI Screen Effect Material", "", "MPCDI Screen Effect");

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "Deffered Build Tile Culling", ToolTips = "Deffered Build Tile Culling Settings"))
    BuildTileCullingSetting mBuildTileCullingSettings;

    CEMeta(Serialize, Editor) 
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "VirtualShadowMap Settings", ToolTips = "VirtualShadowMap Settings")) 
    VirtualShadowMapSettings mVirtualShadowMapSettings;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "LocalLightShadowCache Settings", ToolTips = "LocalLightShadowCache Settings"))
    LocalLightShadowCacheSettings mLocalLightShadowMapSettings;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "Contact Shadow Settings", ToolTips = "Contact Shadow Settings"))
    ContactShadowSettings mContactShadowSettings;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "Massive Lights Shadow Settings", ToolTips = "Massive Lights Shadow Settings"))
    MassiveLightsShadowSettings mMassiveLightsShadowSettings;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "SubSampleShading", ToolTips = "Improving shading aliasing"))
    SubSampleShadingSetting mSubSampleShadingSettings;

    CEMeta(Serialize, Editor) CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "SkyAtmosphere", ToolTips = "SkyAtmosphere Advanced Settings", bHide = true)) SkyAtmosphereAdvancedVars mSkyAtmosphereAdvancedVars;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "TileBasedDefferedLightingSettings", ToolTips = "TileBasedDefferedLightingSettings")) TileBasedDefferedLightingSettings mTileBasedDefferedLightingSettings;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "Indirect Lighting Composite", ToolTips = "Indirect Lighting Composite Settings"))
    IndirectLightingCompositeSetting mIndirectLightingCompositeSettings;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "Volumetric Cloud", ToolTips = "Volumetric Cloud Settings"))
    SkyCloudSetting mCloudSetting;
};

}


