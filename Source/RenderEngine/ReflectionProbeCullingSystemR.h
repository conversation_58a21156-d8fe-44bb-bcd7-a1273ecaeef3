#pragma once
#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/RenderSystemBase.h"
#include "CECommon/Common/FrameContainer.h"
#include "ECS/Develop/DataTypes.h"
#include "RenderEngine/RenderWorldConst.h"
#include "RenderEngine/FrustumCulling.h"
#include "RenderEngine/ReflectionProbeSystemR.h"

namespace cross
{
struct CullingReflectionProbeData
{
#ifdef CE_USE_DOUBLE_TRANSFORM
    std::vector<std::tuple<ecs::EntityID, Float3A, float, float, Float3A>> mSphereRefleProbe;
    std::vector<std::tuple<ecs::EntityID, Float3A, Float3A, float, Float3A>> mBoxRefleProbe;
    void AddSphereReflecProbe(ecs::EntityID rpEntity, const Float3& position, float range, float intensity, const Float3& tilePos);
    void AddBoxReflecProbe(ecs::EntityID rpEntity, const Float3& position, const Float3& range, float intensity, const Float3& tilePos);
#else
    std::vector<std::tuple<ecs::EntityID, Float3A, float, float>> mSphereRefleProbe;
    std::vector<std::tuple<ecs::EntityID, Float3A, Float3A, float>> mBoxRefleProbe;
    void AddSphereReflecProbe(ecs::EntityID rpEntity, const Float3& position, float range, float intensity);
    void AddBoxReflecProbe(ecs::EntityID rpEntity, const Float3& position, const Float3& range, float intensity);
#endif

    void Clear();
};

class ReflectionProbeCulling
{
public:
    static void CullRefleProbesForCamera(const CullingReflectionProbeData& refleProbesData, const BoundingFrustumData& cameraFrustum, const Float3& cameraPos,
        std::function<void(FrameVector<std::pair<ecs::EntityID, float>>*)> outputCameraResultFunction, FrameAllocator* frameAllocator);
};


class ReflectionProbeCullingSystemR : public RenderSystemBase
{
    CEMetaInternal(Reflect)
public:
    RENDER_ENGINE_API static ReflectionProbeCullingSystemR* CreateInstance();

    virtual void Release() override;

    CEFunction(Reflect)
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

protected:
    ReflectionProbeCullingSystemR();
    ~ReflectionProbeCullingSystemR();
    UInt32 mReflectionProbeCount {0};
    CullingReflectionProbeData tRefleProbeData;
    std::unordered_map<ecs::EntityID, ecs::ComponentHandle<ReflectionProbeCameraComponentR>> tRefleProbeComponentMap;
};

}
