#pragma once

#include <vector>
#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/GlobalSystemBase.h"
#include "RenderEngine/RenderPrimitives.h"
#include "RenderEngine/PresentManager.h"
#include "CrossBase/Threading/PresentThread.h"
#include "RenderEngine/RenderContext.h"
#include "RenderEngine/RenderNodeSystemR.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/StellarMeshManager.h"
#include "NativeGraphicsInterface/NGI.h"
#include "NativeGraphicsInterface/NGIXRRuntime.h" 
#include "NativeGraphicsInterface/NGITransientResourceManager.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDPass.h"
#include "MaterialGPUScene.h"


#define ENABLE_EDITOR_PCG_FOLIAGE false
#define FOLIAGE_INSTANCE_COUNT 2000
#define CUBE_FACE_NUM 6
struct IRenderWindow;

namespace cross 
{
struct REDImGui;
struct ImguiwsVisualizer;
struct ImguiwsMemoryProfiler;
struct REDdebugGUI;
struct REDMemoryProfiler;
class FrameParam;
class MeshRendererBaseR;
class RenderWindowR;

std::chrono::duration<double, std::micro> RENDER_ENGINE_API PreVBlankDistance(std::chrono::steady_clock::time_point inPresentReadyTimestamp);
UInt64 RENDER_ENGINE_API PreVBlankTimestampInUs();

struct RenderStatistics
{
public:
    UInt32 mShadowDrawUnitCount {0};
    UInt32 mBaseDrawUnitCount {0};
    //Support buffer statisc later
};

enum class REDGUIState
{
    None,
    REDVisualizer,
    DebugWindow,
    MemoryProfiler,
};

enum class BlockCompression
{
    BC1 = 0,
    BC3,
    BC5,
    BC7,
};

class RendererSystemR : public GlobalRenderSystemBase
{
    friend class EditorUIRenderSystemR;

public:
    RENDER_ENGINE_API static const GlobalSystemDesc& GetDesc();
    RENDER_ENGINE_API static RendererSystemR* CreateInstance();
    RENDER_ENGINE_API void Release() override;
    virtual const GlobalSystemDesc& GetSystemDesc() const override { return GetDesc(); }
    virtual void NotifyShutdownEngine() override;

    RENDER_ENGINE_API void PreWindowCreationInit();

    void OnBeginFrame(FrameParam* frameParam) override;
    void OnBeginFrameByPresentThread(FrameParam* frameParam);
    void SetXRFrameInfo(const NGIXRFrameInfo& xrFrameInfo) { mXRFrameInfo = xrFrameInfo; }
    
    void UpdateResources(FrameParam* fp);
    void PostUpdateResources();
    void OnFirstUpdate(FrameParam* frameParam) override;
    void OnUpdate(FrameParam* fp) override;

    void OnEndFrame(FrameParam* frameParam) override;

    auto* GetRenderingExecutionDescriptor() { return mRED.get(); }
    auto* GetREDVisualizer() { return mREDVisualizer.get(); }
    auto* GetREDdebugGUI() { return mREDdebugGUI.get(); }
    auto* GetImguiwsVisualizer() { return mImguiwsVisualizer.get(); }
    RENDER_ENGINE_API REDImGui* GetClientGUI() const;
    REDTextureView* GetWindowRenderTarget() { return mWindowRenderTargetView; }
    auto* GetDefaultCommandQueue() { return mCmdQue.get(); }
    auto* GetCopyCommandQueue() { return mCopyQue.get(); }
    auto* GetPresentQueue() { return mPresentQue.get(); }
    auto* GetFrameResourceReadyFence() { return mFrameResourceReady.get(); }
    auto* GetFrameRenderingReadyFence()
    {
        return mFrameRenderingReady.get();
    };
    auto* GetFrameCommandReadyFence()
    {
        return mFrameCommandReady.get();
    };
    auto* GetPersistentResourceManager() { return mPersistentResMgr.get(); }
    auto* GetTransientResourceManager() { return mTransientResMgr.get(); }
    auto* GetResourceGroupPool()
    {
        return mResourceGroupPool.get();
    }
    auto* GetGraphicsPipelineStatePool() { return mPipelineStatePool.get(); }
    auto* GetScratchBuffer() { return mScratchBuffer.get(); }
    auto* GetRenderPrimitives() { return mRenderPrimitives.get(); }
    auto* GetFullScreenTriangle() { return mRenderPrimitives->mFullScreenTriangle.get(); }
    NGISampler* GetDefaultSampler() { return mRenderPrimitives->mDefaultSampler.get(); }
    NGISampler* GetPointSampler() { return mRenderPrimitives->mPointSampler.get(); }
    NGISampler* GetLinearPointWrapSampler() { return mRenderPrimitives->mLinearPointWrapSampler.get(); }
    NGISampler* GetLinearPointClampSampler() { return mRenderPrimitives->mLinearPointClampSampler.get(); }
    NGISampler* GetPCFSampler() { return mRenderPrimitives->mPCFSampler.get(); }
    NGISampler* GetClampSampler() { return mRenderPrimitives->mClampSampler.get(); }
    NGISampler* GetRepeatSampler() { return mRenderPrimitives->mRepeatSampler.get(); }
    NGISampler* GetMirrorSampler() { return mRenderPrimitives->mMirrorSampler.get(); }
    NGISampler* GetAnisotropicSampler() { return mRenderPrimitives->mAnisotropicSampler.get(); }
    NGISampler* GetTerrainAlbedoSampler() { return mRenderPrimitives->mTerrainAlbedoSampler.get(); }
    NGITextureView* GetDefaultTexture2DView() { return mRenderPrimitives->mDefaultTexture2DView.get(); }
    NGITextureView* GetDefaultTextureCubeView() { return mRenderPrimitives->mDefaultTextureCubeView.get(); }
    NGITextureView* GetDefaultTexture3DView() { return mRenderPrimitives->mDefaultTexture3DView.get(); }
    NGITextureView* GetDefaultTexture2DArrayView() { return mRenderPrimitives->mDefaultTexture2DArrayView.get(); }
    NGITextureView* GetDefaultTextureCubeArrayView() { return mRenderPrimitives->mDefaultTextureCubeArrayView.get(); }

    NGIConstantBufferAllocator* GetConstantBufferAllocator() { return mConstantBufferAllocator.get(); }

    RENDER_ENGINE_API void ToggleNoDrawWorldMode();
   
    // 初始化NGITexture（创建NGITexture必须初始化后才能使用），通过一个StagingBuffer进行初始化数据的填充
    // 如果一个NGITexture有多个subresource，可调用此函数进行多次初始化
    void InitializeTexture(NGITexture* dstTex, NGIStagingBuffer* stagingBuffer, const NGICopyBufferTexture& region, NGIResourceState afterState)
    {
        mUpdateTextures.emplace_back(dstTex, stagingBuffer, region, NGIResourceState::Undefined, afterState, NGIResourceState::Undefined, NGIResourceState::Undefined);
    }

    // 初始化NGITexture（创建NGITexture必须初始化后才能使用），NGITexture中的数据为未定义
    // 如果只需要初始化部分subresource，可填充额外的subres参数，多次调用
    void InitializeTexture(NGITexture* dstTex, NGIResourceState afterState, UInt32 subres = NGIAllSubresources)
    {
        NGICopyBufferTexture region{};
        region.TextureSubresource = subres;
        mUpdateTextures.emplace_back(dstTex, static_cast<NGIStagingBuffer*>(nullptr), region, NGIResourceState::Undefined, afterState, NGIResourceState::Undefined, NGIResourceState::Undefined);
    }

    // 初始化NGITextureView对应的部分NGITexture（创建NGITexture必须初始化后才能使用），NGITexture中的数据为clearValue
    void InitializeTexture(NGITextureView* dstTex, const NGIClearValue& clearValue, NGIResourceState afterState)
    {
        mClearTextures.emplace_back(dstTex, clearValue, NGIResourceState::Undefined, afterState);
    }

    // 更新NGITexture
    void UpdateTexture(NGITexture* dstTex, NGIStagingBuffer* stagingBuffer, const NGICopyBufferTexture& region, NGIResourceState beforeState, NGIResourceState afterState)
    { 
        std::unique_lock writerLock(mUpdateTextureMutex);
        mUpdateTextures.emplace_back(dstTex, stagingBuffer, region, beforeState, afterState, NGIResourceState::Undefined, NGIResourceState::Undefined);
    }

    void UpdateTexture(NGITexture* dstTexture, NGITexture* srcTexture, const NGICopyTexture& region, NGIResourceState dstBeforeState, NGIResourceState dstAfterState, NGIResourceState srcBeforeState, NGIResourceState srcAfterState)
    {
        std::unique_lock writerLock(mUpdateTextureMutex);
        mUpdateTextures.emplace_back(dstTexture, srcTexture, region, dstBeforeState, dstAfterState, srcBeforeState, srcAfterState);
    }

    // 初始化NGIBuffer，通过一个StagingBuffer进行初始化数据填充
    void InitializeBuffer(NGIBuffer* dstBuf, NGIStagingBuffer* stagingBuffer, const NGICopyBuffer& region, NGIResourceState afterState)
    {
        mUpdateBuffers.emplace_back(dstBuf, stagingBuffer, region, NGIResourceState::Undefined, afterState, NGIResourceState::Undefined, NGIResourceState::Undefined);
    }

    // 初始化NGIBuffer，初始化其值为连续排列的clearValue
    // TODO(peterwjma): optimize type of clear value to NGIClearValue
    void InitializeBuffer(NGIBufferView* dstBuf, UInt32 clearValue, NGIResourceState afterState)
    {
        mClearBuffers.emplace_back(dstBuf, clearValue, NGIResourceState::Undefined, afterState);
    }

    // 更新NGIBuffer
    void UpdateBuffer(NGIBuffer* dstBuf, NGIStagingBuffer* stagingBuffer, const NGICopyBuffer& region, NGIResourceState beforeState, NGIResourceState afterState) 
    { 
        std::unique_lock writerLock(mUpdateBufferMutex);
        mUpdateBuffers.emplace_back(dstBuf, stagingBuffer, region, beforeState, afterState, NGIResourceState::Undefined, NGIResourceState::Undefined);
    }

    void UpdateBuffer(NGIBuffer* dstBuffer, NGIBuffer* srcBuffer, const NGICopyBuffer& region, NGIResourceState dstBeforeState, NGIResourceState dstAfterState, NGIResourceState srcBeforeState, NGIResourceState srcAfterState)
    {
        std::unique_lock writerLock(mUpdateBufferMutex);
        mUpdateBuffers.emplace_back(dstBuffer, srcBuffer, region, dstBeforeState, dstAfterState, srcBeforeState, srcAfterState);
    }
    
    void UpdateBufferWithRawData(void const* data, SizeType size, NGIBuffer* buffer, NGIResourceState destState = NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);    

    template<typename TArg>
    void DestroyNGIObject(TArg&& arg)
    {
        std::unique_lock writerLock(mDestroyObjectsMutex);
        mDelayedDestroyObjects.emplace_back(std::move(arg), CmdSettings::Inst().gMaxQueuedFrame);
    }

    template<typename TArg>
    void DestroyNGIObjectInAsyncThread(TArg&& arg)
    {
        std::unique_lock writerLock(mDestroyObjectsMutex);
        mDelayedDestroyObjects.emplace_back(std::move(arg), CmdSettings::Inst().gMaxQueuedFrame + 1);
    }


    void PostUpdateBuffer(NGIBuffer* destination, NGIStagingBuffer* source, std::vector<NGICopyBuffer> regions /* std::move please */,
        NGIResourceState dstBeforeState, NGIResourceState dstAfterState, const char* debugRegion = nullptr)
    {
        mPostUpdateBuffers.emplace_back(destination, source, std::move(regions), dstBeforeState, dstAfterState, debugRegion);
    }

    void PostUpdateTexture(NGITexture* destination, NGIStagingBuffer* source, std::vector<NGICopyBufferTexture> regions /* std::move please */,
        NGIResourceState dstBeforeState, NGIResourceState dstAfterState, const char* debugRegion = nullptr)
    {
        mPostUpdateTextures.emplace_back(destination, source, std::move(regions), dstBeforeState, dstAfterState, NGIResourceState::Undefined, NGIResourceState::Undefined, debugRegion);
    }

    void PostUpdateTexture(NGITexture* destination, NGITexture* source, std::vector<NGICopyTexture> regions /* std::move please */,
        NGIResourceState dstBeforeState, NGIResourceState dstAfterState, NGIResourceState srcBeforeState = NGIResourceState::Undefined, NGIResourceState srcAfterState = NGIResourceState::Undefined, const char* debugRegion = nullptr)
    {
        mPostUpdateTextures.emplace_back(destination, source, std::move(regions), dstBeforeState, dstAfterState, srcBeforeState, srcAfterState, debugRegion);
    }

    RENDER_ENGINE_API NGIFence* GetFrameReadyFence() const {
        return mFrameResourceReady.get();
    }

    RENDER_ENGINE_API void BindOnBackBufferReady(std::function<void(NGITexture * backBuffer)> f)
    {
        mOnBackBufferReady.push_back(f);
    }

public:
    RENDER_ENGINE_API void Flush();

    struct ReadBackSession
    {
        uint64_t mGUID;

        ReadBackSession();

        bool operator ==(const ReadBackSession& other) const
        {
            return mGUID == other.mGUID;
        }
    };

    struct ReadBackTask
    {
        UInt32 FrameIndex;
        std::unique_ptr<NGIStagingBuffer> StagingBuffer;
        std::variant<void*, std::pmr::vector<UInt8>*> Dst;
        SizeType Size;
        ReadBackSession Session = {};
    };

    RENDER_ENGINE_API void CancelReadBack(ReadBackSession session);

    RENDER_ENGINE_API void ReadBackREDTexture(REDTexture* texture, UInt32 subResource, NGIOffset3D offset, NGIExtent3D extent, void* dst, SizeType size, ReadBackSession session);

    // read back camera must be called from update
    RENDER_ENGINE_API void ReadBackRenderTexture(RenderTextureR* renderTexture, UInt32 subResource, NGIOffset3D offset, NGIExtent3D extent, void* dst, SizeType size);
    
    RENDER_ENGINE_API void UnwrapCubemapToTexture2D(const std::array<RenderTextureR*, 6>& cubemapFaces, RenderTextureR* texture2D);

    RENDER_ENGINE_API void CopyReadBackBuffer(REDPass::BufferType src, const SizeType bufferSize, const NGICopyBuffer& region, void* output);

    RENDER_ENGINE_API void CopyReadBackBufferDynamic(REDPass::BufferType src, const NGICopyBuffer& region, std::pmr::vector<UInt8> * output);

    RENDER_ENGINE_API void ReadBackBuffer(std::unique_ptr<NGIStagingBuffer> buffer, void* dst, SizeType size);

    RENDER_ENGINE_API void BakeVoxelTexture(void* dummyData, void* albedoData, void* normalData, void* emissiveData, SizeType byteSize, UInt32 width, UInt32 height, UInt32 depth, UInt32 numVoxelDirections, Float3 voxelSize,
                                            GraphicsFormat format, RenderWorld* renderWorld, ecs::EntityID eID);

    RENDER_ENGINE_API void BakeTerrainTexture(void* data, SizeType byteSize, UInt32 width, UInt32 height, GraphicsFormat format, MaterialR* material);

    RENDER_ENGINE_API void BakeCubemapTexture(std::vector<void*> data, SizeType byteSize, UInt32 width, UInt32 height, GraphicsFormat format, MaterialR* material);

    RENDER_ENGINE_API void BlockCompressRawImage(BlockCompression compressionMethod, NGITexture* rawImage, NGITextureView* rawImageView, NGITexture*& compressedTexture, NGITextureView*& compressedTextureView, bool noMips = true);

    RENDER_ENGINE_API void TransformCubemapToPanorama(const std::array<RenderTextureR*, 6>& cubemapFaces, RenderTextureR* texture2D);

    void BakeTextureProcess(void* data, UInt32 width, UInt32 height, GraphicsFormat format, MaterialR* material, std::string nameSuffix, NameID const& passID, RenderContext&& ctx);

    // Calculate the captured cube view matrices in UE Coordinates(only used in SkyLightSystem now)
    void CalCubeCaptureMatrixInUECoordinates(Float3 viewPos, float nearPlane, float farPlane, std::array<Float4x4, CUBE_FACE_NUM>& viewMats, Float4x4& projMat);

    // Calculate the captured cube view matrices in CE Coordinates
    void CalCubeCaptureMatrix(Float3 viewPos, float nearPlane, float farPlane, std::array<Float4x4, CUBE_FACE_NUM>& viewMats, Float4x4& projMat);

    const auto& GetXRFrameInfo() const{ return mXRFrameInfo; }
    
    NGICommandList* GetCMDList() { return mImCmdListPre; }

    StellarMesh::StellarMeshManager* GetStellarMeshManager() { return mStellarMeshManager.get(); }

    MaterialGPUScene& GetMaterialGPUScene() { return mMaterialGPUScene; }

    auto GetAccelStructCmd() const
    {
        return mAccelStructList;
    }

    NGIBuffer* mInstancingBuffer = nullptr;

    UInt32* mInstancingBufferOffset = 0u;

    std::vector<std::function<void(NGITexture* backBuffer)>> mOnBackBufferReady;;


    threading::PresentFrameSynchronization mPresentSync;

    ComputeShaderR* mScatterVegetationComputeShader = nullptr;
    ComputeShaderR* mScatterVegetation_ShadowMapComputeShader = nullptr;
    ComputeShaderR* mTextureCompressionShader = nullptr;
    ComputeShaderR* mTextureUtilsShader = nullptr;
    NGIBuffer* mOutputBuffer = nullptr;
    NGIBuffer* mOutputBuffer_ShadowMap = nullptr;
    NGITextureView* mEcotopeMapTextureView = nullptr;
    NGITextureView* mDensityMapTextureView = nullptr;
    NGITextureView* mHeightMapTextureView = nullptr;
    NGITextureView* mNormalMapTextureView = nullptr;
    bool mDenstiyMapDirty = false;
    float mLastFrameTime = 0;
    float mLastGPUDeltaTime = 0;


    RENDER_ENGINE_API static bool RegisterQueuePresentTriggerCallback(uintptr_t key, std::function<void(UInt32)> inCallback);
    RENDER_ENGINE_API static bool UnRegisterQueuePresentTriggerCallback(uintptr_t key);
    using QueuePresentTriggerCbs = std::map<uintptr_t, std::function<void(UInt32)>>;
    static QueuePresentTriggerCbs sQueuePresentTriggerCbs;
    static std::mutex sCallbackMutex;

    int mMtlScratchByte = 0;
    int mMtlVarientByte = 0;
    std::mutex mMtlAddMutex;
    void AddMtlScratchByte(int byteSize);

private:
    RendererSystemR();
    ~RendererSystemR();

    std::mutex mDestroyObjectsMutex;
    std::mutex mUpdateTextureMutex;
    std::mutex mUpdateBufferMutex;
    std::unique_ptr<RenderingExecutionDescriptor> mRED;
    std::unique_ptr<REDVisualizer> mREDVisualizer;
    std::unique_ptr<REDdebugGUI> mREDdebugGUI;
    std::unique_ptr<ImguiwsVisualizer> mImguiwsVisualizer;
    std::unique_ptr<NGICommandQueue> mCmdQue;
    std::unique_ptr<NGICommandQueue> mCopyQue;
    std::unique_ptr<NGICommandQueue> mPresentQue;
    std::unique_ptr<PresentManager> mPresentManager;


    std::unique_ptr<PersistentResourceManager> mPersistentResMgr;
    std::unique_ptr<NGIResourceManager> mTransientResMgr;
    std::unique_ptr<NGIPipelineStatePool> mPipelineStatePool;

    std::unique_ptr<NGIResourceGroupPool> mResourceGroupPool;

    std::unique_ptr<NGIFence> mCopyReady;
    std::unique_ptr<NGIFence> mFrameResourceReady;
    std::unique_ptr<NGIFence> mFrameRenderingReady;
    std::unique_ptr<NGIFence> mFrameCommandReady;

    std::unique_ptr<NGITransientBufferManager> mScratchBuffer;
    std::unique_ptr<NGIConstantBufferAllocator> mConstantBufferAllocator;

    std::unique_ptr<RenderPrimitives> mRenderPrimitives;

    std::unique_ptr<StellarMesh::StellarMeshManager> mStellarMeshManager;

    using TextureUpdateSource = std::variant<NGIStagingBuffer*, NGITexture*>;
    using TextureUpdateRegion = std::variant<NGICopyBufferTexture, NGICopyTexture>;
    using TextureUpdateRegions = std::variant<std::vector<NGICopyBufferTexture>, std::vector<NGICopyTexture>>;

    std::vector<std::tuple<NGITexture*, TextureUpdateSource, TextureUpdateRegion, NGIResourceState, NGIResourceState, NGIResourceState, NGIResourceState>> mUpdateTextures;
    std::vector<std::tuple<NGIBuffer*, NGIBuffer*, NGICopyBuffer, NGIResourceState, NGIResourceState, NGIResourceState, NGIResourceState>> mUpdateBuffers;
    std::vector<std::tuple<NGITextureView*, NGIClearValue, NGIResourceState, NGIResourceState>> mClearTextures;
    std::vector<std::tuple<NGIBufferView*, UInt32, NGIResourceState, NGIResourceState>> mClearBuffers;
    std::vector<std::tuple<NGIBuffer*, NGIBuffer*, std::vector<NGICopyBuffer>, NGIResourceState, NGIResourceState, const char*>> mPostUpdateBuffers;
    std::vector<std::tuple<NGITexture*, TextureUpdateSource, TextureUpdateRegions, NGIResourceState, NGIResourceState, NGIResourceState, NGIResourceState, const char*>> mPostUpdateTextures;
    std::list<std::tuple<std::unique_ptr<NGIObject>, UInt32>> mDelayedDestroyObjects;

    // frame start from 1
    UInt32 mCurrentFrame = 0;
    NGICommandList* mImCmdListScratchBuffer = nullptr;
    NGICommandList* mImCmdListCopy = nullptr;
    NGICommandList* mImCmdListPre = nullptr;
    NGICommandList* mImCmdListPost = nullptr;
    NGICommandList* mAccelStructList = nullptr;
    std::vector<NGICommandList*> mImCmdLists;

    // client only
    REDTexture* mWindowRenderTarget = nullptr;
    REDTextureView* mWindowRenderTargetView = nullptr;

    // readback memory copy at BeginFrame
    std::list<ReadBackTask> mPendingCopyBackTasks;

public:
    std::unique_ptr<cross::NGITexture> mDensityMapTexture_NGI;
    std::unique_ptr<cross::NGITextureView> mDensityMapTextureView_NGI;

    void SetREDGUIState(REDGUIState state) { mREDGUIState = state; };

    REDGUIState GetREDGUIState() { return mREDGUIState; }
    void SetShowTimeStats(bool stats)
    {
        bShowTimeStats = stats;
    }

    RENDER_ENGINE_API void AddPostRenderTask(std::function<void()> task)
    {
        mPostRenderTasks.push_back(std::move(task));
    }

private:
    void BlockCompressAllMips(BlockCompression bcFormat, NGITexture* rawImage, NGITextureView* rawImageView, NGITexture*& compressedTexture, NGITextureView*& compressedTextureView);
    void BlockCompressMipZero(BlockCompression bcFormat, NGITexture* rawImage, NGITextureView* rawImageView, NGITexture*& compressedTexture, NGITextureView*& compressedTextureView);

    std::vector<uint8_t> LoadPipelineCache();
    void SavePipelineCache();
    NGIXRFrameInfo mXRFrameInfo;
    REDGUIState mREDGUIState = REDGUIState::None;
    bool bShowTimeStats{false};
    
    std::vector<std::function<void()>> mPostRenderTasks;
    threading::StallEvent mPresentStallEvent;
    
    MaterialGPUScene mMaterialGPUScene;
};

}   // namespace cross
