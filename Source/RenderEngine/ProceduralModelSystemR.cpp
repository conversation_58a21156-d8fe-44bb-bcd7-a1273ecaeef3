#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "RenderEngine/ProceduralModelSystemR.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/PrimitiveGenerator.h"
#include "RenderEngine/RenderFactory.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/VisibilitySystemR.h"
#include "RenderEngine/MeshBuildSystemR.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/RenderPipelineSystemR.h"

namespace cross {
ecs::ComponentDesc* ProceduralModelComponentR::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetRenderComponentDesc<cross::ProceduralModelComponentR>(false);
}

ProceduralModelSystemR* ProceduralModelSystemR::CreateInstance()
{
    return new ProceduralModelSystemR();
}

void ProceduralModelSystemR::Release() {
    delete this;
}

void ProceduralModelSystemR::OnBeginFrame(FrameParam* frameParam) {
    mModelChangeList.BeginFrame(frameParam, FRAME_STAGE_RENDER);
}

void ProceduralModelSystemR::OnEndFrame(FrameParam* frameParam) {
}

void ProceduralModelSystemR::OnBuildUpdateTasks(FrameParam* frameParam) {
    auto task = CreateTaskFunction(FrameTickStage::Update, {}, [this] {
        SCOPED_CPU_TIMING(GroupRendering, "ProceduralModelUpdateR");

        auto* GPUScene = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>()->GetWorldRenderPipeline()->GetGPUScene();

        // create
        for (UInt32 i = 0, eventCount = mRenderWorld->GetRemainedEventCount<EntityCreateEvent>(true); i < eventCount; ++i)
        {
            auto& lifeEvent = mRenderWorld->GetRemainedEvent<EntityCreateEvent>(i);
            if (ecs::HasComponentMask<ProceduralModelComponentR>(lifeEvent.mData.mChangedComponentMask))
            {
                auto& entityID = lifeEvent.mData.mEntityID;
                if (mRenderWorld->IsEntityAlive(entityID))
                {
                    auto [modelComp, renderNodeComp] = mRenderWorld->GetComponent<ProceduralModelComponentR, RenderNodeComponentR>(entityID);
                    auto& renderNode = modelComp.Write()->mRenderNode;
                    if (EnumHasAnyFlags(lifeEvent.mData.mEventFlag, EntityLifeCycleEventFlag::CreateComponent))
                    {
                        renderNodeComp.mComponent->SetRenderNode(renderNode);
                    }
                }
            }
        }

        // update 
        UInt32 modelChangeCount = mModelChangeList.GetCount();
        for (UInt32 modelChangeIndex = 0; modelChangeIndex < modelChangeCount; ++modelChangeIndex)
        {
            ecs::EntityID changeModelEntity = (mModelChangeList.GetData(modelChangeIndex));
            if (!mRenderWorld->IsEntityAlive(changeModelEntity)) [[unlikely]]
                continue;
            auto [modelH, renderNodeH] = mRenderWorld->GetComponent<ProceduralModelComponentR, RenderNodeComponentR>(changeModelEntity);
            if (!modelH.IsValid()) [[unlikely]]
                continue;

            ModelRenderNode::RenderModel renderModel{};
            tLODSettings.clear();
            UInt8 maxLODCount = 0;
            BoundingBox renderModelBoundingBox{BoundingBox::Flags::MergeIdentity};
            for (UInt32 i = 0; i < GetModelCount(modelH.Read()); ++i)
            {
                auto& model = GetModel(modelH.Write(), i);
                if (!model.mVisible || !model.mRenderMesh)
                    continue;
                if (model.mRenderMesh->GetState() == MeshR::State::Initializing)
                    continue;

                // Assume one procedural model as a subModel
                maxLODCount = std::max(maxLODCount, std::min<UInt8>(model.mMeshData->GetLodCount(), resource::MAX_MESH_LOD_NUM));
                renderModel.mLODModels.resize(maxLODCount);

                for (UInt32 lodIndex = 0; lodIndex < renderModel.mLODModels.size(); ++lodIndex)
                {
                    auto& subModel = renderModel.mLODModels[lodIndex].mSubModels.emplace_back(ModelRenderNode::SubModel{});
                    subModel.mGeometry = &(model.mRenderMesh->GetRenderGeometry(lodIndex));
                    subModel.mBoundingBox = model.mMeshData->GetBoundingBox();
                    subModel.mMaterial = model.mMaterial;

                    BoundingBox::CreateMerged(renderModelBoundingBox, renderModelBoundingBox, subModel.mBoundingBox);
                }

                tLODSettings.emplace_back(nullptr);
            }
            renderModel.mBoundingBox = std::move(renderModelBoundingBox);

            auto& renderNode = modelH.Write()->mRenderNode;
            renderNode->SetLODSettings(tLODSettings);

            renderNode->FreeGPUScene(*GPUScene);
            renderNode->SetRenderModel(std::move(renderModel));
            renderNode->AllocateGPUScene(*GPUScene);
            GPUScene->SetGPUSceneDirty(changeModelEntity);
        }
    });
}

void ProceduralModelSystemR::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    RenderSystemBase::NotifyEvent(event, flag);
    if (event.mEventType == OnSystemAddToRenderWorldEvent::sEventType)
    {
        mRenderWorld->SubscribeRemainedEvent<EntityDestroyEvent>(this, true);
    }
    else if (event.mEventType == RemainedEventUpdatedEvent::sEventType)
    {
        const RemainedEventUpdatedEvent& e = static_cast<const RemainedEventUpdatedEvent&>(event);
        if (e.mData.mRemainedEventType == EntityDestroyEvent::sEventType)
        {
            auto* GPUScene = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>()->GetWorldRenderPipeline()->GetGPUScene();
            for (UInt32 i = e.mData.mFirstIndex; i <= e.mData.mLastIndex; i++)
            {
                auto& lifeEvent = mRenderWorld->GetRemainedEvent<EntityDestroyEvent>(i);
                auto entity = lifeEvent.mData.mEntityID;

                if (ecs::HasComponentMask<ProceduralModelComponentR>(lifeEvent.mData.mChangedComponentMask) && EnumHasAnyFlags(lifeEvent.mData.mEventFlag, EntityLifeCycleEventFlag::DestroyComponent))
                {
                    auto& entityID = lifeEvent.mData.mEntityID;
                    auto modelComp = mRenderWorld->GetComponent<ProceduralModelComponentR>(entityID);
                    auto& renderNode = modelComp.Write()->mRenderNode;
                    renderNode->FreeGPUScene(*GPUScene);
                }
            }
        }
    }
}

void ProceduralModelSystemR::SetModelDirty(ecs::EntityID entity, bool isRemain)
{
    mModelChangeList.EmplaceChangeData(entity);
}

UInt32 ProceduralModelSystemR::GetModelCount(const ProceduralModelComponentReader& modelH)
{
    return (UInt32)modelH->mModels.size();
}

ProceduralModelComponentR::ProceduralModel& ProceduralModelSystemR::GetModel(const ProceduralModelComponentWriter& modelH, UInt32 modelIndex)
{
    Assert(modelIndex < modelH->mModels.size());
    return modelH->mModels[modelIndex];
}

void ProceduralModelSystemR::SetModelVisibility(ecs::EntityID entity, UInt32 modelIndex, bool visible)
{
    auto modelH = mRenderWorld->GetComponent<ProceduralModelComponentR>(entity);
    auto writer = modelH.Write();
    if (modelIndex >= writer->mModels.size())
        return;
    auto& model = writer->mModels[modelIndex];
    model.mVisible = visible;
    mModelChangeList.EmplaceChangeData(entity);
}

void ProceduralModelSystemR::SetModelMaterial(ecs::EntityID entity, UInt32 modelIndex, MaterialR* renderMaterial)
{
    auto modelH = mRenderWorld->GetComponent<ProceduralModelComponentR>(entity);
    auto writer = modelH.Write();
    if (modelIndex >= writer->mModels.size())
        return;
    auto& model = writer->mModels[modelIndex];
    model.mMaterial = renderMaterial;
    mModelChangeList.EmplaceChangeData(entity);
}

const ProceduralModelComponentR::ProceduralModel& ProceduralModelSystemR::GetModel(const ProceduralModelComponentReader& modelH, UInt32 modelIndex) const
{
    Assert(modelIndex < modelH->mModels.size());
    return modelH->mModels[modelIndex];
}

const MeshAssetData* ProceduralModelSystemR::GetMeshAssetData(const ProceduralModelComponentReader& reader, UInt32 modelIndex) const
{
    return GetModel(reader, modelIndex).mMeshData.get();
}

void ProceduralModelSystemR::CreateModel(ecs::EntityID entity, UInt32 modelIndex, MeshAssetDataPtr mesh, MaterialR* renderMaterial)
{
    auto modelH = mRenderWorld->GetComponent<ProceduralModelComponentR>(entity);
    auto writer = modelH.Write();
    if (modelIndex >= writer->mModels.size())
    {
        writer->mModels.resize(modelIndex + 1);
    }
    auto& model = writer->mModels[modelIndex];
    model.Reset();
    model.mMeshData = mesh;
    model.mMaterial = renderMaterial;
    model.mRenderMesh = static_cast<MeshR*>(mesh->GetRenderMesh());
    mModelChangeList.EmplaceChangeData(entity);
}

void ProceduralModelSystemR::UpdateModel(ecs::EntityID entity, UInt32 modelIndex, MeshAssetDataPtr mesh, MaterialR* renderMaterial)
{
    auto modelH = mRenderWorld->GetComponent<ProceduralModelComponentR>(entity);
    auto writer = modelH.Write();
    auto& model = GetModel(writer, modelIndex);
    model.Reset();
    model.mMeshData = mesh;
    model.mMaterial = renderMaterial;
    model.mRenderMesh = static_cast<MeshR*>(mesh->GetRenderMesh());
    mModelChangeList.EmplaceChangeData(entity);
}

void ProceduralModelSystemR::DeleteModel(ecs::EntityID entity, UInt32 modelIndex)
{
    auto modelH = mRenderWorld->GetComponent<ProceduralModelComponentR>(entity).Write();
    if (modelH->mModels.size() <= modelIndex) {
        return;
    }
    modelH->mModels.erase(modelH->mModels.begin() + modelIndex);
    mModelChangeList.EmplaceChangeData(entity);
}

ProceduralModelSystemR::ProceduralModelComponentHandle ProceduralModelSystemR::GetModelHandle(ecs::EntityID entity)
{
    auto modelH = mRenderWorld->GetComponent<ProceduralModelComponentR>(entity);
    return modelH;
}

void ProceduralModelSystemR::SetModelRenderMesh(const ProceduralModelComponentWriter& writer, UInt32 modelIndex, MeshR* renderMesh)
{
    GetModel(writer, modelIndex).mRenderMesh = renderMesh;
}
}
