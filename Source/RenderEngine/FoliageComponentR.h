#pragma once

#include "ECS/Develop/Framework.h"
#include "CECommon/Common/TRS.h"
#include "RenderEngine/RenderEngineForward.h"
#include "RenderEngine/RenderWorldConst.h"
#include "Resource/MeshAssetDataResource.h"

namespace cross
{
    class MaterialR;

    struct alignas(16) FoliageInstanceData
    {
        Float4x4 world;
        Float4x4 preWorld;
        int lightID1;
        int lightID2;
        int lightID3;
        int lightID4;
        int lightCount;
        Float3 tilePosition;
    };

    struct alignas(16) FoliageInstanceCompactData
    {
        UInt4 rotationScale;
        Float3 translation;
        UInt32 entityIndex;
    };

    struct alignas(16) FoliageEntityData
    {
        Float4x4 world;
        Float4x4 preWorld;
        Float4x4 invTransposeWorld;
        Float3 tilePosition;
        float _pad;
        Float3 preTilePosition;
    };

    #define LIGHT_INSTANCE_FLAG_CAST_SHADOW 0x00000001

    struct alignas(16) LightInstanceData
    {
        Float4x4 world;
        // Float4x4 preWorld;
        //  Float4x4 InvWorld;
        //  Float4x4 InvTransposeWorld;
        Float4 singleLightDirPos;
        Float4 singleLightTilePosition;
        Float4 singleLightAttenuation;
        Float4 singleLightColor;
        Float4 singleLightSpotDirection;
        int shadowDataIndex{-1};
        int lightIndex{-1};
        UInt32 instanceID;
        UInt32 flags;
    };

    struct RENDER_ENGINE_API InstanceDataVecContainer
    {
        struct InstanceData
        {
            Transform mTransform;
            bool mDelete = false;
        };
        struct InstanceDataLight
        {
            Transform mTransform;
            Float4 mLightDirPos;
            Float4 mLightTilePosition;
            Float4 mLightAttenuation;
            Float4 mLightColor;
            Float4 mLightSpotDiretion;
        };
    public:
        void AllocTransformVector(size_t size);
        void AllocInstanceData(size_t size);
        void AllocInstanceLightData(size_t size);
        std::vector<InstanceData> mInstanceData;
        std::vector<FoliageInstanceCompactData> mTransformVector;

        std::vector<InstanceDataLight> mInstanceDataLight;

        std::vector<LightInstanceData> mInstanceDataPointLight;
        std::vector<LightInstanceData> mInstanceDataSpotLight;

        void PrepareLightInstanceData(float gloalScale, Transform& localTransform, float globalRangeScale, bool castShadow);
    };
    struct DataContainerR
    {
        std::vector<FoliageInstanceCompactData> mLocalInstanceData;
        std::vector<UInt8> mInstanceData;
        std::vector<UInt8> mPreInstanceData;    
        std::vector<LightInstanceData> mInstanceDataPointLight;
        std::vector<LightInstanceData> mInstanceDataSpotLight;
    };
    struct FoliageComponentR : ecs::IComponent
    {
        ~FoliageComponentR();
        void ClearCPUData();
        friend class FoliageSystemR;

        struct LoDSection
        {
            MeshAssetDataResourcePtr mMeshAsset;
            MaterialR* mDefaultMaterial;
            std::vector<MaterialR*> mSubSectionMaterials;
        };

        struct SubmeshProperty
        {
            bool visible = true;
        };

        CEFunction(Reflect)
        RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();

    private:
        std::vector<LoDSection> mLoDSections;
        std::vector<SubmeshProperty> mSubmeshProperty;

        Float4x4 mWorldMatrix;
            // use for generate motion vector
        UInt64 mInstanceCount;
        UInt64 mPCGReservedCapacity;
        std::shared_ptr<DataContainerR> mDataContainer = std::make_shared<DataContainerR>();


        BoundingBox mWorldBound;
        bool mEnable = true;
        float mScreenSizeScale;
        float mMaxRandomCulling = 1.0f;
        FoliageGenerationType mFoliageGenerationType = FoliageGenerationType::FOLIAGE_GENERATION_TYPE_INSTANCE;

        friend class RenderNodeSystemR;
    };
}
