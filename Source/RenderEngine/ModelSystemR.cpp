#include "EnginePrefix.h"
#include "RenderEngine/ModelSystemR.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "ECS/Develop/Framework.h"
#include "RenderEngine/MeshBuildSystemR.h"
#include "RenderEngine/RenderNodeSystemR.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/SkeletonComponentR.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/MeshBlendShapeSystemR.h"
#include "RenderEngine/RenderPipelineSystemR.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/GPUScene/GPUScene.h"
#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/RenderCamera.h"
#include "RenderEngine/RenderNode/StaticModelRenderNode.h"
#include "RenderEngine/RenderNode/DynamicModelRenderNode.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/AABBSystemR.h"
#include "RenderEngine/EntityLifeCycleRenderDataSystemR.h"
#include "RenderEngine/RenderMesh.h"
#include "RenderNode/StellarMeshRenderNode.h"

namespace cross {
ecs::ComponentDesc* ModelComponentR::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetRenderComponentDesc<cross::ModelComponentR>(false);
}

ModelSystemR* ModelSystemR::CreateInstance()
{
    return new ModelSystemR();
}

void ModelSystemR::Release()
{
    delete this;
}

const MeshAssetData* ModelSystemR::GetMeshAssetData(const RenderModelComponentReader& reader, UInt32 modelIndex) const
{
    return GetModel(reader, modelIndex).mAsset->GetAssetData();
}

void ModelSystemR::OnBeginFrame(FrameParam* frameParam)
{
    mChangeList.GetContainer().clear();
}

void ModelSystemR::OnEndFrame(FrameParam* frameParam) {}

std::shared_ptr<ModelRenderNode> ModelSystemR::CreateModelRenderNode(ecs::EntityID entityID, ecs::ComponentBitMask componentBitMask)
{
    auto modelComp = mRenderWorld->GetComponent<ModelComponentR>(entityID);
    auto modelCompReader = modelComp.Read();
    auto modelCompWriter = modelComp.Write();

    bool isUseDynamicRenderNode = false;

    if (ecs::HasComponentMask<SkeletonComponentR>(componentBitMask))
    {
        isUseDynamicRenderNode = true;
    }

    for (UInt32 modelIndex = 0; modelIndex < GetModelCount(modelCompReader); modelIndex++)
    {
        if (GetModel(modelCompReader, modelIndex).mHasBlendShape)
        {
            isUseDynamicRenderNode = true;
        }
    }

    if (isUseDynamicRenderNode)
    {
        modelCompWriter->mRenderNode = std::make_shared<DynamicModelRenderNode>(modelComp.mComponent->mDistanceCulling.get());
        modelCompWriter->mRenderNode->GetObjectProperties().SetProperty(magic_enum::enum_name<MaterialUsage::USED_WITH_SKELETAL_MESH>(), true);
    }
    else
    {
        // Only support one model for stellar mesh
        auto& model = modelCompReader->mMainModel;
        bool isUseStellarMesh = model.mSharedRenderMesh ? model.mSharedRenderMesh->IsStellarMeshEnable() : false;
        if (isUseStellarMesh)
        {
            // TODO: Require DistanceCulling ?
            modelCompWriter->mRenderNode = std::make_shared<StellarMeshRenderNode>();
        }
        else
        {
            modelCompWriter->mRenderNode = std::make_shared<StaticModelRenderNode>(modelComp.mComponent->mDistanceCulling.get());
        }
    }

    return std::static_pointer_cast<ModelRenderNode>(modelCompWriter->mRenderNode);
}

void ModelSystemR::OnBuildUpdateTasks(FrameParam* frameParam)
{
    constexpr UInt32 FrameCount = 30;

    CreateTaskFunction(FrameTickStage::Update, {}, [this] {
        SCOPED_CPU_TIMING(GroupRendering, "ModelUpdateR");

        auto* GPUScene = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>()->GetWorldRenderPipeline()->GetGPUScene();
        auto* StellarMeshScene = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>()->GetWorldRenderPipeline()->GetStellarMeshScene();
        auto* entityLifyCycleSys = mRenderWorld->GetRenderSystem<EntityLifeCycleRenderDataSystemR>();

        for (UInt32 eventIndex = 0, eventCount = mRenderWorld->GetRemainedEventCount<EntityCreateEvent>(true); eventIndex < eventCount; ++eventIndex)
        {
            auto& lifeEvent = mRenderWorld->GetRemainedEvent<EntityCreateEvent>(eventIndex);
            if (ecs::HasComponentMask<ModelComponentR>(lifeEvent.mData.mChangedComponentMask))
            {
                auto& entityID = lifeEvent.mData.mEntityID;
                if (mRenderWorld->IsEntityAlive(entityID))
                {
                    auto renderNode = CreateModelRenderNode(entityID, lifeEvent.mData.mChangedComponentMask);

                    auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(entityID);
                    if (EnumHasAnyFlags(lifeEvent.mData.mEventFlag, EntityLifeCycleEventFlag::CreateComponent))
                    {
                        renderNodeComp.mComponent->SetRenderNode(renderNode);
                    }
                }
            }
        }

        // fetch remain change list
        while (mChangeList.GetCount() < FrameCount && mRemainChangeList.GetCount() > 0)
        {
            mChangeList.EmplaceChangeData(mRemainChangeList.PopFirstChangeData());
        }

        // Send Geometries to Mesh1
        for (auto it = mChangeList.GetContainer().begin(); it != mChangeList.GetContainer().end(); ++it)
        {
            ecs::EntityID changeModelEntity = (*it);
            if (!mRenderWorld->IsEntityAlive(changeModelEntity)) [[unlikely]]
                continue;
            auto [modelH, renderNodeH] = mRenderWorld->GetComponent<ModelComponentR, RenderNodeComponentR>(changeModelEntity);
            if (!modelH.IsValid()) [[unlikely]]
                continue;
            tLODSettings.clear();


            auto ConvertIndividualModelToRenderModel = [&](ModelComponentR::IndividualModel const& model) -> ModelRenderNode::RenderModel {
                ModelRenderNode::RenderModel renderModel{};

                MeshR* renderMesh = GetRenderMeshInternal(model);
                auto meshAssetData = model.mAsset->GetAssetData();

                renderModel.mReceiveDecals |= model.mReceiveDecals;
                renderModel.mReadyForStreaming = renderMesh->IsReadyForStreaming();

                auto modelLODCount = model.mLODModelProperties.size();
                renderModel.mLODModels.resize(modelLODCount);
                BoundingBox renderModelBoundingBox{BoundingBox::Flags::MergeIdentity};
                for (auto lodIndex = 0u; lodIndex < modelLODCount; lodIndex++)
                {
                    auto const& singleLODModelProperty = model.mLODModelProperties[lodIndex];
                    auto& singleLODRenderModel = renderModel.mLODModels[lodIndex];

                    auto subModelCount = singleLODModelProperty.mSubModelProperties.size();
                    singleLODRenderModel.mSubModels.resize(subModelCount);
                    for (auto subMeshIndex = 0u; subMeshIndex < subModelCount; subMeshIndex++)
                    {
                        auto const& subModelProperty = singleLODModelProperty.mSubModelProperties[subMeshIndex];

                        if (!subModelProperty.mVisible)
                            continue;

                        auto& subModel = singleLODRenderModel.mSubModels[subMeshIndex];

                        UInt32 subMeshStartIndex = 0;
                        UInt32 subMeshCount = 0;
                        meshAssetData->GetMeshLodInfo(lodIndex, subMeshStartIndex, subMeshCount);
                        subModel.mGeometry = subMeshIndex < subMeshCount ? &(renderMesh->GetRenderGeometry(subMeshStartIndex + subMeshIndex)) : nullptr;
                        subModel.mMaterial = subModelProperty.mMaterial;
                        subModel.mBoundingBox = meshAssetData->GetMeshPartBoundingBox(subMeshStartIndex + subMeshIndex);

                        BoundingBox::CreateMerged(renderModelBoundingBox, renderModelBoundingBox, subModel.mBoundingBox);
                    }
                }

                renderModel.mBoundingBox = std::move(renderModelBoundingBox);
                tLODSettings.push_back(model.mAsset->GetLODSetting());
                return renderModel;
            };

            auto CombineRenderModel = [](ModelRenderNode::RenderModel& dstModel, ModelRenderNode::RenderModel&& srcModel) {
                auto maxLODCount = std::max(dstModel.mLODModels.size(), srcModel.mLODModels.size());
                dstModel.mLODModels.resize(maxLODCount);

                BoundingBox::CreateMerged(dstModel.mBoundingBox, dstModel.mBoundingBox, srcModel.mBoundingBox);
                dstModel.mReceiveDecals |= srcModel.mReceiveDecals;
                dstModel.mReadyForStreaming |= srcModel.mReadyForStreaming;

                for (size_t lodIdx = 0; lodIdx < maxLODCount; lodIdx++)
                {
                    if (lodIdx >= srcModel.mLODModels.size()) break;

                    auto& dstSubModels = dstModel.mLODModels[lodIdx].mSubModels;
                    auto& srcSubModels = srcModel.mLODModels[lodIdx].mSubModels;
                    dstSubModels.insert(dstSubModels.begin(), std::make_move_iterator(srcSubModels.begin()), std::make_move_iterator(srcSubModels.end()));
                }
            };

            auto& model = GetModel(modelH.Read(), 0);
            MeshR* renderMesh = GetRenderMeshInternal(model);
            auto renderMeshState = renderMesh->GetState();
            bool isMeshDrawable = model.mVisible && renderMeshState == MeshR::State::Initialized;

            // RenderMesh's state may be initialized below. This may cause crash or mesh invisible.
            // TODO: Not support multiple models here
            if (isMeshDrawable && renderMesh->IsStellarMeshEnable())
            {
                StellarMeshRenderNode::RenderStellarMesh renderStellarMesh{};
                renderStellarMesh.mMeshConstantID = renderMesh->GetMeshIndex();
                auto& renderNode = modelH.Write()->mRenderNode;
                renderNode->FreeStellarMeshScene(*StellarMeshScene, changeModelEntity);
                renderNode->SetRenderStellarMesh(std::move(renderStellarMesh));
                // TODO
                renderNode->UploadStellarMeshScene(*StellarMeshScene, mRenderWorld, changeModelEntity);
            }
            else
            {
                ModelRenderNode::RenderModel renderModel{};
                auto modelCount = GetModelCount(modelH.Read());
                for (UInt32 idx = 0; idx < modelCount; idx++)
                {
                    auto& modelx = GetModel(modelH.Read(), idx);
                    if (!modelx.mVisible)
                        continue;

                    if (!renderMesh || renderMeshState != MeshR::State::Initialized)
                        continue;   // Not built

                    auto tempRenderModel = ConvertIndividualModelToRenderModel(modelx);
                    CombineRenderModel(renderModel, std::move(tempRenderModel));
                }

                auto& renderNode = modelH.Write()->mRenderNode;
                renderNode->SetLODSettings(tLODSettings);

                renderNode->FreeGPUScene(*GPUScene);
                renderNode->SetRenderModel(std::move(renderModel));
                renderNode->AllocateGPUScene(*GPUScene);
                GPUScene->SetGPUSceneDirty(changeModelEntity);
                entityLifyCycleSys->AddRenderEntityUpdateVSM(changeModelEntity);
            }
        }
    });
}

void ModelSystemR::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    RenderSystemBase::NotifyEvent(event, flag);
    if (event.mEventType == OnSystemAddToRenderWorldEvent::sEventType)
    {
        mRenderWorld->SubscribeRemainedEvent<EntityDestroyEvent>(this, true);
    }
    else if (event.mEventType == RemainedEventUpdatedEvent::sEventType)
    {
        const RemainedEventUpdatedEvent& e = static_cast<const RemainedEventUpdatedEvent&>(event);
        if (e.mData.mRemainedEventType == EntityDestroyEvent::sEventType)
        {
            auto* GPUScene = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>()->GetWorldRenderPipeline()->GetGPUScene();
            for (UInt32 i = e.mData.mFirstIndex; i <= e.mData.mLastIndex; i++)
            {
                auto& lifeEvent = mRenderWorld->GetRemainedEvent<EntityDestroyEvent>(i);
                auto entity = lifeEvent.mData.mEntityID;

                if (ecs::HasComponentMask<ModelComponentR>(lifeEvent.mData.mChangedComponentMask) && EnumHasAnyFlags(lifeEvent.mData.mEventFlag, EntityLifeCycleEventFlag::DestroyComponent))
                {
                    auto& entityID = lifeEvent.mData.mEntityID;
                    auto modelComp = mRenderWorld->GetComponent<ModelComponentR>(entityID);
                    auto& renderNode = modelComp.Write()->mRenderNode;
                    if (renderNode)
                        renderNode->FreeGPUScene(*GPUScene);
                }
            }
        }
    }
}

void ModelSystemR::SetModelAsset(ecs::EntityID entity, MeshAssetDataResourcePtr asset, UInt32 modelIndex)
{
    auto modelH = mRenderWorld->GetComponent<ModelComponentR>(entity);
    if ((UInt32)modelH.Read()->mChangeableModels.size() < modelIndex)
    {
        Assert(modelIndex == (UInt32)modelH.Read()->mChangeableModels.size() + 1);
        modelH.Write()->mChangeableModels.emplace_back(ModelComponentR::IndividualModel());
    }

    auto writer = modelH.Write();
    auto& model = GetModel(writer, modelIndex);

    model.mAsset = asset;
    model.mSharedRenderMesh = static_cast<MeshR*>(asset->GetAssetData()->GetRenderMesh());
    Assert(model.mSharedRenderMesh != nullptr);
    model.mRenderMeshDirty = true;
    model.mSkinningStatus = SkinningStatus::NotSkinned;

    auto& privateModelGeometryList = model.mPrivateRenderMesh;
    if (privateModelGeometryList && privateModelGeometryList->GetState() == MeshR::State::Initialized)
    {
        privateModelGeometryList->SetState(MeshR::State::NeedReinitialized);
    }

    const MeshAssetData* meshAsset = asset->GetAssetData();

    auto ResizeModel = [&] {
        auto lodCount = std::min(meshAsset->GetLodCount(), resource::MAX_MESH_LOD_NUM);
        model.mLODModelProperties.resize(lodCount);
        for (int i = 0; i < lodCount; i++)
        {
            auto subMeshCount = asset->GetAssetData()->GetMeshPartCount(i);
            model.mLODModelProperties[i].mSubModelProperties.resize(subMeshCount);
        }
    };

    auto SetBlendShape = [&] {
        // Reset data
        model.mHasBlendShape = false;
        model.mBlendShapeVertexPNTData.reset();

        const auto& lodStartIndexArray = meshAsset->GetLodStartIndex();

        int lodIdx = 0;
        for (auto& lodModelProperties : model.mLODModelProperties)
        {
            int subModelIdx = 0;
            for (auto& subModelProperty : lodModelProperties.mSubModelProperties)
            {
                auto const& meshPartInfo = meshAsset->GetMeshPartInfo(lodStartIndexArray[lodIdx] + subModelIdx);
                subModelProperty.mHasBlendShape = meshPartInfo.mBlendShape.HasBlendShape();
                subModelProperty.mBlendShapeDirty = false;

                if (subModelProperty.mHasBlendShape)
                {
                    InitChannelWeightFromBlendShape(subModelProperty.mChannelWeight, meshPartInfo.mBlendShape);
                }

                subModelIdx++;
            }

            lodIdx++;
        }

        // Model's blend shape vertex data
        if (model.mHasBlendShape)
        {
            model.mBlendShapeVertexPNTData = std::make_shared<BlendShapeVertexData>();
            model.mBlendShapeVertexPNTData->CopyDataFromMesh(meshAsset);
        }
    };

    if (asset)
    {
        ResizeModel();
        SetBlendShape();
    }

    model.mIsStaticBuilt = false;
    SetModelDirty(entity);
}

void ModelSystemR::RebuildModelModelAsset(ecs::EntityID entity)
{
    auto modelH = mRenderWorld->GetComponent<ModelComponentR>(entity);

    UInt32 modelCount = GetModelCount(modelH.Read());
    auto writer = modelH.Write();
    for (UInt32 iModel = 0; iModel < modelCount; iModel++)
    {
        auto& model = GetModel(writer, iModel);
        auto* meshAsset = model.mAsset->GetAssetData();
        meshAsset->GetRenderMesh()->BuildStaticMesh();
    }
    SetModelDirty(entity);
}

void ModelSystemR::SetSkeltModelPose(ecs::EntityID entity, UInt32 modelIndex, FrameVector<SIMDMatrix>* modelPosePtr)
{
    auto modelH = mRenderWorld->GetComponent<ModelComponentR>(entity);
    auto& model = GetModel(modelH.Write(), modelIndex);

    Assert(model.mAsset->IsSkinValid());

    model.mCompletePose = modelPosePtr;
}

void ModelSystemR::SetModelMaterial(ecs::EntityID entity, MaterialR* material, SInt32 subModelIndex, UInt32 modelIndex)
{
    auto modelH = mRenderWorld->GetComponent<ModelComponentR>(entity);
    auto writer = modelH.Write();
    auto& model = GetModel(writer, modelIndex);

    auto& subModelProperties = model.mLODModelProperties[0].mSubModelProperties;
    if (subModelIndex > -1)
    {
        subModelProperties[subModelIndex].mMaterial = material;
    }
    else
    {
        for (auto& prop : subModelProperties)
        {
            prop.mMaterial = material;
        }
    }

    SetModelDirty(entity);
}

void ModelSystemR::SetModelLodMaterial(ecs::EntityID entity, MaterialR* material, UInt32 subModelIndex, UInt32 lodIndex, UInt32 modelIndex)
{
    auto modelH = mRenderWorld->GetComponent<ModelComponentR>(entity);
    auto writer = modelH.Write();
    auto& model = GetModel(writer, modelIndex);

    if (model.mLODModelProperties.size() <= lodIndex)
        model.mLODModelProperties.resize(lodIndex + 1);
    model.mLODModelProperties[lodIndex].mSubModelProperties[subModelIndex].mMaterial = material;
    
    SetModelDirty(entity);
}

MaterialR* ModelSystemR::GetModelLodMaterial(const RenderModelComponentReader& modelH, UInt32 subModelIndex, UInt32 lodIndex, UInt32 modelIndex)
{
    auto& model = GetModel(modelH, modelIndex);

    if (lodIndex < model.mLODModelProperties.size())
    {
        auto& subModelProperties = model.mLODModelProperties[lodIndex].mSubModelProperties;

        if (subModelIndex < subModelProperties.size())
        {
            return subModelProperties[subModelIndex].mMaterial;
        }
    }

    return nullptr;
}

RENDER_ENGINE_API void ModelSystemR::SetModelEnityDistanceCulling(ecs::EntityID entity, const EntityDistanceCulling& entityCulling)
{
    auto modelH = mRenderWorld->GetComponent<ModelComponentR>(entity);
    *modelH.Write()->mDistanceCulling = entityCulling;
}

RENDER_ENGINE_API EntityDistanceCulling ModelSystemR::GetModelEnityDistanceCulling(ecs::EntityID entity)
{
    auto modelH = mRenderWorld->GetComponent<ModelComponentR>(entity);
    return *modelH.Read()->mDistanceCulling;
}

MaterialR* ModelSystemR::GetModelMaterial(const RenderModelComponentReader& modelH, UInt32 subModelIndex, UInt32 modelIndex)
{
    auto& model = GetModel(modelH, modelIndex);

    auto& subModelProperties = model.mLODModelProperties[0].mSubModelProperties;
    if (subModelIndex < (UInt32)subModelProperties.size())
    {
        return subModelProperties[subModelIndex].mMaterial;
    }
    else
    {
        return nullptr;
    }
}

void ModelSystemR::SetBatchInfo(ecs::EntityID entity, MeshBatchInfo batchInfo)
{
    auto modelH = mRenderWorld->GetComponent<ModelComponentR>(entity);
    modelH.Write()->mBatchInfo = batchInfo;
}

MeshR* ModelSystemR::GetModelSharedRenderMesh(const RenderModelComponentWriter& writer, UInt32 modelIndex) const
{
    return GetModel(writer, modelIndex).mSharedRenderMesh;
}

void ModelSystemR::SetModelSharedRenderMesh(const RenderModelComponentWriter& writer, UInt32 modelIndex, MeshR* renderMesh)
{
    GetModel(writer, modelIndex).mSharedRenderMesh = renderMesh;
}

std::shared_ptr<MeshR> ModelSystemR::GetModelPrivateRenderMesh(const RenderModelComponentWriter& writer, UInt32 modelIndex)
{
    auto& privatedModelRenderMesh = GetModel(writer, modelIndex).mPrivateRenderMesh;
    if (!privatedModelRenderMesh || (privatedModelRenderMesh && privatedModelRenderMesh->GetState() == MeshR::State::NeedReinitialized))
    {
        privatedModelRenderMesh.reset(new MeshR());
        SetModelDirty(writer.GetEntityID());
    }
    return privatedModelRenderMesh;
}

void ModelSystemR::ClearModelPrivateRenderMesh(const RenderModelComponentWriter& writer, UInt32 modelIndex)
{
    auto& privatedModelRenderMesh = GetModel(writer, modelIndex).mPrivateRenderMesh;
    if (privatedModelRenderMesh)
        privatedModelRenderMesh.reset();
}

void ModelSystemR::SetModelVisibility(ecs::EntityID entity, bool isVisible, UInt32 modelIndex)
{
    auto writer = mRenderWorld->GetComponent<ModelComponentR>(entity).Write();
    if (GetModel(writer, modelIndex).mVisible != isVisible)
    {
        GetModel(writer, modelIndex).mVisible = isVisible;
        GetModel(writer, modelIndex).mRenderMeshDirty = true;
        GetModel(writer, modelIndex).mSkinningStatus = SkinningStatus::NotSkinned;
        SetModelDirty(entity);
    }
}

void ModelSystemR::SetModelReceiveDecals(ecs::EntityID entity, bool value, UInt32 modelIndex)
{
    auto modelH = mRenderWorld->GetComponent<ModelComponentR>(entity);
    auto writer = modelH.Write();
    GetModel(writer, modelIndex).mReceiveDecals = value;

    SetModelDirty(entity);
}

bool ModelSystemR::GetModelVisibility(ecs::EntityID entity, UInt32 modelIndex)
{
    auto modelH = mRenderWorld->GetComponent<ModelComponentR>(entity);
    auto rpH = mRenderWorld->GetComponent<RenderPropertyComponentR>(entity);
    if (mRenderWorld->GetRenderSystem<RenderPropertySystemR>()->IsHide(rpH.Read()))
        return false;
    auto reader = modelH.Read();
    return GetModel(reader, modelIndex).mVisible;
}

void ModelSystemR::SetModelSubVisibility(ecs::EntityID entity, bool isVisible, SInt32 subModelIndex, UInt32 lodindex, UInt32 modelIndex)
{
    auto modelH = mRenderWorld->GetComponent<ModelComponentR>(entity);
    auto writer = modelH.Write();
    auto& subModelProperties = GetModel(writer, modelIndex).mLODModelProperties[lodindex].mSubModelProperties;
    if (subModelIndex > -1)
    {
        subModelProperties[subModelIndex].mVisible = isVisible;
    }
    else
    {
        for (auto& prop : subModelProperties)
        {
            prop.mVisible = isVisible;
        }
    }

    SetModelDirty(entity);
}

void ModelSystemR::RemoveModel(ecs::EntityID entity, UInt32 modelIndex)
{
    auto modelH = mRenderWorld->GetComponent<ModelComponentR>(entity);
    auto writer = modelH.Write();
    writer->mChangeableModels.erase(writer->mChangeableModels.begin() + modelIndex - 1);
}

RenderModelComponentHandle ModelSystemR::GetModelHandle(ecs::EntityID entity)
{
    return mRenderWorld->GetComponent<ModelComponentR>(entity);
}

UInt8 ModelSystemR::GetModelLODIndex(const RenderModelComponentReader& modelH, UInt32 modelIndex) const
{
    auto meshAsset = GetModelAsset(modelH, modelIndex);
    UInt8 meshLodCount = meshAsset->GetAssetData()->GetLodCount();

    auto* renderNodeSys = mRenderWorld->GetRenderSystem<RenderNodeSystemR>();
    auto* cameraSys = mRenderWorld->GetRenderSystem<CameraSystemR>();
    auto* transformSys = mRenderWorld->GetRenderSystem<TransformSystemR>();
    auto* aabbSys = mRenderWorld->GetRenderSystem<AABBSystemR>();

    const auto& [renderNodeComp, tilePositionComp, tranComp, aabbComp] = mRenderWorld->GetComponent<RenderNodeComponentR, TilePositionComponentR, TransformComponentR, AABBComponentR>(modelH.GetEntityID());

    // Get WorldAABB
    BoundingBox curBoundingBox = aabbSys->GetWorldAABB(aabbComp.Read());

    auto* renderNode = dynamic_cast<const ModelRenderNode*>(renderNodeSys->GetRenderNode(renderNodeComp.Read()));

    if (!renderNode)
    {
        return 0;
    }

    auto maincameraID = cameraSys->GetMainCamera();
    auto maincamera = cameraSys->GetRenderCamera(mRenderWorld->GetComponent<CameraComponentR>(maincameraID).Read());
    auto& cameraView = maincamera->GetCameraView();

    Float3 tilePosition = transformSys->GetTilePosition(tilePositionComp.Read());
#ifdef CE_USE_DOUBLE_TRANSFORM
    curBoundingBox.Transform(curBoundingBox, (tilePosition - cameraView.mCameraTilePosition) * LENGTH_PER_TILE_F);
#endif

    BoundingSphere curBoudingS;
    BoundingSphere::CreateFromBoundingBox(curBoudingS, curBoundingBox);
    Float2 outSize;
    //  Use boundingsphere to be consistent with the rendering
    CameraUtility::GetScreenSize(outSize, BoundingSphere(curBoundingBox.GetCenter(), curBoudingS.GetRadius()), cameraView, maincamera->GetProjectionMode());
    float screenRadius = outSize.y;

    // Calculate LOD
    const resource::MeshAssetLODSetting* lodSetting = renderNode->GetLODSetting();
    return renderNode->GetLODIndex(&screenRadius, meshLodCount, lodSetting);
}

bool ModelSystemR::IsSkeletalModel(const RenderModelComponentReader& modelH) const
{
    return modelH->mMainModel.mAsset ? modelH->mMainModel.mAsset->IsSkinValid() : false;
}

bool ModelSystemR::IsSkeletalModel(const RenderModelComponentReader& reader, UInt32 modelIndex) const
{
    return GetModel(reader, modelIndex).mAsset->IsSkinValid();
}

FrameVector<SIMDMatrix>* ModelSystemR::GetSkeltModelPose(ecs::EntityID entity, UInt32 modelIndex) const
{
    auto modelH = mRenderWorld->GetComponent<ModelComponentR>(entity);
    const auto& model = GetModel(modelH.Read(), modelIndex);

    Assert(model.mAsset->IsSkinValid());

    return model.mCompletePose;
}

void ModelSystemR::SetRenderGeometryAdditiveVertexChannel(ecs::EntityID entity, UInt32 modelIndex, VertexSemantic inSematic)
{
    auto modelH = mRenderWorld->GetComponent<ModelComponentR>(entity);
    GetModel(modelH.Write(), modelIndex).mAdditiveVertexSemantics |= inSematic;
}

bool ModelSystemR::IsRenderGeometryAdditiveVertexChannel(ecs::EntityID entity, UInt32 modelIndex, VertexSemantic inSematic)
{
    auto modelH = mRenderWorld->GetComponent<ModelComponentR>(entity);
    auto result = (GetModel(modelH.Read(), modelIndex).mAdditiveVertexSemantics & inSematic) > 0;
    return result;
}

void ModelSystemR::RemoveRenderGeomertyAdditiveVertexChannel(ecs::EntityID entity, UInt32 modelIndex, VertexSemantic inSematic)
{
    auto modelH = mRenderWorld->GetComponent<ModelComponentR>(entity);
    GetModel(modelH.Write(), modelIndex).mAdditiveVertexSemantics &= !inSematic;
}

bool ModelSystemR::IsSubModelHasBlendShape(ecs::EntityID entity, UInt32 modelIndex, UInt32 subModelIndex) const
{
    auto modelH = mRenderWorld->GetComponent<ModelComponentR>(entity);
    auto& model = GetModel(modelH.Read(), modelIndex);

    auto& subModelProperties = model.mLODModelProperties[0].mSubModelProperties;
    Assert(subModelIndex < model.mLODModelProperties[0].mSubModelProperties.size());
    return subModelProperties[subModelIndex].mHasBlendShape;
}

bool ModelSystemR::IsModelHasBlendShape(const RenderModelComponentReader& modelH, UInt32 modelIndex) const
{
    return GetModel(modelH, modelIndex).mHasBlendShape;
}

bool ModelSystemR::GetSubModelBlendShapeDirty(const RenderModelComponentReader& modelH, UInt32 modelIndex, UInt32 subModelIndex) const
{
    return GetModel(modelH, modelIndex).mLODModelProperties[0].mSubModelProperties[subModelIndex].mBlendShapeDirty;
}

void ModelSystemR::SetSubModelBlendShapeDirty(const RenderModelComponentWriter& modelH, UInt32 modelIndex, UInt32 subModelIndex, bool dirty)
{
    GetModel(modelH, modelIndex).mLODModelProperties[0].mSubModelProperties[subModelIndex].mBlendShapeDirty = dirty;
}

bool ModelSystemR::GetModelBlendShapeDirty(const RenderModelComponentReader& modelH, UInt32 modelIndex) const
{
    return GetModel(modelH, modelIndex).mBlendShapeDirty;
}

void ModelSystemR::SetModelBlendShapeDirty(const RenderModelComponentWriter& modelH, UInt32 modelIndex, bool dirty)
{
    GetModel(modelH, modelIndex).mBlendShapeDirty = dirty;
}

std::vector<std::reference_wrapper<const ChannelWeightData>> ModelSystemR::GetSubModelBlendShapeLODChannelWeights(const RenderModelComponentReader& modelH, UInt32 modelIndex, UInt32 subModelIndex) const
{
    auto& model = GetModel(modelH, modelIndex);

    std::vector<std::reference_wrapper<const ChannelWeightData>> channelWeights;
    for (auto const& singleLODProperties : model.mLODModelProperties)
    {
        channelWeights.emplace_back(singleLODProperties.mSubModelProperties[subModelIndex].mChannelWeight);
    }

    return channelWeights;
}

const ChannelWeightData& ModelSystemR::GetSubModelBlendShapeChannelWeights(const RenderModelComponentReader& modelH, UInt32 modelIndex, UInt32 subModelIndex, UInt32 lodIndex) const
{
    return GetModel(modelH, modelIndex).mLODModelProperties[lodIndex].mSubModelProperties[subModelIndex].mChannelWeight;
}

BlendShapeVertexData* ModelSystemR::GetModelBlendShapeVertexData(const RenderModelComponentWriter& modelH, UInt32 modelIndex)
{
    return GetModel(modelH, modelIndex).mBlendShapeVertexPNTData.get();
}

const BlendShapeVertexData* ModelSystemR::GetModelBlendShapeVertexData(const RenderModelComponentReader& modelH, UInt32 modelIndex) const
{
    return GetModel(modelH, modelIndex).mBlendShapeVertexPNTData.get();
}

bool ModelSystemR::SetSubModelBlendShapeChannelWeight(ecs::EntityID entity, UInt32 modelIndex, UInt32 subModelIndex, HashString channelName, float weight, UInt32 lodIndex)
{
    if (!IsSubModelHasBlendShape(entity, modelIndex, subModelIndex))
    {
        return false;
    }

    auto modelH = mRenderWorld->GetComponent<ModelComponentR>(entity);
    auto& model = GetModel(modelH.Write(), modelIndex);

    const auto lodCount = model.mAsset->GetAssetData()->GetLodCount();
    if (lodIndex >= lodCount)
    {
        return false;
    }

    auto& curLodShapeChannelWeight = model.mLODModelProperties[lodIndex].mSubModelProperties[subModelIndex].mChannelWeight;
    auto res = curLodShapeChannelWeight.find(channelName);
    if (res == curLodShapeChannelWeight.end())
    {
        return false;
    }

    res->second = MathUtils::Clamp<float>(weight, 0.0f, 1.0f);
    SetSubModelBlendShapeDirty(modelH.Write(), modelIndex, subModelIndex, true);

    // To make sure only add to MeshBlendShapeSys once
    if (GetModelBlendShapeDirty(modelH.Read(), modelIndex) == false)
    {
        SetModelBlendShapeDirty(modelH.Write(), modelIndex, true);

        auto meshBlendShapeSys = mRenderWorld->GetRenderSystem<MeshBlendShapeSystemR>();
        meshBlendShapeSys->AddModelNeedRefreshBlendShape(entity, modelIndex);
    }

    // Need Re-AssembleGpuResource for this model because blend shape channel weight changes
    model.mRenderMeshDirty = true;
    model.mSkinningStatus = SkinningStatus::NotSkinned;
    return true;
}

bool ModelSystemR::GetSubModelBlendShapeChannelWeight(const RenderModelComponentReader& modelH, UInt32 modelIndex, UInt32 subModelIndex, HashString channelName, float& outWeight, UInt32 lodIndex) const
{
    if (!IsSubModelHasBlendShape(modelH.GetEntityID(), modelIndex, subModelIndex))
    {
        return false;
    }

    auto& model = GetModel(modelH, modelIndex);

    const auto lodCount = model.mAsset->GetAssetData()->GetLodCount();
    if (lodIndex >= lodCount)
    {
        return false;
    }

    auto& curLodShapeChannelWeight = model.mLODModelProperties[lodIndex].mSubModelProperties[subModelIndex].mChannelWeight;
    auto res = curLodShapeChannelWeight.find(channelName);
    if (res == curLodShapeChannelWeight.end())
    {
        return false;
    }

    outWeight = res->second;
    return true;
}

void ModelSystemR::SetModelDirty(ecs::EntityID entity, bool isRemain)
{
    if (mChangeList.HasChangeData(entity))
        return;
    if (isRemain)
        mRemainChangeList.EmplaceChangeData(entity);
    else
        mChangeList.EmplaceChangeData(entity);
}

void ModelSystemR::SetModelSkinLevelByEntityID(ecs::EntityID entity, UInt32 modelIndex, SkinLevel skinlevel)
{
    // SkinLevel is used to determine whether to skin all models(LOD_0-LOD_N) or only the current LOD model.
    auto modelComp = mRenderWorld->GetComponent<ModelComponentR>(entity);
    SetModelSkinLevel(modelComp.Write(), modelIndex, skinlevel);
}

void ModelSystemR::SetIsStatic(ecs::EntityID entity, bool value)
{
    mRenderWorld->GetComponent<ModelComponentR>(entity).Write()->mIsStatic = value;
}

void ModelSystemR::OnPendingToDestroy()
{
}

MeshR* ModelSystemR::GetRenderMeshInternal(const ModelComponentR::IndividualModel& model)
{
    if (model.mPrivateRenderMesh && !model.mPrivateRenderMesh->GetRenderGeometries().empty())
    {
        return model.mPrivateRenderMesh.get();
    }
    else if (model.mSharedRenderMesh && !model.mSharedRenderMesh->GetRenderGeometries().empty())
    {
        return model.mSharedRenderMesh;
    }
    else
    {
        return nullptr;
    }
}
}   // namespace cross
