#pragma once
#include "RenderEngine/RenderContext.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/ComputeShaderR.h"
#include "RenderEngine/RayTracingShaderR.h"
#include "CrossBase/String/NameID.h"
#include "RenderEngine/RenderEngineForward.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDResource.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDPayload.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDCulling.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDRenderRawDrawUnitsPayload.h"

#include <string>
#include <vector>
#include <optional>
#include <map>
#if _WIN32
    #include <memory_resource>
#endif
namespace cross {
struct REDRenderStellarMeshDesc;

struct REDColorTargetDesc
{
    REDTextureView* Target = nullptr;

    NGILoadOp LoadOp;
    NGIStoreOp StoreOp;

    NGIClearValue ClearValue;

    REDTextureView* ResolveTarget = nullptr;
};

struct REDDepthStencilTargetDesc
{
    REDTextureView* Target = nullptr;

    NGILoadOp DepthLoadOp;
    NGIStoreOp DepthStoreOp;

    NGILoadOp StencilLoadOp;
    NGIStoreOp StencilStoreOp;

    NGIClearValue ClearValue;

    REDTextureView* ResolveTarget = nullptr;
    NGIResolveType DepthResolveType;
    NGIResolveType StencilResolveType;
};

enum class REDPassFlagBit : UInt8
{
    NeedDepth = 1u,
    DepthReadOnly = NeedDepth << 1,
    NeedStencil = DepthReadOnly << 1,
    StencilReadOnly = NeedStencil << 1,
};

ENUM_CLASS_FLAGS(REDPassFlagBit)

struct REDPass;
class RenderWorld;
class RenderCamera;
struct PersistentResourceManager;
struct NGIResourceManager;

struct REDRenderPass
{
    REDRenderPass();
    std::string mName;
    FrameStdVector<REDColorTargetDesc> mColorTargetDescs;

    std::optional<REDDepthStencilTargetDesc> mDepthStencilTargetDesc;

    FrameStdVector<NGIClearValue> mClearValues;

    UInt32 mWidth, mHeight, mLayerCount, mSampleCount;

    /*
     * subpass相当于一个子图，在这里有自己的prologue和epilogue，对应主图的begin render pass和end render pass
     * 子图只处理input/color/depth/stencil之间的关系，resolve target由外部处理
     */
    FrameStdVector<REDPass*> mSubpasses;

    NGIRenderPass* mRenderPass = nullptr;

    NGIFramebuffer* mFramebuffer = nullptr;

    FrameStdMap<REDTextureSubresource, REDResourceRangeState*> mTargets;
    FrameStdVector<NGITextureBarrier> mTargetBarriers;
    REDPass* mBeginPass = nullptr;
    REDPass* mEndPass = nullptr;
    REDPass* mOuterPass = nullptr;

    void AllocateNativeRenderPasss(PersistentResourceManager* resMgr);
    void AllocateNativeFramebuffer(NGIResourceManager* resMgr);
};

struct REDRegion
{
    REDRegion(UInt32 depth, REDRegion* parent, std::string_view name);

    UInt32 Depth;
    REDRegion* Parent;
    std::string Name;
    PropertySet PropertySet{nullptr};
};

struct RENDER_ENGINE_API REDPass
{
    friend struct REDVisualizer;
    friend struct RenderingExecutionDescriptor;
    friend struct REDRenderDrawUnitsPayload;
    friend struct REDDrawMeshPayload;
    friend struct REDRenderRawDrawUnitsPayload;
    friend struct REDRenderStellarMeshPayload;
    friend struct REDRenderPass;

public:
    REDPass(std::string_view name, bool noCulling, REDRegion* region);
    ~REDPass();
    

    /*
        额外的read write接口，用于指定shader resource, unordered access, copy src/dst, resolve src/dst等
        name：资源在shader中的名字
        view：资源本身
        readState：资源被用到的状态
     */
    void AddTextureReference(REDTextureView* view, const REDResourceState& state);

    void AddBufferReference(REDBuffer* buffer, const REDResourceState& state);

    // !!! Depreacated, use AddResourceReferencesFromProperties instead
    void AddPassResourceReferences(NGIPipelineLayout* layout);

    void AddResourceReferencesFromProperties(const NGIResourceGroupLayoutDesc& layoutDesc);

    NGIRenderPass* GetRenderPass()
    {
        if (mRenderPass)
        {
            return mRenderPass->mRenderPass;
        }
        else
            return nullptr;
    }

    UInt32 GetSubpass()
    {
        return mSubpass;
    }

    const auto& GetName() const
    {
        return mName;
    }
    
    void SetProperty(const NameID& name, const void* data, size_t dataSize)
    {
        mContext.SetProperty(name, data, dataSize);
    }

    template<typename T, typename = typename std::enable_if_t<IsOneOf<T, NUMERIC_PROPERTY_TYPES, ALIGNED_NUMERIC_PROPERTY_TYPES, NGISampler*, NGIBufferView*, NGITextureView*>>>
    void SetProperty(const NameID& ID, T&& value)
    {
        mContext.SetProperty(ID, std::forward<T>(value));
    }

    template<typename T, typename = typename std::enable_if_t<IsOneOf<T, NGISampler*, NGIBufferView*, NGITextureView*>>>
    void SetProperty(const NameID& ID, UInt32 num, T&& values)
    {
        mContext.SetProperty(ID, num, std::forward<T>(values));
    }

    void SetProperty(const NameID& ID, UInt32 num, REDTextureView** texViews, NGIResourceState* states = nullptr);

    void SetProperty(const NameID& ID, UInt32 num, REDBufferView** bufViews, NGIResourceState* states = nullptr);

    void SetProperty(const NameID& ID, REDTextureView* texView, NGIResourceState state = NGIResourceState::Undefined)
    {
        SetProperty(ID, 1, &texView, &state);
    }

    void SetProperty(const NameID& ID, REDBufferView* bufView, NGIResourceState state = NGIResourceState::Undefined)
    {
        SetProperty(ID, 1, &bufView, &state);
    }

    void SetProperty(const NameID& ID, NGIAccelStruct* AccelStruct)
    {
        mContext.SetProperty(ID, AccelStruct);
    }

    template<
        typename... TArgs,
        typename = typename std::enable_if_t<std::is_same_v<REDTextureView*, TArgs...>>
    >
    void SetProperty(const NameID& ID, TArgs&&... resViews)
    {
        if constexpr (std::is_same_v<REDTextureView*, TArgs...>)
        {
            REDTextureView* texViewArr[]{resViews...};
            SetProperty(ID, static_cast<UInt32>(sizeof...(resViews)), texViewArr, nullptr);
        }
        else if constexpr (std::is_same_v<REDBufferView*, TArgs...>)
        {
            REDBufferView* texViewArr[]{ resViews... };
            SetProperty(ID, static_cast<UInt32>(sizeof...(resViews)), texViewArr, nullptr);
        }
    }

    void RemoveProperty(const NameID& ID)
    {
        mContext.RemoveProperty(ID);
    }

    // !!!Deprecated
    auto& GetContext()
    {
        return mContext;
    }

    template<typename T>
    void OverrideProperty(const NameID& ID, T&& value)
    {
        Assert(false);
    }

    void DrawScreenQuad(const REDDrawScreenQuad& drawScreenQuad)
    {
        mPayloads.emplace_back(std::make_shared<REDDrawMeshPayload>(drawScreenQuad));
    }

    void DrawMesh(const REDDrawMesh& drawMesh)
    {
        mPayloads.emplace_back(std::make_shared<REDDrawMeshPayload>(drawMesh));
    }

    // !!!Deprecated, use OnExecuted instead
    template<typename TFunc>
    void Execute(TFunc&& executor)
    {
        OnExecute(std::forward<TFunc>(executor));
    }

    template<typename TFunc>
    void OnExecute(TFunc&& executor)
    {
        if constexpr (std::is_invocable_v<TFunc, REDPass*, NGIBundleCommandList*>)
        {
            mPayloads.emplace_back(std::make_shared<REDBundleCommandExecutionPayload>(std::forward<TFunc>(executor)));
        }
        else if constexpr (std::is_invocable_v<TFunc, REDPass*, NGICommandList*>)
        {
            mPayloads.emplace_back(std::make_shared<REDCommandExecutionPayload>(std::forward<TFunc>(executor)));
        }
        else
        {
            Assert(false);
        }
    }

    void AddPayload(std::shared_ptr<REDPayload> payload)
    {
        mPayloads.emplace_back(payload);
    }

    void RenderRawDrawUnits(const NameID& passName, FrameStdVector<REDRawDrawUnit>& drawUnits)
    {
        mPayloads.emplace_back(std::make_shared<REDRenderRawDrawUnitsPayload>(passName, drawUnits));
    }

    REDComputeExecutionPayload* Dispatch(ComputeShaderR* computeShader, const NameID& kernel, UInt32 threadGroupCountX, UInt32 threadGroupCountY, UInt32 threadGroupCountZ);

    REDComputeExecutionPayload* Dispatch(ComputeShaderR* computeShader, const NameID& kernel, UInt3 threadGroupCount);

    REDRayTracingExecutionPayload* DispatchRays(RayTracingShaderR* rayTracingShader, UInt32 width, UInt32 height, UInt32 depth);
    
    void DispatchIndirect(ComputeShaderR* computeShader, const NameID& kernel, REDBuffer* argumentBuffer, UInt32 offset);

    void RenderDrawUnits(const REDRenderDrawUnitsDesc& desc);

    void RenderStellarMesh(const REDRenderStellarMeshDesc& desc);

    template<typename TFunc>
    void OnCulling(TFunc&& func)
    {
        struct FrustumCullingCallbackPayload : public REDPayload
        {
            FrustumCullingCallbackPayload(TFunc&& func) : mFunc{ std::forward<TFunc>(func) } {}

            TFunc mFunc;

            void OnCulling(REDPass* pass) override
            {
                mFunc(pass);
            }
        };

        mPayloads.emplace_back(std::make_shared<FrustumCullingCallbackPayload>(std::forward<TFunc>(func)));
    }

    using BufferType = std::variant<REDBuffer*, NGIBuffer*>;

    using TextureType = std::variant<REDTexture*, NGITexture*>;

    void CopyBufferToBuffer(REDBuffer* dstBuffer, BufferType srcBuffer, UInt32 regionCount, const NGICopyBuffer* regions);

    void CopyBufferToTexture(REDTexture* dstTexture, BufferType srcBuffer, UInt32 regionCount, const NGICopyBufferTexture* regions);

    void CopyTextureToBuffer(REDBuffer* dstBuffer, REDTexture* srcTexture, UInt32 regionCount, const NGICopyBufferTexture* regions);

    void CopyTextureToTexture(REDTexture* dstTexture, REDTexture* srcTexture, UInt32 regionCount, const NGICopyTexture* regions);

    void ClearTexture(REDTextureView* textureView, const NGIClearValue& clearValue);

    void ClearBuffer(REDBufferView* bufferView, UInt32 clearValue);

    //!!!Deprecated
    template<typename T>
    void SetRenderContext(T&& ctx)
    {
        ctx.VisitProperty(Overloaded{
            [&](const NameID& name, const PropertySet::NumericProperty& np) { std::visit(Overloaded{[&](auto& tval) { this->SetProperty(name, tval); }, [&](const FrameStdVector<UInt8>& tval) { this->SetProperty(name, tval.data(), tval.size()); }}, np); },
            [&](const NameID& name, const PropertySet::ResourcePropVector& rp) { std::visit(Overloaded{ [&](auto& tval) { this->SetProperty(name, tval); } , [](const std::monostate&) {} }, rp.mData.front().Value); } });
    }

    bool IsGraphicsPass() const
    {
        return mRenderPass != nullptr;
    }

    FrameStdVector<NGIBufferBarrier>& GetBufferBarrier()
    {
        return mBufferBarriers;
    }

    bool IsVisited() const;
    UInt32 mIndex;

private:
    std::string mName;
    bool mNoCulling = false;
    CEFrameHashMap<REDTextureView*, REDResourceState> mTextureReferences;
    CEFrameHashMap<REDBuffer*, REDResourceState> mBufferReferences;
    CEFrameHashMap<REDTextureSubresource, REDSubresourceInfo> mTextures;
    CEFrameHashMap<REDBuffer*, REDSubresourceInfo> mBuffers;
    FrameStdVector<std::shared_ptr<REDPayload>> mPayloads;
    // render graph related
    // texture usage state in this pass, merged texture state
    FrameStdSet<REDPass*> mPredecessors;
    void AddPredecessor(REDPass* pass)
    {
        if (pass != this)
        {
            mPredecessors.emplace(pass);
        }
    }

    bool mVisited = false;

    FrameStdVector<NGITextureBarrier> mTextureBarriers;
    FrameStdVector<NGIBufferBarrier> mBufferBarriers;

    // render pass related
    REDRenderPass* mRenderPass = nullptr;

    // subpass related
    FrameStdVector<NGIRenderPassTargetIndex> mInputTargets;
    FrameStdVector<NGIRenderPassTargetIndex> mColorTargets;
    REDPassFlagBit mFlags{};
    UInt32 mSubpass = UINT32_MAX;

    // Common
    PropertySet mContext = PropertySet(nullptr);
    REDRegion* mRegion = nullptr;
};

}   // namespace cross