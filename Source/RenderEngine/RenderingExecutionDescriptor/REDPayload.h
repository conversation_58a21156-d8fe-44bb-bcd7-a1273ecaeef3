#pragma once
#include "RenderEngine/RenderEngineForward.h"
#include "CrossBase/String/NameID.h"
#include "RenderEngine/RenderContext.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RenderCamera.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/ComputeShaderR.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDCulling.h"
#include <variant>

namespace cross
{

struct REDPass;

struct REDPayload
{
    virtual ~REDPayload() = default;

    threading::TaskEventPtr mOnAssembleTask;

    // called when assembling render pipeline
    virtual void OnAssemble(REDPass* pass) {}

    // after pass culling and resource's first pass and last pass was calculated
    virtual void OnVisited(REDPass* pass) {}

    // after draw units was generated
    virtual void OnCulling(REDPass* pass) {}

    // after compilation, run parallel
    virtual void OnCompile(REDPass* pass) {}

    // after compilation, run parallel
    virtual void OnExecute(REDPass* pass, NGICommandList* cmdList) const {}
};

const void* GenerateShaderConstData(FrameAllocator* fa, MaterialR::MaterialRenderState& mtlState, const PropertySet* passCtx, const PropertySet* objectCtx = nullptr);

struct RENDER_ENGINE_API REDBundleCommandExecutionPayload : public REDPayload
{
    template<typename TFunc>
    REDBundleCommandExecutionPayload(TFunc&& func)
        : mCallback{ std::forward<TFunc>(func) }
    {}

private:
    std::function<void(REDPass*, NGIBundleCommandList*)> mCallback;

    void OnExecute(REDPass* pass, NGICommandList* cmdList) const override;
};

struct REDCommandExecutionPayload : public REDPayload
{
    template<typename TFunc>
    REDCommandExecutionPayload(TFunc&& func)
        : mCallback{ std::forward<TFunc>(func) }
    {}

private:
    std::function<void(REDPass*, NGICommandList*)> mCallback;

    void OnExecute(REDPass* pass, NGICommandList* cmdList) const override;
};

struct REDComputeExecutionPayload : public REDPayload
{
    REDComputeExecutionPayload(resource::ComputeProgramDesc* ProgramDesc, NGIComputePipelineState* PipelineState, UInt32 ThreadGroupCountX, UInt32 ThreadGroupCountY, UInt32 ThreadGroupCountZ)
        : mProgramDesc{ ProgramDesc }
        , mPipelineState{ PipelineState }
        , mThreadGroupCountX{ ThreadGroupCountX }
        , mThreadGroupCountY{ ThreadGroupCountY }
        , mThreadGroupCountZ{ ThreadGroupCountZ }
    {}

    REDComputeExecutionPayload(resource::ComputeProgramDesc* ProgramDesc, NGIComputePipelineState* PipelineState, REDBuffer* ArgumentBuffer, UInt32 ArgumentOffset)
        : mProgramDesc{ ProgramDesc }
        , mPipelineState{ PipelineState }
        , mArgumentBuffer{ ArgumentBuffer }
        , mArgumentOffset{ ArgumentOffset }
    {}

    void ModifyThreadGroupCount(UInt32 threadGroupCountX, UInt32 threadGroupCountY, UInt32 threadGroupCountZ);

private:
    resource::ComputeProgramDesc* mProgramDesc = nullptr;
    NGIComputePipelineState* mPipelineState = nullptr;
    UInt32 mThreadGroupCountX = 0;
    UInt32 mThreadGroupCountY = 0;
    UInt32 mThreadGroupCountZ = 0;
    REDBuffer* mArgumentBuffer = nullptr;
    UInt32 mArgumentOffset = 0;

    NGIResourceGroup* mResourceGroup = nullptr;
    
    void OnCompile(REDPass* pass) override;

    void OnExecute(REDPass* pass, NGICommandList* cmdList) const override;
};

struct REDRayTracingExecutionPayload : public REDPayload
{
    REDRayTracingExecutionPayload(resource::RayTracingProgramDesc* ProgramDesc, NGIRayTracingPipelineState* PipelineState, UInt32 Width, UInt32 Height, UInt32 Depth)
        : mProgramDesc(ProgramDesc), mPipelineState(PipelineState), mWidth(Width), mHeight(Height), mDepth(Depth) {}

    ~REDRayTracingExecutionPayload() override = default;

private:
    resource::RayTracingProgramDesc* mProgramDesc = nullptr;
    NGIRayTracingPipelineState* mPipelineState = nullptr;
    NGIResourceGroup* mResourceGroup = nullptr;
    UInt32 mWidth, mHeight, mDepth;

    void OnCompile(REDPass* pass) override;

    void OnExecute(REDPass* pass, NGICommandList* cmdList) const override;
};

struct REDDrawScreenQuad
{
    MaterialR* Material;
    NameID PassName;
    std::optional<NGIRasterizationStateDesc> RasterizationStateDesc;
    std::optional<NGIBlendStateDesc> BlendStateDesc;
    std::optional<NGIDepthStencilStateDesc> DepthStencilStateDesc;
    std::optional<NGIDynamicStateDesc> DynamicState;
    PropertySet mObjContext = PropertySet(nullptr);
};

struct REDDrawMesh : public REDDrawScreenQuad
{
    InputLayoutDesc* InputLayoutDesc;
    UInt32 VertexBufferCount;
    NGIBuffer* VertexBuffers[MaxVertexStreams];
    NGIBuffer* IndexBuffer;
    UInt32 VertexOrIndexCount;
    // FinalInstanceCount = max(1, InstanceCount)
    UInt32 InstanceCount;
};

struct REDDrawMeshPayload : public REDDrawMesh, public REDPayload
{
    REDDrawMeshPayload(const REDDrawMesh& drawMesh);

    REDDrawMeshPayload(const REDDrawScreenQuad& drawScreenQuad);

private:
    GeometryPacket* mGeometryPacket = nullptr;
    RenderGeometry* mGeometry = nullptr;
    MaterialR::MaterialRenderState mMaterialState;
    NGIGraphicsPipelineState* mPipelineState = nullptr;
    NGIResourceGroup* mPasGroup = nullptr;
    NGIResourceGroup* mMtlGroup = nullptr;
    NGIResourceGroup* mObjGroup = nullptr;

    void OnCompile(REDPass* pass) override;

    void OnExecute(REDPass* pass, NGICommandList* cmdList) const override;
};

struct REDRenderDrawUnitsDesc
{
    REDDrawUnitList* DrawUnitList;
    // override render state for all draw units
    std::optional<NGIRasterizationStateDesc> RasterizationStateDesc;
    std::optional<NGIBlendStateDesc> BlendStateDesc;
    std::optional<NGIDepthStencilStateDesc> DepthStencilStateDesc;
    std::optional<NGIDynamicStateDesc> DynamicStateDesc;
    std::optional<std::array<NGIViewport, 16>> InitViewport;
    std::optional<std::array<NGIScissor, 16>> InitScissor;
    // override decal specific render state, after above all state override
    bool OverrideDecalState;
};

struct REDImGuiPayload : public REDPayload
{
    REDImGuiPayload(REDTextureView* colorView)
        : mColorView(colorView)
    {}

private:
    void OnCompile(REDPass* pass) override;
    void OnExecute(REDPass* pass, NGICommandList* cmdList) const override;

    REDTextureView* mColorView;
};

}