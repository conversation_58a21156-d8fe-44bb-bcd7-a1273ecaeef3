#include "EnginePrefix.h"
#include "REDDrawUnitList.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDPass.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDPayload.h"
#include "RenderEngine/GPUScene/GPUScene.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"

cross::REDDrawUnitList::REDDrawUnitList(const REDCullingResult& cullingResult, const REDDrawUnitsDesc& desc, REDDrawUnitStatistic* statistic, FrameAllocator* FrameAlloc)
    : mCullingResult{cullingResult}
    , mDesc{desc}
    , mStatistic{statistic}
    , mFrameAlloc(FrameAlloc)
    , mBatchDrawUnits(FrameAlloc)
    , mDrawUnits(FrameAlloc)
    , mObjectPaylodDatas(FrameAlloc)
    , mObjectPayloadDatas2(FrameAlloc)
    , mObjectPayloadDrawUnitIndex(FrameAlloc)
    , mObjectIndexList(FrameAlloc)
{
    
}

cross::REDDrawUnitList::REDDrawUnitList(const REDCullingResult& cullingResult, const REDDrawUnitsDesc& desc, REDDrawUnitStatistic* statistic, std::pmr::vector<PrevFrame*>&& prevReadBack, FrameAllocator* FrameAlloc)
    : mCullingResult{cullingResult}
    , mDesc{desc}
    , mStatistic{statistic}
    , mReadBackFrames(prevReadBack)
    , mFrameAlloc(FrameAlloc)
    , mBatchDrawUnits(FrameAlloc)
    , mDrawUnits(FrameAlloc)
    , mObjectPaylodDatas(FrameAlloc)
    , mObjectPayloadDatas2(FrameAlloc)
    , mObjectPayloadDrawUnitIndex(FrameAlloc)
    , mObjectIndexList(FrameAlloc)
{
}

cross::REDDrawUnitList::~REDDrawUnitList()
{
    ReleaseFrameData();
}

bool cross::REDDrawUnitList::RecordDrawUnitMetaInfo(UInt64 id, UniqueDrawUnitKeyGenerator keyGenerator)
{
    if (mReadBackFrames.size() < CmdSettings::Inst().gMaxQueuedFrame)
        return false; 

    auto& select_frame = mReadBackFrames[id % mReadBackFrames.size()];

    select_frame->mDrawUnitUniqueID.resize(mDrawUnits.size(), 0);
    select_frame->mIndexMap.clear();

    for (UInt32 index = 0; index < mDrawUnits.GetSize(); index++)
    {

        select_frame->mDrawUnitUniqueID[index] = mDrawUnits[index].mStateBucketID;

        auto key = keyGenerator(mDrawUnits[index]);

        if (mDrawUnits[index].mType != RenderNodeType::Foliage)
        {
            // should not collide;
            //Assert(select_frame->mIndexMap[key].size() == 0);
        }

        select_frame->mIndexMap[key].push_back(index);
    }

    return true;
}

cross::REDDrawUnitList::PrevFrame* cross::REDDrawUnitList::GetReadBackBuffer(UInt64 id)
{
    if (mReadBackFrames.size() < CmdSettings::Inst().gMaxQueuedFrame)
    {
        mReadBackFrames.resize(CmdSettings::Inst().gMaxQueuedFrame + 1);
        for (auto & itr : mReadBackFrames)
        {
            itr = new PrevFrame();
        }
    }

    auto select_frame = mReadBackFrames[id % mReadBackFrames.size()];

    return select_frame;
}

void cross::REDDrawUnitList::ReleaseFrameData()
{
    mBatchDrawUnits.clear();
    mDrawUnits.clear();

    mObjectPaylodDatas.clear();
    mObjectPayloadDatas2.clear();

    mObjectPayloadDrawUnitIndex.clear();
    mObjectIndexList.clear();

    mTask = nullptr;
    mRelease = true;
    mVisited = false;
}

void cross::REDDrawUnitList::Dispatch()
{
    // generate drawUnits
    auto* cameraLODSel = mDesc.LODSelectionCamera ? mDesc.LODSelectionCamera : mCullingResult.mDesc.Camera;

    const auto entityBatchCount = static_cast<UInt32>(threading::TaskSystem::Get()->GetNumTaskThreads() * 2);

    // [batchIndex][drawUnitIndex]
    mBatchDrawUnits.resize(entityBatchCount);
    threading::TaskEventArray taskArray;

    for (UInt32 batchIndex = 0; batchIndex < entityBatchCount; ++batchIndex)
    {
        taskArray.Add(threading::Dispatch<threading::ThreadID::TaskThread>({mCullingResult.mTask}, threading::Priority::High, [=](auto) {
            SCOPED_CPU_TIMING(GroupRendering, "GenerateDrawUnitForEachBatch");
            mBatchDrawUnits[batchIndex] = std::pmr::vector<REDDrawUnit>{mFrameAlloc};

            const auto visibleEntityCount = mCullingResult.mEntityData->GetSize();
            const auto [entityStart, entityEnd] = Split2Batches(visibleEntityCount, entityBatchCount, batchIndex);

            mBatchDrawUnits[batchIndex].reserve(entityEnd - entityStart);

            REDDrawUnitCollector collector{mBatchDrawUnits[batchIndex]};

            for (auto entityIndex = entityStart; entityIndex < entityEnd; entityIndex++)
            {
                auto& entityData = mCullingResult.mEntityData->At(static_cast<UInt32>(entityIndex));

                RenderNode::GenerateDrawUnitsParams params{
                    mCullingResult.mDesc.World,
                    mCullingResult.mDesc.Camera,
                    cameraLODSel,
                    entityData,
                    mCullingResult.mDesc,
                    mDesc,
                };

                auto renderNode = entityData.renderNode;
                auto typeMatch = !mDesc.RenderNodeTypeMask || EnumHasAnyFlags(mDesc.RenderNodeTypeMask, renderNode->GetType());
                auto effectMatch = !mDesc.RenderEffectMask || EnumHasAnyFlags(mDesc.RenderEffectMask, renderNode->GetRenderEffect());
                if (renderNode->IsEnabled() && typeMatch && effectMatch)
                {
                    collector.SetCurrentEntityData(&entityData, renderNode);
                    renderNode->GenerateDrawUnits(params, collector);
                }
            }
        }));
    }

    mTask = threading::Dispatch<threading::ThreadID::TaskThread>(taskArray, threading::Priority::High, [=](auto) {
        SCOPED_CPU_TIMING(GroupRendering, "GenerateDrawUnit");

        // merge drawUnits
        {
            SCOPED_CPU_TIMING(GroupRendering, "MergeDrawUnits");

            auto drawUnitCount = std::accumulate(mBatchDrawUnits.begin(), mBatchDrawUnits.end(), size_t{}, [](auto acc, auto& batch) { return batch.size() + acc; });
            mDrawUnits.reserve(static_cast<UInt32>(drawUnitCount));

            for (auto& batch : mBatchDrawUnits)
            {
                mDrawUnits.insert(mDrawUnits.end(), batch.begin(), batch.end());
            }
        }

        // sort drawUnits
        {
            SCOPED_CPU_TIMING(GroupRendering, "SortDrawUnits");
            std::ranges::sort(mDrawUnits, [](auto& a, auto& b) { return a.mKey.Value < b.mKey.Value; });
        }

        AutoInstancing();

        mCullingResult.mCulling->mReadyDrawUnitLists.Push(this);
    });
}

void cross::REDDrawUnitList::AutoInstancing()
{
    SCOPED_CPU_TIMING(GroupRendering, "AutoInstancing");

    if (mDrawUnits.empty())
    {
        return;
    }

    auto drawUnitCount = mDrawUnits.size();

    auto& objectPaylodDatas = mObjectPaylodDatas;
    objectPaylodDatas.reserve(drawUnitCount);
    mObjectPayloadDatas2.reserve(drawUnitCount);
    mObjectPayloadDrawUnitIndex.reserve(drawUnitCount);
    auto& objectIndexList = mObjectIndexList;
    objectIndexList.reserve(drawUnitCount);

    SInt32 prevDrawUnitIndex = -1;
    UInt32 prevStateBucketID = 0;

    UInt32 singleInstancePayloadCount = 0, normalInstancePayloadCount = 0;

    for (UInt32 drawUnitIndex = 0; drawUnitIndex < drawUnitCount; drawUnitIndex++)
    {
        REDDrawUnit& drawUnit = mDrawUnits[drawUnitIndex];

        if (mStatistic)
        {
            mStatistic->PrimitiveCount += drawUnit.mGeometry->GetPrimitiveCount();
        }

        if (auto usingGPUScene = drawUnit.mTypedObjectDataBufferView && drawUnit.mCullingGUIDStart != -1; usingGPUScene)
        {
            if (EnumHasAnyFlags(drawUnit.mFlags, REDDrawUnitFlag::Isolated))
            {
                prevDrawUnitIndex++;

                if (prevDrawUnitIndex != static_cast<SInt32>(drawUnitIndex))
                {
                    mDrawUnits[prevDrawUnitIndex] = mDrawUnits[drawUnitIndex];
                }

                mDrawUnits[prevDrawUnitIndex].mObjectIndexOffset = static_cast<UInt32>(objectPaylodDatas.size());
                prevStateBucketID = 0;
            }
            else if (drawUnit.mStateBucketID == prevStateBucketID && mDrawUnits[prevDrawUnitIndex].MatchesForDynamicInstancing(mDesc.TagName, drawUnit))
            {
                mDrawUnits[prevDrawUnitIndex].mInstanceCount += drawUnit.mInstanceCount;
            }
            else
            {
                prevDrawUnitIndex++;

                if (prevDrawUnitIndex != static_cast<SInt32>(drawUnitIndex))
                {
                    mDrawUnits[prevDrawUnitIndex] = mDrawUnits[drawUnitIndex];
                }

                mDrawUnits[prevDrawUnitIndex].mObjectIndexOffset = static_cast<UInt32>(objectPaylodDatas.size());
                prevStateBucketID = drawUnit.mStateBucketID;
            }

            UInt32 indirectArgIndex = prevDrawUnitIndex;

            objectPaylodDatas.push_back(ObjectPayloadData{drawUnit.mCullingGUIDStart, indirectArgIndex, drawUnit.mObjectIndexStart});
            objectIndexList.push_back(drawUnit.mObjectIndexStart);

            if (drawUnit.mInstanceCount == 1)
            {
                singleInstancePayloadCount++;
            }
            else if (drawUnit.mInstanceCount < LARGE_INSTANCE_PAYLOAD_THRESHOLD)
            {
                normalInstancePayloadCount++;
            }

            mObjectPayloadDatas2.push_back(ObjectPayloadData2{
                drawUnit.mCullingGUIDStart,
                drawUnit.mObjectIndexStart,
                drawUnit.mInstanceCount,
                indirectArgIndex,
            });
            mObjectPayloadDrawUnitIndex.push_back(prevDrawUnitIndex);
        }
        else
        {
            prevDrawUnitIndex++;

            if (prevDrawUnitIndex != static_cast<SInt32>(drawUnitIndex))
            {
                mDrawUnits[prevDrawUnitIndex] = mDrawUnits[drawUnitIndex];
            }

            prevStateBucketID = 0;

            if (UInt32 instanceCount = mDrawUnits[prevDrawUnitIndex].mInstanceCount; instanceCount > 1 && mDrawUnits[prevDrawUnitIndex].mType != RenderNodeType::Foliage)
            {
                AssertMsg(false, "Should not run this code");

                // temp solution for custom instancing
                mDrawUnits[prevDrawUnitIndex].mObjectIndexOffset = static_cast<UInt32>(objectPaylodDatas.size());
                UInt32 indirectArgIndex = prevDrawUnitIndex;

                objectIndexList.reserve(objectIndexList.size() + instanceCount);
                objectPaylodDatas.reserve(objectPaylodDatas.size() + instanceCount);
                for (auto i = 0u; i < instanceCount; ++i)
                {
                    objectPaylodDatas.push_back(ObjectPayloadData{drawUnit.mCullingGUIDStart + static_cast<SInt32>(i), indirectArgIndex, drawUnit.mObjectIndexStart + static_cast<SInt32>(i)});
                    objectIndexList.push_back(mDrawUnits[prevDrawUnitIndex].mObjectIndexStart + static_cast<SInt32>(i));
                }

                mObjectPayloadDatas2.push_back(ObjectPayloadData2{
                    drawUnit.mCullingGUIDStart,
                    drawUnit.mObjectIndexStart,
                    1,
                    indirectArgIndex,
                });
                mObjectPayloadDrawUnitIndex.push_back(prevDrawUnitIndex);

                Assert(false);
            }
        }
    }

    mDrawUnits.resize(prevDrawUnitIndex + 1);

    // Reorder
    auto allocator = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameAllocator();
    std::pmr::vector<ObjectPayloadData2> localObjectPayloadDatas2(mObjectPayloadDatas2.size(), allocator);
    std::pmr::vector<UInt32> localObjectPayloadDrawUnitIndex(mObjectPayloadDatas2.size(), allocator);

    UInt32 singleInstancePayloadIndex = 0;
    UInt32 normalInstancePayloadIndex = singleInstancePayloadCount;
    UInt32 largeInstancePayloadIndex = singleInstancePayloadCount + normalInstancePayloadCount;

    mRangePayloadOffset = normalInstancePayloadIndex;
    mLargePayloadOffset = largeInstancePayloadIndex;

    for (int i = 0; i < mObjectPayloadDatas2.size(); i++)
    {
        const auto& payload = mObjectPayloadDatas2[i];

        if (payload.objectCount == 1)
        {
            localObjectPayloadDatas2[singleInstancePayloadIndex] = payload;
            localObjectPayloadDrawUnitIndex[singleInstancePayloadIndex] = mObjectPayloadDrawUnitIndex[i];
            singleInstancePayloadIndex++;
        }
        else if (payload.objectCount < LARGE_INSTANCE_PAYLOAD_THRESHOLD)
        {
            localObjectPayloadDatas2[normalInstancePayloadIndex] = payload;
            localObjectPayloadDrawUnitIndex[normalInstancePayloadIndex] = mObjectPayloadDrawUnitIndex[i];
            normalInstancePayloadIndex++;
        }
        else
        {
            localObjectPayloadDatas2[largeInstancePayloadIndex] = payload;
            localObjectPayloadDrawUnitIndex[largeInstancePayloadIndex] = mObjectPayloadDrawUnitIndex[i];
            largeInstancePayloadIndex++;
        }
    }

    mObjectPayloadDatas2 = std::move(localObjectPayloadDatas2);
    mObjectPayloadDrawUnitIndex = std::move(localObjectPayloadDrawUnitIndex);

    if (mStatistic)
    {
        mStatistic->DrawUnitCount = drawUnitCount;
        mStatistic->MergedDrawUnitCount = mDrawUnits.size();
    }
}

bool cross::REDDrawUnit::MatchesForDynamicInstancing(const NameID& passName, const REDDrawUnit& other)
{
    if (mGeometry == other.mGeometry && mMaterial == other.mMaterial && mFlags == other.mFlags)
    {
        if (auto* shader = mMaterial->GetShader(passName); shader)
        {
            auto IsSame = [&](auto& propNames) {
                for (auto& name : propNames)
                {
                    auto prop = mObjectProperties->GetNumericProperty(name);
                    auto otherProp = other.mObjectProperties->GetNumericProperty(name);
                    if (prop && otherProp)
                    {
                        bool equal = false;
                        std::visit(Overloaded{[&](const std::vector<UInt8>& val, const std::vector<UInt8>& otherVal) { equal = (val == otherVal); },
                                              [&](auto& val, auto& otherVal) {
                                                  using T1 = std::decay_t<decltype(val)>;
                                                  using T2 = std::decay_t<decltype(otherVal)>;
                                                  if constexpr (std::is_same_v<T1, T2>)
                                                  {
                                                      equal = (memcmp(&val, &otherVal, sizeof(T1)) == 0);
                                                  }
                                              }},
                                   *prop,
                                   *otherProp);
                        if (equal)
                        {
                            continue;
                        }
                        else
                        {
                            return false;
                        }
                    }
                    else if (!prop && !otherProp)
                    {
                        continue;
                    }
                    else
                    {
                        return false;
                    }
                }
                return true;
            };

            if (IsSame(shader->GetMacroIDVec()) && IsSame(shader->GetShaderConsts()))
            {
                auto& resProps = mObjectProperties->GetResourcePropertySet();
                auto& otherResProps = other.mObjectProperties->GetResourcePropertySet();
                for (auto itr = resProps.cbegin(), otherItr = otherResProps.cbegin(); itr != resProps.end() && otherItr != otherResProps.end(); ++itr, ++otherItr)
                {
                    if (itr->first == otherItr->first && itr->second.mData.size() == otherItr->second.mData.size() &&
                        std::equal(itr->second.mData.begin(), itr->second.mData.end(), otherItr->second.mData.begin(), [](auto& a, auto& b) { return a.Value == b.Value && a.Flags == b.Flags; }))
                    {
                        continue;
                    }
                    return false;
                }
            }

            return true;
        }
    }

    return false;
}

std::tuple<SizeType, SizeType> cross::Split2Batches(SizeType count, SizeType batchCount, SizeType batchIndex)
{
    auto batchSize = count / batchCount;
    auto remained = count % batchCount;
    auto start = batchIndex < remained ? batchIndex * (batchSize + 1) : batchIndex * batchSize + remained;
    auto end = start + (batchIndex < remained ? batchSize + 1 : batchSize);
    return {start, end};
}