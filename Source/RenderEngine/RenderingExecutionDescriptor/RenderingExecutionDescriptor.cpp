#include "EnginePrefix.h"
#include "RenderingExecutionDescriptor.h"
#include "CrossBase/Template/EnumClassFlags.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "NativeGraphicsInterface/NGIUtils.h"
#include "NativeGraphicsInterface/NGITransientResourceManager.h"
#include "RenderEngine/RenderContext.h"

#include <filesystem>

/*
 * Note:
 * State Transition
 * Outer Pass
 *	if it's a renderpass
 *		transit to state in begin pass
 *		transit to each state in subpass
 *		transit to state in end pass (state in end pass was state in outer pass)
 *	transite to next outer pass
 *
 * Resources:
 * Normal resource translate to first used state in first used pass from before frame state
 * Extended resource from last frame act just like normal resource, they just don't need to allocate native resource in this frame
 * External resource imported/exported by prologue pass/epilogue pass
 */

namespace cross {
cross::REDAllocator::REDAllocator(UInt32 frameLatency)
{
    auto gcqueueSize = CmdSettings::Inst().gMaxQueuedFrame * 2;
    mGarbageQueue.resize(gcqueueSize + 1);
    AllocateBlock();
}

cross::REDAllocator::~REDAllocator()
{
    VIRTUAL_FREE(reinterpret_cast<void*>(mCurrentBlock.Begin), 0);
    for (auto& itr : mHistoryPages)
    {
        VIRTUAL_FREE(reinterpret_cast<void*>(itr.second.Begin), 0);
    }
    for (const auto& garbage : mGarbageQueue)
    {
        for (void* block : garbage)
        {
            VIRTUAL_FREE(block, 0);
        }
    }
}

void cross::REDAllocator::Tick(UInt64 frameID)
{
    mCurrentFrameIndex = static_cast<UInt32>(frameID % mGarbageQueue.size());
    for (void* block : mGarbageQueue[mCurrentFrameIndex])
    {
        VIRTUAL_FREE(block, 0);
    }
    mGarbageQueue[mCurrentFrameIndex].clear();
}

std::size_t cross::REDAllocator::Allocate(std::size_t size, std::size_t alignment)
{
    // size of REDTexture/REDBuffer should be far less than memory page size
    Assert(size <= gMemoryPageSize);

    if (auto alignedAddress = AlignUp(mCurrentBlockPtr, alignment); alignedAddress + size <= mCurrentBlock.End)
    {
        // there was enough space in current block
        auto localAddress = alignedAddress - mCurrentBlock.Begin;
        auto pageIdxBegin = localAddress / gMemoryPageSize;
        auto pageIdxEnd = (localAddress + size - 1) / gMemoryPageSize + 1;
        Assert(pageIdxEnd <= ArrayCount(mCurrentBlock.PageRefCounts));
        for (auto i = pageIdxBegin; i < pageIdxEnd; ++i)
        {
            mCurrentBlock.PageRefCounts[i]++;
        }
        mCurrentBlock.RefCount++;
        mCurrentBlockPtr = alignedAddress + size;
        return alignedAddress;
    }
    else
    {
        // no enough space, archive current block and allocate a new one
        for (UInt32 i = 0; i < gMemoryPageCountPerBlock; ++i)
        {
            if (mCurrentBlock.PageRefCounts[i] == 0)
            {
                auto pageBegin = mCurrentBlock.Begin + i * gMemoryPageSize;
                VIRTUAL_DISABLE(reinterpret_cast<void*>(pageBegin), gMemoryPageSize);
            }
        }
        if (mCurrentBlock.RefCount > 0)
        {
            mHistoryPages.emplace(mCurrentBlock.End, mCurrentBlock);
        }
        else
        {
            mGarbageQueue[mCurrentFrameIndex].push_back(reinterpret_cast<void*>(mCurrentBlock.Begin));
        }

        AllocateBlock();
        return Allocate(size, alignment);
    }
}

void cross::REDAllocator::Deallocate(std::size_t address, std::size_t size) noexcept
{
    if (mCurrentBlock.Begin <= address && address < mCurrentBlock.End)
    {
        auto localAddress = address - mCurrentBlock.Begin;
        auto pageIdxBegin = localAddress / gMemoryPageSize;
        auto pageIdxEnd = (localAddress + size - 1) / gMemoryPageSize + 1;
        for (auto i = pageIdxBegin; i < pageIdxEnd; ++i)
        {
            Assert(mCurrentBlock.PageRefCounts[i]);
            mCurrentBlock.PageRefCounts[i]--;
        }
        Assert(mCurrentBlock.RefCount);
        mCurrentBlock.RefCount--;
    }
    else
    {
        auto itr = mHistoryPages.lower_bound(address + size);
        if (auto& block = itr->second; itr != mHistoryPages.end() && block.Begin <= address)
        {
            auto localAddress = address - block.Begin;
            auto pageIdxBegin = localAddress / gMemoryPageSize;
            auto pageIdxEnd = (localAddress + size - 1) / gMemoryPageSize + 1;
            Assert(pageIdxEnd <= ArrayCount(mCurrentBlock.PageRefCounts));
            for (auto i = pageIdxBegin; i < pageIdxEnd; ++i)
            {
                Assert(block.PageRefCounts[i]);
                if (--block.PageRefCounts[i] == 0)
                {
                    VIRTUAL_DISABLE(reinterpret_cast<void*>(block.Begin + i * gMemoryPageSize), gMemoryPageSize);
                }
            }

            Assert(block.RefCount);
            if (--block.RefCount == 0)
            {
                mGarbageQueue[mCurrentFrameIndex].push_back(reinterpret_cast<void*>(block.Begin));
                mHistoryPages.erase(itr);
            }
        }
        else
        {
            Assert(false);
        }
    }
}

void cross::REDAllocator::AllocateBlock()
{
    mCurrentBlockPtr = reinterpret_cast<std::size_t>(VIRTUAL_ALLOC(gMemoryBlockSize, 0));
    mCurrentBlock = {mCurrentBlockPtr, mCurrentBlockPtr + gMemoryBlockSize};
}
cross::RenderingExecutionDescriptor::RenderingExecutionDescriptor(NGIResourceManager* transientResMgr, PersistentResourceManager* persistentResMgr, NGITransientBufferManager* stagingMgr)
    : mTransientResMgr{transientResMgr}
    , mPersistentResMgr{persistentResMgr}
    , mStagingMgr{stagingMgr}
{}

cross::REDRenderPass* cross::RenderingExecutionDescriptor::BeginRenderPass(std::string_view ID, UInt32 numColorTargets, const REDColorTargetDesc* colorTargetDescs, const REDDepthStencilTargetDesc* depthStencilDesc, bool noCulling)
{
    AssertMsg(!mCurrentRenderPass, "Last render pass not ended");

    auto& renderPass = *AllocateRenderPassInfo();
    renderPass.mName = ID;

    renderPass.mColorTargetDescs.insert(renderPass.mColorTargetDescs.end(), colorTargetDescs, colorTargetDescs + numColorTargets);
    if (depthStencilDesc)
    {
        renderPass.mDepthStencilTargetDesc.emplace(*depthStencilDesc);
    }
    auto currentPass = &renderPass;

    auto* outerPass = AllocatePass(ID, noCulling);
    outerPass->mRenderPass = currentPass;

    auto* beginPass = AllocateSubPass("Begin");
    beginPass->mRenderPass = currentPass;
    currentPass->mBeginPass = beginPass;
    currentPass->mSubpasses.emplace_back(beginPass);

    SetCurrentRenderPass(currentPass);

    renderPass.mOuterPass = outerPass;
    return &renderPass;
}

cross::REDRenderPass* cross::RenderingExecutionDescriptor::BeginRenderPass(std::string_view ID, UInt32 width, UInt32 height, UInt32 layerCount, UInt32 sampleCount, bool noCulling)
{
    AssertMsg(!mCurrentRenderPass, "Last render pass not ended");

    auto& renderPass = *AllocateRenderPassInfo();
    renderPass.mName = ID;
    renderPass.mWidth = width;
    renderPass.mHeight = height;
    renderPass.mLayerCount = layerCount;
    renderPass.mSampleCount = sampleCount;

    auto currentPass = &renderPass;

    auto* outerPass = AllocatePass(ID, noCulling);
    outerPass->mRenderPass = currentPass;

    auto* beginPass = AllocateSubPass("Begin");
    beginPass->mRenderPass = currentPass;
    currentPass->mBeginPass = beginPass;
    currentPass->mSubpasses.emplace_back(beginPass);

    SetCurrentRenderPass(currentPass);

    renderPass.mOuterPass = outerPass;
    return &renderPass;
}

cross::REDPass* cross::RenderingExecutionDescriptor::AllocateSubRenderPass(std::string_view name, UInt32 inputTargetCount, NGIRenderPassTargetIndex* inputTargetIndices, UInt32 colorTargetCount, NGIRenderPassTargetIndex* colorTargetIndices,
                                                                           REDPassFlagBit flags)
{
    auto currentRenderPass = GetCurrentRenderPass();
    auto* pass = AllocateSubPass(name);
    pass->mInputTargets.insert(pass->mInputTargets.end(), inputTargetIndices, inputTargetIndices + inputTargetCount);
    pass->mColorTargets.insert(pass->mColorTargets.end(), colorTargetIndices, colorTargetIndices + colorTargetCount);
    pass->mFlags |= flags;
    pass->mRenderPass = currentRenderPass;
    currentRenderPass->mSubpasses.emplace_back(pass);
    return pass;
}

void cross::RenderingExecutionDescriptor::EndRenderPass()
{
    auto CurrentRenderPass = GetCurrentRenderPass();
    auto* outerPass = CurrentRenderPass->mOuterPass;
    auto* beginPass = CurrentRenderPass->mBeginPass;

    auto* endPass = AllocateSubPass("End");
    endPass->mRenderPass = CurrentRenderPass;

    CurrentRenderPass->mEndPass = endPass;
    auto& subpasses = CurrentRenderPass->mSubpasses;
    subpasses.emplace_back(endPass);

    auto needResolve = false;

    // compile current render pass

    REDResourceState resolveDstOuterState{
        NGIResourceState::TargetResolveDst,
        false,
        true,
    };

    REDResourceState resolveDstBeginState{
        NGIResourceState::TargetResolveDst,
        false,
        true,
    };

    REDResourceState resolveDstEndState{
        NGIResourceState::TargetResolveDst,
        false,
        true,
    };

    for (auto& targetDesc : CurrentRenderPass->mColorTargetDescs)
    {
        auto resolveColor = EnumHasAnyFlags(targetDesc.StoreOp, NGIStoreOp::Resolve);
        auto storeColor = EnumHasAnyFlags(targetDesc.StoreOp, NGIStoreOp::Store);
        auto* texture = targetDesc.Target->mTexture;
        targetDesc.Target->EnumerateSubresource([&](UInt32 index, UInt32 mip, UInt32 array, UInt32 plane) {
            REDTextureSubresource subres{texture, index};
            CurrentRenderPass->mTargets.emplace(subres, nullptr);

            outerPass->mTextures[subres].State = {
                NGIResourceState::Undefined,
                targetDesc.LoadOp == NGILoadOp::Load,
                storeColor,
            };

            beginPass->mTextures[subres].State = {
                NGIResourceState::TargetReadWrite,
                false,
                true,
            };

            if (resolveColor || storeColor)
            {
                endPass->mTextures[subres].State = {
                    resolveColor ? NGIResourceState::TargetResolveSrc : NGIResourceState::Undefined,
                    true,
                    false,
                };
            }
        });

        if (resolveColor)
        {
            needResolve = true;
            auto* resolveTexture = targetDesc.ResolveTarget->mTexture;
            targetDesc.ResolveTarget->EnumerateSubresource([&](UInt32 index, UInt32 mip, UInt32 array, UInt32 plane) {
                REDTextureSubresource subres{resolveTexture, index};
                CurrentRenderPass->mTargets.emplace(subres, nullptr);

                outerPass->mTextures[subres].State |= resolveDstOuterState;
                beginPass->mTextures[subres].State |= resolveDstBeginState;
                endPass->mTextures[subres].State |= resolveDstEndState;
            });
        }
    }

    if (CurrentRenderPass->mDepthStencilTargetDesc)
    {
        auto& targetDesc = *(CurrentRenderPass->mDepthStencilTargetDesc);
        auto resolveDepth = EnumHasAnyFlags(targetDesc.DepthStoreOp, NGIStoreOp::Resolve);
        auto storeDepth = EnumHasAnyFlags(targetDesc.DepthStoreOp, NGIStoreOp::Store);
        auto resolveStencil = EnumHasAnyFlags(targetDesc.StencilStoreOp, NGIStoreOp::Resolve);
        auto storeStencil = EnumHasAnyFlags(targetDesc.StencilStoreOp, NGIStoreOp::Store);

        auto* texture = targetDesc.Target->mTexture;
        auto [texHasDepth, texHasStencil] = FormatHasDepthStencil(texture->mDesc.Format);
        targetDesc.Target->EnumerateSubresource([&, texHasDepth = texHasDepth](UInt32 index, UInt32 mip, UInt32 array, UInt32 plane) {
            if (plane == 0)
            {
                REDTextureSubresource subres{texture, index};
                CurrentRenderPass->mTargets.emplace(subres, nullptr);

                outerPass->mTextures[subres].State = {
                    NGIResourceState::Undefined,
                    targetDesc.DepthLoadOp == NGILoadOp::Load,
                    storeDepth,
                };

                beginPass->mTextures[subres].State = {
                    NGIResourceState::TargetReadWrite,
                    false,
                    true,
                };

                if (resolveDepth || storeDepth)
                {
                    endPass->mTextures[subres].State = {
                        resolveDepth ? NGIResourceState::TargetResolveSrc : NGIResourceState::Undefined,
                        true,
                        false,
                    };
                }
            }
            if ((texHasDepth && plane == 1) || (!texHasDepth && plane == 0))
            {
                REDTextureSubresource subres{texture, index};
                CurrentRenderPass->mTargets.emplace(subres, nullptr);

                outerPass->mTextures[subres].State = {
                    NGIResourceState::Undefined,
                    targetDesc.StencilLoadOp == NGILoadOp::Load,
                    storeDepth,
                };

                beginPass->mTextures[subres].State = {
                    NGIResourceState::TargetReadWrite,
                    false,
                    true,
                };

                if (resolveStencil || storeStencil)
                {
                    endPass->mTextures[subres].State = {
                        resolveStencil ? NGIResourceState::TargetResolveSrc : NGIResourceState::Undefined,
                        true,
                        false,
                    };
                }
            }
        });

        if (resolveDepth || resolveStencil)
        {
            needResolve = true;
            Assert(targetDesc.ResolveTarget);
            auto* resolveTexture = targetDesc.ResolveTarget->mTexture;
            auto [resolveTexHasDepth, resolveTexHasStencil] = FormatHasDepthStencil(resolveTexture->mDesc.Format);
            targetDesc.ResolveTarget->EnumerateSubresource([&, resolveTexHasDepth = resolveTexHasDepth](UInt32 index, UInt32 mip, UInt32 array, UInt32 plane) {
                if (plane == 0)
                {
                    REDTextureSubresource subres{resolveTexture, index};
                    CurrentRenderPass->mTargets.emplace(subres, nullptr);
                    if (resolveDepth)
                    {
                        outerPass->mTextures[subres].State |= resolveDstOuterState;
                        beginPass->mTextures[subres].State |= resolveDstBeginState;
                        endPass->mTextures[subres].State |= resolveDstEndState;
                    }
                    else
                    {
                        outerPass->mTextures[subres].State |= REDResourceState{NGIResourceState::TargetReadWrite, false, true};
                        beginPass->mTextures[subres].State |= REDResourceState{NGIResourceState::TargetReadWrite, false, true};
                    }
                }
                if ((resolveTexHasDepth && plane == 1) || (!resolveTexHasDepth && plane == 0))
                {
                    REDTextureSubresource subres{resolveTexture, index};
                    CurrentRenderPass->mTargets.emplace(subres, nullptr);
                    if (resolveStencil)
                    {
                        outerPass->mTextures[subres].State |= resolveDstOuterState;
                        beginPass->mTextures[subres].State |= resolveDstBeginState;
                        endPass->mTextures[subres].State |= resolveDstEndState;
                    }
                    else
                    {
                        outerPass->mTextures[subres].State |= REDResourceState{NGIResourceState::TargetReadWrite, false, true};
                        beginPass->mTextures[subres].State |= REDResourceState{NGIResourceState::TargetReadWrite, false, true};
                    }
                }
            });
        }
    }

    // translate input, color and depth desc to resource state
    for (auto* subpass : subpasses)
    {
        if (subpass != beginPass && subpass != endPass)
        {
            // handle input targets
            for (auto inputTargetIndex : subpass->mInputTargets)
            {
                if (inputTargetIndex == NGIRenderPassTargetIndex::TargetDepth || inputTargetIndex == NGIRenderPassTargetIndex::TargetStencil)
                {
                    Assert(CurrentRenderPass->mDepthStencilTargetDesc);
                    const auto& targetDesc = *(CurrentRenderPass->mDepthStencilTargetDesc);
                    auto hasDepth = FormatHasDepth(targetDesc.Target->mDesc.Format);
                    auto hasStencil = FormatHasStencil(targetDesc.Target->mDesc.Format);
                    if (inputTargetIndex == NGIRenderPassTargetIndex::TargetDepth)
                    {
                        Assert(hasDepth);
                        auto* texture = targetDesc.Target->mTexture;
                        targetDesc.Target->EnumerateSubresource([&](UInt32 index, UInt32 mip, UInt32 array, UInt32 plane) {
                            if (plane == 0)
                            {
                                REDResourceState state{
                                    NGIResourceState::SubpassRead,
                                    true,
                                    false,
                                };
                                subpass->mTextures[{texture, index}].State |= state;
                            }
                        });
                    }
                    else
                    {
                        Assert(hasStencil);
                        UInt32 targetPlane = hasDepth ? 1 : 0;

                        auto* texture = targetDesc.Target->mTexture;
                        targetDesc.Target->EnumerateSubresource([&](UInt32 index, UInt32 mip, UInt32 array, UInt32 plane) {
                            if (plane == targetPlane)
                            {
                                REDResourceState state{
                                    NGIResourceState::SubpassRead,
                                    true,
                                    false,
                                };
                                subpass->mTextures[{texture, index}].State |= state;
                            }
                        });
                    }
                }
                else
                {
                    const auto& targetDesc = CurrentRenderPass->mColorTargetDescs[ToUnderlying(inputTargetIndex)];
                    auto* texture = targetDesc.Target->mTexture;
                    targetDesc.Target->EnumerateSubresource([&](UInt32 index, UInt32 mip, UInt32 array, UInt32 plane) {
                        REDResourceState state{
                            NGIResourceState::SubpassRead,
                            true,
                            false,
                        };
                        subpass->mTextures[{texture, index}].State |= state;
                    });
                }
            }

            // handle color targets
            for (auto colorTargetIndex : subpass->mColorTargets)
            {
                auto& targetDesc = CurrentRenderPass->mColorTargetDescs[ToUnderlying(colorTargetIndex)];
                Assert(targetDesc.Target);
                auto* texture = targetDesc.Target->mTexture;

                targetDesc.Target->EnumerateSubresource([&](UInt32 index, UInt32 mip, UInt32 array, UInt32 plane) {
                    REDTextureSubresource key{texture, index};

                    REDResourceState state{
                        NGIResourceState::TargetReadWrite,
                        true,
                        true,
                    };
                    subpass->mTextures[{texture, index}].State |= state;
                });
            }

            // handle depth stencil target
            if (EnumHasAnyFlags(subpass->mFlags, REDPassFlagBit::NeedDepth | REDPassFlagBit::NeedStencil))
            {
                Assert(CurrentRenderPass->mDepthStencilTargetDesc);
                auto& targetDesc = *(CurrentRenderPass->mDepthStencilTargetDesc);
                auto& texViewDesc = targetDesc.Target->mDesc;
                auto* texture = targetDesc.Target->mTexture;

                auto needDepth = EnumHasAnyFlags(subpass->mFlags, REDPassFlagBit::NeedDepth);
                auto depthReadOnly = EnumHasAnyFlags(subpass->mFlags, REDPassFlagBit::DepthReadOnly);
                if (needDepth)
                {
                    Assert(EnumHasAnyFlags(texViewDesc.SubRange.Aspect, NGITextureAspect::Depth));
                    if (std::find(subpass->mInputTargets.begin(), subpass->mInputTargets.end(), NGIRenderPassTargetIndex::TargetDepth) != subpass->mInputTargets.end())
                    {
                        Assert(depthReadOnly);
                    }
                }

                auto needStencil = EnumHasAnyFlags(subpass->mFlags, REDPassFlagBit::NeedStencil);
                auto stencilReadOnly = EnumHasAnyFlags(subpass->mFlags, REDPassFlagBit::StencilReadOnly);
                if (needStencil)
                {
                    Assert(EnumHasAnyFlags(texViewDesc.SubRange.Aspect, NGITextureAspect::Stencil));
                    if (std::find(subpass->mInputTargets.begin(), subpass->mInputTargets.end(), NGIRenderPassTargetIndex::TargetStencil) != subpass->mInputTargets.end())
                    {
                        Assert(stencilReadOnly);
                    }
                }

                auto texHasDepth = FormatHasDepth(targetDesc.Target->mTexture->mDesc.Format);

                targetDesc.Target->EnumerateSubresource([&](UInt32 index, UInt32 mip, UInt32 array, UInt32 plane) {
                    if (needDepth && plane == 0)
                    {
                        REDResourceState state{
                            depthReadOnly ? NGIResourceState::TargetRead : NGIResourceState::TargetReadWrite,
                            true,
                            !depthReadOnly,
                        };
                        subpass->mTextures[{texture, index}].State |= state;
                    }
                    if (needStencil && ((texHasDepth && plane == 1) || ((!texHasDepth && plane == 0))))
                    {
                        REDResourceState state{
                            stencilReadOnly ? NGIResourceState::TargetRead : NGIResourceState::TargetReadWrite,
                            true,
                            !stencilReadOnly,
                        };
                        subpass->mTextures[{texture, index}].State |= state;
                    }
                });
            }
        }
    }

    // compile render pass graph

    // construct pass dependency from resource
    //for (auto& texture : mTextures)
    //{
    //    for (auto& subres : texture.mSubresources)
    //    {
    //        subres.LastWriter = nullptr;
    //        subres.MergedState = nullptr;
    //    }
    //}

    for (auto& [subres, info] : beginPass->mTextures)
    {
        auto* initialMergedState = AllocateResourceRangeState(info.State, beginPass, beginPass);
        subres.Texture->mSubresources[subres.Subresource].LastWriter = beginPass;
        subres.Texture->mSubresources[subres.Subresource].MergedState = initialMergedState;
        info.MergedState = initialMergedState;
        CurrentRenderPass->mTargets[subres] = info.MergedState;
    }

    for (auto* pass : CurrentRenderPass->mSubpasses)
    {
        auto writeNonTargetRes = false;
        for (auto& [subres, info] : pass->mTextures)
        {
            auto& subresource = subres.Texture->mSubresources[subres.Subresource];
            if (subresource.LastWriter)
            {
                pass->AddPredecessor(subresource.LastWriter);
            }
            if (info.State.Write)
            {
                subresource.LastWriter = pass;
            }
        }

        for (auto& [tex, state] : pass->mTextureReferences)
        {
            if (state.Write)
            {
                writeNonTargetRes = true;
            }
        }

        for (auto& [buffer, state] : pass->mBufferReferences)
        {
            if (state.Write)
            {
                writeNonTargetRes = true;
            }
        }

        if (writeNonTargetRes)
        {
            endPass->AddPredecessor(pass);
        }

        // every pass should have a predecessor, except the prologue pass
        if (pass->mPredecessors.empty())
        {
            pass->AddPredecessor(beginPass);
        }
    }

    MarkVisitedPass(endPass);

    for (auto* subpass : CurrentRenderPass->mSubpasses)
    {
        if (subpass->mVisited && subpass != beginPass)
        {
            for (auto& [subres, info] : subpass->mTextures)
            {
                auto*& resMergedState = subres.Texture->mSubresources[subres.Subresource].MergedState;
                if (resMergedState->IsTextureMergeAllowed(info.State))
                {
                    (*resMergedState) |= info.State;
                    resMergedState->mLastPass = subpass;
                }
                else
                {
                    resMergedState = AllocateResourceRangeState(info.State, subpass, subpass);
                }
                info.MergedState = resMergedState;
                if (info.MergedState->NGIState != NGIResourceState::Undefined)
                {
                    CurrentRenderPass->mTargets[subres] = info.MergedState;
                }
            }
        }
    }

    for (auto& [subres, info] : outerPass->mTextures)
    {
        Assert(CurrentRenderPass->mTargets.count(subres));
        info.State.NGIState = CurrentRenderPass->mTargets[subres]->NGIState;
    }

    if (auto count = std::count_if(CurrentRenderPass->mSubpasses.begin(), CurrentRenderPass->mSubpasses.end(), [](auto* subpass) { return subpass->mVisited; }); count == 2)
    {
        Assert(count - 2 <= MaxSupportedSubpasses);
        if (needResolve)
        {
            LOG_INFO("Render pass: {} has no subpass but need resolve!", CurrentRenderPass->mName);
        }
        else
        {
            LOG_INFO("Render pass: {} has no subpass and doesn't need resolve, should be culled!", CurrentRenderPass->mName);
        }
    }

    CurrentRenderPass->AllocateNativeRenderPasss(mPersistentResMgr);

    for (auto* pass : mCurrentRenderPass->mSubpasses)
    {
        for (auto& payload : pass->mPayloads)
        {
            payload->OnAssemble(pass);
        }
    }

    SetCurrentRenderPass(nullptr);
}

cross::REDPass* cross::RenderingExecutionDescriptor::AllocatePass(std::string_view name, bool noCulling)
{
    auto& pass = mPassPool->emplace_back(name, noCulling, GetCurrentRegion());
    mPasses.emplace_back(&pass);
    return &pass;
}

cross::REDCullingResult* cross::RenderingExecutionDescriptor::Cull(const REDCullingDesc& desc)
{
    return mCulling.Dispatch(REDCullingResultDesc{desc}, mFrameID);
}

cross::REDCullingResult* cross::RenderingExecutionDescriptor::CullShadowCaster(const REDCullingDesc& desc)
{
    return mCulling.Dispatch(REDCullingResultDesc{desc, true}, mFrameID);
}

cross::LightCullingResult* cross::RenderingExecutionDescriptor::CullVisibleLight(RenderWorld* world, RenderCamera* camera)
{
    return mCulling.DispatchLightCulling(LightCullingDesc{world, camera}, mFrameID);
}

cross::REDCullingResult* cross::RenderingExecutionDescriptor::CullLocalLightShadowCaster(RenderWorld* world, RenderCamera* camera, BoundingBox* boundingBox, REDObjectType objectMask)
{
    return mCulling.Dispatch(REDCullingResultDesc{REDCullingDesc{world, camera, objectMask}, boundingBox, true}, mFrameID);
}

cross::REDRegion* cross::RenderingExecutionDescriptor::BeginRegion(std::string_view name)
{
    auto currentRegion = GetCurrentRegion();
    auto newRegion = &mRegionPool.emplace_back(REDRegion{currentRegion->Depth + 1, currentRegion, name});
    SetCurrentRegion(newRegion);
    return newRegion;
}

void cross::RenderingExecutionDescriptor::EndRegion()
{
    SetCurrentRegion(GetCurrentRegion()->Parent);
    AssertMsg(GetCurrentRegion(), "Debug region not matching, more end region");
}

cross::REDPass* cross::RenderingExecutionDescriptor::AllocateSubPass(std::string_view name)
{
    auto& pass = mPassPool->emplace_back(name, false, GetCurrentRegion());
    return &pass;
}

cross::REDPass* cross::RenderingExecutionDescriptor::AllocateFlushStatePass()
{
    //auto flushStatePass = mPasses.back();
    //if (flushStatePass->GetName() != gFlushStatePassName)
    //{
    auto flushStatePass = RenderingExecutionDescriptor::AllocatePass(gFlushStatePassName);
    mFlushStatePasses.emplace_back(flushStatePass);
    //}
    return flushStatePass;
}

// allocate a RED texture from scratch
cross::REDTexture* cross::RenderingExecutionDescriptor::AllocateTexture(std::string_view name, const NGITextureDesc& desc)
{
    Assert(desc.Width > 0 && desc.Height > 0 && desc.Depth > 0 && desc.ArraySize > 0 && desc.MipCount > 0 && desc.SampleCount > 0);
    return &mTextures.emplace_back(name, desc);
}

// allocate a RED texture with external NGI texture
cross::REDTexture* cross::RenderingExecutionDescriptor::AllocateTexture(std::string_view name, NGITexture* externalTexture)
{
    if (auto* ret = RenderingExecutionDescriptor::FindExternalTexture(externalTexture); ret)
    {
#ifndef CROSSENGINE_RELEASE
        // LOG_INFO("RETexture:{} with external texture:{} was already created with name:{}", name, fmt::ptr(externalTexture), ret->mName);
#endif
        return ret;
    }

    return &mTextures.emplace_back(name, externalTexture);
}

cross::REDTexture* cross::RenderingExecutionDescriptor::AllocateTexture(std::string_view name, NGITexture* externalTexture, NGIResourceState externalState)
{
    auto* ret = AllocateTexture(name, externalTexture);
    for (auto& subres : ret->mSubresources)
    {
        subres.BeforeFrameState = externalState;
    }
    return ret;
}

cross::REDTexture* cross::RenderingExecutionDescriptor::AllocateTexture(std::string_view name, NGITexture* externalTexture, NGIResourceState* externalState)
{
    auto* ret = AllocateTexture(name, externalTexture);
    for (auto& subres : ret->mSubresources)
    {
        subres.BeforeFrameState = *externalState;
        ++externalState;
    }
    return ret;
}

cross::REDTexture* cross::RenderingExecutionDescriptor::FindExternalTexture(NGITexture* externalTexture)
{
    auto ret = std::find_if(mTextures.begin(), mTextures.end(), [&](const REDTexture& tex) { return tex.mType == REDResourceType::External && tex.mNativeTexture == externalTexture; });
    return ret != mTextures.end() ? &*ret : nullptr;
}

cross::REDTextureView* cross::RenderingExecutionDescriptor::AllocateTextureView(REDTexture* texture, const NGITextureViewDesc& desc)
{
    static const std::map<NGITextureType, std::set<NGITextureType>> gCompatibleTextureTypes{
        {NGITextureType::Texture3D, {NGITextureType::Texture2D, NGITextureType::Texture2DArray}},
        {NGITextureType::TextureCubeArray, {NGITextureType::Texture2D, NGITextureType::Texture2DArray, NGITextureType::TextureCube}},
        {NGITextureType::TextureCube, {NGITextureType::Texture2D, NGITextureType::Texture2DArray}},
        {NGITextureType::Texture2DArray, {NGITextureType::Texture2D}},
    };

    // check view desc
    auto& texDesc = texture->mDesc;
    Assert(EnumHasAllFlags(texDesc.Usage, desc.Usage));
    if (texDesc.Dimension != desc.ViewDimension)
    {
        if (texDesc.SeparateView)
        {
            if (auto compatibleTypes = gCompatibleTextureTypes.find(texDesc.Dimension); compatibleTypes != gCompatibleTextureTypes.cend())
            {
                AssertMsg(compatibleTypes->second.count(desc.ViewDimension), "Texture view type must be a compatible type");
            }
            else
            {
                AssertMsg(false, "Texture type can't have seperate views");
            }
        }
        else
        {
            AssertMsg(false, "Texture type must be same with view type if NGITextureDesc::SeperateView was not set");
        }
    }
    Assert(desc.SubRange.FirstArraySlice < texDesc.ArraySize);
    Assert(desc.SubRange.FirstArraySlice + desc.SubRange.ArraySize <= texDesc.ArraySize);
    Assert(desc.SubRange.MostDetailedMip < texDesc.MipCount);
    Assert(desc.SubRange.MostDetailedMip + desc.SubRange.MipLevels <= texDesc.MipCount);

    if (auto [hasDepth, hasStencil] = FormatHasDepthStencil(texDesc.Format); hasDepth || hasStencil)
    {
        if (!hasDepth)
        {
            Assert(!EnumHasAnyFlags(desc.SubRange.Aspect, NGITextureAspect::Depth));
        }
        if (!hasStencil)
        {
            Assert(!EnumHasAnyFlags(desc.SubRange.Aspect, NGITextureAspect::Stencil));
        }
    }
    else
    {
        Assert(desc.SubRange.Aspect == NGITextureAspect::Color);
    }


    auto* textureView = &mTextureViews.emplace_back(texture, desc);
    texture->mTextureViews.emplace_back(textureView);
    return textureView;
}

cross::REDBuffer* cross::RenderingExecutionDescriptor::AllocateBuffer(std::string_view name, const NGIBufferDesc& desc)
{
    return &mBuffers.emplace_back(name, desc);
}

cross::REDBuffer* cross::RenderingExecutionDescriptor::AllocateBuffer(std::string_view name, NGIBuffer* externalBuffer)
{
#ifndef CROSSENGINE_RELEASE
    if (auto* ret = RenderingExecutionDescriptor::FindExternalBuffer(externalBuffer); ret)
    {
        LOG_WARN("REDBuffer:{} with external buffer:{} was already created with name:{}", name, fmt::ptr(externalBuffer), ret->mName);
        return ret;
    }
#endif

    return &mBuffers.emplace_back(name, externalBuffer);
}

cross::REDBuffer* cross::RenderingExecutionDescriptor::AllocateBuffer(std::string_view name, NGIBuffer* externalBuffer, NGIResourceState externalState)
{
    auto* ret = AllocateBuffer(name, externalBuffer);
    ret->mSubresource.BeforeFrameState = externalState;
    return ret;
}

cross::REDBuffer* cross::RenderingExecutionDescriptor::FindExternalBuffer(NGIBuffer* externalBuffer)
{
    auto ret = std::find_if(mBuffers.begin(), mBuffers.end(), [&](const REDBuffer& buf) { return buf.mType == REDResourceType::External && buf.mNativeBuffer == externalBuffer; });
    return ret != mBuffers.end() ? &*ret : nullptr;
}

cross::REDBufferView* cross::RenderingExecutionDescriptor::AllocateBufferView(REDBuffer* buffer, const NGIBufferViewDesc& desc)
{
    auto& bufDesc = buffer->mDesc;
    Assert(EnumHasAllFlags(bufDesc.Usage, desc.Usage));
    Assert(desc.BufferLocation + desc.SizeInBytes <= bufDesc.Size);
    auto* bufferView = &mBufferViews.emplace_back(buffer, desc);
    Assert(bufferView);
    buffer->mBufferViews.emplace_back(bufferView);
    return bufferView;
}

cross::REDUniquePtr<cross::REDResidentBuffer> cross::RenderingExecutionDescriptor::CreateBuffer(std::string_view name, const NGIBufferDesc& desc)
{
    auto* buffer = new REDResidentBuffer{name, desc, this};
    mResidentBuffers.emplace(buffer);
    return REDUniquePtr<REDResidentBuffer>{buffer};
}

cross::REDUniquePtr<cross::REDResidentBufferView> cross::RenderingExecutionDescriptor::CreateBufferView(REDResidentBuffer* buffer, const NGIBufferViewDesc& desc)
{
    auto* bufferView = new REDResidentBufferView{buffer, desc, this};
    Assert(bufferView);
    return REDUniquePtr<REDResidentBufferView>{bufferView};
}

cross::REDUniquePtr<cross::REDResidentTexture> cross::RenderingExecutionDescriptor::CreateTexture(std::string_view name, const NGITextureDesc& desc)
{
    auto* texture = new REDResidentTexture{name, desc, this};
    mResidentTextures.emplace(texture);
    return REDUniquePtr<REDResidentTexture>{texture};
}

cross::REDUniquePtr<cross::REDResidentTextureView> cross::RenderingExecutionDescriptor::CreateTextureView(REDResidentTexture* texture, const NGITextureViewDesc& desc)
{
    auto* textureView = new REDResidentTextureView{texture, desc, this};
    return REDUniquePtr<REDResidentTextureView>{textureView};
}

void cross::RenderingExecutionDescriptor::Destroy(REDResidentObject* object)
{
    mPendingDestroyObjects.emplace_back(object);
}

void cross::RenderingExecutionDescriptor::FlushState(REDBuffer* buffer)
{
    Assert(buffer->mNativeBuffer && buffer->mType == REDResourceType::External);
    Assert(buffer->mSubresource.BeforeFrameState != NGIResourceState::Undefined);
    AllocateFlushStatePass()->mBuffers[buffer].State |= REDResourceState{buffer->mSubresource.BeforeFrameState};
}

void cross::RenderingExecutionDescriptor::FlushState(REDResidentBuffer* buffer, NGIResourceState newState)
{
    AllocateFlushStatePass()->mBuffers[buffer].State |= REDResourceState{newState};
}

void cross::RenderingExecutionDescriptor::FlushState(REDTexture* texture, UInt32 subResource)
{
    Assert(texture->mNativeTexture && texture->mType == REDResourceType::External);

    auto* flushStatePass = AllocateFlushStatePass();
    if (subResource == NGIAllSubresources)
    {
        for (UInt32 subres = 0; subres < texture->mSubresources.size(); ++subres)
        {
            auto beforeFrameState = texture->mSubresources[subres].BeforeFrameState;
            Assert(beforeFrameState != NGIResourceState::Undefined);
            flushStatePass->mTextures[{texture, subres}].State |= REDResourceState{beforeFrameState};
        }
    }
    else
    {
        auto beforeFrameState = texture->mSubresources[subResource].BeforeFrameState;
        Assert(beforeFrameState != NGIResourceState::Undefined);
        flushStatePass->mTextures[{texture, subResource}].State |= REDResourceState{beforeFrameState};
    }
}

void cross::RenderingExecutionDescriptor::FlushState(REDResidentTexture* texture, NGIResourceState newState, UInt32 subResource)
{
    auto* flushStatePass = AllocateFlushStatePass();
    if (subResource == NGIAllSubresources)
    {
        for (UInt32 subres = 0; subres < texture->mSubresources.size(); ++subres)
        {
            flushStatePass->mTextures[{texture, subres}].State |= REDResourceState{newState};
        }
    }
    else
    {
        flushStatePass->mTextures[{texture, subResource}].State |= REDResourceState{newState};
    }
}

bool cross::RenderingExecutionDescriptor::Validate(REDTexture* texture)
{
    return std::find_if(mTextures.begin(), mTextures.end(), [=](auto& tex) { return tex.mType == REDResourceType::ExtendedFromLast && &tex == texture; }) != mTextures.end();
}

bool cross::RenderingExecutionDescriptor::Validate(REDBuffer* buffer)
{
    return std::find_if(mBuffers.begin(), mBuffers.end(), [=](auto& buf) { return buf.mType == REDResourceType::ExtendedFromLast && &buf == buffer; }) != mBuffers.end();
}

void cross::RenderingExecutionDescriptor::QueueBufferUpload(REDBuffer* dstBuffer, UInt32 dstBufferOffset, const void* initialData, UInt32 initialDataSize)
{
    if (initialDataSize == 0)
    {
        return;
    }

    auto srcBufferWrap = mStagingMgr->AllocateStaging(NGIBufferUsage::CopySrc, initialDataSize);
    srcBufferWrap.MemWrite(0, initialData, initialDataSize);

    NGICopyBuffer region{
        srcBufferWrap.GetNGIOffset(),
        dstBufferOffset,
        initialDataSize,
    };
    mBufferUploads[dstBuffer].emplace_back(srcBufferWrap.GetNGIBuffer(), region);
}

void cross::RenderingExecutionDescriptor::QueueBufferUpload(REDBuffer* dstBuffer, cross::NGIStagingBuffer* srcBuffer, NGICopyBuffer region)
{
    mBufferUploads[dstBuffer].emplace_back(srcBuffer, region);
}

void RenderingExecutionDescriptor::QueueBufferFeedBackDynamic(REDBuffer* srcBuffer, cross::NGIStagingBuffer* dstBuffer, std::pmr::vector<UInt8>* data)
{
    mBufferFeedBack[srcBuffer].push_back({dstBuffer, data});
}

void cross::RenderingExecutionDescriptor::BeginFrame(UInt64 frameID)
{
    SCOPED_CPU_TIMING(GroupRendering, "REDBeginFrame");

    mAllocator.Tick(frameID);
    mBufferAllocator.Tick(frameID);

    {
        SCOPED_CPU_TIMING(GroupRendering, "Clear");
        mBufferUploads.clear();
        mBufferFeedBack.clear();

        mPasses.clear();
        mFlushStatePasses.clear();
        bool isOddFrame = !!(frameID & 1);
        mPassPoolOdd = std::move(FrameDeque<REDPass>(&mCurrentAllocator));
        mPassPool = &mPassPoolOdd;
    }

    mCurrentRegion = &mRegionPool.emplace_back(REDRegion{0, nullptr, "Default"});

    mProloguePass = AllocatePass("Prologue Pass");
    mEpiloguePass = nullptr;

    {
        SCOPED_CPU_TIMING(GroupRendering, "Init Resident Resources");

        for (auto* buf : mResidentBuffers)
        {
            buf->mBufferViews.clear();
            buf->mFirstPass = nullptr;
            buf->mLastPass = nullptr;
            buf->mSubresource.LastWriter = nullptr;
            buf->mSubresource.MergedState = nullptr;
            buf->mSubresource.BeforeFrameState = *buf->mAfterFrameStates;
        }

        for (auto* tex : mResidentTextures)
        {
            tex->mTextureViews.clear();
            tex->mFirstPass = nullptr;
            tex->mLastPass = nullptr;

            auto* state = tex->mAfterFrameStates;
            for (auto& subres : tex->mSubresources)
            {
                subres.LastWriter = nullptr;
                subres.MergedState = nullptr;
                subres.BeforeFrameState = *state++;
            }
        }
    }

    {
        SCOPED_CPU_TIMING(GroupRendering, "Remove legacy resources");

        mTextures.remove_if([=](REDTexture& tex) {
            if (tex.mType == REDResourceType::ExtendedToNext)
            {
                tex.mType = REDResourceType::ExtendedFromLast;
                tex.mTextureViews.clear();
                tex.mFirstPass = nullptr;
                tex.mLastPass = nullptr;
                auto* lastFrameState = tex.mAfterFrameStates;
                for (auto& subres : tex.mSubresources)
                {
                    subres.LastWriter = nullptr;
                    subres.MergedState = nullptr;
                    if (lastFrameState)
                    {
                        subres.BeforeFrameState = *lastFrameState++;
                    }
                }
                return false;
            }
            return true;
        });
        mTextureViews.clear();
        mBuffers.remove_if([=](REDBuffer& buf) {
            if (buf.mType == REDResourceType::ExtendedToNext)
            {
                buf.mType = REDResourceType::ExtendedFromLast;
                buf.mBufferViews.clear();
                buf.mFirstPass = nullptr;
                buf.mLastPass = nullptr;
                buf.mSubresource.LastWriter = nullptr;
                buf.mSubresource.MergedState = nullptr;
                if (buf.mAfterFrameStates)
                {
                    buf.mSubresource.BeforeFrameState = *buf.mAfterFrameStates;
                }
                return false;
            }
            return true;
        });
    }

    mBufferViews.clear();
    mCurrentRenderPass = nullptr;
    mRangeStatePool = std::move(FrameDeque<REDResourceRangeState>(&mCurrentAllocator));
    mRenderPassPool.clear();
    mFrameID = frameID;
    mEnableVisualizer = false;
}

void cross::RenderingExecutionDescriptor::EndFrame()
{
    for (auto* object : mPendingDestroyObjects)
    {
        mResidentBuffers.erase(dynamic_cast<REDResidentBuffer*>(object));
        mResidentTextures.erase(dynamic_cast<REDResidentTexture*>(object));
        delete object;
    }

    mCulling.Clear(mFrameID); 

    threading::TaskEventArray onAssembleTasks;
    for (auto& pass : *mPassPool)
    {
        for (auto& payload : pass.mPayloads)
        {
            if (payload->mOnAssembleTask)
            {
                onAssembleTasks.Add(payload->mOnAssembleTask);
            }
        }
    }
    onAssembleTasks.WaitForCompletion();

    mPendingDestroyObjects.clear();
    mRegionPool.clear();
    mPassPoolOdd.clear();
    mCurrentRegion = nullptr;
}

void cross::RenderingExecutionDescriptor::CompileAndExecute(NGICommandList* copyCmdList, NGICommandList* preCmdList, NGICommandList* postCmdList, std::vector<NGICommandList*>& cmdLists, NGICommandQueue* cmdQue)
{
    SCOPED_CPU_TIMING(GroupRendering, "REDCompile");

    AssertMsg(!mCurrentRenderPass, "Last render pass not ended");

    mEpiloguePass = AllocatePass("Epilogue Pass", true);

    AssertMsg(mCurrentRegion->Parent == nullptr, "Count of BeginRegion/EndRegion not matching, less EndRegion was called");

    {
        SCOPED_CPU_TIMING(GroupRendering, "Init");
        UInt32 id = 0;
        for (auto* pass : mPasses)
        {
            pass->mIndex = id++;
        }
        // clear resource temp info used by RenderPass subgraph
        for (auto& texture : mTextures)
        {
            Assert(texture.mReferenceCount == 0);
            for (auto& subres : texture.mSubresources)
            {
                subres.LastWriter = nullptr;
                subres.MergedState = nullptr;
            }
        }

        for (auto& buffer : mBuffers)
        {
            Assert(buffer.mReferenceCount == 0);
            buffer.mSubresource.LastWriter = nullptr;
            buffer.mSubresource.MergedState = nullptr;
        }

        for (auto& texture : mResidentTextures)
        {
            for (auto& subres : texture->mSubresources)
            {
                subres.LastWriter = nullptr;
                subres.MergedState = nullptr;
            }
        }

        for (auto* buffer : mResidentBuffers)
        {
            buffer->mSubresource.LastWriter = nullptr;
            buffer->mSubresource.MergedState = nullptr;
        }

        // Set external resource state in prologue pass and epilogue pass
        for (auto& texture : mTextures)
        {
            if (texture.mType == REDResourceType::External)
            {
                bool allSubresourceUndefined = true;
                for (UInt32 index = 0; index < texture.mSubresources.size(); index++)
                {
                    auto outFrameState = texture.mSubresources[index].BeforeFrameState;
                    if (outFrameState != NGIResourceState::Undefined)
                    {
                        // prologue pass was responsible for import external resource/extended resource, so it's a writer too
                        REDResourceState state{outFrameState, true, true};

                        REDTextureSubresource subres{&texture, index};
                        auto* initialMergedState = AllocateResourceRangeState(state, mProloguePass, mProloguePass);

                        mProloguePass->mTextures[subres] = {
                            state,
                            initialMergedState,
                        };
                        mEpiloguePass->mTextures[subres] = {
                            state,
                            initialMergedState,
                        };

                        texture.mSubresources[index].MergedState = initialMergedState;
                        texture.mSubresources[index].LastWriter = mProloguePass;
                        allSubresourceUndefined = false;
                    }
                }
                Assert(!allSubresourceUndefined);

                texture.mFirstPass = mProloguePass;
                texture.mLastPass = mEpiloguePass;
            }
        }

        for (auto& buffer : mBuffers)
        {
            if (buffer.mType == REDResourceType::External)
            {
                auto externalState = buffer.mSubresource.BeforeFrameState;
                Assert(externalState != NGIResourceState::Undefined);

                REDResourceState state{externalState, true, true};
                auto* initialMergedState = AllocateResourceRangeState(state, mProloguePass, mProloguePass);

                mProloguePass->mBuffers[&buffer] = {state, initialMergedState};
                mEpiloguePass->mBuffers[&buffer] = {state, initialMergedState};

                buffer.mSubresource.MergedState = initialMergedState;
                buffer.mSubresource.LastWriter = mProloguePass;

                buffer.mFirstPass = mProloguePass;
                buffer.mLastPass = mEpiloguePass;
            }
        }
    }

    {
        SCOPED_CPU_TIMING(GroupRendering, "Process Dependency");

        for (auto* pass : mPasses)
        {
            // all backboard resource was handled by the outer RenderPass pass
            if (pass->mRenderPass)
            {
                for (auto* subpass : pass->mRenderPass->mSubpasses)
                {
                    if (subpass->mVisited)
                    {
                        for (auto& [view, state] : subpass->mTextureReferences)
                        {
                            view->EnumerateSubresource([&, view = view, state = state](UInt32 index, UInt32 mip, UInt32 array, UInt32 plane) {
                                REDTextureSubresource subres{view->mTexture, index};
                                if (!pass->mRenderPass->mTargets.count(subres))
                                {
                                    pass->mTextures[subres].State |= state;
                                }
                                else
                                {
                                    AssertMsg(false, "If texture was both as resource and target, whose state should be handle automatically.");
                                }
                            });
                        }

                        for (auto& [buffer, state] : subpass->mBufferReferences)
                        {
                            pass->mBuffers[buffer].State |= state;
                        }
                    }
                }
            }
            else
            {
                for (auto& [view, state] : pass->mTextureReferences)
                {
                    view->EnumerateSubresource([&, view = view, state = state](UInt32 index, UInt32 mip, UInt32 array, UInt32 plane) { pass->mTextures[{view->mTexture, index}].State |= state; });
                }

                for (auto& [buffer, state] : pass->mBufferReferences)
                {
                    pass->mBuffers[buffer].State |= state;
                }
            }

            for (auto& [subres, info] : pass->mTextures)
            {
                auto& subresource = subres.Texture->mSubresources[subres.Subresource];
                if (subresource.LastWriter)
                {
                    pass->AddPredecessor(subresource.LastWriter);
                }
                if (info.State.Write)
                {
                    subresource.LastWriter = pass;
                    switch (subres.Texture->mType)
                    {
                    case REDResourceType::ExtendedToNext:
                    case REDResourceType::Resident:
                        mEpiloguePass->AddPredecessor(pass);
                        break;
                    default:
                        break;
                    }
                }
            }

            for (auto& [buffer, info] : pass->mBuffers)
            {
                auto& subresource = buffer->mSubresource;
                if (subresource.LastWriter)
                {
                    pass->AddPredecessor(subresource.LastWriter);
                }
                if (info.State.Write)
                {
                    subresource.LastWriter = pass;
                    switch (buffer->mType)
                    {
                    case REDResourceType::ExtendedToNext:
                    case REDResourceType::Resident:
                        mEpiloguePass->AddPredecessor(pass);
                        break;
                    default:
                        break;
                    }
                }
            }

            // every pass should have a predecessor, except the prologue pass
            if (pass->mPredecessors.empty())
            {
                pass->AddPredecessor(mProloguePass);
            }
        }

        for (auto* pass : mFlushStatePasses)
        {
            mEpiloguePass->AddPredecessor(pass);
        }

        for (auto itr = mPasses.rbegin(); itr != mPasses.rend(); ++itr)
        {
            if ((*itr)->mNoCulling)
            {
                MarkVisitedPass((*itr));
            }
        }
    }

    {
        SCOPED_CPU_TIMING(GroupRendering, "Merge resource state");
        /*
        Merge resource state
        Prologue Pass:
        1. Swapchain backbuffer : Present
        2. TAA histroy buffer : TargetReadWrite
        Epilogue Pass:
        1. Swapchain backbuffer : Present
        2. TAA histroy buffer : TargetReadWrite
        */

        for (auto* pass : mPasses)
        {
            if (!pass->mVisited || pass == mProloguePass)
            {
                continue;
            }

            for (auto& [subres, info] : pass->mTextures)
            {
                auto resState = info.State;
                if (pass->IsGraphicsPass())
                {
                    auto& texes = pass->mRenderPass->mBeginPass->mTextures;
                    if (auto ret = texes.find(subres); ret != texes.end())
                    {
                        resState.NGIState = ret->second.State.NGIState;
                    }
                }

                auto*& resMergedState = subres.Texture->mSubresources[subres.Subresource].MergedState;
                if (resMergedState && resMergedState->IsTextureMergeAllowed(resState))
                {
                    (*resMergedState) |= resState;
                    resMergedState->mLastPass = pass;
                }
                else
                {
                    resMergedState = AllocateResourceRangeState(resState, pass, pass);
                }
                info.MergedState = resMergedState;

                if (!subres.Texture->mFirstPass)
                {
                    subres.Texture->mFirstPass = pass;
                }
                subres.Texture->mLastPass = pass;
            }

            for (auto& [buffer, info] : pass->mBuffers)
            {
                auto*& resMergedState = buffer->mSubresource.MergedState;
                if (resMergedState && resMergedState->IsBufferMergeAllowed(info.State))
                {
                    (*resMergedState) |= info.State;
                    resMergedState->mLastPass = pass;
                }
                else
                {
                    resMergedState = AllocateResourceRangeState(info.State, pass, pass);
                }
                info.MergedState = resMergedState;

                if (!buffer->mFirstPass)
                {
                    buffer->mFirstPass = pass;
                }
                buffer->mLastPass = pass;
            }
        }
    }

    {
        SCOPED_CPU_TIMING(GroupRendering, "REDPayload::OnVisited");

        VisitValidPasses([&](REDPass* pass) {
            for (auto& payload : pass->mPayloads)
            {
                payload->OnVisited(pass);
            }
        });
    }

    mCulling.WaitForCompletion();

    {
        SCOPED_CPU_TIMING(GroupRendering, "REDPayload::OnCulling");

        VisitValidPasses([&](REDPass* pass) {
            for (auto& payload : pass->mPayloads)
            {
                payload->OnCulling(pass);
            }
        });
    }


    for (auto& [srcBuffer, updates] : mBufferFeedBack)
    {
        for (auto & itr: updates)
        {
            auto dst_buffer = static_cast<NGIStagingBuffer*>(std::get<0>(itr));
            auto desc = dst_buffer->GetDesc();
            desc.Size = srcBuffer->GetDesc().Size;
            dst_buffer->Allocation(desc);
        }
    }


    for (auto* pass : mPasses)
    {
        if (!pass->mVisited || pass == mProloguePass || pass == mEpiloguePass)
        {
            continue;
        }

        for (auto& [subres, info] : pass->mTextures) 
        {
            if (subres.Texture->mType != REDResourceType::Normal)
                continue;
            subres.Texture->mReferenceCount++;
        }
        for (auto& [buffer, info] : pass->mBuffers)
        {
            if (buffer->mType != REDResourceType::Normal)
                continue;
            buffer->mReferenceCount++;
        }
    }
    // TODO(peterwjma): check texture memory less

    {
        SCOPED_CPU_TIMING(GroupRendering, "Allocate textures and buffers");

        // allocate textures and buffers
        for (auto* pass : mPasses)
        {
            if (!pass->mVisited)
            {
                continue;
            }

            for (auto& [subres, info] : pass->mTextures)
            {
                if (subres.Texture->mNativeTexture == nullptr)
                {
                    // lazy initialize textures in first using pass
                    auto desc = subres.Texture->mDesc;
                    if (mEnableVisualizer)
                    {
                        desc.Usage |= NGITextureUsage::ShaderResource;
                        desc.SeparateView = true;
                    }
                    //LOG_TRACE("Allocate texture: {} with ID: ", subres.Texture->mName);
                    subres.Texture->bIsTransient = mTransientResMgr->IsTransientResource(subres.Texture);
                    auto [nativeTexture, lastFrameStates] = mTransientResMgr->AllocateTexture(desc, subres.Texture->mName, subres.Texture->mMemoryless, subres.Texture->bIsTransient, subres.Texture->mType == REDResourceType::Normal);
                    subres.Texture->Initialize(nativeTexture, lastFrameStates);
                }
                for (auto* textureView : subres.Texture->mTextureViews)
                {
                    if (textureView->mNativeTextureView == nullptr)
                        textureView->mNativeTextureView = mTransientResMgr->AllocateTextureView(textureView->mDesc, subres.Texture->mNativeTexture);
                }
            }
            for (auto& [buffer, info] : pass->mBuffers)
            {
                if (buffer->mNativeBuffer == nullptr)
                {
                    //buffer for upload is not transient
                    buffer->bIsTransient = mTransientResMgr->IsTransientResource(buffer, mBufferUploads.count(buffer));
                    auto [nativeBuffer, lastFrameStates] = mTransientResMgr->AllocateBuffer(buffer->mDesc, buffer->mName, buffer->bIsTransient, buffer->mType == REDResourceType::Normal);
                    buffer->Initialize(nativeBuffer, lastFrameStates);
                }

                for (auto* bufferView : buffer->mBufferViews)
                {
                    if (bufferView->mNativeBufferView == nullptr)
                        bufferView->mNativeBufferView = mTransientResMgr->AllocateBufferView(bufferView->mDesc, buffer->mNativeBuffer);
                }
            }
            for (auto& [subres, info] : pass->mTextures)
            {
                auto tex = subres.Texture;
                if (tex->mType != REDResourceType::Normal || tex->bIsTransient == false)
                    continue;
                tex->mReferenceCount--;
                Assert(tex->mReferenceCount >= 0);
                if (tex->mReferenceCount == 0)
                {
                    mTransientResMgr->DeallocateTexture(tex->mNativeTexture);
                }
            }
            for (auto& [buffer, info] : pass->mBuffers)
            {
                if (buffer->mType != REDResourceType::Normal || buffer->bIsTransient == false)
                    continue;
                buffer->mReferenceCount--;
                Assert(buffer->mReferenceCount >= 0);
                if (buffer->mReferenceCount == 0)
                    mTransientResMgr->DeallocateBuffer(buffer->mNativeBuffer);
            }
        }

        mTransientResMgr->Flush();
    }
    {
        SCOPED_CPU_TIMING(GroupRendering, "Extending Life Time");
        // 1. extend lifetime if needed
        // 2. store the last state of this frame
        // 3. clear merged state in resource
        for (auto& texture : mTextures)
        {
            if (texture.mType == REDResourceType::ExtendedToNext && texture.mNativeTexture)
            {
                mTransientResMgr->ExtendLifetime(texture.mNativeTexture);
            }

            if (auto* states = texture.mAfterFrameStates; states)
            {
                for (auto& subres : texture.mSubresources)
                {
                    if (subres.MergedState)
                    {
                        *states = subres.MergedState->NGIState;
                    }
                    ++states;
                }
            }

            for (auto& subres : texture.mSubresources)
            {
                subres.MergedState = nullptr;
            }
        }

        for (auto& texture : mResidentTextures)
        {
            auto* states = texture->mAfterFrameStates;
            for (auto& subres : texture->mSubresources)
            {
                if (subres.MergedState)
                {
                    *states = subres.MergedState->NGIState;
                    subres.MergedState = nullptr;
                }
                ++states;
            }
        }

        for (auto& buffer : mBuffers)
        {
            if (buffer.mType == REDResourceType::ExtendedToNext && buffer.mNativeBuffer)
            {
                mTransientResMgr->ExtendLifetime(buffer.mNativeBuffer);
            }

            if (buffer.mAfterFrameStates && buffer.mSubresource.MergedState)
            {
                *buffer.mAfterFrameStates = buffer.mSubresource.MergedState->NGIState;
            }

            buffer.mSubresource.MergedState = nullptr;
        }

        for (auto* buffer : mResidentBuffers)
        {
            if (buffer->mSubresource.MergedState)
            {
                *buffer->mAfterFrameStates = buffer->mSubresource.MergedState->NGIState;
            }

            buffer->mSubresource.MergedState = nullptr;
        }
    }

    {
        SCOPED_CPU_TIMING(GroupRendering, "Create render pass, frame buffer and add barrier");
        for (auto* pass : mPasses)
        {
            // skip culled passes
            if (!pass->mVisited)
            {
                continue;
            }

            // init resource merged state of external resources in prolugue pass
            if (pass == mProloguePass)
            {
                for (auto& [subres, info] : pass->mTextures)
                {
                    subres.Texture->mSubresources[subres.Subresource].MergedState = info.MergedState;
                }
                for (auto& [subres, info] : pass->mBuffers)
                {
                    subres->mSubresource.MergedState = info.MergedState;
                }
                continue;
            }

            auto* renderPass = pass->mRenderPass;

            for (auto& [subres, info] : pass->mTextures)
            {
                /*
                add barriers
                if resource was allocated in this pass
                1. subresource was use in this pass
                    lastFrameState was undefined, transit state from lastFrameState to state in this pass
                    note:
                        a. native resource was created in this pass
                            for VK layout transition from Undefine and for D3D12 no-op
                        b. native resource was not create in this pass
                            normal transition
                2. subresource was not used in this pass
                    store lastFrameState in REDTexture::Subresource::LastFrameState
                    note:
                        a. native resource was create in this pass, transition from initial state should be performed in next pass which using the subresource
                            for vk, store Undefined
                            for D3D12, should transit from initial state to next used state, this would be handled by D3D12 NGI backend
                        b. native resource was not created in this pass
                            just store lastFrameState

                if native texture was not allocated in this pass
                1. subresouce was first used in this pass
                    transit state from last frame state (stored in REDTexture::Subresource::LastFrameState)
                2. subresource was not first used in this pass
                    b. transit from previous merged pass if state can't be merged
                    */

                auto*& resMergedState = subres.Texture->mSubresources[subres.Subresource].MergedState;

                auto nextState = info.MergedState->NGIState;

                if (resMergedState == nullptr)
                {
                    Assert(subres.Texture->mType != REDResourceType::External);
                    // subresouce was first used in this pass
                    if (auto beforeFrameState = subres.Texture->mSubresources[subres.Subresource].BeforeFrameState; beforeFrameState != nextState)
                    {
                        NGITextureBarrier barrier{
                            subres.Texture->mNativeTexture,
                            subres.Subresource,
                            beforeFrameState,
                            nextState,
                            !info.MergedState->Read,
                        };
                        if (renderPass && renderPass->mTargets.count(subres))
                        {
                            pass->mRenderPass->mTargetBarriers.emplace_back(barrier);
                        }
                        else
                        {
                            pass->mTextureBarriers.emplace_back(barrier);
                        }
                    }
                }
                else
                {
                    // subresource was not first used in this pass, issue a normal transition if needed
                    if (resMergedState != info.MergedState && info.MergedState->mFirstPass == pass)
                    {
                        NGITextureBarrier barrier{
                            subres.Texture->mNativeTexture,
                            subres.Subresource,
                            resMergedState->NGIState,
                            nextState,
                            !info.MergedState->Read,
                        };

                        if (renderPass && renderPass->mTargets.count(subres))
                        {
                            pass->mRenderPass->mTargetBarriers.emplace_back(barrier);
                        }
                        else
                        {
                            pass->mTextureBarriers.emplace_back(barrier);
                        }
                    }
                }
                resMergedState = info.MergedState;
            }

            for (auto& [buffer, info] : pass->mBuffers)
            {
                auto*& resMergedState = buffer->mSubresource.MergedState;
                auto nextState = info.MergedState->NGIState;

                if (buffer->mFirstPass == pass && buffer->mType != REDResourceType::External)
                {
                    // transient buffer, first used in this pass
                    if (auto beforeFrameState = buffer->mSubresource.BeforeFrameState; beforeFrameState != nextState)
                    {
                        NGIBufferBarrier barrier{
                            buffer->mNativeBuffer,
                            beforeFrameState,
                            nextState,
                        };
                        pass->mBufferBarriers.emplace_back(barrier);
                    }
                }
                else if (resMergedState != info.MergedState && info.MergedState->mFirstPass == pass)
                {
                    // transient buffer or external buffer, not first used in this pass
                    NGIBufferBarrier barrier{
                        buffer->mNativeBuffer,
                        resMergedState->NGIState,
                        nextState,
                    };
                    pass->mBufferBarriers.emplace_back(barrier);
                }

                resMergedState = info.MergedState;
            }

            if (renderPass)
            {
                renderPass->AllocateNativeFramebuffer(mTransientResMgr);
            }
        }
    }

    // add barrier for subpasses
    for (auto* pass : mPasses)
    {
        if (!pass->mVisited || !pass->mRenderPass)
        {
            continue;
        }

        auto* beginPass = pass->mRenderPass->mBeginPass;
        for (auto& [subres, info] : beginPass->mTextures)
        {
            subres.Texture->mSubresources[subres.Subresource].MergedState = nullptr;
        }

        for (auto* subpass : pass->mRenderPass->mSubpasses)
        {
            for (auto& [subres, info] : subpass->mTextures)
            {
                auto*& resMergedState = subres.Texture->mSubresources[subres.Subresource].MergedState;
                if (resMergedState && resMergedState != info.MergedState)
                {
                    subpass->mTextureBarriers.push_back({
                        subres.Texture->mNativeTexture,
                        subres.Subresource,
                        resMergedState->NGIState,
                        info.MergedState->NGIState,
                        !info.MergedState->Read,
                    });
                }
                resMergedState = info.MergedState;
            }
        }
    }

    {
        // pre buffer upload
        std::vector<NGIBufferBarrier> beforeBufferBarriers, afterBufferBarriers;
        std::map<std::tuple<NGIBuffer*, NGIBuffer*>, std::vector<NGICopyBuffer>> bufferUpdates;

        for (auto& [dstBuffer, updates] : mBufferUploads)
        {
            if (auto dstNGIBuffer = dstBuffer->GetNativeBuffer(); dstNGIBuffer)
            {
                auto bufferState = dstBuffer->mSubresource.BeforeFrameState;
                if (bufferState != NGIResourceState::CopyDst)
                {
                    NGIBufferBarrier beforeBarrier{dstNGIBuffer, bufferState, NGIResourceState::CopyDst};
                    beforeBufferBarriers.emplace_back(beforeBarrier);

                    // can't transite resource to undefined state
                    if (bufferState == NGIResourceState::Undefined)
                    {
                        auto* firstUsingPass = dstBuffer->mFirstPass;
                        if (firstUsingPass)
                        {
                            Assert(firstUsingPass->mBuffers.count(dstBuffer));
                            auto nextState = firstUsingPass->mBuffers[dstBuffer].MergedState->NGIState;
                            NGIBufferBarrier afterBarrier{ dstNGIBuffer, NGIResourceState::CopyDst, nextState };
                            afterBufferBarriers.emplace_back(afterBarrier);
                        }
                        else
                        {
                            // resident buffer not used in this frame, just change it's resource state
                            Assert(dstBuffer->mType == REDResourceType::Resident);
                            *dstBuffer->mAfterFrameStates = NGIResourceState::CopyDst;
                        }
                    }
                    else
                    {
                        NGIBufferBarrier afterBarrier{dstNGIBuffer, NGIResourceState::CopyDst, bufferState};
                        afterBufferBarriers.emplace_back(afterBarrier);
                    }
                }

                for (auto& [srcBuffer, region] : updates)
                {
                    bufferUpdates[{dstNGIBuffer, srcBuffer}].emplace_back(region);
                }
            }
        }

        preCmdList->ResourceBarrier(static_cast<UInt32>(beforeBufferBarriers.size()), beforeBufferBarriers.data(), 0, nullptr);
        for (auto& [dstSrc, regions] : bufferUpdates)
        {
            auto [dstBuffer, srcBuffer] = dstSrc;
            copyCmdList->CopyBufferToBuffer(dstBuffer, srcBuffer, static_cast<UInt32>(regions.size()), regions.data());
        }
        preCmdList->ResourceBarrier(static_cast<UInt32>(afterBufferBarriers.size()), afterBufferBarriers.data(), 0, nullptr);
    }

    {
        SCOPED_CPU_TIMING(GroupRendering, "Executing");

        threading::TaskEventArray exeTasks;

        // should limit the number of cmd list
        // https://developer.nvidia.com/blog/vulkan-dos-donts/
        // we limit it to 20

        std::vector<REDPass*> visited_passes;
        visited_passes.reserve(mPasses.size());
        std::copy_if(mPasses.begin(), mPasses.end(), std::back_inserter(visited_passes), [](REDPass* p) {return p->IsVisited(); });

        const UInt32 Cmd_List_Num = 20u;
        const UInt32 MinimumPassNum = 5u;  // add some minimum pass Num 5u;
        UInt32 passNumEachCmdList = (static_cast<UInt32>(visited_passes.size())+ Cmd_List_Num -1) / Cmd_List_Num;

        passNumEachCmdList = std::max(passNumEachCmdList, MinimumPassNum);

        UInt32 NeededCmdList = (static_cast<UInt32>(visited_passes.size()) + passNumEachCmdList - 1) / passNumEachCmdList;

        cmdLists.resize(NeededCmdList);
        cmdQue->AllocateCommandLists(NeededCmdList, cmdLists.data());

        REDRegion* curRegion = mCurrentRegion;

        for (UInt32 passIndex = 0;  passIndex < visited_passes.size(); passIndex += passNumEachCmdList)
        {
            threading::TaskEventArray preTask;

            REDRegion * nextRegion = mCurrentRegion;

            for (UInt32 subPassGroup = passIndex; subPassGroup < std::min(static_cast<UInt32>(visited_passes.size()), passIndex + passNumEachCmdList); subPassGroup++)
            {
                auto pass = visited_passes[subPassGroup];

                if (pass->mRenderPass)
                {
                    for (auto* subpass : pass->mRenderPass->mSubpasses)
                    {
                        if (subpass->IsVisited() && !subpass->mPayloads.empty())
                        {
                            threading::TaskEventArray onAssembleTasks;
                            for (auto& payload : subpass->mPayloads)
                            {
                                if (payload->mOnAssembleTask)
                                {
                                    onAssembleTasks.Add(payload->mOnAssembleTask);
                                }
                            }
                            preTask.Add(threading::Dispatch(onAssembleTasks, [=](auto) 
                                {
                                    for (auto& payload : subpass->mPayloads)
                                    {
                                        payload->OnCompile(subpass);
                                    }
                                }));
                        }
                    }
                }
                else
                {
                    preTask.Add(threading::Dispatch([pass](auto) {
                        for (auto payloadIdx = 0; payloadIdx < pass->mPayloads.size(); payloadIdx++)
                        {
                            pass->mPayloads[payloadIdx]->OnCompile(pass);
                        }
                        }));
                }
                nextRegion = pass->mRegion;
            }

            auto primaryCmdList = cmdLists[passIndex / passNumEachCmdList];

            exeTasks.Add(threading::Dispatch(preTask, [=, beginRegion = curRegion](auto) {
                primaryCmdList->Begin();
                auto currentRegion = beginRegion;
                for (UInt32 subPassGroup = passIndex; subPassGroup < std::min(static_cast<UInt32>(visited_passes.size()), passIndex + passNumEachCmdList); subPassGroup++)
                {
                    auto pass = visited_passes[subPassGroup];

                    // it's quite confusing how these works;
                    MarkDebugRegion(currentRegion, pass->mRegion, primaryCmdList);
                    currentRegion = pass->mRegion;

                    if (auto* renderPass = pass->mRenderPass; renderPass)
                    {
                        auto* beginPass = renderPass->mBeginPass;
                        auto* endPass = renderPass->mEndPass;

                        primaryCmdList->BeginDebugRegion(renderPass->mName.c_str());
                        primaryCmdList->ResourceBarrier(static_cast<UInt32>(pass->mBufferBarriers.size()), pass->mBufferBarriers.data(), static_cast<UInt32>(pass->mTextureBarriers.size()), pass->mTextureBarriers.data());
                        primaryCmdList->BeginRenderPass(renderPass->mRenderPass,
                                                        renderPass->mFramebuffer,
                                                        static_cast<UInt32>(renderPass->mClearValues.size()),
                                                        renderPass->mClearValues.data(),
                                                        static_cast<UInt32>(renderPass->mTargetBarriers.size()),
                                                        renderPass->mTargetBarriers.data(),
                                                        true);

                        auto firstPass = true;

                        for (auto* subpass : renderPass->mSubpasses)
                        {
                            if (!subpass->mVisited || subpass == beginPass)
                                continue;

                            primaryCmdList->ResourceBarrier(0, nullptr, static_cast<UInt32>(subpass->mTextureBarriers.size()), subpass->mTextureBarriers.data());

                            if (subpass != endPass)
                            {
                                primaryCmdList->BeginDebugRegion(subpass->mName.c_str(), nullptr, false);

                                if (firstPass)
                                {
                                    firstPass = false;
                                }
                                else
                                {
                                    primaryCmdList->NextSubPass(true);
                                }

                                for (auto& payload : subpass->mPayloads)
                                {
                                    SCOPED_CPU_TIMING(GroupRendering, "RenderDrawUnits");
                                    payload->OnExecute(subpass, primaryCmdList);
                                }

                                primaryCmdList->JoinBundleCommandLists();
                                primaryCmdList->EndDebugRegion(false);
                            }
                        }

                        primaryCmdList->EndRenderPass();
                        primaryCmdList->EndDebugRegion();
                    }
                    else
                    {
                        // prologue/epilogue/compute/copy pass
                        primaryCmdList->BeginDebugRegion(pass->mName.c_str());
                        primaryCmdList->ResourceBarrier(static_cast<UInt32>(pass->mBufferBarriers.size()), pass->mBufferBarriers.data(), static_cast<UInt32>(pass->mTextureBarriers.size()), pass->mTextureBarriers.data());
                        //Assert(pass->mPayloads.size() <= 2);

                        for (auto& payload : pass->mPayloads)
                        {
                            payload->OnExecute(pass, primaryCmdList);
                        }

                        primaryCmdList->EndDebugRegion();
                    }
                    if (mEnableVisualizer)
                    {
                        SCOPED_CPU_TIMING(GroupRendering, "REDPayload::OnExecuteVisualizer");
                        if (mVisualizer->mSelectResInfo.SelectState && mVisualizer->mSelectResInfo.PrepareState)
                        {
                            for (auto& [texture, subres] : pass->mTextures)
                            {
                                auto name = fmt::format("{}-{}", texture.Texture->mName, texture.Subresource);
                                auto nameHash = mVisualizer->mVisEditor->GetStringHash(name.c_str());
                                if (pass->GetName() == mVisualizer->mSelectResInfo.WritePassName && nameHash == mVisualizer->mSelectResInfo.NameHash)
                                {
                                    mVisualizer->BlitSelectResNode(primaryCmdList);
                                }
                            }
                        }
                        else
                        {
                            std::vector<REDThumbnailInfo*> thumbnailInfos;
                            for (auto& [texture, subres] : pass->mTextures)
                            {
                                if (auto ret = mVisualizer->mThumbnails.find(subres.MergedState); ret != mVisualizer->mThumbnails.end())
                                {
                                    thumbnailInfos.emplace_back(&ret->second);
                                }
                            }
                            mVisualizer->BlitThumbnails(primaryCmdList, thumbnailInfos);
                        }
                    }
                }
                primaryCmdList->End();
            }));

            // recover the region for correct handle
            // indeed, it's confusing
            curRegion = nextRegion;
        }

        MarkDebugRegion(mCurrentRegion, &mRegionPool.front(), postCmdList);

        exeTasks.WaitForCompletion();
    }
}

auto cross::RenderingExecutionDescriptor::FindCommonParent(REDRegion* a, REDRegion* b)
{
    while (a->Depth > b->Depth)
    {
        a = a->Parent;
    }
    while (b->Depth > a->Depth)
    {
        b = b->Parent;
    }
    while (a != b)
    {
        a = a->Parent;
        b = b->Parent;
    }
    return a;
}

void cross::RenderingExecutionDescriptor::IterateToParent(REDRegion* region, REDRegion* parent, NGICommandList* cmdList)
{
    if (region != parent)
    {
        IterateToParent(region->Parent, parent, cmdList);
        cmdList->BeginDebugRegion(region->Name.c_str());
    }
}

void cross::RenderingExecutionDescriptor::MarkDebugRegion(REDRegion* curRegion, REDRegion* nextRegion, NGICommandList* cmdList)
{
    if (curRegion == nextRegion)
    {
        /*if (frameProConnected)
        {
            if (mTimeStamp.GetRegionIndexMap().count(curRegion))
            {
                mTimeStamp.InsertEndTimeStamp(cmdList, curRegion);
            }
            else
            {
                mTimeStamp.InsertBeginTimeStamp(cmdList, curRegion);
            }
        }*/
        return;
    }

    auto parent = FindCommonParent(curRegion, nextRegion);

    while (curRegion != parent)
    {
        cmdList->EndDebugRegion();
        curRegion = curRegion->Parent;
    }
    IterateToParent(nextRegion, parent, cmdList);
}

REDRegion* RenderingExecutionDescriptor::GetCurrentRegion()
{
    return mCurrentRegion;
}

REDRegion* RenderingExecutionDescriptor::GetRootRegion()
{
    return &(*mRegionPool.begin());
}

void RenderingExecutionDescriptor::SetCurrentRegion(REDRegion* region)
{
    mCurrentRegion = region;
}

REDRenderPass* RenderingExecutionDescriptor::GetCurrentRenderPass()
{
    return mCurrentRenderPass;
}

void RenderingExecutionDescriptor::SetCurrentRenderPass(REDRenderPass* renderPass)
{
    mCurrentRenderPass = renderPass;
}

REDRegion* ThreadSafeRenderingExecutionDescriptor::GetCurrentRegion()
{
    std::unique_lock lock(mCurrentRegionMutex);

    auto thread_id = std::hash<std::thread::id>{}(std::this_thread::get_id());
    auto region = mThreadRegions[thread_id];

    if (region == nullptr)
    {
        mThreadRegions[thread_id] = mCurrentRegion;
    }
    return mThreadRegions[thread_id];
}

void ThreadSafeRenderingExecutionDescriptor::SetCurrentRegion(REDRegion* region)
{
    std::unique_lock lock(mCurrentRegionMutex);

    auto thread_id = std::hash<std::thread::id>{}(std::this_thread::get_id());
    auto curregion = mThreadRegions[thread_id];

    Assert(curregion != nullptr);

    mThreadRegions[thread_id] = region;
}

REDRenderPass* ThreadSafeRenderingExecutionDescriptor::GetCurrentRenderPass()
{
    std::unique_lock lock(mCurrentRenderPassMutex);

    auto thread_id = std::hash<std::thread::id>{}(std::this_thread::get_id());
    auto renderPass = mThreadRenderPass[thread_id];
    return mThreadRenderPass[thread_id];
}

void ThreadSafeRenderingExecutionDescriptor::SetCurrentRenderPass(REDRenderPass* renderPass)
{
    std::unique_lock lock(mCurrentRenderPassMutex);
    auto thread_id = std::hash<std::thread::id>{}(std::this_thread::get_id());

    mThreadRenderPass[thread_id] = renderPass;
}

void ThreadSafeRenderingExecutionDescriptor::EndFrame()
{
    RenderingExecutionDescriptor::EndFrame();
    mThreadRegions.clear();
    mThreadRenderPass.clear();
}

void cross::RenderingExecutionDescriptor::MarkVisitedPass(REDPass* pass)
{
    if (!pass->mVisited)
    {
        // LOG_INFO("Visit pass: {}", pass->mName);
        pass->mVisited = true;
        for (auto predecessor : pass->mPredecessors)
        {
            MarkVisitedPass(predecessor);
        }
    }
}
cross::REDPass* ThreadSafeRenderingExecutionDescriptor::AllocatePass(std::string_view name, bool noCulling)
{
    std::unique_lock lock(mPassMutex);
    return RenderingExecutionDescriptor::AllocatePass(name, noCulling);
}
cross::REDPass* ThreadSafeRenderingExecutionDescriptor::AllocateSubPass(std::string_view name)
{
    std::unique_lock lock(mPassMutex);
    return RenderingExecutionDescriptor::AllocateSubPass(name);
}
cross::REDPass* ThreadSafeRenderingExecutionDescriptor::AllocateFlushStatePass()
{
    std::unique_lock lock(mPassMutex);
    return RenderingExecutionDescriptor::AllocateFlushStatePass();
}
cross::REDTexture* ThreadSafeRenderingExecutionDescriptor::AllocateTexture(std::string_view name, const NGITextureDesc& desc)
{
    std::unique_lock lock(mTextureMutex);
    return RenderingExecutionDescriptor::AllocateTexture(name, desc);
}
cross::REDTexture* ThreadSafeRenderingExecutionDescriptor::AllocateTexture(std::string_view name, NGITexture* externalTexture)
{
    std::unique_lock lock(mTextureMutex);
    return RenderingExecutionDescriptor::AllocateTexture(name, externalTexture);
}
REDTextureView* ThreadSafeRenderingExecutionDescriptor::AllocateTextureView(REDTexture* texture, const NGITextureViewDesc& desc)
{
    std::unique_lock lock(mTextureViewsMutex);
    return RenderingExecutionDescriptor::AllocateTextureView(texture, desc);
}
REDTexture* ThreadSafeRenderingExecutionDescriptor::FindExternalTexture(NGITexture* externalTexture)
{
    std::unique_lock lock(mTextureMutex);
    return RenderingExecutionDescriptor::FindExternalTexture(externalTexture);
}
cross::REDBuffer* ThreadSafeRenderingExecutionDescriptor::AllocateBuffer(std::string_view name, const NGIBufferDesc& desc)
{
    std::unique_lock lock(mBufferMutex);
    return RenderingExecutionDescriptor::AllocateBuffer(name, desc);
}
cross::REDBuffer* ThreadSafeRenderingExecutionDescriptor::AllocateBuffer(std::string_view name, NGIBuffer* externalBuffer)
{
    std::unique_lock lock(mBufferMutex);
    return RenderingExecutionDescriptor::AllocateBuffer(name, externalBuffer);
}
REDBufferView* ThreadSafeRenderingExecutionDescriptor::AllocateBufferView(REDBuffer* buffer, const NGIBufferViewDesc& desc)
{
    std::unique_lock lock(mBufferViewMutex);
    return RenderingExecutionDescriptor::AllocateBufferView(buffer, desc);
}
REDBuffer* ThreadSafeRenderingExecutionDescriptor::FindExternalBuffer(NGIBuffer* externalBuffer)
{
    std::unique_lock lock(mBufferMutex);
    return RenderingExecutionDescriptor::FindExternalBuffer(externalBuffer);
}
cross::REDUniquePtr<cross::REDResidentBuffer> ThreadSafeRenderingExecutionDescriptor::CreateBuffer(std::string_view name, const NGIBufferDesc& desc)
{
    std::unique_lock lock(mResidentBufferMutex);
    return RenderingExecutionDescriptor::CreateBuffer(name, desc);
}
cross::REDUniquePtr<cross::REDResidentTexture> ThreadSafeRenderingExecutionDescriptor::CreateTexture(std::string_view name, const NGITextureDesc& desc)
{
    std::unique_lock lock(mResidentTextureMutex);
    return RenderingExecutionDescriptor::CreateTexture(name, desc);
}
void ThreadSafeRenderingExecutionDescriptor::Destroy(REDResidentObject* object)
{
    std::unique_lock lock(mPendingDestroyObjectMutex);
    RenderingExecutionDescriptor::Destroy(object);
}
REDResourceRangeState* ThreadSafeRenderingExecutionDescriptor::AllocateResourceRangeState(const REDResourceState& state, REDPass* firstPass, REDPass* lastPass)
{
    std::unique_lock lock(mRangeStatePoolMutex);
    return RenderingExecutionDescriptor::AllocateResourceRangeState(state, firstPass, lastPass);
}
REDRenderPass* ThreadSafeRenderingExecutionDescriptor::AllocateRenderPassInfo()
{
    std::unique_lock lock(mRenderPassMutex);
    return RenderingExecutionDescriptor::AllocateRenderPassInfo();
}
bool ThreadSafeRenderingExecutionDescriptor::Validate(REDTexture* texture)
{
    std::unique_lock lock(mTextureMutex);
    return RenderingExecutionDescriptor::Validate(texture);
}
bool ThreadSafeRenderingExecutionDescriptor::Validate(REDBuffer* buffer)
{
    std::unique_lock lock(mBufferMutex);
    return RenderingExecutionDescriptor::Validate(buffer);
}
void ThreadSafeRenderingExecutionDescriptor::QueueBufferUpload(REDBuffer* dstBuffer, UInt32 dstBufferOffset, const void* initialData, UInt32 initialDataSize)
{
    std::unique_lock lock(mBufferUploadMutex);
    RenderingExecutionDescriptor::QueueBufferUpload(dstBuffer, dstBufferOffset, initialData, initialDataSize);
}
void ThreadSafeRenderingExecutionDescriptor::QueueBufferUpload(REDBuffer* dstBuffer, cross::NGIStagingBuffer* srcBuffer, NGICopyBuffer region)
{
    std::unique_lock lock(mBufferUploadMutex);
    RenderingExecutionDescriptor::QueueBufferUpload(dstBuffer, srcBuffer, region);
}
cross::REDRegion* ThreadSafeRenderingExecutionDescriptor::BeginRegion(std::string_view name)
{
    std::unique_lock lock(mRegionMutex);
    return RenderingExecutionDescriptor::BeginRegion(name);
}
}   // namespace cross