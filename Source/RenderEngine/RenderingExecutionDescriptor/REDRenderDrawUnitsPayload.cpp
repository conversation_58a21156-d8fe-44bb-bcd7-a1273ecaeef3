#include "EnginePrefix.h"
#include "REDRenderDrawUnitsPayload.h"
#include "REDDrawUnitList.h"
#include "REDPass.h"
#include "RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "GPUScene/GPUScene.h"

void cross::REDRenderDrawUnitsPayload::OnVisited(REDPass* pass)
{
    mDesc.DrawUnitList->mVisited = true;
}

void cross::REDRenderDrawUnitsPayload::OnAssemble(REDPass* pass)
{
    auto npVisitor = [&](const NameID& name, const PropertySet::NumericProperty& prop) {
        std::visit(Overloaded{[&](auto& val) { mCollapsedNumericPropties.SetProperty(name, val); }, [&](const FrameStdVector<UInt8>& val) { mCollapsedNumericPropties.SetProperty(name, val.data(), val.size()); }}, prop);
    };
    pass->GetContext().VisitProperty(Overloaded{npVisitor, [](auto&&...) {}});

    mOnAssembleTask = threading::Dispatch<threading::ThreadID::TaskThread>({mDesc.DrawUnitList->mTask}, threading::Priority::Low, [=](auto) {
        // Assert(mDesc.DrawUnitList->mVisited);

        QUICK_SCOPED_CPU_TIMING("REDRenderDrawUnitsPayload::OnAssemble");

        auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto* graphicsPipelineStatePool = rdrSys->GetGraphicsPipelineStatePool();

        auto* rawDrawUnits = mDesc.DrawUnitList->GetDrawUnits();
        mGraphicsDrawUnits.resize(rawDrawUnits->GetSize());

        // generate GrahpicsDrawUnits
        constexpr UInt32 BATCH_SIZE = 32u;
        UInt32 drawUnitCount = static_cast<UInt32>(rawDrawUnits->GetSize());
        UInt32 batchCount = math::DivideAndRoundUp(drawUnitCount, BATCH_SIZE);

        threading::ParallelFor<threading::ThreadID::TaskThread>(batchCount, threading::Priority::Low, [&](UInt32 batchIndex) {
            QUICK_SCOPED_CPU_TIMING("REDRenderDrawUnitsPayload::OnAssemble");

            auto [drawUnitStart, drawUnitEnd] = Split2Batches(drawUnitCount, batchCount, batchIndex);

            for (auto drawUnitIndex = drawUnitStart; drawUnitIndex < drawUnitEnd; drawUnitIndex++)
            {
                auto& drawUnit = rawDrawUnits->At(drawUnitIndex);


                auto& graphicsDrawUnit = mGraphicsDrawUnits[drawUnitIndex];

                graphicsDrawUnit.mDrawUnit = &drawUnit;

                if (!drawUnit.mFeedBackVisible)
                {
                    continue;
                }



                auto mtlState = drawUnit.mMaterial->GetMaterialRenderState(mDesc.DrawUnitList->GetDesc().TagName, drawUnit.mObjectProperties, &mCollapsedNumericPropties);

                graphicsDrawUnit.mProgram = mtlState.mProgram;

                // dynamic state
                if (mDesc.DynamicStateDesc)
                {
                    graphicsDrawUnit.mDynamicState = *mDesc.DynamicStateDesc;
                }
                else
                {
                    graphicsDrawUnit.mDynamicState = mtlState.mDynamicState;
                }

                // genreate pipeline state
                {
                    auto* objectCtx = mtlState.mObjCtx;
                    const void* shaderConst = nullptr;
                    if (auto& shaderConstLayout = mtlState.mProgram->ShaderConstantLayout; shaderConstLayout && shaderConstLayout->ByteSize > 0)
                    {
                        shaderConst = mtlState.mShaderConstants;
                        auto objOverride = objectCtx && objectCtx->HasShaderConstant(*mtlState.mProgram->ShaderConstantLayout);
                        auto pasOverride = mCollapsedNumericPropties.HasShaderConstant(*mtlState.mProgram->ShaderConstantLayout);

                        if (objOverride || pasOverride)
                        {
                            // TODO(peterwjma): reuse same temp shader const mem in one thread
                            void* tmpShaderConsts = mFrameAlloc->Allocate<UInt8>(shaderConstLayout->ByteSize, FrameStage::FRAME_STAGE_RENDER);
                            memcpy(tmpShaderConsts, shaderConst, shaderConstLayout->ByteSize);
                            if (objOverride)
                            {
                                objectCtx->FillBuffer(*shaderConstLayout, tmpShaderConsts);
                            }
                            if (pasOverride)
                            {
                                mCollapsedNumericPropties.FillBuffer(*shaderConstLayout, tmpShaderConsts);
                            }
                            shaderConst = tmpShaderConsts;
                        }
                    }

                    auto combinedLayout = drawUnit.mGeometry->GetGeometryPacket()->GetInputLayout();
                    if (mtlState.mProgram->InstanceDataLayout.has_value())
                    {
                        // for ce_InstanceIDOffset
                        auto instanceStreamLayout = mtlState.mProgram->InstanceStreamLayout;
                        instanceStreamLayout.SetVertexStride(0);
                        combinedLayout.AddVertexStreamLayout(instanceStreamLayout);
                    }

                    NGIGraphicsPipelineStateDesc pipelineStateDesc{
                        pass->GetRenderPass(),
                        pass->GetSubpass(),
                        mtlState.mProgram->GUID,
                        &mtlState.mProgram->GraphicsProgramDesc,
                        mtlState.mProgram->PipelineLayout,
                        combinedLayout.GetHash().GetHash(),
                        &combinedLayout,
                        drawUnit.mGeometry->GetPrimitiveType(),
                        mDesc.RasterizationStateDesc ? *mDesc.RasterizationStateDesc : *mtlState.mRaterizationState,
                        mDesc.BlendStateDesc ? *mDesc.BlendStateDesc : *mtlState.mBlendState,
                        mDesc.DepthStencilStateDesc ? *mDesc.DepthStencilStateDesc : *mtlState.mDepthStencilState,
                        shaderConst ? mtlState.mProgram->ShaderConstantLayout->ByteSize : 0,
                        shaderConst,
                    };

                    if (mDesc.OverrideDecalState)
                    {
                        auto& depthStencilState = pipelineStateDesc.DepthStencilState;
                        depthStencilState.EnableStencil = true;
                        depthStencilState.StencilWriteMask |= STENCIL_RECEIVE_DECAL_BIT_MASK;
                        depthStencilState.FrontFace.StencilFailOp = StencilOp::Keep;
                        depthStencilState.FrontFace.StencilDepthFailOp = StencilOp::Keep;
                        depthStencilState.FrontFace.StencilPassOp = StencilOp::Replace;
                        depthStencilState.BackFace.StencilFailOp = StencilOp::Keep;
                        depthStencilState.BackFace.StencilDepthFailOp = StencilOp::Keep;
                        depthStencilState.BackFace.StencilPassOp = StencilOp::Replace;

                        if (EnumHasAnyFlags(drawUnit.mFlags, REDDrawUnitFlag::ReceiveDecal))
                        {
                            graphicsDrawUnit.mDynamicState.StencilReference |= STENCIL_RECEIVE_DECAL_BIT_MASK;
                        }
                        else
                        {
                            graphicsDrawUnit.mDynamicState.StencilReference &= ~STENCIL_RECEIVE_DECAL_BIT_MASK;
                        }
                    }

                    if (EnumHasAnyFlags(drawUnit.mFlags, REDDrawUnitFlag::ReverseFaceOrder))
                    {
                        auto& faceOrder = pipelineStateDesc.RasterizationState.FaceOrder;
                        faceOrder = faceOrder == FaceOrder::CCW ? FaceOrder::CW : FaceOrder::CCW;
                    }

                    graphicsDrawUnit.mPipelineState = graphicsPipelineStatePool->AllocateGraphicsPipelineState(pipelineStateDesc);
                }

                auto& objResGroupLayout = mtlState.mProgram->ResourceGroupLayouts[ShaderParamGroup_Model];
                if (objResGroupLayout.ConstantBufferLayouts.size() || objResGroupLayout.ResourceLayouts.size())
                {
                    auto* ngiObjResGroupLayout = mtlState.mProgram->PipelineLayout->GetDesc().ResourceGroupLayouts[ShaderParamGroup_Model];
                    auto rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
                    auto pool = rdrSys->GetRenderingExecutionDescriptor()->GetREDFrameAllocator();
                    PropertySet objProps{pool, drawUnit.mObjectProperties};
                    // TODO(peterwjma): remove if foliage system was refactored
                    if (drawUnit.mType != RenderNodeType::Foliage)
                    {
                        if (drawUnit.mTypedObjectDataBufferView)
                        {
                            objProps.SetProperty(BuiltInProperty::ce_PerObject, drawUnit.mTypedObjectDataBufferView->mREDView->GetNativeBufferView());
                            if (drawUnit.mTypedPrimitiveDataBufferView)
                            {
                                objProps.SetProperty(BuiltInProperty::ce_PerPrimitive, drawUnit.mTypedPrimitiveDataBufferView->mREDView->GetNativeBufferView());
                            }
                        }
                        else
                        {
                            if (drawUnit.mObjectProperties->GetNumericProperty(BuiltInProperty::ce_World) == nullptr)
                            {
                                objProps.SetProperty(BuiltInProperty::ce_World, drawUnit.mWorldTransform->RelativeMatrix);
                                objProps.SetProperty(BuiltInProperty::ce_PreWorld, drawUnit.mWorldTransform->PreRelativeMatrix);
    #if defined(CE_USE_DOUBLE_TRANSFORM)
                                objProps.SetProperty(BuiltInProperty::ce_TilePosition, drawUnit.mWorldTransform->TilePosition);
                                objProps.SetProperty(BuiltInProperty::ce_PreTilePosition, drawUnit.mWorldTransform->PreTilePosition);
    #endif
                                objProps.SetProperty(BuiltInProperty::ce_InvWorld, drawUnit.mWorldTransform->RelativeMatrix.Inverted());
                                objProps.SetProperty(BuiltInProperty::ce_InvTransposeWorld, drawUnit.mWorldTransform->RelativeMatrix.Inverted().Transpose());
                                objProps.SetProperty(BuiltInProperty::ce_InvTransposeInvWorld, drawUnit.mWorldTransform->RelativeMatrix.Inverted().Transpose().Inverted());
                            }
                                objProps.SetProperty(BuiltInProperty::ce_LODIndex, drawUnit.mLODIndex);
                        }
                    }
                        graphicsDrawUnit.mResourceGroupOBJ = objProps.GetResourceGroup(objResGroupLayout, ngiObjResGroupLayout);
                }

                graphicsDrawUnit.mResourceGroupMTL = mtlState.GetResourceBinding();
            }
        });
    });
}

void cross::REDRenderDrawUnitsPayload::OnCompile(REDPass* pass)
{
    Assert(mDesc.DrawUnitList->mVisited);

    // QUICK_SCOPED_CPU_TIMING_DYNAMIC(std::string(pass->GetName()));

    auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* graphicsPipelineStatePool = rdrSys->GetGraphicsPipelineStatePool();

    auto* fa = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameAllocator();

    auto& rawDrawUnits = mDesc.DrawUnitList->GetModifiableDrawUnits();

    // generate GrahpicsDrawUnits
    {
        UInt32 drawUnitCount = static_cast<UInt32>(rawDrawUnits.size());
        UInt32 batchSize = 32u;
        UInt32 batchCount = (drawUnitCount + batchSize - 1) / batchSize;

        threading::ParallelFor(batchCount, [=](UInt32 batchIndex) {
            QUICK_SCOPED_CPU_TIMING("REDRenderDrawUnitsPayload::OnCompile");

            UInt32 drawUnitStart = batchIndex * batchSize;
            UInt32 drawUnitEnd = std::min((batchIndex + 1) * batchSize, drawUnitCount);

            for (UInt32 drawUnitIndex = drawUnitStart; drawUnitIndex < drawUnitEnd; drawUnitIndex++)
            {
                auto& drawUnit = rawDrawUnits[drawUnitIndex];

                if (!drawUnit.mFeedBackVisible)
                {
                    continue;
                }

                auto& graphicsDrawUnit = mGraphicsDrawUnits[drawUnitIndex];

                const resource::Shader::ProgramDesc* lastProgram = nullptr;
                NGIResourceGroup* lastPassResGroup = nullptr;
                if (drawUnitIndex == drawUnitStart || lastProgram != graphicsDrawUnit.mProgram)
                {
                    graphicsDrawUnit.mResourceGroupPAS =
                        pass->GetContext().GetResourceGroup(graphicsDrawUnit.mProgram->ResourceGroupLayouts[ShaderParamGroup_Pass], graphicsDrawUnit.mProgram->PipelineLayout->GetDesc().ResourceGroupLayouts[ShaderParamGroup_Pass]);

                    lastProgram = graphicsDrawUnit.mProgram;
                    lastPassResGroup = graphicsDrawUnit.mResourceGroupPAS;
                }
                else
                {
                    graphicsDrawUnit.mResourceGroupPAS = lastPassResGroup;
                }
            }
        });
    }
}

void cross::REDRenderDrawUnitsPayload::OnExecute(REDPass* pass, NGICommandList* cmdList) const
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);

    Assert(mDesc.DrawUnitList->mVisited);

    if (mGraphicsDrawUnits.empty())
    {
        return;
    }

    NGIBuffer* objectIndexOffsetsBuffer = nullptr;
    UInt64 objectIndexOffsetsBufferOffset = 0;
    if (auto customInstanceIDOffsetsBuffer = mDesc.DrawUnitList->GetObjectIndexOffsetsBuffer<NGIBuffer*>(); customInstanceIDOffsetsBuffer)
    {
        objectIndexOffsetsBuffer = customInstanceIDOffsetsBuffer;
    }
    else
    {
        objectIndexOffsetsBuffer = mDesc.DrawUnitList->mObjectIndexOffsetsBuffer;
        objectIndexOffsetsBufferOffset = mDesc.DrawUnitList->mObjectIndexOffsetsBufferOffset;
    }

    auto drawUnitsCount = static_cast<UInt32>(mGraphicsDrawUnits.size());
    constexpr UInt32 desiredBatchSize = 64;

    const auto numWorkers = threading::TaskSystem::GetNumWorkerThreadsForTask();
    UInt32 numTasks = (drawUnitsCount + (desiredBatchSize - 1)) / desiredBatchSize;
    UInt32 batchSize = desiredBatchSize;
    UInt32 remainder = 0;
    if (static_cast<UInt32>(numWorkers) < numTasks)
    {
        numTasks = static_cast<UInt32>(numWorkers);
        batchSize = drawUnitsCount / static_cast<UInt32>(numWorkers);
        remainder = drawUnitsCount % static_cast<UInt32>(numWorkers);
    }

    const auto width = pass->mRenderPass->mFramebuffer->GetDesc().Width;
    const auto height = pass->mRenderPass->mFramebuffer->GetDesc().Height;
    const auto viewport = mDesc.InitViewport.value_or(std::array<NGIViewport, 16>{NGIViewport{0, 0, static_cast<float>(width), static_cast<float>(height), 0, 1}});
    const auto scissor = mDesc.InitScissor.value_or(std::array<NGIScissor, 16>{NGIScissor{0, 0, width, height}});
    const auto it = std::find_if(viewport.begin(), viewport.end(), [](const NGIViewport& v) { return v.width < 1 || v.height < 1; });
    const UInt32 viewportNum = static_cast<UInt32>(std::distance(viewport.begin(), it));

    auto RecordDrawCommands = [&](NGIBundleCommandList* cmdList, UInt32 offset, UInt32 count) {
        SCOPED_CPU_TIMING(GroupRendering, "RecordDrawCommands");

        for (UInt32 j = offset; j < offset + count; j++)
        {
            const auto& graphicsDrawUnit = mGraphicsDrawUnits[j];
            const auto& drawUnit = *graphicsDrawUnit.mDrawUnit;

            if (!drawUnit.mFeedBackVisible)
            {
                continue;
            }

            cmdList->SetGraphicsPipelineState(graphicsDrawUnit.mPipelineState);
            if (graphicsDrawUnit.mResourceGroupPAS)
            {
                cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Pass, graphicsDrawUnit.mResourceGroupPAS);
            }
            if (graphicsDrawUnit.mResourceGroupMTL)
            {
                cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Material, graphicsDrawUnit.mResourceGroupMTL);
            }
            if (graphicsDrawUnit.mResourceGroupOBJ)
            {
                cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Model, graphicsDrawUnit.mResourceGroupOBJ);
            }

            auto& geometry = drawUnit.mGeometry;
            auto* geometryPacket = geometry->GetGeometryPacket();

            NGIBuffer* vertexBuffers[MaxVertexStreams]{};
            UInt64 vertexBufferOffsets[MaxVertexStreams]{};
            UInt32 vertexBufferCount = geometryPacket->GetStreamCount();

            for (UInt8 i = 0; i < geometryPacket->GetStreamCount(); ++i)
            {
                auto stream = geometryPacket->GetVertexStream(i);
                vertexBuffers[i] = stream->GetGpuBuffer();
                vertexBufferOffsets[i] = stream->GetStreamOffset();
            }
            if (graphicsDrawUnit.mProgram->InstanceDataLayout)
            {
                vertexBuffers[geometryPacket->GetStreamCount()] = objectIndexOffsetsBuffer;
                vertexBufferOffsets[geometryPacket->GetStreamCount()] = objectIndexOffsetsBufferOffset + j * sizeof(UInt32);
                ++vertexBufferCount;
            }

            cmdList->SetVertexBuffers(vertexBufferCount, vertexBuffers, vertexBufferOffsets);

            if (graphicsDrawUnit.mPipelineState->GetDesc().DepthStencilState.EnableStencil)
            {
                cmdList->SetStencilRef(graphicsDrawUnit.mDynamicState.StencilReference);
            }

            if (auto indexBuffer = geometryPacket->GetIndexStream()->GetGpuBuffer(); indexBuffer)
            {
                UInt64 indexOffset = geometryPacket->GetIndexStream()->GetStreamOffset();
                auto indexFormat = geometryPacket->GetIndexFormat();

                cmdList->SetIndexBuffer(indexBuffer, indexOffset, indexFormat);

                if (auto indirectBuffer = mDesc.DrawUnitList->GetIndirectBuffer<NGIBuffer*>(); indirectBuffer && drawUnit.mIndirectCount)
                {
                    if (auto countBuffer = mDesc.DrawUnitList->GetCountBuffer<NGIBuffer*>(); countBuffer && drawUnit.mMaxIndirectDrawCount)
                    {
                        cmdList->DrawIndexedIndirectCount(indirectBuffer, drawUnit.mIndirectBufferOffset, countBuffer, drawUnit.mCountBufferOffset, drawUnit.mMaxIndirectDrawCount, drawUnit.mIndirectStride);
                    }
                    else
                    {
                        cmdList->DrawIndexedIndirect(indirectBuffer, drawUnit.mIndirectBufferOffset, drawUnit.mIndirectCount, drawUnit.mIndirectStride);
                    }
                }
                else
                {
                    cmdList->DrawIndexedInstanced(geometry->GetIndexCount(), drawUnit.mInstanceCount, geometry->GetIndexStart(), geometry->GetVertexStart(), 0);
                }
            }
            else
            {
                if (auto indirectBuffer = mDesc.DrawUnitList->GetIndirectBuffer<NGIBuffer*>(); indirectBuffer && drawUnit.mIndirectCount)
                {
                    if (auto countBuffer = mDesc.DrawUnitList->GetCountBuffer<NGIBuffer*>(); countBuffer && drawUnit.mMaxIndirectDrawCount)
                    {
                        Assert(false);
                    }
                    else
                    {
                        cmdList->DrawIndirect(indirectBuffer, drawUnit.mIndirectBufferOffset, drawUnit.mIndirectCount, drawUnit.mIndirectStride);
                    }
                }
                else
                {
                    cmdList->DrawInstanced(geometry->GetIndexCount(), drawUnit.mInstanceCount, geometry->GetVertexStart(), 0);
                }
            }
        }
    };

    std::vector<NGIBundleCommandList*> bundleCmdLists;
    bundleCmdLists.resize(numTasks);
    cmdList->GetCommandQueue()->AllocateBundleCommandLists(numTasks, bundleCmdLists.data());
    cmdList->ForkBundleCommandLists(numTasks, bundleCmdLists.data());

    threading::ParallelFor(numTasks, [&](UInt32 taskIndex) {
        NGIBundleCommandList* bundleCmdList = bundleCmdLists[taskIndex];

        UInt32 prevRemainderCount = std::min(remainder, taskIndex);
        UInt32 drawUnitStart = taskIndex * batchSize + prevRemainderCount;
        UInt32 drawUnitEnd = std::min(drawUnitStart + batchSize + (taskIndex < remainder ? 1u : 0u), drawUnitsCount);
        UInt32 drawUnitCount = drawUnitEnd - drawUnitStart;

        bundleCmdList->Begin();
        bundleCmdList->SetViewports(viewportNum, viewport.data());
        bundleCmdList->SetScissors(viewportNum, scissor.data());
        RecordDrawCommands(bundleCmdList, drawUnitStart, drawUnitCount);
        bundleCmdList->End();
    });
}