#include "EnginePrefix.h"
#include "REDPass.h"

#include "REDRenderStellarMeshPayload.h"
#include "RenderingExecutionDescriptor.h"
#include "NativeGraphicsInterface/NGIUtils.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDResourceManager.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDRenderDrawUnitsPayload.h"

/*
额外的read write接口，用于指定shader resource, unordered access, copy src/dst, resolve src/dst等
name：资源在shader中的名字
view：资源本身
readState：资源被用到的状态
*/

cross::REDPass::REDPass(std::string_view name, bool noCulling, REDRegion* region)
    : mName{name}
    , mNoCulling{noCulling}
    , mRegion{region}
{
    auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto pool = rdrSys->GetRenderingExecutionDescriptor()->GetREDFrameAllocator();
    mTextureReferences = std::move(CEFrameHashMap<REDTextureView*, REDResourceState>(pool));
    mBufferReferences = std::move(CEFrameHashMap<REDBuffer*, REDResourceState>(pool));
    mTextures = std::move(CEFrameHashMap<REDTextureSubresource, REDSubresourceInfo>(pool));
    mBuffers = std::move(CEFrameHashMap<REDBuffer*, REDSubresourceInfo>(pool));
    mPayloads = std::move(FrameStdVector<std::shared_ptr<REDPayload>>(pool));
    mContext = std::move(PropertySet(pool, region ? &region->PropertySet : nullptr));
    mPredecessors = std::move(FrameStdSet<REDPass*>(pool));
    mTextureBarriers = std::move(FrameStdVector<NGITextureBarrier>(pool));
    mBufferBarriers = std::move(FrameStdVector<NGIBufferBarrier>(pool));
    mInputTargets = std::move(FrameStdVector<NGIRenderPassTargetIndex>(pool));
}

cross::REDRenderPass::REDRenderPass() 
{
    auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto pool = rdrSys->GetRenderingExecutionDescriptor()->GetREDFrameAllocator();
    mColorTargetDescs = std::move(FrameStdVector<REDColorTargetDesc>(pool));
    mClearValues = std::move(FrameStdVector<NGIClearValue>(pool));
    mSubpasses = std::move(FrameStdVector<REDPass*>(pool));
    mTargets = std::move(FrameStdMap<REDTextureSubresource, REDResourceRangeState*>(pool));
    mTargetBarriers = std::move(FrameStdVector<NGITextureBarrier>(pool));
}

void cross::REDRenderPass::AllocateNativeRenderPasss(PersistentResourceManager* resMgr)
{
    NGIRenderPassDesc rpDesc{};

    auto Initialize = [&](const auto& targetDesc)
        {
            auto& desc1 = targetDesc.Target->mTexture->mDesc;
            rpDesc.SampleCount = desc1.SampleCount;
            auto mipLevel = targetDesc.Target->mDesc.SubRange.MostDetailedMip;

            // check color targets and color resolve targets
            if (!std::all_of(mColorTargetDescs.begin(), mColorTargetDescs.end(), [&](auto& colorTarget)
                {
                    auto& desc2 = colorTarget.Target->mTexture->mDesc;
                    if (EnumHasAnyFlags(colorTarget.StoreOp, NGIStoreOp::Resolve))
                    {
                        auto& desc3 = colorTarget.ResolveTarget->mTexture->mDesc;
                        if (desc1.Width != desc3.Width || desc1.Height != desc3.Height || colorTarget.ResolveTarget->mDesc.SubRange.ArraySize != targetDesc.Target->mDesc.SubRange.ArraySize)
                        {
                            LOG_ERROR("{}: color targets or color resolve targets or depth target not share the same size or sample count", mName);
                            return false;
                        }
                    }

                    if (desc2.SampleCount == desc1.SampleCount && desc2.Width == desc1.Width && desc2.Height == desc1.Height && colorTarget.Target->mDesc.SubRange.ArraySize == targetDesc.Target->mDesc.SubRange.ArraySize)
                    {
                        return true;
                    }
                    else
                    {
                        LOG_ERROR("{}: color targets or color resolve targets or depth target not share the same size or sample count", mName);
                        return false;
                    }
                }))
            {
                LOG_ERROR("{} Not all color targets or color resolve targets share the same size or sample count", mName);
            }

            // check depth/stencil resolve target
            if (mDepthStencilTargetDesc)
            {
                const auto& depthStencilTargetDesc = *(mDepthStencilTargetDesc);
                if (EnumHasAnyFlags(depthStencilTargetDesc.DepthStoreOp, NGIStoreOp::Resolve) || EnumHasAnyFlags(depthStencilTargetDesc.StencilStoreOp, NGIStoreOp::Resolve))
                {
                    auto& desc3 = depthStencilTargetDesc.ResolveTarget->mTexture->mDesc;
                    if (desc1.Width != desc3.Width || desc1.Height != desc3.Height || depthStencilTargetDesc.ResolveTarget->mDesc.SubRange.ArraySize != targetDesc.Target->mDesc.SubRange.ArraySize)
                    {
                        LOG_ERROR("Depth/stencil resolve target doesn't share the same size");
                    }
                }
            }
        };

    if (mDepthStencilTargetDesc)
    {
        Initialize(*(mDepthStencilTargetDesc));
    }
    else
    {
        if (!mColorTargetDescs.empty())
        {
            Initialize(mColorTargetDescs.front());
        }
        else
        {
            rpDesc.SampleCount = mSampleCount;
        }
    }

    for (auto& colorTarget : mColorTargetDescs)
    {
        rpDesc.ColorTargets[rpDesc.ColorTargetCount] = {
            colorTarget.Target->mDesc.Format,
            colorTarget.LoadOp,
            colorTarget.StoreOp,
        };
        ++rpDesc.ColorTargetCount;
    }
    if (rpDesc.HasDepthStencil = mDepthStencilTargetDesc.has_value(); rpDesc.HasDepthStencil)
    {
        const auto& depthStencilTarget = *(mDepthStencilTargetDesc);
        rpDesc.DepthStencilFormat = depthStencilTarget.Target->mDesc.Format;
        rpDesc.DepthLoadOp = depthStencilTarget.DepthLoadOp;
        rpDesc.DepthStoreOp = depthStencilTarget.DepthStoreOp;
        rpDesc.DepthResolveType = depthStencilTarget.DepthResolveType;
        rpDesc.StencilLoadOp = depthStencilTarget.StencilLoadOp;
        rpDesc.StencilStoreOp = depthStencilTarget.StencilStoreOp;
        rpDesc.StencilResolveType = depthStencilTarget.StencilResolveType;
    }

    for (auto* subpass : mSubpasses)
    {
        if (subpass != mBeginPass && subpass != mEndPass && subpass->mVisited)
        {
            auto& dstSubpass = rpDesc.Subpasses[rpDesc.SubpassCount];
            std::copy(subpass->mInputTargets.begin(), subpass->mInputTargets.end(), dstSubpass.InputAttachments);
            std::copy(subpass->mColorTargets.begin(), subpass->mColorTargets.end(), dstSubpass.ColorAttachments);
            dstSubpass.InputAttachmentCount = static_cast<UInt32>(subpass->mInputTargets.size());
            dstSubpass.ColorAttachmentCount = static_cast<UInt32>(subpass->mColorTargets.size());
            dstSubpass.NeedDepthStencil = EnumHasAnyFlags(subpass->mFlags, REDPassFlagBit::NeedDepth | REDPassFlagBit::NeedStencil);
            dstSubpass.DepthReadOnly = EnumHasAnyFlags(subpass->mFlags, REDPassFlagBit::DepthReadOnly);
            dstSubpass.StencilReadOnly = EnumHasAnyFlags(subpass->mFlags, REDPassFlagBit::StencilReadOnly);
            subpass->mSubpass = rpDesc.SubpassCount;
            ++rpDesc.SubpassCount;
        }
    }

    mRenderPass = resMgr->AllocateRenderPass(rpDesc);
}

void cross::REDRenderPass::AllocateNativeFramebuffer(NGIResourceManager* resMgr)
{
    NGIFramebufferDesc fbDesc{ mRenderPass };

    auto Initialize = [&](const auto& targetDesc)
    {
        auto& desc1 = targetDesc.Target->mTexture->mDesc;
        auto mipLevel = targetDesc.Target->mDesc.SubRange.MostDetailedMip;
        fbDesc.Width = (std::max)(desc1.Width >> mipLevel, 1u);
        fbDesc.Height = (std::max)(desc1.Height >> mipLevel, 1u);
        fbDesc.LayerCount = targetDesc.Target->mDesc.SubRange.ArraySize;
    };

    if (mDepthStencilTargetDesc)
    {
        Initialize(*(mDepthStencilTargetDesc));
    }
    else
    {
        if (!mColorTargetDescs.empty())
        {
            Initialize(mColorTargetDescs.front());
        }
        else
        {
            fbDesc.Width = mWidth;
            fbDesc.Height = mHeight;
            fbDesc.LayerCount = mLayerCount;
        }
    }

    UInt32 colotTragetIndex = 0;
    for (auto& colorTarget : mColorTargetDescs)
    {
        fbDesc.ColorTargets[colotTragetIndex++] = {
            colorTarget.Target->GetNativeTextureView(),
            EnumHasAnyFlags(colorTarget.StoreOp, NGIStoreOp::Resolve) ? colorTarget.ResolveTarget->GetNativeTextureView() : nullptr,
        };
        mClearValues.emplace_back(colorTarget.ClearValue);
    }

    if (mDepthStencilTargetDesc)
    {
        const auto& depthStencilTarget = *(mDepthStencilTargetDesc);
        fbDesc.DepthStencilTarget = {
            depthStencilTarget.Target->GetNativeTextureView(),
            EnumHasAnyFlags(depthStencilTarget.DepthStoreOp, NGIStoreOp::Resolve) || EnumHasAnyFlags(depthStencilTarget.StencilStoreOp, NGIStoreOp::Resolve) ? depthStencilTarget.ResolveTarget->GetNativeTextureView() : nullptr,
        };
        mClearValues.emplace_back(depthStencilTarget.ClearValue);
    }

    mFramebuffer = resMgr->AllocateFramebuffer(fbDesc);
}

cross::REDPass::~REDPass() 
{
}

void cross::REDPass::AddTextureReference(REDTextureView* view, const REDResourceState& state)
{
    mTextureReferences[view] |= state;
}

void cross::REDPass::AddBufferReference(REDBuffer* buffer, const REDResourceState& state)
{
    mBufferReferences[buffer] |= state;
}

void cross::REDPass::AddPassResourceReferences(NGIPipelineLayout* layout) 
{
    auto& layoutDesc = layout->GetDesc().ResourceGroupLayouts[ShaderParamGroup_Pass]->GetDesc();
    AddResourceReferencesFromProperties(layoutDesc);
}

void cross::REDPass::AddResourceReferencesFromProperties(const NGIResourceGroupLayoutDesc& layoutDesc)
{
    using namespace CrossSchema;

    std::for_each_n(layoutDesc.Resources, layoutDesc.ResourceCount, [&](NGIResourceDesc& resDesc) {
        for (UInt32 arrayIndex = 0; arrayIndex < resDesc.ArraySize; ++arrayIndex)
        {
            switch (GetResourceBindingType(resDesc.Type))
            {
            case NGIResourceBindingType::Texture:
            {
                if (auto* texView = mContext.GetResourceProperty<REDTextureView*>(resDesc.ID, arrayIndex); texView)
                {
                    REDResourceState state{MapStateBit(resDesc.Type) | MapStateStageBit(resDesc.StageMask)};
                    AddTextureReference(texView, state);
                }
                break;
            }
            case NGIResourceBindingType::Buffer:
            {
                if (auto* bufView = mContext.GetResourceProperty<REDBufferView*>(resDesc.ID, arrayIndex); bufView)
                {
                    AssertMsg(bufView->mDesc.BufferLocation == 0 && bufView->mDesc.SizeInBytes == bufView->mBuffer->mDesc.Size, "TODO: add finer-grained check");
                    REDResourceState state{ MapStateBit(resDesc.Type) | MapStateStageBit(resDesc.StageMask) };
                    AddBufferReference(bufView->mBuffer, state);
                }
                break;
            }
            default:
                break;
            }
        }
    });
}

void cross::REDPass::SetProperty(const NameID& ID, UInt32 num, REDBufferView** bufViews, NGIResourceState* states)
{
    if (IsGraphicsPass())
    {
        for (UInt32 i = 0; i < num; ++i)
        {
            auto* bufView = bufViews[i];
            if (!bufView)
            {
                continue;
            }

            auto state = NGIResourceState::Undefined;

            if (states && states[i] != NGIResourceState::Undefined)
            {
                state = states[i];
            }
            else
            {
                UInt32 nUsageGroup = 0;

                constexpr auto visibleSRVBufferUsages =
                    NGIBufferUsage::ByteAddressBuffer |
                    NGIBufferUsage::TexelBuffer |
                    NGIBufferUsage::StructuredBuffer |
                    NGIBufferUsage::TextureBuffer;
                if (EnumHasAnyFlags(bufView->mDesc.Usage, visibleSRVBufferUsages))
                {
                    nUsageGroup++;
                    state |= NGIResourceState::ShaderResourceBit;
                }

                constexpr auto visibleUAVBufferUsage =
                    NGIBufferUsage::RWTexelBuffer |
                    NGIBufferUsage::RWStructuredBuffer |
                    NGIBufferUsage::RWByteAddressBuffer;
                if (EnumHasAnyFlags(bufView->mDesc.Usage, visibleUAVBufferUsage))
                {
                    nUsageGroup++;
                    state |= NGIResourceState::UnorderedAccessBit;
                }

                if (EnumHasAnyFlags(bufView->mDesc.Usage, NGIBufferUsage::ConstantBuffer))
                {
                    nUsageGroup++;
                    state |= NGIResourceState::ConstantBufferBit;
                }

                if (nUsageGroup >= 2)
                {
                    LOG_EDITOR_ERROR("Can't tell usage from buffer view, please specify addtional resource state.");
                    Assert(false);
                }
            }

            AddBufferReference(bufView->mBuffer, REDResourceState{ state | NGIResourceState::GraphicsShaderStageBitMask });
        }
    }
    else
    {
        //auto redundantState = states && std::any_of(states, states + num, [](auto state) { return state != NGIResourceState::Undefined; });
        //AssertMsg(!redundantState, "Non graphics pass must not specify state manually");
    }
    mContext.SetProperty(ID, num, bufViews);
}

void cross::REDPass::SetProperty(const NameID& ID, UInt32 num, REDTextureView** texViews, NGIResourceState* states)
{
    if (IsGraphicsPass())
    {
        auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto pool = rdrSys->GetRenderingExecutionDescriptor()->GetREDFrameAllocator();
        cross::PropertySet::ResourcePropVector props(pool);
        props.mData.reserve(num);

        for (UInt32 i = 0; i < num; ++i)
        {
            auto* texView = texViews[i];
            if (!texView)
            {
                continue;
            }

            auto state = NGIResourceState::Undefined;   

            if (states && states[i] != NGIResourceState::Undefined)
            {
                state = states[i];
            }
            else if (EnumHasAllFlags(texView->mDesc.Usage, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess))
            {
                // we comment this check and assertion because quite lot of shader use RWxxx in different read and write cases
                // so we must handle this case and avoid validation error
                //LOG_EDITOR_ERROR("Can't tell usage from texture view, please specify addtional resource state.");
                //Assert(false);
                state = NGIResourceState::GraphicsShaderStageBitMask | NGIResourceState::UnorderedAccessBit;

            }
            else if (EnumHasAnyFlags(texView->mDesc.Usage, NGITextureUsage::ShaderResource))
            {
                state = NGIResourceState::GraphicsShaderStageBitMask | NGIResourceState::ShaderResourceBit;
            }
            else if (EnumHasAnyFlags(texView->mDesc.Usage, NGITextureUsage::UnorderedAccess))
            {
                state = NGIResourceState::GraphicsShaderStageBitMask | NGIResourceState::UnorderedAccessBit;
            }
            else
            {
                Assert(false);
            }

            NGIResourceBindingFlags flags{ 0 };

            if (EnumHasAnyFlags(state, NGIResourceState::ShaderResourceBit) &&
                mRenderPass->mDepthStencilTargetDesc &&
                mRenderPass->mDepthStencilTargetDesc->Target->mTexture == texView->mTexture)
            {
                auto depthMutiUsage = EnumHasAnyFlags(texView->mDesc.SubRange.Aspect, NGITextureAspect::Depth) &&
                    EnumHasAnyFlags(mFlags, REDPassFlagBit::NeedDepth);
                auto stencilMutiUsage = EnumHasAnyFlags(texView->mDesc.SubRange.Aspect, NGITextureAspect::Stencil) &&
                    EnumHasAnyFlags(mFlags, REDPassFlagBit::NeedStencil);
                if (depthMutiUsage || stencilMutiUsage)
                {
                    // use texture as depth stencil attachment and shader resource at same time
                    // TODO(peterwjma): need more subtle checking

                    if (depthMutiUsage && !EnumHasAnyFlags(mFlags, REDPassFlagBit::DepthReadOnly))
                    {
                        LOG_EDITOR_ERROR("If depth aspect of texture used both as depth attachment and shader resoruce, then depth attachment must be readonly");
                        Assert(false);
                    }
                    if (stencilMutiUsage && !EnumHasAnyFlags(mFlags, REDPassFlagBit::StencilReadOnly))
                    {
                        LOG_EDITOR_ERROR("If stencil aspect of texture used both as stencil attachment and shader resoruce, then stencil attachment must be readonly");
                        Assert(false);
                    }

                    flags |= NGIResourceBindingFlags::RenderPassTargetSimultaneously;
                }
            }

            props.mData.push_back({ texView, flags });

            // state of texture which used both as attachment and shader resource should be tracked automatically
            // so don't add reference here
            if (!EnumHasAnyFlags(flags, NGIResourceBindingFlags::RenderPassTargetSimultaneously))
            {
                AddTextureReference(texView, REDResourceState{ state });
            }
        }

        mContext.SetProperty(ID, std::move(props));
    }
    else
    {
        //auto redundantState = states && std::any_of(states, states + num, [](auto state) { return state != NGIResourceState::Undefined; });
        //AssertMsg(!redundantState, "Non graphics pass must not specify state manually");
        mContext.SetProperty(ID, num, texViews);
    }
}

bool cross::REDPass::IsVisited() const
{
    if (mRenderPass)
    {
        return mRenderPass->mOuterPass->mVisited && mVisited;
    }
    else
    {
        return mVisited;
    }
}

cross::REDComputeExecutionPayload* cross::REDPass::Dispatch(ComputeShaderR* computeShader, const NameID& kernel, UInt32 threadGroupCountX, UInt32 threadGroupCountY, UInt32 threadGroupCountZ)
{
    auto [pso, programDesc] = computeShader->GetPipelineState(mContext, kernel);
    AddPassResourceReferences(pso->GetDesc().PipelineLayout);

    auto payload = std::make_shared<REDComputeExecutionPayload>(programDesc, pso, threadGroupCountX, threadGroupCountY, threadGroupCountZ);
    mPayloads.emplace_back(payload);
    return payload.get();
}

cross::REDComputeExecutionPayload* cross::REDPass::Dispatch(ComputeShaderR* computeShader, const NameID& kernel, UInt3 threadGroupCount)
{
    return Dispatch(computeShader, kernel, threadGroupCount.x, threadGroupCount.y, threadGroupCount.z);
}

cross::REDRayTracingExecutionPayload* cross::REDPass::DispatchRays(RayTracingShaderR* rayTracingShader, UInt32 width, UInt32 height, UInt32 depth)
{
    auto [pso, programDesc] = rayTracingShader->GetPipelineState(mContext);
    AddPassResourceReferences(pso->GetDesc().PipelineLayout);

    auto payload = std::make_shared<REDRayTracingExecutionPayload>(programDesc, pso, width, height, depth);
    mPayloads.emplace_back(payload);
    
    return payload.get();
}

void cross::REDPass::DispatchIndirect(ComputeShaderR* computeShader, const NameID& kernel, REDBuffer* argumentBuffer, UInt32 offset)
{
    auto [pso, programDesc] = computeShader->GetPipelineState(mContext, kernel);
    AddPassResourceReferences(pso->GetDesc().PipelineLayout);
    AddBufferReference(argumentBuffer, REDResourceState{NGIResourceState::IndirectArgument});

    auto payload = std::make_shared<REDComputeExecutionPayload>(programDesc, pso, argumentBuffer, offset);
    mPayloads.emplace_back(payload);
}

void cross::REDPass::RenderDrawUnits(const REDRenderDrawUnitsDesc& desc)
{
    mPayloads.emplace_back(std::make_shared<REDRenderDrawUnitsPayload>(desc));
    if (auto indirectBufferRED = desc.DrawUnitList->GetIndirectBuffer<REDBuffer*>(); indirectBufferRED)
    {
        AddBufferReference(indirectBufferRED, REDResourceState{ NGIResourceState::IndirectArgument });
    }
    if (auto countBufferRED = desc.DrawUnitList->GetCountBuffer<REDBuffer*>(); countBufferRED)
    {
        AddBufferReference(countBufferRED, REDResourceState{ NGIResourceState::IndirectArgument });
    }
    if (auto instanceIDOffsetsBufferRED = desc.DrawUnitList->GetObjectIndexOffsetsBuffer<REDBuffer*>(); instanceIDOffsetsBufferRED)
    {
        AddBufferReference(instanceIDOffsetsBufferRED, REDResourceState{ NGIResourceState::VertexBuffer });
    }
}

void cross::REDPass::RenderStellarMesh(const REDRenderStellarMeshDesc& desc)
{
    mPayloads.emplace_back(std::make_shared<REDRenderStellarMeshPayload>(desc));
    Assert(desc.IndirectBuffer);
    AddBufferReference(desc.IndirectBuffer, REDResourceState{NGIResourceState::IndirectArgument});
}

void cross::REDPass::CopyBufferToBuffer(REDBuffer* dstBuffer, BufferType srcBuffer, UInt32 regionCount, const NGICopyBuffer* regions)
{
    mBuffers[dstBuffer].State |= REDResourceState{NGIResourceState::CopyDst};

    std::visit(Overloaded{
        [&](REDBuffer* buffer)
        {
            mBuffers[buffer].State |= REDResourceState{NGIResourceState::CopySrc};
        }, [](NGIBuffer* buffer) {}
        }, srcBuffer);

    Execute([=, regionsCopy = std::vector<NGICopyBuffer>{regions, regions + regionCount}](REDPass* pass, NGICommandList* cmdList) 
    {
        NGIBuffer* srcNGIBuffer = nullptr;

        std::visit(Overloaded{
            [&](REDBuffer* buffer)
            {
                srcNGIBuffer = buffer->mNativeBuffer;
            },
            [&](NGIBuffer* buffer)
            {
                srcNGIBuffer = buffer;
            }
            }, srcBuffer);

        cmdList->CopyBufferToBuffer(dstBuffer->mNativeBuffer, srcNGIBuffer, regionCount, regionsCopy.data());
    });
}

void cross::REDPass::CopyBufferToTexture(REDTexture* dstTexture, BufferType srcBuffer, UInt32 regionCount, const NGICopyBufferTexture* regions)
{
    for (UInt32 i = 0; i < regionCount; ++i)
    {
        auto& region = regions[i];
        mTextures[{dstTexture, region.TextureSubresource}].State |= REDResourceState{NGIResourceState::CopyDst};
    }
    
    std::visit(Overloaded{
        [&](REDBuffer* buffer)
        {
            mBuffers[buffer].State |= REDResourceState{NGIResourceState::CopySrc};
        }, [](NGIBuffer* buffer) {}
        }, srcBuffer);

    Execute([=, regionsCopy = std::vector<NGICopyBufferTexture>{regions, regions + regionCount}](REDPass* pass, NGICommandList* cmdList) 
    {
        NGIBuffer* srcNGIBuffer = nullptr;

        std::visit(Overloaded{
            [&](REDBuffer* buffer)
            {
                srcNGIBuffer = buffer->mNativeBuffer;
            },
            [&](NGIBuffer* buffer)
            {
                srcNGIBuffer = buffer;
            }
            }, srcBuffer);

        cmdList->CopyBufferToTexture(dstTexture->mNativeTexture, srcNGIBuffer, regionCount, regionsCopy.data());
    });
}

void cross::REDPass::CopyTextureToBuffer(REDBuffer* dstBuffer, REDTexture* srcTexture, UInt32 regionCount, const NGICopyBufferTexture* regions)
{
    mBuffers[dstBuffer].State |= REDResourceState{NGIResourceState::CopyDst};
    for (UInt32 i = 0; i < regionCount; ++i)
    {
        auto& region = regions[i];
        mTextures[{srcTexture, region.TextureSubresource}].State |= REDResourceState{NGIResourceState::CopySrc};
    }
    Execute([=, regionsCopy = std::vector<NGICopyBufferTexture>{regions, regions + regionCount}](REDPass* pass, NGICommandList* cmdList) {
        cmdList->CopyTextureToBuffer(dstBuffer->mNativeBuffer, srcTexture->mNativeTexture, regionCount, regionsCopy.data());
    });
}

void cross::REDPass::CopyTextureToTexture(REDTexture* dstTexture, REDTexture* srcTexture, UInt32 regionCount, const NGICopyTexture* regions)
{
    for (UInt32 i = 0; i < regionCount; ++i)
    {
        auto& region = regions[i];
        mTextures[{dstTexture, region.DstSubresource}].State |= REDResourceState{NGIResourceState::CopyDst};
        mTextures[{srcTexture, region.SrcSubresource}].State |= REDResourceState{NGIResourceState::CopySrc};
    }
    Execute([=, regionsCopy = std::vector<NGICopyTexture>{regions, regions + regionCount}](REDPass* pass, NGICommandList* cmdList) {
        cmdList->CopyTextureToTexture(dstTexture->mNativeTexture, srcTexture->mNativeTexture, regionCount, regionsCopy.data());
    });
}

void cross::REDPass::ClearTexture(REDTextureView* textureView, const NGIClearValue& clearValue)
{
    auto* texture = textureView->mTexture;
    textureView->EnumerateSubresource([=](UInt32 index, UInt32 mip, UInt32 array, UInt32 plane) { mTextures[{texture, index}].State |= REDResourceState{NGIResourceState::CopyDst}; });
    Execute([=](REDPass* pass, NGICommandList* cmdList)
    {
        cmdList->ClearTexture(textureView->mNativeTextureView, clearValue);
    });
}

void cross::REDPass::ClearBuffer(REDBufferView* bufferView, UInt32 clearValue)
{
    auto* buffer = bufferView->mBuffer;
    mBuffers[buffer].State |= REDResourceState{NGIResourceState::CopyDst};
    Execute([=](REDPass* pass, NGICommandList* cmdList)
    {
        cmdList->ClearBuffer(bufferView->mNativeBufferView, clearValue);
    });
}

cross::REDRegion::REDRegion(UInt32 depth, REDRegion* parent, std::string_view name)
    : Depth{ depth }
    , Parent{ parent }
    , Name{ name }
{
    auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto pool = rdrSys->GetRenderingExecutionDescriptor()->GetREDFrameAllocator();
    PropertySet = std::move(cross::PropertySet{pool, parent ? &parent->PropertySet : nullptr});
}