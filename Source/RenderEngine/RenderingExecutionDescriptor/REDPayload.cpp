#include "EnginePrefix.h"
#include "REDPayload.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDPass.h"
#include "Resource/Shader.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "REDRenderRawDrawUnitsPayload.h"
#include "REDImGui/REDdebugGUI.h"
#include "REDImGui/REDVisualizer.h"
#include "RenderEngine/RayTracingScene/BindlessResourceManager.h"

void cross::REDBundleCommandExecutionPayload::OnExecute(REDPass* pass, NGICommandList* cmdList) const
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);

    NGIBundleCommandList* bundleCmdList{};
    cmdList->GetCommandQueue()->AllocateBundleCommandLists(1, &bundleCmdList);
    cmdList->ForkBundleCommandLists(1, &bundleCmdList);

    bundleCmdList->Begin();
    mCallback(pass, bundleCmdList);
    bundleCmdList->End();
}

void cross::REDCommandExecutionPayload::OnExecute(REDPass* pass, NGICommandList* cmdList) const
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);

    mCallback(pass, cmdList);
}

void cross::REDComputeExecutionPayload::ModifyThreadGroupCount(UInt32 threadGroupCountX, UInt32 threadGroupCountY, UInt32 threadGroupCountZ)
{
    mThreadGroupCountX = threadGroupCountX;  
    mThreadGroupCountY = threadGroupCountY;
    mThreadGroupCountZ = threadGroupCountZ;
}

void cross::REDComputeExecutionPayload::OnCompile(REDPass* pass)
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);

    mResourceGroup = pass->GetContext().GetResourceGroup(mProgramDesc->ResourceGroupLayouts[ShaderParamGroup_Pass],
        mPipelineState->GetDesc().PipelineLayout->GetDesc().ResourceGroupLayouts[ShaderParamGroup_Pass]);
}

void cross::REDComputeExecutionPayload::OnExecute(REDPass* pass, NGICommandList* cmdList) const
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);

    cmdList->SetComputePipelineState(mPipelineState);

    if (mResourceGroup != nullptr) 
    {
        cmdList->SetComputeResourceGroup(ShaderParamGroup_Pass, mResourceGroup);
    }

    if (mPipelineState->GetDesc().PipelineLayout->GetDesc().ResourceGroupLayouts[ShaderParamGroup_Bindless]->GetDesc().ResourceCount > 0)
    {
        cmdList->SetComputeResourceGroup(ShaderParamGroup_Bindless, gBindlessResourceManager.GetBindlessResourceGroup().get());
    }
    
    if (mArgumentBuffer)
    {
        cmdList->DispatchIndirect(mArgumentBuffer->mNativeBuffer, mArgumentOffset);
    }
    else
    {
        if (mThreadGroupCountX * mThreadGroupCountY * mThreadGroupCountZ > 0)
        {
            cmdList->Dispatch(mThreadGroupCountX, mThreadGroupCountY, mThreadGroupCountZ);
        }
    }
}

void cross::REDRayTracingExecutionPayload::OnCompile(REDPass* pass)
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);

    // Create resource group for space 0
    mResourceGroup = pass->GetContext().GetResourceGroup(mProgramDesc->ResourceGroupLayouts[ShaderParamGroup_Pass],
        mPipelineState->GetDesc().PipelineLayout->GetDesc().ResourceGroupLayouts[ShaderParamGroup_Pass]);
}

void cross::REDRayTracingExecutionPayload::OnExecute(REDPass* pass, NGICommandList* cmdList) const
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);

    cmdList->SetRayTracingPipeline(mPipelineState);

    // Bind space0
    if (mResourceGroup)
    {
        cmdList->SetRayTracingResourceGroup(ShaderParamGroup_Pass, mResourceGroup);
    }
    // TODO(scolu): Bind space 1, 2 if needed
    // Bind space3
    if (mPipelineState->GetDesc().PipelineLayout->GetDesc().ResourceGroupLayouts[ShaderParamGroup_Bindless]->GetDesc().ResourceCount > 0)
    {
        cmdList->SetRayTracingResourceGroup(ShaderParamGroup_Bindless, gBindlessResourceManager.GetBindlessResourceGroup().get());
    }

    cmdList->TraceRays(mPipelineState, mWidth, mHeight, mDepth);
}

cross::REDDrawMeshPayload::REDDrawMeshPayload(const REDDrawMesh& drawMesh)
    : REDDrawMesh{ drawMesh }
{
}

cross::REDDrawMeshPayload::REDDrawMeshPayload(const REDDrawScreenQuad& drawScreenQuad)
    : REDDrawMesh{ drawScreenQuad }
{
    auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* rdrPrims = rdrSys->GetRenderPrimitives();

    InputLayoutDesc = &rdrPrims->mPostProcessInputLayoutDesc;
    VertexBufferCount = 1;
    VertexBuffers[0] = rdrPrims->mFullScreenTriangle.get();
    VertexOrIndexCount = 3;
    InstanceCount = 1;
}

void cross::REDDrawMeshPayload::OnCompile(REDPass* pass)
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);

    auto& context = pass->GetContext();

    auto* fp = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam();
    auto fa = fp->GetFrameAllocator();
    auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    // geometry
    mGeometryPacket = fa->Allocate<GeometryPacket>(1, FRAME_STAGE_RENDER);
    for (UInt8 i = 0; i < VertexBufferCount; ++i)
    {
        mGeometryPacket->AddVertexStream(VertexBuffers[i], static_cast<UInt32>(VertexBuffers[i]->GetDesc().Size), 0, InputLayoutDesc->GetVertexStreamLayout(i));
    }

    mGeometry = fa->Allocate<RenderGeometry>(1, FRAME_STAGE_RENDER);
    if (IndexBuffer)
    {
        mGeometryPacket->SetIndexStream(IndexBuffer, static_cast<UInt32>(IndexBuffer->GetDesc().Size), VertexOrIndexCount);
        mGeometry->SetData(mGeometryPacket, 0, 0, VertexOrIndexCount, 0, VertexOrIndexCount / 3, PrimitiveTopology::TriangleList);
    }
    else
    {
        mGeometry->SetData(mGeometryPacket, VertexOrIndexCount, 0, 0, 0, VertexOrIndexCount / 3, PrimitiveTopology::TriangleList);
    }

    // pso
    mMaterialState = Material->GetMaterialRenderState(PassName, nullptr, &context);

    const void* shaderConst = GenerateShaderConstData(fa, mMaterialState, &pass->GetContext(), &mObjContext);

    NGIGraphicsPipelineStateDesc pipelineStateDesc
    {
        pass->GetRenderPass(),
        pass->GetSubpass(),
        mMaterialState.mProgram->GUID,
        & mMaterialState.mProgram->GraphicsProgramDesc,
        mMaterialState.mProgram->PipelineLayout,
        InputLayoutDesc->GetHash().GetHash(),
        InputLayoutDesc,
        PrimitiveTopology::TriangleList,
        RasterizationStateDesc ? *RasterizationStateDesc : *mMaterialState.mRaterizationState,
        BlendStateDesc ? *BlendStateDesc : *mMaterialState.mBlendState,
        DepthStencilStateDesc ? *DepthStencilStateDesc : *mMaterialState.mDepthStencilState,
        mMaterialState.mProgram->ShaderConstantLayout ? mMaterialState.mProgram->ShaderConstantLayout->ByteSize : 0,
        shaderConst,
    };

    mPipelineState = rdrSys->GetGraphicsPipelineStatePool()->AllocateGraphicsPipelineState(pipelineStateDesc);

    // dynamic state
    if (DynamicState)
    {
        mMaterialState.mDynamicState = *DynamicState;
    }

    // resource group
    mPasGroup = context.GetResourceGroup(
        mMaterialState.mProgram->ResourceGroupLayouts[ShaderParamGroup_Pass], 
        mMaterialState.mProgram->PipelineLayout->GetDesc().ResourceGroupLayouts[ShaderParamGroup_Pass]);

    mMtlGroup = mMaterialState.GetResourceBinding();

    mObjGroup = mObjContext.GetResourceGroup(
        mMaterialState.mProgram->ResourceGroupLayouts[ShaderParamGroup_Model],
        mMaterialState.mProgram->PipelineLayout->GetDesc().ResourceGroupLayouts[ShaderParamGroup_Model]);
}

void cross::REDDrawMeshPayload::OnExecute(REDPass * pass, NGICommandList * primaryCmdList) const
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);

    auto width = pass->mRenderPass->mFramebuffer->GetDesc().Width;
    auto height = pass->mRenderPass->mFramebuffer->GetDesc().Height;
    NGIViewport viewport{ 0, 0, static_cast<float>(width), static_cast<float>(height), 0, 1 };
    NGIScissor scissor{ 0, 0, width, height };

    NGIBundleCommandList* cmdList;
    primaryCmdList->GetCommandQueue()->AllocateBundleCommandLists(1U, &cmdList);
    primaryCmdList->ForkBundleCommandLists(1U, &cmdList);

    cmdList->Begin();
    cmdList->SetViewports(1, &viewport);
    cmdList->SetScissors(1, &scissor);

    cmdList->SetGraphicsPipelineState(mPipelineState);

    if (mPipelineState->GetDesc().DepthStencilState.EnableStencil)
    {
        cmdList->SetStencilRef(mMaterialState.mDynamicState.StencilReference);
    }

    if (mPasGroup)
    {
        cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Pass, mPasGroup);
    }

    if (mMtlGroup)
    {
        cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Material, mMtlGroup);
    }

    if (mObjGroup)
    {
        cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Model, mObjGroup);
    }

    NGIBuffer* vertexBuffers[MaxVertexStreams]{};
    UInt64 vertexBufferOffsets[MaxVertexStreams]{};
    for (int i = 0; i < mGeometryPacket->GetStreamCount(); ++i)
    {
        auto stream = mGeometryPacket->GetVertexStream(static_cast<UInt8>(i));
        vertexBuffers[i] = stream->GetGpuBuffer();
        vertexBufferOffsets[i] = stream->GetStreamOffset();
    }
    cmdList->SetVertexBuffers(mGeometryPacket->GetStreamCount(), vertexBuffers, vertexBufferOffsets);

    if (auto indexBuffer = mGeometryPacket->GetIndexStream()->GetGpuBuffer(); indexBuffer)
    {
        UInt64 indexOffset = mGeometryPacket->GetIndexStream()->GetStreamOffset();
        auto indexFormat = mGeometryPacket->GetIndexFormat();

        cmdList->SetIndexBuffer(indexBuffer, indexOffset, indexFormat);
        cmdList->DrawIndexedInstanced(mGeometry->GetIndexCount(), std::max(1U, InstanceCount), mGeometry->GetIndexStart(), mGeometry->GetVertexStart(), 0);
    }
    else
    {
        // adapt to draw without indexBuffer, like NanoVG
        cmdList->DrawInstanced(mGeometry->GetVertexCount(), std::max(1U, InstanceCount), mGeometry->GetVertexStart(), 0);
    }

    cmdList->End();
}

void cross::REDImGuiPayload::OnCompile(REDPass* pass) 
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    
    auto* visualizer = rendererSystem->GetREDVisualizer();
    auto* debugGUI = rendererSystem->GetREDdebugGUI();

    if (visualizer->GetTextureView())
    {
        visualizer->GenerateGUI();
    }
    else
    {
        visualizer->OnWindowClosed();
    }
    if (debugGUI->GetTextureView())
    {
        debugGUI->GenerateGUI();
    }
}

void cross::REDImGuiPayload::OnExecute(REDPass* pass, NGICommandList* cmdList) const
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    switch (rendererSystem->GetREDGUIState())
    {
    case REDGUIState::DebugWindow:
        rendererSystem->GetREDdebugGUI()->Draw(pass, cmdList);
        break;
    case REDGUIState::REDVisualizer:
        rendererSystem->GetREDVisualizer()->Draw(pass, cmdList);
        break;
    default:
        break;
    }
}

const void* cross::GenerateShaderConstData(FrameAllocator* fa, MaterialR::MaterialRenderState& mtlState, const PropertySet* passCtx, const PropertySet* objectCtx)
{
    if (auto layout = mtlState.mProgram->ShaderConstantLayout; layout && layout->ByteSize > 0)
    {
        auto shaderConst = mtlState.mShaderConstants;
        auto objOverride = objectCtx && objectCtx->HasShaderConstant(*layout);
        auto pasOverride = passCtx->HasShaderConstant(*layout);

        if (objOverride || pasOverride)
        {
            void* tmpShaderConsts = fa->Allocate<UInt8>(layout->ByteSize, FrameStage::FRAME_STAGE_RENDER);
            memcpy(tmpShaderConsts, shaderConst, layout->ByteSize);
            if (objOverride)
            {
                objectCtx->FillBuffer(*layout, tmpShaderConsts);
            }
            if (pasOverride)
            {
                passCtx->FillBuffer(*layout, tmpShaderConsts);
            }
            return tmpShaderConsts;
        }
        else
        {
            return shaderConst;
        }
    }
    else
    {
        return nullptr;
    }
}
