#pragma once
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "CECommon/Allocator/GrowOnlySpanAllocator.h"
#include "RenderEngine/GPUScene/ObjectIndexAllocator.h"
#include "GPUSceneData.h"

#if CROSSENGINE_WIN
#include <concurrent_vector.h>
#else
#include <vector>
#endif

namespace cross {
struct FFSRenderPipelineSetting;

#if CROSSENGINE_WIN
template<typename T>
using ThreadSafeVector = concurrency::concurrent_vector<T>;
#else
template<typename T>
struct ThreadSafeVector : public std::vector<T>
{
    using std::vector<T>::vector;

    void push_back(const T& val)
    {
        std::lock_guard lock{ mMutex };
        std::vector<T>::push_back(val);
    }

private:
    std::mutex mMutex;
};
#endif

class ScatterBytesUploadBuffer
{
public:
    void* AddScatterData(UInt32 sizeInByte, UInt32 dstBufferOffsetInByte);

    void UploadToDstBuffer(RenderingExecutionDescriptor* red, const FFSRenderPipelineSetting* ffsRenderPipelienSetting, REDBuffer* dstBuffer);

    void UploadToDstBuffer(NGIBuffer* dstBuffer);  // TODO(scolu)

private:

    struct ScatterData
    {
        UInt32 uploadBufferOffsetInFloat;
        UInt32 sizeInFloat;
        UInt32 dstBufferOffsetInFloat;
        float _pad;
    };
    ThreadSafeVector<ScatterData> mScatterDatas;

    std::atomic<UInt32> mCurrentUploadBufferOffsetInFloat = 0;
    ThreadSafeVector<std::tuple<SizeType, SizeType, void*>> mUpdateDate;
};

struct GPUSceneBufferView
{
    REDUniquePtr<REDResidentBufferView> mREDView;
};

class GPUScene
{
public:
    GPUScene();

    void Initialize(RenderWorld* renderWorld, RenderingExecutionDescriptor* red);

    void SetFFSRenderPipelineSetting(const FFSRenderPipelineSetting* renderPipelineSetting);

    void Update();

    void PostUpdate();

    std::vector<ecs::EntityID>& GetUpdateEntities()
    {
        return mUpdateEntities;
    }

#pragma region ThreadSafe
    // the functions below are all thread safe
    std::tuple<UInt32, const GPUSceneBufferView*> Allocate(UInt32 byteStride, UInt32 count);

    void Free(UInt32 byteStride, UInt32 objectIndexStart, UInt32 count);

    // return [PrimitiveCullingGUID, ObjectCullingGUID]
    std::tuple<UInt32, UInt32> AllocateCullingData(UInt32 objectCount);

    void FreeCullingData(UInt32 pimitiveCullingGUIDStart, UInt32 objectCullingGUIDStart, UInt32 objectCount);

    void SetGPUSceneDirty(ecs::EntityID entity);

    void* UploadData(UInt32 sizeInByte, UInt32 dstBufferOffsetInByte, bool clearMemory = true);
#pragma endregion

#pragma region CullingData
    REDResidentBufferView* GetPrimitiveCullingDataBufferSRV() const { return mTypedGPUSceneBufferViews.at(sizeof(PrimitiveCullingData)).mREDView.get(); }

    REDResidentBufferView* GetObjectCullingDataBufferSRV() const { return mTypedGPUSceneBufferViews.at(sizeof(ObjectCullingData)).mREDView.get(); }
#pragma endregion

private:
    enum class EntityState
    {
        Create,
        Change,
    };

    void AddUpdateEntity(ecs::EntityID entity, EntityState state);

    void ResizeGPUBuffer();

    void UploadDataToGPUBuffer();

private:
    RenderWorld* mRenderWorld;
    RenderingExecutionDescriptor* mRED;
    const FFSRenderPipelineSetting* mFFSRenderPipelineSetting;

    std::mutex mMutex;
    std::vector<ecs::EntityID> mUpdateEntities;
    std::unordered_map<ecs::EntityID, EntityState> mEntityStates;
    std::unordered_set<ecs::EntityID> mLastFrameMovedEntities;

    // IndexAllocator
    ObjectIndexAllocator mGPUSceneBufferIndexAllocator;

    // IndexAllocator Mutex
    std::mutex mGPUSceneBufferIndexAllocatorMutex;

    // GPUSceneBuffer
    REDUniquePtr<REDResidentBuffer> mGPUSceneBuffer;

    // TypedGPUSceneBufferViews
    std::unordered_map<UInt32, GPUSceneBufferView> mTypedGPUSceneBufferViews;

    // UploadBuffer
    ScatterBytesUploadBuffer mGPUSceneUploadBuffer;

    constexpr static NGIBufferUsage ObjectBufferUsage = NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopySrc | NGIBufferUsage::CopyDst;
};
}   // namespace cross