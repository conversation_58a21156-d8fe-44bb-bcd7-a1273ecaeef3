#include "GPUScene.h"
#include "RenderEngine/EntityLifeCycleRenderDataSystemR.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/GPUScene/GPUSceneData.h"
#include "RenderEngine/AABBSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"
#include "RenderEngine/RenderMaterial.h"
#include "Resource/Shader.h"

namespace cross {
static UInt32 RoundUpToPowerOfTwo(UInt32 number)
{
    return 1 << CeilfToInt(std::log2f(static_cast<float>(number)));
}

GPUScene::GPUScene()
{
    mTypedGPUSceneBufferViews[sizeof(PrimitiveCullingData)];
    mTypedGPUSceneBufferViews[sizeof(ObjectCullingData)];
}

void GPUScene::Initialize(RenderWorld* renderWorld, RenderingExecutionDescriptor* red)
{
    mRenderWorld = renderWorld;
    mRED = red;
}

void GPUScene::SetFFSRenderPipelineSetting(const FFSRenderPipelineSetting* renderPipelineSetting)
{
    mFFSRenderPipelineSetting = renderPipelineSetting;
}

std::tuple<UInt32, const GPUSceneBufferView*> GPUScene::Allocate(UInt32 byteStride, UInt32 count)
{
    std::lock_guard lock{mGPUSceneBufferIndexAllocatorMutex};

    auto objectIndexStart = mGPUSceneBufferIndexAllocator.Allocate(byteStride, count);
    auto objectDataBufferView = &mTypedGPUSceneBufferViews[byteStride];
    return { objectIndexStart, objectDataBufferView };
}

void GPUScene::Free(UInt32 byteStride, UInt32 objectIndexStart, UInt32 count)
{
    std::lock_guard lock{mGPUSceneBufferIndexAllocatorMutex};

    mGPUSceneBufferIndexAllocator.Free(byteStride, count, objectIndexStart);
}

std::tuple<UInt32, UInt32> GPUScene::AllocateCullingData(UInt32 objectCount)
{
    std::lock_guard lock{mGPUSceneBufferIndexAllocatorMutex};

    return {mGPUSceneBufferIndexAllocator.Allocate(sizeof(PrimitiveCullingData), 1), mGPUSceneBufferIndexAllocator.Allocate(sizeof(ObjectCullingData), objectCount)};
}

void GPUScene::FreeCullingData(UInt32 pimitiveCullingGUIDStart, UInt32 objectCullingGUIDStart, UInt32 objectCount)
{
    std::lock_guard lock{mGPUSceneBufferIndexAllocatorMutex};

    mGPUSceneBufferIndexAllocator.Free(sizeof(PrimitiveCullingData), 1, pimitiveCullingGUIDStart);
    mGPUSceneBufferIndexAllocator.Free(sizeof(ObjectCullingData), objectCount, objectCullingGUIDStart);
}

void* GPUScene::UploadData(UInt32 sizeInByte, UInt32 dstBufferOffsetInByte, bool clearMemory)
{
    QUICK_SCOPED_CPU_TIMING("GPUScene::UploadData");
    if (sizeInByte == 0)
    {
        return nullptr;
    }

    auto* datePtr = mGPUSceneUploadBuffer.AddScatterData(sizeInByte, dstBufferOffsetInByte);
    if (clearMemory)
    {
        memset(datePtr, 0, sizeInByte);
    }
    return datePtr;
}

void GPUScene::Update()
{
    QUICK_SCOPED_CPU_TIMING("GPUScene::Update");

    auto* entityLifeCycleRenderDataSys = mRenderWorld->GetRenderSystem<EntityLifeCycleRenderDataSystemR>();

    // handle entity create
    {
        QUICK_SCOPED_CPU_TIMING("handle entity create");

        for (const auto& data : entityLifeCycleRenderDataSys->GetRenderEntityCreateList())
        {
            auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(data.entity);
            auto renderNodeCompWriter = renderNodeComp.Write();

            if (mRenderWorld->IsEntityAlive(data.entity) && mRenderWorld->HasComponent<RenderNodeComponentR>(data.entity) && mRenderWorld->HasComponent<AABBComponentR>(data.entity))
            {
                AddUpdateEntity(data.entity, EntityState::Create);
            }
        }
    }

    // handle entity change
    {
        QUICK_SCOPED_CPU_TIMING("handle entity change");

        // if a entity moved in the last frame, we need also update GPUScene.
        for (auto entity : mLastFrameMovedEntities)
        {
            if (mRenderWorld->IsEntityAlive(entity) && mRenderWorld->HasComponent<RenderNodeComponentR>(entity) && mRenderWorld->HasComponent<AABBComponentR>(entity))
            {
                AddUpdateEntity(entity, EntityState::Change);

                auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(entity);
                auto renderNodeCompWriter = renderNodeComp.Write();
                renderNodeCompWriter->mRenderNode->ClearTransformState();
            }
        }

        mLastFrameMovedEntities.clear();

        for (const auto& data : entityLifeCycleRenderDataSys->GetRenderEntityChangeList())
        {
            if (!mRenderWorld->IsEntityAlive(data.entity))
                continue;

            auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(data.entity);

            AddUpdateEntity(data.entity, EntityState::Change);

            if (data.type == RenderEntityChangeData::ChangeType::Transform)
            {
                // store moved entity for the next frame
                mLastFrameMovedEntities.insert(data.entity);

                auto renderNodeCompWriter = renderNodeComp.Write();
                renderNodeCompWriter->mRenderNode->MarkAsTransformed();
            }
        }
    }

    ResizeGPUBuffer();

    UploadDataToGPUBuffer();
}

void GPUScene::PostUpdate()
{
    if (mGPUSceneBuffer)
    {
        mRED->FlushState(mGPUSceneBuffer.get(), NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
    }
}

void GPUScene::SetGPUSceneDirty(ecs::EntityID entity)
{
    std::lock_guard lock{ mMutex };
    AddUpdateEntity(entity, EntityState::Change);
}

void GPUScene::AddUpdateEntity(ecs::EntityID entity, EntityState state)
{
    if (mEntityStates.find(entity) == mEntityStates.end())
    {
        mUpdateEntities.emplace_back(entity);
        mEntityStates[entity] = state;
    }
    else
    {
        mEntityStates[entity] = state;
    }
}

void GPUScene::ResizeGPUBuffer()
{
    QUICK_SCOPED_CPU_TIMING("ResizeGPUBuffer");

    auto GPUSceneBufferSizeInByte = std::max(RoundUpToPowerOfTwo(mGPUSceneBufferIndexAllocator.GetUsedSize()), 1u);
    if (!mGPUSceneBuffer || mGPUSceneBuffer->GetDesc().Size != GPUSceneBufferSizeInByte)
    {
        auto newGPUSceneBuffer = mRED->CreateBuffer("GPUScene.Buffer", NGIBufferDesc{GPUSceneBufferSizeInByte, ObjectBufferUsage});

        if (mGPUSceneBuffer)
        {
            NGICopyBuffer region{0, 0, std::min<SizeType>(mGPUSceneBuffer->GetDesc().Size, GPUSceneBufferSizeInByte)};
            mRED->AllocatePass("CopyGPUSceneBuffer")->CopyBufferToBuffer(newGPUSceneBuffer.get(), mGPUSceneBuffer.get(), 1, &region);
        }

        mGPUSceneBuffer = std::move(newGPUSceneBuffer);

        for (auto& [stride, view] : mTypedGPUSceneBufferViews)
        {
            NGIBufferViewDesc desc{
                NGIBufferUsage::StructuredBuffer | NGIBufferUsage::RWStructuredBuffer,
                0,
                GPUSceneBufferSizeInByte,
                GraphicsFormat::Unknown,
                stride,
            };
            view.mREDView = mRED->CreateBufferView(mGPUSceneBuffer.get(), desc);
        }
    }
    else
    {
        for (auto& [stride, view] : mTypedGPUSceneBufferViews)
        {
            if (!view.mREDView)
            {
                NGIBufferViewDesc desc{
                    NGIBufferUsage::StructuredBuffer | NGIBufferUsage::RWStructuredBuffer,
                    0,
                    GPUSceneBufferSizeInByte,
                    GraphicsFormat::Unknown,
                    stride,
                };
                view.mREDView = mRED->CreateBufferView(mGPUSceneBuffer.get(), desc);
            }
        }
    }
}

void GPUScene::UploadDataToGPUBuffer()
{
    if (!mUpdateEntities.empty())
    {
        QUICK_SCOPED_CPU_TIMING("upload data");

        threading::ParallelFor(static_cast<SInt32>(mUpdateEntities.size()), [&](auto i)
        {
            auto& entity = mUpdateEntities[i];
            if (mRenderWorld->IsEntityAlive(entity))
            {
                auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(entity);
                auto* renderNode = renderNodeComp.Read()->mRenderNode.get();
                renderNode->UploadGPUScene(*this, mRenderWorld, entity);
            }
        });

        {
            QUICK_SCOPED_CPU_TIMING("assemble upload pass");
            mGPUSceneUploadBuffer.UploadToDstBuffer(mRED, mFFSRenderPipelineSetting, mGPUSceneBuffer.get());
        }

        mUpdateEntities.clear();
        mEntityStates.clear();
    }
}

void* ScatterBytesUploadBuffer::AddScatterData(UInt32 sizeInBytes, UInt32 dstBufferOffsetInBytes)
{
    Assert(sizeInBytes % 4 == 0);
    UInt32 sizeInFloat = sizeInBytes / 4;

    Assert(dstBufferOffsetInBytes % 4 == 0);
    UInt32 dstBufferOffsetInFloat = dstBufferOffsetInBytes / 4;
    
    auto uploadBufferOffsetInFloat = mCurrentUploadBufferOffsetInFloat.fetch_add(sizeInFloat);

    constexpr UInt32 batchSize = 512;
    auto nBatch = math::DivideAndRoundUp(sizeInFloat, batchSize);

    for (UInt32 i = 0; i < nBatch; ++i)
    {
        auto processedSize = i * batchSize;
        auto remainedSize = sizeInFloat - processedSize;
        auto srcOffset = uploadBufferOffsetInFloat + processedSize;
        auto dstOffset = dstBufferOffsetInFloat + processedSize;

        mScatterDatas.push_back(ScatterData{srcOffset, std::min(batchSize, remainedSize), dstOffset, 0});
    }

    auto * datePtr = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameAllocator()->Allocate(sizeInBytes, FRAME_STAGE_RENDER);
    mUpdateDate.push_back(std::make_tuple(uploadBufferOffsetInFloat * sizeof(float), sizeInBytes, datePtr));
    return datePtr;
}

void ScatterBytesUploadBuffer::UploadToDstBuffer(RenderingExecutionDescriptor* red, const FFSRenderPipelineSetting* ffsRenderPipelienSetting, REDBuffer* dstBuffer)
{
    if (mUpdateDate.empty())
        return;
    auto* dstBufferUAV = red->AllocateBufferView(dstBuffer,
                                                 NGIBufferViewDesc{
                                                     NGIBufferUsage::RWStructuredBuffer,
                                                     0,
                                                     dstBuffer->GetDesc().Size,
                                                     GraphicsFormat::Unknown,
                                                     sizeof(float),
                                                 });

    REDBufferView* scatterBufferSRV;
    REDBufferView* uploadBufferSRV;

    auto* sb = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetScratchBuffer();
    // Allocate ScatterBuffer
    {
        UInt32 scatterBufferDataByteSize = static_cast<UInt32>(mScatterDatas.size() * sizeof(ScatterData));

        auto scatterBuffer = red->AllocateBuffer("ScatterBytesScatterBuffer", {scatterBufferDataByteSize, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst});
        scatterBufferSRV = red->AllocateBufferView(scatterBuffer, NGIBufferViewDesc{NGIBufferUsage::StructuredBuffer, 0, scatterBufferDataByteSize, GraphicsFormat::Unknown, sizeof(ScatterData)});

        auto stagingBufferWrap = sb->AllocateStaging(NGIBufferUsage::CopySrc, scatterBufferDataByteSize);
        stagingBufferWrap.MemWriteIterator(0, mScatterDatas.begin(), mScatterDatas.size());

        NGICopyBuffer region{
            stagingBufferWrap.GetNGIOffset(),
            0,
            scatterBufferDataByteSize,
        };

        red->QueueBufferUpload(scatterBuffer, stagingBufferWrap.GetNGIBuffer(), region);
    }

    // Allocate uploadBufferSRV;
    {
        QUICK_SCOPED_CPU_TIMING("Allocate uploadBufferSRV");

        UInt32 uploadBufferDataByteSize = mCurrentUploadBufferOffsetInFloat * sizeof(float);
        auto stagingBufferWrap = sb->AllocateStaging(NGIBufferUsage::CopySrc, uploadBufferDataByteSize);

        std::sort(mUpdateDate.begin(),
                  mUpdateDate.end(),
                  [](
                  std::tuple<SizeType, SizeType, void*>& a, std::tuple<SizeType, SizeType, void*>& b) {
                      return std::get<0>(a) < std::get<0>(b);
                  });

        threading::ParallelFor(static_cast<SInt32>(mUpdateDate.size()),
                               [&](auto i) {
                                   const auto& [offset, size, date] = mUpdateDate[i];
                                   Assert(offset < uploadBufferDataByteSize);
                                   stagingBufferWrap.MemWrite(offset, date, size);
                               }
            );

        auto uploadBuffer = red->AllocateBuffer("ScatterBytesUploadBuffer", {uploadBufferDataByteSize, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst});
        uploadBufferSRV = red->AllocateBufferView(uploadBuffer, NGIBufferViewDesc{NGIBufferUsage::StructuredBuffer, 0, uploadBufferDataByteSize, GraphicsFormat::Unknown, sizeof(float)});

        NGICopyBuffer region{
            stagingBufferWrap.GetNGIOffset(),
            0,
            uploadBufferDataByteSize,
        };

        red->QueueBufferUpload(uploadBuffer, stagingBufferWrap.GetNGIBuffer(), region);
    }

    // UploadData
    auto* pass = red->AllocatePass("ScatterByteCopy", true);
    {
        UInt32 scatterCount = static_cast<UInt32>(mScatterDatas.size());

        pass->SetProperty("_ScatterBuffer", scatterBufferSRV);
        pass->SetProperty("_UploadBuffer", uploadBufferSRV);
        pass->SetProperty("_DstBuffer", dstBufferUAV);
        pass->SetProperty("_ScatterCount", scatterCount);

        UInt3 groupSize;
        ffsRenderPipelienSetting->ScatterByteCopyComputeShaderR->GetThreadGroupSize(NameID("ScatterByteCopy"), groupSize.x, groupSize.y, groupSize.z);
        pass->Dispatch(ffsRenderPipelienSetting->ScatterByteCopyComputeShaderR, NameID("ScatterByteCopy"), math::DivideAndRoundUp(scatterCount, groupSize.x), 1, 1);
    }

    mScatterDatas.clear();
    mCurrentUploadBufferOffsetInFloat = 0;
    mUpdateDate.clear();

}

}   // namespace cross