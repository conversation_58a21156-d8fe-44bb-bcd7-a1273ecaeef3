#include "MaterialGPUScene.h"


namespace cross {

UInt32 MaterialGPUScene::Allocate(UInt32 byteStride)
{
    std::lock_guard locker(mGPUMaterialBufferIndexAllocatorMutex);
    return mGPUMaterialBufferIndexAllocator.Allocate(byteStride, 1);
}

void MaterialGPUScene::Free(UInt32 byteStride, UInt32 materialIndexStart)
{
    std::lock_guard locker(mGPUMaterialBufferIndexAllocatorMutex);
    mGPUMaterialBufferIndexAllocator.Free(byteStride, 1, materialIndexStart);
}

void* MaterialGPUScene::UploadData(UInt32 sizeInByte, UInt32 dstBufferOffsetInByte, bool clearMemory)
{
    QUICK_SCOPED_CPU_TIMING("MaterialGPUScene::UploadData");
    if (sizeInByte == 0)
    {
        return nullptr;
    }

    auto* datePtr = mGPUMaterialUploadBuffer.AddScatterData(sizeInByte, dstBufferOffsetInByte);
    if (clearMemory)
    {
        memset(datePtr, 0, sizeInByte);
    }
    return datePtr;
}

void MaterialGPUScene::ResizeGPUBuffer()
{
    
}

void MaterialGPUScene::UploadDataToGPUBuffer()
{
    QUICK_SCOPED_CPU_TIMING("MaterialGPUScene::UploadDataToGPUBuffer");

    threading::ParallelFor(static_cast<SInt32>(mDirtyCacheMaterial.size()), [&](auto i)
    {
        for (auto& mtl : mDirtyCacheMaterial)
        {
            if (mtl->GetFx()->GetMaterialProtoType())
            {
                mtl->UploadMaterialGPUScene(*this);
            }
        }
    });
}

void MaterialGPUScene::MarkMaterialCacheDirty(MaterialR* dirtyMaterial)
{
    std::lock_guard lock(mMarkMaterialDirtyMutex);
    mDirtyCacheMaterial.insert(dirtyMaterial);
}

void MaterialGPUScene::UpdateMaterialCache()
{
    std::lock_guard lock(mMtlAddMutex);
    std::lock_guard lockTemp(mMarkMaterialDirtyMutex);
    mMtlScratchByte = 0;
    
    SCOPED_CPU_TIMING(GroupRendering, "UpdateMaterialCache");
    for (const auto mtl: mDirtyCacheMaterial)
    {
        if (!mtl->GetFx()->GetMaterialProtoType())
        {
            mtl->UpdateCache();
        }
    }
    mDirtyCacheMaterial.clear();
}

void MaterialGPUScene::AddMaterialCacheByteSize(int byteSizeChange)
{
    std::lock_guard lock(mMarkMaterialCacheByteSizeMutex);
    mMtlVarientByte += byteSizeChange;
}

}  // namespace cross
