#include "RenderMesh.h"

#include "CECommon/Common/EngineGlobal.h"
#include "Resource/Cluster/ClusterBuilder.h"
#include "Resource/Cluster/ClusterEncoder.h"
#include "RenderEngine/RenderFactory.h"
#include "RenderEngine/VertexStreamLayoutPolicy.h"
#include "RayTracingScene/BindlessResourceManager.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"

namespace cross
{
    MeshR::~MeshR()
    {
#ifdef NGI_ENABLE_RAY_TRACING
        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeHeadless)
        {
            gBindlessResourceManager.OnMeshRelease(this);
        }
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(mBLAS);
#endif
        if (mMeshAssetData)
            gBindlessResourceManager.ReleaseMeshIndex(mMeshIndex);
        if (mMeshAssetData && mMeshAssetData->IsClusterMeshEnabled()) 
        {
            auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
            auto* stellarMeshManager = rendererSystem->GetStellarMeshManager();
            stellarMeshManager->RemoveRenderMesh(this);
        }
    }

    MeshR::MeshR(MeshAssetData* meshAssetData)
    {
        mMeshAssetData = meshAssetData;
        if (mMeshAssetData)
        {
            mMeshIndex = gBindlessResourceManager.AllocateMeshIndex(1);
        }
    }

    void MeshR::BuildStaticMesh(IVertexStreamLayoutParameter* meshParameter)
    {
        Assert(mMeshAssetData);
        bool enableStreaming = gResourceMgr.mStreamingMgr->IsStreamingEnabled();
        SetReadyForStreaming(meshParameter != nullptr);
        mStellarMeshEnable = false;

        if (mMeshAssetData->IsClusterMeshEnabled())
        {
            // TODO(jahwang): cache or serialize gpuClusteredMeshData
            auto generateClusterData = [&]() -> StellarMesh::GPUClusteredMeshData {
                LOG_DEBUG("StellarMesh: Start building clusters for mesh {}.", mMeshAssetData->GetName());

                StellarMesh::ClusterBuilder clusterBuilder{};
                auto clusteredMesh = clusterBuilder.Build(mMeshAssetData);
                StellarMesh::ClusterEncoder clusterEncoder{std::move(clusteredMesh)};
                auto gpuClusteredMesh = clusterEncoder.Encode();

                LOG_DEBUG("StellarMesh: Finish building Clusters for mesh {}. Build Clusters Num: {}.", mMeshAssetData->GetName(), gpuClusteredMesh.mClusters.size());

                return gpuClusteredMesh;
            };

            auto uploadDataToGPU = [&](StellarMesh::GPUClusteredMeshData gpuClusteredMeshData) {

                //UploadStellarMeshResource(gpuClusteredMeshData.mMeshData);

                auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
                auto* stellarMeshManager = rendererSystem->GetStellarMeshManager();
                stellarMeshManager->AddRenderMesh(this, std::move(gpuClusteredMeshData));
            };

            SetState(State::Initializing);
            auto gpuClusteredMeshData = generateClusterData();
            mStellarMeshEnable = gpuClusteredMeshData.mMeshData.mIndexes.size() > 0ull;
            uploadDataToGPU(std::move(gpuClusteredMeshData));
        }
        else if (enableStreaming && mMeshAssetData->IsMeshStreamable() && IsReadyForStreaming())
        {
            SetState(State::Initializing);

            // Create vertex and index buffer of meshes with a specific LOD level
            VertexStreamLayoutStaticLODStreamingParameter* meshParam = dynamic_cast<VertexStreamLayoutStaticLODStreamingParameter*>(meshParameter);
            IVertexStreamLayoutPolicy* streamLayout = RenderFactory::Instance().GetVertexStreamLayoutPolicy(STREAM_LAYOUT_POLICY_STATIC_LOD_STREAMING);
            streamLayout->AssembleGpuResource(this, mMeshAssetData, meshParam);
            // LOG_DEBUG("Streaming: Stream in mesh {} with LOD {}", mMeshAssetData->GetName(), meshParam->SelectedLOD);
        }
        else if (enableStreaming && mMeshAssetData->IsMeshStreamable() && !IsReadyForStreaming())
        {
            SetState(State::Initializing);

            // Collect vertex and index data info of meshes for each LOD level
            VertexStreamLayoutStaticLODStreamingParameter meshParam;
            meshParam.EnableStreaming = false;
            IVertexStreamLayoutPolicy* streamLayout = RenderFactory::Instance().GetVertexStreamLayoutPolicy(STREAM_LAYOUT_POLICY_STATIC_LOD_STREAMING);
            streamLayout->AssembleGpuResource(this, mMeshAssetData, &meshParam);

            // Assemble mesh buffers of the highest LOD level to GPU immediately
            meshParam.EnableStreaming = true;
            meshParam.SelectedLOD = mMeshAssetData->GetLodCount() - 1;
            streamLayout->AssembleGpuResource(this, mMeshAssetData, &meshParam);

            SetReadyForStreaming(true);
        }
        else
        {
            IVertexStreamLayoutPolicy* streamLayout = RenderFactory::Instance().GetVertexStreamLayoutPolicy(STREAM_LAYOUT_POLICY_STATIC);
            SetState(State::Initializing);
            streamLayout->AssembleGpuResource(this, mMeshAssetData, meshParameter);
        }

        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeHeadless)
        {
            gBindlessResourceManager.OnMeshBuild(this);
        }

        SetState(State::Initialized);
    }

    const std::vector<RenderGeometry>& MeshR::GetRenderGeometries() const
    {
        return mGeometries;
    }

    RenderGeometry& MeshR::GetRenderGeometry(UInt32 index)
    {
        return mGeometries[index];
    }

    UInt32 MeshR::GetGeometryCount() const
    {
        return static_cast<UInt32>(mGeometries.size());
    }

    bool MeshR::IsGeometryEmpty() const
    {
        return mGeometries.empty();
    }

    std::vector<LODMeshR>* MeshR::GetLODMeshes()
    {
        return &mLODMeshes;
    }

    LODMeshR* MeshR::GetLODMesh(UInt8 lodLevel)
    {
        if (lodLevel >= 0 && lodLevel < mLODMeshes.size())
        {
            return &mLODMeshes[lodLevel];
        }
        else
        {
            return nullptr;
        }
    }

    UInt32 MeshR::GetLODMeshCount() const
    {
        return static_cast<UInt32>(mLODMeshes.size());
    }

    void MeshR::ClearAndResizeLODMeshes(UInt32 lodCount)
    {
        mLODMeshes.clear();
        mLODMeshes.resize(lodCount);
    }

    UInt32 MeshR::GetLODMeshBufferSize(UInt8 lodLevel) const
    {
        if (lodLevel < mLODMeshes.size())
        {
            return mLODMeshes[lodLevel].GetBufferSize();
        }
        else
        {
            Assert(false);
            return 0;
        }
    }

    // TODO: Not sure if the condition it is enough
    bool MeshR::IsReadyForStreaming() const
    {
        return mReadyForStreaming;
    }

    void MeshR::SetReadyForStreaming(bool ready)
    {
        mReadyForStreaming = ready;
    }

    void MeshR::SetNameHash(UInt32 index, StringHash32 hash)
    {
        mGeometryNameHashes[index] = hash;
    }

    void MeshR::ClearAndResize(UInt32 geometryCount)
    {
        mGeometries.clear();
        mGeometries.resize(geometryCount);
        mGeometryNameHashes.clear();
        mGeometryNameHashes.resize(geometryCount);
    }

    void MeshR::SetState(State state)
    {
        mState.store(state, std::memory_order_relaxed);
    }

    MeshR::State MeshR::GetState() const
    {
        return mState.load(std::memory_order_relaxed);
    }

    bool MeshR::TryUpdate(UInt32 curFrameId)
    {
        return mVersion.exchange(curFrameId) != curFrameId;
    }

    std::mutex& MeshR::GetBuildMutex()
    {
        return mBuildMutex;
    }

    void MeshR::SetMeshIndex(UInt32 index)
    {
        mMeshIndex = index;
    }

    UInt32 MeshR::GetMeshIndex()
    {
        return mMeshIndex;
    }

    StellarMeshResource const& MeshR::GetStellarMeshResource() const
    {
        return mStellarMeshResource;
    }

    bool MeshR::IsStellarMeshEnable() const
    {
        return mStellarMeshEnable;
    }

    void MeshR::UploadStellarMeshResource(MeshStreamData& meshStreamData)
    {
        SCOPED_CPU_TIMING(GroupRendering, "RenderMesh::UploadStellarMeshResource");

        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto& NGIDevice = GetNGIDevice();

        auto UploadBuffer = [&](NGIBufferPtr buffer, NGIResourceState state, void const* srcData) {
            auto bufferSize = buffer->GetDesc().Size;
            //NGIBufferDesc desc{bufferSize, NGIBufferUsage::CopySrc};
            //NGIStagingBuffer* stagingBuffer = NGIDevice.CreateStagingBuffer(desc);

            //auto stagingData = stagingBuffer->MapRange(NGIBufferUsage::CopySrc, 0, bufferSize);
            //memcpy(stagingData, srcData, bufferSize);
            //stagingBuffer->UnmapRange(0, bufferSize);
            
            auto asycStagingBuffer = rendererSystem->GetScratchBuffer()->AllocateStaging_Async(NGIBufferUsage::CopySrc, bufferSize);
            auto [stagingBuffer, stagingOffset, stagingData] = asycStagingBuffer.Raw();
            memcpy(stagingData, srcData, bufferSize);
            stagingBuffer->UnmapRange(stagingOffset, bufferSize);

            NGICopyBuffer region{0, 0, bufferSize};
            rendererSystem->UpdateBuffer(buffer.get(), stagingBuffer, region, NGIResourceState::Undefined, state);
            asycStagingBuffer.Free();
            //rendererSystem->DestroyNGIObjectInAsyncThread(stagingBuffer);
        };

        auto UploadIndexBuffer = [&] {
            auto sizeInByte = meshStreamData.mIndexes.size() * sizeof(UInt32);

            NGIBufferDesc desc{
                sizeInByte,
                NGIBufferUsage::IndexBuffer | NGIBufferUsage::CopyDst,
                0.1f,
            };

            mStellarMeshResource.mIndexBuffer = NGIBufferPtr{NGIDevice.CreateBuffer(desc, "Cluster Mesh IndexBuffer")};
            UploadBuffer(mStellarMeshResource.mIndexBuffer, NGIResourceState::IndexBuffer, meshStreamData.mIndexes.data());
        };

        auto UploadVertexBuffer = [&](NGIBufferPtr& dstBuffer, auto& vertexData, size_t vertexSize) {
            auto sizeInByte = vertexData.size() * vertexSize;

            NGIBufferDesc desc{
                sizeInByte,
                NGIBufferUsage::VertexBuffer | NGIBufferUsage::CopyDst,
                0.1f,
            };

            dstBuffer = NGIBufferPtr{NGIDevice.CreateBuffer(desc, "Cluster Mesh VertexBuffer")};
            UploadBuffer(dstBuffer, NGIResourceState::VertexBuffer, vertexData.data());
        };

        UploadIndexBuffer();
        //if (meshStreamData.mVertexAttribute.mPosition.empty())
        UploadVertexBuffer(mStellarMeshResource.mVertexAttributesBuffer[StellarMeshVertexAttribute::mPosition], meshStreamData.mVertexAttribute.mPosition, sizeof(Float3));
        UploadVertexBuffer(mStellarMeshResource.mVertexAttributesBuffer[StellarMeshVertexAttribute::mNormal]  , meshStreamData.mVertexAttribute.mNormal  , sizeof(Float3));
        if (!meshStreamData.mVertexAttribute.mTangent.empty())
        {
            UploadVertexBuffer(mStellarMeshResource.mVertexAttributesBuffer[StellarMeshVertexAttribute::mTangent]  , meshStreamData.mVertexAttribute.mTangent, sizeof(Float4));
        }
        if (!meshStreamData.mVertexAttribute.mColor.empty())
        {
            UploadVertexBuffer(mStellarMeshResource.mVertexAttributesBuffer[StellarMeshVertexAttribute::mColor], meshStreamData.mVertexAttribute.mColor, sizeof(Float4));
        }
        if (!meshStreamData.mVertexAttribute.mUV0.empty())
        {
            UploadVertexBuffer(mStellarMeshResource.mVertexAttributesBuffer[StellarMeshVertexAttribute::mUV0], meshStreamData.mVertexAttribute.mUV0, sizeof(Float2));
        }
        if (!meshStreamData.mVertexAttribute.mUV1.empty())
        {
            UploadVertexBuffer(mStellarMeshResource.mVertexAttributesBuffer[StellarMeshVertexAttribute::mUV1], meshStreamData.mVertexAttribute.mUV1, sizeof(Float2));
        }
    }

}
