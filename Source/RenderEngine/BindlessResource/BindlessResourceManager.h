#pragma once
#include "CrossBase/Platform/PlatformTypes.h"
#include "Resource/IResourceInterface.h"
#include "RenderGeometry.h"

#include <string>

namespace cross {

#define CONTINUE_IF(x) do { if (x) continue; } while(0)

/**
 * @brief Thread-Safe Continuous Index Allocator
 *
 * A thread-safe version of the index allocator that can be safely used in multi-threaded environments.
 * Provides sequential index allocation with index reuse for different types of objects.
 */
template<bool bDelayedRecycle = false>
class ThreadSafeIndexAllocator
{
public:
    /**
     * @brief Constructor
     * @param name Name of the index allocator, used for debugging
     */
    explicit ThreadSafeIndexAllocator(const std::string& name = "")
        : mName(name) {}

    void Tick()
    {
        static_assert(bDelayedRecycle, "Tick() is only available when delayed recycling is enabled.");

        std::lock_guard<std::mutex> lock(mMutex);
        ++mCurrentFrame;

        auto it = mPendingReleases.begin();
        while (it != mPendingReleases.end())
        {
            if (mCurrentFrame - it->frameReleased >= kRecycleDelay)
            {
                InsertFreeRange(it->start, it->count);
                it = mPendingReleases.erase(it);
            }
            else
            {
                ++it;
            }
        }
    }

    void InsertFreeRange(UInt32 start, UInt32 count)
    {
        UInt32 end = start + count;
        auto it = mFreeRanges.lower_bound({ start, 0 });

        if (it != mFreeRanges.begin())
        {
            auto prev = std::prev(it);
            if (prev->second >= start)
            {
                start = std::min(start, prev->first);
                end   = std::max(end,   prev->second);
                mFreeRanges.erase(prev);
            }
        }

        while (it != mFreeRanges.end() && it->first <= end)
        {
            end = std::max(end, it->second);
            it = mFreeRanges.erase(it);
        }

        mFreeRanges.insert({ start, end });
    }
    
    UInt32 Allocate(UInt32 count = 1)
    {
        std::lock_guard<std::mutex> lock(mMutex);
        
        for (auto it = mFreeRanges.begin(); it != mFreeRanges.end(); ++it)
        {
            UInt32 rangeStart = it->first;
            UInt32 rangeEnd = it->second;

            if (rangeEnd - rangeStart >= count)
            {
                UInt32 allocStart = rangeStart;
                UInt32 newStart = rangeStart + count;
                
                mFreeRanges.erase(it);
                if (newStart < rangeEnd)
                    mFreeRanges.insert({ newStart, rangeEnd });

                return allocStart;
            }
        }
        
        UInt32 allocStart = mNextIndex;
        mNextIndex += count;
        return allocStart;
    }

    void Release(UInt32 start, UInt32 count = 1)
    {
        Assert(start < mNextIndex);

        std::lock_guard<std::mutex> lock(mMutex);

        if constexpr (bDelayedRecycle)
        {
            mPendingReleases.push_back({ start, count, mCurrentFrame });
        }
        else
        {
            InsertFreeRange(start, count);
        }
    }
    
    void Reset()
    {
        std::lock_guard<std::mutex> lock(mMutex);
        mNextIndex = 0;
        mFreeRanges.clear();
    }
    
    UInt32 GetMaxAllocatedIndex() const
    {
        std::lock_guard<std::mutex> lock(mMutex);
        return mNextIndex;
    }
    
    UInt32 GetAllocatedCount() const
    {
        std::lock_guard<std::mutex> lock(mMutex);
        UInt32 freeCount = 0;
        for (const auto& range : mFreeRanges) 
        {
            freeCount += (range.second - range.first);
        }
        return mNextIndex - freeCount;
    }
    
    const std::string& GetName() const
    {
        return mName;
    }

private:
    std::string mName;
    UInt32 mNextIndex = 0;
    std::set<std::pair<UInt32, UInt32>> mFreeRanges;
    mutable std::mutex mMutex;

    static constexpr UInt32 kRecycleDelay = 3;  // delay frames
    struct PendingRelease {
        UInt32 start;
        UInt32 count;
        UInt32 frameReleased;
    };
    std::vector<PendingRelease> mPendingReleases;
    UInt32 mCurrentFrame = 0;
};

/**
 * Manage GPU bindless resources
 *
 * Common usage:
 *     uint subMeshIndex = InstanceID() + GeometryIndex();
 *     SubInstanceData subInstanceData = SubInstanceBuffer[subMeshIndex];
 *
 *     Access Geometry
 *     uint geoIndex = subInstanceData.GeometryIndex;
 *     
 *     Vertex vert0 = VertexBuffers[geoIndex][PrimitiveIndex() * 3 + 0];
 *     uint index0 = IndexBuffers[geoIndex][PrimitiveIndex() * 3 + 0];
 *
 *     Access Material
 *     uint matIndex = subInstanceData.MaterialIndex;
 *     MaterialData matData = MaterialBuffer[matIndex];
 */
class BindlessResourceManager
{
public:
    BindlessResourceManager(BindlessResourceManager const&) = delete;
    BindlessResourceManager(BindlessResourceManager&&) = delete;
    BindlessResourceManager& operator=(BindlessResourceManager const&) = delete;
    BindlessResourceManager& operator=(BindlessResourceManager&&) = delete;

    void Tick();
    
    ~BindlessResourceManager() = default;

    static BindlessResourceManager& Instance();

    /**
     * Gather GPU buffer for each sub mesh
     */
    void OnMeshBuild(IMeshR* mesh);

    UInt32 AllocateMeshIndex(UInt32 count);

    void ReleaseMeshIndex(UInt32 index, UInt32 count = 1);

    UInt32 AllocateBindlessBufferView(NGIBuffer* buffer, GraphicsFormat format, SizeType size, UInt32 offset = 0);

    void ReleaseBindlessBufferView(UInt32 index, UInt32 count = 1);

    /**
     * Release geometry index for each sub mesh
     */
    void OnMeshRelease(IMeshR* mesh);

    /**
     * Allocate material index and caches material data on cpu side
     */
    void AddMaterial(IMaterialR* material);

    /**
     * Allocate texture index and caches texture resource pointer on cpu side
     */
    void AddTexture(IGPUTexture* tex);
    
    void CreateBindlessResourceGroup(UInt32 maxBindlessResourceCount);
    
    auto GetBindlessResourceGroup() const
    {
        return mBindlessResourceGroup;
    }
    
    std::shared_ptr<ThreadSafeIndexAllocator<false>> mBindlessBufferIndexAllocator;
    std::shared_ptr<NGIResourceGroupLayout> mBindlessResourceGroupLayout;
    std::shared_ptr<NGIResourceGroup> mBindlessResourceGroup;
    std::unordered_map<UInt32, std::unique_ptr<NGIBufferView>> mTrackedBindlessBufferViews;
    std::mutex mUpdateBindlessResourceGroupMutex;
    std::mutex mReleaseTrackedBindlessBufferViewMutex;

    std::shared_ptr<ThreadSafeIndexAllocator<false>> mMeshIndexAllocator;
    std::shared_ptr<ThreadSafeIndexAllocator<false>> mGeometryIndexAllocator;
    std::shared_ptr<ThreadSafeIndexAllocator<false>> mMaterialIndexAllocator;
    std::shared_ptr<ThreadSafeIndexAllocator<false>> mTextureIndexAllocator;
    
    std::shared_ptr<NGIStagingBuffer> mMaterialBuffer;
    std::shared_ptr<NGIBufferView> mMaterialBufferView;

    std::vector<IGPUTexture*> mTextureResources;

private:
    BindlessResourceManager();
    void ProcessVertexChannel(const VertexStreamLayout& streamLayout, const BufferStream* bufferStream, GeometryData& geoData);
};

}

#define gBindlessResourceManager cross::BindlessResourceManager::Instance()
