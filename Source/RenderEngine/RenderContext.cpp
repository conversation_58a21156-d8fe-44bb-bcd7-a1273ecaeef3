#include "EnginePrefix.h"
#include "RenderContext.h"
#include "NativeGraphicsInterface/NGIUtils.h"
#include "CECommon/Common/EngineGlobal.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "RenderEngine/RendererSystemR.h"
#include "CECommon/Common/FrameParam.h"
#include "CrossBase/Hash/Hash.h"

namespace cross {
void PropertySet::SetProperty(const NameID& name, const void* data, size_t dataSize)
{
    FrameStdVector<UInt8> vec;
    if (mFramePool)
    {
        vec = std::move(FrameStdVector<UInt8>(mFramePool));
    }
    else
    {
        vec = std::move(FrameStdVector<UInt8>());
    }

    vec.resize(dataSize);
    memcpy(vec.data(), data, dataSize);

    mNumericSet.insert_or_assign(name, vec);
}

void* PropertySet::GetPropertyPtr(const NameID& name, size_t dataSize)
{
    FrameStdVector<UInt8> vec;
    if (mFramePool)
    {
        vec = std::move(FrameStdVector<UInt8>(mFramePool));
    }
    else
    {
        vec = std::move(FrameStdVector<UInt8>());
    }

    vec.resize(dataSize);
    mNumericSet.insert_or_assign(name, vec);

    return vec.data();
}

void ReadOnlyPropertySet::SetProperty(const NameID& name, const void* data, size_t dataSize)
{
    std::unique_ptr<FrameStdVector<UInt8>> vec = nullptr;
    if (mFramePool)
    {
        vec = std::make_unique<FrameStdVector<UInt8>>(mFramePool);
    }
    else
    {
        vec = std::make_unique<FrameStdVector<UInt8>>();
    }

    vec->resize(dataSize);
    memcpy(vec->data(), data, dataSize);

    auto [itr, ret] = mNumericSet.try_emplace(name, std::move(*vec));
    AssertMsg(ret, "Modify after init was not allowed");
}
   
const PropertySet::NumericProperty* PropertySet::GetNumericProperty(const NameID& name) const
{
    if (auto itr = mNumericSet.find(name); itr != mNumericSet.end())   
    {
        return &itr->second;
    }

    if (mParent) 
    {
        return mParent->GetNumericProperty(name);
    }
    return nullptr;
}

bool PropertySet::GetNumericProperty(const NameID& name, size_t size, void* data) const
{
    if (auto* prop = GetNumericProperty(name); prop)
    {
        auto ret = false;
        std::visit(Overloaded{[&](const FrameStdVector<UInt8>& p)
            {
                if (p.size() == size)
                {
                    memcpy(data, p.data(), size);
                    ret = true;
                }
            },
            [&](const auto& p)
            {
                using T = std::decay_t<decltype(p)>;
                if (sizeof(T) == size)
                {
                    memcpy(data, &p, sizeof(T));
                    ret = true;
                }
            }
            }, *prop);   
        return ret;
    }
    return false;
}

const PropertySet::ResourceProperty* PropertySet::GetResourceProperty(const NameID& name, UInt32 arrayIndex) const
{
    if (auto itr = mResourceSet.find(name); itr != mResourceSet.end())
    {
        if (arrayIndex < itr->second.mData.size())
        {
            return &itr->second.mData[arrayIndex];
        }
    }

    if (mParent)
    {
        return mParent->GetResourceProperty(name, arrayIndex);
    }

    return nullptr;
}

void PropertySet::Clear()
{
    mNumericSet.clear();
    mResourceSet.clear();
}

NGIResourceGroup* PropertySet::GetResourceGroup(
    const resource::ShaderResourceGroupLayout& resLayout,
    NGIResourceGroupLayout* ngiLayout) const
{
    using namespace CrossSchema;

    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* allocator = rendererSystem->GetScratchBuffer();
    auto* groupPool = rendererSystem->GetResourceGroupPool();
    auto* frameAllocator = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameAllocator();

    auto bindings = GetResourceBindings(resLayout, ngiLayout);

    for (const auto& cbLayout : resLayout.ConstantBufferLayouts)
    {
        auto cbBuffer = std::move(FrameStdVector<unsigned char>(rendererSystem->GetRenderingExecutionDescriptor()->GetREDFrameAllocator()));
        cbBuffer.resize(cbLayout.ByteSize);
        memset(cbBuffer.data(), 0, cbLayout.ByteSize);

        FillBuffer(cbLayout, cbBuffer.data());

        auto bufferWrap = allocator->AllocateScratch(NGIBufferUsage::ConstantBuffer, cbLayout.ByteSize);
        bufferWrap.MemWrite(0, cbBuffer.data(), cbLayout.ByteSize);

        bindings.push_back(NGIResourceBinding::BindConstBuffer(cbLayout.ID, bufferWrap.GetNGIBuffer(), bufferWrap.GetNGIOffset(), cbLayout.ByteSize));
    }

    if (bindings.size() == 0)
    {
        return nullptr;
    }
    std::sort(bindings.begin(), bindings.end());

    return groupPool->Allocate(ngiLayout, static_cast<UInt32>(bindings.size()), &bindings[0]);
}
//#pragma optimize("", off)
FrameStdVector<NGIResourceBinding> PropertySet::GetResourceBindings(const resource::ShaderResourceGroupLayout& resLayout, NGIResourceGroupLayout* ngiLayout) const
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* frameAllocator = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameAllocator();

    auto resCount = resLayout.ConstantBufferLayouts.size() +
        std::accumulate(resLayout.ResourceLayouts.begin(), resLayout.ResourceLayouts.end(), 0ull, [](auto acc, auto& res) { return acc + res.ArraySize; });

    auto bindings = std::move(FrameStdVector<NGIResourceBinding>(rendererSystem->GetRenderingExecutionDescriptor()->GetREDFrameAllocator()));
    if (resCount == 0)
        return bindings;
    bindings.reserve(resCount);
    for (auto& res : resLayout.ResourceLayouts)
    {
        for (UInt32 arrayIndex = 0; arrayIndex < res.ArraySize; ++arrayIndex)
        {
            if (auto iter = GetResourceProperty(res.ID, arrayIndex); iter != nullptr)
            {
                switch (GetResourceBindingType(res.Type))
                {
                case NGIResourceBindingType::Texture:
                    std::visit(Overloaded{
                        [&](REDTextureView* v)
                        {
                            bindings.push_back(NGIResourceBinding::BindTexture(res.ID, arrayIndex, v->mNativeTextureView, iter->Flags));
                        },
                        [&](NGITextureView* v)
                        {
                            bindings.push_back(NGIResourceBinding::BindTexture(res.ID, arrayIndex, v, iter->Flags));
                        },
                        [](auto)
                        {
                            // wrong resource type for binding
                            Assert(false);
                        }
                        }, iter->Value);
                    break;
                case NGIResourceBindingType::Buffer:
                    std::visit(Overloaded{
                        [&](REDBufferView* v)
                        {
                            bindings.push_back(NGIResourceBinding::BindBuffer(res.ID, arrayIndex, v->mNativeBufferView));
                        },
                        [&](NGIBufferView* v)
                        {
                            bindings.push_back(NGIResourceBinding::BindBuffer(res.ID, arrayIndex, v));
                        },
                        [](auto)
                        {
                            // wrong resource type for binding
                            Assert(false);
                        }
                        }, iter->Value);
                    break;
                case NGIResourceBindingType::Sampler:
                    std::visit(Overloaded{
                        [&](NGISampler* v)
                        {
                            bindings.push_back(NGIResourceBinding::BindSampler(res.ID, arrayIndex, v));
                        },
                        [](auto)
                        {
                            // wrong resource type for binding
                            Assert(false);
                        }
                        }, iter->Value);
                    break;
                case NGIResourceBindingType::AccelStruct:
                    std::visit(Overloaded{
                        [&](NGIAccelStruct* v)
                        {
                            bindings.push_back(NGIResourceBinding::BindAccelStruct(res.ID, v));
                        },
                        [](auto)
                        {
                            // wrong resource type for binding
                            Assert(false);
                        }
                        }, iter->Value);
                default:
                    break;
                }
            }
            else
            {
                bindings.push_back(rendererSystem->GetRenderPrimitives()->GetDefaultBinding(res, arrayIndex));
            }
        }
    }

    return bindings;
}

void PropertySet::FillBuffer(const resource::ShaderBufferLayout& layout, void* dst, bool transposeMatrix) const
{
    for (const auto& m : layout.Members)
    {
        auto* dataPtr = static_cast<UInt8*>(dst) + m.Offset;
        if (auto* prop = GetNumericProperty(m.Name); prop)
        {
            std::visit(Overloaded{
                [&](const Float4x4& v)
                {
                    if (CheckType<Float4x4>(m))
                    {
                        if (transposeMatrix)
                        {
                            auto dataT = v;
                            memcpy(dataPtr, dataT.Transpose().data(), m.Size);
                        }
                        else
                        {
                            memcpy(dataPtr, v.data(), m.Size);
                        }
                    }
                    else
                    {
                        memcpy(dataPtr, v.data(), std::min<size_t>(sizeof(Float4), m.Size));
                        //LOG_EDITOR_WARNING("Const buffer numeric prop: {} - type not matching", m.Name.GetName());
                    }
                },
                [&](const bool& v)
                {
                    if (CheckType<bool>(m))
                    {
                        memset(dataPtr, v ? 0xffff : 0, m.Size);
                    }
                    else
                    {
                        memcpy(dataPtr, &v, std::min<size_t>(sizeof(bool), m.Size));
                        //LOG_EDITOR_WARNING("Const buffer numeric prop: {} - type not matching", m.Name.GetName());
                    }
                },
                [&](const FrameStdVector<UInt8>& v)
                {
                    memcpy(dataPtr, v.data(), std::min<size_t>(v.size(), m.Size));
                },
                [&](const auto& v)
                {
                    using T = std::decay_t<decltype(v)>;
                    memcpy(dataPtr, &v, std::min<size_t>(sizeof(T), m.Size));
                    if (!CheckType<T>(m))
                    {
                        //LOG_EDITOR_WARNING("Const buffer numeric prop: {} - type not matching", m.Name.GetName());
                    }
                } 
                }, *prop);
        }
    }
}

void PropertySet::FillBuffer(const resource::ShaderBufferLayout& layout, StagingBufferWrap bufferWrap, SizeType bufferWrapOffset, bool transposeMatrix) const
{
    for (const auto& m : layout.Members)
    {
        size_t offset = m.Offset + bufferWrapOffset;
        Assert(m.Offset + m.Size <= layout.ByteSize);
        if (auto* prop = GetNumericProperty(m.Name); prop)
        {
            std::visit(Overloaded{[&](const Float4x4& v) {
                                      if (CheckType<Float4x4>(m))
                                      {
                                          if (transposeMatrix)
                                          {
                                              auto dataT = v;
                                              bufferWrap.MemWrite(offset, dataT.Transpose().data(), m.Size);
                                          }
                                          else
                                          {
                                              bufferWrap.MemWrite(offset, v.data(), m.Size);
                                          }
                                      }
                                      else
                                      {
                                          bufferWrap.MemWrite(offset, v.data(), std::min<size_t>(sizeof(Float4), m.Size));
                                          // LOG_EDITOR_WARNING("Const buffer numeric prop: {} - type not matching", m.Name.GetName());
                                      }
                                  },
                                  [&](const bool& v) {
                                      if (CheckType<bool>(m))
                                      {
                                          bufferWrap.MemSet(offset, 0xFF, m.Size);
                                      }
                                      else
                                      {
                                          bufferWrap.MemWrite(offset, &v, std::min<size_t>(sizeof(bool), m.Size));
                                          // LOG_EDITOR_WARNING("Const buffer numeric prop: {} - type not matching", m.Name.GetName());
                                      }
                                  },
                                  [&](const FrameStdVector<UInt8>& v) { bufferWrap.MemWrite(offset, v.data(), std::min<size_t>(v.size(), m.Size)); },
                                  [&](const auto& v) {
                                      using T = std::decay_t<decltype(v)>;
                                      bufferWrap.MemWrite(offset, &v, std::min<size_t>(sizeof(T), m.Size));
                                      if (!CheckType<T>(m))
                                      {
                                          // LOG_EDITOR_WARNING("Const buffer numeric prop: {} - type not matching", m.Name.GetName());
                                      }
                                  }},
                       *prop);
        }
    }
}


bool PropertySet::HasShaderConstant(const resource::ShaderBufferLayout& layout) const
{
    for (const auto& m : layout.Members)
    {
        if (mNumericSet.find(m.Name)!= mNumericSet.end())
        {
            return true;
        }
    }

    if (mParent)
    {
        return mParent->HasShaderConstant(layout);
    }

    return false;
}

void PropertySet::FillShaderKey(resource::ShaderVariationKey& key) const
{
    for (UInt32 i = 0; i < key.GetMacroCount(); i++)
    {
        auto name = key.GetMacroNameID(i);
        if (auto* data = GetNumericProperty(name); data && std::holds_alternative<bool>(*data))
        {
            key.SetMacro(name.GetName(), *std::get_if<bool>(data));
        }
    }
}
#if CROSSENGINE_WIN
PropertySet::ResourcePropVector::ResourcePropVector(FrameAllocatorPool* framalloc)
{
    if (framalloc)
    {
        mData = std::move(std::pmr::vector<ResourceProperty>(framalloc));
    }
    else
    {
        mData = std::move(std::pmr::vector<ResourceProperty>());
    }
}
PropertySet::ResourcePropVector::~ResourcePropVector() 
{
}
#else
PropertySet::ResourcePropVector::ResourcePropVector(FrameAllocatorPool* framalloc)
    : mData(std::vector<ResourceProperty>())
{}
#endif
}   // namespace cross
