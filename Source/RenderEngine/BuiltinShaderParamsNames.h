#pragma once
#include "CrossBase/String/NameID.h"

#define DECLARE_BUILTIN_PROP(x) static inline const NameID x = #x

#define BUILTIN_CAMERA_LIST_VIEW(i)         NameID("ce_View" + std::to_string(i))
#define BUILTIN_CAMERA_LIST_INV_VIEW(i)     NameID("ce_InvView" + std::to_string(i))
#define BUILTIN_CAMERA_LIST_PRE_VIEW(i)     NameID("ce_PreView" + std::to_string(i))
#define BUILTIN_CAMERA_LIST_PRE_INV_VIEW(i) NameID("ce_PreInvView" + std::to_string(i))

#if defined(CE_USE_DOUBLE_TRANSFORM)
#    define BUILTIN_CAMERA_LIST_TILE_POSITION(i)        NameID("ce_CameraTilePosition" + std::to_string(i))
#    define BUILTIN_CAMERA_LIST_PRE_TILE_POSITION(i)    NameID("ce_PreCameraTilePosition" + std::to_string(i))
#endif

namespace cross::BuiltInProperty {
    static inline const NameID World = "World";
    static inline const NameID View = "View";
    static inline const NameID Projection = "Projection";
    static inline const NameID LightColor0 = "LightColor0";
    static inline const NameID SpotDirection0 = "SpotDirection0";
    static inline const NameID AmbientSkyLight = "AmbientSkyLight";
    static inline const NameID AmbientGroundLight = "AmbientGroundLight";
    static inline const NameID AmbientSkyDirection = "AmbientSkyDirection";

    static inline const NameID ce_UseDoubleTransform = "CE_USE_DOUBLE_TRANSFORM";
    static inline const NameID ce_Time = "ce_Time";   // x: time, y: time*2, z: time*3, w: time/20    
    static inline const NameID ce_PreTime = "ce_PreTime";
    static inline const NameID ce_World = "ce_World";
    // motion vector
    static inline const NameID ce_PreWorld = "ce_PreWorld";
    static inline const NameID ce_TilePosition = "ce_TilePosition";
    static inline const NameID ce_PreTilePosition = "ce_PreTilePosition";
    static inline const NameID ce_Projection = "ce_Projection";
    static inline const NameID ce_CameraPos = "ce_CameraPos";
    static inline const NameID ce_PreCameraPos = "ce_PreCameraPos";

    DECLARE_BUILTIN_PROP(ce_View);
    DECLARE_BUILTIN_PROP(ce_PreView);
    DECLARE_BUILTIN_PROP(ce_PreViewMatrix);
    DECLARE_BUILTIN_PROP(ce_InvView);
    DECLARE_BUILTIN_PROP(ce_PreInvView);

    DECLARE_BUILTIN_PROP(ce_CameraTilePosition);
    DECLARE_BUILTIN_PROP(ce_PrevCameraTilePosition);
    DECLARE_BUILTIN_PROP(ce_ClipToPrevClipMat);
    DECLARE_BUILTIN_PROP(ce_ClipToPrevClipMatNoJitter);
    DECLARE_BUILTIN_PROP(ce_PreProjMatrix);
    DECLARE_BUILTIN_PROP(ce_PreProjMatrixJitter);
    DECLARE_BUILTIN_PROP(ce_ProjMatrixJitter);
    DECLARE_BUILTIN_PROP(ce_InvViewProjMatrix);

    static inline const NameID ce_LightDirPos = "ce_LightDirPos";
    static inline const NameID ce_LightTilePos = "ce_LightTilePos";
    static inline const NameID ce_LightAttenuations = "ce_LightAttenuations";
    static inline const NameID ce_LightColors = "ce_LightColors";
    static inline const NameID ce_LightOutSpaceColors = "ce_LightOutSpaceColors";
    static inline const NameID ce_LightSpotDirections = "ce_LightSpotDirections";
    static inline const NameID ce_AmbientSkyLight = "ce_AmbientSkyLight";
    static inline const NameID ce_GlobalEnvSH = "ce_GlobalEnvSH";
    static inline const NameID ce_AmbientGroundLight = "ce_AmbientGroundLight";
    static inline const NameID ce_AmbientSkyDirection = "ce_AmbientSkyDirection";
    static inline const NameID ce_InvWorld = "ce_InvWorld";
    static inline const NameID ce_InvTransposeWorld = "ce_InvTransposeWorld";
    static inline const NameID ce_InvProjection = "ce_InvProjection";
    static inline const NameID ce_Sampler_PCF = "ce_Sampler_PCF";
    static inline const NameID ce_Sampler_Clamp = "ce_Sampler_Clamp";
    static inline const NameID ce_Sampler_Point = "ce_Sampler_Point";
    static inline const NameID ce_Sampler_Linear_Point_Clamp = "ce_Sampler_Linear_Point_Clamp";
    static inline const NameID ce_Sampler_Linear_Point_Wrap = "ce_Sampler_Linear_Point_Wrap";
    static inline const NameID ce_Sampler_Repeat = "ce_Sampler_Repeat";
    static inline const NameID ce_Sampler_Mirror = "ce_Sampler_Mirror";
    static inline const NameID ce_Sampler_Anisotropic = "ce_Sampler_Anisotropic";
    static inline const NameID ce_Sampler_TerrainAlbedo = "ce_Sampler_TerrainAlbedo";
    static inline const NameID ce_LightIndex = "ce_LightIndex";
    static inline const NameID ce_LightCount = "ce_LightCount";
    static inline const NameID ce_ReflectionProbeCount = "ce_ReflectionProbeCount";
    static inline const NameID ce_ReflectionProbeIndex = "ce_ReflectionProbeIndex";
    static inline const NameID ce_ReflectionProbeWeight = "ce_ReflectionProbeWeight";
    static inline const NameID ce_ReflectionProbeMap1 = "ce_ReflectionProbeMap1";
    static inline const NameID ce_ReflectionProbeMap2 = "ce_ReflectionProbeMap2";
    static inline const NameID ce_ReflectionProbeMap3 = "ce_ReflectionProbeMap3";
    static inline const NameID ce_ReflectionProbeMap4 = "ce_ReflectionProbeMap4";
    static inline const NameID ce_RPExtentMipmapCount = "ce_RPExtentMipmapCount";
    static inline const NameID ce_ReflectionProbePosDistance = "ce_ReflectionProbePosDistance";
    static inline const NameID ce_ReflectionProbeBoxMin = "ce_ReflectionProbeBoxMin";
    static inline const NameID ce_ReflectionProbeBoxMax = "ce_ReflectionProbeBoxMax";
    static inline const NameID ce_ReflectionProbeEulerRot = "ce_ReflectionProbeEulerRot";
    static inline const NameID ce_TerrainSlotIndex = "ce_TerrainSlotIndex";

    DECLARE_BUILTIN_PROP(ce_InvTransposeInvWorld);
    DECLARE_BUILTIN_PROP(ce_LODIndex);

    // Deprecated
    static inline const NameID ce_Scene_Depth = "ce_Scene_Depth";

    DECLARE_BUILTIN_PROP(ce_SceneDepth);

    // Deprecated
    static inline const NameID ce_Scene_Color = "ce_Scene_Color";

    DECLARE_BUILTIN_PROP(ce_SceneColor);

    static inline const NameID ce_BoundingSphere = "ce_BoundingSphere";
    static inline const NameID ce_ScreenParams = "ce_ScreenParams";


    DECLARE_BUILTIN_PROP(ce_UE4AmbientProbeSH);
    DECLARE_BUILTIN_PROP(ce_SkyLightTexture);
    DECLARE_BUILTIN_PROP(ce_PreSkyLightTexture);  // Previous captured sky light texture, used for sky reflection blend
    DECLARE_BUILTIN_PROP(ce_SkyLightIntensity);
    DECLARE_BUILTIN_PROP(ce_SkyLightColor);
    DECLARE_BUILTIN_PROP(ce_LightMapIntensity);
    DECLARE_BUILTIN_PROP(ce_FrameIDMod8);
    DECLARE_BUILTIN_PROP(ce_FrameIDMod1024);
    DECLARE_BUILTIN_PROP(ce_FrameID);
    DECLARE_BUILTIN_PROP(ce_PreExposure);

    DECLARE_BUILTIN_PROP(ce_AtmosphereLightData);
    DECLARE_BUILTIN_PROP(ce_CameraForward);
    DECLARE_BUILTIN_PROP(USE_SECONDARY_LIGHT);
    DECLARE_BUILTIN_PROP(USE_SKY_ATMOSPHERE);
    DECLARE_BUILTIN_PROP(VirtualShadowMapId0);
    DECLARE_BUILTIN_PROP(VirtualShadowMapId1);

    // GPU Scene
    DECLARE_BUILTIN_PROP(ce_PerObject);
    DECLARE_BUILTIN_PROP(ce_PerPrimitive);
    DECLARE_BUILTIN_PROP(ce_PerMaterial);
    DECLARE_BUILTIN_PROP(ce_PrimitiveIndex);
    DECLARE_BUILTIN_PROP(CE_INSTANCING);

    DECLARE_BUILTIN_PROP(ce_MaterialBuffer);

    DECLARE_BUILTIN_PROP(ce_AccelerationStructure);
    DECLARE_BUILTIN_PROP(ce_BindlessFloatBuffers);  // bindless float1-4 buffers
    DECLARE_BUILTIN_PROP(ce_BindlessUIntBuffers);  // bindless uint1-4 buffers
    DECLARE_BUILTIN_PROP(ce_BindlessIntBuffers);  // bindless int1-4 buffers
    DECLARE_BUILTIN_PROP(ce_BindlessTextures);  // bindless texture2D
    DECLARE_BUILTIN_PROP(ce_SubInstanceData);  // sub instance data for ray tracing
}   // namespace cross::BuiltInProperty


