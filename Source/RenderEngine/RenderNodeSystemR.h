#pragma once
#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/RenderSystemBase.h"
#include "Resource/MeshAssetDataResource.h"
#include "RenderEngine/RenderWorldConst.h"
#include "RenderEngine/RenderGeometry.h"
#include "RenderEngine/RenderContext.h"
#include "RenderEngine/RenderEngineForward.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/Texture/GPUTexture.h"
#include "RenderEngine/RenderNode/RenderNode.h"
#include "RenderEngine/RenderNode/NormalRenderNode.h"

namespace cross
{
// deprecated!!!
struct RenderMesh
{
    RenderMesh();
    std::array<RenderGeometry*, resource::MAX_MESH_LOD_NUM> mLODGeometries;
    UInt8 mLODCount{ 0 };
    std::vector<MaterialR*> mLODMaterials;   // 0-LOD1
    MaterialR* mDefaultMaterial;
    PropertySet mProperties = PropertySet(nullptr);

    const UInt8* mInstanceData;
    const UInt8* mPreInstanceData;
    UInt64 mInstanceCount{0};
    REDBuffer* mInstanceBuffer{ nullptr };
    Float4 mBoundingSphere;
    UInt32 mVisibilityMask{ 0 };
    bool mAutoInstance{ true };
    bool mCustomInstancing{ false };
    bool mUseLocalSpace{ true };
    bool mReceiveDecals{ true };
    bool mEnableCameraMask { true };

    // for foliage
    const void* mFoliageClusterKey;
};

struct RenderNodeComponentR : ecs::IComponent 
{
    CEFunction(Reflect)
    RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();

    auto GetRenderNode() { return mRenderNode.get(); }
    void SetRenderNode(std::shared_ptr<RenderNode> renderNode) { mRenderNode = renderNode; }

private:
    std::shared_ptr<RenderNode> mRenderNode = std::make_shared<NormalRenderNode>();

    friend class RenderNodeSystemR;
    friend class FoliageSystemR;
    friend class GPUScene;
    friend class RayTracingScene;
    friend class StellarMeshScene;
    friend struct REDCullingProcessor;
};

struct RenderMeshChangeBeforeEventData
{
    RenderMeshChangeBeforeEventData(ecs::EntityID entityID)
        : mEntityID(entityID){};

    ecs::EntityID mEntityID;
};
using RenderMeshChangeBeforeEvent = SystemEvent<RenderMeshChangeBeforeEventData>;

struct RenderMeshChangeEventData
{
    RenderMeshChangeEventData(ecs::EntityID entityID)
        : mEntityID(entityID){};

    ecs::EntityID mEntityID;
};
using RenderMeshChangeEvent = SystemEvent<RenderMeshChangeEventData>;

struct RenderMaterialChangeEventData
{
    RenderMaterialChangeEventData(ecs::EntityID entityID)
        : mEntityID(entityID) {};

    ecs::EntityID mEntityID;
};
using RenderMaterialChangeEvent = SystemEvent<RenderMaterialChangeEventData>;

struct MaterialUpdateEventData
{
    MaterialUpdateEventData(ecs::EntityID entityID)
        : mEntityID(entityID)
    {}

    ecs::EntityID mEntityID;
};
using MaterialUpdateEvent = SystemEvent<MaterialUpdateEventData>;

class RenderNodeSystemR : public RenderSystemBase, public SystemEventManager<RenderMeshChangeBeforeEvent, RenderMeshChangeEvent, RenderMaterialChangeEvent, MaterialUpdateEvent>
{
    CEMetaInternal(Reflect) 

public:
    using RenderNodeComponentH = ecs::ComponentHandle<RenderNodeComponentR>;
    using ReanderNodeCompnentReader = ecs::ScopedComponentRead<RenderNodeComponentR>;
    using RenderNodeCompnentWriter = ecs::ScopedComponentWrite<RenderNodeComponentR>;

public:
    RENDER_ENGINE_API static RenderNodeSystemR* CreateInstance();

    virtual void Release() override;

    CEFunction(Reflect)
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

    virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

    void OnFirstUpdate(FrameParam* frameParam) override;

    void SetRenderMeshes(RenderNodeComponentH renderNodeH, const std::vector<RenderMesh>& renderMeshes, RenderNodeType type = RenderNodeType::Normal);

    const RenderNode* GetRenderNode(const ReanderNodeCompnentReader& reader) const { return reader->mRenderNode.get(); }

    RenderNode* GetRenderNode(const RenderNodeCompnentWriter& writer) { return writer->mRenderNode.get(); }

    void SetObjectVar(RenderNodeComponentH renderNodeH, const NameID& propertyName, const float* val, size_t len);

    void SetObjectVar(RenderNodeComponentH renderNodeH, const NameID& name, bool val);

    void SetObjectTexture(RenderNodeComponentH renderNodeH, const NameID& name, GPUTexture* texture);

    void NotifyMaterialChange(MaterialR* mtlChanged);

protected:
    RenderNodeSystemR();

    ~RenderNodeSystemR();
};

}
