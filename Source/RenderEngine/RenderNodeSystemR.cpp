
#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Allocator/FrameAllocator.h"
#include "RenderEngine/RenderNodeSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/RenderFactory.h"
#include "RenderEngine/FoliageSystemR.h"
#include "RenderEngine/LightSystemR.h"
#include "RenderEngine/FoliageComponentR.h"
#include "RenderEngine/GPUScene/GPUScene.h"
#include "RenderEngine/RenderPipelineSystemR.h"
#include "Resource/Material.h"

namespace cross {
ecs::ComponentDesc* RenderNodeComponentR::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetRenderComponentDesc<cross::RenderNodeComponentR>(false);
}

RenderNodeSystemR::RenderNodeSystemR() {}

RenderNodeSystemR::~RenderNodeSystemR() {}

RenderNodeSystemR* RenderNodeSystemR::CreateInstance()
{
    RenderNodeSystemR* system = new RenderNodeSystemR();
    return system;
}

void RenderNodeSystemR::Release()
{
    delete this;
}

void RenderNodeSystemR::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    if (event.mEventType == OnSystemAddToRenderWorldEvent::sEventType)
    {
        mRenderWorld->SubscribeRemainedEvent<EntityMoveEvent>(this, false);
        mRenderWorld->SubscribeRemainedEvent<EntityDestroyEvent>(this, true);
    }

    RenderSystemBase::NotifyEvent(event, flag);
}

void RenderNodeSystemR::OnBuildUpdateTasks(FrameParam* frameParam)
{
    CreateTaskFunction(FrameTickStage::Update, {}, [this] {
        SCOPED_CPU_TIMING(GroupRendering, "RenderNodeSystemRUpdate");

        // Update Transform
        TransformSystemR* transformSystem = mRenderWorld->GetRenderSystem<TransformSystemR>();
        auto* lightSystem = mRenderWorld->GetRenderSystem<LightSystemR>();
        auto& transformChangeList = transformSystem->GetTransformChangeList();
        for (UInt32 i = 0; i < transformChangeList.GetCount(); ++i)
        {
            auto [entity, worldMatrix] = transformChangeList.GetData(i);
            if (!mRenderWorld->IsEntityAlive(entity))
                continue;
            auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(entity);
            if (renderNodeComp.IsValid())
            {
                auto writer = renderNodeComp.Write(ecs::ComponentAccessFlag::SubComponentAccess2);
                writer->mRenderNode->mWorldTransform = worldMatrix;

                auto scale = transformSystem->GetWorldScale(entity);
                // need reverse;
                bool negative_scale = scale.x * scale.y * scale.z < 0;
                writer->mRenderNode->mNeedReverseCullingFace = negative_scale;
            }

            auto lightComp = mRenderWorld->GetComponent<LightComponentR>(entity);
            if (lightComp.IsValid())
            {
                lightSystem->SetLightDirty(entity);
                lightSystem->AddChangeData(entity);
            }
        }

        {
            auto& previousChangeList = transformSystem->GetPreviousTransformPostRenderUpdateList();
            for (const auto& item : previousChangeList)
            {
                const auto& [entity, prevWorldMatrix] = item;
                if (mRenderWorld->IsEntityAlive(entity))
                {
                    if (auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(entity); renderNodeComp.IsValid())
                    {
                        auto writer = renderNodeComp.Write(ecs::ComponentAccessFlag::SubComponentAccess2);
                        writer->mRenderNode->mWorldTransform.PreRelativeMatrix = prevWorldMatrix.RelativeMatrix;
                        writer->mRenderNode->mWorldTransform.PreTilePosition = prevWorldMatrix.TilePosition;

                        // update foliage preInstanceData
                        if (auto foliageComp = mRenderWorld->GetComponent<FoliageComponentR>(entity); foliageComp.IsValid())
                        {
                            FoliageSystemR* foliageSystem = mRenderWorld->GetRenderSystem<FoliageSystemR>();
                            // foliageSystem->UpdateInstanceData(entity);
                            foliageSystem->SetInstanceDataDirty(entity);
                        }
                    }
                }
            }

            previousChangeList.clear();
        }

        // Update RenderProperty
        RenderPropertySystemR* renderPropertySystem = mRenderWorld->GetRenderSystem<RenderPropertySystemR>();
        auto& renderPropertyChangeList = renderPropertySystem->GetChangeListRenderEffect();
        for (UInt32 i = 0; i < renderPropertyChangeList.GetCount(); ++i)
        {
            auto [entity, renderEffect] = renderPropertyChangeList.GetData(i);
            if (!mRenderWorld->IsEntityAlive(entity))
                continue;
            auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(entity);
            if (renderNodeComp.IsValid())
            {
                auto writer = renderNodeComp.Write(ecs::ComponentAccessFlag::SubComponentAccess2);
                writer->mRenderNode->SetRenderEffect(renderEffect.mRuntimeEffectMask);
            }
        }
        auto& renderPropertyLodChangeList = renderPropertySystem->GetChangeListRenderLodSelected();
        for (UInt32 i = 0; i < renderPropertyLodChangeList.GetCount(); ++i)
        {
            auto [entity, renderLodSelected] = renderPropertyLodChangeList.GetData(i);
            if (!mRenderWorld->IsEntityAlive(entity))
                continue;
            auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(entity);
            if (renderNodeComp.IsValid())
            {
                auto writer = renderNodeComp.Write(ecs::ComponentAccessFlag::SubComponentAccess2);
                writer->mRenderNode->mLodSelected = renderLodSelected.mLodSelected;
            }
        }
    });
}

void RenderNodeSystemR::NotifyMaterialChange(MaterialR* mtlChanged)
{
    auto renderNodesComps = mRenderWorld->Query<RenderNodeComponentR>();
    auto* GPUScene = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>()->GetWorldRenderPipeline()->GetGPUScene();
    for (auto renderNodeH : renderNodesComps)
    {
        auto writer = renderNodeH.Write();
        writer->GetRenderNode()->NotifyMaterialChange(*GPUScene, mtlChanged, this, renderNodeH.GetEntityID());
    }
}

void RenderNodeSystemR::SetRenderMeshes(RenderNodeComponentH renderNodeH, const std::vector<RenderMesh>& renderMeshes, RenderNodeType type)
{
    {
        RenderMeshChangeBeforeEvent event(renderNodeH.GetEntityID());
        DispatchImmediateEvent(event);
    }

    auto writer = renderNodeH.Write(ecs::ComponentAccessFlag::ComponentAccess);
    auto* _renderNode = dynamic_cast<NormalRenderNode*>(writer->mRenderNode.get());
    Assert(_renderNode);
    auto& renderNodes = _renderNode->mRenderNodes;

    renderNodes.resize(renderMeshes.size());   // Attention: RenderNode objects are changed

    for (size_t i = 0; i < renderNodes.size(); ++i)
    {
        auto& renderNode = renderNodes[i];
        auto& renderMesh = renderMeshes[i];
        renderNode.mLODs.resize(renderMesh.mLODCount);

        for (UInt32 lodIdx = 0; lodIdx < renderNode.mLODs.size(); ++lodIdx)
        {
            auto& lod = renderNode.mLODs[lodIdx];
            lod.mGeometry = renderMesh.mLODGeometries[lodIdx];
            lod.mMaterial = (0 < lodIdx && lodIdx <= renderMesh.mLODMaterials.size() && renderMesh.mLODMaterials[lodIdx - 1]) ? renderMesh.mLODMaterials[lodIdx - 1] : renderMesh.mDefaultMaterial;
        }

        renderNode.mBoundingSphere = renderMesh.mBoundingSphere;
        renderNode.mReceiveDecals = renderMesh.mReceiveDecals;
        renderNode.mProperties = std::move(renderMesh.mProperties);
        renderNode.mProperties.SetParent(&writer->mRenderNode->mObjectProperties);
    }

    bool enableCameraMask = true;
    UInt32 instanceCount = 0;
    for (const auto& renderMesh : renderMeshes)
    {
        Assert(instanceCount == 0 || instanceCount == static_cast<UInt32>(renderMesh.mInstanceCount));

        instanceCount = static_cast<UInt32>(renderMesh.mInstanceCount);
        enableCameraMask &= renderMesh.mEnableCameraMask;
    }

    _renderNode->mInstanceCount = instanceCount;
    _renderNode->mType = type;
    _renderNode->mEnableCameraMask = enableCameraMask;

    {
        RenderMeshChangeEvent event(renderNodeH.GetEntityID());
        DispatchImmediateEvent(event);
    }
}

void RenderNodeSystemR::SetObjectVar(RenderNodeComponentH renderNodeH, const NameID& propertyName, const float* val, size_t len)
{
    auto writer = renderNodeH.Write(ecs::ComponentAccessFlag::SubComponentAccess3);
    writer->mRenderNode->mObjectProperties.SetProperty(propertyName, val, len);
}

void RenderNodeSystemR::SetObjectVar(RenderNodeComponentH renderNodeH, const NameID& name, bool val)
{
    auto writer = renderNodeH.Write(ecs::ComponentAccessFlag::SubComponentAccess3);
    writer->mRenderNode->mObjectProperties.SetProperty(name, val);
}

void RenderNodeSystemR::SetObjectTexture(RenderNodeComponentH renderNodeH, const NameID& name, GPUTexture* texture)
{
    auto writer = renderNodeH.Write(ecs::ComponentAccessFlag::SubComponentAccess3);
    if (texture && texture->GetNGITextureView())
    {
        writer->mRenderNode->mObjectProperties.SetProperty(name, texture->GetNGITextureView());
    }
    else
    {
        writer->mRenderNode->mObjectProperties.RemoveProperty(name);
    }
}

void RenderNodeSystemR::OnFirstUpdate(FrameParam* frameParam) {}

RenderMesh::RenderMesh()
{
    auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto pool = rdrSys->GetRenderingExecutionDescriptor()->GetREDFrameAllocator();
    mProperties = std::move(RenderContext(pool));
}
}   // end namespace cross