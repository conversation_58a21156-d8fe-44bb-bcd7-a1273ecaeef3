#include "EnginePrefix.h"
#include "CrossBase/Threading/PresentThread.h"
#include "CrossBase/Math/CrossMath.h"
#include "CrossBase/Hash/Hash.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/GlobalSystemBase.h"
#include "CECommon/Common/GlobalSystemDesc.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Common/FrameCounter.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "NativeGraphicsInterface/NGIXRRuntime.h"
#include "NativeGraphicsInterface/NGITransientResourceManager.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderFactory.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDImGui/REDVisualizer.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDImGui/REDdebugGUI.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDImGui/ImguiwsVisualizer.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderContext.h"
#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/WindowSystemR.h"
#include "RenderEngine/RenderWindowR.h"
#include "RenderEngine/MultiClientFrameSync.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "RenderEngine/AABBSystemR.h"
#include "RenderEngine/RendererVoxelizeHelper.h"
#include "RenderEngine/VoxelizeSystemR.h"
#include "RenderEngine/TerrainSystemR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"
#include "CECommon/Common/FrameStdContainer.h"
#include "CECommon/Common/CmdSettings.h"
#include "FrameSynchronizationSystemR.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
void SystemRegisterR();
namespace cross {

using CrossSchema::ShaderResourceType;

constexpr UInt32 gDrawUnitIncSize = 256;

RendererSystemR::QueuePresentTriggerCbs RendererSystemR::sQueuePresentTriggerCbs;
std::mutex RendererSystemR::sCallbackMutex;

std::chrono::duration<double, std::micro> RENDER_ENGINE_API PreVBlankDistance(std::chrono::steady_clock::time_point inPresentReadyTimestamp)
{
    std::unique_lock<std::mutex> lock(gVBlanksMutex);

    auto itr = std::find_if(gVBlankTimestamps.rbegin(), gVBlankTimestamps.rend(), [=](auto& ts) {
        if (ts <= inPresentReadyTimestamp)
            return true;
        return false;
    });

    if (itr == gVBlankTimestamps.rend())
        return std::chrono::duration<double, std::micro>(1);

    return inPresentReadyTimestamp - *itr;
}

UInt64 RENDER_ENGINE_API PreVBlankTimestampInUs()
{
    std::unique_lock<std::mutex> lock(gVBlanksMutex);
    return 0;
}

void RendererSystemR::Release()
{
    delete this;
}

void RendererSystemR::NotifyShutdownEngine() 
{
    mImguiwsVisualizer.reset();
    mREDdebugGUI.reset();
    mREDVisualizer.reset();
    SavePipelineCache();
}

void RendererSystemR::Flush()
{
    auto frameParam = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam();
    mFrameResourceReady->Wait(frameParam->GetFrameCount() + CmdSettings::Inst().gMaxQueuedFrame - 1, UINT64_MAX);

    if (EngineGlobal::GetSettingMgr()->GetUseSeparatePresentThread())
    {
        threading::FlushPresentCommands();
    }
}

void RendererSystemR::CancelReadBack(ReadBackSession session)
{
    mPostRenderTasks.push_back([=]() {
        for (auto itr = mPendingCopyBackTasks.begin(); itr != mPendingCopyBackTasks.end(); )
        {
            if (itr->Session == session)
            {
                DestroyNGIObject(std::move(itr->StagingBuffer));
                itr = mPendingCopyBackTasks.erase(itr);
            }
            else
            {
                ++itr;
            }
        }
    });
}

void RendererSystemR::ReadBackREDTexture(REDTexture* texture, UInt32 subResource, NGIOffset3D offset, NGIExtent3D extent, void* dst, SizeType size, ReadBackSession session)
{
    mPostRenderTasks.push_back([=]() {
        auto& texDesc = texture->GetDesc();
        auto texelBlockProp = GetFormatTexelBlockProperty(texDesc.Format);
        Assert(texelBlockProp.Width == 1 && texelBlockProp.Height == 1);
        auto bufferPitch = texDesc.Width * texelBlockProp.Size;
        auto bufferRange = bufferPitch * texDesc.Height;
        Assert(size >= bufferRange);
        NGICopyBufferTexture region{
            0,
            bufferRange,
            bufferPitch,
            subResource,
            offset,
            extent,
        };
        NGIBufferDesc stagingBufferDesc{size, NGIBufferUsage::CopyDst};
        std::unique_ptr<NGIStagingBuffer> stagingBufferNGI{GetNGIDevice().CreateStagingBuffer(stagingBufferDesc)};

        auto stagingBufferRED = mRED->AllocateBuffer(texture->GetName(), stagingBufferNGI.get(), NGIResourceState::HostRead);
        mRED->AllocatePass("Copy back REDTexture")->CopyTextureToBuffer(stagingBufferRED, texture, 1, &region);
        mPendingCopyBackTasks.emplace_back(mCurrentFrame, std::move(stagingBufferNGI), dst, size, session);
    });
}
// read back camera must be called from update

void RendererSystemR::ReadBackRenderTexture(RenderTextureR* renderTexture, UInt32 subResource, NGIOffset3D offset, NGIExtent3D extent, void* dst, SizeType size)
{
    mPostRenderTasks.push_back(
        [=]()
        {
            auto& texDesc = renderTexture->GetREDTexture()->GetDesc();
            auto texelBlockProp = GetFormatTexelBlockProperty(texDesc.Format);
            Assert(texelBlockProp.Width == 1 && texelBlockProp.Height == 1);
            auto bufferPitch = texDesc.Width * texelBlockProp.Size;
            auto bufferRange = bufferPitch * texDesc.Height;
            Assert(size >= bufferRange);
            NGICopyBufferTexture region{
                0,
                bufferRange,
                bufferPitch,
                subResource,
                offset,
                extent,
            };
            NGIBufferDesc stagingBufferDesc{ size, NGIBufferUsage::CopyDst };
            std::unique_ptr<NGIStagingBuffer> stagingBufferNGI{ GetNGIDevice().CreateStagingBuffer(stagingBufferDesc) };

            auto stagingBufferRED = mRED->AllocateBuffer(renderTexture->GetInfo().Name, stagingBufferNGI.get(), NGIResourceState::HostRead);
            auto testeImg = renderTexture->GetREDTexture();
            mRED->AllocatePass("Copy back offscreen camera")->CopyTextureToBuffer(stagingBufferRED, testeImg, 1, &region);
            mPendingCopyBackTasks.emplace_back(mCurrentFrame, std::move(stagingBufferNGI), dst, size);
        });
}

void RendererSystemR::UnwrapCubemapToTexture2D(const std::array<RenderTextureR*, 6>& cubemapFaces, RenderTextureR* texture2D)
{
    mPostRenderTasks.push_back([=] 
        {
            const static std::string FaceNames[6] = {"Front", "Right", "Back", "Left", "Top", "Bottom"};
            const static SInt32 faceWitdthsIndex[6] = {1, 2, 3, 0, 1, 1};
            const static SInt32 faceHeightsIndex[6] = {1, 1, 1, 1, 0, 2};
            auto* allFaceTexture = texture2D->GetREDTexture();
            for (UInt32 faceId = 0; faceId < 6; ++faceId)
            {
                auto* srcTex = cubemapFaces[faceId]->GetREDTexture();
                auto width = srcTex->GetDesc().Width;
                NGIOffset3D offset = {faceWitdthsIndex[faceId] * (SInt32)width, faceHeightsIndex[faceId] * (SInt32)width, 0};
                NGICopyTexture region{0, {}, 0, offset, {width, width, 1}};
                std::string PassName = "Copy Cubemap face " + FaceNames[faceId];
                mRED->AllocatePass(PassName)->CopyTextureToTexture(allFaceTexture, srcTex, 1, &region);
            }
        });
}

void RendererSystemR::BakeTextureProcess(void* data, UInt32 width, UInt32 height, GraphicsFormat format, MaterialR* material, std::string nameSuffix, NameID const& passID, RenderContext&& ctx)
{
    auto texelBlockProp = GetFormatTexelBlockProperty(format);
    auto bufferPitch = width * texelBlockProp.Size;
    auto bufferRange = bufferPitch * height;

    NGICopyBufferTexture region{
        0,
        bufferRange,
        bufferPitch,

        0,
        {
            0,
            0,
            0,
        },
        {
            width,
            height,
            1,
        },
    };
    NGIBufferDesc stagingBufferDesc{
        bufferRange,
        NGIBufferUsage::CopyDst,
    };

    std::unique_ptr<NGIStagingBuffer> stagingBuffer{GetNGIDevice().CreateStagingBuffer(stagingBufferDesc)};
    auto* redStagingBuffer = mRED->AllocateBuffer("CopyStagingBuffer" + nameSuffix, stagingBuffer.get());
    auto* bakeTexture = mRED->AllocateTexture("BakeTexture" + nameSuffix, NGITextureDesc{format, NGITextureType::Texture2D, 1, 1, width, height, 1, 1, NGITextureUsage::RenderTarget | NGITextureUsage::CopySrc});
    NGITextureAspect aspect = NGITextureAspect::Color;
    auto* bakeTextureView = mRED->AllocateTextureView(bakeTexture,
                                                      NGITextureViewDesc{NGITextureUsage::RenderTarget | NGITextureUsage::CopySrc,
                                                                         format,
                                                                         NGITextureType::Texture2D,
                                                                         {
                                                                             aspect,
                                                                             0,
                                                                             1,
                                                                             0,
                                                                             1,
                                                                         }});

    NGIClearValue clearValue{{0, 0, 0, 0}};
    REDColorTargetDesc colorTargetDesc{
        bakeTextureView,
        NGILoadOp::Clear,
        NGIStoreOp::Store,
        clearValue,
    };
    mRED->BeginRenderPass("RenderShadingBlitPass" + nameSuffix, 1, &colorTargetDesc, nullptr);
    auto colorTargetIndex = NGIRenderPassTargetIndex::Target0;

    NGIViewport viewport{0, 0, static_cast<float>(width), static_cast<float>(height), 0, 1};
    NGIScissor scissor{0, 0, width, height};

    auto mtlState = material->GetMaterialRenderState(passID);
    InputLayoutDesc mProcessInputLayoutDesc{};
    VertexStreamLayout streamLayout{};
    streamLayout.AddVertexChannelLayout({VertexChannel::Position0, VertexFormat::Float3, 0});
    streamLayout.AddVertexChannelLayout({VertexChannel::TexCoord0, VertexFormat::Float2, sizeof(float) * 3});
    mProcessInputLayoutDesc.AddVertexStreamLayout(streamLayout);

    auto* subPass = mRED->AllocateSubRenderPass("shadingBlitSubPass" + nameSuffix, 0, nullptr, 1, &colorTargetIndex, REDPassFlagBit{0});
    subPass->Execute([=](REDPass* pass, NGIBundleCommandList* cmdList) mutable {
        cmdList->SetViewports(1, &viewport);
        cmdList->SetScissors(1, &scissor);
        NGIBuffer* vbs[]{GetFullScreenTriangle()};
        cmdList->SetVertexBuffers(1, vbs, nullptr);

        const void* shaderConst = nullptr;
        if (mtlState.mProgram->ShaderConstantLayout && mtlState.mProgram->ShaderConstantLayout->ByteSize > 0)
        {
            shaderConst = mtlState.mShaderConstants;
        }

        NGIGraphicsPipelineStateDesc pipelineDesc
        {
            pass->GetRenderPass(),
            pass->GetSubpass(),
            mtlState.mProgram->GUID,
            &mtlState.mProgram->GraphicsProgramDesc,
            mtlState.mProgram->PipelineLayout,
            mProcessInputLayoutDesc.GetHash().GetHash(),
            &mProcessInputLayoutDesc,
            PrimitiveTopology::TriangleList,
            *mtlState.mRaterizationState,
            *mtlState.mBlendState,
            *mtlState.mDepthStencilState,
            mtlState.mProgram->ShaderConstantLayout ? mtlState.mProgram->ShaderConstantLayout->ByteSize : 0,
            shaderConst,
        };

        auto* pipeline = GetGraphicsPipelineStatePool()->AllocateGraphicsPipelineState(pipelineDesc);
        cmdList->SetGraphicsPipelineState(pipeline);

        auto* pg = pass->GetContext().GetPassResourceGroup(mtlState.mProgram->ResourceGroupLayouts[ShaderParamGroup_Pass], mtlState.mProgram->PipelineLayout, ShaderParamGroup_Pass);
        if (pg)
        {
            cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Pass, pg);
        }

        auto* mg = mtlState.GetResourceBinding();
        if (mg)
        {
            cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Material, mg);
        }
        cmdList->DrawInstanced(3, 1, 0, 0);
    });
    subPass->SetRenderContext(std::move(ctx));
    mRED->EndRenderPass();

    auto* copySubPass = mRED->AllocatePass("copyPass" + nameSuffix);
    copySubPass->CopyTextureToBuffer(redStagingBuffer, bakeTexture, 1, &region);

    redStagingBuffer->SetExternalState(NGIResourceState::HostRead);

    mPendingCopyBackTasks.emplace_back(mCurrentFrame, std::move(stagingBuffer), data, bufferRange);
}

void RendererSystemR::CopyReadBackBuffer(REDPass::BufferType src, const SizeType bufferSize, const NGICopyBuffer& region, void* output)
{
    NGIBufferDesc desc{ bufferSize, NGIBufferUsage::CopyDst };
    std::unique_ptr<NGIStagingBuffer> stagingBuffer{GetNGIDevice().CreateStagingBuffer(desc)};
    auto dst = mRED->AllocateBuffer("CopyReadBackDstBuffer", stagingBuffer.get(), NGIResourceState::HostRead);
    mRED->AllocatePass("CopyReadBackPass")->CopyBufferToBuffer(dst, src, 1, &region);
    mPendingCopyBackTasks.emplace_back(mCurrentFrame, std::move(stagingBuffer), output, bufferSize);
}

void RendererSystemR::CopyReadBackBufferDynamic(REDPass::BufferType src, const NGICopyBuffer& region, std::pmr::vector<UInt8>* output)
{
    auto pass = mRED->AllocatePass("DrawUnitFeedBack");
    NGIBufferDesc desc{0, NGIBufferUsage::CopyDst};
    std::unique_ptr<NGIStagingBuffer> stagingBuffer{GetNGIDevice().CreateStagingBuffer(desc)};
    auto dst = mRED->AllocateBuffer("CopyReadBackDstBufferDynamic", stagingBuffer.get(), NGIResourceState::HostRead);
    NGICopyBuffer copy_region{region.SrcOffset, region.DstOffset, 0};
    pass->CopyBufferToBuffer(dst, src, 1, &copy_region);
    mRED->QueueBufferFeedBackDynamic(std::get<REDBuffer*>(src), stagingBuffer.get(), output);
    mPendingCopyBackTasks.emplace_back(mCurrentFrame, std::move(stagingBuffer), output, 0);
}

void RendererSystemR::ReadBackBuffer(std::unique_ptr<NGIStagingBuffer> buffer, void* dst, SizeType size)
{
    mPendingCopyBackTasks.emplace_back(mCurrentFrame, std::move(buffer), dst, size);
}

[[deprecated]]
void RendererSystemR::BakeVoxelTexture(void* dummyData, void* albedoData, void* normalData, void* emissiveData, SizeType byteSize, UInt32 width, UInt32 height, UInt32 depth, UInt32 numVoxelDirections, Float3 voxelSize,
                                       GraphicsFormat format, RenderWorld* renderWorld, ecs::EntityID eID)
{
    auto* transformSystemR = renderWorld->GetRenderSystem<TransformSystemR>();
    auto* voxelizeSystemR = renderWorld->GetRenderSystem<VoxelizeSystemR>();

    mRED->BeginRegion("BakeVoxelTexture");
    UInt32 viewWidth = 256;
    UInt32 viewHeight = 256;
    GraphicsFormat dummyFormat = GraphicsFormat::R8G8B8A8_UNorm;

    auto* cameraSysR = renderWorld->GetRenderSystem<CameraSystemR>();
    auto cameraID = cameraSysR->GetMainCamera();
    auto cameraComp = renderWorld->GetComponent<CameraComponentR>(cameraID);
    const RenderCamera* renderCamera = cameraSysR->GetRenderCamera(cameraComp.Read());
    IRenderPipeline* renderPipeline = cameraSysR->GetRenderPipeline(cameraComp.Read());

    //auto drawFilter = REDDrawFilter{const_cast<RenderWorld*>(renderWorld), renderCamera, NameID("VoxelizePass"), 0, gRenderGroupUI - 1, 0, nullptr};

    auto voxelizeDummy = IRenderPipeline::CreateTextureView2D("Smart.voxelizeDummy", viewWidth, viewHeight, dummyFormat, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc);
    NGIViewport viewport{0, 0, static_cast<float>(viewWidth), static_cast<float>(viewHeight), 0, 1};
    NGIScissor scissor{0, 0, viewWidth, viewHeight};

    REDColorTargetDesc colorTargetDesc{
        voxelizeDummy,
        NGILoadOp::DontCare,
        NGIStoreOp::Store,
        NGIClearValue{0, 0, 0, 0},
    };

    NGITexture *albedoTex, *normalTex, *emissiveTex;
    voxelizeSystemR->GetVoxelizeTexture(eID, albedoTex, normalTex, emissiveTex);

    auto* REDAlbedoTexture = mRED->AllocateTexture("Smart.VoxelAlbedo", albedoTex);
    auto* REDNormalTexture = mRED->AllocateTexture("Smart.VoxelNormal", normalTex);
    auto* REDEmissiveTexture = mRED->AllocateTexture("Smart.VoxelEmissive", emissiveTex);
    auto albedoTexture = mRED->AllocateTextureView(REDAlbedoTexture,
                                                  NGITextureViewDesc{NGITextureUsage::UnorderedAccess,
                                                                     REDAlbedoTexture->mDesc.Format,
                                                                     NGITextureType::Texture3D,
                                                                     {
                                                                         NGITextureAspect::Color,
                                                                         0,
                                                                         1,
                                                                         0,
                                                                         1,
                                                                     }});
    albedoTexture->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);

    auto normalTexture = mRED->AllocateTextureView(REDNormalTexture,
                                                   NGITextureViewDesc{NGITextureUsage::UnorderedAccess,
                                                                      REDNormalTexture->mDesc.Format,
                                                                      NGITextureType::Texture3D,
                                                                      {
                                                                          NGITextureAspect::Color,
                                                                          0,
                                                                          1,
                                                                          0,
                                                                          1,
                                                                      }});
    normalTexture->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);

    auto emissiveTexture = mRED->AllocateTextureView(REDEmissiveTexture,
                                                     NGITextureViewDesc{NGITextureUsage::UnorderedAccess,
                                                                        REDEmissiveTexture->mDesc.Format,
                                                                        NGITextureType::Texture3D,
                                                                        {
                                                                            NGITextureAspect::Color,
                                                                            0,
                                                                            1,
                                                                            0,
                                                                            1,
                                                                        }});
    emissiveTexture->SetExternalState(NGIResourceState::UnorderedAccessBit | NGIResourceState::ShaderStageBitMask);
    //auto albedoTexture = IRenderPipeline::CreateTextureView3D("Smart.VoxelAlbedo", width, height, depth, format, NGITextureUsage::CopySrc | NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);
    //auto normalTexture = IRenderPipeline::CreateTextureView3D("Smart.VoxelNormal", width, height, depth, format, NGITextureUsage::CopySrc | NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);
    //auto emissiveTexture = IRenderPipeline::CreateTextureView3D("Smart.VoxelEmissive", width, height, depth, format, NGITextureUsage::CopySrc | NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);

    // ClearVoxelizeTexture
    {
        FFSRenderPipelineSetting* rpSettings = dynamic_cast<FFSRenderPipelineSetting*>(EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting());
        auto* SmartVoxelizerUpdateShader = rpSettings->mIndirectLightingCompositeSettings.mSmartGISetting.SmartVoxelizerUpdateShaderR;
        auto* pass = mRED->AllocatePass("ClearVoxelizeTexture");
        UInt3 threadCount(width, height, depth);
        pass->SetProperty("RWNormalTexture", normalTexture);
        pass->SetProperty("RWAlbedoTexture", albedoTexture);
        pass->SetProperty("RWEmissiveTexture", emissiveTexture);
        pass->SetProperty("ClipmapIndex", 0);
        pass->SetProperty("ClipmapResolution", threadCount);
        UInt3 groupSize;
        SmartVoxelizerUpdateShader->GetThreadGroupSize("ClearVoxelizeSceneCS", groupSize.x, groupSize.y, groupSize.z);
        pass->Dispatch(SmartVoxelizerUpdateShader, "ClearVoxelizeSceneCS", (threadCount.x + groupSize.x - 1) / groupSize.x, (threadCount.y + groupSize.y - 1) / groupSize.y, (threadCount.z + groupSize.z - 1) / groupSize.z);
    }

    const Int3 ClipmapResolution{static_cast<SInt32>(width), static_cast<SInt32>(height), static_cast<SInt32>(depth / numVoxelDirections)};
    for (auto directionIdx = 0; directionIdx < 3; directionIdx++)
    {
        mRED->BeginRenderPass("VoxelizeScenePass_Axis_" + std::to_string(directionIdx), 1, &colorTargetDesc, nullptr);
        {
            auto colorTargetIndex = NGIRenderPassTargetIndex::Target0;
            REDPass* voxelizePass = mRED->AllocateSubRenderPass("VoxelizeScenePass", 0, nullptr, 1, &colorTargetIndex, REDPassFlagBit::StencilReadOnly);
            renderPipeline->UpdateCameraContext(renderCamera);
            //dynamic_cast<FFSRenderPipeline*>(renderPipeline)->UpdateVTContext(voxelizePass);

            Float3A centerPosition = transformSystemR->GetWorldTranslation(eID);
            Float3 clipBoundingSize{ClipmapResolution.x * voxelSize.x, ClipmapResolution.y * voxelSize.y, ClipmapResolution.z * voxelSize.z};
            Float4 clipmapWorldToUVScale{1.f / clipBoundingSize.x, 1.f / clipBoundingSize.y, 1.f / clipBoundingSize.z, 0};
            Float4 clipmapWorldToUVBias{0.5f, 0.5f, 0.5f, 0};
            FFSRenderPipelineSetting* rpSettings = dynamic_cast<FFSRenderPipelineSetting*>(EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting());
            RendererVoxelizeHelper::InitVoxelPassRenderContext(&voxelizePass->GetContext(),
                                                               true,
                                                               albedoTexture,
                                                               normalTexture,
                                                               emissiveTexture,
                                                               nullptr,
                                                               directionIdx,
                                                               0,
                                                               renderCamera->GetTilePosition(),
                                                               clipmapWorldToUVScale,
                                                               clipmapWorldToUVBias,
                                                               Float4(centerPosition, 1.f),
                                                               Float4(clipBoundingSize / 2.f, 1.f),
                                                               ClipmapResolution,
                                                               rpSettings->mIndirectLightingCompositeSettings.mSmartGISetting.mDiffuseBoost,
                                                               renderCamera);


            //for (auto& drawUnit : drawUnits)
            //{
            //    Float4A scale, translation;
            //    QuaternionA rotation;
            //    drawUnit.worldMatrix.Decompose(scale, rotation, translation);
            //    drawUnit.worldMatrix = Float4x4::Compose(scale, rotation, translation - Float4(centerPosition.x, centerPosition.y, centerPosition.z, 0.0f));
            //    drawUnit.tilePosition = Float3();
            //    drawUnit.preWorldMatrix = drawUnit.worldMatrix;
            //    drawUnit.preTilePosition = Float3();
            //}
            voxelizePass->Execute([=](REDPass* pass, NGIBundleCommandList* cmdList) mutable {
                cmdList->SetViewports(1, &viewport);
                cmdList->SetScissors(1, &scissor);

                //RendererVoxelizeHelper::RenderVoxelDrawCommand(pass, cmdList, drawUnits, NameID("VoxelizePass"));
                Assert(false);
            });
        }
        mRED->EndRenderPass();
    }

    auto texelBlockProp = GetFormatTexelBlockProperty(format);
    auto bufferPitch = width * texelBlockProp.Size;
    auto bufferRange = bufferPitch * height * depth;

    NGICopyBufferTexture region{
        0,
        bufferRange,
        bufferPitch,
        0,
        {
            0,
            0,
            0,
        },
        {
            width,
            height,
            depth,
        },
    };
    NGIBufferDesc stagingBufferDesc{
        bufferRange,
        NGIBufferUsage::CopyDst,
    };

    if(true){
        std::unique_ptr<NGIStagingBuffer> stagingAlbedoBuffer{GetNGIDevice().CreateStagingBuffer(stagingBufferDesc)};
        auto* redStagingAlbedoBuffer = mRED->AllocateBuffer("CopyVoxelAlbedoStagingBuffer", stagingAlbedoBuffer.get());

        auto* copySubPass = mRED->AllocatePass("copyPass");
        copySubPass->CopyTextureToBuffer(redStagingAlbedoBuffer, albedoTexture->mTexture, 1, &region);
        redStagingAlbedoBuffer->SetExternalState(NGIResourceState::HostRead);
        mPendingCopyBackTasks.emplace_back(mCurrentFrame, std::move(stagingAlbedoBuffer), albedoData, bufferRange);
    }

    if(true){
        std::unique_ptr<NGIStagingBuffer> stagingNormalBuffer(GetNGIDevice().CreateStagingBuffer(stagingBufferDesc));
        auto* redStagingNormalBuffer = mRED->AllocateBuffer("CopyVoxelNormalStagingBuffer", stagingNormalBuffer.get());

        auto* copySubPass = mRED->AllocatePass("copyPass");
        copySubPass->CopyTextureToBuffer(redStagingNormalBuffer, normalTexture->mTexture, 1, &region);
        redStagingNormalBuffer->SetExternalState(NGIResourceState::HostRead);
        mPendingCopyBackTasks.emplace_back(mCurrentFrame, std::move(stagingNormalBuffer), normalData, bufferRange);
    }

    if(true){
        std::unique_ptr<NGIStagingBuffer> stagingEmissiveBuffer(GetNGIDevice().CreateStagingBuffer(stagingBufferDesc));
        auto* redStagingEmissiveBuffer = mRED->AllocateBuffer("CopyVoxelEmissiveStagingBuffer", stagingEmissiveBuffer.get());

        auto* copySubPass = mRED->AllocatePass("copyPass");
        copySubPass->CopyTextureToBuffer(redStagingEmissiveBuffer, emissiveTexture->mTexture, 1, &region);
        redStagingEmissiveBuffer->SetExternalState(NGIResourceState::HostRead);
        mPendingCopyBackTasks.emplace_back(mCurrentFrame, std::move(stagingEmissiveBuffer), emissiveData, bufferRange);
    }

    {
        UInt32 copyWidth = 4;
        UInt32 copyHeight = 4;
        auto dummyBufferPitch = copyWidth * texelBlockProp.Size;
        auto dummyBufferRange = dummyBufferPitch * copyHeight;
        NGICopyBufferTexture dummyRegion{
            0,
            dummyBufferRange,
            dummyBufferPitch,

            0,
            {
                0,
                0,
                0,
            },
            {
                copyWidth,
                copyHeight,
                1,
            },
        };
        {
            std::unique_ptr<NGIStagingBuffer> stagingBufferDummy{GetNGIDevice().CreateStagingBuffer({dummyBufferRange, NGIBufferUsage::CopyDst})};
            auto* redStagingBufferDummy = mRED->AllocateBuffer("dummyTexture", stagingBufferDummy.get());

            auto* copySubPass1 = mRED->AllocatePass("copyPass1");
            copySubPass1->CopyTextureToBuffer(redStagingBufferDummy, voxelizeDummy->mTexture, 1, &dummyRegion);

            redStagingBufferDummy->SetExternalState(NGIResourceState::HostRead);

            mPendingCopyBackTasks.emplace_back(mCurrentFrame, std::move(stagingBufferDummy), dummyData, dummyBufferRange);
        }
    }
    mRED->EndRegion();
}

void RendererSystemR::BakeTerrainTexture(void* data, SizeType byteSize, UInt32 width, UInt32 height, GraphicsFormat format, MaterialR* material)
{
    auto texelBlockProp = GetFormatTexelBlockProperty(format);
    auto bufferPitch = width * texelBlockProp.Size;
    auto bufferRange = bufferPitch * height;
    Assert(byteSize >= bufferRange);

    RenderContext ctx(nullptr);
    BakeTextureProcess(data, width, height, format, material, "Terrain", "forward", std::move(ctx));
}

void RendererSystemR::CalCubeCaptureMatrixInUECoordinates(Float3 viewPos, float nearPlane, float farPlane, std::array<Float4x4, CUBE_FACE_NUM>& viewMats, Float4x4& projMat)
{
    auto Float3UE2CE = [](const Float3& In) -> Float3 {
        return {
            In.x,
            In.z,
            -In.y
        };
    };

    // See UE ReflectionEnvironmentCapture.cpp CalcCubeFaceViewRotationMatrix
    static constexpr Float3 XAxis = Float3UE2CE({1.f, 0.f, 0.f});  // (1.f,  0.f,  0.f)
    static constexpr Float3 YAxis = Float3UE2CE({0.f, 1.f, 0.f});  // (0.f,  0.f, -1.f)
    static constexpr Float3 ZAxis = Float3UE2CE({0.f, 0.f, 1.f});  // (0.f,  1.f,  0.f)
    viewMats[0] = Float4x4::CreateLookAt(viewPos, viewPos + XAxis, YAxis);
    viewMats[1] = Float4x4::CreateLookAt(viewPos, viewPos - XAxis, YAxis);
    viewMats[2] = Float4x4::CreateLookAt(viewPos, viewPos + YAxis, -ZAxis);
    viewMats[3] = Float4x4::CreateLookAt(viewPos, viewPos - YAxis, ZAxis);
    viewMats[4] = Float4x4::CreateLookAt(viewPos, viewPos + ZAxis, YAxis);
    viewMats[5] = Float4x4::CreateLookAt(viewPos, viewPos - ZAxis, YAxis);
    
    float fov = MathUtils::ConvertToRadians(90.f);
    float aspectRatio = 1.0f;
    projMat = Float4x4::Zeros();
    auto renderMode = EngineGlobal::GetSettingMgr()->GetRenderMode();
    auto bUseReverseZ = true;
    switch (renderMode)
    {
    case NGIPlatform::D3D12:
    case NGIPlatform::Metal:
    {
        // metal or dx z[0,1]
        auto const halfFov = fov * 0.5f;
        auto const cot = std::cos(halfFov) / std::sin(halfFov);
        auto const detalInverse = 1.0f / (farPlane - nearPlane);
        projMat.m00 = cot / aspectRatio;
        projMat.m11 = cot;
        projMat.m22 = farPlane * detalInverse;
        projMat.m32 = -nearPlane * farPlane * detalInverse;
        projMat.m23 = 1.0f;
    }
    break;
    case NGIPlatform::Vulkan:
    {
        if (bUseReverseZ)
        {
            // Left-multiply original proj matrix by (-1, 1)x(0, 1)
            auto const halfFov = fov * 0.5f;
            auto const cot = std::cos(halfFov) / std::sin(halfFov);
            auto const detalInverse = 1.0f / (farPlane - nearPlane);
            projMat.m00 = cot / aspectRatio;
            projMat.m11 = cot;
            projMat.m22 = -farPlane * detalInverse + 1.0f;
            projMat.m32 = nearPlane * farPlane * detalInverse;
            projMat.m23 = 1.0f;
        }
        else
        {
            auto const halfFov = fov * 0.5f;
            auto const cot = std::cos(halfFov) / std::sin(halfFov);
            auto const detalInverse = 1.0f / (farPlane - nearPlane);
            projMat.m00 = cot / aspectRatio;
            projMat.m11 = cot;
            projMat.m22 = farPlane * detalInverse;
            projMat.m32 = -nearPlane * farPlane * detalInverse;
            projMat.m23 = 1.0f;
        }
    }
    break;
    case NGIPlatform::OpenGLES3:
    {
        projMat = Float4x4::CreatePerspectiveFieldOfView(fov, aspectRatio, nearPlane, farPlane, true);
    }
    break;
    default:
        Assert(false);
        break;
    }
}

void RendererSystemR::CalCubeCaptureMatrix(Float3 viewPos, float nearPlane, float farPlane, std::array<Float4x4, 6>& viewMats, Float4x4& projMat)
{
    static constexpr Float3 XAxis{1.f, 0.f, 0.f};
    static constexpr Float3 YAxis{0.f, 1.f, 0.f};
    static constexpr Float3 ZAxis{0.f, 0.f, 1.f};
    
    viewMats[0] = Float4x4::CreateLookAt(viewPos, viewPos + XAxis, YAxis);
    viewMats[1] = Float4x4::CreateLookAt(viewPos, viewPos - XAxis, YAxis);
    viewMats[2] = Float4x4::CreateLookAt(viewPos, viewPos + YAxis, -ZAxis);
    viewMats[3] = Float4x4::CreateLookAt(viewPos, viewPos + -YAxis, ZAxis);
    viewMats[4] = Float4x4::CreateLookAt(viewPos, viewPos + ZAxis, YAxis);
    viewMats[5] = Float4x4::CreateLookAt(viewPos, viewPos - ZAxis, YAxis);
    
    float fov = MathUtils::ConvertToRadians(90.f);
    float aspectRatio = 1.0f;
    projMat = Float4x4::Zeros();
    auto renderMode = EngineGlobal::GetSettingMgr()->GetRenderMode();
    auto bUseReverseZ = true;
    switch (renderMode)
    {
    case NGIPlatform::D3D12:
    case NGIPlatform::Metal:
    {
        // metal or dx z[0,1]
        auto const halfFov = fov * 0.5f;
        auto const cot = std::cos(halfFov) / std::sin(halfFov);
        auto const detalInverse = 1.0f / (farPlane - nearPlane);
        projMat.m00 = cot / aspectRatio;
        projMat.m11 = cot;
        projMat.m22 = farPlane * detalInverse;
        projMat.m32 = -nearPlane * farPlane * detalInverse;
        projMat.m23 = 1.0f;
    }
    break;
    case NGIPlatform::Vulkan:
    {
        if (bUseReverseZ)
        {
            // Left-multiply original proj matrix by (-1, 1)x(0, 1)
            auto const halfFov = fov * 0.5f;
            auto const cot = std::cos(halfFov) / std::sin(halfFov);
            auto const detalInverse = 1.0f / (farPlane - nearPlane);
            projMat.m00 = cot / aspectRatio;
            projMat.m11 = cot;
            projMat.m22 = -farPlane * detalInverse + 1.0f;
            projMat.m32 = nearPlane * farPlane * detalInverse;
            projMat.m23 = 1.0f;
        }
        else
        {
            auto const halfFov = fov * 0.5f;
            auto const cot = std::cos(halfFov) / std::sin(halfFov);
            auto const detalInverse = 1.0f / (farPlane - nearPlane);
            projMat.m00 = cot / aspectRatio;
            projMat.m11 = cot;
            projMat.m22 = farPlane * detalInverse;
            projMat.m32 = -nearPlane * farPlane * detalInverse;
            projMat.m23 = 1.0f;
        }
    }
    break;
    case NGIPlatform::OpenGLES3:
    {
        projMat = Float4x4::CreatePerspectiveFieldOfView(fov, aspectRatio, nearPlane, farPlane, true);
    }
    break;
    default:
        Assert(false);
        break;
    }
}

void RendererSystemR::BakeCubemapTexture(std::vector<void*> data, SizeType byteSize, UInt32 width, UInt32 height, GraphicsFormat format, MaterialR* material)
{
    auto texelBlockProp = GetFormatTexelBlockProperty(format);
    auto bufferPitch = width * texelBlockProp.Size;
    auto bufferRange = bufferPitch * height;
    Assert(byteSize >= bufferRange);
    Assert(data.size() >= 6);

    std::array<Float4x4, CUBE_FACE_NUM> viewMat;
    Float4x4 projMat;
    CalCubeCaptureMatrix(Float3(0, 0, 0), 1.f, 25.f, viewMat, projMat);

    Float3 sunDir = Float3(30, -75, 0);
    sunDir.Normalize();

    float SourceCubemapAngle = 0;
    float SourceCubemapRotation = SourceCubemapAngle * (PI / 180.f);
    Float2 SinCosSourceCubemapRotation{sin(SourceCubemapRotation), cos(SourceCubemapRotation)};
    Float3 SkyLightCaptureParameters{1.f, 0, 1.f};
    Float4 LowerHemisphereColor{0, 0, 0, 1};

    for (int faceIndex = 0; faceIndex < 6; faceIndex++)
    {
        Float4x4 invViewProjMat = (viewMat[faceIndex] * projMat).Inverted();
        RenderContext ctx(nullptr);
        ctx.SetProperty("u_InvViewProjMatrix", invViewProjMat);
        ctx.SetProperty("u_SunDirection", sunDir);
        ctx.SetProperty("CubeFace", static_cast<float>(faceIndex));
        ctx.SetProperty("SinCosSourceCubemapRotation", SinCosSourceCubemapRotation);
        ctx.SetProperty("SkyLightCaptureParameters", SkyLightCaptureParameters);
        ctx.SetProperty("LowerHemisphereColor", LowerHemisphereColor);
        BakeTextureProcess(data[faceIndex], width, height, format, material, "CubeFace" + std::to_string(faceIndex), "blit", std::move(ctx));
    }
}

void RendererSystemR::AddMtlScratchByte(int byteSize)
{
    std::lock_guard<std::mutex> lock(mMtlAddMutex);
    mMtlScratchByte += byteSize;
}

RendererSystemR::RendererSystemR()
{
}
RendererSystemR::~RendererSystemR()
{
}

void RendererSystemR::PreWindowCreationInit()
{
    auto* device = &GetNGIDevice();
    if (device)
    {        
        mCmdQue.reset(device->CreateCommandQueue({NGICommandQueueType::Direct}));
        mCopyQue.reset(device->CreateCommandQueue({NGICommandQueueType::Copy}));
        mPresentQue.reset(device->CreateCommandQueue({NGICommandQueueType::Compute}));

        GetXRRuntime()->Initialize(mCmdQue.get());
    }


    auto settingManager = EngineGlobal::GetSettingMgr();
    bool bEnableTransientResources = false;
    if (settingManager)
    {
        settingManager->GetValue("TransientResources.enable", bEnableTransientResources);
        if (bEnableTransientResources)
            mTransientResMgr = std::make_unique<NGITransientResourceManager>(device);
        else
            mTransientResMgr = std::make_unique<NGIResourceManager>(device);
    }
    else
        mTransientResMgr = std::make_unique<NGIResourceManager>(device);
    mPersistentResMgr = std::make_unique<PersistentResourceManager>(device);

    {
        auto pipelineCacheData = LoadPipelineCache();
        mPipelineStatePool.reset(device->CreatePipelineStatePool({pipelineCacheData.data(), pipelineCacheData.size()}));
    }

    mResourceGroupPool.reset(device->CreateResourceGroupPool({CmdSettings::Inst().gMaxQueuedFrame}));

    if (EngineGlobal::GetSettingMgr()->GetUseSeparatePresentThread())
    {
        // frame rendering before 1 was ready, used when seperate present thread was enabled
        mFrameRenderingReady.reset(device->CreateTimeLineFence(0));
    }
    mFrameCommandReady.reset(device->CreateTimeLineFence(0));
    // resources for frame from 1 to gFrameCount was ready
    mFrameResourceReady.reset(device->CreateTimeLineFence(CmdSettings::Inst().gMaxQueuedFrame));
    mCopyReady.reset(device->CreateTimeLineFence(0));
    mScratchBuffer.reset(device->CreateTransientBufferManager(NGITransientBufferManagerDesc{}));
    mConstantBufferAllocator = std::make_unique<NGIConstantBufferAllocator>();

    mRenderPrimitives = std::make_unique<RenderPrimitives>(this);

    mStellarMeshManager = std::make_unique<StellarMesh::StellarMeshManager>();

    mRED = std::make_unique<RenderingExecutionDescriptor>(mTransientResMgr.get(), mPersistentResMgr.get(), mScratchBuffer.get());

    mREDVisualizer = std::make_unique<REDVisualizer>(this);
    mREDdebugGUI = std::make_unique<REDdebugGUI>(this);
    mImguiwsVisualizer = std::make_unique<ImguiwsVisualizer>();
    mRED->SetVisualizer(mREDVisualizer.get());
}

void RendererSystemR::UpdateMaterialCache()
{
    std::lock_guard<std::mutex> lock(mMtlAddMutex);
    std::lock_guard<std::mutex> lockTemp(mMarkMaterialDirtyMutex);
    mMtlScratchByte = 0;
    
    SCOPED_CPU_TIMING(GroupRendering, "UpdateMaterialCache");
    for (const auto mtl: mDirtyCacheMaterial)
    {
        Assert(mtl);
        mtl->UpdateCache();
    }
    mDirtyCacheMaterial.clear();
}

void RendererSystemR::UpdateResources(FrameParam* fp)
{
    SCOPED_CPU_TIMING(GroupRendering, "UpdateResources");

    mImCmdListPre->BeginDebugRegion("UpdateResources Pre Cmd");
    mImCmdListCopy->BeginDebugRegion("UpdateResources Copy Cmd");

    // cmd clear texture must be created by queue with NGICommandQueueType::Direct
    for (auto& [texView, clearValue, stateBefore, stateAfter] : mClearTextures)
    {
        auto* tex = texView->GetTexture();
        EnumerateTextureSubresource(tex->GetDesc(), texView->GetDesc().SubRange, [&, texView = texView, clearValue = clearValue, stateBefore = stateBefore, stateAfter = stateAfter](UInt32 subres, UInt32 mip, UInt32 array, UInt32 plane) {
            NGITextureBarrier barrier1{
                tex,
                subres,
                stateBefore,
                NGIResourceState::CopyDst,
            };
            mImCmdListPre->ResourceBarrier(0, nullptr, 1, &barrier1);
        });
        mImCmdListPre->ClearTexture(texView, clearValue);
        EnumerateTextureSubresource(tex->GetDesc(), texView->GetDesc().SubRange, [&, texView = texView, clearValue = clearValue, stateBefore = stateBefore, stateAfter = stateAfter](UInt32 subres, UInt32 mip, UInt32 array, UInt32 plane) {
            NGITextureBarrier barrier2{
                tex,
                subres,
                NGIResourceState::CopyDst,
                stateAfter,
            };
            mImCmdListPre->ResourceBarrier(0, nullptr, 1, &barrier2);
        });
    }
    mClearTextures.clear();

    for (auto& [bufView, clearValue, stateBefore, stateAfter] : mClearBuffers)
    {
        auto* buf = bufView->GetBuffer();
        if (stateBefore != NGIResourceState::Undefined)
        {
            NGIBufferBarrier beforeBarrier{
                buf,
                stateBefore,
                NGIResourceState::CopyDst,
            };
            mImCmdListCopy->ResourceBarrier(1, &beforeBarrier, 0, nullptr);

        }
        mImCmdListCopy->ClearBuffer(bufView, clearValue);
        NGIBufferBarrier afterBarrier{
            buf,
            NGIResourceState::CopyDst,
            stateAfter,
        };
        mImCmdListPre->ResourceBarrier(1, &afterBarrier, 0, nullptr);

    }
    mClearBuffers.clear();

    std::map<std::pair<NGITexture*, UInt32>, std::pair<NGIResourceState, NGIResourceState>> copyDstTextures;
    std::map<std::pair<NGITexture*, UInt32>, std::pair<NGIResourceState, NGIResourceState>> copySrcTextures;
    std::map<NGIBuffer*, std::pair<NGIResourceState, NGIResourceState>> copyDstBuffers;
    std::map<NGIBuffer*, std::pair<NGIResourceState, NGIResourceState>> copySrcBuffers;

    // Assume that a texture will not be both a copy dst and a copy src at the same time; otherwise, a validation error will occur.
    // DefaultTexture.nda is an exception for now.
    for (auto& [dstTexture, source, region, dstStateBefore, dstStateAfter, srcStateBefore, srcStateAfter] : mUpdateTextures)
    {
        if (!dstTexture)
            continue;
        
        if (auto buffer = std::get_if<NGIStagingBuffer*>(&source); buffer && *buffer)
        {
            auto copyRegion = std::get<NGICopyBufferTexture>(region);
            copyDstTextures[{dstTexture, copyRegion.TextureSubresource}] = {dstStateBefore, dstStateAfter};
            copySrcBuffers[*buffer] = {srcStateBefore, srcStateAfter};
        }
        else if (auto texture = std::get_if<NGITexture*>(&source); texture && *texture)
        {
            auto copyRegion = std::get<NGICopyTexture>(region);
            copyDstTextures[{dstTexture, copyRegion.DstSubresource}] = {dstStateBefore, dstStateAfter};
            copySrcTextures[{*texture, copyRegion.SrcSubresource}] = {srcStateBefore, srcStateAfter};
        }
    }

    std::scoped_lock writerLock(mUpdateBufferMutex);

    for (auto& [dstBuffer, srcBuffer, region, dstStateBefore, dstStateAfter, srcStateBefore, srcStateAfter] : mUpdateBuffers)
    {
        if (!dstBuffer || !srcBuffer)
            continue;
        copyDstBuffers[dstBuffer] = {dstStateBefore, dstStateAfter};
        copySrcBuffers[srcBuffer] = {srcStateBefore, srcStateAfter};
    }

    auto* frameAllocator = fp->GetFrameAllocator();
    auto copySrcTextureBarriers = frameAllocator->CreateFrameContainer<FrameVector<NGITextureBarrier>>(FRAME_STAGE_RENDER, static_cast<UInt32>(copySrcTextures.size()));
    auto copySrcBufferBarriers = frameAllocator->CreateFrameContainer<FrameVector<NGIBufferBarrier>>(FRAME_STAGE_RENDER, static_cast<UInt32>(copySrcBuffers.size()));

    for (auto& [texturePair, statePair] : copySrcTextures)
    {
        copySrcTextureBarriers->EmplaceBack(texturePair.first, texturePair.second, NGIResourceState::Undefined, NGIResourceState::CopySrc);
    }

    for (auto& [buffer, statePair] : copySrcBuffers)
    {
        copySrcBufferBarriers->EmplaceBack(buffer, NGIResourceState::Undefined, NGIResourceState::CopySrc);
    }

    if (copySrcTextureBarriers->GetSize() || copySrcBufferBarriers->GetSize())
    {
        auto texBarriers = copySrcTextureBarriers->GetSize() ? &copySrcTextureBarriers->At(0) : nullptr;
        auto bufBarriers = copySrcBufferBarriers->GetSize() ? &copySrcBufferBarriers->At(0) : nullptr;
        mImCmdListCopy->ResourceBarrier(copySrcBufferBarriers->GetSize(), bufBarriers, copySrcTextureBarriers->GetSize(), texBarriers);
    }

    auto copyDstTextureBarriers = frameAllocator->CreateFrameContainer<FrameVector<NGITextureBarrier>>(FRAME_STAGE_RENDER, static_cast<UInt32>(copyDstTextures.size()));
    auto copyDstBufferBarriers = frameAllocator->CreateFrameContainer<FrameVector<NGIBufferBarrier>>(FRAME_STAGE_RENDER, static_cast<UInt32>(copyDstBuffers.size()));

    for (auto& [texturePair, statePair] : copyDstTextures)
    {
        copyDstTextureBarriers->EmplaceBack(texturePair.first, texturePair.second, NGIResourceState::Undefined, NGIResourceState::CopyDst);
    }

    for (auto& [buffer, statePair] : copyDstBuffers)
    {
        copyDstBufferBarriers->EmplaceBack(buffer, NGIResourceState::Undefined, NGIResourceState::CopyDst);
    }

    if (copyDstTextureBarriers->GetSize() || copyDstBufferBarriers->GetSize())
    {
        auto texBarriers = copyDstTextureBarriers->GetSize() ? &copyDstTextureBarriers->At(0) : nullptr;
        auto bufBarriers = copyDstBufferBarriers->GetSize() ? &copyDstBufferBarriers->At(0) : nullptr;
        mImCmdListCopy->ResourceBarrier(copyDstBufferBarriers->GetSize(), bufBarriers, copyDstTextureBarriers->GetSize(), texBarriers);
    }

    for (auto& [dstTexture, source, region, dstStateBefore, dstStateAfter, srcStateBefore, srcStateAfter] : mUpdateTextures)
    {
        if (!dstTexture)
            continue;
        
        if (auto buffer = std::get_if<NGIStagingBuffer*>(&source); buffer && *buffer)
        {
            auto copyRegion = std::get<NGICopyBufferTexture>(region);
            mImCmdListCopy->CopyBufferToTexture(dstTexture, *buffer, 1, &copyRegion);
        }
        else if (auto texture = std::get_if<NGITexture*>(&source); texture && *texture)
        {
            auto copyRegion = std::get<NGICopyTexture>(region);
            mImCmdListCopy->CopyTextureToTexture(dstTexture, *texture, 1, &copyRegion); // to-do: use array update
        }
        else
        {
            auto copyRegion = std::get<NGICopyBufferTexture>(region);
            NGITextureBarrier barrier{
                dstTexture,
                copyRegion.TextureSubresource,
                dstStateBefore,
                dstStateAfter,
            };
            mImCmdListPre->ResourceBarrier(0, nullptr, 1, &barrier);
        }
    }
    mUpdateTextures.clear();

    for (auto& [dstBuffer, srcBuffer, region, dstStateBefore, dstStateAfter, srcStateBefore, srcStateAfter] : mUpdateBuffers)
    {
        Assert(dstStateAfter != NGIResourceState::Undefined || srcStateBefore == NGIResourceState::Undefined);

        if (dstBuffer && srcBuffer)
        {
            mImCmdListCopy->CopyBufferToBuffer(dstBuffer, srcBuffer, 1, &region);
        }
    }
    mUpdateBuffers.clear();
    

    UInt32 ptr = 0;
    for (auto& [texturePair, statePair] : copySrcTextures)
    {
        copySrcTextureBarriers->At(ptr) = {texturePair.first, texturePair.second, NGIResourceState::CopySrc, statePair.second};
        ptr++;
    }

    ptr = 0;
    for (auto& [buffer, statePair] : copySrcBuffers)
    {
        copySrcBufferBarriers->At(ptr) = {buffer, NGIResourceState::CopySrc, statePair.second};
        ptr++;
    }

    if (copySrcTextureBarriers->GetSize() || copySrcBufferBarriers->GetSize())
    {
        auto texBarriers = copySrcTextureBarriers->GetSize() ? &copySrcTextureBarriers->At(0) : nullptr;
        auto bufBarriers = copySrcBufferBarriers->GetSize() ? &copySrcBufferBarriers->At(0) : nullptr;
        mImCmdListPre->ResourceBarrier(copySrcBufferBarriers->GetSize(), bufBarriers, copySrcTextureBarriers->GetSize(), texBarriers);
    }

    ptr = 0;
    for (auto& [texturePair, statePair] : copyDstTextures)
    {
        copyDstTextureBarriers->At(ptr) = {texturePair.first, texturePair.second, NGIResourceState::CopyDst, statePair.second};
        ptr++;
    }

    ptr = 0;
    for (auto& [buffer, statePair] : copyDstBuffers)
    {
        copyDstBufferBarriers->At(ptr) = {buffer, NGIResourceState::CopyDst, statePair.second};
        ptr++;
    }

    if (copyDstTextureBarriers->GetSize() || copyDstBufferBarriers->GetSize())
    {
        auto texBarriers = copyDstTextureBarriers->GetSize() ? &copyDstTextureBarriers->At(0) : nullptr;
        auto bufBarriers = copyDstBufferBarriers->GetSize() ? &copyDstBufferBarriers->At(0) : nullptr;
        mImCmdListPre->ResourceBarrier(copyDstBufferBarriers->GetSize(), bufBarriers, copyDstTextureBarriers->GetSize(), texBarriers);
    }

    mImCmdListCopy->EndDebugRegion();
    mImCmdListPre->EndDebugRegion();
}

void RendererSystemR::PostUpdateResources()
{
    SCOPED_CPU_TIMING(GroupRendering, "PostUpdateResources");
    SCOPED_NGI_CMD_LIST_DEBUG_REGION(mImCmdListPre, "PostUpdateResources");

    for (const auto& [destination, source, regions, dstBeforeState, dstAfterState, debugRegion] : mPostUpdateBuffers)
    {
        SCOPED_CPU_TIMING(GroupRendering, "PostUpdateBuffers");
        Assert(destination);

        SCOPED_NGI_CMD_LIST_DEBUG_REGION(mImCmdListPre, debugRegion);

        if (dstBeforeState != NGIResourceState::Undefined)
        {
            NGIBufferBarrier barrierBefore
            {
                destination,
                dstBeforeState,
                NGIResourceState::CopyDst,
            };
            mImCmdListPre->ResourceBarrier(1U, &barrierBefore, 0U, nullptr);
        }

        if (source && !regions.empty())
        {
            mImCmdListCopy->CopyBufferToBuffer(destination, source, static_cast<UInt32>(regions.size()), regions.data());
        }

        NGIBufferBarrier barrierAfter
        {
            destination,
            dstBeforeState == NGIResourceState::Undefined ? NGIResourceState::Undefined : NGIResourceState::CopyDst,
            dstAfterState,
        };
        mImCmdListPre->ResourceBarrier(1U, &barrierAfter, 0U, nullptr);
    }
    mPostUpdateBuffers.clear();

    for (const auto& [destination, source, regions, dstBeforeState, dstAfterState, srcBeforeState, srcAfterState, debugRegion] : mPostUpdateTextures)
    {
        SCOPED_CPU_TIMING(GroupRendering, "PostUpdateTexture");
        SCOPED_NGI_CMD_LIST_DEBUG_REGION(mImCmdListPre, debugRegion);

        if (auto sourceBuffer = std::get_if<NGIStagingBuffer*>(&source))
        {
            auto bufferRegions = std::get_if<std::vector<NGICopyBufferTexture>>(&regions);
            Assert(bufferRegions);

            if (*sourceBuffer && !bufferRegions->empty())
            {
                NGITextureBarrier barrierBefore
                {
                    destination,
                    NGIAllSubresources,
                    NGIResourceState::Undefined,
                    NGIResourceState::CopyDst,
                };

                mImCmdListCopy->ResourceBarrier(0U, nullptr, 1U, &barrierBefore);

                mImCmdListCopy->CopyBufferToTexture(destination, *sourceBuffer, static_cast<UInt32>(bufferRegions->size()), bufferRegions->data());
            }

            {
                NGITextureBarrier barrierAfter
                {
                    destination,
                    NGIAllSubresources,
                    dstBeforeState,
                    dstAfterState
                };

                mImCmdListPre->ResourceBarrier(0U, nullptr, 1U, &barrierAfter);
            }
        }
        else
        {
            auto sourceTexture = std::get_if<NGITexture*>(&source);
            auto textureRegions = std::get_if<std::vector<NGICopyTexture>>(&regions);
            Assert(sourceTexture && textureRegions);

            if (*sourceTexture && !textureRegions->empty())
            {
                NGITextureBarrier barriersBefore[]
                {
                    {
                        destination,
                        NGIAllSubresources,
                        NGIResourceState::Undefined,
                        NGIResourceState::CopyDst,
                    },
                    {
                        *sourceTexture,
                        NGIAllSubresources,
                        NGIResourceState::Undefined,
                        NGIResourceState::CopySrc,
                    }
                };


                mImCmdListCopy->ResourceBarrier(0U, nullptr, 2, barriersBefore);
  
                mImCmdListCopy->CopyTextureToTexture(destination, *sourceTexture, static_cast<UInt32>(textureRegions->size()), textureRegions->data());
            }

            {
                UInt32 barrierCount = 1;
                NGITextureBarrier barriersAfter[2]
                {
                    {
                        destination,
                        NGIAllSubresources,
                        dstBeforeState,
                        dstAfterState
                    },
                    
                };

                if (*sourceTexture && !textureRegions->empty())
                {
                    barriersAfter[1] = {*sourceTexture, NGIAllSubresources, NGIResourceState::CopySrc, srcAfterState};
                    barrierCount = 2;
                }

                mImCmdListPre->ResourceBarrier(0U, nullptr, barrierCount, barriersAfter);
            }
        }
    }

    mPostUpdateTextures.clear();
}

void RendererSystemR::OnFirstUpdate(FrameParam* frameParam) 
{
    bool value = false;
    bool bUseStats = CmdSettings::Inst().GetConfigValueByName<bool>("gUseEngineStats", value) && value;
    if (bUseStats)
        bShowTimeStats = true;
}

const GlobalSystemDesc& RendererSystemR::GetDesc()
{
    static const GlobalSystemDesc* sDesc = nullptr;
    if (!sDesc)
    {
        auto* descSystem = EngineGlobal::GetGlobalSystemDescSystem();
        sDesc = descSystem->CreateOrGetGlobalSystemDesc("RendererSystemR", false);
    }
    return *sDesc;
}

cross::RendererSystemR* RendererSystemR::CreateInstance()
{
    return new RendererSystemR();
}

void RendererSystemR::OnBeginFrame(FrameParam* frameParam)
{
#ifdef WIN32
    if (SLWrapper::Get().m_sl_initialised)
    {
        SLWrapper::Get().ReflexCallback_Sleep(frameParam->GetFrameCount());
    }
    //just for test 
   /* if (SLWrapper::Get().GetReflexAvailable())
    {
        SLWrapper::Get().ReflexCallback_SimStart(frameParam->GetFrameCount());
    }
    if (SLWrapper::Get().GetReflexAvailable())
    {
        SLWrapper::Get().ReflexCallback_SimEnd(frameParam->GetFrameCount());
    }*/
#endif
    SCOPED_CPU_TIMING(GroupRendering, "BeginRendeirngFrame");
    frameParam->SetRenderStartTime();
    //if (SLWrapper::Get().GetReflexAvailable())
    //    SLWrapper::Get().ReflexCallback_RenderStart(frameParam->GetFrameCount());
    mCurrentFrame = frameParam->GetFrameCount();
    // Persist current execute frame index into device, which start from 1
    RenderFactory::Instance().BeginRenderFrame(frameParam);

    mRED->mCurrentAllocator = std::move(FrameAllocatorPool(frameParam->GetFrameAllocator(), FRAME_STAGE_RENDER));

    while (!mPendingCopyBackTasks.empty())
    {
        auto& [frame, stagingBuffer, dst, range, _] = mPendingCopyBackTasks.front();
        Assert(mCurrentFrame > frame);
        if (mCurrentFrame - frame >= CmdSettings::Inst().gMaxQueuedFrame)
        {
            void* src = nullptr;
            void* dst_pointer = nullptr;

            


            std::visit(Overloaded{[&](void* arg) { dst_pointer = arg; }, [&](std::pmr::vector<UInt8> * arg) {
                                      arg->resize(stagingBuffer->GetSize());
                                      dst_pointer = arg->data();
                                      if (range == 0)
                                      {
                                          range = stagingBuffer->GetSize();
                                      }

            }

            }, dst);

            
            src = stagingBuffer->MapRange(NGIBufferUsage::CopyDst, 0, range);
            memcpy(dst_pointer, src, range);
            stagingBuffer->UnmapRange(0, range);
            mPendingCopyBackTasks.pop_front();
        }
        else
        {
            break;
        }
    }

    // vk calling
    {
        SCOPED_CPU_TIMING(GroupRendering, "ResourcePoolBegin");
        mCmdQue->BeginFrame(frameParam);                // flip current command list by max frame number
        mCopyQue->BeginFrame(frameParam);                // flip current command list by max frame number
        mPipelineStatePool->BeginFrame(frameParam);     // increase all existed GraphicsPipelineState by frame number
        mResourceGroupPool->BeginFrame(frameParam);   // descriptor set concerns
        mScratchBuffer->BeginFrame(frameParam);       // staging buffer concerns

        if (!EngineGlobal::GetSettingMgr()->GetUseSeparatePresentThread())
        {
            mTransientResMgr->BeginFrame(frameParam);       // buffer view concerns -- remove all unused native & view handles
        }
    }

    std::vector<std::unique_ptr<NGIObject>> mToDelete;
    // vk calling
    {
        auto renderDocEnabled = EngineGlobal::GetSettingMgr()->GetRenderdocEnable();
        SCOPED_CPU_TIMING(GroupRendering, "mDelayedDestroyObjects");
        std::unique_lock writerLock(mDestroyObjectsMutex);
  
        for (auto itr = mDelayedDestroyObjects.begin(); itr != mDelayedDestroyObjects.end();)
        {
            auto& [resource, lifetime] = *itr;
            if (--lifetime == 0)
            {
                // there are good chances that program is stucked when relese vulkan object in parallel when renderdoc is enabled
                if (gResourceMgr.ParrallelDeleteEnabled() && !renderDocEnabled)
                {
                    mToDelete.push_back(std::move(std::get<0>(*itr)));
                }
                itr = mDelayedDestroyObjects.erase(itr);
            }
            else
                ++itr;
        }
    }

    if (!mToDelete.empty())
    {
        const auto numWorkers = threading::TaskSystem::GetNumWorkerThreadsForTask();
        UInt32 batchSize = static_cast<UInt32>(mToDelete.size() / numWorkers + 1);
        cross::threading::ParallelFor(static_cast<SInt32>(numWorkers), [&](auto taskIndex) {
            for (UInt32 i = 0; i < batchSize; ++i)
            {
                UInt32 index = taskIndex * batchSize + i;
                if (index < mToDelete.size())
                {
                    mToDelete[index].reset();
                }
            }
        });
        mToDelete.clear();
    }

    mCopyQue->AllocateCommandLists(1, &mImCmdListScratchBuffer);
    mCopyQue->AllocateCommandLists(1, &mImCmdListCopy);

    mCmdQue->AllocateCommandLists(1, &mImCmdListPre);
    mCmdQue->AllocateCommandLists(1, &mImCmdListPost);

    mCmdQue->AllocateCommandLists(1, &mAccelStructList);

    NGICommandList::InitImmediateCommandList(mImCmdListPre);

    mImCmdListScratchBuffer->Begin();
    mImCmdListCopy->Begin();

    mImCmdListPre->Begin();
    mImCmdListPost->Begin();
    mAccelStructList->Begin();
    
    mRED->BeginFrame(mCurrentFrame);
    mREDVisualizer->BeginFrame(mCurrentFrame);
    // Config current frame time parameters
    auto time = frameParam->GetTime();
    Float4 shaderTime{time, time * 2, time * 3, time / 20};
    mRED->SetProperty(BuiltInProperty::ce_Time, shaderTime);
    mRED->SetProperty(BuiltInProperty::ce_PreTime, mLastFrameTime);
    mRED->SetProperty(BuiltInProperty::ce_UseDoubleTransform, true);
    mLastFrameTime = time;
    
    if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeStandAlone)
    {
        auto* windowsSystem = mRenderEngine->GetGlobalSystem<WindowSystemR>();
        auto* window = windowsSystem->GetAppWindow();

        mWindowRenderTarget = window->PrepareBackbuffer(mImCmdListPre);
        NGITextureViewDesc windowRenderTargetViewDesc{
            NGITextureUsage::RenderTarget,
            mWindowRenderTarget->mDesc.Format,
            NGITextureType::Texture2D,
            {
                NGITextureAspect::Color,
                0,
                1,
                0,
                1,
            },
        };
        mWindowRenderTargetView = mRED->AllocateTextureView(mWindowRenderTarget, windowRenderTargetViewDesc);
    }
}

void RendererSystemR::OnBeginFrameByPresentThread(FrameParam* frameParam)
{}

void RendererSystemR::OnUpdate(FrameParam* fp)
{
    std::pair<NGIStagingBuffer*, NGIStagingBuffer*> stagingBufPair{nullptr, nullptr};
    
    {
        SCOPED_CPU_TIMING(GroupRendering, "FrameRendering");

        UpdateMaterialCache();

        UpdateResources(fp);

        PostUpdateResources();

        for (const auto& lambda : mPostRenderTasks) 
        {
            lambda();
        }
        mPostRenderTasks.clear();

        mRED->CompileAndExecute(mImCmdListCopy, mImCmdListPre, mImCmdListPost, mImCmdLists, mCmdQue.get());

        GetScratchBuffer()->RecordBufferCopy(mImCmdListScratchBuffer, mImCmdListPre);
    }
}


void RendererSystemR::OnEndFrame(FrameParam* frameParam) 
{
    SCOPED_CPU_TIMING(GroupRendering, "EndRenderingFrame");

    mRED->EndFrame();
    if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeStandAlone)
    {
        auto backBuffer = mWindowRenderTarget->GetNativeTexture();

        if (!mOnBackBufferReady.empty())
        {
            for (auto& itr : mOnBackBufferReady)
                itr(backBuffer);

            NGITextureBarrier barrier1{backBuffer, NGICalcSubresource(0, 0, 0, 1, 1), NGIResourceState::Undefined, NGIResourceState::Present};

            mImCmdListPost->ResourceBarrier(0, nullptr, 1, &barrier1);
        }
    }

    mImCmdListScratchBuffer->End();
    mImCmdListCopy->End();
    mImCmdListPre->End();
    mImCmdListPost->End();
    mAccelStructList->End();
    
    // copy queue
    {
        UInt64 CopyReadyValue = frameParam->GetFrameCount();
        std::array<NGICommandList*, 2> copyCmdArray{mImCmdListScratchBuffer, mImCmdListCopy};
        mCopyQue->Wait(mFrameResourceReady.get(), mCurrentFrame + CmdSettings::Inst().gMaxQueuedFrame - 1);
        mCopyQue->ExecuteCommandLists(static_cast<UInt32>(copyCmdArray.size()), copyCmdArray.data());
        mCopyQue->Signal(mCopyReady.get(), CopyReadyValue);
        mCmdQue->Wait(mCopyReady.get(), CopyReadyValue);
    }

    mCmdQue->ExecuteCommandLists(1, &mAccelStructList);
    
    mImCmdLists.insert(mImCmdLists.begin(), {mImCmdListPre});
    mImCmdLists.push_back(mImCmdListPost);
    if (EngineGlobal::GetSettingMgr()->GetUseSeparatePresentThread())
    {
        {
            QUICK_SCOPED_CPU_TIMING("Wait ExecuteCommandLists");
            // mFrameCommandReady->Wait(frameParam->GetFrameCount() - 1, UINT64_MAX);
            threading::DispatchPresentCommand([=, curFrame = frameParam->GetFrameCount(), pSize = mImCmdLists.size(), pLists = mImCmdLists.data()]() {
                // set mRenderSubmit timestamp
                bool value = false;
                bool bUseStats = CmdSettings::Inst().GetConfigValueByName<bool>("gUseEngineStats", value) && value;
                if (bUseStats)
                {
                    auto& curFrameStats = EngineGlobal::GetRenderEngine()->GetGlobalSystem<FrameSynchronizationSystemR>()->mFrameStats;
                    curFrameStats->GetFrameTimer(frameParam->GetFrameCount()).mRenderSubmit = std::chrono::high_resolution_clock::now();
                }
                auto cmdQue = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetDefaultCommandQueue();
                cmdQue->ExecuteCommandLists(static_cast<UInt32>(pSize), pLists);
                mFrameCommandReady->Signal(curFrame + CmdSettings::Inst().gMaxQueuedFrame);
            });
        }

        //if (frameParam->GetFrameCount() > 1)
        {
            //mFrameRenderingReady->Signal(frameParam->GetFrameCount());
            threading::DispatchPresentCommand([=, curFrame = frameParam->GetFrameCount()]() {
                //auto frameRenderingReady = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetFrameRenderingReadyFence();
                //frameRenderingReady->Wait(curFrame, UINT64_MAX);
                mFrameCommandReady->Wait(curFrame + CmdSettings::Inst().gMaxQueuedFrame, UINT64_MAX);
                auto* window = EngineGlobal::GetRenderEngine()->GetGlobalSystem<WindowSystemR>()->GetAppWindow();
                if (typeid(*window) == typeid(RenderWindowR))
                {
                    if (!window->Present())
                        LOG_ERROR("Swapchain present failed");
                    mCmdQue->Signal(mFrameResourceReady.get(), curFrame + CmdSettings::Inst().gMaxQueuedFrame);
                    {
                        std::scoped_lock lock(RendererSystemR::sCallbackMutex);
                        std::for_each(RendererSystemR::sQueuePresentTriggerCbs.begin(), RendererSystemR::sQueuePresentTriggerCbs.end(), 
                            [=](auto& cb) { cb.second(curFrame + CmdSettings::Inst().gMaxQueuedFrame); });
                    }
                }
            });
        }
    }
    else
    {
        mCmdQue->ExecuteCommandLists(static_cast<UInt32>(mImCmdLists.size()), mImCmdLists.data());
        // set mRenderSubmit timestamp
        bool value = false;
        bool bUseStats = CmdSettings::Inst().GetConfigValueByName<bool>("gUseEngineStats", value) && value;
        if (bUseStats)
        {
            auto& curFrameStats = EngineGlobal::GetRenderEngine()->GetGlobalSystem<FrameSynchronizationSystemR>()->mFrameStats;
            curFrameStats->GetFrameTimer(frameParam->GetFrameCount()).mRenderSubmit = std::chrono::high_resolution_clock::now();
        }
        // resource for mCurrentFrame + gFrameCount was ready
        mCmdQue->Signal(mFrameResourceReady.get(), mCurrentFrame + CmdSettings::Inst().gMaxQueuedFrame);        
        if (auto xrRuntime = GetXRRuntime(); mXRFrameInfo.Running)
        {
            for (UInt32 i = 0; i < xrRuntime->GetViewCount(); ++i)
            {
                xrRuntime->GetColorSwapchain(i)->Present();
                xrRuntime->GetDepthSwapchain(i)->Present();
            }
            xrRuntime->EndRenderFrame(mXRFrameInfo);
        }
    }
    mCmdQue->EndFrame();
    mCopyQue->EndFrame();
    mScratchBuffer->EndFrame();
    mREDVisualizer->OnEndFrame();
    mREDdebugGUI->OnEndFrame();

    // mUploadBuffer->LogMemoryFootprint();
    RenderFactory::Instance().EndRenderFrame(frameParam);

    frame::AdvanceRenderingFrame();
    frameParam->SetRenderEndTime();
    bool value = false;
    bool bUseStats = CmdSettings::Inst().GetConfigValueByName<bool>("gUseEngineStats", value) && value;
    // get render delta time
    if (bUseStats)
    {
        auto& curFrameStats = EngineGlobal::GetRenderEngine()->GetGlobalSystem<FrameSynchronizationSystemR>()->mFrameStats;
        curFrameStats->GetFrameTimer(frameParam->GetFrameCount()).mRenderDeltaTime = frameParam->GetRenderDeltaTime();
    }

    // must call time stamp after CmdQue->ExecuteCommandLists, for all commandList have call End
    {
        auto GPUProfileItems = GetNGIDevice().PopGpuProfileItems();
        float currentGPUDeltaTime = 0.0f;
        if (GPUProfileItems.empty())
        {
            // current don't have gpu profile item, using mLastGPUDeltaTime as currentGPUDeltaTime
            currentGPUDeltaTime = mLastGPUDeltaTime;
        }
        else
        {
            for (auto& item : GPUProfileItems)
            {
                GPUProfiling::GPUProfilingContextInfo startUp = std::get<0>(item);
                GPUProfiling::GetInstance().PushGPUProfilingTimestamp(startUp, std::get<1>(item));

                if (startUp.vkQueue == (intptr_t)mCmdQue->GetNativeHandle())
                {
                    currentGPUDeltaTime = startUp.queueExecuteTimeMilliseconds;
                }
            }
            mLastGPUDeltaTime = currentGPUDeltaTime;
        }

        if (bShowTimeStats)
        {
            frameParam->SetGPUDeltaTime(currentGPUDeltaTime);

            // Set GPU delta time
            if (bUseStats)
            {
                auto& curFrameStats = EngineGlobal::GetRenderEngine()->GetGlobalSystem<FrameSynchronizationSystemR>()->mFrameStats;
                curFrameStats->GetFrameTimer(frameParam->GetFrameCount()).mGPUDeltaTime = currentGPUDeltaTime;
            }
        }
    }
   /* if (SLWrapper::Get().GetReflexAvailable())
        SLWrapper::Get().ReflexCallback_RenderEnd(frameParam->GetFrameCount());*/
}

bool RendererSystemR::RegisterQueuePresentTriggerCallback(uintptr_t key, std::function<void(UInt32)> inCallback)
{
    std::unique_lock lock(sCallbackMutex);
    sQueuePresentTriggerCbs[key] = (inCallback);
    return true;
}

bool RendererSystemR::UnRegisterQueuePresentTriggerCallback(uintptr_t key)
{
    std::unique_lock lock(sCallbackMutex);
    sQueuePresentTriggerCbs.erase(key);
    return true;
}

RendererSystemR::ReadBackSession::ReadBackSession()
{
    static std::atomic_uint64_t sIDGenerator = 0;
    mGUID = sIDGenerator++;
}

}   // namespace cross

void cross::RendererSystemR::UpdateBufferWithRawData(void const* data, SizeType size, NGIBuffer* buffer, NGIResourceState destState)
{
    SCOPED_CPU_TIMING(GroupRendering, "UpdateBufferWithRawData");
    //auto [staging, offset, ptr] = mScratchBuffer->Allocate(NGIBufferUsage::CopySrc, size);
    auto stagingWrap = mScratchBuffer->AllocateStaging(NGIBufferUsage::CopySrc, size);

    //memcpy(ptr, data, size);
    if (destState == NGIResourceState::IndexBuffer)
    {
        size_t tempDstOffset = 0;
        auto tempSrcPtr = static_cast<UInt8*>((void*)data);
        static const std::size_t mOffsetPerTask = 64000;
        {
            threading::TaskEventArray taskEventArray;
            for (std::size_t mOffset = 0; mOffset < size; mOffset += mOffsetPerTask)
            {
                std::size_t iOffsetStart = mOffset;
                std::size_t iOffsetEnd = iOffsetStart + mOffsetPerTask <= size ? iOffsetStart + mOffsetPerTask : size;
                taskEventArray.Add(threading::Async([&, iOffsetStart, iOffsetEnd, tempDstOffset, tempSrcPtr](auto) { stagingWrap.MemWrite(tempDstOffset, tempSrcPtr, iOffsetEnd - iOffsetStart); }));
                tempDstOffset += iOffsetEnd - iOffsetStart;
                tempSrcPtr += iOffsetEnd - iOffsetStart;
            }
            taskEventArray.WaitForCompletion();
            taskEventArray.Reset();
        }
    }
    else
    {
        stagingWrap.MemWrite(0, data, size);
    }

    // auto infer src offset later when we use custom allocator
    NGICopyBuffer region{stagingWrap.GetNGIOffset(), 0, size};

    UpdateBuffer(buffer, stagingWrap.GetNGIBuffer(), region, NGIResourceState::Undefined, destState);
}

cross::REDImGui* cross::RendererSystemR::GetClientGUI() const
{
    switch (mREDGUIState)
    {
    case REDGUIState::None:
        return nullptr;
    case REDGUIState::REDVisualizer:
        return mREDVisualizer.get();
    case REDGUIState::DebugWindow:
        return mREDdebugGUI.get();
    default:
        return nullptr;
    }
}

void cross::RendererSystemR::ToggleNoDrawWorldMode()
{
    mRED->ToggleNoDrawWorldMode();
}

void cross::RendererSystemR::BlockCompressRawImage(BlockCompression compressionMethod, NGITexture* rawImage, NGITextureView* rawImageView, NGITexture*& compressedTexture, NGITextureView*& compressedTextureView, bool noMips)
{
    Assert(mTextureCompressionShader && "mTextureCompressionShader not loaded!");

    if (noMips)
    {
        BlockCompressMipZero(compressionMethod, rawImage, rawImageView, compressedTexture, compressedTextureView);
    }
    else
    {
        BlockCompressAllMips(compressionMethod, rawImage, rawImageView, compressedTexture, compressedTextureView);
    }
}

void cross::RendererSystemR::BlockCompressAllMips(BlockCompression bcFormat, NGITexture* rawImage, NGITextureView* rawImageView, NGITexture*& compressedTexture, NGITextureView*& compressedTextureView)
{
    std::string BCName{};
    GraphicsFormat intermediateFormat{0};
    GraphicsFormat targetFormat{0};
    switch (bcFormat)
    {
        case BlockCompression::BC1:
        {
            BCName = "BC1";
            intermediateFormat = GraphicsFormat::R32G32_UInt;
            targetFormat = GraphicsFormat::RGB_BC1_SRGB;
            break;
        }
        case BlockCompression::BC3:
        {
            BCName = "BC3";
            intermediateFormat = GraphicsFormat::R32G32B32A32_UInt;
            targetFormat = GraphicsFormat::RGBA_BC3_SRGB;
            break;
        }
        case BlockCompression::BC5:
        {
            BCName = "BC5";
            intermediateFormat = GraphicsFormat::R32G32B32A32_UInt;
            targetFormat = GraphicsFormat::RG_BC5_UNorm;
            break;
        }
        case BlockCompression::BC7:
        {
            BCName = "BC7";
            intermediateFormat = GraphicsFormat::R32G32B32A32_UInt;
            targetFormat = GraphicsFormat::RGBA_BC7_SRGB;
            break;
        }
        default:
        Assert(false);
        break;
    }

    constexpr UInt32 BLOCK_SIZE = 4;
    //constexpr UInt32 PROCESSED_TEXELS_PER_THTREAD = BLOCK_SIZE * BLOCK_SIZE;
    constexpr UInt32 COMPENSATORY_MIPS = 2;            // for rawImage of 4*4, we have 4*4, 2*2, 1*1, 3 miplevels, but, the compressed RT only have 1*1. so we should mannually add this two last mip levels
    constexpr UInt32 MAX_COMPRESSED_MIP_LEVELS = 11;   // mip levels in TextureCompression.compute shader

    UInt32 rawWidth = rawImage->GetDesc().Width;
    UInt32 rawHeight = rawImage->GetDesc().Height;
    UInt32 rawMipCount = rawImage->GetDesc().MipCount;
    UInt32 sampleCount = rawImage->GetDesc().SampleCount;
    UInt32 width = (rawWidth + BLOCK_SIZE - 1) / BLOCK_SIZE;
    UInt32 height = (rawHeight + BLOCK_SIZE - 1) / BLOCK_SIZE;
    UInt32 depth = rawImage->GetDesc().Depth;
    UInt32 arraySize = 1;
    UInt32 compressedRTMipCount = std::min(width, height) > BLOCK_SIZE ? (rawMipCount - COMPENSATORY_MIPS) : 1;

    Assert(compressedRTMipCount <= MAX_COMPRESSED_MIP_LEVELS && "too large image for BlockCompress");

    mRED->BeginRegion("Compression " + BCName);

    REDTexture* rawImageRED = mRED->AllocateTexture("sr image raw", rawImage, NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);

    // 1: compressed to a UAV image
    REDTexture* compressedImageRED = mRED->AllocateTexture("compressed sr image", 
        NGITextureDesc{intermediateFormat, NGITextureType::Texture2D, compressedRTMipCount, sampleCount, width, height, depth, arraySize, NGITextureUsage::UnorderedAccess, false, true});

    REDTextureView* compressedImageViewsRED[MAX_COMPRESSED_MIP_LEVELS];
    for (UInt32 i = 0; i < compressedRTMipCount; ++i)
    {
        compressedImageViewsRED[i] =
            mRED->AllocateTextureView(compressedImageRED, NGITextureViewDesc{NGITextureUsage::UnorderedAccess, intermediateFormat, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Color, i, 1, 0, 1}});
    }

    // fill empty
    for (UInt32 i = compressedRTMipCount - 1; i < MAX_COMPRESSED_MIP_LEVELS; ++i)
    {
        compressedImageViewsRED[i] = compressedImageViewsRED[compressedRTMipCount - 1];
    }

    Float4 compressedImageSize = {static_cast<float>(width), static_cast<float>(height), 1.0f / width, 1.0f / height};

#if true
    // one pass generation all
    {
        UInt32 x, y, z;
        std::string kernalName = "CompressionMipAll" + BCName;
        mTextureCompressionShader->GetThreadGroupSize(kernalName, x, y, z);
        UInt32 warpSize = x * y * z;

        std::vector<std::array<UInt32, 4>> dispatchMipLevelMap;
        std::vector<std::array<UInt32, 4>> textureSizeForMipLevel;
        UInt32 accumulatedTexelCount = 0;
        UInt32 accumulatedGroupCount = 0;
        dispatchMipLevelMap.push_back({accumulatedTexelCount, accumulatedGroupCount, 0, 0});

        UInt32 tWidth = width;
        UInt32 tHeight = height;
        UInt32 rWidth = rawWidth;
        UInt32 rHeight = rawHeight;

        for (UInt32 i = 0; i < compressedRTMipCount; ++i)
        {
            UInt32 levelTexelCount = tWidth * tHeight;
            accumulatedTexelCount += levelTexelCount;

            UInt32 levelGroupCount = (levelTexelCount + warpSize - 1) / warpSize;
            accumulatedGroupCount += levelGroupCount;

            dispatchMipLevelMap.push_back({accumulatedTexelCount, accumulatedGroupCount, 0, 0});
            textureSizeForMipLevel.push_back({tWidth, tHeight, rWidth, rHeight});

            tWidth = tWidth >> 1;
            tHeight = tHeight >> 1;

            rWidth = rWidth >> 1;
            rHeight = rHeight >> 1;
        }

        for (UInt32 i = compressedRTMipCount; i < MAX_COMPRESSED_MIP_LEVELS + 1; ++i)
        {
            dispatchMipLevelMap.push_back({accumulatedTexelCount, accumulatedGroupCount, 0, 0});
            textureSizeForMipLevel.push_back({tWidth, tHeight, rWidth, rHeight});
            rWidth = rWidth >> 1;
            rHeight = rHeight >> 1;
        }
        // fill last item
        textureSizeForMipLevel.push_back({tWidth, tHeight, rWidth, rHeight});

        REDTextureView* rawImageViewRED = mRED->AllocateTextureView(
            rawImageRED, NGITextureViewDesc{NGITextureUsage::ShaderResource, GraphicsFormat::R8G8B8A8_UNorm, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Color, 0, rawMipCount, 0, 1}});
        auto* compressionPass = mRED->AllocatePass("Compression Pass " + BCName, true);
        compressionPass->SetProperty("compressedImageSize", compressedImageSize);
        compressionPass->SetProperty("DispatchMipLevelMap", dispatchMipLevelMap.data(), dispatchMipLevelMap.size() * sizeof(std::array<UInt32, 4>));
        compressionPass->SetProperty("MipLevelsTextureSizeLookUp", textureSizeForMipLevel.data(), textureSizeForMipLevel.size() * sizeof(std::array<UInt32, 4>));
        compressionPass->SetProperty("RawImage", rawImageViewRED);

        Float4 rawImageSize = {static_cast<float>(rawWidth), static_cast<float>(rawHeight), 1.0f / rawWidth, 1.0f / rawHeight};
        compressionPass->SetProperty("rawImageSize", rawImageSize);
        compressionPass->SetProperty("CompressedRTs" + BCName, MAX_COMPRESSED_MIP_LEVELS, &compressedImageViewsRED[0]);

        compressionPass->Dispatch(mTextureCompressionShader, kernalName, accumulatedGroupCount, 1, 1);
    }
#else
    // seperate pass generation
    {
        UInt32 rWidth = rawWidth;
        UInt32 rHeight = rawHeight;
        UInt32 tWidth = width;
        UInt32 tHeight = height;

        for (UInt32 i = 0; i < compressedRTMipCount; ++i)
        {
            REDTextureView* rawImageViewRED =
                mRED->AllocateTextureView(rawImageRED, NGITextureViewDesc{NGITextureUsage::ShaderResource, GraphicsFormat::R8G8B8A8_UNorm, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Color, i, 1, 0, 1}});
            auto* compressionPass = mRED->AllocatePass("Texture Compression for Mips", true);
            compressionPass->SetProperty("compressedImageSize", compressedImageSize);
            compressionPass->SetProperty("RawImage", rawImageViewRED);

            Float4 rawImageSize = {static_cast<float>(rWidth), static_cast<float>(rHeight), 1.0f / rWidth, 1.0f / rHeight};
            compressionPass->SetProperty("rawImageSize", rawImageSize);
            compressionPass->SetProperty("TargetMipLevel", i);
            compressionPass->SetProperty("CompressedRT" + BCName, compressedImageViewsRED[i]);

            UInt32 x, y, z;
            std::string kernalName = "CompressionMip" + BCName;
            mTextureCompressionShader->GetThreadGroupSize(kernalName, x, y, z);
            UInt32 gridSizeX = (tWidth + x - 1) / x;
            UInt32 gridSizeY = (tHeight + y - 1) / y;
            compressionPass->Dispatch(mTextureCompressionShader, kernalName, gridSizeX, gridSizeY, 1);

            tWidth = tWidth >> 1;
            tHeight = tHeight >> 1;
            rWidth = rWidth >> 1;
            rHeight = rHeight >> 1;
        }
    }
#endif
    // 2: transition this UAV image to copy src state
    auto* transitionPass1 = mRED->AllocatePass("Transition", true);
    for (UInt32 i = 0; i < compressedRTMipCount; ++i)
    {
        transitionPass1->AddTextureReference(compressedImageViewsRED[i], REDResourceState{NGIResourceState::CopySrc});
    }

    // 3: copy UAV image to SRV texture
    compressedTexture =
        GetNGIDevice().CreateTexture(NGITextureDesc{targetFormat, NGITextureType::Texture2D, rawMipCount, sampleCount, rawWidth, rawHeight, depth, arraySize, NGITextureUsage::CopyDst, false, true}, "compressed sr texture");
    compressedTextureView =
        GetNGIDevice().CreateTextureView(compressedTexture, NGITextureViewDesc{NGITextureUsage::CopyDst, targetFormat, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Color, 0, rawMipCount, 0, 1}});

    REDTexture* finalTextureRED = mRED->AllocateTexture("compressed sr texture", compressedTexture, NGIResourceState::CopyDst);
    REDTextureView* finalTextureViewRED =
        mRED->AllocateTextureView(finalTextureRED, NGITextureViewDesc{NGITextureUsage::CopyDst, targetFormat, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Color, 0, rawMipCount, 0, 1}});

    auto* copyPass = mRED->AllocatePass("Copy Compression Pass", true);

    std::vector<NGICopyTexture> regions;
    UInt32 cWidth = width;
    UInt32 cHeight = height;
    for (UInt32 i = 0; i < compressedRTMipCount; ++i)
    {
        NGICopyTexture& region = regions.emplace_back();

        region.SrcSubresource = NGICalcSubresource(i, 0, 0, compressedRTMipCount, 1);
        region.SrcOffset = NGIOffset3D{0, 0, 0};
        region.DstSubresource = NGICalcSubresource(i, 0, 0, compressedRTMipCount + COMPENSATORY_MIPS, 1);
        region.DstOffset = NGIOffset3D{0, 0, 0};
        region.Extent = NGIExtent3D{cWidth, cHeight, 1};
        cWidth = cWidth >> 1;
        cHeight = cHeight >> 1;
    }
    // fill last Two COMPENSATORY_MIPS
    for (UInt32 i = compressedRTMipCount; i < compressedRTMipCount + COMPENSATORY_MIPS; ++i)
    {
        NGICopyTexture& region = regions.emplace_back();
        region.SrcSubresource = NGICalcSubresource(compressedRTMipCount - 1, 0, 0, compressedRTMipCount, 1);
        region.SrcOffset = NGIOffset3D{0, 0, 0};
        region.DstSubresource = NGICalcSubresource(i, 0, 0, compressedRTMipCount + COMPENSATORY_MIPS, 1);
        region.DstOffset = NGIOffset3D{0, 0, 0};
        region.Extent = NGIExtent3D{1, 1, 1};
    }
    copyPass->CopyTextureToTexture(finalTextureRED, compressedImageRED, static_cast<UInt32>(regions.size()), regions.data());

    // 4: completion transition to SRV texture
    auto* transitionPass2 = mRED->AllocatePass("Transition", true);
    transitionPass2->AddTextureReference(finalTextureViewRED, REDResourceState{NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask});

    mRED->EndRegion();

    // 5. free all temp resource
    DestroyNGIObject(rawImage);
    DestroyNGIObject(rawImageView);
}

void cross::RendererSystemR::BlockCompressMipZero(BlockCompression bcFormat, NGITexture* rawImage, NGITextureView* rawImageView, NGITexture*& compressedTexture, NGITextureView*& compressedTextureView)
{
    std::string BCName{};
    GraphicsFormat intermediateFormat{0};
    GraphicsFormat targetFormat{0};
    switch (bcFormat)
    {
    case BlockCompression::BC1:
    {
        BCName = "BC1";
        intermediateFormat = GraphicsFormat::R32G32_UInt;
        targetFormat = GraphicsFormat::RGB_BC1_SRGB;
        break;
    }
    case BlockCompression::BC3:
    {
        BCName = "BC3";
        intermediateFormat = GraphicsFormat::R32G32B32A32_UInt;
        targetFormat = GraphicsFormat::RGBA_BC3_SRGB;
        break;
    }
    case BlockCompression::BC5:
    {
        BCName = "BC5";
        intermediateFormat = GraphicsFormat::R32G32B32A32_UInt;
        targetFormat = GraphicsFormat::RG_BC5_UNorm;
        break;
    }
    case BlockCompression::BC7:
    {
        BCName = "BC7";
        intermediateFormat = GraphicsFormat::R32G32B32A32_UInt;
        targetFormat = GraphicsFormat::RGBA_BC7_SRGB;
        break;
    }
    default:
        Assert(false);
        break;
    }

    constexpr UInt32 BLOCK_SIZE = 4;

    UInt32 rawWidth = rawImage->GetDesc().Width;
    UInt32 rawHeight = rawImage->GetDesc().Height;
    Assert(rawImage->GetDesc().MipCount == 1);
    UInt32 mipCount = 1;
    UInt32 sampleCount = rawImage->GetDesc().SampleCount;
    UInt32 width = (rawWidth + BLOCK_SIZE - 1) / BLOCK_SIZE;
    UInt32 height = (rawHeight + BLOCK_SIZE - 1) / BLOCK_SIZE;
    UInt32 depth = rawImage->GetDesc().Depth;
    UInt32 arraySize = 1;

    mRED->BeginRegion("Compression " + BCName);

    REDTexture* rawImageRED = mRED->AllocateTexture("sr image raw", rawImage, NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
    REDTextureView* rawImageViewRED =
        mRED->AllocateTextureView(rawImageRED, NGITextureViewDesc{NGITextureUsage::ShaderResource, GraphicsFormat::R8G8B8A8_UNorm, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Color, 0, 1, 0, 1}});
    // 1: compressed to a UAV image
    REDTexture* compressedImageRED =
        mRED->AllocateTexture("compressed sr image", NGITextureDesc{intermediateFormat, NGITextureType::Texture2D, mipCount, sampleCount, width, height, depth, arraySize, NGITextureUsage::UnorderedAccess, false, true});
    REDTextureView* compressedImageViewRED =
            mRED->AllocateTextureView(compressedImageRED, NGITextureViewDesc{NGITextureUsage::UnorderedAccess, intermediateFormat, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Color, 0, 1, 0, 1}});

    {
        UInt32 rWidth = rawWidth;
        UInt32 rHeight = rawHeight;
        UInt32 tWidth = width;
        UInt32 tHeight = height;

        auto* compressionPass = mRED->AllocatePass("Texture Compression Single Mip", true);

        compressionPass->SetProperty("RawImage", rawImageViewRED);
        Float4 rawImageSize = {static_cast<float>(rWidth), static_cast<float>(rHeight), 1.0f / rWidth, 1.0f / rHeight};
        compressionPass->SetProperty("rawImageSize", rawImageSize);
        compressionPass->SetProperty("CompressedRT" + BCName, compressedImageViewRED);

        UInt32 x, y, z;
        std::string kernalName = "CompressionMip" + BCName;
        mTextureCompressionShader->GetThreadGroupSize(kernalName, x, y, z);
        UInt32 gridSizeX = (tWidth + x - 1) / x;
        UInt32 gridSizeY = (tHeight + y - 1) / y;
        compressionPass->Dispatch(mTextureCompressionShader, kernalName, gridSizeX, gridSizeY, 1);
    }

    // 2: transition this UAV image to copy src state
    {
        auto* transitionPass = mRED->AllocatePass("Transition", true);
        transitionPass->AddTextureReference(compressedImageViewRED, REDResourceState{NGIResourceState::CopySrc});
    }

    // 3: copy UAV image to SRV texture
    compressedTexture = GetNGIDevice().CreateTexture(NGITextureDesc{targetFormat, NGITextureType::Texture2D, mipCount, sampleCount, rawWidth, rawHeight, depth, arraySize, NGITextureUsage::CopyDst, false, true}, "compressed sr texture");
    compressedTextureView = GetNGIDevice().CreateTextureView(compressedTexture, NGITextureViewDesc{NGITextureUsage::CopyDst, targetFormat, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Color, 0, mipCount, 0, 1}});

    REDTexture* finalTextureRED = mRED->AllocateTexture("compressed sr texture", compressedTexture, NGIResourceState::CopyDst);
    REDTextureView* finalTextureViewRED = mRED->AllocateTextureView(finalTextureRED, NGITextureViewDesc{NGITextureUsage::CopyDst, targetFormat, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Color, 0, mipCount, 0, 1}});

    auto* copyPass = mRED->AllocatePass("Copy Compression Pass", true);

    NGICopyTexture region{};

    region.SrcSubresource = NGICalcSubresource(0, 0, 0, mipCount, 1);
    region.SrcOffset = NGIOffset3D{0, 0, 0};
    region.DstSubresource = NGICalcSubresource(0, 0, 0, mipCount, 1);
    region.DstOffset = NGIOffset3D{0, 0, 0};
    region.Extent = NGIExtent3D{width, height, 1};
    copyPass->CopyTextureToTexture(finalTextureRED, compressedImageRED, 1, &region);

    // 4: completion transition to SRV texture
    {
        auto* transitionPass = mRED->AllocatePass("Transition", true);
        transitionPass->AddTextureReference(finalTextureViewRED, REDResourceState{NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask});
    }

    mRED->EndRegion();

    // 5. free all temp resource
    DestroyNGIObject(rawImage);
    DestroyNGIObject(rawImageView);
}

void cross::RendererSystemR::TransformCubemapToPanorama(const std::array<RenderTextureR*, 6>& cubemapFaces, RenderTextureR* texture2D)
{
    mRED->BeginRegion("Transform cubemap to panorama");
    auto* transformPass = mRED->AllocatePass("Transform cubemap to equirectangle", true);

    UInt32 faceSize = cubemapFaces[0]->GetNGITexture()->GetDesc().Width;
    UInt32 dstWidth = texture2D->GetNGITexture()->GetDesc().Width;
    UInt32 dstHeight = texture2D->GetNGITexture()->GetDesc().Height;

    REDTextureView* cubemapFacesView[6];
    for (int i = 0; i < 6; ++i)
    {
        REDTexture* cubeface = mRED->AllocateTexture("cubeface" + std::to_string(i), cubemapFaces[i]->GetNGITexture(), NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
        cubemapFacesView[i] = mRED->AllocateTextureView(
            cubeface, NGITextureViewDesc{NGITextureUsage::ShaderResource, cubemapFaces[i]->GetNGITextureView()->GetDesc().Format, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Color, 0, 1, 0, 1}});
    }

    REDTexture* panorama = mRED->AllocateTexture("Panorama", texture2D->GetNGITexture(), NGIResourceState::UnorderedAccessBit);
    REDTextureView* panoramaView = mRED->AllocateTextureView(
        panorama, NGITextureViewDesc{NGITextureUsage::UnorderedAccess, texture2D->GetNGITextureView()->GetDesc().Format, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Color, 0, 1, 0, 1}});

    transformPass->SetProperty("srcImage", 6, &cubemapFacesView[0]);
    transformPass->SetProperty("dstImage", panoramaView);

    transformPass->SetProperty("srcImageSize", Float4(static_cast<float>(faceSize), static_cast<float>(faceSize), 1.f / faceSize, 1.f / faceSize));
    transformPass->SetProperty("dstImageSize", Float4(static_cast<float>(dstWidth), static_cast<float>(dstHeight), 1.f / dstWidth, 1.f / dstHeight));

    std::string kernalName = "TransformCubemapToEquirectanglar";
    UInt32 x, y, z;
    mTextureUtilsShader->GetThreadGroupSize(kernalName, x, y, z);
    UInt32 gridSizeX = (dstWidth + x - 1) / x;
    UInt32 gridSizeY = (dstHeight + y - 1) / y;
    transformPass->Dispatch(mTextureUtilsShader, kernalName, gridSizeX, gridSizeY, 1);

    mRED->EndRegion();
}

inline std::string GetPipelineCacheBinaryPath()
{
    auto& currentDir = cross::PathHelper::GetCurrentDirectoryPath();
    auto filePath = cross::PathHelper::Combine(currentDir.c_str(), "Saved/PipelineCache/CrossEnginePipelineCache.bin");
    return filePath;
}

std::vector<uint8_t> cross::RendererSystemR::LoadPipelineCache()
{
    std::vector<uint8_t> cache_data;
    if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeStandAlone)
    {
        auto file = EngineGlobal::Inst().GetFileSystem()->Open(GetPipelineCacheBinaryPath());
        if (file)
        {
            UInt64 fileSize = (UInt64)file->GetSize();
            cache_data.resize(fileSize);
            file->Read((char*)cache_data.data(), fileSize);
            file->Close();
        }
    }
    return cache_data;
}

void cross::RendererSystemR::SavePipelineCache()
{
    if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeStandAlone)
    {
        size_t cache_size = GetGraphicsPipelineStatePool()->GetSize();
        std::vector<uint8_t> cache_data(cache_size);
        GetGraphicsPipelineStatePool()->GetData(cache_data.size(), cache_data.data());
        EngineGlobal::Inst().GetFileSystem()->Save(GetPipelineCacheBinaryPath(), (const char*)cache_data.data(), cache_data.size());    
    }
}
