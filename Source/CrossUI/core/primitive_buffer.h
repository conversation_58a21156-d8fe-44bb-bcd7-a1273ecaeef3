#pragma once

#include "types.h"
#include <vector>

namespace oui {

const int MAX_LAYER_COUNT = 4;
const size_t INITIAL_LAYER_SIZE = 4096 * 16;
const size_t INITIAL_INDEX_SIZE = 4096 * 8;

const size_t INITIAL_BASE_LAYER_SIZE = 1024 * 1024 * 16;
const size_t INITIAL_BASE_INDEX_SIZE = 1024 * 1024;

struct primitive_layer {
    int primitive_count;
    int index_count;

    std::vector<float> primitive_buffer;
    std::vector<u32> index_buffer;
};

struct primitive_buffer {
    std::vector<float> merged_primitive_buffer;
    std::vector<u32> merged_index_buffer;

    int primitive_count;
    int index_count;

    primitive_layer layers[MAX_LAYER_COUNT];
};

struct rect_vertex {
    float x, y, w, h;
    ui_color color;
    u32 clip;
};

struct triangle_vertex {
    float x, y;
    u32 rect_offset;
    float alpha;
};

struct triangle_dash_vertex {
    triangle_vertex triangle;
    float offset;
};

struct glyph_vertex {
    float x, y, w, h;
    float uv_x, uv_y, uv_w, uv_h;
    u32 rect_offset;
};

enum PRIMITIVE_TYPE: u32 {
    TRIANGLE = 1 << 26,
    TRIANGLE_TEXTURED = 2 << 26,
    RECTANGLE = 3 << 26,
    RECTANGLE_TEXTURED = 4 << 26,
    TRIANGLE_SCREEN = 7 << 26,
    TRIANGLE_ICON = 8 << 26,
    TRIANGLE_ATLAS = 9 << 26,
    TRIANGLE_DASH = 10 << 26,
    GLYPH = 11 << 26,
    GLYPH_CODE = 12 << 26,
};

enum CORNER: u32 {
    TOP_LEFT = 0 << 24,
    TOP_RIGHT = 1 << 24,
    BOTTOM_LEFT = 2 << 24,
    BOTTOM_RIGHT = 3 << 24,
};

/**
 * vertex_id uint32 index
 * | 000000 00 00000000 00000000 00000000 |
 *   |type| |c||     primitive offset    |
 *          ^
 *          |
 *        corner id [0, 1, 2, 3]
 */
CROSS_UI_API u32 encode_vertex_id(PRIMITIVE_TYPE type, CORNER corner, u32 offset);
CROSS_UI_API u32 decode_vertex_offset(u32 index);

CROSS_UI_API void primitive_layer_create(primitive_layer* layer);
CROSS_UI_API void primitive_buffer_create(primitive_buffer* buffer);
CROSS_UI_API void primitive_buffer_reset(primitive_buffer* buffer);

CROSS_UI_API u32 primitive_layer_write_clipper(primitive_layer* layer, ui_rect rect);
CROSS_UI_API u32 primitive_layer_write_rect_vertex(primitive_layer* layer, rect_vertex vertex);
CROSS_UI_API u32 primitive_layer_write_triangle_vertex(primitive_layer* layer, triangle_vertex vertex);
CROSS_UI_API u32 primitive_layer_write_triangle_dash_vertex(primitive_layer* layer, triangle_dash_vertex vertex);
CROSS_UI_API u32 primitive_layer_write_glyph_vertex(primitive_layer* layer, glyph_vertex vertex);

CROSS_UI_API void primitive_buffer_update(primitive_buffer* buffer);
CROSS_UI_API void primitive_layer_add_index(primitive_layer* layer, u32 index);
}