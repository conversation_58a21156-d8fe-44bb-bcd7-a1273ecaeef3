#pragma once

#include "ui_control.h"
#include <string>

namespace oui {

class CROSS_UI_API UILabel : public UIElement {
public:
    UILayoutConstraint constraint;
    std::string text;
    float char_offsets[128];
    float2 text_size;

    UILabel(std::string text = "");
    ~UILabel();

    void SetContent(std::string content);
};

CROSS_UI_API extern void ui_label(UIState &state, UILabel& label, ui_rect rect, int layer_index = 0, u32 clip = 0, bool render_unit = false);

}