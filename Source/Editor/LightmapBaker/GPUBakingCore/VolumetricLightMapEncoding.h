#pragma once

#include "TLBSDataProtocol/BakingJobDefine.h"
#include "CrossBase/Math/CrossMath.h"
#include "GPUBakingCore/PrecomputedVolumetricLightmap.h"
#include "RenderEngine/LightProbeCache.h"
#include "Editor/LightmapBaker/DawnSettings.h"
#include "imageio.h"

NS_GPUBAKING_BEGIN

class FVolumeLightingSample
{
public:
    /** World space position and radius. */
    cross::Float4A PositionAndRadius;

    /** SH coefficients used with high quality lightmaps. */
    float HighQualityCoefficients[LM_NUM_SH_COEFFICIENTS][3];

    /** SH coefficients used with low quality lightmaps. */
    float LowQualityCoefficients[LM_NUM_SH_COEFFICIENTS][3];

    cross::Float4A SkyBentNormal;

    /** Shadow factor for the stationary directional light. */
    float DirectionalLightShadowing;
};

struct FIrradianceVoxelImportProcessingData
{
    bool bInsideGeometry;
    bool bBorderVoxel;
    float ClosestGeometryDistance;
};

constexpr int SHCoefficientsNum = 6;

struct FImportedVolumetricLightmapBrick
{
    NSwarm::FGuid IntersectingLevelGuid;
    int3 IndirectionTexturePosition;
    int32 TreeDepth;
    float AverageClosestGeometryDistance;
    std::vector<FFloat3Packed> AmbientVector;
    std::vector<FColor> SHCoefficients[SHCoefficientsNum];
    std::vector<FFloat3Packed> LQLightColor;
    std::vector<FColor> LQLightDirection;
    std::vector<FColor> SkyBentNormal;
    std::vector<uint8> DirectionalLightShadowing;
    std::vector<FIrradianceVoxelImportProcessingData> TaskVoxelImportProcessingData;
};

struct FImportedVolumetricLightmapTaskData
{
    std::vector<GPUBaking::FImportedVolumetricLightmapBrick> Bricks;
};

class FVolumetricLightMapEncoder
{
public:
    static void ConvertVolumetricLightmapOutput(const FAdaptiveBrickData& JobOutput, FImportedVolumetricLightmapBrick& BrickData, int32 BrickSize);

    static void ImportVolumetricLightmap(const GPUBaking::FAdaptiveVolumetricLightmapParameters& VolumetricLightmapSettings, const std::vector<FImportedVolumetricLightmapTaskData>& vlmTaskData,
                                         FPrecomputedVolumetricLightmapData& outCurrentLevelData);

    static void SetFromSHVector(const TSHRGB3& SHVector, FVolumeLightingSample& LightingSample);

    static void WriteVolumetricLightmap(FPrecomputedVolumetricLightmapData& LevelData, const std::string& pathdir, cross::VolumetricLightmapData &outData);

private:
    static void WriteVolumetricLightmap(const FVolumetricLightmapDataLayer& TextureData, const int3& AtlasSize, const std::string& filename);

    // static void SetFromTransferMatrix(FImportedVolumetricLightmapBrick& BrickData, const int LightIndex, const int32 CellIndex,const FRGBTransferMatrix3& TransferMatrix);

    // static void SetFromTransferVector(FImportedVolumetricLightmapBrick& BrickData, const int LightIndex, const int32 CellIndex, const GPUBaking::TSHRGB3& SHVector);
};
NS_GPUBAKING_END
