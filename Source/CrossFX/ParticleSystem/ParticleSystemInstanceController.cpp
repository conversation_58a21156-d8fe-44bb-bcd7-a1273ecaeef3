#include "memoryhooker/Module.h"
#include "ParticleSystemInstanceController.h"
#include "ParticleSystemSimulationContext.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/RenderNode/ParticleSystemRenderNode.h"
#include "CrossBase/TileBasedCoordinates.h"

IMPORT_MODULE

namespace cross::fx
{

void ParticleSystemInstanceController::Initialize()
{
    mSystemInstancePtr = std::make_shared<ParticleSystemInstance>();
    mSystemInstancePtr->Init();
}

void ParticleSystemInstanceController::Tick(float dt, const TRSMatrixAType& worldMatrix, const TRSMatrixAType& cameraMatrix)
{
    const UInt32 emitterNums = mSystemInstancePtr->GetEmitterNum();
    if (emitterNums < 1)
    {
        return;
    }

    ParticleSystemSimulationContext context(this, mSystemInstancePtr.get());
    PrepareForSimulate(context, dt, worldMatrix, cameraMatrix);
    UpdateSystemInstance(context, dt);
}

void ParticleSystemInstanceController::CollectParticleRenderData(ParticleRenderData& output)
{
    const UInt32 emitterNums = mSystemInstancePtr->GetEmitterNum();
    if (emitterNums < 1)
    {
        return;
    }
    output.SystemID = mSystemInstancePtr->GetID().Get();
    output.Reserve(emitterNums);

    for (auto emitterIndex = 0u; emitterIndex < emitterNums; ++emitterIndex)
    {
        EmitterInstancePtr emitter = mSystemInstancePtr->GetEmitter(emitterIndex);
        emitter->PrepareRenderData(output);
    }

    output.Visible = mSystemInstancePtr->GetSystemState() != SimulationState::STOPPED;
}

void ParticleSystemInstanceController::CollectParticleVertexView(std::vector<ParticleVertexView>& views)
{
    const UInt32 emitterNums = mSystemInstancePtr->GetEmitterNum();
    if (emitterNums < 1)
    {
        return;
    }

    views.reserve(emitterNums);
    for (auto emitterIndex = 0u; emitterIndex < emitterNums; ++emitterIndex)
    {
        EmitterInstancePtr emitter = mSystemInstancePtr->GetEmitter(emitterIndex);
        emitter->PrepareRenderVertexView(views.emplace_back());
        views.back().mSimulation = emitter->GetSimulationType();
    }
}

void ParticleSystemInstanceController::CollectParticleMaterial(std::vector<ParticleMaterialView>& materials)
{
    const UInt32 emitterNums = mSystemInstancePtr->GetEmitterNum();
    if (emitterNums < 1)
    {
        return;
    }

    materials.reserve(emitterNums);
    for (auto emitterIndex = 0u; emitterIndex < emitterNums; ++emitterIndex)
    {
        EmitterInstancePtr emitter = mSystemInstancePtr->GetEmitter(emitterIndex);
        //materials.emplace_back(ParticleMaterialView{std::move(emitter->GetMaterials()), std::move(emitter->GetVariables())});
        auto& materialView = materials.emplace_back();
        materialView.mSimulation = emitter->GetSimulationType();
        materialView.mMaterials = emitter->GetMaterials();
        for (const ParticleVariable& v : emitter->GetVariables())
        {
            materialView.mVariables[v.Name] = v;
        }
    }
}

void ParticleSystemInstanceController::CollectParticleGpuComputeContext(std::vector<std::shared_ptr<ParticleGpuComputeContext>>& contexts)
{
    const UInt32 emitterNums = mSystemInstancePtr->GetEmitterNum();
    if (emitterNums < 1)
    {
        return;
    }
    contexts.reserve(emitterNums);
    for (auto emitterIndex = 0u; emitterIndex < emitterNums; ++emitterIndex)
    {
        if (EmitterInstancePtr emitter = mSystemInstancePtr->GetEmitter(emitterIndex))
        {
            contexts.emplace_back(emitter->GetComputeContext());
        }
    }
}

void ParticleSystemInstanceController::ResetSystemResource(ParticleSystemResPtr newResource)
{
    if (!newResource)
    {
        Assert(false);
        return;
    }
    mSystemInstancePtr->ResetResource(newResource);
    mSystemInstancePtr->ResetUniqueID();
}

void ParticleSystemInstanceController::ResetEmitterResource(UInt32 emitterIndex, ParticleEmitterResPtr newResource)
{
    mSystemInstancePtr->ResetEmitter(emitterIndex, newResource);
}

void ParticleSystemInstanceController::PrepareForSimulate(ParticleSystemSimulationContext& context, float dt, const TRSMatrixAType& worldMatrix, const TRSMatrixAType& cameraMatrix)
{
    QUICK_SCOPED_CPU_TIMING("PrepareForSimulate");

    const UInt32 emitterNums = mSystemInstancePtr->GetEmitterNum();
    for (auto emitterIndex = 0u; emitterIndex < emitterNums; ++emitterIndex)
    {
        EmitterInstancePtr emitter = mSystemInstancePtr->GetEmitter(emitterIndex);
        auto& moduleContext = emitter->GetModuleContext();
        auto& pipeline = emitter->GetModulePipeline();
        const auto& pipelineState = pipeline.GetState();

        if (emitter->IsComplete() && pipelineState != SimulationState::RESTART)
        {
            continue;
        }

        switch (pipelineState)
        {
        case SimulationState::RUNNING:
        {
            emitter->TryUpdateEmitterDashboard();

            moduleContext.DeltaTime = dt;

            const Double3& cameraPosition {cameraMatrix.m30, cameraMatrix.m31, cameraMatrix.m32};
            GetTileAndOffsetForAbsolutePosition(cameraPosition, moduleContext.CameraTile, moduleContext.CameraPosition);

            const Double3& entityPosition{worldMatrix.m30, worldMatrix.m31, worldMatrix.m32};
            GetTileAndOffsetForAbsolutePosition(entityPosition, moduleContext.BaseTile, moduleContext.BasePosition);

            moduleContext.WorldMatrix = worldMatrix;
            moduleContext.WorldToLocal = worldMatrix.Inverted();
            moduleContext.RelativeWorldMatrix = ParticleSystemUtils::GetRelativeMatrix(worldMatrix);
            moduleContext.InvRelativeWorldMatrix = moduleContext.RelativeWorldMatrix.Inverted();
            moduleContext.RelativeWorldMatrixNoScale = ParticleSystemUtils::GetTransformVelocityMatrix(worldMatrix);
            pipeline.Simulate(ModuleScope::EmitterUpdate, moduleContext);

            if (moduleContext.State == ParticleEmitterState::Reset)
            {
                emitter->ResetSimulation();
                moduleContext.State = ParticleEmitterState::Active;
            }
            emitter->SetEmitterState(moduleContext.State);

            if (emitter->GetSimulationType() == SimulationType::CPU)
            {
                if (UInt32 spawned = static_cast<UInt32>(moduleContext.SpawnCount); spawned > 0u && !emitter->IsInactive())
                {
                    emitter->SpawnParticles(spawned, moduleContext.SpawnedSlots);
                    moduleContext.AliveCount += spawned;
                }
            }
            break;
        }
        case SimulationState::STOPPED:
        {
            emitter->Cleanup();
            emitter->ResetSimulation();
            emitter->SetEmitterState(ParticleEmitterState::Complete);
            break;
        }
        case SimulationState::RESTART:
        {
            emitter->Cleanup();
            emitter->ResetSimulation();
            emitter->SetEmitterState(ParticleEmitterState::Inactive);
            emitter->SetSimulationState(SimulationState::RUNNING);
            moduleContext.State = ParticleEmitterState::Active;
            break;
        }
        default:
            break;
        }
    }
}

void ParticleSystemInstanceController::UpdateSystemInstance(ParticleSystemSimulationContext& context, float dt)
{
    QUICK_SCOPED_CPU_TIMING("UpdateSystemInstance");
    threading::TaskEventArray taskEventArray;

    const UInt32 emitterNums = mSystemInstancePtr->GetEmitterNum();
    for (auto emitterIndex = 0u; emitterIndex < emitterNums; ++emitterIndex)
    {
        EmitterInstancePtr emitter = mSystemInstancePtr->GetEmitter(emitterIndex);
        taskEventArray.Add(threading::Dispatch([&, emitter, dt](auto)
        {
            if (emitter->GetSimulationType() == SimulationType::CPU)
            {

                auto& pipeline = emitter->GetModulePipeline();
                if (pipeline.GetState() == SimulationState::PAUSED)
                {
                    return;
                }

                if (!emitter->IsComplete())
                {
                    pipeline.Simulate(ModuleScope::ParticleSpawn, emitter->GetModuleContext());
                    emitter->GenSpawnEvents();
                }

                ModulePipelineContext& context = emitter->GetModuleContext();
                if (context.AliveCount > 0 && context.ParticleParams->GetStoreSize() > 0)
                {
                    // emitter->GenCustomEvents(
                    //     [deltaTime = context.DeltaTime](const ParticleStatePool& states, UInt32 particleIndex) {
                    //         auto age = states.age[particleIndex];
                    //         auto lifetime = states.lifetime[particleIndex];
                    //         return age < 0.5 * lifetime && age + deltaTime > 0.5 * lifetime + MathUtils::MathEps;
                    //     }
                    // );
                    
                    pipeline.Simulate(ModuleScope::ParticleUpdate, context);
                    pipeline.Simulate(ModuleScope::ParticleRender, context);
                    if (context.ParticleParams && !context.ParticleParams->GetDeadSlots().empty())
                    {
                        emitter->GenDeathEvents();
                    }
                }
            }

            if (!emitter->IsComplete())
            {
                emitter->Tick(dt);
                emitter->PostTick(dt);
            }
        }));
    }
    taskEventArray.WaitForCompletion();
}

}   // namespace cross::fx