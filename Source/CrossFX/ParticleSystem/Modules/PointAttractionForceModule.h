#pragma once
#include "ModuleBase.h"
#include "ModulePipeline.h"
#include "../ParticleAttributeNames.h"

namespace cross::fx {

class PointAttractionForceModule : public ModuleBase
{
public:
    friend class ParticleEmitterResource;

public:
    PointAttractionForceModule(ModuleScope scope, ModuleIndex index, ParticleEmitterResPtr res)
        : ModuleBase(scope, index)
        , mPointAttraction(std::make_shared<PointAttractionForceInfo>(res->GetPointAttractionForce()))
    {
        mEnabled = mPointAttraction->Enabled;
    }

    PointAttractionForceModule(ModuleScope scope, ModuleIndex index, const ParticleEmitterInfo& src)
        : ModuleBase(scope, index)
        , mPointAttraction(std::make_shared<PointAttractionForceInfo>(src.PointAttractionForce))
    {
        mEnabled = mPointAttraction->Enabled;
    };

    void Update(ModulePipelineContext& context) override;

    size_t Transfer(const ParticleEmitterInfo& emitterInfo, ParticleCurvePool& curvePool, size_t offset, std::vector<UInt8>& output) override;

    void Flush(const ParticleEmitterInfo& emitterInfo) override;

    void ResetSeed(UInt32 randomSeed) override;

private:
    std::shared_ptr<PointAttractionForceInfo> mPointAttraction;

    Rand mRand;
};

}   // namespace cross::fx