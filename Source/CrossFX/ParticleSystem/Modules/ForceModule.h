#pragma once
#include "ModuleBase.h"
#include "ModulePipeline.h"
#include "../ParticleAttributeNames.h"

namespace cross::fx {

class ForceModule : public ModuleBase
{
public:
    friend class ParticleEmitterResource;

public:
    ForceModule(ModuleScope scope, ModuleIndex index, ParticleEmitterResPtr res)
        : ModuleBase(scope, index)
    {
        mForce = std::make_shared<ForceInfo>(res->GetForce());
        mEnabled = mForce->Enabled;
        __PARTICLE_CURVE_INIT(mForce);
    };

    ForceModule(ModuleScope scope, ModuleIndex index, const ParticleEmitterInfo& src)
        : ModuleBase(scope, index)
    {
        mForce = std::make_shared<ForceInfo>(src.Force);
        mEnabled = mForce->Enabled;
        __PARTICLE_CURVE_INIT(mForce);
    };

    void Update(ModulePipelineContext& context) override;

    void Flush(const ParticleEmitterInfo& emitterInfo) override;

    size_t Transfer(const ParticleEmitterInfo& emitterInfo, ParticleCurvePool& curvePool, size_t offset, std::vector<UInt8>& output) override;

    void ResetSeed(UInt32 randomSeed) override;

    const ModuleInfoPtr GetModuleInfo() const override { return mForce; };

private:
    std::shared_ptr<ForceInfo> mForce;

    Rand mRand;
};

}   // namespace cross::fx