#pragma once
#include "ModuleBase.h"
#include "ModulePipeline.h"
#include "../ParticleAttributeNames.h"

namespace cross::fx {

class UVModule : public ModuleBase
{
public:
    friend class ParticleEmitterResource;

public:
    UVModule(ModuleScope scope, ModuleIndex index, ParticleEmitterResPtr res)
        : ModuleBase(scope, index)
    {
        mUV = std::make_shared<SubUVInfo>(res->GetSubUV());
        mEnabled = mUV->Enabled;
        __PARTICLE_CURVE_INIT(mUV);
    };

    UVModule(ModuleScope scope, ModuleIndex index, const ParticleEmitterInfo& src)
        : ModuleBase(scope, index)
    {
        mUV = std::make_shared<SubUVInfo>(src.SubUV);
        mEnabled = mUV->Enabled;
        __PARTICLE_CURVE_INIT(mUV);
    };

    void Update(ModulePipelineContext& context) override;

    void Flush(const ParticleEmitterInfo& emitterInfo) override;

    size_t Transfer(const ParticleEmitterInfo& emitterInfo, ParticleCurvePool& curvePool, size_t offset, std::vector<UInt8>& output) override;

    const ModuleInfoPtr GetModuleInfo() const override { return mUV; };

private:
    void UpdateGridInfinity(ParticleStatePool& state, UInt32 particleIndex, Float4& output);
    void updateGridWholeSheet(const ParticleStatePool& state, UInt32 particleIndex, Float4& output);
    void updateGridSingleRow(const ParticleStatePool& state, UInt32 particleIndex, Float4& output);

private:
    std::shared_ptr<SubUVInfo> mUV;
};

}   // namespace cross::fx