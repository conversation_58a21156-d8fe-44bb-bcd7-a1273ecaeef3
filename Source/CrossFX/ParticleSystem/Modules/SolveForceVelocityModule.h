#pragma once
#include "ModuleBase.h"
#include "ModulePipeline.h"
#include "../ParticleAttributeNames.h"

namespace cross::fx {

class SolveForceVelocityModule : public ModuleBase
{
public:
    friend class ParticleEmitterResource;

public:
    SolveForceVelocityModule(ModuleScope scope, ModuleIndex index, ParticleEmitterResPtr res)
        : ModuleBase(scope, index)
    {
        mSolveForceVelocity = std::make_shared<SolveForceVelocityInfo>(res->GetSolveForceVelocity());
        mEnabled = mSolveForceVelocity->Enabled;
        __PARTICLE_CURVE_INIT(mSolveForceVelocity);
    };

    SolveForceVelocityModule(ModuleScope scope, ModuleIndex index, const ParticleEmitterInfo& src)
        : ModuleBase(scope, index)
    {
        mSolveForceVelocity = std::make_shared<SolveForceVelocityInfo>(src.SolveForceVelocity);
        mEnabled = mSolveForceVelocity->Enabled;
        __PARTICLE_CURVE_INIT(mSolveForceVelocity);
    };

    void Update(ModulePipelineContext& context) override;

    void Flush(const ParticleEmitterInfo& emitterInfo) override;

    size_t Transfer(const ParticleEmitterInfo& emitterInfo, ParticleCurvePool& curvePool, size_t offset, std::vector<UInt8>& output) override;

    void ResetSeed(UInt32 randomSeed) override;

    const ModuleInfoPtr GetModuleInfo() const override { return mSolveForceVelocity; };

private:
    std::shared_ptr<SolveForceVelocityInfo> mSolveForceVelocity;

    Rand mRand;
};

}   // namespace cross::fx