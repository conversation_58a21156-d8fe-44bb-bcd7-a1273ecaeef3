#pragma once
#include "ParticleSystemCommon.h"

namespace cross::fx {

class ParticleSystemInstance;
struct ParticleStatePool;
enum class ExecutionMode
{
    SpawnParticles,
    EveryParticles,
};

enum class CEMeta(Reflect, Editor, Puerts) ParticleEventType
{
    SpawnEvent,
    DeathEvent,
    CollisionEvent,
    CustomEvent
};

struct CEMeta(<PERSON><PERSON><PERSON>, Editor, Puerts) ParticleEventData
{
    CEMeta(Reflect, Editor, ScriptReadWrite)
    ParticleEventType type;
    CEMeta(Reflect, Editor, ScriptReadWrite)
    Float3 worldPosition;
    CEMeta(Reflect, Editor, ScriptReadWrite)
    Float3 tilePosition;
    CEMeta(Reflect, Editor, ScriptReadWrite)
    SInt32 emitterIndex;
};

struct ParticleEventSpawnData : public ParticleEventData
{};

struct ParticleEventDeathData : public ParticleEventData
{};

struct ParticleEventCollisionData : public ParticleEventData
{};

struct ParticleEventCustomData : public ParticleEventData
{};

using ParticleEventCallback = std::function<void(const ParticleEventData&)>;
using ParticleCustumEventPredicate = std::function<bool(const ParticleStatePool&, UInt32)>;

class CrossFX_API ParticleEventHandler
{
public:
    ParticleEventHandler() = default;

    ParticleEventHandler(const ParticleSystemInstance* systemInstance, ParticleEventCallback callback, std::string_view name = "")
        : mSystemInstance(systemInstance)
        , mCallback(callback)
        , mName(name)
    {}

    void OnExecute(const ParticleEventData& eventData) const
    {
        mCallback(eventData);
    }

private:
    const ParticleSystemInstance* mSystemInstance;
    ParticleEventCallback mCallback;
    std::string mName;
};

}   // namespace cross::fx