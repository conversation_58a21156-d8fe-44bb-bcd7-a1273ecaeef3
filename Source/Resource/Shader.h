#pragma once
#include "CrossSchema/ShaderAsset_generated.h"
#include "CrossBase/String/NameID.h"
#include "CrossBase/Math/CrossMath.h"
#include "NativeGraphicsInterface/NGI.h"
#include "Resource/Resource.h"
#include "Resource/ResourceManager.h"
#include "Resource/RenderSyncResource.h"
#include "Resource/MaterialDefines.h"
#include "CECommon/Graphics/GPUPrototype.h"
#include "RenderEngine/BuiltinShaderParamsNames.h"
#include "RenderEngine/RenderEngineForward.h"
#include <bitset>

namespace cross {
    enum ShaderParamGroup : UInt8
    {
        ShaderParamGroup_Pass,
        ShaderParamGroup_Material,
        ShaderParamGroup_Model,
        ShaderParamGroup_Bindless,   // space3 for bindless resources
        ShaderParamGroup_Count,
    };

    enum class MaterialUsage : UInt32
    {
        USED_WITH_DEFAULT = 0x0,
        USED_WITH_SKELETAL_MESH = 0x1,
        USED_WITH_LOCAL_SPACE_PARTICLE = 0x2,
        USED_WITH_GLOBAL_SPACE_PARTICLE = 0x4,
        USED_WITH_TERRAIN = 0x8,
    };

    ENUM_CLASS_FLAGS(MaterialUsage);

    class IComputeShaderR;

    namespace resource {
        class ShaderVariationKey
        {
            friend class Shader;
        public:
            ShaderVariationKey(Shader* shader, UInt64 activeKeywords = 0)
                : mShader{shader}
                , mActiveKeywords{activeKeywords}
            {}
            ShaderVariationKey()
                : mShader{nullptr}
                , mActiveKeywords{0}
            {}
            Resource_API size_t GetMacroCount() const;

            Resource_API std::string_view GetMacroName(UInt32 i) const;

            Resource_API cross::NameID GetMacroNameID(UInt32 i);

            Resource_API void SetMacro(std::string_view keyword, bool enable);

            Resource_API bool IsMacroEnable(std::string_view keyword);

            void DisableAllKeywords()
            {
                mActiveKeywords.reset();
            }

            struct Hash
            {
                friend class ShaderVariationKey;

                size_t operator()(const ShaderVariationKey& k) const
                {
                    return k.mActiveKeywords.to_ullong();
                }

                bool operator()(const ShaderVariationKey& a, const ShaderVariationKey& b) const
                {
                    return a.mShader == b.mShader && a.mActiveKeywords == b.mActiveKeywords;
                }
            };

        private:
            Shader* mShader;
            std::bitset<64> mActiveKeywords;
        };

        struct ShaderBufferLayout : public NGIResourceDesc
        {
            UInt32 ByteSize{0};
            std::vector<NGIVariableDesc> Members;
        };

        struct ShaderResourceDesc : public NGIResourceDesc
        {
            CrossSchema::ShaderVariableType ReturnType;
            bool DepthTexture;
        };

        struct ShaderResourceGroupLayout
        {
            // resources except constant buffers and texture buffers
            std::vector<ShaderResourceDesc> ResourceLayouts;
            std::vector<ShaderBufferLayout> ConstantBufferLayouts;
        };

        class CEMeta(Cli, Script) Shader : public Resource
        {
            FRIEND_WITH_REFLECTION_MODULE;
            friend class ShaderVariationKey;

        protected:
            Shader() = default;

            Resource_API Shader(std::string name)
                : mName{std::move(name)}
            {}

        public:
            Resource_API ~Shader();

            struct ProtoType
            {
                GPUProtoType::ID ID = GPUProtoType::InvalidID;
                ShaderBufferLayout Layout;
            };

            struct ProgramDesc
            {
                NGIGraphicsProgramDesc GraphicsProgramDesc;
                NGIPipelineLayout* PipelineLayout;
                CrossUUID GUID;
                std::array<ShaderResourceGroupLayout, ShaderParamGroup_Count> ResourceGroupLayouts;

                ProtoType ObjectProtoType;
                ProtoType PrimitiveProtoType;
                ProtoType MaterialProtoType;
                GPUProtoType::ID MaterialProtoTypeID = GPUProtoType::InvalidID;  // deprecated

                std::optional<ShaderBufferLayout> ShaderConstantLayout;
                std::vector<NGICombinedSamplerDesc> CombinedSamplers;
                //Instancing
                VertexStreamLayout InstanceStreamLayout;
                std::optional<ShaderBufferLayout> InstanceDataLayout;
#if CROSSENGINE_EDITOR
                UInt64 mTime;
#endif
            };
            StatementDefaultResourceAPI(Resource_API, Shader)
            
            class ProtoTypeContainer
            {
            public:
                void FillProtoTypes(const CEHashMap<UInt64, Shader::ProgramDesc>& programDescs, const CEHashMap<std::string_view, UInt32>& keywordIndices);

                Resource_API const Shader::ProtoType* GetPrimitiveProtoType(MaterialUsage usage) const;
                Resource_API const Shader::ProtoType* GetObjectProtoType(MaterialUsage usage) const;
                Resource_API const Shader::ProtoType* GetMaterialProtoType() const;

            private:
                std::unordered_map<MaterialUsage, const Shader::ProtoType*> mPrimitiveProtoTypes;
                std::unordered_map<MaterialUsage, const Shader::ProtoType*> mObjectProtoTypes;
                const Shader::ProtoType* mMaterialProtoType = nullptr;
            };

            Resource_API bool Deserialize(SimpleSerializer const& s) override
            {
                return false;
            }
            Resource_API bool Deserialize(const FBSerializer& s) override;

            Resource_API bool ResetResource() override;

            Resource_API ProgramDesc* GetProgramDesc(const ShaderVariationKey& key);

            auto& GetMacroIDVec()
            {
                return mKeywordIDs;
            }

            template<typename TFunc> void VisitProgram(TFunc&& accesser)
            {
                for (auto& desc : mProgramDescs)
                {
                    accesser(desc.second);
                }
            }
        #if CROSSENGINE_EDITOR
            CEMeta(Cli)
            Resource_API std::string EditorGetProperty();
        #endif

            Resource_API bool SetupWithFlatbuffers(const void* data, const UInt64 size);

            auto* GetObjectProtoType() const { return mObjectProtoType; }

            auto* GetPrimitiveProtoType() const { return mPrimitiveProtoType; }

            //auto GetMaterialProtoTypeID() const { return mMaterialProtoTypeID; }

            const auto& GetVariantLayouts() const { return mVarientLayouts; }

            //Resource_API UInt32 GetMaterialConstantBufferSize() const;

            Resource_API const ShaderBufferLayout& GetMaterialConstantBufferLayout() const;

            auto& GetShaderConsts() 
            {
                return mShaderConsts;
            }

            auto& GetProtoTypeContainer() const { return mProtoTypeContainer; }

            static auto FillBufferLayout(auto* structType, ShaderBufferLayout& layout);

        private:
            std::unique_ptr<UInt8[]> mData;
            const CrossSchema::GraphicsShaderAsset* mResource;
            const CrossSchema::PlatformGraphicsShader* mCurrentShader;

            Resource_API virtual void AddSelfItem(flatbuffers::FlatBufferBuilder& outBuilder, flatbuffers::Offset<void>& outSelfOffset, CrossSchema::ResourceType& outResType) override;

            CEHashMap<UInt64, ProgramDesc> mProgramDescs;
            CrossSchema::GraphicsShaderAssetT mShaderAssetT;

            friend class ShaderVariationKey;

            CEHashMap<std::string_view, UInt32> mKeywordIndices;
            std::vector<cross::NameID> mKeywordIDs;

            std::set<cross::NameID> mShaderConsts;

            void FillAllShaderConsts();

            const ProtoType* mObjectProtoType = nullptr;
            const ProtoType* mPrimitiveProtoType = nullptr;
            const ProtoType* mMaterialProtoType = nullptr;
            std::map<GPUProtoType::ID, ShaderBufferLayout> mVarientLayouts;
            //GPUProtoType::ID mMaterialProtoTypeID = GPUProtoType::InvalidID;

            ProtoTypeContainer mProtoTypeContainer;

            std::string mName;

 #if CROSSENGINE_EDITOR
        public:
            Resource_API bool Serialize(const CrossSchema::GraphicsShaderAssetT& inGraphicsAsset);
 #endif
            friend class cross::ResourceManager;
        };

        auto Shader::FillBufferLayout(auto* structType, ShaderBufferLayout& layout)
        {
            for (const auto& objMem : *structType->members())
            {
                layout.Members.push_back({
                    objMem->name()->c_str(),
                    objMem->type(),
                    objMem->row_count(),
                    objMem->col_count(),
                    0,
                    objMem->offset(),
                    objMem->size(),
                    objMem->array_size(),
                    0,
                });
            }
            layout.ByteSize = structType->size();
            Assert(layout.ByteSize != 0);
            Assert(structType->size() != 0);
        }
        
        // ---------------------------------------------Compute Shader---------------------------------------------
        struct ComputeProgramDesc
        {
            NGIComputeProgramDesc ProgramDesc;
            NGIPipelineLayout* PipelineLayout;
            CrossUUID GUID;
            CrossSchema::uint3 GroupSize;
            std::array<ShaderResourceGroupLayout, ShaderParamGroup_Count> ResourceGroupLayouts;
            std::optional<ShaderBufferLayout> ShaderConstantLayout;
            std::vector<NGICombinedSamplerDesc> CombinedSamplers;
        };

        class CEMeta(Script) ComputeShader : public RenderSyncResource
        {
            FRIEND_WITH_REFLECTION_MODULE;
        private:
            void DestroyRenderData(FrameParam* frameParam) override;

        protected:
            Resource_API ComputeShader();

        public:
            Resource_API bool Deserialize(const FBSerializer& s) override;
            Resource_API bool ResetResource() override;
            Resource_API ComputeProgramDesc* GetProgramDesc(const NameID& name);

            IComputeShaderR* GetRenderObject()
            {
                return mRenderObject;
            }

            bool SetupWithFlatbuffers(const void* data, const UInt64 size);

#if CROSSENGINE_EDITOR
        public:
            Resource_API bool Serialize(const CrossSchema::ComputeShaderAssetT& inComputeAsset);
            friend class cross::ResourceManager;
#endif

        private:
            std::unique_ptr<UInt8[]> mData;
            const CrossSchema::ComputeShaderAsset* mResource;
            const CrossSchema::PlatformComputeShader* mCurrentShader;
            std::shared_mutex  mProgramDescMutex;
            std::unordered_map<NameID, ComputeProgramDesc> mProgramDescs;

            IComputeShaderR* mRenderObject = nullptr;
        };

        
        // ---------------------------------------------RayTracing Shader---------------------------------------------
        struct RayTracingProgramDesc
        {
            NGIRayTracingProgramDesc ProgramDesc;
            NGIPipelineLayout* PipelineLayout = nullptr;
            CrossUUID GUID;
            Shader::ProtoType MaterialProtoType;
            std::array<ShaderResourceGroupLayout, ShaderParamGroup_Count> ResourceGroupLayouts;
            std::optional<ShaderBufferLayout> ShaderConstantLayout;
            std::vector<NGICombinedSamplerDesc> CombinedSamplers;
        };
        
        class CEMeta(Script) RayTracingShader : public RenderSyncResource
        {
            FRIEND_WITH_REFLECTION_MODULE;
            
            void DestroyRenderData(FrameParam* frameParam) override;

        public:
            Resource_API RayTracingShader();
            
            Resource_API bool Deserialize(const FBSerializer& s) override;

            bool ResetResource() override;

            Resource_API RayTracingProgramDesc* GetProgramDesc();
            
            IRayTracingShaderR* GetRenderObject() const
            {
                return mRenderObject;
            }
            
            bool SetupWithFlatbuffers(const void* data, const UInt64 size);

#if CROSSENGINE_EDITOR
            Resource_API bool Serialize(const CrossSchema::RayTracingShaderAssetT& inRayTracingAsset);
            
            friend class ResourceManager;
#endif
            
        private:
            std::unique_ptr<UInt8[]> mData;
            const CrossSchema::RayTracingShaderAsset* mResource = nullptr;
            const CrossSchema::PlatformRayTracingShader* mCurrentShader = nullptr;
            
            // NOTE(scolu): Each RayTracingShader holds one RayTracingProgramDesc only(quite different from ComputeShader, since ComputeShader stores multiple kernels)
            std::shared_mutex  mProgramDescMutex;
            RayTracingProgramDesc mProgramDesc;
            IRayTracingShaderR* mRenderObject = nullptr;

            const Shader::ProtoType* mObjectProtoType = nullptr;
            const Shader::ProtoType* mPrimitiveProtoType = nullptr;
            const Shader::ProtoType* mMaterialProtoType = nullptr;
            Shader::ProtoTypeContainer mProtoTypeContainer;
        };
        
    }  // namespace resource

}  // namespace cross
