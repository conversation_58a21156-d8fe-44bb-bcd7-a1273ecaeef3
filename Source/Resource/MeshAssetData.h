#pragma once
#include "CrossBase/uuid/CrossUUID.h"
#include "ImportMeshAssetData_generated.h"
#include "ResourceManager.h"
#include "Resource/IResourceInterface.h"
#include "CECommon/Common/MeshDefines.h"
#include "CECommon/Common/RenderStateDefines.h"
#include "CrossBase/Math/CrossMath.h"
#include "CrossPhysics/PhysicsEngine/PhysicsGeometry.h"
#include "CEAnimation/Skeleton/ReferenceSkeleton.h"
#include "CECommon/Resource/Serialize/JsonSerialize.h"
#include "MeshDataCommon.h"
#include "Cluster/Cluster.h"
#include "Cluster/ClusterBuilder.h"

namespace cross {

enum class CEMeta(Editor) EditMeshLodError: int
{
    ERR_EDITOR_SUCCESS = 0,
    ERR_EDITOR_RES_TYPE = 1,
    ERR_EDITOR_LOD_OVERFLOW = 2,
    ERR_EDITOR_MESH_PART_COUNT = 3,
    ERR_EDITOR_SERIALIZE = 4,
};

template<typename T>
class CrossSpan
{
public:
    void resize(size_t size)
    {
        if (static_cast<size_t>(mSpan.size()) != size)
        {
            clear();

            auto data = new T[size];
            mSpan.assign(data, size);
        }
    }

    void clear()
    {
        if (mSpan.size() > 0)
        {
            delete[] mSpan.data();
            mSpan.assign(nullptr, 0);
        }
    }

    const T* data() const
    {
        return mSpan.data();
    }

    T* data()
    {
        return mSpan.data();
    }

    void shrink_to_fit()
    {
        // do nothing
    }

    // we use vector to help erase.
    // note, this function should not be call frequently and at performance critical situation
    void erase(const typename gsl::span<T>::iterator begin, const typename gsl::span<T>::iterator end)
    {
        std::vector<T> data(mSpan.size());
        memcpy(data.data(), mSpan.data(), mSpan.size() * sizeof(T));
        data.erase(data.begin() + (begin - mSpan.begin()), data.begin() + (end - mSpan.begin()));

        clear();
        auto new_data = new T[data.size()];
        memcpy(new_data, data.data(), data.size() * sizeof(T));
        mSpan.assign(new_data, data.size());
    }

    void insert(const typename gsl::span<T>::iterator begin, UInt32 count, T value)
    {
        std::vector<T> data(mSpan.size());
        memcpy(data.data(), mSpan.data(), mSpan.size() * sizeof(T));
        data.insert(data.begin() + (begin - mSpan.begin()), count, value);

        clear();

        auto new_data = new T[data.size()];
        memcpy(new_data, data.data(), data.size() * sizeof(T));
        mSpan.assign(new_data, data.size());
    }

    auto begin() const
    {
        return mSpan.begin();
    }

    auto end() const
    {
        return mSpan.end();
    }

    auto size() const
    {
        return static_cast<size_t>(mSpan.size());
    }

    bool empty() const
    {
        return mSpan.size() == 0;
    }

    T& operator[](size_t pos)
    {
        return mSpan[pos];
    }

    const T& operator[](size_t pos) const
    {
        return mSpan[pos];
    }

public:
    gsl::span<T> mSpan;
};


#define MAX_UV_NUM 4

struct UVRangeInfo
{
    Int2 mMin;
    Int2 mGapStart;
    Int2 mGapLength;
    UInt32 mPrecision;
    UInt32 mBitsU;
    UInt32 mBitsV;
};

struct MeshCollisionNode
{
    int Index;
    int LeftIndex;
    int RightIndex;
    MeshBound Bound;
    std::vector<int> TriangleList;
};


struct CustomAttributeInfo
{
    UInt32 mKeyNameHash{0};
    UInt32 mDataFlag{0};
    UInt32 mDataSizeInByte{0};
    UInt32 mDataOffset{0};

    void Clear()
    {
        mKeyNameHash = 0;
        mDataFlag = 0;
        mDataSizeInByte = 0;
        mDataOffset = 0;
    }
};

class BitWriter
{
public:
    BitWriter(std::vector<UInt8>& buffer)
        : mBuffer(buffer)
        , mPendingBits(0ull)
        , mNumPendingBits(0)
    {}

    void PutBits(UInt32 Bits, UInt32 NumBits)
    {
        assert((UInt64)Bits < (1ull << NumBits));
        mPendingBits |= (UInt64)Bits << mNumPendingBits;
        mNumPendingBits += NumBits;

        while (mNumPendingBits >= 8)
        {
            mBuffer.push_back((UInt8)mPendingBits);
            mPendingBits >>= 8;
            mNumPendingBits -= 8;
        }
    }

    void Flush(UInt32 Alignment = 1)
    {
        if (mNumPendingBits > 0)
            mBuffer.push_back((UInt8)mPendingBits);
        while (mBuffer.size() % Alignment != 0)
            mBuffer.push_back(0);
        mPendingBits = 0;
        mNumPendingBits = 0;
    }

private:
    std::vector<UInt8>& mBuffer;
    UInt64 mPendingBits;
    SInt32 mNumPendingBits;
};

struct DeltaShapeInfo
{
    UInt32 mVertexStart{0};
    UInt32 mVertexCount{0};
};

struct BlendShapeChannelInfo
{
    // [0.0, 1.0]
    std::vector<float> NormalizedFullWeights;
    // Each delta shape only has vertices's info it actually influences, and influence has been calculated as delta to base shape.
    std::vector<DeltaShapeInfo> DeltaShapes;
};

template<class Key, class Value, class = std::enable_if_t<std::is_same_v<Key, HashString>>>
using NameMap = CEHashMap<Key, Value>;

// Pair<SubModelIndex, ChannelIndex>
using ChannelCollectInfo = std::vector<std::pair<UInt32, UInt32>>;
using ChannelWeightData = NameMap<HashString, float>;

struct BlendShapeInfo
{
    UInt32 VertexChannelSemanticMask{0};
    // Channel Position0, Normal0, Tangent0, Color0 as InfluencedVertexID
    std::vector<VertexChannelAssetData> VertexChannelData;
    NameMap<HashString, UInt32> ChannelNameToIndexMap;
    std::vector<HashString> ChannelNameData;
    std::vector<BlendShapeChannelInfo> ChannelShapeData;

    bool HasBlendShape() const
    {
        return !ChannelShapeData.empty();
    }

    template<class T1, class T2>
    bool AddChannelData(VertexSemantic sem, VertexChannel channel, VertexFormat format, const std::vector<T1>& inVec)
    {
        if (!IsChannelExist(channel))
        {
            VertexChannelData.emplace_back();
            VertexChannelAssetData& vertexChannel = VertexChannelData.back();
            vertexChannel.mStride = sizeof(T2);
            vertexChannel.mVertexChannel = channel;
            vertexChannel.mDataFormat = format;
            VertexChannelSemanticMask |= static_cast<std::uint32_t>(sem);
            vertexChannel.mData.clear();
        }

        VertexChannelAssetData* vertexChannel = GetVertexChannelData(channel);
        auto orgSize = static_cast<std::uint32_t>(vertexChannel->mData.size());
        vertexChannel->mData.resize(orgSize + static_cast<std::uint32_t>(vertexChannel->mStride) * inVec.size());
        Assert(vertexChannel->mData.size() == orgSize + inVec.size() * sizeof(T2));
        memcpy(vertexChannel->mData.data() + orgSize, inVec.data(), inVec.size() * sizeof(T2));

        return true;
    }

    inline bool AddPosition(const std::vector<Float3>& posVec)
    {
        return AddChannelData<Float3, Float3>(SemanticPosition, VertexChannel::Position0, VertexFormat::Float3, posVec);
    }

    inline bool AddInfluencedID(const std::vector<UInt32>& influencedIDVec)
    {
        return AddChannelData<UInt32, UInt32>(SemanticColor, VertexChannel::Color0, VertexFormat::UInt, influencedIDVec);
    }

    inline bool AddNormal(const std::vector<Float3>& normalVec)
    {
        return AddChannelData<Float3, Float3>(SemanticNormal, VertexChannel::Normal0, VertexFormat::Float3, normalVec);
    }

    inline bool AddTangent(const std::vector<Float4>& tangentVec)
    {
        return AddChannelData<Float4, Float4>(SemanticTangent, VertexChannel::Tangent0, VertexFormat::Float4, tangentVec);
    }

    VertexChannelAssetData* GetVertexChannelData(VertexChannel channel)
    {
        for (int i = 0; i < VertexChannelData.size(); i++)
        {
            if (VertexChannelData[i].mVertexChannel == channel)
            {
                return &VertexChannelData[i];
            }
        }
        return nullptr;
    }

    const VertexChannelAssetData* GetVertexChannelData(VertexChannel channel) const
    {
        for (int i = 0; i < VertexChannelData.size(); i++)
        {
            if (VertexChannelData[i].mVertexChannel == channel)
            {
                return &VertexChannelData[i];
            }
        }
        return nullptr;
    }

    UInt32 GetVertexCount(VertexChannel posChannel) const
    {
        if (!IsChannelExist(posChannel))
        {
            return 0;
        }

        const VertexChannelAssetData* vertexChannel = GetVertexChannelData(posChannel);
        return static_cast<UInt32>(vertexChannel->mData.size()) / static_cast<UInt32>(vertexChannel->mStride);
    }

    bool IsChannelExist(VertexChannel channel) const
    {
        for (int i = 0; i < VertexChannelData.size(); i++)
        {
            if (VertexChannelData[i].mVertexChannel == channel)
            {
                return true;
            }
        }

        return false;
    }

    bool Deserialize(const CrossSchema::ImportBlendShapeInfo& blendShape);
};

struct MeshPartAssetInfo
{
    SInt16 mNameIndex{-1};
    SInt16 mMaterialIndex{-1};
    UInt32 mVertexStart{0};
    UInt32 mVertexCount{0};
    UInt32 mIndexStart{0};
    UInt32 mIndexCount{0};
    UInt32 mPrimitiveCount{0};
    MeshBound mMeshBound;
    UInt64 mMiscFlag{0};
    float mShadowBias{0};
    float mShadowNormalBias{0};
    PrimitiveTopology mPrimitiveType{PrimitiveTopology::Invalid};
    UInt8 mRenderPriority{0};

    CustomAttributeInfo mCustomAttributeInfo;
    std::vector<UInt8> mCustomAttributeData;

    std::vector<MeshCollisionNode> mCollisionTree;

    BlendShapeInfo mBlendShape;

    bool HasBlendShape() const
    {
        return mBlendShape.HasBlendShape();
    }
};

#pragma warning(push)
#pragma warning(disable: 4127) // C4127: conditional expression is constant
template<class T>
void ConvertToFloat(const UInt8* srcAddr, cross::VertexFormat format, T& dest, bool bIgnoreW = false)
{
    switch (format)
    {
    case cross::VertexFormat::Float:
        Assert(sizeof(T) >= sizeof(float));
        *reinterpret_cast<float*>(&dest) = *reinterpret_cast<const float*>(srcAddr);
        break;
    case cross::VertexFormat::Float2:
        Assert(sizeof(T) >= sizeof(float) * 2);
        *reinterpret_cast<float*>(&dest) = *reinterpret_cast<const float*>(srcAddr);
        *(reinterpret_cast<float*>(&dest) + 1) = *(reinterpret_cast<const float*>(srcAddr) + 1);
        break;
    case cross::VertexFormat::Float3:
        Assert(sizeof(T) >= sizeof(float) * 3);
        *reinterpret_cast<float*>(&dest) = *reinterpret_cast<const float*>(srcAddr);
        *(reinterpret_cast<float*>(&dest) + 1) = *(reinterpret_cast<const float*>(srcAddr) + 1);
        *(reinterpret_cast<float*>(&dest) + 2) = *(reinterpret_cast<const float*>(srcAddr) + 2);
        break;
    case cross::VertexFormat::Float4:
        if (bIgnoreW)
        {
            Assert(sizeof(T) >= sizeof(float) * 3);
        }
        else
        {
            Assert(sizeof(T) >= sizeof(float) * 4);
            *(reinterpret_cast<float*>(&dest) + 3) = *(reinterpret_cast<const float*>(srcAddr) + 3);
        }
        *reinterpret_cast<float*>(&dest) = *reinterpret_cast<const float*>(srcAddr);
        *(reinterpret_cast<float*>(&dest) + 1) = *(reinterpret_cast<const float*>(srcAddr) + 1);
        *(reinterpret_cast<float*>(&dest) + 2) = *(reinterpret_cast<const float*>(srcAddr) + 2);
        break;

    case cross::VertexFormat::Half2:
        Assert(sizeof(T) >= sizeof(float) * 2);
        HalfToFloat(*reinterpret_cast<const UInt16*>(srcAddr), *reinterpret_cast<float*>(&dest));
        HalfToFloat(*(reinterpret_cast<const UInt16*>(srcAddr) + 1), *(reinterpret_cast<float*>(&dest) + 1));
        break;
    case cross::VertexFormat::Half3:
        Assert(sizeof(T) >= sizeof(float) * 3);
        HalfToFloat(*reinterpret_cast<const UInt16*>(srcAddr), *reinterpret_cast<float*>(&dest));
        HalfToFloat(*(reinterpret_cast<const UInt16*>(srcAddr) + 1), *(reinterpret_cast<float*>(&dest) + 1));
        HalfToFloat(*(reinterpret_cast<const UInt16*>(srcAddr) + 2), *(reinterpret_cast<float*>(&dest) + 2));
        break;
    case cross::VertexFormat::Half4:
        if (bIgnoreW)
        {
            Assert(sizeof(T) >= sizeof(float) * 3);
        }
        else
        {
            Assert(sizeof(T) >= sizeof(float) * 4);
            HalfToFloat(*(reinterpret_cast<const UInt16*>(srcAddr) + 3), *(reinterpret_cast<float*>(&dest) + 3));
        }
        HalfToFloat(*reinterpret_cast<const UInt16*>(srcAddr), *reinterpret_cast<float*>(&dest));
        HalfToFloat(*(reinterpret_cast<const UInt16*>(srcAddr) + 1), *(reinterpret_cast<float*>(&dest) + 1));
        HalfToFloat(*(reinterpret_cast<const UInt16*>(srcAddr) + 2), *(reinterpret_cast<float*>(&dest) + 2));
        break;

    case cross::VertexFormat::Short2_Norm:
        Assert(sizeof(T) >= sizeof(float) * 2);
        SNormToFloat(*reinterpret_cast<const SInt16*>(srcAddr), *reinterpret_cast<float*>(&dest));
        SNormToFloat(*(reinterpret_cast<const SInt16*>(srcAddr) + 1), *(reinterpret_cast<float*>(&dest) + 1));
        break;
    case cross::VertexFormat::Short3_Norm:
        Assert(sizeof(T) >= sizeof(float) * 3);
        SNormToFloat(*reinterpret_cast<const SInt16*>(srcAddr), *reinterpret_cast<float*>(&dest));
        SNormToFloat(*(reinterpret_cast<const SInt16*>(srcAddr) + 1), *(reinterpret_cast<float*>(&dest) + 1));
        SNormToFloat(*(reinterpret_cast<const SInt16*>(srcAddr) + 2), *(reinterpret_cast<float*>(&dest) + 2));
        break;
    case cross::VertexFormat::Short4_Norm:
        if (bIgnoreW)
        {
            Assert(sizeof(T) >= sizeof(float) * 3);
        }
        else
        {
            Assert(sizeof(T) >= sizeof(float) * 4);
            SNormToFloat(*(reinterpret_cast<const SInt16*>(srcAddr) + 3), *(reinterpret_cast<float*>(&dest) + 3));
        }
        SNormToFloat(*reinterpret_cast<const SInt16*>(srcAddr), *reinterpret_cast<float*>(&dest));
        SNormToFloat(*(reinterpret_cast<const SInt16*>(srcAddr) + 1), *(reinterpret_cast<float*>(&dest) + 1));
        SNormToFloat(*(reinterpret_cast<const SInt16*>(srcAddr) + 2), *(reinterpret_cast<float*>(&dest) + 2));
        break;

    case cross::VertexFormat::Byte3_Norm:
        Assert(sizeof(T) >= sizeof(float) * 3);
        SNormToFloat(*reinterpret_cast<const SInt8*>(srcAddr), *reinterpret_cast<float*>(&dest));
        SNormToFloat(*(reinterpret_cast<const SInt8*>(srcAddr) + 1), *(reinterpret_cast<float*>(&dest) + 1));
        SNormToFloat(*(reinterpret_cast<const SInt8*>(srcAddr) + 2), *(reinterpret_cast<float*>(&dest) + 2));
        break;
    case cross::VertexFormat::Byte4_Norm:
        if (bIgnoreW)
        {
            Assert(sizeof(T) >= sizeof(float) * 3);
        }
        else
        {
            Assert(sizeof(T) >= sizeof(float) * 4);
            SNormToFloat(*(reinterpret_cast<const SInt8*>(srcAddr) + 3), *(reinterpret_cast<float*>(&dest) + 3));
        }
        SNormToFloat(*reinterpret_cast<const SInt8*>(srcAddr), *reinterpret_cast<float*>(&dest));
        SNormToFloat(*(reinterpret_cast<const SInt8*>(srcAddr) + 1), *(reinterpret_cast<float*>(&dest) + 1));
        SNormToFloat(*(reinterpret_cast<const SInt8*>(srcAddr) + 2), *(reinterpret_cast<float*>(&dest) + 2));
        break;
    case cross::VertexFormat::Color:
        Assert(sizeof(T) >= sizeof(float) * 4);
        SNormToFloat(*reinterpret_cast<const UInt8*>(srcAddr), *reinterpret_cast<float*>(&dest));
        SNormToFloat(*(reinterpret_cast<const UInt8*>(srcAddr) + 1), *(reinterpret_cast<float*>(&dest) + 1));
        SNormToFloat(*(reinterpret_cast<const UInt8*>(srcAddr) + 2), *(reinterpret_cast<float*>(&dest) + 2));
        SNormToFloat(*(reinterpret_cast<const UInt8*>(srcAddr) + 3), *(reinterpret_cast<float*>(&dest) + 3));
        break;
    default:
        Assert(false);
        break;
    }
}
#pragma warning(pop)
enum class MeshAssetVersionFlags : UInt32
{
    VersionNone = 0x0000, 
    VersionMeshCurvation = 0x0001, 
    VersionPhysicsCurvation = 0x0002,
    VersionMax = VersionPhysicsCurvation,
};

class MeshAssetData final
{
public:
    Resource_API MeshAssetData();
    Resource_API MeshAssetData(bool is16BitIndex);
    MeshAssetData(MeshAssetData const&) = delete;
    Resource_API MeshAssetData(MeshAssetData&&) = default;
    MeshAssetData& operator=(MeshAssetData const&) = delete;
    Resource_API MeshAssetData& operator=(MeshAssetData&&) = default;
    Resource_API ~MeshAssetData() = default;
    Resource_API void OnDataUploaded();
    inline const std::string& GetName() const
    {
        return mName;
    }
    inline void SetVertexCount(UInt32 count)
    {
        mVertexCount = count;
    }
    inline UInt32 GetVertexCount() const
    {
        return mVertexCount;
    }
    inline void SetPrimitiveCount(UInt32 count)
    {
        mPrimitiveCount = count;
    }
    inline UInt32 GetPrimitiveCount() const
    {
        return mPrimitiveCount;
    }
    inline IndexStreamAssetData& GetIndexStream()
    {
        return mIndexStream;
    }
    inline const IndexStreamAssetData& GetIndexStream() const
    {
        return mIndexStream;
    }
    inline bool HasIndexStream() const
    {
        return mIndexStream.mCount > 0 && !mIndexStream.mData.empty();
    }
    inline UInt32 GetVertexChannelCount() const
    {
        return static_cast<UInt32>(mVertexChannelData.size());
    }
    inline const VertexChannelAssetData* GetVertexChannelDataByIndex(UInt32 index) const
    {
        return index < mVertexChannelData.size() ? &mVertexChannelData[index] : nullptr;
    }
    inline void ClearVertexData()
    {
        for (auto i = 0; i < mVertexChannelData.size(); i++)
        {
            mVertexChannelData[i].mData.clear();
            std::vector<UInt8, ce_stl_allocator<UInt8>>().swap(mVertexChannelData[i].mData);
        }
    }
    inline VertexChannelAssetData* GetVertexChannelData(VertexChannel vertexChannel)
    {
        for (auto i = 0; i < mVertexChannelData.size(); i++)
        {
            if (mVertexChannelData[i].mVertexChannel == vertexChannel)
                return &mVertexChannelData[i];
        }
        return nullptr;
    }
    inline const VertexChannelAssetData* GetVertexChannelData(VertexChannel vertexChannel) const
    {
        for (auto i = 0; i < mVertexChannelData.size(); i++)
        {
            if (mVertexChannelData[i].mVertexChannel == vertexChannel)
                return &mVertexChannelData[i];
        }
        return nullptr;
    }
    inline bool HasVertexChannel(VertexChannel channel) const
    {
        for (auto& vChannel : mVertexChannelData)
        {
            if (vChannel.mVertexChannel == channel)
                return true;
        }
        return false;
    }
    inline bool HasVertexSemantic(VertexSemantic semantic) const
    {
        return (mVertexChannelSemanticMask & semantic) > 0;
    }
    inline UInt32 GetUVChannelCount() const
    {
        UInt32 count = 0;
        for (auto& vChannel : mVertexChannelData)
        {
            if (GetSemantic(vChannel.mVertexChannel) == SemanticTexCoord)
                count++;
        }
        return count;
    }
    inline UInt32 GetUVChannelMaxIndex() const
    {
        UInt32 maxIndex = 0;
        for (auto& vChannel : mVertexChannelData)
        {
            if (GetSemantic(vChannel.mVertexChannel) == SemanticTexCoord)
            {
                UInt32 temp = static_cast<UInt32>(vChannel.mVertexChannel) - static_cast<UInt32>(SemanticTexCoord) + 1U;
                maxIndex = temp > maxIndex ? temp : maxIndex;
            }
        }
        return maxIndex;
    }
    inline bool HasPreDefinedLayout() const
    {
        if (mVertexChannelData.size() > 0)
        {
            return mVertexChannelData[0].mStream != -1;
        }
        return false;
    }
    inline UInt8 GetLodCount() const
    {
        return (UInt8)mMeshPartLodStartIndex.size();
    }
    inline const std::vector<UInt32>& GetLodStartIndex() const
    {
        return mMeshPartLodStartIndex;
    }
    MeshPartAssetInfo& GetMeshPartInfo(UInt32 index)
    {
        return mMeshPartInfo[index];
    }
    const MeshPartAssetInfo& GetMeshPartInfo(UInt32 index) const
    {
        return mMeshPartInfo[index];
    }
    const std::string* GetMeshPartName(UInt32 index) const
    {
        if (index < mMeshPartInfo.size() && mMeshPartInfo[index].mNameIndex < mMeshPartNames.size())
        {
            return &mMeshPartNames[mMeshPartInfo[index].mNameIndex];
        }
        return nullptr;
    }
    const std::string* GetMeshPartMaterial(UInt32 meshPartIndex) const
    {
        if (meshPartIndex < mMeshPartInfo.size() && mMeshPartInfo[meshPartIndex].mMaterialIndex < mMaterialNames.size())
        {
            return &mMaterialNames[mMeshPartInfo[meshPartIndex].mMaterialIndex];
        }
        return nullptr;
    }
    inline UInt32 GetAllLodMeshPartCount() const
    {
        return (UInt32)mMeshPartInfo.size();
    }
    UInt32 GetMeshPartCount(UInt32 lod) const
    {
        if (lod < mMeshPartLodStartIndex.size())
        {
            if (lod + 1 < mMeshPartLodStartIndex.size())
                return mMeshPartLodStartIndex[lod + 1] - mMeshPartLodStartIndex[lod];
            else
                return static_cast<UInt32>(mMeshPartInfo.size()) - mMeshPartLodStartIndex[lod];
        }
        else
        {
            return 0;
        }
    }
    void GetMeshLodInfo(UInt32 lod, UInt32& meshPartStartIndex, UInt32& meshPartCount) const
    {
        if (lod < mMeshPartLodStartIndex.size())
        {
            meshPartStartIndex = mMeshPartLodStartIndex[lod];
            meshPartCount = GetMeshPartCount(lod);
        }
        else
        {
            meshPartStartIndex = 0;
            meshPartCount = 0;
        }
    }

    UInt32 GetMeshPartMaxCount() const
    {
        UInt32 meshPartCount = 0u;
        for (auto lodIndex = 0; lodIndex < mMeshPartLodStartIndex.size(); ++lodIndex)
        {
            meshPartCount = std::max(meshPartCount, GetMeshPartCount(lodIndex));
        }
        return meshPartCount;
    }

    std::vector<VertexChannel> GartherVertexChannelInfos() const
    {
        std::vector<VertexChannel> informations;
        informations.reserve(mVertexChannelData.size());
        for (const auto& channel : mVertexChannelData)
        {
            informations.emplace_back(channel.mVertexChannel);
        }
        return informations;
    }

    std::vector<VertexFormat> GartherVertexFormatInfos() const
    {
        std::vector<VertexFormat> informations;
        informations.reserve(mVertexChannelData.size());
        for (const auto& channel : mVertexChannelData)
        {
            informations.emplace_back(channel.mDataFormat);
        }
        return informations;
    }

    struct GetRawVertexAndIndexDataOutput
    {
        const UInt8* vertexData = nullptr;
        UInt32 vertexCount = 0;
        UInt16 vertexStride = 0;
        VertexFormat vertexDataFormat = VertexFormat::Unknown;

        const UInt8* indexData = nullptr;
        UInt32 indexCount = 0;
        UInt16 indexStride = 0;
        bool is16BitIndex = false;
    };

    Resource_API GetRawVertexAndIndexDataOutput GetRawVertexAndIndexData(UInt32 lod, VertexChannel dataChannel = VertexChannel::Position0) const;
    Resource_API GetRawVertexAndIndexDataOutput GetMeshPartData(UInt32 lod, UInt32 meshPart) const;

    template<typename DstType>
    void GetRawVertexData(UInt32 lod, VertexChannel channel, std::vector<DstType>& output, bool bIgnoreW = false) const
    {
        const UInt32 lodCount = GetLodCount();
        Assert(lodCount > 0 && lod < lodCount);

        const VertexChannelAssetData* vertexChannel = GetVertexChannelData(channel);

        if (vertexChannel == nullptr) return;

        UInt32 lodMeshPartStartIndex = 0;
        UInt32 lodMeshCount = 0;
        GetMeshLodInfo(lod, lodMeshPartStartIndex, lodMeshCount);
        Assert(lodMeshCount > 0);

        UInt32 vertexStart = GetMeshPartInfo(lodMeshPartStartIndex).mVertexStart;
        const UInt8* vertexData = vertexChannel->mData.data() + vertexStart * vertexChannel->mStride;
        UInt32 vertexCount = 0;
        UInt16 vertexStride = vertexChannel->mStride;
        VertexFormat vertexDataFormat = vertexChannel->mDataFormat;

        for (UInt32 meshPart = lodMeshPartStartIndex; meshPart < lodMeshPartStartIndex + lodMeshCount; ++meshPart)
        {
            const MeshPartAssetInfo& part = GetMeshPartInfo(meshPart);
            vertexCount += part.mVertexCount;
        }

        if (vertexStride < sizeof(DstType))
        {
            output.reserve(vertexCount);
            for (auto i = 0u; i < vertexCount; ++i)
            {
                ConvertToFloat(vertexData + i * vertexStride, vertexDataFormat, output.emplace_back(), bIgnoreW);
            }
        }
        else
        {
            output.resize(vertexCount);
            memcpy(output.data(), vertexData, vertexCount * vertexStride);
        }
    }

    template<typename DstType>
    void GetRawVertexData(VertexChannel channel, std::vector<DstType>& output, bool bIgnoreW = false) const
    {
        const UInt32 lodCount = GetLodCount();
        const VertexChannelAssetData* vertexChannel = GetVertexChannelData(channel);

        if (vertexChannel == nullptr)
            return;

        const UInt8* vertexData = vertexChannel->mData.data();
        UInt32 vertexCount = static_cast<UInt32>(vertexChannel->mData.size() / vertexChannel->mStride);
        UInt16 vertexStride = vertexChannel->mStride;
        VertexFormat vertexDataFormat = vertexChannel->mDataFormat;

        if (vertexStride < sizeof(DstType))
        {
            output.reserve(vertexCount);
            for (auto i = 0u; i < vertexCount; ++i)
            {
                ConvertToFloat(vertexData + i * vertexStride, vertexDataFormat, output.emplace_back(), bIgnoreW);
            }
        }
        else
        {
            output.resize(vertexCount);
            memcpy(output.data(), vertexData, vertexCount * vertexStride);
        }
    }

    Resource_API void GetIndexData(UInt32 lod, std::vector<UInt32>& outputIndices) const;

    inline const std::vector<MeshCollisionNode>& GetCollisionTree() const
    {
        return mCollisionTree;
    }
    inline UInt16 GetCustomAttributeVersion() const
    {
        return mCustomAttributeVersion;
    }
    inline UInt16 GetCustomAttributeFlag() const
    {
        return mCustomAttributeVersionFlag;
    }
    inline const UInt8* GetCustomAttributeData() const
    {
        return mCustomAttribute.data();
    }

    const cross::skeleton::ReferenceSkeleton* GetRefSkeleton() const
    {
        return &mMeshRefSkelt;
    }

    inline const std::vector<SIMDMatrix>& GetBindPoseInvMat() const
    {
        return mBindPoseInvMat;
    }
    Resource_API bool IsSkinValid() const;
    UInt32 GetVertexChannelSemanticMask() const
    {
        return mVertexChannelSemanticMask;
    }

    // pick data
    // collision data
    // occlusion data

    Resource_API bool SerializeToCrossSchema(std::vector<char>& fbBufVec, std::string ndaFilePath);
    Resource_API bool DeserializeFromCrossSchema(const CrossSchema::ImportMeshAssetData& importMeshAsset);
    Resource_API bool ToImportMeshAssetDataT(CrossSchema::ImportMeshAssetDataT& meshAssetDataT);

    VertexChannelAssetData& GetChannelAssetData(VertexChannel channel)
    {
        for (int i = 0; i < mVertexChannelData.size(); i++)
        {
            if (mVertexChannelData[i].mVertexChannel == channel) {
                return mVertexChannelData[i];
            }
        }
        throw std::runtime_error("GetChannelAssetData cannot find the channel in mVertexChannelData\n");
    }

    static BoundingBox ComputeAABB(const GetRawVertexAndIndexDataOutput& rawData)
    {
        UInt16 stride = rawData.vertexStride;
        Assert(stride == 3 * sizeof(float));
        auto* data = rawData.vertexData;
        UInt32 count = rawData.vertexCount;

        BoundingBox aabb;
        BoundingBox::CreateFromPoints(aabb, count, reinterpret_cast<const Float3*>(data), stride);

        return aabb;
    }

    BoundingBox GetBoundingBox() const
    {
        if (mAABB.Max.x == -INFINITY || mAABB.Min.x == INFINITY)
        {
            Float3 extent(1.0f, 1.0f, 1.0f);
            Float3 center(0.0f, 0.0f, 0.0f);
            BoundingBox bb(center, extent);
            return bb;
        }
        Float3 extent((mAABB.Max.x - mAABB.Min.x) / 2, (mAABB.Max.y - mAABB.Min.y) / 2, (mAABB.Max.z - mAABB.Min.z) / 2);

        Float3 center(extent.x + mAABB.Min.x, extent.y + mAABB.Min.y, extent.z + mAABB.Min.z);

        BoundingBox bb(center, extent);

        return bb;
    }

    Resource_API BoundingSphere CalculateBoundingSphere(UInt32 lod);

    Resource_API BoundingSphere CalculateBoundingSphere(UInt32 lod, UInt32 meshPartIndex);
    
    Resource_API BoundingBox GetMeshPartBoundingBox(UInt32 meshPart);

    void CalculateWholeAABB()
    {
        VertexChannelAssetData& vertpos = GetChannelAssetData(VertexChannel::Position0);
        UInt32 count = static_cast<UInt32>(vertpos.mData.size()) / static_cast<UInt32>(vertpos.mStride);
        for (UInt32 i = 0; i < count; i++)
        {
            Float3 point;
            memcpy(point.data(), vertpos.mData.data() + i * vertpos.mStride, vertpos.mStride);

            mAABB.Min = {(std::min)(point.x, mAABB.Min.x), (std::min)(point.y, mAABB.Min.y), (std::min)(point.z, mAABB.Min.z)};

            mAABB.Max = {(std::max)(point.x, mAABB.Max.x), (std::max)(point.y, mAABB.Max.y), (std::max)(point.z, mAABB.Max.z)};
        }
    }

    inline void MergeAABB(const MeshBound& aabb) { mAABB.Encapsulate(aabb); }

    inline const auto& GetAABB() { return mAABB; }

    bool CopyMeshPartVertex(std::vector<Float3>& mVertexList, std::vector<int>& mIndexList, UInt32 allMeshPartIndex)
    {
        const MeshPartAssetInfo& meshPart = GetMeshPartInfo(allMeshPartIndex);

        VertexChannelAssetData& vertpos = GetChannelAssetData(VertexChannel::Position0);

        mVertexList.resize(meshPart.mVertexCount);
        memcpy(mVertexList.data(), vertpos.mData.data() + (vertpos.mStride * meshPart.mVertexStart), vertpos.mStride * meshPart.mVertexCount);

        mIndexList.resize(meshPart.mIndexCount);
        const IndexStreamAssetData& assetIndexStream = GetIndexStream();
        for (UInt32 i = 0; i < meshPart.mIndexCount; i++)
        {
            UInt32 t = 0;
            if (assetIndexStream.mIs16BitIndex)
            {
                const UInt16* mData16 = reinterpret_cast<const UInt16*>(assetIndexStream.mData.data());
                t = static_cast<UInt32>(mData16[i + meshPart.mIndexStart]);
            }
            else
            {
                const UInt32* mData32 = reinterpret_cast<const UInt32*>(assetIndexStream.mData.data());
                t = static_cast<UInt32>(mData32[i + meshPart.mIndexStart]);
            }
            Assert(t >= 0);
            //Assert(t < static_cast<UInt32>(mVertexList.size()));
            mIndexList[i] = static_cast<int>(t);
        }

        return true;
    }

    void Clear()
    {
        mName.clear();
        mVertexCount = 0;
        mPrimitiveCount = 0;
        mVertexChannelSemanticMask = 0;
        mIndexStream.Clear();
        mVertexChannelData.clear();
        mMeshPartInfo.clear();
        mMeshPartLodStartIndex.clear();
        mMaterialNames.clear();
        mMeshPartNames.clear();
        mCollisionTree.clear();
        mPhysicsCollision.Clear();
        // mAABB.clear();
        mCustomAttributeVersion = 0;
        mCustomAttributeVersionFlag = 0;
        mCustomAttributeInfo.Clear();
        mCustomAttribute.clear();
    }
    
    const PhysicsCollision* GetPhysicsCollision() const
    {
        return &mPhysicsCollision;
    }

    PhysicsCollision* GetPhysicsCollision()
    {
        return &mPhysicsCollision;
    }

    UInt32 GetMeshCollisionCount() const
    {
        return static_cast<UInt32>(mCollisionTree.size());
    }

    //void SetSharedRenderGeometry(IRuntimeObject* geo) const
    //{
    //    mSharedRenderGeometry = geo;
    //}

    //IRuntimeObject* GetSharedRenderGeometry() const
    //{
    //    return mSharedRenderGeometry;
    //}

    Resource_API IMeshR* GetRenderMesh() const;

    const NameMap<HashString, ChannelCollectInfo>& GetBlendShapeChannelMap(UInt32 lodIndex) const
    {
        Assert(lodIndex < mBlendShapeLODChannelMap.size());
        return mBlendShapeLODChannelMap[lodIndex];
    }

    const std::vector<NameMap<HashString, ChannelCollectInfo>>& GetBlendShapeLODChannelMap() const
    {
        return mBlendShapeLODChannelMap;
    }

    Resource_API bool IsMeshStreamable() const { return mIsStreamable; }

    //-------------For modify interface Begin--------------------
    Resource_API EditMeshLodError UpdateLodMeshData(UInt8 lod, const MeshAssetData* newMeshData);

    Resource_API EditMeshLodError AddLodMeshData(const MeshAssetData* newMeshData);

    Resource_API EditMeshLodError DelLodMeshData(UInt8 lod);

    Resource_API int SeparateTo(Float2 point, Float2 blockSize, std::vector<MeshAssetData>& outMeshs);

    Resource_API int SeparateTo(Float2 point, Float2 blockSize, MeshAssetData& outMesh);

    Resource_API static int Combine(const std::vector<MeshAssetData*>& inMeshs, const std::vector<Float4x4>& trans, const std::vector<std::vector<std::array<int, 4>>>& texIndexs, MeshAssetData& outMesh);

    Resource_API void AddCollisionMesh(int type = 0);

    Resource_API Float3 ChangeMeshCenterToPointsCenter();

    Resource_API void AddVersionFlag(MeshAssetVersionFlags ver)
    {
        mVersion = static_cast<MeshAssetVersionFlags>(static_cast<UInt32>(mVersion) | static_cast<UInt32>(ver));
    };
    Resource_API bool HaveVersionFlag(MeshAssetVersionFlags ver) const
    {
        if (ver == MeshAssetVersionFlags::VersionNone)
            return true;
        return (static_cast<UInt32>(mVersion) & static_cast<UInt32> (ver)) > 0;
    };

    MeshPartAssetInfo& AddMeshPart() { return mMeshPartInfo.emplace_back(); }

    void AddMeshLod() { mMeshPartLodStartIndex.emplace_back(static_cast<UInt32>(mMeshPartInfo.size())); }

    void SetName(const std::string& name) { mName = name; };

    void AddMeshPartName(const std::string& partName) { mMeshPartNames.emplace_back(partName); };

    void AddMaterialName(const std::string& matName) { mMaterialNames.emplace_back(matName); };

    inline void AddVertexCount(UInt32 count) { mVertexCount += count; };

    inline void AddPrimitiveCount(UInt32 count) { mPrimitiveCount += count; };

    template<class T>
    bool AddChannelData(VertexChannel channel, VertexFormat format, const T& data);

    template<class T>
    bool AddChannelData(VertexChannel channel, VertexFormat format, const std::vector<T>& inVec);

    Resource_API void AddChannelData(VertexChannel channel, VertexFormat format, UInt16 stride, const UInt8* inData, UInt32 inSize);

    template<class T>
    void AddIndexData(const T& index0);

    template<class T>
    void AddTriangleData(const T& index0, const T& index1, const T& index2);

    Resource_API void SetMeshStreamable(bool enabled) { mIsStreamable = enabled; }

    //-------------For modify interface End----------------------

    auto GetAssetGUID() const { return mAssetGUID; }
    void SetAssetGUID(CrossUUID assetGUID) { mAssetGUID = assetGUID; }

    Resource_API bool IsClusterMeshEnabled() const;

private:
    //-------------For modify interface Begin--------------------
    void CollectBlendShapeChannelInfo(UInt32 meshPartIndex);

    void SeparateTo(UInt8 lod, std::vector<Double2>& blockCenters, std::vector<MeshAssetData>& outMeshs, std::vector<std::unordered_map<UInt32, UInt32>>& outIndexMaps);
    //-------------For modify interface End----------------------

private:
    MeshAssetVersionFlags mVersion{MeshAssetVersionFlags::VersionNone};
    std::string mName{};
    UInt32 mVertexCount{0};
    UInt32 mPrimitiveCount{0};
    UInt32 mVertexChannelSemanticMask{0};
    IndexStreamAssetData mIndexStream{};
    std::vector<VertexChannelAssetData> mVertexChannelData{};
    std::vector<MeshPartAssetInfo> mMeshPartInfo{};
    std::vector<UInt32> mMeshPartLodStartIndex{};
    std::vector<std::string> mMaterialNames{};
    std::vector<std::string> mMeshPartNames{};
    std::vector<MeshCollisionNode> mCollisionTree{};
    PhysicsCollision mPhysicsCollision{};
    MeshBound mAABB{{INFINITY, INFINITY, INFINITY}, {-INFINITY, -INFINITY, -INFINITY}};

    /*
     *	Skinning information.
     *	The reference skeleton used by this mesh.
     *	If the mesh is static mesh, this member should not be initialized, which means mMeshRefSkelt.GetRawBoneNum() == 0.
     */
    cross::skeleton::ReferenceSkeleton mMeshRefSkelt{};

    /*
     *	Skinning information.
     *	The bindpose inverse matrices array used by this mesh. size = mMeshRefSkelt.GetRawBoneNum()
     *	If the mesh is static mesh, this member should not be initialized, which means mBindPoseInvMat.size() == 0.
     */
    std::vector<SIMDMatrix> mBindPoseInvMat{};

    // Blend shape lod channel info
    std::vector<NameMap<HashString, ChannelCollectInfo>> mBlendShapeLODChannelMap{};

    UInt16 mCustomAttributeVersion{0};
    UInt16 mCustomAttributeVersionFlag{0};
    CustomAttributeInfo mCustomAttributeInfo{};
    std::vector<UInt8> mCustomAttribute{};

    //mutable IRuntimeObject* mSharedRenderGeometry{nullptr};

    std::unique_ptr<IMeshR> mRenderMesh{nullptr};
    bool mIsStreamable = false;

    bool mClusterMeshEnabled{false};

    CrossUUID mAssetGUID{};
};

using MeshAssetDataPtr = std::shared_ptr<MeshAssetData>;

template<class T>
bool MeshAssetData::AddChannelData(VertexChannel channel, VertexFormat format, const T& data)
{
    if (!HasVertexChannel(channel))
    {
        VertexChannelAssetData& vertexChannel = mVertexChannelData.emplace_back();
        vertexChannel.mStride = sizeof(T);
        vertexChannel.mVertexChannel = channel;
        vertexChannel.mDataFormat = format;
        mVertexChannelSemanticMask |= static_cast<UInt32>(GetSemantic(channel));
        vertexChannel.mData.clear();
    }

    VertexChannelAssetData* vertexChannel = GetVertexChannelData(channel);
    auto orgSize = static_cast<UInt32>(vertexChannel->mData.size());
    vertexChannel->mData.resize(orgSize + static_cast<UInt32>(vertexChannel->mStride));
    Assert(vertexChannel->mData.size() == orgSize + sizeof(T));
    memcpy(vertexChannel->mData.data() + orgSize, &data, sizeof(T));

    return true;
};

template<class T>
bool MeshAssetData::AddChannelData(VertexChannel channel, VertexFormat format, const std::vector<T>& inVec)
{
    if (!HasVertexChannel(channel))
    {
        VertexChannelAssetData& vertexChannel = mVertexChannelData.emplace_back();
        vertexChannel.mStride = sizeof(T);
        vertexChannel.mVertexChannel = channel;
        vertexChannel.mDataFormat = format;
        mVertexChannelSemanticMask |= static_cast<UInt32>(GetSemantic(channel));
        vertexChannel.mData.clear();
    }

    VertexChannelAssetData* vertexChannel = GetVertexChannelData(channel);
    auto orgSize = static_cast<UInt32>(vertexChannel->mData.size());
    vertexChannel->mData.resize(orgSize + static_cast<UInt32>(vertexChannel->mStride) * inVec.size());
    Assert(vertexChannel->mData.size() == orgSize + inVec.size() * sizeof(T));
    memcpy(vertexChannel->mData.data() + orgSize, inVec.data(), inVec.size() * sizeof(T));

    return true;
};

template<class T>
void MeshAssetData::AddIndexData(const T& index)
{
    UInt16 stride = mIndexStream.mIs16BitIndex ? 2 : 4;
    auto oldSize = mIndexStream.mData.size();
    mIndexStream.mData.resize(oldSize + stride);
    memcpy(mIndexStream.mData.data() + oldSize, &index, stride);
    mIndexStream.mCount += 1;
};

template<class T>
void MeshAssetData::AddTriangleData(const T& index0, const T& index1, const T& index2)
{
    AddIndexData(index0);
    AddIndexData(index1);
    AddIndexData(index2);
}
}   // namespace cross
