#include "EnginePrefix.h"
#include "Material.h"
#include "Resource/Shader.h"
#include "Resource/AssetStreaming.h"
#include "Resource/ResourceManager.h"
#include "Resource/Texture/Texture2D.h"
#include "Shader.h"
#include "NativeGraphicsInterface/RHI/DeviceTypes.h"
// #include "Runtime/RenderSystem/GPUContext/GPUState/GGraphicState.h"
#include "CECommon/Common/MeshDefines.h"
#include "Threading/RenderingThread.h"
#include "NativeGraphicsInterface/NGI.h"
#include "CrossBase/Serialization/SerializeNode.h"
#include "CECommon/Allocator/FrameAllocator.h"
#include "CECommon/Common/FrameContainer.h"
#include "CrossBase/Threading/Task.h"
#include "CECommon/Common/EngineGlobal.h"
#include "Resource/Texture/Texture.h"
#include "FileSystem/filesystemutil.h"
#include "Resource/ResourceConvertManager.h"
#include <CECommon/Common/FrameStdContainer.h>

using CrossSchema::ShaderStageBit;

namespace cross::resource {
// Deprecated Deserialzation Method
// For Old Asset
bool Material::Deserialize(SimpleSerializer const& s)
{
    Assert(false);
    LOG_ERROR("Old material asset format not support");

    return true;
}

bool Material::Deserialize(const DeserializeNode& s)
{
    Clear();

    mVersion = 1;

    if (s.HasMember("version"))
    {
        mVersion = s["version"].AsInt32();
    }

    if (auto parent = s.HasMember("parent"), fx = s.HasMember("fx"); parent || fx)
    {
        std::string str = GetPathFromSerialNode(parent ? *parent : *fx);
        auto res = gAssetStreamingManager->GetResource(str);
        SetParentInternal(TypeCast<MaterialInterface>(res));
    }
    else if (auto shader = s.HasMember("shader"); shader)   // Temp code
    {
        AssertMsg(false, "Deprecated Material!!!");
    }

    // for material v1.0
    if (auto mpc = s.HasMember("parameter_collection"); mpc && mpc->IsObject())
    {
        const auto& path = GetPathFromSerialNode(*mpc);
        auto res = gAssetStreamingManager->GetResource(path);
        if (res && res->GetClassID() == ClassID(MaterialParameterCollection))
        {
            MPCPtr mpcRes = TypeCast<MaterialParameterCollection>(res);
            SetMpcSource(mpcRes);
        }
    }

    // If loss parent, use default material instead
    if (!mBaseMaterial)
    {
        LOG_ERROR("{} has no parent material or fx!", GetName().c_str());
        mBaseMaterial = TypeCast<MaterialInterface>(GetDefault());
        mBaseMaterial->AddChildMaterial(this);
    }

    if (auto properties = s.HasMember("properties"); properties && properties->IsObject())
    {
        for (auto [k, v] : *properties)
        {
            if (auto prop = Fx::DeserializePropertyValue(v); prop)
            {
                auto name = k.data();
                if (mPropertyMap.find(name) != mPropertyMap.end())
                {
                    LOG_WARN("{} has duplicated property: {}", GetName().c_str(), name);
                    continue;
                }
                if (auto fxProperty = GetFx()->GetProperty(name); fxProperty)
                {
                    if (fxProperty->index() == prop->index())
                    {
                        mPropertyMap[name] = *prop;

                        if (fxProperty->index() == 0)
                        {
                            auto& fxVal = std::get<0>(*fxProperty);
                            auto& matVal = std::get<0>(*prop);
                            if (fxVal.size() != matVal.size())
                            {
                                LOG_WARN("{} has property {} which type is float{} in fx but float{} in material.", GetName().c_str(), name, fxVal.size(), matVal.size());
                                mPropertyMap[name] = *fxProperty;
                            }
                        }
                    }
                    else
                    {
                        mPropertyMap[name] = *fxProperty;
                    }
                }
                else
                {
                    mPropertyMap[name] = *prop;
                }
            }
        }
    }

    if (auto enables = s.HasMember("enable"); enables && enables->IsObject())
    {
        for (auto [passName, enable] : *enables)
        {
            mPasses[passName.data()].mEnable = enable.AsBoolean();
        }
    }

    if (auto state = s.HasMember("state"); state && state->IsObject())
    {
        for (auto [passName, passState] : *state)
        {
            MaterialPassState realState{};
            if (passState.HasMember("depth_enable"))
            {
                // old version
                realState = {DeserializeBlendStateDesc(passState), DeserializeDepthStencilStateDesc(passState), DeserializeRasterizerStateDesc(passState), DeserializeDynamicStateDesc(passState)};
            }
            else
            {
                SerializeContext ctx;
                realState.Deserialize(passState, ctx);
            }
            mPasses[passName.data()].mState = realState;
        }
    }

    if (auto group = s.HasMember("render_group"); group && group->IsObject())
    {
        for (auto [passName, group_num] : *group)
        {
            if (group_num.IsNumber())
            {
                mPasses[passName.data()].mRenderGroup = group_num.AsUInt32();
            }
        }
    }

    // if (EngineGlobal::GetSettingMgr()->GetEnableTangents())
    //{
    //     auto prop = GetProperty(EngineGlobal::GetSettingMgr()->GetQTangentsMarco());
    //     if (!prop || !*std::get_if<bool>(prop))
    //     {
    //         LOG_ERROR("{} QTANGENTS is enabled in project config while disabled/not existed in material, you better know what you are doing!", GetName().c_str());
    //     }
    // }
    // else
    //{
    //     auto prop = GetProperty(EngineGlobal::GetSettingMgr()->GetQTangentsMarco());
    //     if (prop && *std::get_if<bool>(prop))
    //     {
    //         LOG_ERROR("{} QTANGENTS is diabled in project config while enabled in material, you better know what you are doing!", GetName().c_str());
    //     }
    // }

    if (s.HasMember("defines"))
    {
        SerializeContext context{};
        mDefines.Deserialize(s["defines"], context);
        mDefines.ParentMaterial = mBaseMaterial->GetGuid_Str();
    }

    if (IsCreatedByMaterialEditor())
    {
        RegistAllMpc();
    }
    else
    {
        // note, this code is solely for support old version of material
        // it should not be considered valid option in material which create through material editor;
        auto forwardPassID = NameID("forward");
        bool foreward_pass = GetFx()->HasPass(forwardPassID);
        if (foreward_pass)
        {
            bool enable_separate_translucency = GetPropertyBool(NameID("CE_ENABLE_SEPERATE_TRANSLUCENCY"));
            bool parent_separate_trans_state = false;

            if (auto parent_variant = GetParent()->GetProperty(NameID("CE_ENABLE_SEPERATE_TRANSLUCENCY")))
            {
                parent_separate_trans_state = std::get<bool>(*parent_variant);
            }

            if (enable_separate_translucency != parent_separate_trans_state)
            {
                mPasses[forwardPassID].mEnable = GetParent()->IsPassEnable(forwardPassID);
                mPasses[forwardPassID].mRenderGroup = GetParent()->GetRenderGroup(forwardPassID);
                mPasses[forwardPassID].mState = *GetParent()->GetState(forwardPassID);

                mPasses[forwardPassID].mState.value().BlendStateDesc.TargetBlendState[1].WriteMask = enable_separate_translucency ? ColorMask::All : ColorMask::None;
                mPasses[forwardPassID].mState.value().BlendStateDesc.TargetBlendStateVector[1] = mPasses[forwardPassID].mState.value().BlendStateDesc.TargetBlendState[1];
            }
        }
    }

    return true;
}

bool Material::Serialize(SerializeNode&& s, const std::string& path)
{
    if (!Resource::HasAsset())
    {
        CreateAsset(path);
    }

    Resource::ClearReference();

    if (mBaseMaterial)
    {
        Assert(Resource::HasAsset());
        // s["parent"] = mBaseMaterial->GetSerialNode();
        s["parent"] = {_N(ASSET_MAGIC_NUMBER_KEY, ASSET_MAGIC_NUMBER), _N(PATH_KEY, mBaseMaterial->GetGuid_Str())};
        Resource::AddReferenceResource(mBaseMaterial->GetGuid_Str());
    }

    s["version"] = mVersion;

    for (auto& [name, prop] : mPropertyMap)
    {
        std::visit(Overloaded{[&](const std::vector<float>& vec) {
                                  for (auto& e : vec)
                                  {
                                      s["properties"][name.GetName()].PushBack(e);
                                  }
                              },
                              [&](bool val) { s["properties"][name.GetName()] = val; },
                              [&](TexturePtr& texture) {
                                  // if (texture->GetName() == gAssetStreamingManager->GetResource(gResourceMgr.GetDefaultTexturePath())->GetName())
                                  // if (texture->GetName() == gAssetStreamingManager->LoadSynchronously(gResourceMgr.GetDefaultTexturePath())->GetName())
                                  if (texture->GetName() == gResourceMgr.GetResource(gResourceMgr.GetDefaultTexturePath())->GetName())
                                  {
                                      s["properties"][name.GetName()] = "default_texture";
                                  }
                                  // else if (texture->GetName() == ResourceManager::Instance().GBlackTex->GetName())
                                  //{
                                  //    s["properties"][name.GetName()] = "black_texture";
                                  //}
                                  else if (texture->HasAsset())
                                  {
                                      s["properties"][name.GetName()] = texture->GetGuid_Str();
                                      Resource::AddReferenceResource(texture->GetGuid_Str());
                                  }
                              },
                              [&](const SamplerState& samplerState) {
                                  SerializeContext ctx;
                                  s["properties"][name.GetName()] = samplerState.Serialize(ctx);
                              }},
                   prop);
    }

    for (auto& [passName, pass] : mPasses)
    {
        if (pass.mEnable)
        {
            s["enable"][passName.GetName()] = *pass.mEnable;
        }

        if (pass.mRenderGroup)
        {
            s["render_group"][passName.GetName()] = *pass.mRenderGroup;
        }

        if (pass.mState)
        {
            auto& state = *pass.mState;
            SerializeContext ctx;
            s["state"][passName.GetName()] = state.Serialize(ctx);
        }
    }

    if (mParameterCollectionSource && mParameterCollectionSource->HasAsset())
    {
        Resource::AddReferenceResource(mParameterCollectionSource->GetGuid_Str());
        s["parameter_collection"] = {_N(ASSET_MAGIC_NUMBER_KEY, ASSET_MAGIC_NUMBER), _N(PATH_KEY, mParameterCollectionSource->GetGuid_Str())};

        if (mParameterUsageView.HasValue())
        {
            const auto& usages = mParameterUsageView.Members;
            for (const auto& item : usages)
            {
                s["parameter_collection"]["Usage"].PushBack(item);
            }
        }
    }

    {
        SerializeContext context{};
        s["defines"] = mDefines.Serialize(context);
    }

    return Resource::Serialize(std::move(s), path);
}

bool Material::Serialize(SerializeNode&& s, const std::string& path, MaterialPtr existMat)
{
    if (!Resource::HasAsset())
    {
        CreateAsset(path);
    }

    Resource::ClearReference();

    s["version"] = mVersion;

    if (mBaseMaterial)
    {
        Assert(Resource::HasAsset());
        // auto existBaseMat = existMat->mBaseMaterial->GetSerialNode();
        // auto newBaseMat = mBaseMaterial->GetSerialNode();
        // s["parent"] = mBaseMaterial->GetSerialNode();
        s["parent"] = {_N(ASSET_MAGIC_NUMBER_KEY, ASSET_MAGIC_NUMBER), _N(PATH_KEY, mBaseMaterial->GetGuid_Str())};
        Resource::AddReferenceResource(mBaseMaterial->GetGuid_Str());
    }

    // add old's
    std::map<std::string, int> oldSizeMap;
    for (auto& [name, val] : existMat->mPropertyMap)
    {
        // Skip properties not exposed in fx file
        // if (!GetFx()->GetProperty(name))
        //    continue;
        switch (val.index())
        {
        case 0:
        {
            auto vec = std::get_if<0>(&val);
            for (auto& e : *vec)
            {
                s["properties"][name.GetName()].PushBack(e);
                oldSizeMap[name.GetName()]++;
            }
            break;
        }
        case 1:
        {
            s["properties"][name.GetName()] = *std::get_if<1>(&val);
            break;
        }
        case 2:
        {
            auto texture = *std::get_if<2>(&val);
            // if (texture->GetName() == gAssetStreamingManager->GetResource(gResourceMgr.GetDefaultTexturePath())->GetName())
            // if (texture->GetName() == gAssetStreamingManager->LoadSynchronously(gResourceMgr.GetDefaultTexturePath())->GetName())
            if (texture->GetName() == gResourceMgr.GetResource(gResourceMgr.GetDefaultTexturePath())->GetName())
            {
                s["properties"][name.GetName()] = "default_texture";
            }
            // else if (texture->GetName() == ResourceManager::Instance().GBlackTex->GetName())
            //{
            //    s["properties"][name.GetName()] = "black_texture";
            //}
            else if (texture->HasAsset())
            {
                s["properties"][name.GetName()] = texture->GetGuid_Str();
                Resource::AddReferenceResource(texture->GetGuid_Str());
            }
            break;
        }
        case 3:
        {
            break;
        }
        default:
            break;
        }
    }
    // add new
    for (auto& [name, val] : mPropertyMap)
    {
        /*if (s["properties"].HasMember(name.GetName()))
        {
            continue;
        }*/
        switch (val.index())
        {
        case 0:
        {
            for (int index = 0; index < oldSizeMap[name.GetName()]; index++)
            {
                s["properties"][name.GetName()].PopBack();
            }
            auto vec = std::get_if<0>(&val);
            for (auto& e : *vec)
            {
                s["properties"][name.GetName()].PushBack(e);
            }
            break;
        }
        case 1:
        {
            s["properties"][name.GetName()] = *std::get_if<1>(&val);
            break;
        }
        case 2:
        {
            auto texture = *std::get_if<2>(&val);
            // if (texture->GetName() == gAssetStreamingManager->GetResource(gResourceMgr.GetDefaultTexturePath())->GetName())
            // if (texture->GetName() == gAssetStreamingManager->LoadSynchronously(gResourceMgr.GetDefaultTexturePath())->GetName())
            if (texture->GetName() == gResourceMgr.GetResource(gResourceMgr.GetDefaultTexturePath())->GetName())
            {
                s["properties"][name.GetName()] = "default_texture";
            }
            // else if (texture->GetName() == ResourceManager::Instance().GBlackTex->GetName())
            //{
            //    s["properties"][name.GetName()] = "black_texture";
            //}
            else if (texture->HasAsset())
            {
                s["properties"][name.GetName()] = texture->GetGuid_Str();
                Resource::AddReferenceResource(texture->GetGuid_Str());
            }
            break;
        }
        case 3:
        {
            break;
        }
        default:
            break;
        }
    }

    auto SerializePasses = [&](auto& passes) {
        for (auto& [passName, pass] : passes)
        {
            if (pass.mEnable)
            {
                s["enable"][passName.GetName()] = *pass.mEnable;
            }

            if (pass.mRenderGroup)
            {
                s["render_group"][passName.GetName()] = *pass.mRenderGroup;
            }

            if (pass.mState)
            {
                auto& state = *pass.mState;
                SerializeContext ctx;
                s["state"][passName.GetName()] = state.Serialize(ctx);
            }
        }
    };

    SerializePasses(existMat->mPasses);
    SerializePasses(mPasses);

    return Resource::Serialize(std::move(s), path);
}

void Material::VisitProperty(PropertyAccesser func) const
{
    mBaseMaterial->VisitProperty([&](NameID const& name, Material::PropertyType const& val) {
        if (mPropertyMap.find(name) == mPropertyMap.end())
            func(name, val);
    });

    for (const auto& v : mPropertyMap)
    {
        func(v.first, v.second);
    }
}

Material::PropertyType const* Material::GetProperty(NameID const& name) const
{
    if (auto iter = mParameterUsageMap.find(name); iter != mParameterUsageMap.end())
    {
        return &iter->second;
    }
    if (auto iter = mPropertyMap.find(name); iter != mPropertyMap.end())
    {
        return &iter->second;
    }
    else
    {
        return mBaseMaterial->GetProperty(name);
    }
}

bool Material::IsPropertyOverride(const NameID& name) const
{
    return mPropertyMap.find(name) != mPropertyMap.end();
}

void Material::FillParameterCollectionValue(MPCPtr mpc)
{
    for (const auto& mpcInfo : GetFx()->GetMaterialParameterCollectionInfo())
    {
        if (mpcInfo.mMpcSourcePtr == mpc)
        {
            for (const auto& name : mpcInfo.mUsedParameters)
            {
                std::string propertyName = mpcInfo.mName + "_" + name;

                auto value = mpc->GetNumericParameterValue(name);

                // used property has been deleted in mpc
                if (value.empty())
                {
                    value.emplace_back(0.f);
                }

                mPropertyMap[propertyName] = value;
            }
            break;
        }
    }
    mPropertyMap[MATERIAL_ERROR_FLAG_NAME] = HasMpcError();
}

void Material::FillAllParameterCollectionValue()
{
    for (const auto& mpc : GetFx()->GetMaterialParameterCollectionInfo())
    {
        FillParameterCollectionValue(mpc.mMpcSourcePtr);
    }
}

bool Material::HasMpcError()
{
    return GetFx()->HasMpcError();
}

void Material::RegistAllMpc()
{
    for (const auto& mpc : GetFx()->GetMaterialParameterCollectionInfo())
    {
        mpc.mMpcSourcePtr->RegisterMaterialInterfaceHolder(MaterialInterfacePtr(const_cast<Material*>(this)));
    }
}

void Material::UnregistAllMpc()
{
    if (mBaseMaterial == nullptr)
        return;
    for (const auto& mpc : GetFx()->GetMaterialParameterCollectionInfo())
    {
        mpc.mMpcSourcePtr->UnRegisterMaterialInterfaceHolder(MaterialInterfacePtr(const_cast<Material*>(this)));
    }
}

void Material::InitStates(const NameID& passID)
{
    // if (mPasses.find(passID) != mPasses.end())
    //     return;
    auto s = GetState(passID);
    if (s)
    {
        mPasses[passID].mState = *s;
    }
    else
    {
        NGIBlendStateDesc defaultBs = GetDefaultBlendState();
        NGIDepthStencilStateDesc defaultDss = GetDefaultDepthStencilState();
        NGIRasterizationStateDesc defaultRs = GetDefaultRasterizationState();
        NGIDynamicStateDesc defaultDs = GetDefaultDynamicState();

        mPasses[passID].mState = {defaultBs, defaultDss, defaultRs, defaultDs};
    }
}

void Material::Clear()
{
    UnregistAllMpc();
    UnparentInternal();
    mPasses.clear();
    mPropertyMap.clear();
}

std::string Material::GetName() const
{
    if (!Resource::HasAsset() && mBaseMaterial)
    {
        return mBaseMaterial->GetName();
    }
    return Resource::GetName();
}

const std::string& Material::GetGuid_Str() const
{
    if (!Resource::HasAsset() && mBaseMaterial)
    {
        return mBaseMaterial->GetGuid_Str();
    }
    return Resource::GetGuid_Str();
}

bool Material::HasAsset() const
{
    if (!Resource::HasAsset() && mBaseMaterial)
    {
        return mBaseMaterial->HasAsset();
    }
    return Resource::HasAsset();
}

UInt32 Material::GetRenderGroup(NameID const& passID)
{
    const auto& pID = passID.mPropertyID == 0 ? GetFx()->GetDefaultPass().name : passID;
    if (auto iter = mPasses.find(pID); iter != mPasses.end() && iter->second.mRenderGroup)
        return iter->second.mRenderGroup.value();
    else if (mBaseMaterial)
        return mBaseMaterial->GetRenderGroup(pID);
    else
        return GetFx()->GetRenderGroup(passID);
}

bool Material::IsPassEnable(const NameID& passID) const
{
    const auto& pID = passID.mPropertyID == 0 ? GetFx()->GetDefaultPass().name : passID;
    if (auto iter = mPasses.find(pID); iter != mPasses.end() && iter->second.mEnable)
        return iter->second.mEnable.value();
    else if (mBaseMaterial)
        return mBaseMaterial->IsPassEnable(pID);
    else
        return GetFx()->IsPassEnable(pID);
}

void Material::SetPassEnable(const NameID& passID, bool enable)
{
    const auto& pID = passID.mPropertyID == 0 ? GetFx()->GetDefaultPass().name : passID;

    if (!mPasses.contains(pID))
    {
        return;
    }

    if (mPasses[pID].mEnable != enable)
    {
        mPasses[pID].mEnable = enable;

        ResourceManager::Instance().EnqueueMaterialRenderingCommand([passID, enable, materialR = this->mRenderMaterial] { materialR->SetEnable(passID, enable); });

        for (auto* child : mChildMaterial)
        {
            child->OnParentDirty(passID, enable);
        }
    }
}

Shader* Material::GetShader(const NameID& passId)
{
    return GetFx()->GetShader(passId);
}

bool const* Material::GetPropertyVisible(const NameID& name) const
{
    if (auto fx = GetFx())
        return fx->GetPropertyVisible(name);
    else
        return nullptr;
}

bool const* Material::GetPropertyIsColor(const NameID& name) const
{
    if (auto fx = GetFx())
        return fx->GetPropertyIsColor(name);
    else
        return nullptr;
}

Material::StateType const* Material::GetState(const NameID& passID) const
{
    const auto& pID = passID.mPropertyID == 0 ? GetFx()->GetDefaultPass().name : passID;
    if (auto iter = mPasses.find(pID); iter != mPasses.end() && iter->second.mState)
    {
        return &iter->second.mState.value();
    }
    else
    {
        return mBaseMaterial->GetState(pID);
    }
}

void Material::ResetRenderGroup(const NameID& name)
{
    if (auto iter = mPasses.find(name); iter != mPasses.end())
        iter->second.mRenderGroup.reset();
}


void Material::EditorVisitProperty(PropertyAccesser func)
{
    auto const& names = GetFx()->GetExposedPropertyName();
    for (auto& e : names)
    {
        auto* prop = GetProperty(e);
        Assert(prop);
        func(e, *prop);
    }
}

std::string Material::EditorGetPropertyString()
{
    std::string str;
    GetFx()->VisitProperty([&str](const NameID& name, const PropertyType& data) {
        str += name.GetName();
        str.push_back(';');
    });
    if (str.size() > 0 && str.back() == ';')
        str.pop_back();
    return str;
}

std::string Material::EditorGetPassString()
{
    std::string str;
    for (auto& [name, _] : GetFx()->GetAllPass())
    {
        str += name.GetName();
        str.push_back(';');
    }
    if (str.size() > 0 && str.back() == ';')
        str.pop_back();

    return str;
}

bool Material::EditorGetBlendEnable(const NameID& passID)
{
    StateType stateType{
        GetDefaultBlendState(),
        GetDefaultDepthStencilState(),
        GetDefaultRasterizationState(),
        GetDefaultDynamicState(),
        2000,
    };
    if (auto iter = mPasses.find(passID); iter != mPasses.end() && iter->second.mState)
    {
        stateType = *iter->second.mState;
    }
    else
    {
        if (GetFx()->HasPass(passID))
        {
            stateType = GetFx()->GetPass(passID).mState;
        }
        else
        {
            return false;
        }
    }

    const auto& bs = stateType.BlendStateDesc;
    return bs.TargetBlendState[0].EnableBlend;
}

void Material::EditorSetRenderState(const NameID& passID, MaterialRenderState renderState)
{
    // const auto& passID = GetFx()->GetDefaultPass().name;

    switch (renderState)
    {
    case MaterialRenderState::Opacity:
    {
        SetDepthStencilState(passID, GetDefaultDepthStencilState());
        SetBlendState(passID, GetDefaultBlendState());
    }
    break;
    case MaterialRenderState::Transparent:
    {
        SetDepthStencilState(passID, GetTransparentDepthStencilState());
        SetBlendState(passID, GetTransparentBlendState());
        // SetRenderGroup(passID, 3000);
    }
    break;
    // TODO(aki): Additive
    case MaterialRenderState::Inherited:
    {
        if (auto iter = mPasses.find(passID); iter != mPasses.end())
        {
            iter->second.mState.reset();
        }
        SetToRender(passID, *GetState(passID));
    }
    break;
    default:
        break;
    }
}

bool Material::EditorIsPropertyOverrided(const NameID& propID)
{
    return mPropertyMap.find(propID) != mPropertyMap.end();
}
bool Material::EditorIsRenderGroupOverrided(const NameID& passID)
{
    if (auto iter = mPasses.find(passID); iter != mPasses.end() && iter->second.mRenderGroup)
        return true;
    else
        return false;
}

bool Material::EditorIsRenderStateOverrided(const NameID& passID)
{
    if (auto iter = mPasses.find(passID); iter != mPasses.end() && iter->second.mState)
        return true;
    else
        return false;
}

void Material::EditorNotitfySubMaterial()
{
    auto fileSys = EngineGlobal::GetFileSystem();
    gResourceConvertMgr.TraverseFileReDepend(
        fileSys->GetRelativePath(fileSys->GetAbsolutePath(GetName())),
        [fileSys](const std::string& redepend, const ResourceMetaHeader& header) {
            if (header.mClassID == ClassID(Material))
            {
                std::string absPath = fileSys->GetAbsolutePath(redepend);
                filesystem::MarkFileChanged(absPath);
                // gResourceMgr.RemoveResourceThumbnail(absPath);
            }
            return true;
        },
        true);
}

MaterialRenderState Material::EditorGetRenderState(const NameID& passID) const
{
    if (auto iter = mPasses.find(passID); iter != mPasses.end() && iter->second.mState)
    {
        const auto& state = *iter->second.mState;

        const auto& bs = state.BlendStateDesc;
        const auto& dss = state.DepthStencilStateDesc;
        const auto& rs = state.RasterizationStateDesc;
        const auto& ds = state.DynamicStateDesc;

        if (bs == GetDefaultBlendState() && dss == GetDefaultDepthStencilState() && rs == GetDefaultRasterizationState() && ds == GetDefaultDynamicState())
            return MaterialRenderState::Opacity;
        else if (bs == GetTransparentBlendState() && dss == GetTransparentDepthStencilState() && rs == GetDefaultRasterizationState() && ds == GetDefaultDynamicState())
            return MaterialRenderState::Transparent;
        // TODO(aki): additive
        return MaterialRenderState::Custom;
    }
    else
    {
        return MaterialRenderState::Inherited;
    }
}

void Material::AfterAssetDelete()
{
    for (auto childMat : mChildMaterial)
    {
        childMat->SetParent(GetDefault()->GetAsset()->GetName());
        if (childMat->GetAsset() != nullptr)
        {
            cross::SerializeNode emptyNode = {};
            childMat->Serialize(std::move(emptyNode), childMat->GetAsset()->GetName());
        }
    }
}

void Material::UnparentInternal()
{
    if (mBaseMaterial)
    {
        mBaseMaterial->RemoveChildMaterial(this);
        mBaseMaterial.reset();
    }

    if (mParameterCollectionSource)
    {
        mParameterCollectionSource->UnRegisterMaterialHolder(this);
        mParameterCollectionSource.reset();
    }
}

void Material::SetParentInternal(MaterialInterfacePtr parent)
{
    mBaseMaterial = parent;
    mBaseMaterial->AddChildMaterial(this);
    mVersion = parent->GetVersion();

    // Child material's mpc has data, treated as dirty, don't copy parent's.
    if (!mParameterCollectionSource || mParameterCollectionSource->IsEmpty())
    {
        if (const auto parentMtl = dynamic_cast<Material*>(parent.get()); parentMtl)
        {
            mParameterCollectionSource = parentMtl->GetMpcSource();
        }
    }
}

void Material::SetToRender(const NameID& propID, const PropertyType& propVal)
{
    if (mRenderMaterial == nullptr)
    {
        return;
    }

    if (IsLifeStageNormal())
    {
        std::visit(Overloaded{[&](const std::vector<float>& farr) { cross::threading::DispatchRenderingCommand([=] { mRenderMaterial->SetValueProp(propID, farr.data(), farr.size()); }); },
                              [&](const bool val) { cross::threading::DispatchRenderingCommand([=] { mRenderMaterial->SetBool(propID, val); }); },
                              [&](const TexturePtr& texture) { cross::threading::DispatchRenderingCommand([=] { mRenderMaterial->SetTexture(propID, texture->GetTextureR()); }); },
                              [&](const SamplerState& samplerState) { cross::threading::DispatchRenderingCommand([=] { mRenderMaterial->SetSamplerState(propID, samplerState); }); }},
                   propVal);
    }
}

void Material::SetToRender(const NameID& passID, const StateType& states)
{
    if (mRenderMaterial == nullptr)
    {
        return;
    }

    DispatchRenderingCommandWithToken([renderMaterial = this->mRenderMaterial.get(), key = passID, stateInfo = states] {
        auto& [blend, depth, raster, dynamic, _] = stateInfo;
        renderMaterial->SetBlendState(key, blend);
        renderMaterial->SetDepthStencilState(key, depth);
        renderMaterial->SetRasterizationState(key, raster);
        renderMaterial->SetDynamicState(key, dynamic);
    });
}

void Material::SetToRender(const NameID& passID, const UInt32 group)
{
    if (mRenderMaterial == nullptr)
    {
        return;
    }

    DispatchRenderingCommandWithToken([renderMaterial = this->mRenderMaterial.get(), key = passID, value = group] { renderMaterial->SetRenderGroup(key, (UInt16)value); });
}

void Material::SetToRender(const NameID& passID, const bool enable)
{
    if (mRenderMaterial == nullptr)
    {
        return;
    }

    DispatchRenderingCommandWithToken([renderMaterial = this->mRenderMaterial.get(), key = passID, value = enable] { renderMaterial->SetEnable(key, value); });
}

void Material::OnParentDirty(const NameID& propID, const PropertyType& propVal)
{
    // Not overrided in this material
    if (mPropertyMap.find(propID) == mPropertyMap.end())
    {
        SetToRender(propID, propVal);
        for (auto ch : mChildMaterial)
        {
            ch->OnParentDirty(propID, propVal);
        }
    }
}

void Material::OnParentDirty(const NameID& passID, const StateType& states)
{
    if (auto iter = mPasses.find(passID); iter == mPasses.end() || !iter->second.mState)
    {
        SetToRender(passID, states);
        for (auto ch : mChildMaterial)
        {
            ch->OnParentDirty(passID, states);
        }
    }
}

void Material::OnParentDirty(const NameID& passID, UInt32 group)
{
    if (auto iter = mPasses.find(passID); iter == mPasses.end() || !iter->second.mRenderGroup)
    {
        SetToRender(passID, group);
        for (auto ch : mChildMaterial)
        {
            ch->OnParentDirty(passID, group);
        }
    }
}

void Material::OnParentDirty(const NameID& passID, bool enable)
{
    if (auto iter = mPasses.find(passID); iter == mPasses.end() || !iter->second.mEnable.has_value())
    {
        SetToRender(passID, enable);
        for (auto ch : mChildMaterial)
        {
            ch->OnParentDirty(passID, enable);
        }
    }
}

bool Material::RefreshRenderData()
{
    auto fx = GetFx();

    if (mRenderMaterial == nullptr)
    {
        return false;
    }

    threading::DispatchRenderingCommand([=] { mRenderMaterial->Initialize(fx); });

    fx->VisitProperty([this](const NameID& propID, const PropertyType& propVal) { SetToRender(propID, *GetProperty(propID)); });

    for (auto const& [passID, _] : fx->GetAllPass())
    {
        SetToRender(passID, IsPassEnable(passID));
        SetToRender(passID, GetRenderGroup(passID));
        SetToRender(passID, *GetState(passID));
    }
    return true;
}

bool Material::PostDeserialize()
{
    // temporally guard, but why it is empty
    if (mRenderMaterial)
    {
        mRenderMaterial->SetName(GetName());
    }

    if (mParameterCollectionSource)
    {
        mParameterCollectionSource->RegisterMaterialHolder(this);
    }

    if (IsCreatedByMaterialEditor())
    {
        FillAllParameterCollectionValue();
    }

    if (mRenderMaterial)
    {
        RefreshRenderData();
        threading::DispatchRenderingCommand([=] { mRenderMaterial->CreateVTStack(); });
    }

    return true;
}

void Material::Refresh(bool resetResource)
{
    if (resetResource)
    {
        Resource::ResetResource();
    }

    if (mAsset)
    {
        mPropertyMap.clear();
        mPasses.clear();

        UnregistAllMpc();

        gResourceAssetMgr.LoadNDAResource(ResourcePtr(this));
    }
    else
    {
        RegistAllMpc();
    }

    PostDeserialize();
}

void Material::RefreshMaterialTree(bool resetResource, bool skipSelf)
{
    if (!skipSelf)
        Refresh(resetResource);

    auto temporal_cache = mChildMaterial;

    for (auto childmat : temporal_cache)
    {
        childmat->RefreshMaterialTree(resetResource);
    }
}

// hot reload

bool Material::ResetResource()
{
    Resource::ResetResource();
    RefreshMaterialTree(true);

    NotifyChange();

    return true;
}

void Material::CopyFrom(const Material& material)
{
    UnregistAllMpc();

    SetParent(material.mBaseMaterial);

    mPropertyMap = material.mPropertyMap;
    mPasses = material.mPasses;

    mParameterCollectionSource = material.mParameterCollectionSource;
    mParameterUsageView = material.mParameterUsageView;
    mParameterUsageMap = material.mParameterUsageMap;

    mDefines = material.mDefines;

    RegistAllMpc();

    RefreshRenderData();
}

bool Material::SetParent(const std::string& path)
{
    auto res = gAssetStreamingManager->LoadSynchronously(path);
    switch (res->GetClassID())
    {
    case ClassID(Fx):
    case ClassID(Material):
    {
        return SetParent(TypeCast<MaterialInterface>(res));
    }
    default:
        LOG_EDITOR_WARNING("Material Re-Parent error: Wrong file type (not material or fx).");
        return false;
    }
}

bool Material::SetParent(MaterialInterfacePtr parent)
{
    UnparentInternal();
    SetParentInternal(TypeCast<MaterialInterface>(parent));
    RefreshMaterialTree(false, false);
    mDefines.ParentMaterial = mBaseMaterial->GetGuid_Str();
    return true;
}

bool Material::SetParentAndSave(const std::string& path)
{
    auto res = gAssetStreamingManager->LoadSynchronously(path);
    switch (res->GetClassID())
    {
    case ClassID(Fx):
    case ClassID(Material):
    {
        return SetParentAndSave(TypeCast<MaterialInterface>(res));
    }
    default:
        LOG_EDITOR_WARNING("Material Re-Parent error: Wrong file type (not material or fx).");
        return false;
    }
}

bool Material::SetParentAndSave(MaterialInterfacePtr parent)
{
    if (parent->IsDependent(GetGuid_Str()))
    {
        LOG_EDITOR_ERROR("Cannot set {} as a parent as it is already a child of this material instance.", parent->GetName());
        return false;
    }
    UnparentInternal();
    SetParentInternal(TypeCast<MaterialInterface>(parent));
    Serialize(SerializeNode(), GetName());
    RefreshMaterialTree(false, false);
    mDefines.ParentMaterial = mBaseMaterial->GetGuid_Str();
    return true;
}

bool Material::IsDependent(const std::string& guid)
{
    if (GetGuid_Str() == guid)
    {
        return true;
    }
    return mBaseMaterial ? mBaseMaterial->IsDependent(guid) : false;
}

bool Material::HasAncestor(const std::string& name)
{
    if (GetName() == name)
        return true;
    auto nowMaterial = mBaseMaterial;
    while (nowMaterial)
    {
        if (nowMaterial->GetName() == name)
            return true;
        else
            nowMaterial = nowMaterial->GetParent();
    }
    return false;
}

void Material::RemoveProperty(const NameID& name)
{
    if (mPropertyMap.find(name) != mPropertyMap.end())
    {
        mPropertyMap.erase(name);
        if (auto* prop = GetProperty(name); prop)
            SetToRender(name, *prop);
    }
}

void Material::ClearAllProperties()
{
    mPropertyMap.clear();

    for (auto& pass : mPasses)
    {
        pass.second.mEnable = std::nullopt;
        pass.second.mRenderGroup = std::nullopt;
        pass.second.mState = std::nullopt;
    }

    RefreshRenderData();
}

MaterialInterfacePtr Material::CreateInstance()
{
    return Material::CreateMaterialInstance(TypeCast<resource::Material>(SharedFromThis()));
}

MaterialPtr Material::CreateTempInstance()
{
    return Material::CreateMaterialTempInstance(TypeCast<resource::Material>(SharedFromThis()));
}

void Material::UnlinkParent()
{
    // parent is not fx
    if (mBaseMaterial->GetParent())
    {
        mBaseMaterial->VisitProperty([&](NameID const& name, Material::PropertyType const& val) {
            if (mPropertyMap.find(name) == mPropertyMap.end())
            {
                mPropertyMap.emplace(name, val);
            }
        });

        for (auto const& [key, _] : GetFx()->GetAllPass())
        {
            InitStates(key);
        }

        auto fx = mBaseMaterial->GetFx();

        SetParent(TypeCast<MaterialInterface>(fx));
    }
}

void Material::SetBool(const NameID& name, bool value)
{
    auto itr = mPropertyMap.find(name);
    if (itr == mPropertyMap.end() || !std::holds_alternative<bool>(itr->second) || std::get<bool>(itr->second) != value)
    {
        mPropertyMap[name] = value;

        if (this->mRenderMaterial && IsLifeStageNormal())
        {
            ResourceManager::Instance().EnqueueMaterialRenderingCommand([name, value, materialR = this->mRenderMaterial] { materialR->SetBool(name, value); });
        }
        else
        {
            LOG_ERROR("Try to Set Mat {} {} with value {}", GetName(), name.GetName(), value);
        }

        for (auto* child : mChildMaterial)
        {
            child->OnParentDirty(name, static_cast<PropertyType>(value));
        }
    }
}

void Material::SetInt(const NameID& name, int value)
{
    SetFloat(name, static_cast<float>(value));
}

void Material::SetFloat(const NameID& name, float value)
{
    SetFloatArray(name, 1, &value);
}

void Material::SetFloat2(const NameID& name, const float value[2])
{
    SetFloatArray(name, 2, value);
}

void Material::SetFloat3(const NameID& name, const float value[3])
{
    SetFloatArray(name, 3, value);
}

void Material::SetFloat4(const NameID& name, const float value[4])
{
    SetFloatArray(name, 4, value);
}

void Material::SetFloatArray(const NameID& name, int length, const float* pValue /*= nullptr*/)
{
    Assert(length);
    std::vector vec = std::vector<float>(length);
    memcpy(vec.data(), pValue, length * sizeof(float));

    NameID remappedName;
    if (length == 1)
    {
        remappedName = GetFx()->GetParameterRemappedName(Fx::MaterialParamType::Scalar, name);
    }
    else
    {
        remappedName = GetFx()->GetParameterRemappedName(Fx::MaterialParamType::Vector, name);
    }

    mPropertyMap[remappedName] = vec;
    if (this->mRenderMaterial && IsLifeStageNormal())
    {
        ResourceManager::Instance().EnqueueMaterialRenderingCommand([remappedName, vec, materialR = this->mRenderMaterial] { materialR->SetValueProp(remappedName, vec.data(), vec.size()); });
    }
    else
    {
        LOG_ERROR("Try to Set Mat {} {} with length {}", GetName(), remappedName.GetName(), length);
    }

    for (auto* child : mChildMaterial)
    {
        child->OnParentDirty(remappedName, vec);
    }
}

void Material::SetTexture(const NameID& name, TexturePtr texture)
{
    Assert(texture);
    if (!texture)
        return;

    NameID remappedName = GetFx()->GetParameterRemappedName(Fx::MaterialParamType::Texture, name);

    auto createVTStack = false;
    if (auto* oldProp = GetProperty(remappedName); oldProp && std::holds_alternative<TexturePtr>(*oldProp))
    {
        if (std::get<TexturePtr>(*oldProp)->GetTextureInfo().EnableVirtualTextureStreaming || texture->GetTextureInfo().EnableVirtualTextureStreaming)
        {
            createVTStack = true;
        }
    }

    mPropertyMap[remappedName] = texture;

    ResourceManager::Instance().EnqueueMaterialRenderingCommand([=, textureR = (texture)->GetTextureR()] {
        mRenderMaterial->SetTexture(remappedName, textureR);
        if (createVTStack)
        {
            mRenderMaterial->CreateVTStack();
        }
    });

    for (auto* child : mChildMaterial)
    {
        child->OnParentDirty(remappedName, mPropertyMap[remappedName]);
    }
}

// Just for serialize material into nda file without render immediately
void Material::SetTextureProp(const NameID& name, TexturePtr texture)
{
    Assert(texture);
    if (!texture)
        return;

    NameID remappedName = GetFx()->GetParameterRemappedName(Fx::MaterialParamType::Texture, name);

    mPropertyMap[remappedName] = texture;
}

void Material::SetSamplerState(const NameID& name, const SamplerState& samplerState)
{
    auto [itr, ret] = mPropertyMap.emplace(name, samplerState);

    DispatchRenderingCommandWithToken([=] { mRenderMaterial->SetSamplerState(name, samplerState); });

    for (auto* child : mChildMaterial)
    {
        child->OnParentDirty(name, itr->second);
    }
}

void Material::SetProp(const NameID& name, const PropertyType& propVal)
{
    NameID remappedName = name;
    if (auto r = std::get_if<std::vector<float>>(&propVal); r)
    {
        if (r->size() == 1)
        {
            remappedName = GetFx()->GetParameterRemappedName(Fx::MaterialParamType::Scalar, name);
        }
        else if (r->size() == 4)
        {
            remappedName = GetFx()->GetParameterRemappedName(Fx::MaterialParamType::Vector, name);
        }
    }
    else if (auto r2 = std::get_if<TexturePtr>(&propVal); r)
    {
        remappedName = GetFx()->GetParameterRemappedName(Fx::MaterialParamType::Texture, name);
    }

    mPropertyMap[remappedName] = propVal;
}
Resource* Material::MaterialCreateMaterial(char const* shaderPath) 
{
    auto mtlPtr = cross::resource::Material::CreateMaterialInstance(shaderPath);
    //mtlPtr->IncreaseRefCount();
    return mtlPtr.get();
}
void Material::SetBlendState(const NameID& passID, const NGIBlendStateDesc& blendDesc)
{
    const auto& pID = passID.mPropertyID == 0 ? GetFx()->GetDefaultPass().name : passID;
    InitStates(pID);
    mPasses[pID].mState->BlendStateDesc = NGIBlendStateDescForEditor{blendDesc};

    DispatchRenderingCommandWithToken([passID, blendDesc, materialR = this->mRenderMaterial] { materialR->SetBlendState(passID, blendDesc); });

    for (auto* child : mChildMaterial)
    {
        child->OnParentDirty(passID, *mPasses[pID].mState);
    }
}

void Material::SetBlendStateProp(const NameID& passID, const NGIBlendStateDesc& blendDesc)
{
    const auto& pID = passID.mPropertyID == 0 ? GetFx()->GetDefaultPass().name : passID;
    InitStates(pID);
    mPasses[pID].mState->BlendStateDesc = NGIBlendStateDescForEditor{blendDesc};
}

void Material::SetDepthStencilState(const NameID& passID, const NGIDepthStencilStateDesc& depthStencilDesc)
{
    const auto& pID = passID.mPropertyID == 0 ? GetFx()->GetDefaultPass().name : passID;
    InitStates(pID);

    if (mPasses[pID].mState->DepthStencilStateDesc != depthStencilDesc)
    {
        mPasses[pID].mState->DepthStencilStateDesc = depthStencilDesc;
        ResourceManager::Instance().EnqueueMaterialRenderingCommand([passID, depthStencilDesc, materialR = this->mRenderMaterial] { materialR->SetDepthStencilState(passID, depthStencilDesc); }
            );

        for (auto* child : mChildMaterial)
        {
            child->OnParentDirty(passID, *mPasses[pID].mState);
        }
    }
}

void Material::SetDepthStencilStateProp(const NameID& passID, const NGIDepthStencilStateDesc& depthStencilDesc)
{
    const auto& pID = passID.mPropertyID == 0 ? GetFx()->GetDefaultPass().name : passID;
    InitStates(pID);
    mPasses[pID].mState->DepthStencilStateDesc = depthStencilDesc;
}

void Material::SetRasterizerState(const NameID& passID, const NGIRasterizationStateDesc& rasterizerState)
{
    const auto& pID = passID.mPropertyID == 0 ? GetFx()->GetDefaultPass().name : passID;
    InitStates(pID);
    mPasses[pID].mState->RasterizationStateDesc = rasterizerState;

    if (this->mRenderMaterial)
        DispatchRenderingCommandWithToken([passID, rasterizerState, materialR = this->mRenderMaterial] { materialR->SetRasterizationState(passID, rasterizerState); });

    for (auto* child : mChildMaterial)
    {
        child->OnParentDirty(passID, *mPasses[pID].mState);
    }
}

void Material::SetDynamicState(const NameID& passID, const NGIDynamicStateDesc& dynamicState)
{
    const auto& pID = passID.mPropertyID == 0 ? GetFx()->GetDefaultPass().name : passID;
    InitStates(pID);
    if (mPasses[pID].mState->DynamicStateDesc != dynamicState)
    {
        mPasses[pID].mState->DynamicStateDesc = dynamicState;

        if (this->mRenderMaterial)
        {
            ResourceManager::Instance().EnqueueMaterialRenderingCommand([passID, dynamicState, materialR = this->mRenderMaterial] { materialR->SetDynamicState(passID, dynamicState); }
                );
        }

        for (auto* child : mChildMaterial)
        {
            child->OnParentDirty(passID, *mPasses[pID].mState);
        }
    }
}

void Material::SetRenderGroup(NameID const& passID, UInt32 val)
{
    const auto& pID = passID.mPropertyID == 0 ? GetFx()->GetDefaultPass().name : passID;

    mPasses[pID].mRenderGroup = val;

    DispatchRenderingCommandWithToken([passID, val, materialR = this->mRenderMaterial] { materialR->SetRenderGroup(passID, (UInt16)val); });

    for (auto* child : mChildMaterial)
    {
        child->OnParentDirty(passID, val);
    }
}

void Material::SetRenderGroupProp(NameID const& passID, UInt32 val)
{
    const auto& pID = passID.mPropertyID == 0 ? GetFx()->GetDefaultPass().name : passID;

    mPasses[pID].mRenderGroup = val;
}

Material::~Material()
{
    UnparentInternal();
}

MaterialInterfacePtr Material::CreateMaterialInstance(const MaterialPtr& parentPtr)
{
    auto instance = gResourceMgr.CreateResourceAs<resource::Material>();
    instance->SetParent(parentPtr);
    return TypeCast<MaterialInterface>(instance);
}

MaterialInterfacePtr Material::CreateMaterialInstance(const FxPtr& fxPtr)
{
    auto instance = gResourceMgr.CreateResourceAs<resource::Material>();
    instance->SetParent(fxPtr);
    return TypeCast<MaterialInterface>(instance);
}

MaterialInterfacePtr Material::CreateMaterialInstance(const std::string& path)
{
    auto instance = gResourceMgr.CreateResourceAs<resource::Material>();
    instance->SetParent(path);
    return TypeCast<MaterialInterface>(instance);
}

MaterialPtr Material::CreateMaterialTempInstance(const MaterialPtr& parentPtr)
{
    auto instance = gResourceMgr.CreateTempResourceAs<resource::Material>();
    instance->SetParent(parentPtr);
    return instance;
}

MaterialPtr Material::CreateMaterialTempInstance(const FxPtr& fxPtr)
{
    auto instance = gResourceMgr.CreateTempResourceAs<resource::Material>();
    instance->SetParent(fxPtr);
    return instance;
}

void Material::PostDestroy()
{
    MaterialInterface::PostDestroy();
    UnregistAllMpc();
    UnparentInternal();
}

void Material::DestroyRenderData(FrameParam* frameParam)
{
    DispatchRenderingCommandWithToken([this] { mRenderMaterial.reset(); });
}

void Material::NotifyChange()
{
    if (mRenderMaterial == nullptr)
    {
        return;
    }
    threading::DispatchRenderingCommand([=] { mRenderMaterial->NotifyChange(); });
}

void Material::NotifyChangeRecursively()
{
    NotifyChange();
    for (auto& child : mChildMaterial)
    {
        child->NotifyChangeRecursively();
    }
}

void Material::SetMpcSource(MPCPtr source)
{
    mParameterCollectionSource = source;
}

const MPCPtr Material::GetMpcSource() const
{
    return mParameterCollectionSource;
}

void Material::AddParameterUsage(const std::string& item)
{
    if (item.empty() || !mParameterCollectionSource)
    {
        return;
    }
    mParameterUsageView.Add(item);
    mParameterUsageMap.try_emplace(NameID(item), mParameterCollectionSource->GetParameterValue(item));
}

void Material::DeleteParameterUsage(UInt32 index)
{
    if (auto iter = mParameterUsageMap.find(mParameterUsageView[index]); iter != mParameterUsageMap.end())
    {
        mParameterUsageMap.erase(iter->first);
        mParameterUsageView.Delete(index);
    }
    else
    {
        AssertMsg(false, "Can't find usage parameter [{}]", index);
    }
}

void Material::ClearParameterUsage()
{
    mParameterUsageView.Clear();
    mParameterUsageMap.clear();
}

void Material::RefreshParameterUsage(UsageChangedType type, const char* name, SInt32 index)
{
    if (!mParameterCollectionSource)
    {
        return;
    }
    switch (type)
    {
    case cross::resource::UsageChangedType::Set:
    {
        const auto& newValue = mParameterCollectionSource->GetParameterValue(name);
        if (mParameterUsageMap.find(name) != mParameterUsageMap.end())
        {
            if (auto newValuePtr = std::get_if<std::vector<float>>(&newValue))
            {
                SetFloatArray(name, static_cast<int>(newValuePtr->size()), newValuePtr->data());
            }
            if (auto newBoolValue = std::get_if<bool>(&newValue))
            {
                SetBool(name, *newBoolValue);
            }
            mParameterUsageMap[name] = newValue;
            RefreshRenderData();
        }
    }
    break;
    case cross::resource::UsageChangedType::Add:
        AddParameterUsage(name);
        break;
    case cross::resource::UsageChangedType::Delete:
        DeleteParameterUsage(index);
        RefreshRenderData();
        break;
    case cross::resource::UsageChangedType::Clear:
        ClearParameterUsage();
        RefreshRenderData();
        break;
    default:
        break;
    }
}

void Material::SetTexture(const NameID& name, const char* texturepath) 
{
    SetTexture(name, cross::TypeCast<cross::resource::Texture>(gAssetStreamingManager->LoadSynchronously(texturepath)));
}
int Material::GetPropertyType(const NameID& name)
{
    int type = 0;
    if (auto* prop = GetProperty(name); prop)
    {
        type = 0;
        if (auto* vec = std::get_if<std::vector<float>>(prop); vec)
        {
            if (vec->size() <= 4)
                type = static_cast<int>(vec->size());
        }

        else if (auto* bval = std::get_if<bool>(prop); bval)
        {
            type = 5;
        }

        else if (auto* tex = std::get_if<cross::TexturePtr>(prop); tex)
        {
            type = 6;
        }
    }
    return type;
}

float Material::GetPropertyFloat(const NameID& name)
{
    float data = 0;
    if (auto* prop = GetProperty(name); prop)
    {
        if (auto* vec = std::get_if<std::vector<float>>(prop); vec)
        {
            if (vec->size() == 1)
            {
                data = vec->front();
            }
        }
    }
    return data;
}

cross::Float2 Material::GetPropertyFloat2(const NameID& name)
{
    cross::Float2 data;
    if (auto* prop = GetProperty(name); prop)
    {
        if (auto* vec = std::get_if<std::vector<float>>(prop); vec)
        {
            if (vec->size() == 2)
            {
                memcpy(reinterpret_cast<void*>(data.data()), vec->data(), sizeof(float) * vec->size());
            }
        }
    }
    return data;
}

cross::Float3 Material::GetPropertyFloat3(const NameID& name)
{
    cross::Float3 data;
    if (auto* prop = GetProperty(name); prop)
    {
        if (auto* vec = std::get_if<std::vector<float>>(prop); vec)
        {
            if (vec->size() == 3)
            {
                memcpy(reinterpret_cast<void*>(data.data()), vec->data(), sizeof(float) * vec->size());
            }
        }
    }
    return data;
}

cross::Float4 Material::GetPropertyFloat4(const NameID& name)
{
    cross::Float4 data;
    if (auto* prop = GetProperty(name); prop)
    {
        if (auto* vec = std::get_if<std::vector<float>>(prop); vec)
        {
            if (vec->size() == 4)
            {
                memcpy(reinterpret_cast<void*>(data.data()), vec->data(), sizeof(float) * vec->size());
            }
        }
    }
    return data;
}
std::string Material::GetPropertyString(const NameID& name)
{
    if (auto* prop = GetProperty(name); prop)
    {
        if (auto* tex = std::get_if<cross::TexturePtr>(prop); tex)
        {
            return (*tex)->GetName();
        }
    }
    return "";
}
bool Material::GetPropertyBool(const NameID& name)
{

    if (auto* prop = GetProperty(name); prop)
    {
        if (auto* bval = std::get_if<bool>(prop); bval)
        {
            return *bval;
        }
    }
    return false;
}
int Material::GetPropertyOverrided(const NameID& name)
{
    bool overrided = EditorIsPropertyOverrided(name);
    return overrided;
}
int Material::GetRenderState(const NameID& name)
{
    return static_cast<int>(EditorGetRenderState(name));
}
void Material::EditorSetMPCResource(const char* mpcPath) 
{
    SetMpcSource(cross::TypeCast<cross::resource::MaterialParameterCollection>(gAssetStreamingManager->LoadSynchronously(mpcPath)));
}
void Material::EditorSetFloat2(const NameID& name, Float2 value) 
{
    SetFloatArray(name, 2, value.data());
}
void Material::EditorSetFloat3(const NameID& name, Float3 value)
{
    SetFloatArray(name, 3, value.data());
}
void Material::EditorSetFloat4(const NameID& name, Float4 value)
{
    SetFloatArray(name, 4, value.data());
}
std::string Material::EditorGetParentPath()
{
    std::string path = GetParent() ? GetParent()->GetName() : GetFx()->GetName();
    return path;
}

bool Material::EditorGetIsOverrideMat()
{
    return GetParent() != nullptr; 
}

std::string Material::EditorGetFxPath()
{
    std::string path = GetFx()->GetName();
    return path;
}

std::string Material::EditorGetParameterCollectionPath()
{
    auto mpcSource = GetMpcSource();
    std::string path = mpcSource ? mpcSource->GetName() : "EngineResource/Material/DefaultEmpty.mpc";
    return path;
}

}   // namespace cross::resource
