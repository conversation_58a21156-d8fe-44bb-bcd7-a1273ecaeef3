#include "EnginePrefix.h"
#include "MaterialParameterCollection.h"
#include "Resource/Material.h"

namespace cross::resource {

bool MaterialParameterCollection::Serialize(SerializeNode&& s, const std::string& path)
{
    if (!Resource::HasAsset())
    {
        CreateAsset(path);
    }
    Resource::ClearReference();

    SerializeContext context;
    s["Material"] = mParameterInfo.Serialize(context);
    return Resource::Serialize(std::move(s), path);
}

bool MaterialParameterCollection::Deserialize(const DeserializeNode& s)
{
    if (s.HasMember("Material"))
    {
        SerializeContext context;
        mParameterInfo.Deserialize(s["Material"], context); 
    }
    return true;
}
void MaterialParameterCollection::MPC_SetScalerParameter(cross::resource::MaterialParameterCollection* mpc, int index, const char* name, float value, bool usage, bool asbool)
{
    mpc->SetScalerParameter(index, std::string(name), value, usage, asbool);
}

void MaterialParameterCollection::MPC_AddScalerParameter(cross::resource::MaterialParameterCollection* mpc, const char* name, float value) 
{
    mpc->AddScalerParameter(std::string(name), value);
}

void MaterialParameterCollection::MPC_DeleteScalerParameter(cross::resource::MaterialParameterCollection* mpc, int index)
{
    mpc->DeleteScalerParameter(index);
}

void MaterialParameterCollection::MPC_ClearScalerParameter(cross::resource::MaterialParameterCollection* mpc)
{
    mpc->ClearScalerParameter();
}

void MaterialParameterCollection::MPC_SetVectorParameter(cross::resource::MaterialParameterCollection* mpc, int index, const char* name, cross::Float4* value)
{
    mpc->SetVectorParameter(index, name, *value);
}

void MaterialParameterCollection::MPC_SetVectorParameterAutoUsage(cross::resource::MaterialParameterCollection* mpc, int index, bool usage)
{
    mpc->SetVectorParameterAutoUsage(index, usage);
}
MaterialParameterCollection* MaterialParameterCollection::MPC_CreateMaterialParameterCollection() 
{
    auto mpcPtr = gResourceMgr.CreateResourceAs<cross::resource::MaterialParameterCollection>();
    //mpcPtr->IncreaseRefCount();
    return mpcPtr.get();
}
void MaterialParameterCollection::MPC_AddVectorParameter(cross::resource::MaterialParameterCollection* mpc, const char* name, cross::Float4* value)
    {
    mpc->AddVectorParameter(name, *value);
}

void MaterialParameterCollection::MPC_DeleteVectorParameter(cross::resource::MaterialParameterCollection* mpc, int index)
{
    mpc->DeleteVectorParameter(index);
}

void MaterialParameterCollection::MPC_ClearVectorParameter(cross::resource::MaterialParameterCollection* mpc)
{
    mpc->ClearVectorParameter();
}


void MaterialParameterCollection::SetScalerParameter(SInt32 index, const PropertyName& name, float value, bool usage, bool asbool)
{
    if (mParameterInfo.ScalerParameters.empty())
    {
        return;
    }
    auto& parameter = mParameterInfo.ScalerParameters[index];
    std::visit(
        [this, &parameter, &value, &usage, &asbool](const auto& name) {
            if (!name.data())
                return;
            parameter.SetName(name.data());
            parameter.ParameterValue = value;
            parameter.AutoUsage = usage;
            parameter.AsBool = asbool;
            FlushMaterials(UsageChangedType::Set, name.data());
        },
        name);
}

void MaterialParameterCollection::AddScalerParameter(const PropertyName& name, float value)
{
    std::visit([this, &value](const auto& name) { mParameterInfo.ScalerParameters.emplace_back(name.data(), value); }, name);
}

void MaterialParameterCollection::DeleteScalerParameter(SInt32 index)
{
    auto& parameters = mParameterInfo.ScalerParameters;
    Assert(parameters.size() > index);
    parameters.erase(parameters.begin() + index);
    FlushMaterials(UsageChangedType::Delete, nullptr, index);
}

void MaterialParameterCollection::ClearScalerParameter()
{
    mParameterInfo.ScalerParameters.clear();
    FlushMaterials(UsageChangedType::Clear);
}

void MaterialParameterCollection::SetVectorParameter(SInt32 index, const std::string& name, Float4 value)
{
    if (mParameterInfo.VectorParameters.empty())
    {
        return;
    }
    auto& parameter = mParameterInfo.VectorParameters[index];
    parameter.ParameterName = name;
    parameter.ParameterValue = value;
    FlushMaterials(UsageChangedType::Set, name.c_str());
}

void MaterialParameterCollection::AddVectorParameter(const std::string& name, Float4 value)
{
    mParameterInfo.VectorParameters.emplace_back(name, value);
}

void MaterialParameterCollection::DeleteVectorParameter(SInt32 index)
{
    auto& parameters = mParameterInfo.VectorParameters;
    Assert(parameters.size() > index);
    parameters.erase(parameters.begin() + index);
    FlushMaterials(UsageChangedType::Delete, nullptr, index);
}

void MaterialParameterCollection::ClearVectorParameter()
{
    mParameterInfo.VectorParameters.clear();
    FlushMaterials(UsageChangedType::Clear);
}

void MaterialParameterCollection::SetVectorParameterAutoUsage(SInt32 index, bool usage)
{
    if (mParameterInfo.VectorParameters.empty())
    {
        return;
    }
    auto& parameter = mParameterInfo.VectorParameters[index];
    parameter.AutoUsage = usage;
}

MaterialParameterCollection::PropertyType MaterialParameterCollection::GetParameterValue(const NameID& name)
{
    std::vector<float> value;
    if (ScalerParameter* scalerPtr = GetParameter<ScalerParameter>(name))
    {
        if (scalerPtr->AsBool)
        {
            return static_cast<bool>(scalerPtr->ParameterValue != 0.0f);
        }
        else
        {
            value.emplace_back(scalerPtr->ParameterValue);
        }
    }
    else if (VectorParameter* vectorPtr = GetParameter<VectorParameter>(name))
    {
        value.assign(vectorPtr->ParameterValue.data(), vectorPtr->ParameterValue.data() + 4);
    }
    return value;
}

std::vector<float> MaterialParameterCollection::GetNumericParameterValue(const NameID& name)
{
    std::vector<float> value;
    if (ScalerParameter* scalerPtr = GetParameter<ScalerParameter>(name))
    {
        value.emplace_back(scalerPtr->ParameterValue);
    }
    else if (VectorParameter* vectorPtr = GetParameter<VectorParameter>(name))
    {
        value.assign(vectorPtr->ParameterValue.data(), vectorPtr->ParameterValue.data() + 4);
    }
    return value;
}

void MaterialParameterCollection::RegisterMaterialHolder(Material* mat)
{
    std::scoped_lock lock(mMaterialHolderMutex);
    mMaterialHolders.emplace(mat);

    for (const ScalerParameter& scalerParam : mParameterInfo.ScalerParameters)
    {
        mat->VisitProperty([&](NameID const& name, Material::PropertyType const& val) {
            if (scalerParam.ParameterName == name && scalerParam.AutoUsage)
            {
                mat->AddParameterUsage(name.GetName());
            }
        });
    }

    for (const VectorParameter& vectorParam : mParameterInfo.VectorParameters)
    {
        mat->VisitProperty([&](NameID const& name, Material::PropertyType const& val) {
            if (vectorParam.ParameterName == name && vectorParam.AutoUsage)
            {
                mat->AddParameterUsage(name.GetName());
            }
        });
    }
}

void MaterialParameterCollection::RegisterMaterialInterfaceHolder(MaterialInterfacePtr mtl)
{
    std::scoped_lock lock(mFxHolderMutex);
    mMaterialInterfaceHolder.emplace(mtl);
}

void MaterialParameterCollection::UnRegisterMaterialInterfaceHolder(MaterialInterfacePtr mtl)
{
    std::scoped_lock lock(mFxHolderMutex);
    mMaterialInterfaceHolder.erase(mtl);
}

void MaterialParameterCollection::UnRegisterMaterialHolder(Material* mat)
{
    std::scoped_lock lock(mMaterialHolderMutex);
    mMaterialHolders.erase(mat);
    mat->ClearParameterUsage();
}

void MaterialParameterCollection::FlushMaterials(UsageChangedType type, const char* name, SInt32 index)
{
    QUICK_SCOPED_CPU_TIMING("FlushMaterials");

    {
        std::scoped_lock lock(mMaterialHolderMutex);
        threading::ParallelFor(mMaterialHolders.begin(), mMaterialHolders.end(), [&](auto itr) { (*itr)->RefreshParameterUsage(type, name, index); });
    }
    {
        std::scoped_lock lock(mFxHolderMutex);
        // material blueprint
        // is this safe for parallel?
        // It will cost ~10ms / ~140 materials
        for (auto it = mMaterialInterfaceHolder.begin(); it != mMaterialInterfaceHolder.end();)
        {
            QUICK_SCOPED_CPU_TIMING("mMaterialInterfaceHolder");
            auto mtl = it->lock();
            if (mtl)
            {
                mtl->FillParameterCollectionValue(MPCPtr(const_cast<resource::MaterialParameterCollection*>(this)));
                mtl->RefreshRenderData();
                it++;
            }
            else [[unlikely]]
            {
                LOG_ERROR("Invalid MaterialInterfaceHolder!");
                it = mMaterialInterfaceHolder.erase(it);
                Assert(false);
            }
        }
    }
}

uint32_t MaterialParameterCollectionUsageInfo::AddParameterCollectionParameter(std::string_view collectionPath, std::string_view parameterName)
{
    for (uint32_t i = 0; i < m_ParameterCollections.size(); i++)
    {
        auto& collection = m_ParameterCollections[i];
        if (collection.mPath == collectionPath)
        {
            if (!collection.mUsedParameters.contains(std::string(parameterName)))
            {
                collection.mUsedParameters.insert(std::string(parameterName));
            }
            return i;
        }
    }
    MaterialParameterCollectionUsage collection = MaterialParameterCollectionUsage{collectionPath};
    collection.mUsedParameters.insert(std::string(parameterName));

    m_ParameterCollections.emplace_back(std::move(collection));
    return static_cast<uint32_t>(m_ParameterCollections.size() - 1);
}

}   // namespace cross::resource