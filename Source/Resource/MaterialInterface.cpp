#include "MaterialInterface.h"
#include "Fx.h"

namespace cross::resource {

const Shader::ProtoType* MaterialInterface::GetPrimitiveProtoType(MaterialUsage usage) const
{
    const Shader::ProtoType* type = nullptr;

    for (const auto& [name, pass] : GetFx()->GetAllPass())
    {
        if (auto* passType = pass.mShaderPtr->GetProtoTypeContainer().GetPrimitiveProtoType(usage); passType)
        {
            if (!type)
            {
                type = passType;
            }
            else if (passType->ID != type->ID)
            {
                return nullptr;
            }
        }
    }

    return type;
}

const Shader::ProtoType* MaterialInterface::GetObjectProtoType(MaterialUsage usage) const
{
    const Shader::ProtoType* type = nullptr;

    for (const auto& [name, pass] : GetFx()->GetAllPass())
    {
        if (auto* passType = pass.mShaderPtr->GetProtoTypeContainer().GetObjectProtoType(usage); passType)
        {
            if (!type)
            {
                type = passType;
            }
            else if (passType->ID != type->ID)
            {
                return nullptr;
            }
        }
    }

    return type;
}

const Shader::ProtoType* MaterialInterface::GetMaterialProtoType() const
{
    const Shader::ProtoType* type = nullptr;

    for (const auto& [name, pass] : GetFx()->GetAllPass())
    {
        if (auto* passType = pass.mShaderPtr->GetProtoTypeContainer().GetMaterialProtoType(); passType)
        {
            if (!type)
            {
                type = passType;
            }
            else if (passType->ID != type->ID)
            {
                return nullptr;
            }
        }
    }

    return type;
}

void MaterialInterface::AddChildMaterial(MaterialInterface* child)
{
    std::scoped_lock lock(mChildMaterialMutex);
    mChildMaterial.insert(child);
}

void MaterialInterface::RemoveChildMaterial(MaterialInterface* child)
{
    std::scoped_lock lock(mChildMaterialMutex);
    mChildMaterial.erase(child);
}
MaterialInterface::MaterialInterface()    
{
    if (EngineGlobal::Inst().GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeHeadless)
    {
        mRenderMaterial = gResourceMgr.mCreateRenderObjectMgr->GetMaterialR(this);
    }
}
}   // namespace cross::resource
