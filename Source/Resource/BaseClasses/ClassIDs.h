#pragma once

#define ClassID(x)                CLASS_##x
#define	DefineClassID(x,classID)  ClassID(x) = classID,

// Runtime classIDs are kept intentionally small.

enum CEMeta(Editor, Script) ClassIDType : int
{
    DefineClassID(NullType, 0)
    DefineClassID(Object, 1)                        // abstract - base object
    DefineClassID(Named, 2)                     // abstract - object that has a name
    kSmallestResourceTypeID = 3,
            DefineClassID(Resource, 3)              // abstract - basic resource
                DefineClassID(SubResource, 4)       // abstract - produce by resource
                DefineClassID(Material, 5)
                DefineClassID(Texture, 6)
                    DefineClassID(Texture2D, 7)
                    DefineClassID(TextureCube, 15)	
                    DefineClassID(Texture3D, 18)
                    DefineClassID(Texture2DArray, 20) 
                DefineClassID(TextureVirtual, 28)   // abstract - include Texture2DVirtual and TextureUDIM
                    DefineClassID(TextureUDIM, 8) 
                    DefineClassID(Texture2DVirtual, 27)
                DefineClassID(<PERSON><PERSON>, 10)
                DefineClassID(MaterialParameterCollection, 11)
                DefineClassID(MaterialFunction, 9)

                DefineClassID(NavMeshAsset, 17)
                DefineClassID(ScriptResource, 19)

                DefineClassID(MeshAssetDataResource, 23)
                DefineClassID(Fx, 24)
                DefineClassID(PrefabResource, 25)
                DefineClassID(WorldBlock, 26)

                DefineClassID(SkeletonResource, 30)
                DefineClassID(AnimSequenceRes, 33)
                DefineClassID(AnimCompositeRes, 34)
                DefineClassID(AnimatrixRes, 35)
                DefineClassID(AnimatorRes, 36)
                DefineClassID(AnimBlendSpaceRes, 37)
                DefineClassID(SkeletonPhysicsResource, 38)
                DefineClassID(MotionDataAsset, 39)

                DefineClassID(ComputeShader, 40)
                DefineClassID(RED, 41)

                DefineClassID(ParticleSystemResource, 50)
                DefineClassID(ParticleEmitterResource, 51)

                DefineClassID(PCGResource, 61) 
                DefineClassID(BinaryResource, 63)

                DefineClassID(WorkflowGraphResource, 64)
                DefineClassID(InstanceDataResource, 65)
                DefineClassID(InputActionMappingResource, 66)
                DefineClassID(DataAssetResource, 67)
                DefineClassID(HLODResource, 68)
                DefineClassID(RayTracingShader, 69)

#if AIRPORT_SCENE_EDITOR
                DefineClassID(AirportResource, 70)
#endif

                DefineClassID(CurveControllerRes, 101)
                DefineClassID(TerrainResource, 102)
                DefineClassID(FontResource, 103)
                DefineClassID(RenderTextureResource, 104) 
                DefineClassID(TsResource, 105)
    kLargestResourceTypeID = 999,
    kSmallestRuntimeClassID = 1000,
            DefineClassID(Window, 1000)
            DefineClassID(World, 1001) 

    kLargestRuntimeClassID,
    kSmallestEditorClassID = 10000,
    DefineClassID(ResourceReference, 10001)
};

//make sure people dont accidentally define classids in other files:
#undef DefineClassID
