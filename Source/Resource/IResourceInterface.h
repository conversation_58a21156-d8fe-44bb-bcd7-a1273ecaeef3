#pragma once

#include "CrossBase/String/NameID.h"
#include "NativeGraphicsInterface/NGI.h"
#include "CECommon/Common/TextureDefines.h"
#include "Resource/IComputerShaderR.h"
#include "Resource/IRayTracingShaderR.h"
#include "Resource/resourceforward.h"
#include "Resource/Resource.h"
#include "Resource/MaterialDefines.h"
#include "ECS/Develop/Framework/Types.h"

struct TextureInfo
{
    TextureDimension Dimension;
    TextureFormat Format;
    cross::ColorSpace ColorSpace;
    UInt32 Width, Height, Depth;
    UInt32 MipCount;
    UInt32 ArraySize;
    UInt32 MinMips;
    UInt32 MaxMips;
    UInt32 RequestedMips;
    UInt8 MipBias;
    bool EnableStreaming = false;
    bool EnableVirtualTextureStreaming = false;
    bool UDIM = false;
    bool Proxy = false;
};

class TextureDataProvider;


namespace cross
{
    namespace resource
    {
        struct UDIM_Image
        {
            UInt32 x;
            UInt32 y;
            std::shared_ptr<TextureDataProvider> mTextureResourceProvider;
        };

        struct UDIMs
        {
            std::map<std::pair<UInt32, UInt32>, UDIM_Image> mImages;
            std::shared_ptr<TextureDataProvider> mips;
        };
    }
}


class Resource_API TextureDataProvider
{
public:
    virtual ~TextureDataProvider(){};

    virtual TextureInfo GetTextureInfo() const = 0;
    virtual TextureFormat GetTextureFormat() const = 0;

    virtual UInt32 GetImageCount() const = 0;
    virtual UInt32 GetImageIndex(UInt32 mipLevel, UInt32 faceIndex, UInt32 arrayIndex) const = 0;

    virtual void GetImageLevels(UInt32 imageIndex, UInt32& mipLevel, UInt32& faceIndex, UInt32& arrayIndex) const = 0;
    virtual void GetImageSize(UInt32 imageIndex, UInt32& wide, UInt32& high, UInt32& depth) const = 0;
    virtual const UInt8* GetImageData(UInt32 imageIndex) const = 0;
    virtual UInt8* GetImageData(UInt32 imageIndex) = 0;
    virtual UInt32 GetImageDataByteSize(UInt32 imageIndex) const = 0;
    virtual UInt32 GetImagePitchByteSize(UInt32 imageIndex) const = 0;
    virtual const std::string& GetPath() const = 0;

    virtual void OnDataUploaded() = 0;
};

namespace cross
{
    class IVertexStreamLayoutParameter;
    struct NGITexture;
    struct NGITextureView;
    class MeshAssetData;
    namespace resource
    {
        class Fx;

        struct CEMeta(Cli) RenderTextureInfo
        {
            CEMeta(Editor, Serializ, Cli) std::string Name;
            CEMeta(Editor, Serialize, Cli) TextureDimension Dimension = TextureDimension::Tex2D;
            CEMeta(Editor, Serialize, Cli) RenderTextureFormat Format = RenderTextureFormat::R8G8B8A8_UNorm;
            CEMeta(Editor, Serialize, Cli) UInt32 Width = 256;
            CEMeta(Editor, Serialize, Cli) UInt32 Height = 256;
            CEMeta(Editor, Serialize, Cli) UInt32 Depth = 1;
            CEMeta(Editor, Serialize, Cli) UInt32 MipCount = 1;
            CEMeta(Editor, Serialize, Cli) UInt32 LayerCount = 1;
            CEMeta(Editor, Serialize, Cli) UInt32 SampleCount = 1;
            CE_Serialize_Deserialize;
        };
    }

    enum class CEMeta(Editor) GPUTextureType : UInt8
    {
        Normal = 0,
        VT,
        MVT,
        UDIM,
        RenderTexture,
        MaxCount
    };

    class Resource_API IGPUTexture
    {
    public:
        IGPUTexture() 
        {
        };

        virtual ~IGPUTexture()
        {
        }

        virtual cross::NGITexture* GetNGITexture()
        {
            Assert(0);
            return nullptr;
        }

        virtual cross::NGITextureView* GetNGITextureView()
        {
            Assert(0);
            return nullptr;
        }

        virtual void Initialize(const TextureInfo& info, const char* pDebugName = "")
        {
            Assert(0);
        }

        virtual void Initialize(std::shared_ptr<TextureDataProvider> data) 
        {
            Assert(0);
        }

        virtual void Initialize(const std::vector<std::shared_ptr<TextureDataProvider>>& datas, bool enableMerge = false)
        {
            Assert(0);
        }

        virtual void Initialize(const cross::resource::UDIMs& udims)
        {
            Assert(0);
        }

        virtual void Initialize(const TextureInfo& info, const std::vector<IGPUTexture*>& textures, const char* pDebugName = "")
        {
            Assert(0);
        }

        virtual bool UploadImage(UInt32 arrayIndex, UInt32 faceIndex, UInt32 mipIndex, UInt8* data, UInt32 dataSize, UInt32 rowPitch = 0, UInt32 slicePitch = 0) 
        {
            Assert(0);
            return false;
        }

        virtual bool UploadImageRect(UInt32 arrayIndex, UInt32 faceIndex, UInt32 mipIndex, const cross::Rect3u& uploadRect, UInt8* data, UInt32 dataSize, UInt32 rowPitch = 0, UInt32 slicePitch = 0)
        {
            Assert(0);
            return false;
        }

        virtual cross::StreamingScratchBuffer::Range PrepareStreamingData(const TextureDataProvider* dataProvider, std::vector<cross::NGICopyBufferTexture>& updates)
        {
            Assert(0);
            return {};
        }

        virtual void FinalizeStreamingData(const TextureDataProvider* dataProvider, const std::vector<cross::NGICopyBufferTexture>& updates)
        {
            Assert(0);
        }

        virtual GPUTextureType GetType() { return GPUTextureType::Normal; }
    };

class Resource_API IMaterialR
{
    friend class resource::Fx;
    friend class resource::Material;

protected:
    virtual UInt8 SetValueProp(NameID const& name, const float* val, size_t len) = 0;

    virtual UInt8 SetValueProp(NameID const& name, const UInt8* val, size_t len) = 0;

    virtual UInt8 SetBool(NameID const& name, bool val) = 0;

public:
    IMaterialR() {}

    virtual ~IMaterialR() = default;

    virtual void SetTexture(NameID const& name, cross::IGPUTexture* texture) = 0;

    virtual void SetSamplerState(NameID const& name, const SamplerState& samplerState) = 0;

    virtual void SetEnable(NameID const& passID, bool val) = 0;

    virtual void SetBlendState(NameID const& passID, const NGIBlendStateDesc& blendDesc) = 0;

    virtual void SetDepthStencilState(NameID const& passID, const NGIDepthStencilStateDesc& depthStencilState) = 0;

    virtual void SetRasterizationState(NameID const& passID, const NGIRasterizationStateDesc& rasterizerState) = 0;

    virtual void SetDynamicState(NameID const& passID, const NGIDynamicStateDesc& dynamicState) = 0;

    virtual void SetRenderGroup(NameID const& passID, UInt16 val) = 0;

    virtual void NotifyChange() = 0;

    virtual void Initialize(FxPtr fx) = 0;

    virtual void SetName(const std::string& name) = 0;

    virtual const std::string GetName() const = 0;

    virtual void CreateVTStack() = 0;

    virtual bool IsCached() = 0;
    virtual void UpdateCache() = 0;
    //virtual int VarientsByteSize() = 0;

    virtual UInt32 GetMaterialIndex() = 0;

    virtual void SetMaterialIndex(UInt32 Index) = 0;
};

class Resource_API IMeshR
{
public:
    IMeshR() = default;
    IMeshR(IMeshR const&) = delete;
    IMeshR(IMeshR&&) = delete;
    IMeshR& operator=(IMeshR const&) = delete;
    IMeshR& operator=(IMeshR&&) = delete;
    virtual ~IMeshR() = default;

    virtual void SetMeshIndex(UInt32 index) = 0;
    virtual UInt32 GetMeshIndex() = 0;

    // IMeshR got the ref of MeshAssetData when constructed. IMeshR's data will always be built by the ref.
    virtual void BuildStaticMesh(IVertexStreamLayoutParameter* meshParameter = nullptr) = 0;
};

class Resource_API IStreamableResource
{
public:
    IStreamableResource() = default;
    virtual ~IStreamableResource() = default;

    virtual int GetStreamingIndex() const = 0;
};

class IGPUTexture;
class IMaterialR;
struct NGIPipelineLayout;

class ICreateRenderObjectMgr
{
public:
    virtual std::unique_ptr<cross::IGPUTexture> GetGpuTex(const TextureInfo& textureInfo) = 0;
    virtual std::shared_ptr<IMaterialR> GetMaterialR(resource::MaterialInterface* material) = 0;
    virtual std::unique_ptr<IMeshR> GetMeshR(MeshAssetData* meshAssetData) = 0;
    virtual std::shared_ptr<IStreamableResource> GetStreamable(const ResourcePtr& resource) = 0;
    virtual NGIResourceGroupLayout* AllocateResourceGroupLayout(const NGIResourceGroupLayoutDesc& desc) = 0;
    virtual NGIPipelineLayout* AllocatePipelineLayout(const NGIPipelineLayoutDesc& desc) = 0;
    virtual IComputeShaderR* GetComputeShaderR(resource::ComputeShader*) = 0;
    virtual IRayTracingShaderR* GetRayTracingShaderR(resource::RayTracingShader*) = 0;
    virtual std::unique_ptr<cross::IGPUTexture> GetGpuRenderTex(const resource::RenderTextureInfo& info) = 0;

    // ugly...
    virtual void NotifyInstanceDataResourceChange(const resource::InstanceDataResource* resource) = 0;
};

class ITypeScriptHotReloadMgr
{
public:
    virtual ~ITypeScriptHotReloadMgr() = default;
    virtual bool ReloadScriptModule(std::string_view model_name, const std::string& script_content) = 0;
};

class IStreamingMgr
{
public:
    IStreamingMgr() = default;
    virtual ~IStreamingMgr() = default; 
    virtual bool IsStreamingEnabled() const = 0;
    virtual void UpdateRenderStatus(FrameParam* fp) = 0;
    virtual void UpdateDesiredLODCount(const ecs::EntityID& entity, int count) = 0;
    virtual int GetResidentLODCount(const ecs::EntityID& entity) const = 0;
};
}   // namespace cross
