#include "ParticleSystemResource.h"
#include "Resource/ResourceManager.h"
#include "Resource/AssetStreaming.h"
#include "Resource/MaterialInterface.h"
namespace cross::fx
{

bool ParticleSystemResource::Serialize(SerializeNode&& s, const std::string& path)
{
    if (!Resource::HasAsset())
    {
        CreateAsset(path);
    }
    Resource::ClearReference();

    for (const auto& emitterHandle : mEmitterHandles)
    {
        s["Emitters"].PushBack(emitterHandle->GetSerialNode());
        AddReferenceResource(emitterHandle->GetGuid_Str());
    }

    auto SplitStringByNewline = [](const std::string& input) {
        std::vector<std::string> result;
        std::istringstream iss(input);
        std::string line;

        while (std::getline(iss, line))
        {
            if (!line.empty() && line.back() == '\r')
            {
                line.pop_back();
            }
            result.push_back(line);
        }
        return result;
    };

    SerializeContext context;
    for (auto& emitterInfo : mEmitterInfos)
    {
        emitterInfo.SimulationPath = gResourceMgr.ConvertPathToGuid(emitterInfo.SimulationPath);
        if (emitterInfo.Simulation == SimulationType::GPU)
        {
            AddReferenceResource(emitterInfo.SimulationPath);
        }
        auto emitterNode = emitterInfo.Serialize(context);

        Assert(emitterNode["SimulationParameters"].Size() == 0);
        const auto& simulationParametersCode = SplitStringByNewline(emitterInfo.SimulationParameters);
        for (auto& line : simulationParametersCode)
        {
            emitterNode["SimulationParameters"].PushBack(line);
        }

        Assert(emitterNode["OverrideDashboard"].Size() == 0);
        const auto& overrideDashboardCode = SplitStringByNewline(emitterInfo.OverrideDashboard);
        for (auto& line : overrideDashboardCode)
        {
            emitterNode["OverrideDashboard"].PushBack(line);
        }

        Assert(emitterNode["OverrideInitStage"].Size() == 0);
        const auto& overrideInitStageCode = SplitStringByNewline(emitterInfo.OverrideInitStage);
        for (auto& line : overrideInitStageCode)
        {
            emitterNode["OverrideInitStage"].PushBack(line);
        }

        Assert(emitterNode["OverrideUpdateStage"].Size() == 0);
        const auto& overrideUpdateStageCode = SplitStringByNewline(emitterInfo.OverrideUpdateStage);
        for (auto& line : overrideUpdateStageCode)
        {
            emitterNode["OverrideUpdateStage"].PushBack(line);
        }

        s["EmitterInfos"].PushBack(std::move(emitterNode));
        AddReferenceResource(emitterInfo.MeshRenderer.MaterialPath);
        AddReferenceResource(emitterInfo.MeshRenderer.ModelPath);
        AddReferenceResource(emitterInfo.SpriteRenderer.MaterialPath);
        AddReferenceResource(emitterInfo.SpriteRenderer.EmitterMPCPath);
        AddReferenceResource(emitterInfo.MeshRenderer.EmitterMPCPath);
    }

    s["RandomSeed"] = mRandomSeed;
    s["AutoPlay"] = mAutoPlay;

    return Resource::Serialize(std::move(s), path);
}
ParticleSystemResource* ParticleSystemResource::FX_CreateParticleSystem() 
{
    auto mtlPtr = gResourceMgr.CreateResourceAs<cross::fx::ParticleSystemResource>();
    //mtlPtr->IncreaseRefCount();
    return mtlPtr.get();
}
bool ParticleSystemResource::Deserialize(DeserializeNode const& s)
{
    if (s.HasMember("RandomSeed"))
    {
        mRandomSeed = s["RandomSeed"].AsUInt32();
    }

    if (s.HasMember("AutoPlay"))
    {
        mAutoPlay = s["AutoPlay"].AsBoolean();
    }

    auto MergeStrings = [](const std::vector<std::string>& arr) {
        std::ostringstream oss;
        for (const auto& s : arr)
        {
            oss << s << '\n';
        }
        std::string result = oss.str();
        if (!result.empty())
        {
            result.pop_back();
        }
        return result;
    };

    if (auto emitters = s.HasMember("EmitterInfos"); emitters && emitters->IsArray())
    {
        const UInt32 emitterCount = static_cast<UInt32>(emitters->Size());
        mEmitterInfos.clear();
        mEmitterInfos.reserve(emitterCount);
        SerializeContext context;
        auto& arr = *emitters;
        for (auto i = 0u; i < emitterCount; ++i)
        {
            auto& emitterInfo = mEmitterInfos.emplace_back();
            emitterInfo.Deserialize(arr[i], context);

            if (auto simulationParametersCode = arr[i].HasMember("SimulationParameters"); simulationParametersCode && simulationParametersCode->IsArray())
            {
                std::vector<std::string> result;
                for (int j = 0u; j < simulationParametersCode->Size(); j++)
                {
                    result.push_back(simulationParametersCode->At(j).AsString());
                }

                emitterInfo.SimulationParameters = MergeStrings(result);
            }

            if (auto overrideDashboardCode = arr[i].HasMember("OverrideDashboard"); overrideDashboardCode && overrideDashboardCode->IsArray())
            {
                std::vector<std::string> result;
                for (int j = 0u; j < overrideDashboardCode->Size(); j++)
                {
                    result.push_back(overrideDashboardCode->At(j).AsString());
                }

                emitterInfo.OverrideDashboard = MergeStrings(result);
            }

            if (auto overrideInitStageCode = arr[i].HasMember("OverrideInitStage"); overrideInitStageCode && overrideInitStageCode->IsArray())
            {
                std::vector<std::string> result;
                for (int j = 0u; j < overrideInitStageCode->Size(); j++)
                {
                    result.push_back(overrideInitStageCode->At(j).AsString());
                }

                emitterInfo.OverrideInitStage = MergeStrings(result);
            }

            if (auto overrideUpdateStageCode = arr[i].HasMember("OverrideUpdateStage"); overrideUpdateStageCode && overrideUpdateStageCode->IsArray())
            {
                std::vector<std::string> result;
                for (int j = 0u; j < overrideUpdateStageCode->Size(); j++)
                {
                    result.push_back(overrideUpdateStageCode->At(j).AsString());
                }

                emitterInfo.OverrideUpdateStage = MergeStrings(result);
            }
            
            // emitterInfo.EmitterName = arr[i]["EmitterName"].AsString();
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.EmitterState, "EmitterState");
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.LocationShape, "LocationShape");
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.ParticleSpawn, "ParticleSpawn");
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.ParticleInit, "ParticleInit");
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.SizeScale, "SizeScale");
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.ColorScale, "ColorScale");
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.SpriteRotationRate, "SpriteRotationRate");
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.Velocity, "Velocity");
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.VectorNoise, "VectorNoise");
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.GravityForce, "GravityForce");
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.Force, "Force");
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.VortexForce, "VortexForce");
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.PointAttractionForce, "PointAttractionForce");
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.SolveForceVelocity, "SolveForceVelocity");
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.SubUV, "SubUV");
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.ParticleState, "ParticleState");
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.EventGenerator, "EventGenerator");
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.SpriteRenderer, "SpriteRenderer");
            // DESERIALIZE_MODULE(arr[i], context, emitterInfo.MeshRenderer, "MeshRenderer");

            // if (arr[i].HasMember("Simulation"))
            // {
            //     emitterInfo.Simulation = static_cast<SimulationType>(arr[i]["Simulation"].AsInt32());
            // }
            // if (arr[i].HasMember("SimulationPath"))
            // {
            //     emitterInfo.SimulationPath = arr[i]["SimulationPath"].AsString();
            // }
            // if (arr[i].HasMember("WorldX"))
            // {
            //     emitterInfo.WorldX = arr[i]["WorldX"].AsInt32();
            // }
            // if (arr[i].HasMember("WorldY"))
            // {
            //     emitterInfo.WorldY = arr[i]["WorldY"].AsInt32();
            // }
            // if (arr[i].HasMember("RendererType"))
            // {
            //     emitterInfo.RendererType = static_cast<ParticleRendererType>(arr[i]["RendererType"].AsInt32());
            // }
        }
    }

    // TODO(jihuixu): Deprecated, just for compatible old resource
    if (auto emitters = s.HasMember("Emitters"); emitters && emitters->IsArray())
    {
        auto& arr = *emitters;
        const UInt32 emitterCount = static_cast<UInt32>(emitters->Size());
        mEmitterHandles.clear();
        mEmitterHandles.reserve(emitterCount);
        for (auto i = 0u; i < emitterCount; ++i)
        {
            if (arr[i].IsObject())
            {
                for (auto [k, v] : arr[i])
                {
                    if (k == "Path")
                    {
                        const std::string path = v.AsString();
                        ResourcePtr emitterResource = gAssetStreamingManager->GetResource(path);
                        mEmitterHandles.emplace_back(TypeCast<fx::ParticleEmitterResource>(emitterResource));
                    }
                }
            }
        }
    }

    return true;
}

const char* ParticleSystemResource::GetEmitterPath(UInt32 index) const
{
    return mEmitterHandles[index] ? mEmitterHandles[index]->GetEmitterPath() : nullptr;
}

const char* ParticleSystemResource::GetParticleSystemPath() const
{
    return GetAsset() ? GetAsset()->GetName() : nullptr;
}

const ParticleEmitterInfo& ParticleSystemResource::GetEmitterInfo(UInt32 index) const
{
    Assert(index < mEmitterInfos.size());
    return mEmitterInfos[index];
}

}   // namespace cross::fx