#pragma once
#include "Resource/Resource.h"
#include "Resource/MeshAssetData.h"

#include "CECommon/Geometry/CollisionMesh.h"
//#include "RenderEngine/RenderWorldConst.h"
#include "Resource/ResourceManager.h"
#include "CrossBase/Serialization/Codec/FlatBufferCodec.hpp"
#include "CrossBase/Serialization/Archive/MemoryMappingArchive.h"
#include "CrossBase/CEMetaMacros.h"
namespace cross { namespace resource {
    constexpr UInt8 MAX_MESH_LOD_NUM = 8;
    constexpr float MESH_LOD_POWER_BASE = 0.75f;
    struct MeshAssetLODSetting
    {
        struct LevelSetting
        {
            float mFadeTransitionWidth;
            float mScreenReleativeTransitionHeight;
        };

        std::array<LevelSetting, MAX_MESH_LOD_NUM> mLevelSettings;
        float mCulledHeight{ 0.f };
        bool mIsStreamable{ false };

        SerializeNode Serialize() const;
        void Deserialize(const DeserializeNode& json);

        MeshAssetLODSetting()
        {
            // DEPRECATED: Initialize as 0.25 - 0.125 - 0.0625 - 0.03125
            // Now we align with UE: Transition screen size at LOD level x is 0.75 ^ (x + 1)
            for (UInt8 i = 0; i < MAX_MESH_LOD_NUM; ++i)
            {
                mLevelSettings[i].mFadeTransitionWidth = 0.25f;
                mLevelSettings[i].mScreenReleativeTransitionHeight = std::powf(MESH_LOD_POWER_BASE, static_cast<float>(i + 1));
            }
        }
    };

    struct Simplify
    {
        SerializeNode Serialize() const;
        void Deserialize(const DeserializeNode& json);

        int triangleRate;
        Simplify() : triangleRate(100){}
    };

    class Resource_API CEMeta(Cli, Script) MeshAssetDataResource : public Resource
    {
    public:
        virtual ~MeshAssetDataResource();

        StatementDefaultResource(MeshAssetDataResource);
        bool CrossSchemaToMeshAssetData(CrossSchema::ImportMeshAssetData& importMeshAsset);
        bool Serialize(std::string ndapath);
        bool Deserialize(FBSerializer const& s) override;
        bool Serialize(SerializeNode&& s, const std::string& path) override;
        virtual void DeserializeCustomNode(const DeserializeNode& json) override;
        bool ResetResource() override;
    public:
        const skeleton::ReferenceSkeleton* GetRefSkeleton() const
        {
            return mMeshAssetData.GetRefSkeleton();
        }
        const std::vector<SIMDMatrix>& GetBindPoseInvMat() const
        {
            return mMeshAssetData.GetBindPoseInvMat();
        }
        bool IsSkinValid() const
        {
            return mMeshAssetData.IsSkinValid();
        }

        inline const cross::MeshAssetData* GetAssetData() const
        {
            return &mMeshAssetData;
        }

        inline cross::MeshAssetData* GetAssetData()
        {
            return &mMeshAssetData;
        }

        inline const CollisionMesh* GetCollisionMesh(UInt32 meshPartIndex) const
        {
            return &(mCollisionMeshVec[meshPartIndex]);
        }

        inline const MeshAssetLODSetting* GetLODSetting() const
        {
            return &mLODSetting;
        }

        inline MeshAssetLODSetting* GetRawLODSetting()
        {
            return &mLODSetting;
        }

        void OnDataUploaded();

        //Editor Interfaces
        CEMeta(Cli)
        int Mesh_UpdateLodMesh(int lod, const char* lodPath);
        CEMeta(Cli)
        int Mesh_AddLodMesh(const char* lodPath);
        CEMeta(Cli)
        int Mesh_DelLodMesh(int lod);
        CEMeta(Cli)
        bool Mesh_HasCluster();
        //Editor Interfaces
        void ReloadMesh();
        void SetResidentInCpu(bool value) 
        { 
            ResidentInCpu = value;
        }
        inline Simplify* GetSimplify() 
        {
            return &simplify;
        }
        EditMeshLodError UpdateLodMesh(UInt8 lod, MeshAssetDataResource* meshResource);

        EditMeshLodError AddLodMesh(MeshAssetDataResource* meshResource);

        EditMeshLodError DelLodMesh(UInt8 lod);

        int SeparateTo(Float2 point, Float2 blockSize, std::vector<MeshAssetDataResourcePtr>& outMeshAssets, bool splitToPart = false);
        static int Combine(const std::vector<MeshAssetDataResourcePtr>& inMeshAsset, const std::vector<Float4x4>& trans, const std::vector<std::vector<std::array<int, 4>>>& texIndexs, MeshAssetDataResourcePtr outMeshAsset);

        IStreamableResource* GetStreamableProxy() const
        {
            return mStreamableProxy.get();
        }

        bool IsMeshAssetStreamable() const;
        void SetMeshAssetStreamable(bool enabled);

    private:
        void BuildCollisionMesh();

    protected:
        MeshAssetDataResource();
        cross::MeshAssetData mMeshAssetData;
        std::vector<CollisionMesh> mCollisionMeshVec;
        MeshAssetLODSetting mLODSetting;
        Simplify simplify;
        bool ResidentInCpu = false;
        std::shared_ptr<IStreamableResource> mStreamableProxy = nullptr;
        FRIEND_WITH_REFLECTION_MODULE;
        friend class cross::ResourceManager;
    public:
        bool Serialize(const CrossSchema::ImportMeshAssetDataT& inMeshAssetAsset);
    private:
        std::unique_ptr<cross::MemoryMappingArchive> mArchive;
        std::unique_ptr<cross::FBSerializer> mSerializer;
        UInt32 mOffset;
    };

}}   // namespace cross::resource
