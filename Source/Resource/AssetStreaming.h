#pragma once

#include <atomic>
#include <mutex>
#include <queue>
#include <map>
#include "Resource.h"
#include "resourceforward.h"

namespace cross
{
    using AssetStreamingPtr = std::shared_ptr<class AssetStreamingHandle>;
    class AssetStreamingContext
    {
        friend class AssetStreamingManager;
        friend class AssetStreamingHandle;

    public:
        enum class StreamingStage : UInt32
        {
            Queued,
            Start,
            ResolvingDependencies,
            Processing,
            PostDeserialize,
            PostDeserializing,
            Finish
        };

        AssetStreamingContext(AssetStreamingHandle* handle, const std::string& assetPath, std::function<void(ResourcePtr)> completionEvent)
            : mHandle(handle)
            , mAssetPath(assetPath)
            , mCompletionEvent(std::move(completionEvent))
        {
            Assert(mAssetPath != "");
            mStage.store(StreamingStage::Queued, std::memory_order_release);
            mProcessingPercentage.store(0, std::memory_order_relaxed);
        }

        bool Tick();

        StreamingStage GetStreamingStage() const
        {
            return mStage.load(std::memory_order_acquire);
        }

        float GetProcessingPercentage() const
        {
            return mProcessingPercentage.load(std::memory_order_relaxed);
        }

        const std::string GetName() const
        {
            return mAssetPath;
        }
        std::vector<std::pair<std::string, ResourcePtr>>& GetPostDeserializeList()
        {
            return mPostDeserializeList;
        }

        void PushToPostDeserializeListThreadSafe(std::string assetName, ResourcePtr resource);

        void EraseCompletedRequest();

        void PostDeserialize();

        void IncrementalUpdateProcessingProgress(int count);

    private:
        void Trigger()
        {
            mStallEvent.Trigger();
        }

        void Wait()
        {
            mStallEvent.Wait();
            mStallEvent.Reset();
        }

        void SetStreamingStage(StreamingStage stage)
        {
            mStage.store(stage, std::memory_order_release);
        }

        void SetProcessingPercentage(float percentage)
        {
            mProcessingPercentage.store(percentage, std::memory_order_relaxed);
        }

        void CallCompletionEvent() const;

        AssetStreamingHandle* mHandle{};
        const std::string mAssetPath;

        std::atomic<StreamingStage> mStage{};
        std::atomic<float> mProcessingPercentage;
        std::vector<std::string> mImports;
        std::atomic<int> mLoadedAssetsCount = 0;
        int mRequestedAssetsCount = 0;
        // add cached resources, in case global resource GC during loading
        std::unordered_map<std::string, ResourcePtr> mCachedResources;
        UInt32 mIndex{};

        std::unordered_map<std::string, threading::TaskEventPtr> mLoadingTasksMap;
        threading::TaskEventPtr mFinalizeTask;

        threading::StallEvent mStallEvent;

        std::mutex mPostDeserizlizeListMutex;
        std::vector<std::pair<std::string, ResourcePtr>> mPostDeserializeList;
        std::unordered_set<std::string> mPosttaskSet;
        std::function<void(ResourcePtr)> mCompletionEvent;
    };

    class AssetStreamingHandle : public std::enable_shared_from_this<AssetStreamingHandle>
    {
        friend class AssetStreamingManager;
        friend class AssetStreamingContext;

    public:
        static AssetStreamingPtr Create(const std::string& assetPath, std::function<void(ResourcePtr)> completionEvent, bool doPostDeserialize = true)
        {
            class AssetStreamingHandleDerived : public AssetStreamingHandle
            {
            public:
                AssetStreamingHandleDerived(const std::string& assetPath, std::function<void(ResourcePtr)> completionEvent, bool doPostDeserialize)
                    : AssetStreamingHandle(assetPath, std::move(completionEvent), doPostDeserialize) {}
            };
            std::string relativeFileName = EngineGlobal::GetFileSystem()->GetRelativePath(assetPath);
            return std::make_shared<AssetStreamingHandleDerived>(relativeFileName, std::move(completionEvent), doPostDeserialize);
        }

        Resource_API AssetStreamingHandle(const std::string& assetPath, std::function<void(ResourcePtr)> completionEvent, bool doPostDeserialze = true);

        Resource_API void WaitForStreaming();

        Resource_API bool CheckAndCompleteStreaming();

        AssetStreamingContext::StreamingStage GetStreamStage()
        {
            return mStreamingContext->GetStreamingStage();
        }

        ResourcePtr GetResource() const
        {
            Assert(mResource);

            return mResource;
        }

        float GetProgressPercentage() const
        {
            return mStreamingContext->GetProcessingPercentage();
        }

        AssetStreamingContext* GetContext() const
        {
            return mStreamingContext.get();
        }

        bool NeedPostDeserialize() const {
            // @mindalv always set mDoPostDeserialze true. Do postdeserialize at Async thread for all resource
            return true;
        }

    private:
        void PostDeserializeNoWait();

        void PostDeserialize();

        void SetResource(const ResourcePtr& resource)
        {
            Assert(resource);
            mResource = resource;
        }
        std::unique_ptr<AssetStreamingContext> mStreamingContext;
        ResourcePtr mResource;
        bool mDoPostDeserialze = true;
    };

    class Resource_API CEMeta(Script) AssetStreamingManager
    {
        friend class AssetStreamingContext;
        friend class AssetStreamingHandle;

    public:
        enum class StreamingPriority : UInt32
        {
            High,
            Normal,
            Low,
            Num
        };

        struct RequestState
        {
            enum class State : UInt32
            {
                Deserialize,
                PostDeserialize
            };

            threading::TaskEventPtr mEvent;
            State mState;
            ResourcePtr mResource;
        };
        
        CEFunction(ScriptCallable)
        static AssetStreamingManager* Get();

        UInt32 GetMaxNumStreamingTasks() const
        {
            std::scoped_lock lock(mRequestQueuesMutex);

            return mMaxNumStreamingTasks;
        }

        void SetMaxNumStreamingTasks(UInt32 numTasks)
        {
            std::scoped_lock lock(mRequestQueuesMutex);

            mMaxNumStreamingTasks = numTasks;
        }

        AssetStreamingPtr RequestAsyncLoad(const std::string& assetPath, bool needPost = true, std::function<void(ResourcePtr)>&& completionEvent = nullptr, StreamingPriority priority = StreamingPriority::Normal);

        bool CancelLoadRequest(AssetStreamingPtr streamingPtr);

        CEFunction(ScriptCallable)
        ResourcePtr LoadSynchronously(const std::string& assetPath, bool needPostDeserialize = true);

        // note this forceLoad is deprecated
        // you should call  gResourceMgr.ReloadResource;
        CEFunction(ScriptCallable)
        ResourcePtr GetResource(const std::string& assetPath, bool forceLoad = true, bool needPostDeserialize = true);

        UInt32 GetNumInflightLoadRequests();

        void AddAsyncLoadRequests(AssetStreamingPtr streamingPtr, StreamingPriority priority);

    private:
        ResourcePtr TryLoadResource(const std::string& assetPath, AssetStreamingPtr parent);

        threading::TaskEventPtr StartLoadResourceRequest(std::shared_ptr<threading::TaskEventArray> predecessors, const std::string& assetPath, AssetStreamingPtr parent);

        void CompleteLoadRequest(const std::string& assetPath, ResourcePtr resource, bool performPostDeserialize);

        void ProcessLoadRequests();

        AssetStreamingPtr GetNextRequest();

        std::mutex mRequestMutex;
        std::unordered_map<std::string, RequestState> mRequests;

        std::queue<AssetStreamingPtr> mRequestQueues[static_cast<UInt32>(StreamingPriority::Num)];
        mutable std::mutex mRequestQueuesMutex;

        UInt32 mMaxNumStreamingTasks{ 1 };
        UInt32 mNumActiveTasks{ 0 };
    };
}

#define gAssetStreamingManager cross::AssetStreamingManager::Get()
