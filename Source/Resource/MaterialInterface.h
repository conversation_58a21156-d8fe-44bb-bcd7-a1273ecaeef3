#pragma once

#include "Resource/Resource.h"
#include "CECommon/Common/RenderStateDefines.h"
#include "NativeGraphicsInterface/NGI.h"
#include "CrossBase/Serialization/SerializeNode.h"
#include "CECommon/Common/FrameParam.h"
#include "Resource/RenderSyncResource.h"
#include "Resource/Texture/Texture.h"
#include "Resource/Texture/TextureUDIM.h"
#include "Resource/MaterialParameterCollection.h"
#include "Resource/MaterialDefines.h"
#include "Resource/Shader.h"

namespace cross {
class File;
class GInputLayout;
class ShaderBindDatas;
class IMaterialR;
class ResourceManager;

namespace resource {
    enum class MaterialType
    {
        MaterialType_Standard = 0,
        MaterialType_Unlit,
        MaterialType_TwosidedFoliage,
        MaterialType_ImposterFoliage,
        MaterialType_Subsurface
    };

    class Resource_API CEMeta(Cli, Script) MaterialInterface : public RenderSyncResource
    {
    public:
        using PropertyType = std::variant<std::vector<float>, bool, TexturePtr, SamplerState>;
        using StateType = MaterialPassState;
        using PropertyAccesser = TFunction<void(NameID const&, PropertyType const&)>;

        virtual bool RefreshRenderData() = 0;
        MaterialInterface();
        IMaterialR* GetRenderMaterial() { return mRenderMaterial.get(); }

    public:
        virtual Shader* GetShader(const NameID& passId) = 0;

        virtual PropertyType const* GetProperty(const NameID& name) const = 0;
        virtual bool const* GetPropertyVisible(const NameID& name) const = 0;
        virtual bool const* GetPropertyIsColor(const NameID& name) const = 0;

        virtual bool IsPassEnable(const NameID& passID) const = 0;
        virtual void SetPassEnable(const NameID& passID, bool enable) = 0;
        virtual StateType const* GetState(const NameID& passID) const = 0;
        virtual UInt32 GetRenderGroup(NameID const& passID) = 0;
        virtual void VisitProperty(PropertyAccesser func) const = 0;
        virtual bool HasAncestor(const std::string& name) = 0;

        virtual void Refresh(bool resetResource = false) = 0;
        virtual void RefreshMaterialTree(bool resetResource = false, bool skipSelf = false) = 0;

        virtual bool SetParent(const std::string& path) { return false; }
        virtual void SetBool(const NameID& name, bool value) = 0;
        virtual void SetInt(const NameID& name, int value) = 0;
        CEFunction(ScriptCallable)
        virtual void SetFloat(const NameID& name, float value) = 0;
        virtual void SetFloat2(const NameID& name, const float value[2]) = 0;
        virtual void SetFloat3(const NameID& name, const float value[3]) = 0;
        virtual void SetFloat4(const NameID& name, const float value[4]) = 0;
        virtual void SetFloatArray(const NameID& name, int length, const float* pValue) = 0;
        CEFunction(ScriptCallable)
        virtual void SetTexture(const NameID& name, TexturePtr texture) = 0;
        virtual void SetTextureProp(const NameID& name, TexturePtr texture) = 0;
        virtual void SetSamplerState(const NameID& name, const SamplerState& samplerState) = 0;
        virtual void SetProp(const NameID& name, const PropertyType& propVal) = 0;

        virtual void SetBlendState(const NameID& passID, const NGIBlendStateDesc& blendDesc) = 0;
        virtual void SetBlendStateProp(const NameID& passID, const NGIBlendStateDesc& blendDesc) = 0;
        virtual void SetDepthStencilState(const NameID& passID, const NGIDepthStencilStateDesc& depthStencilDesc) = 0;
        virtual void SetDepthStencilStateProp(const NameID& passID, const NGIDepthStencilStateDesc& depthStencilDesc) = 0;
        virtual void SetRasterizerState(const NameID& passID, const NGIRasterizationStateDesc& rasterizerState) = 0;
        virtual void SetDynamicState(const NameID& passID, const NGIDynamicStateDesc& dynamicState) = 0;

        virtual void SetRenderGroup(NameID const& passID, UInt32 val) = 0;
        virtual void SetRenderGroupProp(NameID const& passID, UInt32 val) = 0;

        virtual void RemoveProperty(const NameID& name) { Assert(false); }

        virtual void ResetRenderGroup(const NameID& name) { Assert(false); }

    public:
        virtual FxPtr GetFx() const = 0;
        virtual MaterialInterfacePtr GetParent() { return MaterialInterfacePtr{nullptr}; }
        virtual bool IsDependent(const std::string& materialGuid) = 0;


        virtual MaterialInterfacePtr CreateInstance() = 0;

        virtual MaterialPtr CreateTempInstance() = 0;


        virtual void EditorVisitProperty(PropertyAccesser func) = 0;
        virtual std::string EditorGetPropertyString() = 0;
        virtual std::string EditorGetPassString() = 0;
        virtual void EditorSetRenderState(const NameID& passID, MaterialRenderState renderState) = 0;
        virtual MaterialRenderState EditorGetRenderState(const NameID& passID) const = 0;
        virtual bool EditorGetBlendEnable(const NameID& passID) = 0;
        virtual bool EditorIsPropertyOverrided(const NameID& propID) = 0;
        virtual bool EditorIsRenderGroupOverrided(const NameID& passID) = 0;
        virtual bool EditorIsRenderStateOverrided(const NameID& passID) = 0;
        virtual void EditorNotitfySubMaterial() = 0;
        virtual void AfterAssetDelete() = 0;

        virtual void NotifyChangeRecursively() = 0;

    public:
        CEMeta(Cli)
        int GetVersion() const { return mVersion; }
        void SetVersion(int version) { mVersion = version; }

        bool IsCreatedByMaterialEditor() const { return mVersion == 2; }

        const std::tuple<const Shader::ProtoType*, const Shader::ProtoType*> GetPrimitiveAndObjectProtoType(MaterialUsage usage) const
        {
            return
            {
                GetPrimitiveProtoType(usage),
                GetObjectProtoType(usage),
            };
        }
        const Shader::ProtoType* GetMaterialProtoType() const;

        void AddChildMaterial(MaterialInterface*);
        void RemoveChildMaterial(MaterialInterface*);
        const auto& GetChildMaterials() const { return mChildMaterial; }
        bool HasChildMaterial(MaterialInterface* child) const { return mChildMaterial.find(child) != mChildMaterial.end(); }

        virtual void OnParentDirty(const NameID& propID, const PropertyType& propVal){}
        virtual void OnParentDirty(const NameID& passID, const StateType& states){}
        virtual void OnParentDirty(const NameID& passID, UInt32 group){}
        virtual void OnParentDirty(const NameID& passID, bool enable){}

        virtual void FillParameterCollectionValue(MPCPtr mpc) = 0;
        virtual void FillAllParameterCollectionValue() = 0;
        virtual bool HasMpcError() = 0;
        virtual void RegistAllMpc() = 0;
        virtual void UnregistAllMpc() = 0;

    protected:
        int mVersion{1};
        std::shared_ptr<IMaterialR> mRenderMaterial;

        std::mutex mChildMaterialMutex;
        std::unordered_set<MaterialInterface*> mChildMaterial;

    private:
        const Shader::ProtoType* GetPrimitiveProtoType(MaterialUsage usage)const;
        const Shader::ProtoType* GetObjectProtoType(MaterialUsage usage) const;
    };
}   // namespace resource
}   // namespace cross