#include "CECommon/Common/WorldConst.h"
#include "Resource/Prefab/PrefabData.h"

namespace cross {
bool PrefabEntityData::IsInherit()
{
    return mInheritPrefabId != "" && mInheritPrefabEUID != CrossUUID::GetInvalidUUID();
}

void PrefabEntityData::IncreaseRefCount() 
{
    ReferenceCountObject::IncreaseRefCount();
    if (mPrefabData)
        mPrefabData->IncreaseRefCount();
}

void PrefabEntityData::DecreaseRefCount() 
{
    ReferenceCountObject::DecreaseRefCount();
    if (mRefCount > 0 && mPrefabData)
        mPrefabData->DecreaseRefCount();
}

void PrefabEntityData::IncreaseRefCount() const {
    ReferenceCountObject::IncreaseRefCount();
    if (mPrefabData)
        mPrefabData->IncreaseRefCount();
}

void PrefabEntityData::DecreaseRefCount() const {
    ReferenceCountObject::DecreaseRefCount();
    if (mRefCount > 0 && mPrefabData)
        mPrefabData->DecreaseRefCount();
}

void PrefabEntityData::ClearData()
{
    mData = SerializeNode::EmptyObject();
}

void PrefabData::Push(PrefabEntityDataPtr data) 
{
    std::unique_lock<std::shared_mutex> writeLock(mReadWriteMutex);
    mRefCount += data->GetRefCount();
    mEntityDataMap.emplace(data->mPrefabEUID, data);
    data->mPrefabData = this;
}

UInt64 PrefabData::GetEntityCount() {
    std::shared_lock<std::shared_mutex> readLock(mReadWriteMutex);
    return mEntityDataMap.size();
}

PrefabEntityDataPtr PrefabData::Add(const std::string& inheritPrefabId, CrossUUID inheritPrefabEuid)
{
    PrefabEntityDataPtr entityData = new PrefabEntityData;
    entityData->mPrefabId = mID;
    entityData->mPrefabEUID = CrossUUID::GenerateCrossUUID();
    entityData->mInheritPrefabId = inheritPrefabId;
    entityData->mInheritPrefabEUID = inheritPrefabEuid;
    SerializeNode diffJosn;
    diffJosn[WorldSerializeConst::PrefabID] = inheritPrefabId;
    diffJosn[WorldSerializeConst::PrefabEUID] = inheritPrefabEuid.ToString();
    entityData->mData = std::move(diffJosn);
    Push(entityData);
    return entityData;
}

void PrefabData::Pop(CrossUUID euid)
{
    std::unique_lock<std::shared_mutex> writeLock(mReadWriteMutex);

    std::stack<CrossUUID> delEntities;
    delEntities.push(euid);
    while (!delEntities.empty())
    {
        auto delEuid = delEntities.top();
        delEntities.pop();

        auto it = mEntityDataMap.find(euid);
        if (it != mEntityDataMap.end())
        {
            auto entityData = it->second;
            for (const auto& child : entityData->mChildren)
            {
                delEntities.push(child);
            }
            entityData->mPrefabData = nullptr;
            it = mEntityDataMap.erase(it);
            mRefCount -= entityData->GetRefCount(); 
        }
    }
}

PrefabEntityDataPtr PrefabData::Get(CrossUUID euid)
{
    std::shared_lock<std::shared_mutex> readLock(mReadWriteMutex);
    auto it = mEntityDataMap.find(euid);
    return it != mEntityDataMap.end() ? it->second : nullptr;
}

std::vector<PrefabEntityDataPtr> PrefabData::Get(const std::string& prefabId, CrossUUID prefabEuid)
{
    std::shared_lock<std::shared_mutex> readLock(mReadWriteMutex);
    std::vector<PrefabEntityDataPtr> ret;
    for (auto& [euid, entityData] : mEntityDataMap)
    {
        if (entityData->IsInherit() && entityData->mInheritPrefabId == prefabId && entityData->mInheritPrefabEUID == prefabEuid)
        {
            ret.emplace_back(entityData);
        }
    }
    return ret;
}

void PrefabData::Clear() {
    std::unique_lock<std::shared_mutex> writeLock(mReadWriteMutex);
    for (auto it = mEntityDataMap.begin(); it != mEntityDataMap.end(); it++)
    {
        Assert(it->second->GetRefCount() == 1);
        mRefCount -= it->second->GetRefCount() - 1;
        it->second->mPrefabData = nullptr;
    }
    mEntityDataMap.clear();
}

void PrefabData::RemoveByMap(const PrefabEntityMap& entityMap) {
    std::unique_lock<std::shared_mutex> writeLock(mReadWriteMutex);
    for (auto it = mEntityDataMap.begin(); it != mEntityDataMap.end();)
    {
        if (entityMap.find(it->first) == entityMap.end())
        {
            auto entityData = it->second;
            entityData->mPrefabData = nullptr;
            it = mEntityDataMap.erase(it);
            mRefCount -= entityData->GetRefCount(); 
        }
        else
        {
            it++;
        }            
    }
}
}   // namespace cross