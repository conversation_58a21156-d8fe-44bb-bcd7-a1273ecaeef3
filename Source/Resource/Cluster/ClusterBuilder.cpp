#include "ClusterBuilder.h"
#include "CrossBase/Math/CrossMath.h"
#include "GraphPartitioner.h"
#include "MeshAssetData.h"
#include "Util.h"

namespace {
    using namespace cross;
    using namespace cross::StellarMesh;

    InputMeshData BuildInputMeshData(MeshAssetData* meshAssetData, InputMeshData& meshData)
    {
        meshAssetData->GetRawVertexData(VertexChannel::Position0, meshData.mMeshData.mVertexAttribute.mPosition);
        meshAssetData->GetRawVertexData(VertexChannel::Normal0, meshData.mMeshData.mVertexAttribute.mNormal, true);
        meshAssetData->GetRawVertexData(VertexChannel::Tangent0, meshData.mMeshData.mVertexAttribute.mTangent);
        meshAssetData->GetRawVertexData(VertexChannel::TexCoord0, meshData.mMeshData.mVertexAttribute.mUV0);
        meshAssetData->GetRawVertexData(VertexChannel::TexCoord1, meshData.mMeshData.mVertexAttribute.mUV1);
        meshAssetData->GetRawVertexData(VertexChannel::Color0, meshData.mMeshData.mVertexAttribute.mColor);

        meshAssetData->GetIndexData(0, meshData.mMeshData.mIndexes);

        UInt32 lodMeshPartStartIndex = 0;
        UInt32 lodMeshCount = 0;
        meshAssetData->GetMeshLodInfo(0, lodMeshPartStartIndex, lodMeshCount);

        for (UInt32 meshPart = lodMeshPartStartIndex; meshPart < lodMeshPartStartIndex + lodMeshCount; ++meshPart)
        {
            const MeshPartAssetInfo& meshPartInfo = meshAssetData->GetMeshPartInfo(meshPart);

            for (UInt32 i = 0; i < meshPartInfo.mPrimitiveCount; i++)
            {
                meshData.mMaterialIndices.push_back(meshPartInfo.mMaterialIndex);
            }
        }

        meshData.mTriangleNum = static_cast<UInt32>(meshData.mMeshData.mIndexes.size() / 3);

        return meshData;
    }

    void ClusterTrianglesNaive(InputMeshData const& inputMeshData, std::vector<Cluster>& clusters)
    {

        auto const& verts = inputMeshData.mMeshData.mVertexAttribute;
        auto const& indexes = inputMeshData.mMeshData.mIndexes;
        auto triangleNum = inputMeshData.mTriangleNum;

        Adjacency adjacency(static_cast<UInt32>(indexes.size()));
        EdgeHashTable edgeHash;

        auto GetPosition = [&](UInt32 edgeIndex) -> Float3 { return verts.mPosition[indexes[edgeIndex]]; };

        auto BuildEdgeAdjacency = [&] {
            // TODO: parallel this
            for (SInt32 edgeIndex = 0; edgeIndex < static_cast<SInt32>(indexes.size()); edgeIndex++)
            {
                edgeHash.AddEdge(edgeIndex, GetPosition);
            }

            for (SInt32 edgeIndex = 0; edgeIndex < static_cast<SInt32>(indexes.size()); edgeIndex++)
            {
                edgeHash.ForEachMatchingEdge(edgeIndex, GetPosition, [&](SInt32 edgeIndex, SInt32 otherEdgeIndex) { adjacency.Link(edgeIndex, otherEdgeIndex); });
            }
        };

        BuildEdgeAdjacency();

        std::vector<UInt32> triangles;
        triangles.resize(triangleNum);
        for (auto i = 0u; i < triangleNum; i++)
            triangles[i] = i;

        UInt32 OutputClusterCount = math::DivideAndRoundUp(triangleNum, ClusterPartitionSetting::maxTrianglesInCluster);

        // Loop all clusters and perform cluster partition.
        for (auto i = 0u; i < OutputClusterCount; i++)
        {
            auto cluster_begin = i * ClusterPartitionSetting::maxTrianglesInCluster;
            auto cluster_end = std::min(cluster_begin + ClusterPartitionSetting::maxTrianglesInCluster, triangleNum);
            auto cluster_size = cluster_end - cluster_begin;
            clusters.emplace_back(Cluster{inputMeshData.mMeshData, inputMeshData.mMaterialIndices, std::span{triangles.begin() + cluster_begin, cluster_size}, adjacency});
        }
    }

    void ClusterTriangles(InputMeshData const& inputMeshData, std::vector<Cluster>& clusters)
    {
        auto const& verts = inputMeshData.mMeshData.mVertexAttribute;
        auto const& indexes = inputMeshData.mMeshData.mIndexes;
        auto triangleNum = inputMeshData.mTriangleNum;

        Adjacency adjacency(static_cast<UInt32>(indexes.size()));
        EdgeHashTable edgeHash;

        auto GetPosition = [&](UInt32 edgeIndex) -> Float3 {
            return verts.mPosition[indexes[edgeIndex]];
        };

        auto BuildEdgeAdjacency = [&] {
            // TODO: parallel this
            for (SInt32 edgeIndex = 0; edgeIndex < static_cast<SInt32>(indexes.size()); edgeIndex++)
            {
                edgeHash.AddEdge(edgeIndex, GetPosition);
            }

            for (SInt32 edgeIndex = 0; edgeIndex < static_cast<SInt32>(indexes.size()); edgeIndex++)
            {
                edgeHash.ForEachMatchingEdge(edgeIndex, GetPosition,
                    [&](SInt32 edgeIndex, SInt32 otherEdgeIndex) {
                        adjacency.Link(edgeIndex, otherEdgeIndex);
                    });
            }
        };


        BuildEdgeAdjacency();

        // DisjointSet disjointSet{inputMeshData.mTriangleNum};
        // for (UInt32 edgeIndex = 0; edgeIndex < indexes.size(); edgeIndex++)
        // {
        //     adjacency.ForEach(edgeIndex, 
        //         [&] (UInt32 edgeIndex0, UInt32 edgeIndex1){
        //             if (edgeIndex0 > edgeIndex1)
        //             {
        //                 disjointSet.Union(edgeIndex0, edgeIndex1);
        //             }
        //         });
        // }

        GraphPartitioner::Graph graph;
        graph.mNum = triangleNum;
        graph.mAdjacencyIndex.resize(triangleNum + 1);
        for (UInt32 i = 0; i < triangleNum; i++)
        {
            graph.mAdjacencyIndex[i] = static_cast<SInt32>(graph.mAdjacency.size());

            for (int j = 0; j < 3; j++)
            {
                // find all adjacency triangle for each edge
                adjacency.ForEach(3 * i + j, 
                    [&](SInt32 edgeIndex, SInt32 adjIndex)
                    {
                        graph.mAdjacency.emplace_back(adjIndex / 3);
                    });
            }
        }
        graph.mAdjacencyIndex[triangleNum] = static_cast<SInt32>(graph.mAdjacency.size());

        GraphPartitioner partitioner{std::move(graph)};
        partitioner.PartitionGraph(ClusterPartitionSetting::maxTrianglesInCluster - 4, ClusterPartitionSetting::maxTrianglesInCluster);

        //UInt32 id = 0;
        //UInt32 maxClusterNum = static_cast<UInt32>(std::min(partitioner.mRanges.size(), 2ull));
        for (auto& range : partitioner.mRanges)
        {
            //if (id++ != maxClusterNum-1)
            //    continue;
            clusters.emplace_back(inputMeshData.mMeshData, inputMeshData.mMaterialIndices, std::span{partitioner.mIndexes.begin() + range.mBegin, range.mEnd - range.mBegin}, adjacency);
        }
    }
}

namespace cross::StellarMesh
{
//struct ClusterBuilder::Impl
//{
//    Impl() = default;
//};

ClusterBuilder::ClusterBuilder()
    //: pImpl(std::make_unique<Impl>())
{}

ClusteredMeshData ClusterBuilder::Build(MeshAssetData* meshAssetData)
{
    InputMeshData inputMeshData{};
    BuildInputMeshData(meshAssetData, inputMeshData);

    ClusteredMeshData clusteredMeshData{};
    ClusterTriangles(inputMeshData, clusteredMeshData.mClusters);
    return clusteredMeshData;
}
}   // namespace cross
