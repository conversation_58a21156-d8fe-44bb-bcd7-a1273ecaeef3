#include "ClusterEncoder.h"
#include "ClusterBuilder.h"
#include <algorithm>

namespace cross::StellarMesh {

struct ClusterEncoder::Impl
{
    // Input
    ClusteredMeshData mClusteredMeshData;
    Impl(ClusteredMeshData data)
        : mClusteredMeshData(std::move(data))
    {}

    ~Impl() = default;
    
    void BuildMaterialRangesFor(Cluster& cluster, std::vector<MaterialTriangle>& materialTriangles)
    {
        Assert(cluster.mNumTris <= ClusterPartitionSetting::maxTrianglesInCluster);
        Assert(cluster.mNumTris * 3 == cluster.mMeshData.mIndexes.size());
        Assert(cluster.mMaterialIndexes.size() * 3 == cluster.mMeshData.mIndexes.size());

        constexpr UInt32 maxMaterialCountsInCluster = 8;

        std::vector<UInt32> materialCounts{};
        materialCounts.resize(maxMaterialCountsInCluster, 0);
        for (UInt32 i = 0; i < cluster.mNumTris; i++)
        {
            const UInt32 materialIndex = cluster.mMaterialIndexes[i];
            if (materialIndex >= maxMaterialCountsInCluster)
            {
                // In the current implementation, each cluster can have up to 8 materials.
                // TODO: If the number of materials is greater than 8, the cluster should be further split or other solutions should be used.
                Assert(false);
            }
            else
            {
                materialCounts[materialIndex]++;
            }
        }

        for (UInt32 i = 0; i < cluster.mNumTris; i++)
        {
            MaterialTriangle materialTri{};
            materialTri.Index0 = cluster.mMeshData.mIndexes[i * 3 + 0];
            materialTri.Index1 = cluster.mMeshData.mIndexes[i * 3 + 1];
            materialTri.Index2 = cluster.mMeshData.mIndexes[i * 3 + 2];
            materialTri.MaterialIndex = cluster.mMaterialIndexes[i];
            materialTri.RangeCount = materialCounts[materialTri.MaterialIndex];
            materialTriangles.push_back(materialTri);
        }

        std::sort(materialTriangles.begin(), materialTriangles.end(), [](const MaterialTriangle& lhs, const MaterialTriangle& rhs) {
            if (lhs.RangeCount != rhs.RangeCount)
            {
                return lhs.RangeCount > rhs.RangeCount;
            }
            return lhs.MaterialIndex < rhs.MaterialIndex;
        });

        MaterialRange currentRange{0, 0, materialTriangles.size() > 0ull ? materialTriangles[0].MaterialIndex : 0u};

        for (SInt32 triIndex = 0; triIndex < static_cast<SInt32>(materialTriangles.size()); triIndex++)
        {
            auto& materialTri = materialTriangles[triIndex];
            if (currentRange.mCount > 0 && currentRange.mMaterialIndex != materialTri.MaterialIndex)
            {
                cluster.mMaterialRanges.emplace_back(currentRange);

                currentRange.mCount = 1;
                currentRange.mBegin = triIndex;
                currentRange.mMaterialIndex = materialTri.MaterialIndex;
            }
            else
            {
                currentRange.mCount++;
            }
        }

        if (currentRange.mCount > 0)
        {
            cluster.mMaterialRanges.emplace_back(currentRange);
        }
    }

    void BuildMaterialRanges()
    {
        std::vector<MaterialTriangle> materialTriangles;
        materialTriangles.reserve(128);
        for (UInt32 clusterIndex = 0; clusterIndex; clusterIndex++)
        {
            materialTriangles.clear();
            auto& cluster = mClusteredMeshData.mClusters[clusterIndex];
            BuildMaterialRangesFor(cluster, materialTriangles);

            for (UInt32 triangleIndex = 0; triangleIndex < cluster.mNumTris; triangleIndex++)
            {
                cluster.mMeshData.mIndexes[triangleIndex * 3 + 0] = materialTriangles[triangleIndex].Index0;
                cluster.mMeshData.mIndexes[triangleIndex * 3 + 1] = materialTriangles[triangleIndex].Index1;
                cluster.mMeshData.mIndexes[triangleIndex * 3 + 2] = materialTriangles[triangleIndex].Index2;
                cluster.mMaterialIndexes[triangleIndex] = materialTriangles[triangleIndex].MaterialIndex;
            }
        }
    }

    void PackResult(GPUClusteredMeshData& gpuMeshData)
    {
        gpuMeshData.mClusters.reserve(mClusteredMeshData.mClusters.size());

        auto const& clusters = mClusteredMeshData.mClusters;

        auto& gpuClusters = gpuMeshData.mClusters;
        auto& clusteredMesh = gpuMeshData.mMeshData;

        UInt32 vertexCount = 0;
        UInt32 triangleCount = 0;

        for (auto& cluster : clusters)
        {
            auto gpuCluster = GPUCluster{};
            gpuCluster.mVertexOffset = vertexCount;
            gpuCluster.mVertexCount = cluster.mNumVerts;
            gpuCluster.mTriangleOffset = triangleCount;
            gpuCluster.mTriangleCount = cluster.mNumTris;
            gpuClusters.push_back(std::move(gpuCluster));

            vertexCount += cluster.mNumVerts;
            triangleCount += cluster.mNumTris;

            clusteredMesh.mIndexes.insert(clusteredMesh.mIndexes.end(), cluster.mMeshData.mIndexes.begin(), cluster.mMeshData.mIndexes.end());
            clusteredMesh.mVertexAttribute.mPosition.insert(clusteredMesh.mVertexAttribute.mPosition.end(), cluster.mMeshData.mVertexAttribute.mPosition.begin(), cluster.mMeshData.mVertexAttribute.mPosition.end());
            clusteredMesh.mVertexAttribute.mNormal.insert(clusteredMesh.mVertexAttribute.mNormal.end(), cluster.mMeshData.mVertexAttribute.mNormal.begin(), cluster.mMeshData.mVertexAttribute.mNormal.end());
            clusteredMesh.mVertexAttribute.mTangent.insert(clusteredMesh.mVertexAttribute.mTangent.end(), cluster.mMeshData.mVertexAttribute.mTangent.begin(), cluster.mMeshData.mVertexAttribute.mTangent.end());
            clusteredMesh.mVertexAttribute.mColor.insert(clusteredMesh.mVertexAttribute.mColor.end(), cluster.mMeshData.mVertexAttribute.mColor.begin(), cluster.mMeshData.mVertexAttribute.mColor.end());
            clusteredMesh.mVertexAttribute.mUV0.insert(clusteredMesh.mVertexAttribute.mUV0.end(), cluster.mMeshData.mVertexAttribute.mUV0.begin(), cluster.mMeshData.mVertexAttribute.mUV0.end());
            clusteredMesh.mVertexAttribute.mUV1.insert(clusteredMesh.mVertexAttribute.mUV1.end(), cluster.mMeshData.mVertexAttribute.mUV1.begin(), cluster.mMeshData.mVertexAttribute.mUV1.end());
        }
    }
};


ClusterEncoder::ClusterEncoder(ClusteredMeshData clusteredMeshData)
    : pImpl(std::make_unique<Impl>(std::move(clusteredMeshData)))
{
}

ClusterEncoder::~ClusterEncoder() = default;

GPUClusteredMeshData ClusterEncoder::Encode()
{
    GPUClusteredMeshData gpuMeshData;
    pImpl->BuildMaterialRanges();
    pImpl->PackResult(gpuMeshData);
    return gpuMeshData;
}

}   // namespace cross::StellarMesh
