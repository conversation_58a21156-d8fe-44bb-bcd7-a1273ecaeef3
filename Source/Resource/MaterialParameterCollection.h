#pragma once
#include "DynamicEnum.h"
#include "Resource/Resource.h"
#include "Resource/ResourceManager.h"
#include "CrossBase/Serialization/SerializeNode.h"
#include "Resource/MaterialDefines.h"
#include "Resource/MaterialInterface.h"
namespace cross::resource {

enum class UsageChangedType
{
    Set, Add, Delete, Clear
};

class ParameterUsageItem
{
public:
    ParameterUsageItem() = default;

    template<typename... Args>
    ParameterUsageItem(const std::string& name, Args&&... args)
        : mName(name)
        , mValue{std::forward<Args>(args)...}
    {}

    inline const std::string& Name() const { return mName; }
    inline const std::vector<float>& Value() const { return mValue; }

public:
    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = ""))
    std::string mName;

    CEMeta(<PERSON><PERSON><PERSON>, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "List", ChildPropertyType = "Auto", ToolTips = ""))
    std::vector<float> mValue;

    CE_Serialize_Deserialize;
};

struct Resource_API ParameterCollectionUsages
{
    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "List", ChildPropertyType = "Auto", ToolTips = ""))
    std::vector<std::string> Members;

    const std::string& operator[](size_t index)
    {
        static std::string empty = "";
        return index < Members.size() ? Members[index] : empty;
    }

    bool HasValue() const
    {
        return !Members.empty();
    }

    bool Find(const NameID& id) const
    {
        return std::find(Members.begin(), Members.end(), id.GetName()) != Members.end();
    }

    void Add(const std::string& item)
    {
        Members.emplace_back(item);
    }

    void Delete(UInt32 index)
    {
        Assert(index < Members.size());
        Members.erase(Members.begin() + index);
    }

    void Clear()
    {
        Members.clear();
    }

    CE_Serialize_Deserialize;
};

struct ScalerParameter
{
    ScalerParameter() = default;

    ScalerParameter(const std::string& name, float value)
        : ParameterName(name)
        , ParameterValue(value) {}

    ScalerParameter(const char* name, float value)
        : ParameterName(std::string(name))
        , ParameterValue(value) {}

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Parameter Name"))
    std::string ParameterName;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Parameter Value"))
    float ParameterValue;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether auto add material's usage"))
    bool AutoUsage = true;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether as a bool switch"))
    bool AsBool = false;

    void SetName(const char* name) { ParameterName = name; }

    void SetValue(float value) { ParameterValue = value; }

    CE_Serialize_Deserialize;
};

struct VectorParameter
{
    VectorParameter() = default;

    VectorParameter(const std::string& name, Float4 value)
        : ParameterName(name)
        , ParameterValue(value) {}

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Parameter Name"))
    std::string ParameterName;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Parameter Value"))
    Float4 ParameterValue;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether auto add material's usage"))
    bool AutoUsage = true;

    void SetName(const char* name) { ParameterName = name; }

    void SetValue(Float4 value) { ParameterValue = value; }

    CE_Serialize_Deserialize;
};

struct MaterialParameterInfo
{
    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", bModified = true, ToolTips = ""))
    std::vector<ScalerParameter> ScalerParameters;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", bModified = true, ToolTips = ""))
    std::vector<VectorParameter> VectorParameters;

    CE_Serialize_Deserialize;
};

class Resource_API CEMeta(Cli, Script) MaterialParameterCollection : public Resource
{
public:
    FRIEND_WITH_REFLECTION_MODULE;
    friend class cross::ResourceManager;
    using PropertyType = std::variant<std::vector<float>, bool, TexturePtr, SamplerState>;
    using PropertyName = std::variant<std::string, std::string_view>;

protected:
    MaterialParameterCollection() = default;

public:
    virtual ~MaterialParameterCollection() = default;

    static int GetClassIDStatic()
    {
        return ClassID(MaterialParameterCollection);
    }
    ///EditorInterfaces
    CEMeta(Cli)
    static void MPC_SetScalerParameter(cross::resource::MaterialParameterCollection * mpc, int index, const char* name, float value, bool usage, bool asbool);
    CEMeta(Cli)
    static void MPC_AddScalerParameter(cross::resource::MaterialParameterCollection * mpc, const char* name, float value);
    CEMeta(Cli)
    static void MPC_DeleteScalerParameter(cross::resource::MaterialParameterCollection * mpc, int index);
    CEMeta(Cli)
    static void MPC_ClearScalerParameter(cross::resource::MaterialParameterCollection * mpc);
    CEMeta(Cli)
   static void MPC_SetVectorParameter(cross::resource::MaterialParameterCollection * mpc, int index, const char* name, cross::Float4* value);
    CEMeta(Cli)
    static void MPC_SetVectorParameterAutoUsage(cross::resource::MaterialParameterCollection * mpc, int index, bool usage);
    CEMeta(Cli)
    static void MPC_AddVectorParameter(cross::resource::MaterialParameterCollection * mpc, const char* name, cross::Float4* value);
    CEMeta(Cli)
    static void MPC_DeleteVectorParameter(cross::resource::MaterialParameterCollection * mpc, int index);
    CEMeta(Cli)
    static void MPC_ClearVectorParameter(cross::resource::MaterialParameterCollection * mpc);
    CEMeta(Cli)
    static MaterialParameterCollection* MPC_CreateMaterialParameterCollection();
    ///EditorInterfaces

    // Use for CrossEditor only
    void SetScalerParameter(SInt32 index, const PropertyName& name, float value, bool usage, bool asbool);

    void AddScalerParameter(const PropertyName& name, float value);

    void DeleteScalerParameter(SInt32 index);

    void ClearScalerParameter();

    // Use for CrossEditor only
    void SetVectorParameter(SInt32 index, const std::string& name, Float4 value);

    void AddVectorParameter(const std::string& name, Float4 value);

    void DeleteVectorParameter(SInt32 index);

    void ClearVectorParameter();

    void SetVectorParameterAutoUsage(SInt32 index, bool usage);

    PropertyType GetParameterValue(const NameID& name);

    std::vector<float> GetNumericParameterValue(const NameID& name);

    // Only for material v1.0
    void RegisterMaterialHolder(Material* mat);
    void UnRegisterMaterialHolder(Material* mat);

    // For material v2.0
    void RegisterMaterialInterfaceHolder(MaterialInterfacePtr mtl);
    void UnRegisterMaterialInterfaceHolder(MaterialInterfacePtr mtl);

    template<typename T>
    void SetParameter(const NameID& name, T value)
    {
        if constexpr (std::is_same<T, float>::value)
        {
            auto parameters = &mParameterInfo.ScalerParameters;
            auto it = std::find_if(parameters->begin(), parameters->end(), [&name](const ScalerParameter& param) { return param.ParameterName == name; });
            if (it != parameters->end())
            {
                it->SetValue(value);
                FlushMaterials(UsageChangedType::Set, name.GetName());
            }
        }
        else if constexpr (std::is_same<T, Float4>::value)
        {
            auto parameters = &mParameterInfo.VectorParameters;
            auto it = std::find_if(parameters->begin(), parameters->end(), [&name](const VectorParameter& param) { return param.ParameterName == name; });
            if (it != parameters->end())
            {
                it->SetValue(value);
                FlushMaterials(UsageChangedType::Set, name.GetName());
            }
        }
    }

    template<typename T>
    T* GetParameter(const NameID& name)
    {
        std::vector<T>* parameters = nullptr;
        if constexpr (std::is_same<T, ScalerParameter>::value)
        {
            parameters = &mParameterInfo.ScalerParameters;
        }
        else if constexpr (std::is_same<T, VectorParameter>::value)
        {
            parameters = &mParameterInfo.VectorParameters;
        }

        if (parameters)
        {
            auto it = std::find_if(parameters->begin(), parameters->end(), [&name](const T& param) { return param.ParameterName == name.GetName(); });
            if (it != parameters->end())
            {
                return &(*it);
            }
        }

        return nullptr;
    }

    inline const MaterialParameterInfo& GetParameterInfo() const { return mParameterInfo; }

    inline bool IsEmpty() const { return mParameterInfo.ScalerParameters.empty() && mParameterInfo.VectorParameters.empty(); }

private:
    void FlushMaterials(UsageChangedType type, const char* name = nullptr, SInt32 index = -1);

public:
    bool Serialize(SerializeNode&& s, const std::string& path) override;
    bool Deserialize(const DeserializeNode& s) override;

private:

   
    MaterialParameterInfo mParameterInfo;

    // the mMaterialHolders and mMaterialInterfaceHolder are same in semantics
    // ie, MPC should broadcast to material referenced material/fx
    // except that, the material and the material interface(material blueprint) are different version.

    std::mutex mMaterialHolderMutex;
    std::unordered_set<Material*> mMaterialHolders;

    std::mutex mFxHolderMutex;
    std::unordered_set<ResourceWeakPtr<MaterialInterface>, ResourceWeakPtr<MaterialInterface>::Hash> mMaterialInterfaceHolder;
};

struct MaterialParameterCollectionInfo
{
    std::string mName;  // name created by fx when serialized, should be like "MPC1", "MPC2", ...
    std::string mPath;   
    MPCPtr mMpcSourcePtr;
    std::vector<std::string> mUsedParameters;
    bool mErrorFlag = false;
};

struct MaterialParameterCollectionUsage
{
    std::string_view mPath;
    std::set<std::string> mUsedParameters;
};

class Resource_API MaterialParameterCollectionUsageInfo
{
public:
    uint32_t AddParameterCollectionParameter(std::string_view collectionPath, std::string_view parameterName);

    const auto& GetParameterCollectionUsage() const { return m_ParameterCollections; }

private:
    std::vector<MaterialParameterCollectionUsage> m_ParameterCollections;
};

}   // end namespace cross::resource