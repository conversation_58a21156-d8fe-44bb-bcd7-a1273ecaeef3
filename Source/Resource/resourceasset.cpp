#include "EnginePrefix.h"

#include "NativeGraphicsInterface/NGI.h"

#include "resourceasset.h"
#include "Resource/ResourceManager.h"
#include "Resource/MeshAssetDataResource.h"   // TEMP
#include "Resource/Texture/TextureCube.h"     // TEMP
#include "Resource/Texture/TextureUDIM.h"
#include "Resource/ScriptResource.h"
#include "Resource/TsResource.h"
#include "Runtime/Interface/CrossEngine.h"
#include "Runtime/Interface/CrossEngineImp.h"
#include "Resource/BaseClasses/ClassIDs.h"
#include "Resource/DataCompressor.h"
#include "Resource/FetchResource.h"
#include "FileSystem/memorymappingfile.h"

#if defined(_WIN64)
#    define CESTAT         _stat64
#    define CESTAT_STRUCT struct _stat64
#elif defined(WIN32)
#    define CESTAT         stat
#    define CESTAT_STRUCT  struct stat
#else
#    define CESTAT         stat
#    define CESTAT_STRUCT  struct stat
#endif

namespace cross { namespace resource {
    static constexpr char LZ4[] = {'L', 'Z', '4', '#'};
    inline bool IsResourceSuppotStreaming(ClassIDType ClassID)
    {
        return ClassID == ClassID(Texture2D);
    }
    //=============LoadNDAFileInfo//
    LoadNDAFileInfo::LoadNDAFileInfo(const ResourceMetaHeader& header)
        : mMetaHeader(header)
        , mHasMetaHeader(true)
        , mOffset(header.mJsonStringLength)
    {}

    LoadNDAFileInfo::LoadNDAFileInfo(const AssetHeader& header)
        : mHasMetaHeader(false)
        , mOffset(0)
    {
        mMetaHeader.mMagicNumber = header.mMagicNumber;
        mMetaHeader.mVersion = header.mVersion;
        mMetaHeader.mClassID = header.mClassID;
        mMetaHeader.mDataSize = header.mDataSize;
        mMetaHeader.mContentType = (UInt32)CONTENT_TYPE::CONTENT_TYPE_FLATBUFFER;
    }

    LoadNDAFileInfo::LoadNDAFileInfo(const CrossSchema::ResourceHeader& header)
        : mHasMetaHeader(false)
        , mOffset(0)
    {
        mMetaHeader.mMagicNumber = header.magicnumber();
        mMetaHeader.mVersion = header.version();
        mMetaHeader.mClassID = header.classid();
        mMetaHeader.mDataSize = header.datasize();
        mMetaHeader.mContentType = (UInt32)CONTENT_TYPE::CONTENT_TYPE_FLATBUFFER;
    }

    //=============Asset//
    Asset::Asset(const std::string& inAssetName)
        : Named(std::move(inAssetName))
    {}

    void Asset::MapToGuid()
    {
        auto fileSystem = EngineGlobal::GetFileSystem();
        mGuid = gResourceMgr.GetGuidByPath(fileSystem->GetRelativePath(fileSystem->GetAbsolutePath(mName)));
    }

    void Asset::EnsureGuid()
    {
        if (!CrossUUID::CheckVaid(mGuid))
        {
            auto fileSystem = EngineGlobal::GetFileSystem();
            mGuid = gResourceMgr.GenFileGuid(fileSystem->GetRelativePath(fileSystem->GetAbsolutePath(mName)));
        }
    }

    bool Asset::CheckReference(const AssetPtr& asset)
    {
        if (asset)
        {
            if (!asset->mGuid.empty() && mReferences.find(asset->mGuid) != mReferences.end())
            {
                return true;
            }
            if (!asset->mName.empty() && mReferences.find(asset->mName) != mReferences.end())
            {
                return true;
            }
        }
        return false;
    }


    Asset::Asset(const std::string& inAssetName, const std::string& inOriAssetName)
        : Named(inAssetName, inOriAssetName)
    {}

    bool Asset::NeedReload()
    {
        std::string absolutePath{};
        cross::EngineGlobal::Inst().GetFileSystem()->GetAbsolutePath(mName, absolutePath);
        CESTAT_STRUCT result;
        if (CESTAT(absolutePath.c_str(), &result) == 0)
        {
            // skip
        }
        else
        {
            Assert(0);
        }

        return result.st_mtime == mStModifyTime ? false : true;
    }

    //============ ResourceAssetManager start //
    ResourceAssetManager& ResourceAssetManager::Instance()
    {
        static ResourceAssetManager sInstance;
        return sInstance;
    }

    FileArchive* ResourceAssetManager::OpenAssetFile(const char* fileName, bool useStream)
    {
        std::string theabsolutePath{};
        cross::EngineGlobal::Inst().GetFileSystem()->GetAbsolutePath(fileName, theabsolutePath);
        std::string relativeFileName{};
        cross::EngineGlobal::Inst().GetFileSystem()->GetRelativePath(theabsolutePath, relativeFileName);

        auto fp = mFileSystem->Open(relativeFileName, useStream || mUseStreamMode);
        if (!fp)
        {
            // AssertMsg(0, "the {} file is not exist, please check the path", fileName);
            return nullptr;
        }
        FileArchive* fileArch = new FileArchive(fp);
        return fileArch;
    }

    Archive* ResourceAssetManager::MemoryMapFile(const char* fileName, UInt64 mappedbytes)
    {
        std::string theabsolutePath{};
        cross::EngineGlobal::Inst().GetFileSystem()->GetAbsolutePath(fileName, theabsolutePath);
        std::string relativeFileName{};
        cross::EngineGlobal::Inst().GetFileSystem()->GetRelativePath(theabsolutePath, relativeFileName);
        auto mmfile = mFileSystem->MemoryMapping(relativeFileName, mappedbytes, filesystem::CacheHint::RandomAccess);
        if (!mmfile)
        {
            return nullptr;
        }
        MemoryMappingArchive* mmArch = new MemoryMappingArchive(std::unique_ptr<filesystem::MemoryMappingFile>(dynamic_cast<filesystem::MemoryMappingFile*>(mmfile)));  
        bool isCompressed = *reinterpret_cast<const UInt32*>(mmArch->Data()) == *reinterpret_cast<const UInt32*>(LZ4);
        if (isCompressed)
        {
            auto fp = mFileSystem->Open(fileName, mUseStreamMode);
            auto fileSize = fp->GetSize();
            UInt8* buff = new UInt8[fileSize];
            fp->Read(reinterpret_cast<char*>(buff), fileSize);
            fp->Close();
            BinaryArchive* fileData = new cross::BinaryArchive(buff, fileSize);
            // decompress data
            if (auto decompressData = gDataCompressor->TryDeCompress(fileData->Data(), fileData->Size()))
            {
                delete fileData;
                fileData = decompressData;
                return fileData;
            }
        }
        return mmArch;
    }

    BinaryArchive* ResourceAssetManager::ReadAssetFile(const char* fileName)
    {
        resource::FetchResource::CheckAndFetch(fileName);
        auto fp = mFileSystem->Open(fileName, mUseStreamMode);
        if (!fp)
        {
            // AssertMsg(0, "the {} file is not exist, please check the path", fileName);
            return nullptr;
        }
        auto fileSize = fp->GetSize();
        UInt8* buff = new UInt8[fileSize];
        fp->Read(reinterpret_cast<char*>(buff), fileSize);
        BinaryArchive* fileData = new cross::BinaryArchive(buff, fileSize);
        // decompress data
        if (auto decompressData = gDataCompressor->TryDeCompress(fileData->Data(), fileData->Size()))
        {
            delete fileData;
            fileData = decompressData;
        }
        fp->Close();
        return fileData;
    }

    BinaryArchive* ResourceAssetManager::DecompressFileData(Archive* fileArch, bool& ret, UInt32 offet)
    {
        BinaryArchive* fileData = new BinaryArchive(const_cast<UInt8*>(fileArch->Data() + offet), fileArch->Size() - offet);
        // decompress data
        if (auto decompressData = gDataCompressor->TryDeCompress(fileData->Data(), fileData->Size()))
        {
            fileData = decompressData;
            ret = true;
        }
        else
        {
            ret = false;
        }
        return fileData;
    }

    bool ResourceAssetManager::GetResourceMetaHeader(const char* fileName, ResourceMetaHeader& outHeader)
    {
        FileArchive* fileArch = OpenAssetFile(fileName, true);
        if (!fileArch)
            return false;
        bool ret = GetResourceMetaHeader(fileName, fileArch, outHeader);
        delete fileArch;
        return ret;
    }

    bool ResourceAssetManager::GetResourceMetaHeader(const char* fileName, Archive* fileArch, ResourceMetaHeader& outHeader)
    {
        if (fileArch->Size() <= sizeof(UInt32))
            return false;
        UInt64 originTell = fileArch->Tell();

        UInt32 firstUInt32;
        fileArch->Read(&firstUInt32, sizeof(UInt32));
        fileArch->Seek(originTell);
        if (firstUInt32 == ASSET_MAGIC_NUMBER_JMETA)
            return GetJsonResourceMetaHeader(fileName, fileArch, outHeader);
        else if (firstUInt32 == ASSET_MAGIC_NUMBER_BMETA)
            return GetBinJsonResourceMetaHeader(fileName, fileArch, outHeader);
        else
            return GetOldResourceMetaHeader(fileName, fileArch, outHeader);
    }

    bool ResourceAssetManager::GetLoadNDAInfo(const char* fileName, LoadNDAFileInfo& outInfo)
    {
        // note that this function is usually called from editor's get_resourceType
        // in best case, it should only read the header content and header is not compressed
        // but in current day (2022/05/07), resource content is very chaos
        // it could be :
        // FB without header
        // with Header not compressed
        // all compressed with header
        // header is not compressed, only content is compressed
        // so we need read/uncompress  the whole asset to get correct LoadNDAFileInfo, aka for the metaheader info
        // Map a big enough size for read header
        Archive* mmArch = MemoryMapFile(fileName, ResourceManager::HeadMapSize);
        if (!mmArch)
        {
            delete mmArch;
            return false;
        }     
        outInfo.SetFileName(fileName);
        bool ret = GetLoadNDAInfo(mmArch, outInfo);
        delete mmArch;
        return ret;
    }

    bool ResourceAssetManager::GetLoadNDAInfo(Archive* fileArch, LoadNDAFileInfo& outInfo)
    {
        return GetMetaHeaderLoadNDAInfo(fileArch, outInfo) || GetFBHeaderLoadNDAInfo(fileArch, outInfo);
    }

    ResourceAssetManager::ResourceAssetManager()
    {
        filesystem::FileSystem* pFileSystem = EngineGlobal::Inst().GetFileSystem();
        Assert(pFileSystem);
        mFileSystem = pFileSystem;

        EngineGlobal::GetSettingMgr()->GetValue<bool>("UseFileSystemUseStreamMode", mUseStreamMode);
    }
    
    bool ResourceAssetManager::GetMetaHeaderLoadNDAInfo(Archive* fileArch, LoadNDAFileInfo& outInfo)
    {
        ResourceMetaHeader metaHeader;
        if (GetResourceMetaHeader(outInfo.GetFileName().c_str(), fileArch, metaHeader))
        {
            outInfo = metaHeader;
            return true;
        }
        return false;
    }

    bool ResourceAssetManager::GetFBHeaderLoadNDAInfo(Archive* fileArch, LoadNDAFileInfo& outInfo)
    {
        if (IsScriptFile(outInfo.GetFileName()) || !IsNDAFile(outInfo.GetFileName()))
        {
            return false;
        }
        auto mmarch = dynamic_cast<MemoryMappingArchive*>(fileArch);
        FBSerializer fbserializer{*mmarch};
        CrossSchema::ResourceHeader resourceHeader;
        auto table = CrossSchema::GetResourceAsset(fbserializer.GetCachedBuffer().data());
        if (fbserializer.Read(table, resourceHeader, CrossSchema::ResourceAsset::VT_HEADER))
        {
            outInfo = resourceHeader;
            return true;
        }
        return false;
    }

    ResourcePtr ResourceAssetManager::LoadNDAFile(const char* fileName, ResourceLoadError& loadError)
    {
        Assert(PathHelper::IsAbsoluteFilePath(fileName) == false);
        // read file data
        auto fileData = MemoryMapFile(fileName, ResourceManager::HeadMapSize);
        if (!fileData)
            return nullptr;
        LoadNDAFileInfo fileInfo;
        fileInfo.SetFileName(fileName);
        if (!GetLoadNDAInfo(fileData, fileInfo))
        {
            delete fileData;
            return nullptr;
        }
        fileInfo.SetFileName(fileName);

        auto retResource = LoadNDAFile(fileData, fileInfo);

        delete fileData;
        return retResource;
    }

    bool ResourceAssetManager::LoadNDAFile(const ResourcePtr& inResource, Archive* fileArch, const LoadNDAFileInfo& fileInfo)
    {
        // decompress content data
        UInt32 offset = fileInfo.GetOffet();
        bool decompressed = false;
        auto mmarch = dynamic_cast<MemoryMappingArchive*>(fileArch);
        if (!mmarch)
        {
            LOG_ERROR("Use memory mapping for IO");
        }
        // set base
        inResource->SetResourceVersion(fileInfo.GetVersion());
        inResource->SetOriginAssetPath(fileInfo.GetFileName());
        inResource->SetResourceSize(static_cast<UInt32>(fileArch->Size()));
        inResource->mAsset->SetHeader(fileInfo.GetMetaHeader());

        switch (fileInfo.GetContentType())
        {
        case CONTENT_TYPE::CONTENT_TYPE_SIMPLE:
        {
            mmarch->Remap(0, mmarch->FileSize());
            BinaryArchive* fileData = DecompressFileData(mmarch, decompressed, offset);
            SimpleSerializer serializer{*fileData, sizeof(AssetHeader)};
            inResource->Deserialize(serializer);
            DELETE_BINARYDATA(fileData, !decompressed);
            break;
        }
        case CONTENT_TYPE::CONTENT_TYPE_FLATBUFFER:
        {
            if (mmarch && (!IsResourceSuppotStreaming(static_cast<ClassIDType>(inResource->GetClassID())) || !inResource->GetUseStreaming()))
            {
                fileArch->Remap(0, mmarch->FileSize()); 
            }
            else
            {
                fileArch->Remap(0, offset + 2048);
            }
            bool isCompressed = *reinterpret_cast<const UInt32*>(fileArch->Data() + offset) == *reinterpret_cast<const UInt32*>(LZ4);
            if (isCompressed)
            {
                BinaryArchive* fileData = DecompressFileData(fileArch, decompressed, offset);
                FBSerializer serializer{*fileData, offset};
                std::string strOriginFileName = "";
                CrossSchema::ResourceType resourceType = CrossSchema::ResourceType::NONE;
                CrossSchema::ResourceHeader resourceHeader;

                auto table = CrossSchema::GetResourceAsset(serializer.GetArchive().Data() + offset);
                serializer.Read(table, resourceHeader, CrossSchema::ResourceAsset::VT_HEADER);
                serializer.Read(table, strOriginFileName, CrossSchema::ResourceAsset::VT_NAME);
                serializer.Read(table, resourceType, CrossSchema::ResourceAsset::VT_RESOURCE_TYPE);
                serializer.GetArchive().Seek(offset);
                Assert(resourceType != CrossSchema::ResourceType::NONE);
                inResource->SetOriginAssetPath(strOriginFileName);
                inResource->SetResourceType(resourceType);
                inResource->Deserialize(serializer);
                DELETE_BINARYDATA(fileData, !decompressed);
            }
            else
            {
                FBSerializer serializer{*fileArch, offset};
                std::string strOriginFileName = "";
                CrossSchema::ResourceType resourceType = CrossSchema::ResourceType::NONE;
                CrossSchema::ResourceHeader resourceHeader;

                auto table = CrossSchema::GetResourceAsset(serializer.GetArchive().Data() + offset);
                serializer.Read(table, resourceHeader, CrossSchema::ResourceAsset::VT_HEADER);
                serializer.Read(table, strOriginFileName, CrossSchema::ResourceAsset::VT_NAME);
                serializer.Read(table, resourceType, CrossSchema::ResourceAsset::VT_RESOURCE_TYPE);
                serializer.GetArchive().Seek(offset);
                Assert(resourceType != CrossSchema::ResourceType::NONE);
                inResource->SetOriginAssetPath(strOriginFileName);
                inResource->SetResourceType(resourceType);
                inResource->Deserialize(serializer);
            }
            break;
        }
        case CONTENT_TYPE::CONTENT_TYPE_JSON:
        {
            mmarch->Remap(0, mmarch->FileSize());
            BinaryArchive* fileData = DecompressFileData(mmarch, decompressed, offset);
            UInt64 fileSize = fileData->Size();
            std::string str(fileSize, '\0');
            void* strtAddress = str.data();
            fileData->Read(strtAddress, fileSize);

            bool success = false;
            DeserializeNode jData = DeserializeNode::ParseFromJson(str, &success);
            Assert(success);
            if (fileInfo.HasMetaHeader())
            {
                for (auto& path : fileInfo.GetMetaHeader().mDependency)
                {
                    inResource->AddReferenceResource(path);
                }
            }
            inResource->Deserialize(jData);
            DELETE_BINARYDATA(fileData, !decompressed);
           // LOG_INFO("memory pool size is :{}- {}", threading::TaskSystem::GetThreadIndexForAsync(), DeserializeNode::GetAlloc().Size());
            break;
        }
        case CONTENT_TYPE::CONTENT_TYPE_BSON:
        {
            mmarch->Remap(0, mmarch->FileSize());
            BinaryArchive* fileData = DecompressFileData(mmarch, decompressed, offset);
            UInt64 fileSize = fileData->Size();
            DeserializeNode jData = DeserializeNode::ParseFromBin(fileData->Data(), fileSize);
            if (fileInfo.HasMetaHeader())
            {
                for (auto& path : fileInfo.GetMetaHeader().mDependency)
                {
                    inResource->AddReferenceResource(path);
                }
            }
            inResource->Deserialize(SerializeNode::CreateFromDeserializeNode(jData));
            DELETE_BINARYDATA(fileData, !decompressed);
            break;
        }
        default:
            break;
        };
        // delete decompressed data
        return true;
    }
    
    ResourcePtr ResourceAssetManager::LoadNDAFile(Archive* archive, const LoadNDAFileInfo& fileInfo)
    {
        SCOPED_CPU_TIMING(GroupAssetStreaming, "DeserializeResource");
        ResourcePtr retResource = ResourcePtr(TYPE_CAST(Resource*, ResourceBase::Produce(fileInfo.GetClassID())));
        retResource->SetUseStreaming(fileInfo.GetMetaHeader().mIsStreamFile);
        retResource->CreateAsset(fileInfo.GetFileName(), fileInfo.GetMetaHeader().mGuid);

        // Switch to deserialize customed header first then content
        if (fileInfo.HasMetaHeader() && fileInfo.GetMetaHeader().mCustomInfo != "")
        {
            bool result = false;
            DeserializeNode customNode = SerializeNode::ParseFromJson(fileInfo.GetMetaHeader().mCustomInfo, &result);
            Assert(result);
            retResource->DeserializeCustomNode(customNode);
        }
        LoadNDAFile(retResource, archive, fileInfo);

        // get the file modify time
        {
            std::string absolutePath;
            cross::EngineGlobal::Inst().GetFileSystem()->GetAbsolutePath(fileInfo.GetFileName(), absolutePath);
            PathHelper::Normalize(absolutePath);
            CESTAT_STRUCT result;
            if(CESTAT(absolutePath.c_str(), &result) == 0)
                retResource->mAsset->SetModiyTime(result.st_mtime);
            retResource->mAsset->SetHeader(fileInfo.GetMetaHeader());
        }

        return retResource;
    }

    bool ResourceAssetManager::LoadNDAResource(const ResourcePtr& inResource)
    {
        std::string absolutePath;
        cross::EngineGlobal::Inst().GetFileSystem()->GetAbsolutePath(inResource->GetName(), absolutePath);
        auto fileData = MemoryMapFile(absolutePath.c_str(), ResourceManager::HeadMapSize);
        if (!fileData)
            return false;
        LoadNDAFileInfo fileInfo;
        fileInfo.SetFileName(inResource->GetName());
        if (!GetLoadNDAInfo(fileData, fileInfo))
        {
            delete fileData;
            return false;
        }
        inResource->CreateAsset(inResource->GetName());

        // Deserialize custom node
        if (fileInfo.HasMetaHeader() && fileInfo.GetMetaHeader().mCustomInfo != "")
        {
            bool result = false;
            DeserializeNode customNode = SerializeNode::ParseFromJson(fileInfo.GetMetaHeader().mCustomInfo, &result);
            Assert(result);
            inResource->DeserializeCustomNode(customNode);
        }

        LoadNDAFile(inResource, fileData, fileInfo);
        delete fileData;
        return true;
    }

    ResourcePtr ResourceAssetManager::AsyncLoadNDAFile(const char* fileName, ResourceLoadError& loadError, const threading::TaskEventArray& preloadEvents)
    {
        auto fileData = MemoryMapFile(fileName, ResourceManager::HeadMapSize);
        if (!fileData)
            return nullptr;
        LoadNDAFileInfo fileInfo;
        if (!GetLoadNDAInfo(fileData, fileInfo))
        {
            delete fileData;
            return nullptr;
        }
        ResourcePtr resource = ResourcePtr(TYPE_CAST(Resource*, ResourceBase::Produce(fileInfo.GetClassID())));
        auto asyncLoadEvent = threading::Async(preloadEvents, [this, resource, fileData, &fileInfo](const auto&) {
            ResourceLoadError loadError = ResourceLoadError::Succeeded;
            LoadNDAFile(resource, fileData, fileInfo);
            delete fileData;
        });
        resource->SetAsyncLoadEvent(asyncLoadEvent);
        return resource;
    }

    std::string ResourceAssetManager::LoadScriptFileData(const char* fileName)
    {
        std::string scriptFile = fileName;
        if (StringHelper::EndsWith(fileName, ".mts"))
        {
            scriptFile = cross::StringHelper::Replace(fileName, "/TypeScript/", "/JavaScript/");
            scriptFile = cross::StringHelper::Replace(scriptFile, ".mts", ".mjs");
        }

        std::string fileContent;

        filesystem::IFilePtr file = mFileSystem->Open(scriptFile);
        if (!file)
        {
            LOG_ERROR("LoadScriptFile open file [ {} ] failed!", scriptFile);
            return {};
        }

        UInt64 fileSize = (UInt64)file->GetSize();
        fileContent.resize(fileSize);
        file->Read(fileContent.data(), fileSize);
        return fileContent;
    }

    ResourcePtr ResourceAssetManager::LoadScriptFile(const char* fileName, ResourceLoadError& loadError)
    {
        loadError = ResourceLoadError::Succeeded;
        std::string fileContent = LoadScriptFileData(fileName);
        if (fileContent.empty())
        {
            loadError = ResourceLoadError::OpenFileFailed;
            return nullptr;
        }

        // load script to script engine
        if (cross::IsTSFile(fileName))
        {
            {
                TsPtr tsResource = TsPtr(TYPE_CAST(TsResource*, (ResourceBase::Produce(ClassID(TsResource)))));
                std::string strFileName(fileName);
                tsResource->CreateAsset(strFileName);
                tsResource->SetContent(std::move(fileContent));
                return tsResource;
            }
        }
    #if SUPPORT_LUA
        else
        {
            {
                std::unique_ptr<ScriptResource> scriptResource{static_cast<ScriptResource*>(ResourceBase::Produce(ClassID(ScriptResource)))};
                Assert(scriptResource);
                std::string strFileName(fileName);
                scriptResource->CreateAsset(strFileName);
                scriptResource->SetContent(std::move(fileContent), std::move(strFileName));

                // return a valid script resource
                return scriptResource.release();
            }
        }
    #else
        return nullptr;
    #endif
    }

    bool ResourceAssetManager::LoadScriptResource(const ResourcePtr& inResource)
    {
        auto fileName = inResource->GetName();
        std::string fileContent = LoadScriptFileData(fileName.c_str());
        if (fileContent.empty())
            return false;

        if (inResource->GetClassID() == ClassID(TsResource))
        {
            TsPtr tsResource = TypeCast<TsResource>(inResource);
            tsResource->SetContent(std::move(fileContent));
        }
#if SUPPORT_LUA
        else if (inResource->GetClassID() == ClassID(ScriptResource))
        {
            ScriptResource* scriptResource = dynamic_cast<ScriptResource*>(inResource);
            scriptResource->SetContent(std::move(fileContent), std::move(fileName));
        }
#endif
        return true;
    }


    bool ResourceAssetManager::GetJsonResourceMetaHeader(const char* fileName, Archive* fileArch, ResourceMetaHeader& outHeader)
    {
        // read header
        UInt64 originTell = fileArch->Tell();
        fileArch->Seek(sizeof(ASSET_MAGIC_NUMBER_JMETA) + 1);
        std::string jsonStr;
        int num = -1;
        do
        {
            auto readSize = std::min((UInt64)512, fileArch->Size() - fileArch->Tell());
            if (readSize <= 0)
            {
                // Special case for remap
                readSize = 512;
                auto mmarchive = dynamic_cast<MemoryMappingArchive*>(fileArch);
                if (mmarchive)
                {
                    mmarchive->Remap(0, readSize + fileArch->Size());
                }
                else
                {
                    return false;
                }
            }
            char* buffer = new char[readSize + 1];
            fileArch->Read(buffer, readSize);

            int index = 0;
            while (num != 0 && index < readSize)
            {
                if (buffer[index] == '{')
                {
                    num = num < 0 ? 1 : num + 1;
                }
                else if (buffer[index] == '}')
                {
                    num -= 1;
                }
                index++;
            }
            buffer[index] = '\0';
            jsonStr += buffer;
            delete[] buffer;
        } while (num != 0);
        // read json
        bool success = false;
        DeserializeNode headerNode = DeserializeNode::ParseFromJson(jsonStr, &success);
        Assert(success);
        // parse
        fileArch->Seek(sizeof(ASSET_MAGIC_NUMBER_JMETA) + jsonStr.length() + 1);
        outHeader.mMagicNumber = ASSET_MAGIC_NUMBER_JMETA;
        bool ret = DeserializeResourceMetaHeader(fileName, headerNode, outHeader);
        SInt32 readByteNum = sizeof(ASSET_MAGIC_NUMBER_JMETA) + static_cast<UInt32>(jsonStr.length()) + 1;
        outHeader.mJsonStringLength = outHeader.mVersion == Resource::gResourceJsonHeaderVersion ? readByteNum + 1 : readByteNum;
        return ret;
    }

    bool ResourceAssetManager::GetBinJsonResourceMetaHeader(const char* fileName, Archive* fileArch, ResourceMetaHeader& outHeader)
    {
        UInt64 originTell = fileArch->Tell();
        fileArch->Seek(sizeof(ASSET_MAGIC_NUMBER_BMETA));
        // read header
        SizeType headerSize = 0;
        fileArch->Read(&headerSize, sizeof(headerSize));
        if (fileArch->Size() < headerSize + sizeof(ASSET_MAGIC_NUMBER_BMETA))
            return false;
        UInt8* header = new UInt8[headerSize];
        fileArch->Read(header, headerSize);
        DeserializeNode headerNode = DeserializeNode::ParseFromBin(header, headerSize);
        // parse
        outHeader.mMagicNumber = ASSET_MAGIC_NUMBER_BMETA;
        outHeader.mJsonStringLength = static_cast<SInt32>(fileArch->Tell() - originTell);
        bool ret = DeserializeResourceMetaHeader(fileName, headerNode, outHeader);
        delete[] header;
        return ret;
    }

    bool ResourceAssetManager::GetOldResourceMetaHeader(const char* fileName, Archive* fileArch, ResourceMetaHeader& outHeader)
    {
        if (fileArch->Size() <= BINARY_HEADER_SIZE)
            return false;
        fileArch->Read(&outHeader, BINARY_HEADER_SIZE);
        // support meta header
        if (outHeader.mMagicNumber == ASSET_MAGIC_NUMBER && outHeader.mVersion > 3 && outHeader.mJsonStringLength > 0)
        {
            std::string headerData(outHeader.mJsonStringLength, '\0');
            fileArch->Read(headerData.data(), outHeader.mJsonStringLength);
            bool jsonSuccess = false;
            DeserializeNode jsonData = DeserializeNode::ParseFromJson(headerData, &jsonSuccess);
            Assert(jsonSuccess);
            if (auto arr = jsonData.HasMember("dependency"); arr && arr->IsArray())
            {
                for (int i = 0; i < arr->Size(); i++)
                {
                    auto dependItem = (*arr)[i].AsString();
                    if (!mCheckDependFile || gResourceMgr.CheckFileByGuid(dependItem))
                    {
                        outHeader.mDependency.emplace_back(dependItem);
                    }
                    else
                    {
                        LOG_ERROR("{0} Miss depend file {1}", fileName, dependItem);
                        // Assert(0);
                    }
                }
            }
            if (auto custom = jsonData.HasMember(Resource::Custom_KEY); custom && !custom->IsNull())
            {
                outHeader.mCustomInfo = custom->FormatToJson();
            }
            outHeader.mJsonStringLength += BINARY_HEADER_SIZE;   // mJsonStringLength use to calculate offset
            return true;
        }
        return false;
    }

    bool ResourceAssetManager::DeserializeResourceMetaHeader(const char* fileName, const DeserializeNode& headerNode, ResourceMetaHeader& outHeader)
    {
        outHeader.mVersion = headerNode[Resource::Version_KEY].AsInt32();
        outHeader.mClassID = headerNode[Resource::ClassID_KEY].AsInt32();
        outHeader.mDataSize = headerNode[Resource::DataSize_KEY].AsInt32();
        outHeader.mContentType = headerNode[Resource::ContentType_KEY].AsInt32();
        if (auto guid = headerNode.HasMember(Resource::Guid_KEY); guid && guid->IsString())
        {
            outHeader.mGuid = guid->AsString();
        }
        // custom key
        if (auto custom = headerNode.HasMember(Resource::Custom_KEY); custom && !custom->IsNull())
        {
            if (headerNode.IsBinMode())
            {
                outHeader.mCustomInfo = SerializeNode::CreateFromDeserializeNode(*custom).FormatToJson();
            }
            else
            {
                outHeader.mCustomInfo = custom->FormatToJson();
            }
        }
        // dependency
        if (auto arr = headerNode.HasMember(Resource::Dependence_KEY); arr && arr->IsArray())
        {
            for (size_t i = 0; i < arr->Size(); ++i)
            {
                if (auto dependItem = (*arr)[i].AsString(); !dependItem.empty())
                {
                    if (!mCheckDependFile || gResourceMgr.CheckFileByGuid(dependItem))
                    {
                        outHeader.mDependency.emplace_back(dependItem);
                    }
                    else
                    {
                        LOG_ERROR("{0} Miss depend file {1}", fileName, dependItem);
                    }
                }
            }
        }
        // import setting
        if (auto set = headerNode.HasMember(Resource::ImportSet_KEY); set && !set->IsNull())
        {
            outHeader.mImportSet = set->AsString();
        }
        return true;
    }

    //============ ResourceAssetManager end //
}}   // namespace cross::resource
