#pragma once

#include "Resource/Resource.h"

namespace cross {
enum class CEMeta(Cli) InstanceMemberType
{
    Float1,
    Float2,
    <PERSON>loat3,
    <PERSON>loat4,
    SInt1,
    SInt2,
    SInt3,
    SInt4,
    UInt1,
    UInt2,
    UInt3,
    UInt4,
};

struct ClusterNode
{
    CE_Serialize_Deserialize
    
    ClusterNode(SInt32 partitionCount = 1) : FirstInstance(partitionCount, -1), LastInstance(partitionCount, -1)
    {}

    UInt32 GetInstanceCount(SInt32 partitionCount = 1) const
    {
        Assert(partitionCount <= FirstInstance.size() && partitionCount <= LastInstance.size() && FirstInstance.size() == LastInstance.size());
        if (partitionCount == 1)
        {
            return LastInstance[0] - FirstInstance[0] + 1;
        }

        auto cnt = 0;
        for (int i = 0; i < partitionCount; i++)
            if (FirstInstance[i] >= 0)
                cnt += LastInstance[i] - FirstInstance[i] + 1;
        return cnt;
    }

    CEProperty()
    Float3 BoundMin;
    CEProperty()
    SInt32 FirstChild = -1;
    CEProperty()
    Float3 BoundMax;
    CEProperty()
    SInt32 LastChild = -1;
    CEProperty()
    std::vector<SInt32> FirstInstance = {-1};
    CEProperty()
    std::vector<SInt32> LastInstance = {-1};
};
}   // namespace cross

namespace cross::resource {

struct CEMeta(Cli) InstanceMemberData
{
    CE_Serialize_Deserialize

    CEProperty(Cli)
    std::string mName;

    CEProperty(Cli)
    InstanceMemberType mType;

    CEMeta(Cli)
    std::vector<UInt8> mData;

    CEFunction(AdditionalSerialize)
    void AdditionalSerialize(SerializeNode& inNode, SerializeContext& context) const;

    CEFunction(AdditionalDeserialize)
    void AdditionalDeserialize(const DeserializeNode& inNode, SerializeContext& context);
};

struct CEMeta(Cli) InstanceMemberDataInfo
{
public:
    CEMeta(Cli)
    UInt32 outType;
    CEMeta(Cli)
    void* outData;
    CEMeta(Cli)
    UInt32 outSize;
    CEMeta(Cli)
    UInt32 outStride;
};

struct InstanceData
{
    CE_Serialize_Deserialize

    CEProperty()
    UInt32 mInstanceCount = 1;

    CEProperty()
    std::vector<InstanceMemberData> mInstanceMembers;

    CEProperty()
    std::vector<ClusterNode> mClusterNodes;
};

class Resource_API CEMeta(Cli, Script) InstanceDataResource : public Resource, public InstanceData
{
    FRIEND_WITH_REFLECTION_MODULE;

public:
    using InstanceData::Serialize;
    using InstanceData::Deserialize;

    static int GetClassIDStatic() { return ClassID(InstanceDataResource); }

    InstanceDataResource();

    bool Deserialize(DeserializeNode const& s) override;

    bool Serialize(SerializeNode&& s, const std::string& path) override;

    // For Editor
    CEMeta(Cli)
    static InstanceDataResource* InstanceDataResource_CreateInstanceDataResource();
    
    CEMeta(Cli)
    void SetInstanceCount(UInt32 instanceCount) { mInstanceCount = instanceCount; }
    CEMeta(Cli)
    UInt32 GetInstanceCount() const { return mInstanceCount; }
    CEMeta(Cli)
    void ClearAllInstanceDatas();
    CEMeta(Cli)
    void ClearInstanceMemberData(const std::string& name);

    CEMeta(Cli)
    UInt32 GetInstanceMemberDataNameCount() const
    {
        return static_cast<UInt32>(mInstanceMembers.size());
    }

    CEMeta(Cli)
    const char* GetInstanceMemberDataNameAt(UInt32 index) const
    {
        return mInstanceMembers[index].mName.c_str();
    }

    void SetInstanceMemberData(const InstanceMemberData& data);

    bool HasMember(const std::string& name) const;

    const InstanceMemberData& GetInstanceMemberData(const std::string& name);

    CEMeta(Cli)
    void SetInstanceMemberData(const std::string& name, UInt32 type, const void* data, UInt32 size, UInt32 stride);

    CEMeta(Cli)
    InstanceMemberDataInfo EditorGetInstanceMemberData(const std::string& name);

    CEMeta(Cli)
    void MarkDirty();
};
}   // namespace cross::resource