#pragma once
#include <vector>
#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Resource/Serialize/JsonSerialize.h"
#include "GamePlayBaseFramework/base/memoryhooker/Module.h"

namespace cross {
struct CompactDrawCMD
{
    UInt32 indexCount;
    UInt32 instanceCount;
    UInt32 firstIndex;
    SInt32 vertexOffset;
    UInt32 firstInstance;
};

struct MeshBound
{
    using json = cross::serialize::json;

    Float3 Min = {INFINITY, INFINITY, INFINITY};
    Float3 Max = {-INFINITY, -INFINITY, -INFINITY};

    void Reset()
    {
        Min = {INFINITY, INFINITY, INFINITY};
        Max = {-INFINITY, -INFINITY, -INFINITY};
    }

    void Encapsulate(const Float3& inPoint)
    {
        Min = {(std::min)(Min.x, inPoint.x), (std::min)(Min.y, inPoint.y), (std::min)(Min.z, inPoint.z)};
        Max = {(std::max)(Max.x, inPoint.x), (std::max)(Max.y, inPoint.y), (std::max)(Max.z, inPoint.z)};
    }

    void Encapsulate(const MeshBound& inBound)
    {
        Min = {(std::min)(Min.x, inBound.Min.x), (std::min)(Min.y, inBound.Min.y), (std::min)(Min.z, inBound.Min.z)};
        Max = {(std::max)(Max.x, inBound.Max.x), (std::max)(Max.y, inBound.Max.y), (std::max)(Max.z, inBound.Max.z)};
    }

    bool SeriallizeToJson(json& j)
    {
        std::vector<float> vec;
        vec.resize(3);
        vec[0] = Min.x;
        vec[1] = Min.y;
        vec[2] = Min.z;
        j["Min"] = vec;
        vec[0] = Max.x;
        vec[1] = Max.y;
        vec[2] = Max.z;
        j["Max"] = vec;
        return true;
    }

    MeshBound& operator=(const MeshBound& value)
    {
        this->Max = value.Max;
        this->Min = value.Min;
        return *this;
    }
};

struct VertexChannelAssetData
{
    UInt16 mStride{0};
    UInt16 mFrequency{1};
    SInt16 mStream{-1};
    SInt16 mStreamOffset{-1};
    UInt64 mMiscFlag{0};
    UInt64 mReserve0{0};
    UInt64 mReserve1{0};
    VertexChannel mVertexChannel;
    VertexFormat mDataFormat{VertexFormat::Unknown};

    //using DataType = CrossSpan<UInt8>;//std::vector<UInt8, ce_stl_allocator<UInt8>>;
    using DataType = std::vector<UInt8, ce_stl_allocator<UInt8>>;

    DataType mData;
};

struct IndexStreamAssetData
{
    UInt32 mCount = 0;
    std::vector<UInt8, ce_stl_allocator<UInt8>> mData;
    bool mIs16BitIndex{true};

    void Clear()
    {
        mCount = 0;
        mData.clear();
        mIs16BitIndex = true;
    }

    IndexStreamAssetData& ConvertTo32BitIndex()
    {
        if (!mIs16BitIndex)
        {
            return *this;
        }

        std::vector<UInt8, ce_stl_allocator<UInt8>> newData;
        newData.resize(mCount * sizeof(UInt32));
        const UInt16* src = reinterpret_cast<const UInt16*>(mData.data());
        UInt32* dst = reinterpret_cast<UInt32*>(newData.data());
        for (UInt32 i = 0; i < mCount; ++i)
        {
            dst[i] = static_cast<UInt32>(src[i]);
        }
        mData = std::move(newData);
        mIs16BitIndex = false;

        return *this;
    }

    IndexStreamAssetData& operator=(const IndexStreamAssetData& src)
    {
        mCount = src.mCount;
        mData.resize(src.mData.size());
        std::copy(src.mData.begin(), src.mData.end(), mData.begin());
        mIs16BitIndex = src.mIs16BitIndex;

        return *this;
    }
};

enum struct StellarMeshVertexAttribute
{
    mPosition = 0,
    mNormal,
    mTangent,
    // mBinormal,
    mColor,
    mUV0,
    mUV1,
    mLast_,
};

struct MeshBindlessBufferIndex
{
    UInt32 mIndexBufferindex;
    std::unordered_map<StellarMeshVertexAttribute, UInt32> mVertexAttributesBufferIndex{};
};

struct MeshStreamData
{
    struct VertexAttributes
    {
        std::vector<Float3> mPosition{};
        std::vector<Float3> mNormal{};
        std::vector<Float4> mTangent{};
        // std::vector<Float3>   mBiNormal{};
        std::vector<Float4> mColor{};
        std::vector<Float2> mUV0{};
        std::vector<Float2> mUV1{};
    };

    std::vector<UInt32> mIndexes;
    VertexAttributes mVertexAttribute{};
};

//struct MeshStreamData
//{
//    std::vector<VertexChannelAssetData> mVertexChannelData{};
//    IndexStreamAssetData mIndexStream{};
//
//    MeshStreamData();
//    
//    const VertexChannelAssetData* GetVertexChannelData(VertexChannel vertexChannel) const;
//
//    VertexChannelAssetData* GetVertexChannelData(VertexChannel vertexChannel);
//
//    template<typename DstType>
//    std::vector<DstType> GetVertexData(VertexChannel channel, UInt32 vertexStart, UInt32 vertexCount) const
//    {
//        std::vector<DstType> outData{};
//        auto const& vertexChannel = GetVertexChannelData(channel);
//
//        if (vertexChannel == nullptr)
//        {
//            return outData;
//        }
//
//        UInt16 vertexStride = vertexChannel->mStride;
//        VertexFormat vertexDataFormat = vertexChannel->mDataFormat;
//        UInt8 const* vertexData = vertexChannel->mData.data() + vertexStart * vertexStride;
//
//        if (vertexStride == sizeof(DstType))
//        {
//            outData.resize(vertexCount);
//            memcpy(outData.data(), vertexData, vertexCount * vertexStride);
//        }
//        else
//        {
//            outData.reserve(vertexCount);
//            for (auto i = 0u; i < vertexCount; ++i)
//            {
//                ConvertToFloat(vertexData + i * vertexStride, vertexDataFormat, outData.emplace_back());
//            }
//        }
//
//        return outData;
//    }
//
//    std::vector<UInt32> GetIndexData(UInt32 indexStart, UInt32 indexCount) const;
//
//    void AddIndexData(UInt32 index);
//
//    void AddIndexData(std::vector<UInt32> const& indexes);
//
//    template<class T>
//    void AddVertexData(VertexChannel channel, VertexFormat format, std::vector<T> const& data)
//    {
//        auto vertexChannel = GetVertexChannelData(channel);
//        if (vertexChannel == nullptr)
//        {
//            vertexChannel = &mVertexChannelData.emplace_back();
//            vertexChannel->mStride = sizeof(T);
//            vertexChannel->mVertexChannel = channel;
//            vertexChannel->mDataFormat = format;
//            vertexChannel->mData.clear();
//        }
//
//        auto oriSize = static_cast<UInt32>(vertexChannel->mData.size());
//        vertexChannel->mData.resize(oriSize + data.size() * sizeof(T));
//        memcpy(vertexChannel->mData.data() + oriSize, data.data(), data.size() * sizeof(T));
//    }
//
//    template<class T>
//    void AddVertexData(VertexChannel channel, VertexFormat format, const T& data)
//    {
//        auto vertexChannel = GetVertexChannelData(channel);
//        if (vertexChannel == nullptr)
//        {
//            vertexChannel = &mVertexChannelData.emplace_back();
//            vertexChannel->mStride = sizeof(T);
//            vertexChannel->mVertexChannel = channel;
//            vertexChannel->mDataFormat = format;
//            vertexChannel->mData.clear();
//        }
//
//        auto orgSize = static_cast<UInt32>(vertexChannel->mData.size());
//        vertexChannel->mData.resize(orgSize + sizeof(T));
//        memcpy(vertexChannel->mData.data() + orgSize, &data, sizeof(T));
//    }
//};

}
