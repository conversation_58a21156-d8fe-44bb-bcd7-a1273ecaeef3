#include "EnginePrefix.h"
#include "Shader.h"
#include "CrossBase/Threading/RenderingThread.h"
#include "CECommon/Common/MeshDefines.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Graphics/GPUPrototype.h"
#include "NativeGraphicsInterface/NGI.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "CECommon/Common/SettingsManager.h"

#include "Resource/IResourceInterface.h"
#include "Resource/Texture/Texture.h"
#include "Resource/Texture/TextureUDIM.h"
#include "Resource/ResourceManager.h"
#include <charconv>

#include "CrossBase/Serialization/ResourceMetaHeader.h"
#include <filesystem>
#include <fstream>
#include <ranges>

namespace cross { namespace resource {
#if CROSSENGINE_EDITOR
    static bool CompileOnDemand(const std::string& ndaFileFullPath, const CrossSchema::ShaderVersion& minVer, const std::vector<std::string> defines = {})
    {
        using namespace CrossSchema;

        const std::string& ndaFile = ndaFileFullPath;

        // remove .nda extension, a litte hacky;
        // keep a source file deps in flatbuffers would be better
        std::string srcShader = std::filesystem::path{ndaFile}.replace_extension("").string();

        ClassIDType ndaType = ClassIDType::CLASS_NullType;
        {
            cross::ResourceMetaHeader header;
            if (gResourceAssetMgr.GetResourceMetaHeader(ndaFileFullPath.c_str(), header))
                ndaType = static_cast<ClassIDType>(header.mClassID);
        }

        ShaderVersion version{ShaderCodeFormat::SPIR_V, 1, 3};
        switch (minVer.format())
        {
        case ShaderCodeFormat::DXIL:
            version.mutate_format(ShaderCodeFormat::DXIL);
            version.mutate_major_version(6);
            version.mutate_minor_version(0);
            break;
        case ShaderCodeFormat::MSL_OSX:
            version.mutate_format(ShaderCodeFormat::MSL_OSX);
            version.mutate_major_version(2);
            version.mutate_minor_version(2);
            break;
        case ShaderCodeFormat::ESSL:
            if (ndaType == ClassIDType::CLASS_Shader)
            {
                version.mutate_format(ShaderCodeFormat::ESSL);
                version.mutate_major_version(3);
                version.mutate_minor_version(0);
            }
            else
            {
                LOG_ERROR("[ShaderGen] Unsupport Shader Format {}", EnumNameShaderCodeFormat(minVer.format()));
                return false;
            }
            break;
        case ShaderCodeFormat::SPIR_V:
            break;
        default:
            LOG_ERROR("[ShaderGen] Unsupport Shader Format {}", EnumNameShaderCodeFormat(minVer.format()));
            return false;
        }

        std::string define = defines.size() ? " -d " : "";
        for (const auto& var : defines)
        {
            define += var + " ";
        }

        std::string format = " -f " + std::to_string(static_cast<UInt32>(version.format())) + " " + std::to_string(static_cast<UInt32>(version.major_version())) + " " + std::to_string(static_cast<UInt32>(version.minor_version())) + " ";

#    if CROSSENGINE_OSX
        format += " -gen-all ";
#    endif

        const std::string command = PathHelper::GetEngineBinaryDirectoryPath() + "/ShaderGen -s \"" + srcShader + "\" -o \"" + ndaFile + "\" -eiap \"" + PathHelper::GetCurrentDirectoryPath() + "\" -eier \"" +
                                    PathHelper::GetEngineResourceDirectoryPath() + "\" -eieb \"" + PathHelper::GetEngineBinaryDirectoryPath() + "\" " + define + format;

        LOG_INFO("[ShaderGen] Call With \"{}\"", command);
        const int ret = std::system(command.c_str());

        if (ret < 0)
        {
            LOG_ERROR("[ShaderGen] Create Process Failed");
            return false;
        }
        else if (ret != 0)
        {
            LOG_ERROR("[ShaderGen] Process Return {} And Get Error {}", ret, std::strerror(errno));
            return false;
        }

        return true;
    }

    template<class T = Shader, typename std::enable_if<std::is_same<Shader, T>{} || std::is_same<ComputeShader, T>{} || std::is_same<RayTracingShader, T>{}, bool>::type = true>
    static bool TryReloadShader(const std::string& ndaFileFullPath, T& shader)
    {
        // try reload...
        LoadNDAFileInfo info;
        info.SetFileName(ndaFileFullPath);
        gResourceAssetMgr.GetLoadNDAInfo(ndaFileFullPath.c_str(), info);

        std::ifstream ndaFile{ndaFileFullPath, std::ios::binary | std::ios::in};
        if (ndaFile.is_open())
        {
            ndaFile.seekg(0, std::ios::end);
            const auto bufSize = ndaFile.tellg();
            const auto metaHeaderSize = info.HasMetaHeader() ? info.GetMetaHeader().mJsonStringLength : 0;
            const auto dataSize = bufSize - std::streampos{metaHeaderSize};

            std::vector<UInt8> buffer;
            buffer.resize(dataSize);

            ndaFile.seekg(metaHeaderSize, std::ios::beg);
            ndaFile.read(reinterpret_cast<char*>(buffer.data()), dataSize);
            ndaFile.close();

            flatbuffers::Verifier verifier(buffer.data(), buffer.size());
            if (CrossSchema::VerifyResourceAssetBuffer(verifier))
            {
                shader.ResetResource();
                return shader.SetupWithFlatbuffers(buffer.data(), buffer.size());
            }
        }

        return false;
    }

#endif

    void CreateShaderLayout(size_t numResourceGroups, const cross::resource::ShaderResourceGroupLayout* resourceGroupLayouts, std::optional<cross::resource::ShaderBufferLayout>& scLayout, NGIPipelineLayout*& ngiLayout)
    {
        NGIPipelineLayoutDesc pipelineLayoutDesc{static_cast<UInt32>(numResourceGroups)};

        for (UInt32 i = 0; i < numResourceGroups; ++i)
        {
            auto& group = resourceGroupLayouts[i];
            std::vector<NGIResourceDesc> resDescs;
            resDescs.reserve(group.ResourceLayouts.size() + group.ConstantBufferLayouts.size());
            std::copy(group.ResourceLayouts.begin(), group.ResourceLayouts.end(), std::back_inserter(resDescs));
            std::copy(group.ConstantBufferLayouts.begin(), group.ConstantBufferLayouts.end(), std::back_inserter(resDescs));
            std::sort(resDescs.begin(), resDescs.end());
            NGIResourceGroupLayoutDesc resourceGroupLayoutDesc{
                static_cast<UInt32>(resDescs.size()),
                resDescs.data(),
            };
            pipelineLayoutDesc.ResourceGroupLayouts[i] = gResourceMgr.mCreateRenderObjectMgr->AllocateResourceGroupLayout(resourceGroupLayoutDesc);
        }

        if (scLayout)
        {
            pipelineLayoutDesc.ConstantSpace = scLayout->Space;
            pipelineLayoutDesc.ConstantIndex = scLayout->Index;
            pipelineLayoutDesc.ConstantCount = static_cast<UInt32>(scLayout->Members.size());
            pipelineLayoutDesc.Constants = scLayout->Members.data();
        }

        ngiLayout = gResourceMgr.mCreateRenderObjectMgr->AllocatePipelineLayout(pipelineLayoutDesc);
    }

    bool operator<=(const CrossSchema::ShaderVersion& a, const CrossSchema::ShaderVersion& b)
    {
        if (a.major_version() < b.major_version())
        {
            return true;
        }
        else if (a.major_version() > b.major_version())
        {
            return false;
        }
        else
        {
            return a.minor_version() <= b.minor_version();
        }
    }

    std::tuple<CrossSchema::ShaderVersion, CrossSchema::ShaderVersion> GetShaderVersionRange()
    {
        using namespace CrossSchema;

        switch (EngineGlobal::GetSettingMgr()->GetRenderMode())
        {
        case NGIPlatform::D3D12:
        {
            return {{ShaderCodeFormat::DXIL, 5, 1}, {ShaderCodeFormat::DXIL, 6, 6}};
        }
        case NGIPlatform::OpenGLES3:
        {
            return {{ShaderCodeFormat::ESSL, 3, 0}, {ShaderCodeFormat::ESSL, 3, 1}};
        }
        case NGIPlatform::Vulkan:
        {
            return {{ShaderCodeFormat::SPIR_V, 1, 3}, {ShaderCodeFormat::SPIR_V, 1, 4}};
        }
        case NGIPlatform::WXGame:
        {
            return {{ShaderCodeFormat::ESSL, 1, 0}, {ShaderCodeFormat::ESSL, 1, 0}};
        }
        case NGIPlatform::Metal:
        {
            if constexpr (CROSSENGINE_IOS)
            {
                return {{ShaderCodeFormat::MSL_IOS, 2, 2}, {ShaderCodeFormat::MSL_IOS, 2, 2}};
            }
            else if constexpr (CROSSENGINE_OSX)
            {
                return {{ShaderCodeFormat::MSL_OSX, 2, 2}, {ShaderCodeFormat::MSL_OSX, 2, 2}};
            }
        }
        default:
            break;
        }
        Assert(false);
        return {};
    }

    void ImportShaderLayout(const CrossSchema::ShaderLayout* layout, size_t numResourceGroups, resource::ShaderResourceGroupLayout* resourceGroupLayouts, std::optional<resource::ShaderBufferLayout>& shaderConstantLayout,
                            std::vector<NGICombinedSamplerDesc>& combinedSamplers)
    {
        using namespace CrossSchema;

        if (auto* constBuffers = layout->constant_buffers(); constBuffers)
        {
            for (auto* constBuffer : *constBuffers)
            {
                resource::ShaderBufferLayout cbLayout{
                    {
                        constBuffer->name()->c_str(),
                        constBuffer->type(),
                        constBuffer->space(),
                        constBuffer->index(),
                        constBuffer->array_size(),
                        constBuffer->stage_mask(),
                    },
                    constBuffer->size(),
                };

                if (constBuffer->struct_type())
                {
                    cbLayout.Members.reserve(constBuffer->struct_type()->members()->size());
                    for (auto* member : *constBuffer->struct_type()->members())
                    {
                        cbLayout.Members.push_back({
                            member->name()->c_str(),
                            member->type(),
                            member->row_count(),
                            member->col_count(),
                            member->index(),
                            member->offset(),
                            member->size(),
                            member->array_size(),
                            member->stage_mask(),
                        });
                    }
                }
                else
                {
                    cbLayout.Members.reserve(constBuffer->members()->size());
                    for (auto* member : *constBuffer->members())
                    {
                        cbLayout.Members.push_back({
                            member->name()->c_str(),
                            member->type(),
                            member->row_count(),
                            member->col_count(),
                            member->index(),
                            member->offset(),
                            member->size(),
                            member->array_size(),
                            member->stage_mask(),
                        });
                    }
                }
                
                Assert(constBuffer->space() < numResourceGroups);
                resourceGroupLayouts[constBuffer->space()].ConstantBufferLayouts.emplace_back(std::move(cbLayout));
            }
        }

        if (auto* resources = layout->resources(); resources)
        {
            for (auto* resource : *resources)
            {
                Assert(resource->space() < numResourceGroups);
                auto& resGroupLayout = resourceGroupLayouts[resource->space()];
                auto& resourceDesc = resGroupLayout.ResourceLayouts.emplace_back(ShaderResourceDesc{
                    {
                        resource->name()->c_str(),
                        resource->type(),
                        resource->space(),
                        resource->index(),
                        resource->array_size(),
                        resource->space() == ShaderParamGroup_Bindless ? ToUnderlying(ShaderStageBit::All) : resource->stage_mask(),
                    },
                    resource->return_type(),
                    resource->depth_texture(),
                });
            }
        }

        if (auto* constants = layout->specialization_constants(); constants)
        {
            resource::ShaderBufferLayout cbLayout{
                {constants->name()->c_str(), constants->type(), constants->space(), constants->index(), 1, constants->stage_mask()},
                constants->size(),
            };
            if (constants->struct_type())
            {
                cbLayout.Members.reserve(constants->struct_type()->members()->size());
                for (auto* constant : *(constants->struct_type()->members()))
                {
                    cbLayout.Members.push_back({
                        constant->name()->c_str(),
                        constant->type(),
                        constant->row_count(),
                        constant->col_count(),
                        constant->index(),
                        constant->offset(),
                        constant->size(),
                        constant->array_size(),
                        constant->stage_mask(),
                    });
                }
            }
            else
            {
                cbLayout.Members.reserve(constants->members()->size());
                for (auto* constant : *(constants->members()))
                {
                    cbLayout.Members.push_back({
                        constant->name()->c_str(),
                        constant->type(),
                        constant->row_count(),
                        constant->col_count(),
                        constant->index(),
                        constant->offset(),
                        constant->size(),
                        constant->array_size(),
                        constant->stage_mask(),
                    });
                    auto& constantDesc = cbLayout.Members.back();
                }
            }
            std::sort(cbLayout.Members.begin(), cbLayout.Members.end());
            shaderConstantLayout = std::move(cbLayout);
        }

        if (auto* combSmps = layout->combined_samplers(); combSmps)
        {
            combinedSamplers.reserve(combSmps->size());
            for (const auto& combinedSampler : *(combSmps))
            {
                combinedSamplers.push_back({combinedSampler->name()->c_str(), combinedSampler->texture_name()->c_str(), combinedSampler->sampler_name()->c_str()});
            }
        }
    }

    size_t cross::resource::ShaderVariationKey::GetMacroCount() const
    {
        return mShader->mKeywordIndices.size();
    }

    std::string_view cross::resource::ShaderVariationKey::GetMacroName(UInt32 i) const
    {
        if (const auto* keywords = mShader->mCurrentShader->keywords(); keywords)
        {
            return {
                keywords->Get(i)->c_str(),
                keywords->Get(i)->size(),
            };
        }
        else
        {
            return "";
        }
    }
    cross::NameID cross::resource::ShaderVariationKey::GetMacroNameID(UInt32 i)
    {
        return mShader->mKeywordIDs[i];
    }
    void cross::resource::ShaderVariationKey::SetMacro(std::string_view keyword, bool enable)
    {
        if (auto ret = mShader->mKeywordIndices.find(keyword); ret != mShader->mKeywordIndices.end())
        {
            mActiveKeywords[ret->second] = enable;
        }
    }

    bool ShaderVariationKey::IsMacroEnable(std::string_view keyword)
    {
        if (auto ret = mShader->mKeywordIndices.find(keyword); ret != mShader->mKeywordIndices.end())
        {
            return mActiveKeywords[ret->second];
        }

        return false;
    }

#if CROSSENGINE_EDITOR
    std::string cross::resource::Shader::EditorGetProperty()
    {
        SerializeNode node;

        auto macros = std::make_unique<ShaderVariationKey>(this);

        auto size = macros->GetMacroCount();

        for (UInt32 id = 0; id < size; id++)
        {
            node[macros->GetMacroName(id).data()] = "bool";
        }

        VisitProgram([&](Shader::ProgramDesc const& v) {
            for (const auto& group : v.ResourceGroupLayouts)
            {
                for (const auto& cb : group.ConstantBufferLayouts)
                {
                    if (cb.Space == 1)
                    {
                        for (const auto& member : cb.Members)
                        {
                            if (member.Type == CrossSchema::ShaderVariableType::Bool)
                            {
                                node[member.Name.GetName()] = "bool";
                            }
                            else if (member.ColCount * member.RowCount <= 4)
                            {
                                std::string typeString = "float";
                                if (member.ColCount * member.RowCount > 1)
                                    typeString.push_back(static_cast<char>(member.ColCount * member.RowCount) + '0');
                                node[member.Name.GetName()] = typeString;
                            }
                        }
                    }
                }
                for (const auto& member : group.ResourceLayouts)
                {
                    if (member.Space == 1)
                    {
                        switch (member.Type)
                        {
                        case CrossSchema::ShaderResourceType::Texture1D:
                        case CrossSchema::ShaderResourceType::Texture1DArray:
                        case CrossSchema::ShaderResourceType::Texture2D:
                        case CrossSchema::ShaderResourceType::Texture2DArray:
                        case CrossSchema::ShaderResourceType::Texture2DMS:
                        case CrossSchema::ShaderResourceType::Texture2DMSArray:
                        case CrossSchema::ShaderResourceType::Texture3D:
                        case CrossSchema::ShaderResourceType::TextureCube:
                        case CrossSchema::ShaderResourceType::TextureCubeArray:
                            node[member.ID.GetName()] = "texture";
                            break;
                        default:
                            break;
                        }
                    }
                }
            }
            if (v.ShaderConstantLayout)
            {
                for (const auto& member : (*v.ShaderConstantLayout).Members)
                {
                    if (member.Type == CrossSchema::ShaderVariableType::Bool)
                    {
                        node[member.Name.GetName()] = "bool";
                    }
                    else if (member.ColCount * member.RowCount <= 4)
                    {
                        std::string typeString = "float";
                        if (member.ColCount * member.RowCount > 1)
                            typeString.push_back(static_cast<char>(member.ColCount * member.RowCount) + '0');
                        node[member.Name.GetName()] = typeString;
                    }
                }
            }
        });

        return node.FormatToJson();
    }
#endif

    void cross::resource::Shader::AddSelfItem(flatbuffers::FlatBufferBuilder& outBuilder, flatbuffers::Offset<void>& outSelfOffset, CrossSchema::ResourceType& outResType)
    {
        flatbuffers::Offset<CrossSchema::GraphicsShaderAsset> shaderOffset = CreateGraphicsShaderAsset(outBuilder, &mShaderAssetT);
        outSelfOffset = shaderOffset.Union();
        outResType = CrossSchema::ResourceType::GraphicsShaderAsset;
    }

    VertexFormat GetInstanceDataFormat(CrossSchema::ShaderVariableType type, UInt32 count)
    {
        switch (type)
        {
        case CrossSchema::ShaderVariableType::Float:
            if (count == 1)
                return VertexFormat::Float;
            if (count == 2)
                return VertexFormat::Float2;
            if (count == 3)
                return VertexFormat::Float3;
            if (count == 4)
                return VertexFormat::Float4;
            Assert(false);
            return VertexFormat::Unknown;
        case CrossSchema::ShaderVariableType::Int32:
            if (count == 1)
                return VertexFormat::INT;
            if (count == 4)
                return VertexFormat::INT4;
            return VertexFormat::Unknown;
        case CrossSchema::ShaderVariableType::UInt32:
            if (count == 1)
                return VertexFormat::UInt;
            if (count == 4)
                return VertexFormat::UInt4;
            Assert(false);
            return VertexFormat::Unknown;
        default:
            Assert(false);
            break;
        }
        return VertexFormat::Unknown;
    }

    void Shader::FillAllShaderConsts()
    {
        for (auto& desc : mProgramDescs)
        {
            if (auto& shaderConstLayout = desc.second.ShaderConstantLayout; shaderConstLayout)
            {
                for (auto& member : shaderConstLayout->Members)
                {
                    mShaderConsts.insert(member.Name);
                }
            }
        }
    }

#if CROSSENGINE_EDITOR
    Resource_API bool Shader::Serialize(const CrossSchema::GraphicsShaderAssetT& inGraphicsAsset)
    {
        flatbuffers::FlatBufferBuilder graphicsShaderBuilder{4096};

        const std::string assetFileName = mAsset->GetName();

        auto name = PathHelper::GetBaseFileName(assetFileName);

        ClassIDType classID = ClassID(Shader);

        CrossSchema::ResourceHeader header(ASSET_MAGIC_NUMBER, 0, classID, 0, 0);

        flatbuffers::Offset<CrossSchema::GraphicsShaderAsset> mloc = CreateGraphicsShaderAsset(graphicsShaderBuilder, &inGraphicsAsset);
        flatbuffers::Offset<CrossSchema::ResourceAsset> mloc2 = CreateResourceAsset(graphicsShaderBuilder, &header, graphicsShaderBuilder.CreateString(name), CrossSchema::ResourceType::GraphicsShaderAsset, mloc.Union());
        CrossSchema::FinishResourceAssetBuffer(graphicsShaderBuilder, mloc2);

        mAsset->EnsureGuid();
        ResourceMetaHeader metaHeader;
        metaHeader.mVersion = Resource::gResourceJsonHeaderVersion;
        metaHeader.mClassID = classID;
        metaHeader.mGuid = mAsset->GetGuid();
        metaHeader.mContentType = static_cast<UInt32>(CONTENT_TYPE::CONTENT_TYPE_FLATBUFFER);

        return Resource::Serialize(metaHeader, graphicsShaderBuilder.GetBufferPointer(), graphicsShaderBuilder.GetSize(), assetFileName);
    }
#endif
    Shader::~Shader()
    {
        using namespace CrossSchema;

        for (auto& [_, programDesc] : mProgramDescs)
        {
            if (programDesc.ObjectProtoType.ID != GPUProtoType::InvalidID)
            {
                GPUProtoType::GetInstance()->FreeID(programDesc.ObjectProtoType.ID);
                programDesc.ObjectProtoType.ID = GPUProtoType::InvalidID;
            }
            if (programDesc.PrimitiveProtoType.ID != GPUProtoType::InvalidID)
            {
                GPUProtoType::GetInstance()->FreeID(programDesc.PrimitiveProtoType.ID);
                programDesc.PrimitiveProtoType.ID = GPUProtoType::InvalidID;
            }
        }
    }

    bool Shader::Deserialize(const FBSerializer& s)
    {
        return SetupWithFlatbuffers(reinterpret_cast<const void*>(s.GetArchive().Data() + s.InitialOffset()), s.GetArchive().Size() - s.InitialOffset());
    }

    bool Shader::ResetResource()
    {
        Resource::ResetResource();
        mProgramDescs.clear();
        mKeywordIndices.clear();
        mKeywordIDs.clear();
        return true;
    }
    bool Shader::SetupWithFlatbuffers(const void* data, const UInt64 size)
    {
        using namespace CrossSchema;

        mData = std::make_unique<UInt8[]>(size);
        memcpy(mData.get(), data, size);

        auto fbResourceAsset = CrossSchema::GetResourceAsset(mData.get());
        if (!fbResourceAsset)
        {
            return false;
        }

        mResource = fbResourceAsset->resource_as_GraphicsShaderAsset();
        if (!mResource)
        {
            return false;
        }

        // select current shader code
        auto [minVer, maxVer] = GetShaderVersionRange();

        auto ret = std::find_if(mResource->platform_shaders()->begin(), mResource->platform_shaders()->end(), [minVer = minVer, maxVer = maxVer](const auto& codeList) {
            auto& ver = *(codeList->version());
            return ver.format() == minVer.format() && minVer <= ver && ver <= maxVer;
        });

#if CROSSENGINE_EDITOR
        // When use Shader OnDemand mode, the required shader format may inexistence
        if (mAsset)
        {
            const auto& ndaFileFullPath = PathHelper::GetAbsolutePath(mAsset->mName);
            const bool needReImport = ret == mResource->platform_shaders()->end();
            if (needReImport && CompileOnDemand(ndaFileFullPath, minVer) && TryReloadShader(ndaFileFullPath, *this))
            {
                return true;
            }
        }
#endif

        if (ret == mResource->platform_shaders()->end())
        {
            AssertMsg(false, "Can't find suitable shader code");
            return false;
        }

        // Setup internal
        {
            mCurrentShader = *ret;

            if (const auto* keywords = mCurrentShader->keywords(); keywords)
            {
                UInt32 index = 0;
                for (const auto& keyword : *keywords)
                {
                    mKeywordIndices.emplace(std::string_view{keyword->c_str(), keyword->size()}, index++);
                    mKeywordIDs.emplace_back(cross::NameID(keyword->c_str()));
                }
            }

            mVarientLayouts.clear();
            bool noMtlCb = true;

            mProgramDescs.reserve(mCurrentShader->variants()->size());
            for (const auto* program : *(mCurrentShader->variants()))
            {
                ProgramDesc desc
                {
                    .GraphicsProgramDesc =
                    {
                        .ShaderVersion = *mCurrentShader->version(),
                        .FilePath = HasAsset() ? GetName_CStr() : mName.c_str(),
                    },
                    .GUID = {program->guid()->low(), program->guid()->high()},
                };

                using TDst = NGIShaderCodeDesc(NGIGraphicsProgramDesc::*);
                using TSrc = const ShaderCode* (GraphicsShaderCode::*)() const;

                static const std::tuple<TDst, TSrc> gShaderStages[]{
                    {&NGIGraphicsProgramDesc::VertexShader, &GraphicsShaderCode::vertex_shader},
                    {&NGIGraphicsProgramDesc::HullShader, &GraphicsShaderCode::hull_shader},
                    {&NGIGraphicsProgramDesc::DomainShader, &GraphicsShaderCode::domain_shader},
                    {&NGIGraphicsProgramDesc::GeometryShader, &GraphicsShaderCode::geometry_shader},
                    {&NGIGraphicsProgramDesc::PixelShader, &GraphicsShaderCode::pixel_shader},
                };

                for (auto [dstPtr, srcPtr] : gShaderStages)
                {
                    auto& dst = desc.GraphicsProgramDesc.*dstPtr;
                    auto* src = (program->*srcPtr)();
                    if (src)
                    {
                        dst.EntryPoint = src->entry_point()->c_str();
                        dst.Data = src->code_data()->data();
                        dst.ByteSize = src->code_data()->size();
                        dst.DebugSymbol = src->debug_symbol();
                    }
                }

                ImportShaderLayout(program->layout(), desc.ResourceGroupLayouts.size(), desc.ResourceGroupLayouts.data(), desc.ShaderConstantLayout, desc.CombinedSamplers);

                static const auto FillBufferLayout = [](auto* structType, ShaderBufferLayout& layout) {
                    for (const auto& objMem : *structType->members())
                    {
                        layout.Members.push_back({
                            objMem->name()->c_str(),
                            objMem->type(),
                            objMem->row_count(),
                            objMem->col_count(),
                            0,
                            objMem->offset(),
                            objMem->size(),
                            objMem->array_size(),
                            0,
                        });
                    }
                    layout.ByteSize = structType->size();
                    Assert(layout.ByteSize != 0);
                    Assert(structType->size() != 0);
                };

                if (desc.ResourceGroupLayouts[ShaderParamGroup_Model].ConstantBufferLayouts.empty() && program->layout()->resources())
                {
                    for (auto* resource : *program->layout()->resources())
                    {
                        if (resource->space() == ShaderParamGroup_Model && resource->type() == ShaderResourceType::StructuredBuffer)
                        {
                            if (strcmp(resource->name()->c_str(), BuiltInProperty::ce_PerObject.GetName()) == 0)
                            {
                                auto* structType = resource->struct_type();
                                desc.ObjectProtoType.ID = GPUProtoType::GetInstance()->CreateID(structType);
                                FillBufferLayout(structType, desc.ObjectProtoType.Layout);
                            }
                            if (strcmp(resource->name()->c_str(), BuiltInProperty::ce_PerPrimitive.GetName()) == 0)
                            {
                                auto* structType = resource->struct_type();
                                desc.PrimitiveProtoType.ID = GPUProtoType::GetInstance()->CreateID(structType);
                                FillBufferLayout(structType, desc.PrimitiveProtoType.Layout);
                            }
                        }
                    }
                }

                if (desc.ResourceGroupLayouts[ShaderParamGroup_Material].ConstantBufferLayouts.size() == 1)
                {
                    for (auto* constant_buffer : *program->layout()->constant_buffers())
                    {
                        if (constant_buffer->space() == ShaderParamGroup_Material && constant_buffer->type() == ShaderResourceType::ConstantBuffer)
                        {
                            GPUProtoType::ID structTypeID = GPUProtoType::InvalidID;
                            auto* structType = constant_buffer->struct_type();
                            ShaderBufferLayout variantLayout = desc.ResourceGroupLayouts[ShaderParamGroup_Material].ConstantBufferLayouts.front();
                            variantLayout.Members.clear();
                            variantLayout.ByteSize = 0;
                            if (!structType)
                            {
                                auto* cbMtlLayout = constant_buffer->UnPack();
                                auto newStructType = std::make_unique<ShaderStructTypeT>();

                                for (UInt32 i = 0; i < constant_buffer->members()->size(); ++i)
                                {
                                    auto member = constant_buffer->members()->Get(i);
                                    auto newMem = std::make_unique<ShaderVariableExT>();
                                    newMem->name = member->name()->c_str();
                                    newMem->type = member->type();
                                    newMem->offset = member->offset();
                                    newMem->index = member->index();
                                    newMem->row_count = member->row_count();
                                    newMem->col_count = member->col_count();
                                    newMem->size = member->size();
                                    Assert(newMem->size != 0);
                                    newMem->stage_mask = member->stage_mask();
                                    newStructType->members.emplace_back(std::move(newMem));
                                }
                                newStructType->size = constant_buffer->size();
                                Assert(newStructType->size != 0);

                                flatbuffers::FlatBufferBuilder builder{};
                                flatbuffers::Offset<ShaderStructType> offset;
                                offset = CrossSchema::ShaderStructType::Pack(builder, newStructType.get());
                                builder.Finish(offset);
                                uint8_t* buf = builder.GetBufferPointer();
                                structType = flatbuffers::GetRoot<ShaderStructType>(buf);

                                structTypeID = GPUProtoType::GetInstance()->CreateID(structType);
                                FillBufferLayout(structType, variantLayout);
                            }
                            else
                            {
                                structTypeID = GPUProtoType::GetInstance()->CreateID(structType);
                                FillBufferLayout(structType, variantLayout);
                            }

                            Assert(structTypeID != GPUProtoType::InvalidID);
                            desc.MaterialProtoTypeID = structTypeID;
                            mVarientLayouts[structTypeID] = std::move(variantLayout);
                            noMtlCb = false;
                            
                            break;
                        }
                    }
                }

                desc.GraphicsProgramDesc.CombinedSamplers = desc.CombinedSamplers.empty() ? nullptr : desc.CombinedSamplers.data();
                desc.GraphicsProgramDesc.CombinedSamplerCount = static_cast<UInt32>(desc.CombinedSamplers.size());

                if (auto* vertexStageInputs = program->vertex_shader()->stage_inputs(); vertexStageInputs)
                {
                    for (auto* vertexStageInput : *(vertexStageInputs))
                    {
                        std::string_view semanticName = vertexStageInput->name()->c_str();
                        auto found = false;
                        auto instNamePos = semanticName.find("INSTANCE_");
                        if (instNamePos != std::string_view::npos)
                        {
                            instNamePos += 9;
                            Assert(vertexStageInput->col_count());
                            Assert(vertexStageInput->row_count());
                            // TODO(peterwjma): 这里为了兼容老的shader，取row和col的最小值
                            auto rowCount = std::min(vertexStageInput->row_count(), vertexStageInput->col_count());
                            auto colCount = std::max(vertexStageInput->row_count(), vertexStageInput->col_count());
                            auto type = vertexStageInput->type();
                            auto format = GetInstanceDataFormat(type, colCount);

                            if (!desc.InstanceDataLayout.has_value())
                            {
                                desc.InstanceDataLayout = ShaderBufferLayout();
                                desc.InstanceStreamLayout.SetFrequency(VertexFrequency::PerInstance);
                            }

                            (*desc.InstanceDataLayout)
                                .Members.push_back({NameID(vertexStageInput->name()->c_str() + instNamePos),
                                                    vertexStageInput->type(),
                                                    rowCount,
                                                    colCount,
                                                    vertexStageInput->index(),
                                                    desc.InstanceDataLayout->ByteSize,
                                                    GetByteSize(format) * rowCount,
                                                    1,
                                                    0});
                            UInt32 locationModifier = 0;
                            while (rowCount--)
                            {
                                // desc.GraphicsProgramDesc.VertexAttributes[desc.GraphicsProgramDesc.VertexAttributeCount++] = {
                                //    MakeVertexChannel(VertexSemantic::SemanticInstance, instanceDataIndex),
                                //    vertexStageInput->index() + locationModifier,
                                //};
                                Assert(desc.InstanceStreamLayout.GetChannelCount() < MaxVertexChannelPerStream);
                                desc.InstanceStreamLayout.AddVertexChannelLayout(VertexChannel::InstanceData, format, static_cast<UInt8>(desc.InstanceDataLayout->ByteSize), vertexStageInput->index() + locationModifier);
                                desc.InstanceDataLayout->ByteSize += GetByteSize(format);
                                locationModifier++;
                                // instanceDataIndex++;
                            }
                            desc.InstanceStreamLayout.SetInstanceStepRate(1);
                            // locationModifier += vertexStageInput->col_count() - 1;
                            found = true;
                        }
                        else if (semanticName.find("POSITIONT") != std::string_view::npos)
                        {
                            desc.GraphicsProgramDesc.VertexAttributes[desc.GraphicsProgramDesc.VertexAttributeCount++] = {VertexChannel::PositionT, vertexStageInput->index()};
                            found = true;
                        }
                        else
                        {
                            for (UInt32 i = 0; i < gAllVertexSemanticSerial.size(); ++i)
                            {
                                auto channelName = gAllVertexSemanticSerialName[i];
                                if (auto pos = semanticName.find(channelName); pos != std::string::npos)
                                {
                                    pos += channelName.size();
                                    auto indexStr = semanticName.substr(pos);
                                    UInt32 index;
                                    if (indexStr.empty())
                                    {
                                        index = 0;
                                    }
                                    else
                                    {
                                        auto [_, ec] = std::from_chars(indexStr.data(), indexStr.data() + indexStr.size(), index);
                                        if (ec != std::errc{})
                                        {
                                            Assert(false);
                                        }
                                    }
                                    desc.GraphicsProgramDesc.VertexAttributes[desc.GraphicsProgramDesc.VertexAttributeCount++] = {
                                        MakeVertexChannel(GetSemantic(gAllVertexSemanticSerial[i]), index),
                                        vertexStageInput->index(),
                                    };
                                    found = true;
                                    break;
                                }
                            }
                        }
                        Assert(found);
                    }
                }
#if CROSSENGINE_EDITOR
                desc.mTime = program->mtime();
#endif
                mProgramDescs[program->active_keywords()] = std::move(desc);
            }

            if (auto itr = mKeywordIndices.find(BuiltInProperty::CE_INSTANCING.GetName()); itr != mKeywordIndices.end())
            {
                auto FindSameProtoType = [&](ProtoType ProgramDesc::*protoType) -> const ProtoType* {
                    const ProtoType* sharedProtoType = nullptr;

                    for (auto& [keywords, desc] : mProgramDescs)
                    {
                        std::bitset<64> keywordBits{keywords};
                        if (keywordBits[itr->second])
                        {
                            // variant has CE_INSTANCING keyword, then it must has a valid GPUProtoType
                            if ((desc.*protoType).ID != GPUProtoType::InvalidID)
                            {
                                if (!sharedProtoType)
                                {
                                    sharedProtoType = &(desc.*protoType);
                                }
                                else if (sharedProtoType->ID != (desc.*protoType).ID)
                                {
                                    return nullptr;
                                }
                            }
                            else
                            {
                                return nullptr;
                            }
                        }
                    }
                    return sharedProtoType;
                };

                mObjectProtoType = FindSameProtoType(&ProgramDesc::ObjectProtoType);
                mPrimitiveProtoType = FindSameProtoType(&ProgramDesc::PrimitiveProtoType);
            }

            mProtoTypeContainer.FillProtoTypes(mProgramDescs, mKeywordIndices);
        }

        FillAllShaderConsts();

        return true;
    }

    const ShaderBufferLayout& Shader::GetMaterialConstantBufferLayout() const
    {
        return mProgramDescs.begin()->second.ResourceGroupLayouts[ShaderParamGroup_Material].ConstantBufferLayouts.front();
    }

    void Shader::ProtoTypeContainer::FillProtoTypes(const CEHashMap<UInt64, Shader::ProgramDesc>& programDescs, const CEHashMap<std::string_view, UInt32>& keywordIndices)
    {
        auto GetProtoType = [&](Shader::ProtoType Shader::ProgramDesc::*protoType, std::string_view primaryKeyword) -> const Shader::ProtoType* {
            if (auto itr = keywordIndices.find(primaryKeyword); itr != keywordIndices.end())
            {
                const Shader::ProtoType* sharedProtoType = nullptr;

                for (auto& [keywords, desc] : programDescs)
                {
                    std::bitset<64> keywordBits{keywords};
                    if (keywordBits[itr->second])
                    {
                        if ((desc.*protoType).ID != GPUProtoType::InvalidID)
                        {
                            if (!sharedProtoType)
                            {
                                sharedProtoType = &(desc.*protoType);
                            }
                            else if (sharedProtoType->ID != (desc.*protoType).ID)
                            {
                                return nullptr;
                            }
                        }
                    }
                }

                return sharedProtoType;
            }

            return nullptr;
        };

        for (auto& [usage, usageString] : magic_enum::enum_entries<MaterialUsage>())
        {
            const Shader::ProtoType* primitiveProtoType = GetProtoType(&Shader::ProgramDesc::PrimitiveProtoType, usageString);
            if (primitiveProtoType)
            {
                mPrimitiveProtoTypes[usage] = primitiveProtoType;
            }

            const Shader::ProtoType* objectProtoType = GetProtoType(&Shader::ProgramDesc::ObjectProtoType, usageString);
            if (objectProtoType)
            {
                mObjectProtoTypes[usage] = objectProtoType;
            }
        }
    }

    const Shader::ProtoType* Shader::ProtoTypeContainer::GetPrimitiveProtoType(MaterialUsage usage) const
    {
        auto iter = mPrimitiveProtoTypes.find(usage);
        if (iter != mPrimitiveProtoTypes.end())
            return iter->second;
        return nullptr;
    }

    const Shader::ProtoType* Shader::ProtoTypeContainer::GetObjectProtoType(MaterialUsage usage) const
    {
        auto iter = mObjectProtoTypes.find(usage);
        if (iter != mObjectProtoTypes.end())
            return iter->second;
        return nullptr;
    }

    bool ComputeShader::Deserialize(const FBSerializer& s)
    {
        bool result = SetupWithFlatbuffers(reinterpret_cast<const void*>(s.GetArchive().Data() + s.InitialOffset()), s.GetArchive().Size() - s.InitialOffset());
        if (mRenderObject)
        {
            mRenderObject->Reset(this);
        }
        return result;
    }

    bool ComputeShader::SetupWithFlatbuffers(const void* data, const UInt64 size)
    {
        using namespace CrossSchema;   // safe

        mData = std::make_unique<UInt8[]>(size);
        memcpy(mData.get(), data, size);

        auto fbResourceAsset = CrossSchema::GetResourceAsset(mData.get());
        if (!fbResourceAsset)
        {
            return false;
        }

        mResource = fbResourceAsset->resource_as_ComputeShaderAsset();
        if (!mResource)
        {
            return false;
        }

        // select current shader code
        auto [minVer, maxVer] = GetShaderVersionRange();

        auto ret = std::find_if(mResource->platform_shaders()->begin(), mResource->platform_shaders()->end(), [minVer = minVer, maxVer = maxVer](const auto& codeList) {
            auto& ver = *(codeList->version());
            return ver.format() == minVer.format() && minVer <= ver && ver <= maxVer;
        });

        if (ret == mResource->platform_shaders()->end() || ret->code_list() == nullptr || ret->code_list()->size() == 0)
        {
#if CROSSENGINE_EDITOR
            {
                const auto& ndaFileFullPath = PathHelper::GetAbsolutePath(mAsset->mName);
                if (CompileOnDemand(ndaFileFullPath, minVer))
                {
                    if (TryReloadShader(ndaFileFullPath, *this))
                        return true;
                }
            }
#endif

            AssertMsg(false, "Can't find suitable shader code");
            return false;
        }

        // Setup internal
        {
            mCurrentShader = *ret;

            std::unique_lock lock(mProgramDescMutex);

            mProgramDescs.reserve(mCurrentShader->code_list()->size());
            for (const auto* program : *(mCurrentShader->code_list()))
            {
                ComputeProgramDesc desc{
                    {
                        *(mCurrentShader->version()),
                        {
                            program->compute_shader()->code_data()->data(),
                            program->compute_shader()->code_data()->size(),
                            program->compute_shader()->entry_point()->c_str(),
                            program->compute_shader()->debug_symbol(),
                        },
                        0,
                        nullptr,
                        {(*program->group_size()).x(), (*program->group_size()).y(), (*program->group_size()).z()},
                        GetName_CStr(),
                    },
                    nullptr,
                    {program->guid()->low(), program->guid()->high()},
                    *program->group_size(),
                };
                
                ImportShaderLayout(program->layout(), desc.ResourceGroupLayouts.size(), desc.ResourceGroupLayouts.data(), desc.ShaderConstantLayout, desc.CombinedSamplers);
                desc.ProgramDesc.CombinedSamplers = desc.CombinedSamplers.empty() ? nullptr : desc.CombinedSamplers.data();
                desc.ProgramDesc.CombinedSamplerCount = static_cast<UInt32>(desc.CombinedSamplers.size());

                NameID ID{program->compute_shader()->entry_point()->c_str()};
                mProgramDescs[ID] = std::move(desc);
            }
        }
        return true;
    }

    bool ComputeShader::ResetResource()
    {
        Resource::ResetResource();
        std::unique_lock lock(mProgramDescMutex);
        mProgramDescs.clear();
        return true;
    }

    Shader::ProgramDesc* Shader::GetProgramDesc(const ShaderVariationKey& key)
    {
        const auto activeKey = key.mActiveKeywords.to_ullong();
        auto iter = mProgramDescs.find(activeKey);

#if CROSSENGINE_EDITOR
        // When use Shader OnDemand mode, the required variant may expired or inexistence
        {
            const bool needReImport = iter == mProgramDescs.end() || (mResource->mtime() != 0 && iter->second.mTime != mResource->mtime());
            if (needReImport && mResource && mAsset)
            {
                auto [minVer, maxVer] = GetShaderVersionRange();
                const auto& ndaFileFullPath = PathHelper::GetAbsolutePath(mAsset->mName);
                std::vector<std::string> defines = {};
                for (int i = 0; i < key.GetMacroCount(); i++)
                {
                    if (key.mActiveKeywords[i])
                    {
                        defines.emplace_back(key.GetMacroName(i));
                    }
                }

                if (CompileOnDemand(ndaFileFullPath, minVer, defines) && TryReloadShader(ndaFileFullPath, *this))
                    return GetProgramDesc(key);
            }
        }
#endif

        if (iter != mProgramDescs.end())
        {
            auto& desc = iter->second;
            if (!desc.PipelineLayout)
            {
                CreateShaderLayout(desc.ResourceGroupLayouts.size(), desc.ResourceGroupLayouts.data(), desc.ShaderConstantLayout, desc.PipelineLayout);
            }
            return &desc;
        }
        else
        {
            Assert(false);
            return nullptr;
        }
    }

    ComputeProgramDesc* ComputeShader::GetProgramDesc(const cross::NameID& name)
    {
        std::unique_lock lock(mProgramDescMutex);
        if (auto ret = mProgramDescs.find(name); ret != mProgramDescs.end())
        {
            auto& desc = ret->second;
            if (!desc.PipelineLayout)
            {
                CreateShaderLayout(desc.ResourceGroupLayouts.size(), desc.ResourceGroupLayouts.data(), desc.ShaderConstantLayout, desc.PipelineLayout);
            }
            return &(ret->second);
        }
        else
        {
            Assert(false);
            return nullptr;
        }
    }

    ComputeShader::ComputeShader()
    {
        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeHeadless)
        {
            mRenderObject = gResourceMgr.mCreateRenderObjectMgr->GetComputeShaderR(this);
        }
    }

    void ComputeShader::DestroyRenderData(FrameParam* frameParam)
    {
        if (mRenderObject)
        {
            DispatchRenderingCommandWithToken([renderObject = mRenderObject] { delete renderObject; });
            mRenderObject = nullptr;
        }
    }

#if CROSSENGINE_EDITOR
    Resource_API bool ComputeShader::Serialize(const CrossSchema::ComputeShaderAssetT& inComputeAsset)
    {
        flatbuffers::FlatBufferBuilder shaderBuilder{4096};

        const std::string assetFileName = mAsset->GetName();

        auto name = PathHelper::GetBaseFileName(assetFileName);

        ClassIDType classID = ClassID(ComputeShader);

        CrossSchema::ResourceHeader header(ASSET_MAGIC_NUMBER, 0, classID, 0, 0);

        flatbuffers::Offset<CrossSchema::ComputeShaderAsset> mloc = CreateComputeShaderAsset(shaderBuilder, &inComputeAsset);
        flatbuffers::Offset<CrossSchema::ResourceAsset> mloc2 = CreateResourceAsset(shaderBuilder, &header, shaderBuilder.CreateString(name), CrossSchema::ResourceType::ComputeShaderAsset, mloc.Union());
        CrossSchema::FinishResourceAssetBuffer(shaderBuilder, mloc2);

        mAsset->EnsureGuid();
        ResourceMetaHeader metaHeader;
        metaHeader.mVersion = Resource::gResourceJsonHeaderVersion;
        metaHeader.mClassID = classID;
        metaHeader.mGuid = mAsset->GetGuid();
        metaHeader.mContentType = static_cast<UInt32>(CONTENT_TYPE::CONTENT_TYPE_FLATBUFFER);

        return Resource::Serialize(metaHeader, shaderBuilder.GetBufferPointer(), shaderBuilder.GetSize(), assetFileName);
    }
#endif

    void RayTracingShader::DestroyRenderData(FrameParam* frameParam)
    {
        if (mRenderObject)
        {
            DispatchRenderingCommandWithToken([renderObject = mRenderObject] { delete renderObject; });
            mRenderObject = nullptr;
        }
    }

    RayTracingShader::RayTracingShader()
    {
        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeHeadless)
        {
            mRenderObject = gResourceMgr.mCreateRenderObjectMgr->GetRayTracingShaderR(this);
        }
    }

    bool RayTracingShader::Deserialize(const FBSerializer& s)
    {
        bool result = SetupWithFlatbuffers(reinterpret_cast<const void*>(s.GetArchive().Data() + s.InitialOffset()), s.GetArchive().Size() - s.InitialOffset());
        if (mRenderObject)
        {
            mRenderObject->Reset(this);
        }
        return result;
    }

    bool RayTracingShader::ResetResource()
    {
        Resource::ResetResource();
        std::unique_lock lock(mProgramDescMutex);
        mProgramDesc = RayTracingProgramDesc{};
        return true;
    }

    RayTracingProgramDesc* RayTracingShader::GetProgramDesc()
    {
        std::unique_lock lock(mProgramDescMutex);
        if (!mProgramDesc.PipelineLayout)
        {
            CreateShaderLayout(mProgramDesc.ResourceGroupLayouts.size(), mProgramDesc.ResourceGroupLayouts.data(),
                mProgramDesc.ShaderConstantLayout, mProgramDesc.PipelineLayout);
        }
        return &mProgramDesc;
    }

    bool RayTracingShader::SetupWithFlatbuffers(const void* data, const UInt64 size)
    {
        using namespace CrossSchema;

        mData = std::make_unique<UInt8[]>(size);
        memcpy(mData.get(), data, size);

        auto fbResourceAsset = GetResourceAsset(mData.get());
        if (!fbResourceAsset)
        {
            return false;
        }
        mResource = fbResourceAsset->resource_as_RayTracingShaderAsset();
        if (!mResource)
        {
            return false;
        }

        auto [minVer, maxVer] = GetShaderVersionRange();
        auto ret = std::find_if(mResource->platform_shaders()->begin(), mResource->platform_shaders()->end(),
            [minVer, maxVer](const auto& codeList) {
                auto& ver = *codeList->version();
                return ver.format() == minVer.format() && minVer <= ver && ver <= maxVer;
        });

        Assert(ret != mResource->platform_shaders()->end());

        // Compile shader if not exist
        if (ret == mResource->platform_shaders()->end() || ret->shader_code() == nullptr)
        {
#if CROSSENGINE_EDITOR
            {
                const auto& ndaFileFullPath = PathHelper::GetAbsolutePath(mAsset->mName);
                if (CompileOnDemand(ndaFileFullPath, minVer))
                {
                    if (TryReloadShader(ndaFileFullPath, *this))
                    {
                        return true;
                    }
                }
            }
#endif
            AssertMsg(false, "Can't find suitable shader code");
            return false;
        }

        {
            mCurrentShader = *ret;
            std::unique_lock locker(mProgramDescMutex);
            const RayTracingShaderCode* shader_code = mCurrentShader->shader_code();

            std::vector<NGIShaderCodeDesc> missShaderCodeDescs;
            std::vector<HitGroupDesc> hitGroupDescs;
            std::vector<NGIShaderCodeDesc> callableShaderCodeDescs;

            std::ranges::transform(shader_code->miss_shader_entries()->begin(), shader_code->miss_shader_entries()->end(), std::back_inserter(missShaderCodeDescs),
                [&shader_code](const auto& entry) -> NGIShaderCodeDesc {
                    return {
                        .Data = shader_code->code()->code_data()->data(),
                        .ByteSize = shader_code->code()->code_data()->size(),
                        .EntryPoint = entry->c_str(),
                        .DebugSymbol = shader_code->code()->debug_symbol()};
            });

            // TODO(scolu): Support only one hit group temporally
            Assert(shader_code->closesthit_shader_entries()->size() == 1);
            for (UInt32 hitGroupIndex = 0; hitGroupIndex < shader_code->closesthit_shader_entries()->size(); hitGroupIndex++)
            {
                auto& hitGroup = hitGroupDescs.emplace_back();
                hitGroup.ClosestHitShader = {
                    .Data = shader_code->code()->code_data()->data(),
                    .ByteSize = shader_code->code()->code_data()->size(),
                    .EntryPoint = shader_code->closesthit_shader_entries()->Get(hitGroupIndex)->c_str(),
                    .DebugSymbol = shader_code->code()->debug_symbol()
                };
                if (!shader_code->anyhit_shader_entries() || hitGroupIndex > shader_code->anyhit_shader_entries()->size())
                {
                    hitGroup.AnyHitShader = {
                        .Data = nullptr,
                        .ByteSize = 0,
                        .EntryPoint = "Undefined",
                        .DebugSymbol = false
                    };
                }
                else
                {
                    hitGroup.AnyHitShader = {
                        .Data = shader_code->code()->code_data()->data(),
                        .ByteSize = shader_code->code()->code_data()->size(),
                        .EntryPoint = shader_code->anyhit_shader_entries()->Get(hitGroupIndex)->c_str(),
                        .DebugSymbol = shader_code->code()->debug_symbol()
                    };
                }
                if (!shader_code->intersection_shader_entries() || hitGroupIndex + 1 > shader_code->intersection_shader_entries()->size())
                {
                    hitGroup.IntersectionShader = {
                        .Data = nullptr,
                        .ByteSize = 0,
                        .EntryPoint = "Undefined",
                        .DebugSymbol = false
                    };
                }
                else
                {
                    hitGroup.IntersectionShader = {
                        .Data = shader_code->code()->code_data()->data(),
                        .ByteSize = shader_code->code()->code_data()->size(),
                        .EntryPoint = shader_code->intersection_shader_entries()->Get(hitGroupIndex)->c_str(),
                        .DebugSymbol = shader_code->code()->debug_symbol()
                    };
                }
            }

            if (shader_code->callable_shader_entries()) 
            {
                std::ranges::transform(shader_code->callable_shader_entries()->begin(), shader_code->callable_shader_entries()->end(), std::back_inserter(callableShaderCodeDescs),
                    [&shader_code](const auto& entry) -> NGIShaderCodeDesc {
                        return {
                            .Data = shader_code->code()->code_data()->data(),
                            .ByteSize = shader_code->code()->code_data()->size(),
                            .EntryPoint = entry->c_str(),
                            .DebugSymbol = shader_code->code()->debug_symbol()};
                });
            }

            mProgramDesc.ProgramDesc = {
                .ShaderVersion = *mCurrentShader->version(),
                .RayGenShader = {
                    .Data = shader_code->code()->code_data()->data(),
                    .ByteSize = shader_code->code()->code_data()->size(),
                    .EntryPoint = shader_code->raygen_shader_entry()->c_str(),
                    .DebugSymbol = shader_code->code()->debug_symbol()
                },
                .MissShaders = missShaderCodeDescs,
                .HitGroups = hitGroupDescs,
                .CallableShaders = callableShaderCodeDescs,
                .FilePath = GetName_CStr()
            };
            mProgramDesc.PipelineLayout = nullptr;
            mProgramDesc.GUID = CrossUUID(shader_code->guid()->low(), shader_code->guid()->high());
            
            ImportShaderLayout(shader_code->layout(), mProgramDesc.ResourceGroupLayouts.size(), mProgramDesc.ResourceGroupLayouts.data(), mProgramDesc.ShaderConstantLayout, mProgramDesc.CombinedSamplers);
        }
        return true;
    }

#ifdef CROSSENGINE_EDITOR
    bool RayTracingShader::Serialize(const CrossSchema::RayTracingShaderAssetT& inRayTracingAsset)
    {
        flatbuffers::FlatBufferBuilder shaderBuilder{4096};

        const std::string assetFileName = mAsset->GetName();

        auto name = PathHelper::GetBaseFileName(assetFileName);

        ClassIDType classID = ClassID(RayTracingShader);

        CrossSchema::ResourceHeader header(ASSET_MAGIC_NUMBER, 0, classID, 0, 0);

        flatbuffers::Offset<CrossSchema::RayTracingShaderAsset> mloc = CrossSchema::CreateRayTracingShaderAsset(shaderBuilder, &inRayTracingAsset);
        flatbuffers::Offset<CrossSchema::ResourceAsset> mloc2 = CreateResourceAsset(shaderBuilder, &header, shaderBuilder.CreateString(name), CrossSchema::ResourceType::RayTracingShaderAsset, mloc.Union());
        CrossSchema::FinishResourceAssetBuffer(shaderBuilder, mloc2);

        mAsset->EnsureGuid();
        ResourceMetaHeader metaHeader;
        metaHeader.mVersion = gResourceJsonHeaderVersion;
        metaHeader.mClassID = classID;
        metaHeader.mGuid = mAsset->GetGuid();
        metaHeader.mContentType = static_cast<UInt32>(CONTENT_TYPE::CONTENT_TYPE_FLATBUFFER);

        return Resource::Serialize(metaHeader, shaderBuilder.GetBufferPointer(), shaderBuilder.GetSize(), assetFileName);
    }
#endif

}}   // namespace cross::resource
