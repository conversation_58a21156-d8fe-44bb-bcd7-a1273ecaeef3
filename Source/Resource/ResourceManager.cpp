#include "EnginePrefix.h"
#include "Resource/IResourceInterface.h"
#include "Resource/ResourceManager.h"

#include "Resource/ScriptResource.h"

#include <filesystem>
#include "CrossBase/Template/TypeCast.hpp"

#include "Resource/BaseClasses/RegisterClassesForward.h"

#include "Resource/resourceasset.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/GlobalSystemDesc.h"
#include "Runtime/Interface/CrossEngineImp.h"

#include "Resource/ResourceSyncSystem.h"
#include "Resource/Texture/TextureCube.h"
#include "Resource/DataCompressor.h"
#include "Resource/ResourceConvertManager.h"
#include "CECommon/Common/RenderPipelineSetting.h"
#include "Resource/Texture/Texture2DArray.h"
#include "Resource/Animation/Curve/CurveControllerRes.h"
#include "Resource/Prefab/PrefabResource.h"
#include "Resource/TsResource.h"
#include "Resource/Fx.h"
#include "CECommon/Common/WorldConst.h"
#include "Resource/Animation/Sequence/AnimSequenceRes.h"
#include "Animation/Sequence/AnimSequence.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "CrossImage/imageio.h"
namespace cross {
const std::unordered_set<std::string> DefaultResourcePaths = {"default_texture", "black_texture"};

auto SpliteNameAndFile(char const* name) -> std::pair<std::string, std::string>
{
    std::string_view tempName{name};
    auto pos = tempName.find_first_of(':');

    if (pos == std::string_view::npos)
    {
        return {"", name};
    }
    else
    {
        return {{name, pos}, {name + pos + 1, tempName.length() - pos - 1}};
    }
}

bool NeedSkipFilePath(std::string filePath)
{
    std::transform(filePath.begin(), filePath.end(), filePath.begin(), [](unsigned char c) { return static_cast<unsigned char>(std::tolower(c)); });

    if (filePath.find(".git") != std::string::npos || filePath.find(".js") != std::string::npos || filePath.find(".tga") != std::string::npos)
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool IsNeedConvertClassID(ClassIDType inClassID)
{
    if (inClassID == ClassID(Material) || inClassID == ClassID(Texture) || inClassID == ClassID(Texture2D) || inClassID == ClassID(Shader))
        return true;
    else
        return false;
}

void ResourceManager::TraverseWorkDirToAddHeader()
{
#    if 0
        ResourcePtr& tex = CreateResource<Texture2D>(128, 128);
        ResourcePtr& tex1 = CreateResource<Texture2D>(128, 128, 64);
        ResourcePtr& mat  = CreateResource<Material>();
        ResourcePtr& mat1 = CreateResource<Material>("fx_path");
#    endif
    std::filesystem::path currentWorkPath = std::filesystem::current_path();
    std::string strCurrentWorkPath = currentWorkPath.string();

    using recursive_directory_iterator = std::filesystem::recursive_directory_iterator;
    for (const auto& dirEntry : recursive_directory_iterator(strCurrentWorkPath))
    {
        if (dirEntry.is_regular_file())
        {
            ResourceMetaHeader metaHeader;
            std::string fileName = dirEntry.path().string();
            bool neeSkipConvert = NeedSkipFilePath(fileName);
            bool hasMetaHeader = gResourceAssetMgr.GetResourceMetaHeader(fileName.c_str(), metaHeader);

            if (!neeSkipConvert && !hasMetaHeader)
            {
                ResourcePtr resItem = GetResource(fileName.c_str());
                if (resItem.get() && IsNeedConvertClassID(static_cast<ClassIDType>(resItem->GetClassID())))
                    Assert(false);
            }
        }
    }
}

bool ResourceManager::GetResourceDependencies(const char* resourcePath, std::vector<std::string>& outDependencyPaths, bool recursive)
{
    ResourceMetaHeader metaHeader;
    bool hasMetaHeader = gResourceAssetMgr.GetResourceMetaHeader(ConvertGuidToPath(resourcePath).c_str(), metaHeader);

    if (hasMetaHeader)
    {
        const auto& references = metaHeader.mDependency;
        for (const auto& item : references)
        {
            outDependencyPaths.emplace_back(item);
            if (recursive)
            {
                GetResourceDependencies(item.c_str(), outDependencyPaths, recursive);
            }
        }
    }
    return true;
}

void ResourceManager::AddChangedResource(ResourcePtr res)
{
    auto resId = static_cast<int>(res->GetClassID());
    if (mChangedResourceMap.find(resId) == mChangedResourceMap.end())
        mChangedResourceMap[resId] = {};
    mChangedResourceMap[resId].emplace_back(res);
}

const std::list<ResourcePtr>* ResourceManager::GetChangedResListById(int resId)
{
    if (mChangedResourceMap.find(resId) != mChangedResourceMap.end())
        return &mChangedResourceMap[resId];
    return nullptr;
}

#if  CROSSENGINE_WIN
ResourcePtr ResourceManager::LoadRawResouceForEditorDebug(const char* resoucePath)
{
    resource::ResourceLoadError loadError = resource::ResourceLoadError::Succeeded;
    ResourcePtr obj(gResourceAssetMgr.LoadNDAFile(ConvertGuidToPath(resoucePath).c_str(), loadError));
    Assert(loadError == resource::ResourceLoadError::Succeeded);
    // TODO:
    return obj;
}
#endif


ResourceManager& ResourceManager::Instance()
{
    static ResourceManager sInstance;
    return sInstance;
}

ResourceManager::ResourceManager() : mMaterialRenderingCommandBatcher()
{
    filesystem::FileSystem* pFileSystem = cross::EngineGlobal::Inst().GetFileSystem();
    Assert(pFileSystem);
    mFileSystem = pFileSystem;

    // old code
    EngineGlobal::GetSettingMgr()->GetValue<bool>("UseFileSystemUseStreamMode", mUseStreamMode);

    EngineGlobal::GetSettingMgr()->GetValue("ResourceManager.GCInterval", mGarbageCollectFrameInterval);

    EngineGlobal::GetSettingMgr()->GetValue("ResourceManager.mReleasePerFrame", mReleasePerFrame);

    EngineGlobal::GetSettingMgr()->GetValue("ResourceManager.mEnableParallelDelete", mEnableParallelDelete);
}

void ResourceManager::Destroy()
{
    mDefaultTex.reset();
    mBlackTex.reset();
    mResources.clear();

    while (!mCachedNDAFiles.empty())
    {
        auto begin = mCachedNDAFiles.begin();
        RemoveCachedNDAFile(begin->first.GetCString());
    }
}

void ResourceManager::Initialize()
{
    BuildGuidMap();
    InitDefaultResource();
}

void ResourceManager::CheckLeak()
{
    Assert(mResources.size() == 0);
}

void ResourceUtil::ReleaseResource(Resource* resource)
{
    //if (resource)
    //{
        //std::cout << "ReleaseResource " << resource->GetName() << std::endl;
        //resource->DecreaseRefCount();
    //}
}

Resource* ResourceUtil::ResourceGet(char const* path)
{
    auto res = gAssetStreamingManager->LoadSynchronously(path);
    if (!res)
        return nullptr;
    //res->IncreaseRefCount();
    return res.get();
}

int ResourceUtil::ResourceGetClassID(char const* path)
{
    uint32_t resourceheader = 0;
    int32_t resouceversion = 0;
    cross::ResourceManager::Instance().GetResourecHeaderInfo(path, resourceheader, resouceversion);
    return resourceheader;
}

void CalculateMean(imageio::color_rgba* colors, int count, double& meanR, double& meanG, double& meanB)
{
    double totalR = 0.0;
    double totalG = 0.0;
    double totalB = 0.0;
    for (auto i = 0; i < count; ++i)
    {
        auto& sample = colors[i];
        totalR += sample.r;
        totalG += sample.g;
        totalB += sample.b;
    }
    meanR = totalR / count;
    meanG = totalG / count;
    meanB = totalB / count;
}

void CalculateSquareError(imageio::color_rgba* colors, int count, double meanR, double meanG, double meanB, double& squareErrorR, double& squareErrorG, double& squareErrorB)
{
    squareErrorR = 0.0;
    squareErrorG = 0.0;
    squareErrorB = 0.0;
    for (auto i = 0; i < count; ++i)
    {
        auto& sample = colors[i];
        double errorR = (sample.r - meanR);
        double errorG = (sample.g - meanG);
        double errorB = (sample.b - meanB);
        squareErrorR += errorR * errorR;
        squareErrorG += errorG * errorG;
        squareErrorB += errorB * errorB;
    }
}

void CalculateCovariance(imageio::color_rgba* colors1, imageio::color_rgba* colors2, int count, double meanR1, double meanR2, double meanG1, double meanG2, double meanB1, double meanB2, double& covarianceR, double& covarianceG,
                         double& covarianceB)
{
    covarianceR = 0.0;
    covarianceG = 0.0;
    covarianceB = 0.0;
    for (auto i = 0; i < count; ++i)
    {
        auto& sample1 = colors1[i];
        auto& sample2 = colors2[i];
        covarianceR += (sample1.r - meanR1) * (sample2.r - meanR2);
        covarianceG += (sample1.g - meanG1) * (sample2.g - meanG2);
        covarianceB += (sample1.b - meanB1) * (sample2.b - meanB2);
    }
}
void CalculateSSIM(imageio::color_rgba* colors1, imageio::color_rgba* colors2, int count, double& SSIM)
{
    double meanR1;
    double meanG1;
    double meanB1;
    CalculateMean(colors1, count, meanR1, meanG1, meanB1);

    double meanR2;
    double meanG2;
    double meanB2;
    CalculateMean(colors2, count, meanR2, meanG2, meanB2);

    double squareErrorR1;
    double squareErrorG1;
    double squareErrorB1;
    CalculateSquareError(colors1, count, meanR1, meanG1, meanB1, squareErrorR1, squareErrorG1, squareErrorB1);

    double squareErrorR2;
    double squareErrorG2;
    double squareErrorB2;
    CalculateSquareError(colors2, count, meanR2, meanG2, meanB2, squareErrorR2, squareErrorG2, squareErrorB2);

    double covarianceR;
    double covarianceG;
    double covarianceB;
    CalculateCovariance(colors1, colors2, count, meanR1, meanR2, meanG1, meanG2, meanB1, meanB2, covarianceR, covarianceG, covarianceB);

    double L = 255.0;
    double k1 = 0.01;
    double c1 = (k1 * L) * (k1 * L);
    double k2 = 0.03;
    double c2 = (k2 * L) * (k2 * L);
    double c3 = c2 / 2.0;

    double errorR1ErrorR2 = sqrt(squareErrorR1 * squareErrorR2);
    double errorG1ErrorG2 = sqrt(squareErrorG1 * squareErrorG2);
    double errorB1ErrorB2 = sqrt(squareErrorB1 * squareErrorB2);

    double luminanceR = (2.0 * meanR1 * meanR2 + c1) / (meanR1 * meanR1 + meanR2 * meanR2 + c1);
    double luminanceG = (2.0 * meanG1 * meanG2 + c1) / (meanG1 * meanG1 + meanG2 * meanG2 + c1);
    double luminanceB = (2.0 * meanB1 * meanB2 + c1) / (meanB1 * meanB1 + meanB2 * meanB2 + c1);

    double contrastR = (2.0 * errorR1ErrorR2 + c2) / (squareErrorR1 + squareErrorR2 + c2);
    double contrastG = (2.0 * errorG1ErrorG2 + c2) / (squareErrorG1 + squareErrorG2 + c2);
    double contrastB = (2.0 * errorB1ErrorB2 + c2) / (squareErrorB1 + squareErrorB2 + c2);

    double structureR = (covarianceR + c3) / (errorR1ErrorR2 + c3);
    double structureG = (covarianceG + c3) / (errorG1ErrorG2 + c3);
    double structureB = (covarianceB + c3) / (errorB1ErrorB2 + c3);

    double SSIM_R = luminanceR * contrastR * structureR;
    double SSIM_G = luminanceG * contrastG * structureG;
    double SSIM_B = luminanceB * contrastB * structureB;

    SSIM = (SSIM_R + SSIM_G + SSIM_B) / 3.0;
}
#ifndef ABS
#    define ABS(x) ((x) >= 0 ? (x) : -(x))
#endif
bool ResourceUtil::CompareTexture(const char* path, const char* path1, const char* path2, double& MSE, double& PSNR, double& SSIM)
{
    MSE = 0.0;
    PSNR = 0.0;
    auto texture1 = cross::TypeCast<cross::resource::Texture>(gAssetStreamingManager->LoadSynchronously(path1));
    if (texture1 == nullptr)
    {
        return false;
    }
    int width1 = texture1->GetWidth();
    int height1 = texture1->GetHeight();
    int mipMapCount1 = texture1->GetNumMips();
    auto textureData1 = texture1->GetTextureData();
    TextureInfo textureInfo1 = textureData1->GetTextureInfo();
    TextureFormat textureFormat1 = textureData1->GetTextureFormat();
    if (textureFormat1 != TextureFormat::RGBA32)
    {
        return false;
    }
    if (textureInfo1.Dimension != TextureDimension::Tex2D)
    {
        return false;
    }
    auto imageData1 = &(textureData1->mData[0]);
    imageio::color_rgba* colors1 = (imageio::color_rgba*)imageData1;

    auto texture2 = cross::TypeCast<cross::resource::Texture>(gAssetStreamingManager->LoadSynchronously(path2));
    if (texture2 == nullptr)
    {
        return false;
    }
    int width2 = texture2->GetWidth();
    int height2 = texture2->GetHeight();
    int mipMapCount2 = texture2->GetNumMips();
    auto textureData2 = texture2->GetTextureData();
    TextureInfo textureInfo2 = textureData2->GetTextureInfo();
    TextureFormat textureFormat2 = textureData2->GetTextureFormat();
    if (textureFormat2 != TextureFormat::RGBA32)
    {
        return false;
    }
    if (textureInfo2.Dimension != TextureDimension::Tex2D)
    {
        return false;
    }
    auto imageData2 = &(textureData2->mData[0]);
    imageio::color_rgba* colors2 = (imageio::color_rgba*)imageData2;

    if (width1 != width2 || height1 != height2)
    {
        return false;
    }
    int width = width1;
    int height = height1;

    double SE_R = 0.0;
    double SE_G = 0.0;
    double SE_B = 0.0;
    imageio::image img(width, height);
    for (auto y = 0; y < height; ++y)
    {
        for (auto x = 0; x < width; ++x)
        {
            auto idx = y * width + x;
            auto& sample1 = colors1[idx];
            auto& sample2 = colors2[idx];
            auto& color = img(x, y);
            color.r = static_cast<uint8_t>(ABS(sample1.r - sample2.r));
            color.g = static_cast<uint8_t>(ABS(sample1.g - sample2.g));
            color.b = static_cast<uint8_t>(ABS(sample1.b - sample2.b));
            color.a = 255;

            SE_R += color.r * static_cast<int>(color.r);
            SE_G += color.g * static_cast<int>(color.g);
            SE_B += color.b * static_cast<int>(color.b);
        }
    }
    int count = width * height;
    double MSE_R = SE_R / count;
    double MSE_G = SE_G / count;
    double MSE_B = SE_B / count;
    MSE = (MSE_R + MSE_G + MSE_B) / 3.0;
    double MAX_I = 255.0;
    double MAX_I_SQ = MAX_I * MAX_I;
    PSNR = 10.0 * log10(MAX_I_SQ / MSE);

    CalculateSSIM(colors1, colors2, count, SSIM);

    imageio::save_png(path, img);

    return true;
}
void ResourceUtil::Editor_SaveImage(const char* filename, int width, int height, unsigned int* data) 
{
    imageio::image img(width, height);
    int dataSize = width * height * sizeof(imageio::color_rgba);
    memcpy(img.get_ptr(), data, dataSize);
    imageio::save_png(filename, img, imageio::cImageSaveAll);
}
cross::ImageInfo ResourceUtil::Editor_LoadImage(const char* filename)
{
    cross::ImageInfo info;
    imageio::image img = {};
    bool b = imageio::load_image(filename, img);
    if (b)
    {

        int dataSize = (img.get_width()) * (img.get_height()) * sizeof(imageio::color_rgba);
        info.Data = new unsigned int[dataSize];
        memcpy(info.Data, img.get_ptr(), dataSize);
        info.Width = img.get_width();
        info.Height = img.get_height();
    }
    return info;
}

unsigned int* ResourceUtil::Editor_CreateImageBuffer(int width, int height)
{
    int dataSize = width * height * sizeof(imageio::color_rgba);
    unsigned int* data = new unsigned int[dataSize];
    memset(data, 0, dataSize);
    return data;
 }

void ResourceUtil::Editor_FreeImage(unsigned int* image)
{
    if (image)
    {
        delete[] image;
    }
}
void ResourceUtil::CrossEngine_DeleteResource(const char* filename)
{
    gResourceMgr.DeleteResource(filename);
}
void ResourceUtil::CrossEngine_ReloadResource(const char* filename) 
{
    gResourceMgr.ReloadResource(filename, nullptr);
}
unsigned int* ResourceUtil::Editor_CreateImage(const char* filename, int width, int height, unsigned int color)
 {
    int dataSize = width * height * sizeof(imageio::color_rgba);
    unsigned int* data = new unsigned int[dataSize];
    memset(data, 0, dataSize);
    return data;
}
void ResourceManager::GarbageCollect(bool forcegc, bool disCardInterval)
    {
    QUICK_SCOPED_CPU_TIMING("GarbageCollect");

    {
        if (!mCollectGrabge)
            return;

        bool CollectGarbage = true;
        if (forcegc || mNeedToRelease > 0 || mForceDeleteResource.size() > 0)
        {
            CollectGarbage = true;
        }
        else
        {
            if (!disCardInterval && mGarbageCollectFrameInterval > 0)
            {
                auto* frameParamMgr = EngineGlobal::Inst().GetFrameParamMgr();
                auto fp = frameParamMgr->GetCurrentGameFrameParam();
                if (fp->GetFrameCount() - mLastCollectFrameId < mGarbageCollectFrameInterval)
                {
                    CollectGarbage = false;
                }
                else
                {
                    mLastCollectFrameId = fp->GetFrameCount();
                    mLastCollectTime = fp->GetTime();
                }
            }
        }
        if (!CollectGarbage)
            return;
        std::vector<ResourcePtr> needToParrallelDelete;
        UInt32 accumulateThisFrame = 0;
        mNeedToRelease = 0;
        {
            if (mMutex.try_lock())
            {
                for (auto numPendingDestroy = 1; numPendingDestroy;)
                {
                    numPendingDestroy = 0;

                    UInt32 needReleaseThisFrame = mReleasePerFrame;

                    for (auto iter = mResources.begin(); iter != mResources.end();)
                    {
                        if (iter->second.use_count() == 1)
                        {
                            if (accumulateThisFrame < needReleaseThisFrame)
                            {
                                iter->second->PostDestroy();
                                if (iter->second.use_count() == 1 && mEnableParallelDelete)
                                {
                                    needToParrallelDelete.push_back(iter->second);
                                }
                                iter = mResources.erase(iter);
                                numPendingDestroy++;

                                accumulateThisFrame++;
                            }
                            else
                            {
                                mNeedToRelease++;
                                iter++;
                            }
                        }
                        else
                        {
                            iter++;
                        }
                    }

                    for (auto iter = mForceDeleteResource.begin(); iter != mForceDeleteResource.end();)
                    {
                        if (iter->second.use_count() == 1)
                        {
                            iter->second->PostDestroy();
                            iter = mForceDeleteResource.erase(iter);
                        }
                        else
                        {
                            iter++;
                        }
                    }
                }

                mMutex.unlock();
            }
        }

        if (accumulateThisFrame > 0)
        {
            LOG_INFO("GC {} {} {} {}", mNeedToRelease, accumulateThisFrame, mNeedResetResources.size(), mResources.size());
        }
        if (mEnableParallelDelete && !needToParrallelDelete.empty())
        {

            const auto numWorkers = threading::TaskSystem::GetNumWorkerThreadsForTask();
            UInt32 batchSize = static_cast<UInt32>(needToParrallelDelete.size() / numWorkers + 1);
            cross::threading::ParallelFor(static_cast<SInt32>(numWorkers), [&](auto taskIndex) {
                for (UInt32 i = 0; i < batchSize; ++i)
                {
                    UInt32 index = taskIndex * batchSize + i;
                    if (index < needToParrallelDelete.size())
                    {
                        needToParrallelDelete[index].reset();
                    }
                }
            });
            needToParrallelDelete.clear();
        }
    }
}

ResourcePtr ResourceManager::CreateResourceByClassID(uint64_t classID, const std::string filepath)
{
    using namespace gbf::reflection;
    //if (ResourcePtr ret = Find(filepath.c_str()))
    //{
    //    return ret;
    //}

    ResourcePtr pRes = ResourcePtr(TYPE_CAST(Resource*, ResourceBase::Produce(classID)));
    if (pRes)
    {
        if (auto rttiBase = dynamic_cast<RttiBase*>(pRes.get()))
        {
            pRes->__bind_rtti_info(query_meta_class_by_custom_id(classID), StorageType::StorageRemote, next_reflection_obj_id());
        }
    }
    pRes->CreateAsset(filepath);
    pRes->GetAsset()->EnsureGuid();
    AddResource(filepath.c_str(), pRes);
    return pRes;
}

void ResourceManager::BuildGuidMap()
{
    gResourceConvertMgr.Initialize();
    gResourceConvertMgr.Dump(true);
}

void ResourceManager::InitDefaultResource()
{
    auto renderSetting = EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting();

    LoadDefaultRes<cross::resource::Texture>(Defaulttexturepath);
    LoadDefaultRes<cross::resource::Texture2D>(Defaulttexturepath);
    LoadDefaultRes<cross::resource::TextureCube>(Defaulttexturecubepath);
    LoadDefaultRes<cross::resource::Texture2DArray>(Defaulttexturearraypath);
    LoadDefaultRes<cross::resource::Shader>(Defaultshaderpath);

    LoadDefaultRes<cross::resource::Fx>(renderSetting->DefaultFX.c_str());
    LoadDefaultRes<cross::resource::Material>(renderSetting->DefaultMaterial.c_str());
    LoadDefaultRes<cross::resource::MaterialInterface>(renderSetting->DefaultMaterial.c_str());

    LoadDefaultRes<cross::resource::MeshAssetDataResource>(Defaultmodelpath);
}

bool cross::ResourceManager::ReloadResource(ResourcePtr res)
{
    return gResourceAssetMgr.LoadNDAResource(res);
}

void ResourceManager::TryReloadResource(std::string FilePath, EditorGeneralCallBack CallBack)
{
    std::string theabsolutePath{};
    mFileSystem->GetAbsolutePath(FilePath, theabsolutePath);
    std::string RelPath{};
    mFileSystem->GetRelativePath(theabsolutePath, RelPath);

    if (mResources.find(RelPath) != mResources.end())
    {
        GetResource(RelPath.c_str(), true, [this, CallBack](ResourcePtr res) {
            if (res->GetClassID() == ClassID(Shader))
            {
                std::vector<ResourcePtr> AllFxs;
                std::vector<ResourcePtr> AllMaterials;
                std::for_each(mResources.begin(), mResources.end(), [&res, &AllFxs, &AllMaterials](auto& Elem) {
                    if (Elem.second->GetClassID() == ClassID(Fx))
                    {
                        if (Elem.second->GetAsset()->CheckReference(res->GetAsset()))
                        {
                            AllFxs.push_back(Elem.second);
                        }
                    }
                    if (Elem.second->GetClassID() == ClassID(Material))
                    {
                        AllMaterials.push_back(Elem.second);
                    }
                });
                for (auto& fx : AllFxs)
                {
                    fx->ResetResource();
                    gResourceAssetMgr.LoadNDAResource(fx);
                    for (auto& mat : AllMaterials)
                    {
                        if (mat->GetAsset())
                        {
                            // Here reference may be GUID or Relative Resource Path
                            if (mat->GetAsset()->CheckReference(fx->GetAsset()))
                            {
                                mat->ResetResource();
                            }
                        }
                    }
                }
            }
            else if (res->GetClassID() == ClassID(ComputeShader))
            {
                gResourceAssetMgr.LoadNDAResource(res);
            }
            else if (res->GetClassID() == ClassID(Fx))
            {
                std::vector<MaterialPtr> AllMaterials;
                std::for_each(mResources.begin(), mResources.end(), [&res, &AllMaterials](auto& Elem) {
                    if (Elem.second->GetClassID() == ClassID(Material))
                    {
                        AllMaterials.push_back(TypeCast<resource::Material>(Elem.second));
                    }
                });
                for (auto& mat : AllMaterials)
                {
                    if (AssetPtr assetPtr = mat->GetAsset(); assetPtr && mat->GetFx())
                    {
                        // Here reference may be GUID or Relative Resource Path
                        if (mat->GetFx()->GetName() == res->GetAsset()->GetName() || assetPtr->CheckReference(res->GetAsset()))
                        {
                            mat->ResetResource();
                        }
                    }
                }

                TypeCast<resource::Fx>(res)->NotifyChangeRecursively();
            }
            else if (res->GetClassID() == ClassID(Material))
            {}
            else if (res->GetClassID() == ClassID(Texture2D))
            {
                TypeCast<cross::resource::Texture2D>(res)->PostDeserialize();
            }
            // else if (res->GetClassID() == ClassID(TextureCube))
            //{
            //     TypeCast<cross::resource::TextureCube>(res)->PostDeserialize();
            // }
            // else if (res->GetClassID() == ClassID(Texture3D))
            //{
            //     TypeCast<cross::resource::Texture3D>(res)->PostDeserialize();
            // }
            // else if (res->GetClassID() == ClassID(Texture2DArray))
            //{
            //     TypeCast<cross::resource::Texture2DArray>(res)->PostDeserialize();
            // }
            else if (res->GetClassID() == ClassID(MeshAssetDataResource))
            {
                AddChangedResource(res);
            }
            /*else if (res->GetClassID() == ClassID(CurveControllerRes))
            {
                TypeCast<cross::CurveControllerRes>(res);
            }*/
            else if (res->GetClassID() == ClassID(TsResource))
            {
                TypeCast<cross::resource::TsResource>(res)->HotReloadScript();
            }

            if (CallBack)
            {
                CallBack();
            }
            ResourceReloadEvent e{
                res->GetName(),
                static_cast<ClassIDType>(res->GetClassID()),
            };
            DispatchImmediateEvent<ResourceReloadEvent>(e);
        });
    }
}

void ResourceManager::TryReloadResource(std::string FilePath)
{
    TryReloadResource(FilePath, nullptr);
}

void ResourceManager::ImmediateReloadResource(std::string FilePath)
{
    std::string absPath{};
    mFileSystem->GetAbsolutePath(FilePath, absPath);
    std::string relPath{};
    mFileSystem->GetRelativePath(absPath, relPath);
    ResourcePtr resourcePtr = Find(relPath.c_str());

    if (resourcePtr)
    {
        ClassIDType classID = static_cast<ClassIDType>(resourcePtr->GetClassID());
        if (classID == ClassID(Shader) || classID == ClassID(ComputeShader))
        {
            TryReloadResource(FilePath);
            return;
        }

        if (IsRenderRelatedResource(classID))
        {
            threading::FlushRenderingCommands();
        }
        resourcePtr->ResetResource();

        if (IsScriptFile(relPath))
        {
            gResourceAssetMgr.LoadScriptResource(resourcePtr);
        }
        else
        {
            gResourceAssetMgr.LoadNDAResource(resourcePtr);
        }

        if (classID == ClassID(Fx))
        {
            std::vector<MaterialPtr> AllMaterials;
            std::for_each(mResources.begin(), mResources.end(), [&resourcePtr, &AllMaterials](auto& Elem) {
                if (Elem.second->GetClassID() == ClassID(Material))
                {
                    AllMaterials.push_back(TypeCast<resource::Material>(Elem.second));
                }
            });
            for (auto& mat : AllMaterials)
            {
                if (AssetPtr assetPtr = mat->GetAsset(); assetPtr && mat->GetFx())
                {
                    // Here reference may be GUID or Relative Resource Path
                    if (mat->GetFx()->GetName() == resourcePtr->GetAsset()->GetName() || assetPtr->CheckReference(resourcePtr->GetAsset()))
                    {
                        mat->ResetResource();
                    }
                }
            }
        }
        else if (classID == ClassID(Material))
        {
            TypeCast<cross::resource::Material>(resourcePtr)->ResetResource();
        }
        else if (classID == ClassID(Texture2D))
        {
            TypeCast<cross::resource::Texture2D>(resourcePtr)->PostDeserialize();
        }
        else if (classID == ClassID(TextureCube))
        {
            TypeCast<cross::resource::TextureCube>(resourcePtr)->PostDeserialize();
        }
        else if (classID == ClassID(Texture3D))
        {
            TypeCast<cross::resource::Texture3D>(resourcePtr)->PostDeserialize();
        }
        else if (classID == ClassID(MeshAssetDataResource))
        {
            AddChangedResource(resourcePtr);
        }
    }
}

cross::ResourcePtr ResourceManager::Find(const char* fileName)
{
    // Convert absolute path to relative path
    std::string relativeFilePath = fileName;
    if (PathHelper::IsAbsoluteFilePath(fileName))
    {
        relativeFilePath = PathHelper::GetRelativePath(fileName);
    }

    std::shared_lock<std::shared_mutex> lockGuard(mMutex);
    auto resource = mResources.find(relativeFilePath);
    if (resource != mResources.end())
        return resource->second;
    return {};
}

cross::ResourcePtr cross::ResourceManager::FindPostDeserizalized(const char* fileName)
{
    std::shared_lock<std::shared_mutex> lockGuard(mMutex);
    auto resource = mResources.find(fileName);
    if (resource != mResources.end())
        return resource->second;
    else
    {
        return ResourcePtr(nullptr);
    }
}

cross::ResourcePtr cross::ResourceManager::FindDeserialized(const char* fileName)
{
    // try find in the dense
    std::shared_lock<std::shared_mutex> deserialize_lock(mDeserializedMutex);
    auto ret = mDeserializedResources.find(fileName);
    if (ret != mDeserializedResources.end())
    {
        return ret->second;
    }
    else
    {
        return ResourcePtr(nullptr);
    }
}

cross::ResourcePtr ResourceManager::FindAnywhere(const char* fileName)
{
    if (auto res = FindPostDeserizalized(fileName))
        return res;
    return FindDeserialized(fileName);
}

void ResourceManager::AddResourceDependencies(const DeserializeNode& node, ResourcePtr resource)
{
    if (node.IsString())
    {
        std::string nodeStr = node.AsString();
        const auto& path = ConvertGuidToPath(nodeStr);
        if (EngineGlobal::GetFileSystem()->HaveFile(path))
        {
            resource->AddReferenceResource(nodeStr);
        }
    }
    else if (node.IsObject())
    {
        for (auto it = node.begin(); it != node.end(); ++it)
        {
            AddResourceDependencies(it.Value(), resource);
        }
    }
    else if (node.IsArray())
    {
        for (int idx = 0; idx < node.Size(); idx++)
        {
            AddResourceDependencies(node.At(idx), resource);
        }
    }
}

void cross::ResourceManager::AddResourceDependencies(const DeserializeNode& node, std::set<std::string>& outDenpencies)
{
    if (node.IsString())
    {
        std::string nodeStr = node.AsString();
        const auto& path = ConvertGuidToPath(nodeStr);
        if (EngineGlobal::GetFileSystem()->HaveFile(path))
        {
            outDenpencies.emplace(nodeStr);
        }
    }
    else if (node.IsObject())
    {
        for (auto it = node.begin(); it != node.end(); ++it)
        {
            AddResourceDependencies(it.Value(), outDenpencies);
        }
    }
    else if (node.IsArray())
    {
        for (int idx = 0; idx < node.Size(); idx++)
        {
            AddResourceDependencies(node.At(idx), outDenpencies);
        }
    }
}

void cross::ResourceManager::MarkResourceChanged(const std::string& path)
{
    filesystem::MarkFileChanged(path);
}

void cross::ResourceManager::RemoveResourceThumbnail(const std::string& path)
{
    auto relPath = mFileSystem->GetRelativePath(mFileSystem->GetAbsolutePath(path));
    auto thumbnailPath = "Intermediate/Thumb/" + relPath + ".thumb.nda";
    mFileSystem->RemoveFile(thumbnailPath);
}

void ResourceManager::BuildResourceList()
{
    gResourceConvertMgr.Dump();
}

void ResourceManager::ReLoadResourceList(const std::string& rootPath)
{
    gResourceConvertMgr.Initialize();
}

std::string cross::ResourceManager::ConvertGuidToPath(const std::string& guid)
{
    return gResourceConvertMgr.GetPath(guid, guid);
}

bool cross::ResourceManager::HasGuid(const std::string& guid)
{
    return gResourceConvertMgr.HasGuid(guid);
}

std::string cross::ResourceManager::GetGuidByPath(const std::string& path)
{
    return gResourceConvertMgr.GetGuid(path);
}

std::string cross::ResourceManager::ConvertPathToGuid(const std::string& path)
{
    return gResourceConvertMgr.GetGuid(path, path);
}

void cross::ResourceManager::DeleteFilePaths(std::vector<std::string> paths)
{
#if !COMPILE_FOR_DEPLOY
    auto fileSys = EngineGlobal::GetFileSystem();
    for (auto& path : paths)
    {
        auto relPath = mFileSystem->GetRelativePath(path);
        if (!fileSys->HaveFile(relPath))
        {
            gResourceConvertMgr.Remove(relPath);
#    if CROSSENGINE_EDITOR
            if (auto res = Find(relPath.c_str()); res != nullptr)
            {
                res->AfterAssetDelete();
            }
#    endif
        }
    }
#endif
}

void cross::ResourceManager::ChangeFilePaths(std::vector<std::string> oldPaths, std::vector<std::string> newPaths)
{
    auto fileSys = EngineGlobal::GetFileSystem();
    for (size_t i = 0; i < oldPaths.size(); i++)
    {
        auto oldPath = mFileSystem->GetRelativePath(oldPaths[i]);
        auto newPath = mFileSystem->GetRelativePath(newPaths[i]);
        if (!fileSys->HaveFile(oldPath))
        {
            gResourceConvertMgr.Change(oldPath, newPath);
        }
        if (auto it = mResources.find(oldPath.c_str()); it != mResources.end())
        {
            it->second->GetAsset()->SetName(newPath.c_str());
            mResources.emplace(newPath.c_str(), it->second);
            mResources.erase(oldPath.c_str());
        }
    }
}

void cross::ResourceManager::AddNewFiles(std::vector<std::string> paths, bool newGuid)
{
    for (auto& path : paths)
    {
        gResourceConvertMgr.AddNdaFile(path, newGuid, false);
    }
}

void cross::ResourceManager::AddFileByHeader(const std::string& path, ResourceMetaHeader& metaHeader)
{
    // 1 check Intermediate
    if (StringHelper::StartsWith(path, "Intermediate/"))
        return;
    auto relPath = mFileSystem->GetRelativePath(mFileSystem->GetAbsolutePath(path));
    if (!CrossUUID::CheckVaid(metaHeader.mGuid))
    {
        if (auto guid = gResourceConvertMgr.GetGuid(relPath, "", true); guid != "")
            metaHeader.mGuid = guid;
        else
            metaHeader.mGuid = CrossUUID::GenerateCrossUUID().ToString();
    }
    gResourceConvertMgr.AddNdaFile(relPath, metaHeader);
}

bool cross::ResourceManager::CheckFileByGuid(const std::string& guid)
{
    auto path = ConvertGuidToPath(guid);
    if (DefaultResourcePaths.find(path) != DefaultResourcePaths.end())
        return true;
    return EngineGlobal::GetFileSystem()->HaveFile(path);
}

std::string cross::ResourceManager::GenFileGuid(const std::string& path)
{
    if (auto guid = gResourceConvertMgr.GetGuid(path, "", true); guid != "")
        return guid;

    return CrossUUID::GenerateCrossUUID().ToString();
}

bool ResourceManager::IsRenderRelatedResource(ClassIDType ClassID)
{
    if (ClassID == ClassID(Shader) || ClassID == ClassID(Fx) || ClassID == ClassID(Material) || ClassID == ClassID(ComputeShader) || ClassID == ClassID(MeshAssetDataResource) || ClassID == ClassID(InstanceDataResource))
    {
        return true;
    }
    else
    {
        return false;
    }
}

void ResourceManager::Tick()
{
    QUICK_SCOPED_CPU_TIMING("Tick");


    mChangedResourceMap.clear();

    for (auto& [res, callback] : mNeedResetResources)
    {
        if (IsRenderRelatedResource(static_cast<ClassIDType>(res->GetClassID())))
        {
            threading::FlushRenderingCommands();
        }
        ResourcePtr resourcePtr = res;
        resourcePtr->ResetResource();
        std::string absolutePath = EngineGlobal::GetFileSystem()->GetAbsolutePath(resourcePtr->GetName());
        std::string relPath = EngineGlobal::GetFileSystem()->GetRelativePath(absolutePath);

        bool isScriptFile = IsScriptFile(relPath);

        if (isScriptFile)
        {
            gResourceAssetMgr.LoadScriptResource(resourcePtr);
        }
        else
        {
            gResourceAssetMgr.LoadNDAResource(resourcePtr);
        }
        if (callback)
        {
            callback(resourcePtr);
        }
    }

    mLastFrameResettedResources = std::move(mNeedResetResources);
}

void ResourceManager::AddToResetResouraceList(ResourcePtr inResPtr)
{
    Assert(threading::TaskSystem::IsInGameThread());
    mNeedResetResources.try_emplace(inResPtr, nullptr);
}

ResourcePtr cross::ResourceManager::GetResource(const char* input, bool inNeedReset, std::function<void(ResourcePtr inRes)> inCallback)
{
    SCOPED_CPU_TIMING(GroupAssetStreaming, __FUNCTION__);
    std::string inFileName{input};

    if (inFileName.empty())
    {
        return mNullResource;
    }

    inFileName = gResourceMgr.ConvertGuidToPath(inFileName);
    std::string theabsolutePath{};
    mFileSystem->GetAbsolutePath(inFileName, theabsolutePath);
    std::string relativePath{};
    mFileSystem->GetRelativePath(theabsolutePath, relativePath);
    ResourcePtr object = Find(relativePath.c_str());

    if (object)
    {

        bool resNeedReload = false;

        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeCrossEditor)
        {
            resNeedReload = object->NeedReload();
        }


        if ((inNeedReset || resNeedReload) && object->HasAsset())
        {
            if (auto node = mNeedResetResources.find(object); node != mNeedResetResources.end())
            {
                // Reload with callback has a high priority.
                if (node->second == nullptr)
                {
                    node->second = inCallback;
                }
            }
            else
            {
                mNeedResetResources.emplace(object, inCallback);
            }
        }

        return object;
    }

    std::string strFileName(relativePath.data());
    bool isScriptFile = IsScriptFile(strFileName);
    resource::ResourceLoadError loadError = resource::ResourceLoadError::Succeeded;

    if (!isScriptFile)
    {
        auto info = GetOrCreateNDAFileInfo(inFileName);
        if (info.first == nullptr || info.second == nullptr)
        {
            LOG_ERROR("LoadNDAFile [ {} ] with empty file", inFileName.data());
            return mNullResource;
        }

        ResourcePtr obj(gResourceAssetMgr.LoadNDAFile(info.second, *info.first));
        if (!obj)
        {
            if (loadError == resource::ResourceLoadError::ClassIDError)
            {
                LOG_ERROR("LoadNDAFile [ {} ] classID error!", relativePath.data());
                return mNullResource;
            }
            else if (loadError == resource::ResourceLoadError::ReadHeaderFailed)
            {
                LOG_ERROR("LoadNDAFile read header [ {} ] failed!", relativePath.data());
                return mNullResource;
            }
            else
            {
                LOG_ERROR("LoadNDAFile  [ {} ] failed with unknown reason", relativePath.data());
                return mNullResource;
            }
        }

        object = std::move(obj);
        // a potential gap between Find and addDeserializeResource
        // could result in load one resource multiple times? @yazhenyuan
        AddDerserializedResource(relativePath.c_str(), object);
    }
    else
    {
        ResourcePtr obj(gResourceAssetMgr.LoadScriptFile(relativePath.data(), loadError));
        if (!obj)
        {
            LOG_ERROR("LoadSriptFile [ {} ] failed!", relativePath.data());
            return mNullResource;
        }

        object = std::move(obj);
        AddDerserializedResource(relativePath.c_str(), object);
    }

    return object;
}

bool cross::ResourceManager::CreateResource(ResourceMetaHeader& metaHeader, const std::string& contentStr, const std::string& path)
{
    return Resource::Serialize(metaHeader, contentStr, path);
}

void cross::ResourceManager::DeleteResource(const char* name)
{
    auto res = Find(ConvertGuidToPath(name).c_str());
    if (res)
    {
        std::lock_guard<std::shared_mutex> lockGuard(mMutex);
        mResources.erase(res->GetName());
        mForceDeleteResource[res->GetName()] = res;
    }
}

bool cross::ResourceManager::GetResourceMetaHeader(const std::string& path, ResourceMetaHeader& header)
{
    auto resource = Find(path.c_str());
    if (resource)
    {
        header = resource->GetAsset()->GetHeader();
        return true;
    }

    auto ndaInfo = GetOrCreateNDAFileInfo(path).first;

    if (ndaInfo)
    {
        header = ndaInfo->GetMetaHeader();
        return ndaInfo->HasMetaHeader();
    }
    return false;
}

std::pair<cross::resource::LoadNDAFileInfo*, Archive*> ResourceManager::GetOrCreateNDAFileInfo(const std::string& file)
{
    std::string theabsolutePath{};
    cross::EngineGlobal::Inst().GetFileSystem()->GetAbsolutePath(file, theabsolutePath);
    std::string relativeFileName{};
    cross::EngineGlobal::Inst().GetFileSystem()->GetRelativePath(theabsolutePath, relativeFileName);
    {
        std::shared_lock<std::shared_mutex> lockGuard(mNDAFileMutex);
        auto findResult = mCachedNDAFiles.find(relativeFileName);
        if (findResult != mCachedNDAFiles.end())
        {
            findResult->second.second->Seek(0);
            return findResult->second;
        }
    }

    {
        auto ret = ReadNDAFileAndCacheImp(relativeFileName);
        return ret;
    }

    return {nullptr, nullptr};
}

std::pair<cross::resource::LoadNDAFileInfo*, Archive*> cross::ResourceManager::ReadNDAFileAndCacheImp(const std::string& file)
{
    std::lock_guard<std::shared_mutex> lockGuard(mNDAFileMutex);
    auto binary_archive = gResourceAssetMgr.MemoryMapFile(file.c_str(), ResourceManager::HeadMapSize);
    cross::resource::LoadNDAFileInfo* nda_file_info = nullptr;
    if (binary_archive != nullptr)
    {
        nda_file_info = new cross::resource::LoadNDAFileInfo;
        nda_file_info->SetFileName(file);
        gResourceAssetMgr.GetLoadNDAInfo(binary_archive, *nda_file_info);
        mCachedNDAFiles[file.c_str()] = {nda_file_info, binary_archive};
        binary_archive->Seek(0);
        nda_file_info->SetFileName(file);
        return {nda_file_info, binary_archive};
    }
    return {nullptr, nullptr};
}

void cross::ResourceManager::RemoveCachedNDAFile(const std::string& file)
{
    if (mCachedNDAFiles.count(file))
    {
        delete mCachedNDAFiles[file].first;
        delete mCachedNDAFiles[file].second;
        mCachedNDAFiles.erase(file);
    }
}

ResourcePtr ResourceManager::AddResource(const char* fileName, ResourcePtr resource, bool isRuntimeRes)
{
    if (isRuntimeRes)
    {
        std::lock_guard<std::shared_mutex> lockGuard(mMutex);
        std::lock_guard<std::shared_mutex> fileGuard(mNDAFileMutex);
        if (mResources.find(fileName) == mResources.end())
        {
            mResources.emplace(fileName, resource);
        }

        RemoveCachedNDAFile(fileName);
        RemoveDeserializedResource(fileName, resource);
        return resource;
    }
    else
    {
        std::string relativeFileName = mFileSystem->GetRelativePath(fileName);
        std::lock_guard<std::shared_mutex> lockGuard(mMutex);
        std::lock_guard<std::shared_mutex> fileGuard(mNDAFileMutex);
        if (mResources.find(relativeFileName.c_str()) == mResources.end())
        {
            mResources.emplace(relativeFileName.c_str(), resource);
        }
        RemoveCachedNDAFile(relativeFileName.c_str());
        RemoveDeserializedResource(relativeFileName.c_str(), resource);
        return resource;
    }
}

ResourcePtr cross::ResourceManager::AddDerserializedResource(const char* fileName, ResourcePtr resource)
{
    std::lock_guard<std::shared_mutex> lockGurad(mDeserializedMutex);
    mDeserializedResources.insert({fileName, resource});
    return resource;
}

void ResourceManager::RemoveDeserializedResource(const char* filename, ResourcePtr resource)
{
    std::lock_guard<std::shared_mutex> lockGurad(mDeserializedMutex);
    if (mDeserializedResources.count(filename))
    {
        mDeserializedResources.erase(filename);
    }
}

UInt32 ResourceManager::GetMemoryUsage() const
{
    return UInt32(0);
}

// Async Stuff start///////////////

bool sDumpAsyncTimeState = false;
int sTickCount = 0;
int sDumpCountNum = 10;

resource::ResourceLoadError ResourceManager::GetResourecHeaderInfo(const char* fileName, uint32_t& outResClassId, int32_t& outResVersion)
{
    resource::LoadNDAFileInfo fileInfo;
    fileInfo.SetFileName(fileName);
    if (!gResourceAssetMgr.GetLoadNDAInfo(ConvertGuidToPath(fileName).c_str(), fileInfo))
    {
        return resource::ResourceLoadError::ReadHeaderFailed;
    }
    outResClassId = fileInfo.GetClassID();
    outResVersion = fileInfo.GetVersion();   // version ?
    return resource::ResourceLoadError::Succeeded;
}

void ResourceManager::UpdateAsync()
{
    if (!sDumpAsyncTimeState && sTickCount++ == sDumpCountNum)
    {
        gResourceMgr.OutputAsyncTimeStatue();
        sDumpAsyncTimeState = true;
    }
}

void ResourceManager::OutputAsyncTimeStatue()
{
    // mAsyncTimeState.OutputState();
}

const char* ResourceManager::GetDefaultTexturePath()
{
    return "EngineResource/Texture/DefaultTexture.nda";
}

const char* ResourceManager::GetBlackTexturePath()
{
    return "EngineResource/Texture/BlackTexture.nda";
}

void ResourceManager::DispatchMaterialRenderingCommands()
{
    mMaterialRenderingCommandBatcher.Dispatch();
}

class ResourceTree
{
    class RessourceTreeItem;

public:
    ResourceTree(std::string inRootPath) { mRoot = new RessourceTreeItem(inRootPath, nullptr); }

    ~ResourceTree()
    {
        delete mRoot;
        mRoot = nullptr;

        for (auto resItem : mChilds)
            delete resItem;

        mChilds.clear();
    }

    void AddChild(RessourceTreeItem* inNewItem)
    {
        Assert(mRoot);
        inNewItem->SetParent(mRoot);
        mChilds.push_back(inNewItem);
    }

private:
    class RessourceTreeItem
    {
    public:
        RessourceTreeItem(std::string inPath, RessourceTreeItem* inParent)
        {
            mPath = inPath;
            mParent = inParent;
        }

        ~RessourceTreeItem()
        {
            for (auto item : mChilds)
                delete item;

            mChilds.clear();
        }

        void AddChild(RessourceTreeItem* inChild)
        {
            Assert(inChild);
            mChilds.push_back(inChild);
        }

        void SetParent(RessourceTreeItem* inParent) { mParent = inParent; }

    private:
        std::string mPath = "";
        RessourceTreeItem* mParent = nullptr;
        std::vector<RessourceTreeItem*> mChilds;
    };
    RessourceTreeItem* mRoot = nullptr;
    std::vector<RessourceTreeItem*> mChilds;
};
// Async Stuff end///////////////


bool ResourceManager::ResourceLoaded(const char* name)
{
    return Find(ConvertGuidToPath(name).c_str()).get() != nullptr;
}

bool ResourceManager::ReloadResource(const char* name, std::function<void(ResourcePtr inRes)> callbackFunc)
{
    // LOG_WARN("Deprecated call to \"ResourceManager::ReloadResourceAbsolute\", not thread safe.");

    std::string relativePath = PathHelper::GetRelativePath(ConvertGuidToPath(name));
    auto res = Find(relativePath.c_str());

    bool result = true;
    GetResource(name, true, callbackFunc);

    return result;
}

AssetHeader ResourceManager::GetResourceHeader(char const* name)
{
    resource::LoadNDAFileInfo fileInfo;
    fileInfo.SetFileName(name);
    if (!gResourceAssetMgr.GetLoadNDAInfo(ConvertGuidToPath(name).c_str(), fileInfo))
    {
        LOG_ERROR("ResourceManager::GetResourceHeader(const char*) read header [ {} ] failed!", name);
    }
    AssetHeader header;
    header.mMagicNumber = fileInfo.GetMagicNumber();
    header.mVersion = fileInfo.GetVersion();
    header.mClassID = fileInfo.GetClassID();
    header.mDataSize = fileInfo.GetDataSize();
    return header;
}
void ResourceUtil::ResourceSaveToFile(Resource* resource, char const* resPath)
{

    // flat buffer Serialize
    if (resource->GetClassID() == ClassID(SkeletonResource))
    {
        auto skResPtr = TYPE_CAST(SkeletonResource*, resource);
        skResPtr->Serialize(resPath);
        return;
    }
    if (resource->GetClassID() == ClassID(AnimSequenceRes))
    {
        auto animSeqResPtr = TYPE_CAST(anim::AnimSequenceRes*, resource);
        animSeqResPtr->Serialize(resPath);
        return;
    }
    if (resource->GetClassID() == ClassID(AnimCompositeRes))
    {
        auto animCmpResPtr = TYPE_CAST(anim::AnimCompositeRes*, resource);
        animCmpResPtr->Serialize(resPath);
        return;
    }
    if (resource->GetClassID() == ClassID(Texture2DArray))
    {
        auto t2dResPtr = TYPE_CAST(cross::resource::Texture2DArray*, resource);
        t2dResPtr->Serialize();
        return;
    }

    cross::SerializeNode emptyNode = {};
    std::string strResPath(resPath);

    resource->Serialize(std::move(emptyNode), resPath);
}
bool ResourceUtil::ResourceCheckDependency(Resource* resource, char const* path, bool recursive) 
{
    if (!resource->GetAsset())
        return false;

    auto resGuid = resource->GetAsset()->GetGuid();
    auto depGuid = gResourceMgr.ConvertPathToGuid(path);
    auto depPtr = gAssetStreamingManager->GetResource(depGuid);

    std::queue<std::string> guidQueue;
    guidQueue.push(resGuid);
    while (!guidQueue.empty())
    {
        auto& guid = guidQueue.front();
        auto resPtr = gAssetStreamingManager->GetResource(guid);
        if (resPtr)
        {
            if (resPtr->GetAsset()->CheckReference(depPtr->GetAsset()))
                return true;

            if (recursive)
            {
                for (auto& depend : resPtr->GetReferenceResources())
                {
                    auto dependGuid = gResourceMgr.ConvertPathToGuid(depend);
                    guidQueue.push(dependGuid);
                }
            }
        }
        guidQueue.pop();
    }

    return false;
}

bool ResourceManager::NeedReloadRelatedResources(const char* fileName)
    {
    bool result = false;

    AssetHeader header = GetResourceHeader(fileName);
    switch (header.mClassID)
    {
    case ClassID(Texture):
    case ClassID(Texture2D):
    case ClassID(Texture3D):
    case ClassID(TextureCube):
    case ClassID(Shader):
        result = true;
        break;
    case ClassID(Material):
        break;
    default:
        break;
    }

    return result;
}

bool ResourceManager::ReloadRelatedResources(const char* fileName)
{
#    if 0
        bool result = true;
        
        std::vector<std::string> reloadResources;
        for (ResourceContainer::iterator iter = mResources.begin(); iter != mResources.end(); iter++)
        {
            ResourcePtr resource = iter->second;
            if (resource && resource->GetAsset() && resource->HasReferenceResources())
            {
                UInt32 count = (UInt32)resource->GetReferenceResourceCount();
                for (UInt32 index = 0; index < count; index++)
                {
                    auto referenceResource = resource->GetReferenceResource(index);
                    if (!strcmp(referenceResource.c_str(), fileName))
                    {
                        reloadResources.push_back(resource->GetName());
                        break;
                    }
                }
            }
        }
        
        for (auto resource : reloadResources)
        {
            if (!ReloadResource(resource.c_str()))
            {
                result = false;
            }
        }
        return result;
#    else
    // will use the time stamp to replace the reload, maybe remove this method?
    // Assert(0);
    return false;
#    endif
}


}   // namespace cross
