#pragma once
#include "Resource/BaseClasses/ClassIDs.h"
#include "Resource/Resource.h"
#include "Resource/Animation/AnimResourceBase.h"
#include "CEAnimation/Notify/AnimNotify.h"

using namespace cross::skeleton;

namespace cross::anim
{
    struct BlendSpaceAxisRes
    {
        Axis::Type AxisType{ Axis::None };

        /* axis name */
        CEName AxisName{ NAME_NONE };

        float Min{ 0.f };

        float Max{ 100.f };

        UInt32 GridNum{ 1 };
    };

    struct BlendSpacePatternedPoint
    {	
        CEName Name{ NAME_NONE };

        /* ref anim sequence path created From Disk File */
        CEName SequencePath{ "" };

        /* coordinate reltv to specify Axis which holding by patterned Line */
        float PosX{ 0.f };

        /* coordinate value holding in points aligned which axis */
        Axis::Type Axis = Axis::X;

        BlendSpacePatternedPoint() = default;
        BlendSpacePatternedPoint(const CEName& inName, const CEName& inSequencePath, float inPosX)
            : Name(inName)
            , SequencePath(inSequencePath)
            , PosX(inPosX)
        {}
    };

    struct BlendSpacePatternedLine
    {
        /* coordinate value holding in points aligned which axis */
        Axis::Type Axis = Axis::Y;

        /* coordinate reltv to specify Axis which guaranteed by patterned grid */
        float PosY{ 0.f };

        /* All sequences point holding in current Line */
        std::vector<BlendSpacePatternedPoint> Points;
    };

    struct BlendSpacePatternedGrid
    {
        /* All lines compose into current grid */
        std::vector<BlendSpacePatternedLine> Lines;

        std::vector<BlendSpaceAxisRes> AxisData;

        inline bool IsBlendSpace1D() { return Lines.size() == 1; }
    };

    class Resource_API CEMeta(Script) AnimBlendSpaceRes : public AnimResourceBase
    {
    public:
        FRIEND_WITH_REFLECTION_MODULE;

    public:
        AnimBlendSpaceRes();
        ~AnimBlendSpaceRes() = default;
        static int GetClassIDStatic() { return ClassID(AnimBlendSpaceRes); }
        
        bool Serialize(const std::string& path);
        bool Serialize(SerializeNode&& node, const std::string& path) override;
        bool Deserialize(const DeserializeNode& node) override;

        const CEName& GetAssetName() const { return mAssetName; }
        const BlendSpacePatternedGrid& GetBlendSpaceData() const { return mBlendSpaceData; }

#if CROSSENGINE_EDITOR
        const std::string& GetPreviewBaseAnimPath() const
        {
            return mPreviewBaseAnimPath;
        }
#endif   // CROSSENGINE_EDITOR

        void SetAssetName(const CEName& name) { mAssetName = name; }

    protected:
        /* Asset base name */
        CEName mAssetName{ "" };
        /* Blend Space grid data */
        BlendSpacePatternedGrid mBlendSpaceData;

#if CROSSENGINE_EDITOR
        /* Preview Animation Path*/
        std::string mPreviewBaseAnimPath;
#endif   // CROSSENGINE_EDITOR
    };

}
