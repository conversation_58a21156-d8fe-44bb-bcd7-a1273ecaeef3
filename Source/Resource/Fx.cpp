#include "EnginePrefix.h"
#include "Fx.h"
#include "Resource/AssetStreaming.h"
#include "Resource/Texture/Texture2D.h"
#include "Resource/Material.h"
#include "CrossBase/String/StringCodec.h"

namespace cross { namespace resource {

    Fx::Pass::Pass(const ShaderPtr& shader, const NameID& key)
    {
        name = key;
        mShaderPtr = shader;
    }

    void AddIfNotExist(NameID value, std::vector<NameID>& container)
    {
        if (std::find(container.begin(), container.end(), value) == container.end())
            container.emplace_back(value);
    }

    void RemoveIfExist(NameID value, std::vector<NameID>& container)
    {
        auto iter = std::find(container.begin(), container.end(), value);
        if (iter != container.end())
            container.erase(iter);
    }

    void Fx::CopyFrom(const Fx& fx)
    {
        mVersion = fx.GetVersion();
        mPropertyNames = fx.mPropertyNames;
        mProperties = fx.mProperties;
        mPassNames = fx.mPassNames;
        mPasses = fx.mPasses;
        mGroupNames = fx.mGroupNames;
        mPropertyFlags = fx.mPropertyFlags;
        mExpressionsString = fx.mExpressionsString;
        mDefines = fx.mDefines;

        UnregistAllMpc();
        mMpcs = fx.mMpcs;
        RegistAllMpc();

        RefreshProperties();
        RefreshChildMaterial();
        PostDeserialize();
    }

    void Fx::SetToRender(const NameID& propID, const PropertyType& propVal)
    {
        if (IsLifeStageNormal())
        {
            std::visit(Overloaded{[&](const std::vector<float>& farr) { cross::threading::DispatchRenderingCommand([=] { mRenderMaterial->SetValueProp(propID, farr.data(), farr.size()); }); },
                                  [&](const bool val) { cross::threading::DispatchRenderingCommand([=] { mRenderMaterial->SetBool(propID, val); }); },
                                  [&](const TexturePtr& texture) { cross::threading::DispatchRenderingCommand([=] { mRenderMaterial->SetTexture(propID, texture->GetTextureR()); }); },
                                  [&](const SamplerState& samplerState) { cross::threading::DispatchRenderingCommand([=] { mRenderMaterial->SetSamplerState(propID, samplerState); }); }},
                       propVal);
        }
    }

    void Fx::PostDestroy()
    {
        MaterialInterface::PostDestroy();
        UnregistAllMpc();
    }

    void Fx::DestroyRenderData(FrameParam* frameParam)
    {
        cross::threading::DispatchRenderingCommand([this] { mRenderMaterial.reset(); });
    }

    void Fx::SetToRender(const NameID& passID, const StateType& states)
    {
        cross::threading::DispatchRenderingCommand([renderMaterial = this->mRenderMaterial.get(), key = passID, stateInfo = states] {
            {
                auto& [blend, depth, raster, dynamic, _] = stateInfo;
                renderMaterial->SetBlendState(key, blend);
                renderMaterial->SetDepthStencilState(key, depth);
                renderMaterial->SetRasterizationState(key, raster);
                renderMaterial->SetDynamicState(key, dynamic);
            }
        });
    }

    void Fx::SetToRender(const NameID& passID, const UInt32 group)
    {
        cross::threading::DispatchRenderingCommand([renderMaterial = this->mRenderMaterial.get(), key = passID, value = group] { renderMaterial->SetRenderGroup(key, (UInt16)value); });
    }

    void Fx::SetToRender(const NameID& passID, const bool enable)
    {
        cross::threading::DispatchRenderingCommand([renderMaterial = this->mRenderMaterial.get(), key = passID, value = enable] { renderMaterial->SetEnable(key, value); });
    }

    bool Fx::RefreshRenderData()
    {
        threading::DispatchRenderingCommand([=] { mRenderMaterial->Initialize(FxPtr(this)); });

        VisitProperty([this](const NameID& propID, const PropertyType& propVal) { SetToRender(propID, *GetProperty(propID)); });

        for (auto const& [passID, _] : GetAllPass())
        {
            SetToRender(passID, IsPassEnable(passID));
            SetToRender(passID, GetRenderGroup(passID));
            SetToRender(passID, *GetState(passID));
        }
        return true;
    }

    bool Fx::ResetResource()
    {
        Resource::ResetResource();
        mChildMaterial.clear();
        mPropertyNames.clear();
        mProperties.clear();
        mPassNames.clear();
        mPasses.clear();

        UnregistAllMpc();
        mMpcs.clear();

        return true;
    }

    void Fx::SerializePropertyValue(SerializeNode& s, const PropertyType& value)
    {
        std::visit(Overloaded{[&](const std::vector<float>& vec) {
                                  s = SerializeNode::EmptyArray();
                                  for (auto& f : vec)
                                  {
                                      s.PushBack(f);
                                  }
                              },
                              [&](const bool val) { s = val; },
                              [&](const TexturePtr& tex) { s = tex->GetGuid_Str(); },
                              [&](const SamplerState& val) {
                                  SerializeContext ctx;
                                  s = val.Serialize(ctx);
                              }},
                   value);
        // TODO(aki): Serialize uint array
    }

    std::optional<Fx::PropertyType> Fx::DeserializePropertyValue(const DeserializeNode& s)
    {
        if (s.IsBoolean())
        {
            return s.AsBoolean();
        }
        else if (s.IsNumber())
        {
            std::vector<float> vec{s.AsFloat()};
            return vec;
        }
        else if (s.IsArray())
        {
            // TODO(aki): Deserialize uint array
            std::vector<float> vec;
            vec.reserve(s.Size());
            for (int i = 0; i < s.Size(); i++)
            {
                Assert(s[i].IsNumber());
                vec.push_back(s[i].AsFloat());
            }
            return vec;
        }
        else if (s.IsString())
        {
            std::string str = s.AsString();
            ResourcePtr tex = gAssetStreamingManager->GetResource(str);
            if (!tex)
            {
                tex = Texture::GetDefault();
            }
            return TypeCast<Texture>(tex);
        }
        else if (s.IsObject())
        {
            SamplerState samplerState{};
            SerializeContext ctx;
            samplerState.Deserialize(s, ctx);
            return samplerState;
        }
        return std::optional<Material::PropertyType>();
    }

    bool Fx::Serialize(SerializeNode&& s, const std::string& path)
    {
        if (!HasAsset())
        {
            CreateAsset(path);
        }

        ClearReference();

        Serialize(s);

        return Resource::Serialize(std::move(s), path);
    }

    void Fx::Serialize(SerializeNode& s)
    {
        s["version"] = mVersion;

        for (const auto& name : mPropertyNames)
        {
            if (auto iter = mProperties.find(name); iter != mProperties.end())
            {
                auto& prop = iter->second;
                AddIfNotExist(prop.mGroupName, mGroupNames);
            }
        }

        SerializeNode properties;
        for (const auto& groupName : mGroupNames)
        {
            SerializeNode group;
            for (const auto& name : mPropertyNames)
            {
                if (auto iter = mProperties.find(name); iter != mProperties.end())
                {
                    auto& prop = iter->second;
                    if (prop.mGroupName == groupName)
                    {
                        SerializeNode property;
                        SerializeNode value;
                        SerializePropertyValue(value, prop.mValue);
                        property["Value"] = std::move(value);
                        property["Visible"] = prop.mVisible;
                        property["OverrideType"] = prop.mOverrideType;
                        if (!prop.mParameterInfo.mDisplayName.empty())
                        {
                            property["DisplayName"] = prop.mParameterInfo.mDisplayName;
                        }
                        if (prop.mOverrideType == PropertyOverrideType::Parameter)
                        {
                            property["SortPriority"] = prop.mParameterInfo.mSortPriority;
                        }
                        if (auto vec = std::get_if<std::vector<float>>(&prop.mValue); vec)
                        {
                            if (vec->size() == 1 && prop.mRange.IsValid())
                            {
                                property["Min"] = prop.mRange.mMin;
                                property["Max"] = prop.mRange.mMax;
                            }
                            if (vec->size() == 3 || vec->size() == 4)
                            {
                                property["IsColor"] = prop.mIsColor;
                            }
                        }

                        group[name.GetName()] = std::move(property);

                        if (auto tex = std::get_if<TexturePtr>(&iter->second.mValue); tex)
                        {
                            if (mAsset)
                                AddReferenceResource(tex->get()->GetGuid_Str());
                            property["VirtualTextureLayer"] = prop.mParameterInfo.mVirtualTextureLayer;
                        }
                    }
                }
            }
            properties[groupName.GetName()] = std::move(group);
        }
        s["property"] = std::move(properties);

        SerializeNode passes;
        for (const auto& name : mPassNames)
        {
            if (auto iter = mPasses.find(name); iter != mPasses.end())
            {
                auto& pass = iter->second;
                SerializeNode passNode;
                passNode["enable"] = pass.mEnable;
                passNode["name"] = pass.name.GetName();
#if CROSSENGINE_EDITOR
                if (!pass.mShaderCode.empty())
                {
                    passNode["shader_code"] = pass.mShaderCode;
                }
                else
#endif
                {
                    passNode["shader"] = pass.mShaderPtr.get()->GetGuid_Str();
                    if (mAsset)
                        AddReferenceResource(pass.mShaderPtr.get()->GetGuid_Str());
                }
                SerializeContext ctx;
                passNode["state"] = pass.mState.Serialize(ctx);

                passes.PushBack(std::move(passNode));
            }
        }
        s["pass"] = std::move(passes);

        if (!mExpressionsString.empty())
        {
            s["expression"] = SerializeNode::ParseFromJson(mExpressionsString);
        }

        {
            SerializeContext context{};
            s["defines"] = mDefines.Serialize(context);
        }

        if (!mMpcs.empty())
        {
            SerializeNode mpcs;
            for (int i = 0; i < mMpcs.size(); i++)
            {
                auto& mpcInfo = mMpcs[i];
                SerializeNode mpc;
                SerializeNode usages;
                for (const auto& name : mpcInfo.mUsedParameters)
                {
                    usages.PushBack(name);
                }
                mpc["Path"] = mpcInfo.mPath;
                mpc["Usage"] = std::move(usages);
                mpcs[std::string("MPC") + std::to_string(i)] = std::move(mpc);
                if (mAsset)
                    AddReferenceResource(mpcInfo.mMpcSourcePtr.get()->GetGuid_Str());
            }
            s["material_parameter_collections"] = std::move(mpcs);
        }
    }

    bool Fx::Deserialize(DeserializeNode const& s)
    {
        auto passArr = s.HasMember("pass");
        if (!passArr.has_value() || !passArr->IsArray())
        {
            // Assert(false);
        }

        mPropertyNames.clear();
        mProperties.clear();
        mPassNames.clear();
        mPasses.clear();
        mGroupNames.clear();
        mPropertyFlags.clear();

        UnregistAllMpc();
        mMpcs.clear();

        // The version in new fx is always 1, so the file without version info should be deserialized as old fx.
        mVersion = 0;

        // Get version info
        if (auto versionInfo = s.HasMember("version"); versionInfo.has_value())
        {
            auto& version = versionInfo.value();
            if (version.IsIntegral())
            {
                mVersion = version.AsInt32();
            }
            else
            {
                // For compatibility with old version info
                for (auto [k, v] : version)
                {
                    if (auto p = DeserializePropertyValue(v); p.has_value())
                    {
                        if (auto val = std::get_if<std::vector<float>>(&(p.value())); val && val->size() == 1)
                        {
                            mVersion = static_cast<int>(val->at(0));
                        }
                    }
                }
            }
        }

        // For compatibility with old version
        if (mVersion == 0)
        {
            if (auto properties = s.HasMember("property"); properties.has_value())
            {
                if (properties->IsObject())
                {
                    AddIfNotExist("Default Group", mGroupNames);
                    for (auto [k, v] : properties.value())
                    {
                        if (auto p = DeserializePropertyValue(v); p.has_value())
                        {
                            Fx::Property property{k.data(), p.value()};
                            if (auto tex = std::get_if<TexturePtr>(&property.mValue); tex)
                            {
                                if (mAsset)
                                    AddReferenceResource(tex->get()->GetGuid_Str());
                            }
                            mProperties.emplace(k.data(), std::move(property));
                            AddIfNotExist(k.data(), mPropertyNames);
                        }
                    }
                }
                // Maybe useless?
                else if (properties->IsArray())
                {
                    AddIfNotExist("Default Group", mGroupNames);
                    for (int i = 0; i < properties->Size(); i++)
                    {
                        const auto& prop = properties.value()[i];
                        for (auto [k, v] : prop)
                        {
                            if (auto p = DeserializePropertyValue(v); p.has_value())
                            {
                                Fx::Property property{k.data(), p.value()};
                                if (auto tex = std::get_if<TexturePtr>(&property.mValue); tex)
                                {
                                    if (mAsset)
                                        AddReferenceResource(tex->get()->GetGuid_Str());
                                }
                                mProperties.emplace(k.data(), std::move(property));
                                AddIfNotExist(k.data(), mPropertyNames);
                            }
                        }
                    }
                }
            }
        }
        else if (mVersion >= 1)
        {
            if (auto properties = s.HasMember("property"); properties.has_value())
            {
                for (auto [groupName, group] : properties.value())
                {
                    AddIfNotExist(groupName.data(), mGroupNames);
                    if (group.IsObject())
                    {
                        for (auto [propName, prop] : group)
                        {
                            if (prop.IsObject())
                            {
                                Fx::Property property{groupName.data(), propName.data()};
                                {
                                    if (auto value = prop.HasMember("Value"); value.has_value())
                                    {
                                        if (auto p = DeserializePropertyValue(value.value()); p.has_value())
                                        {
                                            property.mValue = p.value();
                                        }
                                    }

                                    if (auto type = prop.HasMember("OverrideType"); type.has_value())
                                    {
                                        if (auto p = DeserializePropertyValue(type.value()); p.has_value())
                                        {
                                            if (auto val = std::get_if<std::vector<float>>(&(p.value())); val && val->size() == 1)
                                            {
                                                property.mOverrideType = PropertyOverrideType((int)(val->at(0)));
                                            }
                                        }
                                    }

                                    if (auto visible = prop.HasMember("Visible"); visible.has_value())
                                    {
                                        if (auto p = DeserializePropertyValue(visible.value()); p.has_value())
                                        {
                                            if (auto val = std::get_if<bool>(&(p.value())))
                                            {
                                                property.mVisible = *val;
                                            }
                                        }
                                    }

                                    if (auto priority = prop.HasMember("SortPriority"); priority.has_value())
                                    {
                                        if (auto p = DeserializePropertyValue(priority.value()); p.has_value())
                                        {
                                            if (auto val = std::get_if<std::vector<float>>(&(p.value())); val && val->size() == 1)
                                            {
                                                property.mParameterInfo.mSortPriority = val->at(0);
                                            }
                                        }
                                    }

                                    if (auto priority = prop.HasMember("DisplayName"); priority.has_value())
                                    {
                                        property.mParameterInfo.mDisplayName = priority.value().AsString();
                                    }

                                    if (auto priority = prop.HasMember("VirtualTextureLayer"); priority.has_value())
                                    {
                                        property.mParameterInfo.mVirtualTextureLayer = priority.value().AsString();
                                    }

                                    if (auto isColor = prop.HasMember("IsColor"); isColor.has_value())
                                    {
                                        if (auto p = DeserializePropertyValue(isColor.value()); p.has_value())
                                        {
                                            if (auto val = std::get_if<bool>(&(p.value())))
                                            {
                                                property.mIsColor = *val;
                                            }
                                        }
                                    }

                                    if (auto min = prop.HasMember("Min"); min.has_value())
                                    {
                                        if (auto p = DeserializePropertyValue(min.value()); p.has_value())
                                        {
                                            if (auto val = std::get_if<std::vector<float>>(&(p.value())); val && val->size() == 1)
                                            {
                                                property.mRange.mMin = val->at(0);
                                            }
                                        }
                                    }

                                    if (auto max = prop.HasMember("Max"); max.has_value())
                                    {
                                        if (auto p = DeserializePropertyValue(max.value()); p.has_value())
                                        {
                                            if (auto val = std::get_if<std::vector<float>>(&(p.value())); val && val->size() == 1)
                                            {
                                                property.mRange.mMax = val->at(0);
                                            }
                                        }
                                    }
                                }

                                if (auto tex = std::get_if<TexturePtr>(&property.mValue); tex)
                                {
                                    if (mAsset)
                                        AddReferenceResource(tex->get()->GetGuid_Str());
                                }
                                mProperties.emplace(propName.data(), std::move(property));
                                AddIfNotExist(propName.data(), mPropertyNames);
                            }
                        }
                    }
                }
            }
        }

        bool isPassUsingShaderCode = s.HasMember("expression").has_value();
        if (passArr.has_value() && passArr->IsArray())
        {
            for (int i = 0; i < passArr->Size(); i++)
            {
                const auto& jPass = passArr.value()[i];
                auto name = jPass["name"];
                Pass pass = {};

                Assert(name.IsString() && name.AsStringView() != "");

                ShaderPtr shaderPtr;
                pass.mEnable = jPass.HasMember("enable") ? jPass["enable"].AsBoolean() : true;
                pass.name = name.AsString().c_str();
                if (isPassUsingShaderCode && jPass.HasMember("shader_code"))
                {
                    std::string base64ShaderData = jPass["shader_code"].AsString();
                    std::vector<UInt8> shaderBinaryData = Base64Codec::decode(base64ShaderData);
                    shaderPtr = ResourceManager::Instance().CreateTempResourceAs<Shader>(fmt::format("{} : {}", GetName_CStr(), name.AsStringView()));
                    shaderPtr->SetupWithFlatbuffers(shaderBinaryData.data(), shaderBinaryData.size());
#if CROSSENGINE_EDITOR
                    pass.mShaderCode = std::move(base64ShaderData);
#endif
                }
                else
                {
                    if (jPass.HasMember("shader"))
                    {
                        auto shader = jPass["shader"];
                        if (!name.IsString() || !shader.IsString())
                        {
                            LOG_ERROR("Pass format not support");
                        }

                        shaderPtr = TypeCast<Shader>(gAssetStreamingManager->GetResource(static_cast<std::string>(shader.AsString())));

                        if (shaderPtr->GetName() == gResourceMgr.GetDefaultRes<Shader>()->GetName())
                        {
                            LOG_ERROR("Fatal: shader resource miss, FX is {}, Pass is {}", this->GetName(), name.AsString());
                        }
                    }
                    else
                    {
                        shaderPtr = TypeCast<Shader>(gAssetStreamingManager->GetResource(static_cast<std::string>("")));
                    }
                }

                pass.mShaderPtr = shaderPtr;

                if (!isPassUsingShaderCode && shaderPtr)
                {
                    if (mAsset)
                        AddReferenceResource(pass.mShaderPtr->GetGuid_Str());
                }
                if (shaderPtr)
                {
                    FillPropertyByShader(pass);
                }
                if (jPass.HasMember("render_group"))
                {
                    // old state
                    auto state = jPass["state"];
                    pass.mState = {DeserializeBlendStateDesc(state), DeserializeDepthStencilStateDesc(state), DeserializeRasterizerStateDesc(state), DeserializeDynamicStateDesc(state), jPass.Value("render_group", 2000u)};
                }
                else
                {
                    SerializeContext ctx;
                    pass.mState.Deserialize(jPass["state"], ctx);
                }


                // note, this code is solely for old version material, which is not created by material editor
                // so the blend state is not same as the material create by material editor
                // Also, it should be noted that separate translucency is different in FSR and DLSS
                bool need_modified = pass.name == "forward";

                bool enable_separate_translucency = false;
                if (need_modified)
                {
                    // support seperate translucency

                    if (pass.mState.BlendStateDesc.TargetBlendStateVector.size() < 2)
                    {
                        pass.mState.BlendStateDesc.TargetCount = std::max<UInt32>(pass.mState.BlendStateDesc.TargetCount, 2);
                        pass.mState.BlendStateDesc.TargetBlendStateVector.resize(pass.mState.BlendStateDesc.TargetCount);
                        pass.mState.BlendStateDesc.TargetBlendStateVector[1] = pass.mState.BlendStateDesc.TargetBlendState[1];
                    }

                    if (auto variant = GetProperty(NameID("CE_ENABLE_SEPERATE_TRANSLUCENCY")))
                    {
                        enable_separate_translucency = std::get<bool>(*variant);
                        
                    }
                }

                if (enable_separate_translucency)
                {
                    pass.mState.BlendStateDesc.TargetBlendState[1].WriteMask = ColorMask::All;
                    pass.mState.BlendStateDesc.TargetBlendStateVector[1] = pass.mState.BlendStateDesc.TargetBlendState[1];
                }


                AddIfNotExist(pass.name, mPassNames);
                mPasses.emplace(pass.name, std::move(pass));
            }
        }
        if (mVersion >= 2 && s.HasMember("expression"))
        {
            mExpressionsString = s["expression"].FormatToJson();

            auto node = DeserializeNode::ParseFromJson(mExpressionsString);
            assert(node.IsArray());

            for (int i = 0; i < node.Size(); i++)
            {
                auto expressionNode = node.At(i);

                std::string className = expressionNode["Class"].AsString();
                std::string parameterName = expressionNode.HasMember("m_ParameterName") ? expressionNode["m_ParameterName"].AsString() : "";
                std::string displayName = expressionNode.HasMember("m_Name") ? expressionNode["m_Name"].AsString() : "";

                if (parameterName.empty())
                {
                    parameterName = displayName;
                }

                if (className == "cross::MaterialExpressionScalarParameter")
                {
                    mScalarParamNameRemap[displayName] = parameterName;
                }
                else if (className == "cross::MaterialExpressionVectorParameter")
                {
                    mVectorParamNameRemap[displayName] = parameterName;
                }
                else if (className == "cross::MaterialExpressionTextureParameter")
                {
                    mTextureParamNameRemap[displayName] = parameterName;
                }
                else if (className == "cross::MaterialExpressionTextureSampleParameter")
                {
                    mTextureParamNameRemap[displayName] = parameterName;
                }
            }
        }
        else
        {
            mExpressionsString.clear();
        }

        // fill refference manually

        for (const auto& pass : mPasses)
        {
            if (pass.second.mShaderPtr && pass.second.mShaderPtr->GetName() != "")
                if (mAsset)
                    AddReferenceResource(pass.second.mShaderPtr->GetName());
        }

        for (const auto& prop : mProperties)
        {
            if (const auto texture = std::get_if<TexturePtr>(&prop.second.mValue))
            {
                if ((*texture)->GetName() != "")
                    if (mAsset)
                        AddReferenceResource((*texture)->GetName());
            }
        }

        if (s.HasMember("defines"))
        {
            SerializeContext context{};
            mDefines.Deserialize(s["defines"], context);
        }

        if (auto parameterCollections = s.HasMember("material_parameter_collections"); parameterCollections.has_value() && IsCreatedByMaterialEditor())
        {
            for (const auto& [collectionName, mpc] : parameterCollections.value())
            {
                if (auto collectionPath = mpc.HasMember("Path"); collectionPath.has_value())
                {
                    const auto& path = collectionPath.value().AsString();
                    auto res = gAssetStreamingManager->GetResource(path);
                    if (res && res->GetClassID() == ClassID(MaterialParameterCollection))
                    {
                        MPCPtr mpcRes = TypeCast<MaterialParameterCollection>(res);
                        MaterialParameterCollectionInfo mpcUsage = MaterialParameterCollectionInfo{std::string(collectionName), path, mpcRes};
                        if (auto usages = mpc.HasMember("Usage"); usages.has_value() && usages.value().IsArray())
                        {
                            for (int i = 0; i < usages.value().Size(); i++)
                            {
                                const auto& usageName = usages.value().At(i).AsString();
                                mpcUsage.mUsedParameters.emplace_back(usageName);
                            }
                        }
                        mMpcs.emplace_back(std::move(mpcUsage));
                    }
                    else
                    {
                        Assert(false);
                    }
                }
            }
            RegistAllMpc();
        }

        return true;
    }

    bool Fx::PostDeserialize()
    {
        if (mRenderMaterial)
        {   // add mpc properties to mProperties
            if (IsCreatedByMaterialEditor())
            {
                FillAllParameterCollectionValue();
            }

            RefreshRenderData();

            threading::DispatchRenderingCommand([=] { mRenderMaterial->CreateVTStack(); });
        }
        return true;
    }

    void Fx::FillPropertyFlag(const NameID& name, UInt8 flag)
    {
        if (auto iter = mPropertyFlags.find(name); iter != mPropertyFlags.end())
            iter->second |= flag;
        else
            mPropertyFlags.emplace(name, flag);
    }

    void Fx::FillShaderMacro(const NameID& name)
    {
        if (auto iter = mProperties.find(name); iter != mProperties.end())
        {
            iter->second.mUsage = true;
            if (auto* bval = std::get_if<bool>(&iter->second.mValue); !bval)
            {
                iter->second.mValue = false;
            }
        }
        else
        {
            PropertyType defaultValue = false;
            Property property{name.GetName(), defaultValue};
            property.mUsage = true;
            mProperties.emplace(name, std::move(property));
            AddIfNotExist(name, mPropertyNames);
            AddIfNotExist(property.mGroupName, mGroupNames);
        }
    }

    void Fx::FillShaderBuffer(const NGIVariableDesc& member)
    {
        if (auto iter = mProperties.find(member.Name); iter != mProperties.end())
        {
            iter->second.mUsage = true;
            if (auto* bval = std::get_if<bool>(&iter->second.mValue); member.Type == CrossSchema::ShaderVariableType::Bool && !bval)
            {
                iter->second.mValue = false;
            }
            else if (auto* vec = std::get_if<std::vector<float>>(&iter->second.mValue);
                     member.Type == CrossSchema::ShaderVariableType::Float && member.ColCount * member.RowCount <= 4 && (!vec || vec->size() != member.ColCount * member.RowCount))
            {
                std::vector<float> defaultVec(member.ColCount * member.RowCount, 0);
                iter->second.mValue = defaultVec;
            }
        }
        else
        {
            PropertyType defaultValue{};
            if (member.Type == CrossSchema::ShaderVariableType::Bool)
            {
                defaultValue = false;
            }
            else if (member.ColCount * member.RowCount <= 4)
            {
                std::vector<float> vec(member.ColCount * member.RowCount, 0);
                defaultValue = vec;
            }
            Property property{member.Name.GetName(), defaultValue};
            property.mUsage = true;
            mProperties.emplace(member.Name, std::move(property));
            AddIfNotExist(member.Name, mPropertyNames);
            AddIfNotExist(property.mGroupName, mGroupNames);
        }
    }

    TexturePtr GetDefaultTexture(CrossSchema::ShaderResourceType type)
    {
        switch (type)
        {
        case CrossSchema::ShaderResourceType::Texture2D:
            return TypeCast<Texture>(Texture2D::GetDefault());
        case CrossSchema::ShaderResourceType::Texture2DArray:
        case CrossSchema::ShaderResourceType::Texture3D:
        case CrossSchema::ShaderResourceType::TextureCube:
        case CrossSchema::ShaderResourceType::TextureCubeArray:
        case CrossSchema::ShaderResourceType::Texture1D:
        case CrossSchema::ShaderResourceType::Texture1DArray:
        case CrossSchema::ShaderResourceType::Texture2DMS:
        case CrossSchema::ShaderResourceType::Texture2DMSArray:
        default:
            return TypeCast<Texture>(Texture::GetDefault());
        }
    }

    void Fx::FillShaderResource(const ShaderResourceDesc& member)
    {
        if (auto iter = mProperties.find(member.ID); iter != mProperties.end())
        {
            iter->second.mUsage = true;

            switch (member.Type)
            {
            case CrossSchema::ShaderResourceType::Texture1D:
            case CrossSchema::ShaderResourceType::Texture1DArray:
            case CrossSchema::ShaderResourceType::Texture2D:
            case CrossSchema::ShaderResourceType::Texture2DArray:
            case CrossSchema::ShaderResourceType::Texture2DMS:
            case CrossSchema::ShaderResourceType::Texture2DMSArray:
            case CrossSchema::ShaderResourceType::Texture3D:
            case CrossSchema::ShaderResourceType::TextureCube:
            case CrossSchema::ShaderResourceType::TextureCubeArray:
                std::visit(Overloaded{[](TexturePtr&) {},
                                      [&](auto&) {
                                          auto defaultTex = GetDefaultTexture(member.Type);
                                          iter->second.mValue = defaultTex;
                                          if (mAsset)
                                              AddReferenceResource(defaultTex->GetGuid_Str());
                                      }},
                           iter->second.mValue);
                break;
            case CrossSchema::ShaderResourceType::Sampler:
                std::visit(Overloaded{[](SamplerState&) {}, [&](auto&) { iter->second.mValue = SamplerState{}; }}, iter->second.mValue);
                break;
            default:
                Assert(false);
                break;
            }
        }
        else
        {
            TexturePtr defaultTex = GetDefaultTexture(member.Type);
            PropertyType defaultValue = defaultTex;
            Property property{member.ID.GetName(), defaultValue};
            property.mUsage = true;
            if (mAsset)
                AddReferenceResource(defaultTex->GetGuid_Str());
            mProperties.emplace(member.ID, std::move(property));
            AddIfNotExist(member.ID, mPropertyNames);
            AddIfNotExist(property.mGroupName, mGroupNames);
        }
    }

    void Fx::FillPropertyByShader(Pass& pass)
    {
        Assert(pass.mShaderPtr);

        auto macros = std::make_unique<ShaderVariationKey>(pass.mShaderPtr.get());
        for (UInt32 id = 0; id < macros->GetMacroCount(); id++)
        {
            NameID key(macros->GetMacroName(id).data());
            pass.mMacros.emplace(key);
            FillPropertyFlag(key, Shader_Macro);
            FillShaderMacro(key);
        }

        pass.mShaderPtr->VisitProgram([&](Shader::ProgramDesc const& v) {
            auto& mtlResGroupLayout = v.ResourceGroupLayouts[ShaderParamGroup_Material];
            for (const auto& cb : mtlResGroupLayout.ConstantBufferLayouts)
            {
                for (const auto& member : cb.Members)
                {
                    UInt8 flag = 1 << cb.Space | Shader_CbMember;
                    FillPropertyFlag(member.Name, flag);
                    FillShaderBuffer(member);
                }
            }
            for (const auto& res : mtlResGroupLayout.ResourceLayouts)
            {
                UInt8 flag = 1 << res.Space | Shader_Resource;
                FillPropertyFlag(res.ID, flag);
                FillShaderResource(res);
            }

            if (v.ShaderConstantLayout)
            {
                for (const auto& member : (*v.ShaderConstantLayout).Members)
                {
                    FillPropertyFlag(member.Name, Shader_SpMember);
                    FillShaderBuffer(member);
                }
            }
        });
    }

    void Fx::RefreshProperties()
    {
        // Clear usage info
        for (auto& [k, v] : mProperties)
        {
            v.mUsage = false;
        }

        for (auto& [k, v] : mPasses)
        {
            FillPropertyByShader(v);
        }
    }

    void Fx::RefreshChildMaterial()
    {
        auto tempMat = mChildMaterial;
        for (auto* child : tempMat)
        {
            child->RefreshMaterialTree();
        }
    }

    Shader* Fx::GetShader(const NameID& passID)
    {
        auto pID = (passID == 0 ? GetDefaultPass().name : passID);
        Assert(HasPass(passID));
        return mPasses[pID].mShaderPtr.get();
    }

    const Fx::Pass& Fx::GetPass(const NameID& passID) const
    {
        Assert(HasPass(passID));
        return mPasses.at(passID);
    }

    Fx::Pass& Fx::GetPass(const NameID& passID)
    {
        Assert(HasPass(passID));
        return mPasses.at(passID);
    }

    const Fx::Pass& Fx::GetDefaultPass() const
    {
        if (mPasses.find("forward") != mPasses.end())
            return mPasses.at("forward");
        return mPasses.begin()->second;
    }

    bool Fx::IsPassEnable(const NameID& passID) const
    {
        auto pID = (passID == 0 ? GetDefaultPass().name : passID);
        Assert(mPasses.find(pID) != mPasses.end());
        return mPasses.at(pID).mEnable;
    }

    const Fx::StateType* Fx::GetState(const NameID& passID) const
    {
        auto pID = (passID == 0 ? GetDefaultPass().name : passID);
        Assert(mPasses.find(pID) != mPasses.end());
        return &mPasses.at(pID).mState;
    }

    UInt32 Fx::GetRenderGroup(NameID const& passID)
    {
        const auto& pID = passID.mPropertyID == 0 ? GetFx()->GetDefaultPass().name : passID;
        return GetPass(pID).mState.RenderGroup;
    }

    FxRenderState Fx::GetRenderState(const NameID& passID) const
    {
        auto& pass = GetPass(passID);
        const auto& bs = pass.mState.BlendStateDesc;

        if (bs == GetDefaultBlendState())
            return FxRenderState::Opacity;
        else if (bs == GetTransparentBlendState())
            return FxRenderState::Transparent;
        // TODO(aki): additive
        return FxRenderState::Custom;
    }

    Fx::PropertyType const* Fx::GetProperty(NameID const& name) const
    {
        if (mProperties.find(name) != mProperties.end())
        {
            return &(mProperties.at(name).mValue);
        }
        return nullptr;
    }

    bool const* Fx::GetPropertyUsage(NameID const& name) const
    {
        if (mProperties.find(name) != mProperties.end())
        {
            return &(mProperties.at(name).mUsage);
        }
        return nullptr;
    }

    bool const* Fx::GetPropertyVisible(NameID const& name) const
    {
        if (mProperties.find(name) != mProperties.end())
        {
            return &(mProperties.at(name).mVisible);
        }
        return nullptr;
    }

    bool const* Fx::GetPropertyIsColor(NameID const& name) const
    {
        if (mProperties.find(name) != mProperties.end())
        {
            return &(mProperties.at(name).mIsColor);
        }
        return nullptr;
    }

    Fx::Property::Range const* Fx::GetPropertyMinMax(NameID const& name) const
    {
        if (mProperties.find(name) != mProperties.end())
        {
            return &(mProperties.at(name).mRange);
        }
        return nullptr;
    }

    UInt8 Fx::GetPropertyFlag(const NameID& name) const
    {
        if (auto iter = mPropertyFlags.find(name); iter != mPropertyFlags.end())
            return iter->second;
        return 0;
    }

    void Fx::VisitProperty(Fx::PropertyAccesser func) const
    {
        for (const auto& e : mPropertyNames)
        {
            func(e, mProperties.at(e).mValue);
        }
    }

    bool Fx::HasAncestor(const std::string& name)
    {
        return GetName() == name;
    }

    void Fx::Refresh(bool resetResource)
    {
        if (resetResource)
        {
            Resource::ResetResource();
        }

        if (mAsset)
        {
            mPropertyNames.clear();
            mPassNames.clear();
            mGroupNames.clear();
            mProperties.clear();
            mPasses.clear();
            mPropertyFlags.clear();

            UnregistAllMpc();
            mMpcs.clear();

            gResourceAssetMgr.LoadNDAResource(ResourcePtr(this));
        }

        PostDeserialize();
    }

    void Fx::RefreshMaterialTree(bool resetResource, bool skipSelf)
    {
        if (!skipSelf)
            Refresh(resetResource);

        // TODO(peterwjma): notify material changed

        auto temporal_cache = mChildMaterial;

        for (auto childmat : temporal_cache)
        {
            if (skipSelf)
                childmat->Refresh(resetResource);
            childmat->RefreshMaterialTree(resetResource);
        }
    }

    void Fx::SetBool(const NameID& name, bool value)
    {
        AddIfNotExist(name, mPropertyNames);
        mProperties[name].mValue = value;

        if (this->mRenderMaterial && IsLifeStageNormal())
        {
            cross::threading::DispatchRenderingCommand([name, value, materialR = this->mRenderMaterial] { materialR->SetBool(name, value); });
        }
        else
        {
            LOG_ERROR("Try to Set Mat {} {} with value {}", GetName(), name.GetName(), value);
        }

        for (auto* child : mChildMaterial)
        {
            child->OnParentDirty(name, static_cast<PropertyType>(value));
        }
    }

    void Fx::SetInt(const NameID& name, int value)
    {
        SetFloat(name, static_cast<float>(value));
    }

    void Fx::SetFloat(const NameID& name, float value)
    {
        SetFloatArray(name, 1, &value);
    }

    void Fx::SetFloat2(const NameID& name, const float value[2])
    {
        SetFloatArray(name, 2, value);
    }

    void Fx::SetFloat3(const NameID& name, const float value[3])
    {
        SetFloatArray(name, 3, value);
    }

    void Fx::SetFloat4(const NameID& name, const float value[4])
    {
        SetFloatArray(name, 4, value);
    }

    void Fx::SetFloatArray(const NameID& name, int length, const float* pValue)
    {
        Assert(length);

        NameID remappedName;
        if (length == 1)
        {
            remappedName = GetParameterRemappedName(MaterialParamType::Scalar, name);
        }
        else
        {
            remappedName = GetParameterRemappedName(MaterialParamType::Vector, name);
        }

        std::vector vec = std::vector<float>(length);
        memcpy(vec.data(), pValue, length * sizeof(float));

        AddIfNotExist(remappedName, mPropertyNames);

        mProperties[remappedName].mValue = vec;

        if (this->mRenderMaterial && IsLifeStageNormal())
        {
            cross::threading::DispatchRenderingCommand([remappedName, vec, materialR = this->mRenderMaterial] { materialR->SetValueProp(remappedName, vec.data(), vec.size()); });
        }
        else
        {
            LOG_ERROR("Try to Set Mat {} {} with length {}", GetName(), remappedName.GetName(), length);
        }

        for (auto* child : mChildMaterial)
        {
            child->OnParentDirty(remappedName, vec);
        }
    }

    void Fx::SetTexture(const NameID& name, TexturePtr texture)
    {
        Assert(texture);
        if (!texture)
            return;

        NameID remappedName = GetParameterRemappedName(MaterialParamType::Texture, name);

        auto createVTStack = false;
        if (auto* oldProp = GetProperty(remappedName); oldProp && std::holds_alternative<TexturePtr>(*oldProp))
        {
            if (std::get<TexturePtr>(*oldProp)->GetTextureInfo().EnableVirtualTextureStreaming || texture->GetTextureInfo().EnableVirtualTextureStreaming)
            {
                createVTStack = true;
            }
        }

        AddIfNotExist(remappedName, mPropertyNames);
        mProperties[remappedName].mValue = texture;

        if (mRenderMaterial)
        {
            cross::threading::DispatchRenderingCommand([=, textureR = texture->GetTextureR()] {
                mRenderMaterial->SetTexture(remappedName, textureR);
                if (createVTStack)
                {
                    mRenderMaterial->CreateVTStack();
                }
            });
            threading::FlushRenderingCommands();
        }

        for (auto* child : mChildMaterial)
        {
            child->OnParentDirty(remappedName, static_cast<PropertyType>(texture));
        }
    }

    void Fx::SetTextureProp(const NameID& name, TexturePtr texture)
    {
        Assert(texture);
        if (!texture)
            return;

        NameID remappedName = GetParameterRemappedName(MaterialParamType::Texture, name);

        AddIfNotExist(remappedName, mPropertyNames);
        mProperties[remappedName].mValue = texture;
    }

    void Fx::SetSamplerState(const NameID& name, const SamplerState& samplerState)
    {
        AddIfNotExist(name, mPropertyNames);

        const auto& value = mProperties[name].mValue = samplerState;

        if (mRenderMaterial)
        {
            cross::threading::DispatchRenderingCommand([=] { mRenderMaterial->SetSamplerState(name, samplerState); });
        }
        for (auto* child : mChildMaterial)
        {
            child->OnParentDirty(name, value);
        }
    }

    void Fx::SetScalarParamInfo(const NameID& name, std::string displayName, float sliderMin, float sliderMax, float sortPriority)
    {
        NameID remappedName = GetParameterRemappedName(MaterialParamType::Scalar, name);
        AddIfNotExist(remappedName, mPropertyNames);
        mProperties[remappedName].mOverrideType = PropertyOverrideType::Parameter;
        mProperties[remappedName].mRange.mMin = sliderMin;
        mProperties[remappedName].mRange.mMax = sliderMax;
        mProperties[remappedName].mParameterInfo.mDisplayName = displayName;
        mProperties[remappedName].mParameterInfo.mSortPriority = sortPriority;
    }

    void Fx::SetVectorParamInfo(const NameID& name, std::string displayName, float sortPriority)
    {
        NameID remappedName = GetParameterRemappedName(MaterialParamType::Vector, name);
        AddIfNotExist(remappedName, mPropertyNames);
        mProperties[remappedName].mOverrideType = PropertyOverrideType::Parameter;
        mProperties[remappedName].mParameterInfo.mDisplayName = displayName;
        mProperties[remappedName].mParameterInfo.mSortPriority = sortPriority;
    }

    void Fx::SetTextureParamInfo(const NameID& name, std::string displayName, std::string VTLayer, float sortPriority)
    {
        NameID remappedName = GetParameterRemappedName(MaterialParamType::Texture, name);
        AddIfNotExist(remappedName, mPropertyNames);
        mProperties[remappedName].mParameterInfo.mDisplayName = displayName;
        mProperties[remappedName].mOverrideType = PropertyOverrideType::Parameter;
        mProperties[remappedName].mParameterInfo.mVirtualTextureLayer = VTLayer;
        mProperties[remappedName].mParameterInfo.mSortPriority = sortPriority;
    }

    void Fx::SetPropertyOverrideType(const NameID& name, const PropertyOverrideType& type)
    {
        NameID remappedName = GetParameterRemappedName(MaterialParamType::Texture, name);
        AddIfNotExist(remappedName, mPropertyNames);
        mProperties[remappedName].mOverrideType = type;
    }

    void Fx::SetPropertyDisplayName(const NameID& name, const std::string displayName)
    {
        NameID remappedName = GetParameterRemappedName(MaterialParamType::Texture, name);
        AddIfNotExist(remappedName, mPropertyNames);
        mProperties[remappedName].mParameterInfo.mDisplayName = displayName;
    }

    void Fx::SetProp(const NameID& name, const PropertyType& propVal)
    {
        NameID remappedName = name;
        if (auto r = std::get_if<std::vector<float>>(&propVal); r)
        {
            if (r->size() == 1)
            {
                remappedName = GetParameterRemappedName(MaterialParamType::Scalar, name);
            }
            else if (r->size() == 4)
            {
                remappedName = GetParameterRemappedName(MaterialParamType::Vector, name);
            }
        }
        else if (auto r2 = std::get_if<TexturePtr>(&propVal); r2)
        {
            remappedName = GetParameterRemappedName(MaterialParamType::Texture, name);
        }

        mProperties[remappedName].mValue = propVal;
    }

    void Fx::SetPropertyGroup(const char* name, const char* group)
    {
        if (auto iter = mProperties.find(name); iter != mProperties.end())
        {
            iter->second.mGroupName = group;
        }
    }
    void Fx::ChangePropertyPosition(const char* name, const char* prev, const char* next)
    {
        {
            cross::NameID nameToMove{name};
            if (nameToMove == prev || nameToMove == next)
                return;
            auto& names = GetExposedPropertyName();
            if (auto iter = std::find(names.begin(), names.end(), nameToMove); iter != names.end())
            {
                names.erase(iter);
                // Insert before next
                if (auto pos = std::find(names.begin(), names.end(), next); pos != names.end())
                {
                    names.emplace(pos, nameToMove);
                }
                // Or insert after prev
                else if (auto pos2 = std::find(names.begin(), names.end(), prev); pos2 != names.end())
                {
                    if (pos + 1 != names.end())
                        names.emplace(pos2 + 1, nameToMove);
                    else
                        names.emplace_back(nameToMove);
                }
                // Otherwise insert to last
                else
                {
                    names.emplace_back(nameToMove);
                }
            }
        }
    }
    void Fx::ChangePassPosition(const char* name, const char* next)
    {
        cross::NameID nameToMove{name};
        if (nameToMove == next)
            return;
        auto& names = GetExposedPassName();
        if (auto iter = std::find(names.begin(), names.end(), nameToMove); iter != names.end())
        {
            names.erase(iter);
            if (auto pos = std::find(names.begin(), names.end(), next); pos != names.end())
                names.emplace(pos, nameToMove);
            else
                names.emplace_back(nameToMove);
        }
    }
    void Fx::ChangeGroupPosition(const char* name, const char* next)
    {
        cross::NameID groupToMove{name};
        if (groupToMove == next)
            return;
        auto& names = GetExposedGroupName();
        if (auto iter = std::find(names.begin(), names.end(), groupToMove); iter != names.end())
        {
            names.erase(iter);
            if (auto pos = std::find(names.begin(), names.end(), next); pos != names.end())
                names.emplace(pos, groupToMove);
            else
                names.emplace_back(groupToMove);
        }
    }
    void Fx::SetPropertyVisible(const NameID& name, bool visible)
    {
        if (auto iter = mProperties.find(name); iter != mProperties.end())
        {
            iter->second.mVisible = visible;
        }
    }
    std::string Fx::EditorGetPropertyGroupName(const char* name)
    {
        std::string groupName = "";
        auto& properties = GetProperties();
        if (auto iter = properties.find(name); iter != properties.end())
        {
            std::string str{properties.at(name).mGroupName.GetName()};
            groupName.append(str);
        }
        return groupName;
    }
    void Fx::SetPropertyIsColor(const NameID& name, bool isColor)
    {
        if (auto iter = mProperties.find(name); iter != mProperties.end())
        {
            iter->second.mIsColor = isColor;
        }
    }

    void Fx::SetPropertyMin(const NameID& name, float min)
    {
        if (auto iter = mProperties.find(name); iter != mProperties.end())
        {
            iter->second.mRange.mMin = min;
        }
    }

    void Fx::SetPropertyMax(const NameID& name, float max)
    {
        if (auto iter = mProperties.find(name); iter != mProperties.end())
        {
            iter->second.mRange.mMax = max;
        }
    }

    void Fx::RemoveUnusedProps()
    {
        RefreshProperties();

        std::vector<NameID> propsToRemove;

        for (auto& [k, v] : mProperties)
        {
            if (!v.mUsage)
            {
                propsToRemove.push_back(k);
            }
        }

        for (auto& name : propsToRemove)
        {
            mProperties.erase(name);
            RemoveIfExist(name, mPropertyNames);
            mPropertyFlags.erase(name);
        }
    }

    void Fx::CreatePass(const NameID& passID)
    {
        if (HasPass(passID))
            return;

        ShaderPtr defaultShader = TypeCast<Shader>(resource::Shader::GetDefault());
        Pass pass{defaultShader, passID};
        if (mAsset)
            AddReferenceResource(defaultShader->GetGuid_Str());
        AddIfNotExist(passID, mPassNames);
        mPasses.emplace(passID, std::move(pass));

        RefreshProperties();
        if (cross::EngineGlobal::GetSettingMgr()->GetAppStartUpType() != cross::AppStartUpTypeHeadless)
        {
            RefreshRenderData();
            RefreshChildMaterial();
        }
    }

    void Fx::DeletePass(const NameID& passID)
    {
        if (!HasPass(passID))
            return;

        mPasses.erase(passID);
        RemoveIfExist(passID, mPassNames);

        RefreshProperties();

        RefreshChildMaterial();
    }

    void Fx::RenamePass(const NameID& oldName, const NameID& newName)
    {
        if (!HasPass(oldName))
            return;

        auto node = mPasses.extract(oldName);
        node.key() = newName;
        node.mapped().name = newName;
        mPasses.insert(std::move(node));
        auto iter = std::find(mPassNames.begin(), mPassNames.end(), oldName);
        if (iter != mPassNames.end())
        {
            *iter = newName;
        }

        RefreshChildMaterial();
    }

    void Fx::CreateGroup(const NameID& name)
    {
        AddIfNotExist(name, mGroupNames);
    }

    void Fx::DeleteGroup(const NameID& name)
    {
        RemoveIfExist(name, mGroupNames);
    }

    void Fx::RenameGroup(const NameID& oldName, const NameID& newName)
    {
        auto iter = std::find(mGroupNames.begin(), mGroupNames.end(), oldName);
        if (iter != mGroupNames.end())
            *iter = newName;
    }

    void Fx::SetShader(const NameID& passID, ShaderPtr shader)
    {
        if (!HasPass(passID))
            return;

        auto& pass = mPasses.at(passID);
        pass.mShaderPtr = shader;

        if (shader->HasAsset())
        {
            if (mAsset)
                AddReferenceResource(shader->GetGuid_Str());
            pass.mShaderCode.clear();
        }

        RefreshProperties();
        if (cross::EngineGlobal::GetSettingMgr()->GetAppStartUpType() != cross::AppStartUpTypeHeadless)
        {
            RefreshChildMaterial();
        }
    }

    void Fx::SetRenderGroup(const NameID& passID, UInt32 renderGroup)
    {
        if (!HasPass(passID))
            return;

        auto& pass = mPasses.at(passID);
        pass.mState.RenderGroup = renderGroup;
        if (mRenderMaterial && cross::EngineGlobal::GetSettingMgr()->GetAppStartUpType() != cross::AppStartUpTypeHeadless)
        {
            cross::threading::DispatchRenderingCommand([passID, renderGroup, materialR = this->mRenderMaterial] { materialR->SetRenderGroup(passID, (UInt16)renderGroup); });
        }
        for (auto* child : mChildMaterial)
        {
            child->OnParentDirty(passID, renderGroup);
        }
    }

    void Fx::SetRenderGroupProp(const NameID& passID, UInt32 val)
    {
        if (!HasPass(passID))
            return;

        auto& pass = mPasses.at(passID);
        pass.mState.RenderGroup = val;
    }

    void Fx::SetRenderState(const NameID& passID, const StateType& renderState)
    {
        if (!HasPass(passID))
            return;

        auto& pass = mPasses.at(passID);
        pass.mState = renderState;

        SetToRender(passID, renderState);
        SetToRender(passID, renderState.RenderGroup);

        for (auto* child : mChildMaterial)
        {
            child->OnParentDirty(passID, pass.mState);
        }
    }

    void Fx::SetRenderState(const NameID& passID, FxRenderState renderState)
    {
        if (!HasPass(passID))
            return;

        auto& pass = mPasses.at(passID);
        if (renderState == FxRenderState::Opacity)
        {
            pass.mState = {GetDefaultBlendState(), GetDefaultDepthStencilState(), GetDefaultRasterizationState(), GetDefaultDynamicState()};
        }
        else if (renderState == FxRenderState::Transparent)
        {
            pass.mState = {GetTransparentBlendState(), GetTransparentDepthStencilState(), GetDefaultRasterizationState(), GetDefaultDynamicState()};
        }
        else if (renderState == FxRenderState::Custom)
        {
            // Do nothing.
        }

        for (auto* child : mChildMaterial)
        {
            child->OnParentDirty(passID, pass.mState);
        }
    }

    void Fx::SetTargetBlendState(const NameID& passID, const NGITargetBlendStateDesc& state, int index)
    {
        if (!HasPass(passID))
            return;

        auto& pass = mPasses.at(passID);
        auto& bs = pass.mState.BlendStateDesc;
        bs.SetTargetBlendState(state, index);

        for (auto* child : mChildMaterial)
        {
            child->OnParentDirty(passID, pass.mState);
        }
    }

    void Fx::SetPassEnable(const NameID& passID, bool enable)
    {
        if (!HasPass(passID))
            return;

        auto& pass = mPasses.at(passID);
        pass.mEnable = enable;

        if (mRenderMaterial)
            cross::threading::DispatchRenderingCommand([passID, enable, materialR = this->mRenderMaterial] { materialR->SetEnable(passID, enable); });

        for (auto* child : mChildMaterial)
        {
            child->OnParentDirty(passID, enable);
        }
    }

    void Fx::SetBlendState(const NameID& passID, const NGIBlendStateDesc& state)
    {
        if (!HasPass(passID))
            return;

        auto& pass = mPasses.at(passID);
        pass.mState.BlendStateDesc = NGIBlendStateDescForEditor{state};

        if (mRenderMaterial)
            cross::threading::DispatchRenderingCommand([passID, state, materialR = this->mRenderMaterial] { materialR->SetBlendState(passID, state); });

        for (auto* child : mChildMaterial)
        {
            child->OnParentDirty(passID, pass.mState);
        }
    }

    void Fx::SetBlendStateProp(const NameID& passID, const NGIBlendStateDesc& blendDesc)
    {
        if (!HasPass(passID))
            return;

        auto& pass = mPasses.at(passID);
        pass.mState.BlendStateDesc = NGIBlendStateDescForEditor{blendDesc};
    }

    void Fx::SetDepthStencilState(const NameID& passID, const NGIDepthStencilStateDesc& state)
    {
        if (!HasPass(passID))
            return;

        auto& pass = mPasses.at(passID);
        pass.mState.DepthStencilStateDesc = state;

        if (mRenderMaterial)
            cross::threading::DispatchRenderingCommand([passID, state, materialR = this->mRenderMaterial] { materialR->SetDepthStencilState(passID, state); });

        for (auto* child : mChildMaterial)
        {
            child->OnParentDirty(passID, pass.mState);
        }
    }

    void Fx::SetDepthStencilStateProp(const NameID& passID, const NGIDepthStencilStateDesc& state)
    {
        if (!HasPass(passID))
            return;

        auto& pass = mPasses.at(passID);
        pass.mState.DepthStencilStateDesc = state;
    }

    void Fx::SetRasterizerState(const NameID& passID, const NGIRasterizationStateDesc& state)
    {
        if (!HasPass(passID))
            return;

        auto& pass = mPasses.at(passID);
        pass.mState.RasterizationStateDesc = state;

        if (mRenderMaterial)
            cross::threading::DispatchRenderingCommand([passID, state, materialR = this->mRenderMaterial] { materialR->SetRasterizationState(passID, state); });

        for (auto* child : mChildMaterial)
        {
            child->OnParentDirty(passID, pass.mState);
        }
    }

    void Fx::SetDynamicState(const NameID& passID, const NGIDynamicStateDesc& state)
    {
        if (!HasPass(passID))
            return;

        auto& pass = mPasses.at(passID);
        pass.mState.DynamicStateDesc = state;

        DispatchRenderingCommandWithToken([passID, state, materialR = this->mRenderMaterial] { materialR->SetDynamicState(passID, state); });

        for (auto* child : mChildMaterial)
        {
            child->OnParentDirty(passID, pass.mState);
        }
    }

    FxPtr Fx::GetFx() const
    {
        return FxPtr(const_cast<resource::Fx*>(this));
    }

    bool Fx::IsDependent(const std::string& materialGuid)
    {
        return GetFx() && GetFx()->GetGuid_Str() == materialGuid;
    }

    MaterialInterfacePtr Fx::CreateInstance()
    {
        return resource::Material::CreateMaterialInstance(TypeCast<resource::Fx>(SharedFromThis()));
    }

    MaterialPtr Fx::CreateTempInstance()
    {
        return resource::Material::CreateMaterialTempInstance(TypeCast<resource::Fx>(SharedFromThis()));
    }

    void Fx::EditorVisitProperty(Fx::PropertyAccesser func)
    {
        Assert(false);
    }

    std::string Fx::EditorGetPropertyString()
    {
        Assert(false);
        return std::string();
    }

    std::string Fx::EditorGetPassString()
    {
        Assert(false);
        return std::string();
    }

    void Fx::EditorSetRenderState(const NameID& passID, MaterialRenderState renderState)
    {
        Assert(false);
    }

    bool Fx::EditorGetBlendEnable(const NameID& passID)
    {
        Assert(false);
        return false;
    }

    bool Fx::EditorIsPropertyOverrided(const NameID& propID)
    {
        Assert(false);
        return false;
    }

    bool Fx::EditorIsRenderGroupOverrided(const NameID& passID)
    {
        Assert(false);
        return false;
    }

    bool Fx::EditorIsRenderStateOverrided(const NameID& passID)
    {
        Assert(false);
        return false;
    }

    void Fx::EditorNotitfySubMaterial()
    {
        Assert(false);
    }

    void Fx::AfterAssetDelete()
    {
        for (auto childMat : mChildMaterial)
        {
            childMat->SetParent(GetDefault()->GetAsset()->GetName());
            if (childMat->GetAsset() != nullptr)
            {
                cross::SerializeNode emptyNode = {};
                childMat->Serialize(std::move(emptyNode), childMat->GetAsset()->GetName());
            }
        }
    }
    void Fx::NotifyChange()
    {
        PostDeserialize();

        threading::DispatchRenderingCommand([=] { mRenderMaterial->NotifyChange(); });
    }

    void Fx::NotifyChangeRecursively()
    {
        NotifyChange();
        for (auto& child : mChildMaterial)
        {
            child->NotifyChangeRecursively();
        }
    }

    void Fx::FillParameterCollectionValue(MPCPtr mpc)
    {
        for (auto& mpcInfo : mMpcs)
        {
            if (mpcInfo.mMpcSourcePtr == mpc)
            {
                mpcInfo.mErrorFlag = false;
                for (auto& name : mpcInfo.mUsedParameters)
                {
                    std::string propertyName = mpcInfo.mName + "_" + name;
                    auto value = mpc->GetNumericParameterValue(name);

                    // used property has been deleted in mpc
                    if (value.empty())
                    {
                        mpcInfo.mErrorFlag = true;
                        value.emplace_back(0.f);
                    }
                    mProperties[propertyName] = Property(propertyName.c_str(), value);
                    this->SetFloatArray(propertyName.c_str(), static_cast<int>(value.size()), value.data());
                }
                break;
            }
        }
        bool hasMpcError = HasMpcError();
        mProperties[MATERIAL_ERROR_FLAG_NAME] = Property(MATERIAL_ERROR_FLAG_NAME, hasMpcError);
        this->SetBool(MATERIAL_ERROR_FLAG_NAME, hasMpcError);
    }

    void Fx::FillAllParameterCollectionValue()
    {
        for (auto& mpc : mMpcs)
        {
            FillParameterCollectionValue(mpc.mMpcSourcePtr);
        }
    }

    bool Fx::HasMpcError()
    {
        bool errorFlag = false;
        for (const auto& mpcInfo : mMpcs)
        {
            if (mpcInfo.mErrorFlag)
            {
                return true;
            }
        }
        return false;
    }

    void Fx::RegistAllMpc()
    {
        for (const auto& mpc : mMpcs)
        {
            mpc.mMpcSourcePtr->RegisterMaterialInterfaceHolder(MaterialInterfacePtr(const_cast<Fx*>(this)));
        }
    }

    void Fx::UnregistAllMpc()
    {
        for (const auto& mpc : mMpcs)
        {
            mpc.mMpcSourcePtr->UnRegisterMaterialInterfaceHolder(MaterialInterfacePtr(const_cast<Fx*>(this)));
        }
    }
    Fx* Fx::FxCreateFx()
    {
        auto defaultFx = TypeCast<cross::resource::Fx>(gAssetStreamingManager->LoadSynchronously("PipelineResource/FFSRP/Shader/Material/Lit/Lit.fx.nda"));
        auto fxPtr = gResourceMgr.CreateResourceAs<cross::resource::Fx>();
        //fxPtr->IncreaseRefCount();
        fxPtr->CopyFrom(*defaultFx);
        return fxPtr.get();
    }
    Fx* Fx::FxCreateFxNew()
    {
        auto defaultFx = TypeCast<cross::resource::Fx>(gAssetStreamingManager->LoadSynchronously("PipelineResource/FFSRP/Shader/Material/Lit/FxTemplate.fx.nda"));
        auto fxPtr = gResourceMgr.CreateResourceAs<cross::resource::Fx>();
        //fxPtr->IncreaseRefCount();
        fxPtr->CopyFrom(*defaultFx);
        return fxPtr.get();
    }
    std::string Fx::GetGroupNameList()
    {
        std::string str;
        for (auto& name : GetExposedGroupName())
        {
            str += name.GetName();
            str.push_back(';');
        }
        return str;
    }
    std::string Fx::GetPropertyNameList()
    {
        std::string str;
        for (auto& name : GetExposedPropertyName())
        {
            str += name.GetName();
            str.push_back(';');
        }
        if (str.size() > 0 && str.back() == ';')
            str.pop_back();
        return str;
    }
    void Fx::EditorSetFloat2(const NameID& name, Float2 value)
    {
        SetFloatArray(name, 2, value.data());
    }
    void Fx::EditorSetFloat3(const NameID& name, Float3 value)
    {
        SetFloatArray(name, 3, value.data());
    }
    void Fx::EditorSetFloat4(const NameID& name, Float4 value)
    {
        SetFloatArray(name, 4, value.data());
    }
    void Fx::EditorSetFloat(const NameID& name, float value)
    {
        SetFloat(name, value);
    }
    void Fx::EditorSetTexture(const NameID& name, const char* value)
    {
        SetTexture(name, cross::TypeCast<cross::resource::Texture>(gAssetStreamingManager->LoadSynchronously(value)));
    }
    void Fx::EditorSetShader(const NameID& name, const char* value)
    {
        cross::ShaderPtr shader = cross::TypeCast<cross::resource::Shader>(gAssetStreamingManager->LoadSynchronously(value));
        if (!shader)
        {
            shader = cross::TypeCast<cross::resource::Shader>(cross::resource::Shader::GetDefault());
        }
        SetShader(name, shader);
    }
    void Fx::SetMaterialParameterCollectionUsage(const MaterialParameterCollectionUsageInfo& mpcUsage)
    {
        UnregistAllMpc();
        mMpcs.clear();

        const auto& collections = mpcUsage.GetParameterCollectionUsage();

        for (int i = 0; i < collections.size(); i++)
        {
            const auto& collection = collections[i];
            auto res = gAssetStreamingManager->GetResource(std::string(collection.mPath));
            if (res && res->GetClassID() == ClassID(MaterialParameterCollection))
            {
                MPCPtr mpcRes = TypeCast<MaterialParameterCollection>(res);
                MaterialParameterCollectionInfo mpcInfo("MPC" + std::to_string(i), std::string(collection.mPath), mpcRes);
                for (auto& paramName : collection.mUsedParameters)
                {
                    mpcInfo.mUsedParameters.emplace_back(paramName);
                }
                mMpcs.emplace_back(std::move(mpcInfo));
            }
            else
            {
                Assert(false);
            }
        }

        RegistAllMpc();
        FillAllParameterCollectionValue();
    }

    NameID Fx::GetParameterRemappedName(MaterialParamType paramType, NameID name)
    {
        if (paramType == MaterialParamType::Scalar)
        {
            if (mScalarParamNameRemap.count(name))
            {
                return mScalarParamNameRemap[name];
            }
        }
        else if (paramType == MaterialParamType::Vector)
        {
            if (mVectorParamNameRemap.count(name))
            {
                return mVectorParamNameRemap[name];
            }
        }
        else if (paramType == MaterialParamType::Texture)
        {
            if (mTextureParamNameRemap.count(name))
            {
                return mTextureParamNameRemap[name];
            }
        }

        return name;
    }
    MaterialRenderState Fx::EditorGetRenderState(const NameID& passID) const
    {
        Assert(false);
        return MaterialRenderState();
    }
    int Fx::EditorGetPropertyType(const NameID& name)
    {
        size_t type = 0;
        if (auto* prop = GetProperty(name); prop)
        {
            (type) = 0;
            if (auto* vec = std::get_if<std::vector<float>>(prop); vec)
            {
                if (vec->size() <= 4)
                    (type) = vec->size();
            }

            else if (auto* bval = std::get_if<bool>(prop); bval)
            {
                (type) = 5;
            }

            else if (auto* tex = std::get_if<cross::TexturePtr>(prop); tex)
            {
                (type) = 6;
            }
        }
        return static_cast<int>(type);
    }
    bool Fx::EditorGetPropertyUsage(const NameID& name)
    {
        return *(GetPropertyUsage(name));
    }
    bool Fx::EditorGetPropertyVisible(const NameID& name)
    {
        return *(GetPropertyVisible(name));
    }
    bool Fx::EditorGetPropertyIsColor(const NameID& name)
    {
        return *GetPropertyIsColor(name);
    }

    float Fx::EditorGetPropertyFloat(const NameID& name)
    {
        float data = 0.0f;
        if (auto* prop = GetProperty(name); prop)
        {
            if (auto* vec = std::get_if<std::vector<float>>(prop); vec)
            {
                if (vec->size() == 1)
                {
                    data = vec->front();
                }
            }
        }
        return data;
    }
    Float2 Fx::EditorGetPropertyFloat2(const NameID& name)
    {
        Float2 data;
        if (auto* prop = GetProperty(name); prop)
        {
            if (auto* vec = std::get_if<std::vector<float>>(prop); vec)
            {
                if (vec->size() == 2)
                {
                    data.x = (*vec)[0];
                    data.y = (*vec)[1];
                }
            }
        }
        return data;
    }
    Float3 Fx::EditorGetPropertyFloat3(const NameID& name)
    {
        Float3 data;
        if (auto* prop = GetProperty(name); prop)
        {
            if (auto* vec = std::get_if<std::vector<float>>(prop); vec)
            {
                if (vec->size() == 3)
                {
                    data.x = (*vec)[0];
                    data.y = (*vec)[1];
                    data.z = (*vec)[2];
                }
            }
        }
        return data;
    }
    Float4 Fx::EditorGetPropertyFloat4(const NameID& name)
    {
        Float4 data;
        if (auto* prop = GetProperty(name); prop)
        {
            if (auto* vec = std::get_if<std::vector<float>>(prop); vec)
            {
                if (vec->size() == 4)
                {
                    data.x = (*vec)[0];
                    data.y = (*vec)[1];
                    data.z = (*vec)[2];
                    data.w = (*vec)[3];
                }
            }
        }
        return data;
    }
    bool Fx::EditorGetPropertyBool(const NameID& name)
    {
        bool data = false;
        if (auto* prop = GetProperty(name); prop)
        {
            if (auto* bval = std::get_if<bool>(prop); bval)
            {
                (data) = *bval;
            }
        }
        return data;
    }

    std::string Fx::EditorGetPropertyString(const NameID& name)
    {
        std::string data = "";
        if (auto* prop = GetProperty(name); prop)
        {
            if (auto* tex = std::get_if<cross::TexturePtr>(prop); tex)
            {
                data = (*tex)->GetName();
            }
        }
        return data;
    }
    std::string Fx::EditorGetPassNameList()
    {
        std::string str;
        for (auto& name : GetExposedPassName())
        {
            str += name.GetName();
            str.push_back(';');
        }
        if (str.size() > 0 && str.back() == ';')
            str.pop_back();
        return str;
    }

    int Fx::EditorGetPassRenderGroup(const NameID& passID)
    {
        auto& pass = GetPass(passID);
        int renderGroup = static_cast<int>(pass.mState.RenderGroup);
        return renderGroup;
    }

    std::string Fx::EditorGetPassShaderPath(const NameID& passID)
    {
        auto& pass = GetPass(passID);
        std::string shaderName = pass.mShaderPtr->GetName();
        return shaderName;
    }

    FxRenderState Fx::EditorFxGetRenderState(const NameID& passID)
    {
        return GetRenderState(passID);
    }
    BlendStateDescForEditor Fx::EditorGetBlendState(const NameID& passID) const
    {
        auto& pass = GetPass(passID);
        const auto& bs = pass.mState.BlendStateDesc;
        BlendStateDesc blendState;
        cross::BlendStateDesc* desc = reinterpret_cast<cross::BlendStateDesc*>(&blendState);
        desc->FromNGIObj(bs);
        BlendStateDescForEditor blendStateForEditor;
        blendStateForEditor.EnableAlphaToCoverage = desc->EnableAlphaToCoverage;
        blendStateForEditor.EnableIndependentBlend = desc->EnableIndependentBlend;
        blendStateForEditor.TargetBlendState.clear();
        for (size_t i = 0; i < desc->TargetCount; ++i)
        {
            blendStateForEditor.TargetBlendState.emplace_back(desc->TargetBlendState[i]);
        }
        return blendStateForEditor;
    }
    DepthStencilStateDesc Fx::EditorGetDepthStencilState(const NameID& passID) const
    {
        auto& pass = GetPass(passID);
        const auto& dss = pass.mState.DepthStencilStateDesc;
        DepthStencilStateDesc depthStencilState;
        depthStencilState.FromNGIObj(dss);
        return depthStencilState;
    }
    RasterizationStateDesc Fx::EditorGetRasterizationState(const NameID& passID) const
    {
        auto& pass = GetPass(passID);
        const auto& rs = pass.mState.RasterizationStateDesc;
        RasterizationStateDesc rasterizationState;
        rasterizationState.FromNGIObj(rs);
        return rasterizationState;
    }
    DynamicStateDesc Fx::EditorGetDynamicState(const NameID& passID) const
    {
        auto& pass = GetPass(passID);
        const auto& ds = pass.mState.DynamicStateDesc;
        DynamicStateDesc dynamicState;
        dynamicState.FromNGIObj(ds);
        return dynamicState;
    }
    void Fx::EditorSetDepthStencilState(const NameID& passID, DepthStencilStateDesc depthstencilstate)
    {
        cross::NGIDepthStencilStateDesc ngiDesc;
        cross::DepthStencilStateDesc* desc = reinterpret_cast<cross::DepthStencilStateDesc*>(&depthstencilstate);
        desc->ToNGIObj(ngiDesc);
        SetDepthStencilState(passID, ngiDesc);
    }
    void Fx::EditorSetRasterizerState(const NameID& passID, RasterizationStateDesc state)
    {
        cross::NGIRasterizationStateDesc ngiDesc;
        cross::RasterizationStateDesc* desc = reinterpret_cast<cross::RasterizationStateDesc*>(&state);
        desc->ToNGIObj(ngiDesc);
        SetRasterizerState(passID, ngiDesc);
    }
    void Fx::EditorSetBlendState(const NameID& passID, BlendStateDescForEditor state)
    {
        cross::NGIBlendStateDesc ngiDesc;
        BlendStateDesc blendState;
        blendState.EnableAlphaToCoverage = state.EnableAlphaToCoverage;
        blendState.EnableIndependentBlend = state.EnableIndependentBlend;
        for (size_t i = 0; i < state.TargetBlendState.size(); ++i)
        {
            blendState.TargetBlendState[i] = state.TargetBlendState[i];
        }
        cross::BlendStateDesc* desc = reinterpret_cast<cross::BlendStateDesc*>(&blendState);
        desc->ToNGIObj(ngiDesc);
        SetBlendState(passID, ngiDesc);
    }
    void Fx::EditorSetDynamicState(const NameID& passID, DynamicStateDesc state)
    {
        cross::NGIDynamicStateDesc ngiDesc;
        cross::DynamicStateDesc* desc = reinterpret_cast<cross::DynamicStateDesc*>(&state);
        desc->ToNGIObj(ngiDesc);
        SetDynamicState(passID, ngiDesc);
    }
    Float2 Fx::EditorGetPropertyMinMax(const NameID& name)
    {
        Float2 data;
        if (auto range = GetPropertyMinMax(name); range && range->IsValid())
        {
            data.x = range->mMin;
            data.y = range->mMax;
        }
        return data;
    }
}}   // namespace cross::resource
