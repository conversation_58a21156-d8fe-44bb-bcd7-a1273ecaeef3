#pragma once

#ifndef RESOURCE_H
#define RESOURCE_H

#include <optional>
#include "Resource/BaseClasses/Named.h"
#include "Resource/ResourceBase.h"
#include "Resource/resourceforward.h"
#include "CrossBase/Serialization/ResourceMetaHeader.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CrossBase/CEMetaMacros.h"
#include "GamePlayBaseFramework/meta/reflection/builder/class_builder.inl"

namespace cross
{
    class AnimStateMachineAsset;

	class Resource;
	class RenderSyncResource;

    class CurveControllerRes;

    namespace resource
    {
        class Texture;
        class Texture2D;
        class Texture3D;
        class TextureCube;
        class TextureUDIM;
        class Texture2DVirtual;
        class Texture2DArray;
        class Material;
        class MaterialInterface;
        class MaterialParameterCollection;
        class MaterialFunction;
        class ScriptResource;
        class Fx;
        class MeshAssetDataResource;
        class Shader;
        class ComputeShader;
        class RayTracingShader;
        class Asset;
        class ResourceAssetManager;
        class WorldBlock;
        class PrefabResource;
        class TerrainResource;
        class FontResource;
        class PCGResource;
        class RenderTextureResource;
        class BinaryResource;
        class InstanceDataResource;
        class WorkflowGraphResource;
        class TsResource;
        class InputActionMappingResource;
        class DataAssetResource;
        class HLODResource;
#if AIRPORT_SCENE_EDITOR
        class AirportResource;
#endif
    }
}

#include "Resource/AsyncResource.h"
#include "CrossBase/ReferenceCountObject.h"

namespace cross::skeleton
{
	class SkeletonResource;
	class SkeletonPhysicsResource;

	using SkeltResPtr = CE_REAL_PTR_IMPLMENT<SkeletonResource>;
	using SkeltPhyResPtr = CE_REAL_PTR_IMPLMENT<SkeletonPhysicsResource>;
}

namespace cross::anim
{
	class AnimatrixRes;
	class AnimCompositeRes;
	class AnimSequenceRes;
	class AnimatorRes;
	class AnimBlendSpaceRes;
    class MotionDataAsset;
    class AnimResourceBase;

    using AnimResourceBasePtr = CE_REAL_PTR_IMPLMENT<AnimResourceBase>;

	using AnimCmpResPtr = CE_REAL_PTR_IMPLMENT<AnimCompositeRes>;
	using AnimatrixResPtr = CE_REAL_PTR_IMPLMENT<AnimatrixRes>;
	using AnimSeqResPtr = CE_REAL_PTR_IMPLMENT<AnimSequenceRes>;
	using AnimatorResPtr = CE_REAL_PTR_IMPLMENT<AnimatorRes>;
	using AnimBlendSpaceResPtr = CE_REAL_PTR_IMPLMENT<AnimBlendSpaceRes>;
    using MotionDataAssetPtr = CE_REAL_PTR_IMPLMENT<MotionDataAsset>;
}

namespace cross::fx
{
    class ParticleSystemResource;
    class ParticleEmitterResource;

    using ParticleSystemResPtr = CE_REAL_PTR_IMPLMENT<ParticleSystemResource>;
    using ParticleEmitterResPtr = CE_REAL_PTR_IMPLMENT<ParticleEmitterResource>;
}

namespace cross
{
    using ScriptResourcePtr        = CE_REAL_PTR_IMPLMENT<resource::ScriptResource>;
    using ScriptPtr                = CE_REAL_PTR_IMPLMENT<resource::ScriptResource>;
    using TsPtr                    = CE_REAL_PTR_IMPLMENT<resource::TsResource>;
    using TexturePtr               = CE_REAL_PTR_IMPLMENT<resource::Texture>;
    using ShaderPtr                = CE_REAL_PTR_IMPLMENT<resource::Shader>;
    using ComputeShaderPtr         = CE_REAL_PTR_IMPLMENT<resource::ComputeShader>;
    using RayTracingShaderPtr      = CE_REAL_PTR_IMPLMENT<resource::RayTracingShader>;
    using MaterialPtr              = CE_REAL_PTR_IMPLMENT<resource::Material>;
    using MaterialInterfacePtr     = CE_REAL_PTR_IMPLMENT<resource::MaterialInterface>;
    using MaterialFunctionPtr      = CE_REAL_PTR_IMPLMENT<resource::MaterialFunction>;
    using MPCPtr                   = CE_REAL_PTR_IMPLMENT<resource::MaterialParameterCollection>;
    using Texture2DPtr             = CE_REAL_PTR_IMPLMENT<resource::Texture2D>;
    using TextureVirtualPtr        = CE_REAL_PTR_IMPLMENT<resource::Texture2DVirtual>;
    using TextureCubePtr           = CE_REAL_PTR_IMPLMENT<resource::TextureCube>;
    using Texture3DPtr             = CE_REAL_PTR_IMPLMENT<resource::Texture3D>;
    using TextureUDIMPtr           = CE_REAL_PTR_IMPLMENT<resource::TextureUDIM>;
    using Texture2DArrayPtr        = CE_REAL_PTR_IMPLMENT<resource::Texture2DArray>;
    using PrefabResourcePtr        = CE_REAL_PTR_IMPLMENT<resource::PrefabResource>;
    using WorldBlockPtr            = CE_REAL_PTR_IMPLMENT<resource::WorldBlock>;
    using AssetPtr                 = std::shared_ptr<resource::Asset>;
    using ResourcePtr              = CE_REAL_PTR_IMPLMENT<Resource>;
	using RenderSyncResourcePtr    = CE_REAL_PTR_IMPLMENT<RenderSyncResource>;
    using FxPtr                    = CE_REAL_PTR_IMPLMENT<resource::Fx>;
    using MeshAssetDataResourcePtr = CE_REAL_PTR_IMPLMENT<resource::MeshAssetDataResource>;
    using PCGResourcePtr           = CE_REAL_PTR_IMPLMENT<resource::PCGResource>;

    using CurveControllerResPtr    = CE_REAL_PTR_IMPLMENT<CurveControllerRes>;
    using TerrainResourcePtr       = CE_REAL_PTR_IMPLMENT<resource::TerrainResource>;
    using FontResourcePtr          = CE_REAL_PTR_IMPLMENT<resource::FontResource>;
    using RenderTextureResourcePtr = CE_REAL_PTR_IMPLMENT<resource::RenderTextureResource>;
    using BinaryResourcePtr        = CE_REAL_PTR_IMPLMENT<resource::BinaryResource>;
    using InstanceDataResourcePtr  = CE_REAL_PTR_IMPLMENT<resource::InstanceDataResource>;
    using WorkflowGraphResourcePtr = CE_REAL_PTR_IMPLMENT<resource::WorkflowGraphResource>;
    using InputActionMappingResourcePtr = CE_REAL_PTR_IMPLMENT<resource::InputActionMappingResource>;
    using DataAssetResourcePtr = CE_REAL_PTR_IMPLMENT<resource::DataAssetResource>;
    using HLODResourcePtr = CE_REAL_PTR_IMPLMENT<resource::HLODResource>;
#if AIRPORT_SCENE_EDITOR
    using AirportResourcePtr       = CE_REAL_PTR_IMPLMENT<resource::AirportResource>;
#endif

    //using CETexturePtr = CE_REAL_PTR_IMPLMENT<CETexture>;

#define StatementDefaultResource(Res)      \
    static ResourcePtr GetDefault() { return cross::ResourceManager::Instance().GetDefaultRes<Res>(); };

#define StatementDefaultResourceAPI(Api, Res) \
    Api static ResourcePtr GetDefault() { return cross::ResourceManager::Instance().GetDefaultRes<Res>(); };

#define FRIEND_WITH_REFLECTION_MODULE                                                                                                                                                                                                          \
    template<typename T, typename... A>                                                                                                                                                                                                        \
    friend class gbf::reflection::detail::cxx::TCtorCallerImpl;

#define INIT_RTTI_IN_CONSTRUCTOR                                                                                                                                                                                                               \
    if constexpr (std::is_base_of_v<::gbf::reflection::RttiBase, std::remove_pointer_t<decltype(this)>>)                                                                                                                                       \
        this->__bind_rtti_info(__type_of<std::remove_pointer_t<decltype(this)>>(), ::gbf::reflection::StorageType::StorageRemote, ::gbf::reflection::next_reflection_obj_id());                                                                \
    else {}

#define LoadDefaultResource(Res, ResPath) resource::Res::g##Res##Default = gAssetStreamingManager->LoadSynchronously(ResPath)

    class Asset;
    class Resource_API CEMeta(Reflect, Script, Cli, WorkflowType) Resource : public ResourceBase, /* For ResourceFuturePtr */ public ReferenceCountObject, /* For ResourceWeakPtr */ public SafeObject
	{
        friend class ResourceManager;
        friend class resource::ResourceAssetManager;
        FRIEND_WITH_REFLECTION_MODULE;
	public:
        static ResourcePtr GetDefault()
        {
            Assert(false);
            return ResourcePtr(nullptr);
        }

        void Serialize(cross::SimpleSerializer& s) override;
        bool Serialize(cross::SimpleSerializer& s, const std::string& path) override;
        bool Serialize(SerializeNode&& s,                 const std::string& path) override;
        bool Serialize(const std::vector<char>& fbBufVec, const std::string& path, SerializeNode&& customSerialNode = {}) override;
        bool GetIsReadyForUse() const
        {
            return mReadyStat == 2;
        }
        bool GetIsInPostDerserialize() const {
            return mReadyStat == 1;
        }
        int GetReadyState() const
        {
            return mReadyStat;
        }
        void ResetReadyState() { mReadyStat = 0; }

        bool NeedPostDeserialized() const {
            return mReadyStat == 0;
        }
        void SetUseStreaming(bool value)
        {
            bUseStreaming = value;
        }
        bool GetUseStreaming()
        {
            return bUseStreaming;
        }
        virtual bool Deserialize(SimpleSerializer const& s) override;
        virtual bool Deserialize(cross::FBSerializer const& s) override;
        virtual bool Deserialize(DeserializeNode const& s) override;
        virtual bool HasAsset() const { return mAsset != nullptr; }
        virtual void DeserializeCustomNode(const DeserializeNode& json) {};
        virtual bool PostDeserializeForUse() {
            Assert(mReadyStat == 0);
            mReadyStat = 1;
            auto ret = PostDeserialize();
            mReadyStat = 2;
            return ret;
        }
		virtual bool PostDeserialize() { return true; }
        virtual void PostDestroy() { ClearDependResource(); };
        virtual bool ResetResource() override 
        {
            //mReadyStat = 0;
            mResetVersion++;
            return true; 
        };

        virtual UInt64 GetCPUMemoryFootprint() const { return 0; }
        virtual UInt64 GetGPUMemoryFootprint() const { return 0; }

        //proxy for asset
        //std::string Resource::GetSerialPath(std::set<std::string>& inDepencePaths);
        CEFunction(Reflect, ScriptCallable, Puerts)
        virtual std::string             GetName() const;
        const char* const               GetName_CStr() const;
        CEFunction(Reflect, ScriptCallable, Puerts)
        virtual const std::string&      GetGuid_Str() const;
        SerializeNode                   GetSerialNode() const;
        void                            AddDependResource(ResourcePtr inRes);
        void                            DelDependResource(ResourcePtr inRes);
        void                            ClearDependResource();
        CEFunction(ScriptCallable)
        void                            AddReferenceResource(const std::string& inRes);
        CEFunction(ScriptCallable)
        void                            DelReferenceResource(const std::string& inRes);
        void                            CreateAsset(const std::string& inAssetPath, const std::string& guid = "");
        const std::set<std::string>&    GetReferenceResources();
        UInt32                          GetResourceVersion() const;
        UInt32                          GetResourceSize() const;
        void                            ClearReference();

        threading::TaskEventPtr         GetAsyncLoadEvent() const;
        bool                            IsAsyncLoadValid() const;
        bool                            IsAsyncLoadComplete() const;
        void                            WaitForAsyncLoad() const;
        void                            SetAsyncLoadEvent(const threading::TaskEventPtr& event);
        bool                            HasReferenceResources() const noexcept;
        size_t                          GetReferenceResourceCount() const noexcept;
        ResourcePtr                     SharedFromThis() { return ResourcePtr(this); }
        void                            RequestRefresh();
        void                            SetImportSet(const std::string& importSet);

        bool                            IsExternal() const { return mExternal; }
        void                            SetExternal(bool isExternal) { mExternal = isExternal; }

        AssetPtr                        GetAsset() const { return mAsset; }
        UInt32                          GetVersion() const { return mVersion; }
        UInt32                          GetResetVersion() const { return mResetVersion; }
        void                            ResetDirty() { mVersion = mResetVersion; }
        bool                            IsDirty() const { return mVersion != mResetVersion;}
        bool                            ReleaseDirty() 
        {
            bool r = IsDirty();
            ResetDirty();
            return r;
        }

        bool                            NeedReload();
        virtual void                    AfterAssetDelete() {};

    protected:
        void                            SetResourceVersion(UInt32 version);
        void                            SetOriginAssetPath(std::string inOriginAssetPath);
        void                            SetResourceType(CrossSchema::ResourceType inResourceType);
        void                            SetResourceSize(UInt32 size);
        bool                            bUseStreaming : 1;

        virtual void                    AddSelfItem(flatbuffers::FlatBufferBuilder& outBuilder, flatbuffers::Offset<void>& outSelfOffset, CrossSchema::ResourceType& outResType) { Assert(0); };
    protected:
        AssetPtr  mAsset;
        CrossUUID mGuid;
        bool mExternal{ false };
        std::atomic<int> mReadyStat { 0 }; // postseriliaze state 0 not begein 1 in 2 finish
        UInt32 mVersion{0};
        UInt32 mResetVersion{0};
        std::unordered_set<ResourcePtr, ResourcePtr::Hash> mDependResources;
    public:
        static std::string              GetPathFromSerialNode(const DeserializeNode& inSerialNode);
        static std::string              GetNDAFilePath(const std::string& assetPath, const std::string& ndaSavePath);
        static bool                     Serialize(ResourceMetaHeader& inMetaHeader, const std::string& contentStr, const std::string& path);
        static bool                     Serialize(ResourceMetaHeader& inMetaHeader, const UInt8* contentData, SizeType contentSize, const std::string& path);
        static std::string              Serialize(const ResourceMetaHeader& inMetaHeader);
        static std::string              Serialize(const ResourceMetaHeader& inMetaHeader, SerializeNode&& dependenceSeriliazeNode, SerializeNode&& customeNode);

        static constexpr std::string_view  ASSET_MAGIC_NUMBER_KEY     = "ASSET_MAGIC_NUMBER";
        static constexpr std::string_view  PATH_KEY                   = "Path";
        static constexpr std::string_view  ClassID_KEY                = "ClassID";
        static constexpr std::string_view  MagicNumber_KEY            = "MagicNumber";
        static constexpr std::string_view  Version_KEY                = "Version";
        static constexpr std::string_view  Guid_KEY                   = "Guid";
        static constexpr std::string_view  DataSize_KEY               = "DataSize";
        static constexpr std::string_view  ContentType_KEY            = "ContentType";
        static constexpr std::string_view  IsStreamFile_KEY           = "IsStreamFile";
        static constexpr std::string_view  Custom_KEY                 = "Custom";
        static constexpr std::string_view  Dependence_KEY             = "Dependency";
        static constexpr std::string_view  MeshAssetLODSetting_KEY    = "MeshAssetLODSetting";
        static constexpr std::string_view  SimplifySetting_KEY        = "SimplifySetting";
        static constexpr std::string_view  TriangleRate_KEY           = "triangleRate"; 
        static constexpr std::string_view  TextureCubeSHCoefs_KEY     = "CubemapSH";
        static constexpr std::string_view  ImportSet_KEY              = "ImportSet";
        static constexpr int               gResourceVersion           = 4;
        static constexpr int               gResourceJsonHeaderVersion = 5;
	};

    class Resource_API Streamable
    {
    public:
        virtual void RequestStreaming(UInt32) = 0;
        virtual void RequestStreamout(UInt32 NewMipCount) = 0;
    };

    struct NGIDevice;
    struct NGIStagingBuffer;

    struct StreamingScratchBuffer
    {
        struct Range
        {
            void* mDestination;
            SizeType mOffset;
            SizeType mSize;
        };

        Resource_API static StreamingScratchBuffer* Get();

        Resource_API static void Release();

        StreamingScratchBuffer(SizeType bufferSize);

        ~StreamingScratchBuffer() = default;

        Resource_API Range Allocate(SizeType size);

        Resource_API void Free(const Range& range);

        Resource_API NGIStagingBuffer* GetBuffer()
        {
            return mBuffer.get();
        }

        Resource_API void Tick();

    private:
        struct AllocationRange
        {
            SizeType mOffset;
            SizeType mReach;
        };

        static std::unique_ptr<StreamingScratchBuffer> mInstance;

        SizeType mBufferSize;

        std::unique_ptr<NGIStagingBuffer> mBuffer;
        std::vector<AllocationRange> mAllocationRanges;
        std::vector<AllocationRange> mFreeRequests;

        SizeType mRangeIndex;
        std::mutex mMutex;
    };
}

#include "Resource/TypedSubResource.h"

#endif//RESOURCE_H
