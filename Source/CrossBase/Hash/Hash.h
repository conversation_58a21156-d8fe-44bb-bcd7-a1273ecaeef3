#pragma once

#include <memory>
#include "PlatformDefs.h"
#include "Hash/MurmurHash3.h"
#include "CrossBase/String/StringHash.h"
#include "CrossBase/Log.h"
#include "CrossBase/Platform/PlatformTypes.h"

// This requires SSE4.2 which is present on Intel Nehalem (Nov. 2008)
// and AMD Bulldozer (Oct. 2011) processors.
#ifdef _M_X64
#define ENABLE_SSE_CRC32 1
#else
#define ENABLE_SSE_CRC32 0
#endif

#if ENABLE_SSE_CRC32
#pragma intrinsic(_mm_crc32_u32)
#pragma intrinsic(_mm_crc32_u64)
#endif
namespace cross
{
    template <typename T> constexpr T AlignUpWithMask(T value, size_t mask)
    {
        return (T)(((size_t)value + mask) & ~mask);
    }

    template <typename T> constexpr T AlignDownWithMask(T value, size_t mask)
    {
        return (T)((size_t)value & ~mask);
    }

    template <typename T> constexpr T AlignUp(T value, size_t alignment)
    {
        return AlignUpWithMask(value, alignment - 1);
    }

    template <typename T> constexpr T AlignDown(T value, size_t alignment)
    {
        return AlignDownWithMask(value, alignment - 1);
    }

    inline size_t HashRange(const std::uint32_t* const Begin, const std::uint32_t* const End, size_t Hash = 2166136261U)
    {
#ifndef _MANAGED
#if ENABLE_SSE_CRC32
        const UInt64* Iter64 = (const UInt64*)AlignUp(Begin, 8);
        const UInt64* const End64 = (const UInt64* const)AlignDown(End, 8);

        // If not 64-bit aligned, start with a single u32
        if ((UInt32*)Iter64 > Begin)
            Hash = _mm_crc32_u32((UInt32)Hash, *Begin);

        // Iterate over consecutive u64 values
        while (Iter64 < End64)
            Hash = _mm_crc32_u64((UInt64)Hash, *Iter64++);

        // If there is a 32-bit remainder, accumulate that
        if ((UInt32*)Iter64 < End)
            Hash = _mm_crc32_u32((UInt32)Hash, *(UInt32*)Iter64);
#else
        // An inexpensive hash for CPUs lacking SSE4.2
        for (const UInt32* Iter = Begin; Iter < End; ++Iter)
            Hash = 16777619U * Hash ^ *Iter;
#endif
#endif
        return Hash;
    }

    template <typename T> inline size_t HashState(const T* StateDesc, size_t Count = 1, size_t Hash = 2166136261U)
    {
        static_assert((sizeof(T) & 3) == 0 && alignof(T) >= 4, "State object is not word-aligned");
        return HashRange((std::uint32_t*)StateDesc, (std::uint32_t*)(StateDesc + Count), Hash);
    }


    struct Hash128
    {
        union
        {
            unsigned char bytes[16];
            UInt64        u64[2];
            UInt32        u32[4];
        } hashData;

        friend inline bool operator == (const Hash128& lhs, const Hash128& rhs) { return lhs.hashData.u64[0] == rhs.hashData.u64[0] && lhs.hashData.u64[1] == rhs.hashData.u64[1]; }
        friend inline bool operator != (const Hash128& lhs, const Hash128& rhs) { return lhs.hashData.u64[0] != rhs.hashData.u64[0] || lhs.hashData.u64[1] != rhs.hashData.u64[1]; }

        Hash128() { hashData.u64[0] = 0; hashData.u64[1] = 0; }
    };

    struct DoubleHash
    {
    private:
        union Value
        {
            Value() :mCombined(0) {}
            uint64_t mCombined;
            struct
            {
                uint32_t mH0;
                uint32_t mH1;
            }mIndividual;
        }mValue;

    public:
        uint64_t GetHash()const { return mValue.mCombined; }
		uint32_t GetHash0()const { return mValue.mIndividual.mH0; }
		uint32_t GetHash1()const { return mValue.mIndividual.mH1; }
		void SetHash(uint32_t h0, uint32_t h1)
		{
			mValue.mIndividual.mH0 = h0;
			mValue.mIndividual.mH1 = h1;
		}
        void Clear() { mValue.mCombined = 0; }
        bool IsZero()const { return mValue.mCombined == 0; }
        
        inline bool operator==(const DoubleHash& other)const { return mValue.mCombined == other.mValue.mCombined; }
        inline bool operator!=(const DoubleHash& other)const { return mValue.mCombined != other.mValue.mCombined; }
        inline bool operator<(const DoubleHash& other) const { return mValue.mCombined < other.mValue.mCombined; }
        inline bool operator>(const DoubleHash& other) const { return mValue.mCombined > other.mValue.mCombined; }
        inline bool operator()(const DoubleHash& lhs, const DoubleHash& rhs) const { return lhs.mValue.mCombined < rhs.mValue.mCombined; }

        struct hash{ auto operator()(const DoubleHash& x) const { return x.GetHash(); } };
    };
    static_assert(sizeof(DoubleHash) == sizeof(uint64_t), "");

    class CROSS_BASE_API HashFunction
    {
    public:
        //djb2 hash function
        static UInt32 Djb2(const char* pStr);
        static UInt32 Djb2(const char* pStr, UInt32 sizeInByte);
        //prev_hash: value of Djb2 return result
        static UInt32 Djb2AppendData(UInt32 prev_hash, const char* pStr, UInt32 sizeInByte);

        //sdbm hash function
        static UInt32 Sdbm(const char* pStr);
        static UInt32 Sdbm(const char* pStr, UInt32 sizeInByte);
        //prev_hash: value of Sdbm return result
        static UInt32 SdbmAppendData(UInt32 prev_hash, const char* pStr, UInt32 sizeInByte);
        static UInt64 Sdbm64(const char* pStr, UInt32 sizeInByte);

        static UInt32 HashMemory(const void* data, SInt32 length)
        {
			UInt32 result;
			MurmurHash3_x86_32(data, length, 0x43524f53, &result);
			return result;
        }

        static StringHash64 HashString64(const char* str, SInt32 length = 0)
        {
            StringHash64 result;
            if (str && *str)
            {
                length = (length > 0) ? length : (UInt32)strlen(str);
                result.SetHash(HashMemory(str, length), Djb2(str, length));
            }
            return result;
        }

        static StringHash32 HashString32(const char* str, SInt32 length = 0)
        {
            StringHash32 result{ 0 };
            if (str && *str)
            {
                length = (length > 0) ? length : (UInt32)strlen(str);
                result = HashMemory(str, length);
            }
            return result;
        }
    };

	template <class T>
	inline void hash_combine(std::size_t& seed, const T& v)
	{
		std::hash<T> hasher;
		seed ^= hasher(v) + 0x9e3779b9 + (seed << 6) + (seed >> 2);
	}

	struct SimpleStream
	{
		UInt8* mCurrentPos;
		UInt8* mEndPos;

		template<typename T, typename = std::enable_if_t<std::is_enum_v<T> || std::is_arithmetic_v<T> || std::is_pointer_v<T>>>
		SimpleStream& operator << (const T& t)
		{
			if constexpr (std::is_same_v<T, bool>)
			{
				*mCurrentPos = t ? UINT8_MAX : 0;
				mCurrentPos++;
			}
			else
			{
				*reinterpret_cast<T*>(mCurrentPos) = t;
				mCurrentPos += sizeof(T);
			}
			Assert(mCurrentPos <= mEndPos);
			return *this;
		}

		template<typename T, typename TCount>
		SimpleStream& operator << (std::tuple<T*, TCount>&& pair)
		{
			auto[data, count] = pair;
			if constexpr (std::is_enum_v<T> || std::is_arithmetic_v<T> || std::is_pointer_v<T>)
			{
				// ensure no padding
				static_assert(sizeof(T) == alignof(T));
				memcpy(mCurrentPos, data, count * sizeof(T));
				mCurrentPos += count * sizeof(T);
				Assert(mCurrentPos <= mEndPos);
			}
			else
			{
				// may contains padding
				for (UInt32 i = 0; i < count; i++)
				{
					*this << data[i];
				}
			}
			return *this;
		}
	};

	template<size_t MaxStaticSize, typename T>
    size_t Hash(const T& object, size_t dynamicSize)
    {
        if (dynamicSize > MaxStaticSize)
        {
            auto wordCount = (dynamicSize - 1 / 4) + 1;
            // array was zero initialized
            auto packedData = std::make_unique<std::uint32_t[]>(wordCount);
            SimpleStream stream{reinterpret_cast<std::uint8_t*>(packedData.get()), reinterpret_cast<std::uint8_t*>(packedData.get() + wordCount)};
            stream << object;
            return HashState(packedData.get(), wordCount);
        }
        else
        {
            constexpr auto wordCount = (MaxStaticSize - 1) / 4 + 1;
            std::uint32_t packedData[wordCount]{};
            SimpleStream stream{reinterpret_cast<std::uint8_t*>(packedData), reinterpret_cast<std::uint8_t*>(packedData + wordCount)};
            stream << object;
            return HashState(packedData, wordCount);
        }
    }

	template<typename T>
	size_t Hash(const T& object)
	{
		return Hash<sizeof(T)>(object, sizeof(T));
	}
}

namespace std
{

template<typename... TT>
struct hash<std::tuple<TT...>>
{
    size_t operator()(std::tuple<TT...> const& tt) const 
    { 
        size_t seed = 0;
        hash_impl(seed, tt, index_sequence_for<TT...>{});
        return seed;
    }
private:
    template<size_t... I>
    void hash_impl(size_t& seed, const tuple<TT...>& tt, index_sequence<I...>) const
    {
        (cross::hash_combine(seed, get<I>(tt)), ...);
    }
};

}
