#pragma once

#include <array>
#include <string>
#include <string_view>
#include <algorithm>
#include "memoryhooker/Module.h"

#if defined(__clang__) || defined(__GNUC__)
#include <cxxabi.h>
#endif

namespace cross
{
#ifdef _MSC_VER
    constexpr std::array<std::string_view, 3> CrossMSCNameDecorator
    {{
        "struct ",
        "class ",
        "enum ",
    }};
#endif
	template<typename T>
	static constexpr const char* NameDetail()
	{
#if	defined(__GNUC__)
		return __PRETTY_FUNCTION__;
#else
		return __FUNCSIG__;
#endif
	}
	template<typename T>
	static constexpr std::string_view NameDetailPretty()
	{
#if defined(__clang__)
		constexpr std::string_view name{ NameDetail<T>() };
		constexpr size_t start = name.find_first_of('[') + 5;
		constexpr size_t end = name.find_last_of(']');
		return name.substr(start, end-start);
#else
		constexpr std::string_view name{ NameDetail<T>() };
		constexpr size_t start = name.find_first_of('<') + 1;
		constexpr size_t end = name.find_last_of('>');
		std::string_view name2 = name.substr(start, end - start);
		size_t start2 = name2.find_first_of(' ') + 1;
        name2.remove_prefix(start2);
        return name2;
#endif
	}
    template<auto T>
    constexpr std::string_view NameDetail()
    {
#ifdef _MSC_VER
        return __FUNCSIG__;
#else
        return __PRETTY_FUNCTION__;
#endif
    }
    template<auto T>
    inline constexpr std::string_view enum2string()
    {
        constexpr std::string_view sample = NameDetail<int>();
        constexpr size_t pos = sample.find("int");
        constexpr std::string_view str = NameDetail<T>();
        constexpr auto left = str.find("cross::NameDetail") + 18;
        constexpr auto next1 = str.rfind(sample[pos + 3]);
#if defined(__clang__) || defined(_MSC_VER)
        constexpr auto name = str.substr(left, next1 - left);
#else
        constexpr auto name = str.substr(left + 5, next1 - left - 5);
#endif
        return name;
    }


    template <typename T>
    inline std::string DemangleName()
    {
        decltype(auto) typeInfo = typeid(T);
#if defined(_MSC_VER)
        std::string result = typeInfo.name();
        for (auto const& decorator : CrossMSCNameDecorator)
        {
            for (auto itr = result.find(decorator); 
                 itr != std::string::npos; 
                 itr = result.find(decorator))
            {
                result.erase(itr, decorator.length());
            }
        }
        return result;

#elif defined(__clang__) || defined(__GNUC__)
        int status{ 0 };
        char* demangledName = abi::__cxa_demangle(typeInfo.name(), nullptr, nullptr, &status);
        if (demangledName)
        {
            std::string result{ demangledName };
            cross::Memory::Free(demangledName);
            return result;
        }
        throw std::runtime_error{ "Failed to demangle type name." };
#else
#error "Your compiler is not supported, yet."
#endif
    }
}
