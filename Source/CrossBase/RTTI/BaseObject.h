#pragma once

#include <string>
#include "RTTI/Base.h"
#include "Serialization/Codec/SimpleBinaryCodec.hpp"
#include "Serialization/Codec/FlatBufferCodec.hpp"
#include "CrossBase/Serialization/SerializeNode.h"
namespace cross
{
    class CROSS_BASE_API Object : public Base
    {
    public:
        using FactoryFunction = Object*(*)(void);

        struct RTTI
        {
            RTTI* base;							// super rtti class
            Object::FactoryFunction factory;	// the factory function of the class
            int classID;						// the class ID of the class
            std::string className;				// the name of the class
            int size;							// sizeof (Class)
            bool isAbstract;					// is the class Abstract?
        };

        static constexpr int NullTypeID = -1;

    protected:
        Object();

        // disable copy construct
        Object(Object const&) = delete;

        // disable copy assignment
        Object& operator= (Object const&) = delete;

    public:
        // explicit destructor disable composite move construct and move assignment
        virtual ~Object() = default;

    public: // RTTI interfaces
        int GetClassID() const noexcept
        {
            return mClassID;
        }

        std::string const& GetClassName() const
        {
            return ClassIDToString(mClassID);
        }

        /// Creates an object of type classID.
        /// if instanceID is 0 a unique id will be generated  if its non 0 the object will have the specified instanceID
        static Object* Produce(int classID);

        /// Get the class name from the classID
        static const std::string& ClassIDToString(int classID);

        /// Get the classID from the class name, returns -1 if no classID was found
        static int StringToClassID(const std::string& classString);
        static int StringToClassIDCaseInsensitive(const std::string& classString);

        static void RegisterRuntimeClass(int inClassID, int inBaseClass, const std::string& inName, int byteSize, FactoryFunction inFunc, bool isAbstract);

    private: // RTTI internals
        void SetClassID(int classID) noexcept
        {
            mClassID = classID;
        }

    public: // overrides
        virtual unsigned int GetMemoryUsaged() const { return 0; }
        
        virtual void Serialize(cross::SimpleSerializer& s);
        virtual bool Serialize(SerializeNode&& s, const std::string& path);
        virtual bool Serialize(cross::SimpleSerializer& s, const std::string& path);
        virtual bool Serialize(const std::vector<char>& fbBufVec, const std::string& path, SerializeNode&& customeNode = {});
        
        virtual bool Deserialize(cross::SimpleSerializer const& s);
		virtual bool Deserialize(cross::FBSerializer const&s);
        virtual bool Deserialize();
        virtual bool Deserialize(DeserializeNode const& s);
        
        virtual bool CreateGPUResource();
        
        virtual bool ResetResource();

    protected:
        UInt32  mClassID;
    };
}
