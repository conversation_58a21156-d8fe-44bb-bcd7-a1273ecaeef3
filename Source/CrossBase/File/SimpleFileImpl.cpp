#if 0//please use the filesystem to replace simplefile

#include "PCH/CrossBasePCHPrivate.h"
#include <fstream>
//#include "File/SimpleFile.h"
#include "File/SimpleFileImpl.h"
#if CROSSENGINE_LINUX
#include <bits/ios_base.h>
#endif
namespace cross
{
    bool SimpleFileImpl::Open(std::string const& path, File::AccessMode accessMode)
    {
        // exceptional safty leads to these lines of ugly codes
        int fstreamAccessMode = AccessMode(accessMode);
#if CROSSENGINE_LINUX
        mFileStream.open(path, (std::ios_base::openmode)fstreamAccessMode);
#else
        mFileStream.open(path, fstreamAccessMode);
        #endif
        if (mFileStream)
        {
            mFileName = path;
            mOpenMode = fstreamAccessMode;
            return true;
        }

        return false;
    }
    
    void SimpleFileImpl::Close()
    {
        if (mFileStream)
        {
            mOpenMode = 0;
            mFileStream.close();
        }
    }
    
    void SimpleFileImpl::Flush()
    {
        if (mFileStream)
        {
            mFileStream.flush();
        }
    }

    int SimpleFileImpl::GetOpenMode()
    {
        return mOpenMode;
    }

    UInt64 SimpleFileImpl::Read(void* buffer, UInt64 size)
    {
        auto prePose = mFileStream.tellg();
        if ((UInt64)prePose == File::NonPose)
            return File::NonPose;
        
        mFileStream.read(reinterpret_cast<char*>(buffer), size);
        
        auto postPose = mFileStream.tellg();
        if ((UInt64)postPose == File::NonPose)
            return File::NonPose;

        assert(prePose <= postPose);
        return postPose - prePose;
    }
    
    bool SimpleFileImpl::Write(const void* buffer, UInt64 size)
    {
        if (TestBit(std::ios::app))
        {
            mFileStream.seekp(std::ios::end);
        }

        auto pose = mFileStream.tellp();
        if ((UInt64)pose == File::NonPose)
            return false;
        
        mFileStream.write(reinterpret_cast<char const*>(buffer), size);
        return UInt64(mFileStream.tellp() - pose) == size;
    }
    
    UInt64 SimpleFileImpl::Size()
    {
        if (TestBit(std::ios::in))
            return SizeRead();
        else if (TestBit(std::ios::out))
            return SizeWrite();
        return File::NonPose;
    }
    
    bool SimpleFileImpl::Resize(UInt64)
    {
        return false;
    }
    
    bool SimpleFileImpl::SeekRead(SInt64 offset, File::SeekFrom seekPos)
    {
        if (!TestBit(std::ios::in))
        {
            return false;
        }
        auto const dir = SeekFrom(seekPos);
        mFileStream.seekg(offset, dir);
        return true;
    }

    bool SimpleFileImpl::SeekWrite(SInt64 offset, File::SeekFrom seekPos)
    {
        if (!TestBit(std::ios::out))
        {
            return false;
        }
        auto const dir = SeekFrom(seekPos);
        mFileStream.seekp(offset, dir);
        return true;
    }

    bool SimpleFileImpl::Eof() const
    {
        return mFileStream.eof();
    }
    
    constexpr int SimpleFileImpl::AccessMode(File::AccessMode accessMode) noexcept
    {
        switch (accessMode)
        {
            case File::AccessMode::WriteMode:
                return std::ios::binary | std::ios::out;
            case File::AccessMode::ReadWriteMode:
                return std::ios::binary | std::ios::in | std::ios::out;
            case File::AccessMode::AppendMode:
                return std::ios::binary | std::ios::app;
            case File::AccessMode::Trunc:
                return std::ios::binary | std::ios::in | std::ios::trunc;
            default:
                // default with read authority
                return std::ios::binary | std::ios::in;
        }
    }

    constexpr std::ios_base::seekdir SimpleFileImpl::SeekFrom(File::SeekFrom seekPos) noexcept
    {
        switch (seekPos)
        {
        case File::SeekFrom::Current:
            return std::ios::cur;
        case File::SeekFrom::Begin:
            return std::ios::beg;
        case File::SeekFrom::End:
            return std::ios::end;
        default:
            return std::ios::cur;
        }
    }

    UInt64 SimpleFileImpl::SizeRead() const
    {
        auto oldPose = mFileStream.tellg();
        if ((UInt64)oldPose == File::NonPose)
            return File::NonPose;
        
        mFileStream.seekg(0, std::ios::beg);
        auto prePose = mFileStream.tellg();
        if ((UInt64)prePose == File::NonPose)
            return File::NonPose;
        
        mFileStream.seekg(0, std::ios::end);
        auto postPose = mFileStream.tellg();
        if ((UInt64)postPose == File::NonPose)
            return File::NonPose;
        
        assert(prePose <= postPose);
        auto result = postPose - prePose;
        
        mFileStream.seekg(oldPose);
        if (mFileStream.tellg() == oldPose)
            return result;
        return File::NonPose;
    }
    
    bool SimpleFileImpl::SizeWrite() const
    {
        auto oldPose = mFileStream.tellp();
        if ((UInt64)oldPose == File::NonPose)
            return File::NonPose;
        
        mFileStream.seekp(0, std::ios::beg);
        auto prePose = mFileStream.tellp();
        if ((UInt64)prePose == File::NonPose)
            return File::NonPose;
        
        mFileStream.seekp(0, std::ios::end);
        auto postPose = mFileStream.tellp();
        if ((UInt64)postPose == File::NonPose)
            return File::NonPose;
        
        assert(prePose <= postPose);
        auto result = postPose - prePose;
        
        mFileStream.seekp(oldPose);
        if (mFileStream.tellp() == oldPose)
            return result;
        return File::NonPose;
    }
 
    bool SimpleFileImpl::TestBit(int bit) const noexcept
    {
        return (mOpenMode & bit) != 0;
    }

	void SimpleFileImpl::Reset()
	{
		mFileStream.seekg(0, std::ios::beg);
	}
}
#endif
