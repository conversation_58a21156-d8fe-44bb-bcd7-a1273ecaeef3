#pragma once
#include <memory>
#include <map>
#include <array>
#include <unordered_map>
#include <optional>

#include "Serialization/SerializeNode.h"
#include "CrossBase/String/UniqueString.h"


CREATE_MEMBER_FUNC_CHECK(Serialize)
CREATE_MEMBER_FUNC_CHECK(Deserialize)
CREATE_MEMBER_FUNC_CHECK(PostDeserialize)


namespace cross {
using Context = cross::SerializeContext;

template<typename T>
using has_serialize_context = Has_Serialize<T, SerializeNode(Context&)>;

template<typename T>
constexpr bool has_serialize_context_v = has_serialize_context<T>::value;

template<typename T>
using has_deserialize_context = Has_Deserialize<T, bool(const DeserializeNode&, Context&)>;

template<typename T>
constexpr bool has_deserialize_context_v = has_deserialize_context<T>::value;

template<typename T>
using has_post_deserialize_context = Has_PostDeserialize<T, bool(const DeserializeNode&, Context&)>;

template<typename T>
constexpr bool has_post_deserialize_context_v = has_post_deserialize_context<T>::value;


inline std::string_view SerializeKey(const std::string& key)
{
    return std::string_view(key);
}

inline std::string_view SerializeKey(const cross::UniqueString& key)
{
    return key.GetStringView();
}

#pragma region SerializeContext

template<typename T, std::enable_if_t<std::is_constructible_v<SerializeNode, T>, bool> = true>
void Serialize(const T& in, SerializeNode& out, Context& context)
{
    out = SerializeNode(in);
}

template<typename T, std::enable_if_t<!std::is_constructible_v<SerializeNode, T> && std::is_pointer_v<T>, bool> = true>
void Serialize(const T& in, SerializeNode& out, Context& context)
{
    if (in)
        Serialize(*in, out, context);
    else
        out = SerializeNode(nullptr);
}

template<typename T, std::enable_if_t<!std::is_constructible_v<SerializeNode, T> && !std::is_pointer_v<T> && has_serialize_context_v<T>, bool> = true>
void Serialize(const T& in, SerializeNode& out, Context& context)
{
    out = in.Serialize(context);
}

template<typename T, std::enable_if_t<!std::is_constructible_v<SerializeNode, T> && !std::is_pointer_v<T> && !has_serialize_context_v<T>, bool> = true>
void Serialize(const T& in, SerializeNode& out, Context& context)
{
    out = in.Serialize();
}

template<size_t StrLen>
void Serialize(const char (&str)[StrLen], SerializeNode& out, Context& context)
{
    out = SerializeNode(str);
}

template<typename T>
void Serialize(const std::vector<T>& in, SerializeNode& out, Context& context)
{
    out = SerializeNode::EmptyArray();
    for (const auto& v : in)
    {
        SerializeNode node;
        Serialize(v, node, context);
        out.PushBack(std::move(node));
    }
}
template<typename T, size_t N>
void Serialize(const std::array<T, N>& in, SerializeNode& out, Context& context)
{
    out = SerializeNode::EmptyArray();
    for (const auto& v : in)
    {
        SerializeNode node;
        Serialize(v, node, context);
        out.PushBack(std::move(node));
    }
}

template<typename T, size_t N>
void Serialize(const T in[N], SerializeNode& out, Context& context)
{
    out = SerializeNode::EmptyArray();
    for (const auto& v : in)
    {
        SerializeNode node;
        Serialize(v, node, context);
        out.PushBack(std::move(node));
    }
}

template<typename T>
void Serialize(const std::set<T>& in, SerializeNode& out, Context& context)
{
    out = SerializeNode::EmptyArray();
    for (const auto& v : in)
    {
        SerializeNode node;
        Serialize(v, node, context);
        out.PushBack(std::move(node));
    }
}

template<typename T>
void Serialize(const std::unordered_set<T>& in, SerializeNode& out, Context& context)
{
    out = SerializeNode::EmptyArray();
    for (const auto& v : in)
    {
        SerializeNode node;
        Serialize(v, node, context);
        out.PushBack(std::move(node));
    }
}

template<typename T>
void Serialize(const std::shared_ptr<T>& in, SerializeNode& out, Context& context)
{
    if (in)
        Serialize(*in, out, context);
    else
        out = SerializeNode(nullptr);
}

template<typename T>
void Serialize(const std::unique_ptr<T>& in, SerializeNode& out, Context& context)
{
    if (in)
        Serialize(*in, out, context);
    else
        out = SerializeNode(nullptr);
}

template<>
inline void Serialize(const cross::UniqueString& in, SerializeNode& out, Context& context)
{
    out = SerializeNode(in.GetStringView());
}

template<typename KeyType, typename ValueType>
void Serialize(const std::map<KeyType, ValueType>& in, SerializeNode& out, Context& context)
{
    out = SerializeNode::EmptyObject();
    for (const auto& [key, val] : in)
    {
        SerializeNode node;
        Serialize(val, node, context);
        out[SerializeKey(key)] = std::move(node);
    }
}

template<typename KeyType, typename ValueType>
void Serialize(const std::unordered_map<KeyType, ValueType>& in, SerializeNode& out, Context& context)
{
    out = SerializeNode::EmptyObject();
    for (const auto& [key, val] : in)
    {
        SerializeNode node;
        Serialize(val, node, context);
        out[SerializeKey(key)] = std::move(node);
    }
}
#pragma endregion
/// ---------------------------------------------

#pragma region DeserializeContext

template<typename T, std::enable_if_t<std::is_constructible_v<SerializeNode, T>, bool> = true>
bool Deserialize(const DeserializeNode& in, T& out, Context& context)
{
    out = std::move(in.As<T>());
    return true;
}

template<typename T, std::enable_if_t<!std::is_pointer_v<T> && !std::is_constructible_v<SerializeNode, T> && has_deserialize_context_v<T>, bool> = true>
bool Deserialize(const DeserializeNode& in, T& out, Context& context)
{
    if constexpr (std::is_same_v<decltype(out.Deserialize(in, context)), void>)
    {
        out.Deserialize(in, context);
        return true;
    }
    else
    {
        return out.Deserialize(in, context);
    }
}

template<typename T, std::enable_if_t<!std::is_pointer_v<T> && !std::is_constructible_v<SerializeNode, T> && !has_deserialize_context_v<T>, bool> = true>
bool Deserialize(const DeserializeNode& in, T& out, Context& context)
{
    if constexpr (std::is_same_v<decltype(out.Deserialize(in)), void>)
    {
        out.Deserialize(in);
        return true;
    }
    else
    {
        return out.Deserialize(in);
    }
}
template<typename T, std::enable_if_t<std::is_pointer_v<T> && !std::is_constructible_v<SerializeNode, T>, bool> = true>
bool Deserialize(const DeserializeNode& in, T& out, Context& context)
{
    if (!in.IsNull())
    {
        if (out == nullptr)
            out = new T();
        return Deserialize(in, *out, context);
    }
    return true;
}

template<typename T>
bool Deserialize(const DeserializeNode& in, std::vector<T>& out, Context& context)
{
    bool ret = true;
    out.clear();
    if (in.IsArray() && in.Size() > 0)
    {
        out.resize(in.Size());
        for (size_t i = 0; i < in.Size(); i++)
        {
            ret &= Deserialize(in[i], out[i], context);
        }
    }
    else if (!in.IsArray())
    {
        out.resize(1);
        ret &= Deserialize(in, out[0], context);
    }

    return ret;
}

template<typename T, size_t N>
bool Deserialize(const DeserializeNode& in, std::array<T, N>& out, Context& context)
{
    bool ret = true;
    if (in.IsArray() && in.Size() > 0)
    {
        for (size_t i = 0; i < std::min(in.Size(), out.max_size()); i++)
        {
            ret &= Deserialize(in[i], out[i], context);
        }
    }
    return ret;
}
template<typename T, size_t N>
bool Deserialize(const DeserializeNode& in, T out[N], Context& context)
{
    bool ret = true;
    if (in.IsArray() && in.Size() > 0)
    {
        for (size_t i = 0; i < std::min(in.Size(), N); i++)
        {
            ret &= Deserialize(in[i], out[i], context);
        }
    }
    return ret;
}
template<typename T>
bool Deserialize(const DeserializeNode& in, std::set<T>& out, Context& context)
{
    bool ret = true;
    out.clear();
    if (in.IsArray() && in.Size() > 0)
    {
        for (size_t i = 0; i < in.Size(); i++)
        {
            T tmp;
            ret &= Deserialize(in[i], tmp, context);
            out.insert(std::move(tmp));
        }
    }
    return ret;
}

template<typename T>
bool Deserialize(const DeserializeNode& in, std::unordered_set<T>& out, Context& context)
{
    bool ret = true;
    out.clear();
    if (in.IsArray() && in.Size() > 0)
    {
        for (size_t i = 0; i < in.Size(); i++)
        {
            T tmp;
            ret &= Deserialize(in[i], tmp, context);
            out.insert(std::move(tmp));
        }
    }
    return ret;
}

template<typename KeyType, typename ValueType>
bool Deserialize(const DeserializeNode& in, std::map<KeyType, ValueType>& out, Context& context)
{
    bool ret = true;
    out.clear();
    for (const auto& [key, val] : in)
    {
        ret &= Deserialize(val, out[KeyType(key.data(), static_cast<UInt32>(key.size()))], context);
    }
    return ret;
}

template<typename KeyType, typename ValueType>
bool Deserialize(const DeserializeNode& in, std::unordered_map<KeyType, ValueType>& out, Context& context)
{
    bool ret = true;
    out.clear();
    for (const auto& [key, val] : in)
    {
        ret &= Deserialize(val, out[KeyType(key.data(), static_cast<UInt32>(key.size()))], context);
    }
    return ret;
}

template<typename T>
bool Deserialize(const DeserializeNode& in, std::shared_ptr<T>& out, Context& context)
{
    if (!in.IsNull())
    {
        out = std::make_shared<T>();
        return Deserialize(in, *out, context);
    }
    return true;
}

template<typename T>
bool Deserialize(const DeserializeNode& in, std::unique_ptr<T>& out, Context& context)
{
    if (!in.IsNull())
    {
        out = std::make_unique<T>();
        return Deserialize(in, *out, context);
    }
    return true;
}

template<>
inline bool Deserialize(const DeserializeNode& in, cross::UniqueString& out, Context& context)
{
    auto str_view = in.AsStringView();
    out = std::move(cross::UniqueString(str_view.data(), (UInt32)str_view.length()));
    return true;
}
#pragma endregion


#pragma region PostDeserializeContext
template<typename T, std::enable_if_t<has_post_deserialize_context_v<T>, bool> = true>
bool PostDeserialize(const DeserializeNode& in, T& out, Context& context)
{
    return out.PostDeserialize(in, context);
}

template<typename T, std::enable_if_t<std::is_pointer_v<T>, bool> = true>
bool PostDeserialize(const DeserializeNode& in, T& out, Context& context)
{
    if (!in.IsNull())
    {
        if (out == nullptr)
            out = new T();
        return PostDeserialize(in, *out, context);
    }
    return true;
}

template<typename T, std::enable_if_t<!has_post_deserialize_context_v<T> && !std::is_pointer_v<T>, bool> = true>
bool PostDeserialize(const DeserializeNode& in, T& out, Context& context)
{
    return true;
}

template<typename T>
bool PostDeserialize(const DeserializeNode& in, std::vector<T>& out, Context& context)
{
    bool ret = true;
    out.clear();
    if (in.IsArray() && in.Size() > 0)
    {
        out.resize(in.Size());
        for (size_t i = 0; i < in.Size(); i++)
        {
            ret &= PostDeserialize(in[i], out[i], context);
        }
    }
    return ret;
}

template<typename T>
bool PostDeserialize(const DeserializeNode& in, std::set<T>& out, Context& context)
{
    bool ret = true;
    out.clear();
    if (in.IsArray() && in.Size() > 0)
    {
        for (size_t i = 0; i < in.Size(); i++)
        {
            T tmp;
            ret &= PostDeserialize(in[i], tmp, context);
            out.insert(std::move(tmp));
        }
    }
    return ret;
}

template<typename T>
bool PostDeserialize(const DeserializeNode& in, std::unordered_set<T>& out, Context& context)
{
    bool ret = true;
    out.clear();
    if (in.IsArray() && in.Size() > 0)
    {
        for (size_t i = 0; i < in.Size(); i++)
        {
            T tmp;
            ret &= PostDeserialize(in[i], tmp, context);
            out.insert(std::move(tmp));
        }
    }
    return ret;
}

template<typename KeyType, typename ValueType>
bool PostDeserialize(const DeserializeNode& in, std::map<KeyType, ValueType>& out, Context& context)
{
    bool ret = true;
    out.clear();
    for (const auto& [key, val] : in)
    {
        ret &= PostDeserialize(val, out[KeyType(key.data(), static_cast<UInt32>(key.size()))], context);
    }
    return ret;
}

template<typename KeyType, typename ValueType>
bool PostDeserialize(const DeserializeNode& in, std::unordered_map<KeyType, ValueType>& out, Context& context)
{
    bool ret = true;
    out.clear();
    for (const auto& [key, val] : in)
    {
        ret &= PostDeserialize(val, out[KeyType(key.data(), static_cast<UInt32>(key.size()))], context);
    }
    return ret;
}

template<typename T>
bool PostDeserialize(const DeserializeNode& in, std::shared_ptr<T>& out, Context& context)
{
    if (!in.IsNull())
    {
        out = std::make_shared<T>();
        return PostDeserialize(in, *out, context);
    }
    return true;
}

template<typename T>
bool PostDeserialize(const DeserializeNode& in, std::unique_ptr<T>& out, Context& context)
{
    if (!in.IsNull())
    {
        out = std::make_unique<T>();
        return PostDeserialize(in, *out, context);
    }
    return true;
}

#pragma endregion
}   // namespace cross