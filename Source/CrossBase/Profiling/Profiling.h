#pragma once

#include "CrossBaseForward.h"


constexpr UInt32 sFrameProTypeColors[17] = {
    0x7B7B7B,
    0x3588A7,
    0x7B9E06,
    0x624A86,
    0xCC7100,
    0xFFF766,
    0x94D8F6,
    0xC785C8,
    0x085820,
    0xFFF766,
    0xFF7F27,
    0x666620,
    0x72680D,
    0xC6A206,
    0x595959,
    0xF47378,
    0xFFA500,
};

// FNV-1a constants
namespace CompileHash {
static constexpr unsigned long long basis = 14695981039346656037ULL;
static constexpr unsigned long long prime = 1099511628211ULL;

// compile-time hash helper function
constexpr unsigned long long hash_one(char c, const char* remain, unsigned long long value)
{
    return c == 0 ? value : hash_one(remain[0], remain + 1, (value ^ c) * prime);
}

// compile-time hash
constexpr unsigned long long hashcompiletime(const char* str)
{
    return hash_one(str[0], str + 1, basis);
}
}   // namespace CompileHash
#define CROSS_PROFILER_TOKEN_PASTE_INTERNAL(T0, T1) T0##T1
#define CROSS_PROFILER_TOKEN_PASTE(T0, T1)          CROSS_PROFILER_TOKEN_PASTE_INTERNAL(T0, T1)
#define CROSS_PROFILER_TOKEN_LSTR(Str)              L##Str
#if ENABLE_CPU_PROFILING_MP
#define DECLARE_CPU_TIMING_GROUP(Group)                                                                                                                                                                                                    \
        struct CPU_TIMING_GROUP_##Group                                                                                                                                                                                                        \
        {                                                                                                                                                                                                                                      \
            static constexpr auto GetGroupA()                                                                                                                                                                                                  \
            {                                                                                                                                                                                                                                  \
                return #Group;                                                                                                                                                                                                                 \
            }                                                                                                                                                                                                                                  \
            static constexpr auto GetGroupW()                                                                                                                                                                                                  \
            {                                                                                                                                                                                                                                  \
                return CROSS_PROFILER_TOKEN_LSTR(#Group);                                                                                                                                                                                      \
            }                                                                                                                                                                                                                                  \
        };

#define SCOPED_CPU_TIMING_INTERNAL(Group, Name)                                                                                                                                                                                            \
        static UInt64 CROSS_PROFILER_TOKEN_PASTE(CPU_TIMING_TOKEN, __LINE__) = cross::profiling::ScopedCPUTiming::GetToken(CPU_TIMING_GROUP_##Group::GetGroupA(), Name);                                                                       \
        cross::profiling::ScopedCPUTiming CROSS_PROFILER_TOKEN_PASTE(CPU_TIMING_VAR, __LINE__)(CROSS_PROFILER_TOKEN_PASTE(CPU_TIMING_TOKEN, __LINE__))
#define SCOPED_CPU_TIMING(Group, Name) SCOPED_CPU_TIMING_INTERNAL(Group, Name)

DECLARE_CPU_TIMING_GROUP(GroupDefault);
#define QUICK_SCOPED_CPU_TIMING(Name) SCOPED_CPU_TIMING(GroupDefault, Name)

#define DECLARE_THREAD_NAME(Name)

#elif CROSSENGINE_ANDROID || CROSSENGINE_WIN || CROSSENGINE_LINUX
#define DECLARE_CPU_TIMING_GROUP(Group)
#define MACROCONCAT(a, b)    CONCAT_INNER(a, b)
#define CONCAT_INNER(a, b)   a##b
#define UNIQUEVARNAME(base) MACROCONCAT(base, __COUNTER__)
#ifndef _MANAGED
#ifdef USE_PROFILERSTACKTRACEANDMEMORY
constexpr size_t stacktrace = 3;
constexpr size_t memstacktrace = 9;
#define SCOPED_CPU_TIMING(Group, Name) ZoneNamedNCS(MACROCONCAT(Group, __LINE__), Name, sFrameProTypeColors[CompileHash::hashcompiletime(#Group) % 17], stacktrace, true);
#define QUICK_SCOPED_CPU_TIMING(NAME)  ZoneNamedNCS(MACROCONCAT(GroupDynamic, __LINE__), NAME, sFrameProTypeColors[CompileHash::hashcompiletime(#NAME) % 17], stacktrace, true);
#else
#define SCOPED_CPU_TIMING(Group, Name)                                                                                                                                                                                                     \
        ZoneNamedNC(MACROCONCAT(Group, __LINE__), Name, sFrameProTypeColors[CompileHash::hashcompiletime(#Group) % 17], true);                    
#define QUICK_SCOPED_CPU_TIMING(NAME) ZoneNamedNC(MACROCONCAT(GroupDynamic, __LINE__), NAME, sFrameProTypeColors[CompileHash::hashcompiletime(#NAME) % 17], true);
#endif
// note the macro will cache unique name string
// so be careful with name that changes every frame
#define SCOPED_CPU_TIMING_DYNAMIC(Group, Name)              \
        ZoneNamedNC(MACROCONCAT(Group, __LINE__), __FUNCTION__, sFrameProTypeColors[CompileHash::hashcompiletime(#Group) % 17], true);\
        TracyMessage(Name.c_str(), Name.size());

#define QUICK_SCOPED_CPU_TIMING_DYNAMIC(Name)                                                                                                                              \
        ZoneNamedNC(MACROCONCAT(GroupDynamic, __LINE__), __FUNCTION__, sFrameProTypeColors[__LINE__ % 17], true);                                                               \
        TracyMessage(Name.c_str(), Name.size());

#define DECLARE_THREAD_NAME(Name) tracy::SetThreadName(Name);
#else
#define DECLARE_CPU_TIMING_GROUP(Group)
#define SCOPED_CPU_TIMING(Group, Name)
#define QUICK_SCOPED_CPU_TIMING(NAME)
#define SCOPED_CPU_TIMING_DYNAMIC(Group, NAME)
#define DECLARE_THREAD_NAME(Name)
#endif
#else
#define DECLARE_CPU_TIMING_GROUP(Group)
#define SCOPED_CPU_TIMING(Group, Name)
#define QUICK_SCOPED_CPU_TIMING(NAME)
#define SCOPED_CPU_TIMING_DYNAMIC(Group, NAME)
#define DECLARE_THREAD_NAME(Name)
#endif

namespace cross::profiling {
CROSS_BASE_API void Startup();

CROSS_BASE_API void Shutdown();

CROSS_BASE_API void FlipFrame();

CROSS_BASE_API void OnThreadCreation(const char* threadName);

CROSS_BASE_API void OnThreadExit();
class ScopedCPUTiming
{
public:
    CROSS_BASE_API static UInt64 GetToken(const char* group, const char* name);
    CROSS_BASE_API static void Enter(UInt64 token);
    CROSS_BASE_API static void Leave();
    ScopedCPUTiming(UInt64 token)
    {
        Enter(token);
    }

    ~ScopedCPUTiming()
    {
        Leave();
    }

    ScopedCPUTiming(const ScopedCPUTiming&) = delete;
    ScopedCPUTiming& operator=(const ScopedCPUTiming&) = delete;
    ScopedCPUTiming(ScopedCPUTiming&&) = delete;
    ScopedCPUTiming& operator=(ScopedCPUTiming&&) = delete;
};
}   // namespace cross::profiling