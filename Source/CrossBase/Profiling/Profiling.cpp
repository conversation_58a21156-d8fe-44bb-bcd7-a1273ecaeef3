#include "PCH/CrossBasePCHPrivate.h"
#ifndef _MANAGED
#include "Profiling.h"
#include "ThirdParty/microprofile.h"

namespace cross::profiling {
void Startup()
{
#if ENABLE_CPU_PROFILING_MP
    MicroProfileSetEnableAllGroups(1);
    MicroProfileStartContextSwitchTrace();
    OnThreadCreation("Game Thread");
#endif
}

void Shutdown()
{
#if ENABLE_CPU_PROFILING_MP
    OnThreadExit();
    MicroProfileShutdown();
#endif
}

void FlipFrame()
{
#if ENABLE_CPU_PROFILING_MP
    MicroProfileFlip(nullptr);
#endif
}

void OnThreadCreation(const char* threadName)
{
#if ENABLE_CPU_PROFILING_MP
    MicroProfileOnThreadCreate(threadName);
#endif
}

void OnThreadExit()
{
#if ENABLE_CPU_PROFILING_MP
    MicroProfileOnThreadExit();
#endif
}

UInt64 ScopedCPUTiming::GetToken(const char* group, const char* name)
{
#if ENABLE_CPU_PROFILING_MP
#if MICROPROFILE_ENABLED
    return MicroProfileGetToken(group, name, MP_AUTO, MicroProfileTokenTypeCpu);
#else
    return 0;
#endif
#else
    return 0;
#endif
}

void ScopedCPUTiming::Enter(UInt64 token)
{
#if ENABLE_CPU_PROFILING_MP
#if MICROPROFILE_ENABLED
    MicroProfileEnter(token);
#endif
#endif
}

void ScopedCPUTiming::Leave()
{
#if ENABLE_CPU_PROFILING_MP
#if MICROPROFILE_ENABLED
    MicroProfileLeave();
#endif
#endif
}
}   // namespace cross::profiling
#endif