#pragma once

namespace cross
{
    template <typename T>
    class TFutureBase
    {
    public:
        using SharedState = TSharedState<T>;
        using StatePtr = std::shared_ptr<SharedState>;
        using StorageType = typename SharedState::StorageType;

        template <typename Impl>
        friend class TPromiseContext;

        template <typename Callable>
        using TIsContinuable = std::disjunction<
            std::is_invocable<Callable>,
            std::is_invocable<Callable, StorageType>
        >;

    protected:
        TFutureBase() = default;
        ~TFutureBase() = default;
        explicit TFutureBase(StatePtr statePtr) : mState(std::move(statePtr)) {}
        TFutureBase(TFutureBase const&) = default;
        TFutureBase& operator=(TFutureBase const&) = default;
        TFutureBase(TFutureBase&&) = default;
        TFutureBase& operator=(TFutureBase&&) = default;

    public:
        bool IsValid() const noexcept
        {
            return mState != nullptr;
        }

        bool IsReady() const
        {
            return IsValid() && mState->IsReady();
        }

        void Wait()
        {
            if(IsValid())
            {
                mState->Wait();
            }
        }

        template <CROSS_REQUIRE_IF((Duration), TIsDuration<Duration>)>
        bool WaitFor(Duration const& duration)
        {
            return IsValid() && mState->WaitFor(duration);
        }

        template <CROSS_REQUIRE_IF((TimePoint), TIsTimePoint<TimePoint>)>
        bool WaitUntil(TimePoint const& timePoint)
        {
            return IsValid() && mState->WaitUntil(timePoint);
        }

        template <CROSS_REQUIRE_IF((Func), TIsContinuable<Func>)>
        auto Then(Func&& func)
        {
            return Then(launch::Post, std::forward<Func>(func));
        }

        template <CROSS_REQUIRE_IF((Launch, Func), TIsLaunchPolicy<Launch>, TIsContinuable<Func>)>
        auto Then(Launch policy, Func&& continuation)
        {
            static LaunchThreadExecutor executor{};
            return Then(policy, executor, std::forward<Func>(continuation));
        }

        template <CROSS_REQUIRE_IF((Executor, Func), TIsExecutorOf<Executor, void()>, TIsContinuable<Func>)>
        auto Then(Executor& executor, Func&& continuation)
        {
            return Then(launch::Post, executor, std::forward<Func>(continuation));
        }

        template <CROSS_REQUIRE_IF((Launch, Executor, Func), TIsLaunchPolicy<Launch>, TIsExecutorOf<Executor, void()>, TIsContinuable<Func>)>
        auto Then(Launch policy, Executor& executor, Func&& continuation);

    protected:
        StatePtr mState;
    };
    
    template <typename T>
    class TFuture final : public TFutureBase<T>
    {
    public:
        using BaseType = TFutureBase<T>;
        using SharedState = typename BaseType::SharedState;
        using StatePtr = typename BaseType::StatePtr;
        using Reference = typename SharedState::Reference;

    public:
        TFuture() = default;
        ~TFuture() = default;
        explicit TFuture(StatePtr statePtr) : BaseType(std::move(statePtr)) {}
        TFuture(TFuture const&) = default;
        TFuture& operator=(TFuture const&) = default;
        TFuture(TFuture&&) = default;
        TFuture& operator=(TFuture&&) = default;
        
    public:
        Reference Get()
        {
            if(!this->IsValid())
            {
                LOG_ERROR("{}", "No State");
                throw std::runtime_error{ "No State" };
            }
            return this->mState->GetValue();
        }
    };

    template <>
    class TFuture<void> final : public TFutureBase<void>
    {
    public:
        using BaseType = TFutureBase<void>;
        using SharedState = BaseType::SharedState;
        using StatePtr = BaseType::StatePtr;

    public:
        TFuture() = default;
        ~TFuture() = default;
        explicit TFuture(StatePtr statePtr) : BaseType(std::move(statePtr)) {}
        TFuture(TFuture const&) = default;
        TFuture& operator=(TFuture const&) = default;
        TFuture(TFuture&&) = default;
        TFuture& operator=(TFuture&&) = default;
    };
}
