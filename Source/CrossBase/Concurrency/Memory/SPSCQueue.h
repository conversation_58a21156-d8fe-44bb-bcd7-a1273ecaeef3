#pragma once
/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

 // <AUTHOR> (<EMAIL>)
 // <AUTHOR> (<EMAIL>)

 /// modify from folly

#include "CrossBaseForward.h"
#include "memoryhooker/Module.h"
#pragma warning(disable: 4324)

namespace cross
{
    template <class T>
    class SPSCQueue
    {
        // todo ... select from archetecture
        static constexpr SizeType HardwareDestructiveInterferenceSize = 64;

	public:
        using ValueType = T;
        using Pointer = T*;
        using ConstPointer = T * const;

        SPSCQueue(const SPSCQueue&) = delete;
        SPSCQueue& operator=(const SPSCQueue&) = delete;

        // size must be >= 2.
        //
        // Also, note that the number of usable slots in the queue at any
        // given time is actually (size-1), so if you start with an empty queue,
        // isFull() will return true after size-1 insertions.
        explicit  SPSCQueue(UInt32 size)
            : mSize(size)
            , mRecords(static_cast<T*>(cross::Memory::Malloc(sizeof(T) * size)))
            , mReadIndex(0)
            , mWriteIndex(0)
        {
            Assert(size >= 2);
            if (!mRecords)
            {
                throw std::bad_alloc{};
            }
        }

        ~SPSCQueue() 
        {
            // We need to destruct anything that may still exist in our queue.
            // (No real synchronization needed at destructor time: only one
            // thread can be doing this.)
            if constexpr(std::negation_v<std::is_trivially_destructible<T>>)
            {
                size_t readIndex = mReadIndex;
                size_t endIndex = mWriteIndex;
                while (readIndex != endIndex)
                {
                    mRecords[readIndex].~T();
                    if (++readIndex == mSize)
                    {
                        readIndex = 0;
                    }
                }
            }
            cross::Memory::Free(mRecords);
		}

        template <class... Args>
        bool Write(Args&&... recordArgs)
        {
            auto const currentWrite = mWriteIndex.load(std::memory_order_relaxed);
            auto nextRecord = currentWrite + 1;
            if (nextRecord == mSize)
            {
                nextRecord = 0;
            }

            if (nextRecord != mReadIndex.load(std::memory_order_acquire))
            {
                new (&mRecords[currentWrite]) T{ std::forward<Args>(recordArgs)... };
                mWriteIndex.store(nextRecord, std::memory_order_release);
                return true;
            }

            // queue is full
            return false;
        }

		bool Write(const T& record)
		{
			auto const currentWrite = mWriteIndex.load(std::memory_order_relaxed);
			auto nextRecord = currentWrite + 1;
			if (nextRecord == mSize)
			{
				nextRecord = 0;
			}

			if (nextRecord != mReadIndex.load(std::memory_order_acquire))
			{
				new (&mRecords[currentWrite]) T{};
				mRecords[currentWrite] = record;
				mWriteIndex.store(nextRecord, std::memory_order_release);
				return true;
			}

			// queue is full
			return false;
		}
        // move (or copy) the value at the front of the queue to given variable
        bool Read(T& record)
        {
            auto const currentRead = mReadIndex.load(std::memory_order_relaxed);
            if (currentRead == mWriteIndex.load(std::memory_order_acquire))
            {
                // queue is empty
                return false;
            }

            auto nextRecord = currentRead + 1;
            if (nextRecord == mSize)
            {
                nextRecord = 0;
            }

            record = std::move(mRecords[currentRead]);
            mRecords[currentRead].~T();                                 // someone implement move constructor(or assignment), but not 100% guarantee free memory
            mReadIndex.store(nextRecord, std::memory_order_release);
            return true;
        }

        // pointer to the value at the front of the queue (for use in-place) or
        // nullptr if empty.
        T* FrontPtr()
        {
            auto const currentRead = mReadIndex.load(std::memory_order_relaxed);
            if (currentRead == mWriteIndex.load(std::memory_order_acquire))
            {
                // queue is empty
                return nullptr;
            }
            return &mRecords[currentRead];
        }

        // queue must not be empty
        void PopFront()
        {
            auto const currentRead = mReadIndex.load(std::memory_order_relaxed);
            Assert(currentRead != mWriteIndex.load(std::memory_order_acquire));

            auto nextRecord = currentRead + 1;
            if (nextRecord == mSize)
            {
                nextRecord = 0;
            }

            mRecords[currentRead].~T();
            mReadIndex.store(nextRecord, std::memory_order_release);
        }

        bool IsEmpty() const
        {
            return mReadIndex.load(std::memory_order_acquire) ==
                mWriteIndex.load(std::memory_order_acquire);
        }

        bool IsFull() const
        {
            auto nextRecord = mWriteIndex.load(std::memory_order_acquire) + 1;
            if (nextRecord == mSize)
            {
                nextRecord = 0;
            }

            if (nextRecord != mReadIndex.load(std::memory_order_acquire))
            {
                return false;
            }
            // queue is full
            return true;
        }

        // * If called by consumer, then true size may be more (because producer may
        //   be adding items concurrently).
        // * If called by producer, then true size may be less (because consumer may
        //   be removing items concurrently).
        // * It is undefined to call this from any other thread.
        SizeType SizeGuess() const
        {
            int ret = mWriteIndex.load(std::memory_order_acquire) -
                mReadIndex.load(std::memory_order_acquire);
            if (ret < 0)
            {
                ret += mSize;
            }
            return ret;
        }

        // maximum number of items in the queue.
        SizeType Capacity() const noexcept
        {
            return mSize - 1;
        }

    private:
        using AtomicIndex = std::atomic<unsigned int>;

        // cache line padding
        char mPad0[HardwareDestructiveInterferenceSize];

        // array size
        UInt32 const    mSize;
        // array content
        ConstPointer    mRecords;

        // read write cursor
        alignas(HardwareDestructiveInterferenceSize) AtomicIndex mReadIndex;
        alignas(HardwareDestructiveInterferenceSize) AtomicIndex mWriteIndex;

        // cache line padding
        char mPad1[HardwareDestructiveInterferenceSize - sizeof(AtomicIndex)];
    };
}