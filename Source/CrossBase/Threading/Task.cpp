#include "PCH/CrossBasePCHPrivate.h"
#include "Task.h"
#include "memoryhooker/Module.h"
namespace cross::threading
{
    namespace memory
    {
        class MemoryBackingStore
        {
        public:
            MemoryBackingStore(std::uint64_t nodeSize, std::uint64_t nodeAlignment, std::uint64_t blockSize)
                : mNodeSize(nodeSize), mNodeAlignment(nodeAlignment), mBlockSize(blockSize), mStateBlocks(nullptr), mFreeStateBlocks(nullptr), mFreeMemoryBlocks(nullptr)
            {
                Assert(sizeof(std::byte*) * 2U <= nodeSize && nodeAlignment <= nodeSize);
            }

            ~MemoryBackingStore()
            {
                while (mStateBlocks)
                {
                    auto next = mStateBlocks->mNext;
                    DeallocateAligned(mStateBlocks->mBlocks);
                    mStateBlocks = next;
                }
            }

            MemoryBackingStore(const MemoryBackingStore&) = delete;
            MemoryBackingStore& operator=(const MemoryBackingStore&) = delete;

            std::byte* AllocateBlock()
            {
                {
                    std::scoped_lock lock(mMutex);
                    if (mFreeMemoryBlocks)
                    {
                        return UnconditionalAllocateMemoryBlock();
                    }
                }

                const auto count = mBlockSize + 2U;
                const auto address = reinterpret_cast<std::byte*>(AllocateAligned(mNodeSize * count, mNodeAlignment));
                const auto memory = address - sizeof(std::byte*);
                const auto block = new (memory) StateNode{ memory + mNodeSize * 2U, nullptr };
                const auto stub = new (memory + mNodeSize) StateNode{ address, nullptr };

                const auto lastOffset = (mBlockSize - 1U) * mNodeSize;
                for (std::uint64_t offset = 0U; offset != lastOffset;)
                {
                    const auto next = reinterpret_cast<std::byte**>(block->mBlocks + offset);
                    *next = reinterpret_cast<std::byte*>(block->mBlocks + (offset += mNodeSize));
                }
                *reinterpret_cast<std::byte**>(block->mBlocks + lastOffset) = nullptr;

                {
                    std::scoped_lock lock(mMutex);

                    block->mNext = mFreeMemoryBlocks;
                    mFreeMemoryBlocks = block;
                    stub->mNext = mStateBlocks;
                    mStateBlocks = stub;

                    return UnconditionalAllocateMemoryBlock();
                }
            }

            void DeallocateBlock(std::byte* address)
            {
                std::scoped_lock lock(mMutex);

                auto block = mFreeStateBlocks;
                mFreeStateBlocks = block->mNext;

                block->mBlocks = address;
                block->mNext = mFreeMemoryBlocks;
                mFreeMemoryBlocks = block;
            }

        private:
            struct StateNode
            {
                std::byte* mBlocks;
                StateNode* mNext;
            };

            std::byte* UnconditionalAllocateMemoryBlock()
            {
                auto block = mFreeMemoryBlocks;
                mFreeMemoryBlocks = block->mNext;

                block->mNext = mFreeStateBlocks;
                mFreeStateBlocks = block;

                return block->mBlocks;
            }

            void* AllocateAligned(std::uint64_t size, std::uint64_t alignment)
            {
                const auto offset = alignment - 1U;
                auto address = cross::Memory::Malloc(size + offset + sizeof(void*));
                auto aligned = (reinterpret_cast<std::uintptr_t>(address) + sizeof(void*) * 2U + offset) & ~offset;
                *reinterpret_cast<void**>(aligned - sizeof(void*) * 2U) = address;
                return reinterpret_cast<void*>(aligned);
            }

            void DeallocateAligned(void* address)
            { 
                cross::Memory::Free(*reinterpret_cast<void**>(reinterpret_cast<std::uintptr_t>(address) - sizeof(void*) * 2U));
            }

            std::uint64_t mNodeSize;
            std::uint64_t mNodeAlignment;
            std::uint64_t mBlockSize;

            StateNode* mStateBlocks;
            StateNode* mFreeStateBlocks;
            StateNode* mFreeMemoryBlocks;

            mutable std::mutex mMutex;
        };

        class ThreadMemoryPool
        {
        public:
            ThreadMemoryPool(std::uint64_t nodeSize, std::uint64_t nodeAlignment, std::uint64_t blockSize, MemoryBackingStore* backingStore)
                : mNodeSize(nodeSize), mNodeAlignment(nodeAlignment), mBlockSize(blockSize), mAllocationCount(0), mFreeMemoryNodes(nullptr), mOverFreedMemoryNodes(nullptr), mNumOverFreedMemoryNodes(0U), mBackingStore(backingStore)
            {
            }

            ThreadMemoryPool(const ThreadMemoryPool&) = delete;
            ThreadMemoryPool& operator=(const ThreadMemoryPool&) = delete;

            void* Allocate()
            {
                mAllocationCount++;

                if (!mFreeMemoryNodes) [[unlikely]]
                {
                    mFreeMemoryNodes = mBackingStore->AllocateBlock();
                }

                auto node = mFreeMemoryNodes + sizeof(std::byte*);
                mFreeMemoryNodes = *reinterpret_cast<std::byte**>(mFreeMemoryNodes);

                return node;
            }

            void Deallocate(void* address)
            {
                auto node = reinterpret_cast<std::byte*>(address) - sizeof(std::byte*);
                if (--mAllocationCount < 0)
                {
                    mNumOverFreedMemoryNodes++;
                    *reinterpret_cast<std::byte**>(node) = reinterpret_cast<std::byte*>(mOverFreedMemoryNodes);
                    mOverFreedMemoryNodes = node;
                    if (mBlockSize <= mNumOverFreedMemoryNodes) [[unlikely]]
                    {
                        mBackingStore->DeallocateBlock(mOverFreedMemoryNodes);
                        mOverFreedMemoryNodes = nullptr;
                        mNumOverFreedMemoryNodes = 0U;
                    }
                }
                else
                {
                    *reinterpret_cast<std::byte**>(node) = reinterpret_cast<std::byte*>(mFreeMemoryNodes);
                    mFreeMemoryNodes = node;
                }
            }

        private:
            std::uint64_t mNodeSize;
            std::uint64_t mNodeAlignment;
            std::uint64_t mBlockSize;

            std::int64_t mAllocationCount;

            std::byte* mFreeMemoryNodes;

            std::byte* mOverFreedMemoryNodes;
            std::uint64_t mNumOverFreedMemoryNodes;

            MemoryBackingStore* mBackingStore;
        };

        class MemoryPool
        {
        public:
            void* Allocate(std::uint64_t size)
            {
                if (auto chunk = GetChunk(size)) [[likely]]
                {
                    return chunk->Allocate();
                }
                else
                {
                    return ::operator new(size, static_cast<std::align_val_t>(Alignment));
                }
            }

            void Deallocate(void* address, std::uint64_t size)
            {
                if (auto chunk = GetChunk(size)) [[likely]]
                {
                    return chunk->Deallocate(address);
                }
                else
                {
                    return ::operator delete(address, static_cast<std::align_val_t>(Alignment));
                }
            }

        private:
            std::uint64_t GetChunkIndex(std::uint64_t size) const
            {
                return std::max(static_cast<std::uint64_t>(std::numeric_limits<std::uint64_t>::digits - std::countl_zero(size + sizeof(std::byte*) - 1U)), BaseIndex);
            }

            ThreadMemoryPool* GetChunk(std::uint64_t size)
            {
                const auto index = GetChunkIndex(size);
                return index < NumChunks + BaseIndex ? &chunks[index - BaseIndex] : nullptr;
            }

            constexpr static std::uint64_t NumChunks = 10U;
            constexpr static std::uint64_t BaseIndex = 6U;
            constexpr static std::uint64_t Alignment = 64U;
            constexpr static std::uint64_t BlockSize = 1024U;

            static inline std::array<MemoryBackingStore, NumChunks> backingStore
            {
                MemoryBackingStore(1U << (BaseIndex + 0U), Alignment, BlockSize),
                MemoryBackingStore(1U << (BaseIndex + 1U), Alignment, BlockSize),
                MemoryBackingStore(1U << (BaseIndex + 2U), Alignment, BlockSize),
                MemoryBackingStore(1U << (BaseIndex + 3U), Alignment, BlockSize),
                MemoryBackingStore(1U << (BaseIndex + 4U), Alignment, BlockSize),
                MemoryBackingStore(1U << (BaseIndex + 5U), Alignment, BlockSize),
                MemoryBackingStore(1U << (BaseIndex + 6U), Alignment, BlockSize),
                MemoryBackingStore(1U << (BaseIndex + 7U), Alignment, BlockSize),
                MemoryBackingStore(1U << (BaseIndex + 8U), Alignment, BlockSize),
                MemoryBackingStore(1U << (BaseIndex + 9U), Alignment, BlockSize)
            };

            static thread_local inline std::array<ThreadMemoryPool, NumChunks> chunks
            {
                ThreadMemoryPool(1U << (BaseIndex + 0U), Alignment, BlockSize, &backingStore[0U]),
                ThreadMemoryPool(1U << (BaseIndex + 1U), Alignment, BlockSize, &backingStore[1U]),
                ThreadMemoryPool(1U << (BaseIndex + 2U), Alignment, BlockSize, &backingStore[2U]),
                ThreadMemoryPool(1U << (BaseIndex + 3U), Alignment, BlockSize, &backingStore[3U]),
                ThreadMemoryPool(1U << (BaseIndex + 4U), Alignment, BlockSize, &backingStore[4U]),
                ThreadMemoryPool(1U << (BaseIndex + 5U), Alignment, BlockSize, &backingStore[5U]),
                ThreadMemoryPool(1U << (BaseIndex + 6U), Alignment, BlockSize, &backingStore[6U]),
                ThreadMemoryPool(1U << (BaseIndex + 7U), Alignment, BlockSize, &backingStore[7U]),
                ThreadMemoryPool(1U << (BaseIndex + 8U), Alignment, BlockSize, &backingStore[8U]),
                ThreadMemoryPool(1U << (BaseIndex + 9U), Alignment, BlockSize, &backingStore[9U])
            };
        };

        MemoryPool gMemoryPool;

        void* Allocate(std::uint64_t size)
        {
            return gMemoryPool.Allocate(size);
        }

        void Deallocate(void* address, std::uint64_t size)
        {
            gMemoryPool.Deallocate(address, size);
        }
    }
}
