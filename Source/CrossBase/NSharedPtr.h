
#pragma once

#include <stdint.h>
#include <cassert>

namespace cross
{

template<class T>
class NSharedPtr final
{
public:
	NSharedPtr() = default;

	explicit NSharedPtr(T* ptr):mRawPtr(ptr)
	{
		if (mRawPtr)
			mRawPtr->IncreaseRefCount();
	}

	NSharedPtr(const NSharedPtr<T>& sp):mRawPtr(sp.mRawPtr)
	{
		if (mRawPtr)
			mRawPtr->IncreaseRefCount();
	}

	NSharedPtr& operator = (const NSharedPtr& src)
	{
		if (this != &src)
		{
			if (mRawPtr)
			{
				mRawPtr->DecreaseRefCount();
				if (mRawPtr->GetRefCount() == 0)
					NotifyZeroReference();
			}
			mRawPtr = src.mRawPtr;
			if (mRawPtr)
				mRawPtr->IncreaseRefCount();
		}
		return *this;
	}

	~NSharedPtr()
	{
		if (mRawPtr)
		{
			mRawPtr->DecreaseRefCount();
			assert(mRawPtr->GetRefCount() >= 0);
			if (mRawPtr->GetRefCount() == 0)
				NotifyZeroReference();
			mRawPtr = nullptr;
		}
	}

	void reset(T* newObj = nullptr)
	{
		if (mRawPtr != newObj)
		{
			if (mRawPtr)
			{
				mRawPtr->DecreaseRefCount();
				assert(mRawPtr->GetRefCount() >= 0);
				if (mRawPtr->GetRefCount() == 0)
					NotifyZeroReference();
				mRawPtr = nullptr;
			}
			mRawPtr = newObj;
			if (mRawPtr)
				mRawPtr->IncreaseRefCount();
		}
	}

	int32_t use_count() const { return mRawPtr ? mRawPtr->GetRefCount() : 0; }
	T* operator->() const { return mRawPtr; }
	T& operator*() const { return *mRawPtr; }
	bool operator!() const { return mRawPtr == nullptr; }
	operator bool() const { return mRawPtr != nullptr; }
	bool operator== (const NSharedPtr& rhs) const { return mRawPtr == rhs.mRawPtr; }
	bool operator!= (const NSharedPtr& rhs) const { return mRawPtr != rhs.mRawPtr; }
	bool operator== (T* rawPtr) const { return mRawPtr == rawPtr; }
	bool operator!= (T* rawPtr) const { return mRawPtr != rawPtr; }
	T* get() const { return mRawPtr; }


private:
	void NotifyZeroReference() { delete mRawPtr; }

	T* mRawPtr{ nullptr };
};


}
