#include "PCH/CrossBasePCHPrivate.h"
#include "FileSystem/PathHelper.h"
#include "FileSystem/PathNameUtility.h"
#include "String/StringHelper.h"
#include "Threading/Task.h"

static std::string gEngineBinaryDirectory;
static std::string gCurrentDirectory;
static std::string gEngineResourceDirectory;
#if CROSSENGINE_EDITOR
static std::string gAutoSaveDirectory;
#endif
#if CROSSENGINE_IOS || CROSSENGINE_ANDROID
static std::string gDocumentDirectory;
#endif

#if CROSSENGINE_WASM
#include <sys/types.h>
#include <sys/stat.h>
#endif

#if CROSSENGINE_WIN
#    include <io.h>
#    include <corecrt_io.h>
#else
#    include <unistd.h>
#    include <dirent.h>
#endif

namespace cross
{
    namespace PathHelper_Private
    {
        //auto IsSlashOrBackslash = [](char C) { return C == '/' || C == '\\'; };
        //auto IsNotSlashOrBackslash = [](char C) { return C != '/' && C != '\\'; };
    }

    void PathHelper::SetCurrentDirectoryPath(const std::string& path)
    {
        gCurrentDirectory = path;

        if (gCurrentDirectory.back() == '/')
            gCurrentDirectory.pop_back();

        ConvertSeparators(gCurrentDirectory);

#if CROSSENGINE_EDITOR
        SetAutoSaveDirectoryPath(gCurrentDirectory + "/Intermediate");
#endif
    }

    const std::string& PathHelper::GetCurrentDirectoryPath()
    {
        return gCurrentDirectory;
    }

    std::string PathHelper::GetSavedPath()
    {
        return "Saved";
    }

    void PathHelper::SetEngineResourceDirectoryPath(const std::string& path)
    {
        gEngineResourceDirectory = path;
    }

    const std::string& PathHelper::GetEngineResourceDirectoryPath()
    {
        return gEngineResourceDirectory;
    }

    bool PathHelper::IsInEngineResourceDir(const std::string& path) 
    {
        auto absPath = GetAbsolutePath(path);
        return (absPath.find(GetEngineResourceDirectoryPath()) != std::string::npos);
    }

    void PathHelper::SetEngineBinaryDirectoryPath(const std::string& path)
    {
        gEngineBinaryDirectory = path;
        Normalize(gEngineBinaryDirectory);
    }

    const std::string& PathHelper::GetEngineBinaryDirectoryPath()
    {
        return gEngineBinaryDirectory;
    }

#if CROSSENGINE_EDITOR
    void PathHelper::SetAutoSaveDirectoryPath(const std::string& path) {
        gAutoSaveDirectory = path;
    }

    const std::string& PathHelper::GetAutoSaveDirectoryPath()
    {
        return gAutoSaveDirectory;
    }
#endif

#if CROSSENGINE_IOS || CROSSENGINE_ANDROID
    void PathHelper::SetDocumentDirectoryPath(const std::string& path)
    {
        gDocumentDirectory = path;
    }

    const std::string& PathHelper::GetDocumentDirectoryPath()
    {
        return gDocumentDirectory;
    }
#endif
    bool PathHelper::IsDirectoryExist(const std::string& dir_path)
    {
#if CROSSENGINE_WIN
        struct _stat st;
        wchar_t buf[1024];
        MultiByteToWideChar(CP_UTF8, 0, dir_path.c_str(), -1, buf, sizeof(buf) / sizeof(wchar_t));
        buf[sizeof(buf) / sizeof(wchar_t) - 1] = '\0';
        size_t length = wcslen(buf);
        if (length > 0 && (buf[length - 1] == '/' || buf[length - 1] == '\\')) buf[length - 1] = '\0';
        if (_wstat(buf, &st)) return 0;
        if (st.st_mode & _S_IFDIR) return 1;
#elif CROSSENGINE_ANDROID || CROSSENGINE_IOS || CROSSENGINE_WASM || CROSSENGINE_OSX
        struct stat st;
        if (stat(dir_path.c_str(), &st)) return 0;
        if (st.st_mode & S_IFDIR) return 1;
#elif _CELLOS_LV2
        struct stat st;
        char buf[1024];
        snprintf(buf, sizeof(buf), PATH_PREFIX"%s", dir_path.c_str());
        buf[sizeof(buf) - 1] = '\0';
        if (stat(buf, &st)) return 0;
        if (st.st_mode & S_IFDIR) return 1;
#endif
        return 0;
    }

    bool PathHelper::IsFileExist(const std::string& file_path, bool rawPath)
    {
#if CROSSENGINE_WIN
        std::string absFilePath = file_path;
        if (!rawPath)
            absFilePath = GetAbsolutePath(file_path);
        struct _stat st;
        wchar_t buf[1024];
        MultiByteToWideChar(CP_UTF8, 0, absFilePath.c_str(), -1, buf, sizeof(buf) / sizeof(wchar_t));
        buf[sizeof(buf) / sizeof(wchar_t) - 1] = '\0';
        if (_wstat(buf, &st)) return 0;
        if (st.st_mode & _S_IFREG) return 1;
#elif CROSSENGINE_ANDROID || CROSSENGINE_IOS || CROSSENGINE_WASM || CROSSENGINE_OSX
        struct stat st;
        if (stat(file_path.c_str(), &st)) return 0;
        if (st.st_mode & S_IFREG) return 1;
#elif _CELLOS_LV2
        struct stat st;
        char buf[1024];
        snprintf(buf, sizeof(buf), PATH_PREFIX"%s", file_path.c_str());
        buf[sizeof(buf) - 1] = '\0';
        if (stat(buf, &st)) return 0;
        if (st.st_mode & S_IFREG) return 1;
#endif
        return 0;
    }


    bool PathHelper::MakeDirectory(const std::string& dir_path)
    {
#if CROSSENGINE_WIN
        WCHAR wbuf[1024];
        MultiByteToWideChar(CP_UTF8, 0, dir_path.c_str(), -1, wbuf, sizeof(wbuf) / sizeof(wchar_t));
        wbuf[sizeof(wbuf) / sizeof(wchar_t) - 1] = '\0';
        return (_wmkdir(wbuf) == 0);
#elif CROSSENGINE_IOS || defined(__APPLE__) || CROSSENGINE_WASM || CROSSENGINE_OSX
        return (::mkdir(dir_path.c_str(), 0755) == 0);
#elif _CELLOS_LV2
        char buf[1024];
        snprintf(buf, sizeof(buf), PATH_PREFIX"%s", dir_path.c_str());
        buf[sizeof(buf) - 1] = '\0';
        return (::mkdir(buf, 0755) == 0);
#else
        return false;
#endif
    }

    bool PathHelper::CheckDirectory(const std::string& dir_path)
    {
        const SInt32 nMaxFolder = 10;
        std::string strFolder[nMaxFolder];
        std::string strCreatePath = dir_path;
        std::replace(strCreatePath.begin(), strCreatePath.end(), '\\', '/');
        if (strCreatePath.back() == '/') strCreatePath.back() = '\0';
        SInt32 nLoop = 0;
        do
        {
            if (MakeDirectory(strCreatePath.c_str()))
                break;
            strFolder[nLoop++] = strCreatePath;
            size_t pos = strCreatePath.find_last_of('/');
            if (pos == -1) break;
            strCreatePath = strCreatePath.substr(0, pos); // strFolder[nLoop - 1];
        } while (nLoop < nMaxFolder);

        if (nLoop >= nMaxFolder)
            return false;

        while (nLoop > 0)
        {
            --nLoop;
            if (!MakeDirectory(strFolder[nLoop].c_str()))
                return false;
        }

        return true;
    }

    std::string PathHelper::GetExtension(const std::string& inPath, bool includeDot)
    {
        std::string result = GetCleanFileName(inPath);

        // remove the extension
        SInt32 extPos = (std::max)(0, (SInt32)result.find_last_of("."));

        if (extPos > 0)
        {
            if (includeDot)
            {
                result.erase(0, extPos);
            }
            else
            {
                result.erase(0, extPos + 1);
            }
        }
        else
        {
            result = "";
        }

        return result;
    }

    //std::string PathHelper::GetCleanFileName(const std::string& inPath)
    //{
    //	std::string result = inPath;
    //
    //	// Find the previous slash
    //	SInt32 nPreviousSeparatorIndex = MathUtility::Max(0, (SInt32)result.rfind("/", result.size() - 1));
    //	if (nPreviousSeparatorIndex == 0)
    //	{
    //		nPreviousSeparatorIndex = MathUtility::Max(0, (SInt32)result.rfind("\\", result.size() - 1));
    //	}
    //	
    //	if (nPreviousSeparatorIndex > 0)
    //	{
    //		result.erase(0, nPreviousSeparatorIndex + 1);
    //	}
    //
    //	return result;
    //}

    std::string PathHelper::GetCleanFileName(const std::string& inPath)
    {
        std::string result = StringHelper::Replace(inPath, "\\", "/");

        // Find the previous slash
        SInt32 nPreviousSeparatorIndex = (std::max)(0, (SInt32)result.rfind("/", result.size() - 1));

        if (nPreviousSeparatorIndex > 0)
        {
            result.erase(0, nPreviousSeparatorIndex + 1);
        }

        return result;
    }

    std::string_view PathHelper::GetCleanFileName(std::string_view inPath)
    {
#if CROSSENGINE_WIN
        constexpr auto seperator = '\\';
#else
        constexpr auto seperator = '/';
#endif

        auto pose = inPath.rfind(seperator);
        if (pose != std::string_view::npos)
        {
            return inPath.substr(pose + 1);
        }
        return inPath;
    }

    std::string PathHelper::GetBaseFileName(const std::string& inPath, bool removePath)
    {
        std::string result = removePath ? GetCleanFileName(inPath) : inPath;

        // remove the extension
        SInt32 extPos = (std::max)(0, (SInt32)result.rfind("."));

        if (extPos > 0)
        {
            result.erase(extPos, result.size());
        }

        return result;
    }

    std::string PathHelper::GetUniqueFilePath(const std::string& inPath, UInt32 startIdx)
    {
        std::string fileExt = GetExtension(inPath, true);
        std::string baseFileName = GetBaseFileName(inPath, false);

        std::string result = inPath;
        while (IsFileExist(result))
        {
            result = baseFileName + "_" + std::to_string(startIdx) + fileExt;
            startIdx++;
        }

        return result;
    }

    std::string PathHelper::GetDirectoryFromAbsolutePath(const std::string& filePath)
    {
        SInt32 extPos = (std::max)(0, (SInt32)filePath.rfind("."));
        if (extPos > 0) {
            std::string result = StringHelper::Replace(filePath, "\\", "/");
            SInt32 nPreviousSeparatorIndex = (std::max)(0, (SInt32)result.rfind("/", result.size() - 1));
            if (nPreviousSeparatorIndex > 0)
            {
                //result.erase(0, nPreviousSeparatorIndex + 1);
                result = result.substr(0, nPreviousSeparatorIndex + 1);
            }
            return result;
        }
        else {
            return filePath;
        }
    }

    std::string PathHelper::GetDirectoryFromAbsolutePathWithoutEndSeparator(const std::string& filePath)
    {

        std::string normalizedPath = filePath;
        std::replace(normalizedPath.begin(), normalizedPath.end(), '\\', '/');

        size_t lastSeparator = normalizedPath.find_last_of('/');
        if (lastSeparator == std::string::npos)
        {
            return "";   
        }

        std::string dirPath = normalizedPath.substr(0, lastSeparator);

        if (dirPath.empty() && normalizedPath.size() > 0 && normalizedPath[0] == '/')
        {
            return "/";
        }
        if (dirPath.empty() && normalizedPath.length() >= 2 && std::isalpha(normalizedPath[0]) && normalizedPath[1] == ':')
        {
            return normalizedPath.substr(0, 2) + "/";
        }

        return dirPath;
    }

    std::string PathHelper::GetRelativePath(std::string const& absolutePath)
    {
        if (!IsAbsoluteFilePath(absolutePath))
            return absolutePath;
        auto& currentDirectory = PathHelper::GetCurrentDirectoryPath();
        auto& engineResourceDirtory = PathHelper::GetEngineResourceDirectoryPath();
        auto pos = absolutePath.find(currentDirectory);
        std::string result = absolutePath;
        if (pos == std::string::npos || pos != 0)
        {
            pos = absolutePath.find(engineResourceDirtory);
            if (pos == std::string::npos || pos != 0)
            {
                return "";
            }
            result.replace(0, engineResourceDirtory.length() + 1, "");
        }
        else
        {
            if (currentDirectory.length() > 0) //fix bug, maxwan. if currentDirectory.length() == 0 ,it will have issue
            {
                pos = absolutePath.find(currentDirectory);
                result.replace(0, currentDirectory.length() + 1, "");
            }
        }

        return result;
    }

    std::string PathHelper::GetRelativePath(std::string const& rootPath, std::string const& absolutePath)
    {
        auto pos = absolutePath.find(rootPath);
        std::string result = absolutePath;
        if (pos == std::string::npos || pos != 0)
        {
            return result;
        }
        else
        {
            if (rootPath.length() > 0)
            {
                result.replace(0, rootPath.length() + 1, "");
            }
        }

        return result;
    }

    
    std::string PathHelper::GetInternalRootDirectory(const std::string& absolutePath)
    {
        if (!IsAbsoluteFilePath(absolutePath))
            return "";

        auto& currentDirectory = PathHelper::GetCurrentDirectoryPath();
        auto& engineResourceDirtory = PathHelper::GetEngineResourceDirectoryPath();
        auto pos = absolutePath.find(currentDirectory);
        std::string result = currentDirectory;
        if (pos == std::string::npos || pos != 0)
        {
            pos = absolutePath.find(engineResourceDirtory);
            if (pos == std::string::npos || pos != 0)
            {
                return "";
            }
            result = engineResourceDirtory;
        }

        return result;
    }

    bool PathHelper::MakePathIllegal(std::string& fileName, const char replace)
    {
        for (auto& c : fileName)
        {
            if (c == '*' || c == '?' || c == '"' || c == '>' || c == '<' || c == '|')
            {
                c = replace;
            }
        }
        return false;
    }

    bool PathHelper::MakePathIllegal(std::string& fileName, const std::string& beReplace, const char replace)
    {
        for (auto& c : fileName)
        {
            for (auto& b : beReplace)
            {
                if (c == b)
                    c = replace;
            }
        }
        return false;
    }

    void PathHelper::Normalize(std::string& rawPath)
    {
        rawPath = StringHelper::Replace(rawPath, "\\", "/");
        rawPath = StringHelper::Replace(rawPath, "\\0", "");
    }

    void PathHelper::CombineInternal(std::string& outPath, const char** pathes, UInt32 numPathes)
    {
        assert(pathes != nullptr && numPathes > 0);

        outPath += pathes[0];

        for (UInt32 i = 1; i < numPathes; ++i)
        {
            if (outPath.size() > 1 && outPath[outPath.size() - 1] != '/' && outPath[outPath.size() - 1] != '\\' && pathes[i][0] != '.')
            {
                outPath += '/';
            }
            outPath += pathes[i];
        }
    }

    bool PathHelper::CollapseRelativeDirectories(std::string& inPath)
    {
        const char parentDir[] = "/..";
        const SInt32 parentDirLength = static_cast<SInt32>(ArrayCount(parentDir)) - 1; // To avoid hardcoded values

        for (;;)
        {
            // An empty path is finished
            if (inPath.empty())
                break;

            // Consider empty paths or paths which start with .. or /.. as invalid
            if (StringHelper::StartsWith(inPath, "..") || StringHelper::StartsWith(inPath, parentDir))
                return false;

            // If there are no "/.."s left then we're done
            const SInt32 Index = (SInt32)inPath.find(parentDir);
            if (Index == -1)
                break;

            SInt32 previousSeparatorIndex = Index;
            for (;;)
            {
                // Find the previous slash
                previousSeparatorIndex = (std::max)(0, (SInt32)inPath.rfind("/", previousSeparatorIndex - 1));

                // Stop if we've hit the start of the string
                if (previousSeparatorIndex == 0)
                    break;

                // Stop if we've found a directory that isn't "/./"
                if ((Index - previousSeparatorIndex) > 1 && (inPath[previousSeparatorIndex + 1] != '.' || inPath[previousSeparatorIndex + 2] != '/'))
                    break;
            }

            // If we're attempting to remove the drive letter, that's illegal
            SInt32 Colon = (SInt32)inPath.find(":", previousSeparatorIndex);
            if (Colon >= 0 && Colon < Index)
                return false;

            inPath.erase(previousSeparatorIndex, Index - previousSeparatorIndex + parentDirLength);
        }

        inPath = StringHelper::Replace(inPath, "./", "");

        return true;
    }

    bool PathHelper::IsAbsoluteFilePath(const std::string& path)
    {
        // this function can be called with Windows style path names
        if (path.empty())
            return false;

        if (path[0] == kPlatformPathNameSeparator || path[0] == kPathNameSeparator)
            return true; // network paths are absolute
        if (path.size() >= 2 && path[1] == ':')
            return true; // drive letter followed by colon is absolute

        return false;
    }

    std::string PathHelper::GetDriverLetter(const std::string& path) 
    {
        if (IsAbsoluteFilePath(path)) 
        {
            SInt32 index = (SInt32)path.find(":");
            return path.substr(0, index) + ":/";
        }
        else
            return "";
    }

    std::string PathHelper::GetAssetAbsolutePath(const std::string& path)
    {
        if (IsAbsoluteFilePath(path))
            return path;
        else
            return AppendPathName(PathHelper::GetCurrentDirectoryPath(), path);
    }

    std::string PathHelper::GetEngineResourceAbsolutePath(const std::string& path)
    {
        if (IsAbsoluteFilePath(path))
            return path;
        else
            return AppendPathName(PathHelper::GetEngineResourceDirectoryPath(), path);
    }

    std::string PathHelper::GetAbsolutePath(const std::string& path)
    {
        if (IsAbsoluteFilePath(path))
        {
            return path;
        }
        else
        {
            std::string absolutePath = GetAssetAbsolutePath(path);
            if (IsFileExist(absolutePath))
            {
                return absolutePath;
            }
            absolutePath = GetEngineResourceAbsolutePath(path);
            return absolutePath;
        }
    }

    bool PathHelper::RemoveAbsolutePath(const std::string& path)
    {
        if (std::remove(path.c_str()) == 0)
        {
            return true;
        }
        return false;
    }

    void PathHelper::TraverseFiles(const std::string& path, std::function<void(const std::string& path)> handle, bool recursive) {
#if CROSSENGINE_WIN
        std::string dir = path + "/*";
        intptr_t fp = 0;
        struct _finddatai64_t fileinfo;
        if ((fp = _findfirsti64(dir.c_str(), &fileinfo)) >= 0)
        {
            while (_findnexti64(fp, &fileinfo) == 0)
            {
                if (strcmp(fileinfo.name, ".") == 0 || strcmp(fileinfo.name, "..") == 0)
                    continue;
                if ((fileinfo.attrib & _A_SUBDIR) && recursive)
                    TraverseFiles(path + "/" + fileinfo.name, handle, recursive);
                else
                    handle(path + "/" + fileinfo.name);
            }
        }
        _findclose(fp);
#else
        DIR* dir;
        struct dirent* ptr;

        if ((dir = opendir(path.c_str())) != NULL)
        {
            while ((ptr = readdir(dir)) != NULL)
            {
                if (strcmp(ptr->d_name, ".") == 0 || strcmp(ptr->d_name, "..") == 0)   /// current dir OR parent dir
                    continue;
                else if (ptr->d_type == 8)   /// file
                    handle(path + "/" + ptr->d_name);
                else if (ptr->d_type == 4 && recursive)   /// dir
                    TraverseFiles(path + "/" + ptr->d_name, handle, recursive);
            }
        }
        closedir(dir);
#endif
    }

    UInt32 PathHelper::ParallelTraverseFiles(const std::string& path, std::function<void(const std::string& path, const SInt32 inFileIndex)> handle, bool recursive /*= true*/)
    {
        threading::TaskEventArray taskEventArray;
        std::atomic<UInt32> count = 0;

#if CROSSENGINE_WIN
        std::string dir = path + "/*";
        intptr_t fp = 0;
        struct _finddatai64_t fileinfo;
        if ((fp = _findfirsti64(dir.c_str(), &fileinfo)) >= 0)
        {
            while (_findnexti64(fp, &fileinfo) == 0)
            {
                if (strcmp(fileinfo.name, ".") == 0 || strcmp(fileinfo.name, "..") == 0)
                    continue;
                if ((fileinfo.attrib & _A_SUBDIR) && recursive)
                    ParallelTraverseFilesImpl(path + "/" + fileinfo.name, handle, count, &taskEventArray, recursive);
                else
                {
                    taskEventArray.Add(threading::Async([=, &count](auto) {
                        handle(path + "/" + fileinfo.name, count.load(std::memory_order::relaxed));
                        count++;
                    }));
                }
            }
        }
        _findclose(fp);
#else
        DIR* dir;
        struct dirent* ptr;

        if ((dir = opendir(path.c_str())) != NULL)
        {
            while ((ptr = readdir(dir)) != NULL)
            {
                if (strcmp(ptr->d_name, ".") == 0 || strcmp(ptr->d_name, "..") == 0)   /// current dir OR parent dir
                    continue;
                else if (ptr->d_type == 8)   /// file
                    handle(path + "/" + ptr->d_name, count);
                else if (ptr->d_type == 4 && recursive)   /// dir
                    ParallelTraverseFilesImpl(path + "/" + ptr->d_name, handle, count, &taskEventArray, recursive);
            }
        }
        closedir(dir);
#endif

        taskEventArray.WaitForCompletion();
        taskEventArray.Reset();
        return count;
    }

    void PathHelper::ParallelTraverseFilesImpl(const std::string& path, std::function<void(const std::string& path, const SInt32 inFileIndex)> handle, std::atomic<UInt32>& outCount, threading::TaskEventArray* outTasks,
                                               bool recursive /*= true*/)
    {
#if CROSSENGINE_WIN
        std::string dir = path + "/*";
        intptr_t fp = 0;
        struct _finddatai64_t fileinfo;
        if ((fp = _findfirsti64(dir.c_str(), &fileinfo)) >= 0)
        {
            while (_findnexti64(fp, &fileinfo) == 0)
            {
                if (strcmp(fileinfo.name, ".") == 0 || strcmp(fileinfo.name, "..") == 0)
                    continue;
                if ((fileinfo.attrib & _A_SUBDIR) && recursive)
                    ParallelTraverseFilesImpl(path + "/" + fileinfo.name, handle, outCount, outTasks, recursive);
                else
                {
                    outTasks->Add(threading::Async([=, &outCount](auto) {
                        handle(path + "/" + fileinfo.name, outCount.load(std::memory_order::relaxed));
                        outCount++;
                    }));
                }
            }
        }
        _findclose(fp);
#endif
    }

    std::string PathHelper::GetParentPath(const std::string& path)
    {
        std::string normPath = path;
        Normalize(normPath);
        const auto lastSeprator = normPath.find_last_of('/');
        if (lastSeprator != std::string::npos)
        {
            return normPath.substr(0, lastSeprator);
        }
        
        return "./";
    }

}
