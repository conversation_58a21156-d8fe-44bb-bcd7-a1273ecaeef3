#pragma once
#include <unordered_map>
#include <string_view>
#include <mutex>
#include <limits>
#include <boost/preprocessor/enum.hpp>

#include "CrossBase/CrossBaseForward.h"
#include "CrossBase/CEMetaMacros.h"
namespace cross {

class CEMeta(Cli, Script) NameID
{
public:
    CEFunction(ScriptCallable)
    NameID() {
        mPropertyID = gInvalidIndex;
    };

    // deprecated
    NameID([[maybe_unused]]int value)
    {
        mPropertyID = 0;
    }

    NameID(const char* name)
    {
        mPropertyID = PropertyToID(name, &mDebugName);
    }

    CEFunction(ScriptCallable)
    NameID(const std::string& name)
    {
        mPropertyID = PropertyToID(name, &mDebugName);
    }

    NameID(const std::string_view& name)
    {
        mPropertyID = PropertyToID(name, &mDebugName);
    }

    bool IsValid() const
    {
        return mPropertyID != gInvalidIndex;
    }

    CEFunction(ScriptCallable)
    const char* GetName() const
    {
        return mDebugName;
    }

    size_t GetID() const
    {
        return mPropertyID;
    }

    friend bool operator==(const NameID& lhs, const NameID& rhs)
    {
        return lhs.mPropertyID == rhs.mPropertyID;
    }

    friend bool operator==(const NameID& lhs, const char* rhs)
    {
        return std::strcmp(lhs.GetName(), rhs) == 0;
    }

    friend bool operator==(const char* lhs, const NameID& rhs)
    {
        return std::strcmp(lhs, rhs.GetName()) == 0;
    }

    friend bool operator==(const NameID& lhs, SInt32 rhs)
    {
        return lhs.GetID() == rhs;
    }

    friend bool operator==(SInt32 lhs, const NameID& rhs)
    {
        return lhs == rhs.GetID();
    }


    friend bool operator!=(const NameID& lhs, const NameID& rhs)
    {
        return lhs.mPropertyID != rhs.mPropertyID;
    }

    friend bool operator<(const NameID& lhs, const NameID& rhs)
    {
        return lhs.mPropertyID < rhs.mPropertyID;
    }

    size_t mPropertyID = gInvalidIndex;

private:
    static constexpr size_t gInvalidIndex = (std::numeric_limits<size_t>::max)();

    const char* mDebugName = nullptr;

    CROSS_BASE_API static size_t PropertyToID(std::string_view name, const char** ppInternalName);
};

template<char... chs>
struct StaticNameID
{
    template<size_t N>
    static const NameID& Get(const char(&str)[N])
    {
        static_assert(N <= 64, "Size of string literal of static NameID must be less than 64");
        return gNameID;
    }

private:
    static constexpr char gName[sizeof...(chs)]{ chs... };
    inline static const NameID gNameID{gName};
};

#define EXTRACT_CH(z, n, data) (n < sizeof(data) ? data[n] : 0)
#define NAME_ID(str) StaticNameID<BOOST_PP_ENUM(64, EXTRACT_CH, str)>::Get(str)

}   // namespace cross

template<> struct std::hash<cross::NameID> : public std::hash<int>
{
    size_t operator()(const cross::NameID& val) const noexcept
    {
        return val.GetID();
    }
};