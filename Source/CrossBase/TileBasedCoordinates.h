#pragma once

/*
 *   Large World Coordinates System
 */
#if defined(CE_USE_DOUBLE_TRANSFORM)
#    define LENGTH_PER_TILE     (65536.0)
#    define LENGTH_PER_TILE_F (65536.0f)

#pragma warning(push)
#pragma warning(disable : 4189)
namespace cross {
struct TileBasedMatrix
{
    Float4x4 RelativeMatrix{Float4x4::Identity()};
    Float3 TilePosition{0.f, 0.f, 0.f};
    Float4x4 PreRelativeMatrix{Float4x4::Identity()};
    Float3 PreTilePosition{0.f, 0.f, 0.f};
};

FORCEINLINE static void Validate(double inAbsolute, float inTile, float inOffset)
{
    const double Tolerance = 0.01;
    const double CheckAbsolute = inTile * LENGTH_PER_TILE + inOffset;
    const double Delta = std::abs(CheckAbsolute - inAbsolute);
    assert(Delta < Tolerance);
}

FORCEINLINE double GetAbsolutePosition(float inTile)
{
    return static_cast<double>(inTile) * LENGTH_PER_TILE;
}

FORCEINLINE double GetAbsolutePosition(float inTile, float inOffset)
{
    return static_cast<double>(inTile) * LENGTH_PER_TILE + static_cast<double>(inOffset);
}

FORCEINLINE double GetRelativePosition(double inAbsolutePosition, float inTile)
{
    return inAbsolutePosition - static_cast<double>(inTile) * LENGTH_PER_TILE;
}

FORCEINLINE Double3 GetRelativePosition(Double3 inAbsolutePosition, Float3 inTile)
{
    return {
        GetRelativePosition(inAbsolutePosition.x, inTile.x),
        GetRelativePosition(inAbsolutePosition.y, inTile.y),
        GetRelativePosition(inAbsolutePosition.z, inTile.z)
    };
}

FORCEINLINE float GetTileForAbsolutePosition(double inPosition)
{
    if constexpr (LENGTH_PER_TILE == 0.)
    {
        return 0;
    }
    double tile = inPosition / LENGTH_PER_TILE + 0.5;

    return static_cast<float>(std::floor(tile));
}

FORCEINLINE void GetTileAndOffsetForAbsolutePosition(double inPosition, float& outTile, float& outOffset)
{
    outTile = GetTileForAbsolutePosition(inPosition);
    outOffset = static_cast<float>(inPosition - outTile * LENGTH_PER_TILE);
    Validate(inPosition, outTile, outOffset);
}

FORCEINLINE Double3 GetAbsolutePosition(Float3 inTile)
{
    return Double3(GetAbsolutePosition(inTile.x), GetAbsolutePosition(inTile.y), GetAbsolutePosition(inTile.z));
}

FORCEINLINE Double3 GetAbsolutePosition(Float3 inTile, Float3 inOffset)
{
    return Double3(GetAbsolutePosition(inTile.x, inOffset.x), GetAbsolutePosition(inTile.y, inOffset.y), GetAbsolutePosition(inTile.z, inOffset.z));
}

FORCEINLINE Float3 GetTileForAbsolutePosition(Double3 inPos)
{
    return Float3(GetTileForAbsolutePosition(inPos.x), GetTileForAbsolutePosition(inPos.y), GetTileForAbsolutePosition(inPos.z));
}

FORCEINLINE Float3 GetOffsetForTiles(const Float3& inSrcTile, const Float3& inBaseTile)
{
    Float3 DeltaTile = inSrcTile - inBaseTile;
    return Float3(DeltaTile.x * LENGTH_PER_TILE_F, DeltaTile.y * LENGTH_PER_TILE_F, DeltaTile.z * LENGTH_PER_TILE_F);
}

FORCEINLINE void GetTileAndOffsetForAbsolutePosition(Double3 inPos, Float3& outTile, Float3& outOffset)
{
    GetTileAndOffsetForAbsolutePosition(inPos.x, outTile.x, outOffset.x);
    GetTileAndOffsetForAbsolutePosition(inPos.y, outTile.y, outOffset.y);
    GetTileAndOffsetForAbsolutePosition(inPos.z, outTile.z, outOffset.z);
}

FORCEINLINE Float3 GetLargeCoordinateReltvPosition(Float3 posOffsetInModelTile, Float3 modelTileInLargeWorld, Float3 orgTileInLargeWorld)
{
    return posOffsetInModelTile + (modelTileInLargeWorld - orgTileInLargeWorld) * LENGTH_PER_TILE;
}


FORCEINLINE Float4x4 AbsoluteInvMatrixToRelativeMatrix(const Float3& tilePos, const Double4x4& relativeInvMatrix)
{
    return static_cast<Float4x4>(Double4x4::CreateTranslation(GetAbsolutePosition(tilePos)) * relativeInvMatrix);
}

FORCEINLINE Float4x4A AbsoluteMatrixToRelativeMatrix(const Float3& tilePos, const Double4x4& absoluteMatrix)
{
    return static_cast<Float4x4A>(absoluteMatrix * Double4x4::CreateTranslation(-GetAbsolutePosition(tilePos)));
}

FORCEINLINE Double4x4 AbsoluteMatrixToRelativeMatrixDouble(const Float3& tilePos, const Double4x4& absoluteMatrix)
{
    return absoluteMatrix * Double4x4::CreateTranslation(-GetAbsolutePosition(tilePos));
}

FORCEINLINE Double4x4 RelativeMatrixToAbsoluteMatrix(const Float3& tilePos, const Float4x4& RelativeMatrix)
{
    return static_cast<Double4x4>(RelativeMatrix) * Double4x4::CreateTranslation(GetAbsolutePosition(tilePos));
}

FORCEINLINE Float3 WorldToLocalPosition(const Float4x4& invRelativeWorldMatrix, const Float3& baseTile, const Float3& pos, const Float3& tile)
{
    auto reltvPosition = GetLargeCoordinateReltvPosition(pos, tile, baseTile);
    return Float4x4::TransformPointF3(invRelativeWorldMatrix, reltvPosition);
}

FORCEINLINE Float3 LocalToWorldPosition(const Float4x4& relativeWorldMatrix, const Float3& pos)
{
    return Float4x4::TransformPointF3(relativeWorldMatrix, pos);
}

template<typename BoundingClass>
FORCEINLINE void TransfomBoundingObjectToCameraTileSpace(BoundingClass& outBoundingObj, const BoundingClass& inBoundingObj, const Float3& inTileBoundingObj, const Float3& inTileCamera)
{
    inBoundingObj.Transform(outBoundingObj, GetOffsetForTiles(inTileBoundingObj, inTileCamera));
}

}

#pragma warning(pop)

#endif

#if defined(CE_USE_DOUBLE_TRANSFORM)
#define BOOL_USE_DOUBLE_TRANSFORM true
#else
#define BOOL_USE_DOUBLE_TRANSFORM false
#endif
