
#pragma once

#include "CrossBase/safe_ptr.h"
#include <stdint.h>
#include <atomic>
#include <assert.h>

namespace cross
{

class ReferenceCountObject
{
public:
	virtual ~ReferenceCountObject() { assert(mRefCount == 0); }

	virtual void IncreaseRefCount() { mRefCount++; }

	virtual void DecreaseRefCount() 
	{ 
		mRefCount--; 
		if (mRefCount == 0)
			OnZeroReference();
	}

	virtual int32_t GetRefCount() { return mRefCount; }

	virtual void IncreaseRefCount() const { mRefCount++; }

	virtual void DecreaseRefCount() const 
	{
		mRefCount--;
		if (mRefCount == 0)
			OnZeroReference();
	}

	virtual int32_t GetRefCount() const { return mRefCount; }

protected:
	virtual void OnZeroReference() {}

	virtual void OnZeroReference() const {}

private:
	ReferenceCountObject(ReferenceCountObject const&) = delete;

	ReferenceCountObject& operator=(ReferenceCountObject const&) = delete;

protected:
	ReferenceCountObject() = default;

	mutable std::atomic<int32_t> mRefCount{ 0 };
};

using SafeObject = eastl::safe_object;

template<class T>
using SafePtr = eastl::safe_ptr<T>;

}
