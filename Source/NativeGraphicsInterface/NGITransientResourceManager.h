#pragma once
#include "RenderEngine/RenderingExecutionDescriptor/REDResource.h"
#include "NativeGraphicsInterface/NGI.h"
#include "NativeGraphicsInterface/NGITransientResourceAllocator.h"
namespace cross 
{
struct TextureViewFullDesc : public NGITextureViewDesc
{
    NGIUniqueID<NGITexture> TextureID;

    friend bool operator==(const TextureViewFullDesc& a, const TextureViewFullDesc& b);
    friend SimpleStream& operator<<(SimpleStream& stream, const TextureViewFullDesc& desc);
};

struct BufferViewFullDesc : public NGIBufferViewDesc
{
    NGIUniqueID<NGIBuffer> BufferID;

    friend bool operator==(const BufferViewFullDesc& a, const BufferViewFullDesc& b);
    friend SimpleStream& operator<<(SimpleStream& stream, const BufferViewFullDesc& desc);
};

struct RenderPassTargetID
{
    NGIUniqueID<NGITextureView> TargetID;
    NGIUniqueID<NGITextureView> ResolveTargetID;
};

struct FramebufferFullDesc : public NGIFramebufferDesc
{
    RenderPassTargetID ColorTargetIDs[MaxSupportedRenderTargets];
    RenderPassTargetID DepthStencilTargetID;

    friend bool operator==(const FramebufferFullDesc& a, const FramebufferFullDesc& b);
    friend SimpleStream& operator<<(SimpleStream& stream, const FramebufferFullDesc& desc);
};
struct NGIResourceManager : public INGIResourceManager, public NGIObject, NGIDeviceChild
{
    NGI_API NGIResourceManager(NGIDevice* device);
    NGI_API void ExtendLifetime(NGITexture* texture);
    NGI_API void ExtendLifetime(NGIBuffer* buffer);
    NGI_API virtual void DeallocateTexture(NGITexture* texture) {}
    NGI_API virtual void DeallocateBuffer(NGIBuffer* buffer) {}
    NGI_API virtual std::tuple<NGITexture*, NGIResourceState*> AllocateTexture(const NGITextureDesc& desc, std::string_view name, bool memoryless, bool bIsTransient = false, bool bIsNormal = false);
    NGI_API virtual std::tuple<NGIBuffer*, NGIResourceState*> AllocateBuffer(const NGIBufferDesc& desc, std::string_view name, bool bIsTransient = false, bool bIsNormal = false);
    NGI_API NGITextureView* AllocateTextureView(const NGITextureViewDesc& desc, NGITexture* texture);
    NGI_API NGIBufferView* AllocateBufferView(const NGIBufferViewDesc& desc, NGIBuffer* buffer);
    NGI_API NGIFramebuffer* AllocateFramebuffer(const NGIFramebufferDesc& desc);
    NGI_API void OnBeginFrame(FrameParam* frameparam) override;
    NGI_API virtual void Flush() {}
    NGI_API static bool IsTransientResource(REDResource* resource, bool bIsForUpload = false)
    {
        Assert(resource->mType != REDResourceType::External);
        if (resource->mType != REDResourceType::Normal || bIsForUpload)
            return false;
        return resource->bIsTransient;
    }
    struct TextureInfo
    {
        std::string Name;
        std::vector<NGIResourceState> States;
        UInt32 Lifetime;
        NGITexture* NativeTexture{nullptr};
        NGITransientTexture* TransientTexture{nullptr};
    };
    struct BufferInfo
    {
        std::string Name;
        NGIResourceState State;
        UInt32 Lifetime;
        NGIBuffer* NativeBuffer{nullptr};
        NGITransientBuffer* TransientBuffer{nullptr};
    };
    std::unordered_map<NGIBufferDesc, std::list<BufferInfo>, NGIObjectDescHasher> mBufferGroups;
    std::unordered_map<NGITextureDesc, std::list<TextureInfo>, NGIObjectDescHasher> mTextureGroups;
    std::pair<TextureInfo*, std::list<TextureInfo>*> GetTextureInfo(const NGITextureDesc& desc, std::string_view name)
    {
        auto& texGroup = mTextureGroups[desc];
        TextureInfo* texWithSameDesc = nullptr;
        for (auto& texture : texGroup)
        {
            if (texture.Lifetime < CmdSettings::Inst().gMaxQueuedFrame) 
            {
                if (name == std::string_view(texture.Name))
                    return {&texture, &texGroup};
                texWithSameDesc = &texture;
            }
        }
        return {texWithSameDesc, &texGroup};
    }
    std::pair<BufferInfo*, std::list<BufferInfo>*> GetBufferInfo(const NGIBufferDesc& desc, std::string_view name)
    {
        auto& bufGroup = mBufferGroups[desc];
        BufferInfo* bufWithSameDesc = nullptr;
        for (auto& buffer : bufGroup)
        {
            if (buffer.Lifetime < CmdSettings::Inst().gMaxQueuedFrame)
            {
                if (name == std::string_view(buffer.Name))
                    return {&buffer, &bufGroup};
                bufWithSameDesc = &buffer;
            }
        }
        return {bufWithSameDesc, &bufGroup};
    }

    struct Hasher
    {
        size_t operator()(const TextureViewFullDesc& desc) const;

        size_t operator()(const BufferViewFullDesc& desc) const;

        size_t operator()(const FramebufferFullDesc& desc) const;
    };
    using TextureView = std::tuple<std::unique_ptr<NGITextureView>, UInt32>;
    using BufferView = std::tuple<std::unique_ptr<NGIBufferView>, UInt32>;
    using Framebuffer = std::tuple<std::unique_ptr<NGIFramebuffer>, UInt32>;
    std::mutex mTextureViewMutex;
    std::unordered_map<TextureViewFullDesc, TextureView, Hasher> mTextureViews;
    std::mutex mBufferViewMutex;
    std::unordered_map<BufferViewFullDesc, BufferView, Hasher> mBufferViews;
    std::unordered_map<FramebufferFullDesc, Framebuffer, Hasher> mFramebuffers;
};
struct NGITransientResourceManager : public NGIResourceManager
{
    NGI_API NGITransientResourceManager(NGIDevice* device);
    NGI_API std::tuple<NGITexture*, NGIResourceState*> AllocateTexture(const NGITextureDesc& desc, std::string_view name, bool memoryless, bool bIsTransient = false, bool bIsNormal = false) override;
    NGI_API std::tuple<NGIBuffer*, NGIResourceState*> AllocateBuffer(const NGIBufferDesc& desc, std::string_view name, bool bIsTransient = false, bool bIsNormal = false) override;
    NGI_API void DeallocateTexture(NGITexture* texture) override;
    NGI_API void DeallocateBuffer(NGIBuffer* buffer) override;
    NGI_API void OnBeginFrame(FrameParam* frameparam) override;
    NGI_API virtual void Flush() override
    {
        mAllocator->Flush();
    }
private:
    bool bEnableTransientBuffer = true;
    std::vector<NGITransientTexture*> mTransientTextures;
    std::vector<NGITransientBuffer*> mTransientBuffers;
    std::unique_ptr<NGITransientResourceAllocator> mAllocator;
};

class NGIConstantBufferAllocator
{
public:
    NGI_API NGIConstantBufferAllocator();

    NGI_API ~NGIConstantBufferAllocator();

    NGI_API SizeType GetBufferNum() const { return mConstantBuffers.size(); }

    NGI_API bool IsBufferDirty(UInt32 index) const
    {
        Assert(index < mConstantBuffers.size());
        return mWriteDirtyFlag[index];
    }

    NGI_API NGIBuffer* GetBuffer(UInt32 index)
    {
        Assert(index < mConstantBuffers.size());
        return mConstantBuffers[index];
    }

    NGI_API std::pair<NGITransientHeapAllocation, UInt32> Allocate(SizeType size);

    NGI_API void Deallocate(const NGITransientHeapAllocation& allocation, UInt32 index);

    NGI_API void SetWriteDirtyFlag(UInt32 index)
    {
        Assert(index < mConstantBuffers.size());
        mWriteDirtyFlag.set(index);
    }

    NGI_API void ClearWriteDirtyFlag() { mWriteDirtyFlag = 0; }

private:
    static constexpr SizeType BUFFER_SIZE = 1 << 18;
    static constexpr UInt32 MAX_BUFFER_NUM = 64;
    std::vector<NGITransientHeapAllocator> mAllocators;
    std::vector<NGIBuffer*> mConstantBuffers;
    std::bitset<MAX_BUFFER_NUM> mWriteDirtyFlag = 0;
    SizeType mAlignment = 1;
};

}