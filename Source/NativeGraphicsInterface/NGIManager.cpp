#include "EnginePrefix.h"
#include "NGIManager.h"

#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/SettingsManager.h"

#if NGI_SUPPORTS_VULKAN
#    if ENABLE_VR
#        include "NativeGraphicsInterface/Vulkan/VulkanXRDevice.h"
#    else
#        include "NativeGraphicsInterface/Vulkan/VulkanDevice.h"
#    endif
#endif

#if NGI_SUPPORTS_METAL
#    import <Metal/Metal.h>
#    include "NativeGraphicsInterface/Metal/MetalDevice.h"
#endif

#if NGI_SUPPORTS_D3D12
#endif

 #if NGI_SUPPORTS_OPENGLES30
 #    include "GLES3/GLES3Device.h"
 #endif

#if !(CROSSENGINE_IOS || CROSSENGINE_ANDROID)
#include "memoryhooker/Module.h"
IMPORT_MODULE
#endif

namespace 
{
cross::NGIDevice* gDevice = nullptr;
}

bool cross::InitializeNGIDevice()
{
    Assert(!gDevice);
    auto renderMode = EngineGlobal::GetSettingMgr()->GetRenderMode();
    switch (renderMode)
    {
#if NGI_SUPPORTS_D3D12
    case NGIPlatform::D3D12:
        Assert(false);
        return true;
#endif
#if NGI_SUPPORTS_VULKAN
    case NGIPlatform::Vulkan:
    {
        VulkanDevice* device;
#if ENABLE_VR
        device = new VulkanXRDevice{};
#else
        device = new VulkanDevice{};
#endif
        device->Initialize();
        gDevice = device;
        return true;
    }
#endif
 #if NGI_SUPPORTS_OPENGLES30
     case NGIPlatform::OpenGLES3:
         gDevice = new GLES3Device{};
         return true;
 #endif
#if NGI_SUPPORTS_METAL
    case NGIPlatform::Metal:
        gDevice = new MetalDevice{};
        return true;
#endif
#if NGI_SUPPORTS_WXGAME
    case NGIPlatform::WXGame:
        Assert(false);
        return true;
#endif
    default:
        Assert(false);
    }
    return false;
}

void cross::NGIDeviceWaitIdle()
{
    gDevice->WaitIdle();
}

void cross::DestroyNGIDevice()
{
    delete gDevice;
}

cross::NGIDevice* cross::GetNGIDevicePtr()
{
    return gDevice;
}
