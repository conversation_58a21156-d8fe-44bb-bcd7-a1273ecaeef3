#include "EnginePrefix.h"
#include "NGI.h"
#include "CrossBase/Hash/Hash.h"
#include "CrossBase/Hash/MurmurHash3.h"
#include "NativeGraphicsInterface/ScratchBuffer.h"
#include "NativeGraphicsInterface/UploadBuffer.h"
#include "NativeGraphicsInterface/Statistics.h"

#include <memory>

#if CROSSENGINE_ANDROID
_LIBCPP_BEGIN_NAMESPACE_STD
template<typename T, UInt32 size, typename TCount>
tuple(T(&)[size], TCount)->tuple<T*, TCount>;
_LIBCPP_END_NAMESPACE_STD
#endif

std::string cross::ToString(NGIResourceState state)
{
#define RESOURCE_STATE_NAME(S) { NGIResourceState::S, #S },
    static const std::vector<std::tuple<NGIResourceState, std::string_view>> gResourceStateNames
    {
        RESOURCE_STATE_NAME(Present)
        RESOURCE_STATE_NAME(HostRead)
        RESOURCE_STATE_NAME(VertexBuffer)
        RESOURCE_STATE_NAME(IndexBuffer)
        RESOURCE_STATE_NAME(IndirectArgument)
        RESOURCE_STATE_NAME(SubpassRead)
        RESOURCE_STATE_NAME(TargetRead)
        RESOURCE_STATE_NAME(TargetReadWrite)
        RESOURCE_STATE_NAME(TargetResolveSrc)
        RESOURCE_STATE_NAME(TargetResolveDst)
        RESOURCE_STATE_NAME(NoEarlyTestBit)
        RESOURCE_STATE_NAME(CopyDst)
        RESOURCE_STATE_NAME(CopySrc)
        RESOURCE_STATE_NAME(ResolveDst)
        RESOURCE_STATE_NAME(ResolveSrc)
        RESOURCE_STATE_NAME(ConstantBufferBit)
        RESOURCE_STATE_NAME(ShaderResourceBit)
        RESOURCE_STATE_NAME(UnorderedAccessBit)
        RESOURCE_STATE_NAME(VertexShaderBit)
        RESOURCE_STATE_NAME(HullShaderBit)
        RESOURCE_STATE_NAME(DomainShaderBit)
        RESOURCE_STATE_NAME(GeometryShaderBit)
        RESOURCE_STATE_NAME(PixelShaderBit)
        RESOURCE_STATE_NAME(ComputeShaderBit)
        RESOURCE_STATE_NAME(RayTracingShaderBit)
        RESOURCE_STATE_NAME(AccelStructReadBit)
        RESOURCE_STATE_NAME(AccelStructWriteBit)
        RESOURCE_STATE_NAME(AccelStructBuildInputBit)
        RESOURCE_STATE_NAME(AccelStructBuildBLASBit)
    };

    std::string stateDesc;

    if (state == NGIResourceState::Undefined)
    {
        stateDesc = "Undefined";
    }
    else
    {
        for (auto [resState, name] : gResourceStateNames)
        {
            if (EnumHasAnyFlags(state, resState))
            {
                if (stateDesc.empty())
                {
                    stateDesc = name;
                }
                else
                {
                    stateDesc.append(fmt::format("|{}", name));
                }
            }
        }
    }

    return stateDesc;
}

bool cross::operator==(const NGIRenderPassTargetDesc& a, const NGIRenderPassTargetDesc& b)
{
    return
        a.Format == b.Format &&
        a.LoadOp == b.LoadOp &&
        a.StoreOp == b.StoreOp;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIRenderPassTargetDesc& desc)
{
    return stream <<
        desc.Format <<
        desc.LoadOp <<
        desc.StoreOp;
}

bool cross::operator==(const NGISubpassDesc& a, const NGISubpassDesc& b)
{
    return
        std::equal(a.InputAttachments, a.InputAttachments + a.InputAttachmentCount, b.InputAttachments, b.InputAttachments + b.InputAttachmentCount) &&
        std::equal(a.ColorAttachments, a.ColorAttachments + a.ColorAttachmentCount, b.ColorAttachments, b.ColorAttachments + b.ColorAttachmentCount) &&
        a.NeedDepthStencil == b.NeedDepthStencil &&
        a.DepthReadOnly == b.DepthReadOnly &&
        a.StencilReadOnly == b.StencilReadOnly;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGISubpassDesc& desc)
{
    return stream <<
        desc.InputAttachmentCount <<
        std::tuple{ desc.InputAttachments, desc.InputAttachmentCount } <<
        desc.ColorAttachmentCount <<
        std::tuple{ desc.ColorAttachments, desc.ColorAttachmentCount } <<
        desc.NeedDepthStencil <<
        desc.DepthReadOnly <<
        desc.StencilReadOnly;
}

bool cross::operator==(const NGIRenderPassDesc& a, const NGIRenderPassDesc& b)
{
    return
        a.SampleCount == b.SampleCount &&
        std::equal(a.ColorTargets, a.ColorTargets + a.ColorTargetCount, b.ColorTargets, b.ColorTargets + b.ColorTargetCount) &&
        a.HasDepthStencil == b.HasDepthStencil &&
        a.DepthStencilFormat == b.DepthStencilFormat &&
        a.DepthLoadOp == b.DepthLoadOp &&
        a.DepthStoreOp == b.DepthStoreOp &&
        a.DepthResolveType == b.DepthResolveType &&
        a.StencilLoadOp == b.StencilLoadOp &&
        a.StencilStoreOp == b.StencilStoreOp &&
        a.StencilResolveType == b.StencilResolveType &&
        std::equal(a.Subpasses, a.Subpasses + a.SubpassCount, b.Subpasses, b.Subpasses + b.SubpassCount);
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIRenderPassDesc& desc)
{
    return stream <<
        desc.SampleCount <<
        desc.ColorTargetCount <<
        std::tuple{ desc.ColorTargets, desc.ColorTargetCount } <<
        desc.HasDepthStencil <<
        desc.DepthStencilFormat <<
        desc.DepthLoadOp <<
        desc.DepthStoreOp <<
        desc.DepthResolveType <<
        desc.StencilLoadOp <<
        desc.StencilStoreOp <<
        desc.StencilResolveType <<
        desc.SubpassCount <<
        std::tuple{ desc.Subpasses, desc.SubpassCount };
}

bool cross::operator==(const NGIFramebufferDesc& a, const NGIFramebufferDesc& b)
{
    if (a.RenderPass != b.RenderPass ||
        a.Width != b.Width ||
        a.Height != b.Height ||
        a.LayerCount != b.LayerCount)
    {
        return false;
    }

    auto& renderPassDesc = a.RenderPass->GetDesc();
    auto colorTargetCount = renderPassDesc.ColorTargetCount;
    for (UInt32 i = 0; i < colorTargetCount; ++i)
    {
        auto& colorTargetDesc = renderPassDesc.ColorTargets[i];
        auto& colorTargetA = a.ColorTargets[i];
        auto& colorTargetB = b.ColorTargets[i];
        if (colorTargetA.Target != colorTargetB.Target)
        {
            return false;
        }
        if (EnumHasAnyFlags(colorTargetDesc.StoreOp, NGIStoreOp::Resolve))
        {
            if (colorTargetA.ResolveTarget != colorTargetB.ResolveTarget)
            {
                return false;
            }
        }
    }

    if (renderPassDesc.HasDepthStencil)
    {
        if (a.DepthStencilTarget.Target != b.DepthStencilTarget.Target)
        {
            return false;
        }
        if (EnumHasAnyFlags(renderPassDesc.DepthStoreOp, NGIStoreOp::Resolve) || EnumHasAnyFlags(renderPassDesc.StencilStoreOp, NGIStoreOp::Resolve))
        {
            if (a.DepthStencilTarget.ResolveTarget != b.DepthStencilTarget.ResolveTarget)
            {
                return false;
            }
        }
    }

    return true;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIFramebufferDesc& desc)
{
    auto& renderPassDesc = desc.RenderPass->GetDesc();

    stream <<
        desc.RenderPass <<
        desc.Width <<
        desc.Height <<
        desc.LayerCount <<
        renderPassDesc.ColorTargetCount;

    for (UInt32 i = 0; i < renderPassDesc.ColorTargetCount; ++i)
    {
        stream << desc.ColorTargets[i].Target;
        auto& colorTargetDesc = renderPassDesc.ColorTargets[i];
        if (EnumHasAnyFlags(colorTargetDesc.StoreOp, NGIStoreOp::Resolve))
        {
            stream << desc.ColorTargets[i].ResolveTarget;
        }
    }
    if (renderPassDesc.HasDepthStencil)
    {
        stream << desc.DepthStencilTarget.Target;
        if (EnumHasAnyFlags(renderPassDesc.DepthStoreOp, NGIStoreOp::Resolve) || EnumHasAnyFlags(renderPassDesc.StencilStoreOp, NGIStoreOp::Resolve))
        {
            stream << desc.DepthStencilTarget.ResolveTarget;
        }
    }
    return stream;
}

bool cross::operator==(const NGIResourceDesc& a, const NGIResourceDesc& b)
{
    return
        a.ID == b.ID &&
        a.Type == b.Type &&
        a.Space == b.Space &&
        a.Index == b.Index &&
        a.ArraySize == b.ArraySize &&
        a.StageMask == b.StageMask;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIResourceDesc& desc)
{
    return stream <<
        desc.ID.mPropertyID <<
        desc.Type <<
        desc.Space <<
        desc.Index <<
        desc.ArraySize <<
        desc.StageMask;
}

bool cross::operator<(const NGIResourceDesc& a, const NGIResourceDesc& b)
{
    return a.ID < b.ID;
}

bool cross::operator==(const NGIVariableDesc& a, const NGIVariableDesc& b)
{
    return
        a.Name == b.Name &&
        a.Type == b.Type &&
        a.RowCount == b.RowCount &&
        a.ColCount == b.ColCount &&
        a.Index == b.Index &&
        a.Offset == b.Offset &&
        a.Size == b.Size &&
        a.StageMask == b.StageMask;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIVariableDesc& desc)
{
    return stream <<
        desc.Name.GetName() <<
        desc.Type <<
        desc.RowCount <<
        desc.ColCount <<
        desc.Index <<
        desc.Offset <<
        desc.Size <<
        desc.StageMask;
}

bool cross::operator==(const NGIResourceGroupLayoutDesc& a, const NGIResourceGroupLayoutDesc& b)
{
    return std::equal(a.Resources, a.Resources + a.ResourceCount, b.Resources, b.Resources + b.ResourceCount);
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIResourceGroupLayoutDesc& desc)
{
    return stream << desc.ResourceCount << std::tuple{ desc.Resources, desc.ResourceCount };
}

cross::NGIResourceGroupLayout::NGIResourceGroupLayout(const NGIResourceGroupLayoutDesc& desc, NGIDevice* device)
    : NGIObjectDesc{ desc }
    , NGIDeviceChild{ device }
    , mResources{ desc.Resources, desc.Resources + desc.ResourceCount, }
{
    Assert(std::is_sorted(mResources.begin(), mResources.end()));
    mDesc.Resources = mResources.data();
}

bool cross::operator==(const NGIPipelineLayoutDesc& a, const NGIPipelineLayoutDesc& b)
{
    return
        std::equal(a.ResourceGroupLayouts, a.ResourceGroupLayouts + a.ResourceGroupCount, b.ResourceGroupLayouts, b.ResourceGroupLayouts + b.ResourceGroupCount) &&
        std::equal(a.Constants, a.Constants + a.ConstantCount, b.Constants, b.Constants + b.ConstantCount) &&
        a.ConstantSpace == b.ConstantSpace &&
        a.ConstantIndex == b.ConstantIndex;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIPipelineLayoutDesc& desc)
{
    return stream <<
        desc.ResourceGroupCount <<
        std::tuple{ desc.ResourceGroupLayouts, desc.ResourceGroupCount } <<
        desc.ConstantCount <<
        std::tuple{ desc.Constants, desc.ConstantCount } <<
        desc.ConstantSpace <<
        desc.ConstantIndex;
}

bool cross::operator==(const NGIVertexStream& a, const NGIVertexStream& b)
{
    return
        a.Stride == b.Stride &&
        a.InstanceStepRate == b.InstanceStepRate &&
        a.Frequency == b.Frequency;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIVertexStream& desc)
{
    return stream <<
        desc.Stride <<
        desc.InstanceStepRate <<
        desc.Frequency;
}

bool cross::operator==(const NGIVertexChannel& a, const NGIVertexChannel& b)
{
    return
        a.NameOrIndex == b.NameOrIndex &&
        a.Format == b.Format &&
        a.Offset == b.Offset &&
        a.StreamIndex == b.StreamIndex;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIVertexChannel& desc)
{
    return stream <<
        desc.NameOrIndex <<
        desc.Format <<
        desc.Offset <<
        desc.StreamIndex;
}

bool cross::operator==(const NGIInputLayoutDesc& a, const NGIInputLayoutDesc& b)
{
    return
        std::equal(a.Streams, a.Streams + a.StreamCount, b.Streams, b.Streams + b.StreamCount) &&
        std::equal(a.Channels, a.Channels + a.ChannelCount, b.Channels, b.Channels + b.ChannelCount);

}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIInputLayoutDesc& desc)
{
    return stream <<
        std::tuple{ desc.Streams, desc.StreamCount } <<
        std::tuple{ desc.Channels, desc.ChannelCount };
}

cross::NGIPipelineLayout::NGIPipelineLayout(const NGIPipelineLayoutDesc& desc, NGIDevice* device)
    : NGIObjectDesc{ desc }
    , NGIDeviceChild{ device }
    , mConstants{ desc.Constants, desc.Constants + desc.ConstantCount }
{
    Assert(std::is_sorted(desc.Constants, desc.Constants + desc.ConstantCount));
    mDesc.Constants = mConstants.data();
}

bool cross::operator==(const NGIRasterizationStateDesc& a, const NGIRasterizationStateDesc& b)
{
    return
        a.FillMode == b.FillMode &&
        a.CullMode == b.CullMode &&
        a.FaceOrder == b.FaceOrder &&
        a.EnableDepthClip == b.EnableDepthClip &&
        a.EnableAntialiasedLine == b.EnableAntialiasedLine &&
        a.EnableDepthBias == b.EnableDepthBias &&
        a.DepthBias == b.DepthBias &&
        a.DepthBiasClamp == b.DepthBiasClamp &&
        a.SlopeScaledDepthBias == b.SlopeScaledDepthBias &&
        a.ForcedSampleCount == b.ForcedSampleCount &&
        a.LineWidth == b.LineWidth &&
        a.RasterMode == b.RasterMode &&
        a.RasterOverestimationSize == b.RasterOverestimationSize;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIRasterizationStateDesc& desc)
{
    return stream <<
        desc.FillMode <<
        desc.CullMode <<
        desc.FaceOrder <<
        desc.EnableDepthClip <<
        desc.EnableAntialiasedLine <<
        desc.EnableDepthBias <<
        desc.DepthBias <<
        desc.DepthBiasClamp <<
        desc.SlopeScaledDepthBias <<
        desc.ForcedSampleCount <<
        desc.LineWidth <<
        desc.RasterMode <<
        desc.RasterOverestimationSize;
}

bool cross::operator==(const NGIDynamicStateDesc& a, const NGIDynamicStateDesc& b)
{
    return a.StencilReference == b.StencilReference;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIDynamicStateDesc& desc)
{
    return stream << desc.StencilReference;
}

bool cross::operator==(const NGIBufferDesc& a, const NGIBufferDesc& b)
{
    return
        a.Size == b.Size &&
        a.Usage == b.Usage;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIBufferDesc& desc)
{
    return stream <<
        desc.Size <<
        desc.Usage;
}

bool cross::operator==(const NGIBufferViewDesc& a, const NGIBufferViewDesc& b)
{
    return
        a.Usage == b.Usage &&
        a.BufferLocation == b.BufferLocation &&
        a.SizeInBytes == b.SizeInBytes &&
        a.Format == b.Format &&
        a.StructureByteStride == b.StructureByteStride;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIBufferViewDesc& desc)
{
    return stream <<
        desc.Usage <<
        desc.BufferLocation <<
        desc.SizeInBytes <<
        desc.Format <<
        desc.StructureByteStride;
}

bool cross::operator==(const NGITextureDesc& a, const NGITextureDesc& b)
{
    return
        a.Format == b.Format &&
        a.Dimension == b.Dimension &&
        a.MipCount == b.MipCount &&
        a.SampleCount == b.SampleCount &&
        a.Width == b.Width &&
        a.Height == b.Height &&
        a.Depth == b.Depth &&
        a.ArraySize == b.ArraySize &&
        a.Usage == b.Usage &&
        a.MutableFormat == b.MutableFormat &&
        a.SeparateView == b.SeparateView;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGITextureDesc& desc)
{
    return stream <<
        desc.Format <<
        desc.Dimension <<
        desc.MipCount <<
        desc.SampleCount <<
        desc.Width <<
        desc.Height <<
        desc.Depth <<
        desc.ArraySize <<
        desc.Usage <<
        desc.MutableFormat <<
        desc.SeparateView;
}

bool cross::operator==(const NGITextureSubRange& a, const NGITextureSubRange& b)
{
    return
        a.Aspect == b.Aspect &&
        a.MostDetailedMip == b.MostDetailedMip &&
        a.MipLevels == b.MipLevels &&
        a.FirstArraySlice == b.FirstArraySlice &&
        a.ArraySize == b.ArraySize;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGITextureSubRange& desc)
{
    return stream <<
        desc.Aspect <<
        desc.MostDetailedMip <<
        desc.MipLevels <<
        desc.FirstArraySlice <<
        desc.ArraySize;
}

bool cross::operator==(const NGITextureViewDesc& a, const NGITextureViewDesc& b)
{
    return
        a.Usage == b.Usage &&
        a.Format == b.Format &&
        a.ViewDimension == b.ViewDimension &&
        a.SubRange == b.SubRange;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGITextureViewDesc& desc)
{
    return stream <<
        desc.Usage <<
        desc.Format <<
        desc.ViewDimension <<
        desc.SubRange;
}

bool cross::operator==(const NGITargetBlendStateDesc& a, const NGITargetBlendStateDesc& b)
{
    return
        a.EnableBlend == b.EnableBlend &&
        a.EnableLogicOp == b.EnableLogicOp &&
        a.SrcBlend == b.SrcBlend &&
        a.DestBlend == b.DestBlend &&
        a.BlendOp == b.BlendOp &&
        a.SrcBlendAlpha == b.SrcBlendAlpha &&
        a.DestBlendAlpha == b.DestBlendAlpha &&
        a.BlendOpAlpha == b.BlendOpAlpha &&
        a.LogicOp == b.LogicOp &&
        a.WriteMask == b.WriteMask;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGITargetBlendStateDesc& desc)
{
    return stream <<
        desc.EnableBlend <<
        desc.EnableLogicOp <<
        desc.SrcBlend <<
        desc.DestBlend <<
        desc.BlendOp <<
        desc.SrcBlendAlpha <<
        desc.DestBlendAlpha <<
        desc.BlendOpAlpha <<
        desc.LogicOp <<
        desc.WriteMask;
}

bool cross::operator==(const NGIBlendStateDesc& a, const NGIBlendStateDesc& b)
{
    return
        a.EnableAlphaToCoverage == b.EnableAlphaToCoverage &&
        a.EnableIndependentBlend == b.EnableIndependentBlend &&
        std::equal(a.TargetBlendState, a.TargetBlendState + a.TargetCount, b.TargetBlendState, b.TargetBlendState + b.TargetCount);
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIBlendStateDesc& desc)
{
    return stream <<
        desc.EnableAlphaToCoverage <<
        desc.EnableIndependentBlend <<
        std::tuple{ desc.TargetBlendState, desc.TargetCount };
}

cross::NGIBlendStateDescForEditor::NGIBlendStateDescForEditor(const NGIBlendStateDesc& base)
    : NGIBlendStateDesc{ base }
    , TargetBlendStateVector(std::begin(base.TargetBlendState), std::begin(base.TargetBlendState) + base.TargetCount)
{
}

void cross::NGIBlendStateDescForEditor::AdditionalDeserialize(const DeserializeNode& inNode, SerializeContext& context)
{
    Assert(TargetBlendStateVector.size() <= std::extent_v<decltype(TargetBlendState)>);
    TargetCount = static_cast<UInt32>(TargetBlendStateVector.size());
    std::copy(TargetBlendStateVector.begin(), TargetBlendStateVector.end(), std::begin(TargetBlendState));
}

void cross::NGIBlendStateDescForEditor::SetTargetBlendState(const NGITargetBlendStateDesc& state, UInt32 i)
{
    TargetCount = std::max(TargetCount, i + 1);
    TargetBlendState[i] = state;

    TargetBlendStateVector.resize(TargetCount);
    TargetBlendStateVector[i] = state;
}

bool cross::operator==(const NGIStencilOperation& a, const NGIStencilOperation& b)
{
    return
        a.StencilFailOp == b.StencilFailOp &&
        a.StencilDepthFailOp == b.StencilDepthFailOp &&
        a.StencilPassOp == b.StencilPassOp &&
        a.StencilCompareOp == b.StencilCompareOp;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIStencilOperation& desc)
{
    return stream <<
        desc.StencilFailOp <<
        desc.StencilDepthFailOp <<
        desc.StencilPassOp <<
        desc.StencilCompareOp;
}

bool cross::operator==(const NGIDepthStencilStateDesc& a, const NGIDepthStencilStateDesc& b)
{
    return
        a.EnableDepth == b.EnableDepth &&
        a.EnableDepthWrite == b.EnableDepthWrite &&
        a.DepthCompareOp == b.DepthCompareOp &&
        a.EnableStencil == b.EnableStencil &&
        a.StencilReadMask == b.StencilReadMask &&
        a.StencilWriteMask == b.StencilWriteMask &&
        a.FrontFace == b.FrontFace &&
        a.BackFace == b.BackFace;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIDepthStencilStateDesc& desc)
{
    return stream <<
        desc.EnableDepth <<
        desc.EnableDepthWrite <<
        desc.DepthCompareOp <<
        desc.EnableStencil <<
        desc.StencilReadMask <<
        desc.StencilWriteMask <<
        desc.FrontFace <<
        desc.BackFace;
}

bool cross::operator==(const NGISamplerDesc& a, const NGISamplerDesc& b)
{
    return
        a.Filter == b.Filter &&
        a.Bias == b.Bias &&
        a.WrapU == b.WrapU &&
        a.WrapV == b.WrapV &&
        a.WrapW == b.WrapW &&
        a.MaxAnisotropy == b.MaxAnisotropy &&
        a.ComparisonOp == b.ComparisonOp &&
        a.BorderColor == b.BorderColor &&
        a.MinLOD == b.MinLOD &&
        a.MaxLOD == b.MaxLOD;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGISamplerDesc& desc)
{
    return stream <<
        desc.Filter <<
        desc.Bias <<
        desc.WrapU <<
        desc.WrapV <<
        desc.WrapW <<
        desc.MaxAnisotropy <<
        desc.ComparisonOp <<
        desc.BorderColor <<
        desc.MinLOD <<
        desc.MaxLOD;
}

cross::NGIGraphicsPipelineState::NGIGraphicsPipelineState(const TDesc& desc, NGIPipelineStatePool* pipelineStatePool)
    : NGIObjectDesc{ desc }
    , NGIPipelineStatePoolChild{ pipelineStatePool }
{
    if (desc.ConstantDataSize)
    {
        mConstantData = std::make_unique<UInt8[]>(desc.ConstantDataSize);
        memcpy(mConstantData.get(), desc.ConstantData, desc.ConstantDataSize);
        mDesc.ConstantData = mConstantData.get();
    }
}

bool cross::operator==(const NGIGraphicsPipelineStateDesc& a, const NGIGraphicsPipelineStateDesc& b)
{
    return
        a.RenderPass == b.RenderPass &&
        a.Subpass == b.Subpass &&

        a.ProgramGUID == b.ProgramGUID &&

        a.InputLayoutGUID == b.InputLayoutGUID &&

        a.Topology == b.Topology &&
        a.RasterizationState == b.RasterizationState &&
        a.BlendState == b.BlendState &&
        a.DepthStencilState == b.DepthStencilState &&

        a.ConstantDataSize == b.ConstantDataSize &&
        memcmp(a.ConstantData, b.ConstantData, a.ConstantDataSize) == 0;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIGraphicsPipelineStateDesc& desc)
{
    Assert(desc.ConstantDataSize % 4 == 0);
    return stream <<
        desc.RenderPass <<
        desc.Subpass <<
        desc.ProgramGUID.high <<
        desc.ProgramGUID.low <<
        desc.InputLayoutGUID <<
        desc.Topology <<
#if NGI_GRAPHICS_PIPELINE_STATE_DESC_HASHING_OPTIMIZATION
        desc.StateAndConstantHash;
#else
        desc.RasterizationState <<
        desc.BlendState <<
        desc.DepthStencilState <<
        std::tuple{ reinterpret_cast<const UInt32*>(desc.ConstantData), desc.ConstantDataSize / 4 };
#endif
}

bool cross::operator==(const NGIComputePipelineStateDesc& a, const NGIComputePipelineStateDesc& b)
{
    return
        a.ProgramGUID == b.ProgramGUID &&
        memcmp(a.ConstantData, b.ConstantData, a.ConstantDataSize) == 0;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIComputePipelineStateDesc& desc)
{
    return stream <<
        desc.ProgramGUID.high <<
        desc.ProgramGUID.low <<
        std::tuple{ reinterpret_cast<const UInt32*>(desc.ConstantData), desc.ConstantDataSize / 4 };
}

bool cross::operator==(const NGIRayTracingPipelineStateDesc& a, const NGIRayTracingPipelineStateDesc& b)
{
    return a.ProgramGUID == b.ProgramGUID &&
           a.MaxRecursionDepth == b.MaxRecursionDepth &&
           a.MaxPayloadSize == b.MaxPayloadSize &&
           a.MaxAttributeSize == b.MaxAttributeSize &&
           a.ConstantDataSize == b.ConstantDataSize &&
           memcmp(a.ConstantData, b.ConstantData, a.ConstantDataSize) == 0;
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIRayTracingPipelineStateDesc& desc)
{
    Assert(desc.ConstantDataSize % 4 == 0);
    return stream <<
           desc.ProgramGUID.high <<
           desc.ProgramGUID.low <<
           desc.MaxRecursionDepth <<
           desc.MaxPayloadSize <<
           desc.MaxAttributeSize <<
           std::tuple{ reinterpret_cast<const UInt32*>(desc.ConstantData), desc.ConstantDataSize / 4 };
}

cross::NGIRayTracingPipelineState::NGIRayTracingPipelineState(const TDesc& Desc, NGIPipelineStatePool* Pool)
    : NGIObjectDesc{ Desc }
    , NGIPipelineStatePoolChild{ Pool }
{
    if (Desc.ConstantDataSize)
    {
        mConstantData = std::make_unique<UInt8[]>(Desc.ConstantDataSize);
        memcpy(mConstantData.get(), Desc.ConstantData, Desc.ConstantDataSize);
        mDesc.ConstantData = mConstantData.get();
    }
}

cross::SimpleStream& cross::operator<<(SimpleStream& stream, const NGIResourceBinding& binding)
{
    // don't using GetNativeHandle(), NativeHandle will be reused
    stream << binding.ID.mPropertyID << binding.ArrayOffset << binding.Type << binding.Flags;
    switch (binding.Type)
    {
     case NGIResourceBindingType::Sampler:
         stream << binding.Sampler->GetUniqueID();
         break;
     case NGIResourceBindingType::Texture:
         stream << binding.TextureView->GetUniqueID();
         break;
     case NGIResourceBindingType::Buffer:
         stream << binding.BufferView->GetUniqueID();
         stream << binding.BufferView->GetDesc().SizeInBytes;
         break;
     case NGIResourceBindingType::ConstBuffer:
         stream << binding.ConstBufferView.ConstBuffer->GetUniqueID();
         stream << binding.ConstBufferView.Range;
         break;
    case NGIResourceBindingType::AccelStruct:
        stream << binding.AccelStruct->GetUniqueID();
        break;
    default:
        Assert(false);
        break;
    }
    return stream;
}

bool cross::operator<(const NGIResourceBinding& a, const NGIResourceBinding& b)
{
    return a.ID != b.ID ? a.ID < b.ID : a.ArrayOffset < b.ArrayOffset;
}

size_t cross::NGIObjectDescHasher::operator()(const NGIResourceGroupLayoutDesc* desc) const
{
    auto dynamicSize = sizeof(NGIResourceGroupLayoutDesc);
    dynamicSize += desc->ResourceCount * sizeof(NGIResourceDesc);
    return Hash<1024>(*desc, dynamicSize);
}

size_t cross::NGIObjectDescHasher::operator()(const NGIPipelineLayoutDesc* desc) const
{
    auto dynamicSize = sizeof(NGIPipelineLayoutDesc);
    dynamicSize += desc->ConstantCount * sizeof(NGIVariableDesc);
    return Hash<sizeof(NGIPipelineLayoutDesc) + 1024>(*desc, dynamicSize);
}

size_t cross::NGIObjectDescHasher::operator()(const NGIGraphicsPipelineStateDesc* desc) const
{
    auto dataSize = sizeof(NGIGraphicsPipelineStateDesc);
    dataSize += desc->ConstantDataSize;
    return Hash<sizeof(NGIGraphicsPipelineStateDesc)>(*desc, dataSize);
}

size_t cross::NGIObjectDescHasher::operator()(const NGIComputePipelineStateDesc* desc) const
{
    auto dataSize = sizeof(NGIComputePipelineStateDesc);
    dataSize += desc->ConstantDataSize;
    return Hash<sizeof(NGIComputePipelineStateDesc) + 256>(*desc, dataSize);
}

void cross::NGIObject::IncreaseRefCount()
{
    if (mEnableRefCount)
    {
        mRefCount++;
    }
    else
    {
        assert(false);
        return;
    }
}

void cross::NGIObject::DecreaseRefCount()
{
    if (mEnableRefCount)
    {
        mRefCount--;
    }
    else
    {
        assert(false);
        return;
    }
}

cross::NGIComputePipelineState::NGIComputePipelineState(const TDesc& desc, NGIPipelineStatePool* pipelineStatePool)
    : NGIObjectDesc{ desc }
    , NGIPipelineStatePoolChild{ pipelineStatePool }
{
    if (desc.ConstantDataSize)
    {
        mConstantData = std::make_unique<UInt8[]>(desc.ConstantDataSize);
        memcpy(mConstantData.get(), desc.ConstantData, desc.ConstantDataSize);
        mDesc.ConstantData = mConstantData.get();
    }
}

void cross::NGIBundleCommandList::SetStencilRef(UInt32 stencilRef)
{
    Assert("Please implement your NGI backend");
}

cross::NGIResourceGroupLayout* cross::NGIDevice::CreateResourceGroupLayout(const NGIResourceGroupLayoutDesc& desc)
{
    return new NGIResourceGroupLayout{ desc, this, };
}

cross::NGIBuffer* cross::NGIDevice::CreateBuffer(const NGIBufferDesc& desc, const char* pDebugName)
{
    auto resource = CreateBufferImp(desc, pDebugName);
    mGpuResourcesStatistic->AddResource(resource);
    return resource;
}

cross::NGIBufferView* cross::NGIDevice::CreateBufferView(NGIBuffer* pBuffer, const NGIBufferViewDesc& desc)
{
    return CreateBufferViewImp(pBuffer, desc);
}

cross::NGITexture* cross::NGIDevice::CreateTexture(const NGITextureDesc& desc, const char* pDebugName, bool shared)
{
    auto resource = CreateTextureImp(desc, pDebugName, shared);
    mGpuResourcesStatistic->AddResource(resource);
    Assert(resource != nullptr);
    return resource;
}

cross::NGITextureView* cross::NGIDevice::CreateTextureView(cross::NGITexture* texture, const NGITextureViewDesc& desc)
{
    return CreateTextureViewImp(texture, desc);
}

cross::NGIStagingBuffer* cross::NGIDevice::CreateStagingBuffer(const NGIBufferDesc& desc)
{
    auto resource = CreateStagingBufferImp(desc);
    //mGpuResourcesStatistic->AddResource(resource);

    return resource;
}

cross::NGIBuffer::~NGIBuffer()
{
    mDevice->GetGpuResourceStatistics()->ReleaseResource(this);
}

cross::NGITexture::~NGITexture()
{
    mDevice->GetGpuResourceStatistics()->ReleaseResource(this);
}

cross::NGIPipelineStatePoolChild::NGIPipelineStatePoolChild(NGIPipelineStatePool* pipelineStatePool)
    : NGIDeviceChild{ pipelineStatePool->GetDevice()}
    , mPipelineStatePool{ pipelineStatePool }
{}

cross::NGITransientBufferManager* cross::NGIDevice::CreateTransientBufferManager(const NGITransientBufferManagerDesc& desc)
{
    return new ScratchBufferManager{desc, this};
}

cross::NGIUploadBuffer* cross::NGIDevice::CreateUploadBuffer(const NGIUploadBufferDesc& desc, NGIBufferUsage usage)
{
    return new UploadBuffer{ desc, this, usage};
}


cross::NGIResourceGroup::NGIResourceGroup(NGIResourceGroupLayout* layout, NGIResourceGroupPool* pool)
    : mLayout{ layout }
    , mPool{ pool }
{}

cross::NGIResourceGroup* cross::NGIResourceGroupPool::Create(NGIResourceGroupLayout* resourceGroupLayout, UInt32 numBindings, NGIResourceBinding* bindings)
{
    Assert(false);
    return nullptr;
}

cross::NGIResourceGroup* cross::NGIResourceGroupPool::CreateBindlessResourceGroup(NGIResourceGroupLayout* resourceGroupLayout, NGIBindlessResourceType BindlessResourceType, UInt32 maxBindlessResourceCount)
{
    Assert(false);
    return nullptr;
}

cross::NGIComputePipelineState* cross::NGIPipelineStatePool::CreateComputePipelineState(const NGIComputePipelineStateDesc& desc)
{
    Assert(false);
    return nullptr;
}

cross::NGIGraphicsPipelineState* cross::NGIPipelineStatePool::CreateGraphicsPipelineState(const NGIGraphicsPipelineStateDesc& desc)
{
    Assert(false);
    return nullptr;
}

cross::NGIRayTracingPipelineState* cross::NGIPipelineStatePool::CreateRayTracingPipelineState(const NGIRayTracingPipelineStateDesc& desc)
{
    Assert(false);
    return nullptr;
}

cross::StagingBufferWrap cross::NGITransientBufferManager::AllocateStaging(NGIBufferUsage usage, SizeType size, bool dedicated /*= false*/)
{
    NGIBufferDesc desc{size, usage};
    auto* buffer = mDevice->CreateStagingBuffer(desc);
    buffer->mEnableRefCount = false;
    auto* data = buffer->MapRange(usage, 0, size);
    AddPendingDeleteBuffer(std::move(std::make_tuple(GetCurrentLifeIndexID(), std::move(std::unique_ptr<NGIStagingBuffer>(buffer)))));

    return cross::StagingBufferWrap{buffer, 0, data, size};
}

cross::ScratchBufferWrap cross::NGITransientBufferManager::AllocateScratch(NGIBufferUsage usage, SizeType size, bool dedicated)
{
    NGIBufferDesc desc{size, usage};
    auto* buffer = mDevice->CreateStagingBuffer(desc);
    buffer->mEnableRefCount = false;
    auto* data = buffer->MapRange(usage, 0, size);
    AddPendingDeleteBuffer(std::move(std::make_tuple(GetCurrentLifeIndexID(), std::move(std::unique_ptr<NGIStagingBuffer>(buffer)))));

    return cross::ScratchBufferWrap{buffer, 0, data, size};
}

void cross::NGITransientBufferManager::OnBeginFrame(FrameParam* frameparam)
{
    SCOPED_CPU_TIMING(GroupRendering, __FUNCTION__);
    std::unique_lock writerLock(mPendingBufferMutex);
    // process const buffer to be deleted
    mPendingDeleteBuffers.MoveToOutofLife(GetCurrentLifeIndexID());
}

void cross::NGITransientBufferManager::EndFrame() {}

cross::NGICommandList* gImCmdList = nullptr;
cross::NGICommandList* cross::NGICommandList::GetImmediateCommandList()
{
    return gImCmdList;
}

void cross::NGICommandList::InitImmediateCommandList(cross::NGICommandList* imCmdList)
{
    gImCmdList = imCmdList;
}
cross::NGIDevice::NGIDevice()
{
    mGpuResourcesStatistic = std::make_shared<GPUResourceStatistics>();
}

void cross::AsyncUsedStageBuffer::Free()
{
    if (mStageBuffer && mManager)
    {
        mStageBuffer->AsyncUsing = false;
        mStageBuffer->lastAccessed = mManager->GetFrameId();
    }
}

cross::AsyncUsedStageBuffer::AsyncUsedStageBuffer(NGIStagingBuffer* inBuffer, SizeType inType, void* inRawPtr, const StageBuffer* inStageBuffer, NGITransientBufferManager* inManager)
{
    mBuffer = inBuffer;
    mType = inType;
    mRawPtr = inRawPtr;
    mStageBuffer = inStageBuffer;
    mManager = inManager;
    if (mStageBuffer && mManager)
    {
        mStageBuffer->AsyncUsing = true;
        mStageBuffer->lastAccessed = mManager->GetFrameId();
    }
}

void cross::AsyncUsedScratchBuffer::Free()
{
    if (mScratchBuffer && mManager)
    {
        mScratchBuffer->AsyncUsing = false;
        mScratchBuffer->lastAccessed = mManager->GetFrameId();
    }
}

cross::AsyncUsedScratchBuffer::AsyncUsedScratchBuffer(NGIBuffer* inBuffer, SizeType inType, void* inRawPtr, const ScratchBuffer* inScratchBuffer, NGITransientBufferManager* inManager)
{
    mBuffer = inBuffer;
    mType = inType;
    mRawPtr = inRawPtr;
    mScratchBuffer = inScratchBuffer;
    mManager = inManager;
    if (mScratchBuffer && mManager)
    {
        mScratchBuffer->AsyncUsing = true;
        mScratchBuffer->lastAccessed = mManager->GetFrameId();
    }
}
