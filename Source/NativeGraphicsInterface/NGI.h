#pragma once
#include "PlatformDefs.h"
#include "CrossBase/Log.h"
#include "CrossBase/Hash/Hash.h"
#include "CrossBase/Template/EnumClassFlags.h"
#include "CECommon/Common/RenderStateDefines.h"
#include "CECommon/Common/MeshDefines.h"
#include "CrossSchema/ShaderAsset_generated.h"
#include "CrossBase/String/NameID.h"
#include "CrossBase/uuid/CrossUUID.h"
#include "NGI.h"
#include "NGIResourceManager.h"


#include <atomic>
#include <type_traits>
#include <tuple>
#ifndef _MANAGED
#include <span>
#endif
#include <shared_mutex>

#include "Common/FrameContainer.h"
#include "Profiling/GPUProfiling.h"

#if CROSSENGINE_WIN && defined(NGI_DYNAMIC)
#    ifdef NativeGraphicsInterface_EXPORTS
#        define NGI_API __declspec(dllexport)
#    else
#        define NGI_API __declspec(dllimport)
#    endif
#else
#    ifdef __GNUC__
#        define NGI_API __attribute__((visibility("default")))
#    else
#        define NGI_API
#    endif
#endif

namespace cross {

enum struct NGIPlatform : UInt8
{
    Null,
    D3D12,
    Vulkan,
    OpenGLES3,
    Metal,
    WXGame,
};

#if CROSSENGINE_WIN
using NativeWindow = HWND;
#else
using NativeWindow = void*;
#endif

constexpr UInt32 MaxSupportedSubpasses = 4;
constexpr UInt32 MaxSupportedRenderTargets = MaxSupportedBlendTargets;
constexpr UInt32 MaxSupportedResourceGroups = 8;

enum struct GraphicsFormat : UInt32
{
    /*
     * only non-compressed and non-packed format, legacy formats are excluded
     * 3 bits for channel layout: R, RG, RGB, RGBA, BGR, BGRA
     * 2 bits for channel count: 8, 16, 32
     * 2 bits for channel type: SRGB, UNORM, SNORM, UINT, SINT, FLOAT
     */
    CombinationBit = 0b1u << 31u,

    CompLayoutMask = 0b111u << 28u,
    CompLayoutR = 0u << 28u,
    CompLayoutRG = 1u << 28u,
    CompLayoutRGB = 2u << 28u,
    CompLayoutRGBA = 3u << 28u,
    CompLayoutBGR = 4u << 28u,
    CompLayoutBGRA = 5u << 28u,

    CompSizeMask = 0b11u << 26u,
    CompSize8 = 0u << 26u,
    CompSize16 = 1u << 26u,
    CompSize32 = 2u << 26u,

    CompTypeMask = 0b111u << 23u,
    CompTypeSRGB = 0u << 23u,
    CompTypeUNorm = 1u << 23u,
    CompTypeSNorm = 2u << 23u,
    CompTypeUInt = 3u << 23u,
    CompTypeSInt = 4u << 23u,
    CompTypeSFloat = 5u << 23u,

    // sRGB formats
    R8_SRGB = CombinationBit | CompLayoutR | CompSize8 | CompTypeSRGB,
    R8G8_SRGB = CombinationBit | CompLayoutRG | CompSize8 | CompTypeSRGB,
    R8G8B8_SRGB = CombinationBit | CompLayoutRGB | CompSize8 | CompTypeSRGB,
    R8G8B8A8_SRGB = CombinationBit | CompLayoutRGBA | CompSize8 | CompTypeSRGB,

    // 8 bit integer formats
    R8_UNorm = CombinationBit | CompLayoutR | CompSize8 | CompTypeUNorm,
    R8G8_UNorm = CombinationBit | CompLayoutRG | CompSize8 | CompTypeUNorm,
    R8G8B8_UNorm = CombinationBit | CompLayoutRGB | CompSize8 | CompTypeUNorm,
    R8G8B8A8_UNorm = CombinationBit | CompLayoutRGBA | CompSize8 | CompTypeUNorm,
    R8_SNorm = CombinationBit | CompLayoutR | CompSize8 | CompTypeSNorm,
    R8G8_SNorm = CombinationBit | CompLayoutRG | CompSize8 | CompTypeSNorm,
    R8G8B8_SNorm = CombinationBit | CompLayoutRGB | CompSize8 | CompTypeSNorm,
    R8G8B8A8_SNorm = CombinationBit | CompLayoutRGBA | CompSize8 | CompTypeSNorm,
    R8_UInt = CombinationBit | CompLayoutR | CompSize8 | CompTypeUInt,
    R8G8_UInt = CombinationBit | CompLayoutRG | CompSize8 | CompTypeUInt,
    R8G8B8_UInt = CombinationBit | CompLayoutRGB | CompSize8 | CompTypeUInt,
    R8G8B8A8_UInt = CombinationBit | CompLayoutRGBA | CompSize8 | CompTypeUInt,
    R8_SInt = CombinationBit | CompLayoutR | CompSize8 | CompTypeSInt,
    R8G8_SInt = CombinationBit | CompLayoutRG | CompSize8 | CompTypeSInt,
    R8G8B8_SInt = CombinationBit | CompLayoutRGB | CompSize8 | CompTypeSInt,
    R8G8B8A8_SInt = CombinationBit | CompLayoutRGBA | CompSize8 | CompTypeSInt,

    // 16 bit integer formats
    R16_UNorm = CombinationBit | CompLayoutR | CompSize16 | CompTypeUNorm,
    R16G16_UNorm = CombinationBit | CompLayoutRG | CompSize16 | CompTypeUNorm,
    R16G16B16_UNorm = CombinationBit | CompLayoutRGB | CompSize16 | CompTypeUNorm,
    R16G16B16A16_UNorm = CombinationBit | CompLayoutRGBA | CompSize16 | CompTypeUNorm,
    R16_SNorm = CombinationBit | CompLayoutR | CompSize16 | CompTypeSNorm,
    R16G16_SNorm = CombinationBit | CompLayoutRG | CompSize16 | CompTypeSNorm,
    R16G16B16_SNorm = CombinationBit | CompLayoutRGB | CompSize16 | CompTypeSNorm,
    R16G16B16A16_SNorm = CombinationBit | CompLayoutRGBA | CompSize16 | CompTypeSNorm,
    R16_UInt = CombinationBit | CompLayoutR | CompSize16 | CompTypeUInt,
    R16G16_UInt = CombinationBit | CompLayoutRG | CompSize16 | CompTypeUInt,
    R16G16B16_UInt = CombinationBit | CompLayoutRGB | CompSize16 | CompTypeUInt,
    R16G16B16A16_UInt = CombinationBit | CompLayoutRGBA | CompSize16 | CompTypeUInt,
    R16_SInt = CombinationBit | CompLayoutR | CompSize16 | CompTypeSInt,
    R16G16_SInt = CombinationBit | CompLayoutRG | CompSize16 | CompTypeSInt,
    R16G16B16_SInt = CombinationBit | CompLayoutRGB | CompSize16 | CompTypeSInt,
    R16G16B16A16_SInt = CombinationBit | CompLayoutRGBA | CompSize16 | CompTypeSInt,

    // 32 bit integer formats
    R32_UInt = CombinationBit | CompLayoutR | CompSize32 | CompTypeUInt,
    R32G32_UInt = CombinationBit | CompLayoutRG | CompSize32 | CompTypeUInt,
    R32G32B32_UInt = CombinationBit | CompLayoutRGB | CompSize32 | CompTypeUInt,
    R32G32B32A32_UInt = CombinationBit | CompLayoutRGBA | CompSize32 | CompTypeUInt,
    R32_SInt = CombinationBit | CompLayoutR | CompSize32 | CompTypeSInt,
    R32G32_SInt = CombinationBit | CompLayoutRG | CompSize32 | CompTypeSInt,
    R32G32B32_SInt = CombinationBit | CompLayoutRGB | CompSize32 | CompTypeSInt,
    R32G32B32A32_SInt = CombinationBit | CompLayoutRGBA | CompSize32 | CompTypeSInt,

    // HDR formats
    R16_SFloat = CombinationBit | CompLayoutR | CompSize16 | CompTypeSFloat,
    R16G16_SFloat = CombinationBit | CompLayoutRG | CompSize16 | CompTypeSFloat,
    R16G16B16_SFloat = CombinationBit | CompLayoutRGB | CompSize16 | CompTypeSFloat,
    R16G16B16A16_SFloat = CombinationBit | CompLayoutRGBA | CompSize16 | CompTypeSFloat,
    R32_SFloat = CombinationBit | CompLayoutR | CompSize32 | CompTypeSFloat,
    R32G32_SFloat = CombinationBit | CompLayoutRG | CompSize32 | CompTypeSFloat,
    R32G32B32_SFloat = CombinationBit | CompLayoutRGB | CompSize32 | CompTypeSFloat,
    R32G32B32A32_SFloat = CombinationBit | CompLayoutRGBA | CompSize32 | CompTypeSFloat,

    // BGR formats
    B8G8R8_SRGB = CombinationBit | CompLayoutBGR | CompSize8 | CompTypeSRGB,
    B8G8R8A8_SRGB = CombinationBit | CompLayoutBGRA | CompSize8 | CompTypeSRGB,
    B8G8R8_UNorm = CombinationBit | CompLayoutBGR | CompSize8 | CompTypeUNorm,
    B8G8R8A8_UNorm = CombinationBit | CompLayoutBGRA | CompSize8 | CompTypeUNorm,
    B8G8R8_SNorm = CombinationBit | CompLayoutBGR | CompSize8 | CompTypeSNorm,
    B8G8R8A8_SNorm = CombinationBit | CompLayoutBGRA | CompSize8 | CompTypeSNorm,
    B8G8R8_UInt = CombinationBit | CompLayoutBGR | CompSize8 | CompTypeUInt,
    B8G8R8A8_UInt = CombinationBit | CompLayoutBGRA | CompSize8 | CompTypeUInt,
    B8G8R8_SInt = CombinationBit | CompLayoutBGR | CompSize8 | CompTypeSInt,
    B8G8R8A8_SInt = CombinationBit | CompLayoutBGRA | CompSize8 | CompTypeSInt,

    Unknown = 0,

    // 16 bit packed formats
    R4G4B4A4_UNormPack16,
    R5G6B5_UNormPack16,
    R5G5B5A1_UNormPack16,
    Pack16First = R4G4B4A4_UNormPack16,
    Pack16Last = R5G5B5A1_UNormPack16,

    // 32 bit Packed formats
    R9G9B9E5_UFloatPack32,
    R11G11B10_UFloatPack32,
    A2B10G10R10_UNormPack32,
    A2B10G10R10_UIntPack32,
    A2B10G10R10_SIntPack32,
    A2R10G10B10_UNormPack32,
    A2R10G10B10_UIntPack32,
    A2R10G10B10_SIntPack32,
    A2R10G10B10_XRSRGBPack32,
    A2R10G10B10_XRUNormPack32,
    R10G10B10_XRSRGBPack32,
    R10G10B10_XRUNormPack32,
    A10R10G10B10_XRSRGBPack32,
    A10R10G10B10_XRUNormPack32,
    Pack32First = R9G9B9E5_UFloatPack32,
    Pack32Last = A10R10G10B10_XRUNormPack32,

    // Depth Stencil for formats
    D16_UNorm,
    D24_UNorm_X8,
    D24_UNorm_S8_UInt,
    D32_SFloat,
    D32_SFloat_S8_UInt,
    S8_UInt,
    DepthStencilFirst = D16_UNorm,
    DepthStencilLast = S8_UInt,

    // BC Compression formats
    RGB_BC1_SRGB,
    RGB_BC1_UNorm,
    RGBA_BC1_SRGB,
    RGBA_BC1_UNorm,
    RGBA_BC2_SRGB,
    RGBA_BC2_UNorm,
    RGBA_BC3_SRGB,
    RGBA_BC3_UNorm,
    R_BC4_UNorm,
    R_BC4_SNorm,
    RG_BC5_UNorm,
    RG_BC5_SNorm,
    RGB_BC6H_UFloat,
    RGB_BC6H_SFloat,
    RGBA_BC7_SRGB,
    RGBA_BC7_UNorm,
    BCFirst = RGBA_BC1_SRGB,
    BCLast = RGBA_BC7_UNorm,

    // PVRTC
    RGB_PVRTC_2Bpp_SRGB,
    RGB_PVRTC_2Bpp_UNorm,
    RGB_PVRTC_4Bpp_SRGB,
    RGB_PVRTC_4Bpp_UNorm,
    RGBA_PVRTC_2Bpp_SRGB,
    RGBA_PVRTC_2Bpp_UNorm,
    RGBA_PVRTC_4Bpp_SRGB,
    RGBA_PVRTC_4Bpp_UNorm,
    PVRTCFirst = RGB_PVRTC_2Bpp_SRGB,
    PVRTCLast = RGBA_PVRTC_4Bpp_UNorm,

    // ETC
    RGB_ETC_UNorm,
    RGB_ETC2_UNorm = RGB_ETC_UNorm,
    RGB_ETC2_SRGB,
    RGB_A1_ETC2_SRGB,
    RGB_A1_ETC2_UNorm,
    RGBA_ETC2_SRGB,
    RGBA_ETC2_UNorm,
    ETCFirst = RGB_ETC_UNorm,
    ETCLast = RGBA_ETC2_UNorm,

    R_EAC_UNorm,
    R_EAC_SNorm,
    RG_EAC_UNorm,
    RG_EAC_SNorm,
    EACFirst = R_EAC_UNorm,
    EACLast = RG_EAC_SNorm,

    // ASTC
    RGBA_ASTC4X4_SRGB,
    RGBA_ASTC4X4_UNorm,
    RGBA_ASTC5X4_SRGB,
    RGBA_ASTC5X4_UNorm,
    RGBA_ASTC5X5_SRGB,
    RGBA_ASTC5X5_UNorm,
    RGBA_ASTC6X5_SRGB,
    RGBA_ASTC6X5_UNorm,
    RGBA_ASTC6X6_SRGB,
    RGBA_ASTC6X6_UNorm,
    RGBA_ASTC8X5_SRGB,
    RGBA_ASTC8X5_UNorm,
    RGBA_ASTC8X6_SRGB,
    RGBA_ASTC8X6_UNorm,
    RGBA_ASTC8X8_SRGB,
    RGBA_ASTC8X8_UNorm,
    RGBA_ASTC10X5_SRGB,
    RGBA_ASTC10X5_UNorm,
    RGBA_ASTC10X6_SRGB,
    RGBA_ASTC10X6_UNorm,
    RGBA_ASTC10X8_SRGB,
    RGBA_ASTC10X8_UNorm,
    RGBA_ASTC10X10_SRGB,
    RGBA_ASTC10X10_UNorm,
    RGBA_ASTC12X10_SRGB,
    RGBA_ASTC12X10_UNorm,
    RGBA_ASTC12X12_SRGB,
    RGBA_ASTC12X12_UNorm,
    ASTCFirst = RGBA_ASTC4X4_SRGB,
    ASTCLast = RGBA_ASTC12X12_UNorm,

    RGBA_ASTC4X4_UFloat,
    RGBA_ASTC5X4_UFloat,
    RGBA_ASTC5X5_UFloat,
    RGBA_ASTC6X5_UFloat,
    RGBA_ASTC6X6_UFloat,
    RGBA_ASTC8X5_UFloat,
    RGBA_ASTC8X6_UFloat,
    RGBA_ASTC8X8_UFloat,
    RGBA_ASTC10X5_UFloat,
    RGBA_ASTC10X6_UFloat,
    RGBA_ASTC10X8_UFloat,
    RGBA_ASTC10X10_UFloat,
    RGBA_ASTC12X10_UFloat,
    RGBA_ASTC12X12_UFloat,
    ASTCHDRFirst = RGBA_ASTC4X4_UFloat,
    ASTCHDRLast = RGBA_ASTC12X12_UFloat,

    // Video formats
    YUV2,
};

ENUM_CLASS_FLAGS(GraphicsFormat)

using RALRenderTextureFormat = GraphicsFormat;
using RALDepthStencilTextureFormat = GraphicsFormat;
using IndexBufferFormat = GraphicsFormat;
constexpr auto RTFormatInvalid = GraphicsFormat::Unknown;
constexpr auto RTFormatR16 = GraphicsFormat::R16_SFloat;
constexpr auto RTFormatRGBA8 = GraphicsFormat::R8G8B8A8_UNorm;
constexpr auto RTFormatR11G11B10F = GraphicsFormat::R11G11B10_UFloatPack32;
constexpr auto RTFormatRGBA16F = GraphicsFormat::R16G16B16A16_SFloat;
constexpr auto RTFormatR32F = GraphicsFormat::R32_SFloat;
constexpr auto DSFormatInvalid = GraphicsFormat::Unknown;
constexpr auto DSFormatD24S8 = GraphicsFormat::D24_UNorm_S8_UInt;
constexpr auto DSFormatD32S8 = GraphicsFormat::D32_SFloat_S8_UInt;
constexpr auto IndexFormat_UInt16 = GraphicsFormat::R16_UInt;
constexpr auto IndexFormat_UInt32 = GraphicsFormat::R32_UInt;

#define NGIResourceStateCombine(stage) stage##ConstantBufffer = stage##Bit | ConstantBufferBit, stage##ShaderResource = stage##Bit | ShaderResourceBit, stage##UnorderedAccess = stage##Bit | UnorderedAccessBit

// indicate state used in a pass, including a whole render pass or a compute pass or a copy pass
enum struct NGIResourceState : UInt64
{
    Undefined = 0,
    // special exclusive states
    Present = 1,
    HostRead = Present << 1,

    // states with implicit stage semantics
    VertexBuffer = HostRead << 1,
    IndexBuffer = VertexBuffer << 1,
    IndirectArgument = IndexBuffer << 1,
    CopyDst = IndirectArgument << 1,
    CopySrc = CopyDst << 1,
    ResolveDst = CopySrc << 1,
    ResolveSrc = ResolveDst << 1,

    SubpassRead = ResolveSrc << 1,
    TargetRead = SubpassRead << 1,
    TargetReadWrite = TargetRead << 1,
    TargetResolveSrc = TargetReadWrite << 1,
    TargetResolveDst = TargetResolveSrc << 1,

    NoEarlyTestBit = TargetResolveDst << 1,

    // states used with stages below
    ConstantBufferBit = NoEarlyTestBit << 1,
    ShaderResourceBit = ConstantBufferBit << 1,
    UnorderedAccessBit = ShaderResourceBit << 1,

    // stage bit, use with states above
    VertexShaderBit = UnorderedAccessBit << 1,
    HullShaderBit = VertexShaderBit << 1,
    DomainShaderBit = HullShaderBit << 1,
    GeometryShaderBit = DomainShaderBit << 1,
    PixelShaderBit = GeometryShaderBit << 1,
    ComputeShaderBit = PixelShaderBit << 1,
    RayTracingShaderBit = ComputeShaderBit << 1,

    // ray tracing stage bit
    AccelStructReadBit = RayTracingShaderBit << 1,
    AccelStructWrite = AccelStructReadBit << 1,
    AccelStructBuildInputBit = AccelStructWrite << 1,
    AccelStructBuildBLASBit = AccelStructBuildInputBit << 1,  // used for sync blas buffer state when building blas
    
    // predefined combination
    NGIResourceStateCombine(VertexShader),
    NGIResourceStateCombine(HullShader),
    NGIResourceStateCombine(DomainShader),
    NGIResourceStateCombine(GeometryShader),
    NGIResourceStateCombine(PixelShader),
    NGIResourceStateCombine(ComputeShader),
    NGIResourceStateCombine(RayTracingShader),

    GraphicsShaderStageBitMask = VertexShaderBit | HullShaderBit | DomainShaderBit | GeometryShaderBit | PixelShaderBit,
    ShaderStageBitMask = GraphicsShaderStageBitMask | ComputeShaderBit | RayTracingShaderBit,
};

ENUM_CLASS_FLAGS(NGIResourceState)

NGI_API std::string ToString(NGIResourceState state);

struct NGIDevice;
struct NGICommandQueue;

template<typename TDst, typename TSrc> inline TDst rhi_cast(TSrc p)
{
#if CROSSENGINE_RELEASE
    return static_cast<TDst>(p);
#else
    return dynamic_cast<TDst>(p);
#endif
}

struct NGIObject
{
    virtual ~NGIObject() = default;

    NGIObject(const char* pDebugName = "")
    {
        SetDebugName(pDebugName);
    }

    virtual UInt64 GetNativeHandle()
    {
        return reinterpret_cast<UInt64>(this);
    }

    virtual UInt64 GetMemoryNativeHandle()
    {
        return 0;
    }

    UInt32 GetRefCount() const
    {
        return mRefCount;
    }

    NGI_API void IncreaseRefCount();

    NGI_API void DecreaseRefCount();

    bool IsEnableReferenceCount() const
    {
        return mEnableRefCount;
    }

    virtual void SetDebugName(const char* pDebugName)
    {
#if !CROSSENGINE_DEPLOY
        mDebugName = pDebugName;
#endif
    }

    virtual const char* GetDebugName()
    {
#if !CROSSENGINE_DEPLOY
        return mDebugName.c_str();
#else
        return "";
#endif
    }

    bool mEnableRefCount{true};

private:
    std::atomic<UInt32> mRefCount{0};

#if !CROSSENGINE_DEPLOY
    std::string mDebugName;
#endif

    friend struct ScratchBuffer;
};


template<typename T> struct NGIUniqueID
{
    UInt32 ID = 0;

    friend bool operator==(const NGIUniqueID& a, const NGIUniqueID& b)
    {
        return a.ID == b.ID;
    }

    friend SimpleStream& operator<<(SimpleStream& stream, const NGIUniqueID& id)
    {
        return stream << id.ID;
    }
};

// object will have a season wide unique id
template<typename _TDerived> struct NGIUniqueObject
{
    NGIUniqueObject()
    {
        mUniqueID.ID = gCurrentID++;
    }

    NGIUniqueID<_TDerived> GetUniqueID()
    {
        return mUniqueID;
    }

    void AcquireNewUniqueID()
    {
        mUniqueID.ID = gCurrentID++;
    }

protected:
    NGIUniqueID<_TDerived> mUniqueID;

    static inline std::atomic<UInt32> gCurrentID = 1;
};

template<typename _TDesc> struct NGIObjectDesc
{
    using TDesc = _TDesc;

    NGIObjectDesc(const TDesc& desc)
        : mDesc{desc}
    {}

    const TDesc& GetDesc() const
    {
        return mDesc;
    }

protected:
    TDesc mDesc;
};

struct NGIDeviceChild
{
    NGIDeviceChild(NGIDevice* pDevice)
        : mDevice{pDevice}
    {}

    template<typename T = NGIDevice> T* GetDevice() const
    {
        return rhi_cast<T*>(mDevice);
    }
    void Test();

protected:
    NGIDevice* mDevice;
};

#define NGI_DESC_DEVICECHILD_CTOR(T)                                            \
    T(const TDesc& desc, NGIDevice* device, const char* pDebugName = "")       \
        : NGIObjectDesc{desc}                                                   \
        , NGIDeviceChild{device}                                                \
        , NGIObject(pDebugName)                                                 \
    {}                                                                          \
    using TDesc = typename NGIObjectDesc::TDesc;

enum struct NGIHeapType : UInt8
{
    // Used by normal GPU resource, may be mappable on UMA device, like android
    Default,
    // Used for uploading
    Upload,
    // Used for reading back
    Readback,
};

enum struct NGIBufferUsage : UInt32
{
    // use without view
    CopySrc = 1u,
    CopyDst = CopySrc << 1u,
    VertexBuffer = CopyDst << 1u,
    IndexBuffer = VertexBuffer << 1u,
    IndirectBuffer = IndexBuffer << 1u,
    // TODO(peterwjma): implement later
    // Clear = CopyDst << 1u,

    // use with view
    // VK texel uniform buffer(samplerBuffer, isamplerBuffer, usamplerBuffer), DX Buffer<float4>,Buffer<int4>,Buffer<uint4>
    TexelBuffer = IndirectBuffer << 1u,
    // VK texel storage buffer, DX RWBuffer<float4>, etc.
    RWTexelBuffer = TexelBuffer << 1u,
    // VK storage buffer, DX StructuredBuffer<...>
    StructuredBuffer = RWTexelBuffer << 1u,
    // VK storage buffer, DX RWStructuredBuffer<...>
    RWStructuredBuffer = StructuredBuffer << 1u,
    // VK storage buffer, DX ByteAddressBuffer
    ByteAddressBuffer = RWStructuredBuffer << 1u,
    // VK storage buffer, DX RWByteAddressBuffer
    RWByteAddressBuffer = ByteAddressBuffer << 1u,

    // VK uniform buffer, DX constant buffer
    ConstantBuffer = RWByteAddressBuffer << 1,
    // VK storage buffer, DX texture buffer
    TextureBuffer = ConstantBuffer << 1,

    DeviceAddressBit = TextureBuffer << 1,

    AccelStructBuffer = DeviceAddressBit << 1,
    AccelStructBuildInputBuffer = AccelStructBuffer << 1,
    ShaderBindingTableBuffer = AccelStructBuildInputBuffer << 1,

    RayTracingScratchBuffer = RWStructuredBuffer | DeviceAddressBit,
    RayTracingVertexBuffer = AccelStructBuildInputBuffer | VertexBuffer,
    RayTracingIndexBuffer = AccelStructBuildInputBuffer | IndexBuffer
};

ENUM_CLASS_FLAGS(NGIBufferUsage)

struct NGIBufferDesc
{
    SizeType Size;
    NGIBufferUsage Usage;
    // TODO(peterwjma): for future usage
    // bool SimultaneousAccess;

    // TODO(peterwjma): hidden beneath NGI
    // memory backing the buffer may be alised with other resource, and buffer may be distributted in different memory locations
    // bool MayAliased;

    float Priority = 0.5f;

    NGI_API friend bool operator==(const NGIBufferDesc& a, const NGIBufferDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIBufferDesc& desc);
};

struct NGIBuffer : public NGIObject, NGIUniqueObject<NGIBuffer>, NGIObjectDesc<NGIBufferDesc>, NGIDeviceChild
{
    NGI_DESC_DEVICECHILD_CTOR(NGIBuffer)

    NGI_API ~NGIBuffer() override;

    virtual bool Mappable() = 0;

    virtual void* Map() = 0;

    virtual void Unmap() = 0;

    virtual SizeType AlignOffset(NGIBufferUsage usage, SizeType offset, SizeType range) = 0;

    virtual UInt64 GetSize() const {
        return GetDesc().Size;
    }

    //@dangochen: Currently it's only used for gles.
    // Because gl can't know buffer type from a simple void*.
    // So I use offset and range as a key to index the real gl buffer
    // Return value is <GLES3RealBuffer, OffsetInRealBuffer>
    virtual std::pair<void*, SizeType> GetUnderlyingBuffer(SizeType offset)
    {
        Assert(0);
        return {};
    }
};

struct NGIBufferViewDesc
{
    NGIBufferUsage Usage;
    SizeType BufferLocation;
    SizeType SizeInBytes;
    // Used by Texel Buffer, FormatNone for other usage
    GraphicsFormat Format;
    // Used by Structured Buffer, 0 for other usage
    SizeType StructureByteStride;

    NGI_API friend bool operator==(const NGIBufferViewDesc& a, const NGIBufferViewDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIBufferViewDesc& desc);
};

struct NGIBufferView : public NGIObject, NGIUniqueObject<NGIBufferView>, NGIObjectDesc<NGIBufferViewDesc>, NGIDeviceChild
{
    using TDesc = typename NGIBufferView::TDesc;

    NGIBufferView(NGIBuffer* buffer, const TDesc& desc, NGIDevice* device)
        : NGIObjectDesc{ desc }
        , NGIDeviceChild{ device }
        , mBuffer{ buffer }
    {}

    auto GetBuffer()
    {
        return mBuffer;
    }

protected:
    NGIBuffer* mBuffer;
};

enum struct NGITextureUsage : UInt8
{
    // use without view
    CopySrc = 1u,
    CopyDst = CopySrc << 1u,
    // TODO(peterwjma): implement later
    // Clear = CopyDst << 1u,

    // use with view
    RenderTarget = CopyDst << 1u,
    DepthStencil = RenderTarget << 1u,
    UnorderedAccess = DepthStencil << 1u,
    ShaderResource = UnorderedAccess << 1u,
    SubpassInput = ShaderResource << 1u,
};

ENUM_CLASS_FLAGS(NGITextureUsage)

enum struct NGITextureAspect : UInt8
{
    Color = 1u,
    Depth = Color << 1u,
    Stencil = Depth << 1u,
};

ENUM_CLASS_FLAGS(NGITextureAspect)

enum struct NGITextureType : UInt8
{
    Unknown,
    Texture1D,
    Texture1DArray,
    Texture2D,
    Texture2DArray,
    TextureCube,
    TextureCubeArray,
    Texture3D,
};

constexpr auto GPUTextureCube = NGITextureType::TextureCube;

/*
 * Texture can only be Allocateated on default heap temporarly
 */
struct NGITextureDesc
{
    GraphicsFormat Format;
    NGITextureType Dimension;
    UInt32 MipCount;
    UInt32 SampleCount;
    UInt32 Width;
    UInt32 Height;
    UInt32 Depth;
    // 2d face counts, 6 for cubemap, 6xn for cubemaparray where n was array size
    UInt32 ArraySize;

    NGITextureUsage Usage;
    // texture view and texture may have different format
    bool MutableFormat;
    // can create view for each subresource or a different type of view (create 2D texture/array view from 3D texture, e.g.)
    bool SeparateView;

    // TODO(peterwjma): for future usage
    // can be accessed by multiple command queue at same time
    // bool SimultaneousAccess;

    // TODO(peterwjma): hidden beneath NGI
    // memory backing the texture may be alised with other resource, and texture may be distributted in different memory locations
    // bool MayAliased;

    NGI_API friend bool operator==(const NGITextureDesc& a, const NGITextureDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGITextureDesc& desc);
};

struct NGITexture : public NGIObject, NGIUniqueObject<NGITexture>, NGIObjectDesc<NGITextureDesc>, NGIDeviceChild
{
    NGI_DESC_DEVICECHILD_CTOR(NGITexture)

    NGI_API ~NGITexture() override;

    virtual UInt64 GetSize() const = 0;
    virtual bool ManagedBySwapchain() = 0;
};

struct NGITextureSubRange
{
    NGITextureAspect Aspect;
    UInt32 MostDetailedMip;
    UInt32 MipLevels;
    UInt32 FirstArraySlice;
    UInt32 ArraySize;

    NGI_API friend bool operator==(const NGITextureSubRange& a, const NGITextureSubRange& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGITextureSubRange& desc);
};

struct NGITextureViewDesc
{
    NGITextureUsage Usage;
    GraphicsFormat Format;
    NGITextureType ViewDimension;
    NGITextureSubRange SubRange;

    NGI_API friend bool operator==(const NGITextureViewDesc& a, const NGITextureViewDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGITextureViewDesc& desc);
};

struct NGITextureView : NGIObject, NGIUniqueObject<NGITextureView>, NGIObjectDesc<NGITextureViewDesc>, NGIDeviceChild
{
    using TDesc = typename NGITextureView::TDesc;

    NGITextureView(NGITexture* texture, const TDesc& desc, NGIDevice* device)
        : NGIObjectDesc{desc}
        , NGIDeviceChild{device}
        , mTexture{texture}
    {}

    auto GetTexture()
    {
        return mTexture;
    }

protected:
    NGITexture* mTexture;
};

using NGIColorAttachment = NGITextureView;
using NGIDepthStencilAttachment = NGITextureView;

using NGIBlendOp = BlendOp;

struct NGI_API CEMeta(Cli) NGITargetBlendStateDesc
{
    CE_Serialize_Deserialize;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    bool EnableBlend;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    bool EnableLogicOp;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    BlendFactor SrcBlend;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    BlendFactor DestBlend;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    NGIBlendOp BlendOp;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    BlendFactor SrcBlendAlpha;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    BlendFactor DestBlendAlpha;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    NGIBlendOp BlendOpAlpha;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    LogicOp LogicOp;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    ColorMask WriteMask;

    NGI_API friend bool operator==(const NGITargetBlendStateDesc& a, const NGITargetBlendStateDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGITargetBlendStateDesc& desc);
};


struct NGI_API CEMeta(Cli) NGIBlendStateDesc
{
    CE_Serialize_Deserialize;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    bool EnableAlphaToCoverage;
    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    bool EnableIndependentBlend;

    UInt32 TargetCount;
    NGITargetBlendStateDesc TargetBlendState[MaxSupportedRenderTargets];

    NGI_API friend bool operator==(const NGIBlendStateDesc& a, const NGIBlendStateDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIBlendStateDesc& desc);
};

//For Editor
struct NGI_API CEMeta(Cli) NGIBlendStateDescForEditor : public NGIBlendStateDesc
{
    CE_Serialize_Deserialize;

    NGIBlendStateDescForEditor() = default;

    NGIBlendStateDescForEditor(const NGIBlendStateDesc& base);

    CEProperty(EditorPropertyInfo(Category = "Advanced Material", PropertyType = "List", ChildPropertyType = "Struct", DisplayName = "TargetBlendState"))
    std::vector<NGITargetBlendStateDesc> TargetBlendStateVector;

    CEMeta(AdditionalDeserialize)
    void AdditionalDeserialize(const DeserializeNode& inNode, SerializeContext& context);

    void SetTargetBlendState(const NGITargetBlendStateDesc& state, UInt32 i);
};

struct NGI_API CEMeta(Cli) NGIRasterizationStateDesc
{
    CE_Serialize_Deserialize;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    FillMode FillMode;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    CullMode CullMode;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    FaceOrder FaceOrder;

    // DepthClamp was always enabled, DepthClip was ignored by now
    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    bool EnableDepthClip;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    bool EnableAntialiasedLine;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    bool EnableDepthBias;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    SInt16 DepthBias;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    float SlopeScaledDepthBias;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    float DepthBiasClamp;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    UInt32 ForcedSampleCount;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    float LineWidth;

    // Conservative Rasterization disable or not
    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    RasterizationMode RasterMode;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    UInt32 RasterOverestimationSize;

    NGI_API friend bool operator==(const NGIRasterizationStateDesc& a, const NGIRasterizationStateDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIRasterizationStateDesc& desc);
};

struct NGI_API CEMeta(Cli) NGIDynamicStateDesc
{
    CE_Serialize_Deserialize;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    UInt8 StencilReference;

    NGI_API friend bool operator==(const NGIDynamicStateDesc& a, const NGIDynamicStateDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIDynamicStateDesc& desc);
};

using NGIComparisonOp = ComparisonOp;

struct NGI_API CEMeta(Cli) NGIStencilOperation
{
    CE_Serialize_Deserialize;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    StencilOp StencilFailOp;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    StencilOp StencilDepthFailOp;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    StencilOp StencilPassOp;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    NGIComparisonOp StencilCompareOp;

    NGI_API friend bool operator==(const NGIStencilOperation& a, const NGIStencilOperation& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIStencilOperation& desc);
};

struct NGI_API CEMeta(Cli) NGIDepthStencilStateDesc
{
    CE_Serialize_Deserialize;

    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    bool EnableDepth;
    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    bool EnableDepthWrite;
    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    NGIComparisonOp DepthCompareOp;
    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    bool EnableStencil;
    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    UInt8 StencilReadMask;
    CEProperty(EditorPropertyInfo(Category = "Advanced Material"))
    UInt8 StencilWriteMask;
    CEProperty(EditorPropertyInfo(Category = "Advanced Material", PropertyType = "Struct"))
    NGIStencilOperation FrontFace;
    CEProperty(EditorPropertyInfo(Category = "Advanced Material", PropertyType = "Struct"))
    NGIStencilOperation BackFace;

    NGI_API friend bool operator==(const NGIDepthStencilStateDesc& a, const NGIDepthStencilStateDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIDepthStencilStateDesc& desc);
};

enum struct NGIFilter : UInt8
{
    Unknown,
    MinMagMipPoint,
    MinMagPointMipLinear,
    MinPointMagLinearMipPoint,
    MinPointMagMipLinear,
    MinLinearMagMipPoint,
    MinLinearMagPointMipLinear,
    MinMagLinearMipPoint,
    MinMagMipLinear,
    Anisotropic,
};

enum struct NGITextureAddressMode : UInt8
{
    Unknown,
    Wrap,
    Mirror,
    Clamp,
    Border,
    MirrorOnce,
};

enum struct NGIBorderColor : UInt8
{
    Unknown,
    FloatTransparentBlack,
    IntTransparentBlack,
    FloatOpaqueBlack,
    IntOpaqueBlack,
    FloatOpaqueWhite,
    IntOpaqueWhite,
};

struct NGISamplerDesc
{
    NGIFilter Filter;
    float Bias;
    NGITextureAddressMode WrapU;
    NGITextureAddressMode WrapV;
    NGITextureAddressMode WrapW;
    UInt8 MaxAnisotropy;
    NGIComparisonOp ComparisonOp;
    NGIBorderColor BorderColor;
    float MinLOD;
    float MaxLOD;

    NGI_API friend bool operator==(const NGISamplerDesc& a, const NGISamplerDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGISamplerDesc& desc);
};

enum struct NGILoadOp : UInt8
{
    DontCare,
    Load,
    Clear,
};

enum struct NGIStoreOp : UInt8
{
    DontCare = 0u,
    Store = 1u,
    Resolve = 2u,
    StoreAndResolve = Store | Resolve,
};

ENUM_CLASS_FLAGS(NGIStoreOp)

struct NGIRenderPassTargetDesc
{
    GraphicsFormat Format;
    NGILoadOp LoadOp;
    NGIStoreOp StoreOp;

    NGI_API friend bool operator==(const NGIRenderPassTargetDesc& a, const NGIRenderPassTargetDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIRenderPassTargetDesc& desc);
};

enum struct NGIRenderPassTargetIndex : UInt8
{
    Target0,
    Target1,
    Target2,
    Target3,
    Target4,
    Target5,
    Target6,
    Target7,
    TargetDepth,
    TargetStencil,
};

/*
 * can't both read same aspect of an attachment by input attachment and write by color attachment or depth stencil at same time
 */
struct NGISubpassDesc
{
    UInt32 InputAttachmentCount;
    NGIRenderPassTargetIndex InputAttachments[MaxSupportedRenderTargets];
    UInt32 ColorAttachmentCount;
    NGIRenderPassTargetIndex ColorAttachments[MaxSupportedRenderTargets];
    bool NeedDepthStencil;
    bool DepthReadOnly;
    bool StencilReadOnly;

    NGI_API friend bool operator==(const NGISubpassDesc& a, const NGISubpassDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGISubpassDesc& desc);
};

enum struct NGIResolveType : UInt8
{
    DontResolve = 0,
    SampleZero = 1,
    Average = 2,
    Min = 4,
    Max = 8,
};

/*
 * TextureViews in RenderPass was arranged below:
 *	0. Color0, used as NGIRenderPassTargetIndex::Target0
 *	1. Color1, used as NGIRenderPassTargetIndex::Target1
 *	2. Color2, used as NGIRenderPassTargetIndex::Target2
 *	3. ...
 *	4. DepthStencil, used as NGIRenderPassTargetIndex::TargetDepth or NGIRenderPassTargetIndex::TargetStencil
 * format of resolve target must be same with format of resolve source, currently resolve depth stencil was not allowed in Vulkan1.1
 */
struct NGIRenderPassDesc
{
    UInt32 SampleCount;

    UInt32 ColorTargetCount;
    NGIRenderPassTargetDesc ColorTargets[MaxSupportedRenderTargets];

    bool HasDepthStencil;
    GraphicsFormat DepthStencilFormat;

    NGILoadOp DepthLoadOp;
    NGIStoreOp DepthStoreOp;
    NGIResolveType DepthResolveType;

    NGILoadOp StencilLoadOp;
    NGIStoreOp StencilStoreOp;
    NGIResolveType StencilResolveType;

    UInt32 SubpassCount;
    NGISubpassDesc Subpasses[MaxSupportedSubpasses];

    // TODO(peterwjma): external dependencies

    NGI_API friend bool operator==(const NGIRenderPassDesc& a, const NGIRenderPassDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIRenderPassDesc& desc);
};

struct NGIRenderPass : public NGIObject, NGIObjectDesc<NGIRenderPassDesc>, NGIDeviceChild
{
    NGI_DESC_DEVICECHILD_CTOR(NGIRenderPass)
};

struct NGIRenderPassTarget
{
    NGITextureView* Target;
    NGITextureView* ResolveTarget;
};

/*
 * see comments of NGIRenderPassCreateDesc
 */
struct NGIFramebufferDesc
{
    NGIRenderPass* RenderPass;
    UInt32 Width;
    UInt32 Height;
    UInt32 LayerCount;
    NGIRenderPassTarget ColorTargets[MaxSupportedRenderTargets];
    NGIRenderPassTarget DepthStencilTarget;

    NGI_API friend bool operator==(const NGIFramebufferDesc& a, const NGIFramebufferDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIFramebufferDesc& desc);
};

struct NGIFramebuffer : public NGIObject, NGIObjectDesc<NGIFramebufferDesc>, NGIDeviceChild
{
    NGI_DESC_DEVICECHILD_CTOR(NGIFramebuffer)
};

struct NGISampler : public NGIObject, NGIUniqueObject<NGISampler>, NGIObjectDesc<NGISamplerDesc>, NGIDeviceChild
{
    NGI_DESC_DEVICECHILD_CTOR(NGISampler)
};

struct NGIStagingBuffer : public NGIBuffer
{
    constexpr static auto DynamicBufferUsage = NGIBufferUsage::ConstantBuffer | NGIBufferUsage::TextureBuffer | NGIBufferUsage::VertexBuffer | NGIBufferUsage::IndexBuffer | NGIBufferUsage::IndirectBuffer | NGIBufferUsage::TexelBuffer |
                                               NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst | NGIBufferUsage::CopySrc | NGIBufferUsage::ShaderBindingTableBuffer;
    constexpr static auto UploadUsage = NGIBufferUsage::CopySrc;
    constexpr static auto ReadbackUsage = NGIBufferUsage::CopyDst;

    using NGIBuffer::NGIBuffer;

    virtual void Allocation(const NGIBufferDesc & desc) {};

    virtual void* MapRange(NGIBufferUsage usage, SizeType Offset, SizeType Range) = 0;

    virtual void UnmapRange(SizeType Offset, SizeType Range) = 0;

    virtual void FreeRange(SizeType Offset, SizeType Range) = 0;

private:
    bool Mappable() override
    {
        return false;
    }

    void* Map() override
    {
        return nullptr;
    }

    void Unmap() override {}
};

struct NGIResourceDesc
{
    NameID ID;
    CrossSchema::ShaderResourceType Type;
    UInt32 Space;
    UInt32 Index;
    UInt32 ArraySize;
    UInt32 StageMask;

    NGI_API friend bool operator==(const NGIResourceDesc& a, const NGIResourceDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIResourceDesc& desc);
    NGI_API friend bool operator<(const NGIResourceDesc& a, const NGIResourceDesc& b);
};

struct NGIVariableDesc
{
    NameID Name;
    CrossSchema::ShaderVariableType Type;
    UInt32 RowCount;
    UInt32 ColCount;
    UInt32 Index;
    UInt32 Offset;
    UInt32 Size;
    UInt32 ArraySize;
    UInt32 StageMask;

    NGI_API friend bool operator==(const NGIVariableDesc& a, const NGIVariableDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIVariableDesc& desc);
    NGI_API friend bool operator<(const NGIVariableDesc& a, const NGIVariableDesc& b)
    {
        return a.Offset < b.Offset;
    }
};

struct NGIResourceGroupLayoutDesc
{
    UInt32 ResourceCount;
    NGIResourceDesc* Resources;

    NGI_API friend bool operator==(const NGIResourceGroupLayoutDesc& a, const NGIResourceGroupLayoutDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIResourceGroupLayoutDesc& desc);
};

struct NGIResourceGroupLayout : public NGIObject, NGIUniqueObject<NGIResourceGroupLayout>, NGIObjectDesc<NGIResourceGroupLayoutDesc>, NGIDeviceChild
{
    NGIResourceGroupLayout(const NGIResourceGroupLayoutDesc& desc, NGIDevice* device);

    using TDesc = typename NGIObjectDesc::TDesc;

protected:
    std::vector<NGIResourceDesc> mResources;
};

// Resources and Constants must be sorted
struct NGIPipelineLayoutDesc
{
    UInt32 ResourceGroupCount;
    NGIResourceGroupLayout* ResourceGroupLayouts[MaxSupportedResourceGroups];

    UInt32 ConstantCount;
    NGIVariableDesc* Constants;
    UInt32 ConstantSpace;
    UInt32 ConstantIndex;

    NGI_API friend bool operator==(const NGIPipelineLayoutDesc& a, const NGIPipelineLayoutDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIPipelineLayoutDesc& desc);
};

struct NGIPipelineLayout : public NGIObject, NGIObjectDesc<NGIPipelineLayoutDesc>, NGIDeviceChild
{
    NGIPipelineLayout(const NGIPipelineLayoutDesc& desc, NGIDevice* device);

    using TDesc = typename NGIObjectDesc::TDesc;

protected:
    std::vector<NGIResourceDesc> mResources;

    std::vector<NGIVariableDesc> mConstants;
};

struct NGIVertexStream
{
    UInt32 Stride;
    UInt16 InstanceStepRate;
    VertexFrequency Frequency;

    NGI_API friend bool operator==(const NGIVertexStream& a, const NGIVertexStream& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIVertexStream& desc);
};

struct NGIVertexChannel
{
    // converted from VertexChannel for D3D12, index for gl/vulkan/metal
    UInt32 NameOrIndex;
    VertexFormat Format;
    UInt32 Offset;
    UInt8 StreamIndex;

    NGI_API friend bool operator==(const NGIVertexChannel& a, const NGIVertexChannel& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIVertexChannel& desc);
};

struct NGIInputLayoutDesc
{
    UInt32 StreamCount;
    NGIVertexStream Streams[MaxVertexStreams];
    UInt32 ChannelCount;
    NGIVertexChannel Channels[MaxVertexChannelCount];

    NGI_API friend bool operator==(const NGIInputLayoutDesc& a, const NGIInputLayoutDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIInputLayoutDesc& desc);
};

struct NGIShaderCodeDesc
{
    const void* Data;
    UInt32 ByteSize;
    const char* EntryPoint;
    bool DebugSymbol;
};

struct NGIVertexAttribute
{
    VertexChannel ChannelName;
    UInt32 Location;
};

struct NGICombinedSamplerDesc
{
    const char* Name;
    const char* TextureName;
    const char* SamplerName;
};

struct NGIGraphicsProgramDesc
{
    CrossSchema::ShaderVersion ShaderVersion;

    NGIShaderCodeDesc VertexShader;
    NGIShaderCodeDesc HullShader;
    NGIShaderCodeDesc DomainShader;
    NGIShaderCodeDesc GeometryShader;
    NGIShaderCodeDesc PixelShader;

    UInt32 VertexAttributeCount;
    NGIVertexAttribute VertexAttributes[MaxVertexChannelCount];

    UInt32 CombinedSamplerCount;
    NGICombinedSamplerDesc* CombinedSamplers;

    const char* FilePath = nullptr;
};

enum class NGIAccelStructType : UInt8
{
    BottomLevel = 0x0,
    TopLevel,
};

enum class NGIGeometryType : UInt8
{
    Triangle = 0x0,
    AABB,
    Sphere,
};

enum class NGIGeometryFlag : UInt8
{
    None = 0x0,
    Opaque = 0x1,
    NoDuplicateAnyHitInvocation = Opaque << 1,
};

struct NGIGeometryTriangle
{
    NGIBuffer* VertexBuffer = nullptr;
    NGIBuffer* IndexBuffer = nullptr;
    NGIBuffer* TransformBuffer = nullptr;
    GraphicsFormat VertexFormat = GraphicsFormat::Unknown;
    GraphicsFormat IndexFormat = GraphicsFormat::Unknown;

    uint32_t VertexCount = 0;
    uint32_t IndexCount = 0;
    uint32_t VertexStride = 0;
    uint64_t VertexOffset = 0;
    uint64_t IndexOffset = 0;
};

// TODO(scolu)
struct NGIGeometryAABB
{

};

// TODO(scolu)
struct NGIGeometrySphere
{

};

struct NGIGeometryDesc
{
    union GeometryUnion
    {
        NGIGeometryTriangle Triangle;
        NGIGeometryAABB AABB;
        NGIGeometrySphere Sphere;
    } GeometryData;

    bool UseTransform = false;
    float Transform[12];  // Row major in vulkan

    NGIGeometryFlag Flag = NGIGeometryFlag::None;
    NGIGeometryType GeometryType = NGIGeometryType::Triangle;
};

enum class NGIInstanceFlag : UInt8
{
    None = 0x0,
    TriangleCullDisable = 0x1,
    TriangleFrontCounterclockwise = TriangleCullDisable << 1,
    ForceOpaque = TriangleFrontCounterclockwise << 1,
    ForceNoneOpaque = ForceOpaque << 1,
};

ENUM_CLASS_FLAGS(NGIInstanceFlag)

struct NGIAccelStruct;

struct NGIInstanceDesc
{
    float Transform[12];
    uint32_t InstanceID : 24;
    uint32_t InstanceMask : 8;
    uint32_t InstanceContribToHitGroupIndex : 24;
    NGIInstanceFlag Flag : 8;
    NGIAccelStruct* BottomLevelAS;
};

enum class NGIAccelStructBuildFlag
{
    None = 0x0,
    AllowUpdate = 0x1,
    AllowCompaction = AllowUpdate << 1,
    PreferFastTrace = AllowCompaction << 1,
    PreferFastBuild = PreferFastTrace << 1,
    MinimizeMemory = PreferFastBuild << 1,
    PerformUpdate = MinimizeMemory << 1,
};

ENUM_CLASS_FLAGS(NGIAccelStructBuildFlag)

struct NGIAccelStructDesc
{
    std::string DebugName = "Unknown";
    bool IsTopLevel = false;
    size_t TopLevelMaxInstance = 0;  // TLAS only
    std::vector<NGIGeometryDesc> BottomLevelGeometries;  // BLAS only
    NGIAccelStructBuildFlag BuildFlag = NGIAccelStructBuildFlag::None;
};

struct NGIAccelStructSize
{
    UInt64 AccelStructSize = 0;
    UInt64 BuildScratchSize = 0;
    UInt64 UpdateScratchSize = 0;
};

struct NGIAccelStruct : public NGIObject, NGIUniqueObject<NGIAccelStruct>, NGIObjectDesc<NGIAccelStructDesc>, NGIDeviceChild
{
    NGI_DESC_DEVICECHILD_CTOR(NGIAccelStruct)

    NGI_API ~NGIAccelStruct() override {}

    NGIAccelStructSize mSizeInfo;

    virtual UInt32 GetTopLevelUploadBufferSize() const
    {
        Assert(false);
        return 0;
    }

    virtual UInt32 GetTopLevelMaxInstanceCount() const
    {
        Assert(false);
        return 0;
    }
};

struct NGIGraphicsPipelineStateDesc
{
    // RenderPass was stored permanentelly, use it as part of key directly
    NGIRenderPass* RenderPass;
    UInt32 Subpass;

    // each shader variant has a global unique GUID, currently it's generated when compiling shader
    CrossUUID ProgramGUID;
    // stored in Fx, each pass holds one
    const NGIGraphicsProgramDesc* Program;
    NGIPipelineLayout* PipelineLayout;

    // mesh GUID
    UInt64 InputLayoutGUID;
    // stored in GeometryPacket
    const InputLayoutDesc* InputLayout;

    // stored in material
    PrimitiveTopology Topology;
#if NGI_GRAPHICS_PIPELINE_STATE_DESC_HASHING_OPTIMIZATION
    UInt64 StateAndConstantHash;
#endif
    NGIRasterizationStateDesc RasterizationState;
    NGIBlendStateDesc BlendState;
    NGIDepthStencilStateDesc DepthStencilState;

    // specialization constants are also part of hash key
    UInt32 ConstantDataSize;
    const void* ConstantData;

    NGI_API friend bool operator==(const NGIGraphicsPipelineStateDesc& a, const NGIGraphicsPipelineStateDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIGraphicsPipelineStateDesc& desc);
};

struct NGIPipelineStatePool;

struct NGIPipelineStatePoolChild : public NGIDeviceChild
{
    NGIPipelineStatePoolChild(NGIPipelineStatePool* pipelineStatePool);

protected:
    NGIPipelineStatePool* mPipelineStatePool;
};

struct NGIGraphicsPipelineState : public NGIObject, NGIObjectDesc<NGIGraphicsPipelineStateDesc>, NGIPipelineStatePoolChild
{
    using TDesc = typename NGIObjectDesc::TDesc;

    NGIGraphicsPipelineState(const TDesc& desc, NGIPipelineStatePool* pipelineStatePool);

protected:
    std::unique_ptr<UInt8[]> mConstantData;
};

struct NGIComputeProgramDesc
{
    CrossSchema::ShaderVersion ShaderVersion;
    NGIShaderCodeDesc ComputeShader;
    UInt32 CombinedSamplerCount;
    NGICombinedSamplerDesc* CombinedSamplers;
    UInt32 DispatchGroupSize[3];

    const char* FilePath = nullptr;
};
struct StageBuffer;
struct ScratchBuffer;
struct NGITransientBufferManager;
struct NGIStagingBuffer;
struct NGIBuffer;
class NGI_API AsyncUsedStageBuffer
{
public:
    ~AsyncUsedStageBuffer() = default;
    AsyncUsedStageBuffer(const AsyncUsedStageBuffer&) = default;

    std::tuple<NGIStagingBuffer*, SizeType, void*> Raw() const { return {mBuffer, mType, mRawPtr}; }
    void Free();

private:
    AsyncUsedStageBuffer() = default;
    AsyncUsedStageBuffer(NGIStagingBuffer* inBuffer, SizeType inType, void* inRawPtr, const StageBuffer* inStageBuffer, NGITransientBufferManager* inManager);

    NGIStagingBuffer* mBuffer;
    SizeType mType;
    void* mRawPtr;
    const StageBuffer* mStageBuffer;
    NGITransientBufferManager* mManager;
    friend struct NGITransientBufferManager;
    friend struct StageBufferPool;
    friend struct ScratchBufferManager;
};

class NGI_API AsyncUsedScratchBuffer
{
public:
    ~AsyncUsedScratchBuffer() = default;
    AsyncUsedScratchBuffer(const AsyncUsedScratchBuffer&) = default;

    std::tuple<NGIBuffer*, SizeType, void*> Raw() const { return {mBuffer, mType, mRawPtr}; }
    void Free();

private:
    AsyncUsedScratchBuffer() = default;
    AsyncUsedScratchBuffer(NGIBuffer* inBuffer, SizeType inType, void* inRawPtr, const ScratchBuffer* inScratchBuffer, NGITransientBufferManager* inManager);

    NGIBuffer* mBuffer;
    SizeType mType;
    void* mRawPtr;
    const ScratchBuffer* mScratchBuffer;
    NGITransientBufferManager* mManager;
    friend struct NGITransientBufferManager;
    friend struct StageBufferPool;
    friend struct ScratchBufferManager;
};

struct NGIComputePipelineStateDesc
{
    // each shader variant has a global unique GUID, currently it's simply increased from 0
    CrossUUID ProgramGUID;
    const NGIComputeProgramDesc* Program;
    NGIPipelineLayout* PipelineLayout;

    // specialization constants are also part of hash key
    UInt32 ConstantDataSize;
    const void* ConstantData;

    NGI_API friend bool operator==(const NGIComputePipelineStateDesc& a, const NGIComputePipelineStateDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIComputePipelineStateDesc& desc);
};

struct NGIComputePipelineState : public NGIObject, NGIObjectDesc<NGIComputePipelineStateDesc>, NGIPipelineStatePoolChild
{
    using TDesc = typename NGIObjectDesc::TDesc;

    NGIComputePipelineState(const TDesc& desc, NGIPipelineStatePool* pipelineStatePool);

protected:
    std::unique_ptr<UInt8[]> mConstantData;
};

struct NGITransientBufferManagerDesc
{
    NGIBufferUsage Usage;
    SizeType InitialSize = 128 * 1024 * 1024;
    SizeType LargeRequestThreshold = 2 * 1024 * 1024;
    bool EnableShrink = true;
    double ShrinkThreshold = 0.8f;
    double MinShrinkFactor = 0.0001;
    double MaxShrinkFactor = 0.01;
};
struct NGICommandList;

//BufferType should be NGIStagingBuffer or NGIBuffer
template<typename BufferType>
class BufferWrap {
public:
    BufferWrap(BufferType* ngiBuffer, SizeType ngiBufferOffset, void* mapPtr, size_t mapSize)
        : mNGIBuffer(ngiBuffer)
        , mNGIBufferOffset(ngiBufferOffset)
#ifndef _MANAGED
        , mMapData(static_cast<uint8_t*>(mapPtr), mapSize)
#endif
    {}

    BufferWrap()
        : mNGIBuffer(nullptr)
        , mNGIBufferOffset(0)
        , mMapData()
    {}

    ~BufferWrap() = default;
    BufferWrap(const BufferWrap&) = default;
    BufferWrap(BufferWrap&&) = default;
    BufferWrap& operator=(const BufferWrap&) = default;
    BufferWrap& operator=(BufferWrap&&) = default;

    auto IsValid() const -> bool { return mNGIBuffer != nullptr; }
    auto GetNGIBuffer() const -> BufferType* { return mNGIBuffer; }
    auto GetNGIOffset() const -> SizeType { return mNGIBufferOffset; }
    auto GetMapSize() const -> SizeType { return mMapData.size(); }
    auto MemSet(SizeType dstByteOffset, uint8_t val, SizeType byteSize) -> void
    {
#ifndef _MANAGED
        std::fill_n(std::next(mMapData.begin(), dstByteOffset), byteSize, val);
#endif
    }
    auto MemWrite(SizeType dstByteOffset, const void* srcPtr, SizeType srcByteSize) -> void
    {
#ifndef _MANAGED
        // Assert(srcByteSize);
        Assert(dstByteOffset + srcByteSize <= mMapData.size());
        std::copy_n(static_cast<const uint8_t*>(srcPtr), srcByteSize, std::next(mMapData.begin(), dstByteOffset));
#endif
    }

    template<class IteratorType, class DiffType>
    auto MemWriteIterator(SizeType dstByteOffset, IteratorType srcBegin, DiffType count) -> void
    {
#ifndef _MANAGED
        // Assert(count > 0);
        Assert(dstByteOffset + sizeof(typename IteratorType::value_type) * count <= mMapData.size());
        std::copy_n(srcBegin, count, reinterpret_cast<typename IteratorType::value_type*>(mMapData.data() + dstByteOffset));
#endif
    }

    template<typename containerType>
    auto MemWriteContainer(containerType* container, uint32_t copySize, uint32_t container_offset) -> uint32_t
    {
#ifndef _MANAGED
        return container->CopyDataToDest(mMapData, copySize, container_offset);
#endif
    }

private:
    BufferType* mNGIBuffer = nullptr;
    SizeType mNGIBufferOffset = 0;
    std::span<uint8_t> mMapData;
};

using StagingBufferWrap = BufferWrap<NGIStagingBuffer>;
using ScratchBufferWrap = BufferWrap<NGIBuffer>;

struct NGITransientBufferManager : public INGIResourceManager, NGIObjectDesc<NGITransientBufferManagerDesc>, NGIDeviceChild
{
    NGITransientBufferManager(const NGITransientBufferManagerDesc& desc, NGIDevice* device)
        : NGIObjectDesc(desc)
        , NGIDeviceChild(device){};

    //deprecated:
    virtual ScratchBufferWrap Allocate(NGIBufferUsage usage, SizeType size, bool dedicated = false) { return AllocateScratch(usage, size, dedicated); }
    virtual AsyncUsedStageBuffer Allocate_Async(NGIBufferUsage usage, SizeType size, bool dedicated = false) { return AllocateStaging_Async(usage, size, dedicated); }


    virtual StagingBufferWrap AllocateStaging(NGIBufferUsage usage, SizeType size, bool dedicated = false);
    virtual ScratchBufferWrap AllocateScratch(NGIBufferUsage usage, SizeType size, bool dedicated = false);

    virtual AsyncUsedStageBuffer AllocateStaging_Async(NGIBufferUsage usage, SizeType size, bool dedicated = false) { return AsyncUsedStageBuffer(); }
    virtual AsyncUsedScratchBuffer AllocateScratch_Async(NGIBufferUsage usage, SizeType size, bool dedicated = false) { return AsyncUsedScratchBuffer(); }

    virtual void RecordBufferCopy(NGICommandList* cmdCopyList, NGICommandList* cmdDrawList) = 0;

    void OnBeginFrame(FrameParam* frameparam) override;

    void EndFrame() override;

protected:
    typedef std::tuple<UInt32, std::unique_ptr<NGIBuffer>> PendingBufferTuple;

    void AddPendingDeleteBuffer(PendingBufferTuple&& item)
    {
        std::unique_lock writerLock(mPendingBufferMutex);
        GetCurrent(mPendingDeleteBuffers).emplace_back(std::move(item));
    }

    SizeType GetPendingDeleteBuffersSize()
    {
        std::shared_lock readerLock(mPendingBufferMutex);
        SizeType totalSize = 0;
        for (auto& itr : mPendingDeleteBuffers.mContainers)
        {
            for (auto& [lifetime, buffer] : *itr)
            {
                totalSize += buffer->GetDesc().Size;
            }
        }

        return totalSize;
    }

private:
    LifeTimeContainer<std::list<PendingBufferTuple>> mPendingDeleteBuffers;
    std::shared_mutex mPendingBufferMutex;
};

struct NGIUploadBufferDesc
{
    UInt32 mMinChunkSize{ 7U };
    UInt32 mMaxChunkSize{ 12U };
    UInt32 mCapacity{ 128U };
};

struct NGIUploadBuffer : public NGIObjectDesc<NGIUploadBufferDesc>, NGIDeviceChild
{
    struct Memory
    {
        NGIStagingBuffer* mBuffer;
        void* mMapped;
        UInt64 mOffset;
    };

    NGIUploadBuffer(const NGIUploadBufferDesc& desc, NGIDevice* device) : NGIObjectDesc(desc), NGIDeviceChild(device) {}
    virtual ~NGIUploadBuffer() = default;

    virtual const Memory* Allocate(UInt32 size) = 0;

    virtual void Free(const Memory* memory) = 0;

    virtual UInt32 Shrink() { return 0U; }

    virtual void LogMemoryFootprint() {}
};

struct HitGroupDesc {
    NGIShaderCodeDesc ClosestHitShader;
    NGIShaderCodeDesc AnyHitShader;
    NGIShaderCodeDesc IntersectionShader;
};

struct NGIRayTracingProgramDesc
{
    CrossSchema::ShaderVersion ShaderVersion;
    NGIShaderCodeDesc RayGenShader;
    std::vector<NGIShaderCodeDesc> MissShaders;
    std::vector<HitGroupDesc> HitGroups;
    std::vector<NGIShaderCodeDesc> CallableShaders;
    
    const char* FilePath = nullptr;
};

struct NGIRayTracingPipelineStateDesc
{
    CrossUUID ProgramGUID;
    const NGIRayTracingProgramDesc* Program;
    NGIPipelineLayout* PipelineLayout;
    UInt32 MaxRecursionDepth;
    UInt32 MaxPayloadSize;
    UInt32 MaxAttributeSize;
    UInt32 ConstantDataSize;
    const void* ConstantData;

    NGI_API friend bool operator==(const NGIRayTracingPipelineStateDesc& a, const NGIRayTracingPipelineStateDesc& b);
    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIRayTracingPipelineStateDesc& desc);
};

struct NGIRayTracingPipelineState : public NGIObject, NGIObjectDesc<NGIRayTracingPipelineStateDesc>, NGIPipelineStatePoolChild
{
    using TDesc = typename NGIObjectDesc::TDesc;

    NGIRayTracingPipelineState(const TDesc& Desc, NGIPipelineStatePool* Pool);

    /**
     * Retrieves shader binding table regions for ray tracing dispatch
     *
     * @param raygenRegion [out] Region for ray generation shaders
     * @param missRegion [out] Region for miss shaders
     * @param hitRegion [out] Region for hit group shaders
     * @param callableRegion [out] Region for callable shaders
     */
    virtual void GetShaderBindingTableRegions(
        void* raygenRegion,
        void* missRegion,
        void* hitRegion,
        void* callableRegion) const = 0;

protected:
    std::unique_ptr<UInt8[]> mConstantData;
};

struct NGIPipelineStatePoolDesc
{
    void* InitialData;
    SizeType InitialDataSize;
};

struct NGIPipelineStatePool : public NGIObject, NGIDeviceChild, INGIResourceManager
{
    using NGIDeviceChild::NGIDeviceChild;

    virtual NGIGraphicsPipelineState* AllocateGraphicsPipelineState(const NGIGraphicsPipelineStateDesc& desc) = 0;

    virtual NGIComputePipelineState* CreateComputePipelineState(const NGIComputePipelineStateDesc& desc);

    virtual NGIGraphicsPipelineState* CreateGraphicsPipelineState(const NGIGraphicsPipelineStateDesc& desc);

    virtual NGIRayTracingPipelineState* CreateRayTracingPipelineState(const NGIRayTracingPipelineStateDesc& desc);

    virtual SizeType GetSize() = 0;

    virtual void GetData(SizeType size, void* pData) = 0;
};

struct NGIConstBufferView
{
    NGIBuffer* ConstBuffer;
    SizeType Offset;
    SizeType Range;
};

enum class NGIResourceBindingType : UInt8
{
    Unknown,
    Sampler,
    Texture,
    Buffer,
    ConstBuffer,
    AccelStruct
};

enum class NGIResourceBindingFlags
{
    RenderPassTargetSimultaneously = 0x1,
};

ENUM_CLASS_FLAGS(NGIResourceBindingFlags)

struct NGIResourceBinding
{
    NameID ID;
    UInt32 ArrayOffset;

    NGIResourceBindingType Type;
    NGIResourceBindingFlags Flags;

    union
    {
        NGISampler* Sampler;
        NGITextureView* TextureView;
        NGIBufferView* BufferView;
        NGIConstBufferView ConstBufferView;
        NGIAccelStruct* AccelStruct;
    };

    static NGIResourceBinding BindSampler(NameID ID, UInt32 arrayOffset, NGISampler* sampler)
    {
        NGIResourceBinding binding{
            ID,
            arrayOffset,
            NGIResourceBindingType::Sampler,
        };
        binding.Sampler = sampler;
        return binding;
    }

    static NGIResourceBinding BindSampler(NameID ID, NGISampler* sampler)
    {
        return BindSampler(ID, 0, sampler);
    }

    static NGIResourceBinding BindTexture(NameID ID, UInt32 arrayOffset, NGITextureView* textureView, NGIResourceBindingFlags flags)
    {
        NGIResourceBinding binding{
            ID,
            arrayOffset,
            NGIResourceBindingType::Texture,
            flags,
        };
        binding.TextureView = textureView;
        return binding;
    }

    static NGIResourceBinding BindTexture(NameID ID, NGITextureView* textureView)
    {
        return BindTexture(ID, 0, textureView, NGIResourceBindingFlags{ 0 });
    }

    static NGIResourceBinding BindBuffer(NameID ID, UInt32 arrayOffset, NGIBufferView* bufferView)
    {
        NGIResourceBinding binding{
            ID,
            arrayOffset,
            NGIResourceBindingType::Buffer,
        };
        binding.BufferView = bufferView;
        return binding;
    }

    static NGIResourceBinding BindBuffer(NameID ID, NGIBufferView* bufferView)
    {
        return BindBuffer(ID, 0, bufferView);
    }

    static NGIResourceBinding BindConstBuffer(NameID ID, NGIBuffer* constBuffer, SizeType offset, SizeType range)
    {
        NGIResourceBinding binding{
            ID,
            0,
            NGIResourceBindingType::ConstBuffer,
        };
        binding.ConstBufferView = {constBuffer, offset, range};
        return binding;
    }

    static NGIResourceBinding BindAccelStruct(NameID ID, NGIAccelStruct* accel)
    {
        NGIResourceBinding binding{
            ID,
            0,
            NGIResourceBindingType::AccelStruct,
        };
        binding.AccelStruct = accel;
        return binding;
    }

    NGI_API friend SimpleStream& operator<<(SimpleStream& stream, const NGIResourceBinding& binding);
    NGI_API friend bool operator<(const NGIResourceBinding& a, const NGIResourceBinding& b);
};

struct NGIResourceGroupPool;

struct NGIResourceGroup : public NGIObject
{
    NGIResourceGroup(NGIResourceGroupLayout* layout, NGIResourceGroupPool* pool);

    virtual void SetSamplers(NameID ID, UInt32 ArrayOffset, UInt32 Num, NGISampler** ppSamplers) = 0;

    virtual void SetTextures(NameID ID, UInt32 ArrayOffset, UInt32 Num, NGITextureView** ppTextureViews) = 0;

    virtual void SetBuffers(NameID ID, UInt32 ArrayOffset, UInt32 Num, NGIBufferView** ppBufferViews) = 0;

    virtual void SetConstBuffer(NameID ID, NGIBuffer* pConstBuffer, SizeType Offset, SizeType Range) = 0;

    virtual void SetAccelStruct(NameID ID, NGIAccelStruct* accel) = 0;

protected:
    NGIResourceGroupLayout* mLayout;

    NGIResourceGroupPool* mPool;
};

struct NGIResourceGroupPoolDesc
{
    UInt32 Lifetime;
};

enum class NGIBindlessResourceType
{
    Buffer = 0x1,
    CombinedImageSampler = Buffer << 1
};

ENUM_CLASS_FLAGS(NGIBindlessResourceType)

struct NGIResourceGroupPool : public NGIObject, NGIDeviceChild, INGIResourceManager
{
    using NGIDeviceChild::NGIDeviceChild;

    // bindings must be sorted
    virtual NGIResourceGroup* Allocate(NGIResourceGroupLayout* resourceGroupLayout, UInt32 numBindings, NGIResourceBinding* bindings) = 0;

    // create and update resource group
    virtual NGIResourceGroup* Create(NGIResourceGroupLayout* resourceGroupLayout, UInt32 numBindings, NGIResourceBinding* bindings);

    // create persistent resource group
    virtual NGIResourceGroup* CreateBindlessResourceGroup(NGIResourceGroupLayout* resourceGroupLayout, NGIBindlessResourceType BindlessResourceType, UInt32 maxBindlessResourceCount);
};

struct NGIMemoryBarrier
{
    NGIResourceState StateBefore;
    NGIResourceState StateAfter;
};

struct NGIBufferBarrier
{
    NGIBuffer* Buffer;
    NGIResourceState StateBefore;
    NGIResourceState StateAfter;
};

struct NGITextureBarrier
{
    NGITexture* Texture;
    UInt32 Subresource;
    NGIResourceState StateBefore;
    NGIResourceState StateAfter;
    bool Discard;
};

struct NGIFence : public NGIObject, NGIDeviceChild
{
    using NGIDeviceChild::NGIDeviceChild;

    virtual UInt64 GetCompletedValue() = 0;

    /*
     * wait on single thread
     * @ret: timeout if return false
     */
    virtual bool Wait(UInt64 value, UInt64 timeout) = 0;

    // TODO(peterwjma): not implemented on Vulkan yet
    virtual void Signal(UInt64 value) = 0;
};

enum struct NGICommandQueueType : UInt8
{
    Direct,
    Compute,
    Copy,
};

struct NGICommandQueueDesc
{
    NGICommandQueueType type;
};

enum class NGIQueryHeapType
{
    TimeStamp,
};

struct NGIQueryHeapDesc
{
    NGIQueryHeapType Type;
    UInt32 Count;
};

struct NGIQueryHeap : public NGIObject, NGIObjectDesc<NGIQueryHeapDesc>, NGIDeviceChild
{
    NGI_DESC_DEVICECHILD_CTOR(NGIQueryHeap)
};

union NGIClearValue
{
    float colorf[4];
    UInt32 coloru[4];
    SInt32 colori[4];
    struct
    {
        float depth;
        UInt8 stencil;
    } depthStencil;
};

struct NGIViewport
{
    float x;
    float y;
    float width;
    float height;
    float minDepth;
    float maxDepth;
};

struct NGIScissor
{
    SInt32 x;
    SInt32 y;
    UInt32 width;
    UInt32 height;
};

struct NGICopyBuffer
{
    SizeType SrcOffset;
    SizeType DstOffset;
    SizeType NumBytes;
};

struct NGIOffset3D
{
    SInt32 X;
    SInt32 Y;
    SInt32 Z;
};

struct NGIExtent3D
{
    UInt32 Width;
    UInt32 Height;
    UInt32 Depth;
};

/*
 * BufferOffset must be aligned with NGIDevice::GetTextureDataPlacementAlignment(GraphicsFormat format).
 * Additional, if format is a compressed format, BufferRowPitch must be aligned with NGIDevice::GetTextureDataPitchAlignment(GraphicsFormat format)
 */
struct NGICopyBufferTexture
{
    SizeType BufferOffset;
    SizeType BufferRange;
    UInt32 BufferRowPitch;

    UInt32 TextureSubresource;
    NGIOffset3D TextureOffset;
    NGIExtent3D TextureExtent;
};

struct NGICopyTexture
{
    UInt32 SrcSubresource;
    NGIOffset3D SrcOffset;
    UInt32 DstSubresource;
    NGIOffset3D DstOffset;
    NGIExtent3D Extent;
};

/*
 bundle command list will inherts
     1. render pass
     2. frame buffer
 bundle command list will not inherts
     1. vertex, index buffer
     2. pipeline state
     3. scissors, viewports
     4. other states

 *** important***
SetViewports, SetScissors, DrawXXXs should only be called between BeginRenderPass and EndRenderPass
 */
struct NGIBundleCommandList : public NGIDeviceChild
{
    virtual void Begin() = 0;
    /*
     * Pipiline bind layout as a part of pipeline state, all resource group will be invalidad if pipeline layout was changed when switching between pipeline states
     */
    virtual void SetGraphicsPipelineState(NGIGraphicsPipelineState* pPipelineState) = 0;

    virtual void SetGraphicsResourceGroup(UInt32 slot, NGIResourceGroup* pResourceGroup) = 0;

    virtual void SetVertexBuffers(UInt32 num, NGIBuffer** ppVertexBuffers, UInt64* pOffsets) = 0;

    /*
     * GraphicFormat can only be FormatR32_UInt or FormatR16_UInt
     */
    virtual void SetIndexBuffer(NGIBuffer* pIndexBuffer, UInt64 offset, GraphicsFormat format) = 0;

    virtual void SetViewports(UInt32 NumViewports, const NGIViewport* pViewports) = 0;

    virtual void SetScissors(UInt32 NumScissors, const NGIScissor* pScissors) = 0;

    virtual void SetStencilRef(UInt32 stencilRef);

    virtual void DrawInstanced(UInt32 vertexCountPerInstance, UInt32 instanceCount, UInt32 startVertexLocation, UInt32 startInstanceLocation) = 0;

    virtual void DrawIndexedInstanced(UInt32 indexCountPerInstance, UInt32 instanceCount, UInt32 startIndexLocation, SInt32 baseVertexLocation, UInt32 startInstanceLocation) = 0;

    virtual void DrawIndexedIndirect(NGIBuffer* buffer, UInt32 offset, UInt32 drawCount, UInt32 stride) {};

    virtual void DrawIndexedIndirectCount(NGIBuffer* buffer, UInt32 offset, NGIBuffer* countBuffer, UInt32 countBufferOffset, UInt32 maxDrawCount, UInt32 stride){};

    virtual void DrawIndirect(NGIBuffer* buffer, UInt32 offset, UInt32 drawCount, UInt32 stride){};

    virtual void EndQuery(NGIQueryHeap* queryHeap, NGIQueryHeapType type, UInt32 index){};

    virtual void ResolveQueryData(NGIQueryHeap* queryHeap, NGIQueryHeapType type, UInt32 index, UInt32 numQueries, NGIBuffer* dstBuffer, UInt64 DestinationBufferOffset){};

    // on vulkan, it will call vkEndCommandBuffer for current command list
    virtual void End() = 0;

protected:
    using NGIDeviceChild::NGIDeviceChild;

    virtual ~NGIBundleCommandList() = default;
};

/*
 DispatchXXX, CopyXXX, ResourceBarrier should only be called outside BeginRenderPass and EndRenderPass
 */
struct NGICommandList : public NGIBundleCommandList
{
    NGI_API static NGICommandList* GetImmediateCommandList();
    NGI_API static void InitImmediateCommandList(NGICommandList* imCmdList);
    /* begin render pass, if use bundle command list to record commands, then DrawXXX can't be directly recoreded to this command list
     * resource transition of targets were only allowed among render pass, which was indicated by initial target barriers
     */
    virtual void BeginRenderPass(NGIRenderPass* pRenderPass, NGIFramebuffer* pFramebuffer, UInt32 ClearValueCount, NGIClearValue* pClearValues, UInt32 TargetBarrierCount, NGITextureBarrier* TargetBarriers, bool useBundle) = 0;

    // start recording command to bundle command lits, and associate bundle command lists with this command list
    // BeginBundleCommandLists can be called multiple times, then the next EndBundleCommandLists will end them all
    virtual void ForkBundleCommandLists(UInt32 num, NGIBundleCommandList* const* ppBundleCmdLists) = 0;

    // finish recording command to bundle command lists, and associate bundle command lists with this command list
    // on vulkan, it will call vkCmdEndCommandBuffer for each bundle command list, and call vkCmdExecuteCommands for all bundle command lists
    // on metal, do nothing
    virtual void JoinBundleCommandLists() = 0;

    virtual void NextSubPass(bool useBundle) = 0;

    virtual void EndRenderPass() = 0;

    virtual void SetComputePipelineState(NGIComputePipelineState* pPipelineState) {}

    virtual void SetComputeResourceGroup(UInt32 slot, NGIResourceGroup* pResourceGroup) {}

    virtual void Dispatch(UInt32 threadGroupCountX, UInt32 threadGroupCountY, UInt32 threadGroupCountZ) {}

    virtual void DispatchIndirect(NGIBuffer* argumentBuffer, UInt32 offset) {}

    virtual void CopyBufferToBuffer(NGIBuffer* dstBuffer, NGIBuffer* srcBuffer, UInt32 regionCount, const NGICopyBuffer* regions) = 0;

    virtual void CopyBufferToTexture(NGITexture* dstTexture, NGIBuffer* srcBuffer, UInt32 regionCount, const NGICopyBufferTexture* regions) = 0;

    virtual void CopyTextureToBuffer(NGIBuffer* dstBuffer, NGITexture* srcTexture, UInt32 regionCount, const NGICopyBufferTexture* regions) = 0;

    virtual void CopyTextureToTexture(NGITexture* dstTexture, NGITexture* srcTexture, UInt32 regionCount, const NGICopyTexture* regions) = 0;

    virtual void ClearTexture(NGITextureView* texture, const NGIClearValue& clearValue) {}

    // TODO(peterwjma): optimize to clear texel
    virtual void ClearBuffer(NGIBufferView* buffer, UInt32 clearValue) {}

    virtual void ResourceBarrier(UInt32 BufferBarrierCount, NGIBufferBarrier* pBufferBarrieres, UInt32 TextureBarrierCount, NGITextureBarrier* pTextureBarriers) = 0;
    
    virtual void MemBarrier(NGIMemoryBarrier* pMemBarrier) { Assert(false); }

    virtual void BeginDebugRegion(const char* label, const float* color = nullptr, bool bAddTimeStamp = true) {}

    virtual void EndDebugRegion(bool bAddTimeStamp = true) {}

    virtual NGICommandQueue* GetCommandQueue() = 0;

    virtual UInt64 GetNativeHandle() = 0;

#ifdef NGI_ENABLE_RAY_TRACING
    /**
     * Bind RayTracing Pipeline
     * @param pPipelineState
     */
    virtual void SetRayTracingPipeline(NGIRayTracingPipelineState* pPipelineState) { Assert(false); };

    /**
     * Bind RayTracing Pipeline Resource Group
     * @param slot 
     * @param pResourceGroup 
     */
    virtual void SetRayTracingResourceGroup(UInt32 slot, NGIResourceGroup* pResourceGroup) { Assert(false); }
    
    /**
     * Creates an acceleration structure object for ray tracing
     *
     * This function allocates and initializes either a Bottom Level Acceleration Structure (BLAS)
     * or a Top Level Acceleration Structure (TLAS) based on the description provided.
     *
     * @param Desc Description of the acceleration structure to create, including type (BLAS/TLAS),
     *             geometry data for BLAS, and build flags
     * @return Pointer to the created acceleration structure object, or nullptr on failure
     */
    virtual NGIAccelStruct* CreateAccelStruct(const NGIAccelStructDesc& Desc) { Assert(false); return nullptr; }

    /**
     * Builds a Bottom Level Acceleration Structure (BLAS) for ray tracing
     *
     * @param AS The acceleration structure object to build
     * @param pGeometries Array of geometry descriptions (triangles, AABBs, etc.)
     * @param NumGeometries Number of geometries in the array
     * @param BuildFlags Flags controlling the build process (allow update, prefer fast trace, etc.)
     * @param ScratchBuffer
     */
    virtual void BuildBottomLevelAccelStruct(
        NGIAccelStruct* AS,
        const NGIGeometryDesc* pGeometries,
        size_t NumGeometries,
        NGIAccelStructBuildFlag BuildFlags,
        void* ScratchBuffer = nullptr) { Assert(false); }

    /**
     * Compacts a previously built Bottom Level Acceleration Structure to reduce memory usage
     *
     * This function performs compaction on a BLAS that was built with the AllowCompaction flag.
     * Compaction can significantly reduce the memory footprint of acceleration structures.
     */
    virtual void CompactBottomLevelAccelStruct() { Assert(false); }

    /**
     * Builds a Top Level Acceleration Structure (TLAS) for ray tracing
     *
     * @param AS The acceleration structure object to build
     * @param pInstances Array of instance descriptions, each referencing a BLAS
     * @param NumInstances Number of instances in the array
     * @param BuildFlags Flags controlling the build process (allow update, prefer fast trace, etc.)
     */
    virtual void BuildTopLevelAccelStruct(
        NGIAccelStruct* AS,
        const NGIInstanceDesc* pInstances,
        size_t NumInstances,
        NGIAccelStructBuildFlag BuildFlags,
        void* ScratchBuffer = nullptr) { Assert(false); }

    /**
     * Builds a Top Level Acceleration Structure (TLAS) using instance data from a GPU buffer
     *
     * This variant is useful when instance data is already on the GPU or is dynamically generated.
     *
     * @param AS The acceleration structure object to build
     * @param InstanceBuffer Buffer containing instance data in the required format
     * @param InstanceBufferOffset Offset into the buffer where instance data begins
     * @param pInstances
     * @param NumInstances Number of instances in the buffer
     * @param BuildFlags Flags controlling the build process (allow update, prefer fast trace, etc.)
     */
    virtual void BuildTopLevelAccelStructFromBuffer(
        NGIAccelStruct* AS,
        ScratchBufferWrap InstanceBuffer,
        UInt64 InstanceBufferOffset,
        const NGIInstanceDesc* pInstances,
        size_t NumInstances,
        NGIAccelStructBuildFlag BuildFlags,
        void* ScratchBuffer = nullptr) { Assert(false); }
    
    /** 
     * Wait for blases Build Finish using when building TLAS
     */
    virtual void BLASBarrier(UInt32 NumBlas, NGIAccelStruct* blases) { Assert(false); }

    /** 
     * Wait for tlas build finish
     */
    virtual void TLASBarrier(NGIAccelStruct* tlas) { Assert(false); }

    /**
     * Traces rays using the specified ray tracing pipeline state
     *
     * @param pipelineState Ray tracing pipeline state to use
     * @param width Width dimension of the ray trace (typically the render target width)
     * @param height Height dimension of the ray trace (typically the render target height)
     * @param depth Depth dimension of the ray trace (typically 1)
     */
    virtual void TraceRays(NGIRayTracingPipelineState* pipelineState, uint32_t width, uint32_t height, uint32_t depth) { Assert(false); }
#endif

protected:

    using NGIBundleCommandList::NGIBundleCommandList;   
    
    virtual ~NGICommandList() = default;
};

struct ScopedNGICmdListDebugRegion
{
    ScopedNGICmdListDebugRegion(NGICommandList* cmdList, const char* label = nullptr, const float* color = nullptr)
        : mCmdList(cmdList), mLabel(label)
    {
        Assert(cmdList);
        if (mLabel)
        {
            mCmdList->BeginDebugRegion(label, color);
        }
    }

    ~ScopedNGICmdListDebugRegion()
    {
        if (mLabel)
        {
            mCmdList->EndDebugRegion();
        }
    }

private:
    NGICommandList* mCmdList;
    const char* mLabel;
};

#define SCOPED_NGI_CMD_LIST_PASTE_INTERNAL(T0, T1) T0##T1
#define SCOPED_NGI_CMD_LIST_PASTE(T0, T1) SCOPED_NGI_CMD_LIST_PASTE_INTERNAL(T0, T1)
#define SCOPED_NGI_CMD_LIST_DEBUG_REGION(CmdList, Label, ...) ScopedNGICmdListDebugRegion SCOPED_NGI_CMD_LIST_PASTE(NGI_CMD_LIST_DEBUG_REGION, __LINE__)(CmdList, Label, ##__VA_ARGS__)

/*
 * thread safe
 */
struct NGICommandQueue : public NGIObject, INGIResourceManager, NGIDeviceChild
{
    using NGIDeviceChild::NGIDeviceChild;

    virtual void AllocateCommandLists(UInt32 num, NGICommandList** ppCommandLists) = 0;

    virtual void AllocateBundleCommandLists(UInt32 num, NGIBundleCommandList** ppCommandLists) = 0;

    virtual void ExecuteCommandLists(UInt32 num, NGICommandList** ppLists) = 0;

    virtual void Signal(NGIFence* pFence, UInt64 value) = 0;

    virtual void Wait(NGIFence* pFence, UInt64 value) = 0;
};

enum class CEMeta(Cli) NGIScreenMode:UInt32
{
    Windowed,
    ExclusiveFullscreen,
    BorderlessFullscreen
};

enum class NGIPresentMode
{
    Immediate,
    FIFO,
    Mailbox,
};

struct NGISwapchainDesc
{
    NativeWindow Window;
    UInt32 Width;
    UInt32 Height;
    GraphicsFormat Format;
    UInt32 BufferCount;
    NGIPresentMode PresentMode;
    NGICommandQueue* CommandQueue;
    NGICommandQueue* GraphicsCommandQueue;
    NGIScreenMode ScreenMode;
};

struct NGISwapchain : public NGIObject, NGIObjectDesc<NGISwapchainDesc>, NGIDeviceChild
{
    NGI_DESC_DEVICECHILD_CTOR(NGISwapchain)

    virtual ~NGISwapchain() = default;

    virtual UInt32 GetCurrentBackBufferIndex() = 0;

    virtual NGITexture* GetBuffer(UInt32 index) = 0;

#if CROSSENGINE_ANDROID
    virtual void ReCreateSurface(NativeWindow nativeWindow) {}
#endif

    virtual void ResizeBuffers(UInt32 width, UInt32 height, NGIScreenMode screenMode) = 0;

    // acquire next avaliable back buffer on rendering thread
    virtual bool Acquire(NGIFence* toSingalFence) { return true; }

    // call when rendering and presentation on different queue, signal a frame rendering was ready
    virtual void Signal() { }

    // called on present thread
    virtual bool Present() = 0;

    // called on activate state changed
    virtual void OnActivate(bool inActive) { };
};

struct NGIFormatCapability
{
    NGIBufferUsage BufferCapability;
    NGITextureUsage TextureCapability;
    // if don't support MSAA for such format, this value is 1; if support 2xMSAA and 4xMSAA, this value is 1 | 2 | 4 = 7; if support 2xMSAA, 4xMSAA and 8xMSAA, this value is 1 | 2 | 4 | 8 = 15
    UInt32 MultiSampleCounts;
};

struct GPUResourceStatistics;
struct NGIDevice
{
public:
    NGIDevice();
    virtual ~NGIDevice() = default;

    virtual NGIPlatform GetPlatform() = 0;

    virtual bool NGISupportsMultithreading() = 0;

    virtual bool IsSupportMSAA(NGIResolveType depthResolveType , NGIResolveType stencilResolveType) = 0;

    virtual bool IsSupportDrawIndirectCount()
    {
        return false;
    }

    virtual void* GetNativeDevice()
    {
        return nullptr;
    }

    virtual void WaitIdle() = 0;

    virtual NGIFormatCapability GetFormatCapability(GraphicsFormat format) = 0;

    virtual SizeType GetTextureDataPlacementAlignment(GraphicsFormat format) = 0;

    virtual SizeType GetTextureDataPitchAlignment(GraphicsFormat format) = 0;

    virtual NGISwapchain* CreateSwapchain(const NGISwapchainDesc& desc) = 0;

    virtual NGICommandQueue* CreateCommandQueue(const NGICommandQueueDesc& desc) = 0;

    virtual NGIFence* CreateFence(UInt64 initialValue) = 0;

    virtual NGIFence* CreateTimeLineFence(UInt64 initialValue) = 0;

    virtual NGITransientBufferManager* CreateTransientBufferManager(const NGITransientBufferManagerDesc& desc);

    virtual NGIUploadBuffer* CreateUploadBuffer(const NGIUploadBufferDesc& desc, NGIBufferUsage usage);

    virtual NGISampler* CreateSampler(const NGISamplerDesc& desc) = 0;

    virtual NGIFramebuffer* CreateFramebuffer(const NGIFramebufferDesc& desc) = 0;

    virtual NGIRenderPass* CreateRenderPass(const NGIRenderPassDesc& desc) = 0;

    virtual NGIResourceGroupLayout* CreateResourceGroupLayout(const NGIResourceGroupLayoutDesc& desc);

    virtual NGIResourceGroupPool* CreateResourceGroupPool(const NGIResourceGroupPoolDesc& desc) = 0;

    virtual NGIPipelineLayout* CreatePipelineLayout(const NGIPipelineLayoutDesc& desc) = 0;

    virtual NGIPipelineStatePool* CreatePipelineStatePool(const NGIPipelineStatePoolDesc& desc) = 0;

    virtual std::vector<std::tuple<GPUProfiling::GPUProfilingContextInfo, std::vector<GPUProfiling::GPUProfilingItem>>> PopGpuProfileItems() = 0;

    virtual NGIQueryHeap* CreateQueryHeap(const NGIQueryHeapDesc& desc)
    {
        return nullptr;
    }

    virtual bool IsMemoryBudgetEnabled() const { return false; }

    virtual void SetFrameIndex(UInt32  frameindex) {};

    NGI_API cross::NGIBuffer* CreateBuffer(const NGIBufferDesc& desc, const char* pDebugName = "");

    NGI_API cross::NGIBufferView* CreateBufferView(NGIBuffer* pBuffer, const NGIBufferViewDesc& desc);

    NGI_API cross::NGITexture* CreateTexture(const NGITextureDesc& desc, const char * pDebugName = "", bool shared = false);

    NGI_API cross::NGITextureView* CreateTextureView(cross::NGITexture* texture, const NGITextureViewDesc& desc);

    NGI_API cross::NGIStagingBuffer* CreateStagingBuffer(const NGIBufferDesc& desc);

    // resource related
    // use protected to avoid miss use

    bool IsRenderDocEnabled() const { return mIsRenderDocEnabled; }

    virtual NGI_API std::shared_ptr<GPUResourceStatistics> GetGpuResourceStatistics()
    {
        return mGpuResourcesStatistic;
    }

    virtual NGI_API void DumpVideoGraphicsMemory(const std::string name)
    {
    }
    std::string DumpTransientResources()
    {
        std::string result;
        result += "AliasedSize: " + std::to_string(mTransientResourceStats.mAliasedSize) + "  ";
        result += "TotalHeapCapacity: " + std::to_string(mTransientResourceStats.mTotalHeapCapacity);
        return result;
    }
    struct
    {
        UInt64 mAliasedSize = 0;
        UInt64 mTotalHeapCapacity = 0;
    } mTransientResourceStats;
protected:
    virtual NGIBuffer* CreateBufferImp(const NGIBufferDesc& desc, const char* pDebugName) = 0;

    virtual NGIBufferView* CreateBufferViewImp(NGIBuffer* pBuffer, const NGIBufferViewDesc& desc) = 0;

    virtual NGITexture* CreateTextureImp(const NGITextureDesc& desc, const char* pDebugName, bool shared = false) = 0;

    virtual NGITextureView* CreateTextureViewImp(NGITexture* pTexture, const NGITextureViewDesc& desc) = 0;

    virtual NGIStagingBuffer* CreateStagingBufferImp(const NGIBufferDesc& desc) = 0;
    std::shared_ptr<GPUResourceStatistics> mGpuResourcesStatistic;

    bool mIsRenderDocEnabled = false;
};


struct NGIObjectDescHasher
{
    bool operator()(const NGIResourceGroupLayoutDesc* desc1, const NGIResourceGroupLayoutDesc* desc2) const
    {
        return *desc1 == *desc2;
    }

    bool operator()(const NGIPipelineLayoutDesc* desc1, const NGIPipelineLayoutDesc* desc2) const
    {
        return *desc1 == *desc2;
    }

    bool operator()(const NGIGraphicsPipelineStateDesc* desc1, const NGIGraphicsPipelineStateDesc* desc2) const
    {
        return *desc1 == *desc2;
    }

    bool operator()(const NGIComputePipelineStateDesc* desc1, const NGIComputePipelineStateDesc* desc2) const
    {
        return *desc1 == *desc2;
    }

    NGI_API size_t operator()(const NGIResourceGroupLayoutDesc* desc) const;

    NGI_API size_t operator()(const NGIPipelineLayoutDesc* desc) const;

    NGI_API size_t operator()(const NGIGraphicsPipelineStateDesc* desc) const;

    NGI_API size_t operator()(const NGIComputePipelineStateDesc* desc) const;

    template<typename TDesc> size_t operator()(const TDesc& desc) const
    {
        static_assert(std::is_same_v<TDesc, NGIRenderPassDesc> || std::is_same_v<TDesc, NGISamplerDesc> || std::is_same_v<TDesc, NGITextureDesc> || std::is_same_v<TDesc, NGIBufferDesc>);
        return Hash(desc);
    }
};
}   // namespace cross
