#pragma once
#include "GLES3Include.h"
#include "NativeGraphicsInterface/NGI.h"
#include <mutex>
#include <shared_mutex>

namespace cross {
struct GLES3ResourceGroupPool;
struct GLES3PipelineStatePool;
struct GLES3ProgramBinaryCache;
struct GLES3StagingBuffer;
struct GLES3TextureView;
struct GLES3Sampler;
struct GLES3Device;
struct GLES3CommandQueue;

// NameID to Binding. May have different space
struct GLES3PipelineLayout : public NGIPipelineLayout
{
    GLES3PipelineLayout(const NGIPipelineLayoutDesc& desc, NGIDevice* device)
        : NGIPipelineLayout(desc, device)
    {}
};


struct GLES3ResourceGroup : public NGIResourceGroup
{
    GLES3ResourceGroup(NGIResourceGroupLayout* layout, GLES3ResourceGroupPool* pool);
    ~GLES3ResourceGroup() override = default;

    void SetSamplers(NameID ID, UInt32 arrayOffset, UInt32 num, NGISampler** samplers) override;
    void SetTextures(NameID ID, UInt32 arrayOffset, UInt32 num, NGITextureView** textureViews) override;
    void SetBuffers(NameID ID, UInt32 arrayOffset, UInt32 num, NGIBufferView** bufferViews) override;
    void SetConstBuffer(NameID ID, NGIBuffer* constBuffer, SizeType offset, SizeType range) override;
    void SetAccelStruct(NameID ID, NGIAccelStruct* accel) override
    {
        Assert(false);
    }

    struct ConstBufferBindingInfo
    {
        SizeType offset;
        SizeType range;
        GLES3StagingBuffer* buffer;
    };
    std::unordered_map<NameID, ConstBufferBindingInfo> mConstBufferBinding;
    std::unordered_map<NameID, GLES3Sampler*> mSamplerBinding;
    std::unordered_map<NameID, GLES3TextureView*> mTextureBinding;
};

struct GLES3ResourceGroupPool : public NGIResourceGroupPool
{
    GLES3ResourceGroupPool(const NGIResourceGroupPoolDesc& desc, GLES3Device* device);

    ~GLES3ResourceGroupPool() override = default;

    NGIResourceGroup* Allocate(NGIResourceGroupLayout* resourceGroupLayout, UInt32 numBindings, NGIResourceBinding* bindings) override;

    void OnBeginFrame(FrameParam* frameparam) override;

    UInt32 GetLifeTime() const
    {
        return mDesc.Lifetime;
    }

protected:
    NGIResourceGroupPoolDesc mDesc;
    std::vector<std::vector<std::unique_ptr<GLES3ResourceGroup>>> mResourceGroups;
    int mCurrentGroup = 0;
    std::mutex mResourceGroupsMutex;
};

struct GLES3Shader
{
    enum Type
    {
        VertexShader,
        PixelShader,
        ComputeShader
    };
    GLES3Shader(Type type, const NGIShaderCodeDesc& desc, const NGIPipelineLayout* pipelineLayout, UInt32 constantDataSize, const void* constantData);
    ~GLES3Shader();
    std::string GeneraterConstantCode(const NGIPipelineLayout* pipelineLayout, UInt32 constantDataSize, const void* constantData);
    GLuint mShader = 0;
    Type mType;
};

struct ProgramKey
{
    ProgramKey(CrossUUID UUID, UInt32 dataSize, const void* cosntantData);

    std::vector<UInt8> mData;

    struct Hasher
    {
        std::size_t operator()(ProgramKey const& desc) const
        {
            Assert(desc.mData.size() % 4 == 0);
            return HashState(reinterpret_cast<const UInt32*>(desc.mData.data()), desc.mData.size() / 4);
        }

        bool operator()(const ProgramKey& a, const ProgramKey& b) const
        {
            return a.mData == b.mData;
        }
    };
};

class GLES3ProgramReflection
{
public:
    GLES3ProgramReflection(){};
    GLES3ProgramReflection(GLuint program, UInt32 combinedSamplerCount, NGICombinedSamplerDesc* combinedSamplers);
    void Init(GLuint program, UInt32 combinedSamplerCount, NGICombinedSamplerDesc* combinedSamplers);
    bool IsInit()
    {
        return mInited;
    }
    virtual ~GLES3ProgramReflection() {}
    virtual int GetVertexAttribLocation(VertexChannel channelName) const;
    virtual int GetConstBufferBindPoint(NameID constBufferName) const;
    const std::vector<UInt32>* GetCombinedSampler(NameID name) const;
    const std::vector<UInt32>* GetCombinedTexture(NameID name) const;

protected:
    // Search and skip first str in second str.
    // If the whole first str is skipped, return true.
    bool SkipFirstStrInSecondStr(const char* first, const char*& second)
    {
        while (*first == *second && *first != 0)
        {
            first++;
            second++;
        }
        return *first == 0;   // Have we skipped the whole first string
    }

    // <vertexChannel, location>
    std::vector<std::pair<VertexChannel, int>> mVertexAttributeSlot;
    std::unordered_map<NameID, int> mCBNameBinding;
    std::unordered_map<NameID, std::vector<UInt32>> mCombinedSamplers;
    std::unordered_map<NameID, std::vector<UInt32>> mCombinedTextures;
    std::atomic<bool> mInited{false};
};

struct GLES3Program
{
    GLES3Program(ProgramKey key, const NGIGraphicsProgramDesc* programDesc, UInt32 constantDataSize, const void* constantData, const NGIPipelineLayout* pipelineLayout, GLES3ProgramBinaryCache* binaryCache);
    ~GLES3Program();

    void ApplyToGL();
    const GLES3ProgramReflection* GetReflection() const
    {
        Assert(mReflection->IsInit());
        return mReflection.get();
    }

private:
    GLuintRef mProgram{nullptr};
    std::shared_ptr<GLES3ProgramReflection> mReflection{nullptr};
};

struct GLES3ProgramBinaryCache
{
    void Deserialize(const void* data, size_t size);
    std::vector<UInt8> Serialize();

    struct ProgramBinary
    {
        std::vector<UInt8> data;
        GLsizei lenth;
        GLenum binaryFormat;
    };
    const ProgramBinary* Find(ProgramKey key);
    void AddCache(ProgramKey key, std::vector<UInt8>&& data, GLsizei length, GLenum binaryFormat);
    void DeleteCache(ProgramKey key);

protected:
    std::unordered_map<ProgramKey, ProgramBinary, ProgramKey::Hasher, ProgramKey::Hasher> mCache;
    std::mutex mCacheMutex;
};

struct GLES3ProgramPool : public GLES3ProgramBinaryCache
{
    GLES3ProgramPool(int maxFrameLife);

    void BeginFrame();

    GLES3Program* FindOrCreateProgram(ProgramKey key, const NGIGraphicsProgramDesc* programDesc, const NGIPipelineLayout* pipelineLayout, UInt32 constantDataSize, const void* constantData);
    void FreeProgram(ProgramKey key);

protected:
    struct CachedProgram
    {
        std::unique_ptr<GLES3Program> program;
        int remainFrameOrRefCount;
    };
    std::unordered_map<ProgramKey, CachedProgram, ProgramKey::Hasher, ProgramKey::Hasher> mInUsingCache;
    std::unordered_map<ProgramKey, CachedProgram, ProgramKey::Hasher, ProgramKey::Hasher> mPendingDeleteCache;
    int mMaxFrameLife;
};

struct GLES3GraphicsPipelineState : public NGIGraphicsPipelineState
{
    GLES3GraphicsPipelineState(const NGIGraphicsPipelineStateDesc& desc, GLES3PipelineStatePool* pool);

    ~GLES3GraphicsPipelineState() override;

    void ApplyToGL();

    GLES3Program* GetProgram() const
    {
        return mProgram;
    }
    const InputLayoutDesc* GetInputLayout() const
    {
        return &mInputLayout;
    }

protected:
    void ApplyBlendStateToGL();
    void ApplyRasterizerStateToGL();
    void ApplyDepthStencilStateToGL();

    ProgramKey mProgramKey;
    GLES3Program* mProgram;
    InputLayoutDesc mInputLayout;   // Because upper layer will clear inputlayout before gl use it. We need to store a copy here
};

struct GLES3PipelineStatePool : public NGIPipelineStatePool
{
    GLES3PipelineStatePool(const NGIPipelineStatePoolDesc& desc, GLES3Device* device);

    ~GLES3PipelineStatePool() override = default;

    NGIGraphicsPipelineState* AllocateGraphicsPipelineState(const NGIGraphicsPipelineStateDesc& desc) override;

    void OnBeginFrame(FrameParam* frameparam) override;

    SizeType GetSize() override;

    void GetData(SizeType size, void* pData) override;

    using PipelineStatePool = std::unordered_map<const NGIGraphicsPipelineStateDesc*, std::tuple<std::unique_ptr<GLES3GraphicsPipelineState>, UInt32>, NGIObjectDescHasher, NGIObjectDescHasher>;

    GLES3ProgramPool mProgramPool;       // ProgramPool must initialize before PipelineStatePool

    PipelineStatePool mPipelineStates;   // dango gl TODO: don't use map, use std::vector to avoid macro memory

    std::shared_mutex mPipelineStatesMutex;
};


struct GLES3RenderPass : public NGIRenderPass
{
    GLES3RenderPass(const NGIRenderPassDesc& desc, GLES3Device* device);

    ~GLES3RenderPass() override = default;

    UInt64 GetNativeHandle() override
    { /*return reinterpret_cast<UInt64>(mRenderPass);*/
        Assert(false);
        return 0;
    }
};

struct GLES3Framebuffer : public NGIFramebuffer
{
    GLES3Framebuffer(const NGIFramebufferDesc& desc, GLES3Device* device);

    ~GLES3Framebuffer() override;

    void BindFrameBuffer(UInt32 numClearValues, NGIClearValue* clearValues, GLES3RenderPass* renderPass);
    void UnBindFrameBuffer(GLES3RenderPass* renderPass);

    static void FramebufferBindTextureView(NGITextureView* view, GLenum attachIdx);

    void ResolveFrameBuffer();

    void CheckFramebuffer(GLenum framebuffer);

protected:
    GLuint mFrameBuffer;
    std::vector<GLenum> mClearDrawbuffers;
    std::vector<GLenum> mDrawbuffers;

    GLuint mResolveSrcFramebuffer = 0;
    GLuint mResolveDstFramebuffer = 0;
    GLbitfield mResolveAttachFlags = 0;

    GLuint mResolveSrcFramebufferForBackbuffer = 0;
};

struct GLES3CommandList : public NGICommandList
{
    static GLES3CommandList* GetImmediateCommandList()
    {
        return static_cast<GLES3CommandList*>(NGICommandList::GetImmediateCommandList());
    }

    GLES3CommandList(GLES3CommandQueue* queue);

    ~GLES3CommandList() override = default;

    void Begin() override;

    void BeginRenderPass(NGIRenderPass* pRenderPass, NGIFramebuffer* pFramebuffer, UInt32 ClearValueCount, NGIClearValue* pClearValues, UInt32 TargetBarrierCount, NGITextureBarrier* TargetBarriers, bool useBundle) override;

    void ForkBundleCommandLists(UInt32 num, NGIBundleCommandList* const* bundleCmdLists) override;

    void JoinBundleCommandLists() override;

    void SetGraphicsPipelineState(NGIGraphicsPipelineState* pipelineState) override;

    void SetGraphicsResourceGroup(UInt32 slot, NGIResourceGroup* resourceGroup) override;

    void SetVertexBuffers(UInt32 num, NGIBuffer** vertexBuffers, UInt64* offsets) override;

    void SetIndexBuffer(NGIBuffer* indexBuffer, UInt64 offset, GraphicsFormat format) override;

    void SetViewports(UInt32 num, const NGIViewport* viewports) override;

    void SetScissors(UInt32 num, const NGIScissor* scissors) override;

    void DrawInstanced(UInt32 vertexCountPerInstance, UInt32 instanceCount, UInt32 startVertexLocation, UInt32 startInstanceLocation) override;

    void DrawIndexedInstanced(UInt32 indexCountPerInstance, UInt32 instanceCount, UInt32 startIndexLocation, SInt32 baseVertexLocation, UInt32 startInstanceLocation) override;

    void NextSubPass(bool useBundle) override;

    void EndRenderPass() override;

    void CopyBufferToBuffer(NGIBuffer* dstBuffer, NGIBuffer* srcBuffer, UInt32 regionCount, const NGICopyBuffer* regions) override;

    void CopyBufferToTexture(NGITexture* dstTexture, NGIBuffer* srcBuffer, UInt32 regionCount, const NGICopyBufferTexture* regions) override;

    void CopyTextureToBuffer(NGIBuffer* dstBuffer, NGITexture* srcTexture, UInt32 regionCount, const NGICopyBufferTexture* regions) override;

    void CopyTextureToTexture(NGITexture* dstTexture, NGITexture* srcTexture, UInt32 regionCount, const NGICopyTexture* regions) override;

    void ResourceBarrier(UInt32 bufferBarrierCount, NGIBufferBarrier* bufferBarrieres, UInt32 textureBarrierCount, NGITextureBarrier* textureBarriers) override;

    void End() override;

    void Reset();

    void RealExcuteCmdsOnRenderThread();

    void RecordCommand(const char* debugName, std::function<void()>&& worker, bool allowExecImmediate = false);

    NGICommandQueue* GetCommandQueue() override
    {
        return reinterpret_cast<NGICommandQueue*>(const_cast<GLES3CommandQueue*>(mQueue));
    }

    UInt64 GetNativeHandle() override
    {
        return 0;
    }

private:
    void DrawImpl(bool drawWithOutIndex, UInt32 vertexOrIndexCountPerInstance, UInt32 instanceCount, UInt32 startVertexLocation, UInt32 startIndexLocation, UInt32 startInstanceLocation);

    GLES3CommandQueue* const mQueue;

    struct Command
    {
        Command(const char* name, std::function<void()>&& worker)
            : debugName(name)
            , worker(std::move(worker))
        {}
        const char* debugName;
        std::function<void()> worker;
    };
    std::vector<Command> mCommands;
    std::mutex mCmdMutex;
    bool mUseBundle{false};

    std::vector<const GLES3CommandList*> mBundleCommandLists;

    struct RenderPassContext
    {
        GLES3RenderPass* renderpass = nullptr;
        GLES3Framebuffer* frameBuffer = nullptr;
        void Reset()
        {
            memset(this, 0, sizeof(*this));
        }
    };
    RenderPassContext mRenderPassContext;

    struct DrawCallContext
    {
        GLES3GraphicsPipelineState* pipelineStateObj = nullptr;
        std::array<GLES3ResourceGroup*, 8> resourceGroup{};
        std::array<std::pair<NGIBuffer*, UInt64>, MaxVertexStreams> vertexBuffers{};   // UInt64 is offset
        // <buffer, offset, indexBitWide>
        std::tuple<NGIBuffer*, UInt64, GLenum> indexBuffer = {nullptr, 0, GL_UNSIGNED_SHORT};

        void Reset()
        {
            memset(this, 0, sizeof(*this));
        }
    };
    DrawCallContext mDrawCallContext;

    struct ViewPortAndScissorContext
    {
        NGIViewport viewPort{};
        NGIScissor scissor{};

        void Reset()
        {
            memset(this, 0, sizeof(*this));
        }
    };
    ViewPortAndScissorContext mViewPortAndScissorContext;
};

struct GLES3Fence : public NGIFence
{
    constexpr static uint32_t FencePoolSize = 10;

    GLES3Fence(UInt64 initialValue, GLES3Device* device);

    ~GLES3Fence() override = default;

    UInt64 GetCompletedValue() override;

    bool Wait(UInt64 value, UInt64 timeout) override;

    void Signal(UInt64 value) override;

    UInt64 mCurrentValue;
};

struct GLES3CommandQueue : public NGICommandQueue
{
    GLES3CommandQueue(const NGICommandQueueDesc& desc, GLES3Device* device);

    ~GLES3CommandQueue() override = default;

    void ExecuteCommandLists(UInt32 num, NGICommandList** lists) override;

    void Signal(NGIFence* fence, UInt64 value) override;

    void Wait(NGIFence* fence, UInt64 value) override;
    
    void OnBeginFrame(FrameParam* frameparam) override
    {
        mCmdPool.BeginFrame(frameparam);
        mBundleCmdPool.BeginFrame(frameparam);
    }

    void EndFrame() override {}

    void AllocateCommandLists(UInt32 num, NGICommandList** commandLists) override
    {
        mCmdPool.Allocate(num, reinterpret_cast<void**>(commandLists));
    }

    void AllocateBundleCommandLists(UInt32 num, NGIBundleCommandList** commandLists) override
    {
        mBundleCmdPool.Allocate(num, reinterpret_cast<void**>(commandLists));
    }

    NGICommandQueueDesc mDesc;

    typedef enum
    {
        COMMAND_BUFFER_LEVEL_PRIMARY,
        COMMAND_BUFFER_LEVEL_SECONDARY,
    } CommandBufferLevel;

    struct CommandPool : public INGIResourceManager
    {
        CommandPool(CommandBufferLevel level, GLES3CommandQueue* queue)
            : mLevel(level)
            , mQueue{queue}
        {}

        ~CommandPool() 
        {
            for (auto& cmdList : mFreeCmdLists)
            {
                delete cmdList;
            }
            for (auto& [_, cmdList] : mPendingCmdLists)
            {
                delete cmdList;
            }
        }

        GLES3CommandQueue* const mQueue;
        CommandBufferLevel const mLevel;

        std::deque<std::tuple<UInt32, GLES3CommandList*>> mPendingCmdLists;

        std::vector<GLES3CommandList*> mFreeCmdLists;

        void OnBeginFrame(FrameParam* frameparam) override;

        void EndFrame() override {}

        void Allocate(UInt32 num, void** commandLists);
    };

    CommandPool mCmdPool{COMMAND_BUFFER_LEVEL_PRIMARY, this};

    CommandPool mBundleCmdPool{COMMAND_BUFFER_LEVEL_SECONDARY, this};

    friend struct GLES3CommandList;
};

}   // namespace cross
