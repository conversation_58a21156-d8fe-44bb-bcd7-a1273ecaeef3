#pragma once
#include "VulkanInclude.h"
#include "NativeGraphicsInterface/NGI.h"
#include "CrossSchema/ShaderDefines_generated.h"
#include <variant>

namespace cross {

class VulkanDevice;
struct VulkanCommandQueue;
inline VkImageAspectFlags GraphicsFormat2ImageAspect(GraphicsFormat format) 
{
    switch (format)
    {
    case GraphicsFormat::D16_UNorm:
    case GraphicsFormat::D24_UNorm_X8:
    case GraphicsFormat::D24_UNorm_S8_UInt:
    case GraphicsFormat::D32_SFloat:
    case GraphicsFormat::D32_SFloat_S8_UInt:
    case GraphicsFormat::S8_UInt:
    //case GraphicsFormat::DepthStencilFirst:
    //case GraphicsFormat::DepthStencilLast:
        return VK_IMAGE_ASPECT_DEPTH_BIT;
    default:
        return VK_IMAGE_ASPECT_COLOR_BIT;
    }
}
struct VulkanTexture : public NGITexture
{
    VulkanTexture(const NGITextureDesc& desc, VulkanDevice* pDevice, const char* pDebugName = "", bool shared = false);
    VulkanTexture(const NGITextureDesc& desc, VulkanDevice* pDevice, bool hasMemory, const char* pDebugName = "", bool shared = false);
    VulkanTexture(const NGITextureDesc& desc, const VkImageCreateInfo& createInfo, VulkanDevice* pDevice, const char* pDebugName = "", bool shared = false);
    VulkanTexture(const NGITextureDesc& desc, VkImage image, VulkanDevice* pDevice, const char* pDebugName = "");

    ~VulkanTexture();

    virtual UInt64 GetSize() const override
    {
        return mAllocationInfo.size;
    }

    bool ManagedBySwapchain() override
    {
        return mExternal;
    }

    void SetDebugName(const char* pName) override;

    UInt64 GetNativeHandle() override
    {
        return reinterpret_cast<UInt64>(mTexture);
    }

    UInt64 GetMemoryNativeHandle() override {
        return reinterpret_cast<UInt64>(mAllocationInfo.deviceMemory);
    }

    NGI_API VkDeviceMemory GetAllocationHandle();

    bool mExternal = false;

    VkImage mTexture = VK_NULL_HANDLE;

    VmaAllocation mAllocation = VK_NULL_HANDLE;

    VmaAllocationInfo mAllocationInfo;

    friend struct VulkanSwapchain;
    static VkImageCreateInfo CreateImageCreateInfo(const NGITextureDesc& desc, bool shared = false);
};

struct VulkanBuffer : public NGIBuffer
{
    VulkanBuffer(const NGIBufferDesc& desc, VulkanDevice* pDevice, const char* pDebugName = "");
    VulkanBuffer(const NGIBufferDesc& desc, VulkanDevice* pDevice, bool bHasMemory, const char* pDebugName = "");
    ~VulkanBuffer();

    NGI_API static UInt32 CalculateBufferAlignment(VulkanDevice* pDevice, NGIBufferUsage usage, bool bZeroSize);

    void SetDebugName(const char* pName) override;
    
    uint64_t GetDeviceAddress(uint64_t Offset = 0);

    UInt64 GetNativeHandle() override
    {
        return reinterpret_cast<UInt64>(mBuffer);
    }

    bool Mappable() override;

    void* Map() override;

    void Unmap() override;

    SizeType AlignOffset(NGIBufferUsage usage, SizeType offset, SizeType range) override;

    VkBuffer mBuffer = VK_NULL_HANDLE;

    VmaAllocation mAllocation = VK_NULL_HANDLE;

    VmaAllocationInfo mAllocationInfo;
};

struct VulkanTextureView : public NGITextureView
{
    VulkanTextureView(NGITexture* pTexture, const NGITextureViewDesc& desc, VulkanDevice* pDevice);

    ~VulkanTextureView();

    void SetDebugName(const char* pName) override;

    UInt64 GetNativeHandle() override
    {
        return reinterpret_cast<UInt64>(mView);
    }

    VkImageView mView;
};

struct VulkanBufferView : public NGIBufferView
{
    VulkanBufferView(NGIBuffer* pBuffer, const NGIBufferViewDesc& desc, VulkanDevice* pDevice);

    ~VulkanBufferView();

    UInt64 GetNativeHandle() override
    {
        return mView ? reinterpret_cast<UInt64>(mView) : reinterpret_cast<UInt64>(mBuffer);
    }

    void SetDebugName(const char* pName) override;

    VkBuffer mBuffer = VK_NULL_HANDLE;

    VkBufferView mView = VK_NULL_HANDLE;
};

struct VulkanSampler : public NGISampler
{

    VulkanSampler(const NGISamplerDesc& desc, VulkanDevice* pDevice);

    ~VulkanSampler();

    UInt64 GetNativeHandle() override
    {
        return reinterpret_cast<UInt64>(mSampler);
    }

    VkSampler mSampler;
};

struct VulkanStagingBuffer : public NGIStagingBuffer
{

    VulkanStagingBuffer(const NGIBufferDesc& desc, VulkanDevice* pDevice, const char* pDebugName = "");

    ~VulkanStagingBuffer();

    void Allocation(const NGIBufferDesc& desc) override;

    void SetDebugName(const char* pDebugName) override;

    uint64_t GetDeviceAddress(uint64_t Offset = 0);

    UInt64 GetNativeHandle() override
    {
        return reinterpret_cast<UInt64>(mBuffer);
    }

    SizeType AlignOffset(NGIBufferUsage usage, SizeType offset, SizeType range) override;

    void* MapRange(NGIBufferUsage usage, SizeType Offset, SizeType Range) override
    {
        // EStagingBufferUsage::eDynamicBuffer can't be mapped, only CopySrc and CopyDst could call MapRange
        Assert(mAllocationInfo.pMappedData);
        return static_cast<uint8_t*>(mAllocationInfo.pMappedData) + Offset;
    }

    void UnmapRange(SizeType Offset, SizeType Range) override {};

    void FreeRange(SizeType Offset, SizeType Range) override {}

    VkBuffer mBuffer = 0;

    VmaAllocation mAllocation;
    VmaAllocationInfo mAllocationInfo;
};

}   // namespace cross
