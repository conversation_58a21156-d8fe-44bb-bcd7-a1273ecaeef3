#pragma once
#include "VulkanInclude.h"
#include "NativeGraphicsInterface/NGI.h"
#include <shared_mutex>
#include <optional>
#include <list>
#include <tuple>
#include <queue>
#if _WIN32
#include <memory_resource>
#endif
#include "CECommon/Allocator/FrameAllocator.h"
#include "CECommon/Common/FrameStdContainer.h"
namespace cross {

class VulkanDevice;
struct VulkanTexture;
struct VulkanBuffer;
struct VulkanStagingBuffer;
struct VulkanResourceGroupPool;
struct VulkanPipelineStatePool;
struct VulkanCommandQueue;

struct VulkanResourceGroupLayout : public NGIResourceGroupLayout
{
    VulkanResourceGroupLayout(const NGIResourceGroupLayoutDesc& desc, VulkanDevice* device);

    ~VulkanResourceGroupLayout();

    UInt64 GetNativeHandle() override
    {
        return reinterpret_cast<UInt64>(mDescriptorSetLayout);
    }

    VkDescriptorSetLayout mDescriptorSetLayout = VK_NULL_HANDLE;

    std::map<NameID, VkDescriptorSetLayoutBinding> mID2DescriptorBinding;

    size_t mDescriptorBindingCount = 0;

    std::map<NameID, UInt32> mDynamicBufferIndices;

    friend struct VulkanResourceGroup;
};

struct VulkanPipelineLayout : public NGIPipelineLayout
{

    VulkanPipelineLayout(const NGIPipelineLayoutDesc& desc, VulkanDevice* pDevice);

    ~VulkanPipelineLayout();

    UInt64 GetNativeHandle() override
    {
        return reinterpret_cast<UInt64>(mPipelineLayout);
    }

    VkPipelineLayout mPipelineLayout;

    struct DescriptorBinding
    {
        UInt32 Space;
        VkDescriptorSetLayoutBinding Binding;
    };

    // ID to space and binding description
    std::map<NameID, DescriptorBinding> mID2DescriptorBinding;
};

struct DescriptorSetCreator;
struct DescriptorSet
{
    DescriptorSet() = default;

    DescriptorSet(DescriptorSet&& other)
    {
        Set = std::move(other.Set);
        Pool = std::move(other.Pool);
        Lifetime = std::move(other.Lifetime);
        DynamicBuffers = std::move(other.DynamicBuffers);
    }

    struct DynamicBufferKey
    {
        NameID ID;
        UInt32 Offset;
    };

    struct DynamicBuffer
    {
        // dynamic buffer index in this descriptor set
        UInt32 Index;
        VkDeviceSize Offset;
        VkDeviceSize Range;
    };

    VkDescriptorSet Set = VK_NULL_HANDLE;

    // which pool create this Set
    DescriptorSetCreator* Pool = nullptr;
    // life time for dynamic usage
    mutable UInt32 Lifetime = 0;
    // ref count for static usage
    std::atomic_uint32_t RefCount = 0;

    struct Hasher
    {
        bool operator()(const DynamicBufferKey& a, const DynamicBufferKey& b) const;
    };

    std::map<DynamicBufferKey, DynamicBuffer, Hasher> DynamicBuffers;
};

struct VulkanResourceGroup : public NGIResourceGroup
{
    VulkanResourceGroup(VulkanResourceGroupLayout* layout, VulkanResourceGroupPool* pool);

    ~VulkanResourceGroup() override;

    UInt64 GetNativeHandle() override
    {
        return reinterpret_cast<UInt64>(mDescriptorSet);
    }

    void SetSamplers(NameID ID, UInt32 ArrayOffset, UInt32 Num, NGISampler** ppSamplers) override;

    void SetTextures(NameID ID, UInt32 ArrayOffset, UInt32 Num, NGITextureView** ppTextureViews) override;

    void SetBuffers(NameID ID, UInt32 ArrayOffset, UInt32 Num, NGIBufferView** ppBufferViews) override;

    void SetConstBuffer(NameID ID, NGIBuffer* pConstBuffer, SizeType Offset, SizeType Range) override;

    void SetAccelStruct(NameID ID, NGIAccelStruct* accel) override;

    VkDescriptorSet mDescriptorSet = VK_NULL_HANDLE;

    DescriptorSet* mSharedDescriptorSet = nullptr;

    std::vector<UInt32> mDynamicOffsets;

    void Initialize(VulkanResourceGroupLayout* layout);
};

/*
 * Persistent resource group
 * Create a descriptor pool for each resource group
 */
struct VulkanBindlessResourceGroup : public VulkanResourceGroup
{
    VulkanBindlessResourceGroup(VulkanResourceGroupLayout* layout, VulkanResourceGroupPool* pool, NGIBindlessResourceType bindlessResourceType, UInt32 maxBindlessResourceCount);

    ~VulkanBindlessResourceGroup() override;
    
    VkDescriptorPool mRawVulkanPool = VK_NULL_HANDLE;
    
    UInt32 mMaxBindlessResourceCount;
};

struct ResourceBindingKey
{
    ResourceBindingKey(VulkanResourceGroupLayout* layout, UInt32 num, const NGIResourceBinding* bindings, FrameAllocator* alloc = nullptr);
#if _WIN32
    std::pmr::vector<UInt8> mData;
    FrameAllocatorPool mPool;
#else
    std::vector<UInt8> mData;
#endif
    struct Hasher
    {
        bool operator()(const ResourceBindingKey& a, const ResourceBindingKey& b) const;
        size_t operator()(const ResourceBindingKey& desc) const;
    };
};

using DescriptorSetPoolType = std::unordered_map<ResourceBindingKey, std::list<DescriptorSet>, ResourceBindingKey::Hasher, ResourceBindingKey::Hasher>;

struct DescriptorSetCreator
{
    DescriptorSetCreator() = default;
    ~DescriptorSetCreator();

    DescriptorSet* Allocate(UInt32 FrameId, const ResourceBindingKey& resourceBindingKey, VulkanResourceGroupLayout* mLayout, UInt32 numBindings, const NGIResourceBinding* bindings, VulkanResourceGroup* outResourceGroup);

    void Init(VulkanDevice* device);

    DescriptorSetPoolType ReleaseDescriptorSetPool()
    {
        return std::move(mDescriptorSets);
    }

    int32_t GetFreeSetNum() const
    {
        return mFreeSet;
    }

    void FreeDescriptorSet(VkDevice device, const std::vector<VkDescriptorSet> vkDescriptorSets);

private:
    DescriptorSetCreator(const DescriptorSetCreator&) = delete;
    DescriptorSetCreator& operator=(const DescriptorSetCreator&) = delete;
    DescriptorSetCreator(DescriptorSetCreator&&) = delete;
    DescriptorSetCreator& operator=(DescriptorSetCreator&&) = delete;

    VulkanDevice* mDevice{};
    VkDescriptorPool mPool{};
    std::mutex mPoolMutex;
    int32_t mFreeSet = 0;
    DescriptorSetPoolType mDescriptorSets{};
};

struct VulkanResourceGroupPool : public NGIResourceGroupPool
{
    VulkanResourceGroupPool(const NGIResourceGroupPoolDesc& desc, VulkanDevice* pDevice);

    ~VulkanResourceGroupPool();

    NGIResourceGroup* Allocate(NGIResourceGroupLayout* layout, UInt32 numBindings, NGIResourceBinding* bindings) override;
    
    NGIResourceGroup* Create(NGIResourceGroupLayout* layout, UInt32 numBindings, NGIResourceBinding* bindings) override;
    
    NGIResourceGroup* CreateBindlessResourceGroup(NGIResourceGroupLayout* layout, NGIBindlessResourceType BindlessResourceType, UInt32 maxBindlessResourceCount) override;

    void OnBeginFrame(FrameParam* frameparam) override;

    static DescriptorSet* FindReadyDescriptorSet(const DescriptorSetPoolType& descriptorSetPools, UInt32 frameID, const ResourceBindingKey& resourceBindingKey, VulkanResourceGroupLayout* vkLayout, UInt32 numBindings,
                                                 const NGIResourceBinding* bindings, VulkanResourceGroup* outResourceGroup);

private:
    std::tuple<VulkanResourceGroup*, DescriptorSet*> AllocateImpl(NGIResourceGroupLayout* layout, UInt32 numBindings, NGIResourceBinding* bindings);

    constexpr static UInt32 ACTUAL_NUMBER_POOL = 8;
    const static UInt32 MAX_DESCRIPTOR_SET_LIFE_TIME = 3;

    std::array<DescriptorSetCreator, ACTUAL_NUMBER_POOL> mDescriptorSetCreators;

    // add a random threadIndex mapping per frame to balance cost for each thread
    std::array<UInt32, ACTUAL_NUMBER_POOL> mThreadIndexMapping;
    DescriptorSetPoolType mReadyDescriptorSetPools;

    FrameAllocator* mFramePool = nullptr;

    // resource group holder can be reused every frame
    std::vector<std::unique_ptr<VulkanResourceGroup>> mResourceGroups;
    std::atomic_uint32_t mUsedResourceGroupCount{};
    std::shared_mutex mResourceGroupsMutex;

    VulkanResourceGroup* AllocateResourceGroup(VulkanResourceGroupLayout* layout);
};

struct VulkanGraphicsPipelineState : public NGIGraphicsPipelineState
{
    VulkanGraphicsPipelineState(const NGIGraphicsPipelineStateDesc& desc, VulkanPipelineStatePool* pool);

    ~VulkanGraphicsPipelineState();

    UInt64 GetNativeHandle() override
    {
        return reinterpret_cast<UInt64>(mPipeline);
    }

    VkPipeline mPipeline;
};

struct VulkanPipelineStatePool : public NGIPipelineStatePool
{
    VulkanPipelineStatePool(const NGIPipelineStatePoolDesc& desc, VulkanDevice* pDevice);

    ~VulkanPipelineStatePool();

    UInt64 GetNativeHandle() override
    {
        return reinterpret_cast<UInt64>(mCache);
    }

    NGIGraphicsPipelineState* AllocateGraphicsPipelineState(const NGIGraphicsPipelineStateDesc& desc) override;

    NGIComputePipelineState* CreateComputePipelineState(const NGIComputePipelineStateDesc& desc) override;

    NGIGraphicsPipelineState* CreateGraphicsPipelineState(const NGIGraphicsPipelineStateDesc& desc) override;

    NGIRayTracingPipelineState* CreateRayTracingPipelineState(const NGIRayTracingPipelineStateDesc& desc) override;

    void OnBeginFrame(FrameParam* frameparam) override;

    SizeType GetSize() override;

    void GetData(SizeType size, void* pData) override;

    VkPipelineCache mCache;

    using PipelineStateEntry = std::tuple<std::unique_ptr<VulkanGraphicsPipelineState>, UInt32>;

    using PipelineStateLib = std::list<PipelineStateEntry>;

    using PipelineStatePool = std::unordered_map<const NGIGraphicsPipelineStateDesc*, PipelineStateLib::iterator, NGIObjectDescHasher, NGIObjectDescHasher>;
    // need to set it to relative large to avoid createPipelines frequently
    const static int MAX_PIPELINESTATECACHE_NUM = 256;

    PipelineStateLib mPipeStatesLib;
    PipelineStatePool mPipelineStates;


    std::shared_mutex mPipelineStatesMutex;
};

struct VulkanComputePipelineState : public NGIComputePipelineState
{
    VulkanComputePipelineState(const NGIComputePipelineStateDesc& desc, VulkanPipelineStatePool* pool);

    ~VulkanComputePipelineState();

    UInt64 GetNativeHandle() override
    {
        return reinterpret_cast<UInt64>(mPipeline);
    }

    VkPipeline mPipeline;
};

struct VulkanRayTracingPipelineState : public NGIRayTracingPipelineState
{
    /**
     * Creates a ray tracing pipeline state object
     *
     * This constructor performs the following operations:
     * 1. Creates various ray tracing shaders (RayGen, Miss, Hit Group, Callable)
     * 2. Organizes shaders into shader groups
     * 3. Processes specialization constants (if any)
     * 4. Creates the ray tracing pipeline
     * 5. Creates shader binding tables (SBT)
     * 6. Cleans up temporary resources
     *
     * @param desc Ray tracing pipeline state description structure, containing shader programs, pipeline layout, etc.
     * @param pool Pipeline state pool, used to manage the lifecycle of pipeline state objects
     */
    VulkanRayTracingPipelineState(const NGIRayTracingPipelineStateDesc& desc, VulkanPipelineStatePool* pool);

    /**
     * Destroys the ray tracing pipeline state object and associated resources
     */
    ~VulkanRayTracingPipelineState();

    UInt64 GetNativeHandle() override
    {
        return reinterpret_cast<UInt64>(mPipeline);
    }

    /**
     * Retrieves shader binding table regions for ray tracing dispatch
     *
     * This function returns the regions needed for vkCmdTraceRaysKHR
     *
     * @param raygenRegion [out] Region for ray generation shaders
     * @param missRegion [out] Region for miss shaders
     * @param hitRegion [out] Region for hit group shaders
     * @param callableRegion [out] Region for callable shaders
     */
    void GetShaderBindingTableRegions(
        void* raygenRegion,
        void* missRegion,
        void* hitRegion,
        void* callableRegion) const override
    {
        if (raygenRegion) {
            *static_cast<VkStridedDeviceAddressRegionKHR*>(raygenRegion) = mSBT.RayGenRegion;
        }
        if (missRegion) {
            *static_cast<VkStridedDeviceAddressRegionKHR*>(missRegion) = mSBT.MissRegion;
        }
        if (hitRegion) {
            *static_cast<VkStridedDeviceAddressRegionKHR*>(hitRegion) = mSBT.HitRegion;
        }
        if (callableRegion) {
            *static_cast<VkStridedDeviceAddressRegionKHR*>(callableRegion) = mSBT.CallableRegion;
        }
    }

    VkPipeline mPipeline = VK_NULL_HANDLE;

    // TODO(scolu): NGI Abstract?
    struct ShaderBindingTable {
        VulkanStagingBuffer* Buffer = nullptr;  // single buffer for all sbt data

        // SBT region information
        VkStridedDeviceAddressRegionKHR RayGenRegion = {};
        VkStridedDeviceAddressRegionKHR MissRegion = {};
        VkStridedDeviceAddressRegionKHR HitRegion = {};
        VkStridedDeviceAddressRegionKHR CallableRegion = {};

        ~ShaderBindingTable();
    } mSBT;

    // Shader group counts (for debugging and resource management)
    UInt32 mRayGenShaderCount = 1; // Always have one ray gen shader
    UInt32 mMissShaderCount = 0;
    UInt32 mHitGroupCount = 0;
    UInt32 mCallableShaderCount = 0;

private:
    /**
     * Creates shader binding table buffers and regions
     *
     * @param device Vulkan device
     * @param shaderHandleData Shader group handle data
     * @param handleSize Size of each shader group handle
     * @param ShaderGroupBaseAlignment Aligned size of each shader group handle
     */
    void CreateShaderBindingTable(
        VulkanDevice* device,
        void* shaderHandleData,
        UInt32 handleSize,
        UInt32 ShaderGroupBaseAlignment);
};


struct VulkanRenderPass : public NGIRenderPass
{

    VulkanRenderPass(const NGIRenderPassDesc& desc, VulkanDevice* pDevice);

    ~VulkanRenderPass();

    UInt64 GetNativeHandle() override
    {
        return reinterpret_cast<UInt64>(mRenderPass);
    }

    void SetDebugName(const char* pDebugName) override;

    VkRenderPass mRenderPass;
};

struct VulkanFramebuffer : public NGIFramebuffer
{

    VulkanFramebuffer(const NGIFramebufferDesc& desc, VulkanDevice* pDevice);

    ~VulkanFramebuffer();

    UInt64 GetNativeHandle() override
    {
        return reinterpret_cast<UInt64>(mFramebuffer);
    }

    void SetDebugName(const char* pDebugName) override;

    VkFramebuffer mFramebuffer;
};

struct VulkanCommandList : public NGICommandList
{
    VulkanCommandList(VkCommandBufferLevel, VkCommandPool, VulkanCommandQueue*);

    VulkanCommandList(const VulkanCommandList&) = default;

    virtual ~VulkanCommandList(){};

    void Begin() override;

    void BeginRenderPass(NGIRenderPass* pRenderPass, NGIFramebuffer* pFramebuffer, UInt32 ClearValueCount, NGIClearValue* pClearValues, UInt32 TargetBarrierCount, NGITextureBarrier* TargetBarriers, bool useBundle) override;

    void ForkBundleCommandLists(UInt32 num, NGIBundleCommandList* const* ppBundleCmdLists) override;

    void JoinBundleCommandLists() override;

    void SetGraphicsPipelineState(NGIGraphicsPipelineState* pPipelineState) override;

    void SetGraphicsResourceGroup(UInt32 slot, NGIResourceGroup* pResourceGroup) override;

    void SetVertexBuffers(UInt32 num, NGIBuffer** ppVertexBuffers, UInt64* pOffsets) override;

    void SetIndexBuffer(NGIBuffer* pIndexBuffer, UInt64 offset, GraphicsFormat format) override;

    void SetViewports(UInt32 num, const NGIViewport* pViewports) override;

    void SetScissors(UInt32 num, const NGIScissor* pScissors) override;

    void DrawInstanced(UInt32 vertexCountPerInstance, UInt32 instanceCount, UInt32 startVertexLocation, UInt32 startInstanceLocation) override;

    void DrawIndexedInstanced(UInt32 indexCountPerInstance, UInt32 instanceCount, UInt32 startIndexLocation, SInt32 baseVertexLocation, UInt32 startInstanceLocation) override;

    void DrawIndexedIndirect(NGIBuffer* buffer, UInt32 offset, UInt32 drawCount, UInt32 stride) override;

    void DrawIndexedIndirectCount(NGIBuffer* buffer, UInt32 offset, NGIBuffer* countBuffer, UInt32 countBufferOffset, UInt32 maxDrawCount, UInt32 stride) override;

    void DrawIndirect(NGIBuffer* buffer, UInt32 offset, UInt32 drawCount, UInt32 stride) override;

    void SetStencilRef(UInt32 stencilRef) override;

    void NextSubPass(bool useBundle) override;

    void EndRenderPass() override;

    void SetComputePipelineState(NGIComputePipelineState* pPipelineState) override;

    void SetComputeResourceGroup(UInt32 slot, NGIResourceGroup* pResourceGroup) override;

    void Dispatch(UInt32 threadGroupCountX, UInt32 threadGroupCountY, UInt32 threadGroupCountZ) override;

    void DispatchIndirect(NGIBuffer* argumentBuffer, UInt32 offset) override;

    void CopyBufferToBuffer(NGIBuffer* pDstBuffer, NGIBuffer* pSrcBuffer, UInt32 RegionCount, const NGICopyBuffer* pRegions) override;

    void CopyBufferToTexture(NGITexture* pDstTexture, NGIBuffer* pSrcBuffer, UInt32 RegionCount, const NGICopyBufferTexture* pRegions) override;

    void CopyTextureToBuffer(NGIBuffer* dstBuffer, NGITexture* srcTexture, UInt32 regionCount, const NGICopyBufferTexture* regions) override;

    void CopyTextureToTexture(NGITexture* dstTexture, NGITexture* srcTexture, UInt32 regionCount, const NGICopyTexture* regions) override;

    void ClearTexture(NGITextureView* textureView, const NGIClearValue& clearValue) override;

    void ClearBuffer(NGIBufferView* bufferView, UInt32 clearValue) override;

    void ResourceBarrier(UInt32 bufferBarrierCount, NGIBufferBarrier* pBufferBarrieres, UInt32 textureBarrierCount, NGITextureBarrier* pTextureBarriers) override;

    void MemBarrier(NGIMemoryBarrier* pMemBarrier) override;
    
#ifdef NGI_ENABLE_RAY_TRACING
    /**
     * Bind RayTracing Pipeline
     * @param pPipelineState
     */
    void SetRayTracingPipeline(NGIRayTracingPipelineState* pPipelineState) override;

    /**
     * Bind RayTracing Pipeline Resource Group
     * @param slot 
     * @param pResourceGroup 
     */
    void SetRayTracingResourceGroup(UInt32 slot, NGIResourceGroup* pResourceGroup) override;
    
    /**
     * Creates an acceleration structure object for ray tracing
     *
     * This function allocates and initializes either a Bottom Level Acceleration Structure (BLAS)
     * or a Top Level Acceleration Structure (TLAS) based on the description provided.
     *
     * @param Desc Description of the acceleration structure to create, including type (BLAS/TLAS),
     *             geometry data for BLAS, and build flags
     * @return Pointer to the created acceleration structure object, or nullptr on failure
     */
    NGIAccelStruct* CreateAccelStruct(const NGIAccelStructDesc& Desc) override;

    /**
     * Builds a Bottom Level Acceleration Structure (BLAS) for ray tracing
     *
     * @param AS The acceleration structure object to build
     * @param pGeometries Array of geometry descriptions (triangles, AABBs, etc.)
     * @param NumGeometries Number of geometries in the array
     * @param BuildFlags Flags controlling the build process (allow update, prefer fast trace, etc.)
     * @param ScratchBuffer
     */
    void BuildBottomLevelAccelStruct(NGIAccelStruct* AS, const NGIGeometryDesc* pGeometries, size_t NumGeometries,
        NGIAccelStructBuildFlag BuildFlags, void* ScratchBuffer = nullptr) override;

    /**
     * Compacts a previously built Bottom Level Acceleration Structure to reduce memory usage
     *
     * This function performs compaction on a BLAS that was built with the AllowCompaction flag.
     * Compaction can significantly reduce the memory footprint of acceleration structures.
     */
    void CompactBottomLevelAccelStruct() override;

    /**
     * Builds a Top Level Acceleration Structure (TLAS) for ray tracing
     *
     * @param AS The acceleration structure object to build
     * @param pInstances Array of instance descriptions, each referencing a BLAS
     * @param NumInstances Number of instances in the array
     * @param BuildFlags Flags controlling the build process (allow update, prefer fast trace, etc.)
     */
    void BuildTopLevelAccelStruct(NGIAccelStruct* AS, const NGIInstanceDesc* pInstances, size_t NumInstances,
        NGIAccelStructBuildFlag BuildFlags, void* ScratchBuffer = nullptr) override;

    /**
     * Builds a Top Level Acceleration Structure (TLAS) using instance data from a GPU buffer
     *
     * This variant is useful when instance data is already on the GPU or is dynamically generated.
     *
     * @param AS The acceleration structure object to build
     * @param InstanceBuffer Buffer containing instance data in the required format
     * @param InstanceBufferOffset Offset into the buffer where instance data begins
     * @param pInstances
     * @param NumInstances Number of instances in the buffer
     * @param BuildFlags Flags controlling the build process (allow update, prefer fast trace, etc.)
     */
    void BuildTopLevelAccelStructFromBuffer(NGIAccelStruct* AS, ScratchBufferWrap InstanceBuffer, UInt64 InstanceBufferOffset,
        const NGIInstanceDesc* pInstances, size_t NumInstances, NGIAccelStructBuildFlag BuildFlags, void* ScratchBuffer = nullptr) override;

    /**
     * Internal implementation for building a Top Level Acceleration Structure
     *
     * This is a helper function used by both BuildTopLevelAccelStruct and BuildTopLevelAccelStructFromBuffer
     * to avoid code duplication.
     *
     * @param AS The acceleration structure object to build
     * @param InstanceData GPU device address pointing to instance data
     * @param NumInstances Number of instances
     * @param BuildFlags Flags controlling the build process
     */
    void BuildTopLevelAccelStructInternal(NGIAccelStruct* AS, VkDeviceAddress InstanceData, size_t NumInstances,
        NGIAccelStructBuildFlag BuildFlags, void* ScratchBuffer);

    /** 
     * Wait for blases Build Finish using when building TLAS
     */
    void BLASBarrier(UInt32 NumBlas, NGIAccelStruct* blases) override;

    /** 
     * Wait for tlas build finish
     */
    void TLASBarrier(NGIAccelStruct* tlas) override;

    /**
     * Traces rays using the specified ray tracing pipeline state
     *
     * @param pipelineState Ray tracing pipeline state to use
     * @param width Width dimension of the ray trace (typically the render target width)
     * @param height Height dimension of the ray trace (typically the render target height)
     * @param depth Depth dimension of the ray trace (typically 1)
     */
    void TraceRays(NGIRayTracingPipelineState* pipelineState, uint32_t width, uint32_t height, uint32_t depth);
#endif

    void BeginDebugRegion(const char* label, const float* color, bool bAddTimeStamp = true) override;

    void EndDebugRegion(bool bAddTimeStamp = true) override;

    void EndQuery(NGIQueryHeap* queryHeap, NGIQueryHeapType type, UInt32 index) override;

    void ResolveQueryData(NGIQueryHeap* queryHeap, NGIQueryHeapType type, UInt32 index, UInt32 numQueries, NGIBuffer* dstBuffer, UInt64 DestinationBufferOffset) override;

    void End() override;

    UInt64 GetNativeHandle() override
    {

        return reinterpret_cast<UInt64>(mCommandList);

    }

    NGICommandQueue* GetCommandQueue() override
    {
        return reinterpret_cast<NGICommandQueue*>(const_cast<VulkanCommandQueue*>(mQueue));
    }

    VkCommandBuffer mCommandList;

    void Reset();

private:
    void ResourceBarrier1(UInt32 bufferBarrierCount, NGIBufferBarrier* pBufferBarrieres, UInt32 textureBarrierCount, NGITextureBarrier* pTextureBarriers);

    void ResourceBarrier2(UInt32 bufferBarrierCount, NGIBufferBarrier* pBufferBarrieres, UInt32 textureBarrierCount, NGITextureBarrier* pTextureBarriers);

    VkCommandBufferLevel const mLevel;

    VkCommandPool const mCommandPool;

    VulkanCommandQueue* const mQueue = nullptr;

    VulkanGraphicsPipelineState* mGraphicsPipeline = nullptr;

    VulkanComputePipelineState* mComputePipeline = nullptr;

    VulkanRayTracingPipelineState* mRayTracingPipeline = nullptr;

    VkRenderPass mRenderPass = VK_NULL_HANDLE;

    UInt32 mSubpass = 0;

    VkFramebuffer mFramebuffer = VK_NULL_HANDLE;

    std::vector<VkCommandBuffer> mBundleCommandLists;
};


struct VulkanFenceBase : public NGIFence
{
    VulkanFenceBase(VulkanDevice* pDevice);

    virtual ~VulkanFenceBase() {};

    virtual void Signal(VkQueue queue, UInt64 value, UInt64 waitValue = 0) = 0;
    virtual void Wait(VkQueue queue, UInt64 value) {};
};

/*
 * TODO: refactor when Vulkan1.2 is engaged
 */

struct VulkanFence : public VulkanFenceBase
{
    VulkanFence(UInt64 initialValue, VulkanDevice* pDevice);
    ~VulkanFence();

    UInt64 GetCompletedValue() override;

    bool Wait(UInt64 value, UInt64 timeout) override;
    void Signal(UInt64 value) override {}
    void Signal(VkQueue queue, UInt64 value, UInt64 waitValue = 0) override;

    explicit operator VkFence() const noexcept { return mFence; }

private:
    constexpr static VkFenceCreateInfo gFenceCreateInfo{
        VK_STRUCTURE_TYPE_FENCE_CREATE_INFO,
        nullptr,
        0   // unsignal state while fence initialized
    };

    UInt64 mCurrentValue;
    UInt64 mCompleteValue;

    VkFence mFence;
};

/* Used when Vulan 1.2 supported */
struct VulkanTimelineSemaphore : public VulkanFenceBase
{
    VulkanTimelineSemaphore(UInt64 initialValue, VulkanDevice* pDevice, bool external = false);
    ~VulkanTimelineSemaphore();

    UInt64 GetCompletedValue() override;

    bool Wait(UInt64 value, UInt64 timeout) override;

    void Signal(UInt64 value) override;

    void Wait(VkQueue queue, UInt64 value) override;

    void Signal(VkQueue queue, UInt64 value, UInt64 waitValue = 0) override;

    UInt64 GetNativeHandle() override
    {
        return reinterpret_cast<UInt64>(mTimelineSemaphore);
    }

    UInt64 GetSignalValue()
    {
        return mSignalValue;
    }

    VkSemaphore mTimelineSemaphore;

    UInt64 mSignalValue;

    bool mExternal;

    std::mutex mMutex;
};


struct VulkanCommandQueue : public NGICommandQueue
{
    struct CommandPool : public INGIResourceManager
    {
        CommandPool(VkCommandBufferLevel level, VulkanCommandQueue* queue)
            : mLevel{level}
            , mQueue{queue}
        {}

        ~CommandPool();

        VulkanCommandQueue* const mQueue;
        VkCommandBufferLevel const mLevel;

        std::deque<std::tuple<UInt32, VkCommandPool, VulkanCommandList>> mPendingCmdLists;

        std::vector<std::tuple<VkCommandPool, VulkanCommandList>> mFreeCmdLists;

        std::mutex mMutex;

        void OnBeginFrame(FrameParam* frameparam) override;

        void EndFrame() override;

        void Allocate(UInt32 num, void** ppCommandLists);
    };

    VulkanCommandQueue(const NGICommandQueueDesc& desc, VulkanDevice* pDevice);

    ~VulkanCommandQueue();

    UInt64 GetNativeHandle() override
    {
        return reinterpret_cast<UInt64>(mCommandQueue);
    }

    void AllocateCommandLists(UInt32 num, NGICommandList** ppCommandLists) override;

    void AllocateBundleCommandLists(UInt32 num, NGIBundleCommandList** ppCommandLists) override;

    void ExecuteCommandLists(UInt32 num, NGICommandList** ppLists) override;

    void Signal(NGIFence* pFence, UInt64 value) override;

    void Wait(NGIFence* pFence, UInt64 value) override;

    static void QueueSubmit(VkQueue& queue, UInt32 num, VkSubmitInfo& info);

    void OnBeginFrame(FrameParam* frameparam) override
    {
        mCmdPool.BeginFrame(frameparam);
        mBundleCmdPool.BeginFrame(frameparam);
    }

    void EndFrame() override
    {
        mCmdPool.EndFrame();
        mBundleCmdPool.EndFrame();
    }

    VkQueue mCommandQueue;

    std::mutex mMutex;
    static std::mutex mSubmitMutex;

    NGICommandQueueType mType;

    uint32_t mFamilyIndex;

    uint32_t mIndex;

    CommandPool mCmdPool{VK_COMMAND_BUFFER_LEVEL_PRIMARY, this};

    CommandPool mBundleCmdPool{VK_COMMAND_BUFFER_LEVEL_SECONDARY, this};

    friend struct VulkanCommandList;
};

struct VulkanQueryHeap : public NGIQueryHeap
{
    VulkanQueryHeap(const NGIQueryHeapDesc& desc, VulkanDevice* device);

    ~VulkanQueryHeap();

    VkQueryPool mQueryPool;
};
}   // namespace cross