#include "PlatformDefs.h"




#ifdef CROSSENGINE_WIN

template<typename T> auto VMA_MAX(const T& x, const T& y)
{
    return x > y ? x : y;
}

template<typename T> auto VMA_MIN(const T& x, const T& y)
{
    return x < y ? x : y;
}

#    define VMA_MAX VMA_MAX
#    define VMA_MIN VMA_MIN

#endif


#include "VulkanInclude.h"

#if !CROSSENGINE_IOS
#    define VOLK_IMPLEMENTATION
#    include "volk.h"
#endif

#define VMA_IMPLEMENTATION
#define VMA_STATIC_VULKAN_FUNCTIONS 0

#if !CROSSENGINE_WIN
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wnullability-completeness"
#pragma clang diagnostic ignored "-Wswitch"
#include "vk_mem_alloc.h"
#pragma clang diagnostic pop
#else
#pragma warning(push)
#pragma warning(disable : 4100; disable : 4127; disable : 4189)
#include "vk_mem_alloc.h"
#pragma warning(pop)
#endif


#include "External/Vulkan/include/vulkan/vulkan.hpp"

void cross::PrintCheckResult(VkResult result, const char* exp)
{
#if CROSSENGINE_OSX || CROSSENGINE_ANDROID
    // TODO(peterwjma): investigate how to match the surface properties on macos
    if (result == VK_SUBOPTIMAL_KHR)
        return;
#endif
    LOG_ERROR("{} {}", exp, result);
#if NGI_BREAK_ON_ERROR
    DEBUG_BREAK;
#endif
}

VkFormat cross::MapGraphicsFormat(GraphicsFormat format)
{
    switch (format)
    {
    case GraphicsFormat::Unknown:
        return VK_FORMAT_UNDEFINED;
    case GraphicsFormat::R8_SRGB:
        return VK_FORMAT_R8_SRGB;
    case GraphicsFormat::R8G8_SRGB:
        return VK_FORMAT_R8G8_SRGB;
    case GraphicsFormat::R8G8B8_SRGB:
        return VK_FORMAT_R8G8B8_SRGB;
    case GraphicsFormat::R8G8B8A8_SRGB:
        return VK_FORMAT_R8G8B8A8_SRGB;
    case GraphicsFormat::R8_UNorm:
        return VK_FORMAT_R8_UNORM;
    case GraphicsFormat::R8G8_UNorm:
        return VK_FORMAT_R8G8_UNORM;
    case GraphicsFormat::R8G8B8_UNorm:
        return VK_FORMAT_R8G8B8_UNORM;
    case GraphicsFormat::R8G8B8A8_UNorm:
        return VK_FORMAT_R8G8B8A8_UNORM;
    case GraphicsFormat::R8_SNorm:
        return VK_FORMAT_R8_SNORM;
    case GraphicsFormat::R8G8_SNorm:
        return VK_FORMAT_R8G8_SNORM;
    case GraphicsFormat::R8G8B8_SNorm:
        return VK_FORMAT_R8G8B8_SNORM;
    case GraphicsFormat::R8G8B8A8_SNorm:
        return VK_FORMAT_R8G8B8A8_SNORM;
    case GraphicsFormat::R8_UInt:
        return VK_FORMAT_R8_UINT;
    case GraphicsFormat::R8G8_UInt:
        return VK_FORMAT_R8G8_UINT;
    case GraphicsFormat::R8G8B8_UInt:
        return VK_FORMAT_R8G8B8_UINT;
    case GraphicsFormat::R8G8B8A8_UInt:
        return VK_FORMAT_R8G8B8A8_UINT;
    case GraphicsFormat::R8_SInt:
        return VK_FORMAT_R8_SINT;
    case GraphicsFormat::R8G8_SInt:
        return VK_FORMAT_R8G8_SINT;
    case GraphicsFormat::R8G8B8_SInt:
        return VK_FORMAT_R8G8B8_SINT;
    case GraphicsFormat::R8G8B8A8_SInt:
        return VK_FORMAT_R8G8B8A8_SINT;
    case GraphicsFormat::R16_UNorm:
        return VK_FORMAT_R16_UNORM;
    case GraphicsFormat::R16G16_UNorm:
        return VK_FORMAT_R16G16_UNORM;
    case GraphicsFormat::R16G16B16_UNorm:
        return VK_FORMAT_R16G16B16_UNORM;
    case GraphicsFormat::R16G16B16A16_UNorm:
        return VK_FORMAT_R16G16B16A16_UNORM;
    case GraphicsFormat::R16_SNorm:
        return VK_FORMAT_R16_SNORM;
    case GraphicsFormat::R16G16_SNorm:
        return VK_FORMAT_R16G16_SNORM;
    case GraphicsFormat::R16G16B16_SNorm:
        return VK_FORMAT_R16G16B16_SNORM;
    case GraphicsFormat::R16G16B16A16_SNorm:
        return VK_FORMAT_R16G16B16A16_SNORM;
    case GraphicsFormat::R16_UInt:
        return VK_FORMAT_R16_UINT;
    case GraphicsFormat::R16G16_UInt:
        return VK_FORMAT_R16G16_UINT;
    case GraphicsFormat::R16G16B16_UInt:
        return VK_FORMAT_R16G16B16_UINT;
    case GraphicsFormat::R16G16B16A16_UInt:
        return VK_FORMAT_R16G16B16A16_UINT;
    case GraphicsFormat::R16_SInt:
        return VK_FORMAT_R16_SINT;
    case GraphicsFormat::R16G16_SInt:
        return VK_FORMAT_R16G16_SINT;
    case GraphicsFormat::R16G16B16_SInt:
        return VK_FORMAT_R16G16B16_SINT;
    case GraphicsFormat::R16G16B16A16_SInt:
        return VK_FORMAT_R16G16B16A16_SINT;
    case GraphicsFormat::R32_UInt:
        return VK_FORMAT_R32_UINT;
    case GraphicsFormat::R32G32_UInt:
        return VK_FORMAT_R32G32_UINT;
    case GraphicsFormat::R32G32B32_UInt:
        return VK_FORMAT_R32G32B32_UINT;
    case GraphicsFormat::R32G32B32A32_UInt:
        return VK_FORMAT_R32G32B32A32_UINT;
    case GraphicsFormat::R32_SInt:
        return VK_FORMAT_R32_SINT;
    case GraphicsFormat::R32G32_SInt:
        return VK_FORMAT_R32G32_SINT;
    case GraphicsFormat::R32G32B32_SInt:
        return VK_FORMAT_R32G32B32_SINT;
    case GraphicsFormat::R32G32B32A32_SInt:
        return VK_FORMAT_R32G32B32A32_SINT;
    case GraphicsFormat::R16_SFloat:
        return VK_FORMAT_R16_SFLOAT;
    case GraphicsFormat::R16G16_SFloat:
        return VK_FORMAT_R16G16_SFLOAT;
    case GraphicsFormat::R16G16B16_SFloat:
        return VK_FORMAT_R16G16B16_SFLOAT;
    case GraphicsFormat::R16G16B16A16_SFloat:
        return VK_FORMAT_R16G16B16A16_SFLOAT;
    case GraphicsFormat::R32_SFloat:
        return VK_FORMAT_R32_SFLOAT;
    case GraphicsFormat::R32G32_SFloat:
        return VK_FORMAT_R32G32_SFLOAT;
    case GraphicsFormat::R32G32B32_SFloat:
        return VK_FORMAT_R32G32B32_SFLOAT;
    case GraphicsFormat::R32G32B32A32_SFloat:
        return VK_FORMAT_R32G32B32A32_SFLOAT;
    case GraphicsFormat::B8G8R8_SRGB:
        return VK_FORMAT_B8G8R8_SRGB;
    case GraphicsFormat::B8G8R8A8_SRGB:
        return VK_FORMAT_B8G8R8A8_SRGB;
    case GraphicsFormat::B8G8R8_UNorm:
        return VK_FORMAT_B8G8R8_UNORM;
    case GraphicsFormat::B8G8R8A8_UNorm:
        return VK_FORMAT_B8G8R8A8_UNORM;
    case GraphicsFormat::B8G8R8_SNorm:
        return VK_FORMAT_B8G8R8_SNORM;
    case GraphicsFormat::B8G8R8A8_SNorm:
        return VK_FORMAT_B8G8R8A8_SNORM;
    case GraphicsFormat::B8G8R8_UInt:
        return VK_FORMAT_B8G8R8_UINT;
    case GraphicsFormat::B8G8R8A8_UInt:
        return VK_FORMAT_B8G8R8A8_UINT;
    case GraphicsFormat::B8G8R8_SInt:
        return VK_FORMAT_B8G8R8_SINT;
    case GraphicsFormat::B8G8R8A8_SInt:
        return VK_FORMAT_B8G8R8A8_SINT;

    case GraphicsFormat::R4G4B4A4_UNormPack16:
        return VK_FORMAT_R4G4B4A4_UNORM_PACK16;
    case GraphicsFormat::R5G6B5_UNormPack16:
        return VK_FORMAT_R5G6B5_UNORM_PACK16;
    case GraphicsFormat::R5G5B5A1_UNormPack16:
        return VK_FORMAT_R5G5B5A1_UNORM_PACK16;

    case GraphicsFormat::R9G9B9E5_UFloatPack32:
        return VK_FORMAT_E5B9G9R9_UFLOAT_PACK32;
    case GraphicsFormat::R11G11B10_UFloatPack32:
        return VK_FORMAT_B10G11R11_UFLOAT_PACK32;
    case GraphicsFormat::A2B10G10R10_UNormPack32:
        return VK_FORMAT_A2B10G10R10_UNORM_PACK32;
    case GraphicsFormat::A2B10G10R10_UIntPack32:
        return VK_FORMAT_A2B10G10R10_UINT_PACK32;
    case GraphicsFormat::A2B10G10R10_SIntPack32:
        return VK_FORMAT_A2B10G10R10_SINT_PACK32;
    case GraphicsFormat::A2R10G10B10_UNormPack32:
        return VK_FORMAT_A2R10G10B10_UNORM_PACK32;
    case GraphicsFormat::A2R10G10B10_UIntPack32:
        return VK_FORMAT_A2R10G10B10_UINT_PACK32;
    case GraphicsFormat::A2R10G10B10_SIntPack32:
        return VK_FORMAT_A2R10G10B10_SINT_PACK32;

    case GraphicsFormat::A2R10G10B10_XRSRGBPack32:
    case GraphicsFormat::A2R10G10B10_XRUNormPack32:
    case GraphicsFormat::R10G10B10_XRSRGBPack32:
    case GraphicsFormat::R10G10B10_XRUNormPack32:
    case GraphicsFormat::A10R10G10B10_XRSRGBPack32:
    case GraphicsFormat::A10R10G10B10_XRUNormPack32:
        Assert(false);
        return VK_FORMAT_UNDEFINED;

    case GraphicsFormat::D16_UNorm:
        return VK_FORMAT_D16_UNORM;
    case GraphicsFormat::D24_UNorm_X8:
        return VK_FORMAT_X8_D24_UNORM_PACK32;
    case GraphicsFormat::D24_UNorm_S8_UInt:
        return VK_FORMAT_D24_UNORM_S8_UINT;
    case GraphicsFormat::D32_SFloat:
        return VK_FORMAT_D32_SFLOAT;
    case GraphicsFormat::D32_SFloat_S8_UInt:
        return VK_FORMAT_D32_SFLOAT_S8_UINT;
    case GraphicsFormat::S8_UInt:
        return VK_FORMAT_S8_UINT;

    case GraphicsFormat::RGB_BC1_SRGB:
        return VK_FORMAT_BC1_RGB_SRGB_BLOCK;
    case GraphicsFormat::RGB_BC1_UNorm:
        return VK_FORMAT_BC1_RGB_UNORM_BLOCK;
    case GraphicsFormat::RGBA_BC1_SRGB:
        return VK_FORMAT_BC1_RGBA_SRGB_BLOCK;
    case GraphicsFormat::RGBA_BC1_UNorm:
        return VK_FORMAT_BC1_RGBA_UNORM_BLOCK;
    case GraphicsFormat::RGBA_BC2_SRGB:
        return VK_FORMAT_BC2_SRGB_BLOCK;
    case GraphicsFormat::RGBA_BC2_UNorm:
        return VK_FORMAT_BC2_UNORM_BLOCK;
    case GraphicsFormat::RGBA_BC3_SRGB:
        return VK_FORMAT_BC3_SRGB_BLOCK;
    case GraphicsFormat::RGBA_BC3_UNorm:
        return VK_FORMAT_BC3_UNORM_BLOCK;
    case GraphicsFormat::R_BC4_UNorm:
        return VK_FORMAT_BC4_UNORM_BLOCK;
    case GraphicsFormat::R_BC4_SNorm:
        return VK_FORMAT_BC4_SNORM_BLOCK;
    case GraphicsFormat::RG_BC5_UNorm:
        return VK_FORMAT_BC5_UNORM_BLOCK;
    case GraphicsFormat::RG_BC5_SNorm:
        return VK_FORMAT_BC5_SNORM_BLOCK;
    case GraphicsFormat::RGB_BC6H_UFloat:
        return VK_FORMAT_BC6H_UFLOAT_BLOCK;
    case GraphicsFormat::RGB_BC6H_SFloat:
        return VK_FORMAT_BC6H_SFLOAT_BLOCK;
    case GraphicsFormat::RGBA_BC7_SRGB:
        return VK_FORMAT_BC7_SRGB_BLOCK;
    case GraphicsFormat::RGBA_BC7_UNorm:
        return VK_FORMAT_BC7_UNORM_BLOCK;

    case GraphicsFormat::RGB_PVRTC_2Bpp_SRGB:
        return VK_FORMAT_PVRTC1_2BPP_SRGB_BLOCK_IMG;
    case GraphicsFormat::RGB_PVRTC_2Bpp_UNorm:
        return VK_FORMAT_PVRTC1_2BPP_UNORM_BLOCK_IMG;
    case GraphicsFormat::RGB_PVRTC_4Bpp_SRGB:
        return VK_FORMAT_PVRTC1_4BPP_SRGB_BLOCK_IMG;
    case GraphicsFormat::RGB_PVRTC_4Bpp_UNorm:
        return VK_FORMAT_PVRTC1_4BPP_UNORM_BLOCK_IMG;
    case GraphicsFormat::RGBA_PVRTC_2Bpp_SRGB:
        return VK_FORMAT_PVRTC2_2BPP_SRGB_BLOCK_IMG;
    case GraphicsFormat::RGBA_PVRTC_2Bpp_UNorm:
        return VK_FORMAT_PVRTC2_2BPP_UNORM_BLOCK_IMG;
    case GraphicsFormat::RGBA_PVRTC_4Bpp_SRGB:
        return VK_FORMAT_PVRTC2_4BPP_SRGB_BLOCK_IMG;
    case GraphicsFormat::RGBA_PVRTC_4Bpp_UNorm:
        return VK_FORMAT_PVRTC2_4BPP_UNORM_BLOCK_IMG;

    case GraphicsFormat::RGB_ETC2_SRGB:
        return VK_FORMAT_ETC2_R8G8B8_SRGB_BLOCK;
    case GraphicsFormat::RGB_ETC2_UNorm:
        return VK_FORMAT_ETC2_R8G8B8_UNORM_BLOCK;
    case GraphicsFormat::RGB_A1_ETC2_SRGB:
        return VK_FORMAT_ETC2_R8G8B8A1_SRGB_BLOCK;
    case GraphicsFormat::RGB_A1_ETC2_UNorm:
        return VK_FORMAT_ETC2_R8G8B8A1_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ETC2_SRGB:
        return VK_FORMAT_ETC2_R8G8B8A8_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ETC2_UNorm:
        return VK_FORMAT_ETC2_R8G8B8A8_UNORM_BLOCK;
    case GraphicsFormat::R_EAC_UNorm:
        return VK_FORMAT_EAC_R11_UNORM_BLOCK;
    case GraphicsFormat::R_EAC_SNorm:
        return VK_FORMAT_EAC_R11_SNORM_BLOCK;
    case GraphicsFormat::RG_EAC_UNorm:
        return VK_FORMAT_EAC_R11G11_UNORM_BLOCK;
    case GraphicsFormat::RG_EAC_SNorm:
        return VK_FORMAT_EAC_R11G11_SNORM_BLOCK;

    case GraphicsFormat::RGBA_ASTC4X4_SRGB:
        return VK_FORMAT_ASTC_4x4_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC4X4_UNorm:
        return VK_FORMAT_ASTC_4x4_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC4X4_UFloat:
        return VK_FORMAT_ASTC_4x4_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC5X4_SRGB:
        return VK_FORMAT_ASTC_5x4_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC5X4_UNorm:
        return VK_FORMAT_ASTC_5x4_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC5X4_UFloat:
        return VK_FORMAT_ASTC_5x4_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC5X5_SRGB:
        return VK_FORMAT_ASTC_5x5_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC5X5_UNorm:
        return VK_FORMAT_ASTC_5x5_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC5X5_UFloat:
        return VK_FORMAT_ASTC_5x5_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC6X5_SRGB:
        return VK_FORMAT_ASTC_6x5_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC6X5_UNorm:
        return VK_FORMAT_ASTC_6x5_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC6X5_UFloat:
        return VK_FORMAT_ASTC_6x5_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC6X6_SRGB:
        return VK_FORMAT_ASTC_6x6_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC6X6_UNorm:
        return VK_FORMAT_ASTC_6x6_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC6X6_UFloat:
        return VK_FORMAT_ASTC_6x6_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC8X5_SRGB:
        return VK_FORMAT_ASTC_8x5_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X5_UNorm:
        return VK_FORMAT_ASTC_8x5_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X5_UFloat:
        return VK_FORMAT_ASTC_8x5_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC8X6_SRGB:
        return VK_FORMAT_ASTC_8x6_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X6_UNorm:
        return VK_FORMAT_ASTC_8x6_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X6_UFloat:
        return VK_FORMAT_ASTC_8x6_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC8X8_SRGB:
        return VK_FORMAT_ASTC_8x8_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X8_UNorm:
        return VK_FORMAT_ASTC_8x8_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X8_UFloat:
        return VK_FORMAT_ASTC_8x8_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC10X5_SRGB:
        return VK_FORMAT_ASTC_10x5_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X5_UNorm:
        return VK_FORMAT_ASTC_10x5_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X5_UFloat:
        return VK_FORMAT_ASTC_10x5_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC10X6_SRGB:
        return VK_FORMAT_ASTC_10x6_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X6_UNorm:
        return VK_FORMAT_ASTC_10x6_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X6_UFloat:
        return VK_FORMAT_ASTC_10x6_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC10X8_SRGB:
        return VK_FORMAT_ASTC_10x8_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X8_UNorm:
        return VK_FORMAT_ASTC_10x8_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X8_UFloat:
        return VK_FORMAT_ASTC_10x8_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC10X10_SRGB:
        return VK_FORMAT_ASTC_10x10_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X10_UNorm:
        return VK_FORMAT_ASTC_10x10_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X10_UFloat:
        return VK_FORMAT_ASTC_10x10_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC12X10_SRGB:
        return VK_FORMAT_ASTC_12x10_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC12X10_UNorm:
        return VK_FORMAT_ASTC_12x10_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC12X10_UFloat:
        return VK_FORMAT_ASTC_12x10_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC12X12_SRGB:
        return VK_FORMAT_ASTC_12x12_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC12X12_UNorm:
        return VK_FORMAT_ASTC_12x12_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC12X12_UFloat:
        return VK_FORMAT_ASTC_12x12_SFLOAT_BLOCK_EXT;
    default:
        Assert(false);
        return VK_FORMAT_UNDEFINED;
    }
}

VkImageType cross::MapTextureType(NGITextureType type)
{
    switch (type)
    {
    case NGITextureType::Texture1D:
    case NGITextureType::Texture1DArray:
        return VK_IMAGE_TYPE_1D;
    case NGITextureType::Texture2D:
    case NGITextureType::Texture2DArray:
    case NGITextureType::TextureCube:
    case NGITextureType::TextureCubeArray:
        return VK_IMAGE_TYPE_2D;
    case NGITextureType::Texture3D:
        return VK_IMAGE_TYPE_3D;
    default:
        AssertMsg(false, "Should not run to this branch");
        return VK_IMAGE_TYPE_MAX_ENUM;
    }
}

VkImageViewType cross::MapTextureViewType(NGITextureType type)
{
    switch (type)
    {
    case NGITextureType::Texture1D:
        return VK_IMAGE_VIEW_TYPE_1D;
    case NGITextureType::Texture1DArray:
        return VK_IMAGE_VIEW_TYPE_1D_ARRAY;
    case NGITextureType::Texture2D:
        return VK_IMAGE_VIEW_TYPE_2D;
    case NGITextureType::Texture2DArray:
        return VK_IMAGE_VIEW_TYPE_2D_ARRAY;
    case NGITextureType::TextureCube:
        return VK_IMAGE_VIEW_TYPE_CUBE;
    case NGITextureType::TextureCubeArray:
        return VK_IMAGE_VIEW_TYPE_CUBE_ARRAY;
    case NGITextureType::Texture3D:
        return VK_IMAGE_VIEW_TYPE_3D;
    default:
        AssertMsg(false, "Should not run to this place");
        return VK_IMAGE_VIEW_TYPE_MAX_ENUM;
    }
}

VkSampleCountFlagBits cross::MapSampleCount(UInt32 count)
{
    switch (count)
    {
    case 1:
        return VK_SAMPLE_COUNT_1_BIT;
    case 2:
        return VK_SAMPLE_COUNT_2_BIT;
    case 4:
        return VK_SAMPLE_COUNT_4_BIT;
    case 8:
        return VK_SAMPLE_COUNT_8_BIT;
    case 16:
        return VK_SAMPLE_COUNT_16_BIT;
    case 32:
        return VK_SAMPLE_COUNT_32_BIT;
    case 64:
        return VK_SAMPLE_COUNT_64_BIT;
    default:
        AssertMsg(false, "Invalid sample count");
        return VK_SAMPLE_COUNT_FLAG_BITS_MAX_ENUM;
    }
}

VkImageUsageFlags cross::MapTextureUsage(NGITextureUsage usage)
{
    VkImageUsageFlags vkUsageFlags = 0;
    if (EnumHasAnyFlags(usage, NGITextureUsage::CopySrc))
        vkUsageFlags |= VK_IMAGE_USAGE_TRANSFER_SRC_BIT;
    if (EnumHasAnyFlags(usage, NGITextureUsage::CopyDst))
        vkUsageFlags |= VK_IMAGE_USAGE_TRANSFER_DST_BIT;
    if (EnumHasAnyFlags(usage, NGITextureUsage::RenderTarget))
        vkUsageFlags |= VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT;
    if (EnumHasAnyFlags(usage, NGITextureUsage::DepthStencil))
        vkUsageFlags |= VK_IMAGE_USAGE_DEPTH_STENCIL_ATTACHMENT_BIT;
    if (EnumHasAnyFlags(usage, NGITextureUsage::UnorderedAccess))
        vkUsageFlags |= VK_IMAGE_USAGE_STORAGE_BIT;
    if (EnumHasAnyFlags(usage, NGITextureUsage::ShaderResource))
        vkUsageFlags |= VK_IMAGE_USAGE_SAMPLED_BIT;
    if (EnumHasAnyFlags(usage, NGITextureUsage::SubpassInput))
        vkUsageFlags |= VK_IMAGE_USAGE_INPUT_ATTACHMENT_BIT, vkUsageFlags |= VK_IMAGE_USAGE_SAMPLED_BIT;
    return vkUsageFlags;
}

VkBufferUsageFlags cross::MapBufferUsage(NGIBufferUsage usage)
{
    VkBufferUsageFlags vkBufferUsageFlags = 0;
    if (EnumHasAnyFlags(usage, NGIBufferUsage::CopySrc))
        vkBufferUsageFlags |= VK_BUFFER_USAGE_TRANSFER_SRC_BIT;
    if (EnumHasAnyFlags(usage, NGIBufferUsage::CopyDst))
        vkBufferUsageFlags |= VK_BUFFER_USAGE_TRANSFER_DST_BIT;
    // TODO(scolu): Remove extra usage when using dedicated ray tracing vertex | index buffer
    if (EnumHasAnyFlags(usage, NGIBufferUsage::VertexBuffer))
        vkBufferUsageFlags |= VK_BUFFER_USAGE_VERTEX_BUFFER_BIT
#ifdef NGI_ENABLE_RAY_TRACING
    | VK_BUFFER_USAGE_SHADER_DEVICE_ADDRESS_BIT | VK_BUFFER_USAGE_ACCELERATION_STRUCTURE_BUILD_INPUT_READ_ONLY_BIT_KHR | VK_BUFFER_USAGE_STORAGE_BUFFER_BIT
#endif
    ;
    if (EnumHasAnyFlags(usage, NGIBufferUsage::IndexBuffer))
        vkBufferUsageFlags |= VK_BUFFER_USAGE_INDEX_BUFFER_BIT
#ifdef NGI_ENABLE_RAY_TRACING
    | VK_BUFFER_USAGE_SHADER_DEVICE_ADDRESS_BIT | VK_BUFFER_USAGE_ACCELERATION_STRUCTURE_BUILD_INPUT_READ_ONLY_BIT_KHR | VK_BUFFER_USAGE_STORAGE_BUFFER_BIT
#endif
    ;
    if (EnumHasAnyFlags(usage, NGIBufferUsage::IndirectBuffer))
        vkBufferUsageFlags |= VK_BUFFER_USAGE_INDIRECT_BUFFER_BIT;
    if (EnumHasAnyFlags(usage, NGIBufferUsage::TexelBuffer))
        vkBufferUsageFlags |= VK_BUFFER_USAGE_UNIFORM_TEXEL_BUFFER_BIT;
    if (EnumHasAnyFlags(usage, NGIBufferUsage::RWTexelBuffer))
        vkBufferUsageFlags |= VK_BUFFER_USAGE_STORAGE_TEXEL_BUFFER_BIT;
    if (EnumHasAnyFlags(usage, NGIBufferUsage::StructuredBuffer))
        vkBufferUsageFlags |= VK_BUFFER_USAGE_STORAGE_BUFFER_BIT;
    if (EnumHasAnyFlags(usage, NGIBufferUsage::RWStructuredBuffer))
        vkBufferUsageFlags |= VK_BUFFER_USAGE_STORAGE_BUFFER_BIT;
    if (EnumHasAnyFlags(usage, NGIBufferUsage::ByteAddressBuffer))
        vkBufferUsageFlags |= VK_BUFFER_USAGE_STORAGE_BUFFER_BIT;
    if (EnumHasAnyFlags(usage, NGIBufferUsage::RWByteAddressBuffer))
        vkBufferUsageFlags |= VK_BUFFER_USAGE_STORAGE_BUFFER_BIT;
    if (EnumHasAnyFlags(usage, NGIBufferUsage::ConstantBuffer))
        vkBufferUsageFlags |= VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT;
    if (EnumHasAnyFlags(usage, NGIBufferUsage::TextureBuffer))
        vkBufferUsageFlags |= VK_BUFFER_USAGE_STORAGE_BUFFER_BIT;
    if (EnumHasAnyFlags(usage, NGIBufferUsage::DeviceAddressBit))
        vkBufferUsageFlags |= VK_BUFFER_USAGE_SHADER_DEVICE_ADDRESS_BIT;
    if (EnumHasAnyFlags(usage, NGIBufferUsage::AccelStructBuffer))
        vkBufferUsageFlags |= VK_BUFFER_USAGE_ACCELERATION_STRUCTURE_STORAGE_BIT_KHR | VK_BUFFER_USAGE_SHADER_DEVICE_ADDRESS_BIT;
    if (EnumHasAnyFlags(usage, NGIBufferUsage::ShaderBindingTableBuffer))
        vkBufferUsageFlags |= VK_BUFFER_USAGE_SHADER_BINDING_TABLE_BIT_KHR | VK_BUFFER_USAGE_SHADER_DEVICE_ADDRESS_BIT;
    if (EnumHasAnyFlags(usage, NGIBufferUsage::AccelStructBuildInputBuffer))
        vkBufferUsageFlags |= VK_BUFFER_USAGE_ACCELERATION_STRUCTURE_BUILD_INPUT_READ_ONLY_BIT_KHR | VK_BUFFER_USAGE_SHADER_DEVICE_ADDRESS_BIT;

    return vkBufferUsageFlags;
}

VkImageAspectFlags cross::MapTextureAspect(NGITextureAspect aspect)
{
    VkImageAspectFlags vkAspectFlags = 0;
    if (EnumHasAnyFlags(aspect, NGITextureAspect::Color))
        vkAspectFlags |= VK_IMAGE_ASPECT_COLOR_BIT;
    if (EnumHasAnyFlags(aspect, NGITextureAspect::Depth))
        vkAspectFlags |= VK_IMAGE_ASPECT_DEPTH_BIT;
    if (EnumHasAnyFlags(aspect, NGITextureAspect::Stencil))
        vkAspectFlags |= VK_IMAGE_ASPECT_STENCIL_BIT;
    return vkAspectFlags;
}

VkShaderStageFlagBits cross::MapShaderStage(CrossSchema::ShaderStageBit stage)
{
    using namespace CrossSchema;   // safe

    switch (stage)
    {
    case ShaderStageBit::Vertex:
        return VK_SHADER_STAGE_VERTEX_BIT;
    case ShaderStageBit::Hull:
        return VK_SHADER_STAGE_TESSELLATION_CONTROL_BIT;
    case ShaderStageBit::Domain:
        return VK_SHADER_STAGE_TESSELLATION_EVALUATION_BIT;
    case ShaderStageBit::Geometry:
        return VK_SHADER_STAGE_GEOMETRY_BIT;
    case ShaderStageBit::Pixel:
        return VK_SHADER_STAGE_FRAGMENT_BIT;
    case ShaderStageBit::Compute:
        return VK_SHADER_STAGE_COMPUTE_BIT;
    default:
        AssertMsg(false, "Not implemented yet");
        return VK_SHADER_STAGE_FLAG_BITS_MAX_ENUM;
    }
}

VkShaderStageFlags cross::MapShaderStageFlags(UInt32 flags)
{
    using namespace CrossSchema;   // safe

    VkShaderStageFlags vkFlags = 0;
    if (flags & static_cast<UInt32>(ShaderStageBit::Vertex))
        vkFlags |= VK_SHADER_STAGE_VERTEX_BIT;
    if (flags & static_cast<UInt32>(ShaderStageBit::Hull))
        vkFlags |= VK_SHADER_STAGE_TESSELLATION_CONTROL_BIT;
    if (flags & static_cast<UInt32>(ShaderStageBit::Domain))
        vkFlags |= VK_SHADER_STAGE_TESSELLATION_EVALUATION_BIT;
    if (flags & static_cast<UInt32>(ShaderStageBit::Geometry))
        vkFlags |= VK_SHADER_STAGE_GEOMETRY_BIT;
    if (flags & static_cast<UInt32>(ShaderStageBit::Pixel))
        vkFlags |= VK_SHADER_STAGE_FRAGMENT_BIT;
    if (flags & static_cast<UInt32>(ShaderStageBit::Compute))
        vkFlags |= VK_SHADER_STAGE_COMPUTE_BIT;
    if (flags & static_cast<UInt32>(ShaderStageBit::RayGen))
        vkFlags |= VK_SHADER_STAGE_RAYGEN_BIT_KHR;
    if (flags & static_cast<UInt32>(ShaderStageBit::ClosestHit))
        vkFlags |= VK_SHADER_STAGE_CLOSEST_HIT_BIT_KHR;
    if (flags & static_cast<UInt32>(ShaderStageBit::AnyHit))
        vkFlags |= VK_SHADER_STAGE_ANY_HIT_BIT_KHR;
    if (flags & static_cast<UInt32>(ShaderStageBit::Miss))
        vkFlags |= VK_SHADER_STAGE_MISS_BIT_KHR;
    if (flags & static_cast<UInt32>(ShaderStageBit::InterSection))
        vkFlags |= VK_SHADER_STAGE_INTERSECTION_BIT_KHR;
    if (flags & static_cast<UInt32>(ShaderStageBit::Callable))
        vkFlags |= VK_SHADER_STAGE_CALLABLE_BIT_KHR;
    return vkFlags;
}

VkFormat cross::MapVertexFormat(VertexFormat format)
{
    switch (format)
    {
    case VertexFormat::Float:
        return VK_FORMAT_R32_SFLOAT;
    case VertexFormat::Float2:
        return VK_FORMAT_R32G32_SFLOAT;
    case VertexFormat::Float3:
        return VK_FORMAT_R32G32B32_SFLOAT;
    case VertexFormat::Float4:
        return VK_FORMAT_R32G32B32A32_SFLOAT;
    case VertexFormat::Half2:
        return VK_FORMAT_R16G16_SFLOAT;
    case VertexFormat::Half3:
        return VK_FORMAT_R16G16B16_SFLOAT;
    case VertexFormat::Half4:
        return VK_FORMAT_R16G16B16A16_SFLOAT;
    case VertexFormat::Byte4:
        return VK_FORMAT_R8G8B8A8_SINT;
    case VertexFormat::UByte4:
        return VK_FORMAT_R8G8B8A8_UINT;
    case VertexFormat::Byte3_Norm:
        return VK_FORMAT_R8G8B8_SNORM;
    case VertexFormat::Byte4_Norm:
        return VK_FORMAT_R8G8B8A8_SNORM;
    case VertexFormat::UByte3_Norm:
        return VK_FORMAT_R8G8B8_UNORM;
    case VertexFormat::UByte4_Norm:
        return VK_FORMAT_R8G8B8A8_UNORM;
    case VertexFormat::Short2:
        return VK_FORMAT_R16G16_SINT;
    case VertexFormat::Short4:
        return VK_FORMAT_R16G16B16A16_SINT;
    case VertexFormat::UShort2:
        return VK_FORMAT_R16G16_UINT;
    case VertexFormat::UShort4:
        return VK_FORMAT_R16G16B16A16_UINT;
    case VertexFormat::Short2_Norm:
        return VK_FORMAT_R16G16_SNORM;
    case VertexFormat::Short3_Norm:
        return VK_FORMAT_R16G16B16_SNORM;
    case VertexFormat::Short4_Norm:
        return VK_FORMAT_R16G16B16A16_SNORM;
    case VertexFormat::UShort2_Norm:
        return VK_FORMAT_R16G16_UNORM;
    case VertexFormat::UShort3_Norm:
        return VK_FORMAT_R16G16B16_UNORM;
    case VertexFormat::UShort4_Norm:
        return VK_FORMAT_R16G16B16A16_UNORM;
        case VertexFormat::INT4:
            return VK_FORMAT_R32G32B32A32_SINT;
        case VertexFormat::INT:
            return VK_FORMAT_R32_SINT;
        case VertexFormat::UInt4:
            return VK_FORMAT_R32G32B32A32_UINT;
        case VertexFormat::UInt:
            return VK_FORMAT_R32_UINT;
    case VertexFormat::UInt_R10G10B10A2:
    case VertexFormat::UInt_R10G10B10A2_Norm:
    default:
        AssertMsg(false, "Invalid format");
        return VK_FORMAT_UNDEFINED;
    }
}

VkPrimitiveTopology cross::MapPrimitiveTopology(PrimitiveTopology topology)
{
    switch (topology)
    {
    case PrimitiveTopology::Point:
        return VK_PRIMITIVE_TOPOLOGY_POINT_LIST;
    case PrimitiveTopology::LineList:
        return VK_PRIMITIVE_TOPOLOGY_LINE_LIST;
    case PrimitiveTopology::LineStrip:
        return VK_PRIMITIVE_TOPOLOGY_LINE_STRIP;
    case PrimitiveTopology::LineListAdj:
        return VK_PRIMITIVE_TOPOLOGY_LINE_LIST_WITH_ADJACENCY;
    case PrimitiveTopology::LineStripAdj:
        return VK_PRIMITIVE_TOPOLOGY_LINE_STRIP_WITH_ADJACENCY;
    case PrimitiveTopology::TriangleList:
        return VK_PRIMITIVE_TOPOLOGY_TRIANGLE_LIST;
    case PrimitiveTopology::TriangleStrip:
        return VK_PRIMITIVE_TOPOLOGY_TRIANGLE_STRIP;
    case PrimitiveTopology::TriangleListAdj:
        return VK_PRIMITIVE_TOPOLOGY_TRIANGLE_LIST_WITH_ADJACENCY;
    case PrimitiveTopology::TriangleStripAdj:
        return VK_PRIMITIVE_TOPOLOGY_TRIANGLE_STRIP_WITH_ADJACENCY;
    case PrimitiveTopology::PatchList:
        return VK_PRIMITIVE_TOPOLOGY_PATCH_LIST;
    default:
        AssertMsg(false, "Invalid topology");
        return VK_PRIMITIVE_TOPOLOGY_MAX_ENUM;
    }
}

VkCullModeFlags cross::MapCullMode(CullMode cullMode)
{
    switch (cullMode)
    {
    case CullMode::None:
        return VK_CULL_MODE_NONE;
    case CullMode::Front:
        return VK_CULL_MODE_FRONT_BIT;
    case CullMode::Back:
        return VK_CULL_MODE_BACK_BIT;
    default:
        AssertMsg(false, "Invalid cull mode");
        return VK_CULL_MODE_FLAG_BITS_MAX_ENUM;
    }
}

VkCompareOp cross::MapComparisonOp(NGIComparisonOp op)
{
    switch (op)
    {
    case NGIComparisonOp::Never:
        return VK_COMPARE_OP_NEVER;
    case NGIComparisonOp::Less:
        return VK_COMPARE_OP_LESS;
    case NGIComparisonOp::Equal:
        return VK_COMPARE_OP_EQUAL;
    case NGIComparisonOp::LessEqual:
        return VK_COMPARE_OP_LESS_OR_EQUAL;
    case NGIComparisonOp::Greater:
        return VK_COMPARE_OP_GREATER;
    case NGIComparisonOp::NotEqual:
        return VK_COMPARE_OP_NOT_EQUAL;
    case NGIComparisonOp::GreaterEqual:
        return VK_COMPARE_OP_GREATER_OR_EQUAL;
    case NGIComparisonOp::Always:
        return VK_COMPARE_OP_ALWAYS;
    default:
        return VK_COMPARE_OP_NEVER;
    }
}

VkStencilOp cross::MapStencilOp(StencilOp op)
{
    switch (op)
    {
    case StencilOp::Keep:
        return VK_STENCIL_OP_KEEP;
    case StencilOp::Zero:
        return VK_STENCIL_OP_ZERO;
    case StencilOp::Replace:
        return VK_STENCIL_OP_REPLACE;
    case StencilOp::IncrementSaturate:
        return VK_STENCIL_OP_INCREMENT_AND_CLAMP;
    case StencilOp::DecrementSaturate:
        return VK_STENCIL_OP_DECREMENT_AND_CLAMP;
    case StencilOp::Invert:
        return VK_STENCIL_OP_INVERT;
    case StencilOp::IncrementWarp:
        return VK_STENCIL_OP_INCREMENT_AND_WRAP;
    case StencilOp::DecrementWarp:
        return VK_STENCIL_OP_DECREMENT_AND_WRAP;
    default:
        return VK_STENCIL_OP_KEEP;
    }
}

VkBlendFactor cross::MapBlendFactor(BlendFactor factor, bool alpha)
{
    switch (factor)
    {
    case BlendFactor::Zero:
        return VK_BLEND_FACTOR_ZERO;
    case BlendFactor::One:
        return VK_BLEND_FACTOR_ONE;
    case BlendFactor::SrcColor:
        return VK_BLEND_FACTOR_SRC_COLOR;
    case BlendFactor::InvSrcColor:
        return VK_BLEND_FACTOR_ONE_MINUS_SRC_COLOR;
    case BlendFactor::SrcAlpha:
        return VK_BLEND_FACTOR_SRC_ALPHA;
    case BlendFactor::InvSrcAlpha:
        return VK_BLEND_FACTOR_ONE_MINUS_SRC_ALPHA;
    case BlendFactor::DestAlpha:
        return VK_BLEND_FACTOR_DST_ALPHA;
    case BlendFactor::InvDestAlpha:
        return VK_BLEND_FACTOR_ONE_MINUS_DST_ALPHA;
    case BlendFactor::DestColor:
        return VK_BLEND_FACTOR_DST_COLOR;
    case BlendFactor::InvDestColor:
        return VK_BLEND_FACTOR_ONE_MINUS_DST_COLOR;
    case BlendFactor::SrcAlphaSaturate:
        return VK_BLEND_FACTOR_SRC_ALPHA_SATURATE;
    case BlendFactor::Constant:
        return alpha ? VK_BLEND_FACTOR_CONSTANT_ALPHA : VK_BLEND_FACTOR_CONSTANT_COLOR;
    case BlendFactor::InvConstant:
        return alpha ? VK_BLEND_FACTOR_ONE_MINUS_CONSTANT_ALPHA : VK_BLEND_FACTOR_ONE_MINUS_CONSTANT_COLOR;
    case BlendFactor::Src1Color:
        return VK_BLEND_FACTOR_SRC1_COLOR;
    case BlendFactor::InvSrc1Color:
        return VK_BLEND_FACTOR_ONE_MINUS_SRC1_COLOR;
    case BlendFactor::Src1Alpha:
        return VK_BLEND_FACTOR_SRC1_ALPHA;
    case BlendFactor::InvSrc1Alpha:
        return VK_BLEND_FACTOR_ONE_MINUS_SRC1_ALPHA;
    default:
        return VK_BLEND_FACTOR_ZERO;
    }
}

VkBlendOp cross::MapBlendOp(NGIBlendOp op)
{
    switch (op)
    {
    case NGIBlendOp::Add:
        return VK_BLEND_OP_ADD;
    case NGIBlendOp::Subtract:
        return VK_BLEND_OP_SUBTRACT;
    case NGIBlendOp::ReverseSubtract:
        return VK_BLEND_OP_REVERSE_SUBTRACT;
    case NGIBlendOp::Min:
        return VK_BLEND_OP_MIN;
    case NGIBlendOp::Max:
        return VK_BLEND_OP_MAX;
    default:
        return VK_BLEND_OP_ADD;
    }
}

VkColorComponentFlags cross::MapColorMask(ColorMask mask)
{
    VkColorComponentFlags vkColorFlags = 0;
    if (EnumHasAnyFlags(mask, ColorMask::R))
        vkColorFlags |= VK_COLOR_COMPONENT_R_BIT;
    if (EnumHasAnyFlags(mask, ColorMask::G))
        vkColorFlags |= VK_COLOR_COMPONENT_G_BIT;
    if (EnumHasAnyFlags(mask, ColorMask::B))
        vkColorFlags |= VK_COLOR_COMPONENT_B_BIT;
    if (EnumHasAnyFlags(mask, ColorMask::A))
        vkColorFlags |= VK_COLOR_COMPONENT_A_BIT;
    return vkColorFlags;
}

VkAttachmentLoadOp cross::MapLoadOp(NGILoadOp loadOp)
{
    switch (loadOp)
    {
    case NGILoadOp::DontCare:
        return VK_ATTACHMENT_LOAD_OP_DONT_CARE;
    case NGILoadOp::Load:
        return VK_ATTACHMENT_LOAD_OP_LOAD;
    case NGILoadOp::Clear:
        return VK_ATTACHMENT_LOAD_OP_CLEAR;
    default:
        AssertMsg(false, "Invalid load op");
        return VK_ATTACHMENT_LOAD_OP_MAX_ENUM;
    }
}

VkAttachmentStoreOp cross::MapStoreOp(NGIStoreOp storeOp)
{
    switch (storeOp)
    {
    case NGIStoreOp::DontCare:
    case NGIStoreOp::Resolve:
        return VK_ATTACHMENT_STORE_OP_DONT_CARE;
    case NGIStoreOp::Store:
    case NGIStoreOp::StoreAndResolve:
        return VK_ATTACHMENT_STORE_OP_STORE;
    default:
        AssertMsg(false, "Invalid store op");
        return VK_ATTACHMENT_STORE_OP_MAX_ENUM;
    }
}

VkResolveModeFlagBits cross::MapResolveMode(NGIResolveType resolveType)
{
    switch (resolveType)
    {
    case NGIResolveType::DontResolve:
        return VK_RESOLVE_MODE_NONE_KHR;
    case NGIResolveType::SampleZero:
        return VK_RESOLVE_MODE_SAMPLE_ZERO_BIT_KHR;
    case NGIResolveType::Average:
        return VK_RESOLVE_MODE_AVERAGE_BIT_KHR;
    case NGIResolveType::Min:
        return VK_RESOLVE_MODE_MIN_BIT_KHR;
    case NGIResolveType::Max:
        return VK_RESOLVE_MODE_MAX_BIT_KHR;
    default:
        AssertMsg(false, "Invalid resolve type");
        return VK_RESOLVE_MODE_FLAG_BITS_MAX_ENUM;
    }
}

VmaMemoryUsage cross::MapHeapType(NGIHeapType type)
{
    switch (type)
    {
    case NGIHeapType::Default:
        return VMA_MEMORY_USAGE_GPU_ONLY;
    case NGIHeapType::Upload:
        return VMA_MEMORY_USAGE_CPU_ONLY;
    case NGIHeapType::Readback:
        return VMA_MEMORY_USAGE_GPU_TO_CPU;
    default:
        AssertMsg(false, "Invalid heap type");
        return VMA_MEMORY_USAGE_MAX_ENUM;
    }
}

VkDescriptorType cross::MapDescriptorType(CrossSchema::ShaderResourceType type)
{
    using namespace CrossSchema;   // safe

    switch (type)
    {
    case ShaderResourceType::Sampler:
        return VK_DESCRIPTOR_TYPE_SAMPLER;
    case ShaderResourceType::Texture1D:
    case ShaderResourceType::Texture1DArray:
    case ShaderResourceType::Texture2D:
    case ShaderResourceType::Texture2DArray:
    case ShaderResourceType::Texture2DMS:
    case ShaderResourceType::Texture2DMSArray:
    case ShaderResourceType::Texture3D:
    case ShaderResourceType::TextureCube:
    case ShaderResourceType::TextureCubeArray:
        return VK_DESCRIPTOR_TYPE_SAMPLED_IMAGE;
    case ShaderResourceType::RWTexture1D:
    case ShaderResourceType::RWTexture1DArray:
    case ShaderResourceType::RWTexture2D:
    case ShaderResourceType::RWTexture2DArray:
    case ShaderResourceType::RWTexture3D:
        return VK_DESCRIPTOR_TYPE_STORAGE_IMAGE;
    case ShaderResourceType::TexelBuffer:
        return VK_DESCRIPTOR_TYPE_UNIFORM_TEXEL_BUFFER;
    case ShaderResourceType::RWTexelBuffer:
        return VK_DESCRIPTOR_TYPE_STORAGE_TEXEL_BUFFER;
    case ShaderResourceType::ConstantBuffer:
        return VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER_DYNAMIC;
    case ShaderResourceType::TextureBuffer:
    case ShaderResourceType::StructuredBuffer:
        return VK_DESCRIPTOR_TYPE_STORAGE_BUFFER_DYNAMIC;
    case ShaderResourceType::RWStructuredBuffer:
    case ShaderResourceType::ByteAddressBuffer:
    case ShaderResourceType::RWByteAddressBuffer:
        return VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
    case ShaderResourceType::SubpassInput:
    case ShaderResourceType::SubpassInputMS:
        return VK_DESCRIPTOR_TYPE_INPUT_ATTACHMENT;
    case ShaderResourceType::AccelStruct:
        return VK_DESCRIPTOR_TYPE_ACCELERATION_STRUCTURE_KHR;
    default:
        AssertMsg(false, "Invalid resource type");
        return VK_DESCRIPTOR_TYPE_MAX_ENUM;
    }
}

std::tuple<VkFilter, VkFilter, VkSamplerMipmapMode, VkBool32> cross::MapFilter(NGIFilter filter)
{
    switch (filter)
    {
    case NGIFilter::MinMagMipPoint:
        return {
            VK_FILTER_NEAREST,
            VK_FILTER_NEAREST,
            VK_SAMPLER_MIPMAP_MODE_NEAREST,
            VK_FALSE,
        };
    case NGIFilter::MinMagPointMipLinear:
        return {
            VK_FILTER_NEAREST,
            VK_FILTER_NEAREST,
            VK_SAMPLER_MIPMAP_MODE_LINEAR,
            VK_FALSE,
        };
    case NGIFilter::MinPointMagLinearMipPoint:
        return {
            VK_FILTER_NEAREST,
            VK_FILTER_LINEAR,
            VK_SAMPLER_MIPMAP_MODE_NEAREST,
            VK_FALSE,
        };
    case NGIFilter::MinPointMagMipLinear:
        return {
            VK_FILTER_NEAREST,
            VK_FILTER_LINEAR,
            VK_SAMPLER_MIPMAP_MODE_LINEAR,
            VK_FALSE,
        };
    case NGIFilter::MinLinearMagMipPoint:
        return {
            VK_FILTER_LINEAR,
            VK_FILTER_NEAREST,
            VK_SAMPLER_MIPMAP_MODE_NEAREST,
            VK_FALSE,
        };
    case NGIFilter::MinLinearMagPointMipLinear:
        return {
            VK_FILTER_LINEAR,
            VK_FILTER_NEAREST,
            VK_SAMPLER_MIPMAP_MODE_LINEAR,
            VK_FALSE,
        };
    case NGIFilter::MinMagLinearMipPoint:
        return {
            VK_FILTER_LINEAR,
            VK_FILTER_LINEAR,
            VK_SAMPLER_MIPMAP_MODE_NEAREST,
            VK_FALSE,
        };
    case NGIFilter::MinMagMipLinear:
        return {
            VK_FILTER_LINEAR,
            VK_FILTER_LINEAR,
            VK_SAMPLER_MIPMAP_MODE_LINEAR,
            VK_FALSE,
        };
    case NGIFilter::Anisotropic:
        return {
            VK_FILTER_LINEAR,
            VK_FILTER_LINEAR,
            VK_SAMPLER_MIPMAP_MODE_LINEAR,
            VK_TRUE,
        };
    default:
        AssertMsg(false, "Invalid filter type");
        return {
            VK_FILTER_MAX_ENUM,
            VK_FILTER_MAX_ENUM,
            VK_SAMPLER_MIPMAP_MODE_MAX_ENUM,
            VK_FALSE,
        };
    }
}

VkSamplerAddressMode cross::MapTextureAddressMode(NGITextureAddressMode mode)
{
    switch (mode)
    {
    case NGITextureAddressMode::Wrap:
        return VK_SAMPLER_ADDRESS_MODE_REPEAT;
    case NGITextureAddressMode::Mirror:
        return VK_SAMPLER_ADDRESS_MODE_MIRRORED_REPEAT;
    case NGITextureAddressMode::Clamp:
        return VK_SAMPLER_ADDRESS_MODE_CLAMP_TO_EDGE;
    case NGITextureAddressMode::Border:
        return VK_SAMPLER_ADDRESS_MODE_CLAMP_TO_BORDER;
    case NGITextureAddressMode::MirrorOnce:
        return VK_SAMPLER_ADDRESS_MODE_MIRROR_CLAMP_TO_EDGE;
    default:
        AssertMsg(false, "Invalid address mode");
        return VK_SAMPLER_ADDRESS_MODE_MAX_ENUM;
    }
}

VkBorderColor cross::MapBorderColor(NGIBorderColor color)
{
    switch (color)
    {
    case NGIBorderColor::FloatTransparentBlack:
        return VK_BORDER_COLOR_FLOAT_TRANSPARENT_BLACK;
    case NGIBorderColor::IntTransparentBlack:
        return VK_BORDER_COLOR_INT_TRANSPARENT_BLACK;
    case NGIBorderColor::FloatOpaqueBlack:
        return VK_BORDER_COLOR_FLOAT_OPAQUE_BLACK;
    case NGIBorderColor::IntOpaqueBlack:
        return VK_BORDER_COLOR_INT_OPAQUE_BLACK;
    case NGIBorderColor::FloatOpaqueWhite:
        return VK_BORDER_COLOR_FLOAT_OPAQUE_WHITE;
    case NGIBorderColor::IntOpaqueWhite:
        return VK_BORDER_COLOR_INT_OPAQUE_WHITE;
    default:
        Assert(false);
        return VK_BORDER_COLOR_MAX_ENUM;
    }
}

VkVertexInputRate cross::MapVertexFrequency(VertexFrequency frequency)
{
    switch (frequency)
    {
    case VertexFrequency::PerVertex:
        return VK_VERTEX_INPUT_RATE_VERTEX;
    case VertexFrequency::PerInstance:
        return VK_VERTEX_INPUT_RATE_INSTANCE;
    default:
        Assert(false);
        return VK_VERTEX_INPUT_RATE_MAX_ENUM;
    }
}

VkPresentModeKHR cross::MapPresentMode(NGIPresentMode presentMode)
{
    switch (presentMode)
    {
    case cross::NGIPresentMode::Immediate:
        return VK_PRESENT_MODE_IMMEDIATE_KHR;
    case cross::NGIPresentMode::FIFO:
        return VK_PRESENT_MODE_FIFO_KHR;
    case cross::NGIPresentMode::Mailbox:
        return VK_PRESENT_MODE_MAILBOX_KHR;
    default:
        return VK_PRESENT_MODE_IMMEDIATE_KHR;
    }
}

VkQueryType cross::MapQueryHeapType(NGIQueryHeapType queryHeapType)
{
    switch (queryHeapType)
    {
    case NGIQueryHeapType::TimeStamp:
        return VK_QUERY_TYPE_TIMESTAMP;
    default:
        return VK_QUERY_TYPE_MAX_ENUM;
        Assert(false);
    }
}
