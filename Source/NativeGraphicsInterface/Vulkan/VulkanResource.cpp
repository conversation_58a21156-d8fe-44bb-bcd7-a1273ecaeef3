#include "VulkanResource.h"
#include "VulkanDevice.h"
#include "VulkanObject.h"
#include "NativeGraphicsInterface/NGIUtils.h"
#include "Common/FrameCounter.h"
#include "vulkan.hpp"
#include "memoryhooker/MemoryProfile.h"
VkImageCreateInfo cross::VulkanTexture::CreateImageCreateInfo(const NGITextureDesc& desc, bool shared)
{
    VmaAllocationCreateInfo allocCreateInfo{
        VMA_ALLOCATION_CREATE_DEDICATED_MEMORY_BIT,
        VMA_MEMORY_USAGE_GPU_ONLY,
    };
    // use const_cast to pass andrioid;
    // allocCreateInfo.pUserData = const_cast<char*>(pDebugName);
    VkImageCreateInfo createInfo{
        VK_STRUCTURE_TYPE_IMAGE_CREATE_INFO,
        nullptr,
        desc.MutableFormat ? VK_IMAGE_CREATE_MUTABLE_FORMAT_BIT : 0u,
        MapTextureType(desc.Dimension),
        MapGraphicsFormat(desc.Format),
        {
            desc.Width,
            0,
            0,
        },
        desc.MipCount,
        0,
        MapSampleCount(desc.SampleCount),
        allocCreateInfo.usage == VMA_MEMORY_USAGE_GPU_TO_CPU ? VK_IMAGE_TILING_LINEAR : VK_IMAGE_TILING_OPTIMAL,
        MapTextureUsage(desc.Usage),
        VK_SHARING_MODE_EXCLUSIVE,
        0,
        nullptr,
        VK_IMAGE_LAYOUT_UNDEFINED,
    };
    switch (desc.Dimension)
    {
    case NGITextureType::Texture1D:
        Assert(desc.Height == 1);
        Assert(desc.Depth == 1);
        Assert(desc.ArraySize == 1);
        createInfo.extent.height = 1;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = 1;
        break;
    case NGITextureType::Texture1DArray:
        Assert(desc.Height == 1);
        Assert(desc.Depth == 1);
        createInfo.extent.height = 1;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = desc.ArraySize;
        break;
    case NGITextureType::Texture2D:
        Assert(desc.Depth == 1);
        Assert(desc.ArraySize == 1);
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = 1;
        break;
    case NGITextureType::Texture2DArray:
        Assert(desc.Depth == 1);
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = desc.ArraySize;
        break;
    case NGITextureType::Texture3D:
        Assert(desc.ArraySize == 1);
        createInfo.flags |= desc.SeparateView ? VK_IMAGE_CREATE_2D_ARRAY_COMPATIBLE_BIT : 0;
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = desc.Depth;
        createInfo.arrayLayers = 1;
        break;
    case NGITextureType::TextureCube:
        Assert(desc.Depth == 1);
        Assert(desc.ArraySize == 6);
        createInfo.flags |= VK_IMAGE_CREATE_CUBE_COMPATIBLE_BIT;
        // createInfo.flags |= desc.SeparateView ? VK_IMAGE_CREATE_2D_ARRAY_COMPATIBLE_BIT : 0;
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = 6;
        break;
    case NGITextureType::TextureCubeArray:
        Assert(desc.ArraySize % 6 == 0);
        createInfo.flags |= VK_IMAGE_CREATE_CUBE_COMPATIBLE_BIT;
        // createInfo.flags |= desc.SeparateView ? VK_IMAGE_CREATE_2D_ARRAY_COMPATIBLE_BIT : 0;
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = desc.ArraySize;
        break;
    default:
        Assert(false);
        break;
    }

    VkExternalMemoryImageCreateInfo extImageCreateInfo = {};
    if (shared)
    {
        extImageCreateInfo.sType = VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_IMAGE_CREATE_INFO;
#if CROSSENGINE_WIN
        extImageCreateInfo.handleTypes |= VK_EXTERNAL_MEMORY_HANDLE_TYPE_OPAQUE_WIN32_BIT;
#else
        extImageCreateInfo.handleTypes |= VK_EXTERNAL_MEMORY_HANDLE_TYPE_OPAQUE_FD_BIT_KHR;
#endif
        createInfo.pNext = &extImageCreateInfo;

        // allocCreateInfo.pool = GetDevice<VulkanDevice>()->GetExportableVMAPool();
    }
    return createInfo;
}

cross::VulkanTexture::VulkanTexture(const NGITextureDesc& desc, VulkanDevice* pDevice, bool hasMemory, const char* pDebugName, bool shared)
    : NGITexture{desc, pDevice, pDebugName}
    , mExternal{false}
{
    QUICK_SCOPED_CPU_TIMING("VulkanTexture");
    auto createInfo = CreateImageCreateInfo(desc, shared);
    VK_CHECK(vkCreateImage(reinterpret_cast<VkDevice>(GetDevice<VulkanDevice>()->GetNativeDevice()), &createInfo, nullptr, &mTexture));
}
cross::VulkanTexture::VulkanTexture(const NGITextureDesc& desc, const VkImageCreateInfo& createInfo, VulkanDevice* pDevice, const char* pDebugName, bool shared)
    : NGITexture{desc, pDevice, pDebugName}
    , mExternal{false}
{
    Assert(shared == false);
    QUICK_SCOPED_CPU_TIMING("VulkanTexture");
    // auto createInfo = CreateImageCreateInfo(desc, shared);
    VK_CHECK(vkCreateImage(reinterpret_cast<VkDevice>(GetDevice<VulkanDevice>()->GetNativeDevice()), &createInfo, nullptr, &mTexture));
}
cross::VulkanTexture::VulkanTexture(const NGITextureDesc& desc, VulkanDevice* pDevice, const char* pDebugName, bool shared)
    : NGITexture{desc, pDevice, pDebugName}
    , mExternal{false}
{
    VmaAllocationCreateInfo allocCreateInfo{
        VMA_ALLOCATION_CREATE_DEDICATED_MEMORY_BIT,
        VMA_MEMORY_USAGE_GPU_ONLY,
    };
    // use const_cast to pass android;
    // allocCreateInfo.pUserData = const_cast <char*>(pDebugName);
    VkImageCreateInfo createInfo{
        VK_STRUCTURE_TYPE_IMAGE_CREATE_INFO,
        nullptr,
        desc.MutableFormat ? VK_IMAGE_CREATE_MUTABLE_FORMAT_BIT : 0u,
        MapTextureType(desc.Dimension),
        MapGraphicsFormat(desc.Format),
        {
            desc.Width,
            0,
            0,
        },
        desc.MipCount,
        0,
        MapSampleCount(desc.SampleCount),
        allocCreateInfo.usage == VMA_MEMORY_USAGE_GPU_TO_CPU ? VK_IMAGE_TILING_LINEAR : VK_IMAGE_TILING_OPTIMAL,
        MapTextureUsage(desc.Usage),
        VK_SHARING_MODE_EXCLUSIVE,
        0,
        nullptr,
        VK_IMAGE_LAYOUT_UNDEFINED,
    };
    switch (desc.Dimension)
    {
    case NGITextureType::Texture1D:
        Assert(mDesc.Height == 1);
        Assert(mDesc.Depth == 1);
        Assert(mDesc.ArraySize == 1);
        createInfo.extent.height = 1;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = 1;
        break;
    case NGITextureType::Texture1DArray:
        Assert(mDesc.Height == 1);
        Assert(mDesc.Depth == 1);
        createInfo.extent.height = 1;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = desc.ArraySize;
        break;
    case NGITextureType::Texture2D:
        Assert(mDesc.Depth == 1);
        Assert(mDesc.ArraySize == 1);
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = 1;
        break;
    case NGITextureType::Texture2DArray:
        Assert(mDesc.Depth == 1);
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = desc.ArraySize;
        break;
    case NGITextureType::Texture3D:
        Assert(mDesc.ArraySize == 1);
        createInfo.flags |= desc.SeparateView ? VK_IMAGE_CREATE_2D_ARRAY_COMPATIBLE_BIT : 0;
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = desc.Depth;
        createInfo.arrayLayers = 1;
        break;
    case NGITextureType::TextureCube:
        Assert(mDesc.Depth == 1);
        Assert(mDesc.ArraySize == 6);
        createInfo.flags |= VK_IMAGE_CREATE_CUBE_COMPATIBLE_BIT;
        //createInfo.flags |= desc.SeparateView ? VK_IMAGE_CREATE_2D_ARRAY_COMPATIBLE_BIT : 0;
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = 6;
        break;
    case NGITextureType::TextureCubeArray:
        Assert(mDesc.ArraySize % 6 == 0);
        createInfo.flags |= VK_IMAGE_CREATE_CUBE_COMPATIBLE_BIT;
        //createInfo.flags |= desc.SeparateView ? VK_IMAGE_CREATE_2D_ARRAY_COMPATIBLE_BIT : 0;
        createInfo.extent.height = desc.Height;
        createInfo.extent.depth = 1;
        createInfo.arrayLayers = desc.ArraySize;
        break;
    default:
        Assert(false);
        break;
    }

    VkExternalMemoryImageCreateInfo extImageCreateInfo = {};
    if (shared)
    {
        extImageCreateInfo.sType = VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_IMAGE_CREATE_INFO;
#if CROSSENGINE_WIN
        extImageCreateInfo.handleTypes |= VK_EXTERNAL_MEMORY_HANDLE_TYPE_OPAQUE_WIN32_BIT;
#else
        extImageCreateInfo.handleTypes |= VK_EXTERNAL_MEMORY_HANDLE_TYPE_OPAQUE_FD_BIT_KHR;
#endif
        createInfo.pNext = &extImageCreateInfo;

        allocCreateInfo.pool = GetDevice<VulkanDevice>()->GetExportableVMAPool();
    }



    VK_MEMCHECK(vmaCreateImage(GetDevice<VulkanDevice>()->GetAllocator(), &createInfo, &allocCreateInfo, &mTexture, &mAllocation, &mAllocationInfo), GetDevice<VulkanDevice>()->GetAllocator());
    TRACE_MEM_NAME(GetDevice<VulkanDevice>()->GetAllocator(), "CreateTexture" + std::to_string(GetDesc().Width) + "_" + std::to_string(GetDesc().Height));
    ALLOC_NGI_TAGED(&mTexture, mAllocationInfo.size);
    NGI_LOG_DEBUG("Create Texture: {} from scratch", VkFormatHandle(mTexture));



    SetDebugName(pDebugName);
}

cross::VulkanTexture::VulkanTexture(const NGITextureDesc& desc, VkImage image, VulkanDevice* pDevice, const char* pDebugName)
    : NGITexture{desc, pDevice, pDebugName }
    , mTexture{image}
    , mExternal{true}
{
    NGI_LOG_DEBUG("Create texture: {} from swapchain backbuffer", VkFormatHandle(mTexture));
}

cross::VulkanTexture::~VulkanTexture()
{
    QUICK_SCOPED_CPU_TIMING("~VulkanTexture");
    if (!mExternal)
    {
        if (mAllocation == nullptr)
        {
            QUICK_SCOPED_CPU_TIMING("vkDestroyImage");
            vkDestroyImage(reinterpret_cast<VkDevice>(GetDevice<VulkanDevice>()->GetNativeDevice()), mTexture, nullptr);
        }
        else
        {
            QUICK_SCOPED_CPU_TIMING("vmaDestroyImage");
            vmaDestroyImage(GetDevice<VulkanDevice>()->GetAllocator(), mTexture, mAllocation);
            TRACE_MEM_NAME(GetDevice<VulkanDevice>()->GetAllocator(), "ReleaseTexture" + std::to_string(GetDesc().Width) + "_" + std::to_string(GetDesc().Height));
        }
        FREE_NGI_TAGED(&mTexture);
    }

    NGI_LOG_DEBUG("Destroy {} texture: {}", mExternal ? "external" : "", VkFormatHandle(mTexture));
}

void cross::VulkanTexture::SetDebugName(const char* pDebugName)
{
    NGIObject::SetDebugName(pDebugName);

    GetDevice<VulkanDevice>()->SetDebugName(VK_OBJECT_TYPE_IMAGE, mTexture, pDebugName);

    if (!mExternal && mAllocation)
    {
        vmaSetAllocationName(GetDevice<VulkanDevice>()->GetAllocator(), mAllocation, const_cast<char*>(GetDebugName()));
    //vmaSetAllocationUserData(GetDevice<VulkanDevice>()->GetAllocator(), mAllocation, const_cast<char*>(GetDebugName()));
    }
}

VkDeviceMemory cross::VulkanTexture::GetAllocationHandle() {
     return mAllocationInfo.deviceMemory;
}
UInt32 cross::VulkanBuffer::CalculateBufferAlignment(VulkanDevice* pDevice, NGIBufferUsage usage, bool bZeroSize)
{
    QUICK_SCOPED_CPU_TIMING("CalculateBufferAlignment");
    auto HasAnyFlags = [](VkBufferUsageFlags usage, VkBufferUsageFlags flag) {
        return (usage & flag) != 0;
    };
    VkBufferUsageFlags vkUsage = MapBufferUsage(usage);
    auto& limits = pDevice->mPhysicalDeviceInfo->GetLimits();
    const bool bIsTexelBuffer = HasAnyFlags(vkUsage, VkBufferUsageFlags(VK_BUFFER_USAGE_UNIFORM_TEXEL_BUFFER_BIT | VK_BUFFER_USAGE_STORAGE_TEXEL_BUFFER_BIT));
    const bool bIsStorageBuffer = HasAnyFlags(vkUsage, VkBufferUsageFlags(VK_BUFFER_USAGE_STORAGE_BUFFER_BIT));
    const bool bIsVertexOrIndexBuffer = HasAnyFlags(vkUsage, VkBufferUsageFlags(VK_BUFFER_USAGE_VERTEX_BUFFER_BIT | VK_BUFFER_USAGE_INDEX_BUFFER_BIT));
    const bool bIsAccelerationStructureBuffer = HasAnyFlags(vkUsage, VkBufferUsageFlags(VK_BUFFER_USAGE_ACCELERATION_STRUCTURE_STORAGE_BIT_KHR));
    const bool bIsUniformBuffer = HasAnyFlags(vkUsage, VkBufferUsageFlags(VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT));

    UInt32 alignment = 1;
    if (bIsTexelBuffer || bIsStorageBuffer)
    {
        alignment = std::max(alignment, (UInt32)limits.minTexelBufferOffsetAlignment);
        alignment = std::max(alignment, (UInt32)limits.minStorageBufferOffsetAlignment);
    }
    else if (bIsVertexOrIndexBuffer)
    {
        // No alignment restrictions on Vertex or Index buffers, leave it at 1
    }
    else if (bIsAccelerationStructureBuffer)
    {
        Assert(false);
    }
    else if (bIsUniformBuffer)
    {
        alignment = std::max(alignment, (UInt32)limits.minUniformBufferOffsetAlignment);
    }
    else
    {
        AssertMsg(false, "Unknown buffer alignment for VkBufferUsageFlags combination");
    }

    return alignment;
}

cross::VulkanBuffer::VulkanBuffer(const NGIBufferDesc& desc, VulkanDevice* pDevice, bool bHasMemory, const char* pDebugName)
     : NGIBuffer{ desc, pDevice, pDebugName }
{
    QUICK_SCOPED_CPU_TIMING("VulkanBuffer");
    VkBufferCreateInfo createInfo{
        VK_STRUCTURE_TYPE_BUFFER_CREATE_INFO,
        nullptr,
        0,
        desc.Size,
        MapBufferUsage(desc.Usage),
        VK_SHARING_MODE_EXCLUSIVE,
        0,
        nullptr,
    };
    VK_CHECK(vkCreateBuffer(reinterpret_cast<VkDevice>(GetDevice<VulkanDevice>()->GetNativeDevice()), &createInfo, nullptr, &mBuffer));
}
cross::VulkanBuffer::VulkanBuffer(const NGIBufferDesc& desc, VulkanDevice* pDevice, const char* pDebugName)
    : NGIBuffer{desc, pDevice, pDebugName}
{
    VkBufferCreateInfo createInfo{
        VK_STRUCTURE_TYPE_BUFFER_CREATE_INFO,
        nullptr,
        0,
        desc.Size,
        MapBufferUsage(desc.Usage),
        VK_SHARING_MODE_EXCLUSIVE,
        0,
        nullptr,
    };
    VmaAllocationCreateInfo allocCreateInfo{
        .flags = VMA_ALLOCATION_CREATE_DEDICATED_MEMORY_BIT,
        .usage = VMA_MEMORY_USAGE_AUTO_PREFER_DEVICE
    };
    // use const_cast to pass andrioid;
    // allocCreateInfo.pUserData = const_cast<char*>("");
    allocCreateInfo.priority = desc.Priority;

#ifdef NGI_ENABLE_RAY_TRACING
    if (desc.Usage == NGIBufferUsage::RayTracingScratchBuffer)
    {
        allocCreateInfo.pool = GetDevice<VulkanDevice>()->GetRayTracingScratchBufferVMAPool();
    }
#endif

    VK_MEMCHECK(vmaCreateBuffer(GetDevice<VulkanDevice>()->GetAllocator(), &createInfo, &allocCreateInfo, &mBuffer, &mAllocation, &mAllocationInfo), GetDevice<VulkanDevice>()->GetAllocator());
    TRACE_MEM(GetDevice<VulkanDevice>()->GetAllocator(), "CreateBuffer", this);
    ALLOC_NGI_TAGED(&mBuffer, mAllocationInfo.size);
    NGI_LOG_DEBUG("Create buffer: {} with object address: {}, Vulkan Handle: {}", GetDebugName(), fmt::ptr(this), VkFormatHandle(mBuffer));

    SetDebugName(pDebugName);
}

cross::VulkanBuffer::~VulkanBuffer()
{
    NGI_LOG_DEBUG("Destroy buffer: {} with object address: {}, Vulkan Handle: {}", GetDebugName(), fmt::ptr(this), VkFormatHandle(mBuffer));
    QUICK_SCOPED_CPU_TIMING("~VulkanBuffer");
    if (mAllocation)
    {
        QUICK_SCOPED_CPU_TIMING("vmaDestroyBuffer");
        vmaDestroyBuffer(GetDevice<VulkanDevice>()->GetAllocator(), mBuffer, mAllocation);
    }
    else
    {
        QUICK_SCOPED_CPU_TIMING("vkDestroyBuffer");
        vkDestroyBuffer(reinterpret_cast<VkDevice>(GetDevice<VulkanDevice>()->GetNativeDevice()), mBuffer, nullptr);
    }
    TRACE_MEM(GetDevice<VulkanDevice>()->GetAllocator(), "ReleaseBuffer", this);
    FREE_NGI_TAGED(&mBuffer);
}

void cross::VulkanBuffer::SetDebugName(const char* pDebugName)
{
    NGIObject::SetDebugName(pDebugName);

    GetDevice<VulkanDevice>()->SetDebugName(VK_OBJECT_TYPE_BUFFER, mBuffer, pDebugName);
    if (mAllocation)
        vmaSetAllocationName(GetDevice<VulkanDevice>()->GetAllocator(), mAllocation, const_cast<char*>(GetDebugName()));
    //vmaSetAllocationUserData(GetDevice<VulkanDevice>()->GetAllocator(), mAllocation, const_cast<char*>(GetDebugName()));
}

uint64_t cross::VulkanBuffer::GetDeviceAddress(uint64_t Offset)
{
    VkDevice vkDevice = GetDevice<VulkanDevice>()->Get();
    VkBufferDeviceAddressInfo addressInfo = {};
    addressInfo.sType = VK_STRUCTURE_TYPE_BUFFER_DEVICE_ADDRESS_INFO;
    addressInfo.buffer = mBuffer;

    return vkGetBufferDeviceAddress(vkDevice, &addressInfo) + Offset;
}

bool cross::VulkanBuffer::Mappable()
{
    const auto& memoryType = GetDevice<VulkanDevice>()->mPhysicalDeviceInfo->mMemProp.memoryTypes[mAllocationInfo.memoryType];
    return memoryType.propertyFlags & (VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT);
}

void* cross::VulkanBuffer::Map()
{
    void* pData = nullptr;
    VK_CHECK(vmaMapMemory(GetDevice<VulkanDevice>()->GetAllocator(), mAllocation, &pData));
    return pData;
}

void cross::VulkanBuffer::Unmap()
{
    vmaUnmapMemory(GetDevice<VulkanDevice>()->GetAllocator(), mAllocation);
}

SizeType cross::VulkanBuffer::AlignOffset(NGIBufferUsage usage, SizeType offset, SizeType range)
{
    SizeType alignment = 1;
    switch (usage)
    {
    case NGIBufferUsage::ConstantBuffer:
        alignment = GetDevice<VulkanDevice>()->mPhysicalDeviceInfo->mProps.properties.limits.minUniformBufferOffsetAlignment;
        break;
    case NGIBufferUsage::TextureBuffer:
        alignment = GetDevice<VulkanDevice>()->mPhysicalDeviceInfo->mProps.properties.limits.minStorageBufferOffsetAlignment;
        break;
    case NGIBufferUsage::VertexBuffer:
        // max component size of vertex format
        alignment = sizeof(float);
        break;
    case NGIBufferUsage::IndexBuffer:
        // max size of index format
        alignment = sizeof(UInt32);
        break;
    case NGIBufferUsage::CopySrc:
        // max block size for compressed format or pixel size for uncompressed format was 16
        // for copying buffer there is no restriction
        alignment = 16;
        break;
#ifdef NGI_ENABLE_RAY_TRACING
    case NGIBufferUsage::RayTracingScratchBuffer:
        alignment = GetDevice<VulkanDevice>()->mPhysicalDeviceInfo->mAccelerationStructureProps.minAccelerationStructureScratchOffsetAlignment;
        break;
#endif
    default:
        alignment = GetDevice<VulkanDevice>()->mPhysicalDeviceInfo->mProps.properties.limits.minStorageBufferOffsetAlignment;
        // Assert(false);
        break;
    }
    return AlignSize(offset, alignment);
}

cross::VulkanTextureView::VulkanTextureView(NGITexture* texture, const NGITextureViewDesc& desc, VulkanDevice* device)
    : NGITextureView{ texture, desc, device }
{
    VkImageViewUsageCreateInfo usageCreateInfo{
        VK_STRUCTURE_TYPE_IMAGE_VIEW_USAGE_CREATE_INFO,
        nullptr,
        MapTextureUsage(desc.Usage),
    };

    VkImageViewCreateInfo createInfo{
        VK_STRUCTURE_TYPE_IMAGE_VIEW_CREATE_INFO,
        &usageCreateInfo,
        0,
        static_cast<VulkanTexture*>(texture)->mTexture,
        MapTextureViewType(desc.ViewDimension),
        MapGraphicsFormat(desc.Format),
        {},
        {
            MapTextureAspect(desc.SubRange.Aspect),
            desc.SubRange.MostDetailedMip,
            desc.SubRange.MipLevels,
            desc.SubRange.FirstArraySlice,
            desc.SubRange.ArraySize,
        },
    };

    if (EnumHasAnyFlags(desc.Usage, NGITextureUsage::ShaderResource) && EnumHasAllFlags(desc.SubRange.Aspect, NGITextureAspect::Depth | NGITextureAspect::Stencil))
    {
        LOG_WARN("Create texture view from texture: {}, but texture view desc has both depth and stencil aspect, usage should not include NGITextureUsage::ShaderResource", texture->GetDebugName());
        Assert(false);
    }

    if (GetDevice<VulkanDevice>()->GetVulkanVersion() < VK_API_VERSION_1_2)
    {
        auto [depth, stencil] = FormatHasDepthStencil(desc.Format);
        if ((depth || stencil) && texture->GetDesc().Format != desc.Format)
        {
            LOG_WARN("Depth/stencil texture must create view with the same format");
            Assert(false);
        }
    }

    VK_CHECK(vkCreateImageView(GetDevice<VulkanDevice>()->Get(), &createInfo, gVkAllocCallback, &mView));
    SetDebugName(texture->GetDebugName());
    NGI_LOG_DEBUG("Create texture view: {} for texture: {}", VkFormatHandle(mView), VkFormatHandle(createInfo.image));
}

cross::VulkanTextureView::~VulkanTextureView()
{
    NGI_LOG_DEBUG("Destroy texture view: {}", VkFormatHandle(mView));

    vkDestroyImageView(GetDevice<VulkanDevice>()->Get(), mView, gVkAllocCallback);
}

void cross::VulkanTextureView::SetDebugName(const char* pDebugName)
{
    NGIObject::SetDebugName(pDebugName);

    GetDevice<VulkanDevice>()->SetDebugName(VK_OBJECT_TYPE_IMAGE_VIEW, mView, pDebugName);
}

cross::VulkanBufferView::VulkanBufferView(NGIBuffer* pBuffer, const NGIBufferViewDesc& desc, VulkanDevice* pDevice)
    : NGIBufferView{pBuffer, desc, pDevice}
{
    auto pVkBuffer = static_cast<VulkanBuffer*>(pBuffer);
    mBuffer = pVkBuffer->mBuffer;
    if (EnumHasAnyFlags(desc.Usage, NGIBufferUsage::TexelBuffer | NGIBufferUsage::RWTexelBuffer))
    {
        VkBufferViewCreateInfo info{
            VK_STRUCTURE_TYPE_BUFFER_VIEW_CREATE_INFO,
            nullptr,
            0,
            pVkBuffer->mBuffer,
            MapGraphicsFormat(desc.Format),
            desc.BufferLocation,
            desc.SizeInBytes == 0? VK_WHOLE_SIZE : desc.SizeInBytes
        };
        VK_CHECK(vkCreateBufferView(GetDevice<VulkanDevice>()->Get(), &info, gVkAllocCallback, &mView));
    }
}

cross::VulkanBufferView::~VulkanBufferView()
{
    if (mView)
        vkDestroyBufferView(GetDevice<VulkanDevice>()->Get(), mView, gVkAllocCallback);
}

void cross::VulkanBufferView::SetDebugName(const char* pName)
{
    NGIObject::SetDebugName(pName);

    if (mView)
    {
        GetDevice<VulkanDevice>()->SetDebugName(VK_OBJECT_TYPE_BUFFER_VIEW, mView, pName);
    }
}
cross::VulkanSampler::VulkanSampler(const NGISamplerDesc& desc, VulkanDevice* pDevice)
    : NGISampler{desc, pDevice}
{
    // TODO(peterwjma): edge color ignored
    auto [minFilter, magFilter, mipmapMode, anisotropyEnable] = MapFilter(desc.Filter);
    VkSamplerCreateInfo info{
        VK_STRUCTURE_TYPE_SAMPLER_CREATE_INFO,
        nullptr,
        0,
        magFilter,
        minFilter,
        mipmapMode,
        MapTextureAddressMode(desc.WrapU),
        MapTextureAddressMode(desc.WrapV),
        MapTextureAddressMode(desc.WrapW),
        desc.Bias,
        anisotropyEnable,
        static_cast<float>(desc.MaxAnisotropy),
        MapBool(desc.ComparisonOp != NGIComparisonOp::Unknown),
        MapComparisonOp(desc.ComparisonOp),
        desc.MinLOD,
        desc.MaxLOD,
        MapBorderColor(desc.BorderColor),
        VK_FALSE,
    };
    VK_CHECK(vkCreateSampler(GetDevice<VulkanDevice>()->Get(), &info, gVkAllocCallback, &mSampler));

    NGI_LOG_DEBUG("Create sampler: {}", VkFormatHandle(mSampler));
}

cross::VulkanSampler::~VulkanSampler()
{
    NGI_LOG_DEBUG("Destroy sampler: {}", VkFormatHandle(mSampler));

    vkDestroySampler(GetDevice<VulkanDevice>()->Get(), mSampler, gVkAllocCallback);
}

void cross::VulkanStagingBuffer::Allocation(const cross::NGIBufferDesc& desc)
{
    mDesc = desc;
    enum class EStagingBufferUsage
    {
        // VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT
        eDynamicBuffer,
        // VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT
        eUpload,
        // VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_CACHED_BIT.
        eReadback
    };

    EStagingBufferUsage stagingBufferUsage = EStagingBufferUsage::eDynamicBuffer;

    if (EnumOnlyHasFlags(desc.Usage, UploadUsage))
    {
        stagingBufferUsage = EStagingBufferUsage::eUpload;
    }
    else if (EnumOnlyHasFlags(desc.Usage, ReadbackUsage))
    {
        stagingBufferUsage = EStagingBufferUsage::eReadback;
    }
    else
    {
        stagingBufferUsage = EStagingBufferUsage::eDynamicBuffer;
    }

    VkBufferCreateInfo createInfo{
        VK_STRUCTURE_TYPE_BUFFER_CREATE_INFO,
        nullptr,
        0,
        desc.Size,
        // for staging buffer copy data to eDynamicBuffer
        MapBufferUsage(stagingBufferUsage == EStagingBufferUsage::eDynamicBuffer ? DynamicBufferUsage : desc.Usage),
        VK_SHARING_MODE_EXCLUSIVE,
        0,
        nullptr,
    };

    VmaAllocationCreateInfo allocInfo = {};
    // https://gpuopen-librariesandsdks.github.io/VulkanMemoryAllocator/html/usage_patterns.html
    switch (stagingBufferUsage)
    {
    case EStagingBufferUsage::eDynamicBuffer:
        allocInfo.usage = VMA_MEMORY_USAGE_AUTO_PREFER_HOST;
        allocInfo.flags = VMA_ALLOCATION_CREATE_HOST_ACCESS_SEQUENTIAL_WRITE_BIT | VMA_ALLOCATION_CREATE_MAPPED_BIT;
        break;
    case EStagingBufferUsage::eUpload:
        allocInfo.usage = VMA_MEMORY_USAGE_AUTO_PREFER_HOST;
        allocInfo.flags = VMA_ALLOCATION_CREATE_HOST_ACCESS_SEQUENTIAL_WRITE_BIT | VMA_ALLOCATION_CREATE_MAPPED_BIT;
        break;
    case EStagingBufferUsage::eReadback:
        allocInfo.usage = VMA_MEMORY_USAGE_AUTO_PREFER_HOST;
        allocInfo.flags = VMA_ALLOCATION_CREATE_HOST_ACCESS_RANDOM_BIT | VMA_ALLOCATION_CREATE_MAPPED_BIT;
        break;
    default:;
    }

    // TODO for user define un normal staging buffer, which is used incorrectly
    if (desc.Usage != DynamicBufferUsage && desc.Usage != UploadUsage && desc.Usage != ReadbackUsage)
    {
        allocInfo.usage = VMA_MEMORY_USAGE_AUTO_PREFER_HOST;
        allocInfo.flags = VMA_ALLOCATION_CREATE_HOST_ACCESS_SEQUENTIAL_WRITE_BIT | VMA_ALLOCATION_CREATE_MAPPED_BIT;
    }

    VK_MEMCHECK(vmaCreateBuffer(GetDevice<VulkanDevice>()->GetAllocator(), &createInfo, &allocInfo, &mBuffer, &mAllocation, &mAllocationInfo), GetDevice<VulkanDevice>()->GetAllocator());
    TRACE_MEM(GetDevice<VulkanDevice>()->GetAllocator(), "CreateStagingBuffer", this);
    ALLOC_NGI_TAGED(&mBuffer, mAllocationInfo.size);
    NGI_LOG_DEBUG("Create const buffer: {} with size: {}", VkFormatHandle(mBuffer), desc.Size);

    SetDebugName("VulkanStagingBuffer");
}

cross::VulkanStagingBuffer::VulkanStagingBuffer(const NGIBufferDesc& desc, VulkanDevice* device, const char* pDebugName)
    : NGIStagingBuffer{desc, device}
{
    if (desc.Size != 0)
    {
        Allocation(desc);
        SetDebugName(pDebugName);
    }
}

cross::VulkanStagingBuffer::~VulkanStagingBuffer()
{
    NGI_LOG_DEBUG("Destroy const buffer: {}", VkFormatHandle(mBuffer));
    vmaDestroyBuffer(GetDevice<VulkanDevice>()->GetAllocator(), mBuffer, mAllocation);
    TRACE_MEM(GetDevice<VulkanDevice>()->GetAllocator(), "ReleaseStagingBuffer", this);
    FREE_NGI_TAGED(&mBuffer);
}

void cross::VulkanStagingBuffer::SetDebugName(const char* pDebugName)
{
    NGIObject::SetDebugName(pDebugName);

    if (mBuffer)
    {
        GetDevice<VulkanDevice>()->SetDebugName(VK_OBJECT_TYPE_BUFFER, mBuffer, pDebugName);
        vmaSetAllocationName(GetDevice<VulkanDevice>()->GetAllocator(), mAllocation, const_cast<char*>(GetDebugName()));
    }
}

uint64_t cross::VulkanStagingBuffer::GetDeviceAddress(uint64_t Offset)
{
    VkDevice vkDevice = GetDevice<VulkanDevice>()->Get();
    VkBufferDeviceAddressInfo addressInfo = {};
    addressInfo.sType = VK_STRUCTURE_TYPE_BUFFER_DEVICE_ADDRESS_INFO;
    addressInfo.buffer = mBuffer;

    return vkGetBufferDeviceAddress(vkDevice, &addressInfo) + Offset;
}

SizeType cross::VulkanStagingBuffer::AlignOffset(NGIBufferUsage usage, SizeType offset, SizeType range)
{
    SizeType alignment = 0;
    switch (usage)
    {
    case NGIBufferUsage::ConstantBuffer:
        alignment = GetDevice<VulkanDevice>()->mPhysicalDeviceInfo->mProps.properties.limits.minUniformBufferOffsetAlignment;
        break;
    case NGIBufferUsage::TextureBuffer:
        alignment = GetDevice<VulkanDevice>()->mPhysicalDeviceInfo->mProps.properties.limits.minStorageBufferOffsetAlignment;
        break;
    case NGIBufferUsage::VertexBuffer:
        // max component size of vertex format
        alignment = sizeof(float);
        break;
    case NGIBufferUsage::IndexBuffer:
        // max size of index format
        alignment = sizeof(UInt32);
        break;
    case NGIBufferUsage::CopySrc:
        // max block size for compressed format or pixel size for uncompressed format was 16
        // for copying buffer there is no restriction
        alignment = 16;
        break;
#ifdef NGI_ENABLE_RAY_TRACING
    case NGIBufferUsage::RayTracingScratchBuffer:
        alignment = GetDevice<VulkanDevice>()->mPhysicalDeviceInfo->mAccelerationStructureProps.minAccelerationStructureScratchOffsetAlignment;
        break;
#endif
    default:
        alignment = GetDevice<VulkanDevice>()->mPhysicalDeviceInfo->mProps.properties.limits.minStorageBufferOffsetAlignment;
        // Assert(false);
        break;
    }
    return AlignSize(offset, alignment);
}
