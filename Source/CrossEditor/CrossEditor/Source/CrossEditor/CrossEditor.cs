using EditorUI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace CrossEditor
{
    public class CrossEditor
    {
        static void LoadTheme(string Filename)
        {
            XmlScript Xml = new XmlScript();
            Xml.Open(Filename);
            Record RootRecord = Xml.GetRootRecord();
            Record RecordTheme = RootRecord.FindByTypeString("Theme");
            Record RecordShortcuts = RootRecord.FindByTypeString("Shortcuts");
            if (RecordShortcuts != null)
            {
                ShortcutConfig.GetInstance().LoadShortcutConfig(RecordShortcuts);
            }
        }

        static void ProcessThumbnail(string[] Args)
        {
            string ThumbnailProject = "";
            if (FileHelper.IsFileExists("EnableThumbnailTest.txt"))
            {
                ThumbnailProject = PathHelper.ToAbsolutePath("G:\\ProjectFFS");
                ThumbnailUI.bRunThumbTest = true;
            }
            foreach (string Arg in Args)
            {
                string ThumbnailProjectPrefix = "ThumbnailProject=";
                if (Arg.StartsWith(ThumbnailProjectPrefix))
                {
                    ThumbnailProject = Arg.Substring(ThumbnailProjectPrefix.Length);
                    ThumbnailProject = ThumbnailProject.Trim('"');
                    ThumbnailProject = PathHelper.ToStandardForm(ThumbnailProject);
                }
            }
            if (ThumbnailProject != "")
            {
                ThumbnailUI ThumbnailUI = ThumbnailUI.GetInstance();
                //if (ThumbnailUI.Initialize(ThumbnailProject))
                //{
                //    ThumbnailUI.Run();
                //    ThumbnailUI.Release();
                //}
                SystemHelper.ExitProcess(0);
            }
        }

        static void TeminateThumbnail()
        {
            ThumbnailHost.GetInstance().TeminateGenerateThumbnailProcess();
        }

        static void SetupMacEnvironment()
        {
            if (Environment.OSVersion.Platform == PlatformID.Unix)
            {
                string BinDirectory = System.IO.Directory.GetCurrentDirectory();
                string FrameworkDirectory = string.Format("{0}/{1}/Frameworks", BinDirectory, EditorUtilities.GetModeString());
                Environment.SetEnvironmentVariable("DYLD_LIBRARY_PATH", FrameworkDirectory);
            }
        }

        static void SetupTDM()
        {
            //TDM.ITDataMaster.Instance.SetRouterAddress(false, "https://hc.tdm.qq.com:8013/tdm/v1/route");
            //TDM.ITDataMaster.Instance.Initialize("939738315", "CrossEditorTDM", false);
        }

        static bool IsStartupFromCommandLine(string[] Args)
        {
            if (Args.Length == 1)
            {
                if (Args[0].StartsWith("TargetProject="))
                {
                    return true;
                }
            }
            return false;
        }



        internal static void AddEnvironmentPaths(IEnumerable<string> paths)
        {
            var path = new[] { Environment.GetEnvironmentVariable("PATH") ?? string.Empty };
            string newPath = string.Join(Path.PathSeparator.ToString(), path.Concat(paths));
            Environment.SetEnvironmentVariable("PATH", newPath);
        }

        [System.Runtime.InteropServices.DllImport("kernel32.dll", SetLastError = true, CharSet = System.Runtime.InteropServices.CharSet.Unicode)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [STAThread]
        public static void Main(string[] Args)
        {
            // Explicitly load mimalloc-redirect.dll as early as possible to ensure
            // it hooks memory allocations before any other module, especially the
            // debug C++ runtime (ucrtbased.dll), performs its own allocations.
            // This is critical for preventing loader lock issues in Debug builds.
#if DEBUG
            // In Debug builds, we link against the secure/debug versions.
            // The exact name of the redirect DLL might be "mimalloc-redirect-secure.dll"
            // or "mimalloc-redirect-debug.dll". We default to the most common one.
            // Adjust if your build process produces a different name.
            string mimallocRedirectName = "mimalloc-redirect.dll"; 
#else
            string mimallocRedirectName = "mimalloc-redirect.dll";
#endif

#if MACOSX
            System.Console.WriteLine("[Platform]: MACOSX");
#endif
            CrossSynchronizationContext.Instance.Initialize();

            DirectoryHelper.SetupWorkingDirectory();
            string WorkingDirectory = Directory.GetCurrentDirectory();

#if !DEBUG
            if (Environment.OSVersion.Platform != PlatformID.Unix)
            {
                //AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler((obj, args) =>
                //{
                //    MiniDump.TryDump(WorkingDirectory + "\\CrossEngine.dmp");
                //});
            }
#endif
            // Runtime.CopyDllToDotnet(WorkingDirectory);
            SetupMacEnvironment();
            SetupTDM();
            ProcessThumbnail(Args);

            if (StringHelper.IsUnicodeString(WorkingDirectory))
            {
                Device.ShowMessageBox(null, "Tips", "There is non-ascii char/chars in working directory path string.");
            }

            //CommandLineExecutor.GetInstance().ExecuteCommand(Args);
            LoadTheme("Editor/Themes/Default.lpxml");
            EditorConfig.GetInstance().LoadEditorConfig();

            // add  an exe to match arg parse library
            string[] ArgsWithExe = { };

            string TargetProject = "";
            if (IsStartupFromCommandLine(Args))
            {
                int Length = "TargetProject=".Length;
                TargetProject = Args[0].Substring(Length);
                ArgsWithExe = Args;
            }
            else
            {
                if (!EditorUtilities.IsPublishEditor())
                {
                    string CurrentDirectory = Directory.GetCurrentDirectory();
                    if (CurrentDirectory.Contains("Pack") == false)
                    {
                        string LastProjectPath = EditorConfig.GetInstance().LastProjectPath;
                        if (LastProjectPath != "")
                        {
                            if (FileHelper.IsFileExists(LastProjectPath))
                            {
                                TargetProject = LastProjectPath;
                            }
                        }
                    }
                }
                ArgsWithExe = Args.ToList().Prepend("exe").ToArray();
            }

            string ProjectDirectory = "";
            if (TargetProject != "")
            {
                ProjectDirectory = PathHelper.GetDirectoryName(TargetProject);
            }
            else
            {
                ProjectDirectory = EditorUtilities.GetResourceDirectory();
            }



            if (TargetProject != "")
            {
                CrossEngine.TestLoadDylibOSX();

                bool bHide = true;
                StartupUI StartupUI = StartupUI.GetInstance();
                StartupUI.Initialize(ProjectDirectory, bHide, ArgsWithExe);
                CommandLineExecutor.GetInstance().ExecuteCommand(Args);
                StartupUI.Release();
                UIManager UIManager = StartupUI.GetUIManager();


                ProjectsUI ProjectsUI = ProjectsUI.GetInstance();
                ProjectsUI.Initialize(UIManager, false);
                ProjectsUI.SetTargetProjectFileName(TargetProject);
                ProjectsUI.Release();


                MainUI MainUI = MainUI.GetInstance();
                if (MainUI.Initialize(UIManager))
                {
                    ResourceFilterUI.GetInstance().GetCollectingThread().Start();

                    int Version = 1001;
                    PluginManager.GetInstance().LoadPlugins(Version);
                    MainUI.Run();
                    PluginManager.GetInstance().ReleasePlugins();
                    MainUI.Release();
                }

                TeminateThumbnail();
                SystemHelper.ExitProcess(0);
            }
            else
            {
                bool bHide = false;
                StartupUI StartupUI = StartupUI.GetInstance();
                if (StartupUI.Initialize(ProjectDirectory, bHide, ArgsWithExe))
                {
                    StartupUI.Run();
                    CommandLineExecutor.GetInstance().ExecuteCommand(Args);

                    StartupUI.Release();
                }

                UIManager UIManager = StartupUI.GetUIManager();

                ProjectsUI ProjectsUI = ProjectsUI.GetInstance();
                if (ProjectsUI.Initialize(UIManager, bHide))
                {
                    ProjectsUI.Run();
                    ProjectsUI.Release();
                }


                if (ProjectsUI.GetClosed() == false)
                {
                    string ProjectFilename = ProjectsUI.GetInstance().GetTargetProjectFilename();
                    string ProjectDirectory1 = PathHelper.GetDirectoryName(ProjectFilename);
                    MainUI.GetInstance().CopyProjectConfigJson(ProjectDirectory1);

                    string ExecutableDirectory = DirectoryHelper.GetExecutableDirectory();
                    string ProjectPath = ProjectsUI.GetInstance().GetTargetProjectFilename();
                    string DotNetProgramPath = EditorUtilities.GetDotNetProgramPath();
                    string DllPath = string.Format("{0}/CrossEditor.dll", ExecutableDirectory);
                    string ExePath = string.Format("{0}/CrossEditor.exe", ExecutableDirectory);
                    if (Environment.OSVersion.Platform != PlatformID.Unix)
                    {
                        DotNetProgramPath = string.Format("\"{0}\"", DotNetProgramPath);
                    }
                    DllPath = string.Format("\"{0}\"", DllPath);
                    ProjectPath = string.Format("\"{0}\"", ProjectPath);
                    if (Environment.OSVersion.Platform == PlatformID.Win32NT)
                    {
                        string ProgramPath = string.Format("TargetProject={0}", ProjectPath);
                        ProcessHelper.Execute(ExePath, ProgramPath);
                        SystemHelper.ExitProcess(0);
                    }
                    else
                    {
                        string EditorProgramPath = string.Format("{0} TargetProject={1}", DllPath, ProjectPath);
                        ProcessHelper.Execute(DotNetProgramPath, EditorProgramPath);
                        SystemHelper.ExitProcess(0);
                    }
                }
            }
        }
    }
}
