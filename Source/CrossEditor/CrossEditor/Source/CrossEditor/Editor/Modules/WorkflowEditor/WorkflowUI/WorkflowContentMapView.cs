using EditorUI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace CrossEditor
{
    public class WorkflowContentMapView
    {
        class MixedButton
        {
            public Button left;
            public Button right;
        }
        public enum MapViewChangeEvent { Add, Delete, Rename, ItemUpdate }

        public delegate void OnMapViewChangedEvent(MapViewChangeEvent Event, string key, string other_key);

        public delegate void OnMapViewItemSelectEvent(string item);

        public event OnMapViewChangedEvent OnMapViewChanged = null;

        public event OnMapViewItemSelectEvent OnMapViewItemSelect = null;

        OperationBarUI _OperationBarUI = new OperationBarUI();
        SearchUI _SearchUI = new SearchUI();
        ScrollView _ScrollView = new ScrollView();
        Panel _ScrollPanel;
        protected object _Dictionary;

        Type _DictionaryType;
        PropertyInfo _PropertyInfo_Count;
        MethodInfo _MethodInfo_GetMethod;
        MethodInfo _MethodInfo_SetMethod;
        MethodInfo _MethodInfo_EraseMethod;
        MethodInfo _MethodInfo_ContainsMethod;
        MethodInfo _MethodInfo_GetKeys;

        MethodInfo _MethodInfo_SetItemName;

        Type _ItemType;
        protected string _ItemKey;

        Button _ButtonAdd;

        List<MixedButton> _ItemButtons = new List<MixedButton>();
        int _RowHeight = 20;
        public WorkflowContentMapView()
        {

            _OperationBarUI.Initialize();
            _OperationBarUI.GetPanelBar().SetText("Details");
            _OperationBarUI.GetPanelBar().SetTextAlign(TextAlign.CenterLeft);
            _OperationBarUI.GetPanelBar().SetFontSize(12);
            _OperationBarUI.GetPanelBar().SetTextOffsetX(6);

            _SearchUI.Initialize();
            _SearchUI.SearchEvent += OnSearchUISearch;
            _SearchUI.CancelEvent += OnSearchUICancel;
            Panel PanelBack = _SearchUI.GetPanelBack();
            PanelBack.SetPosition(0, 2, 130, 20);
            PanelBack.SetBackgroundColor(OperationBarUI.BAR_COLOR);
            _OperationBarUI.AddRight(PanelBack);

            _ButtonAdd = new Button();
            _ButtonAdd.Initialize();
            _ButtonAdd.SetFontSize(12);
            _ButtonAdd.SetText("+");
            _ButtonAdd.SetTextOffsetY(1);
            _ButtonAdd.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonAdd.SetToolTips("Add Item");
            _ButtonAdd.SetPosition(0, 2, _RowHeight, 20);
            _ButtonAdd.ClickedEvent += OnButtonAddClicked;
            _OperationBarUI.AddRight(_ButtonAdd);

            _ScrollView.Initialize();
            _ScrollView.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            _ScrollView.GetHScroll().SetEnable(false);

            _ScrollPanel = _ScrollView.GetScrollPanel();
            _ScrollPanel.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            _ScrollPanel.PaintEvent += (Sender) =>
            {
                SetChildInnerEnable(Sender);
                Sender.PaintThis();
                Sender.PaintChildren();
            };
            _ScrollView.PositionChangedEvent += OnScrollViewChanged;
        }

        public void TriggerMapViewChanged(MapViewChangeEvent Event, string key, string other_key)
        {
            OnMapViewChanged?.Invoke(Event, key, other_key);
        }

        public void BindProperty(object InDict, string DictName)
        {
            _OperationBarUI.GetPanelBar().SetText(DictName);
            _Dictionary = InDict;
            _DictionaryType = _Dictionary.GetType();
            _PropertyInfo_Count = _DictionaryType.GetProperty("Count");
            _MethodInfo_GetMethod = _DictionaryType.GetMethod("get_Item");
            _MethodInfo_SetMethod = _DictionaryType.GetMethods().Single(x => x.Name == "Insert" && x.GetParameters().Length == 2);
            _MethodInfo_EraseMethod = _DictionaryType.GetMethods().Single(x => x.Name == "Erase" && x.GetParameters().Length == 1 && x.GetParameters()[0].ParameterType == typeof(string));
            _MethodInfo_ContainsMethod = _DictionaryType.GetMethod("ContainsKey");
            _MethodInfo_GetKeys = _DictionaryType.GetMethod("Keys");
            _ItemType = _MethodInfo_SetMethod.GetParameters()[1].ParameterType;
            _MethodInfo_SetItemName = _ItemType.GetMethod("SetName");

            RefreshButtons();
        }
        public void OnAddToParent(VContainer mContainer)
        {
            mContainer.AddFixedChild(_OperationBarUI.GetPanelBar());
            mContainer.AddSizableChild(Control, 1);
        }

        private void RefreshInspectorUI()
        {
            int ScrollPanelWidth = _ScrollView.GetWidth();
            int Y = 0;
            if (_ItemButtons != null && _Dictionary != null)
            {
                foreach (var button in _ItemButtons)
                {
                    if (button.left.GetVisible())
                    {
                        Y += _RowHeight;
                    }
                }
                if (Y > _ScrollView.GetHeight())
                {
                    ScrollPanelWidth = _ScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                }
                Y = 0;
                foreach (var button in _ItemButtons)
                {
                    if (button.left.GetVisible())
                    {
                        // button.panel.SetPosition(0, Y, ScrollPanelWidth, _RowHeight);
                        button.left.SetPosition(0, Y, ScrollPanelWidth - _RowHeight - 2, _RowHeight);
                        button.right.SetPosition(ScrollPanelWidth - _RowHeight, Y, _RowHeight, _RowHeight);
                        Y += _RowHeight;
                    }
                }
            }

            int Height = Y;
            _ScrollPanel.SetSize(ScrollPanelWidth, Height);
            _ScrollView.UpdateScrollBar();
        }



        private void SetChildInnerEnable(Control Control)
        {
            for (int i = 0; i < Control.GetChildCount(); ++i)
            {
                Control Child = Control.GetChild(i);
                if (UIManager.RectInRect(Child.GetScreenX(), Child.GetScreenY(), Child.GetWidth(), Child.GetHeight(),
                    _ScrollView.GetScreenX(), _ScrollView.GetScreenY(), _ScrollView.GetWidth(), _ScrollView.GetHeight()))
                {
                    Child.SetInnerEnable(true);

                    if (Child.GetChildCount() != 0)
                    {
                        SetChildInnerEnable(Child);
                    }
                }
                else
                {
                    Child.SetInnerEnable(false);
                }
            }
        }

        public Control Control => _ScrollView;

        public void OnSearchUISearch(SearchUI Sender, string Pattern)
        {
            if (_ItemButtons != null)
            {
                List<Button> ResultButton = new List<Button>();
                _ItemButtons.ForEach((OneButton) =>
                {

                    // Traverse all controls
                    if (StringHelper.IgnoreCaseContains(OneButton.left.GetText(), Pattern))
                    {
                        OneButton.left.SetVisible(true);
                        OneButton.right.SetVisible(true);
                    }
                    else
                    {
                        OneButton.left.SetVisible(false);
                        OneButton.right.SetVisible(false);
                    }
                });

                RefreshInspectorUI();
            }
        }

        public void OnSearchUICancel(SearchUI Sender)
        {
            if (_ItemButtons != null)
            {
                _ItemButtons.ForEach((OneButton) =>
                {
                    OneButton.left.SetVisible(true);
                    OneButton.right.SetVisible(true);
                });

                RefreshInspectorUI();
            }
        }

        protected int GetDictionaryCount(object Dictionary)
        {
            return (int)_PropertyInfo_Count.GetValue(Dictionary);
        }

        protected object GetDictionaryItem(object Dictionary, object Key)
        {
            return _MethodInfo_GetMethod.Invoke(Dictionary, new object[] { Key });
        }

        protected bool ContainesDictionaryKey(object Dictionary, object Key)
        {
            return (bool)_MethodInfo_ContainsMethod.Invoke(Dictionary, new object[] { Key });
        }
        protected void RemoveDictionayItem(object Dictionary, object Key)
        {
            _MethodInfo_EraseMethod.Invoke(Dictionary, new object[] { Key });
        }
        protected void SetDictionaryItem(object Dictionary, object Key, object Value)
        {
            _MethodInfo_SetMethod.Invoke(Dictionary, new object[] { Key, Value });
        }

        protected object NewItem(string ItemName)
        {
            if (_ItemType == typeof(string))
            {
                return "";
            }
            else
            {
                var obj = Activator.CreateInstance(_ItemType);
                _MethodInfo_SetItemName?.Invoke(obj, new object[] { ItemName });
                return obj;
            }
        }

        UIManager GetUIManager()
        {
            return _OperationBarUI.GetPanelBar().GetUIManager();
        }

        protected void OnButtonAddClicked(Button Sender)
        {
            Sender.CloseToolTips();
            CancelSelect();
            TextInputUI TextInputUI = new TextInputUI();
            TextInputUI.Initialize(GetUIManager(), "Create Item", "Please input Item Name:", "");
            TextInputUI.InputedEvent += (TextInputUI Sender, string StringInputed) =>
            {
                if (StringInputed == "")
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Invalid Name", "Item name can not be empty!");
                    return;
                }
                if (ContainesDictionaryKey(_Dictionary, StringInputed))
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Invalid Name", "Duplicated Item name: " + StringInputed);
                    return;
                }
                var Item = NewItem(StringInputed);

                SetDictionaryItem(_Dictionary, StringInputed, Item);
                RefreshButtons();
                if (OnMapViewChanged != null)
                {
                    OnMapViewChanged(MapViewChangeEvent.Add, StringInputed, new string(""));
                }

                // switch inspected object to the newly created object
                _ItemKey = StringInputed;
                if (OnMapViewItemSelect != null)
                {
                    OnMapViewItemSelect(StringInputed);
                }

            };
            TextInputUI.ShowDialog();

        }

        protected virtual string GetItemNameFromKey(string Key)
        {
            return Key;
        }

        protected void RefreshButtons()
        {
            _ItemButtons.Clear();
            var DictKeys = (List<string>)_MethodInfo_GetKeys.Invoke(_Dictionary, null);
            DictKeys.Sort((x, y) => GetItemNameFromKey(x).CompareTo(GetItemNameFromKey(y)));
            _ScrollView.ClearChildren();
            foreach (var Key in DictKeys)
            {
                Button ItemSelectButton = new Button();
                ItemSelectButton.Initialize();
                ItemSelectButton.SetTagString1(Key);
                ItemSelectButton.SetFontSize(12);
                ItemSelectButton.SetText(GetItemNameFromKey(Key));
                ItemSelectButton.SetTextOffsetY(1);
                ItemSelectButton.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                ItemSelectButton.ClickedEvent += OnButtonItemSelectClicked;
                Button ItemMenuButton = new Button();
                ItemMenuButton.Initialize();
                ItemMenuButton.SetTagString1(Key);
                ItemMenuButton.SetFontSize(12);
                ItemMenuButton.SetText("...");
                ItemMenuButton.SetToolTips("Operations");
                ItemMenuButton.SetTextOffsetY(1);
                ItemMenuButton.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                ItemMenuButton.ClickedEvent += OnButtonItemMenuClicked;

                MixedButton ItemMixedButton = new MixedButton();
                ItemMixedButton.left = ItemSelectButton;
                ItemMixedButton.right = ItemMenuButton;
                _ItemButtons.Add(ItemMixedButton);
                _ScrollView.AddChild(ItemMixedButton.left);
                _ScrollView.AddChild(ItemMixedButton.right);

            }
            _SearchUI.TriggerSearchEvent();
        }

        void OnButtonItemSelectClicked(Button Sender)
        {
            _ItemKey = Sender.GetTagString1();
            EditorLogger.Log(LogMessageType.Information, string.Format("OnButtonItemSelectClicked {0}", _ItemKey));
            if (OnMapViewItemSelect != null)
            {
                OnMapViewItemSelect(_ItemKey);
            }
        }

        void OnButtonItemMenuClicked(Button Sender)
        {
            _ItemKey = Sender.GetTagString1();

            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();

            BuildItemMenu(MenuContextMenu);


            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);
        }

        protected virtual void BuildItemMenu(Menu MenuContextMenu)
        {
            MenuItem MenuItem_DeleteItem = new MenuItem();
            MenuItem_DeleteItem.SetText("Delete");
            MenuItem_DeleteItem.ClickedEvent += OnMenuItemDeleteClicked;


            MenuItem MenuItem_Rename = new MenuItem();
            MenuItem_Rename.SetText("Rename");
            MenuItem_Rename.ClickedEvent += OnMenuItemRenameClicked;


            MenuContextMenu.AddMenuItem(MenuItem_DeleteItem);
            MenuContextMenu.AddMenuItem(MenuItem_Rename);
        }

        void OnMenuItemDeleteClicked(MenuItem MenuItem)
        {
            RemoveDictionayItem(_Dictionary, _ItemKey);
            RefreshButtons();
            if (OnMapViewChanged != null)
            {
                OnMapViewChanged(MapViewChangeEvent.Delete, _ItemKey, new string(""));
            }
        }

        void OnMenuItemRenameClicked(MenuItem MenuItem)
        {

            TextInputUI TextInputUI = new TextInputUI();
            TextInputUI.Initialize(GetUIManager(), "Rename Item", "Please input a New Name:", _ItemKey);
            var pre_item_key = _ItemKey;
            TextInputUI.InputedEvent += (TextInputUI Sender, string StringInputed) =>
            {

                if (StringInputed == "")
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Invalid Name", "Item name can not be empty!");
                    return;
                }
                if (ContainesDictionaryKey(_Dictionary, StringInputed))
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Invalid Name", "Duplicated Item name: " + StringInputed);
                    return;
                }
                CancelSelect();
                var pre_item_value = GetDictionaryItem(_Dictionary, pre_item_key);
                _MethodInfo_SetItemName.Invoke(pre_item_value, new object[] { StringInputed });
                SetDictionaryItem(_Dictionary, StringInputed, pre_item_value);
                RemoveDictionayItem(_Dictionary, pre_item_key);
                RefreshButtons();
                if (OnMapViewChanged != null)
                {
                    OnMapViewChanged(MapViewChangeEvent.Rename, pre_item_key, StringInputed);
                }
            };
            TextInputUI.ShowDialog();
        }

        public void OnScrollViewChanged(Control sender, bool poschange, bool sizechange)
        {
            _SearchUI.TriggerSearchEvent();
        }
        void CancelSelect()
        {
            GetUIManager().SetFocusControl(null);
            _ItemKey = null;
            if (OnMapViewItemSelect != null)
            {
                OnMapViewItemSelect(_ItemKey);
            }
        }
    }
}
