using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Text;

namespace CrossEditor
{
    public class GradientColor
    {
        const float CE_KINDA_SMALL_NUMBER = 0.0001f;
        protected Panel _CommonControl;
        protected Panel _ColorStopUI;
        protected Panel _GradientColorUI;
        protected Panel _AlphaStopUI;
        public List<MoveStop> _ColorStops = new List<MoveStop>();
        public List<MoveStop> _AlphaStops = new List<MoveStop>();

        protected CurveGraphicsHelper CurveGraphicsHelper;
        bool bLeftMouseDown = false;
        IMovable HoldingObject = null;
        int LastMouseLocationX = 0;
        int LastMouseLocationY = 0;
        int LeftMouseFirstLocationX = 0;
        int LeftMouseFirstLocationY = 0;
        MoveStop CurMoveStop = new MoveStop();
        AdjustColor AdjustColor = new AdjustColor();
        protected int _StopSize = 2000;
        UInt32[] _Colors = new UInt32[2000];
        UInt32[] _stopPoseX = new UInt32[2000];
        protected List<CurveManager> _Curves = new List<CurveManager>();
        //test 
        int MouseX = 0, MouseY = 0;

        public GradientColor()
        {

        }

        public void Initialize(int Height)
        {
            _CommonControl = new Panel();
            _CommonControl.Initialize();
            _CommonControl.SetHeight(Height);
            //_CommonControl.SetBackgroundColor(Color.EDITOR_UI_TEST_COLOR_RED);
            _CommonControl.MouseMoveEvent += CommonControlMouseMove;
            _CommonControl.LeftMouseUpEvent += CommonControlLeftMouseUp;
            _CommonControl.LeftMouseDownEvent += CommonControlLeftMouseDown;
            _CommonControl.LeftMouseDoubleClickedEvent += CommonControlLeftMouseDoubleClicked;
            _CommonControl.PaintEvent += CommonControlPaint;
            _CommonControl.PositionChangedEvent += CommonControlPositionChanged;

            _ColorStopUI = new Panel();
            _ColorStopUI.Initialize();
            _ColorStopUI.SetY(0);
            _ColorStopUI.SetHeight(30);
            _ColorStopUI.LeftMouseDownEvent += ColorStopUILeftMouseDown;
            //_ColorStopUI.SetBackgroundColor(Color.EDITOR_UI_TEST_COLOR_GREEN);
            //_ColorStopUI.PaintEvent += ColorStopUIPaintEvent;

            _GradientColorUI = new Panel();
            _GradientColorUI.Initialize();
            _GradientColorUI.SetY(30);
            _GradientColorUI.SetHeight(60);
            //_GradientColorUI.SetBackgroundColor(Color.EDITOR_UI_COLOR_WHITE);

            _AlphaStopUI = new Panel();
            _AlphaStopUI.Initialize();
            _AlphaStopUI.SetY(90);
            _AlphaStopUI.SetHeight(30);
            //_AlphaStopUI.SetBackgroundColor(Color.EDITOR_UI_TEST_COLOR_RED);
            _AlphaStopUI.LeftMouseDownEvent += AlphaStopUILeftMouseDown;
            //_AlphaStopUI.PaintEvent += AlphaStopUIPaintEvent;
            _AlphaStopUI.MouseMoveEvent += AlphaStopUIMouseMoveEvent;
            _AlphaStopUI.LeftMouseUpEvent += AlphaStopUILeftMouseUp;

            _CommonControl.AddChild(_ColorStopUI);
            _CommonControl.AddChild(_GradientColorUI);
            _CommonControl.AddChild(_AlphaStopUI);

        }

        public void InitializeGradientStop()
        {
            Vector2m PosStart = new Vector2f(0.0f, 0.0f);
            Vector2m PosEnd = new Vector2f(1.0f, 0.0f);
            Vector2m PosStartScreen = CurveGraphicsHelper.WorldToScreen(PosStart);
            Vector2m PosEndScreen = CurveGraphicsHelper.WorldToScreen(PosEnd);

            //
            List<CurveManager> Curves = LinearColorCurveEditorUI.GetInstance().GetCurves();
            if (Curves.Count != 4)
            {
                return;
            }

            //generate colorstops
            MoveStop Stop1 = new MoveStop();
            Stop1.Initialize(StopType.ColorStop, (int)PosStartScreen.X - 10, _ColorStopUI.GetScreenY() + 10, 0);
            AddGradientStop(StopType.ColorStop, Stop1);
            Stop1.SetPoints(new List<Point> { Curves[0].Points[0], Curves[1].Points[0], Curves[2].Points[0] });

            MoveStop Stop2 = new MoveStop();
            Stop2.Initialize(StopType.ColorStop, (int)PosEndScreen.X - 10, _ColorStopUI.GetScreenY() + 10, 1);
            AddGradientStop(StopType.ColorStop, Stop2);
            Stop2.SetPoints(new List<Point> { Curves[0].Points[1], Curves[1].Points[1], Curves[2].Points[1] });

            //generate alphastops
            MoveStop Stop3 = new MoveStop();
            Stop3.Initialize(StopType.AlphaStop, (int)PosStartScreen.X - 10, _AlphaStopUI.GetScreenY(), 0);
            AddGradientStop(StopType.AlphaStop, Stop3);
            Stop3.SetPoints(new List<Point> { Curves[3].Points[0] });

            MoveStop Stop4 = new MoveStop();
            Stop4.Initialize(StopType.AlphaStop, (int)PosEndScreen.X - 10, _AlphaStopUI.GetScreenY(), 1);
            AddGradientStop(StopType.AlphaStop, Stop4);
            Stop4.SetPoints(new List<Point> { Curves[3].Points[1] });
        }

        public void UpdateWidth(CurveGraphicsHelper CurveGraphicsHelper)
        {
            int Width = CurveGraphicsHelper.Width;
            _CommonControl.SetWidth(Width);
            _ColorStopUI.SetWidth(Width);
            _GradientColorUI.SetWidth(Width);
            _AlphaStopUI.SetWidth(Width);

            this.CurveGraphicsHelper = CurveGraphicsHelper;
            if (this.CurveGraphicsHelper != null && !LinearColorCurveEditorUI.GetInstance().GetBLoadLoadCurve())
            {
                if (_ColorStops.Count == 0 && _AlphaStops.Count == 0)
                {
                    InitializeGradientStop();
                }
            }

            if (this.CurveGraphicsHelper != null && LinearColorCurveEditorUI.GetInstance().GetBLoadLoadCurve())
            {
                if (_Curves.Count != 0 && _ColorStops.Count == 0 && _AlphaStops.Count == 0)
                {
                    AutoAddGradientStop(_Curves);
                }
            }
        }

        #region Event Trigger
        private void CommonControlPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            Scale(CurveGraphicsHelper);
        }

        public void Move(float DeltaX, float DeltaY, CurveGraphicsHelper CurveGraphicsHelper)
        {
            //move ColorStops
            foreach (var ColorStop in _ColorStops)
            {
                ColorStop.Move((decimal)DeltaX, (decimal)DeltaY, CurveGraphicsHelper);
            }

            //move AlphaStops
            foreach (var AlphaStop in _AlphaStops)
            {
                AlphaStop.Move((decimal)DeltaX, (decimal)DeltaY, CurveGraphicsHelper);
            }
        }

        public void Scale(CurveGraphicsHelper CurveGraphicsHelper)
        {
            //move ColorStops
            foreach (var ColorStop in _ColorStops)
            {
                Vector2f Scale = CurveGraphicsHelper.WorldToScreen(new Vector2f(ColorStop._ScaleX, 0));
                ColorStop.Scale(Scale.X - ColorStop._ValueX - 10, 0, CurveGraphicsHelper);
            }

            //move AlphaStops
            foreach (var AlphaStop in _AlphaStops)
            {
                Vector2f Scale = CurveGraphicsHelper.WorldToScreen(new Vector2f(AlphaStop._ScaleX, 0));
                AlphaStop.Scale(Scale.X - AlphaStop._ValueX - 10, 0, CurveGraphicsHelper);
            }
        }

        private void CommonControlLeftMouseDoubleClicked(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            List<MoveStop> StopList = new List<MoveStop>();
            if (StopUIHitTest(MouseX, MouseY, StopType.ColorStop))
            {
                StopList = _ColorStops;
            }
            else if (StopUIHitTest(MouseX, MouseY, StopType.AlphaStop))
            {
                StopList = _AlphaStops;
            }

            foreach (var Stop in StopList)
            {
                MoveStop MoveStop = Stop.HitTest(MouseX, MouseY, CurveGraphicsHelper);
                if (MoveStop == null) continue;
                if (MoveStop.GetStopType() == StopType.ColorStop)
                {
                    OpenColorSelectUI(Sender.GetUIManager(), MoveStop);
                    return;
                }
                else
                {
                    OpenAlphaSelectUI(Sender.GetUIManager(), MoveStop);
                    return;
                }
            }
        }

        private void CommonControlPaint(Control Sender)
        {
            UIManager UIManager = Sender.GetUIManager();

            // Draw mouse X,Y
            //EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            //EditorUICanvas.DrawRectangle(MouseX, MouseY, 30, 30,
            //        ref Color.White);
            //Font Font = UIManager.GetDefaultFont(14);
            //Font.DrawString(MouseX + " , " + MouseY, ref Color.White, MouseX + 2, MouseY + 2);

            if (CurveGraphicsHelper != null)
            {
                Draw(CurveGraphicsHelper, UIManager);
            }

            //draw ColorStops
            foreach (var ColorStop in _ColorStops)
            {
                ColorStop.Draw(UIManager, StopType.ColorStop, _ColorStopUI.GetScreenY() + 10, CurveGraphicsHelper);
            }

            //draw AlphaStops
            foreach (var AlphaStop in _AlphaStops)
            {
                AlphaStop.Draw(UIManager, StopType.AlphaStop, _AlphaStopUI.GetScreenY(), CurveGraphicsHelper);
            }
        }

        private void CommonControlLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            bLeftMouseDown = true;
            LastMouseLocationX = MouseX;
            LastMouseLocationY = MouseY;
            LeftMouseFirstLocationX = MouseX;
            LeftMouseFirstLocationY = MouseY;

            StopType StopType = StopType.None;
            List<MoveStop> StopList = new List<MoveStop>();

            if (StopUIHitTest(MouseX, MouseY, StopType.ColorStop))
            {
                StopType = StopType.ColorStop;
                StopList = _ColorStops;
            }
            else if (StopUIHitTest(MouseX, MouseY, StopType.AlphaStop))
            {
                StopType = StopType.AlphaStop;
                StopList = _AlphaStops;
            }

            if (StopType == StopType.None)
                return;

            foreach (var Stop in StopList)
            {
                MoveStop MoveStop = Stop.HitTest(MouseX, MouseY, CurveGraphicsHelper);
                if (MoveStop != null)
                {
                    //Highlight
                    SetCurStopNull();
                    MoveStop.SetHighLight(true);
                    CurMoveStop = MoveStop;
                    HoldingObject = MoveStop;
                    return;
                }
            }

            if (SetCurStopNull())
                return;

            int LocationX = MouseX - 10;
            int LocationY = StopType == StopType.ColorStop ? _ColorStopUI.GetScreenY() + 10 : _AlphaStopUI.GetScreenY();
            MoveStop NewAlphaStop = new MoveStop();
            float ScaleX = (float)CurveGraphicsHelper.ScreenToWorld(new Vector2f(LocationX + 10, LocationY)).X;
            NewAlphaStop.Initialize(StopType, LocationX, LocationY, ScaleX);
            List<Point> Points = LinearColorCurveEditorUI.GetInstance().AddKeyInGradient(MouseX, MouseY, StopType);
            NewAlphaStop.SetPoints(Points);
            AddGradientStop(StopType, NewAlphaStop);

            EditOperation_AddStop EditOperation_AddStop = new EditOperation_AddStop(NewAlphaStop, MouseX, MouseY);
            EditOperationManager.GetInstance().AddOperation(EditOperation_AddStop);
        }

        private void CommonControlLeftMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            //add move stop operation redo and undo
            if (bLeftMouseDown)
            {
                int DeltaX = LastMouseLocationX - LeftMouseFirstLocationX;
                int DeltaY = LastMouseLocationY - LeftMouseFirstLocationY;
                if (HoldingObject is MoveStop)
                {
                    EditOperation_MoveStop EditOperation_MoveStop = new EditOperation_MoveStop((MoveStop)HoldingObject, DeltaX, CurveGraphicsHelper);
                    EditOperationManager.GetInstance().AddOperation(EditOperation_MoveStop);
                }
            }
            bLeftMouseDown = false;
            HoldingObject = null;
        }

        private void CommonControlMouseMove(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (bLeftMouseDown)
            {
                int DeltaX = MouseX - LastMouseLocationX;
                int DeltaY = MouseY - LastMouseLocationY;
                if (HoldingObject != null)
                {
                    HoldingObject.MoveTo(DeltaX, DeltaY, CurveGraphicsHelper);
                }
                LastMouseLocationX = MouseX;
                LastMouseLocationY = MouseY;
            }

            //test 
            this.MouseX = MouseX;
            this.MouseY = MouseY;
        }

        private void ColorStopUIPaintEvent(Control Sender)
        {
        }

        private void ColorStopUILeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
        }


        private void AlphaStopUILeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
        }

        private void AlphaStopUILeftMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
        }

        private void AlphaStopUIMouseMoveEvent(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
        }
        private void AlphaStopUIPaintEvent(Control Sender)
        {
        }

        #endregion

        public void DeleteStop()
        {
            if (CurMoveStop != null && CurMoveStop._ContainPoints.Count != 0)
            {
                if (CurMoveStop.GetStopType() == StopType.ColorStop)
                {
                    _ColorStops.Remove(CurMoveStop);
                }
                else
                {
                    _AlphaStops.Remove(CurMoveStop);
                }
                LinearColorCurveEditorUI.GetInstance().DeleteKeyInGradientAlpha(CurMoveStop._ContainPoints);

                EditOperation_DeleteStop EditOperation_DeleteStop = new EditOperation_DeleteStop(CurMoveStop, CurMoveStop._ValueX, CurMoveStop._ValueY);
                EditOperationManager.GetInstance().AddOperation(EditOperation_DeleteStop);
            }
        }

        public void DeleteStopFromPoints(Dictionary<CurveManager, List<Point>> DicPoints)
        {
            foreach (var Stop in _ColorStops)
            {
                foreach (var Item in DicPoints)
                {
                    foreach (var Point in Item.Value)
                    {
                        if (Stop._ContainPoints.Contains(Point))
                        //if (IsContainPoint(Stop._ContainPoints, Point))
                        {
                            _ColorStops.Remove(Stop);
                            EditOperation_DeleteStop EditOperation_DeleteStop = new EditOperation_DeleteStop(Stop, Stop._ValueX, Stop._ValueY, true);
                            EditOperationManager.GetInstance().AddOperation(EditOperation_DeleteStop);
                            return;
                        }
                    }
                }
            }

            foreach (var Stop in _AlphaStops)
            {
                foreach (var Item in DicPoints)
                {
                    foreach (var Point in Item.Value)
                    {
                        if (Stop._ContainPoints.Contains(Point))
                        //if (IsContainPoint(Stop._ContainPoints, Point))
                        {
                            _AlphaStops.Remove(Stop);
                            EditOperation_DeleteStop EditOperation_DeleteStop = new EditOperation_DeleteStop(Stop, Stop._ValueX, Stop._ValueY, true);
                            EditOperationManager.GetInstance().AddOperation(EditOperation_DeleteStop);
                            return;
                        }
                    }
                }
            }
        }

        bool SetCurStopNull()
        {
            if (CurMoveStop != null)
            {
                CurMoveStop.SetHighLight(false);
                CurMoveStop = null;
                return true;
            }
            return false;
        }

        public Panel GetPanel()
        {
            return _CommonControl;
        }

        public bool PanelContainCoord(int MouseX, int MouseY)
        {
            return MouseX >= _CommonControl.GetScreenX() && MouseX <= _CommonControl.GetScreenX() + _CommonControl.GetWidth() &&
                   MouseY >= _CommonControl.GetScreenY() && MouseY <= _CommonControl.GetScreenY() + _CommonControl.GetHeight();
        }

        public void Draw(CurveGraphicsHelper CurveGraphicsHelper, UIManager UIManager)
        {
            Vector2f Center = new Vector2f(CurveGraphicsHelper.X, CurveGraphicsHelper.Y);
            CurveGraphicsHelper.DrawRectangle(UIManager, Color.EDITOR_UI_HILIGHT_COLOR_GREEN, 5, Center, 5);

            //Draw GradientColorArea
            // The start and end location in slate units of the area to draw
            int Start = _GradientColorUI.GetScreenX();
            int Finish = Start + _GradientColorUI.GetWidth();

            List<MoveStop> Stops = new List<MoveStop>();
            bool bHasAnyAlphaKeys = HasAnyAlphaStops();
            bool bHasTransparency = false;

            // Sample the curve every 2 units.  THe curve could be non-linear so sampling at each stop would display an incorrect gradient
            for (int CurrentStep = Start; CurrentStep < Finish; CurrentStep += 2)
            {
                // Figure out the time from the current screen unit
                float Time = LinearColorCurveEditorUI.GetInstance().GetAxis().GetAxisValue(CurrentStep, CurveGraphicsHelper);
                // Sample the curve
                Float4 Color = LinearColorCurveEditorUI.GetInstance().GetUnadjustedLinearColorValue(Time);
                Color LinearColor = GetLinearColorValue(Color);
                Color SRGBColor = HDRColorSelectUI.GetSRGBPreviewColor(LinearColor);
                if (!bHasAnyAlphaKeys)
                {
                    // Only show alpha if there is at least one key.  For some curves, alpha may not be important
                    SRGBColor.A = 1.0f;
                    bHasTransparency = false;
                }
                else
                {
                    bHasTransparency |= (SRGBColor.A < 1.0f);
                }

                Vector2i ValueXY = new Vector2i(CurrentStep, _GradientColorUI.GetScreenY() + _GradientColorUI.GetWidth());
                float ScaleX = (float)CurveGraphicsHelper.ScreenToWorld(new Vector2f(ValueXY.X, ValueXY.Y)).X;
                Stops.Add(new MoveStop(ValueXY, SRGBColor, ScaleX));
            }

            int TotalSize = Stops.Count;
            UInt32[] stopPoseX = new UInt32[TotalSize];
            UInt32[] stopPoseY = new UInt32[TotalSize];
            UInt32[] Colors = new UInt32[TotalSize];

            int index = 0;
            for (int i = 0; i < TotalSize; ++i)
            {
                stopPoseX[i] = (UInt32)Stops[i]._ValueX;
                stopPoseY[i] = (UInt32)Stops[i]._ValueY;
                Colors[i] = Stops[i]._Color.ToDword();

                if (Stops[i]._ScaleX >= 0 && Stops[i]._ScaleX <= 1)
                {
                    _stopPoseX[index] = stopPoseX[i];
                    _Colors[index] = Colors[i];
                    ++index;
                }
            }

            //assign value
            _StopSize = index;
            //_Colors = Colors;
            //_stopPoseX = stopPoseX;

            int[] posSize = new int[4];
            posSize[0] = _GradientColorUI.GetScreenX();
            posSize[1] = _GradientColorUI.GetScreenY();
            posSize[2] = _GradientColorUI.GetScreenX() + _GradientColorUI.GetWidth();
            posSize[3] = _GradientColorUI.GetScreenY() + _GradientColorUI.GetHeight();

            //Draw Gradient Color
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.GetUIRenderInterface().DrawGradient(
                Marshal.UnsafeAddrOfPinnedArrayElement(posSize, 0),
                TotalSize,
                Marshal.UnsafeAddrOfPinnedArrayElement(stopPoseX, 0),
                Marshal.UnsafeAddrOfPinnedArrayElement(stopPoseY, 0),
                Marshal.UnsafeAddrOfPinnedArrayElement(Colors, 0));
        }
        public RectangleF GetBound(StopType StopType)
        {
            Panel Panel;
            if (StopType == StopType.ColorStop)
            {
                Panel = _ColorStopUI;
            }
            else
            {
                Panel = _AlphaStopUI;
            }
            return new RectangleF(Panel.GetScreenX(), Panel.GetScreenY(), Panel.GetWidth(), Panel.GetHeight());
        }

        public bool StopUIHitTest(int MouseX, int MouseY, StopType StopType)
        {
            return GetBound(StopType).Contains(MouseX, MouseY);
        }

        public bool HasAnyAlphaStops()
        {
            return _AlphaStops.Count > 0;
        }

        public void AddGradientStop(StopType StopType, MoveStop NewAlphaStop)
        {
            if (StopType == StopType.ColorStop)
            {
                _ColorStops.Add(NewAlphaStop);
            }
            else
            {
                _AlphaStops.Add(NewAlphaStop);
            }
        }

        public void OpenColorSelectUI(UIManager UIManager, MoveStop MoveStop)
        {
            Color Color = MoveStop._Color;
            HDRColorSelectUI HDRColorSelectUI = new HDRColorSelectUI();
            HDRColorSelectUI.Initialize(UIManager, "Color", Color);
            HDRColorSelectUI.ColorSelectedEvent += (HDRColorSelectUI Sender1) =>
            {
                List<Point> Points = MoveStop._ContainPoints;
                if (Points.Count != 0)
                {
                    Color NewColor = Sender1.GetNewColor();
                    Points[0].ValueY = (decimal)NewColor.R;
                    Points[0].OwnerCurve.PostModifiedCurve();
                    Points[1].ValueY = (decimal)NewColor.G;
                    Points[1].OwnerCurve.PostModifiedCurve();
                    Points[2].ValueY = (decimal)NewColor.B;
                    Points[2].OwnerCurve.PostModifiedCurve();
                }
            };
            HDRColorSelectUI.ColorSelectingEvent += (Sender1) =>
            {

            };
            DialogUIManager.GetInstance().ShowDialogUI(HDRColorSelectUI);
        }

        public void OpenAlphaSelectUI(UIManager UIManager, MoveStop MoveStop)
        {
            Menu Menu = new Menu(UIManager);
            Menu.Initialize();

            MenuItem MenuItem_Opacity = new MenuItem();
            Panel Container = new Panel();
            {
                int FontSize = 16;
                Label Label = new Label();
                Label.SetText("Opacity");
                int LabelWidth = UIManager.MeasureString_Fast(FontSize, Label.GetText());
                Label.SetFontSize(FontSize);
                Label.SetPosition(34, 4, LabelWidth, FontSize);
                Container.AddChild(Label);

                EditWithProgress Edit = new EditWithProgress(Container);
                Edit.SetPosition(34 + LabelWidth + 10, 4, 125, FontSize);
                Edit.SetRange(0m, 1m);
                Edit.SetStep(0.01m);
                Edit.SetText(MoveStop._Alpha.ToString());
                Edit.GetEditValue().SetFontSize(FontSize);
                Edit.TextChangedEvent += (Sender) =>
                {
                    MoveStop._Alpha = Edit.GetEditBoxValue();
                    // modify curve value
                    MoveStop._ContainPoints[0].ValueY = (decimal)MoveStop._Alpha;
                };

                Container.SetSize(LabelWidth + 200, 24);
                Container.LeftMouseDownEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
                {
                    bContinue = false;
                };
            }
            MenuItem_Opacity.SetControl(Container);
            Menu.AddMenuItem(MenuItem_Opacity);

            Button Button = new Button();
            Button.SetPosition(MoveStop._ValueX + 20, MoveStop._ValueY, 2, 2);

            UIManager.GetContextMenu().ShowMenu(Menu, Button);
        }

        public Color GetLinearColorValue(Float4 OriginalColor)
        {
            // Only clamp value if the color is RGB < 255
            bool bShouldClampValue = (OriginalColor.x <= 1 && OriginalColor.y <= 1 && OriginalColor.z <= 1);

            // Convert to HSV
            float H, S, V;
            HDRColorSelectUI.RGBtoHSV(OriginalColor.x, OriginalColor.y, OriginalColor.z, out H, out S, out V);
            Color HSVColor = new Color(H, S, V, 1f);
            float PixelHue = HSVColor.R;
            float PixelSaturation = HSVColor.G;
            float PixelValue = HSVColor.B;

            // Apply brightness adjustment
            PixelValue *= AdjustColor.Brightness;

            // Apply brightness power adjustment
            if (!MathUtils.IsNearlyEqual(AdjustColor.BrightnessCurve, 1.0f, (float)CE_KINDA_SMALL_NUMBER) && AdjustColor.BrightnessCurve != 0.0f)
            {
                // Raise HSV.V to the specified power
                PixelValue = (float)Math.Pow(PixelValue, AdjustColor.BrightnessCurve);
            }

            // Apply "vibrancy" adjustment
            if (!MathUtils.IsNearlyZero(AdjustColor.Vibrance, (float)CE_KINDA_SMALL_NUMBER))
            {
                float SatRaisePow = 5.0f;
                float InvSatRaised = (float)Math.Pow(1.0f - PixelSaturation, SatRaisePow);

                float ClampedVibrance = Math.Clamp(AdjustColor.Vibrance, 0.0f, 1.0f);
                float HalfVibrance = ClampedVibrance * 0.5f;

                float SatProduct = HalfVibrance * InvSatRaised;

                PixelSaturation += SatProduct;
            }

            // Apply saturation adjustment
            PixelSaturation *= AdjustColor.Saturation;

            // Apply hue adjustment
            PixelHue += AdjustColor.Hue;

            // Clamp HSV values
            {
                PixelHue = MathUtils.Fmod(PixelHue, 360.0f);
                if (PixelHue < 0.0f)
                {
                    // Keep the hue value positive as HSVToLinearRGB prefers that
                    PixelHue += 360.0f;
                }
                PixelSaturation = Math.Clamp(PixelSaturation, 0.0f, 1.0f);

                if (bShouldClampValue)
                {
                    PixelValue = Math.Clamp(PixelValue, 0.0f, 1.0f);
                }
            }

            // Convert back to a linear color
            float R, G, B;
            HDRColorSelectUI.HSVtoRGB(out R, out G, out B, PixelHue, PixelSaturation, PixelValue);
            Color LinearColor = new Color(R, G, B, 1f);

            // Remap the alpha channel
            LinearColor.A = MathUtils.Lerp(AdjustColor.MinAlpha, AdjustColor.MaxAlpha, OriginalColor.w);

            return LinearColor;
        }

        public AdjustColor GetAdjustColor()
        {
            return AdjustColor;
        }

        public void GenerateCurveGradientTex(UIManager UIManager)
        {
            GradientTexturePath(UIManager);
        }

        public void GradientTexturePath(UIManager UIManager)
        {
            PathInputUIFilterItem PathInputUIFilterItem = new PathInputUIFilterItem();
            PathInputUIFilterItem.Name = "Texture Files";
            PathInputUIFilterItem.Extensions.Add("nda");

            string SelectedFilePath = "";
            bool bContentsOnly = true;

            PathInputUIEx PathInputUI = new PathInputUIEx();
            string DefaultDrivePath = EditorUtilities.AddEditorDrives(PathInputUI, bContentsOnly);
            PathInputUI.Initialize(UIManager, "Save Gradient Curve As", PathInputUIType.SaveFile, PathInputUIFilterItem, DefaultDrivePath);
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                SelectedFilePath = PathInputed;
            };
            PathInputUI.DialogCloseEvent += (DialogUI DialogSender) =>
            {
                OperationQueue.GetInstance().AddOperation(() =>
                {
                    if (SelectedFilePath != "")
                    {
                        StringBuilder TipString = new StringBuilder();
                        var thread = new System.Threading.Thread(
                            () =>
                            {
                                bool flag = false;
                                TextureImportSetting setting = new TextureImportSetting();
                                if (_StopSize != 0)
                                {
                                    flag = AssetImporterManager.Instance().GenerateCurveGradientTex(SelectedFilePath, setting, Marshal.UnsafeAddrOfPinnedArrayElement(_Colors, 0), _StopSize);
                                }
                                else
                                {
                                    flag = false;
                                }

                                if (flag)
                                {
                                    TipString.Append("Exporting is successful!");
                                }
                                else
                                {
                                    TipString.Append("Nda is not valid.generating is fail!");
                                }
                                OperationQueue.GetInstance().AddOperation(() =>
                                {
                                    CommonDialogUI.ShowSimpleOKDialog(UIManager, "Gradient Curve Info", TipString.ToString());
                                });
                            }
                        );
                        thread.Start();
                    }
                });
            };
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }

        public void ClearGradientStops()
        {
            _ColorStops.Clear();
            _AlphaStops.Clear();
        }

        public void AutoAddGradientStop(List<CurveManager> Curves)
        {
            if (Curves.Count == 0) return;
            List<List<Point>> ColorStopPoints = new List<List<Point>>();
            List<Point> AlphaStopPoints = new List<Point>();

            if (Curves.Count == 4)
            {
                for (int i = 0; i < Curves[0].Points.Count; ++i)
                {
                    List<Point> ColorStopPoint = new List<Point>() { Curves[0].Points[i], Curves[1].Points[i], Curves[2].Points[i] };
                    ColorStopPoints.Add(ColorStopPoint);
                }

                for (int i = 0; i < Curves[3].Points.Count; ++i)
                {
                    AlphaStopPoints.Add(Curves[3].Points[i]);
                }
            }
            else
            {
                return;
            }

            //ColorStop
            foreach (var ColorStop in ColorStopPoints)
            {
                float ScaleX = ColorStop[0].ValueX;
                int LocationX = (int)CurveGraphicsHelper.WorldToScreen(new Vector2f(ScaleX, 0.0f)).X - 10;
                int LocationY = _ColorStopUI.GetScreenY() + 10;
                MoveStop NewAlphaStop = new MoveStop();
                NewAlphaStop.Initialize(StopType.ColorStop, LocationX, LocationY, ScaleX);
                NewAlphaStop.SetPoints(ColorStop);
                AddGradientStop(StopType.ColorStop, NewAlphaStop);
            }

            foreach (var AlphaStop in AlphaStopPoints)
            {
                float ScaleX = AlphaStop.ValueX;
                int LocationX = (int)CurveGraphicsHelper.WorldToScreen(new Vector2f(ScaleX, 0.0f)).X - 10;
                int LocationY = _AlphaStopUI.GetScreenY();
                MoveStop NewAlphaStop = new MoveStop();
                NewAlphaStop.Initialize(StopType.AlphaStop, LocationX, LocationY, ScaleX);
                NewAlphaStop.SetPoints(new List<Point>() { AlphaStop });
                AddGradientStop(StopType.AlphaStop, NewAlphaStop);
            }
        }

        public void SetLoadCurve(List<CurveManager> Curves)
        {
            _Curves = Curves;
        }
    }
}
