using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    sealed class AnimatorUI : DockingUI
    {
        bool _ParamLayoutDirty = true;
        bool _StateLayoutDirty = true;

        VContainer _VContainerMain;
        HSplitter _HSplitterMain;

        // left panel
        ScrollView _ScrollViewForParameters;
        Panel _ScrollPanelForParameters;
        VContainer _LeftScrollForParameters;
        VContainer _LeftParamsForParameters;
        Button _ButtonSave;

        // left Panel Operation Bar
        OperationBarUI _OperationBarForParameters;
        Button _OperationBtnCreateFroParameters;
        Label _ParamsCountLabelForParameters;

        // left Panel Parameters
        List<Inspector_Property> _LeftParamsProperty = new List<Inspector_Property>();

        // right panel
        VContainer _VAnimatorStatesContainer;
        ScrollView _ScrollViewForAnimatorStates;
        Panel _ScrollPanelForAnimatorStates;
        Panel _PanelNavForAnimatorStates;

        readonly int _ScrollPanelMinWidth = 1500;
        readonly int _ScrollPanelMinHeight = 1500;

        DateTime _ScrLeftBtnDwTimeStamp = DateTime.MinValue;
        DateTime _ScrLeftBtnUpTimeStamp = DateTime.MinValue;

        // Animator Data
        AnimatorContext _AnimContext = new AnimatorContext();
        AnimatorUiLayersController _AnimLayers = null;

        internal IAnimatorUiLayerDesc CurrentActiveLayerDesc
        {
            get { return _AnimLayers.GetCurrentLayerDesc(); }
        }

        internal IAnimatorUiLayerCtrl CurrentActiveLayerCtrl
        {
            get { return _AnimLayers.GetCurrentLayerCtrl(); }
        }

        private static AnimatorUI _Instance = null;

        private AnimatorUI()
        {
            _AnimContext.AddParam<int>(100, "wxt");
            _AnimContext.AddParam<bool>(false, "bool test");
            _AnimContext.AddParam<float>(5.0f, "float test");

            _AnimContext.onAnimParamChanged += OnAnimParamChanged;
            _AnimContext.onAnimStateChanged += OnAnimStateChanged;
        }

        public static AnimatorUI GetInstance()
        {
            if (_Instance == null)
                _Instance = new AnimatorUI();

            return _Instance;
        }

        public bool Initialize()
        {
            _VContainerMain = new VContainer();
            _VContainerMain.Initialize();
            _VContainerMain.SetSize(500, 300);

            _HSplitterMain = new HSplitter();
            _HSplitterMain.Initialize();
            _VContainerMain.AddSizableChild(_HSplitterMain, 1.0f);

            // left panel --- parameters
            _ScrollViewForParameters = new ScrollView();
            _ScrollViewForParameters.Initialize();
            _ScrollViewForParameters.GetHScroll().SetEnable(false);
            _ScrollViewForParameters.SetSize(500, 100);
            _ScrollPanelForParameters = _ScrollViewForParameters.GetScrollPanel();

            _HSplitterMain.AddChild(_ScrollPanelForParameters);

            // left panel --- parameters OperationBar
            _LeftScrollForParameters = new VContainer();
            _LeftScrollForParameters.Initialize();
            _LeftScrollForParameters.SetSize(300, 300);
            _ScrollPanelForParameters.AddChild(_LeftScrollForParameters);

            _OperationBarForParameters = new OperationBarUI();
            _OperationBarForParameters.Initialize();
            _OperationBtnCreateFroParameters = OperationBarUI.CreateTextButton("CreateParam");
            _OperationBtnCreateFroParameters.ClickedEvent += OnCreateParameterButtonClicked;
            _OperationBarForParameters.AddLeft(_OperationBtnCreateFroParameters);

            _LeftScrollForParameters.AddFixedChild(_OperationBarForParameters.GetPanelBar());

            // left panel --- panel for all params
            _LeftParamsForParameters = new VContainer();
            _LeftParamsForParameters.Initialize();
            _LeftParamsForParameters.SetSize(500, 300);
            _LeftScrollForParameters.AddSizableChild(_LeftParamsForParameters, 1.0f);

            _ParamsCountLabelForParameters = new Label();
            _ParamsCountLabelForParameters.Initialize();
            _ParamsCountLabelForParameters.SetFontSize(Control.UI_DEFAULT_FONT_SIZE);
            _ParamsCountLabelForParameters.SetTextAlign(TextAlign.CenterCenter);
            _ParamsCountLabelForParameters.SetTextColor(Color.FromRGBA(255, 255, 255, 255));
            _ParamsCountLabelForParameters.SetText("Empty Param");

            _ButtonSave = new Button();
            _ButtonSave.Initialize();
            _ButtonSave.SetText("Save As");
            _ButtonSave.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonSave.SetFontSize(18);
            _ButtonSave.SetTextOffsetY(2);
            _ButtonSave.ClickedEvent += OnButtonSaveAnimatorAs;

            // right panel for fsm states
            _VAnimatorStatesContainer = new VContainer();
            _VAnimatorStatesContainer.Initialize();
            _VAnimatorStatesContainer.SetSize(3000, 300);
            _HSplitterMain.AddChild(_VAnimatorStatesContainer);

            _PanelNavForAnimatorStates = new Panel();
            _PanelNavForAnimatorStates.SetSize(500, BAR_HEIGHT);
            _PanelNavForAnimatorStates.SetBackgroundColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            _VAnimatorStatesContainer.AddFixedChild(_PanelNavForAnimatorStates);

            _ScrollViewForAnimatorStates = new ScrollView();
            _ScrollViewForAnimatorStates.Initialize();
            _ScrollViewForAnimatorStates.GetHScroll().SetEnable(true);
            _ScrollViewForAnimatorStates.GetVScroll().SetEnable(true);
            _ScrollViewForAnimatorStates.GetScrollPanel().SetSize(_ScrollPanelMinWidth, _ScrollPanelMinHeight);
            _ScrollViewForAnimatorStates.GetScrollPanel().SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            _ScrollViewForAnimatorStates.RightMouseUpEvent += OnAnimStatesContentRightMouseUp;

            _ScrollPanelForAnimatorStates = _ScrollViewForAnimatorStates.GetScrollPanel();
            _ScrollPanelForAnimatorStates.PaintEvent += OnAnimStatePanelPaint;
            _ScrollPanelForAnimatorStates.MouseMoveEvent += OnAnimStatesContentMouseMove;
            _ScrollPanelForAnimatorStates.LeftMouseDoubleClickedEvent += OnAnimStatesContentDoubleClick;
            _ScrollPanelForAnimatorStates.LeftMouseDownEvent += OnAnimStatesContentLeftButtonDown;
            _ScrollPanelForAnimatorStates.LeftMouseUpEvent += OnAnimStatesContentLeftButtonUp;

            _VAnimatorStatesContainer.AddSizableChild(_ScrollViewForAnimatorStates, 1.0f);

            // create basic layer
            _AnimLayers = new AnimatorUiLayersController(_ScrollPanelForAnimatorStates);
            CurrentActiveLayerCtrl.Initialize(_AnimContext);

            base.Initialize("Animator", _VContainerMain);
            return true;
        }

        internal void Refresh(string Path)
        {
            AnimatorSerializer serializer = new AnimatorSerializer();

            _AnimContext = serializer.Deserialize(Path);
            if (_AnimContext == null)
                return;

            _AnimContext.onAnimParamChanged += OnAnimParamChanged;
            _AnimContext.onAnimStateChanged += OnAnimStateChanged;

            _AnimContext.OnDeserializeFinished();

            _ParamLayoutDirty = true;
            _StateLayoutDirty = true;

            _AnimLayers = new AnimatorUiLayersController(_ScrollPanelForAnimatorStates);
            CurrentActiveLayerCtrl.Initialize(_AnimContext);
        }

        public void Update()
        {
            if (_ParamLayoutDirty || _StateLayoutDirty)
                UpdateLayout();

            _ParamLayoutDirty = false;
            _StateLayoutDirty = false;

            Update_CheckScrollSizeNeedFit();
        }

        private void Update_CheckScrollSizeNeedFit()
        {
            int leftMainPanelWidth = _HSplitterMain.GetChild(0).GetWidth();
            int leftMainPanelHeight = _HSplitterMain.GetChild(0).GetHeight();

            int rightMainPanelWidth = _HSplitterMain.GetChild(1).GetWidth();
            int rightMainPanelHeight = _HSplitterMain.GetChild(1).GetHeight();

            int scrollContentWidth = _ScrollPanelForAnimatorStates.GetWidth();
            int scrollContentHeight = _ScrollPanelForAnimatorStates.GetHeight();

            if (scrollContentWidth < rightMainPanelWidth ||
                scrollContentHeight < rightMainPanelHeight)
                _ScrollViewForAnimatorStates.SetSize(
                    Math.Max(rightMainPanelWidth, scrollContentWidth),
                    Math.Max(rightMainPanelHeight, scrollContentHeight));
        }

        public override void OnPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            UpdateLayout();
        }

        #region Update Ui 

        private void UpdateLayout()
        {
            UpdateLayout_Parameters();
            UpdateLayout_ExecuteStates();
        }

        private void UpdateLayout_Parameters()
        {
            // Ui Adapt to parameter count
            if (_ParamLayoutDirty)
            {
                _LeftParamsForParameters.ClearChildren();
                _LeftParamsProperty.Clear();

                for (int i = 0; i < _AnimContext.ParamCount(); ++i)
                {
                    Inspector_Property property = CreateParamterProperty(_LeftParamsForParameters, _AnimContext.GetParam(i));
                    _LeftParamsProperty.Add(property);
                }

                _LeftParamsForParameters.AddChild(_ButtonSave);
            }

            if (_AnimContext.ParamCount() == 0)
            {
                _LeftScrollForParameters.ClearChildren();
                _LeftScrollForParameters.AddFixedChild(_OperationBarForParameters.GetPanelBar());
                _LeftScrollForParameters.AddFixedChild(_ParamsCountLabelForParameters);
            }

            // Ui Layout refresh
            int ScrollPanelWidth = _LeftScrollForParameters.GetWidth();
            int Y = _OperationBtnCreateFroParameters.GetHeight() + 8;

            foreach (Inspector_Property Inspector in _LeftParamsProperty)
            {
                Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
            }

            Y += 2;
            int BtnSaveSpanX = 5;
            _ButtonSave.SetPosition(BtnSaveSpanX, Y + 5, Math.Max(200, _ScrollPanelForParameters.GetWidth() - BtnSaveSpanX * 2), 30);

            // Hack Code Here, Layer Navigation Bar is Static now
            if (_PanelNavForAnimatorStates.GetChildCount() == 0)
            {
                string NavigateItem = CurrentActiveLayerDesc.Name();

                int BarHeight = _PanelNavForAnimatorStates.GetHeight();
                int BorderHeight = 4;
                int FontSize = BAR_HEIGHT - BorderHeight * 2;
                Font Font = GetUIManager().GetDefaultFont(FontSize);

                int X = 0;

                int NavigateItemWidth = Font.MeasureString_Fast(NavigateItem) + 6;
                Button ButtonNavigateItem = new Button();
                ButtonNavigateItem.SetText(NavigateItem);
                ButtonNavigateItem.SetFontSize(FontSize);
                ButtonNavigateItem.SetPosition(X, BorderHeight, NavigateItemWidth, FontSize);
                _PanelNavForAnimatorStates.AddChild(ButtonNavigateItem);
            }
        }

        private void UpdateLayout_ExecuteStates()
        {
            _ScrollViewForAnimatorStates.UpdateScrollBar();

            if (_StateLayoutDirty == false)
                return;

            _ScrollPanelForAnimatorStates.ClearChildren();

            CurrentActiveLayerCtrl.OnUpdateLayout(_AnimContext);
        }

        internal Inspector_Property CreateParamterProperty(Control Container, IAnimatorParam param)
        {
            Inspector_Property inspector = null;

            if (param.ToType() == typeof(int))
                inspector = new Inspector_Anim_Int(_AnimContext);
            else if (param.ToType() == typeof(bool))
                inspector = new Inspector_Anim_Bool(_AnimContext);
            else if (param.ToType() == typeof(float))
                inspector = new Inspector_Anim_Float(_AnimContext);

            ObjectProperty ObjectProperty = new ObjectProperty();
            ObjectProperty.Object = param.Value;
            ObjectProperty.Name = param.Name;
            ObjectProperty.Type = param.ToType();
            ObjectProperty.GetPropertyValueFunction = GetParamPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetParamPropertyValueFunction;

            inspector.InspectProperty(ObjectProperty);

            return inspector;
        }

        public object GetParamPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            IAnimatorParam param = _AnimContext.FindPram(PropertyName);
            return param != null ? param.Value : null;
        }

        public void SetParamPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            IAnimatorParam param = _AnimContext.FindPram(PropertyName);

            if (param != null && param.ToType() == PropertyValue.GetType())
                param.Value = PropertyValue;
        }

        #endregion

        private void OnButtonSaveAnimatorAs(Button Sender)
        {
            PathInputUIFilterItem PathInputUIFilterItem = new PathInputUIFilterItem();
            PathInputUIFilterItem.Name = "Scene Files";
            PathInputUIFilterItem.Extensions.Add("act");

            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            string ProjectName = MainUI.GetInstance().GetProjectName();

            string SelectedFilePath = "";

            PathInputUIEx PathInputUI = new PathInputUIEx();
            PathInputUI.AddDrive(ProjectName, ProjectDirectory);
            PathInputUI.Initialize(GetUIManager(), "Save Animator As", PathInputUIType.SaveFile, PathInputUIFilterItem, ProjectDirectory);
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                SelectedFilePath = PathInputed;
            };
            PathInputUI.DialogCloseEvent += (DialogUI DialogSender) =>
            {
                if (SelectedFilePath != "")
                {
                    AnimatorSerializer serializer = new AnimatorSerializer();
                    serializer.Serialize(SelectedFilePath, _AnimContext);
                }
            };
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }

        #region Create Parameters Callback

        public void OnCreateParameterButtonClicked(Button Sender)
        {
            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();
            BulidCreateParamsMenuItems(MenuContextMenu);

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);
        }

        public void BulidCreateParamsMenuItems(Menu Menu)
        {
            MenuItem MenuItem_Create_Bool = new MenuItem();
            MenuItem_Create_Bool.SetText("Bool");
            MenuItem_Create_Bool.ClickedEvent += OnMenuItemCreateBoolClicked;

            MenuItem MenuItem_Create_Integer = new MenuItem();
            MenuItem_Create_Integer.SetText("Integer");
            MenuItem_Create_Integer.ClickedEvent += OnMenuItemCreateIntClicked;

            MenuItem MenuItem_Create_Float = new MenuItem();
            MenuItem_Create_Float.SetText("Float");
            MenuItem_Create_Float.ClickedEvent += OnMenuItemCreateFloatClicked;

            Menu.AddMenuItem(MenuItem_Create_Bool);
            Menu.AddMenuItem(MenuItem_Create_Integer);
            Menu.AddMenuItem(MenuItem_Create_Float);
        }

        void OnMenuItemCreateBoolClicked(MenuItem MenuItem)
        {
            _AnimContext.AddParam<bool>(false, "Need a Name");
        }

        void OnMenuItemCreateIntClicked(MenuItem MenuItem)
        {
            _AnimContext.AddParam<int>(0, "Need a Name");
        }

        void OnMenuItemCreateFloatClicked(MenuItem MenuItem)
        {
            _AnimContext.AddParam<float>(0.0f, "Need a Name");
        }

        void OnAnimParamChanged(IAnimatorParam param, AnimatorContext.ParamOperation op)
        {
            _ParamLayoutDirty = true;

            // Close current inspector for refresh later
            InspectorUI InspectorUI = InspectorUI.GetInstance();
            InspectorUI.SetObjectInspected(null);
            InspectorUI.InspectObject();
        }

        #endregion

        #region Animator States Scroll View Callback

        void OnAnimStatesContentRightMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            CurrentActiveLayerCtrl.OnPanelRightMouseUp(_AnimContext, Sender, MouseX, MouseY, ref bContinue);
        }

        void OnAnimStatesContentMouseMove(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            CurrentActiveLayerCtrl.OnPanelMouseMove(_AnimContext, Sender, MouseX, MouseY, ref bContinue);
        }

        void OnAnimStatesContentDoubleClick(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            CurrentActiveLayerCtrl.OnPanelDoubleClick(_AnimContext, Sender, MouseX, MouseY, ref bContinue);
        }

        void OnAnimStatesContentLeftButtonDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            _ScrLeftBtnDwTimeStamp = DateTime.Now;
            _ScrLeftBtnUpTimeStamp = DateTime.MinValue;
        }

        void OnAnimStatesContentLeftButtonUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            _ScrLeftBtnUpTimeStamp = DateTime.Now;

            TimeSpan delta = _ScrLeftBtnUpTimeStamp - _ScrLeftBtnDwTimeStamp;
            if (delta.TotalMilliseconds < 250 && delta.TotalMilliseconds > 100)
                CurrentActiveLayerCtrl.OnPanelSingleClick(_AnimContext, Sender, MouseX, MouseY, ref bContinue);
        }

        void OnAnimStateChanged(IAnimatorState state, AnimatorContext.StateOperation op, params object[] args)
        {
            _StateLayoutDirty = true;

            CurrentActiveLayerCtrl.OnPanelStatePropertyChanged(_AnimContext, state, op, args);
        }

        void OnAnimStatePanelPaint(Control Sender)
        {
            Sender.PaintThis();

            DrawScrollGrid();
            CurrentActiveLayerCtrl.OnLayerPaint(_AnimContext, Sender);

            Sender.PaintChildren();
        }

        #endregion

        public Vector2i ConvertStatePanelToScreenCoordinate(int LocalX, int LocalY)
        {
            int stateScrollX = _ScrollViewForAnimatorStates.GetScreenX();
            int stateScrollY = _ScrollViewForAnimatorStates.GetScreenY();

            int statePanelOffsetX = _ScrollPanelForAnimatorStates.GetX();
            int statePanelOffsetY = _ScrollPanelForAnimatorStates.GetY();

            int PanelX = LocalX + stateScrollX + statePanelOffsetX;
            int PanelY = LocalY + stateScrollY + statePanelOffsetY;
            return new Vector2i(PanelX, PanelY);
        }

        static public void ConvertToLocalCoordinate(Control Control, int WorldX, int WorldY, ref int LocalX, ref int LocalY)
        {
            LocalX = WorldX - Control.GetScreenX();
            LocalY = WorldY - Control.GetScreenY();
        }

        public void DrawLink(Vector2i Point1, Vector2i Point2, float width, float blendFactor, Color color)
        {
            UIManager UIManager = GetUIManager();
            Clicross.Float4 colorf = new Clicross.Float4();
            colorf.x = color.R;
            colorf.y = color.G;
            colorf.z = color.B;
            colorf.w = color.A;
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.GetUIRenderInterface().DrawLink(
                ConvertStatePanelToScreenCoordinate(Point1.X, Point1.Y).X, ConvertStatePanelToScreenCoordinate(Point1.X, Point1.Y).Y,
                ConvertStatePanelToScreenCoordinate(Point2.X, Point2.Y).X, ConvertStatePanelToScreenCoordinate(Point2.X, Point2.Y).Y,
                width, blendFactor, colorf);
        }

        public void DrawLine(Vector2i Point1, Vector2i Point2, float width, float blendFactor, Color color)
        {
            UIManager UIManager = GetUIManager();
            Clicross.Float4 colorf = new Clicross.Float4();
            colorf.x = color.R;
            colorf.y = color.G;
            colorf.z = color.B;
            colorf.w = color.A;
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.GetUIRenderInterface().DrawLine(
                ConvertStatePanelToScreenCoordinate(Point1.X, Point1.Y).X, ConvertStatePanelToScreenCoordinate(Point1.X, Point1.Y).Y,
                ConvertStatePanelToScreenCoordinate(Point2.X, Point2.Y).X, ConvertStatePanelToScreenCoordinate(Point2.X, Point2.Y).Y,
                width, blendFactor, colorf);
        }

        private void DrawScrollGrid()
        {
            UIManager UIManager = GetUIManager();
            int statePanelOffsetX = _ScrollPanelForAnimatorStates.GetX();
            int statePanelOffsetY = _ScrollPanelForAnimatorStates.GetY();

            int stateScrollWidth = _ScrollPanelForAnimatorStates.GetWidth();
            int stateScrollHeight = _ScrollPanelForAnimatorStates.GetHeight();
            Color thickColor = Color.FromRGBA(255, 255, 255, 255);
            Color thinColor = Color.FromRGBA(204, 153, 153, 128);
            Float4 ThickColorf = new Float4();
            ThickColorf.x = thickColor.R;
            ThickColorf.y = thickColor.G;
            ThickColorf.z = thickColor.B;
            ThickColorf.w = thickColor.A;
            Float4 ThinColorf = new Float4();
            ThinColorf.x = thinColor.R;
            ThinColorf.y = thinColor.G;
            ThinColorf.z = thinColor.B;
            ThinColorf.w = thinColor.A;
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            //EditorUICanvas.GetUICanvasInterface().DrawGrid(2.0f, 
            //    50, 
            //    statePanelOffsetX * 1.0f, statePanelOffsetY * 1.0f,
            //    stateScrollWidth * 1.0f, stateScrollHeight * 1.0f, ThickColorf,ThinColorf);
        }
    }
}
