using CEngine;
using EditorUI;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    public class SubSeqSectionPropertyEditorUI : Control, IPropertyEditorUI
    {
        ScrollView _ScrollView;
        Panel _ScrollPanel;

        Menu _Menu;

        LevelSubSeqSection _Value;
        List<KeyFrame> _KeyFrames = new List<KeyFrame>();

        Inspector _Inspector;
        InspectorHandler _InspectorHandler;


        public void Initialize(List<KeyFrame> KeyFrames)
        {
            _ScrollView = new ScrollView();
            _ScrollView.Initialize();
            _ScrollView.GetHScroll().SetEnable(true);
            _ScrollView.SetPosition(10, 50, 430, 430);
            _ScrollView.SetBackgroundColor(Color.EDITOR_UI_TEST_COLOR_RED);

            _ScrollPanel = _ScrollView.GetScrollPanel();
            _ScrollPanel.SetBackgroundColor(Color.EDITOR_UI_HILIGHT_COLOR_GREEN);

            _KeyFrames = KeyFrames;

            _Value = ((SubSeqKeyFrame)KeyFrames[0]).Section;

            _InspectorHandler = new InspectorHandler();
            _InspectorHandler.UpdateLayout += UpdateLayout;
            _InspectorHandler.InspectObject += () => { _Inspector.InspectObject(_Value); };
            _InspectorHandler.ReadValue += () => { _Inspector.ReadValue(); };

            _Inspector = new Inspector_Struct_With_Property();
            _Inspector.InspectObject(_Value);
            _Inspector.SetContainer(_ScrollPanel);
            _Inspector.SetInspectorHandler(_InspectorHandler);
            _Inspector.SetPropertyModifiedFunction(OnValueModified);
            UpdateLayout();
        }

        public void UpdateLayout()
        {
            int ScrollPanelWidth = _ScrollView.GetWidth();
            int Y = 0;
            if (_Inspector != null)
            {
                _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                if (Y > _ScrollView.GetHeight())
                {
                    ScrollPanelWidth = _ScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                    Y = 0;
                    _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                }
            }
            int Height = Y;
            _ScrollView.GetScrollPanel().SetSize(ScrollPanelWidth, Height);
            _ScrollPanel.SetSize(ScrollPanelWidth, Height);
            _ScrollView.UpdateScrollBar();
        }

        public void SetMenu(Menu Menu)
        {
            _Menu = Menu;
        }

        public Menu GetMenu()
        {
            return _Menu;
        }

        public Panel GetScrollPanel()
        {
            return _ScrollPanel;
        }
        public void OnValueModified(object propertyOwner, PropertyInfo propertyInfo)
        {
            if (_KeyFrames.Count == 0) return;

            LevelSubSeqSection property = (LevelSubSeqSection)propertyOwner;

            foreach (var KeyFrame in _KeyFrames)
            {
                if (KeyFrame is SubSeqKeyFrame key)
                {
                    var track = key.OwnerTrack as SubSeqTrack;
                    key.SetKeyValue((decimal)property.SectionStart);

                    if (propertyInfo.Name == "SubSeqPath")
                    {
                        property.SubSeqPath = ResourceManager.Instance().ConvertGuidToPath(property.SubSeqPath);
                        property.SectionName = PathHelper.GetNameOfPath(property.SubSeqPath);
                        key.Section.SectionName = property.SectionName;
                    }
                    //propertyInfo.SetValue(key.Section, propertyInfo.GetValue(property));
                    track.ModifyValue(key, property);
                }
            }
            CinematicUI.GetInstance().SetModified();
        }
    }
}
