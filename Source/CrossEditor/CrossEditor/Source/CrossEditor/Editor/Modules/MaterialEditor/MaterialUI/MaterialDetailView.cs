using Clicross;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    public class MaterialDetailView : VContainer
    {
        ScrollView mScrollView = new ScrollView();
        Panel mScrollPanel;
        SearchUI mSearchUI;
        OperationBarUI mOperationBar;
        object mObjectInspected;
        object mObjectTag;
        Inspector mInspector;
        InspectorHandler mInspectorHandler = new InspectorHandler();

        Button mAdvancedSettings;
        Inspector mInspectorAdvanced;
        RenderStateInfo mRenderStateInfo = null;

        MaterialEditor mMaterialEditorContext;
        CEngine.ClassIDType mType;

        public MaterialDetailView(MaterialEditor context, CEngine.ClassIDType type)
        {
            mMaterialEditorContext = context;
            mType = type;

            mScrollView.Initialize();
            mScrollView.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            mScrollView.GetHScroll().SetEnable(false);

            mScrollPanel = mScrollView.GetScrollPanel();
            mScrollPanel.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            mScrollPanel.PaintEvent += (Sender) =>
            {
                SetChildInnerEnable(Sender);
                Sender.PaintThis();
                Sender.PaintChildren();
            };

            mInspectorHandler.InspectObject = DoInspectObject;
            mInspectorHandler.UpdateLayout = DoUpdateLayout;
            mInspectorHandler.ReadValue = DoReadValue;
        }

        public override void Initialize()
        {
            base.Initialize();

            mOperationBar = new OperationBarUI();
            mOperationBar.Initialize();

            mSearchUI = new SearchUI();
            mSearchUI.Initialize();
            mSearchUI.SearchEvent += OnSearchUISearch;
            mSearchUI.CancelEvent += OnSearchUICancel;
            Panel PanelBack = mSearchUI.GetPanelBack();
            PanelBack.SetBackgroundColor(OperationBarUI.BAR_COLOR);

            mAdvancedSettings = new Button();
            mAdvancedSettings.Initialize();
            //need new icon
            mAdvancedSettings.SetImage(UIManager.LoadUIImage("Editor/Icons/Edit/TranslateMode.png"));
            mAdvancedSettings.SetDownColor(Color.EDITOR_UI_MENU_BACK_COLOR);
            mOperationBar.AddLeft(mAdvancedSettings);
            mAdvancedSettings.SetPosition(0, 0, 18, 18);
            mAdvancedSettings.ClickedEvent += (Button Sender) =>
            {
                Menu advancedSettingMenu = new Menu(GetUIManager());
                advancedSettingMenu.Initialize();
                MenuItem advancedSettingsShow = new MenuItem();
                advancedSettingsShow.SetText("Show Advanced Settings");
                advancedSettingsShow.ClickedEvent += (MenuItem Sender) =>
                {
                    var materialDefines = mMaterialEditorContext.GetMaterialDefines();

                    if (materialDefines.EnableAdvancedMode == false)
                    {
                        materialDefines.EnableAdvancedMode = true;
                        //mMaterialEditorContext
                        Inspect(materialDefines.AdvancedRenderStates, "RenderState");
                        mInspector.SetPropertyReadOnly("RenderGroupBias", true);
                        mMaterialEditorContext.SetPropertyChangedFlagForEditor(true);
                    }
                };
                MenuItem advancedSettingsClose = new MenuItem();
                advancedSettingsClose.SetText("Close Advanced Settings");
                advancedSettingsClose.ClickedEvent += (MenuItem Sender) =>
                {
                    if (mMaterialEditorContext.GetMaterialDefines().EnableAdvancedMode == true)
                    {
                        mRenderStateInfo = null;
                        if (mObjectInspected is MaterialDefines && ((mObjectInspected as MaterialDefines).BlendMode == MaterialBlendMode.Translucent || (mObjectInspected as MaterialDefines).BlendMode == MaterialBlendMode.Additive || (mObjectInspected as MaterialDefines).BlendMode == MaterialBlendMode.Modulate || (mObjectInspected as MaterialDefines).BlendMode == MaterialBlendMode.AlphaComposite))
                        {
                            mInspector.SetPropertyReadOnly("RenderGroupBias", false);
                        }
                        mMaterialEditorContext.GetMaterialDefines().EnableAdvancedMode = false;
                        mMaterialEditorContext.ResetRenderState();
                    }
                };
                advancedSettingMenu.AddMenuItem(advancedSettingsShow);
                advancedSettingMenu.AddMenuItem(advancedSettingsClose);
                GetUIManager().GetContextMenu().ShowMenu(advancedSettingMenu, Sender.GetScreenX(), Sender.GetScreenY() + 20);
            };

            mOperationBar.AddLeft(PanelBack);

            AddFixedChild(mOperationBar.GetPanelBar());
            AddSizableChild(Control, 1);
        }

        public void InspectObject()
        {
            mInspectorHandler.InspectObject();
            mInspectorHandler.UpdateLayout();
        }

        private void SetChildInnerEnable(Control Control)
        {
            for (int i = 0; i < Control.GetChildCount(); ++i)
            {
                Control Child = Control.GetChild(i);
                if (UIManager.RectInRect(Child.GetScreenX(), Child.GetScreenY(), Child.GetWidth(), Child.GetHeight(),
                    mScrollView.GetScreenX(), mScrollView.GetScreenY(), mScrollView.GetWidth(), mScrollView.GetHeight()))
                {
                    Child.SetInnerEnable(true);

                    if (Child.GetChildCount() != 0)
                    {
                        SetChildInnerEnable(Child);
                    }
                }
                else
                {
                    Child.SetInnerEnable(false);
                }
            }
        }

        void OnExpressionPropertyChange(object PropertyOwner, PropertyInfo Property)
        {
            if (PropertyOwner is MaterialExpression)
            {
                mMaterialEditorContext.OnPropertyChange(PropertyOwner as MaterialExpression);
            }
            else if (PropertyOwner is MaterialPassState && Property.Name == "RenderGroup")
            {
                mInspector.ReadValue();
            }
            else
            {
                if (PropertyOwner is MaterialDefines)
                {
                    if (mRenderStateInfo == null && ((PropertyOwner as MaterialDefines).BlendMode == MaterialBlendMode.Translucent || (PropertyOwner as MaterialDefines).BlendMode == MaterialBlendMode.Additive || (PropertyOwner as MaterialDefines).BlendMode == MaterialBlendMode.Modulate || (PropertyOwner as MaterialDefines).BlendMode == MaterialBlendMode.AlphaComposite))
                        mInspector.SetPropertyReadOnly("RenderGroupBias", false);
                    else if (mRenderStateInfo == null && (PropertyOwner as MaterialDefines).BlendMode == MaterialBlendMode.Masked)
                    {
                        mInspector.SetPropertyReadOnly("RenderGroupBias", true);
                        mMaterialEditorContext.SetPassRenderGroup("gpass", 2500);
                    }
                    else
                    {
                        mInspector.SetPropertyReadOnly("RenderGroupBias", true);
                    }
                    mMaterialEditorContext.OnMaterialDefinesChange();
                }
                else if (PropertyOwner is MaterialFunctionDefines)
                {
                    mMaterialEditorContext.OnMaterialFunctionDefinesChange();
                }
            }
        }

        private void DoInspectObject()
        {
            mScrollPanel.ClearChildren();
            if (mObjectInspected != null || mRenderStateInfo != null)
            {
                if (mObjectInspected != null)
                {
                    if (mObjectInspected is MaterialExpression)
                    {
                        mInspector = new Inspector_MaterialExpression();
                        (mInspector as Inspector_MaterialExpression).SetMaterialEditorContext(mMaterialEditorContext);
                    }
                    else if (mObjectInspected is MaterialDefines || mObjectInspected is MaterialFunctionDefines)
                    {
                        mInspector = new Inspector_Struct_With_Property();
                    }
                    if (mInspector != null)
                    {
                        mInspector.SetContainer(mScrollPanel);
                        mInspector.SetInspectorHandler(mInspectorHandler);
                        mInspector.SetPropertyModifiedFunction(OnExpressionPropertyChange);
                        mInspector.InspectObject(mObjectInspected, mObjectTag);

                        var materialDefines = mObjectInspected as MaterialDefines;
                        if (materialDefines?.EnableAdvancedMode == true)
                        {
                            Inspect(materialDefines.AdvancedRenderStates, "RenderState");
                            mInspector.SetPropertyReadOnly("RenderGroupBias", true);
                        }
                        if (mObjectInspected is MaterialDefines && ((mObjectInspected as MaterialDefines).BlendMode == MaterialBlendMode.Opaque || (mObjectInspected as MaterialDefines).BlendMode == MaterialBlendMode.Masked))
                        {
                            mInspector.SetPropertyReadOnly("RenderGroupBias", true);
                        }
                        mInspector.UpdateCheckExpand();
                    }
                }
                if (mObjectInspected is RenderStateInfo && mRenderStateInfo != null)
                {
                    mInspectorAdvanced = new Inspector_RenderState();
                    (mInspectorAdvanced as Inspector_RenderState).SetMaterialEditorContext(mMaterialEditorContext);
                    mInspectorAdvanced.SetContainer(mScrollPanel);
                    mInspectorAdvanced.SetInspectorHandler(mInspectorHandler);
                    mInspectorAdvanced.InspectObject(mRenderStateInfo);
                    mInspectorAdvanced.SetPropertyModifiedFunction(OnExpressionPropertyChange);
                    mInspectorAdvanced.UpdateCheckExpand();
                }
                else if (mRenderStateInfo != null)
                {
                    mInspectorAdvanced = null;
                }
                UpdateLayout();
            }
        }

        private void DoUpdateLayout()
        {
            //Panel operationPanel = mOperationBar.GetPanelBar();
            //operationPanel.SetSize(mScrollView.GetWidth(), operationPanel.GetHeight());
            //operationPanel.SetPos(2, 0);

            int ScrollPanelWidth = mScrollView.GetWidth();
            int Y = 0;
            if (mInspector != null && mObjectInspected != null)
            {
                mInspector.UpdateLayout(ScrollPanelWidth, ref Y);
                if (Y > mScrollView.GetHeight())
                {
                    ScrollPanelWidth = mScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                    Y = 0;
                    mInspector.UpdateLayout(ScrollPanelWidth, ref Y);
                }
            }
            if (mInspectorAdvanced != null && mRenderStateInfo != null)
            {
                mInspectorAdvanced.UpdateLayout(ScrollPanelWidth, ref Y);
            }
            int Height = Y;
            mScrollPanel.SetSize(ScrollPanelWidth, Height);
            mScrollView.UpdateScrollBar();
        }

        private void UpdateLayout()
        {
            mInspectorHandler.UpdateLayout();
        }

        private void DoReadValue()
        {
            if (mInspector != null)
            {
                mInspector.ReadValue();
            }
        }

        public void Tick()
        {
            if (mMaterialEditorContext.GetSelectedExpressionsCount() > 0)
            {
                var expression = mMaterialEditorContext.GetSelectedExpression(mMaterialEditorContext.GetSelectedExpressionsCount() - 1);
                Inspect(expression);
            }
            else
            {
                if (mType == CEngine.ClassIDType.CLASS_Fx)
                {
                    Inspect(mMaterialEditorContext.GetMaterialDefines());
                }
                else
                {
                    Inspect(mMaterialEditorContext.GetMaterialFunctionDefines());
                }
            }

            UpdateLayout();
        }

        void Inspect(object ObjectInspected, Object Tag = null)
        {
            if (Tag as String == "RenderState")
            {
                if (ObjectInspected == null)
                {
                    return;
                }

                if (mRenderStateInfo != null && mRenderStateInfo.Equals(ObjectInspected))
                {
                    return;
                }
                mRenderStateInfo = ObjectInspected as RenderStateInfo;
                SetObjectInspected(mRenderStateInfo);
                SetObjectTag(Tag);
            }
            else
            {
                if (ObjectInspected == null)
                {
                    return;
                }

                if (mObjectInspected != null && mObjectInspected.Equals(ObjectInspected))
                {
                    return;
                }

                SetObjectInspected(ObjectInspected);
                SetObjectTag(Tag);
            }
            InspectObject();
        }

        public void SetObjectInspected(object ObjectInspected)
        {
            mObjectInspected = ObjectInspected;
            if (mInspector != null)
            {
                mInspector.WriteValue();
            }

            if (mObjectInspected == null)
            {
                mInspector = null;
            }
        }

        public void SetObjectTag(Object Tag)
        {
            mObjectTag = Tag;
        }

        public void OnSearchUISearch(SearchUI Sender, string Pattern)
        {
            if (mInspector != null)
            {
                List<Inspector> SearchResult = new List<Inspector>();
                mInspector.ForEach((Inspector) =>
                {
                    if (Inspector == mInspector)
                    {
                        return;
                    }
                    // Traverse all controls
                    Queue<Control> Controls = new Queue<Control>();
                    Controls.Enqueue(Inspector.GetSelfContainer());
                    while (Controls.Count > 0)
                    {
                        Control Control = Controls.Dequeue();
                        for (int i = 0; i < Control.GetChildCount(); ++i)
                        {
                            Controls.Enqueue(Control.GetChild(i));
                        }
                        // Try to match label text
                        if (Control is Label)
                        {
                            Label Label = Control as Label;
                            if (StringHelper.IgnoreCaseContains(Label.GetText(), Pattern))
                            {
                                SearchResult.Add(Inspector);
                                break;
                            }
                        }
                        // Try to match edit text
                        else if (Control is Edit)
                        {
                            Edit Edit = Control as Edit;
                            if (StringHelper.IgnoreCaseContains(Edit.GetText(), Pattern))
                            {
                                SearchResult.Add(Inspector);
                                break;
                            }
                        }
                    }
                });
                // Hide all inspectors
                mInspector.ForEach((Inspector) =>
                {
                    Inspector.SetVisible(false);
                });
                // Show search result
                foreach (Inspector Result in SearchResult)
                {
                    Inspector This = Result;
                    while (This != null)
                    {
                        This.SetVisible(true);
                        This = This.GetParentInspector();
                    }

                    Result.ForEach((Inspector) =>
                    {
                        Inspector.SetVisible(true);
                    });
                }
                if (SearchResult.Count == 0)
                {
                    mInspector.SetVisible(true);
                }
                UpdateLayout();
            }
        }

        public void OnSearchUICancel(SearchUI Sender)
        {
            if (mInspector != null)
            {
                mInspector.ForEach((Inspector) =>
                {
                    Inspector.SetVisible(true);
                });

                UpdateLayout();
            }
        }

        public void OnDragDropManagerDragEnd(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY, ref bool bContinue)
        {
            if (mScrollView.GetVisible_Recursively() && mScrollView.IsPointIn_Recursively(MouseX, MouseY))
            {
                ProjectUI ProjectUI = ProjectUI.GetInstance();
                if (ProjectUI.IsPathesDragging())
                {
                    List<string> PathesDragged = ProjectUI.GetPathesDragged();
                    DropPathOnInspector(mInspector, MouseX, MouseY, PathesDragged);
                }
            }
        }

        bool DropPathOnInspector(Inspector Inspector, int MouseX, int MouseY, List<string> PathesDragged)
        {
            if (Inspector == null)
            {
                return false;
            }
            List<Inspector> ChildInspectors = Inspector.GetChildInspectors();
            foreach (Inspector ChildInspector in ChildInspectors)
            {
                if (DropPathOnInspector(ChildInspector, MouseX, MouseY, PathesDragged))
                {
                    return true;
                }
            }
            return Inspector.OnDropPathes(MouseX, MouseY, PathesDragged);
        }

        public Control Control => mScrollView;
    }
}
