using CEngine;
using System.IO;
using System.Reflection;
using System.Xml;
using System.Xml.Serialization;


namespace CrossEditor
{
    [XmlRoot]
    public class FileWatchConfig
    {
        const string gConfigFileName = EditorConfig.EDITOR_CONFIG_RELATIVE_PATH + "FileWatchConfig.config";

        [XmlElement("Path")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Watch Path")]
        public string Path
        {
            set { FileWatch.GetInsance().AddSrcFileWatcher(value); }
            get { return FileWatch.GetInsance().GetSrcWatchingPath(); }
        }


        [XmlElement("Enable Source Watch")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Watch Source File Enable")]
        public bool EnableWatchSourceFile
        {
            set { FileWatch.GetInsance().Enable(FileWatchType.WatchSourceFile, value); }
            get { return FileWatch.GetInsance().IsEnable(FileWatchType.WatchSourceFile); }
        }

        [XmlElement("Enable Project Watch")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Watch Project File Enable")]
        public bool EnableWatchProjectFile
        {
            set { FileWatch.GetInsance().Enable(FileWatchType.WatchProjectFile, value); }
            get { return FileWatch.GetInsance().IsEnable(FileWatchType.WatchProjectFile); }
        }

        [XmlElement("Enable Engine Watch")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Watch Engine File Enable")]
        public bool EnableWatchEngineFile
        {
            set { FileWatch.GetInsance().Enable(FileWatchType.WatchEngineFile, value); }
            get { return FileWatch.GetInsance().IsEnable(FileWatchType.WatchEngineFile); }
        }


        public FileWatchConfig()
        {
            Path = "";
            EnableWatchSourceFile = false;
            EnableWatchProjectFile = true;
            EnableWatchEngineFile = true;
        }

        public void Load()
        {
            if (File.Exists(gConfigFileName) == false)
            {
                return;
            }
            XmlSerializer Ser = new XmlSerializer(typeof(FileWatchConfig));

            using (StreamReader Reader = new StreamReader(gConfigFileName))
            {
                FileWatchConfig config = (FileWatchConfig)Ser.Deserialize(Reader);
                Path = MainUI.GetInstance().GetProjectDirectory();
                EnableWatchSourceFile = config.EnableWatchSourceFile;
                EnableWatchProjectFile = config.EnableWatchProjectFile;
                EnableWatchEngineFile = config.EnableWatchEngineFile;
                Reader.Close();
            }
        }

        public void Dump()
        {
            XmlSerializer Ser = new XmlSerializer(typeof(FileWatchConfig));
            using (StreamWriter Writer = new StreamWriter(gConfigFileName))
            {
                Ser.Serialize(Writer, this);
                Writer.Close();
            }
        }
    }
}
