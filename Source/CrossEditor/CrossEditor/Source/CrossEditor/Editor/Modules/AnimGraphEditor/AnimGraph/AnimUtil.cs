using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    class AnimUtil
    {
        public static Vector2i GetLineEdgePosition(Node Node, int DestX, int DestY)
        {
            int OutMargin = 5;

            Rect NewRect = new Rect();
            NewRect.X = Node.X - OutMargin;
            NewRect.Y = Node.Y - OutMargin;
            NewRect.Width = Node.Width + OutMargin * 2;
            NewRect.Height = Node.Height + OutMargin * 2;

            Vector2i Position = GetLineRectIntersection(new Vector2f(DestX, DestY), NewRect);

            return Position;
        }

        static Vector2i GetLineRectIntersection(Vector2f Point, Rect Rect)
        {

            Vector2f LT = new Vector2f(Rect.X, Rect.Y);
            Vector2f RT = new Vector2f(Rect.X + Rect.Width, Rect.Y);
            Vector2f LB = new Vector2f(Rect.X, Rect.Y + Rect.Height);
            Vector2f RB = new Vector2f(Rect.X + Rect.Width, Rect.Y + Rect.Height);

            Vector2f Center = new Vector2f(Rect.X + Rect.Width * 0.5f, Rect.Y + Rect.Height * 0.5f);
            Vector2i Intersection = new Vector2i((int)Center.X, (int)Center.Y);

            if (Point.X < Rect.X)
            {
                if (Point.Y < Rect.Y)
                {
                    if (GetIntersectPoint(Center, Point, LT, RT, ref Intersection))
                    {
                        // Intersect Top Line
                        return Intersection;
                    }
                    else if (GetIntersectPoint(Center, Point, LT, LB, ref Intersection))
                    {
                        // Intersect Left Line
                        return Intersection;
                    }
                }
                else
                {
                    if (GetIntersectPoint(Center, Point, LB, RB, ref Intersection))
                    {
                        // Intersect Bottom Line
                        return Intersection;
                    }
                    else if (GetIntersectPoint(Center, Point, LT, LB, ref Intersection))
                    {
                        // Intersect Left Line
                        return Intersection;
                    }
                }
            }
            else
            {
                if (Point.Y < Rect.Y)
                {
                    if (GetIntersectPoint(Center, Point, LT, RT, ref Intersection))
                    {
                        // Intersect Top Line
                        return Intersection;
                    }
                    else if (GetIntersectPoint(Center, Point, RT, RB, ref Intersection))
                    {
                        // Intersect Right Line
                        return Intersection;
                    }
                }
                else
                {
                    if (GetIntersectPoint(Center, Point, LB, RB, ref Intersection))
                    {
                        // Intersect Bottom Line
                        return Intersection;
                    }
                    else if (GetIntersectPoint(Center, Point, RT, RB, ref Intersection))
                    {
                        // Intersect Right Line
                        return Intersection;
                    }
                }
            }

            return Intersection;
        }

        static bool GetIntersectPoint(Vector2f PointA, Vector2f PointB, Vector2f PointC, Vector2f PointD, ref Vector2i Point)
        {
            bool Intersect = false;

            Vector2f AB = new Vector2f(PointB.X - PointA.X, PointB.Y - PointA.Y);
            Vector2f AC = new Vector2f(PointC.X - PointA.X, PointC.Y - PointA.Y);
            Vector2f AD = new Vector2f(PointD.X - PointA.X, PointD.Y - PointA.Y);

            Vector2f CD = new Vector2f(PointD.X - PointC.X, PointD.Y - PointC.Y);
            Vector2f CA = new Vector2f(PointA.X - PointC.X, PointA.Y - PointC.Y);
            Vector2f CB = new Vector2f(PointB.X - PointC.X, PointB.Y - PointC.Y);

            if (Cross(AB.X, AB.Y, AC.X, AC.Y) * Cross(AB.X, AB.Y, AD.X, AD.Y) < 0.0f
                && Cross(CD.X, CD.Y, CA.X, CA.Y) * Cross(CD.X, CD.Y, CB.X, CB.Y) < 0.0f)
            {
                Intersect = true;

                float K = Cross(AC.X, AC.Y, AD.X, AD.Y) / (Cross(AB.X, AB.Y, AD.X, AD.Y) - Cross(AB.X, AB.Y, AC.X, AC.Y));
                float DX = K * (PointB.X - PointA.X);
                float DY = K * (PointB.Y - PointA.Y);

                Point.X = (int)(PointA.X + DX);
                Point.Y = (int)(PointA.Y + DY);
            }

            return Intersect;
        }

        static float Cross(float X1, float Y1, float X2, float Y2)
        {
            return X1 * Y2 - X2 * Y1;
        }

        public static string TrimAnimPath(string inPath)
        {
            List<string> ReplacedPatterns = new List<string> { ".nda", "_ANIM", "_Anim", "_Unreal Take", "_Composite", "_Acl" };
            foreach (var item in ReplacedPatterns)
            {
                inPath = inPath.Replace(item, "");
            }
            return inPath;
        }
    }
}
