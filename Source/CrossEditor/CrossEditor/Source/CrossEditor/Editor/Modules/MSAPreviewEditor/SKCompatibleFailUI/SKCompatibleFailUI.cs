using EditorUI;

namespace CrossEditor
{
    internal delegate void SKCompatibleFailUIInputedEventHandler(SKCompatibleFailUI Sender);

    internal class SKCompatibleFailUI : DialogUI
    {
        Texture _TreeBoneItemTexture;

        Label _AssetTypeLeft;
        Tree _PathLeft;
        Label _AssetTypeRight;
        Tree _PathRight;

        Tree _HierarchyLeft;
        Tree _HierarchyRight;

        CompatibleFailInfo _CompatibleFailInfo;
        PreviewRefSkeleton _RunSk = null;
        PreviewRefSkeleton _AssetSk = null;

        Color _FailedItemColor = Color.FromRGB(255, 0, 0);
        Color _MatchItemColor = Color.FromRGB(204, 255, 204);

        public event SKCompatibleFailUIInputedEventHandler InputedEvent;

        public SKCompatibleFailUI()
        {
        }

        public void Initialize(UIManager UIManager, string Title, CompatibleFailInfo CompatibleFailedInfo)
        {
            _TreeBoneItemTexture = UIManager.LoadUIImage("Editor/Tree/Game/Skeleton.png");

            base.Initialize(UIManager, Title, 1100, 800);

            _CompatibleFailInfo = CompatibleFailedInfo;
            _RunSk = _CompatibleFailInfo.SkeletonAsset.GetRefSkeleton();

            // Left Panel
            _AssetTypeLeft = new Label();
            _AssetTypeLeft.Initialize();
            _AssetTypeLeft.SetText("Skeleton Asset Path:");
            _AssetTypeLeft.SetFontSize(15);
            _AssetTypeLeft.SetTextAlign(TextAlign.CenterLeft);
            _PanelDialog.AddChild(_AssetTypeLeft);
            _AssetTypeLeft.SetPosition(30, 60, 500, 15);

            _PathLeft = new Tree();
            _PathLeft.Initialize();
            _PathLeft.SetEnableDragDrop(false);
            _PathLeft.SetEnableRename(false);
            _PathLeft.SetCanSelectNothing(true);
            _PathLeft.SetFontSize(15);
            _PathLeft.SetTextAlign(TextAlign.CenterLeft);
            _PathLeft.GetRootItem().SetText("\"" + CompatibleFailedInfo.SkeletonAsset.AssetPath + "\"");
            _PathLeft.SelectItem(null);
            _PanelDialog.AddChild(_PathLeft);
            _PathLeft.SetPosition(30, 80, 500, 40);

            _HierarchyLeft = new Tree();
            _HierarchyLeft.Initialize();
            _HierarchyLeft.SetEnableDragDrop(false);
            _HierarchyLeft.SetEnableRename(false);
            _HierarchyLeft.SetCanSelectNothing(true);
            _PanelDialog.AddChild(_HierarchyLeft);
            _HierarchyLeft.SetPosition(30, 135, 500, 600);
            //_HierarchyLeft.RightMouseUpEvent += OnHierRightMouseUp;

            // Right Panel
            _AssetTypeRight = new Label();
            _AssetTypeRight.Initialize();
            _AssetTypeRight.SetFontSize(15);
            _AssetTypeRight.SetTextAlign(TextAlign.CenterLeft);

            _PathRight = new Tree();
            _PathRight.Initialize();
            _PathRight.SetEnableDragDrop(false);
            _PathRight.SetEnableRename(false);
            _PathRight.SetCanSelectNothing(true);
            _PathRight.SetFontSize(15);
            _PathRight.SetTextAlign(TextAlign.CenterLeft);

            if (_CompatibleFailInfo.SkeltMesh != null)
            {
                _AssetSk = _CompatibleFailInfo.SkeltMesh.GetRefSkeleton();
                _AssetTypeRight.SetText("SkeletalMesh Asset Path:");
                _PathRight.GetRootItem().SetText("\"" + _CompatibleFailInfo.SkeltMesh.AssetPath + "\"");
            }
            else if (_CompatibleFailInfo.AnimSeq != null)
            {
                _AssetSk = _CompatibleFailInfo.AnimSeq.GetRefSkeleton();
                _AssetTypeRight.SetText("AnimSequence Asset Path:");
                _PathRight.GetRootItem().SetText("\"" + _CompatibleFailInfo.AnimSeq.AssetPath + "\"");
            }

            _PanelDialog.AddChild(_AssetTypeRight);
            _AssetTypeRight.SetPosition(570, 60, 500, 15);

            _PathRight.SelectItem(null);
            _PanelDialog.AddChild(_PathRight);
            _PathRight.SetPosition(570, 80, 500, 40);

            _HierarchyRight = new Tree();
            _HierarchyRight.Initialize();
            _HierarchyRight.SetEnableDragDrop(false);
            _HierarchyRight.SetEnableRename(false);
            _HierarchyRight.SetCanSelectNothing(true);
            _PanelDialog.AddChild(_HierarchyRight);
            _HierarchyRight.SetPosition(570, 135, 500, 600);
            //_HierarchyLeft.RightMouseUpEvent += OnHierRightMouseUp;

            ShowSkeletonHierarchy();
        }

        private void ShowSkeletonHierarchy()
        {
            // show run skeleton Hierarchy
            if (_AssetSk != null)
            {
                TreeItem CurSkeltRefSkRootItem = _HierarchyLeft.GetRootItem();
                BuildRefSkHierarchyRecursively(_HierarchyLeft, CurSkeltRefSkRootItem, _RunSk.Root);

                // show asset reference skeleton Hierarchy
                TreeItem CurAssetRefSkRootItem = _HierarchyRight.GetRootItem();
                BuildRefSkHierarchyRecursively(_HierarchyRight, CurAssetRefSkRootItem, _AssetSk.Root);

                HighlightFailedBone();

                SortSkeletonHierarchyRecursively(CurSkeltRefSkRootItem);

                SortSkeletonHierarchyRecursively(CurAssetRefSkRootItem);
            }
        }

        private void BuildRefSkHierarchyRecursively(Tree BoneTree, TreeItem BoneItem, PreviewBone CurBone)
        {
            BoneItem.SetFolder(true);
            BoneItem.SetExpanded(true);
            BoneItem.SetImageFolded(_TreeBoneItemTexture);
            BoneItem.SetImageExpanded(_TreeBoneItemTexture);
            BoneItem.SetText(CurBone.BoneName);
            BoneItem.SetTagObject(CurBone);

            for (int i = 0; i < CurBone.ChildrenCount(); ++i)
            {
                TreeItem ChildItem = BoneTree.CreateItem();
                BoneItem.AddChild(ChildItem);
                BuildRefSkHierarchyRecursively(BoneTree, ChildItem, CurBone.GetChildBone(i));
            }
        }

        private void SortSkeletonHierarchyRecursively(TreeItem BoneItem)
        {
            BoneItem.Sort();

            var ChildList = BoneItem.GetChildList();
            foreach (TreeItem ChildItem in ChildList)
            {
                SortSkeletonHierarchyRecursively(ChildItem);
            }
        }

        private void HighlightFailedBone()
        {
            int RunSkMissedBoneIndex = _CompatibleFailInfo.RunSkMissedBone.BoneIndex;
            int ResRefSkMissedBoneIndex = _CompatibleFailInfo.ResRefSkMissedBone.BoneIndex;

            if (RunSkMissedBoneIndex == 0 && ResRefSkMissedBoneIndex == 0)
            {
                _HierarchyLeft.GetRootItem().SetTextColor(_FailedItemColor);
                SetItemColorRecursively(_HierarchyRight.GetRootItem());
                return;
            }

            PreviewBone RunSkBone = _RunSk.GetBoneList()[RunSkMissedBoneIndex];
            PreviewBone AssetSkBone = _AssetSk.GetBoneList()[ResRefSkMissedBoneIndex];

            TreeItem RunSkTreeItem = _HierarchyLeft.FindItemByTagObject(RunSkBone);
            TreeItem AssetSkTreeItem = _HierarchyRight.FindItemByTagObject(AssetSkBone);
            SetParentChain(RunSkTreeItem, AssetSkTreeItem);
        }

        private void SetItemColorRecursively(TreeItem Item)
        {
            Item.SetTextColor(_FailedItemColor);

            var ChildList = Item.GetChildList();
            foreach (TreeItem ChildItem in ChildList)
            {
                SetItemColorRecursively(ChildItem);
            }
        }

        private bool IsParentChainMatch(TreeItem RunSkTreeItem, TreeItem AssetSkTreeItem)
        {
            PreviewBone RunSkBone = RunSkTreeItem.GetTagObject() as PreviewBone;
            PreviewBone AssetSkBone = AssetSkTreeItem.GetTagObject() as PreviewBone;

            return RunSkBone.BoneName == AssetSkBone.BoneName;
        }

        private void SetParentChain(TreeItem RunSkTreeItem, TreeItem AssetSkTreeItem)
        {
            SetItemColorRecursively(AssetSkTreeItem);

            while (IsParentChainMatch(RunSkTreeItem, AssetSkTreeItem))
            {
                RunSkTreeItem.SetTextColor(_FailedItemColor);
                AssetSkTreeItem.SetTextColor(_FailedItemColor);

                RunSkTreeItem = RunSkTreeItem.GetParent();
                AssetSkTreeItem = AssetSkTreeItem.GetParent();
            }

            RunSkTreeItem.SetTextColor(_FailedItemColor);
            AssetSkTreeItem.SetTextColor(_FailedItemColor);

            _HierarchyLeft.SelectItem(RunSkTreeItem);
            _HierarchyRight.SelectItem(AssetSkTreeItem);
        }

        public override void ShowDialog()
        {
            base.ShowDialog();
        }

        public override void OnDeviceChar(Device Sender, char Char)
        {
            base.OnDeviceChar(Sender, Char);

            Device Device = GetDevice();
            bool bControl = Device.IsControlDown();
            bool bShift = Device.IsShiftDown();
            bool bAlt = Device.IsAltDown();
            bool bNone = !bControl && !bShift && !bAlt;

            if (bNone && (Char == '\r' || Char == '\n'))
            {
                OnButtonOKClicked(null);
            }
        }

        void OnButtonOKClicked(Button Sender)
        {
            CloseDialog();

            if (InputedEvent != null)
            {
                InputedEvent(this);
            }
        }
    }
}
