using CEngine;
namespace CrossEditor
{
    class TerrainLayer
    {
        public string _BaseColorTexture;
        public string _NormalTexture;
        public string _HMRATexture;

        public TerrainLayer()
        {
            _BaseColorTexture = "";
            _NormalTexture = "";
            _HMRATexture = "";
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Base color texture path", FileTypeDescriptor = "Texture Files#nda", ObjectClassID1 = ClassIDType.CLASS_Texture)]
        public string BaseColorTexture
        {
            get
            {
                return _BaseColorTexture;
            }
            set
            {
                _BaseColorTexture = value;
            }
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Normal texture path", FileTypeDescriptor = "Texture Files#nda", ObjectClassID1 = ClassIDType.CLASS_Texture)]
        public string NormalTexture
        {
            get
            {
                return _NormalTexture;
            }
            set
            {
                _NormalTexture = value;
            }
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Height, metallic, roughness, ambient occlusion texture path", FileTypeDescriptor = "Texture Files#nda", ObjectClassID1 = ClassIDType.CLASS_Texture)]
        public string HMRATexture
        {
            get
            {
                return _HMRATexture;
            }
            set
            {
                _HMRATexture = value;
            }
        }
    }
}
