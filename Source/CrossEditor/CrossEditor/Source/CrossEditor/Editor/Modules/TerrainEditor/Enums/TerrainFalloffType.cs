namespace CrossEditor
{
    enum TerrainFalloffType
    {
        [TerrainEnumInfo(DisplayName = "Smooth Falloff", TexturePath = "EngineResource/Editor/Icons/Terrain/FalloffSmooth.nda")]
        Smooth,

        [TerrainEnumInfo(DisplayName = "Linear Falloff", TexturePath = "EngineResource/Editor/Icons/Terrain/FalloffLinear.nda")]
        Linear,

        [TerrainEnumInfo(DisplayName = "Spherical Falloff", TexturePath = "EngineResource/Editor/Icons/Terrain/FalloffSpherical.nda")]
        Spherical,

        [TerrainEnumInfo(DisplayName = "Tip Falloff", TexturePath = "EngineResource/Editor/Icons/Terrain/FalloffTip.nda")]
        Tip,
    }
}
