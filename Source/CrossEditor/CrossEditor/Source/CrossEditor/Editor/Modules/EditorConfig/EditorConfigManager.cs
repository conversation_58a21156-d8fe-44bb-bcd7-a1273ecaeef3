using CEngine;
using System.Collections.Generic;

namespace CrossEditor
{
    class ConfigNode
    {
        public object mConfig;
        public string mName;
        public List<ConfigNode> mSubConfigs;

        public ConfigNode(object config, string name)
        {
            mConfig = config;
            mName = name;
            mSubConfigs = new List<ConfigNode>();
        }
    }

    class EditorConfigManager
    {
        static EditorConfigManager gInstance = null;

        public List<ConfigNode> mConfigList;

        public static EditorConfigManager GetInstance()
        {
            if (gInstance == null)
                gInstance = new EditorConfigManager();
            return gInstance;
        }

        public T GetConfig<T>()
        {
            foreach (ConfigNode node in mConfigList)
            {
                if (node.mConfig.GetType() == typeof(T))
                    return (T)node.mConfig;
            }
            return default;
        }

        public EditorConfigManager()
        {
            mConfigList = new List<ConfigNode>();
        }

        public void Initialize()
        {
            InitGlobalConfig();
            InitBuildConfig();
            InitCookSetting();
            InitFileWatchSetting();
            InitShaderConfig();
            InitAutoSaveConfig();
        }

        private void InitGlobalConfig()
        {
            GlobalConfig globalConfig = new GlobalConfig();
            globalConfig.Load();
            mConfigList.Add(new ConfigNode(globalConfig, "GlobalConfig"));
        }

        private void InitBuildConfig()
        {
            BuildConfig buildConfig = new BuildConfig();
            buildConfig.Load();
            mConfigList.Add(new ConfigNode(buildConfig, "BuildConfig"));
        }

        private void InitCookSetting()
        {
            CookSettingManager csm = AssetCookerManager.Instance().GetCookSetting();
            ConfigNode cook = new ConfigNode(csm, "CookSettingManager");
            csm.Load();
            vector_string tps = csm.GetCookTypes();
            for (int index = 0; index < tps.Count; index++)
            {
                var cookSetting = csm.GetCookSetting(tps[index]);
                cook.mSubConfigs.Add(new ConfigNode(cookSetting, tps[index]));
            }
            mConfigList.Add(cook);
        }

        private void InitFileWatchSetting()
        {
            FileWatchConfig fwc = new FileWatchConfig();
            fwc.Load();
            mConfigList.Add(new ConfigNode(fwc, "FileWatchConfig"));
        }

        private void InitShaderConfig()
        {
            ShaderConfig shaderConfig = new ShaderConfig();
            shaderConfig.Load();
            mConfigList.Add(new ConfigNode(shaderConfig, "ShaderConfig"));
        }

        private void InitAutoSaveConfig()
        {
            AutoSaveConfig conf = new AutoSaveConfig();
            conf.Load();
            mConfigList.Add(new ConfigNode(conf, "AutoSaveConfig"));
        }
    }
}
