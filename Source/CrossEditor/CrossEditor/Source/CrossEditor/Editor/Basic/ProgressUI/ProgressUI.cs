using EditorUI;

namespace CrossEditor
{
    delegate void ProgressUICancelEventHandler(ProgressUI Sender);

    class ProgressUI : DialogUI
    {
        Progress _Progress;
        Label _LabelStepTips;
        ProgressBar _ProgressBarStep;
        Label _LabelItemTips;
        ProgressBar _ProgressBarItem;

        public event ProgressUICancelEventHandler CancelEvent;

        public ProgressUI()
        {
        }

        public void Initialize(UIManager UIManager, Progress Progress, bool bEnableCancel)
        {
            base.Initialize(UIManager, Progress.Name, 810, 200);
            _Progress = Progress;

            SetCanClose(bEnableCancel);

            _LabelStepTips = new Label();
            _LabelStepTips.SetPosition(20, 60, 770, 20);
            _LabelStepTips.SetFontSize(16);
            _LabelStepTips.SetText("Step 1/3: Analyzing dependencies...");
            _LabelStepTips.SetTextAlign(TextAlign.CenterLeft);
            _PanelDialog.AddChild(_LabelStepTips);

            _ProgressBarStep = new ProgressBar();
            _ProgressBarStep.SetPosition(20, 82, 770, 20);
            _ProgressBarStep.SetProgress(0.5f);
            _PanelDialog.AddChild(_ProgressBarStep);

            _LabelItemTips = new Label();
            _LabelItemTips.SetPosition(20, 130, 770, 20);
            _LabelItemTips.SetFontSize(16);
            _LabelItemTips.SetText("Item 3/5: Contents/World/Sponza.world");
            _LabelItemTips.SetTextAlign(TextAlign.CenterLeft);
            _PanelDialog.AddChild(_LabelItemTips);

            _ProgressBarItem = new ProgressBar();
            _ProgressBarItem.SetPosition(20, 152, 770, 20);
            _ProgressBarItem.SetProgress(0.5f);
            _PanelDialog.AddChild(_ProgressBarItem);

            UpdateProgress();
        }

        public override void Update()
        {
            UpdateProgress();
            if (_Progress.bClose)
            {
                OperationQueue.GetInstance().AddOperation(() =>
                {
                    CloseDialog();
                });
            }
        }

        public override void CloseDialog()
        {
            base.CloseDialog();

            if (_Progress.bClose == false)
            {
                if (CancelEvent != null)
                {
                    CancelEvent(this);
                }
            }
        }

        void UpdateProgress()
        {
            string StepTipsString = string.Format("Step {0}/{1}: {2}", _Progress.CurrentStep + 1, _Progress.TotalSteps, _Progress.StepTips);
            float StepProgress = _Progress.CurrentStep / (float)_Progress.TotalSteps;

            string ItemTipsString = string.Format("Item {0}/{1}: {2}", _Progress.CurrentItem, _Progress.TotalItems, _Progress.ItemTips);
            float ItemProgress = _Progress.CurrentItem / (float)_Progress.TotalItems;

            if (_Progress.bStarted == false)
            {
                StepTipsString = "Step -/-";
                ItemTipsString = "Item -/-";
                StepProgress = 0.0f;
                ItemProgress = 0.0f;
            }
            else if (_Progress.bDone)
            {
                StepTipsString = "Steps Done.";
                ItemTipsString = "Items Done.";
                StepProgress = 1.0f;
                ItemProgress = 1.0f;
            }

            _LabelStepTips.SetText(StepTipsString);
            _ProgressBarStep.SetProgress(StepProgress);
            _LabelItemTips.SetText(ItemTipsString);
            _ProgressBarItem.SetProgress(ItemProgress);
        }
    }
}
