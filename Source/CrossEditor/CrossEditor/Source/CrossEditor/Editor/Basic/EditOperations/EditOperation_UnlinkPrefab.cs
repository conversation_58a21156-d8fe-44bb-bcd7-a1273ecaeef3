using CEngine;
using System.Collections.Generic;

namespace CrossEditor
{
    public class PrefabEntityData
    {
        public Entity mEntity;
        public string mPrefabId;
        public CrossUUID mPrefabEuid;
    }

    public class EditOperation_UnlinkPrefab : EditOperation
    {
        public List<PrefabEntityData> mEntities = new List<PrefabEntityData>();
        public bool mIsCompleted = false;

        public EditOperation_UnlinkPrefab(bool comlete)
        {
            mIsCompleted = comlete;
        }

        public void Add(Entity entity)
        {
            PrefabEntityData data = new PrefabEntityData();
            data.mEntity = entity;
            data.mPrefabId = entity.GetPrefabId();
            data.mPrefabEuid = entity.GetPrefabEuid();
            mEntities.Add(data);
        }

        public override void Undo()
        {
            foreach (PrefabEntityData data in mEntities)
            {
                data.mEntity.RuntimeLinkPrefab(data.mPrefabId, data.mPrefabEuid);
                data.mEntity.UpdatePrefab();
            }
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
        }

        public override void Redo()
        {
            foreach (PrefabEntityData data in mEntities)
            {
                data.mEntity.RuntimeUnlinkPrefab(mIsCompleted);
            }
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
        }
    }
}
