using EditorUI;

namespace CrossEditor
{
    public class DockingUI
    {
        public const int BAR_HEIGHT = 28;
        protected DockingCard _DockingCard;
        protected bool _IsFocused;

        protected MenuItem _MenuItem = null;
        public delegate void ClickDockingDelegate(DockingCard card);
        public DockingUI()
        {
        }

        public bool Initialize(string Name, Control ContentControl)
        {
            _DockingCard = new DockingCard();
            _DockingCard.Initialize();
            _DockingCard.SetText(Name);
            _DockingCard.SetTagObject(this);
            _DockingCard.SetContent(ContentControl);
            _DockingCard.CloseEvent += OnClose;

            ContentControl.PositionChangedEvent += OnPositionChanged;
            ContentControl.FocusChangedEvent += OnFocusChangedHandler;

            return false;
        }

        public UIManager GetUIManager()
        {
            if (_DockingCard != null)
            {
                return _DockingCard.GetUIManager();
            }
            else
            {
                return UIManager.GetMainUIManager();
            }
        }

        public bool InitMenuItem(ClickDockingDelegate callBack = null, string title = "")
        {
            if (_MenuItem == null)
            {
                _MenuItem = new MenuItem();
                if (title == "")
                {
                    title = this.GetType().Name;
                    _MenuItem.SetText(title);
                }
            }

            if (callBack != null)
            {
                _MenuItem.ClickedEvent += delegate (MenuItem sender)
                {
                    callBack(GetDockingCard());
                };
            }

            return true;
        }

        public MenuItem GetMenuItem() { return _MenuItem; }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        public Graphics2D GetGraphics2D()
        {
            return GetUIManager().GetGraphics2D();
        }

        public FrameUI GetFrameUI()
        {
            UIManager UIManager = GetUIManager();
            Window Window = WindowManager.GetInstance().FindWindow(UIManager);
            if (Window != null)
            {
                return Window.GetFrameUI();
            }
            return MainUI.GetInstance().GetFrameUI();
        }

        public DockingCard GetDockingCard()
        {
            return _DockingCard;
        }

        public virtual bool IsFocused()
        {
            return _IsFocused;
        }

        public virtual bool IsVisible()
        {
            return _DockingCard.GetVisible_Recursively();
        }

        public virtual bool IsDockingCardActive()
        {
            if (_DockingCard == null)
            {
                return false;
            }

            if (_DockingCard.GetDockingBlock() == null)
            {
                return false;
            }

            if (_DockingCard.GetRootParent() != GetUIManager().GetRoot())
            {
                return false;
            }

            if (_DockingCard.GetVisible_Recursively() == false)
            {
                return false;
            }

            if (_DockingCard.GetActive() == false)
            {
                return false;
            }

            return true;
        }

        public virtual bool IsDockingCardDisplay()
        {
            if (_DockingCard == null)
            {
                return false;
            }

            if (_DockingCard.GetDockingBlock() == null)
            {
                return false;
            }

            if (_DockingCard.GetRootParent() != GetUIManager().GetRoot())
            {
                return false;
            }

            if (_DockingCard.GetVisible_Recursively() == false)
            {
                return false;
            }

            return true;
        }

        public virtual void Update(long TimeElapsed)
        {

        }

        public virtual void OnPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
        }

        public virtual void OnFocusChangedHandler(Control Sender)
        {
            bool isFocused = _DockingCard.GetDockingBlock() != null && _DockingCard.GetDockingBlock().GetFocused() && _DockingCard.GetActive();
            if (_IsFocused != isFocused)
            {
                if (_IsFocused)
                {
                    OnLeave();
                }
                else
                {
                    OnEnter();
                }
                _IsFocused = isFocused;
            }
        }

        public virtual void OnClose(DockingCard Sender, ref bool bNotToClose)
        {
            GetUIManager().SetFocusControl(null);
            //DockingBlock block = Sender.GetDockingBlock();
            //if(block != null)
            //{
            //    block.RemoveDockingCard(Sender);
            //}
        }

        protected virtual void OnEnter()
        {

        }

        protected virtual void OnLeave()
        {

        }
    }

}
