using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace CrossEditor
{
    public abstract class Importer
    {
        public int priority = 0;
        public ImportSetting CurrentImportSetting = null;

        public Importer(ImportSetting setting, int priori = 0) { priority = priori; }

        public virtual bool AcceptPath(List<string> paths) { return false; }

        public virtual void ShowUI(ImporterDelegate simpleDele, List<string> pathes) { }

        public async Task<AssetImportResult> DoImport(string TargetDirectory, string Path)
        {
            if (DirectoryHelper.IsDirectoryExists(Path))
            {
                return await FolderImport(TargetDirectory, Path);
            }
            else
            {
                return await FileImport(TargetDirectory, Path);
            }
        }

        public abstract Task<AssetImportResult> FolderImport(string TargetDirectory, string Path);

        public abstract Task<AssetImportResult> FileImport(string TargetDirectory, string Path);
        public virtual void SetImportSetting() { }
    }


    // could this become a reflection based?
    // current not supported, let's use register for now;
    public class GeneralFileImporter : Importer
    {
        public GeneralFileImporter(HashSet<AssetType> types, Type importUI, string title, ImportSetting setting, int priori = 0) : base(setting, priori)
        {
            ImportTypes = types;
            ui = importUI;
            CurrentImportSetting = setting;
            Title = title;
        }

        public HashSet<AssetType> ImportTypes = new HashSet<AssetType>();
        public Type ui = null;

        public string Title = null;

        public override bool AcceptPath(List<string> paths)
        {
            return ProjectUI.GetInstance().IsPathHasAsset(paths, ImportTypes);
        }

        public override void SetImportSetting()
        {
            if (CurrentImportSetting != null)
            {
                CurrentImportSetting.SetEngineImportSetting();
            }
        }

        public override void ShowUI(ImporterDelegate simpleDele, List<string> pathes)
        {
            if (ui != null && CurrentImportSetting!= null)
            {
                ImportUI import_ui = (ImportUI)Activator.CreateInstance(ui);
                import_ui.Initialize(ProjectUI.GetInstance().GetUIManager(), Title, pathes[0], CurrentImportSetting);
                import_ui.InputedEvent += (ImportUI Sender1) =>
                {
                    simpleDele(this);
                };
                DialogUIManager.GetInstance().ShowDialogUI(import_ui);
            }
            else
            {
                simpleDele(this);
            }
        }

        public override async Task<AssetImportResult> FileImport(string TargetDirectory, string Path)
        {
            return await ProjectUI.GetInstance().DoFileImport(TargetDirectory, Path);
        }

        public override async Task<AssetImportResult> FolderImport(string TargetDirectory, string Path)
        {
            return await ProjectUI.GetInstance().DoFolderImport(TargetDirectory, Path);
        }
    }


    public class GeneralFolderImporter : GeneralFileImporter
    {
        public GeneralFolderImporter() : base(null, null, "", null, 0) { }

        public override bool AcceptPath(List<string> paths) { return true; }

        public override void ShowUI(ImporterDelegate simpleDele, List<string> pathes)
        {
            simpleDele(this);
        }
    }

    public class ShaderImporter : GeneralFileImporter
    {
        public ShaderImporter() : base(new HashSet<AssetType> { AssetType.Shader, AssetType.ComputeShader, AssetType.RayTracingShader }, null, null, null, 0) { }
        public override async Task<AssetImportResult> FileImport(string TargetDirectory, string Path)
        {
            // always Copy extenal shader source so it won't missmathc
            var FileName = PathHelper.GetFileName(Path);
            var NewFilePath = PathHelper.CombinePath(TargetDirectory, FileName);
            // do not check filename, since shader's content may change
            if (!File.Exists(NewFilePath))
            {
                ProjectUI.GetInstance().DoCopyFile(Path, NewFilePath);
            }

            return await ProjectUI.GetInstance().DoFileImport(TargetDirectory, Path);
        }
    }

    public class TextureImporter : GeneralFileImporter
    {
        public TextureImporter(HashSet<AssetType> types, Type importUI, string title, ImportSetting setting, int priori = 0) : base(types, importUI, title, setting, priori) { }

        public override void ShowUI(ImporterDelegate SimpleDelegate, List<string> pathes)
        {
            bool _bMultipleTexture = false;

            ProjectUI.GetInstance()._bAutoRecognition = false;
            ProjectUI.GetInstance().Dictionary = TextureEditorUI.GetInstance().AutoRecognition(ref pathes);

            string path = "";

            if (pathes.Count > 0)
            {
                if (pathes.Count == 1)
                {
                    _bMultipleTexture = false;
                    path = pathes[0];
                }
                else
                {
                    _bMultipleTexture = true;
                }
            }
            else
            {
                if (ProjectUI.GetInstance().Dictionary.Count != 0)
                {
                    ProjectUI.GetInstance()._bAutoRecognition = true;
                    path = ProjectUI.GetInstance().Dictionary.First().Value[0];
                }
            }

            TextureImportUI TextureImportUI = new TextureImportUI();
            if (_bMultipleTexture || ProjectUI.GetInstance()._bAutoRecognition)
            {
                CurrentImportSetting = new TextureImportSetting();
                (CurrentImportSetting as TextureImportSetting).ColorSpace = ImportColorSpace.Linear;
                (CurrentImportSetting as TextureImportSetting).Compression = TextureCompression.Uncompressed;
            }
            else
            {
                CurrentImportSetting = AssetImporterManager.Instance().GetTextureImportSettings(path, (CurrentImportSetting as TextureImportSetting), false);
            }
            TextureImportUI.Initialize(ProjectUI.GetInstance().GetUIManager(), "Texture Import", path, CurrentImportSetting, _bMultipleTexture);
            TextureImportUI.InputedEvent += (ImportUI Sender1) =>
            {
                SimpleDelegate(this);
            };
            DialogUIManager.GetInstance().ShowDialogUI(TextureImportUI);
        }
    }



    public class MSDFImporter : GeneralFileImporter
    {
        public MSDFImporter(HashSet<AssetType> types, Type importUI, string title, ImportSetting setting, int priori = 0) : base(types, importUI, title, setting, priori) { }

        public override void ShowUI(ImporterDelegate SimpleDelegate, List<string> pathes)
        {
            MSDFGenerateUI MSDFGenerateUI = new MSDFGenerateUI();
            MSDFGenerateUI.Initialize(ProjectUI.GetInstance().GetUIManager(), "MSDF Generate", pathes[0]);
            MSDFGenerateUI.InputedEvent += (ImportUI Sender) =>
            {
                ProjectUI.GetInstance().RefreshListView(false);

                (CurrentImportSetting as FontImportSetting).MSDFResource = (Sender as MSDFGenerateUI).GetGenerateMSDFPath();
                (CurrentImportSetting as FontImportSetting).FontInfoResource = (Sender as MSDFGenerateUI).GetGenerateInfoPath();
                ImportUI FontImportUI = new ImportUI();
                FontImportUI.Initialize(ProjectUI.GetInstance().GetUIManager(), "Font Import", "", CurrentImportSetting);
                FontImportUI.InputedEvent += (ImportUI Sender) =>
                {
                    SimpleDelegate(this);
                };
                DialogUIManager.GetInstance().ShowDialogUI(FontImportUI);
            };
            DialogUIManager.GetInstance().ShowDialogUI(MSDFGenerateUI);
        }
    }
}
