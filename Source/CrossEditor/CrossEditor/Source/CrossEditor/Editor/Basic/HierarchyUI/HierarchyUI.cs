using CEngine;
using Editor<PERSON>;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Clicegf;
namespace CrossEditor
{
    public class HierarchyUI : DockingUI
    {
        const string TempPath = "/Contents/Prefab/.Temp";
        static HierarchyUI _Instance = new HierarchyUI();

        EditorUI.Texture _TextureIconRoot;
        EditorUI.Texture _TextureIconEntity;
        EditorUI.Texture _TextureIconMesh;
        EditorUI.Texture _TextureIconCamera;
        EditorUI.Texture _TextureIconLandscape;

        EditorUI.Texture _TextureIconReflectionProbe_Box;
        EditorUI.Texture _TextureIconReflectionProbe_Sphere;
        EditorUI.Texture _TextureIconPostProcessVolume;

        EditorUI.Texture _TextureIconSkyAtmosphere;
        EditorUI.Texture _TextureIconAtmosphericFog;
        EditorUI.Texture _TextureIconVolumetricCloud;

        EditorUI.Texture _TextureIconDirectionalLight;
        EditorUI.Texture _TextureIconPointLight;
        EditorUI.Texture _TextureIconSpotLight;
        EditorUI.Texture _TextureIconRectLight;
        EditorUI.Texture _TextureIconSkyLight;

        EditorUI.Texture _TextureIconFoldedFolder;
        EditorUI.Texture _TextureIconExpandedFolder;
        EditorUI.Texture _TextureEntityVisible;
        EditorUI.Texture _TextureEntityHidden;

        Panel _Container;
        OperationBarUI _OperationBarUI;
        Button _ButtonCreate;
        SearchUI _SearchUI;
        Tree _Tree;
        Panel _VisibilityPanel;
        SceneBase _CurScene;
        int _itemCount = 0;
        List<Entity> _EntitiesCopied;
        List<string> _EntitiesCopiedPath;
        Entity _EntityDragged;
        List<Entity> _EntitiesDragged;
        Entity _CurPilotEntity;

        bool _bSearchMode;
        List<Entity> _SelectedEntitiesInSearchMode;

        Dictionary<TreeItem, EditorUI.Texture> _TreeItemIconMap;

        bool _bNotToRecordSelection;
        bool _bShowEditorEntities;

        List<Entity> PackParentEntities = new List<Entity>();
        public static HierarchyUI GetInstance()
        {
            return _Instance;
        }

        HierarchyUI()
        {
            _EntitiesCopied = new List<Entity>();
            _EntitiesCopiedPath = new List<string>();
            _bSearchMode = false;
            _SelectedEntitiesInSearchMode = null;
            _TreeItemIconMap = new Dictionary<TreeItem, EditorUI.Texture>();
            _bNotToRecordSelection = false;
            _bShowEditorEntities = false;
        }

        public bool Initialize()
        {
            _TextureIconRoot = UIManager.LoadUIImage("Editor/Icons/AssetIcon_16/Root_16.png");
            _TextureIconEntity = UIManager.LoadUIImage("Editor/Icons/AssetIcon_16/Entity_16.png");
            _TextureIconMesh = UIManager.LoadUIImage("Editor/Icons/AssetIcon_16/Mesh_16.png");
            _TextureIconCamera = UIManager.LoadUIImage("Editor/Icons/AssetIcon_16/Camera_16.png");
            _TextureIconLandscape = UIManager.LoadUIImage("Editor/Icons/AssetIcon_16/Landscape_16.png");

            _TextureIconReflectionProbe_Box = UIManager.LoadUIImage("Editor/Icons/AssetIcon_16/ReflectionProbe_Box_16.png");
            _TextureIconReflectionProbe_Sphere = UIManager.LoadUIImage("Editor/Icons/AssetIcon_16/ReflectionProbe_Sphere_16.png");
            _TextureIconPostProcessVolume = UIManager.LoadUIImage("Editor/Icons/AssetIcon_16/PostProcessVolume_16.png");

            _TextureIconSkyAtmosphere = UIManager.LoadUIImage("Editor/Icons/AssetIcon_16/SkyAtmosphere_16.png");
            _TextureIconAtmosphericFog = UIManager.LoadUIImage("Editor/Icons/AssetIcon_16/AtmosphericFog_16.png");
            _TextureIconVolumetricCloud = UIManager.LoadUIImage("Editor/Icons/AssetIcon_16/VolumetricCloud_16.png");

            _TextureIconDirectionalLight = UIManager.LoadUIImage("Editor/Icons/AssetIcon_16/DirectionalLight_16.png");
            _TextureIconPointLight = UIManager.LoadUIImage("Editor/Icons/AssetIcon_16/PointLight_16.png");
            _TextureIconSpotLight = UIManager.LoadUIImage("Editor/Icons/AssetIcon_16/SpotLight_16.png");
            _TextureIconRectLight = UIManager.LoadUIImage("Editor/Icons/AssetIcon_16/RectLight_16.png");
            _TextureIconSkyLight = UIManager.LoadUIImage("Editor/Icons/AssetIcon_16/SkyLight_16.png");

            _TextureIconFoldedFolder = UIManager.LoadUIImage("Editor/Tree/Project/FoldedFolder.png");
            _TextureIconExpandedFolder = UIManager.LoadUIImage("Editor/Tree/Project/Folder.png");
            _TextureEntityVisible = UIManager.LoadUIImage("Editor/Icons/Common/Visible.png");
            _TextureEntityHidden = UIManager.LoadUIImage("Editor/Icons/Common/Hidden.png");

            _Container = new Panel();
            _Container.Initialize();
            _Container.SetSize(500, 300);

            _OperationBarUI = new OperationBarUI();
            _OperationBarUI.Initialize();
            _Container.AddChild(_OperationBarUI.GetPanelBar());

            _ButtonCreate = OperationBarUI.CreateTextButton("Create");
            _ButtonCreate.ClickedEvent += OnButtonCreateClicked;
            _OperationBarUI.AddLeft(_ButtonCreate);

            _SearchUI = new SearchUI();
            _SearchUI.Initialize();
            _SearchUI.SearchEvent += OnSearchUISearch;
            _SearchUI.CancelEvent += OnSearchUICancel;
            Panel PanelBack = _SearchUI.GetPanelBack();
            PanelBack.SetPosition(0, 2, 130, 20);
            PanelBack.SetBackgroundColor(OperationBarUI.BAR_COLOR);
            _OperationBarUI.AddRight(PanelBack);

            _OperationBarUI.Refresh();

            _Tree = new Tree();
            _Tree.Initialize();
            _Tree.SetEnableDragDrop(true);
            _Tree.SetEnableRename(true);
            _Tree.SetCanSelectNothing(true);
            _Tree.SetEnableMultipleSelection(true);
            _Tree.SetSelectOnMouseUp(true);
            _Tree.RightMouseUpEvent += OnTreeRightMouseUp;
            _Tree.KeyDownEvent += OnTreeKeyDown;
            _Tree.ItemOpenedEvent += OnTreeItemOpened;
            _Tree.ItemSelectedEvent += OnTreeItemSelected;
            _Tree.ItemRenameEvent += OnTreeItemRename;
            _Tree.ItemExpandedEvent += OnTreeItemExpanded;
            _Tree.ItemFoldedEvent += OnTreeItemFolded;
            _Tree.ItemPaintEvent += OnTreeItemPaint;
            _Tree.GetVScroll().ScrolledEvent += OnTreeScrolled;
            _Container.AddChild(_Tree);

            _VisibilityPanel = new Panel();
            _VisibilityPanel.Initialize();
            _VisibilityPanel.MouseMoveEvent += OnVisibilityPanelMouseMove;
            _Container.AddChild(_VisibilityPanel);

            _Container.PositionChangedEvent += OnContainerPositionChanged;

            DragDropManager DragDropManager = DragDropManager.GetInstance();
            DragDropManager.DragBeginEvent += OnDragDropManagerDragBegin;
            DragDropManager.DragMoveEvent += OnDragDropManagerDragMove;
            DragDropManager.DragEndEvent += OnDragDropManagerDragEnd;
            DragDropManager.DragClearEvent += OnDragDropManagerDragClear;
            DragDropManager.DragCancelEvent += OnDragDropManagerDragCancel;

            base.Initialize("Entities", _Container);

            return true;
        }

        private void OnContainerPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            int Width = Sender.GetWidth();
            int Height = Sender.GetHeight();

            _OperationBarUI.GetPanelBar().SetWidth(Width);
            int BarHeight = _OperationBarUI.GetPanelBar().GetHeight();
            _Tree.SetPosition(0, BarHeight, Width, Height - BarHeight);
            VScroll VScroll = _Tree.GetVScroll();
            _VisibilityPanel.SetPosition(Width - 20 - (VScroll.GetVisible() ? 20 : 0), BarHeight, 20, Height - BarHeight);
        }

        private void OnTreeScrolled(Scroll Sender)
        {
            int Y = _Tree.GetBaseY();
            for (int i = 0; i < _VisibilityPanel.GetChildCount(); ++i)
            {
                Panel Child = _VisibilityPanel.GetChild(i) as Panel;
                if (Child.GetVisible())
                {
                    Child.SetY(Y);
                    Y += 20;
                }
            }
        }

        public void Update()
        {
            if (_CurScene == null)
            {
                return;
            }

            bool bDirty = _CurScene.GetDirty();

            if (_CurScene is GameScene)
            {
                bDirty = true;
            }

            TreeItem RootItem = _Tree.GetRootItem();
            if (bDirty)
            {
                RootItem.SetExtraText("*");
            }
            else
            {
                RootItem.SetExtraText(null);
            }
        }

        public Tree GetTree()
        {
            return _Tree;
        }

        public void BindScene(SceneBase NewScene)
        {
            ExitSearchMode();
            _CurScene = NewScene;
            UpdateHierarchy();
            SelectEntities(GetSelectedEntities());
        }

        public SceneBase GetScene()
        {
            return _CurScene;
        }

        void OnButtonCreateClicked(Button Sender)
        {
            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();
            BulidCreateEntityMenu(MenuContextMenu, "ButtonCreate");

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);
        }

        #region Search Event

        void EnterSearchMode()
        {
            _bSearchMode = true;
        }

        public void ExitSearchMode()
        {
            if (_bSearchMode)
            {
                _bSearchMode = false;
                _SearchUI.ClearSearchPattern();
                _SelectedEntitiesInSearchMode = GetSelectedEntitiesFromTree();
            }
        }

        void DoCancelSearch()
        {
            if (_bSearchMode)
            {
                ExitSearchMode();
                UpdateHierarchy();
                _SearchUI.GetEdit().SetFocus();
            }
        }

        void DoSearch()
        {
            string SearchPattern = _SearchUI.GetSearchPattern();
            if (SearchPattern != "")
            {
                World World = _CurScene.GetWorld();
                Entity Root = null;
                if (World != null)
                {
                    Root = World.Root;
                    Root.ClearSearch();
                    bool bMatchWholeComponentName = StringHelper.IgnoreCaseEqual(SearchPattern, "Light");
                    Root.Search(SearchPattern, bMatchWholeComponentName);
                }
                EnterSearchMode();
            }
            else
            {
                ExitSearchMode();
            }
            UpdateHierarchy();
        }

        void OnSearchUISearch(SearchUI Sender, string Pattern)
        {
            DoSearch();
        }

        void OnSearchUICancel(SearchUI Sender)
        {
            DoCancelSearch();
        }

        #endregion

        public void UpdateHierarchy()
        {
            bool bNotToRecordSelection = _bNotToRecordSelection;
            _bNotToRecordSelection = true;
            ShowHierarchy();
            if (_SelectedEntitiesInSearchMode != null)
            {
                SelectEntities(_SelectedEntitiesInSearchMode);
                _SelectedEntitiesInSearchMode = null;
            }
            UpdateVisibilityButton();
            _bNotToRecordSelection = bNotToRecordSelection;
        }

        public void ShowHierarchy()
        {
            if (_CurScene == null)
            {
                return;
            }
            TreeItem RootItem = _Tree.GetRootItem();
            RootItem.ClearChildren();
            _VisibilityPanel.ClearChildren();
            _TreeItemIconMap.Clear();
            Entity Root = _CurScene.GetRoot();
            ShowHierarchy(RootItem, Root);
        }

        public void ShowHierarchy(TreeItem TreeItem, Entity Entity)
        {
            UpdateIcon(TreeItem, Entity);
            TreeItem.SetFolder(true);
            if (Entity != null)
            {
                TreeItem.SetExpanded(Entity.bExpand);
            }
            else
            {
                TreeItem.SetExpanded(true);
            }

            if (Entity == null)
            {
                TreeItem.SetText("Root");
            }
            else
            {
                TreeItem.SetText(Entity.GetName());
                TreeItem.SetTagObject(Entity);
                UpdateTextColor(TreeItem, Entity);
                Panel VisibilityButton = CreateVisibilityButton();
                VisibilityButton.SetTagObject(TreeItem);
                _VisibilityPanel.AddChild(VisibilityButton);

                foreach (Entity ChildEntity in Entity.Children)
                {
                    if (_bShowEditorEntities == false && _CurScene.GetEditorEntities().Contains(ChildEntity.EntityID))
                    {
                        continue;
                    }
                    if (_bSearchMode == false || ChildEntity.bMatch || ChildEntity.bChildMatch)
                    {
                        TreeItem ChildTreeItem = _Tree.CreateItem();
                        TreeItem.AddChild(ChildTreeItem);
                        _itemCount++;
                        ShowHierarchy(ChildTreeItem, ChildEntity);
                    }
                }
            }
        }

        public void UpdateTextColor(TreeItem TreeItem, Entity Entity)
        {
            bool IsHide = !Entity.Enable;

            if (IsHide)
            {
                TreeItem.SetTextColor(Color.FromRGBA(128, 128, 128, 255));
            }
            else if (Entity.IsPrefabInstance())
            {
                if (Entity.IsPrefabInstanceRoot())
                {
                    TreeItem.SetTextColor(Color.FromRGBA(248, 222, 126, 255));
                }
                else if (Entity.IsInheritPrefabInstance())
                {
                    TreeItem.SetTextColor(Color.FromRGBA(30, 99, 210, 255));
                }
                else
                {
                    TreeItem.SetTextColor(Color.FromRGBA(80, 200, 120, 255));
                }
            }
            else
            {
                TreeItem.SetTextColor(Color.EDITOR_UI_LIGHT_TEXT_COLOR);
            }
        }

        public void UpdateIcon(TreeItem TreeItem, Entity Entity)
        {
            if (Entity != null && Entity.bFolder)
            {
                TreeItem.SetImageFolded(_TextureIconFoldedFolder);
                TreeItem.SetImageExpanded(_TextureIconExpandedFolder);
                return;
            }

            EditorUI.Texture Icon = _TextureIconEntity;
            if (Entity != null)
            {
                if (_Tree.GetRootItem() == TreeItem)
                {
                    Icon = _TextureIconRoot;
                }
                else if (Entity.HasComponent(typeof(Light)))
                {
                    Light LightComponent = Entity.GetComponent(typeof(Light)) as Light;
                    switch (LightComponent.mLight.mType)
                    {
                        case LightType.Spot:
                            Icon = _TextureIconSpotLight;
                            break;
                        case LightType.Directional:
                            Icon = _TextureIconDirectionalLight;
                            break;
                        case LightType.Rect:
                            Icon = _TextureIconRectLight;
                            break;
                        case LightType.Point:
                            Icon = _TextureIconPointLight;
                            break;
                    }
                }
                else if (Entity.HasComponent(typeof(Camera)))
                {
                    Icon = _TextureIconCamera;
                }
                else if (Entity.HasComponent(typeof(PostProcessVolumeComponent)))
                {
                    Icon = _TextureIconPostProcessVolume;
                }
                else if (Entity.HasComponent(typeof(ReflectionProbeComponent)))
                {
                    ReflectionProbeComponent Component = Entity.GetComponent(typeof(ReflectionProbeComponent)) as ReflectionProbeComponent;
                    switch (Component.ReflectionProbe.mRefleProbeShapeType)
                    {
                        case ReflectionProbeShapeType.Sphere:
                            Icon = _TextureIconReflectionProbe_Sphere;
                            break;
                        case ReflectionProbeShapeType.Box:
                            Icon = _TextureIconReflectionProbe_Box;
                            break;
                    }
                }
                else if (Entity.HasComponent(typeof(SkyAtmosphereComponent)))
                {
                    Icon = _TextureIconSkyAtmosphere;
                }
                else if (Entity.HasComponent(typeof(SkyLightComponent)))
                {
                    Icon = _TextureIconSkyLight;
                }
                else if (Entity.HasComponent(typeof(CloudComponent)))
                {
                    Icon = _TextureIconVolumetricCloud;
                }
                else if (Entity.HasComponent(typeof(ModelComponent)))
                {
                    Icon = _TextureIconMesh;
                }
            }
            _TreeItemIconMap.Add(TreeItem, Icon);
        }

        public Entity GetSelectedEntity()
        {
            List<Entity> SelectedEntities = GetSelectedEntities();
            if (SelectedEntities.Count > 0)
            {
                return SelectedEntities[0];
            }
            return null;
        }

        public List<Entity> GetSelectedEntities()
        {
            List<Entity> SelectionList = new List<Entity>();
            if (_CurScene != null)
            {
                SelectionList = _CurScene.GetSelectionList().Clone();
            }
            // Try get selected entities from tree
            if (SelectionList.Count == 0)
            {
                SelectionList = GetSelectedEntitiesFromTree();
            }
            return SelectionList;
        }

        public List<Entity> GetSelectedEntitiesFromTree()
        {
            List<Entity> SelectionList = new List<Entity>();
            List<TreeItem> TreeItems = _Tree.GetSelectedItems();
            foreach (TreeItem Item in TreeItems)
            {
                Entity Entity = Item.GetTagObject() as Entity;
                if (Entity != null)
                {
                    SelectionList.Add(Entity);
                }
            }
            return SelectionList;
        }

        public Entity GetCurPiolotEntity()
        {
            return _CurPilotEntity;
        }

        public void SetCurPiolotEntity(Entity Entity)
        {
            _CurPilotEntity = Entity;
        }


        public List<Entity> GetRootChildren()
        {
            List<Entity> ret = new List<Entity>(); ;
            Entity Root = _CurScene.GetRoot();
            foreach (Entity ChildEntity in Root.Children)
            {
                ret.Add(ChildEntity);
            }
            return ret;
        }

        Entity GetParentEntity(bool bForceRoot)
        {
            Entity ParentEntity = GetSelectedEntity();
            if (bForceRoot || ParentEntity == null)
            {
                ParentEntity = _CurScene.GetRoot();
            }
            return ParentEntity;
        }

        public void ClearEntitySelectionNoEvent()
        {
            _Tree.SelectItemNoEvent(null);
        }

        public void AddEntitySelectionNoEvent(Entity Entity)
        {
            TreeItem TreeItem = _Tree.FindItemByTagObject(Entity);
            _Tree.AddSelectItemNoEvent(TreeItem);

            //UpdateVisibilityButton();
        }

        public void SelectEntity(Entity Entity, bool bNoEvent = false)
        {
            SelectEntities(new List<Entity> { Entity }, bNoEvent);
        }

        public void SelectEntities(List<Entity> Entities, bool bNoEvent = false)
        {
            bool bNotToRecordSelection = _bNotToRecordSelection;
            _bNotToRecordSelection = true;
            if (bNoEvent)
            {
                _Tree.SelectItemNoEvent(null);
            }
            else
            {
                _Tree.SelectItem(null);
            }
            foreach (Entity Entity in Entities)
            {
                TreeItem TreeItem = _Tree.FindItemByTagObject(Entity);
                if (bNoEvent)
                {
                    _Tree.AddSelectItemNoEvent(TreeItem);
                }
                else
                {
                    _Tree.AddSelectItem(TreeItem);
                }
            }
            _bNotToRecordSelection = bNotToRecordSelection;
        }

        public bool ShouldForceRoot(MenuItem Sender)
        {
            string TagString = Sender.GetTagString();
            return TagString == "ButtonCreate";
        }

        void ChangeHierarchy(Entity EntityDragged, Entity EntityToDrop, int InsertPosition, EditOperation_ChangeParentEntity_Entities EditOperation_ChangeParentEntity_Entities)
        {
            if (EntityDragged == EntityToDrop)
            {
                return;
            }
            if (EntityToDrop.IsChildOf_Recursive(EntityDragged))
            {
                return;
            }
            int OldInsertIndex = EntityDragged.CalculateIndex();
            if (EntityDragged.Parent == EntityToDrop)
            {
                if (OldInsertIndex < InsertPosition)
                {
                    InsertPosition--;
                }
            }
            Entity EntityOldParent = EntityDragged.Parent;
            EntityOldParent.RemoveChildEntity(EntityDragged);
            EntityToDrop.InsertChildEntity(EntityDragged, InsertPosition);
            //Runtime.World_TransformJoint(EntityToDrop.World._World, EntityDragged.EntityID, EntityToDrop.EntityID);
            Entity Entity = null;
            if (InsertPosition > 0)
            {
                Entity = EntityToDrop.Children[InsertPosition - 1];
            }
            ulong EntityID = ulong.MaxValue;
            if (Entity != null)
            {
                EntityID = Entity.EntityID;
            }
            IntPtr World = EntityToDrop.World._World;
            // found this disjoint cause wrong block file (json file contain rootParent, causing being wrongly identified as rootEntity)
            //TransformSystemG.DisjointParent(World, EntityDragged.EntityID);
            TransformSystemG.Joint(World, EntityDragged.EntityID, EntityToDrop.EntityID, EntityID, true);
            if (Entity == null)
            {
                TransformSystemG.SetEntityFirstChild(World, EntityToDrop.EntityID, EntityDragged.EntityID);
            }

            Transform Transform = EntityDragged.GetTransformComponent();
            if (Transform != null)
            {
                Transform.SyncDataFromEngine();
            }
            EditorScene.GetInstance().SetDirty();

            if (EditOperation_ChangeParentEntity_Entities != null)
            {
                EditOperation_ChangeParentEntity_Entities.AddChangeParentEntityItem(EntityOldParent, EntityToDrop, EntityDragged, OldInsertIndex, InsertPosition);
            }
            else
            {
                EditOperation_ChangeParentEntity EditOperation_ChangeParentEntity = new EditOperation_ChangeParentEntity(EntityOldParent, EntityToDrop, EntityDragged, OldInsertIndex, InsertPosition);
                EditOperationManager.GetInstance().AddOperation(EditOperation_ChangeParentEntity);
            }

            UpdateHierarchy();
            SelectEntity(EntityDragged);
        }

        void OnTreeRightMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (!_CurScene.IsEditable())
            {
                return;
            }
            if (Sender.IsPointIn(MouseX, MouseY) == false)
            {
                return;
            }
            ShowContextMenu(MouseX, MouseY);
            bContinue = false;
        }

        void OnTreeKeyDown(Control Sender, Key Key, ref bool bContinue)
        {
            if (!_CurScene.IsEditable() || IsFocused() == false)
            {
                return;
            }
            Device Device = GetDevice();
            ShortcutConfig ShortcutConfig = ShortcutConfig.GetInstance();
            if (Key == Key.Delete)
            {
                if (Device.IsNoneModifiersDown() || Device.IsShiftDownOnly())
                {
                    DoDelete();
                    bContinue = false;
                }
            }
            else if (Key == Key.F2)
            {
                if (Device.IsNoneModifiersDown())
                {
                    DoRename();
                    bContinue = false;
                }
            }
            else if (Key == Key.Escape)
            {
                if (Device.IsNoneModifiersDown())
                {
                    DoCancelSearch();
                    bContinue = false;
                }
            }
            else if (ShortcutConfig.IsShortcutDown(GetDevice(), "EditSelectAll", Key))
            {
                SelectAllEntity();
                bContinue = false;
            }
            else if (ShortcutConfig.IsShortcutDown(GetDevice(), "PackOrUnPack", Key))
            {
                if (_Tree.GetSelectedItems().Count > 1)
                {
                    OnMenuItemPackEntityClicked(null);
                }
                else if (_Tree.GetSelectedItems().Count == 1)
                {
                    OnMenuItemUnPackEntityClicked(null);
                }
            }
            else if (ShortcutConfig.IsShortcutDown(GetDevice(), "EntityFallToGroundAsEntirety", Key))
            {
                OnMenuItemEntityFallToGroundAsEntiretyClicked(null);
            }
            else if (ShortcutConfig.IsShortcutDown(GetDevice(), "EntityFallToGroundAsMesh", Key))
            {
                OnMenuItemEntityFallToGroundAsMeshClicked(null);
            }
            else if (ShortcutConfig.IsShortcutDown(GetDevice(), "InstanceFallToGround", Key))
            {
                OnMenuItemInstanceFallToGroundClicked(null);
            }
            else if (ShortcutConfig.IsShortcutDown(GetDevice(), "EntityPiolot", Key))
            {
                Entity Entity = GetSelectedEntity();
                if (Entity != null)
                {
                    _CurPilotEntity = Entity;
                }
                EditorSceneUI.GetInstance().ActivePiolotRelated(true, _CurPilotEntity.GetName());
            }
        }

        public void LocateToCurrentItem()
        {
            _CurScene.FocusOnSelectedEntities();
        }

        void OnTreeItemOpened(Tree Sender, TreeItem TreeItem)
        {
            LocateToCurrentItem();
        }

        void MoveCameraToEntity()
        {
            TreeItem TreeItem = _Tree.GetSelectedItem();
            if (TreeItem != null)
            {
                Entity Entity = (Entity)TreeItem.GetTagObject();
                Transform Transform = Entity.GetTransformComponent();
                Transform CameraTransform = _CurScene.GetCameraEntityTransform();

                EditOperation_ChangeTRS EditOperation = new EditOperation_ChangeTRS(_CurScene.GetCameraEntity(), CameraTransform.GetWorldTranslation(), Transform.GetWorldTranslation(),
                    CameraTransform.GetWorldRotation(), Transform.GetWorldRotation());
                EditOperationManager.GetInstance().AddOperation(EditOperation);
                EditOperation.Redo();
            }
        }

        void MoveEntityToCamera()
        {
            TreeItem TreeItem = _Tree.GetSelectedItem();
            if (TreeItem != null)
            {
                Entity Entity = (Entity)TreeItem.GetTagObject();
                Transform Transform = Entity.GetTransformComponent();
                Transform CameraTransform = _CurScene.GetCameraEntityTransform();

                EditOperation_ChangeTRS EditOperation = new EditOperation_ChangeTRS(Entity, Transform.GetWorldTranslation(), CameraTransform.GetWorldTranslation(),
                    Transform.GetWorldRotation(), CameraTransform.GetWorldRotation());
                EditOperationManager.GetInstance().AddOperation(EditOperation);
                EditOperation.Redo();
            }
        }

        public void DropToPointer()
        {
            Entity SelectEntity = GetSelectedEntity();
            if (SelectEntity == null)
                return;
            Transform EntityTransform = SelectEntity.GetTransformComponent();
            Double3 CurrentPosition = EntityTransform.GetWorldTranslation();
            Double3 DropLocation = _CurScene.GetSceneUI().CalculateDropLocation().ToDouble3();
            Double3 Delta = DropLocation.Subtract(CurrentPosition);

            EditOperationManager.GetInstance().BeginRecordCompoundOperation();
            foreach (Entity Entity in _CurScene.ToTopEntities(GetSelectedEntities()))
            {
                Transform Transform = Entity.GetTransformComponent();
                Double3 OldTranslation = Transform.GetWorldTranslation();
                Double3 NewTranslation = OldTranslation.Add(Delta);
                EditOperation_ChangeTRS EditOperation = new EditOperation_ChangeTRS(Entity, OldTranslation, NewTranslation);
                EditOperationManager.GetInstance().AddOperation(EditOperation);
                EditOperation.Redo();
            }
            EditOperationManager.GetInstance().EndRecordCompoundOperation();
        }

        void OnTreeItemSelected(Tree Sender, TreeItem TreeItem)
        {
            GetUIManager().SetFocusControl(_Tree);
            List<Entity> SelectedEntities = GetSelectedEntitiesFromTree();

            EditOperation_SelectEntities EditOperation_SelectEntities = null;
            if (_CurScene is EditorScene)
            {
                EditOperation_SelectEntities = (_CurScene as EditorScene).BeginSelectEntities();
            }
            _CurScene.SetSelectionList(SelectedEntities);
            if (EditOperation_SelectEntities != null)
            {
                (_CurScene as EditorScene).EndSelectEntities(EditOperation_SelectEntities);
            }

            UpdateVisibilityButton();
        }

        void OnTreeItemRename(Tree Sender, string NewName, string OldName, TreeItem TreeItem)
        {
            if (!_CurScene.IsEditable())
            {
                return;
            }
            if (NewName == "")
            {
                return;
            }
            if (NewName == OldName)
            {
                return;
            }
            // Check if there has the same name in it's level or not
            TreeItem Parent = TreeItem.GetParent();
            if (Parent != null)
            {
                foreach (TreeItem Child in Parent.GetChildList())
                {
                    if (Child.GetText() == NewName)
                    {
                        return;
                    }
                }
            }
            Entity Entity = (Entity)TreeItem.GetTagObject();
            Entity.SetName(NewName);
            EditorScene.GetInstance().SetDirty();

            EditOperation_RenameEntity EditOperation = new EditOperation_RenameEntity(Entity, OldName, NewName);
            EditOperationManager.GetInstance().AddOperation(EditOperation);

            UpdateHierarchy();
            SelectEntity(Entity);
        }

        void OnTreeItemExpanded(Tree Sender, TreeItem TreeItem)
        {
            Entity Entity = (Entity)TreeItem.GetTagObject();
            Entity.SetBExpand(true);

            _CurScene.SetDirty(true);
            UpdateVisibilityButton();
        }

        void OnTreeItemFolded(Tree Sender, TreeItem TreeItem)
        {
            Entity Entity = (Entity)TreeItem.GetTagObject();
            Entity.SetBExpand(false);

            _CurScene.SetDirty(true);
            UpdateVisibilityButton();
        }

        void OnTreeItemPaint(Tree Sender, TreeItem TreeItem, int X, int Y, int Indent, int ScreenX, int ScreenY)
        {
            EditorUI.Texture Icon;
            if (_TreeItemIconMap.TryGetValue(TreeItem, out Icon))
            {
                GetUIManager().GetGraphics2D().DrawTexture(Icon, Color.White, X + Indent + 20, Y + 1, 18, 18);
            }
        }

        public bool IsDraggingEntity()
        {
            return _EntityDragged != null;
        }

        public Entity GetEntityDragged()
        {
            return _EntityDragged;
        }

        public bool IsDraggingCreatePrefab()
        {
            return _CurScene is EditorScene && _EntityDragged != null;
        }

        public bool IsPrefabEnity(Entity entity)
        {
            if (_CurScene is PrefabScene)
                return entity.IsInheritPrefabInstance();
            return entity.IsPrefabInstance();
        }

        void OnDragDropManagerDragBegin(DragDropManager Sender, UIManager UIManager, int OriginalMouseX, int OriginalMouseY, ref bool bDragBegin, ref string ImageFilename)
        {
            if (UIManager != GetUIManager())
            {
                return;
            }
            if (!_CurScene.IsEditable())
            {
                return;
            }
            if (_Tree.IsPointIn_Recursively(OriginalMouseX, OriginalMouseY))
            {
                TreeItemHitTest HitTest = _Tree.HitTest(OriginalMouseX, OriginalMouseY);
                if (HitTest.HitResult != TreeItemHitResult.Nothing)
                {
                    TreeItem TreeItemDrag = HitTest.ItemHit;
                    if (TreeItemDrag != null)
                    {
                        Entity Entity = (Entity)TreeItemDrag.GetTagObject();
                        if (Entity.IsRoot() == false)
                        {
                            bDragBegin = true;
                            _EntityDragged = Entity;
                            _EntitiesDragged = GetSelectedEntities();
                        }
                    }
                }
            }
        }

        void OnDragDropManagerDragMove(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY)
        {
            if (!_CurScene.IsEditable())
            {
                return;
            }
            ProjectUI ProjectUI = ProjectUI.GetInstance();
            TreeDropInfo DropInfo = null;
            if (IsDraggingEntity() || ProjectUI.IsPathesDragging())
            {
                if (UIManager == GetUIManager() && _Tree.IsPointIn(MouseX, MouseY))
                {
                    DropInfo = new TreeDropInfo();
                    if (IsDraggingEntity())
                    {
                        DropInfo.bDetailed = true;
                    }
                    _Tree.HitTest(MouseX, MouseY, false, DropInfo);
                    //TreeItem TreeItemDrop = DropInfo.InsertParent;
                    //if (TreeItemDrop != null)
                    //{
                    //    Entity EntityToDrop = (Entity)TreeItemDrop.GetTagObject();
                    //    if (EntityToDrop.IsRoot())
                    //    {
                    //        DropInfo = null;
                    //    }
                    //}
                }
            }
            _Tree.SetDropInfo(DropInfo);
        }

        void OnDragDropManagerDragEnd(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY, ref bool bContinue)
        {
            if (UIManager != GetUIManager())
            {
                return;
            }
            if (!_CurScene.IsEditable())
            {
                return;
            }
            if (!IsDockingCardActive())
                return;

            if (IsDraggingEntity())
            {
                if (_Tree.IsPointIn(MouseX, MouseY))
                {
                    TreeDropInfo DropInfo = new TreeDropInfo();
                    DropInfo.bDetailed = true;
                    _Tree.HitTest(MouseX, MouseY, false, DropInfo);
                    TreeItem TreeItemDrop = DropInfo.InsertParent;
                    if (TreeItemDrop != null)
                    {
                        EditOperation_ChangeParentEntity_Entities EditOperation_ChangeParentEntity_Entities = new EditOperation_ChangeParentEntity_Entities();
                        Entity EntityToDrop = (Entity)TreeItemDrop.GetTagObject();
                        int Count = _EntitiesDragged.Count;
                        for (int i = Count - 1; i >= 0; i--)
                        {
                            Entity EntityDrag = _EntitiesDragged[i];
                            if (EntityToDrop != EntityDrag)
                            {
                                bool canChange = !EntityDrag.IsPrefabInstance() || !EntityDrag.IsInheritPrefabInstance() || EntityDrag.IsPrefabInstanceRoot();
                                canChange &= !EntityDrag.IsPrefabInstance() || EntityDrag.IsPrefabInstanceRoot() || EntityToDrop.GetPrefabId() == EntityDrag.GetPrefabId();
                                if (canChange)
                                {
                                    ChangeHierarchy(EntityDrag, EntityToDrop, DropInfo.InsertPosition, EditOperation_ChangeParentEntity_Entities);
                                }
                            }
                        }
                        EditOperationManager.GetInstance().AddOperation(EditOperation_ChangeParentEntity_Entities);
                    }
                }
            }
            ProjectUI ProjectUI = ProjectUI.GetInstance();
            if (ProjectUI.IsPathesDragging() && IsDockingCardActive())
            {
                if (_Tree.IsPointIn(MouseX, MouseY))
                {
                    TreeDropInfo DropInfo = new TreeDropInfo();
                    DropInfo.bDetailed = true;
                    _Tree.HitTest(MouseX, MouseY, false, DropInfo);
                    Entity EntityToDrop = null;
                    TreeItem TreeItemDrop = DropInfo.InsertParent;
                    if (TreeItemDrop != null)
                    {
                        EntityToDrop = (Entity)TreeItemDrop.GetTagObject();
                    }
                    else
                    {
                        EntityToDrop = _CurScene is PrefabScene ? (_CurScene as PrefabScene).GetPrefab() : _CurScene.GetRoot();
                    }
                    if (EntityToDrop != null)
                    {
                        _CurScene.GetSceneUI().DropPathes(EntityToDrop, false);
                    }
                }
            }
        }

        void OnDragDropManagerDragClear(DragDropManager Sender)
        {
            DragClearStates();
        }

        void OnDragDropManagerDragCancel(DragDropManager Sender)
        {
            DragClearStates();
        }

        void DragClearStates()
        {
            _EntityDragged = null;
            _EntitiesDragged = null;
            _Tree.SetDropInfo(null);
        }

        #region Build Menu

        public void BulidCreateEntityMenu(Menu Menu, string TagString)
        {
            MenuItem MenuItem_CreateEmpty = new MenuItem();
            MenuItem_CreateEmpty.SetText("Create Empty");
            MenuItem_CreateEmpty.SetTagString(TagString);
            MenuItem_CreateEmpty.ClickedEvent += OnMenuItemCreateEmptyClicked;

            MenuItem MenuItem_CreateFolder = new MenuItem();
            MenuItem_CreateFolder.SetText("Folder");
            MenuItem_CreateFolder.ClickedEvent += OnMenuItemCreateFolderClicked;

            MenuItem MenuItem_CreateEmptyChild = new MenuItem();
            MenuItem_CreateEmptyChild.SetText("Create Empty Child");
            MenuItem_CreateEmptyChild.SetTagString("CreateChild");
            MenuItem_CreateEmptyChild.ClickedEvent += OnMenuItemCreateEmptyClicked;

            MenuItem MenuItem_CreateCube = new MenuItem();
            MenuItem_CreateCube.SetText("Cube");
            MenuItem_CreateCube.SetTagString(TagString);
            MenuItem_CreateCube.ClickedEvent += OnMenuItemCreateCubeClicked;

            MenuItem MenuItem_CreateSphere = new MenuItem();
            MenuItem_CreateSphere.SetText("Sphere");
            MenuItem_CreateSphere.SetTagString(TagString);
            MenuItem_CreateSphere.ClickedEvent += OnMenuItemCreateSphereClicked;

            MenuItem MenuItem_CreateCapsule = new MenuItem();
            MenuItem_CreateCapsule.SetText("Capsule");
            MenuItem_CreateCapsule.SetTagString(TagString);
            MenuItem_CreateCapsule.ClickedEvent += OnMenuItemCreateCapsuleClicked;

            MenuItem MenuItem_CreateCylinder = new MenuItem();
            MenuItem_CreateCylinder.SetText("Cylinder");
            MenuItem_CreateCylinder.SetTagString(TagString);
            MenuItem_CreateCylinder.ClickedEvent += OnMenuItemCreateCylinderClicked;

            MenuItem MenuItem_CreateCone = new MenuItem();
            MenuItem_CreateCone.SetText("Cone");
            MenuItem_CreateCone.SetTagString(TagString);
            MenuItem_CreateCone.ClickedEvent += OnMenuItemCreateConeClicked;

            MenuItem MenuItem_CreatePlane = new MenuItem();
            MenuItem_CreatePlane.SetText("Plane");
            MenuItem_CreatePlane.SetTagString(TagString);
            MenuItem_CreatePlane.ClickedEvent += OnMenuItemCreatePlaneClicked;

            Menu Menu_3DObjects = new Menu(GetUIManager());
            Menu_3DObjects.Initialize();
            MenuItem MenuItem_3DObjects = new MenuItem();
            MenuItem_3DObjects.SetText("3D Objects");
            MenuItem_3DObjects.SetMenu(Menu_3DObjects);

            Menu_3DObjects.AddMenuItem(MenuItem_CreateCube);
            Menu_3DObjects.AddMenuItem(MenuItem_CreateSphere);
            Menu_3DObjects.AddMenuItem(MenuItem_CreateCapsule);
            Menu_3DObjects.AddMenuItem(MenuItem_CreateCylinder);
            Menu_3DObjects.AddMenuItem(MenuItem_CreateCone);
            Menu_3DObjects.AddMenuItem(MenuItem_CreatePlane);

            MenuItem MenuItem_CreateDirectionalLight = new MenuItem();
            MenuItem_CreateDirectionalLight.SetText("Directional Light");
            MenuItem_CreateDirectionalLight.SetTagString(TagString);
            MenuItem_CreateDirectionalLight.ClickedEvent += OnMenuItemCreateDirectionalLightClicked;

            MenuItem MenuItem_CreatePointLight = new MenuItem();
            MenuItem_CreatePointLight.SetText("Point Light");
            MenuItem_CreatePointLight.SetTagString(TagString);
            MenuItem_CreatePointLight.ClickedEvent += OnMenuItemCreatePointLightClicked;

            MenuItem MenuItem_CreateSpotLight = new MenuItem();
            MenuItem_CreateSpotLight.SetText("Spot Light");
            MenuItem_CreateSpotLight.SetTagString(TagString);
            MenuItem_CreateSpotLight.ClickedEvent += OnMenuItemCreateSpotLightClicked;

            MenuItem MenuItem_CreateRectLight = new MenuItem();
            MenuItem_CreateRectLight.SetText("Rect Light");
            MenuItem_CreateRectLight.SetTagString(TagString);
            MenuItem_CreateRectLight.ClickedEvent += OnMenuItemCreateRectLightClicked;

            MenuItem MenuItem_CreateSkyLight = new MenuItem();
            MenuItem_CreateSkyLight.SetText("Sky Light");
            MenuItem_CreateSkyLight.SetTagString(TagString);
            MenuItem_CreateSkyLight.ClickedEvent += OnMenuItemCreateSkyLightClicked;

            Menu Menu_Light = new Menu(GetUIManager());
            Menu_Light.Initialize();
            MenuItem MenuItem_Light = new MenuItem();
            MenuItem_Light.SetText("Light");
            MenuItem_Light.SetMenu(Menu_Light);

            Menu_Light.AddMenuItem(MenuItem_CreateDirectionalLight);
            Menu_Light.AddMenuItem(MenuItem_CreatePointLight);
            Menu_Light.AddMenuItem(MenuItem_CreateSpotLight);
            Menu_Light.AddMenuItem(MenuItem_CreateRectLight);
            Menu_Light.AddMenuItem(MenuItem_CreateSkyLight);

            MenuItem MenuItem_CreateTerrain = new MenuItem();
            MenuItem_CreateTerrain.SetText("Terrain");
            MenuItem_CreateTerrain.SetTagString(TagString);
            MenuItem_CreateTerrain.ClickedEvent += OnMenuItemCreateTerrainClicked;

            MenuItem MenuItem_CreateDecal = new MenuItem();
            MenuItem_CreateDecal.SetText("Decal");
            MenuItem_CreateDecal.SetTagString(TagString);
            MenuItem_CreateDecal.ClickedEvent += OnMenuItemCreateDecalClicked;

            MenuItem MenuItem_CreateParticleSystem = new MenuItem();
            MenuItem_CreateParticleSystem.SetText("ParticleSystem");
            MenuItem_CreateParticleSystem.SetTagString(TagString);
            MenuItem_CreateParticleSystem.ClickedEvent += OnMenuItemCreateParticleSystemClicked;

            MenuItem MenuItem_CreatePostProcessVolume = new MenuItem();
            MenuItem_CreatePostProcessVolume.SetText("PostProcessVolume");
            MenuItem_CreatePostProcessVolume.SetTagString(TagString);
            MenuItem_CreatePostProcessVolume.ClickedEvent += OnMenuItemCreatePostProcessVolumeClicked;

            MenuItem MenuItem_CreateVolumeTrigger = new MenuItem();
            MenuItem_CreateVolumeTrigger.SetText("VolumeTrigger");
            MenuItem_CreateVolumeTrigger.SetTagString(TagString);
            MenuItem_CreateVolumeTrigger.ClickedEvent += OnMenuItemCreateVolumeTriggerClicked;

            MenuItem MenuItem_CreateCloud = new MenuItem();
            MenuItem_CreateCloud.SetText("Cloud");
            MenuItem_CreateCloud.SetTagString(TagString);
            MenuItem_CreateCloud.ClickedEvent += OnMenuItemCreateCloudClicked;

            MenuItem MenuItem_CreateReflectionProbe = new MenuItem();
            MenuItem_CreateReflectionProbe.SetText("ReflectionProbe");
            MenuItem_CreateReflectionProbe.SetTagString(TagString);
            MenuItem_CreateReflectionProbe.ClickedEvent += OnMenuItemCreateReflectionProbeClicked;

            MenuItem MenuItem_CreateCamera = new MenuItem();
            MenuItem_CreateCamera.SetText("Camera");
            MenuItem_CreateCamera.SetTagString(TagString);
            MenuItem_CreateCamera.ClickedEvent += OnMenuItemCreateCameraClicked;

            MenuItem MenuItem_CreateVRView = new MenuItem();
            MenuItem_CreateVRView.SetText("VRView");
            MenuItem_CreateVRView.SetTagString(TagString);
            MenuItem_CreateVRView.ClickedEvent += OnMenuItemCreateVRViewClicked;

            MenuItem MenuItem_CreateScript = new MenuItem();
            MenuItem_CreateScript.SetText("Script");
            MenuItem_CreateScript.SetTagString(TagString);
            MenuItem_CreateScript.ClickedEvent += OnMenuItemCreateScriptClicked;

            Menu.AddMenuItem(MenuItem_CreateEmpty);
            Menu.AddMenuItem(MenuItem_CreateFolder);
            if (TagString == "MainMenu" || TagString == "ButtonCreate")
            {
                Menu.AddMenuItem(MenuItem_CreateEmptyChild);
            }
            Menu.AddMenuItem(MenuItem_3DObjects);
            Menu.AddMenuItem(MenuItem_Light);
            Menu.AddMenuItem(MenuItem_CreateScript);
            //Menu.AddMenuItem(MenuItem_CreateTerrain);
            Menu.AddMenuItem(MenuItem_CreateDecal);
            Menu.AddMenuItem(MenuItem_CreateCamera);
            Menu.AddMenuItem(MenuItem_CreateReflectionProbe);
            Menu.AddMenuItem(MenuItem_CreateParticleSystem);
            Menu.AddMenuItem(MenuItem_CreatePostProcessVolume);
            Menu.AddMenuItem(MenuItem_CreateVolumeTrigger);
            Menu.AddMenuItem(MenuItem_CreateCloud);
            Menu.AddMenuItem(MenuItem_CreateVRView);
        }

        public void BuildPrefabMenu(Menu Root, string TagString)
        {
            TreeItem SelectedItem = _Tree.GetSelectedItem();
            if (SelectedItem == null) return;

            Entity entity = (Entity)SelectedItem.GetTagObject();
            if (entity == null) return;

            Menu Menu_Prefab = new Menu(GetUIManager());
            Menu_Prefab.Initialize();
            MenuItem MenuItem_Prefab = new MenuItem();
            MenuItem_Prefab.SetText("Prefab");
            MenuItem_Prefab.SetMenu(Menu_Prefab);

            bool isShow = false;

            if (_CurScene is EditorScene)
            {
                MenuItem MenuItem_CreatePrefab = new MenuItem();
                MenuItem_CreatePrefab.SetText("Create Prefab");
                MenuItem_CreatePrefab.ClickedEvent += OnMenuItemCreatePrefab;
                Menu_Prefab.AddMenuItem(MenuItem_CreatePrefab);

                if (entity.IsPrefabInstance())
                {
                    MenuItem MenuItem_OpenPrefab = new MenuItem();
                    MenuItem_OpenPrefab.SetText("Open Prefab");
                    MenuItem_OpenPrefab.ClickedEvent += OnMenuItemOpenPrefab;
                    Menu_Prefab.AddMenuItem(MenuItem_OpenPrefab);

                    MenuItem MenuItem_Revert = new MenuItem();
                    MenuItem_Revert.SetText("Revert Prefab");
                    MenuItem_Revert.ClickedEvent += OnMenuItemRevertPrefab;
                    Menu_Prefab.AddMenuItem(MenuItem_Revert);

                    if (entity.IsPrefabInstanceRoot())
                    {
                        MenuItem MenuItem_RevertAll = new MenuItem();
                        MenuItem_RevertAll.SetText("Revert Prefab Completely");
                        MenuItem_RevertAll.ClickedEvent += OnMenuItemRevertPrefabCompletely;
                        Menu_Prefab.AddMenuItem(MenuItem_RevertAll);

                        MenuItem MenuItem_Update_Hierarhy = new MenuItem();
                        MenuItem_Update_Hierarhy.SetText("Update Prefab");
                        MenuItem_Update_Hierarhy.ClickedEvent += OnMenuItemApplyHierarhyToPrefab;
                        Menu_Prefab.AddMenuItem(MenuItem_Update_Hierarhy);
                    }

                    MenuItem MenuItem_Unlink = new MenuItem();
                    MenuItem_Unlink.SetText("Unlink Prefab");
                    MenuItem_Unlink.ClickedEvent += OnMenuItemUnlinkPrefab;
                    Menu_Prefab.AddMenuItem(MenuItem_Unlink);

                    MenuItem MenuItem_UnlinkCompletely = new MenuItem();
                    MenuItem_UnlinkCompletely.SetText("Unlink Prefab Completely");
                    MenuItem_UnlinkCompletely.ClickedEvent += OnMenuItemUnlinkPrefabCompletely;
                    Menu_Prefab.AddMenuItem(MenuItem_UnlinkCompletely);
                }
                isShow = true;
            }
            else if (_CurScene is PrefabScene)
            {
                PrefabScene PrefabScene = _CurScene as PrefabScene;
                if (PrefabScene.GetPrefabId() == entity.GetPrefabId() && entity.IsPrefabInstanceRoot())
                {
                    MenuItem MenuItem_Update = new MenuItem();
                    MenuItem_Update.SetText("Update Prefab");
                    MenuItem_Update.ClickedEvent += OnMenuItemUpdatePrefab;
                    Menu_Prefab.AddMenuItem(MenuItem_Update);
                    isShow = true;
                }

                if (entity.IsInheritPrefabInstance() || entity.GetPrefabId() != PrefabScene.GetPrefabId())
                {
                    MenuItem MenuItem_OpenPrefab = new MenuItem();
                    MenuItem_OpenPrefab.SetText("Open Prefab");
                    MenuItem_OpenPrefab.ClickedEvent += OnMenuItemOpenPrefab;
                    Menu_Prefab.AddMenuItem(MenuItem_OpenPrefab);

                    MenuItem MenuItem_Revert = new MenuItem();
                    MenuItem_Revert.SetText("Revert Prefab");
                    MenuItem_Revert.ClickedEvent += OnMenuItemRevertPrefab;
                    Menu_Prefab.AddMenuItem(MenuItem_Revert);

                    if (entity.IsPrefabInheritInstanceRoot())
                    {
                        MenuItem MenuItem_RevertAll = new MenuItem();
                        MenuItem_RevertAll.SetText("Revert Prefab Completly");
                        MenuItem_RevertAll.ClickedEvent += OnMenuItemRevertPrefabCompletely;
                        Menu_Prefab.AddMenuItem(MenuItem_RevertAll);
                    }

                    isShow = true;
                }


            }
            if (isShow)
            {
                Root.AddMenuItem(MenuItem_Prefab);
                Root.AddSeperator();
            }
        }


        public void BuildGameObjectMenu(Menu Root)
        {
            // Create GameObject
            List<(string, Type)> menuItems = new List<(string, Type)>();

            var toolbar = new CreateGameObjectMenuItem();
            var DisplayUINames = toolbar.GetDynamicUINames();
            menuItems.Add((DisplayUINames, typeof(CreateGameObjectMenuItem)));

            MenuItemUtils.CreateMenu(Root, Root.GetUIManager(), menuItems,
                delegate ((List<string>, Type) entry)
                {
                    return (MenuItem)Activator.CreateInstance(entry.Item2);
                },
                delegate (Menu m, int level)
                {
                }
            );
            Root.AddSeperator();

        }

        public void BuildVisibilityMenu(Menu Root)
        {
            List<Entity> SelectedEntities = GetSelectedEntities();

            Menu Menu_Visibility = new Menu(GetUIManager());
            Menu_Visibility.Initialize();
            MenuItem MenuItem_Visibility = new MenuItem();
            MenuItem_Visibility.SetText("Visibility");
            MenuItem_Visibility.SetMenu(Menu_Visibility);

            Root.AddMenuItem(MenuItem_Visibility);

            MenuItem MenuItem_HideSelected = new MenuItem();
            MenuItem_HideSelected.SetText("Hide Selected");
            MenuItem_HideSelected.ClickedEvent += (Sender) => { SetVisibility(SelectedEntities, false); };

            MenuItem MenuItem_HideSelectedChildren = new MenuItem();
            MenuItem_HideSelectedChildren.SetText("Hide Selected Children");
            MenuItem_HideSelectedChildren.ClickedEvent += (Sender) => { SetChildrenVisibility(SelectedEntities, false); };

            MenuItem MenuItem_HideAll = new MenuItem();
            MenuItem_HideAll.SetText("Hide All Entities");
            MenuItem_HideAll.ClickedEvent += (Sender) => { SetRootVisibility(false); };

            MenuItem MenuItem_ShowSelected = new MenuItem();
            MenuItem_ShowSelected.SetText("Show Selected");
            MenuItem_ShowSelected.ClickedEvent += (Sender) => { SetVisibility(SelectedEntities, true); };

            MenuItem MenuItem_ShowSelectedChildren = new MenuItem();
            MenuItem_ShowSelectedChildren.SetText("Show Selected Children");
            MenuItem_ShowSelectedChildren.ClickedEvent += (Sender) => { SetChildrenVisibility(SelectedEntities, true); };

            MenuItem MenuItem_ShowAll = new MenuItem();
            MenuItem_ShowAll.SetText("Show All Entities");
            MenuItem_ShowAll.ClickedEvent += (Sender) => { SetRootVisibility(true); };

            if (SelectedEntities.Count == 0)
            {
                Menu_Visibility.AddMenuItem(MenuItem_HideAll);
                Menu_Visibility.AddMenuItem(MenuItem_ShowAll);
            }
            else if (SelectedEntities.Count == 1)
            {
                Menu_Visibility.AddMenuItem(MenuItem_HideSelected);
                Menu_Visibility.AddMenuItem(MenuItem_HideSelectedChildren);
                Menu_Visibility.AddMenuItem(MenuItem_ShowSelected);
                Menu_Visibility.AddMenuItem(MenuItem_ShowSelectedChildren);
            }
            else
            {
                Menu_Visibility.AddMenuItem(MenuItem_HideSelected);
                Menu_Visibility.AddMenuItem(MenuItem_ShowSelected);
            }
        }

        public void BuildSelectableMenu(Menu Root)
        {
            List<Entity> SelectedEntities = GetSelectedEntities();

            Menu Menu_Selectable = new Menu(GetUIManager());
            Menu_Selectable.Initialize();
            MenuItem MenuItem_Selectable = new MenuItem();
            MenuItem_Selectable.SetText("Selectable");
            MenuItem_Selectable.SetMenu(Menu_Selectable);

            MenuItem MenuItem_Enable = new MenuItem();
            MenuItem_Enable.SetText("Enable Selectable");
            MenuItem_Enable.ClickedEvent += (Sender) =>
            {
                foreach (Entity Entity in SelectedEntities)
                {
                    Entity.Selectable = true;
                }
            };
            Menu_Selectable.AddMenuItem(MenuItem_Enable);

            MenuItem MenuItem_Disable = new MenuItem();
            MenuItem_Disable.SetText("Disable Selectable");
            MenuItem_Disable.ClickedEvent += (Sender) =>
            {
                foreach (Entity Entity in SelectedEntities)
                {
                    Entity.Selectable = false;
                }
            };
            Menu_Selectable.AddMenuItem(MenuItem_Disable);

            if (SelectedEntities.Count > 0)
                Root.AddMenuItem(MenuItem_Selectable);
        }

        public void BuildChangeEntityMenu(Menu Root)
        {
            List<Entity> SelectedEntities = GetSelectedEntities();
            if (SelectedEntities.Count <= 0)
                return;
            Menu Menu_ChangeEntity = new Menu(GetUIManager());
            Menu_ChangeEntity.Initialize();
            MenuItem MenuItem_Change = new MenuItem();
            MenuItem_Change.SetText("Tools");
            MenuItem_Change.SetMenu(Menu_ChangeEntity);

            if (SelectedEntities.Count > 1)
            {
                MenuItem MenuItem_CombineEntity = new MenuItem();
                MenuItem_CombineEntity.SetText("Combine Entity");
                MenuItem_CombineEntity.ClickedEvent += OnMenuItemCombineEntitiesClicked;
                Menu_ChangeEntity.AddMenuItem(MenuItem_CombineEntity);

                MenuItem MenuItem_CombineLodEntity = new MenuItem();
                MenuItem_CombineLodEntity.SetText("Combine And Split Lod Entity");
                MenuItem_CombineLodEntity.ClickedEvent += OnMenuItemCombineAndSplitLodEntitiesClicked;
                Menu_ChangeEntity.AddMenuItem(MenuItem_CombineLodEntity);
            }
            else
            {
                MenuItem MenuItem_SplitEntity = new MenuItem();
                MenuItem_SplitEntity.SetText("Split Entity");
                MenuItem_SplitEntity.ClickedEvent += OnMenuItemSplitEntityClicked;
                Menu_ChangeEntity.AddMenuItem(MenuItem_SplitEntity);

                if (SelectedEntities[0].Children.Count > 1)
                {
                    MenuItem MenuItem_CombineEntity = new MenuItem();
                    MenuItem_CombineEntity.SetText("Combine Children");
                    MenuItem_CombineEntity.ClickedEvent += OnMenuItemCombineEntityClicked;
                    Menu_ChangeEntity.AddMenuItem(MenuItem_CombineEntity);
                }

                MenuItem MenuItem_BreakToEntities = new MenuItem();
                MenuItem_BreakToEntities.SetText("Break To Entities");
                MenuItem_BreakToEntities.ClickedEvent += OnMenuItemBreakToEntitiesClicked;
                Menu_ChangeEntity.AddMenuItem(MenuItem_BreakToEntities);
            }

            Root.AddMenuItem(MenuItem_Change);
            Root.AddSeperator();
        }

        public void BuildChangeBlockMenu(Menu Root)
        {
            Menu Menu_ChangeEntity = new Menu(GetUIManager());
            Menu_ChangeEntity.Initialize();
            MenuItem MenuItem_Change = new MenuItem();
            MenuItem_Change.SetText("Change Block");
            MenuItem_Change.SetMenu(Menu_ChangeEntity);

            List<Entity> SelectedEntities = GetSelectedEntities();
            if (SelectedEntities.Count >= 1)
            {
                vector_string allBlocks = CrossEngineApi.GetAllWorldBlockIds(SelectedEntities[0].World.GetNativePointer());
                foreach (string blockId in allBlocks)
                {
                    MenuItem MenuItem_Block = new MenuItem();
                    MenuItem_Block.SetText(blockId);
                    MenuItem_Block.ClickedEvent += OnMenuItemChangeBlockClicked;
                    Menu_ChangeEntity.AddMenuItem(MenuItem_Block);
                }
            }

            Root.AddMenuItem(MenuItem_Change);
            Root.AddSeperator();
        }

        public bool IsMenuItemCopyEnable()
        {
            TreeItem SelectedItem = _Tree.GetSelectedItem();
            if (SelectedItem == null)
            {
                return false;
            }
            else
            {
                if (SelectedItem.GetParent() == null)
                {
                    return false;
                }
            }
            return true;
        }

        public bool IsMenuItemPivotSetPivotToCenterEnable()
        {
            List<Entity> Entities = GetSelectedEntities();
            foreach (Entity Entity in Entities)
            {
                ModelComponent ModelComponent = Entity.GetModelComponent();
                if (ModelComponent != null)
                {
                    return true;
                }
            }
            return false;
        }

        public bool IsMenuItemPivotResetPivotEnable()
        {
            return IsMenuItemPivotSetPivotToCenterEnable();
        }

        public bool IsMenuItemPasteEnable()
        {
            if (_EntitiesCopied.Count == 0 && _EntitiesCopiedPath.Count == 0)
            {
                return false;
            }
            return true;
        }

        public bool IsMenuItemRenameEnable()
        {
            return IsMenuItemCopyEnable();
        }

        public bool IsMenuItemDuplicateEnable()
        {
            return IsMenuItemCopyEnable();
        }

        public bool IsMenuItemDeleteEnable()
        {
            TreeItem SelectedItem = _Tree.GetSelectedItem();
            if (SelectedItem == null)
            {
                return false;
            }
            Entity entity = (Entity)SelectedItem.GetTagObject();
            if (entity == null)
            {
                return false;
            }
            if (_CurScene is EditorScene)
            {
                return entity.CanDelete();
            }
            if (_CurScene is PrefabScene)
            {
                bool isTmp = entity.GetPrefabId() != PrefabScene.GetInstance().GetPrefabId();
                return isTmp ? (entity.IsPrefabInstanceRoot() || !entity.IsPrefabInstance()) : (!entity.IsPrefabInstanceRoot() && entity.CanDelete());
            }
            return true;
        }

        public bool IsMenuItemLocateToEntityEnable()
        {
            TreeItem SelectedItem = _Tree.GetSelectedItem();
            if (SelectedItem != null)
            {
                Entity Entity = (Entity)SelectedItem.GetTagObject();
                if (Entity.bFolder)
                    return false;
            }
            if (SelectedItem == null)
            {
                return false;
            }
            return true;
        }

        public bool IsEntityExistComponent(Entity Entity, Type Type)
        {
            Queue<Entity> Queue = new Queue<Entity>();
            Queue.Enqueue(Entity);
            while (Queue.Count != 0)
            {
                Entity Head = Queue.Dequeue();
                if (Head.HasComponent(Type))
                {
                    return true;
                }
                foreach (var Child in Head.Children)
                {
                    Queue.Enqueue(Child);
                }
            }
            return false;
        }

        void ShowContextMenu(int MouseX, int MouseY)
        {
            List<TreeItem> SelectedItems = _Tree.GetSelectedItems();

            MenuItem MenuItem_FoldAll = new MenuItem();
            MenuItem_FoldAll.SetText("Fold All");
            MenuItem_FoldAll.ClickedEvent += OnMenuItemFoldAllClicked;

            MenuItem MenuItem_ExpandAll = new MenuItem();
            MenuItem_ExpandAll.SetText("Expand All");
            MenuItem_ExpandAll.ClickedEvent += OnMenuItemExpandAllClicked;

            MenuItem MenuItem_PackEntity = new MenuItem();
            MenuItem_PackEntity.SetText("Pack Entity");
            MenuItem_PackEntity.ClickedEvent += OnMenuItemPackEntityClicked;

            MenuItem MenuItem_UnPackEntity = new MenuItem();
            MenuItem_UnPackEntity.SetText("UnPack Entity");
            MenuItem_UnPackEntity.ClickedEvent += OnMenuItemUnPackEntityClicked;

            MenuItem MenuItem_MergeEntity = new MenuItem();
            MenuItem_MergeEntity.SetText("Merge And Export FBX");
            MenuItem_MergeEntity.ClickedEvent += OnMenuItemMergeEntityClicked;

            MenuItem MenuItem_SelectChildren = new MenuItem();
            MenuItem_SelectChildren.SetText("Select Children");
            MenuItem_SelectChildren.ClickedEvent += OnMenuItemSelectChildrenClicked;

            MenuItem MenuItem_SelectAllChildren = new MenuItem();
            MenuItem_SelectAllChildren.SetText("Select All Children");
            MenuItem_SelectAllChildren.ClickedEvent += OnMenuItemSelectAllChildrenClicked;

            MenuItem MenuItem_Preview = new MenuItem();
            MenuItem_Preview.SetText("Preview");
            MenuItem_Preview.ClickedEvent += OnMenuItemPreviewClicked;

            MenuItem MenuItem_Copy = new MenuItem();
            MenuItem_Copy.SetText("Copy");
            MenuItem_Copy.ClickedEvent += OnMenuItemCopyClicked;

            MenuItem MenuItem_Paste = new MenuItem();
            MenuItem_Paste.SetText("Paste");
            MenuItem_Paste.ClickedEvent += OnMenuItemPasteClicked;

            MenuItem MenuItem_Rename = new MenuItem();
            MenuItem_Rename.SetText("Rename");
            MenuItem_Rename.ClickedEvent += OnMenuItemRenameClicked;

            MenuItem MenuItem_Duplicate = new MenuItem();
            MenuItem_Duplicate.SetText("Duplicate");
            MenuItem_Duplicate.ClickedEvent += OnMenuItemDuplicateClicked;

            MenuItem MenuItem_Delete = new MenuItem();
            MenuItem_Delete.SetText("Delete");
            MenuItem_Delete.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/Delete.png"));
            MenuItem_Delete.ClickedEvent += OnMenuItemDeleteClicked;

            MenuItem MenuItem_LocateToEntity = new MenuItem();
            MenuItem_LocateToEntity.SetText("Locate To Entity");
            MenuItem_LocateToEntity.ClickedEvent += OnMenuItemLocateToEntityClicked;

            MenuItem MenuItem_MoveCameraToEntity = new MenuItem();
            MenuItem_MoveCameraToEntity.SetText("Move Camera To Entity");
            MenuItem_MoveCameraToEntity.ClickedEvent += OnMenuItemMoveCameraToEntityClicked;

            MenuItem MenuItem_MoveEntityToCamera = new MenuItem();
            MenuItem_MoveEntityToCamera.SetText("Move Entity To Camera");
            MenuItem_MoveEntityToCamera.ClickedEvent += OnMenuItemMoveEntityToCameraClicked;

            MenuItem MenuItem_EntityFallToGroundDouble = new MenuItem();
            MenuItem_EntityFallToGroundDouble.SetText("Entity Fall To Ground As Entirety(Alt + M)");
            MenuItem_EntityFallToGroundDouble.ClickedEvent += OnMenuItemEntityFallToGroundAsEntiretyClicked;

            MenuItem MenuItem_EntityFallToGroundDoubleTest = new MenuItem();
            MenuItem_EntityFallToGroundDoubleTest.SetText("Entity Fall To Ground As Mesh(Alt + N)");
            MenuItem_EntityFallToGroundDoubleTest.ClickedEvent += OnMenuItemEntityFallToGroundAsMeshClicked;

            MenuItem MenuItem_SplitEntitySubModel = new MenuItem();
            MenuItem_SplitEntitySubModel.SetText("Entity SubModels To Entities");
            MenuItem_SplitEntitySubModel.ClickedEvent += OnMenuItemSplitEntitySubModel;

            MenuItem MenuItem_MakeModelToLocalCenter = new MenuItem();
            MenuItem_MakeModelToLocalCenter.SetText("Change Entity Models Pivot To LocalCenter");
            MenuItem_MakeModelToLocalCenter.ClickedEvent += OnMenuItemMakeModelToLocalCenter;

            MenuItem MenuItem_InstanceFallToGround = new MenuItem();
            MenuItem_InstanceFallToGround.SetText("Instance Fall To Ground(Alt + B)");
            MenuItem_InstanceFallToGround.ClickedEvent += OnMenuItemInstanceFallToGroundClicked;

            MenuItem MenuItem_PivotSetPivotToCenter = new MenuItem();
            MenuItem_PivotSetPivotToCenter.SetText("Set Pivot To Center");
            MenuItem_PivotSetPivotToCenter.ClickedEvent += OnMenuItemPivotSetPivotToCenterClicked;

            MenuItem MenuItem_PivotResetPivot = new MenuItem();
            MenuItem_PivotResetPivot.SetText("Reset Pivot");
            MenuItem_PivotResetPivot.ClickedEvent += OnMenuItemPivotResetPivotClicked;

            MenuItem MenuItem_Pilot = new MenuItem();
            MenuItem_Pilot.SetText("Pilot(Ctrl + Shift + P)");
            MenuItem_Pilot.ClickedEvent += OnMenuItemPilotClicked;

            MenuItem MenuItem_BreakUpToEntities = new MenuItem();
            MenuItem_BreakUpToEntities.SetText("Break Up To Entities");
            MenuItem_BreakUpToEntities.ClickedEvent += OnMenuItemBreakUpToEntitiesClicked;

            MenuItem MenuItem_CombineTogether = new MenuItem();
            MenuItem_CombineTogether.SetText("Combine Together");
            MenuItem_CombineTogether.ClickedEvent += OnMenuItemCombineTogetherClicked;

            MenuItem MenuItem_ShowEditorEntities = new MenuItem();
            MenuItem_ShowEditorEntities.SetText(string.Format("{0} Editor Entities", _bShowEditorEntities ? "Hide" : "Show"));
            MenuItem_ShowEditorEntities.ClickedEvent += OnMenuItemShowEditorEntitiesClicked;

            //MenuItem MenuItem_GeneratePCGMap = new MenuItem();
            //MenuItem_GeneratePCGMap.SetText("Generate PCG Map");
            //MenuItem_GeneratePCGMap.ClickedEvent += OnMenuItemGeneratePCGMapClicked;

            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();

            MenuContextMenu.AddMenuItem(MenuItem_FoldAll);
            MenuContextMenu.AddMenuItem(MenuItem_ExpandAll);
            MenuContextMenu.AddMenuItem(MenuItem_ShowEditorEntities);
            if (SelectedItems.Count > 0)
            {
                MenuContextMenu.AddMenuItem(MenuItem_Copy);
                MenuContextMenu.AddMenuItem(MenuItem_Paste);
                MenuContextMenu.AddMenuItem(MenuItem_SelectChildren);
                MenuContextMenu.AddMenuItem(MenuItem_SelectAllChildren);
                MenuContextMenu.AddMenuItem(MenuItem_Preview);
                MenuContextMenu.AddSeperator();
                MenuContextMenu.AddMenuItem(MenuItem_MergeEntity);
                MenuContextMenu.AddSeperator();

                // Pivot
                Menu Menu_Pivot = new Menu(GetUIManager());
                Menu_Pivot.Initialize();
                MenuItem MenuItem_Pivot = new MenuItem();
                MenuItem_Pivot.SetText("Pivot");
                MenuItem_Pivot.SetMenu(Menu_Pivot);
                Menu_Pivot.AddMenuItem(MenuItem_PivotSetPivotToCenter);
                Menu_Pivot.AddMenuItem(MenuItem_PivotResetPivot);

                if (SelectedItems.Count == 1)
                {
                    Entity SelectedEntity = SelectedItems[0].GetTagObject() as Entity;

                    // Basic operation
                    MenuContextMenu.AddMenuItem(MenuItem_Duplicate);
                    MenuContextMenu.AddMenuItem(MenuItem_Delete);
                    MenuContextMenu.AddSeperator();
                    MenuContextMenu.AddMenuItem(MenuItem_Rename);
                    if (!SelectedEntity.bFolder)
                    {
                        MenuContextMenu.AddMenuItem(MenuItem_LocateToEntity);
                        MenuContextMenu.AddMenuItem(MenuItem_MoveCameraToEntity);
                        MenuContextMenu.AddMenuItem(MenuItem_MoveEntityToCamera);
                        MenuContextMenu.AddMenuItem(MenuItem_EntityFallToGroundDouble);
                        MenuContextMenu.AddMenuItem(MenuItem_EntityFallToGroundDoubleTest);
                        MenuContextMenu.AddMenuItem(MenuItem_SplitEntitySubModel);
                        MenuContextMenu.AddMenuItem(MenuItem_MakeModelToLocalCenter);
                        if (IsEntityExistComponent(SelectedEntity, typeof(FoliageComponent)))
                        {
                            MenuContextMenu.AddMenuItem(MenuItem_InstanceFallToGround);
                        }

                    }
                    if (SelectedEntity.Children.Count > 1)
                    {
                        MenuContextMenu.AddSeperator();
                        MenuContextMenu.AddMenuItem(MenuItem_UnPackEntity);
                    }
                    MenuContextMenu.AddSeperator();

                    MenuContextMenu.AddMenuItem(MenuItem_Pivot);
                    if (SelectedEntity == _CurPilotEntity)
                    {
                        MenuItem_Pilot.SetText("Stop Piloting");
                        MenuItem_Pilot.SetTagString("Seleted");
                    }
                    MenuContextMenu.AddMenuItem(MenuItem_Pilot);
                    MenuContextMenu.AddSeperator();

                    // Prefab
                    BuildPrefabMenu(MenuContextMenu, "ContextMenu");
                    //if (SelectedEntity.GetComponent(typeof(PCGBuildingComponent)) != null)
                    //{
                    //    MenuContextMenu.AddMenuItem(MenuItem_GeneratePCGMap);
                    //    MenuContextMenu.AddSeperator();
                    //}
                }
                else
                {
                    MenuContextMenu.AddMenuItem(MenuItem_PackEntity);
                    MenuContextMenu.AddSeperator();
                    MenuContextMenu.AddMenuItem(MenuItem_Pivot);
                    MenuContextMenu.AddSeperator();
                    MenuContextMenu.AddMenuItem(MenuItem_EntityFallToGroundDouble);
                    MenuContextMenu.AddSeperator();
                }

                // Foliage
                Entity firstEntity = GetSelectedEntity();
                bool bEnableBreakUpToEntities = false;
                bool bEnableCombineTogether = false;
                if (firstEntity.GetFoliageComponent() != null)
                {
                    if (firstEntity.Children.Count == 0)
                    {
                        bEnableBreakUpToEntities = true;
                    }
                    else
                    {
                        bEnableCombineTogether = true;
                    }
                }
                MenuItem_BreakUpToEntities.SetEnable(bEnableBreakUpToEntities);
                MenuItem_CombineTogether.SetEnable(bEnableCombineTogether);
                Menu Menu_Foliage = new Menu(GetUIManager());
                Menu_Foliage.Initialize();
                MenuItem MenuItem_Foliage = new MenuItem();
                MenuItem_Foliage.SetText("Foliage");
                MenuItem_Foliage.SetMenu(Menu_Foliage);
                Menu_Foliage.AddMenuItem(MenuItem_BreakUpToEntities);
                Menu_Foliage.AddMenuItem(MenuItem_CombineTogether);

                MenuContextMenu.AddMenuItem(MenuItem_Foliage);
                MenuContextMenu.AddSeperator();
            }
            else
            {
                MenuContextMenu.AddMenuItem(MenuItem_Paste);
                MenuContextMenu.AddSeperator();
            }

            // Create entity
            Menu Menu_Entity = new Menu(GetUIManager());
            Menu_Entity.Initialize();
            MenuItem MenuItem_Entity = new MenuItem();
            MenuItem_Entity.SetText("Entity");
            MenuItem_Entity.SetMenu(Menu_Entity);
            BulidCreateEntityMenu(Menu_Entity, "ContextMenu");

            MenuContextMenu.AddMenuItem(MenuItem_Entity);
            MenuContextMenu.AddSeperator();

            //GameObject
            BuildGameObjectMenu(MenuContextMenu);
            // Visibility
            BuildVisibilityMenu(MenuContextMenu);
            // Selectable
            BuildSelectableMenu(MenuContextMenu);
            // Change entity
            BuildChangeEntityMenu(MenuContextMenu);
            // Change Block
            BuildChangeBlockMenu(MenuContextMenu);

            bool bPivotSetPivotToCenterEnable = IsMenuItemPivotSetPivotToCenterEnable();
            bool bPivotResetPivotEnable = IsMenuItemPivotResetPivotEnable();
            bool bCopyEnable = IsMenuItemCopyEnable();
            bool bPasteEnable = IsMenuItemPasteEnable();
            bool bRenameEnable = IsMenuItemRenameEnable();
            bool bDuplicateEnable = IsMenuItemDuplicateEnable();
            bool bDeleteEnable = IsMenuItemDeleteEnable();
            bool bLocateToEntityEnable = IsMenuItemLocateToEntityEnable();

            MenuItem_PivotSetPivotToCenter.SetEnable(bPivotSetPivotToCenterEnable);
            MenuItem_PivotResetPivot.SetEnable(bPivotResetPivotEnable);
            MenuItem_Copy.SetEnable(bCopyEnable);
            MenuItem_Paste.SetEnable(bPasteEnable);
            MenuItem_Rename.SetEnable(bRenameEnable);
            MenuItem_Duplicate.SetEnable(bDuplicateEnable);
            MenuItem_Delete.SetEnable(bDeleteEnable);
            MenuItem_LocateToEntity.SetEnable(bLocateToEntityEnable);

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, MouseX, MouseY);
        }

        void OnMenuItemFoldAllClicked(MenuItem Sender)
        {
            List<TreeItem> SelectedItem = _Tree.GetSelectedItems();
            if (SelectedItem.Count > 0)
            {
                foreach (TreeItem Item in SelectedItem)
                {
                    Item.SetExpanded_Recursively(false);
                }
            }
            else
            {
                _Tree.GetRootItem().SetExpanded_Recursively(false);
            }

            UpdateVisibilityButton();
        }

        void OnMenuItemExpandAllClicked(MenuItem Sender)
        {
            List<TreeItem> SelectedItem = _Tree.GetSelectedItems();
            if (SelectedItem.Count > 0)
            {
                foreach (TreeItem Item in SelectedItem)
                {
                    Item.SetExpanded_Recursively(true);
                }
            }
            else
            {
                _Tree.GetRootItem().SetExpanded_Recursively(true);
            }

            UpdateVisibilityButton();
        }

        void OnMenuItemPreviewClicked(MenuItem Sender)
        {
            List<Entity> SelectEntities = GetSelectedEntities();
            if (SelectEntities.Count != 0)
            {
                MainUI.GetInstance().ActivateDockingCard_PreviewScene();
                PreviewSceneUI.GetInstance().SetPreviewEntity(SelectEntities);
            }
        }

        void OnMenuItemCreateFolderClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateTransformComponent(Entity);
                DoSetName(Entity, "New Folder");
                Entity.SetBFolder(true);
                EndAddEntity(Entity);
            }
        }

        void OnMenuItemPackEntityClicked(MenuItem Sender)
        {
            PackParentEntities.Clear();
            Entity RootEntity = GetParentEntity(true);
            Entity Entity = DoCreateEntity(true);
            List<Entity> SelectedEntities = GetSelectedEntities();
            List<Entity> TopEntities = EditorScene.GetInstance().ToTopEntities(SelectedEntities);

            if (Entity != null)
            {
                DoCreateTransformComponent(Entity);
                Transform TransformComponent = Entity.GetTransformComponent();
                TransformComponent.SetWorldTranslation(GetCenterPosition(SelectedEntities));
                DoSetName(Entity, "PackEntities");
                for (int i = 0; i < TopEntities.Count; i++)
                {
                    Entity ParentEntity = TopEntities[i].Parent;
                    Entity.AddChildEntity(TopEntities[i]);
                    ParentEntity.RemoveChildEntity(TopEntities[i]);
                    TopEntities[i].Parent = Entity;
                    TopEntities[i].RuntimeJointToParent();
                    PackParentEntities.Add(ParentEntity);
                }
                Entity.RuntimeJointToParent();
                UpdateHierarchy();
                SelectEntity(Entity);
                EditorScene.GetInstance().SetDirty();
            }
            TreeItem Item = _Tree.FindItemByTagObject(Entity);
            Item.SetExpanded(true);

            PackParentEntities.Add(RootEntity);
            EditOperation_PackEntities EditPackOperation = new EditOperation_PackEntities(PackParentEntities, Entity, TopEntities);
            EditOperationManager.GetInstance().AddOperation(EditPackOperation);
        }

        void OnMenuItemUnPackEntityClicked(MenuItem Sender)
        {
            Entity RootEntity = GetParentEntity(true);
            List<Entity> SelectedEntities = GetSelectedEntities();
            Entity CurEntity = SelectedEntities[0];
            Entity CurParentEntity = CurEntity.Parent;
            List<Entity> TopEntities = new List<Entity>();
            for (int i = 0; i < CurEntity.Children.Count; i++)
            {
                CurParentEntity.AddChildEntity(CurEntity.Children[i]);
                CurEntity.Children[i].RuntimeJointToParent();
                TopEntities.Add(CurEntity.Children[i]);
            }

            CurEntity.RemoveChildEntities();
            UpdateHierarchy();
            SelectEntity(null);

            EditOperation_UnPackEntities EditUnPackOperation = new EditOperation_UnPackEntities(CurParentEntity, CurEntity, TopEntities);
            EditOperationManager.GetInstance().AddOperation(EditUnPackOperation);
        }

        void OnMenuItemMergeEntityClicked(MenuItem Sender)
        {
            List<Entity> SelectedEntites = GetSelectedEntities();
            vector_string ModelPaths = new vector_string();
            vector_cross_Float3 ModelPositions = new vector_cross_Float3();
            vector_cross_Float3 ModelRotations = new vector_cross_Float3();
            vector_cross_Float3 ModelScales = new vector_cross_Float3();
            foreach (var Value in SelectedEntites)
            {
                if (!Value.HasComponent(typeof(ModelComponent))) continue;
                string MeshPath = ResourceManager.Instance().ConvertGuidToPath(Value.GetModelComponent().Models[0].ModelPath);
                string MeshFullPath = EditorUtilities.StandardFilenameToEditorFilename(MeshPath);
                ClassIDType ObjectClassID = Resource.GetResourceTypeStatic(MeshFullPath);
                if (ObjectClassID == ClassIDType.CLASS_MeshAssetDataResource)
                {
                    ModelPaths.Add(MeshFullPath);
                    Double3 Tranform = Value.GetTransformComponent().GetWorldTranslation();
                    ModelPositions.Add(new Float3((float)Tranform.x, (float)Tranform.y, (float)Tranform.z));
                    Double3 Rotation = Value.GetTransformComponent().Rotation;
                    ModelRotations.Add(new Float3((float)Rotation.x, (float)Rotation.y, (float)Rotation.z));
                    Double3 Scale = Value.GetTransformComponent().Scale;
                    ModelScales.Add(new Float3((float)Scale.x, (float)Scale.y, (float)Scale.z));
                }
            }
            if (ModelPaths.Count != 0)
            {
                PathInputUIFilterItem PathInputUIFilterItem = new PathInputUIFilterItem();
                PathInputUIFilterItem.Name = "FBX Files";
                PathInputUIFilterItem.Extensions.Add("fbx");


                string SelectedFilePath = "";
                bool bContentsOnly = false;

                PathInputUIEx PathInputUI = new PathInputUIEx();
                string DefaultDrivePath = EditorUtilities.AddEditorDrives(PathInputUI, bContentsOnly);
                //PathInputUI.AddDrive(ProjectName, ProjectDirectory);
                PathInputUI.AddDefaultDrives();
                PathInputUI.Initialize(GetUIManager(), "Save Fbx As", PathInputUIType.SaveFile, PathInputUIFilterItem, DefaultDrivePath);
                PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
                {
                    SelectedFilePath = PathInputed;
                };
                PathInputUI.DialogCloseEvent += (DialogUI DialogSender) =>
                {
                    OperationQueue.GetInstance().AddOperation(() =>
                    {
                        if (SelectedFilePath != "")
                        {
                            var thread = new System.Threading.Thread(
                                () =>
                                {
                                    bool flag = AssetExporterManager.Instance().MergeSelectModelsAsFBX(ModelPaths, ModelPositions, ModelRotations, ModelScales, SelectedFilePath);
                                    StringBuilder TipString = new StringBuilder();
                                    if (flag)
                                    {
                                        TipString.Append("Exporting is successful!");
                                    }
                                    else
                                    {
                                        TipString.Append("Nda is not a valid mesh asset, exporting is fail!");
                                    }
                                    OperationQueue.GetInstance().AddOperation(() =>
                                    {
                                        CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Fbx Export Info", TipString.ToString());
                                    });
                                }
                            );
                            thread.Start();
                        }
                    });
                };
                DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
            }
        }

        void OnMenuItemSelectChildrenClicked(MenuItem Sender)
        {
            List<TreeItem> SelectedItems = _Tree.GetSelectedItems().Clone();
            _Tree.SelectItemNoEvent(null);
            foreach (TreeItem Item in SelectedItems)
            {
                foreach (TreeItem Child in Item.GetChildList())
                {
                    _Tree.AddSelectItemNoEvent(Child);
                }
            }
            OnTreeItemSelected(_Tree, null);
        }

        void OnMenuItemSelectAllChildrenClicked(MenuItem Sender)
        {
            List<TreeItem> SelectedItems = _Tree.GetSelectedItems().Clone();
            _Tree.SelectItemNoEvent(null);
            Queue<TreeItem> Queue = new Queue<TreeItem>();
            foreach (TreeItem Item in SelectedItems)
            {
                Queue.Enqueue(Item);
                while (Queue.Count != 0)
                {
                    TreeItem Head = Queue.Dequeue();
                    _Tree.AddSelectItemNoEvent(Head);
                    foreach (TreeItem Child in Head.GetChildList())
                    {
                        Queue.Enqueue(Child);
                    }
                }
            }
            OnTreeItemSelected(_Tree, null);
        }

        public void ClearTempFile()
        {
            string TargetDirectory = MainUI.GetInstance().GetProjectDirectory() + TempPath;
            if (!DirectoryHelper.IsDirectoryExists(TargetDirectory))
            {
                DirectoryHelper.CreateDirectory(TargetDirectory);
            }
            FileInfo[] FileInfos = DirectoryHelper.GetFilesInDirectory(TargetDirectory);
            for (int i = 0; i < FileInfos.Length; i++)
            {
                FileInfo FileInfo = FileInfos[i];
                if (FileInfo.Name.Contains(".copy.block"))
                {
                    FileHelper.DeleteFile(FileInfo.FullName);
                    ResourceManager.Instance().DeleteFilePaths(new vector_string { FileInfo.FullName });
                }
            }
        }

        public void OnMenuItemCopyClicked(MenuItem Sender)
        {
            List<Entity> SelectedEntites = GetSelectedEntities();
            if (SelectedEntites.Count == 0)
            {
                return;
            }
            // Delete last copy file
            foreach (string Path in _EntitiesCopiedPath)
            {
                if (FileHelper.IsFileExists(Path))
                {
                    FileHelper.DeleteFile(Path);
                    ResourceManager.Instance().DeleteFilePaths(new vector_string { Path });
                }
            }
            // Do multiple copy
            _EntitiesCopiedPath.Clear();
            _EntitiesCopied.Clear();
            List<Entity> TopEntities = _CurScene.ToTopEntities(SelectedEntites);
            foreach (Entity Entity in TopEntities)
            {
                string Extension = ".copy.block";
                string TargetDirectory = MainUI.GetInstance().GetProjectDirectory() + TempPath;
                string TargetPath = TargetDirectory + "/" + Entity.GetName() + Extension;
                Clicross.GameWorldInterface.World_SaveEntity(Entity.World._WorldInterface, Entity.EntityID, TargetPath);

                _EntitiesCopiedPath.Add(TargetPath);
                _EntitiesCopied.Add(Entity);
            }
        }

        public void OnMenuItemPasteClicked(MenuItem Sender)
        {
            if (_EntitiesCopied.Count == 0)
            {
                return;
            }

            TreeItem Root = _Tree.GetRootItem();
            Entity RootEntity = Root.GetTagObject() as Entity;
            bool bForceRoot = false;
            Entity ParentEntity = GetParentEntity(bForceRoot);
            // Do multiple paste
            EditOperationManager.GetInstance().BeginRecordCompoundOperation();
            {

                if (RootEntity.World != _EntitiesCopied[0].World)
                {
                    foreach (string Path in _EntitiesCopiedPath)
                    {
                        if (FileHelper.IsFileExists(Path))
                        {
                            Clicross.GameWorldInterface.World_LoadEntity(RootEntity.World._WorldInterface, ParentEntity.EntityID, Path);
                        }
                    }
                }
                // Do multiple paste directly
                else
                {
                    List<Entity> EntitiesCloned = new List<Entity>();
                    // Do clone.
                    foreach (Entity EntityCopied in _EntitiesCopied)
                    {
                        Entity EntityCloned = EntityCopied.Clone(ParentEntity);
                        EntitiesCloned.Add(EntityCloned);
                    }
                    // Set parent after clone, which can avoid duplicate cloning.
                    int EntityCount = _EntitiesCopied.Count;
                    for (int i = 0; i < EntityCount; i++)
                    {
                        Entity EntityCopied = _EntitiesCopied[i];
                        Entity EntityCloned = EntitiesCloned[i];
                        Entity ParentEntity1 = EntityCopied.Parent;
                        if (ParentEntity1 == null)
                        {
                            ParentEntity1 = ParentEntity;
                        }
                        ParentEntity1.AddChildEntity(EntityCloned);
                        EntityCloned.RuntimeJointToParent();
                        // Make sure name is unique in this level
                        DoSetName(EntityCloned, EntityCopied.GetName());
                    }
                    EndAddEntities(EntitiesCloned);
                }
            }
            EditOperationManager.GetInstance().EndRecordCompoundOperation();
        }

        void DoRename()
        {
            List<TreeItem> SelectiontList = _Tree.GetSelectedItems();
            if (SelectiontList.Count == 1)
            {
                TreeItem SelectedItem = SelectiontList[0];
                Entity Entity = (Entity)SelectedItem.GetTagObject();
                if (Entity != null && Entity.IsRoot() == false)
                {
                    _Tree.StartRename(SelectedItem);
                }
            }
        }

        public void OnMenuItemRenameClicked(MenuItem Sender)
        {
            DoRename();
        }

        public void OnMenuItemDuplicateClicked(MenuItem Sender)
        {
            DuplicateSelectedItem();
        }

        public bool DuplicateSelectedItem()
        {
            Entity Entity = GetSelectedEntity();
            if (Entity != null && Entity.IsRoot() == false)
            {
                Entity NewEntity = InnerDuplicateItem(Entity, Entity.GetName());
                EndAddEntity(NewEntity);
                return true;
            }
            return false;
        }

        public Entity InnerDuplicateItem(Entity Entity, string EntityName)
        {
            Entity ParentEntity = Entity.Parent;
            Entity NewEntity = Entity.Clone(ParentEntity);
            ParentEntity.AddChildEntity(NewEntity);
            NewEntity.RuntimeJointToParent();
            // Make sure name is unique in this level
            DoSetName(NewEntity, EntityName);
            return NewEntity;
        }

        public void DoDelete()
        {
            if (!IsMenuItemDeleteEnable() || SequenceIsPlaying())
            {
                return;
            }
            List<Entity> SelectedEntities = GetSelectedEntities();
            List<Entity> SelectedEntities1 = new List<Entity>();
            List<ulong> EditorEntities = _CurScene.GetEditorEntities();
            foreach (Entity SelectedEntity in SelectedEntities)
            {
                if (EditorEntities.Contains(SelectedEntity.EntityID) == false)
                {
                    SelectedEntities1.Add(SelectedEntity);
                }
            }
            _CurScene.DeleteEntities(SelectedEntities1);
            _CurScene.ClearSelection();
            _CurScene.SetDirty();
            UpdateHierarchy();
        }

        public void OnMenuItemDeleteClicked(MenuItem Sender)
        {
            DoDelete();
        }

        void OnMenuItemLocateToEntityClicked(MenuItem Sender)
        {
            LocateToCurrentItem();
        }

        void OnMenuItemMoveCameraToEntityClicked(MenuItem Sender)
        {
            MoveCameraToEntity();
        }

        void OnMenuItemMoveEntityToCameraClicked(MenuItem Sender)
        {
            MoveEntityToCamera();
        }

        void OnMenuItemInstanceFallToGroundClicked(MenuItem Sender)
        {
            Entity SelectedEntitiy = GetSelectedEntity();

            SelectedEntitiy.TraverseAll((Entity) =>
            {
                if (Entity.HasComponent(typeof(FoliageComponent)))
                {
                    FoliageComponent FoliageComponent = Entity.GetFoliageComponent();
                    FoliageComponent.FallToGround();
                }
            });
        }

        void OnMenuItemEntityFallToGroundAsEntiretyClicked(MenuItem Sender)
        {
            if (_CurScene is EditorScene EditorScene)
            {
                List<Entity> SelectedEntities = GetSelectedEntities();
                EditorScene.FallToGroundAsEntirety(SelectedEntities);
                InspectorUI.GetInstance().InspectObject();
            }
        }

        void OnMenuItemEntityFallToGroundAsMeshClicked(MenuItem Sender)
        {
            if (_CurScene is EditorScene EditorScene)
            {
                List<Entity> SelectedEntities = GetSelectedEntities();
                EditorScene.FallToGroundAsMesh(SelectedEntities);
                InspectorUI.GetInstance().InspectObject();
            }
        }

        void OnMenuItemSplitEntitySubModel(MenuItem Sender)
        {
            Entity SelectedEntitiy = GetSelectedEntity();
            var modelComp = SelectedEntitiy.GetModelComponent();
            if (modelComp == null || modelComp.Models.Count() <= 1)
                return;
            Entity SelectedEntitiyParent = SelectedEntitiy.World.Root;
            if (SelectedEntitiy.Parent != null)
                SelectedEntitiyParent = SelectedEntitiy.Parent;

            Entity newRoot = SelectedEntitiy.World.CreateEntity();
            SelectedEntitiyParent.AddChildEntity(newRoot);
            newRoot.CreateComponent<Transform>();
            newRoot.RuntimeJointToParent();
            newRoot.SetName(SelectedEntitiy.GetName() + "_SubModelToEntities");
            newRoot.GetTransformComponent().Translation = Double3.Zero();
            newRoot.GetTransformComponent().Rotation = Double3.Zero();
            foreach (var model in modelComp.Models)
            {
                Entity newEntity = newRoot.World.CreateEntity();

                newEntity.CreateComponent<Transform>();
                newRoot.AddChildEntity(newEntity);
                newEntity.RuntimeJointToParent();
                var newModlComp = newEntity.CreateComponent<ModelComponent>();
                List<Model> ModelList = new List<Model>();
                ModelList.Add(new Model(model));
                newModlComp.Models = ModelList;
                newModlComp.Entity = newEntity;
                newModlComp.GPUSkin = true;

                newEntity.SetName(PathHelper.GetNameOfPath(model.ModelPath));
                newEntity.GetTransformComponent().Translation = Double3.Zero();
                newEntity.GetTransformComponent().Rotation = Double3.Zero();
            }
            SelectedEntitiyParent.RemoveChildEntity(SelectedEntitiy);
            SelectedEntitiyParent.RefreshTree();
            UpdateHierarchy();
            ProjectUI.GetInstance().UpdateAll();
        }

        void OnMenuItemMakeModelToLocalCenter(MenuItem Sender)
        {
            Entity SelectedEntitiy = GetSelectedEntity();
            SelectedEntitiy.TraverseAll((Entity) =>
            {
                if (Entity.HasComponent(typeof(ModelComponent)))
                {
                    var modelComponent = Entity.GetModelComponent();
                    if (modelComponent.Models.Count() == 1)
                    {
                        var newPos = ModelChange.ChangeModelCenter(modelComponent.Models.ToArray()[0].ModelPath);
                        Entity.GetTransformComponent().Translation = new Double3(newPos.x, newPos.y, newPos.z);
                    }
                }
            });
            SelectedEntitiy.RefreshTree();
            UpdateHierarchy();
            ProjectUI.GetInstance().UpdateAll();
        }

        void SetPivot(Entity Entity, Double3 Pivot)
        {
            PivotComponent PivotComponent = Entity.GetPivotComponent();
            if (PivotComponent == null)
            {
                PivotComponent = Entity.CreateComponent<PivotComponent>();
                PivotComponent.Reset();
            }
            PivotComponent.Pivot = Pivot;
            EditorScene.GetInstance().SetDirty();
            InspectorUI.GetInstance().InspectObject();
        }

        void OnMenuItemPivotSetPivotToCenterClicked(MenuItem Sender)
        {
            List<Entity> Entities = GetSelectedEntities();
            foreach (Entity Entity in Entities)
            {
                ModelComponent ModelComponent = Entity.GetModelComponent();
                if (ModelComponent != null)
                {
                    Vector3f EntityMin = new Vector3f(float.MaxValue, float.MaxValue, float.MaxValue);
                    Vector3f EntityMax = new Vector3f(float.MinValue, float.MinValue, float.MinValue);

                    Vector3f ItemMin = new Vector3f();
                    Vector3f ItemMax = new Vector3f();

                    foreach (Model Model in ModelComponent.Models)
                    {
                        string ModelPath = Model.ModelPath;
                        if (ModelPath != "" && ModelPath != null)
                        {
                            Clicross.ResourceAABB aabb = Clicross.GameWorldInterface.Resource_GetAABBFromMeshAssetData(Model.ModelPath);

                            EntityMin.X = Math.Min((float)aabb.min.x, ItemMin.X);
                            EntityMin.Y = Math.Min((float)aabb.min.y, ItemMin.Y);
                            EntityMin.Z = Math.Min((float)aabb.min.z, ItemMin.Z);

                            EntityMax.X = Math.Max((float)aabb.max.x, ItemMax.X);
                            EntityMax.Y = Math.Max((float)aabb.max.y, ItemMax.Y);
                            EntityMax.Z = Math.Max((float)aabb.max.z, ItemMax.Z);
                        }
                    }

                    float CenterX = (EntityMin.X + EntityMax.X) / 2.0f;
                    float CenterY = (EntityMin.Y + EntityMax.Y) / 2.0f;
                    float CenterZ = (EntityMin.Z + EntityMax.Z) / 2.0f;
                    SetPivot(Entity, new Double3(CenterX, CenterY, CenterZ));
                }
            }
        }

        void OnMenuItemPivotResetPivotClicked(MenuItem Sender)
        {
            List<Entity> Entities = GetSelectedEntities();
            foreach (Entity Entity in Entities)
            {
                ModelComponent ModelComponent = Entity.GetModelComponent();
                if (ModelComponent != null)
                {
                    SetPivot(Entity, new Double3(0.0, 0.0, 0.0));
                }
            }
        }

        void OnMenuItemPilotClicked(MenuItem Sender)
        {
            if (Sender.GetTagString() == "")
            {
                Entity Entity = GetSelectedEntity();
                if (Entity != null)
                {
                    _CurPilotEntity = Entity;
                }
                EditorSceneUI.GetInstance().ActivePiolotRelated(true, _CurPilotEntity.GetName());
            }
            else
            {
                _CurPilotEntity = null;
                EditorSceneUI.GetInstance().ActivePiolotRelated(false);
            }
        }

        void OnMenuItemBreakUpToEntitiesClicked(MenuItem Sender)
        {
            List<Entity> Entities = GetSelectedEntities();
            foreach (Entity Entity in Entities)
            {
                if (Entity != null)
                {
                    BreakUpToEntities(Entity);
                    EditorScene.GetInstance().SetDirty();
                    UpdateHierarchy();
                    SelectEntity(Entity);
                }
            }
        }

        public static void BreakUpToEntities(Entity Entity)
        {
            FoliageComponent FoliageComponent = Entity.GetFoliageComponent();
            if (FoliageComponent != null)
            {
                List<RTSData> InstanceData = FoliageComponent.InstanceData;
                List<Entity> EntityList = new List<Entity>();

                int i = 0;
                foreach (RTSData RTSData in InstanceData)
                {
                    Entity EntityItem = Entity.World.CreateEntity();
                    Entity.AddChildEntity(EntityItem);

                    string MeshPath = FoliageComponent.PrimaryMeshAsset;
                    string MaterialPath = FoliageComponent.PrimaryMaterial;

                    // submesh count
                    int subMeshCount = 0;
                    int lodCount = FoliageComponent.LoDSections.Count;
                    foreach (LoDSection section in FoliageComponent.LoDSections)
                    {
                        subMeshCount = Math.Max(subMeshCount, section.SubSectionMaterials.Count);
                    }
                    List<LODProperty> lODProperties = new List<LODProperty>();
                    for (int lodIndex = 0; lodIndex < lodCount; ++lodIndex)
                    {
                        LODProperty lODProperty = new LODProperty();
                        for (int subIndex = 0; subIndex < subMeshCount; ++subIndex)
                        {
                            SubModelProperty subModelProperty = new SubModelProperty();
                            LoDSection section = FoliageComponent.LoDSections[lodIndex];
                            string lodMat = subIndex < section.SubSectionMaterials.Count ? section.SubSectionMaterials[subIndex] : section.DefaultMaterial;
                            subModelProperty.MaterialPath = lodMat;
                            lODProperty.SubModels.Add(subModelProperty);
                        }
                        lODProperties.Add(lODProperty);
                    }

                    ModelComponent ModelComponent = EntityItem.CreateComponent<ModelComponent>();
                    ModelComponent.Reset();
                    List<Model> ModelList = new List<Model>();
                    Model model = new Model(MeshPath, MaterialPath);
                    model.LODProperties = lODProperties;
                    ModelList.Add(model);
                    ModelComponent.Models = ModelList;

                    Transform TransformComponent = EntityItem.CreateComponent<Transform>();
                    EntityItem.RuntimeJointToParent();

                    Matrix4x4d identity = new Matrix4x4d();
                    identity.LoadIdentity();
                    TransformComponent.SetMatrix(ref identity);

                    TransformComponent.Translation = RTSData.Translation;
                    TransformComponent.Rotation = RTSData.Rotation;
                    TransformComponent.Scale = RTSData.Scale;

                    RenderProperty RenderProperty = (RenderProperty)EntityItem.GetComponent(typeof(RenderProperty));
                    RenderProperty.CastShadow = true;

                    string Name = string.Format("{0}_{1}", Entity.GetName(), i + 1);
                    EntityItem.SetName(Name);

                    EntityList.Add(EntityItem);

                    i++;
                }

                List<RTSData> EmptyInstanceData = new List<RTSData>();
                FoliageComponent.InstanceData = EmptyInstanceData;

                EditOperation_Foliage EditOperation_BreakUpToEntities = new EditOperation_Foliage(FoliageEditOperationType.BreakUpToEntities, Entity, EntityList, InstanceData);
                EditOperationManager.GetInstance().AddOperation(EditOperation_BreakUpToEntities);
            }
        }

        void OnMenuItemCombineTogetherClicked(MenuItem Sender)
        {
            List<Entity> Entities = GetSelectedEntities();
            foreach (Entity Entity in Entities)
            {
                if (Entity != null)
                {
                    CombineTogether(Entity);
                    EditorScene.GetInstance().SetDirty();
                    UpdateHierarchy();
                    SelectEntity(Entity);
                }
            }
        }

        public void OnMenuItemShowEditorEntitiesClicked(MenuItem Sender)
        {
            _bShowEditorEntities = !_bShowEditorEntities;
            UpdateHierarchy();
        }

        public void OnMenuItemSplitEntityClicked(MenuItem Sender)
        {
            Entity SelectedEntity = _Tree.GetSelectedItems()[0].GetTagObject() as Entity;
            SplitEntity(SelectedEntity);
        }

        public void OnMenuItemCombineEntityClicked(MenuItem Sender)
        {
            Entity SelectedEntity = _Tree.GetSelectedItems()[0].GetTagObject() as Entity;
            CombineEntity(SelectedEntity);
        }

        public void OnMenuItemCombineEntitiesClicked(MenuItem Sender)
        {
            List<Entity> entities = new List<Entity>();
            foreach (TreeItem item in _Tree.GetSelectedItems())
            {
                entities.Add(item.GetTagObject() as Entity);
            }
            CombineEntities(entities);
        }

        public void OnMenuItemCombineAndSplitLodEntitiesClicked(MenuItem Sender)
        {
            List<Entity> entities = new List<Entity>();
            foreach (TreeItem item in _Tree.GetSelectedItems())
            {
                entities.Add(item.GetTagObject() as Entity);
            }
            vector_vector_string lodMeshs = new vector_vector_string();
            vector_string lodMats = new vector_string();
            foreach (Entity en in entities)
            {
                ModelComponent mc = en.GetModelComponent();
                if (mc != null)
                {
                    List<Model> models = mc.GetModels();
                    if (models.Count > 0 && models[0].LODProperties.Count > 0)
                    {
                        vector_string meshs = new vector_string();
                        meshs.Add(models[0].ModelPath);
                        lodMeshs.Add(meshs);
                        foreach (var model in models[0].LODProperties[0].SubModels)
                        {
                            lodMats.Add(model.MaterialPath);
                        }
                    }
                }
            }
            MeshLodCombineAndSplitUI MeshLodCombineAndSplitUI = MeshLodCombineAndSplitUI.GetInstance();
            MeshLodCombineAndSplitUI.Initialize(GetUIManager());
            MeshLodCombineAndSplitUI.SetParameters(lodMeshs, lodMats);
            DialogUIManager.GetInstance().ShowDialogUI(MeshLodCombineAndSplitUI);
        }

        public void OnMenuItemBreakToEntitiesClicked(MenuItem Sender)
        {
            Entity SelectedEntity = _Tree.GetSelectedItems()[0].GetTagObject() as Entity;
            ModelComponent mc = SelectedEntity.GetModelComponent();
            if (mc != null && mc.Models.Count() > 1)
            {
                List<Model> models = mc.Models;
                List<Entity> entities = new List<Entity>();
                for (int i = 0; i < models.Count(); i++)
                {
                    Entity newEntity = SelectedEntity.Clone(SelectedEntity.Parent);
                    newEntity.SetName(SelectedEntity.GetName() + ".part" + (i + 1).ToString());
                    ModelComponent nmc = newEntity.GetModelComponent();
                    nmc.Models = new List<Model> { models[i] };
                    newEntity.RuntimeUnlinkPrefab(true);
                    entities.Add(newEntity);
                }
                for (int i = 0; i < entities.Count(); i++)
                {
                    SelectedEntity.AddChildEntity(entities[i]);
                    entities[i].RuntimeJointToParent();
                }
                SelectedEntity.RemoveComponent(mc);
                UpdateHierarchy();
            }
        }

        public void OnMenuItemChangeBlockClicked(MenuItem Sender)
        {
            string blockId = Sender.GetText();
            List<Entity> SelectedEntities = GetSelectedEntities();
            foreach (Entity entity in SelectedEntities)
            {
                CrossEngineApi.ChangeEntityBlock(entity.World.GetNativePointer(), entity.EntityID, blockId);
            }
        }

        //public void OnMenuItemGeneratePCGMapClicked(MenuItem Sender)
        //{
        //    Entity entity = GetSelectedEntity();
        //    if (PCGBuildingSystemG.GenPCGMapBuilding(entity.World._World, entity.GetEntityIdStruct(), true))
        //    {
        //        entity.RefreshTree();
        //        UpdateHierarchy();
        //    }
        //}

        public void OnMenuItemCurvationCorrectionClicked(bool recurisive)
        {
            foreach (TreeItem item in _Tree.GetSelectedItems())
            {
                Entity entity = item.GetTagObject() as Entity;
                CrossEngineApi.WorldEntityWGS84CurvationCorrection(entity.World._World, entity.EntityID, recurisive);
            }
        }

        public static void CombineTogether(Entity Entity)
        {
            FoliageComponent FoliageComponent = Entity.GetFoliageComponent();
            if (FoliageComponent != null)
            {
                if (FoliageComponent.InstanceData.Count == 0 && Entity.Children.Count > 0)
                {
                    List<Entity> EntitiesToRemove = new List<Entity>();

                    List<RTSData> InstanceData1 = new List<RTSData>();
                    List<RTSData> InstanceData2 = new List<RTSData>();
                    string MeshPath = FoliageComponent.PrimaryMeshAsset;
                    foreach (Entity EntityItem in Entity.Children)
                    {
                        ModelComponent ModelComponent = EntityItem.GetModelComponent();
                        if (ModelComponent != null && ModelComponent.Models.Count > 0)
                        {
                            Model Model = ModelComponent.Models[0];
                            if (Model.ModelPath == MeshPath)
                            {
                                Transform TransformComponent = EntityItem.GetTransformComponent();
                                RTSData RTSData1 = new RTSData();
                                RTSData1.Translation = Vector3d.FromDouble3(TransformComponent.Translation);
                                RTSData1.Rotation = Vector3d.FromDouble3(TransformComponent.Rotation);
                                RTSData1.Scale = Vector3d.FromDouble3(TransformComponent.Scale);
                                InstanceData1.Add(RTSData1);

                                RTSData RTSData2 = new RTSData();
                                RTSData2.Translation = RTSData1.Translation;
                                RTSData2.Rotation = RTSData1.Rotation;
                                RTSData2.Scale = RTSData1.Scale;
                                InstanceData2.Add(RTSData2);

                                EntitiesToRemove.Add(EntityItem);
                            }
                        }
                    }
                    FoliageComponent.InstanceData = InstanceData1;

                    int Count = EntitiesToRemove.Count;
                    for (int i = Count - 1; i >= 0; i--)
                    {
                        Entity EntityItem = EntitiesToRemove[i];
                        Entity.RemoveChildEntity(EntityItem);
                        EntityItem.RuntimeRemove();
                    }

                    EditOperation_Foliage EditOperation_CombineTogether = new EditOperation_Foliage(FoliageEditOperationType.CombineTogether, Entity, EntitiesToRemove, InstanceData2);
                    EditOperationManager.GetInstance().AddOperation(EditOperation_CombineTogether);
                }
            }
        }

        public void CombineSceneFoliages()
        {
            World World = EditorScene.GetInstance().GetWorld();
            Entity Root = World.Root;
            EditOperationManager.GetInstance().BeginRecordCompoundOperation();
            CombineSceneFoliages(Root);
            EditOperationManager.GetInstance().EndRecordCompoundOperation();
            TreeItem SelectItem = _Tree.GetSelectedItem();
            UpdateHierarchy();
            if (SelectItem == null)
            {
                _Tree.SelectItem(null);
            }
        }

        public void CombineSceneFoliages(Entity Entity)
        {
            CombineTogether(Entity);
            foreach (Entity EntityChild in Entity.Children)
            {
                CombineSceneFoliages(EntityChild);
            }
        }

        #endregion

        #region MenuItem Prefab Event

        void OnMenuItemCreatePrefab(MenuItem Sender)
        {
            Entity e = GetSelectedEntity();
            if (e != null)
            {
                string path = "";
                e.CreatePrefab(path, e.GetName() == "DirectionalLight");
                UpdateHierarchy();
                ProjectUI.GetInstance().UpdateAll();
            }
        }

        void OnMenuItemOpenPrefab(MenuItem Sender)
        {
            Entity e = GetSelectedEntity();
            if (e != null)
            {
                MainUI.GetInstance().ActivateDockingCard_PrefabEditor();
                PrefabSceneUI.GetInstance().OpenPrefab(e);
            }
        }

        void OnMenuItemRevertPrefab(MenuItem Sender)
        {
            Entity e = GetSelectedEntity();
            if (e != null)
            {
                bool ret = PrefabManager.GetInstance().RevertPrefabEntity(e.World.GetNativePointer(), e);
                if (ret)
                {
                    e.RefreshEntity(false);
                    InspectorUI.GetInstance().InspectObject();
                }
            }
        }

        void OnMenuItemRevertPrefabCompletely(MenuItem Sender)
        {
            Entity e = GetSelectedEntity();
            if (e != null)
            {
                bool ret = PrefabManager.GetInstance().RevertPrefabInstance(e.World.GetNativePointer(), e, true);
                if (ret)
                {
                    e.RefreshEntity(true);
                    InspectorUI.GetInstance().ReadValue();
                }
            }
        }

        void OnMenuItemApplyHierarhyToPrefab(MenuItem Sender)
        {
            Entity e = GetSelectedEntity();
            if (e != null)
            {
                if (e.CanUpdateHierarhy())
                {
                    World prefabWorld = PrefabScene.GetInstance().GetWorld();
                    PrefabSceneUI.GetInstance().UpdatePrefab(e, e.World, prefabWorld);
                }
                else
                {
                    World prefabWorld = PrefabScene.GetInstance().GetWorld();
                    PrefabSceneUI.GetInstance().UpdatePrefab(e, e.World, prefabWorld, false);
                }
            }
        }

        void OnMenuItemUnlinkPrefab(MenuItem Sender)
        {
            Entity e = GetSelectedEntity();
            if (e != null)
            {
                e.UnlinkPrefab(false);
            }
            UpdateHierarchy();
        }

        void OnMenuItemUpdatePrefab(MenuItem Sender)
        {
            PrefabSceneUI.GetInstance().UpdatePrefab(GetSelectedEntity(), PrefabScene.GetInstance().GetWorld(), EditorScene.GetInstance().GetWorld());
        }

        void OnMenuItemUnlinkPrefabCompletely(MenuItem Sender)
        {
            Entity e = GetSelectedEntity();
            if (e != null)
            {
                e.UnlinkPrefab(true);
            }
            UpdateHierarchy();
        }

        #endregion

        #region Create Entity and Componet Event

        void EndAddEntity(Entity Entity)
        {
            EndAddEntities(new List<Entity> { Entity });
        }

        void EndAddEntities(List<Entity> Entities)
        {
            foreach (Entity Entity in Entities)
            {
                EditOperation_AddChildEntity EditOperation = new EditOperation_AddChildEntity(Entity.Parent, Entity);
                EditOperationManager.GetInstance().AddOperation(EditOperation);
                if (GameObject.GetGameObjectFromEntity(Entity) == null)
                {
                    var World = _CurScene.GetWorld();
                    GOContext GoContext = new GOContext(World._WorldInterface);
                    GOHandle handle = new GOHandle();
                    handle.EntityID = Entity.EntityID;
                    handle.GOType = "GameObject";
                    handle.GOName = Entity.GetName();

                    GoContext.TransferToGameObjectWithHandle(handle);
                    World.Root.RefreshTree();
                }
            }

            UpdateHierarchy();
            SelectEntities(Entities);
            EditorScene.GetInstance().SetDirty();
        }

        Entity DoCreateEntity(bool bForceRoot)
        {
            Entity ParentEntity = GetParentEntity(bForceRoot);
            if (ParentEntity == null)
            {
                return null;
            }
            Entity NewEntity = ParentEntity.World.CreateEntity();
            ParentEntity.AddChildEntity(NewEntity);
            return NewEntity;
        }

        void DoSetName(Entity Entity, string NamePrefix)
        {
            string Name = EditorScene.GetInstance().CalculateNewName(Entity.Parent, NamePrefix);
            Entity.SetName(Name);
        }

        void DoCreateModelComponent(Entity Entity, string Path)
        {
            ModelComponent ModelComponent = Entity.CreateComponent<ModelComponent>();
            ModelComponent.Reset();
            List<Model> ModelList = new List<Model>();
            ModelList.Add(new Model(Path));
            ModelComponent.Models = ModelList;
        }

        void DoCreateTransformComponent(Entity Entity)
        {
            Transform TransformComponent = Entity.CreateComponent<Transform>();
            Entity.RuntimeJointToParent();
            // After setting entity local matrix to identity matrix, it will present at parent's local center.
            Matrix4x4d Identity = new Matrix4x4d();
            Identity.LoadIdentity();
            TransformComponent.SetMatrix(ref Identity);
        }

        void DoCreatePhysicsComponent(Entity Entity)
        {
            Physics PhysicsComponent = Entity.CreateComponent<Physics>();
            // do something
        }

        void DoCreateLightComponent(Entity Entity, LightType LightType)
        {
            Light Light = Entity.CreateComponent<Light>();
            Light.Reset();
            LightComponentG comp = Light.mLight;

            comp.mType = LightType;
            comp.mColor.x = 1.0f;
            comp.mColor.y = 1.0f;
            comp.mColor.z = 1.0f;
            Light.mLight = comp;
        }

        void DoCreateSkyLightComponent(Entity entity)
        {
            var skyLight = entity.CreateComponent<SkyLightComponent>();
            skyLight.Reset();
        }

        void DoCreateDecalComponent(Entity entity)
        {
            var decal = entity.CreateComponent<DecalComponent>();
            decal.Reset();
        }
        void DoCreateReflectionProbeComponent(Entity Entity)
        {
            ReflectionProbeComponent reflectionProbeComponent = Entity.CreateComponent<ReflectionProbeComponent>();
            reflectionProbeComponent.Reset();
        }
        void DoCreateParticleSystemComponent(Entity Entity)
        {
            ParticleSystemComponent ParticleSystemComponent = Entity.CreateComponent<ParticleSystemComponent>();
            ParticleSystemComponent.Reset();
        }
        void DoCreatePostProcessVolumeComponent(Entity Entity)
        {
            PostProcessVolumeComponent postProcessVolumeComponent = Entity.CreateComponent<PostProcessVolumeComponent>();
            postProcessVolumeComponent.Reset();
        }
        void DoCreateCloudComponent(Entity Entity)
        {
            CloudComponent cloudComponent = Entity.CreateComponent<CloudComponent>();
            cloudComponent.Reset();
        }

        void DoCreateCameraComponent(Entity Entity)
        {
            Camera Camera = Entity.CreateComponent<Camera>();
            Camera.Reset();
        }

        void DoCreateVRViewComponent(Entity Entity)
        {
            var VRView = Entity.CreateComponent<VRViewComponent>();
            VRView.Reset();
        }

        void DoCreateScriptComponent(Entity Entity)
        {
            Script Script = Entity.CreateComponent<Script>();
            Script.Reset();
        }

        void DoCreateTerrainComponent(Entity Entity)
        {
            TerrainComponent TerrainComponent = Entity.CreateComponent<TerrainComponent>();
            TerrainComponent.Reset();
        }

        void DoCreatePostVolumeTriggerComponent(Entity Entity)
        {
            VolumeTriggerComponent VolumeTriggerComponent = Entity.CreateComponent<VolumeTriggerComponent>();
            VolumeTriggerComponent.Reset();
            Physics Physics = Entity.CreateComponent<Physics>();

            vector_cross_PhysicsGeometryBox Boxes = new vector_cross_PhysicsGeometryBox();
            Boxes.Add(new PhysicsGeometryBox());
            Physics.ExtraBoxes = Boxes;
            Physics.CollisionType = CollisionTypeValue.Actor;
            Physics.BlockMask.BlockWith(CollisionTypeValue.Actor);
            Physics.Reset();
        }

        void SelectAllEntity()
        {
            _Tree.SelectItemNoEvent(null);
            _Tree.SelectItemNoEvent(_Tree.GetRootItem());
            OnMenuItemSelectAllChildrenClicked(null);
        }

        #endregion

        #region Create Entity Menu Item Clicked Event

        public void OnMenuItemCreateEmptyClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateTransformComponent(Entity);
                DoSetName(Entity, "Entity");
                EndAddEntity(Entity);
            }
        }

        public void OnMenuItemCreateCubeClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateModelComponent(Entity, "EngineResource/Model/Cube.nda");
                DoCreateTransformComponent(Entity);
                DoCreatePhysicsComponent(Entity);
                DoSetName(Entity, "Cube");
                EndAddEntity(Entity);
            }
        }

        public void OnMenuItemCreateSphereClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateModelComponent(Entity, "EngineResource/Model/Sphere.nda");
                DoCreateTransformComponent(Entity);
                DoCreatePhysicsComponent(Entity);
                DoSetName(Entity, "Sphere");
                EndAddEntity(Entity);
            }
        }

        public void OnMenuItemCreateCapsuleClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateModelComponent(Entity, "EngineResource/Model/Capsule.nda");
                DoCreateTransformComponent(Entity);
                DoCreatePhysicsComponent(Entity);
                DoSetName(Entity, "Capsule");
                EndAddEntity(Entity);
            }
        }

        public void OnMenuItemCreateCylinderClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateModelComponent(Entity, "EngineResource/Model/Cylinder.nda");
                DoCreateTransformComponent(Entity);
                DoCreatePhysicsComponent(Entity);
                DoSetName(Entity, "Cylinder");
                EndAddEntity(Entity);
            }
        }

        public void OnMenuItemCreateConeClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateModelComponent(Entity, "EngineResource/Model/Cone.nda");
                DoCreateTransformComponent(Entity);
                DoCreatePhysicsComponent(Entity);
                DoSetName(Entity, "Cone");
                EndAddEntity(Entity);
            }
        }

        public void OnMenuItemCreatePlaneClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateModelComponent(Entity, "EngineResource/Model/EditorPlane.nda");
                DoCreateTransformComponent(Entity);
                DoCreatePhysicsComponent(Entity);
                DoSetName(Entity, "Plane");
                EndAddEntity(Entity);
            }
        }

        public void OnMenuItemCreateDirectionalLightClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateTransformComponent(Entity);
                DoCreateLightComponent(Entity, LightType.Directional);
                DoSetName(Entity, "DirectionalLight");
                EndAddEntity(Entity);
            }
        }

        public void OnMenuItemCreatePointLightClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateTransformComponent(Entity);
                DoCreateLightComponent(Entity, LightType.Point);
                DoSetName(Entity, "PointLight");
                EndAddEntity(Entity);
            }
        }

        public void OnMenuItemCreateSpotLightClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateTransformComponent(Entity);
                DoCreateLightComponent(Entity, LightType.Spot);
                DoSetName(Entity, "SpotLight");
                EndAddEntity(Entity);
            }
        }

        public void OnMenuItemCreateRectLightClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateTransformComponent(Entity);
                DoCreateLightComponent(Entity, LightType.Rect);
                DoSetName(Entity, "RectLight");
                EndAddEntity(Entity);
            }
        }

        public void OnMenuItemCreateSkyLightClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateTransformComponent(Entity);
                DoCreateSkyLightComponent(Entity);
                DoSetName(Entity, "SkyLight");
                EndAddEntity(Entity);
            }
        }

        //TODO
        public void OnMenuItemCreateReflectionProbeClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateTransformComponent(Entity);
                DoCreateReflectionProbeComponent(Entity);
                DoSetName(Entity, "ReflectionProbe");
                EndAddEntity(Entity);
            }
        }

        public void OnMenuItemCreateTerrainClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            CreateTerrainEntity(bForceRoot);
        }

        public Entity CreateTerrainEntity(bool bForceRoot)
        {
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateTransformComponent(Entity);
                DoCreateTerrainComponent(Entity);
                DoSetName(Entity, "Terrain");
                EndAddEntity(Entity);
            }
            return Entity;
        }

        public void OnMenuItemCreateDecalClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateTransformComponent(Entity);
                DoCreateDecalComponent(Entity);

                // Set default local rotation as (-90, 0, 0)
                Transform transform = Entity.GetTransformComponent();
                if (transform != null)
                {
                    transform.Rotation = new Double3(-90, 0, 0);
                }

                DoSetName(Entity, "DecalActor");
                EndAddEntity(Entity);
            }
        }

        public void OnMenuItemCreateParticleSystemClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {

                DoCreateTransformComponent(Entity);
                DoCreateParticleSystemComponent(Entity);
                DoSetName(Entity, "ParticleSystem");
                if (!Entity.HasComponent(typeof(RenderProperty)))
                {
                    Entity.CreateComponent<RenderProperty>();
                }
                EndAddEntity(Entity);
            }
        }

        public void OnMenuItemCreatePostProcessVolumeClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateTransformComponent(Entity);
                DoCreatePostProcessVolumeComponent(Entity);
                DoSetName(Entity, "PostProcessVolume");
                EndAddEntity(Entity);
            }
        }

        public void OnMenuItemCreateVolumeTriggerClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateTransformComponent(Entity);
                Transform Transform = Entity.GetTransformComponent();
                Transform.Translation = new Double3(0, 120, 0);
                Transform.Scale = new Double3(100, 100, 100);
                DoCreatePostVolumeTriggerComponent(Entity);
                DoSetName(Entity, "VolumeTrigger");
                EndAddEntity(Entity);
            }
        }

        public void OnMenuItemCreateCloudClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateTransformComponent(Entity);
                DoCreateCloudComponent(Entity);
                //DoCreateModelComponent(Entity, "EngineResource/Model/Cube.nda");
                DoSetName(Entity, "Cloud");
                EndAddEntity(Entity);
            }
        }

        public Entity CreateCamera(string CameraName, bool bForceRoot)
        {
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateTransformComponent(Entity);
                DoCreateCameraComponent(Entity);
                DoSetName(Entity, CameraName);
                EndAddEntity(Entity);
            }
            return Entity;
        }

        public void OnMenuItemCreateCameraClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            CreateCamera("Camera", bForceRoot);
        }

        public void OnMenuItemCreateVRViewClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateTransformComponent(Entity);
                DoCreateCameraComponent(Entity);
                DoCreateVRViewComponent(Entity);
                DoSetName(Entity, "VRViewCamera");
                EndAddEntity(Entity);
            }
        }

        public void OnMenuItemCreateScriptClicked(MenuItem Sender)
        {
            bool bForceRoot = ShouldForceRoot(Sender);
            Entity Entity = DoCreateEntity(bForceRoot);
            if (Entity != null)
            {
                DoCreateTransformComponent(Entity);
                DoCreateScriptComponent(Entity);
                DoSetName(Entity, "Script");
                EndAddEntity(Entity);
            }
        }

        #endregion

        #region User Config

        public void SaveUserConfig(Record RootRecord)
        {
            Record RecordHierarchyUI = RootRecord.AddChild();
            RecordHierarchyUI.SetTypeString("HierarchyUI");
            Record RecordHierarchyTreeState = RecordHierarchyUI.AddChild();
            TreeState HierarchyTreeState = _Tree.SaveState();
            HierarchyTreeState.Save(RecordHierarchyTreeState);
        }

        public void LoadUserConfig(Record RootRecord)
        {
            Record RecordHierarchyUI = RootRecord.FindByTypeString("HierarchyUI");
            if (RecordHierarchyUI != null)
            {
                Record RecordTreeState = RecordHierarchyUI.FindByTypeString("TreeState");
                TreeState TreeState = new TreeState();
                TreeState.Load(RecordTreeState);
                _Tree.LoadState(TreeState);
                TreeItem RootItem = _Tree.GetRootItem();
                RootItem.SetExpanded(true);
            }
        }

        #endregion

        #region Entity Visibility

        public void SwitchSelectedVisibility()
        {
            List<Entity> Entities = GetSelectedEntities();
            if (Entities.Count == 0)
            {
                return;
            }

            List<Entity> InvisibleEntities = new List<Entity>();
            List<Entity> VisibleEntities = new List<Entity>();
            foreach (Entity Entity in Entities)
            {
                if (Entity.Enable)
                {
                    VisibleEntities.Add(Entity);
                }
                else
                {
                    InvisibleEntities.Add(Entity);
                }
            }

            EditOperation_ChangeVisibility EditOperation = new EditOperation_ChangeVisibility(VisibleEntities, InvisibleEntities);
            EditOperationManager.GetInstance().AddOperation(EditOperation);
            EditOperation.Redo();
        }

        void SetVisibility(List<Entity> Entities, bool visibility)
        {
            List<Entity> Empty = new List<Entity>();
            EditOperation_ChangeVisibility EditOperation = new EditOperation_ChangeVisibility(visibility ? Empty : Entities, visibility ? Entities : Empty);
            EditOperationManager.GetInstance().AddOperation(EditOperation);
            EditOperation.Redo();
        }

        void SetChildrenVisibility(List<Entity> Entities, bool visibility)
        {
            if (Entities.Count != 1)
                return;

            SetVisibility(Entities[0].Children, visibility);
        }

        void SetRootVisibility(bool visibility)
        {
            Entity Root = _Tree.GetRootItem().GetTagObject() as Entity;
            SetVisibility(new List<Entity> { Root }, visibility);
        }

        Panel CreateVisibilityButton()
        {
            Panel Button = new Panel();

            Button.SetSize(20, 20);
            Button.LeftMouseDoubleClickedEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            {
                bContinue = false;
            };
            Action<List<Entity>, bool> SwitchVisibility = (Entities, bEnable) =>
            {
                SetVisibility(Entities, bEnable);
            };
            Button.LeftMouseUpEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            {
                bContinue = false;
                TreeItem Item = Sender.GetTagObject() as TreeItem;
                Entity Entity = Item.GetTagObject() as Entity;
                bool bEnable = Entity.Enable;
                List<Entity> SelectEntities = GetSelectedEntities();
                if (SelectEntities.Contains(Entity))
                {
                    SwitchVisibility(SelectEntities, !bEnable);
                }
                else
                {
                    SwitchVisibility(new List<Entity> { Entity }, !bEnable);
                }
            };

            return Button;
        }

        private void OnVisibilityPanelMouseMove(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            for (int i = 0; i < Sender.GetChildCount(); ++i)
            {
                Panel Child = Sender.GetChild(i) as Panel;
                if (Child.GetVisible())
                {
                    TreeItem Item = Child.GetTagObject() as TreeItem;
                    Entity Entity = Item.GetTagObject() as Entity;
                    if (Entity.World != null)
                    {
                        if (!Entity.Enable || _Tree.InSelectedItems(Item) || (_Tree.IsPointIn(MouseX, MouseY) &&
                        MouseY >= Child.GetScreenY() && MouseY < Child.GetScreenY() + 20))
                        {
                            Child.SetImage(Entity.Enable ? _TextureEntityVisible : _TextureEntityHidden);
                        }
                        else
                        {
                            Child.SetImage(null);
                        }
                    }
                }
            }
        }

        public void UpdateVisibilityButton()
        {
            int Y = _Tree.GetBaseY();
            for (int i = 0; i < _VisibilityPanel.GetChildCount(); ++i)
            {
                Panel Child = _VisibilityPanel.GetChild(i) as Panel;
                TreeItem Item = Child.GetTagObject() as TreeItem;
                bool bItemVisible = true;
                TreeItem Parent = Item.GetParent();
                while (Parent != null)
                {
                    if (Parent.GetExpanded() == false)
                    {
                        bItemVisible = false;
                        break;
                    }
                    Parent = Parent.GetParent();
                }
                Child.SetVisible(bItemVisible);

                if (Child.GetVisible())
                {
                    Entity Entity = Item.GetTagObject() as Entity;
                    UpdateTextColor(Item, Entity);
                    if (!Entity.Enable || _Tree.InSelectedItems(Item))
                    {
                        Child.SetImage(Entity.Enable ? _TextureEntityVisible : _TextureEntityHidden);
                    }
                    else
                    {
                        Child.SetImage(null);
                    }

                    Child.SetY(Y);
                    Y += 20;
                }
            }

            if (Y >= _Tree.GetHeight() - (_Tree.GetVScroll().GetVisible() ? 18 : 0))
            {
                _VisibilityPanel.SetX(_Container.GetWidth() - 40);
            }
            else
            {
                _VisibilityPanel.SetX(_Container.GetWidth() - 20);
            }
        }

        #endregion

        #region CenterPosition
        Double3 GetCenterPosition(List<Entity> Entities)
        {
            Double3 TotalPos = new Vector3f(0.0f, 0.0f, 0.0f);
            foreach (Entity Value in Entities)
            {
                TotalPos = TotalPos.Add(Value.GetTransformComponent().GetWorldTranslation());
            }
            return TotalPos.Divide(Entities.Count);
        }
        #endregion

        #region entity split and combine
        void SplitEntity(Entity entity)
        {
            ModelComponent mc = entity.GetModelComponent();
            if (mc != null)
            {
                List<Model> models = mc.GetModels();
                if (models.Count > 0 && models[0].LODProperties.Count > 0)
                {
                    MeshAssetDataResource meshData = (MeshAssetDataResource)Resource.Get(models[0].ModelPath, false);
                    List<string> materials = new List<string>();
                    for (int i = 0; i < models[0].LODProperties.Count; i++)
                    {
                        for (int j = 0; j < models[0].LODProperties[i].SubModels.Count; j++)
                        {
                            materials.Add(models[0].LODProperties[i].SubModels[j].MaterialPath);///May Cause Problem
                        }
                    }
                    MeshSeparateUI MeshSeparateUI = MeshSeparateUI.GetInstance();
                    MeshSeparateUI.Initialize(GetUIManager());
                    MeshSeparateUI.SetMesh(meshData, materials);
                    DialogUIManager.GetInstance().ShowDialogUI(MeshSeparateUI);
                }
            }
        }

        void CombineEntity(Entity entity)
        {
            Dictionary<string, vector_string> fxMeshs = new Dictionary<string, vector_string>();
            Dictionary<string, vector_string> fxMats = new Dictionary<string, vector_string>();
            Dictionary<string, vector_cross_Float4x4> fxTrans = new Dictionary<string, vector_cross_Float4x4>();
            void GetAllModel(Entity root, Matrix4x4d matrix)
            {
                Transform transform = root.GetTransformComponent();
                Matrix4x4d newMatrix = new Matrix4x4d();
                transform.GetMatrix(ref newMatrix);
                newMatrix.Mul(ref newMatrix, ref matrix);

                ModelComponent mc = root.GetModelComponent();
                if (mc != null)
                {
                    List<Model> models = mc.GetModels();
                    if (models.Count > 0 && models[0].LODProperties.Count > 0)
                    {
                        for (int j = 0; j < models[0].LODProperties[0].SubModels.Count; j++)
                        {
                            string matPath = models[0].LODProperties[0].SubModels[j].MaterialPath;
                            string fxPath = ((Material)(Material)Resource.Get(matPath)).GetFxPath();
                            if (!fxMeshs.ContainsKey(fxPath))
                            {
                                fxMeshs[fxPath] = new vector_string();
                                fxMats[fxPath] = new vector_string();
                                fxTrans[fxPath] = new vector_cross_Float4x4();
                            }
                            fxMeshs[fxPath].Add(models[0].ModelPath);
                            fxMats[fxPath].Add(matPath);
                            fxTrans[fxPath].Add(newMatrix.ToFloat4x4());
                        }
                    }
                }

                foreach (Entity child in root.Children)
                {
                    GetAllModel(child, newMatrix);
                }
            }
            Matrix4x4d matrix = new Matrix4x4d();
            matrix.LoadIdentity();
            GetAllModel(entity, matrix);

            foreach (var fxPath in fxMeshs.Keys)
            {
                MeshCombineUI MeshCombineUI = MeshCombineUI.GetInstance();
                MeshCombineUI.Initialize(GetUIManager());
                MeshCombineUI.SetParameters(fxMeshs[fxPath], fxMats[fxPath], fxTrans[fxPath]);
                DialogUIManager.GetInstance().ShowDialogUI(MeshCombineUI);
            }
        }

        void CombineEntities(List<Entity> entities)
        {
            vector_string meshs = new vector_string();
            vector_string mats = new vector_string();
            vector_cross_Float4x4 trans = new vector_cross_Float4x4();
            foreach (Entity en in entities)
            {
                ModelComponent mc = en.GetModelComponent();
                if (mc != null)
                {
                    List<Model> models = mc.GetModels();
                    if (models.Count > 0 && models[0].LODProperties.Count > 0)
                    {
                        Transform transform = en.GetTransformComponent();
                        Matrix4x4d LocalMatrix = new Matrix4x4d();
                        transform.GetMatrix(ref LocalMatrix);

                        meshs.Add(models[0].ModelPath);
                        foreach (var model in models[0].LODProperties[0].SubModels)
                        {
                            mats.Add(model.MaterialPath);
                        }
                        trans.Add(LocalMatrix.ToFloat4x4());
                    }
                }
            }
            MeshCombineUI MeshCombineUI = MeshCombineUI.GetInstance();
            MeshCombineUI.Initialize(GetUIManager());
            MeshCombineUI.SetParameters(meshs, mats, trans);
            DialogUIManager.GetInstance().ShowDialogUI(MeshCombineUI);
        }

        #endregion

        #region Relate Sequencer
        public void ProcessLevelSequence(string path)
        {
            World World = EditorScene.GetInstance().GetWorld();
            Entity Root = World.Root;
            Queue<Entity> Queue = new Queue<Entity>();
            Queue.Enqueue(Root);
            while (Queue.Count != 0)
            {
                Entity Head = Queue.Dequeue();
                if (Head.HasComponent(typeof(ControllableUnitComponent)))
                {
                    if (Head.GetComponent<ControllableUnitComponent>().ControllerType == ControllableUnitType.CurveController
                         && CinematicUI.GetInstance().GetCurveCtrResPath(Head) == path && (Head.GetName().Contains("LevelSequence") ||
                         Head.Parent != null && Head.Parent.HasComponent(typeof(TODLightComponent))))
                    {
                        CinematicUI.GetInstance().SetLevelSequenceEntity(Head);
                        return;
                    }
                }

                foreach (var Child in Head.Children)
                {
                    Queue.Enqueue(Child);
                }
            }

            Entity Entity = DoCreateEntity(true);
            if (Entity != null)
            {
                DoCreateTransformComponent(Entity);
                CinematicUI.GetInstance().AddControllableUnitComponent(Entity);
                DoSetName(Entity, "LevelSequenceEntity");
                EndAddEntity(Entity);
                CinematicUI.GetInstance().SetLevelSequenceEntity(Entity);
            }
        }

        public bool SequenceIsPlaying()
        {
            return CinematicUI.GetInstance().GetIsPlaying();
        }
        #endregion

        #region Piolot Relate
        public void MoveEntityToEditorCamera()
        {
            if (_CurPilotEntity != null)
            {
                Transform Transform = _CurPilotEntity.GetTransformComponent();
                Transform CameraTransform = _CurScene.GetCameraEntityTransform();

                if (Transform != null)
                {
                    Transform.SetWorldTranslation(CameraTransform.GetWorldTranslation());
                    Transform.SetWorldRotation(CameraTransform.GetWorldRotation());
                }
            }
        }

        public void MoveEditorCameraToEntity(Entity Entity)
        {
            Transform Transform = Entity.GetTransformComponent();
            Transform CameraTransform = _CurScene.GetCameraEntityTransform();

            CameraTransform.SetWorldTranslation(Transform.GetWorldTranslation());
            CameraTransform.SetWorldRotation(Transform.GetWorldRotation());
        }
        #endregion
    }
}
