using EditorUI;
using System;
using System.Diagnostics;
using System.Reflection;

namespace CrossEditor
{
    public class EditorLogger
    {
        public static void Log(LogMessageType LogMessageType, string String)
        {
            StackTrace StackTrace1 = new StackTrace(true);
            StackFrame StackFrame1 = StackTrace1.GetFrame(1);
            string Filename = StackFrame1.GetFileName();
            int LineNumber = StackFrame1.GetFileLineNumber();
            MethodBase Method = StackFrame1.GetMethod();
            Type Type = Method.DeclaringType;
            string TypeName = Type.Name;
            string MethodName = Method.Name;
            Console.WriteLine("[Editor {0}]:[{1} Line:{2} {3}.{4}()]{5}", LogMessageType, Filename, LineNumber, TypeName, MethodName, String);
            ConsoleUI.GetInstance().AddLogItem(LogMessageType, String);
        }
    }
}
