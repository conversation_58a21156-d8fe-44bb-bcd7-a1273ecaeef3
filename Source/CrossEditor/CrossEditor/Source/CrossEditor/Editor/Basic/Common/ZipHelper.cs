using EditorUI;
using System;
using System.IO;
using System.IO.Compression;
using System.Text;

namespace CrossEditor
{
    class ZipHelper
    {
        public static void CompressFileZip(string filePath, string zipPath)
        {
            if (FileHelper.IsFileExists(zipPath))
            {
                string zipname = PathHelper.GetFileName(zipPath);
                string zipBackPath = zipPath.Replace(zipname, PathHelper.GetNameOfPath(filePath) + string.Format("{0:yyyyMMddHHmmss}", DateTime.Now) + "_back.zip");
                FileHelper.RenameFile(zipPath, zipBackPath);
            }
            FileInfo fileInfo = new FileInfo(filePath);
            string dirPath = fileInfo.DirectoryName?.Replace("\\", "/") + "/";
            string tempPath = dirPath + Guid.NewGuid() + "_temp/";
            if (!Directory.Exists(tempPath))
            {
                Directory.CreateDirectory(tempPath);
            }
            fileInfo.CopyTo(tempPath + fileInfo.Name);
            CompressDirectoryZip(tempPath, zipPath);
            DirectoryInfo directory = new DirectoryInfo(tempPath);
            if (directory.Exists)
            {
                //将文件夹属性设置为普通,如：只读文件夹设置为普通
                directory.Attributes = FileAttributes.Normal;

                directory.Delete(true);
            }
        }
        public static void CompressDirectoryZip(string folderPath, string zipPath)
        {
            if (FileHelper.IsFileExists(zipPath))
            {
                string zipname = PathHelper.GetFileName(zipPath);
                string zipBackPath = zipPath.Replace(zipname, PathHelper.GetNameOfPath(folderPath) + string.Format("{0:yyyyMMddHHmmss}", DateTime.Now) + "_back.zip");
                FileHelper.RenameFile(zipPath, zipBackPath);
            }
            DirectoryInfo directoryInfo = new DirectoryInfo(zipPath);

            if (directoryInfo.Parent != null)
            {
                directoryInfo = directoryInfo.Parent;
            }

            if (!directoryInfo.Exists)
            {
                directoryInfo.Create();
            }

            ZipFile.CreateFromDirectory(folderPath, zipPath, CompressionLevel.Optimal, true, Encoding.UTF8);
        }

        public static void DecompressZip(string zipPath, string folderPath)
        {
            DirectoryInfo directoryInfo = new DirectoryInfo(folderPath);

            if (!directoryInfo.Exists)
            {
                directoryInfo.Create();
            }

            ZipFile.ExtractToDirectory(zipPath, folderPath);
        }
    }
}
