using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    class FrameExecutor
    {
        static FrameExecutor _Instance = new FrameExecutor();

        public static FrameExecutor GetInstance()
        {
            return _Instance;
        }

        Queue<Operation> _OperationQueue;

        FrameExecutor()
        {
            _OperationQueue = new Queue<Operation>();
        }

        public void AddOperation(Operation Operation)
        {
            _OperationQueue.Enqueue(Operation);
        }

        public void Update()
        {
            if (_OperationQueue.Count > 0)
            {
                Operation Operation = _OperationQueue.Dequeue();
                Operation();
            }
        }
    }
}
