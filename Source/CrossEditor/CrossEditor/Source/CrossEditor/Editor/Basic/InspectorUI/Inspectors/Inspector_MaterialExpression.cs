using CEngine;
using Clicross;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_MaterialExpression : Inspector_Struct_With_Property
    {
        MaterialEditor mMaterialEditorContext;

        public void SetMaterialEditorContext(MaterialEditor context)
        {
            mMaterialEditorContext = context;
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            _Object = Object;

            ClearChildInspectors();
            Type Type = _Object.GetType();

            // None-Grouped Properties
            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(Type);
            List<PropertyInfo> NoneGroupedProperties = new List<PropertyInfo>();
            HashSet<string> Categories = new HashSet<string>();
            foreach (PropertyInfo PropertyInfo in Properties)
            {
                PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfo);
                // hide property in some situation
                if (PropertyInfo.PropertyType == typeof(Clicross.ExpressionInput) || PropertyInfo.PropertyType == typeof(Clicross.ExpressionOutput) ||
                        PropertyInfo.PropertyType == typeof(Clicross.ExpressionAttributesInput))
                {
                    continue;
                }
                if (PropertyInfoAttribute.Category == "")
                {
                    NoneGroupedProperties.Add(PropertyInfo);
                }
                else
                {
                    Categories.Add(PropertyInfoAttribute.Category);
                }
            }

            // Grouped Properties
            foreach (string Category in Categories)
            {
                Inspector_GroupedProperty Inspector_GroupedProperty = new Inspector_GroupedProperty(_Object, Category);
                Inspector_GroupedProperty.GetPropertyValueFunction = GetPropertyValueFunction;
                Inspector_GroupedProperty.SetPropertyValueFunction = SetPropertyValueFunction;
                Inspector_GroupedProperty.InspectObject(_Object);

                AddChildInspector(Inspector_GroupedProperty);
            }

            int exp_cnt = mMaterialEditorContext.GetSelectedExpressionsCount();

            foreach (PropertyInfo PropertyInfo in NoneGroupedProperties)
            {
                if (exp_cnt > 1)
                {
                    bool is_hide = false;
                    for (int i = 0; i < exp_cnt; i++)
                    {
                        bool is_find = false;
                        object exp = mMaterialEditorContext.GetSelectedExpression(i);
                        if (_Object.Equals(exp))
                            continue;
                        List<PropertyInfo> tmp_properties = PropertyCollector.CollectPropertiesOfType(exp.GetType());
                        foreach (PropertyInfo tmp_property in tmp_properties)
                        {
                            if (PropertyInfo.PropertyType == typeof(Clicross.ExpressionInput) || PropertyInfo.PropertyType == typeof(Clicross.ExpressionOutput) ||
                            PropertyInfo.PropertyType == typeof(Clicross.ExpressionAttributesInput))
                            {
                                continue;
                            }

                            if (tmp_property.Name == PropertyInfo.Name)
                            {
                                is_find = true;
                                break;
                            }
                        }
                        if(!is_find)
                        {
                            is_hide = true;
                            break;
                        }
                    }

                    if (!is_hide)
                    {
                        AddPropertyInspector(PropertyInfo, _Object);
                    }
                }
                else
                    AddPropertyInspector(PropertyInfo, _Object);
            }
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
        }

        public new object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);

            if (ValueExtraProperty != null)
            {
                List<object> ValueList = new List<object>();
                Type Expression_Type = Object.GetType();

                for (int i = 0; i < mMaterialEditorContext.GetSelectedExpressionsCount(); i++)
                {
                    object exp = mMaterialEditorContext.GetSelectedExpression(i);
                    object Value1 = GetPropertyValue(exp, PropertyName);
                    ValueList.Add(Value1);
                }
                ValueListChecker.CheckValueList(ValueExtraProperty, ValueList);
            }
            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(Object);
            }
            return null;
        }

        public void AddPropertyInspector(PropertyInfo PropertyInfo, object BindObject)
        {
            PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfo, BindObject);
            if (PropertyInfoAttribute.bHide)
            {
                return;
            }
            if (PropertyInfoAttribute.bAdvanced)
            {
                if (_bHasAdvancedChild == false)
                {
                    InitializeAdvancedButton();
                }
                _bHasAdvancedChild = true;
            }
            if (PropertyInfoAttribute.bModified)
            {
                if (_bHasModifyChild == false)
                {
                    InitializeModifiedButton(PropertyInfoAttribute.DisplayName);
                }
                _bHasModifyChild = true;
            }
            ObjectProperty ObjectProperty = new ObjectProperty();
            ObjectProperty.Object = BindObject;
            ObjectProperty.Name = PropertyInfo.Name;
            if (PropertyInfoAttribute.DisplayName != "")
            {
                ObjectProperty.DisplayName = PropertyInfoAttribute.DisplayName;
            }
            else
            {
                ObjectProperty.DisplayName = PropertyInfo.Name;
            }

            if (ObjectProperty.DisplayName.StartsWith("m_"))
            {
                ObjectProperty.DisplayName = ObjectProperty.DisplayName.Substring(2);
            }

            ObjectProperty.Type = PropertyInfo.PropertyType;
            ObjectProperty.ReadOnly = PropertyInfoAttribute.bReadOnly;
            ObjectProperty.Advanced = PropertyInfoAttribute.bAdvanced;
            ObjectProperty.ValueMin = Convert.ToDecimal(PropertyInfoAttribute.ValueMin);
            ObjectProperty.ValueMax = Convert.ToDecimal(PropertyInfoAttribute.ValueMax);
            ObjectProperty.ValueStep = Convert.ToDecimal(PropertyInfoAttribute.ValueStep);
            ObjectProperty.DefaultValue = PropertyInfoAttribute.DefaultValue;
            ObjectProperty.KeyFrame = PropertyInfoAttribute.bKeyFrame;
            ObjectProperty.PropertyInfoAttribute = PropertyInfoAttribute;
            BindPropertyFunction(ref ObjectProperty);

            string PropertyTypeString = PropertyInfo.PropertyType.ToString();
            if (PropertyInfoAttribute.PropertyType != "")
            {
                PropertyTypeString = PropertyInfoAttribute.PropertyType;
            }
            Inspector Inspector_Property = InspectorManager.GetInstance().CreatePropertyInspector(PropertyTypeString, ObjectProperty.Type.IsEnum);
            AddChildInspector(Inspector_Property);
            Inspector_Property.InspectProperty(ObjectProperty);
        }

        public new void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            for(int i = 0; i < mMaterialEditorContext.GetSelectedExpressionsCount(); i++)
            {
                object tmp_obj = mMaterialEditorContext.GetSelectedExpression(i);
                Type type = tmp_obj.GetType();
                PropertyInfo PropertyInfo = tmp_obj.GetType().GetProperty(PropertyName);

                if (PropertyInfo != null)
                {
                    if (type.IsSubclassOf(typeof(Clicross.MaterialExpressionShaderConst)) && PropertyName == "m_Name")
                    {
                        if (!IsShaderConstNameValid(PropertyValue as string))
                        {
                            ReadValue();
                            return;
                        }
                    }

                    PropertyInfo.SetValue(tmp_obj, PropertyValue);

                    PropertyModifiedEvent?.Invoke(tmp_obj, PropertyInfo);
                }
            }
            
            if ((Object is MaterialExpressionMaterialParameterCollection) && PropertyName == "m_MpcFile")
            {
                ((MaterialExpressionMaterialParameterCollection)Object).m_MpcEnum.OnChangeMpcFilePath(PropertyValue.ToString());
                InspectObject(Object);
            }
        }

        bool IsShaderConstNameValid(string name)
        {
            if (name.Length == 0)
            {
                return false;
            }

            if (!((name[0] >= 'a' && name[0] <= 'z') || (name[0] >= 'A' && name[0] <= 'Z') || name[0] == '_' || name[0] == '@'))
            {
                return false;
            }

            for (int i = 1; i < name.Length; i++)
            {
                if (!((name[i] >= 'a' && name[i] <= 'z') || (name[i] >= 'A' && name[i] <= 'Z') || name[i] == '_' || (name[i] >= '0' && name[i] <= '9')))
                {
                    return false;
                }
            }

            for (int i = 0; i < mMaterialEditorContext.GetMaterialParameterGroupsCount(); i++)
            {
                foreach (var param in mMaterialEditorContext.GetMaterialParameterGroup(i).Parameters)
                {
                    if (name == param.ParameterName)
                    {
                        return false;
                    }
                }

            }

            return true;
        }
    }
}
