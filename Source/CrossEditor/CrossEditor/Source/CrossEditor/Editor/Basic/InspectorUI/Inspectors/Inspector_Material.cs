using CEngine;
using EditorUI;
using System;

namespace CrossEditor
{
    class Inspector_Material : Inspector
    {
        protected Material _Material;

        protected Panel _PanelIcon;
        protected Label _LabelName;

        public override void InspectObject(object Object, object Tag = null)
        {
            _Material = (Material)Object;

            _PanelIcon = new Panel();
            _PanelIcon.Initialize();
            _PanelIcon.SetEnable(true);
            _PanelIcon.SetTagString1(_Material.Path);
            // --- force refresh icon
            RefreshIcon(true);
            // ---
            ThumbnailHelper.GetInstance().EnableThumbnail(_PanelIcon);
            _SelfContainer.AddChild(_PanelIcon);

            _LabelName = new Label();
            _LabelName.Initialize();
            _LabelName.SetFontSize(16);
            string MaterialName = PathHelper.GetNameOfPath(_Material.Path);
            _LabelName.SetText(MaterialName + "(Material)");
            _SelfContainer.AddChild(_LabelName);

            ObjectProperty ParentObjectProperty = new ObjectProperty();
            ParentObjectProperty.Object = _Material;
            ParentObjectProperty.Type = typeof(string);
            ParentObjectProperty.PropertyInfoAttribute = new PropertyInfoAttribute();
            ParentObjectProperty.PropertyInfoAttribute.ObjectClassID1 = ClassIDType.CLASS_Fx;
            ParentObjectProperty.PropertyInfoAttribute.ObjectClassID2 = ClassIDType.CLASS_Material;
            if (_Material.IsOverride)
            {
                ParentObjectProperty.Name = "Parent";
                ParentObjectProperty.GetPropertyValueFunction = (object obj, string name, ValueExtraProperty ValueExtraProperty) => { return _Material.Parent; };
                ParentObjectProperty.SetPropertyValueFunction = (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
                    =>
                { SetParentValue(PropertyName, "Parent", PropertyValue, SubProperty); };
            }
            else
            {
                ParentObjectProperty.Name = "Fx";
                ParentObjectProperty.GetPropertyValueFunction = (object obj, string name, ValueExtraProperty ValueExtraProperty) => { return _Material.Parent; };
                ParentObjectProperty.SetPropertyValueFunction = (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
                    =>
                { SetParentValue(PropertyName, "Fx", PropertyValue, SubProperty); };
            }
            Inspector ParentStr_Property = InspectorManager.GetInstance().CreatePropertyInspector("StringAsResource", ParentObjectProperty.Type.IsEnum);
            ParentStr_Property.InspectProperty(ParentObjectProperty);
            AddChildInspector(ParentStr_Property);

            var ParameterCollection = (MaterialParameterCollection)Resource.Get(_Material.GetMPCPath(),false);
            if (ParameterCollection != null)
            {
                Inspector MPC_Inspector = InspectorManager.GetInstance().CreatePropertyInspector("StringAsResource", false);
                ObjectProperty MPCProperty = new ObjectProperty();
                MPCProperty.Object = ParameterCollection;
                MPCProperty.Type = typeof(string);
                MPCProperty.PropertyInfoAttribute = new PropertyInfoAttribute();
                MPCProperty.PropertyInfoAttribute.FileTypeDescriptor = "Material Parameter Collection#mpc";
                MPCProperty.PropertyInfoAttribute.ObjectClassID1 = ClassIDType.CLASS_MaterialParameterCollection;
                MPCProperty.Name = "Material Parameter Collection";
                MPCProperty.GetPropertyValueFunction = (object obj, string name, ValueExtraProperty ValueExtraProperty) =>
                {
                    return ParameterCollection.GetGUID();
                };
                MPCProperty.SetPropertyValueFunction = (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty) =>
                {
                    _Material.SetParameterCollection(PropertyValue.ToString());
                    RefreshIcon();
                    OperationQueue.GetInstance().AddOperation(() => { GetInspectorHandler().ReadValue(); });
                };
                MPC_Inspector.InspectProperty(MPCProperty);
                AddChildInspector(MPC_Inspector);

                ParameterCollectionUsages UsagesWrapper = CEResource.GetMaterialParameterUsages(_Material.ResourcePtr.GetPointer());
                Inspector_Property_List MPC_UsageList = InspectorManager.GetInstance().CreatePropertyInspector("List", false) as Inspector_Property_List;
                ObjectProperty UsageProperty = new ObjectProperty();
                UsageProperty.Object = UsagesWrapper;
                UsageProperty.Type = UsagesWrapper.Members.GetType();
                UsageProperty.Name = "Collection Usage";
                UsageProperty.ReadOnly = false;
                UsageProperty.PropertyInfoAttribute = new PropertyInfoAttribute();
                UsageProperty.PropertyInfoAttribute.ChildPropertyType = typeof(string).ToString();
                UsageProperty.GetPropertyValueFunction = (object obj, string name, ValueExtraProperty ValueExtraProperty) =>
                {
                    return UsagesWrapper.Members;
                };
                UsageProperty.SetPropertyValueFunction += (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty) =>
                {
                    if (PropertyValue is vector_string)
                    {
                        CEResource.SetParameterUsage(_Material.ResourcePtr.GetPointer(), (vector_string)PropertyValue);
                        _Material.HandlePropertyChanged();
                    }
                };
                MPC_UsageList.InspectProperty(UsageProperty);

                AddChildInspector(MPC_UsageList);
            }

            Fx ParentFx = (Fx)Resource.Get(_Material.GetFxPath());
            if (ParentFx != null)
            {
                foreach (var Group in ParentFx.Groups)
                {
                    if (Group.Properties.Count == 0)
                        continue;
                    Inspector_GroupedProperty GroupedProperty = new Inspector_GroupedProperty(_Material, Group.Name);
                    GroupedProperty.BuildPropertyInspector = () =>
                    {
                        foreach (Fx.Property FxProperty in Group.Properties)
                        {
                            Material.Property Property = _Material.Properties.Find(Item => Item.Name == FxProperty.Name);
                            if (Property is null)
                            {
                                Console.WriteLine("[Material Load Warning] Property \"{0}\" is not in the material, but in Fx!", FxProperty.Name);
                                continue;
                            }
                            if (!FxProperty.Usage || !FxProperty.Visible)
                            {
                                continue;
                            }
                            string PropertyName = Property.Name;
                            Type PropertyType = Property.Value.GetType();
                            string PropertyTypeString = PropertyType.ToString();
                            bool bIsEnum = PropertyType.IsEnum;
                            ObjectProperty ObjectProperty = new ObjectProperty();
                            ObjectProperty.Object = _Material;
                            ObjectProperty.Name = PropertyName;
                            ObjectProperty.Type = PropertyType;
                            ObjectProperty.GetPropertyValueFunction = GetPropertyValue;
                            ObjectProperty.SetPropertyValueFunction = SetPropertyValue;
                            if (FxProperty.IsColor || Fx.IsColorName(Property.Name))
                            {
                                if (FxProperty.Type == Fx.PropertyType.Float3)
                                {
                                    PropertyTypeString = "Vector3fAsColor";
                                }
                                else if (FxProperty.Type == Fx.PropertyType.Float4)
                                {
                                    PropertyTypeString = "Vector4fAsColor";
                                }
                            }
                            if (FxProperty.Type == Fx.PropertyType.Texture)
                            {
                                PropertyTypeString = "StringAsResource";
                                ObjectProperty.PropertyInfoAttribute = new PropertyInfoAttribute();
                                ObjectProperty.PropertyInfoAttribute.FileTypeDescriptor = "Texture Assets#nda";
                                ObjectProperty.PropertyInfoAttribute.ObjectClassID1 = ClassIDType.CLASS_Texture;
                                ObjectProperty.PropertyInfoAttribute.ObjectClassID2 = ClassIDType.CLASS_TextureUDIM;
                                ObjectProperty.PropertyInfoAttribute.ObjectClassID3 = ClassIDType.CLASS_Texture2DArray;
                            }
                            ObjectProperty.DefaultValue = "MaterialOverride";
                            ObjectProperty.RevertPropertyValueFunction = ResetPropertyValue;
                            if (Property.Override)
                            {
                                ObjectProperty.Override = true;
                            }
                            if (FxProperty.HasRange())
                            {
                                ObjectProperty.ValueMin = (decimal)FxProperty.Min;
                                ObjectProperty.ValueMax = (decimal)FxProperty.Max;
                                PropertyTypeString = "FloatWithTrack";
                            }
                            Inspector Inspector_Property = InspectorManager.GetInstance().CreatePropertyInspector(PropertyTypeString, bIsEnum);
                            GroupedProperty.AddChildInspector(Inspector_Property);
                            Inspector_Property.InspectProperty(ObjectProperty);
                        }
                    };
                    AddChildInspector(GroupedProperty);
                    GroupedProperty.InspectObject(_Material);
                }
            }

            foreach (var PassProp in _Material.PassProperties)
            {
                Inspector_GroupedProperty GroupedProperty = new Inspector_GroupedProperty(_Material, PassProp.Name + " pass");
                GroupedProperty.BuildPropertyInspector = () =>
                {
                    ObjectProperty GroupProperty = new ObjectProperty();
                    GroupProperty.Object = PassProp.Name;
                    GroupProperty.Name = "RenderGroup";
                    GroupProperty.Type = typeof(int);
                    GroupProperty.GetPropertyValueFunction = GetRenderGroup;
                    GroupProperty.SetPropertyValueFunction = SetRenderGroup;
                    if (PassProp.RenderGroupOverrided)
                    {
                        GroupProperty.RevertPropertyValueFunction = ResetRenderGroup;
                    }
                    Inspector Inspector_RenderGroup = InspectorManager.GetInstance().CreatePropertyInspector(GroupProperty.Type.ToString(), GroupProperty.Type.IsEnum);
                    GroupedProperty.AddChildInspector(Inspector_RenderGroup);
                    Inspector_RenderGroup.InspectProperty(GroupProperty);

                    ObjectProperty StateProperty = new ObjectProperty();
                    StateProperty.Object = PassProp.Name;
                    StateProperty.Name = "RenderState";
                    StateProperty.Type = typeof(Clicross.MaterialRenderState);
                    StateProperty.GetPropertyValueFunction = GetRenderState;
                    StateProperty.SetPropertyValueFunction = SetRenderState;
                    Inspector Inspector_RenderState = InspectorManager.GetInstance().CreatePropertyInspector(StateProperty.Type.ToString(), StateProperty.Type.IsEnum);
                    GroupedProperty.AddChildInspector(Inspector_RenderState);
                    Inspector_RenderState.InspectProperty(StateProperty);
                };
                AddChildInspector(GroupedProperty);
                GroupedProperty.InspectObject(_Material);
            }
        }

        private void RefreshIcon(bool force = false)
        {
            if (force)
                ResourceManager.Instance().MarkResourceChanged(_Material.Path);
            _PanelIcon.SetImage(null);
            ProjectUI.GetInstance().RefreshListView(false);
        }

        public Material GetMaterial() => _Material;

        private object GetRenderState(object obj, string name, ValueExtraProperty ValueExtraProperty)
        {
            return _Material.GetRenderState((string)obj);
        }

        private void SetRenderState(object obj, string name, object value, SubProperty SubProperty)
        {
            _Material.SetRenderState((string)obj, (Clicross.MaterialRenderState)value);
            RefreshIcon();
            OperationQueue.GetInstance().AddOperation(() => { GetInspectorHandler().ReadValue(); });
        }

        private object GetRenderGroup(object obj, string name, ValueExtraProperty ValueExtraProperty)
        {
            return _Material.GetRenderGroup((string)obj);
        }

        private void SetRenderGroup(object obj, string name, object value, SubProperty SubProperty)
        {
            _Material.SetRenderGroup((string)obj, (int)value);
            RefreshIcon();
            OperationQueue.GetInstance().AddOperation(() => { GetInspectorHandler().ReadValue(); });
        }

        private void ResetRenderGroup(object obj, string name)
        {
            _Material.ResetRenderGroup((string)obj);
            RefreshIcon();
            OperationQueue.GetInstance().AddOperation(() => { GetInspectorHandler().ReadValue(); });
        }

        private object GetPropertyValue(object obj, string name, ValueExtraProperty ValueExtraProperty)
        {
            return _Material.GetPropertyValue(name);
        }

        private void SetPropertyValue(object obj, string name, object value, SubProperty SubProperty)
        {
            if (value.Equals(_Material.GetPropertyValue(name))) return;
            _Material.SetPropertyValue(name, value);
            RefreshIcon();
            OperationQueue.GetInstance().AddOperation(() => { GetInspectorHandler().ReadValue(); });
        }

        private void SetParentValue(object obj, string name, object value, SubProperty SubProperty)
        {
            _Material.SetParentValue(name, value.ToString());
            RefreshIcon();
            GetInspectorHandler().InspectObject();
        }

        private void ResetPropertyValue(object obj, string name)
        {
            _Material.ResetPropertyValue(name);
            RefreshIcon();
            OperationQueue.GetInstance().AddOperation(() => { GetInspectorHandler().ReadValue(); });
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            int Height = 0;
            _LabelName.SetPosition(SPAN_X, Height + 7, _LabelName.CalculateTextWidth(), 14);
            Height += 28;
            _PanelIcon.SetPosition(SPAN_X, Height, 256, 256);
            Height += 258;
            _SelfContainer.SetPosition(0, Y, Width, Height);
            Y += Height;

            base.UpdateLayout(Width, ref Y);
        }

        public override int GetIndent()
        {
            return 0;
        }
    }
}
