using CEngine;
using Clicross;
using EditorUI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace CrossEditor
{
    public delegate void ImporterDelegate(Importer importer);
    public class ProjectUI : DockingUI
    {
        const string JavascriptTemplateFilename = "Editor/Template/JavascriptTemplate.js";
        const string LuaTemplateFilename = "Editor/Template/LuaTemplate.lua";
        const string TypescriptTemplateFilename = "Editor/Template/TypeScriptTemplate.mts";
        const string TypeScriptInheritTemplateFilename = "Editor/Template/TypeScriptInheritTemplate.mts";
        const string CompositeTemplateFilename = "/Animation/CompositeTemplate.nda";
        const string AnimatorTemplateFilename = "Editor/Template/Animator.stb.nda";
        const string Directories = "Directories";
        const int LIST_VIEW_ITEM_WIDTH_MIN = 40;
        const int LIST_VIEW_ITEM_WIDTH_MAX = 150;
        List<string> FilterName = new List<string> { ".nda", ".prefab", ".model", ".system" };
        List<string> NoNeedText = new List<string> { ".nda", ".model", ".world" };
        List<string> ScaleFontText = new List<string> { ".shader", ".particle" };

        static ProjectUI _Instance = new ProjectUI();

        Texture _TextureTreeItemFoldedFolder;
        Texture _TextureTreeItemExpandedFolder;

        Texture _TexturePanelFolder;
        Texture _TexturePanelFile;
        Texture _TexturePanelFile_World;

        Texture _TextureButtonNavigatorSeperator;

        VContainer _VContainer1;
        OperationBarUI _OperationBarUI;
        OperationBarUI _ClassificationBarUI;
        Button _ButtonCreate;
        SearchUI _SearchUI;
        Check _CheckGlobalSearch;
        Check _CheckClassifyLabel;
        TrackBar _TrackBarScale;
        HSplitter _HSplitter;
        Tree _Tree;
        VContainer _VContainer2;
        Panel _PanelNavigator;
        ListView _ListView;
        List<ClassifyLabelUI> _ClassifyLabels = new List<ClassifyLabelUI>();
        List<ClassifyLabelUI> _ActiveClassifyLabels = new List<ClassifyLabelUI>();
        bool _bListViewContextMenu;

        string _NavigationPath;
        bool _bSearchMode;
        bool _bGlobalSearch;
        string _SelectedPathInSearchMode;

        List<string> _PathesDragged;
        List<string> _PathesDropped;


        CollisionGenerateSetting _CollisionGenerateSetting;

        List<Importer> mImporters = new List<Importer>();


        Color _UnderLineColor;
        bool _bClassifyLabel = true;
        bool _bCheckAll = false;
        public bool _bAutoRecognition = false;
        public Dictionary<string, List<string>> Dictionary = new Dictionary<string, List<string>>();

        object _InspectedResource;

        private static Queue<EditorGeneralCallBack> ShaderReloaders = new Queue<EditorGeneralCallBack>();




        public static ProjectUI GetInstance()
        {
            return _Instance;
        }

        ProjectUI()
        {
            _bListViewContextMenu = false;
            _bSearchMode = false;
            _bGlobalSearch = true;
            _NavigationPath = "";
            _SelectedPathInSearchMode = "";
            _PathesDropped = new List<string>();

            mImporters.Add(new GeneralFileImporter(new HashSet<AssetType> { AssetType.Model, AssetType.FoliagePointCloud }, typeof(ImportUI), "Model Import", new ModelImportSettings()));
            mImporters.Add(new GeneralFileImporter(new HashSet<AssetType> { AssetType.Animatrix, AssetType.AnimComposite, AssetType.AnimBlendSpace }, typeof(ImportUI), "Animation Import", null));
            mImporters.Add(new GeneralFileImporter(new HashSet<AssetType> { AssetType.Texture }, typeof(TextureImportUI), "Texture Import", new TextureImportSetting()));
            mImporters.Add(new ShaderImporter());
            mImporters.Add(new GeneralFileImporter(new HashSet<AssetType> { AssetType.Font }, typeof(MSDFGenerateUI), "Font Import", new FontImportSetting()));
            mImporters.Add(new GeneralFileImporter(new HashSet<AssetType> { AssetType.OSM }, typeof(ImportUI), "OSM Import", new OSMImportSetting()));
            mImporters.Add(new GeneralFileImporter(new HashSet<AssetType> { AssetType.SHP }, typeof(ImportUI), "OSM Import", new SHPImportSetting()));
            mImporters.Add(new GeneralFileImporter(new HashSet<AssetType> { AssetType.TMap }, typeof(ImportUI), "Tencent Map  Import", new TMapImportSetting()));
            mImporters.Add(new GeneralFileImporter(new HashSet<AssetType> { AssetType.City }, typeof(ImportUI), "Combine Import", new CityImportSettings()));


            _CollisionGenerateSetting = new CollisionGenerateSetting();
            _CollisionGenerateSetting.CollisionGenerationType = GenerateCollisionType.ComplexCollision;
        }


        // it still confusing that we use priority to do different import.
        public void RegisterImporter(Importer importer)
        {
            for (int i = 0; i < mImporters.Count; i++)
            {
                if (importer.priority > mImporters[i].priority)
                {
                    mImporters.Insert(i, importer);
                    return;
                }
            }
            mImporters.Add(importer);
        }

        public bool Initialize()
        {
            _VContainer1 = new VContainer();
            _VContainer1.Initialize();
            _VContainer1.SetSize(500, 300);
            _VContainer1.ApplicationActivateEvent += OnVContainer1ApplicationActivate;

            _OperationBarUI = new OperationBarUI();
            _OperationBarUI.Initialize();
            _VContainer1.AddFixedChild(_OperationBarUI.GetPanelBar());

            _ButtonCreate = OperationBarUI.CreateTextButton("Create");
            _ButtonCreate.ClickedEvent += OnButtonCreateClicked;
            _OperationBarUI.AddLeft(_ButtonCreate);

            _TrackBarScale = new TrackBar();
            _TrackBarScale.Initialize();
            _TrackBarScale.SetValue(0.5f);
            _TrackBarScale.ValueChangedEvent += OnTrackBarScaleValueChanged;
            _TrackBarScale.SetPosition(0, 5, 200, 14);
            _TrackBarScale.SetBackgroundColor(Color.FromRGB(50, 50, 50));
            _OperationBarUI.AddRight(_TrackBarScale);

            _CheckClassifyLabel = new Check();
            _CheckClassifyLabel.Initialize();
            _CheckClassifyLabel.SetImageUnchecked(UIManager.LoadUIImage("Editor/UI/Check/Unchecked.png"));
            _CheckClassifyLabel.SetImageChecked(UIManager.LoadUIImage("Editor/UI/Check/Checked.png"));
            _CheckClassifyLabel.SetAutoCheck(true);
            _CheckClassifyLabel.SetChecked(_bClassifyLabel);
            _CheckClassifyLabel.SetPosition(0, 6, 14, 20);
            _CheckClassifyLabel.ClickedEvent += OnCheckClassifyLabelClicked;
            _OperationBarUI.AddRight(_CheckClassifyLabel);

            Label ClassifyLabel = new Label();
            ClassifyLabel.Initialize();
            ClassifyLabel.SetText("Classify Label");
            ClassifyLabel.SetFontSize(17);
            ClassifyLabel.SetPosition(0, 6, ClassifyLabel.CalculateTextWidth(), 16);
            _OperationBarUI.AddRight(ClassifyLabel);

            _CheckGlobalSearch = new Check();
            _CheckGlobalSearch.Initialize();
            _CheckGlobalSearch.SetImageUnchecked(UIManager.LoadUIImage("Editor/UI/Check/Unchecked.png"));
            _CheckGlobalSearch.SetImageChecked(UIManager.LoadUIImage("Editor/UI/Check/Checked.png"));
            _CheckGlobalSearch.SetAutoCheck(true);
            _CheckGlobalSearch.SetChecked(_bGlobalSearch);
            _CheckGlobalSearch.SetPosition(0, 6, 14, 20);
            _CheckGlobalSearch.ClickedEvent += OnCheckGloablSearchClicked;
            _OperationBarUI.AddRight(_CheckGlobalSearch);

            Label CheckLabel = new Label();
            CheckLabel.Initialize();
            CheckLabel.SetText("Global Search");
            CheckLabel.SetFontSize(17);
            CheckLabel.SetPosition(0, 6, CheckLabel.CalculateTextWidth(), 16);
            _OperationBarUI.AddRight(CheckLabel);

            _SearchUI = new SearchUI();
            _SearchUI.Initialize();
            _SearchUI.SearchEvent += OnSearchUISearch;
            _SearchUI.CancelEvent += OnSearchUICancel;
            Panel PanelBack = _SearchUI.GetPanelBack();
            PanelBack.SetPosition(0, 2, 230, 20);
            PanelBack.SetBackgroundColor(OperationBarUI.BAR_COLOR);
            _OperationBarUI.AddRight(PanelBack);

            _OperationBarUI.Refresh();

            _HSplitter = new HSplitter();
            _HSplitter.Initialize();
            _VContainer1.AddSizableChild(_HSplitter, 1.0f);

            _TextureTreeItemFoldedFolder = UIManager.LoadUIImage("Editor/Tree/Project/FoldedFolder.png");
            _TextureTreeItemExpandedFolder = UIManager.LoadUIImage("Editor/Tree/Project/Folder.png");

            _TexturePanelFolder = UIManager.LoadUIImage("Editor/Others/PanelFolder.png");
            _TexturePanelFile = UIManager.LoadUIImage("Editor/Others/PanelFile.png");
            _TexturePanelFile_World = UIManager.LoadUIImage("Editor/Others/PanelFile_World.png");

            _TextureButtonNavigatorSeperator = UIManager.LoadUIImage("Editor/Others/ButtonNavigatorSeperator.png");

            _Tree = new Tree();
            _Tree.Initialize();
            _Tree.SetEnableDragDrop(true);
            _Tree.SetEnableRename(true);
            _Tree.ItemSelectedEvent += OnTreeItemSelected;
            _Tree.RightMouseUpEvent += OnTreeRightMouseUp;
            _Tree.KeyDownEvent += OnTreeKeyDown;
            _Tree.ItemRenameEvent += OnTreeItemRename;
            _HSplitter.AddChild(_Tree);

            _VContainer2 = new VContainer();
            _VContainer2.Initialize();
            _VContainer2.SetSize(500, 300);
            _HSplitter.AddChild(_VContainer2);

            _PanelNavigator = new Panel();
            _PanelNavigator.SetSize(500, BAR_HEIGHT);
            _PanelNavigator.SetBackgroundColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            _VContainer2.AddFixedChild(_PanelNavigator);

            _ClassificationBarUI = new OperationBarUI();
            _ClassificationBarUI.Initialize();
            _VContainer2.AddFixedChild(_ClassificationBarUI.GetPanelBar());

            InitializeClassfyLabels();

            _ListView = new ListView();
            _ListView.Initialize();
            _ListView.SetSize(500, 300);
            _ListView.SetListViewStyle(ListViewStyle.Icon);
            _ListView.SetEnableRename(true);
            _ListView.SetSelectRealFilename(true);
            _ListView.SelectionChangedEvent += OnListViewSelectionChanged;
            _ListView.ItemDoubleClickedEvent += OnListViewItemDoubleClicked;
            _ListView.ItemRenameEvent += OnListViewItemRename;
            _ListView.ItemIconPaintEvent += OnListViewItemIconPaint;
            _ListView.RightMouseUpEvent += OnListViewRightMouseUp;
            _ListView.KeyDownEvent += OnListViewKeyDown;
            _ListView.MouseWheelEvent += OnListViewMouseWheel;
            _VContainer2.AddSizableChild(_ListView, 1.0f);

            DragDropManager DragDropManager = DragDropManager.GetInstance();
            DragDropManager.DragBeginEvent += OnDragDropManagerDragBegin;
            DragDropManager.DragMoveEvent += OnDragDropManagerDragMove;
            DragDropManager.DragEndEvent += OnDragDropManagerDragEnd;
            DragDropManager.DragClearEvent += OnDragDropManagerDragClear;
            DragDropManager.DragCancelEvent += OnDragDropManagerDragCancel;

            ShowDirectory();

            base.Initialize("Resource", _VContainer1);

            return true;
        }

        void InitializeClassfyLabels()
        {
            ClassifyLabelUI _WorldLabel = new ClassifyLabelUI();
            List<CEngine.ClassIDType> WorldClassIDList = new List<CEngine.ClassIDType>();
            WorldClassIDList.Add(CEngine.ClassIDType.CLASS_World);
            _WorldLabel.Initialize("World", WorldClassIDList);
            _ClassificationBarUI.AddLeft(_WorldLabel.GetPanel());

            ClassifyLabelUI _MaterialLabel = new ClassifyLabelUI();
            List<CEngine.ClassIDType> MaterialClassIDList = new List<CEngine.ClassIDType>();
            MaterialClassIDList.Add(CEngine.ClassIDType.CLASS_Material);
            _MaterialLabel.Initialize("Material", MaterialClassIDList);
            _ClassificationBarUI.AddLeft(_MaterialLabel.GetPanel());

            ClassifyLabelUI _TextureLabel = new ClassifyLabelUI();
            List<CEngine.ClassIDType> TextureClassIDList = new List<CEngine.ClassIDType>();
            TextureClassIDList.Add(CEngine.ClassIDType.CLASS_Texture);
            TextureClassIDList.Add(CEngine.ClassIDType.CLASS_Texture2D);
            TextureClassIDList.Add(CEngine.ClassIDType.CLASS_TextureCube);
            TextureClassIDList.Add(CEngine.ClassIDType.CLASS_Texture3D);
            TextureClassIDList.Add(CEngine.ClassIDType.CLASS_Texture2DArray);
            _TextureLabel.Initialize("Texture", TextureClassIDList);
            _ClassificationBarUI.AddLeft(_TextureLabel.GetPanel());

            ClassifyLabelUI _MeshLabel = new ClassifyLabelUI();
            List<CEngine.ClassIDType> MeshClassIDList = new List<CEngine.ClassIDType>();
            MeshClassIDList.Add(CEngine.ClassIDType.CLASS_MeshAssetDataResource);
            _MeshLabel.Initialize("Mesh", MeshClassIDList);
            _ClassificationBarUI.AddLeft(_MeshLabel.GetPanel());

            ClassifyLabelUI _FxLabel = new ClassifyLabelUI();
            List<CEngine.ClassIDType> FxClassIDList = new List<CEngine.ClassIDType>();
            FxClassIDList.Add(CEngine.ClassIDType.CLASS_Fx);
            _FxLabel.Initialize("Fx", FxClassIDList);
            _ClassificationBarUI.AddLeft(_FxLabel.GetPanel());

            ClassifyLabelUI _ShaderLabel = new ClassifyLabelUI();
            List<CEngine.ClassIDType> ShaderClassIDList = new List<CEngine.ClassIDType>();
            ShaderClassIDList.Add(CEngine.ClassIDType.CLASS_ComputeShader);
            ShaderClassIDList.Add(CEngine.ClassIDType.CLASS_Shader);
            _ShaderLabel.Initialize("Shader", ShaderClassIDList);
            _ClassificationBarUI.AddLeft(_ShaderLabel.GetPanel());

            ClassifyLabelUI _PrefabLabel = new ClassifyLabelUI();
            List<CEngine.ClassIDType> PrefabClassIDList = new List<CEngine.ClassIDType>();
            PrefabClassIDList.Add(CEngine.ClassIDType.CLASS_PrefabResource);
            _PrefabLabel.Initialize("Prefab", PrefabClassIDList);
            _ClassificationBarUI.AddLeft(_PrefabLabel.GetPanel());

            ClassifyLabelUI _AnimLabel = new ClassifyLabelUI();
            List<CEngine.ClassIDType> AnimClassIDList = new List<CEngine.ClassIDType>();
            AnimClassIDList.Add(CEngine.ClassIDType.CLASS_AnimatorRes);
            AnimClassIDList.Add(CEngine.ClassIDType.CLASS_AnimatrixRes);
            AnimClassIDList.Add(CEngine.ClassIDType.CLASS_AnimBlendSpaceRes);
            AnimClassIDList.Add(CEngine.ClassIDType.CLASS_AnimCompositeRes);
            AnimClassIDList.Add(CEngine.ClassIDType.CLASS_AnimSequenceRes);
            _AnimLabel.Initialize("Anim", AnimClassIDList);
            _ClassificationBarUI.AddLeft(_AnimLabel.GetPanel());

            ClassifyLabelUI _SkeletonLabel = new ClassifyLabelUI();
            List<CEngine.ClassIDType> SkeletonClassIDList = new List<CEngine.ClassIDType>();
            SkeletonClassIDList.Add(CEngine.ClassIDType.CLASS_SkeletonResource);
            SkeletonClassIDList.Add(CEngine.ClassIDType.CLASS_SkeletonPhysicsResource);
            _SkeletonLabel.Initialize("Skeleton", SkeletonClassIDList);
            _ClassificationBarUI.AddLeft(_SkeletonLabel.GetPanel());

            ClassifyLabelUI _ParticleSystemLabel = new ClassifyLabelUI();
            List<CEngine.ClassIDType> ParticleClassIDList = new List<CEngine.ClassIDType>();
            ParticleClassIDList.Add(CEngine.ClassIDType.CLASS_ParticleSystemResource);
            _ParticleSystemLabel.Initialize("ParticleSystem", ParticleClassIDList);
            _ClassificationBarUI.AddLeft(_ParticleSystemLabel.GetPanel());

            ClassifyLabelUI _ParticleEmitterLabel = new ClassifyLabelUI();
            List<CEngine.ClassIDType> ParticleEmitterClassIDList = new List<CEngine.ClassIDType>();
            ParticleEmitterClassIDList.Add(CEngine.ClassIDType.CLASS_ParticleEmitterResource);
            _ParticleEmitterLabel.Initialize("ParticleEmitter", ParticleEmitterClassIDList);
            _ClassificationBarUI.AddLeft(_ParticleEmitterLabel.GetPanel());

            ClassifyLabelUI _WorkflowLabel = new ClassifyLabelUI();
            List<CEngine.ClassIDType> WorkflowClassIDList = new List<CEngine.ClassIDType>();
            WorkflowClassIDList.Add(CEngine.ClassIDType.CLASS_WorkflowGraphResource);
            _WorkflowLabel.Initialize("WorkflowGraph", WorkflowClassIDList);
            _ClassificationBarUI.AddLeft(_WorkflowLabel.GetPanel());

            ClassifyLabelUI _SequencerLabel = new ClassifyLabelUI();
            List<CEngine.ClassIDType> SequencerClassIDList = new List<CEngine.ClassIDType>();
            SequencerClassIDList.Add(CEngine.ClassIDType.CLASS_CurveControllerRes);
            _SequencerLabel.Initialize("Sequencer", SequencerClassIDList);
            _ClassificationBarUI.AddLeft(_SequencerLabel.GetPanel());

            ClassifyLabelUI _OtherLabel = new ClassifyLabelUI();
            List<CEngine.ClassIDType> OtherClassIDList = new List<CEngine.ClassIDType>();
            OtherClassIDList.Add(CEngine.ClassIDType.CLASS_NullType);
            _OtherLabel.Initialize("Other", OtherClassIDList);
            _ClassificationBarUI.AddLeft(_OtherLabel.GetPanel());

            _ClassifyLabels.Add(_WorldLabel);
            _ClassifyLabels.Add(_MaterialLabel);
            _ClassifyLabels.Add(_TextureLabel);
            _ClassifyLabels.Add(_MeshLabel);
            _ClassifyLabels.Add(_FxLabel);
            _ClassifyLabels.Add(_ShaderLabel);
            _ClassifyLabels.Add(_PrefabLabel);
            _ClassifyLabels.Add(_AnimLabel);
            _ClassifyLabels.Add(_SkeletonLabel);
            _ClassifyLabels.Add(_ParticleSystemLabel);
            _ClassifyLabels.Add(_ParticleEmitterLabel);
            _ClassifyLabels.Add(_WorkflowLabel);
            _ClassifyLabels.Add(_SequencerLabel);
            _ClassifyLabels.Add(_OtherLabel);

            foreach (ClassifyLabelUI UI in _ClassifyLabels)
            {
                UI.LeftMouseDownEvent += ClassificationLeftMouseDownEvent;
            }
        }

        public void UpdateAll()
        {
            UpdateDirectory();
            bool bResetScroll = false;
            RefreshListView(bResetScroll);
        }

        public void OnVContainer1ApplicationActivate(Control Sender, bool bApplicationActivated)
        {
            if (bApplicationActivated)
            {
                UpdateAll();
            }
        }

        void OnButtonCreateClicked(Button Sender)
        {
            _bListViewContextMenu = false;

            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();
            BulidCreateMenuItems(MenuContextMenu);

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);
        }

        void OnTrackBarScaleValueChanged(TrackBar Sender)
        {
            float Value = Sender.GetValue();
            int ItemWidth = (int)(LIST_VIEW_ITEM_WIDTH_MIN + (LIST_VIEW_ITEM_WIDTH_MAX - LIST_VIEW_ITEM_WIDTH_MIN) * Value);
            if (ItemWidth == LIST_VIEW_ITEM_WIDTH_MIN)
            {
                _ListView.SetListViewStyle(ListViewStyle.List);
            }
            else
            {
                _ListView.SetListViewStyle(ListViewStyle.Icon);
            }
            _ListView.SetItemWidth(ItemWidth);
            bool bResetScroll = false;
            RefreshListView(bResetScroll);
            ListViewItem SelectedItem = _ListView.GetSelectedItem();
            if (SelectedItem != null)
            {
                _ListView.ScrollToItem(SelectedItem);
            }
        }

        #region Search Mode

        void OnCheckGloablSearchClicked(Check Sender)
        {
            _bGlobalSearch = Sender.GetChecked();
            _SearchUI.TriggerSearchEvent();
        }

        void OnSearchUISearch(SearchUI Sender, string Pattern)
        {
            DoSearch();
        }

        void OnSearchUICancel(SearchUI Sender)
        {
            DoCancelSearch();
        }

        void EnterSearchMode()
        {
            _bSearchMode = true;
            _ListView.ClearSelectedItems();
        }

        void ExitSearchMode()
        {
            if (_bSearchMode)
            {
                _bSearchMode = false;
                _NavigationPath = "";
                ListViewItem SelectedItem = _ListView.GetSelectedItem();
                if (SelectedItem != null)
                {
                    _SelectedPathInSearchMode = SelectedItem.Path;
                }
                _ListView.ClearSelectedItems();
            }
            _SearchUI.ClearSearchPattern();
        }

        void DoCancelSearch()
        {
            if (_bSearchMode)
            {
                ExitSearchMode();
                bool bResetScroll = true;
                RefreshListView(bResetScroll);
                JumpToSelectedPathInSearchMode();
            }
        }

        void DoSearch()
        {
            string SearchPattern = _SearchUI.GetSearchPattern();
            _ListView.ClearItems();
            if (SearchPattern != "")
            {
                EnterSearchMode();
            }
            else
            {
                ExitSearchMode();
            }
            bool bResetScroll = true;
            RefreshListView(bResetScroll);
            JumpToSelectedPathInSearchMode();
        }

        void RefreshNavigator_Search()
        {
            List<ListViewItem> SelectedListViewItems = _ListView.GetSelectedItems();
            if (SelectedListViewItems.Count == 1)
            {
                ListViewItem SelectedListViewItem = _ListView.GetSelectedItem();
                DebugHelper.Assert(SelectedListViewItem != null && SelectedListViewItem.bIsDirectory == false);
                string Filename = PathHelper.ToStandardForm(SelectedListViewItem.Path);
                string StandardFilename = EditorUtilities.EditorFilenameToStandardFilename(Filename);
                string Directory = PathHelper.GetDirectoryName(StandardFilename);
                _NavigationPath = Directory;
            }
            RefreshNavigator();
        }

        void RefreshListView_Search(bool bResetScroll)
        {
            ListViewState ListViewState = null;
            if (bResetScroll == false)
            {
                ListViewState = _ListView.SaveState();
            }

            _ListView.ClearItems();

            FileInfo[] FileInfos;
            if (_bGlobalSearch)
            {
                string RootDirectory = GetRootDirectory();
                bool bRecursively = true;
                FileInfo[] FileInfos1 = DirectoryHelper.GetFilesInDirectory(RootDirectory, bRecursively);
                string ResourceDirectory = EditorUtilities.GetResourceDirectory();
                FileInfo[] FileInfos2 = DirectoryHelper.GetFilesInDirectory(ResourceDirectory, bRecursively);
                FileInfos = new FileInfo[FileInfos1.Length + FileInfos2.Length];
                Array.Copy(FileInfos1, FileInfos, FileInfos1.Length);
                Array.Copy(FileInfos2, 0, FileInfos, FileInfos1.Length, FileInfos2.Length);
            }
            else
            {
                TreeItem Selection = _Tree.GetSelectedItem();
                string DirectoryPath = Selection.GetTagString();
                if (DirectoryPath == "")
                {
                    return;
                }

                _NavigationPath = Selection.GetPath();
                if (_NavigationPath.StartsWith(Directories + "/"))
                {
                    _NavigationPath = _NavigationPath.Substring(Directories.Length + 1);
                }
                RefreshNavigator();

                FileInfos = DirectoryHelper.GetFilesInDirectory(DirectoryPath, true);
            }

            string SearchPattern = _SearchUI.GetSearchPattern();
            foreach (FileInfo FileInfo in FileInfos)
            {
                if (FileInfo.Extension.Equals(".block"))
                {
                    continue;
                }

                string FileName = FileInfo.Name;
                string[] PatternList = SearchPattern.Split(' ', '\t');
                List<string> PatternList1 = new List<string>();
                foreach (string Pattern in PatternList)
                {
                    string Pattern1 = Pattern.Trim();
                    if (Pattern1 != "")
                    {
                        PatternList1.Add(Pattern);
                    }
                }
                bool bFound = true;
                foreach (var Pattern in PatternList1)
                {
                    if (FileName.Contains(Pattern, StringComparison.OrdinalIgnoreCase) == false)
                    {
                        bFound = false;
                        break;
                    }
                }
                if (!bFound)
                {
                    continue;
                }
                string FilePath = FileInfo.FullName;
                FilePath = PathHelper.ToStandardForm(FilePath);

                if (IsClassifyMode())
                {
                    bool bMatch = false;
                    foreach (ClassifyLabelUI UI in _ActiveClassifyLabels)
                    {
                        if (UI.IsFileMatchClassIDType(FilePath, FileInfo.Extension))
                        {
                            bMatch = true;
                            break;
                        }
                    }

                    if (!bMatch)
                    {
                        continue;
                    }
                }

                ListViewItem ListViewItem = new ListViewItem();
                ListViewItem.Name = FileName;
                ListViewItem.Image = GetPanelFile(FilePath);
                ListViewItem.bIsDirectory = false;
                ListViewItem.Path = FilePath;
                //ListViewItem.Type = "File";
                ListViewItem.Type = GetFileDisplay(FilePath, FileInfo.Extension);
                _ListView.AddItem(ListViewItem);
                if (_bCheckAll)
                {
                    _ListView.AddSelection(ListViewItem);
                }
            }

            if (bResetScroll)
            {
                _ListView.ResetScroll();
            }
            else
            {
                if (_bCheckAll)
                {
                    ListViewState = _ListView.SaveState();
                }
                if (ListViewState != null)
                {
                    _ListView.LoadState(ListViewState);
                    _bCheckAll = false;
                }
            }
            _ListView.UpdateUI();
        }

        #endregion

        #region Jump to path
        public void JumpToPath(string Path)
        {
            string DirectoryName = PathHelper.GetDirectoryName(Path);
            bool b = SelectTreeItemByTag(DirectoryName);
            if (b)
            {
                ExitSearchMode();
                ClearActiveClassifyLabel();
                RefreshListView(true);
                ListViewItem ListViewItem = _ListView.FindItemByPath(Path);
                if (ListViewItem != null)
                {
                    _ListView.SelectItem(ListViewItem);
                    _ListView.SetFocus();
                }
            }
        }

        public void JumpToSelectedPathInSearchMode()
        {
            if (_SelectedPathInSearchMode != "")
            {
                JumpToPath(_SelectedPathInSearchMode);
                _SelectedPathInSearchMode = "";
            }
        }

        #endregion

        #region Tree
        public void UpdateDirectory()
        {
            TreeItem RootItem = _Tree.GetRootItem();
            TreeState TreeState = _Tree.SaveState();
            ShowDirectory();
            _Tree.LoadState(TreeState);
            RootItem.SetExpanded(true);
            JumpToSelectedPathInSearchMode();
        }

        string GetRootDirectory()
        {
            return MainUI.GetInstance().GetProjectDirectory() + "/Contents";
        }

        void ShowDirectory()
        {
            string RootDirectory = GetRootDirectory();
            if (RootDirectory != "")
            {
                TreeItem RootItem = _Tree.GetRootItem();
                RootItem.SetFolder(true);
                RootItem.SetExpanded(false);
                RootItem.SetText(Directories);
                RootItem.SetImageFolded(_TextureTreeItemFoldedFolder);
                RootItem.SetImageExpanded(_TextureTreeItemExpandedFolder);
                RootItem.SetTagString("");
                RootItem.ClearChildren();

                TreeItem TreeItemContents = _Tree.CreateItem();
                RootItem.AddChild(TreeItemContents);
                ShowDirectory(TreeItemContents, RootDirectory);

                string ResourceDirectory = EditorUtilities.GetResourceDirectory();

                string EngineResourceDirectory = ResourceDirectory + "/EngineResource";
                TreeItem TreeItemEngineResource = _Tree.CreateItem();
                RootItem.AddChild(TreeItemEngineResource);
                ShowDirectory(TreeItemEngineResource, EngineResourceDirectory);

                string PipelineDirectory = ResourceDirectory + "/PipelineResource";
                TreeItem TreeItemPipelineResource = _Tree.CreateItem();
                RootItem.AddChild(TreeItemPipelineResource);
                ShowDirectory(TreeItemPipelineResource, PipelineDirectory);
            }
        }

        void ShowDirectory(TreeItem TreeItem, string DirectoryPath)
        {
            DirectoryPath = PathHelper.ToStandardForm(DirectoryPath);
            string DirectoryName = PathHelper.GetFileName(DirectoryPath);
            if (DirectoryPath == "")
            {
                DirectoryName = "Empty";
            }
            TreeItem.SetFolder(true);
            TreeItem.SetExpanded(false);
            TreeItem.SetText(DirectoryName);
            TreeItem.SetImageFolded(_TextureTreeItemFoldedFolder);
            TreeItem.SetImageExpanded(_TextureTreeItemExpandedFolder);
            TreeItem.SetTagString(DirectoryPath);

            DirectoryInfo[] SubDirectoryInfos = DirectoryHelper.GetSubDirectories(DirectoryPath);
            int Count = SubDirectoryInfos.Length;
            for (int i = 0; i < Count; i++)
            {
                DirectoryInfo SubDirectoryInfo = SubDirectoryInfos[i];
                TreeItem TreeItemDirectory = _Tree.CreateItem();
                TreeItem.AddChild(TreeItemDirectory);
                ShowDirectory(TreeItemDirectory, SubDirectoryInfo.FullName);
            }
            if (Count == 0)
            {
                TreeItem.SetImageExpanded(_TextureTreeItemFoldedFolder);
            }
        }

        bool SelectTreeItemByTag(string TagString)
        {
            TreeItem RootItem = _Tree.GetRootItem();
            TreeItem DirectoryItem = RootItem.SearchByTagString(TagString);
            if (DirectoryItem != null)
            {
                _Tree.SelectItem(DirectoryItem);
                return true;
            }
            return false;
        }

        void OnTreeItemSelected(Tree Sender, TreeItem TreeItem)
        {
            string DirectoryPath = TreeItem.GetTagString();
            if (DirectoryPath == "")
            {
                _ListView.ClearItems();
                return;
            }
            string NavigationPath = TreeItem.GetPath();
            if (NavigationPath.StartsWith(Directories + "/"))
            {
                NavigationPath = NavigationPath.Substring(Directories.Length + 1);
            }
            if (_NavigationPath == NavigationPath)
            {
                return;
            }
            bool bResetScroll = true;
            RefreshListView(bResetScroll);
        }

        void OnTreeRightMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (Sender.IsPointIn(MouseX, MouseY) == false)
            {
                return;
            }
            TreeItemHitTest HitTest = _Tree.HitTest(MouseX, MouseY);
            if (HitTest.HitResult != TreeItemHitResult.Nothing)
            {
                bool bListViewContextMenu = false;
                ShowContextMenu(bListViewContextMenu, MouseX, MouseY);
            }
            bContinue = false;
        }

        void OnTreeKeyDown(Control Sender, Key Key, ref bool bContinue)
        {
            Device Device = GetDevice();
            if (Key == Key.Delete)
            {
                if (Device.IsNoneModifiersDown())
                {
                    DoDelete_Tree();
                    bContinue = false;
                }
            }
            else if (Key == Key.F2)
            {
                if (Device.IsNoneModifiersDown())
                {
                    DoStartRename_Tree();
                    bContinue = false;
                }
            }
            else if (Key == Key.Escape)
            {
                if (Device.IsNoneModifiersDown())
                {
                    DoCancelSearch();
                    bContinue = false;
                }
            }
        }

        void DoRenameTreeFolder(string NewName, string OldName, string OldDirectoryName)
        {
            if (NewName == "")
            {
                return;
            }
            if (NewName == OldName)
            {
                return;
            }
            if (EditorUtilities.CheckFileOrDirectoryName(NewName) == false)
            {
                string Tips = string.Format("Directory name: {0} contains illegal char/chars.", NewName);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            string ParentDirectoryName = PathHelper.GetDirectoryName(OldDirectoryName);
            string NewDirectoryName = ParentDirectoryName + "/" + NewName;
            if (DirectoryHelper.IsDirectoryExists(NewDirectoryName))
            {
                string NewDirectoryName1 = EditorUtilities.EditorFilenameToStandardFilename(NewDirectoryName);
                string Tips = string.Format("Directory: {0} already exists.", NewDirectoryName1);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            DoRenameDirectory(OldDirectoryName, NewDirectoryName);
            UpdateDirectory();
            TreeItem TreeItem = _Tree.FindItemByTagString(NewDirectoryName);
            if (TreeItem != null)
            {
                _Tree.SelectItem(TreeItem);
            }
        }

        void OnTreeItemRename(Tree Sender, string NewName, string OldName, TreeItem TreeItem)
        {
            string Path = TreeItem.GetTagString();
            DoRenameTreeFolder(NewName, OldName, Path);
        }

        #endregion

        #region Navigator

        void RefreshNavigator()
        {
            _PanelNavigator.ClearChildren();
            string Directory = _NavigationPath;
            string[] NavigateItems = Directory.Split('/');
            int BorderHeight = 4;
            int SpanX = 3;
            int FontSize = BAR_HEIGHT - BorderHeight * 2;
            int SeperatorWidth = 20;
            int SeperatorHeight = 20;
            int SeperatorY = (BAR_HEIGHT - SeperatorHeight) / 2;
            Font Font = GetUIManager().GetDefaultFont(FontSize);
            string Path = "";
            int Count = NavigateItems.Length;
            int X = 0;

            string Root;
            if (NavigateItems[0].Contains("Contents"))
            {
                Root = MainUI.GetInstance().GetProjectDirectory();
            }
            else
            {
                Root = EditorUtilities.GetResourceDirectory();
            }

            for (int i = 0; i < Count; i++)
            {
                string NavigateItem = NavigateItems[i];
                if (Path != "")
                {
                    Path = Path + "/" + NavigateItem;
                }
                else
                {
                    Path = NavigateItem;
                }

                string Path1 = Root + "/" + Path;

                int NavigateItemWidth = Font.MeasureString_Fast(NavigateItem) + 6;
                Button ButtonNavigateItem = new Button();
                ButtonNavigateItem.SetText(NavigateItem);
                ButtonNavigateItem.SetFontSize(FontSize);
                ButtonNavigateItem.SetTagString1(Path1);
                ButtonNavigateItem.SetPosition(X, BorderHeight, NavigateItemWidth, FontSize);
                ButtonNavigateItem.ClickedEvent += OnButtonNavigateItemClicked;
                _PanelNavigator.AddChild(ButtonNavigateItem);

                X += NavigateItemWidth + SpanX;

                if (i != Count - 1)
                {
                    Button ButtonNavigatorSeperator = new Button();
                    ButtonNavigatorSeperator.SetImage(_TextureButtonNavigatorSeperator);
                    ButtonNavigatorSeperator.SetTagString1(Path1);
                    ButtonNavigatorSeperator.SetPosition(X, SeperatorY, SeperatorWidth, SeperatorHeight);
                    ButtonNavigatorSeperator.ClickedEvent += OnButtonNavigatorSeperatorClicked;
                    _PanelNavigator.AddChild(ButtonNavigatorSeperator);

                    X += SeperatorWidth + SpanX;
                }
            }
        }

        void OnButtonNavigateItemClicked(Button Sender)
        {
            string DirectoryPath = Sender.GetTagString1();
            SelectTreeItemByTag(DirectoryPath);
            DoSearch();
        }

        void OnButtonNavigatorSeperatorClicked(Button Sender)
        {
            string DirectoryPath = Sender.GetTagString1();

            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();

            DirectoryInfo[] SubDirectoryInfos = DirectoryHelper.GetSubDirectories(DirectoryPath);
            foreach (DirectoryInfo SubDirectoryInfo in SubDirectoryInfos)
            {
                string SubDirectoryName = SubDirectoryInfo.Name;
                string SubDirectoryPath = DirectoryPath + "/" + SubDirectoryName;
                MenuItem MenuItem_SubDirectoryX = new MenuItem();
                MenuItem_SubDirectoryX.SetText(SubDirectoryName);
                MenuItem_SubDirectoryX.SetTagString(SubDirectoryPath);
                MenuItem_SubDirectoryX.ClickedEvent += OnMenuItemSubDirectoryXClicked;
                MenuContextMenu.AddMenuItem(MenuItem_SubDirectoryX);
            }

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);
        }

        void OnMenuItemSubDirectoryXClicked(MenuItem Sender)
        {
            string SubDirectoryPath = Sender.GetTagString();
            SelectTreeItemByTag(SubDirectoryPath);
            DoSearch();
        }

        #endregion

        #region ListView

        public void SetListViewItemWidth(int ItemWidth)
        {
            float Value = (ItemWidth - LIST_VIEW_ITEM_WIDTH_MIN) / (float)(LIST_VIEW_ITEM_WIDTH_MAX - LIST_VIEW_ITEM_WIDTH_MIN);
            _TrackBarScale.SetValue(Value);
            OnTrackBarScaleValueChanged(_TrackBarScale);
        }

        Texture GetPanelFile(string FileName)
        {
            if (FileName.EndsWith(".world"))
            {
                return _TexturePanelFile_World;
            }
            else
            {
                return _TexturePanelFile;
            }
        }

        void RefreshListView_Explore(bool bResetScroll)
        {
            ListViewState ListViewState = null;
            if (bResetScroll == false)
            {
                ListViewState = _ListView.SaveState();
            }

            _ListView.ClearItems();

            TreeItem Selection = _Tree.GetSelectedItem();
            string DirectoryPath = Selection.GetTagString();
            if (DirectoryPath == "")
            {
                return;
            }

            _NavigationPath = Selection.GetPath();
            if (_NavigationPath.StartsWith(Directories + "/"))
            {
                _NavigationPath = _NavigationPath.Substring(Directories.Length + 1);
            }
            RefreshNavigator();

            DirectoryInfo[] SubDirectoryInfos;
            FileInfo[] FileInfos;
            DirectoryHelper.GetSubDirectoriesAndFiles(DirectoryPath, out SubDirectoryInfos, out FileInfos);

            if (!IsClassifyMode())
            {
                for (int i = 0; i < SubDirectoryInfos.Length; i++)
                {
                    DirectoryInfo SubDirectoryInfo = SubDirectoryInfos[i];
                    string SubDirectoryPath = SubDirectoryInfo.FullName;
                    SubDirectoryPath = PathHelper.ToStandardForm(SubDirectoryPath);
                    string SubDirectoryName = PathHelper.GetFileName(SubDirectoryPath);

                    ListViewItem ListViewItem = new ListViewItem();
                    ListViewItem.Name = SubDirectoryName;
                    ListViewItem.Image = _TexturePanelFolder;
                    ListViewItem.bIsDirectory = true;
                    ListViewItem.Path = SubDirectoryPath;
                    ListViewItem.Type = "Folder";
                    _ListView.AddItem(ListViewItem);
                    if (_bCheckAll)
                    {
                        _ListView.AddSelection(ListViewItem);
                    }
                }
            }

            if (IsClassifyMode())
            {
                FileInfos = DirectoryHelper.GetFilesInDirectory(DirectoryPath, true);
            }
            for (int i = 0; i < FileInfos.Length; i++)
            {
                FileInfo FileInfo = FileInfos[i];
                if (FileInfo.Extension.Equals(".block"))
                {
                    continue;
                }

                string FilePath = FileInfo.FullName;
                FilePath = PathHelper.ToStandardForm(FilePath);
                string FileName = FileInfo.Name;

                if (IsClassifyMode())
                {
                    bool bMatch = false;
                    foreach (ClassifyLabelUI UI in _ActiveClassifyLabels)
                    {
                        if (UI.IsFileMatchClassIDType(FilePath, FileInfo.Extension))
                        {
                            bMatch = true;
                            break;
                        }
                    }

                    if (!bMatch)
                    {
                        continue;
                    }
                }

                ListViewItem ListViewItem = new ListViewItem();
                ListViewItem.Name = FileName;
                ListViewItem.Image = GetPanelFile(FilePath);
                ListViewItem.bIsDirectory = false;
                ListViewItem.Path = FilePath;
                //ListViewItem.Type = "File";
                ListViewItem.Type = GetFileDisplay(FilePath, FileInfo.Extension);
                CEngine.ClassIDType ObjectClassID = ResourceTypeCache.GetInstance().GetResourceType_Cache(FilePath);
                if (ObjectClassID != CEngine.ClassIDType.CLASS_NullType)
                {
                    ListViewItem.ResourceTypeName = ObjectClassID.ToString().Replace("CLASS_", "").Replace("Resource", "");
                }
                _ListView.AddItem(ListViewItem);
                if (_bCheckAll)
                {
                    _ListView.AddSelection(ListViewItem);
                }
            }

            if (bResetScroll)
            {
                _ListView.ResetScroll();
            }
            else
            {
                if (_bCheckAll)
                {
                    ListViewState = _ListView.SaveState();
                }
                if (ListViewState != null)
                {
                    _ListView.LoadState(ListViewState);
                    _bCheckAll = false;
                }
            }
            _ListView.UpdateUI();
        }

        public void RefreshListView(bool bResetScroll)
        {
            if (_bSearchMode)
            {
                RefreshListView_Search(bResetScroll);
            }
            else
            {
                RefreshListView_Explore(bResetScroll);
            }
        }

        void OpenListViewItem(ListViewItem ListViewItem)
        {
            if (ListViewItem.bIsDirectory)
            {
                string DirectoryPath = ListViewItem.Path;
                SelectTreeItemByTag(DirectoryPath);
            }
            else
            {
                MainUI MainUI = MainUI.GetInstance();
                EditorScene EditorScene = EditorScene.GetInstance();
                string FilePath = ListViewItem.Path;
                string Extension = PathHelper.GetExtension(FilePath);
                if (StringHelper.IgnoreCaseEqual(Extension, ".world"))
                {
                    MainUI.GeneralOpenScene(FilePath);
                }
                else if (StringHelper.IgnoreCaseEqual(Extension, ".nda"))
                {
                    AssetType AssetType = AssetImporterManager.Instance().GetAssetType(FilePath);
                    if (AssetType == AssetType.Default)
                    {
                        string FilePath1 = EditorUtilities.EditorFilenameToStandardFilename(FilePath);
                        CEngine.ClassIDType ObjectClassID = Resource.GetResourceTypeStatic(FilePath1);
                        if (ObjectClassID == CEngine.ClassIDType.CLASS_World)
                        {
                            string WorldPath = FilePath.Replace(".nda", ".world");
                            if (FileHelper.IsFileExists(WorldPath))
                            {
                                MainUI.GeneralOpenScene(WorldPath);
                            }
                        }
                        else if (ObjectClassID == CEngine.ClassIDType.CLASS_MeshAssetDataResource)
                        {
                            MainUI.ActivateDockingCard_MEPreview();
                            MEPreviewUI.GetInstance().SetMeshPath(FilePath);
                        }
                        else if (ObjectClassID == CEngine.ClassIDType.CLASS_AnimatorRes)
                        {
                            DockingCard DockingCard = MainUI.FindDockingCardByFilePath(FilePath);
                            if (DockingCard != null)
                            {
                                MainUI.ActivateDockingCard(DockingCard);
                            }
                            else
                            {
                                StoryBoardUI StoryBoardUI = StoryBoardUI.CreateInstance();
                                StoryBoardUI.Initialize();
                                StoryBoardUI.OpenStoryBoard(FilePath);
                                MainUI.ActivateDockingCard_Animator(StoryBoardUI.GetDockingCard());
                            }
                        }
                        if (CanNdaFileBeInspect(FilePath1))
                        {
                            if (ObjectClassID == CEngine.ClassIDType.CLASS_Texture2D || ObjectClassID == CEngine.ClassIDType.CLASS_Texture2DVirtual)
                            {
                                DockingUI DockingUI = TextureEditorManager.GetInstance().FindDockingUIByFilePath(FilePath);
                                TextureEditorUI TextureEditorUI;
                                if (DockingUI != null)
                                {
                                    Resource.Get(EditorUtilities.EditorFilenameToStandardFilename(FilePath), true);
                                    string FilePathName = PathHelper.GetFileName(FilePath1);
                                    DockingUI.GetDockingCard().SetText(FilePathName);
                                    TextureEditorUI = (TextureEditorUI)DockingUI;
                                }
                                else
                                {
                                    TextureEditorUI = new TextureEditorUI();
                                    TextureEditorUI.OpenTexture(FilePath);
                                }
                                TextureEditorUI.UpdateInspector(FilePath);
                                MainUI.ActivateDockingCard_TextureEditor(TextureEditorUI.GetDockingCard());
                                return;
                            }
                            else if (ObjectClassID == CEngine.ClassIDType.CLASS_Fx)
                            {
                                string Path1 = EditorUtilities.EditorFilenameToStandardFilename(FilePath);
                                Fx fx = Resource.Get(Path1) as Fx;

                                if (fx.GetVersion() == 2)
                                {
                                    MaterialEditorUIManager.Instance.OpenMaterial(Path1, CEngine.ClassIDType.CLASS_Fx);
                                }
                                else
                                {
                                    FxEditorUI FxEditorUI = FxEditorUI.GetInstance();
                                    FxEditorUI.OpenFx(fx as Fx);
                                    MainUI.ActivateDockingCard_FxView(FxEditorUI.GetDockingCard());
                                }
                                return;
                            }
                            else if (ObjectClassID == CEngine.ClassIDType.CLASS_Material)
                            {
                                string Path1 = EditorUtilities.EditorFilenameToStandardFilename(FilePath);
                                Material material = Resource.Get(Path1) as Material;

                                if (material.GetVersion() == 2)
                                {
                                    MaterialEditorUIManager.Instance.OpenMaterial(Path1, CEngine.ClassIDType.CLASS_Material);
                                    return;
                                }
                            }
                            else if (ObjectClassID == CEngine.ClassIDType.CLASS_CurveControllerRes)
                            {
                                OpenNda(FilePath, ObjectClassID);
                                return;
                            }
                            else if(ObjectClassID == CEngine.ClassIDType.CLASS_DataAssetResource)
                            {
                                DataAssetEditorUIManager.Instance.OpenDataAsset(FilePath);
                                return;
                            }

                            InspectSelectedItem();
                        }
                    }
                }
                else if (StringHelper.IgnoreCaseEqual(Extension, ".vs"))
                {
                    DockingCard DockingCard = MainUI.FindDockingCardByFilePath(FilePath);
                    if (DockingCard != null)
                    {
                        MainUI.ActivateDockingCard(DockingCard);
                    }
                    else
                    {
                        BasicNodeGraphUI BasicNodeGraphUI = BasicNodeGraphUI.CreateInstance();
                        BasicNodeGraphUI.Initialize();
                        BasicNodeGraphUI.OpenVisualScript(FilePath);
                        MainUI.ActivateDockingCard_BasicNodeGraph(BasicNodeGraphUI.GetDockingCard());
                    }
                }
                else if (StringHelper.IgnoreCaseEqual(Extension, ".stb"))
                {
                    DockingCard DockingCard = MainUI.FindDockingCardByFilePath(FilePath);
                    if (DockingCard != null)
                    {
                        MainUI.ActivateDockingCard(DockingCard);
                    }
                    else
                    {
                        StoryBoardUI StoryBoardUI = StoryBoardUI.CreateInstance();
                        StoryBoardUI.Initialize();
                        StoryBoardUI.OpenStoryBoard(FilePath);
                        MainUI.ActivateDockingCard_Animator(StoryBoardUI.GetDockingCard());
                    }
                }
                else if (StringHelper.IgnoreCaseEqual(Extension, ".cur"))
                {
                    CurveEditorUI.GetInstance().OpenCurFile(FilePath);
                    MainUI.ActivateDockingCard_CurveEditor();
                }
                else if (StringHelper.IgnoreCaseEqual(Extension, ".mtl"))
                {
                    DockingCard DockingCard = MainUI.FindDockingCardByFilePath(FilePath);
                    if (DockingCard != null)
                    {
                        MainUI.ActivateDockingCard(DockingCard);
                    }
                    else
                    {
                        //MaterialEditorUI MaterialEditorUI = new MaterialEditorUI();
                        //MaterialEditorUI.Initialize();
                        //MaterialEditorUI.OpenMaterial(FilePath);
                        //MainUI.ActivateDockingCard_MaterialEditor(MaterialEditorUI.GetDockingCard());
                    }
                }
                else if (StringHelper.IgnoreCaseEqual(Extension, ".prefab") || StringHelper.IgnoreCaseEqual(Extension, ".model"))
                {
                    MainUI.ActivateDockingCard_PrefabEditor();
                    PrefabSceneUI.GetInstance().SetPrefab(FilePath);
                }
                else if (StringHelper.IgnoreCaseEqual(Extension, ".system"))
                {
                    MainUI.ActivateDockingCard_ParticleSystem();
                    ParticleSystemUI.GetInstance().SetResourcePath(FilePath);
                    //InspectSelectedItem();
                }
                else if (StringHelper.IgnoreCaseEqual(Extension, ".emitter"))
                {
                    InspectSelectedItem();
                }
                else if (StringHelper.IgnoreCaseEqual(Extension, ".mpc"))
                {
                    InspectSelectedItem();
                }
                else if (StringHelper.IgnoreCaseEqual(Extension, ".materialfunction"))
                {
                    string Path1 = EditorUtilities.EditorFilenameToStandardFilename(FilePath);
                    MaterialEditorUIManager.Instance.OpenMaterial(Path1, CEngine.ClassIDType.CLASS_MaterialFunction);
                }
                else if (StringHelper.IgnoreCaseEqual(Extension, ".iam"))
                {
                    InputActionMappingEditorUIManager.Instance.OpenInputActionMapping(FilePath);
                }
                else
                {
                    ProcessHelper.OpenFile(FilePath);
                }
            }
        }

        bool CanNdaFileBeInspect(string FilePath)
        {
            string FilePath1 = EditorUtilities.EditorFilenameToStandardFilename(FilePath);
            string Extension = PathHelper.GetExtension(FilePath1);
            if (StringHelper.IgnoreCaseEqual(Extension, ".nda"))
            {
                AssetType AssetType = AssetImporterManager.Instance().GetAssetType(FilePath1);
                if (AssetType == AssetType.Default)
                {
                    CEngine.ClassIDType ObjectClassID = Resource.GetResourceTypeStatic(FilePath1);
                    if (ObjectClassID == CEngine.ClassIDType.CLASS_Material ||
                        ObjectClassID == CEngine.ClassIDType.CLASS_Fx ||
                        ObjectClassID == CEngine.ClassIDType.CLASS_MeshAssetDataResource ||
                        ObjectClassID == CEngine.ClassIDType.CLASS_TerrainResource ||
                        ObjectClassID == CEngine.ClassIDType.CLASS_CurveControllerRes ||
                        ObjectClassID == CEngine.ClassIDType.CLASS_Texture2DArray ||
                        ObjectClassID == CEngine.ClassIDType.CLASS_DataAssetResource ||
                        Resource.IsObjectTexture(ObjectClassID)) 
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        public void InspectSelectedItem()
        {
            ListViewItem ListViewItem = _ListView.GetSelectedItem();
            if (ListViewItem != null && ListViewItem.bIsDirectory == false)
            {
                string Path = ListViewItem.Path;
                InspectResourceItem(Path);
            }
            else
            {
                InspectResourceObject(null);
            }
        }

        public void InspectResourceItem(string Path)
        {
            object Object = null;
            string Extension = PathHelper.GetExtension(Path);
            if (StringHelper.IgnoreCaseEqual(Extension, ".nda"))
            {
                string Path1 = EditorUtilities.EditorFilenameToStandardFilename(Path);
                if (CanNdaFileBeInspect(Path1))
                {
                    CEngine.ClassIDType ObjectClassID = Resource.GetResourceTypeStatic(Path);
                    if (ObjectClassID == CEngine.ClassIDType.CLASS_Fx)
                    {
                        Fx fx = Resource.Get(Path1) as Fx;
                        if (fx.GetVersion() == 2)
                        {
                            MaterialEditorUIManager.Instance.OpenMaterial(Path1, CEngine.ClassIDType.CLASS_Fx);
                            return;
                        }
                        else
                        {
                            FxEditorUI FxEditorUI = FxEditorUI.GetInstance();
                            FxEditorUI.OpenFx(fx);
                            MainUI MainUI = MainUI.GetInstance();
                            MainUI.ActivateDockingCard_FxView(FxEditorUI.GetDockingCard());
                            return;
                        }
                    }
                    if (ObjectClassID == CEngine.ClassIDType.CLASS_Material)
                    {
                        if (CinematicUI.GetInstance().GetEditStatus() == CinematicUI.EditStatus.EnterEdit && CinematicUI.GetInstance().QueryMaterialIsExist(Path1))
                        {
                            Resource res = Resource.Get(Path1);
                            Material Material = res as Material;
                            var InstancePtr = Clicross.resource.Material.Material_CreateInstance(Material.ResourcePtr as Clicross.resource.Material);
                            MaterialInstance Instance = new MaterialInstance(InstancePtr);
                            Instance.Refresh();
                            Instance.SetPath(Instance.Parent + "_Instance");
                            Object = Instance;
                        }
                        else
                        {
                            Material material = Resource.Get(Path1) as Material;
                            if (material.GetVersion() == 2)
                            {
                                MaterialEditorUIManager.Instance.OpenMaterial(Path1, CEngine.ClassIDType.CLASS_Material);
                                return;
                            }
                            else
                            {
                                Resource res = Resource.Get(Path1);
                                Object = res;
                            }
                        }
                    }
                    else
                    {
                        Resource res = Resource.Get(Path1);
                        Object = res;
                    }
                }
            }
            if (StringHelper.IgnoreCaseEqual(Extension, ".act"))
            {
                AnimatorUI.GetInstance().Refresh(Path);
            }
            if (StringHelper.IgnoreCaseEqual(Extension, ".system") || StringHelper.IgnoreCaseEqual(Extension, ".emitter") || StringHelper.IgnoreCaseEqual(Extension, ".mpc"))
            {
                string Path1 = EditorUtilities.EditorFilenameToStandardFilename(Path);
                Object = Resource.Get(Path1, false);
            }
            InspectResourceObject(Object);
        }

        public void InspectResourceObject(object Object)
        {
            ResourceInspectorUI.Inspect(Object);
            _InspectedResource = Object;
        }

        void OnListViewSelectionChanged(ListView Sender)
        {
            if (_bSearchMode || IsClassifyMode())
            {
                RefreshNavigator_Search();
            }
        }

        void OnListViewItemDoubleClicked(ListView Sender, ListViewItem ListViewItem)
        {
            OpenListViewItem(ListViewItem);
        }

        void DoRenameListViewFile(string NewName, string OldName, string OldFilename)
        {
            if (EditorUtilities.CheckFileOrDirectoryName(NewName) == false)
            {
                string Tips = string.Format("File name: {0} contains illegal char/chars.", NewName);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            string DirectoryName = PathHelper.GetDirectoryName(OldFilename);
            string NewFilename = DirectoryName + "/" + NewName;
            if (FileHelper.IsFileExists(NewFilename))
            {
                string NewFilename1 = EditorUtilities.EditorFilenameToStandardFilename(NewFilename);
                string Tips = string.Format("File: {0} already exists.", NewFilename1);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            DoRenameFile(OldFilename, NewFilename);
            bool bResetScroll = false;
            RefreshListView(bResetScroll);
            ListViewItem NewListViewItem = _ListView.FindItemByPath(NewFilename);
            if (NewListViewItem != null)
            {
                _ListView.SelectItem(NewListViewItem);
                _ListView.TriggerSelectionChangedEvent();
            }
        }

        void DoRenameListViewFolder(string NewName, string OldName, string OldDirectoryName)
        {
            if (EditorUtilities.CheckFileOrDirectoryName(NewName) == false)
            {
                string Tips = string.Format("Directory name: {0} contains illegal char/chars.", NewName);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            string ParentDirectoryName = PathHelper.GetDirectoryName(OldDirectoryName);
            string NewDirectoryName = ParentDirectoryName + "/" + NewName;
            if (DirectoryHelper.IsDirectoryExists(NewDirectoryName))
            {
                string NewDirectoryName1 = EditorUtilities.EditorFilenameToStandardFilename(NewDirectoryName);
                string Tips = string.Format("Directory: {0} already exists.", NewDirectoryName1);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            DoRenameDirectory(OldDirectoryName, NewDirectoryName);
            UpdateDirectory();
            bool bResetScroll = false;
            RefreshListView(bResetScroll);
            ListViewItem NewListViewItem = _ListView.FindItemByPath(NewDirectoryName);
            if (NewListViewItem != null)
            {
                _ListView.SelectItem(NewListViewItem);
            }
        }

        void DoRenameListView(string NewName, string OldName, bool bFolder, string OldPath)
        {
            if (NewName == "")
            {
                return;
            }
            if (NewName == OldName)
            {
                return;
            }
            if (bFolder)
            {
                DoRenameListViewFolder(NewName, OldName, OldPath);
            }
            else
            {
                DoRenameListViewFile(NewName, OldName, OldPath);
            }
        }

        void OnListViewItemRename(ListView Sender, string NewName, string OldName, ListViewItem ListViewItem)
        {
            bool bFolder = ListViewItem.bIsDirectory;
            DoRenameListView(NewName, OldName, bFolder, ListViewItem.Path);
        }

        void OnListViewItemIconPaint(ListView Sender, ListViewItem ListViewItem, Control PanelItemIcon)
        {
            int X1 = PanelItemIcon.GetScreenX();
            int Y1 = PanelItemIcon.GetScreenY();
            int Width1 = PanelItemIcon.GetWidth();
            int Height1 = PanelItemIcon.GetHeight();
            int X = Sender.GetScreenX();
            int Y = Sender.GetScreenY();
            int Width = Sender.GetWidth();
            int Height = Sender.GetHeight();
            if (UIManager.RectInRect(X1, Y1, Width1, Height1, X, Y, Width, Height))
            {
                Device Device = GetDevice();
                if (Device.GetActivated())
                {
                    if (ListViewItem.bIsDirectory == false)
                    {
                        // either image is not set or image is invalid, gc ed
                        if (ListViewItem.Image == _TexturePanelFile || !ThumbnailHelper.GetInstance().UpdateThumbnailTextureUsage(ListViewItem.Image))
                        {
                            string Path = ListViewItem.Path;
                            Texture ThumbnailTexture = ThumbnailHelper.GetInstance().GetThumbnailTexture(Path);

                            if (ThumbnailTexture != null)
                            {
                                ListViewItem.Image = ThumbnailTexture;
                                PanelItemIcon.SetImage(ThumbnailTexture);
                                PanelItemIcon.ClearChildren();
                            }
                        }
                    }
                }

                // it's a hack, ideally all the previous check would make sure the image is valid (no matter gced or not)
                // but in reallity it happens occassionlly (Released but pass the check), and it's hard to locate the reason
                if (ListViewItem.Image.GetInternalResourceValid() == false )
                {
                    EditorLogger.Log(LogMessageType.Warning, "Use a releasedTexture" + ListViewItem.Path+ Device.GetActivated());
                    ListViewItem.Image = _TexturePanelFile;
                    PanelItemIcon.SetImage(_TexturePanelFile);
                    PanelItemIcon.ClearChildren();
                }

                PanelItemIcon.PaintThis();
                PanelItemIcon.PaintChildren();
                PanelItemIcon.PaintThisAfter();
                if (!ListViewItem.bIsDirectory)
                {
                    string FilePath = ListViewItem.Path;
                    string Extension = PathHelper.GetExtension(FilePath);
                    if (Extension == "") return;
                    bool IsExist = false;
                    foreach (var Value in FilterName)
                    {
                        if (StringHelper.IgnoreCaseEqual(Extension, Value))
                        {
                            IsExist = true;
                            break;
                        }
                    }
                    if (IsExist)
                    {
                        string FilePath1 = EditorUtilities.EditorFilenameToStandardFilename(FilePath);
                        CEngine.ClassIDType ObjectClassID = ResourceTypeCache.GetInstance().GetResourceType_Cache(FilePath1);
                        _UnderLineColor = ClassifyLabelUI.GetClassificationColor(ObjectClassID);
                        GetUIManager().GetGraphics2D().FillRectangle(_UnderLineColor, PanelItemIcon.GetScreenX(),
                                                PanelItemIcon.GetScreenY() + PanelItemIcon.GetHeight() - 3, PanelItemIcon.GetWidth(), 3);
                    }
                    else
                    {
                        if (Extension.Length > 0 && StringHelper.IgnoreCaseEqual(Extension, ".world"))
                        {
                            _UnderLineColor = new Color(0.75f, 0.25f, 0.25f, 1.0f);
                            GetUIManager().GetGraphics2D().FillRectangle(_UnderLineColor, PanelItemIcon.GetScreenX(),
                                                PanelItemIcon.GetScreenY() + PanelItemIcon.GetHeight() - 3, PanelItemIcon.GetWidth(), 3);
                        }
                        else
                        {
                            GetUIManager().GetGraphics2D().FillRectangle(Color.EDITOR_UI_GRAY_TEXT_COLOR, PanelItemIcon.GetScreenX(),
                                                    PanelItemIcon.GetScreenY() + PanelItemIcon.GetHeight() - 3, PanelItemIcon.GetWidth(), 3);
                        }
                    }

                    bool IsNeedText = true;
                    foreach (var Value in NoNeedText)
                    {
                        if (StringHelper.IgnoreCaseEqual(Extension, Value))
                        {
                            IsNeedText = false;
                            break;
                        }
                    }

                    if (IsNeedText)
                    {
                        string Text = Extension.Substring(1).ToLower();
                        if (Text == "system")
                        {
                            Text = "particle";
                        }
                        Font Font;

                        bool IsScaleFontText = false;
                        foreach (var Value in ScaleFontText)
                        {
                            if (StringHelper.IgnoreCaseEqual(Extension, Value))
                            {
                                IsScaleFontText = true;
                                break;
                            }
                        }

                        if (IsScaleFontText)
                        {
                            Font = GetUIManager().GetDefaultFont(15);
                        }
                        else
                        {
                            Font = GetUIManager().GetDefaultFont(11);
                        }
                        Color TextColor = new Color(0.0f, 0.0f, 0.0f, 1.0f);
                        Font.DrawString(GetUIManager(), Text, ref TextColor, PanelItemIcon.GetScreenX(), PanelItemIcon.GetScreenY(), PanelItemIcon.GetWidth(),
                                        PanelItemIcon.GetHeight(), TextAlign.CenterCenter);
                    }
                }
            }
        }

        void OnListViewRightMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (Sender.IsPointIn(MouseX, MouseY) == false)
            {
                return;
            }
            bool bListViewContextMenu = true;
            ShowContextMenu(bListViewContextMenu, MouseX, MouseY);
            bContinue = false;
            _bCheckAll = false;
        }

        void OnListViewKeyDown(Control Sender, Key Key, ref bool bContinue)
        {
            Device Device = GetDevice();
            ShortcutConfig ShortcutConfig = ShortcutConfig.GetInstance();
            if (Key == Key.Delete)
            {
                if (Device.IsNoneModifiersDown() || Device.IsShiftDownOnly())
                {
                    DoDelete_ListView();
                    bContinue = false;
                }
            }
            else if (Key == Key.F2)
            {
                if (Device.IsNoneModifiersDown())
                {
                    DoStartRename_ListView();
                    bContinue = false;
                }
            }
            else if (Key == Key.Escape)
            {
                if (Device.IsNoneModifiersDown())
                {
                    DoCancelSearch();
                    bContinue = false;
                }
            }
            else if (ShortcutConfig.IsShortcutDown(GetDevice(), "FolderNew", Key))
            {
                OnMenuItemCreateFolderClicked(null);
                bContinue = false;
            }
            else if (ShortcutConfig.IsShortcutDown(GetDevice(), "EditPaste", Key))
            {
                DuplicateSelectedFiles();
                bContinue = false;
            }
            else if (ShortcutConfig.IsShortcutDown(GetDevice(), "EditSelectAll", Key))
            {
                _bCheckAll = true;
                bool bResetScroll = false;
                RefreshListView(bResetScroll);
                bContinue = false;
            }
        }

        void OnListViewMouseWheel(Control Sender, int MouseX, int MouseY, int MouseDeltaZ, int MouseDeltaW, ref bool bContinue)
        {
            if (_ListView.IsPointIn(MouseX, MouseY))
            {
                Device Device = GetDevice();
                if (Device.IsControlDownOnly())
                {
                    _TrackBarScale.OnMouseWheel(MouseDeltaZ);
                    bContinue = false;
                }
            }
        }

        #endregion

        #region Drag drop

        public bool IsPathesDragging()
        {
            return _PathesDragged != null;
        }

        public List<string> GetPathesDragged()
        {
            return _PathesDragged;
        }

        void OnDragDropManagerDragBegin(DragDropManager Sender, UIManager UIManager, int OriginalMouseX, int OriginalMouseY, ref bool bDragBegin, ref string ImageFilename)
        {
            if (UIManager != GetUIManager())
            {
                return;
            }

            if (!IsDockingCardActive())
            {
                return;
            }

            if (_ListView.IsPointIn_Recursively(OriginalMouseX, OriginalMouseY))
            {
                ListViewItem ListViewItem = _ListView.HitTest(OriginalMouseX, OriginalMouseY);
                if (ListViewItem != null)
                {
                    List<ListViewItem> SelectedItems = _ListView.GetSelectedItems();
                    if (SelectedItems.Contains(ListViewItem))
                    {
                        _PathesDragged = new List<string>();
                        foreach (ListViewItem SelectedItem in SelectedItems)
                        {
                            _PathesDragged.Add(SelectedItem.Path);
                        }
                        bDragBegin = true;
                    }
                }
            }
            else if (_Tree.IsPointIn_Recursively(OriginalMouseX, OriginalMouseY))
            {
                TreeItemHitTest HitTest = _Tree.HitTest(OriginalMouseX, OriginalMouseY);
                if (HitTest.HitResult != TreeItemHitResult.Nothing)
                {
                    TreeItem TreeItemDrag = HitTest.ItemHit;
                    if (TreeItemDrag != null)
                    {
                        _PathesDragged = new List<string>();
                        string Path = TreeItemDrag.GetTagString();
                        _PathesDragged.Add(Path);
                        bDragBegin = true;
                    }
                }
            }
        }

        void OnDragDropManagerDragMove(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY)
        {
            if (!IsDockingCardActive())
            {
                return;
            }

            TreeDropInfo DropInfo = null;
            if (UIManager == GetUIManager() && IsPathesDragging())
            {
                if (_ListView.IsPointIn_Recursively(MouseX, MouseY))
                {
                    ListViewItem DropTargetItem = _ListView.HitTest(MouseX, MouseY);
                    if (DropTargetItem != null)
                    {
                        if (DropTargetItem.bIsDirectory)
                        {
                            _ListView.SetDropTargetItem(DropTargetItem);
                        }
                        else
                        {
                            _ListView.SetDropTargetItem(null);
                        }
                    }
                    else
                    {
                        _ListView.SetDropTargetItem(null);
                    }
                }
                else if (_Tree.IsPointIn_Recursively(MouseX, MouseY))
                {
                    DropInfo = new TreeDropInfo();
                    DropInfo.bDetailed = false;
                    _Tree.HitTest(MouseX, MouseY, false, DropInfo);
                }
            }
            _Tree.SetDropInfo(DropInfo);
        }

        async void DoDrop(string TargetDirectory)
        {
            Entity entity = HierarchyUI.GetInstance().GetEntityDragged();
            if (entity == null)
            {
                Device Device = GetDevice();
                bool bNoneModifiersDown = Device.IsNoneModifiersDown();
                bool bControlDownOnly = Device.IsControlDownOnly();
                if (bNoneModifiersDown || bControlDownOnly)
                {
                    bool bImport = false;
                    bool bCopy = !bNoneModifiersDown;
                    bool bMove = bNoneModifiersDown;
                    await ImportFileList(TargetDirectory, bImport, bCopy, bMove, _PathesDragged);
                }
            }
            else
            {
                entity.CreatePrefab(TargetDirectory);
                HierarchyUI.GetInstance().UpdateHierarchy();
                UpdateAll();
                string PrefabRelativePath = ResourceManager.Instance().ConvertGuidToPath(entity.GetPrefabId());
                string PrefabPath = EditorUtilities.StandardFilenameToEditorFilename(PrefabRelativePath);
                JumpToPath(PrefabPath);
            }
        }

        string GetCurrentTargetDirectory()
        {
            TreeItem SelectedItem = _Tree.GetSelectedItem();
            string TargetDirectory = SelectedItem.GetTagString();
            if (TargetDirectory == "")
            {
                if (_NavigationPath != "")
                {
                    TargetDirectory = MainUI.GetInstance().GetProjectDirectory() + "/" + _NavigationPath;
                }
                else
                {
                    TargetDirectory = MainUI.GetInstance().GetProjectDirectory() + "/Contents";
                }
            }
            return TargetDirectory;
        }

        void OnDragDropManagerDragEnd(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY, ref bool bContinue)
        {
            if (UIManager != GetUIManager())
            {
                return;
            }

            if (!IsDockingCardActive())
            {
                return;
            }

            if (IsPathesDragging() || HierarchyUI.GetInstance().IsDraggingCreatePrefab())
            {
                if (_ListView.IsPointIn_Recursively(MouseX, MouseY))
                {
                    string TargetDirectory = GetCurrentTargetDirectory();
                    ListViewItem ListViewItemToDrop = _ListView.HitTest(MouseX, MouseY);
                    if (ListViewItemToDrop != null)
                    {
                        if (ListViewItemToDrop.bIsDirectory)
                        {
                            TargetDirectory = ListViewItemToDrop.Path;
                        }
                    }
                    DoDrop(TargetDirectory);
                }
                else if (_Tree.IsPointIn_Recursively(MouseX, MouseY))
                {
                    TreeDropInfo DropInfo = new TreeDropInfo();
                    _Tree.HitTest(MouseX, MouseY, false, DropInfo);
                    TreeItem TreeItemDrop = DropInfo.InsertParent;
                    if (TreeItemDrop != null)
                    {
                        string TargetDirectory = TreeItemDrop.GetTagString();
                        DoDrop(TargetDirectory);
                    }
                }
            }
        }

        void OnDragDropManagerDragClear(DragDropManager Sender)
        {
            DragClearStates();
        }

        void OnDragDropManagerDragCancel(DragDropManager Sender)
        {
            DragClearStates();
        }

        void DragClearStates()
        {
            _PathesDragged = null;
            _ListView.SetDropTargetItem(null);
            _Tree.SetDropInfo(null);
        }

        public void OnDeviceDropFilesBegin(Device Sender)
        {
            _PathesDropped.Clear();
        }

        public void OnDeviceDropFilesFile(Device Sender, string Filename)
        {
            _PathesDropped.Add(Filename);
        }

        bool IsPathImportable(string Path)
        {
            AssetType AssetType = AssetImporterManager.Instance().GetAssetType(Path);
            if (AssetType != AssetType.Unknown && AssetType != AssetType.Default)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public bool IsPathesDroppedImportable()
        {
            foreach (string PathDropped in _PathesDropped)
            {
                if (DirectoryHelper.IsDirectoryExists(PathDropped))
                {
                    string Directory = PathDropped;
                    DirectoryWalker DirectoryWalker = new DirectoryWalker();
                    DirectoryWalker.WalkDirectory(Directory, true);
                    int Count = DirectoryWalker.GetDirectoryWalkItemCount();
                    for (int i = 0; i < Count; i++)
                    {
                        DirectoryWalkItem DirectoryWalkItem = DirectoryWalker.GetDirectoryWalkItem(i);
                        if (DirectoryWalkItem.bIsDirectory == false)
                        {
                            string Path = DirectoryWalkItem.Path;
                            if (IsPathImportable(Path))
                            {
                                return true;
                            }
                        }
                    }
                }
                else if (FileHelper.IsFileExists(PathDropped))
                {
                    string Path = PathDropped;
                    if (IsPathImportable(Path))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        bool IsAssetExportable(List<string> Paths)
        {
            foreach (var FilePath in Paths)
            {
                AssetType AssetType = AssetImporterManager.Instance().GetAssetType(FilePath);
                if (AssetType == AssetType.Default)
                {
                    CEngine.ClassIDType ObjectClassID = Resource.GetResourceTypeStatic(FilePath);
                    if (ObjectClassID == CEngine.ClassIDType.CLASS_MeshAssetDataResource ||
                        ObjectClassID == CEngine.ClassIDType.CLASS_Texture2D)
                    {
                        return true;
                    }
                }
            }
            return false;
        }


        bool IsPathInAsset(string Path, HashSet<AssetType> assetTypes)
        {
            AssetType AssetType = AssetImporterManager.Instance().GetAssetType(Path);
            return assetTypes.Contains(AssetType);
        }

        public bool IsPathHasAsset(List<string> Pathes, HashSet<AssetType> assetTypes)
        {
            foreach (string Path1 in Pathes)
            {
                if (DirectoryHelper.IsDirectoryExists(Path1))
                {
                    string Directory = Path1;
                    DirectoryWalker DirectoryWalker = new DirectoryWalker();
                    DirectoryWalker.WalkDirectory(Directory, true);
                    int Count = DirectoryWalker.GetDirectoryWalkItemCount();
                    for (int i = 0; i < Count; i++)
                    {
                        DirectoryWalkItem DirectoryWalkItem = DirectoryWalker.GetDirectoryWalkItem(i);
                        if (DirectoryWalkItem.bIsDirectory == false)
                        {
                            AssetType AssetType = AssetImporterManager.Instance().GetAssetType(DirectoryWalkItem.Path);
                            return assetTypes.Contains(AssetType);
                        }
                    }
                }
                else if (FileHelper.IsFileExists(Path1))
                {
                    AssetType AssetType = AssetImporterManager.Instance().GetAssetType(Path1);
                    return assetTypes.Contains(AssetType);
                }
            }
            return false;
        }


        public void ShowImportOrCopyUI(SimpleCommonDialogResultDelegate SimpleCommonDialogResultDelegate)
        {
            if (IsPathesDroppedImportable())
            {
                CommonDialogUI CommonDialogUI = new CommonDialogUI();
                CommonDialogUI.Initialize(GetUIManager(), "Import Or Copy", "Do you want to import the assets dragged?", CommonDialogType.UserDefined);
                CommonDialogUI.AddButton(110, 160, CommonDialogResult.UserDefined1, "Import");
                CommonDialogUI.AddButton(290, 160, CommonDialogResult.UserDefined2, "Copy");
                CommonDialogUI.AddButton(470, 160, CommonDialogResult.UserDefined3, "Cancel");
                CommonDialogUI.CloseEvent += (CommonDialogUI Sender1, CommonDialogResult Result) =>
                {
                    SimpleCommonDialogResultDelegate(Result);
                };
                DialogUIManager.GetInstance().ShowDialogUI(CommonDialogUI);
            }
            else
            {
                SimpleCommonDialogResultDelegate(CommonDialogResult.UserDefined2);
            }
        }

        string GetDirectoryPathOfTreeItem(TreeItem TreeItem)
        {
            string TagString = "";
            if (TreeItem != null)
            {
                TagString = TreeItem.GetTagString();
            }
            if (TagString == "")
            {
                return GetRootDirectory();
            }
            return TagString;
        }

        public void OnDeviceDropFilesEnd(Device Sender)
        {
            int MouseX = Sender.GetMouseX();
            int MouseY = Sender.GetMouseY();
            string TargetDirectory = "";
            if (_ListView.GetVisible_Recursively())
            {
                if (_ListView.IsPointIn_Recursively(MouseX, MouseY))
                {
                    ListViewItem ListViewItem = _ListView.HitTest(MouseX, MouseY);
                    if (ListViewItem != null && ListViewItem.bIsDirectory)
                    {
                        TargetDirectory = ListViewItem.Path;
                    }
                    else
                    {
                        TargetDirectory = GetCurrentTargetDirectory();
                    }
                }
            }
            if (_Tree.GetVisible_Recursively())
            {
                if (_Tree.IsPointIn_Recursively(MouseX, MouseY))
                {
                    TreeItemHitTest HitTest = _Tree.HitTest(MouseX, MouseY);
                    if (HitTest.HitResult != TreeItemHitResult.Nothing)
                    {
                        TreeItem ItemHit = HitTest.ItemHit;
                        TargetDirectory = GetDirectoryPathOfTreeItem(ItemHit);
                    }
                }
            }

            if (TargetDirectory == "")
            {
                TargetDirectory = _NavigationPath;
            }

            ShowImportOrCopyUI((CommonDialogResult Result) =>
            {
                bool bImport = false;
                bool bCopy = false;
                bool bMove = false;
                if (Result == CommonDialogResult.UserDefined1)
                {
                    bImport = true;
                    bCopy = false;
                }
                else if (Result == CommonDialogResult.UserDefined2)
                {
                    bImport = false;
                    bCopy = true;
                }
                else
                {
                    return;
                }

                ImporterDelegate SimpleDelegate = async (Importer importer) =>
                {
                    await ImportFileList(TargetDirectory, bImport, bCopy, bMove, _PathesDropped, importer);
                    if (_bAutoRecognition)
                    {
                        var ShaderPath = CrossEngineApi.GetSettingManager().GetRenderPipelineSettingForEditor().DefaultFX;
                        foreach (var Item in Dictionary)
                        {
                            if (await ImportFileList(TargetDirectory, bImport, bCopy, bMove, Item.Value, importer))
                            {
                                Thread.Sleep(1500);
                                Material.AutoCreateAndSave(ShaderPath, TargetDirectory, GetNdaPath(TargetDirectory, Item.Value[0]),
                                    GetNdaPath(TargetDirectory, Item.Value[1]), GetNdaPath(TargetDirectory, Item.Value[2]));
                            }
                        }
                        RefreshListView(false);
                    }

                };


                foreach (var Item in mImporters)
                {
                    if (Item.AcceptPath(_PathesDropped))
                    {
                        Item.ShowUI(SimpleDelegate, _PathesDropped);
                        return;
                    }
                }
            });
        }

        private async Task<bool> ImportFileList(string TargetDirectory, bool bImport, bool bCopy, bool bMove, List<string> Pathes, Importer importer = null)
        {
            bool AllSuccess = true;
            StringBuilder WarnString = new StringBuilder();

            if (importer == null)
            {
                importer = new GeneralFolderImporter();
            }

            int Index = 1;
            foreach (string PathDropped in Pathes)
            {
                AssetImportResult Result = await DoCopyOrMove(importer, TargetDirectory, PathDropped, bImport, bCopy, bMove);
                if (Result != null && !Result.bSuccess)
                {
                    AllSuccess = false;
                    foreach (var ErrorCode in Result.ErrorCodes)
                    {
                        WarnString.AppendLine(Convert.ToString(Index) + ": [" + Convert.ToString(ErrorCode) + "] " + PathDropped);
                    }
                }
                if (Result != null && Result.bSuccess)
                {
                    WarnString.AppendLine(Convert.ToString(Index) + ": [ImportFileSuccess] " + PathDropped);
                }
                Index++;
            }
            if (AllSuccess == false)
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Import Warning", WarnString.ToString());
            }
            else
            {

                if (WarnString.Length != 0)
                {
                    if (!_bAutoRecognition)
                    {
                        if (Pathes.Count > 1)
                        {
                            CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Import Success", "All files are imported successfully.");
                        }
                        else
                        {
                            CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Import Success", WarnString.ToString());
                        }
                    }
                }
            }
            UpdateAll();
            return AllSuccess;
        }

        #endregion

        #region Menu

        public void BulidCreateMenuItems(Menu Menu)
        {
            MenuItem MenuItem_Create_Folder = new MenuItem();
            MenuItem_Create_Folder.SetText("Folder");
            MenuItem_Create_Folder.ClickedEvent += OnMenuItemCreateFolderClicked;

            MenuItem MenuItem_Create_VisualScript = new MenuItem();
            MenuItem_Create_VisualScript.SetText("Visual Script");
            MenuItem_Create_VisualScript.ClickedEvent += OnMenuItemCreateVisualScriptClicked;

            MenuItem MenuItem_Create_CSharpScript = new MenuItem();
            MenuItem_Create_CSharpScript.SetText("C# Script");
            MenuItem_Create_CSharpScript.ClickedEvent += OnMenuItemCreateCSharpScriptClicked;

            MenuItem MenuItem_Create_JSScript = new MenuItem();
            MenuItem_Create_JSScript.SetText("JS Script");
            MenuItem_Create_JSScript.ClickedEvent += OnMenuItemCreateJSScriptClicked;

            MenuItem MenuItem_Create_LuaScript = new MenuItem();
            MenuItem_Create_LuaScript.SetText("Lua Script");
            MenuItem_Create_LuaScript.ClickedEvent += OnMenuItemCreateLuaScriptClicked;

            MenuItem MenuItem_Create_TSScript = new MenuItem();
            MenuItem_Create_TSScript.SetText("TypeScript Script");
            MenuItem_Create_TSScript.ClickedEvent += OnMenuItemCreateTypeScriptClicked;

            MenuItem MenuItem_Create_Animator = new MenuItem();
            MenuItem_Create_Animator.SetText("Animator");
            MenuItem_Create_Animator.ClickedEvent += OnMenuItemCreateAnimatorClicked;

            MenuItem MenuItem_Create_Composite = new MenuItem();
            MenuItem_Create_Composite.SetText("Composite");
            MenuItem_Create_Composite.ClickedEvent += OnMenuItemCreateCompositeClicked;

            MenuItem MenuItem_Create_MotionMatchData = new MenuItem();
            MenuItem_Create_MotionMatchData.SetText("MotionMatch Data");
            MenuItem_Create_MotionMatchData.ClickedEvent += OnMenuItemCreateMotionMatchData;

            MenuItem MenuItem_Create_Scene = new MenuItem();
            MenuItem_Create_Scene.SetText("Scene");
            MenuItem_Create_Scene.ClickedEvent += OnMenuItemCreateSceneClicked;

            MenuItem MenuItem_Create_Shader = new MenuItem();
            MenuItem_Create_Shader.SetText("Shader(.shader)");
            MenuItem_Create_Shader.ClickedEvent += OnMenuItemCreateShaderClicked;

            MenuItem MenuItem_Create_ShaderModule = new MenuItem();
            MenuItem_Create_ShaderModule.SetText("Shader Module(.hlsl)");
            MenuItem_Create_ShaderModule.ClickedEvent += OnMenuItemCreateShaderModuleClicked;

            MenuItem MenuItem_Create_Fx = new MenuItem();
            MenuItem_Create_Fx.SetText("Fx");
            MenuItem_Create_Fx.ClickedEvent += OnMenuItemCreateFxClicked;

            MenuItem MenuItem_Create_Fx_New = new MenuItem();
            MenuItem_Create_Fx_New.SetText("MaterialGraph");
            MenuItem_Create_Fx_New.ClickedEvent += OnMenuItemCreateFxNewClicked;

            MenuItem MenuItem_Create_Material = new MenuItem();
            MenuItem_Create_Material.SetText("Material");
            MenuItem_Create_Material.ClickedEvent += OnMenuItemCreateMaterialClicked;

            MenuItem MenuItem_Create_MaterialFunction = new MenuItem();
            MenuItem_Create_MaterialFunction.SetText("MaterialFunction");
            MenuItem_Create_MaterialFunction.ClickedEvent += OnMenuItemCreateMaterialFunctionClicked;

            MenuItem MenuItem_Create_MPC = new MenuItem();
            MenuItem_Create_MPC.SetText("MaterialParameterCollection");
            MenuItem_Create_MPC.ClickedEvent += OnMenuItemCreateMPCClicked;

            MenuItem MenuItem_Create_Curve = new MenuItem();
            MenuItem_Create_Curve.SetText("Curve");
            MenuItem_Create_Curve.ClickedEvent += OnMenuItemCreateCurveClicked;

            MenuItem MenuItem_Create_CurveController = new MenuItem();
            MenuItem_Create_CurveController.SetText("Curve Controller");
            MenuItem_Create_CurveController.ClickedEvent += OnMenuItemCreateCurveControllerClicked;

            MenuItem MenuItem_Create_DataAsset = new MenuItem();
            MenuItem_Create_DataAsset.SetText("Data Asset");
            MenuItem_Create_DataAsset.ClickedEvent += OnMenuItemCreateDataAssetClicked;

            MenuItem MenuItem_Create_WorkflowGraph = new MenuItem();
            MenuItem_Create_WorkflowGraph.SetText("Workflow Graph");
            MenuItem_Create_WorkflowGraph.ClickedEvent += OnMenuItemCreateWorkflowEditorClicked;

            MenuItem MenuItem_Create_InputActionMapping = new MenuItem();
            MenuItem_Create_InputActionMapping.SetText("Input Action Mapping");
            MenuItem_Create_InputActionMapping.ClickedEvent += OnMenuItemCreateInputActionMappingClicked;

            MenuItem MenuItem_Create_ParticleSystem = new MenuItem();
            MenuItem_Create_ParticleSystem.SetText("Particle System");
            MenuItem_Create_ParticleSystem.ClickedEvent += OnMenuItemCreateParticleSystemClicked;

            MenuItem MenuItem_Create_InstanceDataResource = new MenuItem();
            MenuItem_Create_InstanceDataResource.SetText("InstanceDataResource");
            MenuItem_Create_InstanceDataResource.ClickedEvent += OnMenuItemCreateInstanceDataResourceClicked;

            MenuItem MenuItem_Create_ParticleEmitter = new MenuItem();
            MenuItem_Create_ParticleEmitter.SetText("Particle Emitter");
            MenuItem_Create_ParticleEmitter.ClickedEvent += OnMenuItemCreateParticleEmitterClicked;

            MenuItem MenuItem_Create_RenderTexture = new MenuItem();
            MenuItem_Create_RenderTexture.SetText("Render Texture");
            MenuItem_Create_RenderTexture.ClickedEvent += OnMenuItemCreateRenderTextureClicked;

            MenuItem MenuItem_Create_Prefab = new MenuItem();
            MenuItem_Create_Prefab.SetText("Prefab");
            MenuItem_Create_Prefab.ClickedEvent += OnMenuItemCreatePrefabClicked;

            Menu.AddMenuItem(MenuItem_Create_Folder);
            //Menu.AddMenuItem(MenuItem_Create_Scene);
            Menu.AddSeperator();
            Menu.AddMenuItem(MenuItem_Create_VisualScript);
            Menu.AddMenuItem(MenuItem_Create_CSharpScript);
            Menu.AddMenuItem(MenuItem_Create_JSScript);
            Menu.AddMenuItem(MenuItem_Create_LuaScript);
            Menu.AddMenuItem(MenuItem_Create_TSScript);
            Menu.AddSeperator();
            Menu.AddMenuItem(MenuItem_Create_Animator);
            Menu.AddMenuItem(MenuItem_Create_Composite);
            Menu.AddMenuItem(MenuItem_Create_MotionMatchData);
            Menu.AddMenuItem(MenuItem_Create_Curve);
            Menu.AddMenuItem(MenuItem_Create_CurveController);
            Menu.AddSeperator();
            Menu.AddMenuItem(MenuItem_Create_Shader);
            Menu.AddMenuItem(MenuItem_Create_ShaderModule);
            Menu.AddMenuItem(MenuItem_Create_Fx);
            Menu.AddMenuItem(MenuItem_Create_Fx_New);
            Menu.AddMenuItem(MenuItem_Create_Material);
            Menu.AddMenuItem(MenuItem_Create_MaterialFunction);
            Menu.AddMenuItem(MenuItem_Create_MPC);
            Menu.AddMenuItem(MenuItem_Create_ParticleSystem);
            Menu.AddMenuItem(MenuItem_Create_ParticleEmitter);
            Menu.AddMenuItem(MenuItem_Create_InstanceDataResource);
            Menu.AddSeperator();
            Menu.AddMenuItem(MenuItem_Create_RenderTexture);
            Menu.AddSeperator();
            Menu.AddMenuItem(MenuItem_Create_Prefab);
            Menu.AddSeperator();
            Menu.AddMenuItem(MenuItem_Create_WorkflowGraph);
            Menu.AddMenuItem(MenuItem_Create_DataAsset);
            Menu.AddSeperator();
            Menu.AddMenuItem(MenuItem_Create_InputActionMapping);
        }

        void ShowContextMenu(bool bListViewContextMenu, int MouseX, int MouseY)
        {
            _bListViewContextMenu = bListViewContextMenu;

            if (_bListViewContextMenu)
            {
                if (_NavigationPath == "")
                {
                    return;
                }
            }

            MenuItem MenuItem_FoldAll = new MenuItem();
            MenuItem_FoldAll.SetText("Fold All");
            MenuItem_FoldAll.ClickedEvent += OnMenuItemFoldAllClicked;

            MenuItem MenuItem_ExpandAll = new MenuItem();
            MenuItem_ExpandAll.SetText("Expand All");
            MenuItem_ExpandAll.ClickedEvent += OnMenuItemExpandAllClicked;

            MenuItem MenuItem_Inspect = new MenuItem();
            MenuItem_Inspect.SetText("Inspect");
            MenuItem_Inspect.ClickedEvent += OnMenuItemInspectClicked;

            Menu Menu_Create = new Menu(GetUIManager());
            Menu_Create.Initialize();
            MenuItem MenuItem_Create = new MenuItem();
            MenuItem_Create.SetText("Create");
            MenuItem_Create.SetMenu(Menu_Create);

            BulidCreateMenuItems(Menu_Create);

            MenuItem MenuItem_ShowInExplorer = new MenuItem();
            MenuItem_ShowInExplorer.SetText("Show In Explorer");
            MenuItem_ShowInExplorer.ClickedEvent += OnMenuItemShowInExplorerClicked;

            MenuItem MenuItem_ShowInFolderView = new MenuItem();
            MenuItem_ShowInFolderView.SetText("Show in Folder View");
            MenuItem_ShowInFolderView.SetEnable(true);
            MenuItem_ShowInFolderView.ClickedEvent += OnMenuItemShowInFolderViewClicked;

            MenuItem MenuItem_Open = new MenuItem();
            MenuItem_Open.SetText("Open");
            MenuItem_Open.ClickedEvent += OnMenuItemOpenClicked;

            MenuItem MenuItem_Open_InMaterialEditor = new MenuItem();
            MenuItem_Open_InMaterialEditor.SetText("Open In MaterialEditor(Experiment)");
            MenuItem_Open_InMaterialEditor.ClickedEvent += OnMenuItemOpenInMateiralEditor;

            MenuItem MenuItem_Open_InWorkflowEditor = new MenuItem();
            MenuItem_Open_InWorkflowEditor.SetText("Open In WorkflowEditor(Experiment)");
            MenuItem_Open_InWorkflowEditor.ClickedEvent += OnMenuItemOpenInWorkflowEditor;

            MenuItem MenuItem_Delete = new MenuItem();
            MenuItem_Delete.SetText("Delete");
            MenuItem_Delete.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/Delete.png"));
            MenuItem_Delete.ClickedEvent += OnMenuItemDeleteClicked;

            MenuItem MenuItem_Duplicate = new MenuItem();
            MenuItem_Duplicate.SetText("Duplicate");
            MenuItem_Duplicate.ClickedEvent += OnMenuItemDuplicateClicked;

            MenuItem MenuItem_CopyRelativePath = new MenuItem();
            MenuItem_CopyRelativePath.SetText("Copy Relative Path");
            MenuItem_CopyRelativePath.ClickedEvent += OnMenuItemCopyRelativePathClicked;

            MenuItem MenuItem_CopyAbsolutePath = new MenuItem();
            MenuItem_CopyAbsolutePath.SetText("Copy Absolute Path");
            MenuItem_CopyAbsolutePath.ClickedEvent += OnMenuItemCopyAbsolutePathClicked;

            MenuItem MenuItem_Rename = new MenuItem();
            MenuItem_Rename.SetText("Rename");
            MenuItem_Rename.ClickedEvent += OnMenuItemRenameClicked;

            MenuItem MenuItem_Import = new MenuItem();
            MenuItem_Import.SetText("Import");
            MenuItem_Import.ClickedEvent += OnMenuItemImportClicked;

            MenuItem MenuItem_Export = new MenuItem();
            MenuItem_Export.SetText("Export");
            MenuItem_Export.ClickedEvent += OnMenuItemExportClicked;

            MenuItem MenuItem_ImportAsset = new MenuItem();
            MenuItem_ImportAsset.SetText("Import Asset...");
            MenuItem_ImportAsset.ClickedEvent += OnMenuItemImportAssetClicked;

            MenuItem MenuItem_ImportAssets = new MenuItem();
            MenuItem_ImportAssets.SetText("Import Assets...");
            MenuItem_ImportAssets.ClickedEvent += OnMenuItemImportAssetsClicked;

            MenuItem MenuItem_CookDirectory = new MenuItem();
            MenuItem_CookDirectory.SetText("Cook Directory");
            MenuItem_CookDirectory.ClickedEvent += OnMenuItemCookDirectoryClicked;

            MenuItem MenuItem_CookFile = new MenuItem();
            MenuItem_CookFile.SetText("Cook File");
            MenuItem_CookFile.ClickedEvent += OnMenuItemCookFileClicked;

            MenuItem MenuItem_UnCookFile = new MenuItem();
            MenuItem_UnCookFile.SetText("UnCook File");
            MenuItem_UnCookFile.ClickedEvent += OnMenuItemUnCookFileClicked;

            MenuItem MenuItem_AddCookRequire = new MenuItem();
            MenuItem_AddCookRequire.SetText("Add Cook Require");
            MenuItem_AddCookRequire.ClickedEvent += OnMenuItemAddCookRequireClicked;

            MenuItem MenuItem_AddCookIgnore = new MenuItem();
            MenuItem_AddCookIgnore.SetText("Add Cook Ignore");
            MenuItem_AddCookIgnore.ClickedEvent += OnMenuItemAddCookIgnoreClicked;

            MenuItem MenuItem_CopyResource = new MenuItem();
            MenuItem_CopyResource.SetText("Migrate Resource");
            MenuItem_CopyResource.ClickedEvent += OnMenuItemCopyResourceClicked;

            MenuItem MenuItem_ZipResource = new MenuItem();
            MenuItem_ZipResource.SetText("Zip Resource");
            MenuItem_ZipResource.ClickedEvent += OnMenuItemZipResourceClicked;

            MenuItem MenuItem_ResaveResource = new MenuItem();
            MenuItem_ResaveResource.SetText("Resave Resource");
            MenuItem_ResaveResource.ClickedEvent += OnReSaveAssetClicked;


            MenuItem MenuItem_ApplyBack = new MenuItem();
            MenuItem_ApplyBack.SetText("Use Back");
            MenuItem_ApplyBack.ClickedEvent += OnMenuItemUseBackClicked;



            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();
            if (bListViewContextMenu == false)
            {
                MenuContextMenu.AddMenuItem(MenuItem_FoldAll);
                MenuContextMenu.AddMenuItem(MenuItem_ExpandAll);
                MenuContextMenu.AddSeperator();
            }
            MenuContextMenu.AddMenuItem(MenuItem_Inspect);
            MenuContextMenu.AddSeperator();
            MenuContextMenu.AddMenuItem(MenuItem_Create);
            MenuContextMenu.AddSeperator();
            MenuContextMenu.AddMenuItem(MenuItem_ShowInExplorer);
            MenuContextMenu.AddMenuItem(MenuItem_ShowInFolderView);
            MenuContextMenu.AddSeperator();
            MenuContextMenu.AddMenuItem(MenuItem_Open);
            MenuContextMenu.AddMenuItem(MenuItem_Delete);
            MenuContextMenu.AddMenuItem(MenuItem_Open_InMaterialEditor);
            MenuContextMenu.AddMenuItem(MenuItem_Open_InWorkflowEditor);
            MenuContextMenu.AddSeperator();
            MenuContextMenu.AddMenuItem(MenuItem_Duplicate);
            MenuContextMenu.AddSeperator();
            MenuContextMenu.AddMenuItem(MenuItem_CopyRelativePath);
            MenuContextMenu.AddMenuItem(MenuItem_CopyAbsolutePath);
            MenuContextMenu.AddSeperator();
            MenuContextMenu.AddMenuItem(MenuItem_Rename);
            MenuContextMenu.AddSeperator();
            if (bListViewContextMenu)
            {
                MenuContextMenu.AddMenuItem(MenuItem_CookFile);
                MenuItem_CookFile.SetEnable(IsCookEnable());
                MenuContextMenu.AddMenuItem(MenuItem_UnCookFile);
            }
            else
            {
                MenuContextMenu.AddMenuItem(MenuItem_CookDirectory);
                MenuContextMenu.AddMenuItem(MenuItem_AddCookRequire);
                MenuContextMenu.AddMenuItem(MenuItem_AddCookIgnore);
                MenuItem_CookDirectory.SetEnable(IsCookEnable());
            }

            MenuContextMenu.AddSeperator();

            if (_bListViewContextMenu)
            {
                List<ListViewItem> SelectedItems = _ListView.GetSelectedItems();
                int Count = SelectedItems.Count;
                if (Count == 0)
                {
                    MenuItem_Open.SetEnable(false);
                    MenuItem_Delete.SetEnable(false);
                }

                if (Count != 1)
                {
                    MenuItem_Rename.SetEnable(false);
                }

                bool bHasFiles = false;
                foreach (ListViewItem Item in SelectedItems)
                {
                    if (Item.bIsDirectory == false)
                    {
                        bHasFiles = true;
                    }
                }
                if (!bHasFiles)
                {
                    MenuItem_Duplicate.SetEnable(false);
                }
                if (Count > 0)
                {
                    MenuContextMenu.AddMenuItem(MenuItem_Import);
                }
                else
                {
                    MenuContextMenu.AddMenuItem(MenuItem_ImportAsset);
                    MenuContextMenu.AddMenuItem(MenuItem_ImportAssets);
                }
                if (Count > 0)
                {
                    List<string> SelectedPathList = GetSelectedPathList();
                    bool bIsAssetExportable = IsAssetExportable(SelectedPathList);
                    if (bIsAssetExportable)
                    {
                        MenuContextMenu.AddMenuItem(MenuItem_Export);
                    }
                }
            }
            else
            {
                TreeItem SelectedItem = _Tree.GetSelectedItem();
                bool bCanFoldOrExpandAll = (SelectedItem == null) || SelectedItem.GetChildCount() > 0;
                MenuItem_FoldAll.SetEnable(bCanFoldOrExpandAll);
                MenuItem_ExpandAll.SetEnable(bCanFoldOrExpandAll);
                MenuItem_Open.SetEnable(false);
                if (SelectedItem == _Tree.GetRootItem())
                {
                    MenuItem_Delete.SetEnable(false);
                }
                List<TreeItem> SelectionList = _Tree.GetSelectedItems();
                if (SelectionList.Count != 1)
                {
                    MenuItem_Rename.SetEnable(false);
                }
                else
                {

                    TreeItem Selection = SelectionList[0];
                    if (Selection == _Tree.GetRootItem())
                    {
                        return;
                    }
                    if (Selection.GetParent() == _Tree.GetRootItem())
                    {
                        MenuItem_Rename.SetEnable(false);
                    }
                }
                MenuItem_Duplicate.SetEnable(false);

                MenuContextMenu.AddMenuItem(MenuItem_Import);
            }
            MenuContextMenu.AddSeperator();
            MenuContextMenu.AddMenuItem(MenuItem_CopyResource);
            MenuContextMenu.AddSeperator();
            MenuContextMenu.AddMenuItem(MenuItem_ZipResource);


            MenuContextMenu.AddMenuItem(MenuItem_ResaveResource);

            string selectedFilePath = _ListView.GetSelectedItem() != null ? _ListView.GetSelectedItem().Path : "";
            if (StringHelper.IgnoreCaseEqual(PathHelper.GetExtension(selectedFilePath), ".nda"))
            {
                if (Resource.GetResourceTypeStatic(selectedFilePath) == CEngine.ClassIDType.CLASS_MeshAssetDataResource)
                {
                    MenuItem menuItem = new MenuItem();
                    menuItem.SetText("Generate collsion");
                    menuItem.ClickedEvent += OnMenuItemGenerateCollision;
                    MenuContextMenu.AddMenuItem(menuItem);
                }
            }

            if (_bListViewContextMenu && CheckHasBack(GetSelectedPath()))
            {
                MenuContextMenu.AddMenuItem(MenuItem_ApplyBack);
            }

            GenerateReflectedProjectUIItems(MenuContextMenu);

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, MouseX, MouseY);
        }


        void GenerateReflectedProjectUIItems(Menu menu)
        {
            List<(string, Type)> projectMenuTypes = new List<(string, Type)>();
            foreach (var item in CrossEngine.GetInstance().ProjectMenuItems)
            {
                bool show = item.Value.ShowOrder > 0;

                if (item.Value.OnlyListViewShow)
                {
                    show = show && _bListViewContextMenu;
                }

                if (show)
                {
                    projectMenuTypes.Add((item.Value.DisplayUINames, item.Key));
                }
            }

            MenuItemUtils.CreateMenu(menu, menu.GetUIManager(), projectMenuTypes,
                delegate ((List<string>, Type) entry)
                {
                    var menuItem = (ProjectMenuItem)Activator.CreateInstance(entry.Item2);
                    if (menuItem.Show())
                    {
                        return menuItem;
                    }
                    else
                    { return null; }
                },
                delegate (Menu m, int level)
                {

                }
            );
        }

        string GetNewDirectoryName(string Path)
        {
            int i = 0;
            while (true)
            {
                string IndexString = i.ToString();
                if (i == 0)
                {
                    IndexString = "";
                }
                string NewDirectoryName = string.Format("NewFolder{0}", IndexString);
                string NewDirectory = Path + "/" + NewDirectoryName;
                if (DirectoryHelper.IsDirectoryExists(NewDirectory) == false)
                {
                    return NewDirectoryName;
                }
                i++;
            }
        }

        void OnMenuItemCreateFolderClicked(MenuItem MenuItem)
        {
            if (_bListViewContextMenu)
            {
                List<ListViewItem> SelectedListViewItems = _ListView.GetSelectedItems();
                if (SelectedListViewItems.Count == 1)
                {
                    ListViewItem SelectedListViewItem = _ListView.GetSelectedItem();
                    if (SelectedListViewItem != null && SelectedListViewItem.bIsDirectory)
                    {
                        OpenListViewItem(SelectedListViewItem);
                    }
                }
            }
            string Path = GetCurrentTargetDirectory();
            string NewDirectoryName = GetNewDirectoryName(Path);
            string NewDirectory = Path + "/" + NewDirectoryName;
            DirectoryHelper.CreateDirectory(NewDirectory);
            UpdateDirectory();
            bool bResetScroll = false;
            RefreshListView(bResetScroll);
            ListViewItem ListViewItem = _ListView.FindItemByPath(NewDirectory);
            if (ListViewItem != null)
            {
                _ListView.SelectItem(ListViewItem);
                _ListView.StartRename(ListViewItem);
            }
        }

        public string GetNewFileName(string Path, string Formatter)
        {
            int i = 0;
            while (true)
            {
                string IndexString = i.ToString();
                if (i == 0)
                {
                    IndexString = "";
                }
                string NewFileName = string.Format(Formatter, IndexString);
                string NewFilename = Path + "/" + NewFileName;
                if (FileHelper.IsFileExists(NewFilename) == false)
                {
                    return NewFileName;
                }
                i++;
            }
        }

        string GetFilenameToCreate(string Formatter)
        {
            if (_bListViewContextMenu)
            {
                List<ListViewItem> SelectedListViewItems = _ListView.GetSelectedItems();
                if (SelectedListViewItems.Count == 1)
                {
                    ListViewItem SelectedListViewItem = _ListView.GetSelectedItem();
                    if (SelectedListViewItem != null && SelectedListViewItem.bIsDirectory)
                    {
                        OpenListViewItem(SelectedListViewItem);
                    }
                }
            }
            TreeItem SelectedTreeItem = _Tree.GetSelectedItem();
            string Path = GetCurrentTargetDirectory();
            string SelectionPath = SelectedTreeItem.GetPath();
            string NewFileName = GetNewFileName(Path, Formatter);
            string NewFilename = Path + "/" + NewFileName;
            return NewFilename;
        }

        string GetDirectoryToCreate()
        {
            if (_bListViewContextMenu)
            {
                List<ListViewItem> SelectedListViewItems = _ListView.GetSelectedItems();
                if (SelectedListViewItems.Count == 1)
                {
                    ListViewItem SelectedListViewItem = _ListView.GetSelectedItem();
                    if (SelectedListViewItem != null && SelectedListViewItem.bIsDirectory)
                    {
                        OpenListViewItem(SelectedListViewItem);
                    }
                }
            }
            string Path = GetCurrentTargetDirectory();
            return Path;
        }

        void DoCreateFile(string Formatter, string TemplateFilename = null, bool bScript = false)
        {
            string NewFilename = GetFilenameToCreate(Formatter);
            if (TemplateFilename != null)
            {
                if (bScript)
                {
                    string ClassName = PathHelper.GetFileName(NewFilename);
                    ClassName = PathHelper.GetNameOfPath(ClassName);
                    string TemplateText = FileHelper.ReadTextFile(TemplateFilename);
                    TemplateText = TemplateText.Replace("CustumScriptClassName", ClassName);
                    FileHelper.WriteTextFile(NewFilename, TemplateText, TextFileFormat.UTF8);
                }
                else
                {
                    FileHelper.CopyFile(TemplateFilename, NewFilename);
                }
            }
            else
            {
                string TemplateText = "";
                FileHelper.WriteTextFile(NewFilename, TemplateText, TextFileFormat.UTF8);
            }
            bool bResetScroll = false;
            RefreshListView(bResetScroll);
            ListViewItem ListViewItem = _ListView.FindItemByPath(NewFilename);
            if (ListViewItem != null)
            {
                _ListView.SelectItem(ListViewItem);
                _ListView.StartRename(ListViewItem);
            }
        }

        void OnMenuItemCreateVisualScriptClicked(MenuItem MenuItem)
        {
            DoCreateFile("NewClass{0}.vs");
        }

        void OnMenuItemCreateCSharpScriptClicked(MenuItem MenuItem)
        {
            DoCreateFile("NewClass{0}.cs", null, true);
        }

        void OnMenuItemCreateJSScriptClicked(MenuItem MenuItem)
        {
            string TemplateFilename = JavascriptTemplateFilename;
            DoCreateFile("NewClass{0}.js", TemplateFilename, true);
        }

        void OnMenuItemCreateLuaScriptClicked(MenuItem MenuItem)
        {
            string TemplateFilename = LuaTemplateFilename;
            DoCreateFile("NewClass{0}.lua", TemplateFilename, true);
        }

        void OnMenuItemCreateTypeScriptClicked(MenuItem MenuItem)
        {
            string TemplateFilename = TypescriptTemplateFilename;
            DoCreateFile("NewClass{0}.mts", TemplateFilename, true);
        }

        void OnMenuItemCreateAnimatorClicked(MenuItem MenuItem)
        {
            string NewFilename = GetFilenameToCreate("NewAnimator{0}.nda");
            string tempFilePath = PathHelper.CombinePath(EditorUtilities.GetResourceDirectory(), AnimatorTemplateFilename);
            DoCopyFile(tempFilePath, NewFilename);
            RefreshListView(false);
            ListViewItem ListViewItem = _ListView.FindItemByPath(NewFilename);
            if (ListViewItem != null)
            {
                _ListView.SelectItem(ListViewItem);
                _ListView.StartRename(ListViewItem);
            }
        }

        void OnMenuItemCreateCompositeClicked(MenuItem MenuItem)
        {
            DoCreateFile("NewComposite{0}.nda", CompositeTemplateFilename);
        }

        void OnMenuItemCreateMotionMatchData(MenuItem menuItem)
        {
            string sequence_paths = "";

            ListViewItem SelectedListViewItems = _ListView.GetSelectedItem();
            if (SelectedListViewItems != null)
            {
                //foreach(ListViewItem item in SelectedListViewItems)
                ListViewItem item = SelectedListViewItems;
                {
                    var path = EditorUtilities.EditorFilenameToStandardFilename(item.Path);
                    var extension = PathHelper.GetExtension(path);
                    if (StringHelper.IgnoreCaseEqual(extension, ".nda"))
                    {
                        CEngine.ClassIDType object_class = Resource.GetResourceTypeStatic(path);
                        if (object_class == CEngine.ClassIDType.CLASS_AnimSequenceRes)
                        {
                            sequence_paths = path;
                        }

                    }
                }
            }

            CreateMotionMatchUI create_motion_match_ui = new CreateMotionMatchUI();
            create_motion_match_ui.Initialize(GetUIManager(), "Create Motion Match Data", sequence_paths);
            create_motion_match_ui.InputedEvent += (CreateMotionMatchUI sender1, string sequences) =>
            {
                string newFileName = GetFilenameToCreate("NewMotionMatch{0}.nda");
                var motionmatch_resource = Clicross.AnimationEditorUtil.Animation_CreateMotionMatchData(sequences);

                Clicross.ResourceUtil.ResourceSaveToFile(motionmatch_resource, newFileName);
                motionmatch_resource.Dispose();
                bool bResetScroll = false;
                RefreshListView(bResetScroll);
                ListViewItem ListViewItem = _ListView.FindItemByPath(newFileName);
                if (ListViewItem != null)
                {
                    _ListView.SelectItem(ListViewItem);
                    _ListView.StartRename(ListViewItem);
                }
                OnListViewSelectionChanged(_ListView);

            };

            DialogUIManager.GetInstance().ShowDialogUI(create_motion_match_ui);
        }

        void OnMenuItemCreateSceneClicked(MenuItem MenuItem)
        {
            string TemplateFilename = EditorScene.GetInstance().GetTemplateSceneFilename();
            string TemplateFilename1 = EditorUtilities.StandardFilenameToEditorFilename(TemplateFilename);
            DoCreateFile("NewScene{0}.world", TemplateFilename1);
        }

        public void ImportTerrain(string TerrainFilename, string TerrainNdaFilename)
        {
            string jsonStr = File.ReadAllText(TerrainFilename);
            CEAssetPipeline.gTerrainImportSetting.DeserializeFromString(jsonStr);
            AssetImporterManager.Instance().ImportAsset(TerrainFilename, TerrainNdaFilename);
        }

        void OnMenuItemCreateShaderClicked(MenuItem MenuItem)
        {
            DoCreateFile("NewShader{0}.shader");
        }

        void OnMenuItemCreateShaderModuleClicked(MenuItem MenuItem)
        {
            DoCreateFile("NewShaderModule{0}.hlsl");
        }

        void OnMenuItemCreateMaterialClicked(MenuItem MenuItem)
        {
            ListViewItem SelectedListViewItem = _ListView.GetSelectedItem();

            CreateMaterialUI CreateMaterialUI = new CreateMaterialUI();

            if (SelectedListViewItem != null && SelectedListViewItem.bIsDirectory == false)
            {
                string FilePath = SelectedListViewItem.Path;
                FilePath = EditorUtilities.EditorFilenameToStandardFilename(FilePath);
                string Extension = PathHelper.GetExtension(FilePath);
                if (StringHelper.IgnoreCaseEqual(Extension, ".nda"))
                {
                    AssetType AssetType = AssetImporterManager.Instance().GetAssetType(FilePath);
                    if (AssetType == AssetType.Default)
                    {
                        CEngine.ClassIDType ObjectClassID1 = Resource.GetResourceTypeStatic(FilePath);
                        if (ObjectClassID1 == CEngine.ClassIDType.CLASS_Fx)
                        {
                            CreateMaterialUI.InitializeFromFx(GetUIManager(), "Create Material From Fx", FilePath);
                        }
                        else if (ObjectClassID1 == CEngine.ClassIDType.CLASS_Material)
                        {
                            Material Material = (Material)Resource.Get(FilePath);
                            if (Material != null)
                            {
                                CreateMaterialUI.InitializeFromMaterial(GetUIManager(), "Create Material from Material", FilePath);
                            }
                        }
                        else if (ObjectClassID1 == CEngine.ClassIDType.CLASS_Texture2D)
                        {
                            CreateMaterialUI.InitializeFromImage(GetUIManager(), "Create Material from Image with Default Fx", FilePath);
                        }
                    }
                }
            }
            else
            {
                var ShaderPath = CrossEngineApi.GetSettingManager().GetRenderPipelineSettingForEditor().DefaultFX;
                CreateMaterialUI.InitializeFromFx(GetUIManager(), "Create Material From Fx", ShaderPath);
            }

            CreateMaterialUI.InputedEvent += (CreateMaterialUI Sender1, string Shader) =>
            {

                string NewFilename = Material.CreateAndSave(GetUIManager(), Shader, Sender1.GetDefaultImage(), GetDirectoryToCreate());

                bool bResetScroll = false;
                RefreshListView(bResetScroll);
                ListViewItem ListViewItem = _ListView.FindItemByPath(NewFilename);
                if (ListViewItem != null)
                {
                    _ListView.SelectItem(ListViewItem);
                    _ListView.StartRename(ListViewItem);
                }
                OnListViewSelectionChanged(_ListView);
            };
            DialogUIManager.GetInstance().ShowDialogUI(CreateMaterialUI);
        }

        void OnMenuItemCreateMaterialFunctionClicked(MenuItem MenuItem)
        {
            string NewFilename = GetFilenameToCreate("NewMaterialFunction{0}.materialfunction");
            MaterialFunction.CreateResource(NewFilename);
            RefreshListView(false);
            ListViewItem ListViewItem = _ListView.FindItemByPath(NewFilename);
            if (ListViewItem != null)
            {
                _ListView.SelectItem(ListViewItem);
                _ListView.StartRename(ListViewItem);
            }
        }

        void OnMenuItemCreateDataAssetClicked(MenuItem MenuItem)
        {
            ListViewItem SelectedListViewItem = _ListView.GetSelectedItem();

            CreateDataAssetUI CreateDataAssetUI = new CreateDataAssetUI();

            CEngine.ClassIDType baseClassIdType = CEngine.ClassIDType.CLASS_NullType;
            string FilePath = null;

            if (SelectedListViewItem != null && SelectedListViewItem.bIsDirectory == false)
            {
                FilePath = SelectedListViewItem.Path;
                FilePath = EditorUtilities.EditorFilenameToStandardFilename(FilePath);
                baseClassIdType = Resource.GetResourceTypeStatic(FilePath);
                string Extension = PathHelper.GetExtension(FilePath);
                if (StringHelper.IgnoreCaseEqual(Extension, ".flow"))
                {
                    CreateDataAssetUI.InitializeFromWorkflow(GetUIManager(), "Create Data Asset From Workflow", FilePath);
                }

                CreateDataAssetUI.InputedEvent += (CreateDataAssetUI Sender1) =>
                {
                    //string NewFilename = Material.CreateAndSave(GetUIManager(), Shader, Sender1.GetDefaultImage(), GetDirectoryToCreate());

                    string path = GetDirectoryToCreate();
                    string newFileName = path + "/" + GetNewFileName(path, "NewDataAsset{0}.nda");
                    DataAssetEditorContext.CreateAndSave(newFileName, FilePath, (int)baseClassIdType);

                    bool bResetScroll = false;
                    RefreshListView(bResetScroll);
                    ListViewItem ListViewItem = _ListView.FindItemByPath(newFileName);
                    if (ListViewItem != null)
                    {
                        _ListView.SelectItem(ListViewItem);
                        _ListView.StartRename(ListViewItem);
                    }
                    OnListViewSelectionChanged(_ListView);
                };

                DialogUIManager.GetInstance().ShowDialogUI(CreateDataAssetUI);
            }
        }

        void OnMenuItemCreateWorkflowEditorClicked(MenuItem MenuItem)
        {

            StringListSelectDialog classSelectDialog = new StringListSelectDialog();
            var tempClassList = new List<string>();
            tempClassList = WorkflowEditorUIManager.Instance.ClassNames;
            classSelectDialog.Initialize(GetUIManager(), "Select A Class To Create WorkflowGraph", tempClassList);
            classSelectDialog.StringSelectEvent += (StringListSelectDialog Sender1, string className) =>
            {
                if (className.Length == 0)
                {
                    return;
                }
                string NewFilename = WorkflowEditorUIManager.CreateAndSave(GetUIManager(), className, GetDirectoryToCreate());
                bool bResetScroll = false;
                RefreshListView(bResetScroll);
                ListViewItem ListViewItem = _ListView.FindItemByPath(NewFilename);
                if (ListViewItem != null)
                {
                    _ListView.SelectItem(ListViewItem);
                    _ListView.StartRename(ListViewItem);
                }
                OnListViewSelectionChanged(_ListView);
            };
            DialogUIManager.GetInstance().ShowDialogUI(classSelectDialog);
        }

        void OnMenuItemCreateInputActionMappingClicked(MenuItem MenuItem)
        {
            string path = GetDirectoryToCreate();
            string NewFileName = path + "/" + GetNewFileName(path, "NewInputActionMapping{0}.iam");
            InputActionMappingContext.CreateAndSave(NewFileName);
            bool bResetScroll = false;
            RefreshListView(bResetScroll);
            ListViewItem ListViewItem = _ListView.FindItemByPath(NewFileName);
            if (ListViewItem != null)
            {
                _ListView.SelectItem(ListViewItem);
                _ListView.StartRename(ListViewItem);
            }
            OnListViewSelectionChanged(_ListView);
        }

        void OnMenuItemCreateMPCClicked(MenuItem MenuItem)
        {
            string NewFilename = GetFilenameToCreate("NewParameterCollection{0}.mpc");
            MaterialParameterCollection.CreateResource(NewFilename);
            RefreshListView(false);
            ListViewItem ListViewItem = _ListView.FindItemByPath(NewFilename);
            if (ListViewItem != null)
            {
                _ListView.SelectItem(ListViewItem);
                _ListView.StartRename(ListViewItem);
            }
        }

        void OnMenuItemCreateFxClicked(MenuItem MenuItem)
        {
            string NewFilename = GetFilenameToCreate("NewFx{0}.nda");
            Fx.Create(NewFilename);
            bool bResetScroll = false;
            RefreshListView(bResetScroll);
            ListViewItem ListViewItem = _ListView.FindItemByPath(NewFilename);
            if (ListViewItem != null)
            {
                _ListView.SelectItem(ListViewItem);
                _ListView.StartRename(ListViewItem);
            }
            OnListViewSelectionChanged(_ListView);
        }

        void OnMenuItemCreateFxNewClicked(MenuItem MenuItem)
        {
            string NewFilename = GetFilenameToCreate("NewFx{0}.nda");
            Fx.Create_New(NewFilename);
            bool bResetScroll = false;
            RefreshListView(bResetScroll);
            ListViewItem ListViewItem = _ListView.FindItemByPath(NewFilename);
            if (ListViewItem != null)
            {
                _ListView.SelectItem(ListViewItem);
                _ListView.StartRename(ListViewItem);
            }
            OnListViewSelectionChanged(_ListView);
        }

        void OnMenuItemCreateCurveClicked(MenuItem Sender)
        {
            DoCreateFile("NewCurve{0}.cur");
        }
        void OnMenuItemCreateCurveControllerClicked(MenuItem Sender)
        {
            string NewFilename = GetFilenameToCreate("NewCurveController{0}.nda");

            var CurveControllerRes = Clicross.ControllableUnitSystemG.EditorCreateCurveControllerRes(EditorScene.GetInstance().GetWorld()._WorldInterface);
            Clicross.ResourceUtil.ResourceSaveToFile(CurveControllerRes, NewFilename);
            CurveControllerRes.Dispose();

            bool bResetScroll = false;
            RefreshListView(bResetScroll);
            ListViewItem ListViewItem = _ListView.FindItemByPath(NewFilename);
            if (ListViewItem != null)
            {
                _ListView.SelectItem(ListViewItem);
                _ListView.StartRename(ListViewItem);
            }
        }

        void OnMenuItemShowInExplorerClicked(MenuItem MenuItem)
        {
            List<string> SelectedPathList = GetSelectedPathList();
            foreach (string Path in SelectedPathList)
            {
                ProcessHelper.OpenContainingFolder(Path);
            }
        }

        void OnMenuItemShowInFolderViewClicked(MenuItem MenuItem)
        {
            if (_bSearchMode)
            {
                DoCancelSearch();
            }

            if (IsClassifyMode())
            {
                DoCancelClassify();
            }
        }

        void OnMenuItemOpenClicked(MenuItem MenuItem)
        {
            if (_bListViewContextMenu)
            {
                List<ListViewItem> SelectedListViewItems = _ListView.GetSelectedItems();
                if (SelectedListViewItems.Count == 1)
                {
                    ListViewItem Selection = _ListView.GetSelectedItem();
                    if (Selection != null)
                    {
                        OpenListViewItem(Selection);
                    }
                }
                else
                {
                    foreach (ListViewItem ListViewItem in SelectedListViewItems)
                    {
                        if (ListViewItem.bIsDirectory == false)
                        {
                            OpenListViewItem(ListViewItem);
                        }
                    }
                }
            }
        }

        void OnMenuItemOpenInMateiralEditor(MenuItem MenuItem)
        {
            if (_bListViewContextMenu)
            {
                List<ListViewItem> SelectedListViewItems = _ListView.GetSelectedItems();
                if (SelectedListViewItems.Count == 1)
                {
                    ListViewItem Selection = _ListView.GetSelectedItem();
                    if (Selection != null)
                    {
                        string FilePath = Selection.Path;
                        string Extension = PathHelper.GetExtension(FilePath);
                        if (StringHelper.IgnoreCaseEqual(Extension, ".nda"))
                        {
                            AssetType AssetType = AssetImporterManager.Instance().GetAssetType(FilePath);
                            if (AssetType == AssetType.Default)
                            {
                                string FilePath1 = EditorUtilities.EditorFilenameToStandardFilename(FilePath);
                                CEngine.ClassIDType ObjectClassID = Resource.GetResourceTypeStatic(FilePath1);
                                if (ObjectClassID == CEngine.ClassIDType.CLASS_Fx || ObjectClassID == CEngine.ClassIDType.CLASS_Material || ObjectClassID == CEngine.ClassIDType.CLASS_MaterialFunction)
                                {
                                    MaterialEditorUIManager.Instance.OpenMaterial(FilePath, ObjectClassID);
                                }
                            }
                        }
                        else if (StringHelper.IgnoreCaseEqual(Extension, ".materialfunction"))
                        {
                            MaterialEditorUIManager.Instance.OpenMaterial(FilePath, CEngine.ClassIDType.CLASS_MaterialFunction);
                        }
                    }
                }
            }
        }

        void OnMenuItemOpenInWorkflowEditor(MenuItem MenuItem)
        {
            if (_bListViewContextMenu)
            {
                List<ListViewItem> SelectedListViewItems = _ListView.GetSelectedItems();
                if (SelectedListViewItems.Count == 1)
                {
                    ListViewItem Selection = _ListView.GetSelectedItem();
                    if (Selection != null)
                    {
                        string FilePath = Selection.Path;
                        string Extension = PathHelper.GetExtension(FilePath);
                        if (StringHelper.IgnoreCaseEqual(Extension, ".flow"))
                        {
                            string FilePath1 = EditorUtilities.EditorFilenameToStandardFilename(FilePath);
                            CEngine.ClassIDType ObjectClassID = Resource.GetResourceTypeStatic(FilePath1);
                            if (ObjectClassID == CEngine.ClassIDType.CLASS_WorkflowGraphResource)
                            {
                                WorkflowEditorUIManager.Instance.OpenWorkflow(FilePath, ObjectClassID);
                            }
                        }
                    }
                }
            }
        }
        void OnMenuItemCreateParticleSystemClicked(MenuItem MenuItem)
        {
            string NewFilename = GetFilenameToCreate("NewParticleSystem{0}.system");
            ParticleSystemResource.CreateResource(NewFilename);
            RefreshListView(false);
            ListViewItem ListViewItem = _ListView.FindItemByPath(NewFilename);
            if (ListViewItem != null)
            {
                _ListView.SelectItem(ListViewItem);
                _ListView.StartRename(ListViewItem);
            }
        }

        void OnMenuItemCreateInstanceDataResourceClicked(MenuItem MenuItem)
        {
            string NewFilename = GetFilenameToCreate("NewInstanceDataResource{0}.nda");
            InstanceDataResource.CreateResource(NewFilename);
            RefreshListView(false);
            ListViewItem ListViewItem = _ListView.FindItemByPath(NewFilename);
            if (ListViewItem != null)
            {
                _ListView.SelectItem(ListViewItem);
                _ListView.StartRename(ListViewItem);
            }
        }

        void OnMenuItemCreateParticleEmitterClicked(MenuItem MenuItem)
        {
            string NewFilename = GetFilenameToCreate("NewParticleEmitter{0}.emitter");
            ParticleEmitterResource.CreateResource(NewFilename);
            RefreshListView(false);
            ListViewItem ListViewItem = _ListView.FindItemByPath(NewFilename);
            if (ListViewItem != null)
            {
                _ListView.SelectItem(ListViewItem);
                _ListView.StartRename(ListViewItem);
            }
        }
        void OnMenuItemCreateRenderTextureClicked(MenuItem MenuItem)
        {
            string NewFilename = GetFilenameToCreate("NewRenderTexture{0}.renderTexture");
            Clicross.resource.RenderTextureResource.RenderTexture_CreateRenderTexture(NewFilename);
            RefreshListView(false);
            ListViewItem ListViewItem = _ListView.FindItemByPath(NewFilename);
            if (ListViewItem != null)
            {
                _ListView.SelectItem(ListViewItem);
                _ListView.StartRename(ListViewItem);
            }
        }

        void OnMenuItemCreatePrefabClicked(MenuItem MenuItem)
        {
            string NewFilename = GetFilenameToCreate("NewPrefab{0}.prefab");
            string tempFilePath = PathHelper.CombinePath(EditorUtilities.GetResourceDirectory(), "EngineResource/Model/PrefabTemplate.prefab");
            DoCopyFile(tempFilePath, NewFilename);
            RefreshListView(false);
            ListViewItem ListViewItem = _ListView.FindItemByPath(NewFilename);
            if (ListViewItem != null)
            {
                _ListView.SelectItem(ListViewItem);
                _ListView.StartRename(ListViewItem);
            }
        }


        public string GetSelectedListViewPath()
        {
            ListViewItem SelectedListViewItem = _ListView.GetSelectedItem();
            if (SelectedListViewItem != null)
            {
                return SelectedListViewItem.Path;
            }
            return "";
        }

        public bool CheckHasBack(string path)
        {
            string backFile = EditorUtilities.EditorFilenameToBackFilename(path);
            return FileHelper.IsFileExists(backFile);
        }

        string GetSelectedPath()
        {
            if (_bListViewContextMenu)
            {
                List<ListViewItem> SelectedListViewItems = _ListView.GetSelectedItems();
                int Count = SelectedListViewItems.Count;
                if (Count == 0)
                {
                    TreeItem SelectedTreeItem = _Tree.GetSelectedItem();
                    if (SelectedTreeItem != null)
                    {
                        return SelectedTreeItem.GetTagString();
                    }
                    else
                    {
                        return "";
                    }
                }
                ListViewItem SelectedListViewItem = _ListView.GetSelectedItem();
                if (SelectedListViewItem != null)
                {
                    return SelectedListViewItem.Path;
                }
                else
                {
                    return "";
                }
            }
            else
            {
                TreeItem SelectedTreeItem = _Tree.GetSelectedItem();
                if (SelectedTreeItem != null)
                {
                    return SelectedTreeItem.GetTagString();
                }
                else
                {
                    return "";
                }
            }
        }

        public List<string> GetSelectedPathList()
        {
            List<string> SelectedPathList = new List<string>();
            if (_bListViewContextMenu)
            {
                List<ListViewItem> SelectedListViewItems = _ListView.GetSelectedItems();
                int Count = SelectedListViewItems.Count;
                if (Count == 0)
                {
                    TreeItem SelectedTreeItem = _Tree.GetSelectedItem();
                    if (SelectedTreeItem != null)
                    {
                        SelectedPathList.Add(SelectedTreeItem.GetTagString());
                    }
                }
                for (int i = 0; i < Count; i++)
                {
                    ListViewItem SelectedListViewItem = _ListView.GetSelectedItem(i);
                    if (SelectedListViewItem != null)
                    {
                        SelectedPathList.Add(SelectedListViewItem.Path);
                    }
                }
            }
            else
            {
                TreeItem SelectedTreeItem = _Tree.GetSelectedItem();
                if (SelectedTreeItem != null)
                {
                    SelectedPathList.Add(SelectedTreeItem.GetTagString());
                }
            }
            return SelectedPathList;
        }

        public List<ListViewItem> GetSelectedItemList()
        {
            return _ListView.GetSelectedItems();
        }

        void DoDelete_ListView()
        {
            _bListViewContextMenu = true;
            List<string> SelectedPathList = GetSelectedPathList();
            int Count = SelectedPathList.Count;
            StringBuilder StringBuilder = new StringBuilder();
            for (int i = 0; i < Count; i++)
            {
                string Path = SelectedPathList[i];
                string Name = PathHelper.GetFileName(Path);
                StringBuilder.Append(Name);
                if (i == Count - 2)
                {
                    StringBuilder.Append(" and ");
                }
                else if (i != Count - 1)
                {
                    StringBuilder.Append(", ");
                }
            }
            string FileNameList = StringBuilder.ToString();
            string Question = string.Format("Do you want to delete {0}?", FileNameList);
            CommonDialogUI CommonDialogUI = new CommonDialogUI();
            CommonDialogUI.Initialize(GetUIManager(), "Tips", Question, CommonDialogType.OKCancel);
            CommonDialogUI.CloseEvent += (CommonDialogUI Sender, CommonDialogResult Result) =>
            {
                if (Result == CommonDialogResult.OK)
                {
                    foreach (string Path in SelectedPathList)
                    {
                        if (DirectoryHelper.IsDirectoryExists(Path))
                        {
                            DoDeleteDirectory(Path);
                        }
                        else if (FileHelper.IsFileExists(Path))
                        {
                            DoDeleteFile(Path);
                        }
                    }
                    UpdateDirectory();
                    bool bResetScroll = false;
                    RefreshListView(bResetScroll);
                }
            };
            DialogUIManager.GetInstance().ShowDialogUI(CommonDialogUI);
        }

        void DoDelete_Tree()
        {
            _bListViewContextMenu = false;
            if (_Tree.GetSelectedItem() == _Tree.GetRootItem())
            {
                return;
            }
            string Path = GetSelectedPath();
            if (Path == "")
            {
                return;
            }
            string DirectoryName = PathHelper.GetFileName(Path);
            string Question = string.Format("Do you want to delete folder: {0}?", DirectoryName);
            CommonDialogUI CommonDialogUI = new CommonDialogUI();
            CommonDialogUI.Initialize(GetUIManager(), "Tips", Question, CommonDialogType.OKCancel);
            CommonDialogUI.CloseEvent += (CommonDialogUI Sender, CommonDialogResult Result) =>
            {
                if (Result == CommonDialogResult.OK)
                {
                    TreeItem NextVisibileNonChild = _Tree.FindNextVisibleNonChild();
                    if (NextVisibileNonChild != null)
                    {
                        _Tree.SelectItem(NextVisibileNonChild);
                    }
                    DoDeleteDirectory(Path);
                    UpdateDirectory();
                }
            };
            DialogUIManager.GetInstance().ShowDialogUI(CommonDialogUI);
        }

        void OnMenuItemDeleteClicked(MenuItem MenuItem)
        {
            if (_bListViewContextMenu)
            {
                DoDelete_ListView();
            }
            else
            {
                DoDelete_Tree();
            }
        }

        void OnMenuItemDuplicateClicked(MenuItem MenuItem)
        {
            DuplicateSelectedFiles();
        }

        public void DuplicateSelectedFiles()
        {
            _bListViewContextMenu = true;
            string TargetDirectory = GetCurrentTargetDirectory();
            List<string> SelectedPathList = GetSelectedPathList();
            int Count = SelectedPathList.Count;
            for (int i = 0; i < Count; i++)
            {
                string FilePath = SelectedPathList[i];
                bool bMove = false;
                if (FileHelper.IsFileExists(FilePath))
                {
                    DoCopyOrMoveFile(TargetDirectory, FilePath, bMove);
                }
            }
            UpdateAll();
        }

        void OnMenuItemCopyRelativePathClicked(MenuItem MenuItem)
        {
            List<string> SelectedPathList = GetSelectedPathList();
            int Count = SelectedPathList.Count;
            StringBuilder StringBuilder = new StringBuilder();
            for (int i = 0; i < Count; i++)
            {
                string Path = SelectedPathList[i];
                string StandardFilename = EditorUtilities.EditorFilenameToStandardFilename(Path);
                StringBuilder.Append(StandardFilename);
                if (i != Count - 1)
                {
                    StringBuilder.Append('\n');
                }
            }
            GetDevice().SetClipboardText(StringBuilder.ToString());
        }

        void OnMenuItemCopyAbsolutePathClicked(MenuItem MenuItem)
        {
            List<string> SelectedPathList = GetSelectedPathList();
            int Count = SelectedPathList.Count;
            StringBuilder StringBuilder = new StringBuilder();
            for (int i = 0; i < Count; i++)
            {
                string Path = SelectedPathList[i];
                StringBuilder.Append(Path);
                if (i != Count - 1)
                {
                    StringBuilder.Append('\n');
                }
            }
            GetDevice().SetClipboardText(StringBuilder.ToString());
        }

        void DoStartRename_ListView()
        {
            List<ListViewItem> SelectedListViewItems = _ListView.GetSelectedItems();
            if (SelectedListViewItems.Count == 1)
            {
                ListViewItem SelectedListViewItem = _ListView.GetSelectedItem();
                if (SelectedListViewItem != null)
                {
                    _ListView.StartRename(SelectedListViewItem);
                }
            }
        }

        void DoStartRename_Tree()
        {
            List<TreeItem> SelectionList = _Tree.GetSelectedItems();
            if (SelectionList.Count == 1)
            {
                TreeItem Selection = SelectionList[0];
                if (Selection != _Tree.GetRootItem())
                {
                    _Tree.StartRename(Selection);
                }
            }
        }

        void DoStartRename()
        {
            if (_bListViewContextMenu)
            {
                DoStartRename_ListView();
            }
            else
            {
                DoStartRename_Tree();
            }
        }

        void OnMenuItemRenameClicked(MenuItem Sender)
        {
            DoStartRename();
        }


        public async Task<AssetImportResult> DoAssetImport(string TargetDirectory, string Path)
        {
            string AssetPath = Path;
            AssetImportResult Result = null;
            AssetType AssetType = AssetImporterManager.Instance().GetAssetType(AssetPath);
            if (AssetType != AssetType.Unknown && AssetType != AssetType.Default)
            {
                foreach (var item in mImporters)
                {
                    item.SetImportSetting();
                }

                string FileName = PathHelper.GetNameOfPath(Path);
                if (AssetType == AssetType.ComputeShader || AssetType == AssetType.Shader || AssetType == AssetType.RayTracingShader)
                {
                    FileName = PathHelper.GetFileName(Path); // keep extension
                }

                string NDAPath = TargetDirectory + "/" + FileName + ".nda";

                string Information = string.Format("Importing {0} ...", Path);
                FrameUI FrameUI = GetFrameUI();
                string SavedStatusBarText1 = FrameUI.GetStatusBarText1();
                Color SavedStatusBarText1Color = FrameUI.GetStatusBarText1Color();
                FrameUI.SetStatusBarText1(Information);
                FrameUI.SetStatusBarText1Color(new Color(1.0f, 1.0f, 1.0f, 1.0f));
                Result = await SceneRuntime.ImportAssetAsync(AssetPath, NDAPath);

                FrameUI.SetStatusBarText1(SavedStatusBarText1);
                FrameUI.SetStatusBarText1Color(SavedStatusBarText1Color);

                if (Result.bSuccess)
                {
                    if (AssetType == AssetType.Shader)
                    {
                        EditorGeneralCallBack ShaderReloader = () =>
                        {
                            Fx.TryRefreshRelatedUI(NDAPath);
                            Material.TryRefreshRelatedUI(NDAPath);
                            ShaderReloaders.Dequeue();
                        };
                        ShaderReloaders.Enqueue(ShaderReloader);
                        ResourceManager.Instance().TryReloadResource(NDAPath, ShaderReloader);
                    }
                    else
                    {
                        ResourceManager.Instance().TryReloadResource(NDAPath);
                    }
                }
            }
            return Result;
        }

        void OnMenuItemFoldAllClicked(MenuItem Sender)
        {
            TreeItem SelectedItem = _Tree.GetSelectedItem();
            if (SelectedItem != null)
            {
                SelectedItem.SetExpanded_Recursively(false);
            }
            else
            {
                _Tree.GetRootItem().SetExpanded_Recursively(false);
            }
        }

        void OnMenuItemExpandAllClicked(MenuItem Sender)
        {
            TreeItem SelectedItem = _Tree.GetSelectedItem();
            if (SelectedItem != null)
            {
                SelectedItem.SetExpanded_Recursively(true);
            }
            else
            {
                _Tree.GetRootItem().SetExpanded_Recursively(true);
            }
        }

        void OnMenuItemInspectClicked(MenuItem MenuItem)
        {
            InspectSelectedItem();
        }

        void OnMenuItemImportClicked(MenuItem MenuItem)
        {
            List<string> SelectedPathList = GetSelectedPathList();
            ImporterDelegate SimpleDelegate = async (Importer importer) =>
            {
                string TargetDirectory = PathHelper.GetDirectoryName(SelectedPathList[0]);
                bool bImport = true;
                bool bCopy = false;
                bool bMove = false;
                await ImportFileList(TargetDirectory, bImport, bCopy, bMove, SelectedPathList, importer);
            };


            foreach (var Item in mImporters)
            {
                if (Item.AcceptPath(SelectedPathList))
                {
                    Item.ShowUI(SimpleDelegate, SelectedPathList);
                    return;
                }
            }
        }
        [DllImport("AssetPipeline", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.Cdecl)]
        [return: MarshalAs(UnmanagedType.I1)]
        public static extern bool AssetPipeline_ExportTexture(string target, string source);

        void OnMenuItemExportClicked(MenuItem MenuItem)
        {
            List<string> SelectedPathList = GetSelectedPathList();
            foreach (var Path in SelectedPathList)
            {
                CEngine.ClassIDType ObjectClassID = Resource.GetResourceTypeStatic(Path);
                if (ObjectClassID == CEngine.ClassIDType.CLASS_Texture2D)
                {
                    string AbsolutePath = EditorUtilities.StandardFilenameToEditorFilename(Path);
                    string TargetPath = AbsolutePath.Replace(".nda", "");
                    bool b1 = AssetPipeline_ExportTexture(TargetPath, Path);
                    if(b1 == false)
                    {
                        CommonDialogUI commonDialogUI = new CommonDialogUI();
                        commonDialogUI.Initialize(GetUIManager(), "Texture Export", "This Texture Has No Raw Data", CommonDialogType.OK);
                        DialogUIManager.GetInstance().ShowDialogUI(commonDialogUI);
                    }
                }
                else if (ObjectClassID == CEngine.ClassIDType.CLASS_MeshAssetDataResource)
                {
                    PathInputUIFilterItem PathInputUIFilterItem = new PathInputUIFilterItem();
                    PathInputUIFilterItem.Name = "FBX Files";
                    PathInputUIFilterItem.Extensions.Add("fbx");


                    string SelectedFilePath = "";
                    bool bContentsOnly = false;

                    PathInputUIEx PathInputUI = new PathInputUIEx();
                    string DefaultDrivePath = EditorUtilities.AddEditorDrives(PathInputUI, bContentsOnly);
                    //PathInputUI.AddDrive(ProjectName, ProjectDirectory);
                    PathInputUI.AddDefaultDrives();
                    PathInputUI.Initialize(GetUIManager(), "Save Fbx As", PathInputUIType.SaveFile, PathInputUIFilterItem, DefaultDrivePath);
                    PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
                    {
                        SelectedFilePath = PathInputed;
                    };
                    PathInputUI.DialogCloseEvent += (DialogUI DialogSender) =>
                    {
                        OperationQueue.GetInstance().AddOperation(() =>
                        {
                            if (SelectedFilePath != "")
                            {
                                StringBuilder TipString = new StringBuilder();
                                var thread = new System.Threading.Thread(
                                    () =>
                                    {
                                        bool flag = AssetExporterManager.Instance().ExportSelectModelsAsFBX(Path, SelectedFilePath);
                                        if (flag)
                                        {
                                            TipString.Append("Exporting is successful!");
                                        }
                                        else
                                        {
                                            TipString.Append("Nda is not a valid mesh asset,exporting is fail!");
                                        }
                                        OperationQueue.GetInstance().AddOperation(() =>
                                        {
                                            CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Fbx Export Info", TipString.ToString());
                                        });
                                    }
                                );
                                thread.Start();
                            }
                        });
                    };
                    DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
                }
            }
            UpdateAll();
        }

        public void OnMenuItemImportAssetClicked(MenuItem MenuItem)
        {
            PathInputUIFilterItem PathInputUIFilterItem = new PathInputUIFilterItem();
            PathInputUIFilterItem.Name = "All Files";
            PathInputUIFilterItem.Extensions = new List<string>();
            PathInputUIFilterItem.Extensions.Add("*");

            bool bContentsOnly = false;
            PathInputUIEx PathInputUI = new PathInputUIEx();
            string DefaultDrivePath = EditorUtilities.AddEditorDrives(PathInputUI, bContentsOnly);
            PathInputUI.AddDefaultDrives();
            PathInputUI.Initialize(GetUIManager(), "Import Asset", PathInputUIType.OpenFile, PathInputUIFilterItem, DefaultDrivePath);
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                List<string> PathList = new List<string>();
                PathList.Add(PathInputed);
                ImporterDelegate SimpleDelegate = async (Importer importer) =>
                {
                    string TargetDirectory = GetCurrentTargetDirectory();
                    bool bImport = true;
                    bool bCopy = false;
                    bool bMove = false;
                    await ImportFileList(TargetDirectory, bImport, bCopy, bMove, PathList, importer);
                };


                foreach (var Item in mImporters)
                {
                    if (Item.AcceptPath(PathList))
                    {
                        Item.ShowUI(SimpleDelegate, PathList);
                        return;
                    }
                }
            };
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }

        public void OnMenuItemImportAssetsClicked(MenuItem MenuItem)
        {
            bool bContentsOnly = false;
            PathInputUIEx PathInputUI = new PathInputUIEx();
            string DefaultDrivePath = EditorUtilities.AddEditorDrives(PathInputUI, bContentsOnly);
            PathInputUI.AddDefaultDrives();
            PathInputUI.Initialize(GetUIManager(), "Import Assets", PathInputUIType.OpenFolder, null, DefaultDrivePath);
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                List<string> PathList = new List<string>();
                PathList.Add(PathInputed);
                ImporterDelegate SimpleDelegate = async (Importer importer) =>
                {
                    string TargetDirectory = GetCurrentTargetDirectory();
                    bool bImport = true;
                    bool bCopy = false;
                    bool bMove = false;
                    await DoCopyOrMove(importer, TargetDirectory, PathInputed, bImport, bCopy, bMove);
                    UpdateAll();
                };

                foreach (var Item in mImporters)
                {
                    if (Item.AcceptPath(PathList))
                    {
                        Item.ShowUI(SimpleDelegate, PathList);
                        return;
                    }
                }
            };
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }

        private void GetShaderPathes(string ShaderDirectory, List<string> ShaderPathes)
        {
            if (ShaderDirectory.Contains("Deprecated") || ShaderDirectory.Contains(".history"))
                return;

            FileInfo[] FileInfos = DirectoryHelper.GetFilesInDirectory(ShaderDirectory);
            foreach (var FileInfo in FileInfos)
            {
                if (PathHelper.GetExtension(FileInfo.FullName) == ".shader" || PathHelper.GetExtension(FileInfo.FullName) == ".compute")
                {
                    ShaderPathes.Add(FileInfo.FullName.Replace("\\", "/"));
                }
            }

            DirectoryInfo[] DirectoryInfos = DirectoryHelper.GetSubDirectories(ShaderDirectory);
            foreach (var DirectoryInfo in DirectoryInfos)
            {
                GetShaderPathes(DirectoryInfo.FullName, ShaderPathes);
            }
        }

        public void GetFxPathes(string FxDirectory, List<string> FxPathes)
        {
            if (FxDirectory.Contains("Deprecated") || FxDirectory.Contains(".history"))
                return;

            FileInfo[] FileInfos = DirectoryHelper.GetFilesInDirectory(FxDirectory);
            foreach (var FileInfo in FileInfos)
            {
                if (PathHelper.GetExtension(FileInfo.FullName) == ".nda")
                {
                    var FxPath = FileInfo.FullName.Replace("\\", "/");

                    string Path1 = EditorUtilities.EditorFilenameToStandardFilename(FxPath);
                    CEngine.ClassIDType ObjectClassID = Resource.GetResourceTypeStatic(Path1);

                    if (ObjectClassID == CEngine.ClassIDType.CLASS_Fx)
                    {
                        FxPathes.Add(FxPath);
                    }
                }
            }

            DirectoryInfo[] DirectoryInfos = DirectoryHelper.GetSubDirectories(FxDirectory);
            foreach (var DirectoryInfo in DirectoryInfos)
            {
                GetFxPathes(DirectoryInfo.FullName, FxPathes);
            }
        }

        public async void OnMenuItemRecompileShadersClicked(string shaderDirectory)
        {

            List<string> ShaderPathes = new List<string>();
            GetShaderPathes(shaderDirectory, ShaderPathes);

            // get shader importer
            Importer shaderimporter = new ShaderImporter();

            // Import
            List<string> ShaderImportSuccessNames = new List<string>();
            List<string> ShaderImportFailNames = new List<string>();

            foreach (string ShaderPath in ShaderPathes)
            {
                AssetImportResult Result = await DoCopyOrMove(shaderimporter, PathHelper.GetDirectoryName(ShaderPath), ShaderPath, true, false, false);

                if (Result.bSuccess)
                {
                    ShaderImportSuccessNames.Push(ShaderPath);
                }
                else
                {
                    ShaderImportFailNames.Push(ShaderPath);
                }
            }

            // Log
            CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "FFS Shader Compile Result", string.Format("Shader Compile Success:{0}\n\nShader Compile Fail:{1}", ShaderImportSuccessNames.Count, ShaderImportFailNames.Count));

            StringBuilder LogString = new StringBuilder();
            LogString.AppendLine(string.Format("\n  Shader Compile Success:{0}", ShaderImportSuccessNames.Count));
            LogString.AppendLine(string.Format("\n  Shader Compile Fail:{0}", ShaderImportFailNames.Count));
            foreach (string ShaderImportFailName in ShaderImportFailNames)
                LogString.AppendLine("    " + ShaderImportFailName);
            EditorLogger.Log(LogMessageType.Error, LogString.ToString());
        }

        public async System.Threading.Tasks.Task OnMenuItemRegenerateAllFxClicked(List<string> fxDirectories)
        {
            List<string> FxPathes = new List<string>();
            foreach (var path in fxDirectories)
            {
                GetFxPathes(path, FxPathes);
            }

            List<string> RegeneratedFx = new List<string>();

            ProgressUI LoadingUI = new ProgressUI();
            Progress _Progress = new Progress("Regenerate all Fx", 1);
            _Progress.SetStep(0, "", 1);
            LoadingUI.Initialize(GetUIManager(), _Progress, false);
            DialogUIManager.GetInstance().ShowDialogUI(LoadingUI);

            Background.SuspendBackgroundThreads();
            {
                await System.Threading.Tasks.Task.Delay(1);


                foreach (string FxPath in FxPathes)
                {
                    if (MaterialEditorUIManager.Instance.RegenerateFx(FxPath))
                    {
                        RegeneratedFx.Add(FxPath);
                    }
                }
            }
            Background.ResumeBackgroundThreads();

            _Progress.Done();
            _Progress.Close();

            // Log
            StringBuilder LogString = new StringBuilder();
            LogString.AppendLine(string.Format("\n  Fx Generate Success:{0}", RegeneratedFx.Count));
            foreach (string fxName in RegeneratedFx)
                LogString.AppendLine("    " + fxName);

            CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Fx Regenerate Result", LogString.ToString());
        }

        public void OnMenuItemCookDirectoryClicked(MenuItem MenuItem)
        {
            TreeItem SelectedItem = _Tree.GetSelectedItem();
            if (SelectedItem == null) return;

            BuildProject.GetInstance().CookDirectoty(SelectedItem.GetTagString());
        }

        public void OnMenuItemCookFileClicked(MenuItem MenuItem)
        {
            ListViewItem SelectedItem = _ListView.GetSelectedItem();
            if (SelectedItem == null) return;

            BuildProject.GetInstance().CookFile(SelectedItem.Path);
        }

        public void OnMenuItemUnCookFileClicked(MenuItem MenuItem)
        {
            ListViewItem SelectedItem = _ListView.GetSelectedItem();
            if (SelectedItem == null) return;

            AssetCookerManager.Instance().UnCookAsset(SelectedItem.Path);
        }

        public void OnMenuItemAddCookRequireClicked(MenuItem MenuItem)
        {
            TreeItem SelectedItem = _Tree.GetSelectedItem();
            if (SelectedItem == null) return;

            BuildConfig config = EditorConfigManager.GetInstance().GetConfig<BuildConfig>();
            config.AssetRequireDirs.Add(SelectedItem.GetTagString());
        }

        public void OnMenuItemAddCookIgnoreClicked(MenuItem MenuItem)
        {
            TreeItem SelectedItem = _Tree.GetSelectedItem();
            if (SelectedItem == null) return;

            BuildConfig config = EditorConfigManager.GetInstance().GetConfig<BuildConfig>();
            config.AssetIgnoreDirs.Add(SelectedItem.GetTagString());
        }

        public void OnMenuItemCopyResourceClicked(MenuItem MenuItem)
        {
            PathInputUIEx PathInputUI = new PathInputUIEx();
            PathInputUIFilterItem PathInputUIFilterItem = new PathInputUIFilterItem();
            string MyDocumentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            PathInputUI.Initialize(GetUIManager(), "Copy To", PathInputUIType.OpenFolder, PathInputUIFilterItem, MyDocumentsPath);
            string SelectedFilePath = "";
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                SelectedFilePath = PathInputed;
            };
            PathInputUI.DialogCloseEvent += (DialogUI DialogSender) =>
            {
                if (SelectedFilePath != "")
                {
                    ProgressUI LoadingUI = new ProgressUI();
                    Progress Progress = new Progress("Migrate Progress ", 2);
                    LoadingUI.Initialize(GetUIManager(), Progress, false);
                    DialogUIManager.GetInstance().ShowDialogUI(LoadingUI);

                    List<string> SelectedPathList = GetSelectedPathList();
                    Progress.SetStep(0, "Analyze Dependencies", SelectedPathList.Count);
                    var thread = new System.Threading.Thread(() =>
                    {
                        vector_string outDepencies = new vector_string();
                        foreach (var value in SelectedPathList)
                        {
                            vector_string paths = new vector_string();
                            paths.Add(value);
                            if (value.EndsWith(".world"))
                            {
                                GetWorldBlocks(value, paths);
                            }
                            foreach (string path in paths)
                            {
                                string relPath = EditorUtilities.EditorFilenameToStandardFilename(path);
                                ResourceManager.Instance().GetResourceDependencies(relPath, outDepencies, true);
                                outDepencies.Add(relPath);
                            }
                            Progress.SetItem(1, "Done...");
                        }
                        Progress.SetStep(1, "Migrate Progress...", (int)outDepencies.Count);
                        HashSet<string> cacheDepencies = new HashSet<string>();
                        string projectDir = MainUI.GetInstance().GetProjectDirectory();
                        for (int i = 0; i < outDepencies.Count; i++)
                        {
                            string depPath = ResourceManager.Instance().ConvertGuidToPath(outDepencies[i]);
                            if (!cacheDepencies.Contains(depPath))
                            {
                                cacheDepencies.Add(depPath);
                                string srcPath = PathHelper.CombinePath(projectDir, depPath);
                                if (FileHelper.IsFileExists(srcPath))
                                {
                                    string desPath = PathHelper.CombinePath(SelectedFilePath, depPath);
                                    FileHelper.CopyFile(srcPath, desPath);
                                }
                            }
                            Progress.SetItem(i + 1, "Done...");
                        }
                        Progress.Done();
                        Progress.Close();
                    });
                    thread.Start();
                }
            };
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }

        public void OnMenuItemGenerateCollision(MenuItem MenuItem)
        {
            List<string> SelectedPathList = GetSelectedPathList();
            ImportUI CollisionGenerateUI = new ImportUI();
            CollisionGenerateUI.Initialize(GetUIManager(), "Generate Collision", "", _CollisionGenerateSetting);
            CollisionGenerateUI.InputedEvent += (ImportUI Sender1) =>
            {
                bool succ = true;
                foreach (string filePath in SelectedPathList)
                    succ &= AssetImporterManager.Instance().GenerateCollision(filePath, _CollisionGenerateSetting);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Generate collision", succ ? "Success!" : "Failed!");
            };
            DialogUIManager.GetInstance().ShowDialogUI(CollisionGenerateUI);
        }

        public void OnMenuItemZipResourceClicked(MenuItem MenuItem)
        {
            List<string> SelectedPathList = GetSelectedPathList();

            if (ResourceTypeCache.GetInstance().GetCacheState() == false)
            {
                CommonDialogUI CommonDialogUI = new CommonDialogUI();
                CommonDialogUI.Initialize(GetUIManager(), "Warning", "Engine is analysis resource, Try it later.", CommonDialogType.OK);
                DialogUIManager.GetInstance().ShowDialogUI(CommonDialogUI);
                return;
            }

            PathInputUIEx PathInputUI = new PathInputUIEx();
            PathInputUIFilterItem PathInputUIFilterItem = new PathInputUIFilterItem();
            string MyDocumentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            PathInputUI.Initialize(GetUIManager(), "Zip To", PathInputUIType.OpenFolder, PathInputUIFilterItem, MyDocumentsPath);
            string SelectedFilePath = "";
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                SelectedFilePath = PathInputed;
            };

            PathInputUI.DialogCloseEvent += (DialogUI DialogSender) =>
            {
                if (SelectedFilePath != "")
                {

                    foreach (string path in SelectedPathList)
                    {
                        if (FileHelper.IsFileExists(path))
                        {
                            string fileName = PathHelper.GetFileName(path);
                            fileName = PathHelper.GetNameOfPath(fileName);
                            ZipHelper.CompressFileZip(path, SelectedFilePath + "/" + fileName + ".zip");
                        }
                        else
                        {
                            string temp2 = PathHelper.GetNameOfPath(path);
                            ZipHelper.CompressDirectoryZip(path, SelectedFilePath + "/" + temp2 + ".zip");
                        }
                    }
                }
            };
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }


        public void OnReSaveAssetClicked(MenuItem MenuItem)
        {
            List<string> SelectedPathList = GetSelectedPathList();
            foreach (string path in SelectedPathList)
            {
                var resource = Resource.Get(path);
                if (resource.ClassID == CEngine.ClassIDType.CLASS_Material)
                {
                    resource.Save();
                }
            }
        }

        public void OnMenuItemUseBackClicked(MenuItem MenuIten)
        {
            string path = GetSelectedPath();
            vector_string paths = new vector_string();
            if (path.EndsWith(".world"))
                GetWorldBlocks(path, paths);
            else
                paths.Add(path);
            foreach (string item in paths)
            {
                string backPath = EditorUtilities.EditorFilenameToBackFilename(item);
                FileHelper.CopyFile(backPath, item);
            }
        }

        public bool IsCookEnable()
        {
            return !BuildProject.GetInstance().IsBuilding();
        }

        public bool IsPathModelAsset(string AssetPath)
        {
            if (FileHelper.IsFileExists(AssetPath))
            {
                AssetType AssetType = AssetImporterManager.Instance().GetAssetType(AssetPath);
                if (AssetType == AssetType.Model)
                {
                    return true;
                }
            }
            return false;
        }

        string NewFileIsExists(string TargetDirectory, string FileName)
        {
            if (FileHelper.IsFileExists(FileName))
            {
                string RealName = PathHelper.GetNameOfPath(FileName);
                string Extension = PathHelper.GetExtension(FileName);
                string StringPart;
                int NumberPart;
                int NumPartDefault = 1;
                EditorUtilities.SplitObjectName(RealName, out StringPart, out NumberPart, NumPartDefault);
                string NewFilename1 = TargetDirectory + "/" + StringPart;
                int i = NumberPart;
                while (true)
                {
                    FileName = NewFilename1 + i.ToString() + Extension;
                    if (FileHelper.IsFileExists(FileName) == false)
                    {
                        break;
                    }
                    i++;
                }
            }
            return FileName;
        }

        void DoCopyOrMoveFile(string TargetDirectory, string Filename, bool bMove)
        {
            string FileName = PathHelper.GetFileName(Filename);
            string NewFilename = TargetDirectory + "/" + FileName;
            if (bMove && NewFilename == Filename)
            {
                return;
            }
            NewFilename = NewFileIsExists(TargetDirectory, NewFilename);
            if (bMove)
                DoMoveFile(Filename, NewFilename);
            else
                DoCopyFile(Filename, NewFilename);
        }

        void DoCopyOrMoveFolder(string TargetDirectory, string Directory, bool bMove)
        {
            if (TargetDirectory == Directory)
            {
                return;
            }
            if (PathHelper.IsSubDirectory(Directory, TargetDirectory))
            {
                return;
            }
            string DirectoryName = PathHelper.GetFileName(Directory);
            string NewDirectory = TargetDirectory + "/" + DirectoryName;
            if (bMove && NewDirectory == Directory)
            {
                return;
            }
            if (DirectoryHelper.IsDirectoryExists(NewDirectory))
            {
                string StringPart;
                int NumberPart;
                int NumPartDefault = 1;
                EditorUtilities.SplitObjectName(DirectoryName, out StringPart, out NumberPart, NumPartDefault);
                string NewDirectory1 = TargetDirectory + "/" + StringPart;
                int i = NumberPart;
                while (true)
                {
                    NewDirectory = NewDirectory1 + i.ToString();
                    if (DirectoryHelper.IsDirectoryExists(NewDirectory) == false)
                    {
                        break;
                    }
                    i++;
                }
            }
            if (bMove)
            {
                DoMoveDirectory(Directory, NewDirectory);
            }
            else
            {
                DoCopyDirectory(Directory, NewDirectory);
            }
        }



        public void GenerateMSDF()
        {
            MSDFGenerateUI MSDFGenerateUI = new MSDFGenerateUI();
            MSDFGenerateUI.Initialize(GetUIManager(), "MSDF Generate", "");
            MSDFGenerateUI.InputedEvent += (Sender) =>
            {
                RefreshListView(false);
            };
            DialogUIManager.GetInstance().ShowDialogUI(MSDFGenerateUI);
        }


        async Task<AssetImportResult> DoCopyOrMove(Importer importer, string TargetDirectory, string Path, bool bImport, bool bCopy, bool bMove)
        {
            Device Device = GetDevice();
            AssetImportResult bSuccess = null;

            if (bCopy || bMove)
            {
                if (DirectoryHelper.IsDirectoryExists(Path))
                {
                    DoCopyOrMoveFolder(TargetDirectory, Path, bMove);
                }
                else if (FileHelper.IsFileExists(Path))
                {
                    DoCopyOrMoveFile(TargetDirectory, Path, bMove);
                }
            }

            if (bImport)
            {
                if (importer != null)
                {
                    return await importer.DoImport(TargetDirectory, Path);
                }
            }
            return bSuccess;
        }

        public async Task<AssetImportResult> DoFolderImport(string TargetDirectory, string Path)
        {
            AssetImportResult bSuccess = null;
            List<string> delayImportFiles = new List<string>();
            string Directory = PathHelper.ToStandardForm(Path);
            string ParentDirectory = PathHelper.GetDirectoryName(Directory);
            DirectoryWalker DirectoryWalker = new DirectoryWalker();
            DirectoryWalker.WalkDirectory(Directory, true);
            int Count = DirectoryWalker.GetDirectoryWalkItemCount();
            for (int i = 0; i < Count; i++)
            {
                DirectoryWalkItem DirectoryWalkItem = DirectoryWalker.GetDirectoryWalkItem(i);
                if (DirectoryWalkItem.bIsDirectory == false)
                {
                    string Path1 = DirectoryWalkItem.Path;
                    string Extension = PathHelper.GetExtension(Path1);
                    string RelativePath = Path1.Substring(ParentDirectory.Length);
                    string RelativeDirectory = PathHelper.GetDirectoryName(RelativePath);
                    string TargetDirectory1 = TargetDirectory + RelativeDirectory;
                    if (DirectoryHelper.IsDirectoryExists(TargetDirectory1) == false)
                    {
                        DirectoryHelper.CreateDirectory(TargetDirectory1);
                    }
                    if (StringHelper.IgnoreCaseEqual(Extension, ".foliage"))
                    {
                        delayImportFiles.Add(Path1);
                    }
                    else
                    {
                        bSuccess = await DoAssetImport(TargetDirectory1, Path1);
                    }
                }
            }

            for (int i = 0; i < delayImportFiles.Count; i++)
            {
                string Path1 = delayImportFiles[i];
                string RelativePath = Path1.Substring(ParentDirectory.Length);
                string RelativeDirectory = PathHelper.GetDirectoryName(RelativePath);
                string TargetDirectory1 = TargetDirectory + RelativeDirectory;
                bSuccess = await DoAssetImport(TargetDirectory1, Path1);
            }

            return bSuccess;
        }

        public async Task<AssetImportResult> DoFileImport(string TargetDirectory, string Path)
        {
            AssetImportResult bSuccess = null;
            bSuccess = await DoAssetImport(TargetDirectory, Path);
            return bSuccess;
        }


        public void DoDeleteDirectory(string dir)
        {
            vector_string srcPaths = new vector_string();
            GetDirFiles(dir, srcPaths);
            DirectoryHelper.DeleteDirectory(dir);
            bool hit = PrefabManager.GetInstance().RemovePrefabs(srcPaths, EditorScene.GetInstance().GetWorld()._World, PrefabScene.GetInstance().GetWorld()._World);
            ResourceManager.Instance().DeleteFilePaths(srcPaths);
            if (hit)
            {
                PrefabScene.GetInstance().GetWorld().Root.RefreshTree();
                EditorScene.GetInstance().GetWorld().Root.RefreshTree();
                HierarchyUI.GetInstance().UpdateHierarchy();
            }
        }

        public void DoDeleteFile(string filename)
        {
            filename = PathHelper.ToStandardForm(filename);
            FileHelper.DeleteFile(filename);
            OperateWorldFile(3, filename, "");
            vector_string srcPaths = new vector_string();
            srcPaths.Add(filename);
            bool hit = PrefabManager.GetInstance().RemovePrefabs(srcPaths, EditorScene.GetInstance().GetWorld()._World, PrefabScene.GetInstance().GetWorld()._World);
            ResourceManager.Instance().DeleteFilePaths(srcPaths);

            if (_InspectedResource != null)
            {
                Resource res = _InspectedResource as Resource;
                string relPath = EditorUtilities.EditorFilenameToStandardFilename(filename);
                if (res != null && relPath == res.Path)
                {
                    InspectResourceObject(null);
                }
            }
            if (hit)
            {
                PrefabScene.GetInstance().GetWorld().Root.RefreshTree();
                EditorScene.GetInstance().GetWorld().Root.RefreshTree();
                HierarchyUI.GetInstance().UpdateHierarchy();
            }
        }

        public void DoRenameDirectory(string oldDir, string newDir)
        {
            vector_string srcPaths = new vector_string();
            vector_string desPaths = new vector_string();
            GetDirFiles(oldDir, srcPaths, newDir, desPaths);
            DirectoryHelper.RenameDirectory(oldDir, newDir);
            ResourceManager.Instance().ChangeFilePaths(srcPaths, desPaths);
        }

        public void OperateWorldFile(int Type, string OldFile, string NewFile)
        {
            if (!StringHelper.IgnoreCaseEqual(PathHelper.GetExtension(OldFile), ".world"))
                return;
            //Type: 0-Copy  1-Rename
            string OldFileName = PathHelper.GetNameOfPath(OldFile);
            string OldDirName = PathHelper.GetDirectoryName(OldFile);
            string NewFileName = PathHelper.GetNameOfPath(NewFile);
            string NewDirName = PathHelper.GetDirectoryName(NewFile);

            FileInfo[] FileInfos = DirectoryHelper.GetFilesInDirectory(OldDirName, true);

            Regex BlockRegex = new Regex(OldFileName + @"_L\d_X\d_Y\d_Z\d.block");
            Regex PreloadBlockRegex = new Regex(OldFileName + "_PreLoad.block");
            foreach (FileInfo FileInfo in FileInfos)
            {
                string FileName = FileInfo.Name;
                string Extension = PathHelper.GetExtension(FileName);
                if (StringHelper.IgnoreCaseEqual(Extension, ".block") && FileName.StartsWith(OldFileName))
                {
                    string NewName = "";
                    Match BlockMatch = BlockRegex.Match(FileName);
                    if (BlockMatch.Success)
                    {
                        NewName = BlockMatch.Value.Replace(OldFileName, NewFileName);
                        goto MatchSuccess;
                    }

                    Match PreloadBlockMatch = PreloadBlockRegex.Match(FileName);
                    if (PreloadBlockMatch.Success)
                    {
                        NewName = PreloadBlockMatch.Value.Replace(OldFileName, NewFileName);
                        goto MatchSuccess;
                    }
                    goto MatchEnd;
                MatchSuccess:
                    switch (Type)
                    {
                        case 0:
                            NewName = PathHelper.CombinePath(NewDirName, NewName);
                            DoCopyFile(FileInfo.FullName, NewName);
                            break;
                        case 1:
                            NewName = PathHelper.CombinePath(OldDirName, NewName);
                            DoRenameFile(FileInfo.FullName, NewName);
                            break;
                        case 2:
                            NewName = PathHelper.CombinePath(NewDirName, NewName);
                            DoMoveFile(FileInfo.FullName, NewName);
                            break;
                        case 3:
                            DoDeleteFile(FileInfo.FullName);
                            break;
                    }
                MatchEnd:
                    continue;
                }
            }
        }

        public void DoRenameFile(string oldFilename, string newFileName)
        {
            oldFilename = PathHelper.ToStandardForm(oldFilename);
            newFileName = PathHelper.ToStandardForm(newFileName);
            FileHelper.RenameFile(oldFilename, newFileName);
            OperateWorldFile(1, oldFilename, newFileName);
            vector_string srcPaths = new vector_string();
            srcPaths.Add(oldFilename);
            vector_string desPaths = new vector_string();
            desPaths.Add(newFileName);
            ResourceManager.Instance().ChangeFilePaths(srcPaths, desPaths);

            if (_InspectedResource != null)
            {
                Resource res = _InspectedResource as Resource;
                string relPath = EditorUtilities.EditorFilenameToStandardFilename(oldFilename);
                if (res != null && relPath == res.Path)
                {
                    InspectResourceObject(null);
                }
            }
        }

        public void DoCopyDirectory(string oldDir, string newDir)
        {
            oldDir = PathHelper.ToStandardForm(oldDir);
            DirectoryHelper.CopyDirectory(oldDir, newDir);
            vector_string srcPaths = new vector_string();
            GetDirFiles(newDir, srcPaths);
            ResourceManager.Instance().AddNewFiles(srcPaths, EditorUtilities.IsProjectPath(oldDir));
        }

        public void DoCopyFile(string oldFilename, string newFileName)
        {
            oldFilename = PathHelper.ToStandardForm(oldFilename);
            newFileName = PathHelper.ToStandardForm(newFileName);
            FileHelper.CopyFile(oldFilename, newFileName);
            OperateWorldFile(0, oldFilename, newFileName);
            vector_string srcPaths = new vector_string();
            srcPaths.Add(newFileName);
            ResourceManager.Instance().AddNewFiles(srcPaths, EditorUtilities.IsProjectPath(oldFilename));
        }

        public void DoMoveDirectory(string oldDir, string newDir)
        {
            vector_string srcPaths = new vector_string();
            vector_string desPaths = new vector_string();
            GetDirFiles(oldDir, srcPaths, newDir, desPaths);
            DirectoryHelper.CopyDirectory(oldDir, newDir);
            DirectoryHelper.DeleteDirectory(oldDir);
            ResourceManager.Instance().ChangeFilePaths(srcPaths, desPaths);
        }

        public void DoMoveFile(string oldFilename, string newFileName)
        {
            oldFilename = PathHelper.ToStandardForm(oldFilename);
            newFileName = PathHelper.ToStandardForm(newFileName);
            FileHelper.CopyFile(oldFilename, newFileName);
            FileHelper.DeleteFile(oldFilename);
            OperateWorldFile(2, oldFilename, newFileName);
            vector_string srcPaths = new vector_string();
            srcPaths.Add(oldFilename);
            vector_string desPaths = new vector_string();
            desPaths.Add(newFileName);
            ResourceManager.Instance().ChangeFilePaths(srcPaths, desPaths);
        }

        public void GetDirFiles(string srcPath, vector_string srcDirs, string desPath = null, vector_string desDirs = null)
        {
            DirectoryInfo DirInfo = new DirectoryInfo(srcPath);
            foreach (FileInfo f in DirInfo.GetFiles())
            {
                srcDirs.Add(srcPath + "/" + f.Name);
                if (desPath != null)
                {
                    desDirs.Add(desPath + "/" + f.Name);
                }
            }
            foreach (DirectoryInfo d in DirInfo.GetDirectories())
            {
                if (desPath != null)
                {
                    GetDirFiles(srcPath + "/" + d.Name, srcDirs, desPath + "/" + d.Name, desDirs);
                }
                else
                {
                    GetDirFiles(srcPath + "/" + d.Name, srcDirs);
                }
            }
        }

        public void GetWorldBlocks(string worldPath, vector_string blocks)
        {
            string worldDir = PathHelper.GetDirectoryName(worldPath);
            string worldName = PathHelper.GetNameOfPath(worldPath);
            Regex blockRegex = new Regex(worldName + @"_.+\.block");
            FileInfo[] FileInfos = DirectoryHelper.GetFilesInDirectory(worldDir, false);
            foreach (FileInfo fileInfo in FileInfos)
            {
                string fileName = fileInfo.Name;
                if (!fileName.EndsWith(".block"))
                    continue;
                Match match = blockRegex.Match(fileName);
                if (match.Success)
                {
                    blocks.Add(PathHelper.ToStandardForm(fileInfo.FullName));
                }
            }
        }

        public string GetNdaPath(string TargetDirectory, string Path)
        {
            if (Path == "") return "";
            string FileName = PathHelper.GetNameOfPath(Path);
            string NdaPath = TargetDirectory + "/" + FileName + ".nda";
            string EditorNdaPath = EditorUtilities.EditorFilenameToStandardFilename(NdaPath);
            return EditorNdaPath;
        }
        #endregion

        #region User config

        public void SaveUserConfig(Record RootRecord)
        {
            Record RecordProjectUI = RootRecord.AddChild();
            RecordProjectUI.SetTypeString("ProjectUI");
            Record RecordProjectTreeState = RecordProjectUI.AddChild();
            TreeState ProjectTreeState = _Tree.SaveState();
            ProjectTreeState.Save(RecordProjectTreeState);
            Record RecordProjectUIState = RecordProjectUI.AddChild();
            RecordProjectUIState.SetTypeString("ProjectUIState");
            RecordProjectUIState.SetInt("TreeWidth", _Tree.GetWidth());
            RecordProjectUIState.SetInt("VContainer2Width", _VContainer2.GetWidth());
            RecordProjectUIState.SetInt("ListViewItemWidth", _ListView.GetItemWidth());
        }

        public void LoadUserConfig(Record RootRecord)
        {
            Record RecordProjectUI = RootRecord.FindByTypeString("ProjectUI");
            if (RecordProjectUI != null)
            {
                Record RecordTreeState = RecordProjectUI.FindByTypeString("TreeState");
                TreeState TreeState = new TreeState();
                TreeState.Load(RecordTreeState);
                _Tree.LoadState(TreeState);
                TreeItem RootItem = _Tree.GetRootItem();
                RootItem.SetExpanded(true);
                Record RecordProjectUIState = RecordProjectUI.FindByTypeString("ProjectUIState");
                if (RecordProjectUIState != null)
                {
                    _Tree.SetWidth(RecordProjectUIState.GetInt("TreeWidth"));
                    _VContainer2.SetWidth(RecordProjectUIState.GetInt("VContainer2Width"));
                    SetListViewItemWidth(RecordProjectUIState.GetInt("ListViewItemWidth"));
                }
            }
        }

        #endregion

        #region Classify Label

        void OnCheckClassifyLabelClicked(Check Sender)
        {
            _bClassifyLabel = Sender.GetChecked();
            ClearActiveClassifyLabel();
            foreach (ClassifyLabelUI UI in _ClassifyLabels)
            {
                UI.SetEnable(_bClassifyLabel);
            }
            RefreshListView(true);
        }

        private void ClassificationLeftMouseDownEvent(ClassifyLabelUI Sender)
        {
            if (_ActiveClassifyLabels.Contains(Sender))
            {
                _ActiveClassifyLabels.Remove(Sender);
            }
            else if (Sender.GetEnable())
            {
                _ActiveClassifyLabels.Add(Sender);
            }

            _ListView.ClearItems();
            bool bResetScroll = true;
            RefreshListView(bResetScroll);
        }

        void ClearActiveClassifyLabel()
        {
            foreach (ClassifyLabelUI UI in _ActiveClassifyLabels)
            {
                UI.RestoreColor();
            }
            _ActiveClassifyLabels.Clear();
        }

        bool IsClassifyMode()
        {
            return _ActiveClassifyLabels.Count > 0;
        }

        void DoCancelClassify()
        {
            if (IsClassifyMode())
            {
                ClearActiveClassifyLabel();
                ListViewItem SelectedItem = _ListView.GetSelectedItem();
                if (SelectedItem != null)
                {
                    _SelectedPathInSearchMode = SelectedItem.Path;
                }
                JumpToSelectedPathInSearchMode();
            }
        }

        #endregion

        #region Open Nda
        public void OpenNda(string FilePath, CEngine.ClassIDType ObjectClassID)
        {
            if (ObjectClassID == CEngine.ClassIDType.CLASS_CurveControllerRes)
            {
                string Path1 = EditorUtilities.EditorFilenameToStandardFilename(FilePath);
                CinematicUI CinematicUI = CinematicUI.GetInstance();
                string GuidPath = ResourceManager.Instance().ConvertPathToGuid(Path1);
                CinematicUI.SetCurveCtrPath(GuidPath);
                CinematicUI.OnMenuItemClearAll(null);
                HierarchyUI.GetInstance().ProcessLevelSequence(GuidPath);
                ResourceManager.Instance().ImmediateReloadResource(ResourceManager.Instance().ConvertGuidToPath(CinematicUI.GetCurveCtrPath()));
                if (CinematicUI.InitializationJudgment())
                {
                    MainUI.GetInstance().ActivateDockingCard_Cinematic();
                    Entity CurEntity = HierarchyUI.GetInstance().GetScene().GetSelection();
                    CinematicUI.ResetHead();
                    CinematicUI.FitOnRange();
                    CinematicUI.RefreshInspector(CurEntity);
                }
            }
        }
        #endregion


        #region File Display
        string GetFileDisplay(string FilePath, string Extension)
        {
            CEngine.ClassIDType FileClassIDType = ResourceTypeCache.GetInstance().GetResourceType_Cache(FilePath);


            string Value;
            if (FileClassIDType == CEngine.ClassIDType.CLASS_NullType)
            {
                Value = Extension;
                if (Value == null)
                {
                    Value = "File";
                }
                return Value;
            }
            ClassifyLabelUI.ClassifyTypeMap.TryGetValue(FileClassIDType, out Value);
            if (Value == null)
            {
                Value = "File";
            }
            return Value;

        }
        #endregion
    }
}
