using CEngine;
using Clicegf;
using System;
using System.Reflection;

namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Gameplay/CmCamera", InspectorType = typeof(Inspector_MultiTypeComponent<CmCameraComponent>))]
    public class CmCameraComponent : GameObjectComponent, IMultiTypeComponent
    {
        static string[] _NativeNames = { "cegf::CmCameraComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        public override void SyncDataFromEngine()
        {
            _CameraType = GetRunimeComponent().GetCmCameraType();
            CmCamera = CreateInstance(_CameraType);
            CmCamera.GetFromComponent(GetRunimeComponent());
        }

        private CmCameraType _CameraType = CmCameraType.TRACK;
        [PropertyInfo(PropertyType = "Auto", ToolTips = "CmBrainCameraApplyType")]
        public CmCameraType Type
        {
            get { return _CameraType; }
            set
            {
                if (_CameraType != value)
                {
                    _CameraType = value;
                    CmCamera = CreateInstance(_CameraType);
                    SetToRuntime();
                }
            }
        }

        private Clicegf.CmCameraComponent GetRunimeComponent()
        {
            var go = GetRuntimeOwnerGameObject();
            var comp = (Clicegf.CmCameraComponent)(go.GetComponentByMetaClassName(_NativeNames[0]));
            return comp;
        }

        public void SetToRuntime()
        {
            CmCamera.SetToComponent(GetRunimeComponent());
        }

        public void GetFromRuntime()
        {
            CmCamera.GetFromComponent(GetRunimeComponent());
        }

        public object GetInspectedObject()
        {
            return CmCamera;
        }

        private CmCameraEditorBase CmCamera;
        static protected string GetCmCameraTypeName(CmCameraType type)
        {
            return "Clicegf.CmCamera" + type.ToString();
        }

        protected CmCameraEditorBase CreateInstance(CmCameraType type)
        {
            Type typeInst = CrossEngine.GetType(GetCmCameraTypeName(type));
            return typeInst != null ? Activator.CreateInstance(typeInst) as CmCameraEditorBase : null;
        }
    }
}
