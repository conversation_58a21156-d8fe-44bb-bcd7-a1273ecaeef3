namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Rendering/FoliageComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class FoliageGameComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::FoliageComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<FoliageComponent>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<FoliageComponent>();
            }

            mECSEditorComponents["FoliageComponent"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["FoliageComponent"] = mGameObject.mEntity.GetComponent<FoliageComponent>();
        }
    }
}
