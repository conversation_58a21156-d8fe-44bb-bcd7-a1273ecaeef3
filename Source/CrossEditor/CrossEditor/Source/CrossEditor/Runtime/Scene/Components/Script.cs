using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.IO;

namespace CrossEditor
{
    public class Script : Component
    {
        string _Path;
        List<Property> _ScriptProperties;
        DateTime _LastWriteTime;

        static string[] _NativeNames = { "cross::ScriptComponentG" };
        static int _ComponentOrder = 0;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 3;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }
        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public Script()
        {
            _Path = "";
            _ScriptProperties = new List<Property>();
            _LastWriteTime = DateTime.MinValue;
        }

        public override void Reset()
        {
            base.Reset();
            Path = "";
        }

        public override void Initialize(string initialization)
        {
            base.Initialize(initialization);
            Path = initialization;
        }


        public static new bool OnPathDroped(Inspector_Entity entity_inspector, string path, string Extension)
        {
            if (StringHelper.IgnoreCaseEqual(Extension, ".lua"))
            {
                entity_inspector.AddComponentClicked<Script>(path);
                return true;
            }
            return false;
        }

        public override void SyncDataFromEngine()
        {
            _Path = ScriptSystemG.GetScriptPath(Entity.World.GetNativePointer(), Entity.EntityID);
            _Enable = ScriptSystemG.GetScriptEnable(Entity.World.GetNativePointer(), Entity.EntityID);
        }

        public override void RuntimeReapplyProperties()
        {
            //Do this in post serialize
            ReflectScriptProperties(false);
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable this script.")]
        public override bool Enable
        {
            get { return _Enable; }
            set
            {
                _Enable = value;
                ScriptSystemG.SetScriptEnable(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Script path.", FileTypeDescriptor = "Script Files#js|lua")]
        public string Path
        {
            get
            {
                return _Path;
            }
            set
            {
                if (_Path != value)
                {
                    _LastWriteTime = DateTime.MinValue;
                    EditorScene.GetInstance().SetDirty();
                }

                _Path = value;
                if (_Path == "") return;
                ScriptSystemG.SetScriptPath(Entity.World.GetNativePointer(), Entity.EntityID, _Path);
                ReflectScriptProperties();
            }
        }

        public void TryUpdateScript()
        {
            var AbsPath = CrossEngine.GetFileAbsolutPath(_Path);
            if (AbsPath == null || AbsPath == "")
                return;

            FileInfo fi = new FileInfo(AbsPath);
            if (fi.Exists && fi.LastWriteTime != _LastWriteTime)
            {
                _LastWriteTime = fi.LastWriteTime;
                ResourceManager.Instance().TryReloadResource(_Path);
                ReflectScriptProperties(true);
            }
        }


        public List<Property> GetScriptProperties()
        {
            return _ScriptProperties;
        }

        void ReflectScriptMember()
        {
            ScriptSystemG.ReflectScriptMember(Entity.World.GetNativePointer(), Entity.EntityID);
        }
        int GetScriptMemberCount()
        {
            uint Count = ScriptSystemG.GetMemberCount(Entity.World.GetNativePointer(), Entity.EntityID);
            return (int)Count;
        }

        string GetScriptMemberName(uint Index)
        {
            return ScriptSystemG.GetMemberName(Entity.World.GetNativePointer(), Entity.EntityID, Index);
        }

        PropertyType GetScriptMemberType(string Name)
        {
            int type = ScriptSystemG.GetMemberType(Entity.World.GetNativePointer(), Entity.EntityID, Name);
            ScriptDataType ScriptDataType = (ScriptDataType)type;
            switch (ScriptDataType)
            {
                case ScriptDataType.Null:
                    return PropertyType.Unknown;
                case ScriptDataType.String:
                    return PropertyType.String;
                case ScriptDataType.Boolean:
                    return PropertyType.Bool;
                case ScriptDataType.Number:
                    return PropertyType.Double;
                case ScriptDataType.Vector2:
                    return PropertyType.Vector2;
                case ScriptDataType.Vector3:
                    return PropertyType.Vector3;
                case ScriptDataType.Entity:
                    return PropertyType.Entity;
                default:
                    return PropertyType.Unknown;
            }
        }

        bool GetScriptBoolMember(string Name)
        {
            return ScriptSystemG.GetBoolMember(Entity.World.GetNativePointer(), Entity.EntityID, Name);
        }

        double GetScriptNumberMember(string Name)
        {
            return ScriptSystemG.GetNumberMember(Entity.World.GetNativePointer(), Entity.EntityID, Name);
        }

        string GetScriptStringMember(string Name)
        {
            return ScriptSystemG.GetStringMember(Entity.World.GetNativePointer(), Entity.EntityID, Name);
        }

        Vector2f GetScriptVector2Member(string Name)
        {
            Float2 Float2 = ScriptSystemG.GetVector2Member(Entity.World.GetNativePointer(), Entity.EntityID, Name);
            return new Vector2f(Float2);
        }

        Vector3f GetScriptVector3Member(string Name)
        {
            Float3 Float3 = ScriptSystemG.GetVector3Member(Entity.World.GetNativePointer(), Entity.EntityID, Name);
            return new Vector3f(Float3);
        }

        EntityIDStruct GetScriptEntityMember(string Name)
        {
            EntityIDStruct EntityID = ScriptSystemG.GetEntityMember(Entity.World.GetNativePointer(), Entity.EntityID, Name);
            return EntityID;
        }

        Property FindScriptProperty(List<Property> ScriptProperties, string Name)
        {
            foreach (Property Property in ScriptProperties)
            {
                if (Property.Name == Name)
                {
                    return Property;
                }
            }
            return null;
        }

        Property FindScriptProperty(string Name)
        {
            return FindScriptProperty(_ScriptProperties, Name);
        }

        void GetScriptProperty(string Name)
        {
            Property Property = FindScriptProperty(_ScriptProperties, Name);
            GetScriptProperty(Property);
        }

        void GetScriptProperty(Property Property)
        {
            if (Property.Type == PropertyType.Double)
            {
                Property.Value = GetScriptNumberMember(Property.Name);
            }
        }

        public void ReflectScriptProperties(bool bDoRuntimeReflect = true)
        {
            if (bDoRuntimeReflect)
            {
                ReflectScriptMember();
            }

            _ScriptProperties.Clear();
            int Count = GetScriptMemberCount();
            if (_ScriptProperties.Count != Count)
                EditorScene.GetInstance().SetDirty();

            for (uint i = 0; i < Count; i++)
            {
                string Name = GetScriptMemberName(i);
                PropertyType Type = GetScriptMemberType(Name);
                Property ScriptProperty = new Property();
                ScriptProperty.Name = Name;
                ScriptProperty.Type = Type;
                ScriptProperty.bEditable = true;
                switch (Type)
                {
                    case PropertyType.Bool:
                        ScriptProperty.Value = GetScriptBoolMember(Name);
                        break;
                    case PropertyType.String:
                        ScriptProperty.Value = GetScriptStringMember(Name);
                        break;
                    case PropertyType.Double:
                        ScriptProperty.Value = GetScriptNumberMember(Name);
                        break;
                    case PropertyType.Vector2:
                        ScriptProperty.Value = GetScriptVector2Member(Name);
                        break;
                    case PropertyType.Vector3:
                        ScriptProperty.Value = GetScriptVector3Member(Name);
                        break;
                    case PropertyType.Entity:
                        ScriptProperty.Value = GetScriptEntityMember(Name);
                        break;
                    default:
                        continue;
                }
                _ScriptProperties.Add(ScriptProperty);
            }
        }

        // 注：此函数会刷新World中所有设置了this.Path脚本的Script组件
        public void RefreshScript()
        {
            ResourceManager.Instance().TryReloadResource(Path);
        }

        public void SetProperty(string name, object value)
        {
            //Console.WriteLine("Set script property {0} to {1}", name, value);
            int Count = _ScriptProperties.Count;
            for (int i = Count - 1; i >= 0; i--)
            {
                Property ScriptProperty = _ScriptProperties[i];
                if (ScriptProperty.Name.Equals(name))
                {
                    switch (ScriptProperty.Type)
                    {
                        case PropertyType.Bool:
                            bool boolValue = (bool)(value is bool ? value : false);
                            ScriptProperty.Value = boolValue;
                            ScriptSystemG.SetBoolMember(Entity.World.GetNativePointer(), Entity.EntityID, name, boolValue);
                            break;
                        case PropertyType.String:
                            string stringValue = (string)(value is string ? value : "");
                            ScriptProperty.Value = stringValue;
                            ScriptSystemG.SetStringMember(Entity.World.GetNativePointer(), Entity.EntityID, name, stringValue);
                            break;
                        case PropertyType.Double:
                            double doubleValue = (double)(value is double ? value : 0.0);
                            ScriptProperty.Value = doubleValue;
                            ScriptSystemG.SetNumberMember(Entity.World.GetNativePointer(), Entity.EntityID, name, doubleValue);
                            break;
                        case PropertyType.Vector2:
                            Vector2f vector2Value = (Vector2f)value;
                            ScriptProperty.Value = vector2Value;
                            ScriptSystemG.SetVector2Member(Entity.World.GetNativePointer(), Entity.EntityID, name, vector2Value.ToFloat2());
                            break;
                        case PropertyType.Vector3:
                            Vector3f vector3Value = (Vector3f)value;
                            ScriptProperty.Value = vector3Value;
                            ScriptSystemG.SetVector3Member(Entity.World.GetNativePointer(), Entity.EntityID, name, vector3Value.ToFloat3());
                            break;
                        case PropertyType.Entity:
                            EntityIDStruct entityValue = value as EntityIDStruct;
                            ScriptProperty.Value = entityValue;
                            ScriptSystemG.SetEntityMember(Entity.World.GetNativePointer(), Entity.EntityID, name, entityValue);
                            break;
                        default:
                            continue;
                    }

                    return;
                }
            }
        }
    }
}
