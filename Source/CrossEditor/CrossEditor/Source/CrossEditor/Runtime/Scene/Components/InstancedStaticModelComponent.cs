using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;

namespace CrossEditor
{
    using InstancedStaticModel = Model;

	internal static class Ext
	{
		internal static void ResizeWithNew<T>(this List<T> list, int count) where T : new()
		{
			if (list.Count > count)
			{
				list.Resize(count);
			}
			else if (list.Count < count)
			{
				list.AddRange(Enumerable.Range(0, count - list.Count).Select(i => new T()));
			}
		}
	}

	public class TRS
    {
        Float3 _Translation = new Float3();
        Float3 _Rotation = new Float3();
        Float3 _Scale = new Float3(1, 1, 1);

        public Float3 Translation { set => _Translation = value; get => _Translation; }
        public Float3 Rotation { set => _Rotation = value; get => _Rotation; }
        public Float3 Scale { set => _Scale = value; get => _Scale; }
    }

    public class InstanceMemberData
    {
        string _Name = "MemberName";
        Clicross.InstanceMemberType _MemberType;
        Object _Data = new List<float>();

        [PropertyInfo(PropertyType = "Auto")]
        public string Name { get => _Name; set => _Name = value; }

        [PropertyInfo(PropertyType = "Auto")]
        public Clicross.InstanceMemberType MemberType
        {
            get => _MemberType;
            set
            {
                if (_MemberType != value)
                {
                    _MemberType = value;

                    int dataSize = (int)_Data.GetType().GetProperty("Count").GetValue(_Data);
                    var elementType = GetDataElementType();

                    _Data = Activator.CreateInstance(typeof(List<>).MakeGenericType(elementType));
                    typeof(ListHelper).GetMethod("Resize").MakeGenericMethod(elementType).Invoke(null, new object[] { _Data, dataSize, Activator.CreateInstance(elementType) });
                }
            }
        }

        [PropertyInfo(PropertyType = "List", bFixedItems = true)]
        public Object Data { get => _Data; set => _Data = value; }

        public Type GetDataElementType()
        {
            Dictionary<Clicross.InstanceMemberType, Type> TypeMaps = new Dictionary<Clicross.InstanceMemberType, Type> {
                    { Clicross.InstanceMemberType.Float1, typeof(float) },
                    { Clicross.InstanceMemberType.Float2, typeof(Float2) },
                    { Clicross.InstanceMemberType.Float3, typeof(Float3) },
                    { Clicross.InstanceMemberType.Float4, typeof(Float4) },
                    { Clicross.InstanceMemberType.UInt1, typeof(uint) },
                    { Clicross.InstanceMemberType.UInt2, typeof(Vector2ui) },
                    { Clicross.InstanceMemberType.UInt3, typeof(Vector3ui) },
                    { Clicross.InstanceMemberType.UInt4, typeof(Vector4ui) },
                    { Clicross.InstanceMemberType.SInt1, typeof(int) },
                    { Clicross.InstanceMemberType.SInt2, typeof(Vector2i) },
                    { Clicross.InstanceMemberType.SInt3, typeof(Vector3i) },
                    { Clicross.InstanceMemberType.SInt4, typeof(Vector4i) },
                };

            return TypeMaps[MemberType];
        }
    }

    public class InstanceData
    {
        InstanceDataResource _InstanceDataResource;
        string _InstanceDataResourcePath;
        UInt32 _InstanceCount;
        List<TRS> _InstanceTransformData = new List<TRS>();
        List<InstanceMemberData> _InstanceMemberDatas = new List<InstanceMemberData>();

        [PropertyInfo(PropertyType = "StringAsResource", FileTypeDescriptor = "InstanceDataResource#nda", ObjectClassID1 = ClassIDType.CLASS_InstanceDataResource)]
        public string InstanceDataResourcePath
        {
            get => _InstanceDataResourcePath;
            set
            {
                _InstanceDataResourcePath = value;

                UpdateInstanceDataFromResource();
            }
        }

        [PropertyInfo(PropertyType = "Auto")]
        public UInt32 InstanceCount
        {
            get => _InstanceCount;
            set
            {
                if (_InstanceCount != value)
                {
                    _InstanceCount = value;

                    // Resize InstanceTransformData
                    _InstanceTransformData.Resize((int)_InstanceCount);
                    for (int i = 0; i < _InstanceCount; i++)
                    {
                        if (_InstanceTransformData[i] == null)
                        {
                            _InstanceTransformData[i] = new TRS();
                        }
                    }

                    // Resize InstanceMemberData
                    foreach (InstanceMemberData instanceMemberData in _InstanceMemberDatas)
                    {
                        var elementType = instanceMemberData.GetDataElementType();
                        typeof(ListHelper).GetMethod("Resize").MakeGenericMethod(elementType).Invoke(null, new object[] { instanceMemberData.Data, (int)_InstanceCount, Activator.CreateInstance(elementType) });
                    }

                    SaveDataToResource();
                }
            }
        }

        [PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", bFixedItems = true)]
        public List<TRS> InstanceTransformData
        {
            get => _InstanceTransformData;
            set
            {
                _InstanceTransformData = value;

                SaveDataToResource();
            }
        }

        [PropertyInfo(PropertyType = "List")]
        public List<InstanceMemberData> InstanceMemberDatas
        {
            get => _InstanceMemberDatas;
            set
            {
                _InstanceMemberDatas = value;

                // Make sure new InstanceMemberData has correct data array size.
                foreach (InstanceMemberData instanceMemberData in _InstanceMemberDatas)
                {
                    var elementType = instanceMemberData.GetDataElementType();
                    typeof(ListHelper).GetMethod("Resize").MakeGenericMethod(elementType).Invoke(null, new object[] { instanceMemberData.Data, (int)_InstanceCount, Activator.CreateInstance(elementType) });
                }

                SaveDataToResource();
            }
        }

        public void UpdateInstanceDataFromResource()
        {
            _InstanceCount = 0;
            _InstanceMemberDatas.Clear();

            Resource resource = Resource.Get(ResourceManager.Instance().ConvertGuidToPath(_InstanceDataResourcePath), false);
            if (resource != null)
            {
                _InstanceDataResource = resource as InstanceDataResource;

                _InstanceCount = _InstanceDataResource.GetInstanceCount();

                // Get InstanceTransformDatas
                unsafe
                {
                    UInt32 type = 0, size = 0, stride = 0;
                    IntPtr translationIntPtr = IntPtr.Zero;
                    IntPtr rotationIntPtr = IntPtr.Zero;
                    IntPtr scaleIntPtr = IntPtr.Zero;
                    _InstanceDataResource.GetInstanceMemberData("Translation", ref type, ref translationIntPtr, ref size, ref stride);
                    _InstanceDataResource.GetInstanceMemberData("Rotation", ref type, ref rotationIntPtr, ref size, ref stride);
                    _InstanceDataResource.GetInstanceMemberData("Scale", ref type, ref scaleIntPtr, ref size, ref stride);

                    float* translationData = (float*)translationIntPtr;
                    float* rotationData = (float*)rotationIntPtr;
                    float* scaleData = (float*)scaleIntPtr;

                    _InstanceTransformData.Resize((int)_InstanceCount);

                    for (int i = 0; i < _InstanceCount; i++)
                    {
                        _InstanceTransformData[i] = new TRS();

                        _InstanceTransformData[i].Translation.x = *translationData++;
                        _InstanceTransformData[i].Translation.y = *translationData++;
                        _InstanceTransformData[i].Translation.z = *translationData++;

                        _InstanceTransformData[i].Rotation.x = *rotationData++;
                        _InstanceTransformData[i].Rotation.y = *rotationData++;
                        _InstanceTransformData[i].Rotation.z = *rotationData++;

                        _InstanceTransformData[i].Scale.x = *scaleData++;
                        _InstanceTransformData[i].Scale.y = *scaleData++;
                        _InstanceTransformData[i].Scale.z = *scaleData++;
                    }
                }

                // Get InstanceMemberDatas
                List<string> memberNames = new List<string>();
                for (uint i = 0; i < _InstanceDataResource.GetInstanceMemberDataNameCount(); i++)
                {
                    memberNames.Add(_InstanceDataResource.GetInstanceMemberDataNameAt(i));
                }

                foreach (var name in memberNames)
                {
                    if (name == "Translation" || name == "Rotation" || name == "Scale")
                    {
                        continue;
                    }

                    unsafe
                    {
                        UInt32 type = 0, size = 0, stride = 0;
                        IntPtr data = IntPtr.Zero;
                        _InstanceDataResource.GetInstanceMemberData(name, ref type, ref data, ref size, ref stride);

                        byte* byteDataPtr = (byte*)data;

                        InstanceMemberData instanceMemberData = new InstanceMemberData();
                        instanceMemberData.Name = name;
                        instanceMemberData.MemberType = (Clicross.InstanceMemberType)type;

                        switch (instanceMemberData.MemberType)
                        {
                            case Clicross.InstanceMemberType.Float1:
                                {
                                    List<float> datas = new List<float>();
                                    datas.Resize((int)_InstanceCount);

                                    for (int i = 0; i < _InstanceCount; i++)
                                    {
                                        datas[i] = *(float*)byteDataPtr;
                                        byteDataPtr += stride;
                                    }

                                    instanceMemberData.Data = datas;
                                }
                                break;
                            case Clicross.InstanceMemberType.Float2:
                                {
                                    List<Float2> datas = new List<Float2>();
                                    datas.Resize((int)_InstanceCount);

                                    for (int i = 0; i < _InstanceCount; i++)
                                    {
                                        float* floatDataPtr = (float*)byteDataPtr;

                                        datas[i] = new Float2();
                                        datas[i].x = *floatDataPtr++;
                                        datas[i].y = *floatDataPtr++;

                                        byteDataPtr += stride;
                                    }

                                    instanceMemberData.Data = datas;
                                }
                                break;
                            case Clicross.InstanceMemberType.Float3:
                                {
                                    List<Float3> datas = new List<Float3>();
                                    datas.Resize((int)_InstanceCount);

                                    for (int i = 0; i < _InstanceCount; i++)
                                    {
                                        float* floatDataPtr = (float*)byteDataPtr;

                                        datas[i] = new Float3();
                                        datas[i].x = *floatDataPtr++;
                                        datas[i].y = *floatDataPtr++;
                                        datas[i].z = *floatDataPtr++;

                                        byteDataPtr += stride;
                                    }

                                    instanceMemberData.Data = datas;
                                }
                                break;
                            case Clicross.InstanceMemberType.Float4:
                                {
                                    List<Float4> datas = new List<Float4>();
                                    datas.Resize((int)_InstanceCount);

                                    for (int i = 0; i < _InstanceCount; i++)
                                    {
                                        float* floatDataPtr = (float*)byteDataPtr;

                                        datas[i] = new Float4();
                                        datas[i].x = *floatDataPtr++;
                                        datas[i].y = *floatDataPtr++;
                                        datas[i].z = *floatDataPtr++;
                                        datas[i].w = *floatDataPtr++;

                                        byteDataPtr += stride;
                                    }

                                    instanceMemberData.Data = datas;
                                }
                                break;
                            case Clicross.InstanceMemberType.UInt1:
                                {
                                    List<uint> datas = new List<uint>();
                                    datas.Resize((int)_InstanceCount);

                                    for (int i = 0; i < _InstanceCount; i++)
                                    {
                                        datas[i] = *(uint*)byteDataPtr;
                                        byteDataPtr += stride;
                                    }

                                    instanceMemberData.Data = datas;
                                }
                                break;
                            case Clicross.InstanceMemberType.UInt2:
                                {
                                    List<Vector2ui> datas = new List<Vector2ui>();
                                    datas.Resize((int)_InstanceCount);

                                    for (int i = 0; i < _InstanceCount; i++)
                                    {
                                        uint* UIntDataPtr = (uint*)byteDataPtr;

                                        var x = *UIntDataPtr++;
                                        var y = *UIntDataPtr++;
                                        datas[i] = new Vector2ui(x, y);

                                        byteDataPtr += stride;
                                    }

                                    instanceMemberData.Data = datas;
                                }
                                break;
                            case Clicross.InstanceMemberType.UInt3:
                                {
                                    List<Vector3ui> datas = new List<Vector3ui>();
                                    datas.Resize((int)_InstanceCount);

                                    for (int i = 0; i < _InstanceCount; i++)
                                    {
                                        uint* UIntDataPtr = (uint*)byteDataPtr;

                                        var x = *UIntDataPtr++;
                                        var y = *UIntDataPtr++;
                                        var z = *UIntDataPtr++;
                                        datas[i] = new Vector3ui(x, y, z);

                                        byteDataPtr += stride;
                                    }

                                    instanceMemberData.Data = datas;
                                }
                                break;
                            case Clicross.InstanceMemberType.UInt4:
                                {
                                    List<Vector4ui> datas = new List<Vector4ui>();
                                    datas.Resize((int)_InstanceCount);

                                    for (int i = 0; i < _InstanceCount; i++)
                                    {
                                        uint* UIntDataPtr = (uint*)byteDataPtr;

                                        var x = *UIntDataPtr++;
                                        var y = *UIntDataPtr++;
                                        var z = *UIntDataPtr++;
                                        var w = *UIntDataPtr++;
                                        datas[i] = new Vector4ui(x, y, z, w);

                                        byteDataPtr += stride;
                                    }

                                    instanceMemberData.Data = datas;
                                }
                                break;
                            case Clicross.InstanceMemberType.SInt1:
                                {
                                    List<int> datas = new List<int>();
                                    datas.Resize((int)_InstanceCount);

                                    for (int i = 0; i < _InstanceCount; i++)
                                    {
                                        datas[i] = *(int*)byteDataPtr;
                                        byteDataPtr += stride;
                                    }

                                    instanceMemberData.Data = datas;
                                }
                                break;
                            case Clicross.InstanceMemberType.SInt2:
                                {
                                    List<Vector2i> datas = new List<Vector2i>();
                                    datas.Resize((int)_InstanceCount);

                                    for (int i = 0; i < _InstanceCount; i++)
                                    {
                                        int* intDataPtr = (int*)byteDataPtr;

                                        var x = *intDataPtr++;
                                        var y = *intDataPtr++;
                                        datas[i] = new Vector2i(x, y);

                                        byteDataPtr += stride;
                                    }

                                    instanceMemberData.Data = datas;
                                }
                                break;
                            case Clicross.InstanceMemberType.SInt3:
                                {
                                    List<Vector3i> datas = new List<Vector3i>();
                                    datas.Resize((int)_InstanceCount);

                                    for (int i = 0; i < _InstanceCount; i++)
                                    {
                                        int* intDataPtr = (int*)byteDataPtr;

                                        var x = *intDataPtr++;
                                        var y = *intDataPtr++;
                                        var z = *intDataPtr++;
                                        datas[i] = new Vector3i(x, y, z);

                                        byteDataPtr += stride;
                                    }

                                    instanceMemberData.Data = datas;
                                }
                                break;
                            case Clicross.InstanceMemberType.SInt4:
                                {
                                    List<Vector4i> datas = new List<Vector4i>();
                                    datas.Resize((int)_InstanceCount);

                                    for (int i = 0; i < _InstanceCount; i++)
                                    {
                                        int* intDataPtr = (int*)byteDataPtr;

                                        var x = *intDataPtr++;
                                        var y = *intDataPtr++;
                                        var z = *intDataPtr++;
                                        var w = *intDataPtr++;
                                        datas[i] = new Vector4i(x, y, z, w);

                                        byteDataPtr += stride;
                                    }

                                    instanceMemberData.Data = datas;
                                }
                                break;
                        }

                        AddInstanceMemberData(instanceMemberData);
                    }
                }
            }
        }

        void AddInstanceMemberData(InstanceMemberData instanceMemberData)
        {
            for (int i = 0; i < _InstanceMemberDatas.Count; i++)
            {
                if (_InstanceMemberDatas[i].Name == instanceMemberData.Name)
                {
                    _InstanceMemberDatas[i] = instanceMemberData;
                    return;
                }
            }

            _InstanceMemberDatas.Add(instanceMemberData);
        }

        void SaveDataToResource()
        {
            if (_InstanceDataResource != null)
            {
                _InstanceDataResource.SetInstanceCount(_InstanceCount);
                _InstanceDataResource.ClearAllInstanceDatas();

                if (_InstanceCount > 0)
                {
                    // Save InstanceTransformData
                    {
                        float[] translationData = new float[_InstanceCount * 3];
                        float[] rotationData = new float[_InstanceCount * 3];
                        float[] scaleData = new float[_InstanceCount * 3];

                        for (int elementIndex = 0; elementIndex < _InstanceCount; elementIndex++)
                        {
                            translationData[elementIndex * 3] = _InstanceTransformData[elementIndex].Translation.x;
                            translationData[elementIndex * 3 + 1] = _InstanceTransformData[elementIndex].Translation.y;
                            translationData[elementIndex * 3 + 2] = _InstanceTransformData[elementIndex].Translation.z;

                            rotationData[elementIndex * 3] = _InstanceTransformData[elementIndex].Rotation.x;
                            rotationData[elementIndex * 3 + 1] = _InstanceTransformData[elementIndex].Rotation.y;
                            rotationData[elementIndex * 3 + 2] = _InstanceTransformData[elementIndex].Rotation.z;

                            scaleData[elementIndex * 3] = _InstanceTransformData[elementIndex].Scale.x;
                            scaleData[elementIndex * 3 + 1] = _InstanceTransformData[elementIndex].Scale.y;
                            scaleData[elementIndex * 3 + 2] = _InstanceTransformData[elementIndex].Scale.z;
                        }

                        uint dataSize = _InstanceCount * 12u;

                        unsafe
                        {
                            fixed (float* p1 = translationData, p2 = rotationData, p3 = scaleData)
                            {
                                _InstanceDataResource.SetInstanceMemberData("Translation", (uint)Clicross.InstanceMemberType.Float3, (IntPtr)p1, dataSize, 12u);
                                _InstanceDataResource.SetInstanceMemberData("Rotation", (uint)Clicross.InstanceMemberType.Float3, (IntPtr)p2, dataSize, 12u);
                                _InstanceDataResource.SetInstanceMemberData("Scale", (uint)Clicross.InstanceMemberType.Float3, (IntPtr)p3, dataSize, 12u);
                            }
                        }
                    }

                    // Save InstanceMemberDatas
                    foreach (InstanceMemberData instanceMemberData in _InstanceMemberDatas)
                    {
                        float[] dstDataFloat;
                        uint[] dstDataUInt;
                        int[] dstDataInt;

                        uint dataStride = 0u;

                        var elementType = instanceMemberData.GetDataElementType();
                        if (elementType == typeof(float))
                        {
                            var dataList = (List<float>)instanceMemberData.Data;
                            dstDataFloat = dataList.ToArray();

                            dataStride = 4u;
                        }
                        else if (elementType == typeof(Float2))
                        {
                            var dataList = (List<Float2>)instanceMemberData.Data;
                            dstDataFloat = new float[_InstanceCount * 2];
                            for (int elementIndex = 0; elementIndex < _InstanceCount; elementIndex++)
                            {
                                dstDataFloat[elementIndex * 2] = dataList[elementIndex].x;
                                dstDataFloat[elementIndex * 2 + 1] = dataList[elementIndex].y;
                            }

                            dataStride = 8u;
                        }
                        else if (elementType == typeof(Float3))
                        {
                            var dataList = (List<Float3>)instanceMemberData.Data;
                            dstDataFloat = new float[_InstanceCount * 3];
                            for (int elementIndex = 0; elementIndex < _InstanceCount; elementIndex++)
                            {
                                dstDataFloat[elementIndex * 3] = dataList[elementIndex].x;
                                dstDataFloat[elementIndex * 3 + 1] = dataList[elementIndex].y;
                                dstDataFloat[elementIndex * 3 + 2] = dataList[elementIndex].z;
                            }

                            dataStride = 12u;
                        }
                        else if (elementType == typeof(Float4))
                        {
                            var dataList = (List<Float4>)instanceMemberData.Data;
                            dstDataFloat = new float[_InstanceCount * 4];
                            for (int elementIndex = 0; elementIndex < _InstanceCount; elementIndex++)
                            {
                                dstDataFloat[elementIndex * 4] = dataList[elementIndex].x;
                                dstDataFloat[elementIndex * 4 + 1] = dataList[elementIndex].y;
                                dstDataFloat[elementIndex * 4 + 2] = dataList[elementIndex].z;
                                dstDataFloat[elementIndex * 4 + 3] = dataList[elementIndex].w;
                            }

                            dataStride = 16u;
                        }
                        else if (elementType == typeof(uint))
                        {
                            var dataList = (List<uint>)instanceMemberData.Data;
                            dstDataUInt = dataList.ToArray();

                            dataStride = 4u;
                        }
                        else if (elementType == typeof(Vector2ui))
                        {
                            var dataList = (List<Vector2ui>)instanceMemberData.Data;
                            dstDataUInt = new uint[_InstanceCount * 2];
                            for (int elementIndex = 0; elementIndex < _InstanceCount; elementIndex++)
                            {
                                dstDataUInt[elementIndex * 2] = dataList[elementIndex].X;
                                dstDataUInt[elementIndex * 2 + 1] = dataList[elementIndex].Y;
                            }

                            dataStride = 8u;
                        }
                        else if (elementType == typeof(Vector3ui))
                        {
                            var dataList = (List<Vector3ui>)instanceMemberData.Data;
                            dstDataUInt = new uint[_InstanceCount * 3];
                            for (int elementIndex = 0; elementIndex < _InstanceCount; elementIndex++)
                            {
                                dstDataUInt[elementIndex * 3] = dataList[elementIndex].X;
                                dstDataUInt[elementIndex * 3 + 1] = dataList[elementIndex].Y;
                                dstDataUInt[elementIndex * 3 + 2] = dataList[elementIndex].Z;
                            }

                            dataStride = 12u;
                        }
                        else if (elementType == typeof(Vector4ui))
                        {
                            var dataList = (List<Vector4ui>)instanceMemberData.Data;
                            dstDataUInt = new uint[_InstanceCount * 4];
                            for (int elementIndex = 0; elementIndex < _InstanceCount; elementIndex++)
                            {
                                dstDataUInt[elementIndex * 4] = dataList[elementIndex].X;
                                dstDataUInt[elementIndex * 4 + 1] = dataList[elementIndex].Y;
                                dstDataUInt[elementIndex * 4 + 2] = dataList[elementIndex].Z;
                                dstDataUInt[elementIndex * 4 + 3] = dataList[elementIndex].W;
                            }

                            dataStride = 16u;
                        }
                        else if (elementType == typeof(int))
                        {
                            var dataList = (List<int>)instanceMemberData.Data;
                            dstDataInt = dataList.ToArray();

                            dataStride = 4u;
                        }
                        else if (elementType == typeof(Vector2i))
                        {
                            var dataList = (List<Vector2i>)instanceMemberData.Data;
                            dstDataInt = new int[_InstanceCount * 2];
                            for (int elementIndex = 0; elementIndex < _InstanceCount; elementIndex++)
                            {
                                dstDataInt[elementIndex * 2] = dataList[elementIndex].X;
                                dstDataInt[elementIndex * 2 + 1] = dataList[elementIndex].Y;
                            }

                            dataStride = 8u;
                        }
                        else if (elementType == typeof(Vector3i))
                        {
                            var dataList = (List<Vector3i>)instanceMemberData.Data;
                            dstDataInt = new int[_InstanceCount * 3];
                            for (int elementIndex = 0; elementIndex < _InstanceCount; elementIndex++)
                            {
                                dstDataInt[elementIndex * 3] = dataList[elementIndex].X;
                                dstDataInt[elementIndex * 3 + 1] = dataList[elementIndex].Y;
                                dstDataInt[elementIndex * 3 + 2] = dataList[elementIndex].Z;
                            }

                            dataStride = 12u;
                        }
                        else if (elementType == typeof(Vector4i))
                        {
                            var dataList = (List<Vector4i>)instanceMemberData.Data;
                            dstDataInt = new int[_InstanceCount * 4];
                            for (int elementIndex = 0; elementIndex < _InstanceCount; elementIndex++)
                            {
                                dstDataInt[elementIndex * 4] = dataList[elementIndex].X;
                                dstDataInt[elementIndex * 4 + 1] = dataList[elementIndex].Y;
                                dstDataInt[elementIndex * 4 + 2] = dataList[elementIndex].Z;
                                dstDataInt[elementIndex * 4 + 3] = dataList[elementIndex].W;
                            }

                            dataStride = 16u;
                        }

                        uint dataSize = _InstanceCount * dataStride;

                        unsafe
                        {
                            fixed (float* p = dstDataFloat)
                            {
                                if (p != null)
                                {
                                    IntPtr ptr = (IntPtr)p;
                                    _InstanceDataResource.SetInstanceMemberData(instanceMemberData.Name, (uint)instanceMemberData.MemberType, ptr, dataSize, dataStride);
                                }
                            }

                            fixed (uint* p = dstDataUInt)
                            {
                                if (p != null)
                                {
                                    IntPtr ptr = (IntPtr)p;
                                    _InstanceDataResource.SetInstanceMemberData(instanceMemberData.Name, (uint)instanceMemberData.MemberType, ptr, dataSize, dataStride);
                                }
                            }

                            fixed (int* p = dstDataInt)
                            {
                                if (p != null)
                                {
                                    IntPtr ptr = (IntPtr)p;
                                    _InstanceDataResource.SetInstanceMemberData(instanceMemberData.Name, (uint)instanceMemberData.MemberType, ptr, dataSize, dataStride);
                                }
                            }
                        }
                    }
                }

                _InstanceDataResource.MarkDirty();
            }
        }
    }

    [ComponentAttribute(DisplayUINames = "Mesh/InstancedStaticModel Component", NeedRenderProperty = true)]
    class InstancedStaticModelComponent : Component
    {
        static string[] _NativeNames = { "cross::InstancedStaticModelComponentG", "cross::AABBComponentG" };

        InstancedStaticModel _Model;
        InstanceData _InstanceData = new InstanceData();
        protected EntityDistanceCulling mEntityDistanceCulling = new EntityDistanceCulling();
        float _GlobalScale = 1.0f;
        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public void NotifyInstanceDataResourceChange()
        {
            InstancedStaticModelSystemG.SetInstanceDataResourcePath(Entity.World.GetNativePointer(), Entity.EntityID, _InstanceData.InstanceDataResourcePath);
        }

        static int _ComponentOrder = 2;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        static int _GroupOrder = 1;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        public override void Reset()
        {
            base.Reset();
            _Model = new InstancedStaticModel();
            _InstanceData = new InstanceData();
        }

        public override void SyncDataFromEngine()
        {
            _Model = GetModelFromEngineSide();
            UpdateInstanceDataFromEngineSide();
        }

        public InstancedStaticModel GetModel()
        {
            return _Model;
        }

        public void UpdateEngineSideModel()
        {
            // when model path is empty, set to default static model path
            if (!_Model.IsModelValid())
            {
                _Model.ModelPath = InstancedStaticModel.StaticMeshDefault();
            }

            InstancedStaticModelSystemG.SetModelAssetPath(Entity.World.GetNativePointer(), Entity.EntityID, _Model.ModelPath);
            InstancedStaticModelSystemG.SetModelVisible(Entity.World.GetNativePointer(), Entity.EntityID, _Model.Visible);
            InstancedStaticModelSystemG.SetModelReceiveDecals(Entity.World.GetNativePointer(), Entity.EntityID, _Model.ReceiveDecals);

            UpdateEngineSideSubModelProperties();
            _Model._NeedLoadChannels = false;
            _Model._ModelAssetDirty = false;
        }

        void UpdateEngineSideSubModelProperties()
        {
			int lodCount = (int)InstancedStaticModelSystemG.GetModelAssetLODCount(Entity.World.GetNativePointer(), Entity.EntityID);
            _Model.LODProperties.ResizeWithNew(lodCount);

			foreach (var (lod, lodIndex) in _Model.LODProperties.Select((lod, index) => (lod, index)))
			{
				var SubMeshCount = (int)InstancedStaticModelSystemG.GetSubModelCount(Entity.World.GetNativePointer(), Entity.EntityID, (uint)lodIndex);
				lod.SubModels.ResizeWithNew(SubMeshCount);

				foreach (var (submesh, submeshIndex) in lod.SubModels.Select((submesh, index) => (submesh, index)))
				{
					InstancedStaticModelSystemG.SetModelMaterialPath(Entity.World.GetNativePointer(), Entity.EntityID, submesh.MaterialPath, submeshIndex, (int)lodIndex);
					InstancedStaticModelSystemG.SetSubModelVisible(Entity.World.GetNativePointer(), Entity.EntityID, submesh.Visible, (uint)lodIndex, submeshIndex);
				}
			}
		}

        //public void SetDefaultMaterial()
        //{
        //    uint lodCount = InstancedStaticModelSystemG.GetModelAssetLODCount(Entity.World.GetNativePointer(), Entity.EntityID);
        //    for (int i = 0; i < lodCount; i++)
        //    {
        //        InstancedStaticModelSystemG.SetModelMaterialPath(Entity.World.GetNativePointer(), Entity.EntityID, SubModelProperty.MaterialDefault(), i, -1);
        //    }
        //}

        InstancedStaticModel GetModelFromEngineSide()
        {
            // model path and visibility
            string ModelPath = InstancedStaticModelSystemG.GetModelAssetPath(Entity.World.GetNativePointer(), Entity.EntityID);
            bool ModelVisible = InstancedStaticModelSystemG.IsModelVisible(Entity.World.GetNativePointer(), Entity.EntityID);
            bool ModelReceiveDecals = InstancedStaticModelSystemG.GetModelReceiveDecals(Entity.World.GetNativePointer(), Entity.EntityID);

            InstancedStaticModel model = new InstancedStaticModel(ModelPath);
            model.Visible = ModelVisible;
            model.ReceiveDecals = ModelReceiveDecals;

            // sub mesh material path and visibility
            uint modellodcount = InstancedStaticModelSystemG.GetModelAssetLODCount(Entity.World.GetNativePointer(), Entity.EntityID);
			List<LODProperty> SubModelProperties = new List<LODProperty>();
			for (uint lodindex = 0; lodindex < modellodcount; lodindex++)
			{
				SubModelProperties.Add(new LODProperty());
				uint SubMeshCount = InstancedStaticModelSystemG.GetSubModelCount(Entity.World.GetNativePointer(), Entity.EntityID, lodindex);
				for (uint SubModelPropertyIndex = 0; SubModelPropertyIndex < SubMeshCount; SubModelPropertyIndex++)
				{
					string MaterialPath;
					MaterialPath = InstancedStaticModelSystemG.GetModelMaterialPath(Entity.World.GetNativePointer(), Entity.EntityID, (uint)SubModelPropertyIndex, lodindex);
					if (MaterialPath.ToString().Length == 0)
					{
						SubModelProperties[(int)lodindex].SubModels.Add(new SubModelProperty());
					}
					else
					{
						SubModelProperties[(int)lodindex].SubModels.Add(new SubModelProperty(MaterialPath.ToString()));
					}

					bool SubMeshVisible = InstancedStaticModelSystemG.IsSubModelVisible(Entity.World.GetNativePointer(), Entity.EntityID, lodindex, SubModelPropertyIndex);

					SubModelProperties[(int)lodindex].SubModels[(int)SubModelPropertyIndex].MaterialPath = MaterialPath;
				}
			}
            model.LODProperties = SubModelProperties;
            return model;
        }

        void UpdateInstanceDataFromEngineSide()
        {
            _InstanceData.InstanceDataResourcePath = InstancedStaticModelSystemG.GetInstanceDataResourcePath(Entity.World.GetNativePointer(), Entity.EntityID);
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable this mesh renderer.")]
        public override bool Enable
        {
            get { return _Enable; }
            set
            {
                _Enable = value;
            }
        }

        [PropertyInfo(PropertyType = "Struct", ToolTips = "Models that this mesh renderer uses.")]
        public InstancedStaticModel Model
        {
            get
            {
                return _Model;
            }
            set
            {
                _Model = value;

                UpdateEngineSideModel();

                Entity.SetDirty();
            }
        }

        [PropertyInfo(PropertyType = "Auto")]
        public InstanceData InstanceDatas
        {
            get
            {
                return _InstanceData;
            }

            set
            {
                _InstanceData = value;

                NotifyInstanceDataResourceChange();
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Instance Scale")]
        public float GlobalScale
        {
            get
            {
                _GlobalScale = InstancedStaticModelSystemG.GetGlobalScale(Entity.World.GetNativePointer(), Entity.EntityID);
                return _GlobalScale;
            }
            set
            {
                _GlobalScale = value;
                InstancedStaticModelSystemG.SetGlobalScale(Entity.World.GetNativePointer(), Entity.EntityID, value,true);
            }
        }
        [PropertyInfo(PropertyType = "Struct", ToolTips = "Entity distance culling")]
        public EntityDistanceCulling DistanceCulling
        {
            get
            {
                mEntityDistanceCulling = InstancedStaticModelSystemG.GetModelEnityDistanceCulling(Entity.World.GetNativePointer(), Entity.EntityID);
                return mEntityDistanceCulling;
            }
            set
            {
                mEntityDistanceCulling = value;
                InstancedStaticModelSystemG.SetModelEnityDistanceCulling(Entity.World.GetNativePointer(), Entity.EntityID, mEntityDistanceCulling);
            }
        }
    }
}
