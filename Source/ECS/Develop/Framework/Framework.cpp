#include "EnginePrefix.h"
#include "CrossBase/CrossConstant.h"
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/Framework/Types.h"
// core interface & type interface
namespace cross::ecs
{
#if ENABLE_GUARDED_COMPONENT_ACCESS_CHECKS
	std::atomic_uint64_t DataRaceGuard::sNumInstances;
#endif

    TypeInfo* AllocateTypeInfo(UInt32 size, UInt32 alignment, std::string name, UInt32 nameHash, LifeCycleFunction functions, UInt32 fieldCount)
    {
        auto const fieldInfoSize = sizeof(FieldInfo) * fieldCount;
        auto const allocateSize = sizeof(TypeInfo) + sizeof(FieldInfo) * fieldCount;
        auto* typeInfo = reinterpret_cast<TypeInfo*>(cross::Memory::Malloc(allocateSize));
        new (typeInfo) TypeInfo{size, alignment, std::move(name), {{nameHash, 0}}, functions, 0, fieldCount, {}};
        if (fieldInfoSize > 0)
        {
            memset(typeInfo->Fields, 0, fieldInfoSize);
        }
        return typeInfo;
    }

    void FreeTypeInfo(TypeInfo* typeInfo)
    {
        if (typeInfo)
        {
            typeInfo->~TypeInfo();
            cross::Memory::Free(typeInfo);
        }
    }

    Chunk* AllocateChunk()
    {
        // allocate memory
        auto* chunk = reinterpret_cast<Chunk*>(cross::Memory::Malloc(Chunk::ChunkSize));
        std::memset(chunk, 0, Chunk::ChunkSize);
        return chunk;
    }

    void FreeChunk(Chunk* chunk)
    {
		if (chunk)
		{ 
			cross::Memory::Free(chunk);
		}
    }

    void ClearChunk(Chunk* chunk)
    {
        if (!chunk)
        {
            return;
        }

        auto* prototype = chunk->Type;
        for (ComponentIndex componenetIndex{ 0 }; componenetIndex < prototype->ComponentCount; ++componenetIndex)
        {
            auto const& componentInfo = prototype->Components[componenetIndex];
            auto destructor = componentInfo.Desc->LifeFunction.Dtor;
            if (destructor)
            {
                auto* data = GetComponentPtrFromChunk(chunk, componenetIndex, EntityOffset{ 0 });
                DEBUG_ASSERT(data);
                for (UInt32 elementIndex = 0; elementIndex < chunk->ElementCount; ++elementIndex)
                {
                    destructor(data);
                    data += componentInfo.Desc->Size;
                }
            }
        }

#if ENABLE_GUARDED_COMPONENT_ACCESS
		{
			auto guard = GetDataRaceGuardPtr(chunk, 0, 0);
			for (UInt32 i = 0; i != chunk->ElementCount * prototype->ComponentCount; i++)
			{
				ResetDataRaceGuardPtr(guard++);
			}
		}
#endif

        chunk->ElementCount = 0;
    }

    UInt8* GetComponentPtrFromChunk(Chunk const* chunk, ComponentIndex const componentIndex, EntityOffset const ettOffset)
    {
        DEBUG_ASSERT(chunk && chunk->Type);
        DEBUG_ASSERT(componentIndex < chunk->Type->ComponentCount);
        DEBUG_ASSERT(ettOffset < chunk->Type->CapacityInChunk);
        DEBUG_ASSERT(chunk->ElementCount <= chunk->Type->CapacityInChunk);
        auto& compInfo = chunk->Type->Components[componentIndex];
        if (compInfo.Desc->Size == 0)
        {
            // Tag component
            return nullptr;
        }
        return const_cast<UInt8*>(reinterpret_cast<UInt8 const*>(chunk) + compInfo.Offset + compInfo.Desc->Size * ettOffset);
    }

    UInt8* GetComponentPtrFromChunk(Chunk* chunk, ComponentIndex const componentIndex, EntityOffset const ettOffset)
    {
        return const_cast<UInt8*>(GetComponentPtrFromChunk(const_cast<Chunk const*>(chunk), componentIndex, ettOffset));
    }

    size_t GetComponentIndex(Prototype const* prototype, ComponentDesc const* type)
    {
        DEBUG_ASSERT(prototype && type);
        if (!prototype || !type)
        {
            return Prototype::NPos;
        }
        for (size_t loop = 0; loop < prototype->ComponentCount; ++loop)
        {
            auto const& dataInfo = prototype->Components[loop];
            if (dataInfo.Desc == type)
            {
                return loop;
            }
        }
        return Prototype::NPos;
    }

	AtomicDataRaceGuardPtr* GetDataRaceGuardPtr(Chunk* chunk, UInt32 entityChunkIndex, UInt32 componentIndex)
	{
#if ENABLE_GUARDED_COMPONENT_ACCESS
		Assert(chunk && chunk->Type);
		Assert(entityChunkIndex == 0 || entityChunkIndex < chunk->ElementCount);
		Assert(componentIndex == 0 || componentIndex < chunk->Type->ComponentCount);

		return reinterpret_cast<AtomicDataRaceGuardPtr*>(reinterpret_cast<UInt8*>(chunk) + chunk->Type->GuardOffset + (entityChunkIndex * chunk->Type->ComponentCount + componentIndex) * sizeof(AtomicDataRaceGuardPtr));
#else
		return nullptr;
#endif
	}

	void ResetDataRaceGuardPtr(void* ptr)
	{
#if ENABLE_GUARDED_COMPONENT_ACCESS
		auto guard = static_cast<AtomicDataRaceGuardPtr*>(ptr)->Load();
		if (auto payload = guard.mState.mPayload)
		{
			DataRaceGuard::Destroy(payload);
			std::memset(ptr, 0, sizeof(AtomicDataRaceGuardPtr));
		}
#endif
	}
}

// prototype interface
namespace cross::ecs
{
	ecs::ComponentDesc* EntityIDComponent::GetDesc()
	{
		return EngineGlobal::GetECSFramework().GetIDComponentDesc();
	}

	ecs::ComponentDesc* EntityMetaComponentG::GetDesc()
	{
		return EngineGlobal::GetECSFramework().GetMetaComponentDesc();
	}

    void ChunkDeleter::operator()(Chunk* chunk) const
    {
        if (chunk)
        {
            ClearChunk(chunk);
            FreeChunk(chunk);
        }
    }

    DataTable::DataTable(PrototypePtr ptr, EntityIndexMap* array):mPrototype(ptr), mEntityIndexMap(array)
    {
    }

    Chunk* DataTable::RequestChunk(BlockID blockId)
    {
		if (!mChunks.empty())
		{
			auto& backChunk = mChunks.back();
			DEBUG_ASSERT(backChunk->ElementCount <= mPrototype->CapacityInChunk);
			if (backChunk->ElementCount < mPrototype->CapacityInChunk)
			{
				return backChunk.get();
			}
		}
        // allocate a new one and initialize
        auto* chunk = AllocateChunk();
        chunk->Type = mPrototype;
        chunk->BlockId = blockId;
        chunk->ElementCount = 0;
        chunk->ChunkNumber = static_cast<UInt32>(mChunks.size());

        EntityOffset zero{ 0 };
		if (mPrototype->IDComponentIndex > -1)
		{
			ComponentIndex idx{ (UInt16)mPrototype->IDComponentIndex };
			chunk->IDComponentArray = GetComponentPtrFromChunk(chunk, idx, zero);
		}

		if (mPrototype->MetaComponentIndex > -1)
		{
			ComponentIndex idx{ (UInt16)mPrototype->MetaComponentIndex };
			chunk->MeteComponentArray = GetComponentPtrFromChunk(chunk, idx, zero);
		}
		
		chunk->mPolicy = ChunkStoragePolicy::Tight;
        
        const UInt32 capacity = mPrototype->CapacityInChunk;
        if (capacity > BitArray::BitsPerBlock)
        {
            UInt8 count = ToUInt8(capacity / BitArray::BitsPerBlock + (capacity % BitArray::BitsPerBlock ? 1 : 0));
            BitArray::Block* blockArrayStart = reinterpret_cast<BitArray::Block*>(reinterpret_cast<UInt8*>(chunk) + sizeof(Chunk)); // Allocated here
            for (size_t i = 0; i < chunk->mEntityMask.size(); ++i)
            {
                chunk->mEntityMask[i] = BitArray(blockArrayStart + i * count, count);
            }
        }
        else
        {
            for (size_t i = 0; i < chunk->mEntityMask.size(); ++i)
            {
                chunk->mEntityMask[i] = BitArray(); // No default construction for chunk's member
            }
        }

        // register chunk
		mLastTightChunk = (UInt32)mChunks.size();
        mChunks.emplace_back(chunk);
        return chunk;
    }

    void DataTable::ReleaseUnusedChunk()
    {
        if (!mChunks.empty())
        {
            auto& firstAvaliableChunk = mChunks[mLastTightChunk];
            if (firstAvaliableChunk->ElementCount == 0)
            {
                mChunks.pop_back();	
				mLastTightChunk--;
            }
        }
    }

    EntityID DataTable::Remove(EntityHandle entityHandle)
    {
        EntityID result;

        auto entityIndex = GetEntityIndex(entityHandle);

        if (entityIndex.mChunk && entityIndex.mOffset != InvalidEntityOffset)
        {
            DEBUG_ASSERT(mChunks.size() > 0);
            DEBUG_ASSERT(entityIndex.mChunk && entityIndex.mChunk->ElementCount > entityIndex.mOffset);
            DEBUG_ASSERT(entityIndex.mChunk->Type == mPrototype);
            DEBUG_ASSERT(entityIndex.mChunk->ChunkNumber < mChunks.size());

            //back-swap-erase
            auto backChunk = mChunks[mLastTightChunk].get();
            DEBUG_ASSERT(backChunk->ElementCount > 0);                   // empty chunk check
            auto const backEntityIndex = backChunk->ElementCount - 1;

            // the entity handle component index
            ComponentIndex entityIDComponentIndex{ 0 };
            for (UInt32 i = 0; i < entityIndex.mChunk->Type->ComponentCount; i++)
            {
                if (entityIndex.mChunk->Type->Components[i].Desc == EntityIDComponent::GetDesc())
                {
                    entityIDComponentIndex = { (UInt16)i };
                    break;
                }
            }

            // get back entity pointer for the EntityHandleComponent
            auto const backEntityHandlePtr = reinterpret_cast<EntityIDComponent*>(
                GetComponentPtrFromChunk(backChunk, entityIDComponentIndex, backEntityIndex));

            auto backhandle = GetEntityHandle(backEntityHandlePtr->mEntityID);
            // back-swap-erase for non-back element
            if (backChunk != entityIndex.mChunk || backEntityIndex != entityIndex.mOffset)
            {
                DEBUG_ASSERT(backEntityHandlePtr);

                // update the index map in the prototype instance
                mEntityIndexMap->at(entityHandle) = EntityIndex();
                (*mEntityIndexMap)[backhandle] = entityIndex;

                // do the back-swap-erase procedure for each component
                for (ComponentIndex loop{ 0 }; loop < mPrototype->ComponentCount; ++loop)
                {
                    // calculate the component data pointer to erase
                    auto removeComponentPtr = GetComponentPtrFromChunk(entityIndex.mChunk, loop, entityIndex.mOffset);
                    DEBUG_ASSERT(removeComponentPtr);

                    // calculate the last component data pointer to swap
                    auto backComponentPtr = GetComponentPtrFromChunk(backChunk, loop, backEntityIndex);
                    DEBUG_ASSERT(backComponentPtr);

                    auto& lifeFunc = mPrototype->Components[loop].Desc->LifeFunction;
                    lifeFunc.Move(removeComponentPtr, backComponentPtr);

                    if (lifeFunc.Dtor)
                        lifeFunc.Dtor(backComponentPtr);
                }

                for (UInt32 i = 0; i < MaskNum - 1; i++)
                {
                    (entityIndex.mChunk->mEntityMask)[i].SetBit(entityIndex.mOffset, (backChunk->mEntityMask)[i].GetBit(backEntityIndex));
                    (backChunk->mEntityMask)[i].SetBit(backEntityIndex, 0);
                }

                result = backEntityHandlePtr->mEntityID;
            }
            //One chunk also need to update handle index map or we are deleting just the back element
            else
            {
                mEntityIndexMap->at(entityHandle) = EntityIndex();

                // destruct back element
                for (ComponentIndex loop{ 0 }; loop < mPrototype->ComponentCount; ++loop)
                {
                    auto& lifeFunc = mPrototype->Components[loop].Desc->LifeFunction;
                    if (lifeFunc.Dtor)
                    {
                        auto backComponentPtr = GetComponentPtrFromChunk(backChunk, loop, backEntityIndex);
                        lifeFunc.Dtor(backComponentPtr);
                    }
                }

                for (UInt32 i = 0; i < MaskNum - 1; i++)
                {
                    (backChunk->mEntityMask)[i].SetBit(entityIndex.mOffset, 0);
                }
            }
            mEntityCount--;

#if ENABLE_GUARDED_COMPONENT_ACCESS
            {
                auto guard = GetDataRaceGuardPtr(backChunk, backEntityIndex, 0);
                for (UInt32 i = 0; i != mPrototype->ComponentCount; i++)
                {
                    ResetDataRaceGuardPtr(guard++);
                }
            }
#endif

            // update the last chunk
            backChunk->ElementCount = backEntityIndex;

            ReleaseUnusedChunk();
        }

        //This is he back entity which is swapped, if any
        return result;
    }

    void DataTable::RemoveWithNoBackSwap(Chunk * chunk, EntityOffset chunkIndex)
	{
		DEBUG_ASSERT(mChunks.size() > 0);
		DEBUG_ASSERT(chunk);
        DEBUG_ASSERT(chunk->Type == mPrototype);
		DEBUG_ASSERT(chunk->ChunkNumber < mChunks.size());


		// the entity handle component index
		ComponentIndex entityIDComponentIndex{ 0 };
		for (UInt32 i = 0; i < chunk->Type->ComponentCount; i++)
		{
			if (chunk->Type->Components[i].Desc == EntityIDComponent::GetDesc())
			{
				entityIDComponentIndex = { (UInt16)i };
				break;
			}
		}
		// get the back pointer for the EntityIDComponent
		auto const entityHandlePtr = reinterpret_cast<EntityIDComponent*>(GetComponentPtrFromChunk(chunk, entityIDComponentIndex,chunkIndex));
		
		// update the index map in the prototype instance
		DEBUG_ASSERT(entityHandlePtr);
		auto removehandle = GetEntityHandle(entityHandlePtr->mEntityID);
        mEntityIndexMap->at(removehandle).Reset(); // remove
		mEntityCount--;
		///Dtor the chunk data
		for (ComponentIndex loop{ 0 }; loop < mPrototype->ComponentCount; ++loop)
		{
			auto& compInfo = mPrototype->Components[loop];
			auto destructor = compInfo.Desc->LifeFunction.Dtor;
			if (destructor)
			{
				auto componentptr = GetComponentPtrFromChunk(chunk, loop, chunkIndex);
				destructor(componentptr);
			}
		}

#if ENABLE_GUARDED_COMPONENT_ACCESS
		{
			auto guard = GetDataRaceGuardPtr(chunk, chunkIndex, 0);
			for (UInt32 i = 0; i != mPrototype->ComponentCount; i++)
			{
				ResetDataRaceGuardPtr(guard++);
			}
		}
#endif

        chunk->ElementCount -= 1;
		///Swap the chunk if not full
		if ((chunk->ChunkNumber < (mChunks.size() -1)) && (chunk->ElementCount < mChunks.back()->ElementCount))
		{
			std::swap(mChunks.back(), mChunks[chunk->ChunkNumber]);	
			UInt32 tmp = chunk->ChunkNumber;
			mChunks[tmp]->ChunkNumber = tmp;
			chunk->ChunkNumber = (UInt32)(mChunks.size() - 1);
		}

        (chunk->mEntityMask)[ToUnderlying(RuntimeMaskType::ProtoTypeHolelist)].SetBit(chunkIndex, true);
        ReleaseUnusedChunk();
	}

    ComponentInfo& DataTable::GetComponentInfo(ComponentIndex compIdx)
    {
        DEBUG_ASSERT(compIdx < mPrototype->ComponentCount);
        return mPrototype->Components[compIdx];
    }

    ComponentInfo const& DataTable::GetComponentInfo(ComponentIndex compIdx) const
    {
        DEBUG_ASSERT(compIdx < mPrototype->ComponentCount);
        return mPrototype->Components[compIdx];
    }

    void DataTable::RecordEntity(EntityHandle entityHandle, Chunk* chunk)
    {
        DEBUG_ASSERT(!IsEmptyPrototype());
        DEBUG_ASSERT(chunk);
        DEBUG_ASSERT(entityHandle != EntityHandle::InvalidHandle());
        //UInt32 const Capacity = mPrototype->CapacityInChunk;
        //UInt32 const ChunkNumber = chunk->ChunkNumber;
        EntityIndex ettIndex;
        if (chunk->mPolicy == ChunkStoragePolicy::FreeList)
        {
            auto& holelist = (chunk->mEntityMask)[(size_t)RuntimeMaskType::ProtoTypeHolelist];
            auto holeIndex = holelist.Findfirstbit(true);
            ettIndex.mChunk = chunk;
            if (holeIndex > 0)
            {
                holelist.SetBit(holeIndex, false);
                ettIndex.mOffset = holeIndex;
            }
            else
            {
                ettIndex.mOffset = chunk->ElementCount;
            }
        }
        else
        {
            ettIndex.mChunk = chunk;
            ettIndex.mOffset = chunk->ElementCount;
        }

        if (mEntityIndexMap->size() <= entityHandle)
            mEntityIndexMap->resize(entityHandle + 1000);

        mEntityIndexMap->at(entityHandle) = ettIndex;

        mEntityCount++;
        chunk->ElementCount++;
    }

    bool DataTable::GetEntityMask(EntityHandle handle, RuntimeMaskType type) noexcept
    {
        auto ettIndex = GetEntityIndex(handle);
        return (ettIndex.mChunk->mEntityMask)[static_cast<int>(type)].GetBit(ettIndex.mOffset);
    }

    void DataTable::SetEntityMask(bool value, EntityHandle handle, RuntimeMaskType type)
    {
        auto ettIndex = GetEntityIndex(handle);
        (ettIndex.mChunk->mEntityMask)[static_cast<int>(type)].SetBit(ettIndex.mOffset, value);
    }


	ecs::ComponentDesc* Framework::GetGameComponentDescByIndex(UInt32 maskBitIndex)
	{
		if (maskBitIndex == 0)
		{
			return &mIDComponentDesc->Desc;
		}
		else if (maskBitIndex <= mGameComponentDescs.size())
		{
			return &mGameComponentDescs[maskBitIndex - 1].Desc;
		}
		else
		{
			return nullptr;
		}
	}

	ecs::ComponentDesc* Framework::GetRenderComponentDescByIndex(UInt32 maskBitIndex)
	{
		if (maskBitIndex == 0)
		{
			return &mIDComponentDesc->Desc;
		}
		else if (maskBitIndex <= mRenderComponentDescs.size())
		{
			return &mRenderComponentDescs[maskBitIndex - 1].Desc;
		}
		else
		{
			return nullptr;
		}
	}

	ecs::ComponentDesc* Framework::GetIDComponentDesc()
	{
		static ecs::ComponentDesc* sDesc{ nullptr };
		if (!sDesc)
		{
			sDesc = GetOrCreateComponentDesc<EntityIDComponent>(true, true, true);
            sDesc->Init({ false, true, false });
		}
		return sDesc;
	}

	ecs::ComponentDesc* Framework::GetMetaComponentDesc()
	{
		static ecs::ComponentDesc* sDesc{ nullptr };
		if (!sDesc)
		{
			sDesc = GetOrCreateComponentDesc<EntityMetaComponentG>(true, false, true);
            sDesc->Init({ false, true, false });
		}
		return sDesc;
	}

	void Framework::Init()
    {
		mGameComponentDescs.reserve(ECSConst::sMaxComponentCount);
		mRenderComponentDescs.reserve(ECSConst::sMaxComponentCount);

		EntityIDComponent::GetDesc();
		EntityMetaComponentG::GetDesc();

        mPrototypes.Init(mCompDescMap,*this);
    }

	
}
