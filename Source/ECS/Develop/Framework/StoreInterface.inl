#pragma once
#include "ECS/Develop/Framework/Types.h"

namespace cross::ecs 
{
inline void AddComponentData(ComponentIndex const compIdx, Chunk* chunk)
{
    DEBUG_ASSERT(chunk && chunk->Type);
    auto prototype = chunk->Type;
    DEBUG_ASSERT(compIdx.mVal < prototype->ComponentCount);
    auto typeInfo = prototype->Components[compIdx].Desc;
    DEBUG_ASSERT(typeInfo);

    EntityOffset entityIndexOffset;
    if (chunk->mPolicy == ChunkStoragePolicy::FreeList)
    {
        auto& holeList = (chunk->mEntityMask)[ToUnderlying(RuntimeMaskType::ProtoTypeHolelist)];
        auto holeIndex = holeList.Findfirstbit(true);

        if (holeIndex > 0)
        {
            entityIndexOffset = holeIndex;
        }
        else
        {
            entityIndexOffset = chunk->ElementCount;
        }
    }
    else
    {
        entityIndexOffset = chunk->ElementCount;
    }
    auto componentDataPtr = GetComponentPtrFromChunk(chunk, compIdx, entityIndexOffset);
    if (componentDataPtr)
    {
        auto ctor = typeInfo->LifeFunction.Ctor;
        if (ctor)
        {
            ctor(componentDataPtr);
        }
    }
}

template<typename StoreType> 
inline void Store<StoreType>::BeginFrame(FrameParam* fp)
{
    if constexpr (IsGameStore::value)
    {
        mGameIDGen->BeginFrame(fp);
    }
}

template<typename StoreType> 
inline void Store<StoreType>::EndFrame(FrameParam* fp)
{
    if constexpr (IsGameStore::value)
    {
        mGameIDGen->EndFrame();
    }
}

// template<typename StoreType> template<typename... Components, typename> 
// inline auto Store<StoreType>::CreateComponents(EntityID entity)
// {
//     using TupleType = std::tuple<Components...>;
// 
//     constexpr UInt32 Counts = sizeof...(Components);
//     if constexpr (Counts == 1)
//     {
//         auto resulthandle = AddComponent<std::tuple_element_t<0, TupleType>>(entity);
//         return resulthandle;
//     }
//     else
//     {
//         auto resulttuple = AddComponent<Components...>(entity);
//         return resulttuple;
//     }
// }

// template<typename StoreType> template<typename... Components, typename> 
// inline auto Store<StoreType>::AddComponent(EntityID entity)
// {
//     using TupleType = std::tuple<Components...>;
//     constexpr UInt32 Counts = sizeof...(Components);
// 
//     if constexpr (IsGameStore::value)
//     {
//         if constexpr (Counts == 1)
//         {
//             auto AllEmptyResult = TGetComponentTypeT<std::tuple_element_t<0, TupleType>>::InvalidHandle();
//             if (!mGameIDGen->Alive(entity))
//             {
//                 LOG_ERROR("ECS::Entity not alive!");
//                 Assert(false);
//                 return AllEmptyResult;
//             }
//         }
//         else
//         {
//             using ResultType = std::tuple<TGetComponentTypeT<Components>...>;
//             constexpr ResultType AllEmptyResult = {TGetComponentTypeT<Components>::InvalidHandle()...};
//             if (!mGameIDGen->Alive(entity))
//             {
//                 LOG_ERROR("ECS::Entity not alive!");
//                 Assert(false);
//                 return AllEmptyResult;
//             }
//         }
//     }
//     else
//     {
//         DEBUG_ASSERT(threading::TaskSystem::IsInRenderingThread());
//     }
//     auto entityHandle = entity.GetHandle();
//     DEBUG_ASSERT(entityHandle != EntityHandle::InvalidHandle());
// 
//     PrototypePtr curType = GetSafePrototype(entityHandle);
// 
//     ComponentBitMask mergedMask = curType->ComponentMask;
//     std::array<SInt32, sizeof...(Components)> maskIndexes = {Components::GetDesc()->GetMaskBitIndex()...};
//     std::for_each(maskIndexes.begin(), maskIndexes.end(), [&mergedMask](UInt32 index) { mergedMask.Set(index, true); });
// 
//     UInt32 curDataTypeIndex = GetDataTableIndexByExistedPrototype(curType->Hash);
//     UInt32 newDataTypeIndex = GetDataTableIndexForNewPrototype(mergedMask);
// 
//     if (curDataTypeIndex != newDataTypeIndex)
//     {
//         TransferEntityOnPrototypeChanged(entity, mChunkGroups[curDataTypeIndex], mChunkGroups[newDataTypeIndex]);
//     }
// 
//     return GetComponent<Components...>(entity);
// }

template<typename StoreType> 
inline bool Store<StoreType>::HasComponent(EntityID entity, const ComponentDesc* meta) const
{
    // check alive
    if (!CheckEntityStatusOnRead(entity)) return false;

    auto entityHandle = GetEntityHandle(entity);
    DEBUG_ASSERT(entityHandle != EntityHandle::InvalidHandle());

    // get current prototype
    PrototypePtr prototype = GetPrototype(entityHandle);
    if (!prototype)
    {
        LOG_ERROR("ECS::Prototype not valid!");
        return false;
    }
    return PrototypeRegistry::PrototypeHasComponent(prototype, meta);
}

template<typename StoreType> 
inline bool Store<StoreType>::HasComponent(EntityID entity, const ComponentBitMask& compBitMask) const
{
    if (!CheckEntityStatusOnRead(entity)) return false;

    auto entityHandle = GetEntityHandle(entity);
    DEBUG_ASSERT(entityHandle != EntityHandle::InvalidHandle());

    // get current prototype
    PrototypePtr prototype = GetPrototype(entityHandle);
    if (!prototype)
    {
        LOG_ERROR("ECS::Prototype not valid!");
        return false;
    }

    auto mask = prototype->ComponentMask & compBitMask;
    return (mask == compBitMask);
}

template<typename StoreType> template<typename... Components, typename> 
inline auto Store<StoreType>::HasComponent(EntityID entity) const
{
    constexpr size_t ComponentSize = sizeof...(Components);
    using TupleType = std::tuple<Components...>;

    bool isAlive = (IsGameStore::value) ? mGameIDGen->Alive(entity) : mRenderIDGen->Alive(entity);

    if constexpr (ComponentSize == 1)
    {
        if (!isAlive)
        {
            LOG_ERROR("ECS::Entity not alive!");
            return false;
        }

        if (!CheckEntityStatusOnRead(entity)) return false;

        // get entity handle
        auto entityHandle = GetEntityHandle(entity);
        DEBUG_ASSERT(entityHandle != EntityHandle::InvalidHandle());

        // get current prototype
        PrototypePtr prototype = GetPrototype(entityHandle);
        if (!prototype)
        {
            LOG_ERROR("ECS::Prototype not valid!");
            return false;
        }
        // has check internal
        return PrototypeRegistry::PrototypeHasComponent<std::tuple_element_t<0, TupleType>>(prototype);
    }
    else
    {
        using ResultType = std::bitset<ComponentSize>;
        constexpr ResultType AllFalseResult{};

        if (!isAlive)
        {
            LOG_ERROR("ECS::Entity not alive!");
            return AllFalseResult;
        }

        if (!CheckEntityStatusOnRead(entity)) return AllFalseResult;

        // get entity handle
        auto entityHandle = GetEntityHandle(entity);
        PrototypePtr prototype = GetPrototype(entityHandle);
        if (!prototype)
        {
            LOG_ERROR("ECS::Prototype not valid!");
            return AllFalseResult;
        }

        // check for each component
        ResultType result;
        HashComponentData<std::tuple<Components...>>(result, prototype, std::make_index_sequence<ComponentSize>{});
        return result;
    }
}

template<typename StoreType> template<typename... Components, typename> 
inline auto Store<StoreType>::GetComponent(EntityID entity) const
{
    using TupleType = std::tuple<Components...>;
    constexpr size_t Count = sizeof...(Components);

    bool isAlive = (IsGameStore::value) ? mGameIDGen->Alive(entity) : mRenderIDGen->Alive(entity);
    if (!isAlive)
    {
        LOG_ERROR("ECS::Entity not alive");
        Assert(false);

        if constexpr (Count == 1)
        {
            return TGetComponentTypeT<std::tuple_element_t<0, TupleType>>::InvalidHandle();
        }
        else
        {
            using ResultType = std::tuple<TGetComponentTypeT<Components>...>;
            constexpr ResultType AllEmptyResult = { TGetComponentTypeT<Components>::InvalidHandle()... };
           return AllEmptyResult;
        }
    }


    // get entity handle
    if constexpr (Count == 1)
    {
        if (!CheckEntityStatusOnRead(entity)) return TGetComponentTypeT<std::tuple_element_t<0, TupleType>>::InvalidHandle();
        return GetComponentImp<Components...>(entity);
    }
    else
    {
        using ResultType = std::tuple<TGetComponentTypeT<Components>...>;
        constexpr ResultType AllEmptyResult = {TGetComponentTypeT<Components>::InvalidHandle()...};

        if (!CheckEntityStatusOnRead(entity)) return AllEmptyResult;
        return GetComponentImp<Components...>(entity);
    }
}

template<typename StoreType> template<typename... Components, typename> 
inline auto Store<StoreType>::GetComponentImp(EntityID entity) const
{
    using TupleType = std::tuple<Components...>;
    constexpr size_t Count = sizeof...(Components);
    // access the data

    auto entityHandle = GetEntityHandle(entity);
    auto [chunk, index] = GetEntityIndex(entityHandle);
    if constexpr (Count == 1)
    {
        if (chunk == nullptr || index == EntityHandle::InvalidHandle())
        {
            return TGetComponentTypeT<std::tuple_element_t<0, TupleType>>::InvalidHandle();
        }
        return GetComponentData<std::tuple_element_t<0, TupleType>>(chunk, index);
    }
    else
    {
        using ResultType = std::tuple<TGetComponentTypeT<Components>...>;
        constexpr ResultType AllEmptyResult = {TGetComponentTypeT<Components>::InvalidHandle()...};
        if (chunk == nullptr || index == EntityHandle::InvalidHandle())
        {
            return AllEmptyResult;
        }
        return std::make_tuple(GetComponentData<Components>(chunk, index)...);
    }
}

// template<typename StoreType> template<typename... Components, typename> 
// inline bool Store<StoreType>::SetComponent(EntityID entity, Components&&... components)
// {
//     // check alive
//     if constexpr (IsGameStore::value)
//     {
//         if (!IsEntityAlive(entity))
//         {
//             LOG_ERROR("ECS::Entity not alive");
//             return false;
//         }
//     }
//     else
//     {
//         DEBUG_ASSERT(threading::TaskSystem::IsInRenderingThread());
//     }
//     // get entity handle
//     auto entityHandle = GetEntityHandle(entity);
//     DEBUG_ASSERT(entityHandle != EntityHandle::InvalidHandle());
// 
//     // remove all component first
//     auto result = RemoveAllComponents(entity);
//     if (!result)
//     {
//         LOG_ERROR("ECS::Remove failed");
//         return result;
//     }
// 
//     // then add the new component
//     result = AddComponent(entity, std::forward<Components>(components)...);
//     return result;
// }

template<typename StoreType> template<typename... Components> 
inline auto Store<StoreType>::Query(BlockID blockId) const
{
    // the result
    detail::TQueryResult<0, Components...> result(GetCurrentFrameAllocator(), mStage);

    if (blockId == sInvalidBlockStoreId)
    {
        for (const auto& blockStore : mBlockStores)
        {
            AssertMsg(blockStore.second.get(), "Block store must be valid.");
            blockStore.second->template Query<Components...>(result);
        }
    }
    else if (auto it = mBlockStores.find(blockId); it != mBlockStores.end())
    {
        AssertMsg(it->second.get(), "Block store must be valid.");
        it->second->template Query<Components...>(result);
    }

    // return the query result
    return result;
}

template<typename... Components>
void BlockStore::Query(detail::TQueryResult<0, Components...>& outResult) const
{
    // count of component
    constexpr size_t Count = sizeof...(Components);

    // search for all prototype instances
    for (auto& table : mChunkGroups)
    {
        // skip the hole array index
        if (table.IsEmptyPrototype() || table.EntityEmpty())
        {
            continue;
        }
        auto resultMask = outResult.RequiredMatcher && table.GetPrototype()->ComponentMask;

        if (resultMask == outResult.RequiredMatcher)
        {
            std::array<ComponentInfo*, Count> dataInfo = { GetComponentDataInfo<Components>(&table)... };
            outResult.AddPrototype(&table, dataInfo);
        }
    }
}

template<typename StoreType> template<typename... Components> 
inline auto Store<StoreType>::QueryWithMask(RuntimeMaskType type, BlockID blockId) const
{
    
    // the result
    detail::TQueryResult<1, Components...> result(GetCurrentFrameAllocator(), mStage);
    result.mMask = type;

    if (blockId == sInvalidBlockStoreId)
    {
        for (const auto& blockStore : mBlockStores)
        {
            AssertMsg(blockStore.second.get(), "Block store must be valid.");
            blockStore.second->template QueryWithMask<Components...>(type, result);
        }
    }
    else if (auto it = mBlockStores.find(blockId); it != mBlockStores.end())
    {
        AssertMsg(it->second.get(), "Block store must be valid.");
        it->second->template QueryWithMask<Components...>(type, result);
    }

    // return the query result
    return result;
}

template<typename... Components>
void BlockStore::QueryWithMask(RuntimeMaskType type, detail::TQueryResult<1, Components...>& outResult) const
{
    // count of component
    constexpr size_t Count = sizeof...(Components);

    // search for all prototype instances
    for (auto& table : mChunkGroups)
    {
        // skip the hole array index
        if (table.IsEmptyPrototype() || table.EntityEmpty())
        {
            continue;
        }
        auto resultMask = outResult.RequiredMatcher && table.GetPrototype()->ComponentMask;

        if (resultMask == outResult.RequiredMatcher)
        {
            std::array<ComponentInfo*, Count> dataInfo = { GetComponentDataInfo<Components>(&table)... };
            outResult.AddPrototype(&table, dataInfo);
        }
    }
}

// template<typename StoreType> template<typename Tuple, size_t... Indices> 
// inline void Store<StoreType>::AddComponentsData(std::array<UInt16, sizeof...(Indices)> const& order, Chunk* chunk, Tuple&& tuple, std::index_sequence<Indices...>)
// {
//     (AddComponentData(ComponentIndex{order[Indices]}, chunk, std::get<Indices>(std::forward<Tuple>(tuple))), ...);
// }

// template<typename StoreType> template<typename ComponentsTuple, size_t... Indices> 
// inline ComponentsTuple Store<StoreType>::AddComponentsDataAndReturn(EntityID entity, Chunk* chunk, std::index_sequence<Indices...>)
// {
//     return ComponentsTuple{AddComponentDataAndReturn<typename std::tuple_element_t<Indices, ComponentsTuple>::HandledType>(entity, chunk)...};
// }

template<typename StoreType> template<typename ComponentsTuple, size_t... Indices> 
inline void Store<StoreType>::HashComponentData(std::bitset<sizeof...(Indices)>& result, PrototypePtr const& prototype, std::index_sequence<Indices...>) const
{
    (result.set(Indices, PrototypeRegistry::PrototypeHasComponent<std::tuple_element_t<Indices, ComponentsTuple>>(prototype)), ...);
}

template<typename Component> 
ComponentInfo* BlockStore::GetComponentDataInfo(const DataTable* table)
{
    if (!table)
    {
        return nullptr;
    }
    return PrototypeRegistry::ProtyotypeGetComponentInfo<Component>(table->GetPrototype());
}

inline ecs::EntityID BlockStore::TransferEntity(EntityID entity, DataTable& srcDataTable, DataTable& destDataTable, BlockID destStoreBlockId)
{
    EntityID movedEntity;
    
    if (srcDataTable.GetIndex() == destDataTable.GetIndex())
        return movedEntity;

    auto entityHandle = GetEntityHandle(entity);

    if (destDataTable.IsEmptyPrototype())
    {
        movedEntity = srcDataTable.Remove(entityHandle);
    }
    else
    {
        // cache the prototype pointers
        auto& srcPrototype = srcDataTable.GetPrototype();
        auto& destPrototype = destDataTable.GetPrototype();

        auto destChunk = destDataTable.RequestChunk(destStoreBlockId);
        DEBUG_ASSERT(destChunk);

        // if prototype transfer from the non-type prototype, add a entity handle component object first
        ComponentIndex fromLoop{ 0 }, toLoop{ 0 };
        if (!srcDataTable.IsEmptyPrototype())
        {
            // get the data position
            auto srcEttIdx = srcDataTable.GetEntityIndex(entityHandle);
            DEBUG_ASSERT(srcEttIdx.IsValid());

            for (ComponentIndex destCompIdx{ 0 }; destCompIdx < destPrototype->ComponentCount; destCompIdx++)
            {
                const auto& destCompInfo = destPrototype->Components[destCompIdx];
                if (srcPrototype->ComponentMask[destCompInfo.Desc->GetMaskBitIndex()])
                {
                    ComponentBitMask merged = srcPrototype->ComponentMask & ComponentBitMask(0, destCompInfo.Desc->GetMaskBitIndex());
                    ComponentIndex srcCompIndex{ static_cast<UInt16>(merged.GetPopCount() - 1) };
                    MoveComponentData(srcEttIdx.mChunk, srcCompIndex, srcEttIdx.mOffset, destChunk, destCompIdx);
                }
                else
                {
                    AddComponentData(destCompIdx, destChunk);
                }
            }

            for (UInt32 i = 0; i < MaskNum - 1; i++)
            {
                (destChunk->mEntityMask)[i].SetBit(destChunk->ElementCount, (srcEttIdx.mChunk->mEntityMask)[i].GetBit(srcEttIdx.mOffset));
            }

            movedEntity = srcDataTable.Remove(entityHandle);
        }
        else
        {
            Assert(destPrototype->IDComponentIndex > -1);
            for (ComponentIndex idx{ 0 }; idx < destPrototype->ComponentCount; idx++)
            {
                if (idx == destPrototype->IDComponentIndex)
                {
                    AddComponentData(idx, destChunk, EntityIDComponent{ {}, entity});
                }
                else
                {
                    AddComponentData(idx, destChunk);
                }
            }
        }

        destDataTable.RecordEntity(entityHandle, destChunk);
    }

    return movedEntity;
}

template<typename StoreType>
inline void Store<StoreType>::TransferEntityOnPrototypeChanged(EntityID entity, BlockStore* blockStore, UInt32 srcDataTableIndex, UInt32 destDataTableIndex)
{
    CheckStatusOnWrite();

    DataTable& srcDataTable = blockStore->GetDataTable(srcDataTableIndex);
    DataTable& destDataTable = blockStore->GetDataTable(destDataTableIndex);
    
    EntityID movedEntity = BlockStore::TransferEntity(entity, srcDataTable, destDataTable, blockStore->GetBlockId());

    if (movedEntity != EntityID())
    {
        UInt32 flag{0};
        EntityMemoryMovedEvent event(movedEntity);
        mMoveEntityEventReceiver->NotifyEvent(event, flag);
    }
}

template<typename StoreType> 
inline void Store<StoreType>::ClearStoreData()
{
    CheckStatusOnWrite();

    mEntityIndexMap.clear();
    for (const auto& blockStore : mBlockStores)
    {
        blockStore.second->Clear();
    }
    mBlockStores.clear();
}

template<typename StoreType>
inline void Store<StoreType>::DeleteBlockStore(BlockID blockId)
{
    CheckStatusOnWrite();
    AssertMsg(blockId != sRuntimeBlockStoreId, "Runtime Block cannot be romoved until the whole store needs to be cleared.");

    auto it = mBlockStores.find(blockId);
    if (it == mBlockStores.end())
        return;
    BlockStore* blockStore = it->second.get();

    // Release ID & write index map
    UInt32 dataTableCount = blockStore->GetDataTableCount();
    for(UInt32 dataTableIndex = 0; dataTableIndex < dataTableCount; ++dataTableIndex)
    {
        DataTable& dataTable = blockStore->GetDataTable(dataTableIndex);
        for(auto& chunk : dataTable.Chunks())
        {
            for (UInt32 i = 0; i < chunk->ElementCount; ++i)
            {
                auto* idComponent = GetIDComponent(chunk.get(), i);
                mEntityIndexMap[GetEntityHandle(idComponent->mEntityID)] = EntityIndex();
                if constexpr (IsGameStore::value) // Block unloading is called by Game World
                {
                    ReleaseEntityID(idComponent->mEntityID);
                }
                else
                {
                    FinishEntityID(idComponent->mEntityID);
                }
            }
        }
    }

    blockStore->Clear();
    mBlockStores.erase(blockId);
    AssertMsg(!mBlockStores.empty(), "BlockStores cannot be empty until the whole store needs to be cleared.");
}

inline void BlockStore::Clear()
{
    mChunkGroups.clear();
}

inline void BlockStore::MoveComponentData(Chunk const* srcChunk, ComponentIndex srcCompIdx, EntityOffset srcEntityOffset, Chunk* destChunk, ComponentIndex destCompIdx, EntityOffset destEntityOffset)
{
    // check input parameter for source side of copy
    DEBUG_ASSERT(srcChunk && srcChunk->Type);
    DEBUG_ASSERT(srcCompIdx < srcChunk->Type->ComponentCount);
    DEBUG_ASSERT(srcEntityOffset < srcChunk->ElementCount);

    // check input parameter for destination size of copy
    DEBUG_ASSERT(destChunk && destChunk->Type);
    DEBUG_ASSERT(destCompIdx < destChunk->Type->ComponentCount);

    // get component information
    auto& srcComponentInfo = srcChunk->Type->Components[srcCompIdx];
    [[maybe_unused]] auto& dstComponentInfo = destChunk->Type->Components[destCompIdx];

    // check runtime type safety
    DEBUG_ASSERT(srcComponentInfo.Hash == dstComponentInfo.Hash);
    if (srcComponentInfo.Desc->Size == 0)
    {
        // Tag Component
        return;
    }

    // calculate chunk index for destination part
    auto destEntityOfs = destEntityOffset == InvalidEntityOffset ? destChunk->ElementCount : destEntityOffset;
    if (destChunk->mPolicy == ChunkStoragePolicy::FreeList)
    {
        auto& holelist = (destChunk->mEntityMask)[(size_t)RuntimeMaskType::ProtoTypeHolelist];
        auto holeIndex = holelist.Findfirstbit(true);
        destEntityOfs = holeIndex > 0 ? holeIndex : destChunk->ElementCount;
    }

    // get the desired data pointer
    auto dstComponentDataPtr = GetComponentPtrFromChunk(destChunk, destCompIdx, destEntityOfs);
    auto srcComponentDataPtr = GetComponentPtrFromChunk(srcChunk, srcCompIdx, srcEntityOffset);

    // do the copy
    auto& lifeFunction = srcComponentInfo.Desc->LifeFunction;

    if (lifeFunction.Ctor)
    {
        lifeFunction.Ctor(dstComponentDataPtr);
    }

    if (lifeFunction.Move)
    {
        lifeFunction.Move(dstComponentDataPtr, srcComponentDataPtr);
    }
}

template<typename StoreType> 
bool Store<StoreType>::RemoveAllComponents(EntityID entity)
{
    if (!CheckEntityStatusOnWrite(entity)) return false;

    // get entity handle
    const auto entityHandle = GetEntityHandle(entity);
    DEBUG_ASSERT(entityHandle != EntityHandle::InvalidHandle());

    Chunk* chunk = GetChunk(entityHandle);
    if (!chunk)
    {
        LOG_ERROR("ECS::No data components!");
        return false;
    }
    const PrototypePtr prototype = chunk->Type;
    BlockStore* blockStore = mBlockStores.at(chunk->BlockId).get();
    const UInt32 prototypeTableIndex = blockStore->GetDataTableIndexByExistedPrototype(prototype);
    TransferEntityOnPrototypeChanged(entity, blockStore, prototypeTableIndex, blockStore->sEmptyPrototypeChunkGroupIndex);

    return true;
}

template<typename StoreType>
bool Store<StoreType>::MoveEntityToRuntimeBlockStore(ecs::EntityID entity)
{
    if (!CheckEntityStatusOnWrite(entity)) return false;
    const auto entityHandle = GetEntityHandle(entity);
    DEBUG_ASSERT(entityHandle != EntityHandle::InvalidHandle());

    Chunk* chunk = GetChunk(entityHandle);
    if (!chunk)
    {
        LOG_ERROR("ECS::No data components!");
        return false;
    }
    const PrototypePtr prototype = chunk->Type;
    BlockStore* srcBlockStore = mBlockStores.at(chunk->BlockId).get();
    const UInt32 srcDataTableIndex = srcBlockStore->GetDataTableIndexByExistedPrototype(prototype);

    BlockStore* destBlockStore = mBlockStores.at(sRuntimeBlockStoreId).get();
    const UInt32 destDataTableIndex = destBlockStore->GetDataTableIndexForNewPrototype(prototype);

    ecs::EntityID movedEntity = BlockStore::TransferEntity(entity, srcBlockStore->GetDataTable(srcDataTableIndex), destBlockStore->GetDataTable(destDataTableIndex), sRuntimeBlockStoreId);
    if (movedEntity != EntityID())
    {
        UInt32 flag{ 0 };
        EntityMemoryMovedEvent event(movedEntity);
        mMoveEntityEventReceiver->NotifyEvent(event, flag);
    }

    return true;
}

template<typename StoreType> 
inline PrototypePtr Store<StoreType>::GetPrototype(EntityID entity) const
{
    if (!IsEntityAliveInner(entity)) return nullptr;

    // get entity handle
    auto entityHandle = GetEntityHandle(entity);
    DEBUG_ASSERT(entityHandle != EntityHandle::InvalidHandle());

    // get current prototype
    return GetPrototype(entityHandle);
}

template<typename StoreType> 
ComponentsArrayType Store<StoreType>::GetAllComponentsArray(EntityID entity) const
{
    std::vector<std::pair<ecs::IComponent*, const ComponentDesc*>> components;
    auto entityHandle = GetEntityHandle(entity);

    bool isAlive = (IsGameStore::value) ? mGameIDGen->Alive(entity) : mRenderIDGen->Alive(entity);
    if (!isAlive)
    {
        LOG_ERROR("ECS::Entity not alive!");
        DEBUG_ASSERT(false);
        return components;
    }
    
    if (GetPrototype(entityHandle) == nullptr)
    {
        LOG_ERROR("ECS::No data components");
        return components;
    }

    auto ettIndex = GetEntityIndex(entityHandle);
    for (UInt16 compIdx = 0; compIdx < ettIndex.mChunk->Type->ComponentCount; compIdx++)
    {
        auto* dataptr = reinterpret_cast<ecs::IComponent*>(GetComponentPtrFromChunk(ettIndex.mChunk, {compIdx}, ettIndex.mOffset));
        components.push_back(std::make_pair(dataptr, ettIndex.mChunk->Type->Components[compIdx].Desc));
    }
    return components;
}

template<typename StoreType>
ComponentBitMask Store<StoreType>::AddComponentByPrototype(EntityID entity, PrototypePtr prototype, BlockID targetBlockForEmptyEntity)
{
    if (prototype == mEmptyPrototype)
        return ComponentBitMask();

    if (!CheckEntityStatusOnWrite(entity)) return ComponentBitMask();

    // get entity handle
    const auto entityHandle = GetEntityHandle(entity);
    DEBUG_ASSERT(entityHandle != EntityHandle::InvalidHandle());

    PrototypePtr oldType = GetSafePrototype(entityHandle);

    auto newTypeMask = oldType->ComponentMask;
    newTypeMask |= prototype->ComponentMask;
    if (newTypeMask == oldType->ComponentMask)
        return ComponentBitMask();

    BlockID blockId = targetBlockForEmptyEntity;
    Assert(mBlockStores.find(targetBlockForEmptyEntity) != mBlockStores.end());
    // Entities with empty prototype are treated as runtime entities,
    // because entities in static blocks should add components just when creating by other API
    if (oldType != mEmptyPrototype)
    {
        blockId = GetBlockId(entityHandle);
    }
    BlockStore* blockStore = mBlockStores.at(blockId).get();
    UInt32 curTypeIndex = blockStore->GetDataTableIndexByExistedPrototype(oldType);
    UInt32 newTypeDataIndex = blockStore->GetDataTableIndexForNewPrototype(newTypeMask);

    TransferEntityOnPrototypeChanged(entity, blockStore, curTypeIndex, newTypeDataIndex);

    ComponentBitMask result = newTypeMask & (~oldType->ComponentMask);
    return result;
}

template<typename StoreType>
inline bool Store<StoreType>::RemoveComponentByPrototype(EntityID entity, PrototypePtr prototypeRemoved)
{
    if (!CheckEntityStatusOnWrite(entity)) return false;

    // get entity handle
    auto entityHandle = GetEntityHandle(entity);
    DEBUG_ASSERT(entityHandle != EntityHandle::InvalidHandle());

    if (prototypeRemoved == mEmptyPrototype)
        return true;

    const PrototypePtr srcPrototype = GetSafePrototype(entityHandle);
    if (srcPrototype == mEmptyPrototype)
        return false;

    DEBUG_ASSERT(srcPrototype->IsGameType == IsGameStore::value);
    DEBUG_ASSERT(srcPrototype->IsGameType == prototypeRemoved->IsGameType);

    auto newTypeMask = srcPrototype->ComponentMask;
    auto subMask = ~prototypeRemoved->ComponentMask;
    newTypeMask &= subMask;
    newTypeMask |= mEmptyPrototype->ComponentMask;

    if (newTypeMask == srcPrototype->ComponentMask)
        return true;

    BlockID blockId = GetBlockId(entityHandle);
    BlockStore* blockStore = mBlockStores.at(blockId).get();
    UInt32 curTypeIndex = blockStore->GetDataTableIndexByExistedPrototype(srcPrototype);
    UInt32 newTypeDataIndex = blockStore->GetDataTableIndexForNewPrototype(newTypeMask);

    TransferEntityOnPrototypeChanged(entity, blockStore, curTypeIndex, newTypeDataIndex);

    return true;
}

template<typename StoreType>
detail::QueryResult Store<StoreType>::Query(const PrototypePtr& prototype, BlockID blockId) const
{
    // the result
    detail::QueryResult result(prototype, GetCurrentFrameAllocator(), mStage);
    if (blockId == sInvalidBlockStoreId)
    {
        for (const auto& blockStore : mBlockStores)
        {
            blockStore.second->Query(prototype, result);
        }
    }
    else if(auto it = mBlockStores.find(blockId); it != mBlockStores.end())
    {
        it->second->Query(prototype, result);
    }
    return result;
}

inline void BlockStore::Query(const PrototypePtr& prototype, detail::QueryResult& outResult) const
{
    for (auto& chunkGroup : mChunkGroups)
    {
        // skip the hole array index
        if (chunkGroup.IsEmptyPrototype() || chunkGroup.EntityEmpty())
        {
            continue;
        }
        auto resultmask = outResult.RequiredMatcher && chunkGroup.GetPrototype()->ComponentMask;

        if (resultmask == outResult.RequiredMatcher)
        {
            std::vector<ComponentInfo*> dataInfo;
            ///ignore entity component and meta
            size_t startindex = 2;
            for (size_t i = startindex; i < prototype->ComponentCount; i++)
            {
                auto data = PrototypeRegistry::ProtyotypeGetComponentInfo(chunkGroup.GetPrototype(), prototype->Components[i].Desc);
                dataInfo.push_back(data);

            }
            outResult.AddPrototype(&chunkGroup, dataInfo);
        }
    }
}

template<typename StoreType>
bool Store<StoreType>::CheckStatusOnWrite() const
{
    if constexpr (IsGameStore::value)
    {
        Assert(threading::TaskSystem::IsInGameThread());
    }
    else
    {
        Assert(threading::TaskSystem::IsInRenderingThread());
    }
    return true;
}

template<typename StoreType>
bool Store<StoreType>::CheckEntityStatusOnWrite(ecs::EntityID entity) const
{
    if (!CheckStatusOnWrite()) return false;

    if (!IsEntityAliveInner(entity))
    {
        LOG_ERROR("ECS::Entity not alive");
        return false;
    }

    return true;
}

template<typename StoreType>
bool Store<StoreType>::CheckEntityStatusOnRead(ecs::EntityID entity) const
{
    if (!IsEntityAliveInner(entity))
    {
        LOG_ERROR("ECS::Entity not alive");
        return false;
    }

    return true;
}

}   // namespace cross::ecs
