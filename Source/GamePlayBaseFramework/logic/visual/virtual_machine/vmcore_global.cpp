#include "visual/virtual_machine/vmcore_global.hpp"

#include "visual/virtual_machine/string/cachedstringmanager.h"
#include "visual/virtual_machine/string/conststring.h"
#include "memoryhooker/Module.h"
IMPORT_MODULE

static gbf::machine::CacheStringManager		sCacheStringManager;
gbf::machine::CacheStringManager* GCacheStringManager = &sCacheStringManager;

//declare a static const string member here
gbf::machine::ConstString gbf::machine::ConstString::EMPTY_STRING;



