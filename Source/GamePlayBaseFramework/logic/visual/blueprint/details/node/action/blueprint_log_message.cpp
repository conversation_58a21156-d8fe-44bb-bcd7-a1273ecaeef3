#include "visual/blueprint/details/node/action/blueprint_log_message.h"
// #include "visual/virtual_machine/extend/vmvalue.h"
#include "core/imodules/ilog_module.h"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"

namespace gbf { namespace logic {
    UBlueprintLogMessage::UBlueprintLogMessage()
        : UBlueprintActionNode(BlueprintSlotAvailableFlag::NodeVarIn, "Log Message")
    {
        InitializeSlotsImpl();
    }

    UBlueprintActionNode::ProcessingInfo UBlueprintLogMessage::RtActivateLogicImpl(machine::VCoroutine* coroutine_, UBlueprintExecSlot* slot)
    {
        UBlueprintActionNode::ProcessingInfo info;
        info.State = UBlueprintActionNode::LogicState::Ok;

        INF_DEF("NodeGraphVM: %s \n", RtTryGetDataSlotValue(BlueprintLinkDirection::In, 0, coroutine_->GetCurrentMemoryScope(), coroutine_->GetGlobalMemoryScope(), std::string{}).c_str());

        RtActiveOuputLink(coroutine_, 0);

        return info;
    }

    void UBlueprintLogMessage::InitializeSlotsImpl()
    {
        // int
        AddExecSlot(BlueprintLinkDirection::In, 0, "");
        AddExecSlot(BlueprintLinkDirection::Out, 0, "");
        // out
        AddDataSlot(BlueprintLinkDirection::In, 0, machine::VValueKind::kString, "Message");
    }

    ////SeqNode* ActionNodeLogMessage::copy_impl()
    ////{
    ////	return __gc_new(ActionNodeLogMessage)();
    ////}
}}   // namespace gbf::logic
