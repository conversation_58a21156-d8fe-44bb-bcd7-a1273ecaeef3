#include "visual/blueprint/details/node/variable/blueprint_variable_generic.h"

#include "visual/blueprint/details/blueprint_context.h"
#include "visual/blueprint/details/blueprint_graph_base.h"
#include "visual/blueprint/details/blueprint_graph_group.h"
#include "visual/blueprint/details/blueprint_named_variable.h"
#include "visual/blueprint/details/blueprint_workspace.h"
#include "visual/blueprint/details/node/blueprint_node.h"
#include "visual/blueprint/details/node/blueprint_data_slot.h"

// #include "visual/virtual_machine/extend/vmvalue.h"
#include "visual/virtual_machine/runtime/memory_scope.hpp"
#include "visual/virtual_machine/runtime/named_memory_scope.hpp"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"
#include "visual/virtual_machine/utils/vvalue_util.hpp"
#include "core/utils/string_util.h"
////#include "visual/virtual_machine/string/conststringutil.h"

namespace gbf { namespace logic {
    UBlueprintConstValueNode::UBlueprintConstValueNode()
        : UBlueprintNode(BlueprintNodeType::Variable, BlueprintSlotAvailableFlag::VarOut)
        , m_value_kind(machine::VValueKind::kBoolean)
        , m_value(false)
    {
        InitializeSlotsImpl();
    }

    void UBlueprintConstValueNode::set_value_kind(machine::VValueKind in_value_kind)
    {
        if (int(in_value_kind) > int(machine::VValueKind::kString))
        {
            return;
        }
        m_value_kind = in_value_kind;
        m_value = machine::VValueUtil::CreateDefaultValueByType(in_value_kind);
        InitializeSlotsImpl();
    }
    machine::VValueKind UBlueprintConstValueNode::get_value_kind() const
    {
        return m_value_kind;
    }
    machine::VValue UBlueprintConstValueNode::get_value() const
    {
        return m_value;
    }

    void UBlueprintConstValueNode::set_value(const machine::VValue& new_value)
    {
        if (new_value.kind() == m_value_kind)
        {
            m_value = new_value;
        }
        else if (new_value.kind() == gbf::reflection::ValueKind::kInteger && m_value_kind == gbf::reflection::ValueKind::kReal)
        {
            m_value = gbf::reflection::make_value<double>(gbf::reflection::value_ref_as<std::int64_t>(new_value));
        }
        m_title = std::string(reflection::detail::ValueKindAsString(m_value_kind)) + "(" + machine::VValueUtil::ValueToJsonStr(m_value) + ")";
    }

    void UBlueprintConstValueNode::InitializeSlotsImpl()
    {
        m_in_variable_slots.clear();
        m_in_instruction_slots.clear();
        m_out_variable_slots.clear();
        m_out_instruction_slots.clear();
        m_slot_available_flag = BlueprintSlotAvailableFlag::VarOut;
        auto out_slot_name = "OutValue";
        AddDataSlot(BlueprintLinkDirection::Out, 0, m_value_kind, out_slot_name);

        auto slot = this->GetDataSlotOut(0);
        assert(slot);
        slot->SetDefaultValueAsAny(m_value);
        slot->set_name(out_slot_name);

        m_title = std::string(reflection::detail::ValueKindAsString(m_value_kind)) + "(" + machine::VValueUtil::ValueToJsonStr(m_value) + ")";
    }

    void UBlueprintConstValueNode::SerializeToJson(machine::IVMStreamWriter& writer)
    {
        base::SerializeToJson(writer);
        writer.AddIntProperty("value_kind", int(m_value_kind));
        writer.StartObject("value");
        machine::VValueUtil::SaveVValueToJsonMap(m_value, writer);
        writer.EndObject();
    }

    void UBlueprintConstValueNode::DeserializeFields(machine::IVMStreamReadNode& node)
    {
        UBlueprintNode::DeserializeFields(node);
        m_value_kind = (machine::VValueKind)node.GetIntProperty("value_kind");
        auto temp_node = node.GetNode("value");
        m_value = machine::VValueUtil::CreateVValueFromJsonMap(*temp_node);
        InitializeSlotsImpl();

    }

    void UBlueprintConstValueNode::get_editor_fields(std::vector<std::pair<std::string, machine::VValue>>& result)
    {
        base::get_editor_fields(result);
        result.push_back(std::make_pair(std::string("value"), m_value));
    }

    bool UBlueprintConstValueNode::update_editor_field(const std::string& field_name, const machine::VValue& field_value)
    {
        if (base::update_editor_field(field_name, field_value))
        {
            return true;
        }
        if (field_name == "value")
        {
            set_value(field_value);
            return true;
        }
        return false;
    }

    machine::VValuePtr UBlueprintConstValueNode::RtPullResult(UBlueprintDataSlot* slot, machine::MemoryScope* local, machine::NamedMemoryScope* global)
    {
        return std::make_shared<machine::VValue>(m_value);
    }

    UBlueprintSetNamedVariableNode::UBlueprintSetNamedVariableNode()
        : UBlueprintActionNode(BlueprintSlotAvailableFlag::All)
    {

    }

    void UBlueprintSetNamedVariableNode::set_variable(const std::string& variable_name)
    {
        assert(!variable_name.empty());
        m_variable_name = variable_name;
        m_title = StringUtil::Format("Set %s", m_variable_name.data());
        InitializeSlotsImpl();
    }

    UBlueprintActionNode::ProcessingInfo UBlueprintSetNamedVariableNode::RtActivateLogicImpl(machine::VCoroutine* coroutine_, UBlueprintExecSlot*)
    {
        ProcessingInfo result = {LogicState::Error, ""};

        auto local = coroutine_->GetCurrentMemoryScope();
        auto global = coroutine_->GetGlobalMemoryScope();

        auto value = RtGetDataSlotValue(BlueprintLinkDirection::In, 0, local, global);
        assert(value);
        if (!value)
        {
            result.ErrorMessage = "Invalid input value!";
            return result;
        }

        auto global_value = global->QueryValue(m_variable_name);
        if (global_value)
        {
            *global_value = *value;
        }
        else
        {
            result.ErrorMessage = "Invalid named variable!";
            return result;
        }
        

        // before get output trigger variable change event
        // TODO ... error handling and interrupt
        auto& context = coroutine_->GetContextObject().Ref<UBlueprintContext>();
        context.OnGlobalEvent(UBlueprintNamedVariable::get_event_name(m_variable_name), *global_value);
        context.Update();

        RtActiveOuputLink(coroutine_, 0);
        result.State = LogicState::Ok;
        return result;
    }

    void UBlueprintSetNamedVariableNode::InitializeSlotsImpl()
    {
        assert(!m_variable_name.empty());

        m_in_variable_slots.clear();
        m_in_instruction_slots.clear();
        m_out_variable_slots.clear();
        m_out_instruction_slots.clear();
        auto value = GetParentGraph()->GetGroup()->GetNamedVariableByName(m_variable_name);
        gbf::machine::VValueKind value_type = gbf::machine::VValueKind::kNone;
        gbf::reflection::TypeId value_type_id = UINT64_MAX;
        gbf::machine::VValue value_default = gbf::machine::VValue::nothing;
        if (value)
        {
            value_type = value->get_value_type();
            value_type_id = value->get_type_id();
        }
        
        m_slot_available_flag = BlueprintSlotAvailableFlag::All;

        // instructions
        AddExecSlot(BlueprintLinkDirection::In, 0, "");
        AddExecSlot(BlueprintLinkDirection::Out, 0, "");

        // variable input and output
        AddDataSlot(BlueprintLinkDirection::In, 0, value_type, "In", value_type_id);
        AddDataSlot(BlueprintLinkDirection::Out, 0, value_type, "Out", value_type_id);

        // init slot variable
        auto slot = this->GetDataSlotIn(0);
        assert(slot);
        slot->SetDefaultValueAsAny(value_default);
        slot->set_name(m_variable_name);
    }

    machine::VValuePtr UBlueprintSetNamedVariableNode::RtPullResult(UBlueprintDataSlot*, machine::MemoryScope*, machine::NamedMemoryScope* global)
    {
        assert(!m_variable_name.empty() && global);
        return global->QueryValue(m_variable_name);
    }

    void UBlueprintSetNamedVariableNode::on_variable_type_changed(const std::string& var_name)
    {
        auto value = GetParentGraph()->GetGroup()->GetNamedVariableByName(var_name);
        gbf::machine::VValueKind value_type = gbf::machine::VValueKind::kNone;
        gbf::reflection::TypeId value_type_id = UINT64_MAX;
        gbf::machine::VValue value_default = gbf::machine::VValue::nothing;
        if (value)
        {
            value_type = value->get_value_type();
            value_type_id = value->get_type_id();

            if (value_type_id == UINT64_MAX)
            {
                assert(value_type <= machine::VValueKind::kString);
                value_type_id = machine::VValueUtil::ValueKindToTypeId(value_type);
            }

            GetDataSlot(BlueprintLinkDirection::Out, 0)->set_variable_typeid(value_type_id);
            GetDataSlot(BlueprintLinkDirection::In, 0)->set_variable_typeid(value_type_id);
        }
    }

    void UBlueprintSetNamedVariableNode::SerializeToJson(machine::IVMStreamWriter& writer)
    {
        writer.AddStringProperty("variable_name", m_variable_name);
        base::SerializeToJson(writer);
    }

    void UBlueprintSetNamedVariableNode::DeserializeFields(machine::IVMStreamReadNode& node)
    {
        base::DeserializeFields(node);
        auto name = node.GetStringProperty("variable_name");
        set_variable(name);
        
    }

    UBlueprintGetNamedVariableNode::UBlueprintGetNamedVariableNode()
        : UBlueprintNode(BlueprintNodeType::Variable, BlueprintSlotAvailableFlag::VarOut)
    {

    }

    void UBlueprintGetNamedVariableNode::set_variable(const std::string& variable_name)
    {
        assert(!variable_name.empty());
        m_variable_name = variable_name;
        m_title = StringUtil::Format("Get %s", m_variable_name.data());
        InitializeSlotsImpl();
    }

    machine::VValuePtr UBlueprintGetNamedVariableNode::RtPullResult(UBlueprintDataSlot*, machine::MemoryScope*, machine::NamedMemoryScope* global)
    {
        assert(!m_variable_name.empty() && global);
        return global->QueryValue(m_variable_name);
    }

    void UBlueprintGetNamedVariableNode::InitializeSlotsImpl()
    {
        assert(!m_variable_name.empty());

        m_in_variable_slots.clear();
        m_in_instruction_slots.clear();
        m_out_variable_slots.clear();
        m_out_instruction_slots.clear();

        m_slot_available_flag = BlueprintSlotAvailableFlag::VarOut;
        auto value = GetParentGraph()->GetGroup()->GetNamedVariableByName(m_variable_name);
        gbf::machine::VValueKind value_type = gbf::machine::VValueKind::kNone;
        gbf::reflection::TypeId value_type_id = UINT64_MAX;
        gbf::machine::VValue value_default = gbf::machine::VValue::nothing;
        if (value)
        {
            value_type = value->get_value_type();
            value_type_id = value->get_type_id();
        }
        AddDataSlot(BlueprintLinkDirection::Out, 0, value_type, "Out", value_type_id);

        auto slot = this->GetDataSlotOut(0);
        assert(slot);
        slot->SetDefaultValueAsAny(value_default);
        slot->set_name(m_variable_name);
    }

    void UBlueprintSetNamedVariableNode::on_variable_name_changed(const std::string& old_name, const std::string& new_name)
    {
        m_variable_name = new_name;
        m_title = StringUtil::Format("Set %s", m_variable_name.data());

        auto slot = this->GetDataSlotIn(0);
        slot->set_name(m_variable_name);
    }

    void UBlueprintGetNamedVariableNode::on_variable_name_changed(const std::string& old_name, const std::string& new_name)
    {
        m_variable_name = new_name;
        m_title = StringUtil::Format("Get %s", m_variable_name.data());

        auto slot = this->GetDataSlotOut(0);
        slot->set_name(m_variable_name);
    }

    void UBlueprintGetNamedVariableNode::SerializeToJson(machine::IVMStreamWriter& writer)
    {
        base::SerializeToJson(writer);
        writer.AddStringProperty("variable_name", m_variable_name);
        writer.AddBoolProperty("editor_visible", mbVisible);
    }

    void UBlueprintGetNamedVariableNode::on_variable_type_changed(const std::string& var_name)
    {
        auto value = GetParentGraph()->GetGroup()->GetNamedVariableByName(var_name);
        gbf::machine::VValueKind value_type = gbf::machine::VValueKind::kNone;
        gbf::reflection::TypeId value_type_id = UINT64_MAX;
        gbf::machine::VValue value_default = gbf::machine::VValue::nothing;
        if (value)
        {
            value_type = value->get_value_type();
            value_type_id = value->get_type_id();

            if (value_type_id == UINT64_MAX)
            {
                assert(value_type <= machine::VValueKind::kString);
                value_type_id = machine::VValueUtil::ValueKindToTypeId(value_type);
            }

            GetDataSlot(BlueprintLinkDirection::Out, 0)->set_variable_typeid(value_type_id);
        }
    }

    void UBlueprintGetNamedVariableNode::DeserializeFields(machine::IVMStreamReadNode& node)
    {
        base::DeserializeFields(node);
        auto name = node.GetStringProperty("variable_name");
        set_variable(name);
        if (node.HasProperty("editor_visible"))
        {
            auto visible = node.GetBoolProperty("editor_visible");
            set_visible_for_editor(visible);
        }
        
    }

}}   // namespace gbf::logic

