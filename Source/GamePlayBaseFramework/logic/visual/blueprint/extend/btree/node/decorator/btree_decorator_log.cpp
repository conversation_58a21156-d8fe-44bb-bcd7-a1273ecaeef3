#include "visual/blueprint/extend/btree/node/decorator/btree_decorator_log.h"
#include "core/imodules/ilog_module.h"

namespace gbf {
namespace logic {
UBtreeDecoratorLog::UBtreeDecoratorLog() { m_title = "DecoratorLog"; }

UBtreeDecoratorLog::~UBtreeDecoratorLog() {}

machine::VMRunStepStatus UBtreeDecoratorLog::Decorate(machine::VCoroutine* coroutine_, const machine::VObject& obj, machine::VMRunStepStatus self_status,
                                                  machine::VMRunStepStatus last_child_status) {
  INF_DEF("BTDecoratorLog: %s\n", m_log_message.c_str());
  return self_status;
}

void UBtreeDecoratorLog::Serial<PERSON><PERSON><PERSON><PERSON><PERSON>(machine::IVMStreamWriter& writer) {
  base::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(writer);
  writer.AddStringProperty("log_message", m_log_message);
}
void UBtreeDecoratorLog::DeserializeFrom<PERSON><PERSON>(machine::IVMStreamReadNode& node) {
  base::Deserialize<PERSON><PERSON><PERSON><PERSON>(node);
  m_log_message = node.GetStringProperty("log_message");
}
}  // namespace logic
}  // namespace gbf
