#include "visual/blueprint/extend/btree/btree_connection.h"
#include "visual/blueprint/extend/btree/btree_graph.h"
#include "visual/blueprint/extend/btree/btree_slot.h"

namespace gbf {
namespace logic {
UBtreeConnection::UBtreeConnection(UBtreeSlot* out_slot, UBtreeSlot* in_slot) : m_out_slot(out_slot), m_in_slot(in_slot) {
  m_connect_id = UBtreeGraph::GetConnectionIdFromSlots(m_out_slot, m_in_slot);
}

UBtreeConnection::~UBtreeConnection() {}

uint64_t UBtreeConnection::get_connect_id() const noexcept { return m_connect_id; }

void UBtreeConnection::SerializeTo<PERSON>son(machine::IVMStreamWriter& writer) {
  writer.AddUintProperty("in_slot", m_in_slot->get_full_slot_id());
  writer.AddUintProperty("out_slot", m_out_slot->get_full_slot_id());
}

void UBtreeConnection::Deserialize<PERSON>rom<PERSON>son(machine::IVMStreamReadNode& node) {
  uint32_t slot_id = node.GetIntProperty("in_slot");
  assert(slot_id == m_in_slot->get_full_slot_id());

  slot_id = node.GetIntProperty("out_slot");
  assert(slot_id == m_out_slot->get_full_slot_id());
}
}  // namespace logic
}  // namespace gbf
