#include "visual/blueprint/extend/btree/btree_context.h"
#include "visual/blueprint/extend/btree/btree_graph.h"
#include "reflection/objects/make_user_object.hpp"

namespace gbf {
namespace logic {
UBtreeContext::UBtreeContext(UBtreeWorkspace* parent_workspace, UBtreeGraph* main_graph, const machine::VObject& agent,
                             machine::NamedMemoryScopePtr global_mem_scope)
    : m_parent_workspace(parent_workspace), m_main_graph(main_graph), m_agent_object(agent), m_global_memory_scope(global_mem_scope) {
  InitRunEnvironment(global_mem_scope);
  // ToDo: add first root instruction here
}

UBtreeContext::~UBtreeContext() {
  m_scheduler.reset();
  m_main_coroutine.reset();
  m_global_memory_scope.reset();
}

void UBtreeContext::StartFromRoot() { m_main_graph->StartFromRoot(m_main_coroutine.get()); }

machine::VMRunStepStatus UBtreeContext::Update() { return m_scheduler->Update(); }

void UBtreeContext::InitRunEnvironment(machine::NamedMemoryScopePtr global_mem_scope) {
  m_scheduler = reflection::make_shared_with_rtti<machine::VScheduler>();
  m_main_coroutine = m_scheduler->NewRootCoroutine(m_agent_object, global_mem_scope);
}

}  // namespace logic
}  // namespace gbf
