#include "runtime_resource/runtime_resource_manager.hpp"
#include "core/core_global.hpp"
#include "core/utils/path_tool.hpp"
#include "core/utils/string_util.h"
#include "math/mathtool.h"
#include "reflection/objects/make_user_object.hpp"
#include "runtime_resource/runtime_resource.hpp"
#include "memoryhooker/Module.h"
IMPORT_MODULE


namespace gbf {
namespace logic {

RuntimeResourceManager::RuntimeResourceManager() {
  round_extra_time_ms_ = URuntimeResource::kRoundExtraCachedTimeMs;
  next_check_time_ms_ = round_extra_time_ms_;
}

RuntimeResourceManager::~RuntimeResourceManager() {}

RuntimeResourcePtr RuntimeResourceManager::CreateManual(std::string_view resName, std::string_view resGroup, RuntimeResourceType hint_type,
                                                        RuntimeResourceKeepMode keepMode) {
  auto res = CreateImpl(resName, resGroup, keepMode, hint_type);
  res->SetCreateModeAsManual();
  AddResource(res);
  return res;
}

RuntimeResourcePtr RuntimeResourceManager::CreateOrRetrieve(std::string_view resName, std::string_view resGroup, RuntimeResourceType hint_type,
                                                            RuntimeResourceKeepMode keepMode) {
  if (resName.empty() || resName == "") return {};

  std::string normalizePath = PathTool::NormalizeFilePath(std::string(resName));

  auto res = GetByName(normalizePath);
  if (!res) {
    res = CreateImpl(normalizePath, resGroup, keepMode, hint_type);
    AddResource(res);
  }
  return res;
}

////gbf::logic::RuntimeResourcePtr RuntimeResourceManager::CreateFromStream(ByteBufferPtr data_stream, RuntimeResourceType hint_type,
////                                                                        RuntimeResourceKeepMode keep_mode) {
////
////}

RuntimeResourcePtr RuntimeResourceManager::GetByName(std::string_view resName) {
  RuntimeResourcePtr res;
  auto iter = weak_named_map_.find(std::string(resName));
  if (iter != weak_named_map_.end()) {
    res = iter->second.lock();
  }
  return res;
}

void RuntimeResourceManager::VisitAllItems(VisitRuntimeResourceFunction&& visitFunc) const {
  for (auto iter = weak_named_map_.begin(); iter != weak_named_map_.end(); iter++) {
    auto resPtr = iter->second.lock();
    if (resPtr) visitFunc(resPtr.get());
  }
}

int RuntimeResourceManager::GetResCount() { return (int)weak_named_map_.size(); }

void RuntimeResourceManager::Remove(URuntimeResource* rawPtr) { RemoveImpl(rawPtr); }

void RuntimeResourceManager::RemoveAll() {
  cached_map_.clear();
  delay_removed_map_.clear();
  always_alive_map_.clear();

  for (auto i = weak_named_map_.begin(); i != weak_named_map_.end(); i++) {
    auto resPtr = i->second.lock();
    if (resPtr) resPtr->Unload();
  }

  weak_named_map_.clear();
}

void RuntimeResourceManager::RemoveUnusedResources() {
  // force remove not needed resources
  DoResourceCheck(GTimer->GetMilliseconds(), true);

  std::vector<std::string> tmpResNameList;
  for (auto iter = weak_named_map_.begin(); iter != weak_named_map_.end(); iter++) {
    auto resPtr = iter->second.lock();
    if (!resPtr) {
      tmpResNameList.emplace_back(iter->first);
    }
  }

  for (const auto& i : tmpResNameList) {
    weak_named_map_.erase(i);
  }
}

void RuntimeResourceManager::Unload(std::string_view resName) {
  if (resName.length() == 0) return;
  auto resPtr = GetByName(resName);
  if (resPtr) {
    resPtr->Unload();
  }
}

void RuntimeResourceManager::UnloadAll() {
  for (auto i = weak_named_map_.begin(); i != weak_named_map_.end(); i++) {
    auto resPtr = i->second.lock();
    if (resPtr) resPtr->Unload();
  }
}

RuntimeResourcePtr RuntimeResourceManager::CreateImpl(std::string_view resName, std::string_view resGroup, RuntimeResourceKeepMode keepMode,
                                                      RuntimeResourceType hint_type) {
  if (hint_type == RuntimeResourceType::Unknown) {
    // Now we try to deduce resource type from extension
    std::string base_name, ext_name;
    StringUtil::SplitBaseFilename(std::string(resName), base_name, ext_name);
    hint_type = ExtensionNameToResourceType(ext_name);
  }

  if (hint_type == RuntimeResourceType::Unknown) {
    ERR_DEF("RuntimeResourceManager::CreateImpl() Deduce resource type failed, resoure name:%s", resName.data());
    return {};
  }

  auto creator_iter = resource_creator_map_.find(hint_type);
  if (creator_iter == resource_creator_map_.end()) {
    ERR_DEF("RuntimeResourceManager::CreateImpl() can not find register creator to handle this type resource, resoure name:%s", resName.data());
    return {};
  }

  return (creator_iter->second)(resName, resGroup, keepMode);
}

void RuntimeResourceManager::AddResource(RuntimeResourcePtr res) {
  std::string resName = res->get_resource_name();
  weak_named_map_.emplace(resName, res);
  ////mWeakNamedMap.insert(std::make_pair(res->GetResName(), res));

  auto mode = res->get_keep_mode();
  if (mode == RuntimeResourceKeepMode::CachedTime) {
    cached_map_.emplace(res->get_hash_id(), res);
  } else if (mode == RuntimeResourceKeepMode::AlwaysAlive) {
    // save to always alive array if keep mode is always alive
    always_alive_map_.emplace(res->get_hash_id(), res);
  }
}

static bool s_IsDeleted = false;

void RuntimeResourceManager::RemoveImpl(URuntimeResource* resPtr) {
  // CAP_LOCK_AUTO_MUTEX;

  resPtr->Unload();
  weak_named_map_.erase(resPtr->get_resource_name());
  if (resPtr->get_keep_mode() == RuntimeResourceKeepMode::AlwaysAlive) {
    always_alive_map_.erase(resPtr->get_hash_id());
  } else if (resPtr->get_keep_mode() == RuntimeResourceKeepMode::CachedTime) {
    cached_map_.erase(resPtr->get_hash_id());
  }
}

void RuntimeResourceManager::OutputStatusToLog() {
  INF_DEF("----------> RuntimeResourceManager dump start:");

  for (auto& resPair : weak_named_map_) {
    auto resPtr = resPair.second.lock();
    if (resPtr) {
      if (resPtr->is_loaded()) {
        INF_DEF("[%s] loaded with size:%d", resPtr->get_resource_name().c_str(), (int)resPtr->get_size());
      } else {
        INF_DEF("[%s] in other status:%d", resPtr->get_resource_name().c_str(), (int)resPtr->get_loading_state());
      }
    }
  }

  INF_DEF("----------> RuntimeResourceManager dump end!");
}

void RuntimeResourceManager::DoResourceCheck(uint32_t frameTime, bool forceRemove) {
  // detect need removed resources
  for (auto iter = cached_map_.begin(); iter != cached_map_.end(); iter++) {
    if (iter->second->has_runtime_object_) {
      if (iter->second.use_count() == 2 && iter->second->get_need_gc() && !iter->second->get_already_in_delay_handled()) {
        uint32_t uEngTime = frameTime;
        uEngTime += GetCahcedPeriod();
        uEngTime += iter->second->get_round_cached_time();
        _AddToDelayRemovedMap(uEngTime, iter->second);
        iter->second->set_already_in_delay_handled(true);
      }
    } else {
      // NO Resource Runtime Object Hold Archive Object , So Reference Count is 1
      if (iter->second.use_count() == 1 && !iter->second->get_already_in_delay_handled()) {
        uint32_t uEngTime = frameTime;
        uEngTime += GetCahcedPeriod();
        uEngTime += iter->second->get_round_cached_time();
        _AddToDelayRemovedMap(uEngTime, iter->second);
        iter->second->set_already_in_delay_handled(true);
      }
    }
  }

  // try to remove out cache time resources
  auto lastNeedHandled = delay_removed_map_.upper_bound(frameTime);
  if (forceRemove) {
    lastNeedHandled = delay_removed_map_.end();
  }

  if (lastNeedHandled == delay_removed_map_.begin()) return;

  for (auto iter = delay_removed_map_.begin(); iter != lastNeedHandled; iter++) {
    long refCount = iter->second.use_count();
    auto resPtr = iter->second.lock();
    if (!resPtr) continue;

    resPtr->set_already_in_delay_handled(false);
    if ((resPtr->has_runtime_object_ && refCount == 2) || (!resPtr->has_runtime_object_ && refCount == 1)) {
      // Change Keep Mode from CachedTime to Reference, Let ResourceManager Do GC.
      resPtr->ClearCacheMode();
      cached_map_.erase(resPtr->get_hash_id());  // erase from keep strong map
    }
  }

  // need remove all flag as removed items here.
  delay_removed_map_.erase(delay_removed_map_.begin(), lastNeedHandled);
}

void RuntimeResourceManager::_AddToDelayRemovedMap(uint32_t removeTime, const RuntimeResourcePtr& fileResPtr) {
  delay_removed_map_.insert(std::make_pair(removeTime, fileResPtr));
}

void RuntimeResourceManager::Update() {
  auto frameTime = GTimer->GetMilliseconds();

  if (frameTime >= next_check_time_ms_) {
    DoResourceCheck(frameTime, false);
    next_check_time_ms_ = frameTime + URuntimeResource::kCheckRuntimeResourcePeriodMs + round_extra_time_ms_;
  }
  DoCustomUpdate();
}

bool RuntimeResourceManager::RegisterRuntimeResource(RuntimeResourceType res_type, RuntimeResourceCreator&& creator) {
  auto iter = resource_creator_map_.find(res_type);
  if (iter != resource_creator_map_.end()) {
    ERR_DEF("Resource with extension %s is alreay register in RuntimeResourceManager!", ResourceTypeToExtensionName(res_type).data());
    return false;
  }

  resource_creator_map_[res_type] = std::move(creator);
  return true;
}

std::string_view RuntimeResourceManager::ResourceTypeToExtensionName(RuntimeResourceType res_type) {
  switch (res_type) {
    case gbf::logic::RuntimeResourceType::Texture:
      return "texture";
    case gbf::logic::RuntimeResourceType::Polyer:
      return "polyer";
    case gbf::logic::RuntimeResourceType::Mesh:
      return "mesh";
    case gbf::logic::RuntimeResourceType::Skeleton:
      return "skeleton";
    case gbf::logic::RuntimeResourceType::Animation:
      return "animation";
    case gbf::logic::RuntimeResourceType::RootMotion:
      return "root_motion";
    case gbf::logic::RuntimeResourceType::TagPoint:
      return "tag_point";
    case gbf::logic::RuntimeResourceType::AnimationTransition:
      return "animation_transition";
    case gbf::logic::RuntimeResourceType::Material:
      return "material";
    case gbf::logic::RuntimeResourceType::Particle:
      return "particle";
    case gbf::logic::RuntimeResourceType::Prefab:
      return "prefab";
    case gbf::logic::RuntimeResourceType::GameLevel:
      return "game_level";
    case gbf::logic::RuntimeResourceType::Navigation:
      return "navigation";
    case gbf::logic::RuntimeResourceType::UiLayout:
      return "ui_layout";
    case gbf::logic::RuntimeResourceType::UiAnimation:
      return "ui_animation";
    case gbf::logic::RuntimeResourceType::RuntimeShader:
      return "runtime_shader";
    case gbf::logic::RuntimeResourceType::Movie:
      return "movie";
    case gbf::logic::RuntimeResourceType::Sound:
      return "sound";
    case gbf::logic::RuntimeResourceType::BluePrint:
      return "blue_print";
    case gbf::logic::RuntimeResourceType::ArtFontAnimation:
      return "art_font_animation";
    case gbf::logic::RuntimeResourceType::CameraAnimation:
      return "camera_animation";
    case gbf::logic::RuntimeResourceType::Unknown:
      // just by last return
      break;
    default:
      // just by last return
      break;
  }

  return "unknown";
}

gbf::logic::RuntimeResourceType RuntimeResourceManager::ExtensionNameToResourceType(std::string_view ext_name) {
  if (ext_name == "texture") {
    return RuntimeResourceType::Texture;
  } else if (ext_name == "polyer") {
    return RuntimeResourceType::Polyer;
  } else if (ext_name == "mesh") {
    return RuntimeResourceType::Mesh;
  } else if (ext_name == "skeleton") {
    return RuntimeResourceType::Skeleton;
  } else if (ext_name == "animation") {
    return RuntimeResourceType::Animation;
  } else if (ext_name == "root_motion") {
    return RuntimeResourceType::RootMotion;
  } else if (ext_name == "tag_point") {
    return RuntimeResourceType::TagPoint;
  } else if (ext_name == "animation_transition") {
    return RuntimeResourceType::AnimationTransition;
  } else if (ext_name == "material") {
    return RuntimeResourceType::Material;
  } else if (ext_name == "particle") {
    return RuntimeResourceType::Particle;
  } else if (ext_name == "prefab") {
    return RuntimeResourceType::Prefab;
  } else if (ext_name == "game_level") {
    return RuntimeResourceType::GameLevel;
  } else if (ext_name == "navigation") {
    return RuntimeResourceType::Navigation;
  } else if (ext_name == "ui_layout") {
    return RuntimeResourceType::UiLayout;
  } else if (ext_name == "ui_animation") {
    return RuntimeResourceType::UiAnimation;
  } else if (ext_name == "runtime_shader") {
    return RuntimeResourceType::RuntimeShader;
  } else if (ext_name == "movie") {
    return RuntimeResourceType::Movie;
  } else if (ext_name == "sound") {
    return RuntimeResourceType::Sound;
  } else if (ext_name == "blue_print") {
    return RuntimeResourceType::BluePrint;
  } else if (ext_name == "art_font_animation") {
    return RuntimeResourceType::ArtFontAnimation;
  } else if (ext_name == "camera_animation") {
    return RuntimeResourceType::CameraAnimation;
  }

  return RuntimeResourceType::Unknown;
}

}  // namespace logic
}  // namespace gbf
