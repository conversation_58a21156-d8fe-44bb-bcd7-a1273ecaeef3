using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text.RegularExpressions;
using YamlDotNet.Serialization;
////using Microsoft.VisualStudio.Setup.Configuration;

namespace Clangen
{
    public class CustomPathInfo
    {
        public string Path { get; set; }
        public bool Relative { get; set; }
    }

    public class ClangProjectSettingsInfo
    {
        public string RootSourcePath { get; set; }
        public List<CustomPathInfo> AdditionalIncludeDirectories { get; set; }
        public List<string> ASTGenerateIncludes { get; set; }
        public List<string> CompileExtraArgs { get; set; }
    }

    public class ExportPassInfo
    {
        public string Type { get; set; }
        public ClangProjectSettingsInfo ClangProjectSettings { get; set; }
        public string Path { get; set; }
        public string ServiceProxyPath { get; set; }

        public string PathExt0 { get; set; }

        public string DetailConfigFile { get; set; }

        public string OutFileName { get; set; }

        public bool Relative { get; set; }
    }



    class AppConfig
    {
        private static AppConfig _instance;

        public static AppConfig Instance
        {
            get
            {
                return _instance;
            }
        }

        public static Assembly AppAssembly { get; private set; }

        private static string DefaultConfigPath = "CppConfig.yaml";
        private static Regex MatchPattern = new Regex(@"\$\((\w+)\)");

        public static bool EnableSeparateTempalte { get; private set; } = false;

        public List<string> SystemIncludes { get; set; }

        public string ClangenRootPath { get; set; }

        public bool RpcWithContextFlag { get; private set; } = false;


        ////public Dictionary<string, CustomPathInfo> ExportPathMap { get; set; }

        public List<ExportPassInfo> ExportPassArray { get; private set; } = new List<ExportPassInfo>();

        public string NDK_ROOT { get; set; }

        public static AppConfig GetConfig(string[] args, Assembly assembly, Dictionary<string, string> env)
        {
            if (_instance != null)
            {
                throw new Exception("Config can only create once!");
            }

            AppAssembly = assembly;

            string cfgFilePath = DefaultConfigPath;
            if (System.Environment.OSVersion.Platform == PlatformID.Win32NT)
            {
                cfgFilePath = "CppConfig.win.yaml";
            }
            else
            {
                cfgFilePath = "CppConfig.linux.yaml";
            }

            bool needShowHelp = false;
            bool rpcWithContextFlag = false;
            NDesk.Options.OptionSet argset = new NDesk.Options.OptionSet() {
                { "cfg=", $"the {{*.yaml}} for app config.\nDefault is: {cfgFilePath}",
                   v =>  cfgFilePath = v },
                { "enable_separate_template", "enable separate template mode, default is false.",
                    v => EnableSeparateTempalte = v != null },
                { "h|help",  "show this message and exit",
                    v => needShowHelp = v != null },
                { "rpc_with_context_flag", "add context in rpc function",
                    v => rpcWithContextFlag = v != null },
            };
            argset.Parse(args);

            if (needShowHelp)
            {
                argset.WriteOptionDescriptions(Console.Out);
                Environment.Exit(0);
            }

            if (!File.Exists(cfgFilePath))
            {
                Log.Error($"{cfgFilePath} can not find here!");
                Environment.Exit(-1);
            }

            if (EnableSeparateTempalte)
            {
                Log.Warning("App now run in separate template mode, you must keep all templates right!");
            }

            Log.Info($"App config file now load is: {cfgFilePath}");

            string fileContent = File.ReadAllText(cfgFilePath);
            var deserializer = new DeserializerBuilder().Build();
            AppConfig config = deserializer.Deserialize<AppConfig>(fileContent);
            config.RpcWithContextFlag = rpcWithContextFlag;
            _instance = config;

            return config;
        }

        //public static CppConfig GetConfig(Dictionary<string, string> env)
        //{
        //    return GetConfig(ConfigPath, env);
        //}

        public string[] GetClangArgs(ExportPassInfo passInfo)
        {
            List<string> args = new List<string>();

            //int loop = 0;
            foreach (var path in SystemIncludes)
            {
                //check directory exist
                if (!System.IO.Directory.Exists(path))
                {
                    System.Console.WriteLine("Fatal Error Find:Include path '{0}' is not exist in system!", path);
                }

                args.Add("-I" + path);
            }

            foreach (var pathInfo in passInfo.ClangProjectSettings.AdditionalIncludeDirectories)
            {
                string tmpPath = "";
                if (pathInfo.Relative)
                {
                    tmpPath = Path.Combine(passInfo.ClangProjectSettings.RootSourcePath, pathInfo.Path);
                }
                else
                {
                    tmpPath = pathInfo.Path;
                }

                //check directory exist
                if (!System.IO.Directory.Exists(tmpPath))
                {
                    System.Console.WriteLine("Fatal Error Find:Include path '{0}' is not exist in system!", tmpPath);
                }

                args.Add("-I" + tmpPath);
            }

            foreach (var extra in passInfo.ClangProjectSettings.CompileExtraArgs)
            {
                args.Add(extra);
            }

            //Add pre define macros here
            args.Add("-DCLANG_GENERATOR");
            args.Add("-D__clang__");

            args.Add("-xc++");
            args.Add("-dM");
            args.Add("-E");
            args.Add("-fparse-all-comments");   //all comments handle here

            return args.ToArray();
        }

        public List<string> GetSources(ExportPassInfo passInfo)
        {
            List<string> tmpSources = new List<string>();
            foreach (var pathInfo in passInfo.ClangProjectSettings.ASTGenerateIncludes)
            {
                tmpSources.Add(pathInfo);
            }

            return tmpSources;
        }

        public static void EvnVarSubstitution(AppConfig config, Dictionary<string, string> env)
        {
            config.SystemIncludes = EvnVarSubstitution(config.SystemIncludes, env);
            ////config.Exports = EvnVarSubstitution(config.Exports, env);
        }

        static List<string> EvnVarSubstitution(List<string> strList, Dictionary<string, string> env)
        {
            List<string> outPut = new List<string>();
            foreach (string str in strList)
            {
                outPut.Add(EvnVarSubstitution(str, env));
            }
            return outPut;
        }

        static string EvnVarSubstitution(string str, Dictionary<string, string> env)
        {
            string result = MatchPattern.Replace(str, match =>
            {
                string key = match.Groups[1].Value;
                if (env.ContainsKey(key))
                    return env[key];

                return match.ToString();
            });
            return result;
        }
    }
}