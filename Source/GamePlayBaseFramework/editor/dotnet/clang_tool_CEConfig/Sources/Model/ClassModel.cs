using Clangen.Parser;
using CppAst;
using DotLiquid;
using DotLiquid.Util;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Clangen.Model
{
    //class BaseClassModel : Drop
    //{
    //    public string Name { get; }
    //    public string FullName { get; }
    //    public BaseClassModel(CppBaseType c)
    //    {
    //        var ct = c.Type.GetCanonicalType() as CppClass;
    //        Name = ct.Name;
    //        FullName = ct.FullName;
    //    }
    //}

    class TypeScriptFunctionModel : Drop
    {
        // 单个函数，或者有重载但是只选了一个的函数
        public List<FunctionModel> SelectFunctions { get; }
        // T GetXXX() const 这类函数可以作为一个属性，在调试脚本的时候
        public List<FunctionModel> MethodAsPropertyFunctions { get; }
        // 静态函数重载
        public Dictionary<string, List<FunctionModel>> OverloadStaticFunctions { get; }
        // 函数重载
        public Dictionary<string, List<FunctionModel>> OverloadMethodFunctions { get; }
        public TypeScriptFunctionModel(List<FunctionModel> functions)
            : base()
        {
            SelectFunctions = new List<FunctionModel>();
            MethodAsPropertyFunctions = new List<FunctionModel>();
            OverloadStaticFunctions = new Dictionary<string, List<FunctionModel>>();
            OverloadMethodFunctions = new Dictionary<string, List<FunctionModel>>();
            var groupFunctions = functions.Where(f => f.NeedBindingScript).GroupBy(f => f.Name);
            foreach (var item in groupFunctions)
            {
                if (item.Count() > 1)
                {
                    {
                        var staticFunc = item.Where(f => f.IsStatic);
                        if (staticFunc.Count() > 1)
                        {
                            OverloadStaticFunctions[item.Key] = staticFunc.ToList();
                        }
                        else if (staticFunc.Count() == 1)
                        {
                            SelectFunctions.AddRange(staticFunc);
                        }
                    }

                    {
                        var methodFunc = item.Where(f => !f.IsStatic);
                        if (methodFunc.Count() > 1)
                        {
                            OverloadMethodFunctions[item.Key] = methodFunc.ToList();
                        }
                        else if (methodFunc.Count() == 1)
                        {
                            SelectFunctions.AddRange(methodFunc);
                        }
                    }
                }
                else
                {
                    SelectFunctions.AddRange(item);
                }
            }

            // MethodAsPropertyFunctions 
            var _MethodAsPropertyFunctions = functions.Where(f => f.IsMethodAsProperty);
            foreach (var func in _MethodAsPropertyFunctions)
            {
                MethodAsPropertyFunctions.Add(func);
            }
        }
    }

    class ClassModel : EntityModel
    {
        //public List<BaseClassModel> BaseClasses { get; }
        public List<ClassModel> BaseClasses { get; }
        public List<ClassModel> TemplateArgumentsClasses { get; }
        public List<FieldModel> Fields { get; }
        public List<FunctionModel> Functions { get; }
        public List<EnumModel> Enums { get; }
        public List<ConstructorModel> Constructors { get; }
        public DestructorModel Destructor { get; } = null;
        public TypeScriptFunctionModel TypeScriptFunction { get; }
        public bool HasDestructor => Destructor != null;

        public string FullName { get; }
        public string AliasName { get; }
        public string ScriptName => CodeGenName + "Script";
        public string InterfaceName => CodeGenName + "Interface";
        public string BaseCallHelperName => CodeGenName + "BaseCallHelper";
        public string BaseCallHelperScriptName => CodeGenName + "BaseCallHelperScript";
        public string DelegateName => CodeGenName + "Delegate";
        public string DelegateScriptName => CodeGenName + "DelegateScript";
        public string TypeScriptExtensionName => CodeGenName + "Extension";
        public string CodeGenName => IsTemplateSpecializedClass ? PlainFullName : Name;
        public string PlainFullName => FullName.Replace("::", "_").Replace("<", "_").Replace(">", "_");
        public string TypeScriptAliasName()
        {
            if (IsTemplateSpecializedClass)
            {
                if (IsResourcePointer)
                {
                    return AliasName;
                }
                // other template specialized class
            }
            return Name;
        }
        public string Namespace { get; }
        //public string SourceFile { get; }
        public bool IsClass => ClassDecl.ClassKind == CppClassKind.Class;
        public bool IsStruct => ClassDecl.ClassKind == CppClassKind.Struct;
        //public bool IsTemplate => ClassDecl.ClassKind == CppClassKind.Template;

        public override bool NeedSerialization { get; } = false;
        public override bool NeedBindingScript { get; } = false;
        public override bool NeedBindingTs { get; } = false;
        public override bool NeedBindingLua { get; } = false;
        public override bool NeedBindingEditor => _NeedBindingEditor;
        public override bool NeedReflection => _NeedReflection || IsWorflowType;
        public string AdditionalSerializeMethod { get; } = null;
        public string AdditionalDeserializeMethod { get; } = null;
        public string PrecheckSerializeNodeMethod { get; } = null;
        public string PrecheckDeserializeNodeMethod { get; } = null;

        public bool IsSystem => CEMeta.IsSystem;
        //public bool InheritsFromRenderSystemBase { get; } = false;
        //public bool InheritsFromGameSystemBase { get; } = false;
        public bool IsComponent => CEMeta.IsComponent;
        public bool IsGameplay => CEMeta.IsGameplay;
        public bool IsWorflowType => CEMeta.IsWorflowType;
        public bool IsWorkflowStruct => CEMeta.IsWorflowType;
        public bool IsGameObject => InheritsFrom("cegf::GameObject") || FullName.Equals("cegf::GameObjectComponent") || InheritsFrom("cegf::GameObjectComponent") || FullName.Equals("cegf::GameObject");
        public bool IsTemplateSpecializedClass => ClassDecl.TemplateKind == CppTemplateKind.TemplateSpecializedClass;
        // smart pointer operator->()
        public bool IsResourcePointer => TemplateFrom("cross::ResourceFuturePtr") && IsTemplateSpecializedClass &&
                                      TemplateArgumentsClasses.Count > 0 && TemplateArgumentsClasses[0].NeedBindingTs;
        public ClassModel SystemClass { get; set; } = null;
        public List<ClassModel> ComponentClasses { get; set; } = null;

        public CppClass ClassDecl;
        //private ClassModel Parent;
        public bool NeedExport => NeedBindingEditor || NeedBindingScript || NeedReflection;
        public bool HasGeneratedBody = false;
        bool _NeedBindingEditor = false;
        bool _NeedReflection = false;
        public string CliClassName
        {
            get
            {
                return string.Format("{0}::{1}", "Cli" + Clangen.NamespaceTool.CookNamespaceToCppNamespace(Namespace), Name);
            }
            set { }
        }
        public string CliLeafClassName
        {
            get
            {
                //return string.Format("{0}", Name);
                return string.Format("{0}", CliClassName);
            }
        }

        public bool Equals(ClassModel other)
        {
            // 实现逻辑，比如比较某些关键属性
            return other != null && FullName == other.FullName;
        }

        public override bool Equals(object obj) => Equals(obj as ClassModel);
        public override int GetHashCode()
        {
            // 返回一个适当的哈希码
            return FullName.GetHashCode();
        }
        private bool HasClassByName(List<CppBaseType> bases, string name)
        {
            bool result = false;
            foreach (var b in bases)
            {
                var t = b.Type.GetCanonicalType() as CppClass;
                if (t.FullName == name)
                {
                    return true;
                }
                else
                {
                    if (t.BaseTypes.Count > 0)
                    {
                        result |= HasClassByName(t.BaseTypes, name);
                    }
                }
            }
            return result;
        }
        public bool IsRttiClass
        {
            get
            {
                return HasClassByName(ClassDecl.BaseTypes, "gbf::reflection::RttiBase");
            }
        }
        //Nested classes are not handled
        public ClassModel(CppClass decl, AstContent ast)
            : base(decl)
        {
            ClassDecl = decl;
            CEMeta = new CEMetaManager(decl.MetaAttributes);
            Name = decl.Name;
            FullName = ClassDecl.FullName;
            AliasName = ClassDecl.AliasName;
            Namespace = FullName.Remove(Math.Max(0, FullName.Length - decl.Name.Length - 2));

            HasGeneratedBody = ClassDecl.Typedefs.Any(t => t.Name.StartsWith("__CEMETA_GENERATED_CODE"));

            Fields = new List<FieldModel>();
            foreach (var f in ClassDecl.Fields)
            {
                if (!f.MetaAttributes.IsNull)
                {
                    var field = new FieldModel(f, this);
                    if (field.CEMeta.HasKey("ClassMeta"))
                    {
                        CEMeta.Append(field.CEMeta);
                    }
                    else
                    {
                        Fields.Add(field);
                    }
                }
            }

            Functions = new List<FunctionModel>();
            Destructor = null;
            foreach (var v in ClassDecl.Functions)
            {
                if (v.Flags.HasFlag(CppFunctionFlags.Method))
                {
                    Functions.Add(new FunctionModel(v, this));
                }
                else
                if (v.Flags.HasFlag(CppFunctionFlags.Destructor))
                {
                    Destructor = new DestructorModel(v, this);
                }
            }

            Constructors = ClassDecl.Constructors.Select(v => new ConstructorModel(v, this)).ToList();


            Enums = ClassDecl.Enums.Select(v => new EnumModel(v, this)).ToList();

            //BaseClasses = new List<BaseClassModel>();
            BaseClasses = new List<ClassModel>();
            foreach (var b in ClassDecl.BaseTypes)
            {
                var t = b.Type.GetCanonicalType() as CppClass;
                if (HasMetaAttributes(t))
                {
                    var baseClass = ast.GetOrCreateClassDeclaration(t);
                    BaseClasses.Add(baseClass);
                }
            }

            NeedSerialization = Functions.Any(f => f.CEMeta.HasKey("Serialize"));
            _NeedBindingEditor = CEMeta.HasKey("Editor") || Fields.Any(f => f.NeedBindingEditor) || Functions.Any(f => f.NeedBindingEditor) || Enums.Any(e => e.NeedBindingEditor);
            //NeedBindingScript = CEMeta.HasKey("Script") || Fields.Any(f => f.NeedBindingScript) || Functions.Any(f => f.NeedBindingScript);
            _NeedReflection = CEMeta.HasKey("Reflect") || Fields.Any(f => f.NeedReflection) || Functions.Any(f => f.NeedReflection);

            if (IsComponent)
            {
                NeedBindingScript = false;
            }
            else if (IsSystem || IsGameplay)
            {
                NeedBindingScript = CEMeta.HasKey("Script") || CEMeta.HasKey("Puerts") || Fields.Any(f => f.NeedBindingScript) || Functions.Any(f => f.NeedBindingScript);
            }
            else
            {
                NeedBindingScript = CEMeta.HasKey("Script") || CEMeta.HasKey("Puerts") || Fields.Any(f => f.NeedBindingScript) || Functions.Any(f => f.NeedBindingScript);
            }
            NeedBindingTs = CEMeta.HasKey("Script") || CEMeta.HasKey("Puerts");

            NeedBindingLua = (CEMeta.HasKey("Script") && !CEMeta.HasKey("Puerts")) || Fields.Any(f => f.NeedBindingLua) || Functions.Any(f => f.NeedBindingLua); ;
            foreach (var func in Functions)
            {
                if (func.CEMeta.HasKey("PrecheckSerializeNode"))
                {
                    PrecheckSerializeNodeMethod = func.Name;
                }
                else if (func.CEMeta.HasKey("PrecheckDeserializeNode"))
                {
                    PrecheckDeserializeNodeMethod = func.Name;
                }
                else if (func.CEMeta.HasKey("AdditionalSerialize"))
                {
                    AdditionalSerializeMethod = func.Name;
                }
                else if (func.CEMeta.HasKey("AdditionalDeserialize"))
                {
                    AdditionalDeserializeMethod = func.Name;
                }
            }

            if (ClassDecl.TemplateKind == CppTemplateKind.TemplateClass || ClassDecl.TemplateKind == CppTemplateKind.PartialTemplateClass)
            {
                NeedBindingScript = false;
                _NeedReflection = false;
                _NeedBindingEditor = false;
                NeedBindingTs = false;
            }

            TypeScriptFunction = new TypeScriptFunctionModel(Functions);

            // for resource ptr
            TemplateArgumentsClasses = new List<ClassModel>();
            foreach (var cppTempArg in ClassDecl.TemplateSpecializedArguments)
            {
                if (cppTempArg.ArgAsType is CppClass cppClass)
                {
                    var argCppClass = ast.GetOrCreateClassDeclaration(cppClass);
                    TemplateArgumentsClasses.Add(argCppClass);
                }
            }

            if (IsTemplateSpecializedClass && !IsResourcePointer)
            {
                NeedBindingTs = false;
            }
        }

        static public bool HasMetaAttributes(CppClass c)
        {
            bool needExportMember = c.Fields.Any(v => !v.MetaAttributes.IsNull)
                || c.Functions.Any(v => !v.MetaAttributes.IsNull)
                || c.Enums.Any(v => !v.MetaAttributes.IsNull)
                || c.Constructors.Any(v => !v.MetaAttributes.IsNull);
            return !c.MetaAttributes.IsNull || needExportMember;
        }

        public string RenderBase
        {
            get
            {
                return string.Join(", ", BaseClasses.Select(v => v.FullName));
            }
        }
        public bool HasTsBase()
        {
            if (IsResourcePointer && TemplateArgumentsClasses.Count > 0 )
            {
                return TemplateArgumentsClasses[0].HasTsBase();
            }

            if (BaseClasses.Count > 0 && BaseClasses[0].NeedBindingTs)
            {
                return true;
            }
            return false;
        }
        public string TsBaseFullName()
        {
            if (IsResourcePointer)
            {
                StringBuilder sb = new StringBuilder();
                string fullparent = ClassDecl.FullParentName;
                if (string.IsNullOrEmpty(fullparent))
                {
                    sb.Append(Name);
                }
                else
                {
                    sb.Append($"{fullparent}::{Name}");
                }

                sb.Append('<');
                sb.Append(TemplateArgumentsClasses[0].TsBaseFullName());
                sb.Append('>');
                return sb.ToString();
            }

            if (BaseClasses.Count > 0 && BaseClasses[0].NeedBindingTs)
            {
                return BaseClasses[0].FullName;
            }
            return "";
        }
        public string TsBaseName()
        {
            if (IsResourcePointer)
            {
                StringBuilder sb = new StringBuilder();
                sb.Append(Name);
                sb.Append('<');
                sb.Append(TemplateArgumentsClasses[0].TsBaseName());
                sb.Append('>');
                return sb.ToString();
            }

            if (BaseClasses.Count > 0 && BaseClasses[0].NeedBindingTs)
            {
                return BaseClasses[0].Name;
            }
            return "";
        }
        public List<ConstructorModel> EditorConstructors
        {
            get
            {
                var ret = Constructors.Where(f => f.NeedBindingEditor && f.Parameters.Count > 0).ToList();

                if (Constructors.Count == 0)
                {
                    // implicitly default constructor
                    ret.Add(ConstructorModel.DefaultConstructor(this));
                }
                else
                {
                    var defaultCtor = Constructors.Find(f => f.IsDefaultConstructor);
                    if (defaultCtor != null)
                    {
                        // explicitly default constructor
                        CppVisibility visibility = defaultCtor.IsPublic ? CppVisibility.Public : CppVisibility.Private;
                        ret.Add(ConstructorModel.DefaultConstructor(this, defaultCtor.IsDeleted, visibility));
                    }
                    else
                    {
                        // not implicitly default constructor
                        ret.Add(ConstructorModel.DefaultConstructor(this, true));
                    }
                }
                return ret;
            }
        }
        public List<ConstructorModel> TsConstructors
        {
            get
            {
                var ret = Constructors.Where(f => f.NeedBindingTs).ToList();
                return ret;
            }
        }

        public override string ToString()
        {
            return "class " + FullName;
        }

        HashSet<string> allBaseNames = null;
        public bool InheritsFrom(string baseName)
        {
            if (allBaseNames is null)
            {
                allBaseNames = new HashSet<string>();
                HashSet<string> baseNameSet = new HashSet<string>();
                List<CppClass> classes = new List<CppClass>() { ClassDecl };
                while (classes.Count > 0)
                {
                    List<CppClass> baseClasses = new List<CppClass>();
                    foreach (var c in classes)
                    {
                        allBaseNames.Add(c.FullName);
                        baseClasses.AddRange(c.BaseTypes.Select(b => b.Type.GetCanonicalType() as CppClass).Where(c => c != null));
                    }
                    classes = baseClasses;
                }
                allBaseNames.Remove(FullName);
            }
            return allBaseNames.Contains(baseName);
        }

        public bool TemplateFrom(string matchTemplateName)
        {
            if (ClassDecl.TemplateKind == CppTemplateKind.TemplateSpecializedClass)
            {
                string templateName = ClassDecl.SpecializedTemplate.FullName;
                return templateName.StartsWith(matchTemplateName + "<");
            }

            return false;
        }

        public void AddField(ClassModel type, string name, string attr)
        {
            var cppField = new CppField(type.ClassDecl, name)
            {
                Visibility = CppVisibility.Public,
                StorageQualifier = CppStorageQualifier.None,
            };
            var metaAttr = CEMetaAttributeTool.ParseMetaStringFor(attr, out var errorMessage);
            cppField.MetaAttributes.MetaList.Add(metaAttr);

            ClassDecl.Fields.Add(cppField);

            var field = new FieldModel(cppField, this)
            {
                IsAdditional = true
            };
            _NeedBindingEditor |= field.NeedBindingEditor;
            _NeedReflection |= field.NeedReflection;
            Fields.Add(field);
        }
    }
}
