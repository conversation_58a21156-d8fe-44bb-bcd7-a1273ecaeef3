using Clangen;
using Irony.Parsing;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CppAst
{
    public static class CEMetaAttributeTool
    {
        public const string kMetaLeaderWord = "CEMeta:";

        public static bool IsCEAttribute(string meta)
        {
            return meta.StartsWith(kMetaLeaderWord);
        }
        public static MetaAttribute ParseMetaStringFor(string meta, out string errorMessage)
        {
            meta = meta.Substring(kMetaLeaderWord.Length);
            var attribute = new MetaAttribute();
            errorMessage = "";
            try
            {
                attribute.FeatureName = "";
                attribute.ArgumentMap = CEMetaParser.Parse(meta);
            }
            catch (Exception ex)
            {
                errorMessage = ex.Message;
            }
            return attribute;
        }

        public static string MetaAttributeMapToString(MetaAttributeMap attrMap)
        {
            return string.Join(" ", attrMap.MetaList.Select(m => MetaObjectToString(m.ArgumentMap, false)));
        }

        public static string MetaObjectToString(object value, bool isOutermost = true)
        {
            if (value is MetaAttribute meta)
            {
                return MetaObjectToString(meta.ArgumentMap, isOutermost);
            }
            else if (value is null)
            {
                return "";
            }
            else if (value != null && value is Dictionary<string, object> valueDict)
            {
                var strList = new List<string>();
                var paramList = valueDict.Select(item => item.Key + MetaObjectToString(item.Value, false));

                var ret = string.Join(", ", paramList);
                return isOutermost ? ret : "(" + ret + ")";
            }
            else
            {
                return (isOutermost ? "" : " = ") + value.ToString();
            }
        }
    }

    //CEMeta example: Meta, A(B = True, C(D = 1, F = 1.0)), G = "message: \"xxxxx\"";

    public static class CEMetaParser
    {
        public static Dictionary<string, object> Parse(string content)
        {
            if (string.IsNullOrEmpty(content))
            {
                return new();
            }
            else
            {
                var parser = new Irony.Parsing.Parser(CEMetaGrammar.Instance);
                var ast = parser.Parse(content);
                if (!ast.HasErrors())
                {
                    var obj = ParseObject(ast.Root);
                    return obj;
                }
                else
                {
                    var errorMessage = string.Join(" ", ast.ParserMessages);
                    throw new Exception(string.Format("CEMeta({0}), {1}", content, errorMessage));
                }
            }
        }

        private static bool ParseBoolean(ParseTreeNode node)
        {
            return node.ChildNodes[0].Token.ValueString == "True";
        }

        private static string ParseString(ParseTreeNode node)
        {
            return "\"" + StringUtil.CEscape(node.Token.ValueString) + "\"";
        }

        private static Dictionary<string, object> ParseObject(ParseTreeNode node)
        {
            var result = new Dictionary<string, object>();

            foreach (var child in node.ChildNodes)
            {
                var key = child.ChildNodes[0].Token.ValueString;
                if (!result.ContainsKey(key))
                {
                    object value = null;
                    if (child.ChildNodes.Count > 1)
                    {
                        var valueNode = child.ChildNodes[child.ChildNodes.Count - 1];
                        switch (valueNode.Term.Name)
                        {
                            case "Value":
                                value = ParseValue(valueNode);
                                break;
                            case "Object":
                                value = ParseObject(valueNode);
                                break;
                            case "String":
                                value = ParseString(valueNode);
                                break;
                            default:
                                throw new Exception($"Unknonw term {node.Term.Name}");
                        }
                    }
                    result[key] = value;
                }
            }
            return result;
        }

        private static object ParseValue(ParseTreeNode node)
        {
            var value = node.ChildNodes[0];
            switch (value.Term.Name)
            {
                case "String":
                    return ParseString(value);
                case "Number":
                    return value.Token.Value;
                case "Identifier":
                    return value.Token.Text;
                case "Boolean":
                    return ParseBoolean(value);
            }
            throw new Exception($"Unknonw term {value.Term.Name}");
        }
    }

    [Language("CEMetaGrammar", "0.1", "Grammer for CEMeta")]
    public class CEMetaGrammar : Grammar
    {
        public static string TerminalObject = "Object";

        public CEMetaGrammar()
        {
            // Termianl
            var String = new StringLiteral("String", "\"");
            var Number = new NumberLiteral("Number");
            var Id = new IdentifierTerminal("Identifier", "_.:", "_.:");
            var Comma = ToTerm(",");

            // Non Termianl
            var Boolean = new NonTerminal("Boolean");
            var Value = new NonTerminal("Value");
            var Prop = new NonTerminal("Property");
            var Object = new NonTerminal("Object");

            // Rules
            Boolean.Rule = ToTerm("True") | "False";
            Value.Rule = Id | String | Number | Boolean;
            Prop.Rule = Id | (Id + "=" + (Value)) | (Id + "(" + Object + ")") | (Id + "(" + String + ")");

            Object.Rule = MakePlusRule(Object, Comma, Prop) | (Object + Comma);

            MarkPunctuation("(", ")", ",");

            //Set grammar root
            this.Root = Object;
        }

        public static CEMetaGrammar Instance
        {
            get
            {
                if (CurrentGrammar == null)
                {
                    return new CEMetaGrammar();
                }
                else
                {
                    return (CEMetaGrammar)CurrentGrammar;
                }
            }
        }
    }
}