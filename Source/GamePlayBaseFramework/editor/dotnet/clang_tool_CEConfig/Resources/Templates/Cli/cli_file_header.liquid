//code generate from clangen.cliplus
#pragma once
#include "{{marshal_tool_header_path}}"
#using <system.dll>
#ifndef managed_gbf_EXPORTS
#using <managed_gbf.dll>
#endif
//--->export include files begin
{{ includes }}
//--->export include files end

//***************************************************************
//***************All gobal enums export here*********************
//***************************************************************
{%- for enum in enums -%}
{{ enum.render }}
{%- endfor -%}

using System::IntPtr;
using System::String;
using System::Object;
using namespace ClangenCli;
using namespace cross;
//***************************************************************
//***************All class predeclaration here*******************
//***************************************************************
{%- for class in classes -%}
{{ class.render_pre_declaration | strip_newlines}}
{%- endfor -%}
{%- for class in override_classes -%}
{{ class.render_pre_declaration | strip_newlines}}
{%- endfor -%}
{%- for class in template_instance_list -%}
{{ class.render_pre_declaration | strip_newlines }}
{%- endfor -%}
{%- for class in stl_containers -%}
{{ class.render_pre_declaration | strip_newlines }}
{%- endfor -%}


//***************************************************************
//***************All class instances export here*****************
//***************************************************************
{%- for class in classes -%}
{%- unless class.need_ignore -%}
{{ class.render_bridge_header }}
{%- endunless -%}
{%- endfor -%}

//***************************************************************
//**********All class template instances export here*************
//***************************************************************
{%- for class in template_instance_list -%}
{{ class.render_bridge_header }}
{%- endfor -%}

//***************************************************************
//**********All stl container export here************************
//***************************************************************
{%- for class in stl_containers -%}
{{ class.render_header }}
{%- endfor -%}

//***************************************************************
//**********All override class export here***********************
//***************************************************************
{%- for override_class in override_class_list -%}
{{ override_class.render_bridge_header }};
{%- endfor -%}
