// {{Type.full_cpp_name}} export start
{%- unless Type.is_embeded -%}
{%- for ns in Type.export_namespace_list -%}namespace {{ns}}{
{%- endfor -%}
{%- endunless -%}
{%- if Type.is_vector -%}
	{%- assign value_type = Type.first_param_drop -%}
	#define STLDECL_MANAGEDTYPE {{ value_type.managed_cli_type_name }}
	#define STLDECL_NATIVETYPE {{ value_type.native_cpp_type_name }}
	{{ Type.access }}INC_DECLARE_STLVECTOR({{ Type.unscoped_name }}, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE, {{ Type.inner_access }})
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE
{%- elsif Type.is_list -%}
	{%- assign value_type = Type.first_param_drop -%}
	#define STLDECL_MANAGEDTYPE {{ value_type.managed_cli_type_name }}
	#define STLDECL_NATIVETYPE {{ value_type.native_cpp_type_name }}
	{{ Type.access }}INC_DECLARE_STLLIST({{ Type.unscoped_name }}, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE, {{ Type.inner_access }})
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE
{%- elsif Type.is_deque -%}
	{%- assign value_type = Type.first_param_drop -%}
	#define STLDECL_MANAGEDTYPE {{ value_type.managed_cli_type_name }}
	#define STLDECL_NATIVETYPE {{ value_type.native_cpp_type_name }}
	{{ Type.access }}INC_DECLARE_STLDEQUE({{ Type.unscoped_name }}, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE, {{ Type.inner_access }})
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE
{%- elsif Type.is_set -%}
	{%- assign value_type = Type.first_param_drop -%}
	#define STLDECL_MANAGEDTYPE {{ value_type.managed_cli_type_name }}
	#define STLDECL_NATIVETYPE {{ value_type.native_cpp_type_name }}
	{{ Type.access }}INC_DECLARE_STLSET({{ Type.unscoped_name }}, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE, {{ Type.inner_access }})
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE
{%- elsif Type.is_map -%}
	{%- assign key_type = Type.first_param_drop -%}
	{%- assign mapped_type = Type.second_param_drop -%}
	#define STLDECL_MANAGEDKEY {{ key_type.managed_cli_type_name }}
	#define STLDECL_MANAGEDVALUE {{ mapped_type.managed_cli_type_name }}
	#define STLDECL_NATIVEKEY {{ key_type.native_cpp_type_name }}
	#define STLDECL_NATIVEVALUE {{ mapped_type.native_cpp_type_name }}
	{{ Type.access }}INC_DECLARE_STLMAP({{ Type.unscoped_name }}, STLDECL_MANAGEDKEY, STLDECL_MANAGEDVALUE, STLDECL_NATIVEKEY, STLDECL_NATIVEVALUE, {{ Type.inner_access }})
	#undef STLDECL_MANAGEDKEY
	#undef STLDECL_MANAGEDVALUE
	#undef STLDECL_NATIVEKEY
	#undef STLDECL_NATIVEVALUE
{%- elsif Type.is_multi_set -%}
	{%- assign value_type = Type.first_param_drop -%}
	#define STLDECL_MANAGEDTYPE {{ value_type.managed_cli_type_name }}
	#define STLDECL_NATIVETYPE {{ value_type.native_cpp_type_name }}
	{{ Type.access }}INC_DECLARE_STLMULTISET({{ Type.unscoped_name }}, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE, {{ Type.inner_access }})
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE
{%- elsif Type.is_multi_map -%}
	{%- assign key_type = Type.first_param_drop -%}
	{%- assign mapped_type = Type.second_param_drop -%}
	#define STLDECL_MANAGEDKEY {{ key_type.managed_cli_type_name }}
	#define STLDECL_MANAGEDVALUE {{ mapped_type.managed_cli_type_name }}
	#define STLDECL_NATIVEKEY {{ key_type.native_cpp_type_name }}
	#define STLDECL_NATIVEVALUE {{ mapped_type.native_cpp_type_name }}
	{{ Type.access }}INC_DECLARE_STLMULTIMAP({{ Type.unscoped_name }}, STLDECL_MANAGEDKEY, STLDECL_MANAGEDVALUE, STLDECL_NATIVEKEY, STLDECL_NATIVEVALUE, {{ Type.inner_access }})
	#undef STLDECL_MANAGEDKEY
	#undef STLDECL_MANAGEDVALUE
	#undef STLDECL_NATIVEKEY
	#undef STLDECL_NATIVEVALUE
{%- elsif Type.is_unordered_map-%}
	{%- assign key_type = Type.first_param_drop -%}
	{%- assign mapped_type = Type.second_param_drop -%}
	#define STLDECL_MANAGEDKEY {{ key_type.managed_cli_type_name }}
	#define STLDECL_MANAGEDVALUE {{ mapped_type.managed_cli_type_name }}
	#define STLDECL_NATIVEKEY {{ key_type.native_cpp_type_name }}
	#define STLDECL_NATIVEVALUE {{ mapped_type.native_cpp_type_name }}
	{{ Type.access }}INC_DECLARE_STLHASHMAP({{ Type.unscoped_name }}, STLDECL_MANAGEDKEY, STLDECL_MANAGEDVALUE, STLDECL_NATIVEKEY, STLDECL_NATIVEVALUE, {{ Type.inner_access }})
	#undef STLDECL_MANAGEDKEY
	#undef STLDECL_MANAGEDVALUE
	#undef STLDECL_NATIVEKEY
	#undef STLDECL_NATIVEVALUE
{%- elsif Type.is_shared_ptr -%}
	{{ Type.render_as_shared_ptr }}
{%- else -%}
	// {{ Type.unscoped_name }} unsupported.
{%- endif -%}
{%- unless Type.is_embeded -%}
{%- for ns in Type.export_namespace_list -%}
}   //end namespace {{ns}}
{%- endfor -%}
{%- endunless -%}
