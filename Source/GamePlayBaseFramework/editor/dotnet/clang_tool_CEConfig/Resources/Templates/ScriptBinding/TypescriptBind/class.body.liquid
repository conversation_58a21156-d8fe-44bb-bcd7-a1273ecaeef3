
{%- capture delegate_class_name -%}{{class.delegate_script_name}}{%- endcapture -%}
{%- capture cpp_class_name -%}{{class.full_name}}{%- endcapture -%}
namespace cross::scripts {
void {{delegate_class_name}}::Class()
{
    {%- if class.is_template_specialized_class -%}
        {%- if class.is_resource_pointer -%}
            {%- include 'ScriptBinding/TypescriptBind/TemplateSpecialized/resourcePointerExtensionStruct.body' with class -%}
        {%- endif -%}
        {%- comment -%} other template specialized class {%- endcomment -%}
    {%- endif -%}
    ::PUERTS_NAMESPACE::DefineClass<{{cpp_class_name}}>({% if is_plug_in %} ::PUERTS_NAMESPACE::RegisterType::PlugIn {% else %} ::PUERTS_NAMESPACE::RegisterType::Core {% endif %})
    {%- if class.has_ts_base -%}
        .Extends<{{class.ts_base_full_name}}>()
    {%- endif -%}
    {%- if class.constructors.size == 0 -%}
        .Constructor<>()
    {%- elseif class.ts_constructors.size == 1 -%}
        .Constructor<{{class.ts_constructors[0].str_parameters_no_name}}>()
    {%- elseif class.ts_constructors.size > 1 -%}
        .Constructor(CombineConstructors(
        {%- for cons in class.ts_constructors -%}
            {%- if cons.str_parameters_no_name == blank -%}
        MakeConstructor({{cpp_class_name}}){% if forloop.last == false %},{% endif %}
            {%- else -%}
        MakeConstructor({{cpp_class_name}}, {{cons.str_parameters_no_name}}){% if forloop.last == false %},{% endif %}
            {%- endif -%}
        {%- endfor -%}
        ))
    {%- endif -%}
    {%- for field in class.fields -%}
    {%- if field.need_binding_script -%}
        {%- if field.is_static -%}
            {%- if field.is_writable -%}
        .Variable("{{field.name}}", MakeVariable(&{{cpp_class_name}}::{{field.name}}))
            {%- elsif field.is_readable  -%}
        .Variable("{{field.name}}", MakeReadonlyProperty(&{{cpp_class_name}}::{{field.name}}))
            {%- endif -%}
        {%- else -%}
            {%- if field.is_writable -%}
        .Property("{{field.name}}", MakeProperty(&{{cpp_class_name}}::{{field.name}}))
            {%- elsif field.is_readable  -%}
        .Property("{{field.name}}", MakeReadonlyProperty(&{{cpp_class_name}}::{{field.name}}))
            {%- endif -%}
        {%- endif -%}
    {%- endif -%}
    {%- endfor -%}
    {%- assign type_script_function = class.type_script_function -%}
    {%- for func in type_script_function.select_functions -%}
        {%- if func.is_static -%}
        .Function("{{func.name}}", SelectFunction({{func.type_script_function_signature}}, &{{cpp_class_name}}::{{func.name}}))
        {%- else -%}
        .Method("{{func.name}}", SelectFunction({{func.type_script_function_signature}}, &{{cpp_class_name}}::{{func.name}}))
        {%- endif -%}
    {%- endfor -%}
    {%- for item in type_script_function.overload_static_functions -%}
        {%-  assign func_name = item.Key -%}
        {%-  assign funcs = item.Value -%}
        .Function("{{func_name}}", CombineOverloads(
        {%- for func in funcs -%}
            MakeOverload({{func.type_script_function_signature}}, &{{cpp_class_name}}::{{func.name}}){% if forloop.last == false %},{% endif %}
        {%- endfor -%}
        ))
    {%- endfor -%}
    {%- for item in type_script_function.overload_method_functions -%}
        {%-  assign func_name = item.Key -%}
        {%-  assign funcs = item.Value -%}
        .Method("{{func_name}}", CombineOverloads(
        {%- for func in funcs -%}
            MakeOverload({{func.type_script_function_signature}}, &{{cpp_class_name}}::{{func.name}}){% if forloop.last == false %},{% endif %}
        {%- endfor -%}
        ))
    {%- endfor -%}
    {%- for func in type_script_function.method_as_property_functions -%}
        {%- if func.is_const and func.parameters.size == 0 -%}
        .Property("CEMethodAsProperty_{{func.name}}", MakeMethodAsProperty({{cpp_class_name}}, &{{cpp_class_name}}::{{func.name}}))
        {%- else -%}
        {{cpp_class_name}}::{{func.name}} is not a func as "T GetXXX() const"
        {%- endif -%}
    {%- endfor -%}
    {%- if class.is_template_specialized_class -%}
        {%- if class.is_resource_pointer -%}
            {%- include 'ScriptBinding/TypescriptBind/TemplateSpecialized/resourcePointerExtensionRegister.body' with class -%}
        {%- endif -%}
        {%- comment -%} other template specialized class {%- endcomment -%}
    {%- endif -%}
        .Register();
}
}

{%- for func in class.functions -%}
        {%- if func.is_script_impl -%}
{{func.return_type.full_name}} {{cpp_class_name}}::{{func.name}}({{func.str_parameters}})
{
{%- if func.return_type.is_void -%} 
    mTSObject.Action("{{func.name}}", {{func.str_parameters_no_type}});
{%- else -%}
    return mTSObject.Func<{{func.return_type.full_name}}>("{{func.name}}", {{func.str_parameters_no_type}});
{%- endif -%}
}
        {%- endif -%}
{%- endfor -%}