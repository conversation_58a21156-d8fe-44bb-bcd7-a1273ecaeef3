/* ----------------------------------------------------------------------------
 * This file was automatically generated by CodeGen {% include 'version' %}
 * --------------------------------------------------------------------------*/

#include "EnginePrefix.h"
 {%- if gen_lua -%}
#include "ScriptEngine/ScriptEngine.h"
#include "Scripts/ScriptModule.h"
{% endif %}
{%- for include in includes -%}
#include "{{include}}"
{%- endfor -%}

namespace cross::scripts {

void CodeGenRegisterGeneratedClass()
{ 
{%- if gen_lua -%}
    auto engine = cross::ScriptModule::Instance().GetScriptEnginePtr();
    script::EngineScope engineScope(engine.get());
    {%- for class in classes -%}
     {%- if class.need_binding_lua -%}
    engine->registerNativeClass(cross::scripts::{{class.script_name}}::Class());
    {%- endif -%}
    {%- endfor -%}
    {% if need_extend_components %}
    {%- for class in classes -%}
    {%- if class.is_system -%}
    {%- capture component_name -%}{{class.name | replace: "SystemG", "Component"}}{%- endcapture -%}
    engine->getClassDefine<cross::scripts::Entity>().AppendInstanceFunc("Get{{component_name}}", &cross::scripts::Entity::GetComponent<{{class.script_name}}>);
    engine->getClassDefine<cross::scripts::Entity>().AppendInstanceFunc("Has{{component_name}}", &cross::scripts::Entity::HasComponent<{{class.script_name}}>);
    {%- endif -%}
    {%- endfor -%}
    {% if classes.size > 0 %}
    engine->registerNativeClass(engine->getClassDefine<cross::scripts::Entity>().getNativeRegister());
    {% endif %}
    {% endif %}

{% endif %}

    // class
    {%- for class in classes -%}
    {%- if class.need_binding_ts -%}
    cross::scripts::{{class.delegate_script_name}}::Class();
    {%- endif -%}
    {%- endfor -%} 

    // Enum
    {%- for enum in enums -%}
    {%- if enum.need_binding_ts -%}
    cross::scripts::{{enum.delegate_script_name}}::Class();
    {%- endif -%}
    {%- endfor -%} 
}
}
