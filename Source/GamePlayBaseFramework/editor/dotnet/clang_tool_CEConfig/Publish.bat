@echo off

set HASH=""
FOR /F %%i IN ('git rev-parse --short HEAD') DO set HASH=%%i

set out_path="Publish"
IF NOT "%~1" == "" (
    set out_path=%~1
)

echo "out path: %out_path%"

dotnet publish -c Release --no-self-contained -p:PublishSingleFile=true -r win-x64 --version-suffix=%HASH% -o %out_path%\win
dotnet publish -c Release --no-self-contained -p:PublishSingleFile=true -r linux-x64 --version-suffix=%HASH% -o %out_path%\linux
dotnet publish -c Release --no-self-contained -p:PublishSingleFile=true -r osx-x64 --version-suffix=%HASH% -o %out_path%\osx
