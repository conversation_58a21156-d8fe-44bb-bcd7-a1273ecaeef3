using DotLiquid;

namespace CppAst.Pyra
{
    public class EnumDrop : General.EnumDrop
    {
        ////private static Template embedded_template_;
        private static Template msEnumLT;

        static EnumDrop()
        {
            ////embedded_template_ = TemplateHelper.ParseTemplate(Exporter.kPonderLiquidTemplatePath + "/Templates/embedded_enum.liquid");
            msEnumLT = Clangen.TemplateHelper.ParseTemplate("Exporters/ExportPyra/Templates/global_enum.liquid");
        }

        public EnumDrop(string className, CppEnum e, Exporter exporter) :
            base(className, e, exporter)
        {
        }

        internal string GenerateRenderContent(DotLiquid.Template tempLT, string useCase, bool stripEndLineWrap = false)
        {
            string result = tempLT.Render(DotLiquid.Hash.FromAnonymousObject(new
            {
                @enum = this,
                use_case = useCase,
            }));

            return stripEndLineWrap ? Clangen.NamespaceTool.TrimTailLineWrap(result) : result;
        }


        public string RenderEmbeded
        {
            get
            {
                return GenerateRenderContent(msEnumLT, "Embeded");
            }
        }

        public string Render
        {
            get
            {
                return GenerateRenderContent(msEnumLT, "Ponder");
            }
        }
        public string RenderLua
        {
            get
            {
                return GenerateRenderContent(msEnumLT, "Lua");
            }
        }
    }
}
