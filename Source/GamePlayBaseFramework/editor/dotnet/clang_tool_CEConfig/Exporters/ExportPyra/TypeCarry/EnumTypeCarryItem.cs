using System;

namespace CppAst.Pyra
{
    public class EnumTypeCarrayItem : General.TypeCarryItem
    {
        public EnumTypeCarrayItem()
        {
            mNativeTypeName = "enum";
        }

        public override string ToTypeName(TypeConverterType convType, General.TypeContentOffer typeContent)
        {
            var enumdrop = Exporter.Instance.TryGetCollectEnumDrop(typeContent.FullCookName);
            switch (convType)
            {
                case TypeConverterType.BridgeCliInput:
                case TypeConverterType.BridgeCliReturn:
                    if (enumdrop != null)
                    {
                        return Clangen.NamespaceTool.CookNamespaceToCppNamespace(enumdrop.ExportCookName);
                    }
                    else
                    {
                        return "int";
                    }

                    return "int";
            }
            throw new NotImplementedException();
        }
        public override string DoCallCovert(CallConverterType callConvType, string callExpr, General.TypeContentOffer typeContent)
        {
            if (callConvType == CallConverterType.Bridge2Native)
            {
                return string.Format("static_cast<{0}>({1})", typeContent.FullCppName, callExpr);
            }
            else if (callConvType == CallConverterType.Native2Bridge)
            {
                var enumdrop = Exporter.Instance.TryGetCollectEnumDrop(typeContent.FullCookName);
                if (enumdrop != null)
                {
                    return string.Format("({1})((int){0})", callExpr, Clangen.NamespaceTool.CookNamespaceToCppNamespace(enumdrop.ExportCookName));
                }
                else
                {
                    return string.Format("((int){0})", callExpr);
                }
            }
            ////else if(callConvType == CallConverterType.CsApi2Bridge)
            ////{
            ////    return string.Format("(int){0}", callExpr);
            ////}
            ////else if(callConvType == CallConverterType.CsBridge2Api)
            ////{
            ////    var enumdrop = Exporter.Instance.TryGetCollectEnumDrop(typeContent.FullCookName);
            ////    if(enumdrop != null)
            ////    {
            ////        return string.Format("({0}){1}", enumdrop.ExportCookName, callExpr);
            ////    }
            ////}

            return callExpr;
        }

        public override bool IsValidForExport(General.TypeContentOffer typeContent)
        {
            return true;
        }

        public override string DoAssignment(General.TypeCarryItem.CallConverterType callType, string leftExpr, string rightExpr, General.TypeContentOffer typeContent)
        {

            var stringTypeOffer = typeContent as General.EnumTypeContentOffer;

            if (callType == CallConverterType.Bridge2Native)
            {
                return string.Format("{0} = static_cast<{2}>({1})", leftExpr, rightExpr, typeContent.FullCppName);
            }


            return base.DoAssignment(callType, leftExpr, rightExpr, typeContent);
        }
    }
}
