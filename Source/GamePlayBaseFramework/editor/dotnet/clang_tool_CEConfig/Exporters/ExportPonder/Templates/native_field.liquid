{%- case use_case -%}
{%comment%}----------------------------------------------------------------------------------{%endcomment%}
  {%- when 'Static' -%}
.static_property("{{ field.name }}", &{{ field.class_name }}::{{ field.name }}) {%comment%}.static_property("{{ field.name }}", [](){ return {{ field.class_name }}::{{ field.name }}; }){%endcomment%}
{%comment%}----------------------------------------------------------------------------------{%endcomment%}
  {%- when 'Normal' -%}
.property("{{ field.name }}", &{{ field.class_name }}::{{ field.name }}){% if field.tag_number != 0 %}.property_tag_number({{field.tag_number}}){% endif %}
{%comment%}----------------------------------------------------------------------------------{%endcomment%}
  {%- when 'CharArray' -%}
.property("{{ field.name }}", 
				[]({{ field.class_name }}& cls){ cls.{{ field.name }}[ {{ field.char_array_size }} - 1] = 0; return std::string(cls.{{ field.name }}); }, 
				[]({{ field.class_name }}& cls, const std::string& str){ strncpy(cls.{{ field.name }}, str.c_str(), {{ field.char_array_size }});} )
	{% if field.tag_number != 0 %}.property_tag_number({{field.tag_number}}){% endif %}
{%- endcase -%}