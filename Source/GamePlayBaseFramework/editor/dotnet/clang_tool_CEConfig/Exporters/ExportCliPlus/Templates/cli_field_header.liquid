{%- capture field_decl -%}{% if field.is_static %}static {% endif %} property {{field.bridge_cpp_return_type_name }} {{ field.name }}{%- endcapture -%}
    [System::ComponentModel::BrowsableAttribute(false)]
    {%- if field.cs_attributes.size > 0 -%}
        %csattributes {{field.name}} "{%- for item in field.cs_attributes -%}[{{- item -}}]{%- endfor -%}"
    {%- endif -%}
    {{ field_decl | strip_newlines }}
    {
    public:
        {{field.bridge_cpp_return_type_name }} get();
{%- unless field.is_const -%}
        void set({{ field.bridge_cpp_set_param_list_string }} );
{%- endunless -%}
    }