using System.Collections.Generic;
using System.Text;

namespace CppAst.General
{
    public enum TypeContentKind : int
    {
        Unknown = 0,
        BuildIn,
        String,
        Enum,
        Record,
        Pointer,
        LReference,
        RReference,
        TemplateInstance,
        ComponentHandle,
        Delegate,
        Typedef,
    }

    public enum StringTypeKind : int
    {
        RawString = 0,
        CharArray = 1,
        StdString = 2,
        OldStringArray = 3,
        VmcoreConstString = 4,
    }

    public enum LinkKindType : int
    {
        NoLink = 0,
        LinkChild = 1,
        LinkAlias = 2,
    }

    public abstract class TypeContentOffer
    {
        public abstract TypeContentKind Kind
        {
            get;
        }

        public bool IsEnum => Kind == TypeContentKind.Enum;

        public bool IsString => Kind == TypeContentKind.String;

        public bool IsBuildIn => Kind == TypeContentKind.BuildIn;

        public bool IsRecord => Kind == TypeContentKind.Record;

        public bool IsTemplateInstanse => Kind == TypeContentKind.TemplateInstance;

        public bool IsPointerOrReference => IsPointer || IsReference;

        public bool IsPointer => Kind == TypeContentKind.Pointer;

        public bool IsReference => Kind == TypeContentKind.LReference || Kind == TypeContentKind.RReference;

        public bool IsComponentHandle => Kind == TypeContentKind.ComponentHandle;

        public bool IsVoid => Kind == TypeContentKind.BuildIn && (this as BuildInTypeContentOffer).IsBuildInVoid;

        public bool IsDelegate => Kind == TypeContentKind.Delegate;

        public bool IsBuildInArray { get; set; }

        public int ReferenceCountToTarget
        {
            get
            {
                int refCount = 0;
                var cur = this;
                while (cur != null)
                {
                    if (cur.IsReference) refCount++;
                    cur = cur.LinkToType;
                }
                return refCount;
            }
        }

        public int PointerCountToTarget
        {
            get
            {
                int ptCount = 0;
                var cur = this;
                while (cur != null)
                {
                    if (cur.IsPointer) ptCount++;
                    cur = cur.LinkToType;
                }
                return ptCount;
            }
        }


        public bool IsBuildInPointer => ReferenceCountToTarget == 0 && PointerCountToTarget == 1 && LinkListTargetType.IsBuildIn;

        public bool IsBuildInReference => ReferenceCountToTarget == 1 && PointerCountToTarget == 0 && LinkListTargetType.IsBuildIn;

        public bool IsAliasToOrRealBuildIn => LinkListTargetType.IsBuildIn && ReferenceCountToTarget == 0 && PointerCountToTarget == 0;


        public bool LinkListNoPR
        {
            get
            {
                var curType = this;
                while (curType != null)
                {
                    if (curType.IsPointerOrReference)
                    {
                        return false;
                    }

                    curType = curType.LinkToType;
                }
                return true;
            }
        }

        //First level is record or template instance
        public bool IsValueUDT => LinkListNoPR && (LinkListTargetType.Kind == TypeContentKind.Record || LinkListTargetType.Kind == TypeContentKind.TemplateInstance);

        public bool IsSelfConst
        {
            get;
            protected set;
        }

        ////public string ConstPrefix => IsConst ? "const " : "";

        public abstract string LeafCppName
        {
            get;
        }

        public abstract string FullCppName
        {
            get;
        }

        public virtual LinkKindType LinkKind => LinkKindType.NoLink;

        public string FullCookName
        {
            get
            {
                return Clangen.NamespaceTool.CppNamespaceToCookNamespace(FullCppName);
            }
        }
        public abstract TypeContentOffer LinkToType
        {
            get;
        }

        public TypeContentOffer LinkListTargetType
        {
            get
            {
                var curType = this;
                while (curType != null)
                {
                    if (curType.LinkKind == LinkKindType.NoLink)
                    {
                        return curType;
                    }

                    curType = curType.LinkToType;
                }
                return this;
            }
        }

        public bool LinkListHasConst
        {
            get
            {
                var curType = this;
                while (curType != null)
                {
                    if (curType.IsSelfConst)
                    {
                        return true;
                    }

                    curType = curType.LinkToType;
                }
                return false;
            }
        }

        public string PointerOrReferencePrefixForLinkList
        {
            get
            {
                StringBuilder prefix = new StringBuilder();
                ////bool hasConst = false;
                var curType = this;
                while (curType != null)
                {
                    ////hasConst = hasConst || curType.IsSelfConst;
                    if (curType.IsPointerOrReference)
                    {
                        var ptType = curType as PointerOrReferenceTypeContentOffer;
                        prefix.Append(ptType.PrefixString);
                    }

                    curType = curType.LinkToType;
                }

                return prefix.ToString();
            }
        }

        public virtual string GetFullCppNameWithAliasNameMap(Clangen.Parser.ClangAST.AliasNameMap aliasMap)
        {
            return FullCppName;
        }
    }

    public class UnknownTypeContentOffer : TypeContentOffer
    {
        string mDebugTypeName;

        public override TypeContentKind Kind => TypeContentKind.Unknown;

        public override string LeafCppName => mDebugTypeName;

        public override string FullCppName => mDebugTypeName;

        public override TypeContentOffer LinkToType => null;

        public UnknownTypeContentOffer(string debugName)
        {
            mDebugTypeName = debugName;
        }
    }

    public class EnumTypeContentOffer : TypeContentOffer
    {
        //private CXType mNativeEnumType;
        string full_cpp_name_;
        public EnumTypeContentOffer(string full_cpp_name)
        {
            full_cpp_name_ = full_cpp_name;
        }

        public override TypeContentKind Kind => TypeContentKind.Enum;

        public override string LeafCppName => Clangen.NamespaceTool.GetOnlyLeafName(FullCppName);

        public override string FullCppName => full_cpp_name_;

        public override TypeContentOffer LinkToType => null;
    }

    public class BuildInTypeContentOffer : TypeContentOffer
    {
        string mNativeTypeName;
        bool mIsBuildInVoid;

        public BuildInTypeContentOffer(string nativeTypeName, bool isConst, bool isVoid)
        {
            mNativeTypeName = nativeTypeName;
            IsSelfConst = isConst;
            mIsBuildInVoid = isVoid;
        }

        public bool IsBuildInVoid => mIsBuildInVoid;

        public override TypeContentKind Kind => TypeContentKind.BuildIn;

        public override string LeafCppName => mNativeTypeName;

        public override string FullCppName => mNativeTypeName;

        public override TypeContentOffer LinkToType => null;
    }

    public class StringTypeContentOffer : TypeContentOffer
    {
        public string TypeName
        {
            get;
            private set;
        }
        public StringTypeKind DetailType
        {
            get;
            private set;
        }

        public int CharArraySize
        {
            get;
            private set;
        }

        public StringTypeContentOffer(string typeName, StringTypeKind detailType, bool isConst, int charArraySize)
        {
            TypeName = typeName;
            DetailType = detailType;
            IsSelfConst = isConst;
            CharArraySize = charArraySize;
        }

        public override TypeContentKind Kind => TypeContentKind.String;

        public override string LeafCppName => TypeName;

        public override string FullCppName => TypeName;

        public override TypeContentOffer LinkToType => null;
    }
    public class DelegateTypeContentOffer : TypeContentOffer
    {
        public string ReturnTypeName
        {
            get;
            private set;
        }
        public List<string> ParamNames
        {
            get;
            private set;
        }
        public string cppName { get; set; }
        public DelegateTypeContentOffer(string typeName, List<string> paramNames, string cppname)
        {
            ReturnTypeName = typeName;
            ParamNames = paramNames;
            cppName = cppname;
        }

        public override TypeContentKind Kind => TypeContentKind.Delegate;

        public override string LeafCppName => cppName;

        public override string FullCppName => cppName;

        public override TypeContentOffer LinkToType => null;
    }

    public class RecordTypeContentOffer : TypeContentOffer
    {
        string mLeafName;
        string mFullName;
        public RecordTypeContentOffer(string leafName, string fullName, bool isConst)
        {
            mLeafName = leafName;
            mFullName = fullName;
            IsSelfConst = isConst;
        }

        public bool IsArrayContainer => FullCppName.StartsWith("std::vector<") || FullCppName.StartsWith("std::array<");

        public override TypeContentKind Kind => TypeContentKind.Record;

        public override string LeafCppName => mLeafName;

        public override string FullCppName => mFullName;

        public override TypeContentOffer LinkToType => null;
    }
    public class ComponentHandleContentOffer : TypeContentOffer
    {
        string mLeafName;
        string mFullName;
        public ComponentHandleContentOffer(string leafName, string fullName, bool isConst, bool isWriter, string componentName, string systemName)
        {
            mLeafName = leafName;
            mFullName = fullName;
            IsSelfConst = isConst;
            IsComponentReader = !isWriter;
            IsComponentWriter = isWriter;
            ComponentName = componentName;
            SystemName = systemName;
        }

        public bool IsArrayContainer => FullCppName.StartsWith("std::vector<") || FullCppName.StartsWith("std::array<");

        public override TypeContentKind Kind => TypeContentKind.ComponentHandle;

        public override string LeafCppName => mLeafName;

        public override string FullCppName => mFullName;

        public string ComponentName;
        public string SystemName;

        public override TypeContentOffer LinkToType => null;
        public bool IsComponentReader { get; }
        public bool IsComponentWriter { get; }
    }

    public class PointerOrReferenceTypeContentOffer : TypeContentOffer
    {
        TypeContentOffer mLinkTo;
        TypeContentKind mDetailKind;

        public PointerOrReferenceTypeContentOffer(TypeContentOffer basicType,
            string prefixString,
            TypeContentKind detailKind,
            bool isConst)
        {
            mLinkTo = basicType;
            PrefixString = prefixString;
            mDetailKind = detailKind;
            IsSelfConst = isConst;
        }

        public string PrefixString
        {
            get;
            private set;
        }

        public override TypeContentKind Kind => mDetailKind;

        public override string LeafCppName => LinkToType.LeafCppName;

        public override string FullCppName
        {
            get
            {
                return string.Format("{0} {1}{2} {3}",
                    LinkToType.LinkListHasConst ? "const" : "",
                    LinkToType.FullCppName,
                    PointerOrReferencePrefixForLinkList,
                    IsSelfConst ? "const" : "");
            }
        }

        public override string GetFullCppNameWithAliasNameMap(Clangen.Parser.ClangAST.AliasNameMap aliasMap)
        {
            return string.Format("{0} {1} {2} {3}",
                LinkToType.LinkListHasConst ? "const" : "",
                LinkToType.GetFullCppNameWithAliasNameMap(aliasMap),
                PointerOrReferencePrefixForLinkList,
                IsSelfConst ? "const" : "");
        }

        public override TypeContentOffer LinkToType => mLinkTo;

        public override LinkKindType LinkKind => LinkKindType.LinkChild;

    }

    public class TemplateInstanceTypeContentOffer : TypeContentOffer
    {
        string mNsName;
        string mTemplateInstanceName;

        public TemplateInstanceTypeContentOffer(string nsName, string tempInsName, bool isConst)
        {
            mNsName = nsName;
            mTemplateInstanceName = tempInsName;
            IsSelfConst = isConst;
        }

        public override TypeContentKind Kind => TypeContentKind.TemplateInstance;

        public override string LeafCppName => mTemplateInstanceName;

        public override string FullCppName => string.IsNullOrEmpty(mNsName) ? mTemplateInstanceName : string.Format("{0}::{1}", mNsName, mTemplateInstanceName);

        public bool IsArrayContainer => FullCppName.StartsWith("std::vector<") || FullCppName.StartsWith("std::array<");

        public override TypeContentOffer LinkToType => null;

        public override string GetFullCppNameWithAliasNameMap(Clangen.Parser.ClangAST.AliasNameMap aliasMap)
        {
            string nsCookName = Clangen.NamespaceTool.CppNamespaceToCookNamespace(mNsName);
            string tempInsCookName = string.IsNullOrEmpty(mNsName) ? mTemplateInstanceName : string.Format("{0}.{1}", nsCookName, mTemplateInstanceName);

            tempInsCookName = aliasMap.GetFinalyAliasName(tempInsCookName);

            return Clangen.NamespaceTool.CookNamespaceToCppNamespace(tempInsCookName);
        }
    }

    public class TypedefTypeContentOffer : TypeContentOffer
    {
        TypeContentOffer mAliasTypeOffer;
        string mLeafName;
        string mFullCppName;

        public TypedefTypeContentOffer(TypeContentOffer aliasType, string fullCppName, bool isConst)
        {
            mFullCppName = fullCppName;
            mLeafName = Clangen.NamespaceTool.GetOnlyLeafName(mFullCppName);
            mAliasTypeOffer = aliasType;
            IsSelfConst = isConst;
        }


        public override TypeContentKind Kind => TypeContentKind.Typedef;

        public override string LeafCppName => mLeafName;

        public override string FullCppName => mFullCppName;

        public override TypeContentOffer LinkToType => mAliasTypeOffer;

        public override LinkKindType LinkKind => LinkKindType.LinkAlias;
    }
}
