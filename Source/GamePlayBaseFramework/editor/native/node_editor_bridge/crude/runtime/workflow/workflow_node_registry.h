#pragma once

#include "workflow_node.h"
#include "node_editor_bridge_fwd.hpp"
#include "reflection/objects/make_user_object.hpp"
#include "blueprint/details/blueprint_graph_base.h"
#include "blueprint/details/blueprint_event.h"
#include <unordered_set>

#include "Input/blueprint_key_event_node.h"

namespace gbf { namespace logic {
    class UBpGraphBase;
}}   // namespace gbf::logic

namespace cross {
class NODE_EDITOR_BRIDGE_API CEMeta(Reflect) GlobalBlueprintEventLibrary : public gbf::reflection::RttiBase
{
public:
    struct EventDesc
    {
        std::string event_name;
        std::string description;
        gbf::logic::UBlueprintEvent::EventParamInfoLists param_list;
        const gbf::reflection::MetaClass* related_node_class = nullptr;
    };

    static GlobalBlueprintEventLibrary& GetInstance()
    {
        static GlobalBlueprintEventLibrary ins;
        return ins;
    }

    void AddEvent(EventDesc && e)
    {
        m_all_global_events[e.event_name] = std::move(e);
    }

    bool HasEvent(const std::string& event_name)
    {
        return m_all_global_events.find(event_name) != m_all_global_events.end();
    }

    const EventDesc& GetEventDesc(const std::string& event_name)
    {
        return m_all_global_events[event_name];
    }

    void RemoveEvent(const std::string& event_name)
    {
        if (HasEvent(event_name))
        {
            m_all_global_events.erase(m_all_global_events.find(event_name));
        }
    }

    template<typename Lambda>
    void TraverseAllEvents(Lambda func)
    {
        for (auto& itr: m_all_global_events)
        {
            func(itr.first, itr.second);
        }
    }

private:
    std::map<std::string, EventDesc> m_all_global_events;
};

class NODE_EDITOR_BRIDGE_API WorkflowNodeRegistry
{
public:
    struct NodeCreator
    {
        std::string className;
        std::string menuName;
        std::vector<std::string> categories;
        const gbf::reflection::MetaClass* metaClass = nullptr;
        const gbf::reflection::Property* metaProp = nullptr;
        const gbf::reflection::IFuncCaller* metaFunc = nullptr;
        bool isPropGet = false;
        bool isNamedVar = false;
        bool isBpCallFunc = false;
        bool isEventCall = false;
        const GlobalBlueprintEventLibrary::EventDesc* descForEventNode = nullptr;
        std::function<std::shared_ptr<WorkflowNode>(gbf::logic::UBpGraphBase*)> createFunction;
        bool hasInput;
        bool hasOutput;
        bool allowEditedByUser;
        std::shared_ptr<WorkflowNode> Create(gbf::logic::UBpGraphBase* logicGraph);
    };

    // template <typename NodeType>
    // static void RegisterNode(bool allowEditedByUser = true);

    static WorkflowNodeRegistry& GetInstance() {
        static WorkflowNodeRegistry _instance;
        return _instance;
    }

    const auto& GetAllNodeCreators() const
    {
        return m_NodeCreators;
    }

    std::shared_ptr<WorkflowNode> CreateNode(const std::string& name, gbf::logic::UBpGraphBase* logicGraph);
    void ResetAgent(const std::string& agentClassName);
    void AddNamedVar(const std::string& varName);
    void DeleteNamedVar(const std::string& varName);
    void AddBlueprintFunction(const std::string& funcName, const std::string& description);
    void DeleteBlueprintFunction(const std::string& funcName);

    void AddEvent(const std::string& eventName, const std::string& description, const GlobalBlueprintEventLibrary::EventDesc* related_node_class = nullptr);
    void RenameEvent(const std::string& eventName, const std::string& new_name, const std::string& new_description);
    void DeleteEvent(const std::string& eventName);

    template<typename T>
    void RegisterNode(const std::string& className, const std::string& catName)
    {

        NodeCreator temp_creator;
        temp_creator.className = className ;
        temp_creator.hasInput = true;
        temp_creator.hasOutput = true;
        temp_creator.allowEditedByUser = true;

        temp_creator.menuName = className;
        temp_creator.categories.push_back(catName);
        temp_creator.createFunction = [](gbf::logic::UBpGraphBase* logic_graph) {
            auto cur_logic_node_raw = logic_graph->CreateNodeByType<T>();
            auto cur_logic_node_shared = logic_graph->GetNodeById(cur_logic_node_raw->id());
            auto cur_workflow_node = gbf::reflection::make_shared_with_rtti<WorkflowNode>();
            cur_workflow_node->Init(cur_logic_node_shared);
            return cur_workflow_node;
        };
        m_NodeCreators[temp_creator.className] = temp_creator;
    }

private:
    bool TryAddClass(const gbf::reflection::MetaClass* metaClass);
    void RegisterFunction(const gbf::reflection::MetaClass* metaClass, const gbf::reflection::IFuncCaller* func);
    void RegisterProperty(const gbf::reflection::MetaClass* metaClass, const gbf::reflection::Property* prop);

    void RegisterDefaultCreators();

private:
    WorkflowNodeRegistry(){}

    std::unordered_map<std::string, NodeCreator> m_NodeCreators;
    std::vector<const gbf::reflection::MetaClass*> m_WaitingQueue;
    std::unordered_set<const gbf::reflection::MetaClass*> m_AlreadyInQueue;
    const gbf::reflection::MetaClass* m_BaseRttiMeta;
};

}   // namespace cross