#pragma once

#include <variant>
#include "crude/runtime/crude/crude_utils.h"

namespace crude_blueprint {


// A type-eraser implement for crude blueprints
struct PinValue {
  using ValueType = std::variant<std::monostate, FlowPin*, bool, int32_t, float, std::string>;

  PinValue() = default;
  PinValue(const PinValue&) = default;
  PinValue(PinValue&&) = default;
  PinValue& operator=(const PinValue&) = default;
  PinValue& operator=(PinValue&&) = default;

  PinValue(FlowPin* pin) : m_Value(pin) {}
  PinValue(bool value) : m_Value(value) {}
  PinValue(int32_t value) : m_Value(value) {}
  PinValue(float value) : m_Value(value) {}
  PinValue(std::string&& value) : m_Value(std::move(value)) {}
  PinValue(const std::string& value) : m_Value(value) {}
  PinValue(const char* value) : m_Value(std::string(value)) {}

  PinType GetType() const { return static_cast<PinType>(m_Value.index()); }

  template <typename T>
  T& As() {
    return std::get<T>(m_Value);
  }

  template <typename T>
  const T& As() const {
    return std::get<T>(m_Value);
  }

 private:
  ValueType m_Value;
};


}

