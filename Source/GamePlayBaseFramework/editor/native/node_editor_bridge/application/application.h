#pragma once
#include <imgui.h>
#include <memory>
#include <string>

#include "node_editor_bridge_fwd.hpp"
#include "core/utils/byte_buffer.h"
#include "CEMetaMacros.h"
struct Platform;
struct Renderer;

namespace node_editor {

struct NODE_EDITOR_BRIDGE_API CEMeta(Cli) Application {
  CEMeta(Cli) Application();
  CEMeta(Cli) Application(const char* name);


  Application(const char* name, int argc, char** argv);

  CEMeta(Cli) ~Application();

  CEMeta(Cli) bool Create(int width = -1, int height = -1);

  CEMeta(Cli) bool CreateExternal(int width, int height, uint64_t external_hwnd);

  CEMeta(Cli) void RebindWindowCallback();

  CEMeta(Cli) uint64_t OnExternalWindowMessage(uint64_t hwnd, int msg, uint64_t wparam, uint64_t lparam);

  CEMeta(Cli) void OnInputTextByIME(const char* text);

  CEMeta(Cli) int Run();

  CEMeta(Cli) void SetTitle(const char* title);

  CEMeta(Cli) bool Close();
  CEMeta(Cli) void Quit();

  CEMeta(Cli) const std::string& GetName() const;

  CEMeta(Cli) ImFont* DefaultFont() const;
  CEMeta(Cli) ImFont* HeaderFont() const;

  CEMeta(Cli) ImTextureID LoadTexture(const char* path);

  CEMeta(Cli) ImTextureID LoadTextureFromMemory(gbf::ByteBufferPtr buf);

  CEMeta(Cli) ImTextureID CreateTexture(const void* data, int width, int height);

  CEMeta(Cli) void DestroyTexture(ImTextureID texture);

  CEMeta(Cli) int GetTextureWidth(ImTextureID texture);

  CEMeta(Cli) int GetTextureHeight(ImTextureID texture);

  CEMeta(Cli) virtual void OnStart() {}
  CEMeta(Cli) virtual void OnStop() {}
  CEMeta(Cli) virtual void OnFrame(float deltaTime) {}

  CEMeta(Cli) virtual ImGuiWindowFlags GetWindowFlags() const;

  CEMeta(Cli) virtual bool CanClose() { return true; }

  CEMeta(Cli) void Frame();
 private:
  CEMeta(Cli) void RecreateFontAtlas();

  

  std::string m_Name;
  std::string m_IniFilename;
  std::unique_ptr<Platform> m_Platform;
  std::unique_ptr<Renderer> m_Renderer;
  ImGuiContext* m_Context = nullptr;
  ImFont* m_DefaultFont = nullptr;
  ImFont* m_HeaderFont = nullptr;
};

}  // namespace node_editor

////int Main(int argc, char** argv);