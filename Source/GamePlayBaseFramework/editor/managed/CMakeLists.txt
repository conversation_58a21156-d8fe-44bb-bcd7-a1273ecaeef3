cmake_minimum_required(VERSION 3.16)

set(managed_gbf "managed_gbf")
# set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /Zc:twoPhase-")

# Physx support add here
include_directories(
    "${PROJECT_SOURCE_DIR}/externals/include/physx"
)
set(LIBRARY_OUTPUT_PATH "${CMAKE_BINARY_DIR}")
set(EXECUTABLE_OUTPUT_PATH "${CMAKE_BINARY_DIR}")
if(WIN32)
    link_directories(
        "${PROJECT_SOURCE_DIR}/externals/lib/win"
    )
else()
    message(STATUS "Not support in other platforms!")
endif()

#include_directories(
#    ${PROJECT_SOURCE_DIR}/third_party
#    ${PROJECT_SOURCE_DIR}/base
#    ${PROJECT_SOURCE_DIR}/meta
#    ${PROJECT_SOURCE_DIR}/async
#    ${PROJECT_SOURCE_DIR}/imodules
#    ${PROJECT_SOURCE_DIR}/logic
#    ${CMAKE_CURRENT_SOURCE_DIR}
#
#    ${PROJECT_SOURCE_DIR}/editor/native/node_editor_bridge
#)

link_directories(
    ${CMAKE_BINARY_DIR}
)

add_definitions(
    -DGBF_CLI_EXPORTS
    -DCLANGEN_ENABLE_USE_RTTIBASE_SUPPORT
)

set(all_project_src "")

file(GLOB managed_gbf_src "*.*")
source_group(\\ FILES ${managed_gbf_src})
list(APPEND all_project_src ${managed_gbf_src})

file(GLOB utils_src "utils/*.*")
source_group(\\utils FILES ${utils_src})
list(APPEND all_project_src ${utils_src})

file(GLOB managed_common_src "cli/common/*.*")
source_group(cli\\common FILES ${managed_common_src})
list(APPEND all_project_src ${managed_common_src})

file(GLOB managed_common_container_src "cli/common/container/*.*")
source_group(cli\\common\\container FILES ${managed_common_container_src})
list(APPEND all_project_src ${managed_common_container_src})

file(GLOB managed_common_gc_src "cli/common/gc/*.*")
source_group(cli\\common\\gc FILES ${managed_common_gc_src})
list(APPEND all_project_src ${managed_common_gc_src})

file(GLOB managed_auto_src "cli/auto/*.*")
source_group(cli\\auto FILES ${managed_auto_src})
list(APPEND all_project_src ${managed_auto_src})
add_library(${managed_gbf} SHARED 
    ${all_project_src}
)
set(MANAGED_GBF_VCXPROJ_PATH "${CMAKE_CURRENT_BINARY_DIR}/managed_gbf.vcxproj" CACHE FILEPATH "Path to managed_gbf.vcxproj")

# Create a temporary variable with the global flags
set(MODIFIED_CXX_FLAGS "${CMAKE_CXX_FLAGS}")

# Replace the conflicting flags with the ones we need for C++/CLI
string(REPLACE "/EHsc" "/EHa" MODIFIED_CXX_FLAGS "${MODIFIED_CXX_FLAGS}")

# Now, set the final, correct flags for all source files in this target.
# This includes the essential /clr switch and the C++17 standard.
set_source_files_properties(${all_project_src}
    PROPERTIES COMPILE_FLAGS "${MODIFIED_CXX_FLAGS} /clr"
)
if(GBF_COMPILE_WITH_CE)
    set(dependItems CrossEngine CrossBase CECommon ECS CrossPhysics CEResource RenderEngine CEEditorRuntime GameFramework)
	get_property(m3ddirs GLOBAL PROPERTY "M3RDDirs")
	get_managed_thirdparty_dir(MANAGED_THIRDPARTY_DIR)
	foreach(target ${m3ddirs})
		include_directories(SYSTEM "${target}")
	endforeach()
	## definitions
	get_property(definitions DIRECTORY ${CROSSENGINE_SRC_DIR}/Source PROPERTY COMPILE_DEFINITIONS) 
	foreach(def ${definitions})
	 target_compile_definitions(${managed_gbf} PUBLIC "${def}")
	endforeach()
	## CXX flags
	get_property(cppflags GLOBAL PROPERTY "CPPFLAG")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${cppflags}")
    include_directories(${CROSSENGINE_SRC_DIR}/BuildingBlocks/EditorRuntime)
	include_directories("${CROSSENGINE_SRC_DIR}/Source/Projects/PrecompiledHeaders")
	include_directories("${CROSSENGINE_SRC_DIR}/Source")
	include_directories("${CROSSENGINE_SRC_DIR}/Source/External")
	include_directories("${CROSSENGINE_SRC_DIR}/Source/Reflection/include")
    include_directories("${CROSSENGINE_SRC_DIR}/Source/CEGameFramework")
    include_directories("${CROSSENGINE_SRC_DIR}/Source/Scripts")
	include_directories(${ENGINE_GENERATED_CODE_DIR})
	foreach(item ${dependItems})
		include_directories("${CROSSENGINE_SRC_DIR}/Source/${item}")
		add_dependencies(${managed_gbf} ${item})
		target_link_libraries(${managed_gbf} ${item})
	endforeach()
endif()
add_dependencies(${managed_gbf} 
    gbf_core
    reflection
    imod_shared
    #
    # config_system
    # job_system
    # lua_system
    # net_libevent
    # net_raw_socket
    # package_system
    # gbf_log

    node_editor_bridge
    AssetPipeline
)
set_target_properties(${managed_gbf} PROPERTIES COMMON_LANGUAGE_RUNTIME "")
target_link_libraries(${managed_gbf} 
    gbf_core
    reflection
    imod_shared
    #
    # config_system
    # job_system
    # lua_system
    # net_libevent
    # net_raw_socket
    # package_system
    # gbf_log
    node_editor_bridge
)

target_include_directories(${managed_gbf} 
   PUBLIC $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/third_party> $<INSTALL_INTERFACE:Source/GamePlayBaseFramework/third_party>
   PUBLIC $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/base> $<INSTALL_INTERFACE:Source/GamePlayBaseFramework/base>
   PUBLIC $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/meta> $<INSTALL_INTERFACE:Source/GamePlayBaseFramework/meta>
   PUBLIC $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/logic> $<INSTALL_INTERFACE:Source/GamePlayBaseFramework/logic>
   PUBLIC $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/async> $<INSTALL_INTERFACE:Source/GamePlayBaseFramework/async>
   PUBLIC $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/imodules> $<INSTALL_INTERFACE:Source/GamePlayBaseFramework/imodules>
   PUBLIC $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/editor/managed/>  $<INSTALL_INTERFACE:Source/GamePlayBaseFramework/editor/managed/>

   PUBLIC $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/editor/native/node_editor_bridge>  $<INSTALL_INTERFACE:Source/GamePlayBaseFramework/editor/native/node_editor_bridge>
)

#set_target_properties(${managed_gbf} PROPERTIES COMPILE_FLAGS "/clr /EHa")
set_target_properties(${managed_gbf} PROPERTIES VS_DOTNET_REFERENCES "System;System.Core;System.Data;System.Drawing;System.Xml;WindowsBase")
set_target_properties(${managed_gbf} PROPERTIES COMMON_LANGUAGE_RUNTIME "")
set_target_properties(${managed_gbf} PROPERTIES UNITY_BUILD OFF)
set_target_properties(${managed_gbf} PROPERTIES LINK_FLAGS "/ignore:4099")

SET_PROPERTY(TARGET ${managed_gbf} PROPERTY FOLDER "editor/managed")

#set_output_dir(${managed_gbf})
#set_target_properties(${managed_gbf} PROPERTIES VS_DOTNET_REFERENCES "System" "System.Data" "System.Drawing" "System.Windows.Forms" "System.Xml")
    
# target_link_libraries(${managed_gbf} debug gtestd${CMAKE_STATIC_LIBRARY_SUFFIX}
#                                         optimized gtest${CMAKE_STATIC_LIBRARY_SUFFIX})

# target_link_libraries(${managed_gbf} gtest${CMAKE_STATIC_LIBRARY_SUFFIX})

# add_test(NAME ${managed_gbf} 
#     COMMAND ${managed_gbf})


#set(EXECUTABLE_OUTPUT_PATH ${CMAKE_BINARY_DIR})
