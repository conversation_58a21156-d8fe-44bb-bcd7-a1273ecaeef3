#include "clr_class_attribute.h"

namespace ClangenCli
{
	//-------------------------------------------------------------------------------------------
	System::String^ ClassCategory::CategoryPath::get()
	{
		return mCategoryPath;
	}

	void ClassCategory::CategoryPath::set(System::String^ value)
	{
		mCategoryPath = value;
	}
	//-------------------------------------------------------------------------------------------
	System::String^ ClassDisplayName::DisplayName::get()
	{
		return mDisplayName;
	}

	void ClassDisplayName::DisplayName::set(System::String^ value)
	{
		mDisplayName = value;
	}
	//-------------------------------------------------------------------------------------------

	bool ClassVisible::IsVisible::get()
	{
		return mIsVisible;
	}

	void ClassVisible::IsVisible::set(bool value)
	{
		mIsVisible = value;
	}
	//-------------------------------------------------------------------------------------------
}
