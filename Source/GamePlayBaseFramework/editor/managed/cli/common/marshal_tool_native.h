#pragma once
#include <cstring>
#include <string>
#include <vcclr.h>
#include "GamePlayBaseFramework/editor/managed/cli/common/container/stl_vector.h" 
namespace cross{}
namespace ClangenCli {

#define CLR_NULL ((Object ^) nullptr)

#define DECLARE_NATIVE_STRING(nvar, mstr)                                                                                                                                                                                                      \
    std::string nvar;                                                                                                                                                                                                                          \
    InitNativeStringWithCLRString(nvar, mstr);

////#define DECLARE_NATIVE_UTFSTRING(utfnvar,m_str)								\
////		Captain::UTFString utfnvar;													\
////		InitNativeUTFStringWithCLRString(utfnvar,m_str);
////
////#define DECLARE_NATIVE_CONST_STRING(utfnvar,m_str)								\
////		Captain::ConstString utfnvar;													\
////		InitNativeConstStringWithCLRString(utfnvar,m_str);

#define SET_NATIVE_STRING(nvar, mstr) InitNativeStringWithCLRString(nvar, mstr);
////#define SET_NATIVE_UTFSTRING(nvar, mstr)	InitNativeUTFStringWithCLRString(nvar, mstr);
#define TO_CLR_STRING(capstr) gcnew System::String((capstr).c_str())
////#define CONST_STRING_TO_CLR_STRING(capstr)			gcnew System::String((capstr).c_str())
////#define UTF_TO_CLR_STRING(capstr)			gcnew System::String((capstr).asWStr_c_str())
inline std::wstring ToNativeWideString(System::String ^ str)
{
    pin_ptr<wchar_t const> ptr = PtrToStringChars(str);
    return std::wstring(ptr, str->Length);
}

inline System::String ^
    ToManagedStringWithDefault(const std::string& str) { return gcnew System::String(str.c_str(), 0, (int)str.length()); }

    inline System::String
    ^
    ToManagedString(const std::string& str) { return gcnew System::String(str.c_str(), 0, (int)str.length(), System::Text::Encoding::UTF8); }

    inline System::String
    ^
    ToManagedString(const std::wstring& str) { return gcnew System::String(str.c_str(), 0, (int)str.length()); }

    inline System::String
    ^
    ToManagedString(char const* str) { return gcnew System::String(str, 0, (int)strlen(str), System::Text::Encoding::UTF8); }

    template<typename T>
    inline System::String ^
    ToManagedString(T const& obj) { return ToManagedString(obj.c_str()); }

    template<typename T>
    inline System::String ^ ToManagedString(T* ptr) { return ToManagedString(*ptr); }

    inline void InitNativeStringWithCLRString(std::string& ostr, System::String ^ mstr)
{
    if (mstr == nullptr)
    {
        ostr = "";
        return;
    }

    System::IntPtr p_mstr = System::Runtime::InteropServices::Marshal::StringToHGlobalAnsi(mstr);
    ostr = static_cast<char*>(p_mstr.ToPointer());
    System::Runtime::InteropServices::Marshal::FreeHGlobal(p_mstr);
}
////void InitNativeUTFStringWithCLRString(UTFString& ostr, System::String^ mstr);
////void InitNativeConstStringWithCLRString(ConstString& ostr, System::String^ mstr);

// Most of Cap classes that are wrapped by MCaptain derive from CLRObject.
// It acts as the connection between the .NET objects and the Cap objects that they wrap.
// Without it, a new .NET object will be created each time an Cap object is requested.
// In order to use it, a rebuild of CapMain, the renderers and the plugins is required.
// It doesn't interfere with the Cap code; native applications that use Cap will link to the
// new DLLs without problems.

// ToNative and ToManaged are used to simplify conversions inside templates

template<typename M, typename N>
inline N ToNative(M value)
{
    return (N)value;
}

template<typename M, typename N>
inline M ToManaged(const N& value)
{
    return (M) const_cast<N&>(value);
}

inline std::string ToNativeDefaultString(System::String ^ mstr)
{
    std::string ostr;
    InitNativeStringWithCLRString(ostr, mstr);
    return ostr;
}

// By default, C++ use UTF-8 encoding string
inline std::string ToNativeString(System::String ^ mstr)
{
    std::string ostr;
    if (mstr)
    {
        array<System::Byte> ^ bytes = System::Text::Encoding::UTF8->GetBytes(mstr);
        ostr.resize(bytes->Length);
        System::Runtime::InteropServices::Marshal::Copy(bytes, 0, System::IntPtr((void*)ostr.data()), bytes->Length);
        ostr.push_back(0);   // make sure there is a null terminator
        ostr.pop_back();
    }
    return ostr;
    // System::IntPtr pointer = Marshal::StringToHGlobalAnsi(mstr);
    // char* charPointer = reinterpret_cast<char*>(pointer.ToPointer());
    // std::string returnString{ charPointer, static_cast<size_t>(mstr->Length) };
    // Marshal::FreeHGlobal(pointer);
    // return returnString;
}
////inline System::String^ ToManagedString(const Captain::ConstString& str)
////{
////	return CONST_STRING_TO_CLR_STRING(str);
////}

////inline Captain::ConstString ToNativeConstString(System::String^ str)
////{
////	DECLARE_NATIVE_CONST_STRING(o_str, str)
////	return o_str;
////}

////inline System::String^ ToManagedConstString(const Captain::ConstString& str)
////{
////	return CONST_STRING_TO_CLR_STRING(str);
////}

template<>
inline std::string ToNative(System::String ^ str)
{
    DECLARE_NATIVE_STRING(o_str, str)
    return o_str;
}

////template <>
////inline Captain::ConstString ToNative(System::String^ str)
////{
////	return ToNativeConstString(str);
////}

////template <>
////inline Captain::GUI::UString ToNative(System::String^ str)
////{
////	return Captain::GUI::UString{ ToNativeString(str) };
////}

template<>
    inline System::String ^ ToManaged(const std::string& str) { return TO_CLR_STRING(str); }

    ////template <>
    ////inline System::String^ ToManaged(const Captain::ConstString& str)
    ////{
    ////	return ToManagedConstString(str);
    ////}

    ////template <>
    ////inline System::String^ ToManaged(Captain::GUI::UString const& str)
    ////{
    ////	return ToManagedString(str.c_str());
    ////}

}