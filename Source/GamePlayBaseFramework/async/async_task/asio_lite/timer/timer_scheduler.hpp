#pragma once

#include "async_task/async_task_config.hpp"
#include "async_task/asio_lite/timer/timer_scheduler_fwd.hpp"

#include "async_task/asio_lite/timer/winrt_timer_scheduler.hpp"

////#if defined(ASIO_WINDOWS_RUNTIME)
////# include "asio/detail/winrt_timer_scheduler.hpp"
////#elif defined(ASIO_HAS_IOCP)
////# include "asio/detail/win_iocp_io_context.hpp"
////#elif defined(ASIO_HAS_EPOLL)
////# include "asio/detail/epoll_reactor.hpp"
////#elif defined(ASIO_HAS_KQUEUE)
////# include "asio/detail/kqueue_reactor.hpp"
////#elif defined(ASIO_HAS_DEV_POLL)
////# include "asio/detail/dev_poll_reactor.hpp"
////#else
////# include "asio/detail/select_reactor.hpp"
////#endif

