#pragma once

#include <unordered_set>

#include "async_task/async_task_export.hpp"
#include "async_task/tasks/iasync_task.hpp"

#include "async_task/tasks/coroutines/cotask17.hpp"
#include "async_task/tasks/coroutines/impl17/new_co_task.hpp"

#include "async_task/tasks/iawaitable17.hpp"

#include "reflection/builder/class_builder.hpp"
////#include "reflection/objects/make_user_object.hpp"

namespace gbf::coro {

class ASYNC_TASKS_API async_task17 : public iasync_task {
 public:
  // Nested Types
  using awaitable_ptr = std::shared_ptr<tasks17::iawaitable17>;

 public:
  async_task17(uint64_t taskId, coro::coroutine_handle17 coHandle, coro_service_manager* manager);

  ~async_task17();

  template <typename AwaitableType>
  void do_awaitable_suspend(AwaitableType&& awaitable) {
    using transform_helper = cotask17_need_await_transform<AwaitableType>;
    if constexpr (std::is_base_of_v<tasks17::iawaitable17, AwaitableType>) {
      //normal awaitable just call impl version here
      do_awaitable_suspend_impl(std::forward<AwaitableType>(awaitable));
    } else if constexpr (transform_helper::value) {
      //transform awaitable support here, do transform first, then call impl version
      do_awaitable_suspend_impl(transform_helper::co17_transform(std::move(awaitable)));
    } else {
      SAFE_STATIC_ASSERT_FALSE("Invalid awaitable type for async_task17::do_awaitable_suspend()!");
    }
  }

  //real implement for awaitable suspend implement
  template <typename AwaitHandleType>
  auto do_awaitable_suspend_impl(AwaitHandleType&& awaitHandle) -> std::enable_if_t<std::is_base_of<tasks17::iawaitable17, AwaitHandleType>::value> {
    ////mAwaitHandle = reflection::make_user_object(std::forward<AwaitHandleType>(awaitHandle));
    current_awaitable_ = std::make_shared<AwaitHandleType>(std::forward<AwaitHandleType>(awaitHandle));

    if (current_awaitable_->is_await_ready()) {
      // Just add to immediate run queue
      await_setting(AwaitMode::kAwaitNever);
    } else {
      // Call suspend here~
      do_awaitable_suspend_impl();
    }
  }

  void clear_awaitable() {
    await_mode_ = AwaitMode::kAwaitUnknown;
    await_timeout_ = 0;

    current_awaitable_.reset();
  }

  bool has_awaitable() const { return current_awaitable_.operator bool(); }

  const awaitable_ptr& get_awaitable() const { return current_awaitable_; }

  uint64_t get_return_type_id() const override { return co_handle_.get_return_type_id(); }

  std::string_view get_return_type_name() const override { return co_handle_.get_return_type_name(); }

  const coro_promise17* get_promise() const { return co_handle_.get_pointer(); }
 protected:
  CoroRunningState resume_impl(JobType job_type) override;
  ////bool IsDoneImpl() const override { return mCoHandle.done(); }

  AwaitMode do_awaitable_suspend_impl();
  void do_awaitable_resume_impl();

 protected:
  coroutine_handle17  co_handle_;
  // Await handle here is actor as a system call~~
  awaitable_ptr             current_awaitable_;
};
}  // namespace gbf::coro
