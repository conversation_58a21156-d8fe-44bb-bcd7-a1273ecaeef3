#pragma once

#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <ctime>

#define BYTESIZE (8 * sizeof(unsigned char))

namespace rstudio_original {
template <int iSize>
class CBitMap {
 public:
  CBitMap();
  ~CBitMap();

  CBitMap<iSize>& operator=(CBitMap<iSize>& rExBitMap);
  bool IsBitSetted(int iBitSeq) const;
  int SetBit(int iBitSeq);
  int ClearBit(int iBitSeq);
  int ClearAllBits();
  int GetSettedBits(int* piBitSeqs);
  int Get(int* piSize, char* pbyMap);
  int Set(int iInputSize, char* pbyMap);
  void Show(FILE* fpOut);

 private:
  unsigned char m_acBitMap[iSize];
};

template <int iSize>
CBitMap<iSize>::CBitMap() {
  // m_nMaxNumber = nSize;
}

template <int iSize>
CBitMap<iSize>::~CBitMap() {}

template <int iSize>
CBitMap<iSize>& CBitMap<iSize>::operator=(CBitMap<iSize>& rExBitMap) {
  memcpy((void*)m_acBitMap, (const void*)rExBitMap.m_acBitMap, sizeof(m_acBitMap));
  return *this;
}

template <int iSize>
bool CBitMap<iSize>::IsBitSetted(int iBitSeq) const {
  int iMapIdx = 0;
  int iBitIdx = 0;
  unsigned char ucTempSMap = 0x80;

  if (iBitSeq < 0 || iBitSeq >= (int)(iSize * BYTESIZE)) {
    return false;
  }

  iMapIdx = iBitSeq / BYTESIZE;
  iBitIdx = iBitSeq % BYTESIZE;

  if (m_acBitMap[iMapIdx] & (ucTempSMap >> iBitIdx)) {
    return true;
  } else {
    return false;
  }
}

template <int iSize>
int CBitMap<iSize>::SetBit(int iBitSeq) {
  int iMapIdx = 0;
  int iBitIdx = 0;
  unsigned char ucTempSMap = 0x80;

  if (iBitSeq < 0 || iBitSeq >= (int)(iSize * BYTESIZE)) {
    return -1;
  }

  iMapIdx = iBitSeq / BYTESIZE;
  iBitIdx = iBitSeq % BYTESIZE;

  m_acBitMap[iMapIdx] |= (ucTempSMap >> iBitIdx);

  return 0;
}

template <int iSize>
int CBitMap<iSize>::ClearBit(int iBitSeq) {
  int iMapIdx = 0;
  int iBitIdx = 0;
  unsigned char ucTempSMap = 0x80;

  if (iBitSeq < (int)0 || iBitSeq >= (int)(iSize * BYTESIZE)) {
    return 0;
  }

  iMapIdx = iBitSeq / BYTESIZE;
  iBitIdx = iBitSeq % BYTESIZE;

  m_acBitMap[iMapIdx] &= (0xff ^ (ucTempSMap >> iBitIdx));

  return 0;
}

template <int iSize>
int CBitMap<iSize>::ClearAllBits() {
  memset((void*)m_acBitMap, 0, sizeof(m_acBitMap));
  return 0;
}

template <int iSize>
int CBitMap<iSize>::GetSettedBits(int* piBitSeqs) {
  int iBitCount;
  int i;

  if (!piBitSeqs) {
    return -1;
  }
  iBitCount = 0;

  for (i = 0; i < iSize * BYTESIZE; i++) {
    if (IsBitSetted(i)) {
      piBitSeqs[iBitCount] = i;
      iBitCount++;
    }
  }

  return iBitCount;
}

template <int iSize>
int CBitMap<iSize>::Get(int* riSize, char* pbyMap) {
  *riSize = iSize;
  memcpy((void*)pbyMap, (const void*)m_acBitMap, *riSize);
  return 0;
}

template <int iSize>
int CBitMap<iSize>::Set(int iInputSize, char* pbyMap) {
  assert(iInputSize <= iSize);
  memcpy((void*)m_acBitMap, (const void*)pbyMap, iInputSize);
  return 0;
}

template <int iSize>
void CBitMap<iSize>::Show(FILE* fpOut) {
  int i;

  if (!fpOut) {
    return;
  }
  fprintf(fpOut, "BITMAP:\n");

  int iBitCount = (int)(iSize * BYTESIZE);
  for (i = 0; i < iBitCount; i++) {
    fprintf(fpOut, "%2d", IsBitSetted(i));
  }

  fprintf(fpOut, "\n");
  fflush(fpOut);
}

}  // namespace gbf_original
/*
int main()
{
        int iItem;
        int i;
        int *pHeadInt = NULL;

        CBitMap<6> bmTemp1, bmTemp2;

        bmTemp1.ClearAllBits();
        bmTemp1.SetBit(13);
        bmTemp1.SetBit(16);
        bmTemp1.SetBit(17);
        bmTemp1.SetBit(8);
        bmTemp1.SetBit(35);
        bmTemp1.ClearBit(34);
        bmTemp1.SetBit(36);
        bmTemp1.ClearBit(35);

        bmTemp2 = bmTemp1;

        bmTemp1.Show(stdout);
        bmTemp2.Show(stdout);

        printf("(34 bit is set) = %d\n", bmTemp2.IsBitSetted(34));

        printf("(6 bit is set) = %d\n", bmTemp2.IsBitSetted(6));

        return 0;
}
*/
