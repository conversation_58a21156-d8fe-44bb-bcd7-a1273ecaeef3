#include "core/core_global.hpp"
#include "core/modules/default_log_module.h"
#include "core/modules/default_profiler_module.h"
#include "memoryhooker/Module.h"
IMPORT_MODULE

gbf::GameFramework* GGame = nullptr;

static gbf::DefaultLogModule SDefaultLogModule;
gbf::ILogModule* GLog = &SDefaultLogModule;


static gbf::DefaultProfilerModule SDefaultProfilerModule;
gbf::IProfilerModule* GProfiler = &SDefaultProfilerModule;
gbf::IProfilerModule* GDefaultProfiler = &SDefaultProfilerModule;

static gbf::threads::ThreadTimer SGlobalTimer;
gbf::threads::ThreadTimer* GTimer = &SGlobalTimer;

