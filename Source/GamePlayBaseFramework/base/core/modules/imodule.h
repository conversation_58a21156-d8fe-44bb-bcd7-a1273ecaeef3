// Copyright 2020-2025 RStudio Games, Inc. All Rights Reserved.
#pragma once

/*
        game pipeline will be ensure:	the module init first, will last call release.
                                                                        the module start first, will last call stop.
*/
#include <sstream>

#include "core/config.hpp"
#include "iinterface_manager.h"  //all module use this

namespace gbf {
static const int kMaxModuleNameLen = 64;

enum class ModuleCallReturnStatus : int {
  Succeed = 0,
  Fail,
  Pending,
};
#pragma warning(push)
#pragma warning(disable : 4251)
class GBF_CORE_API IModule {
  friend class GameFramework;

 public:
  IModule() : interface_mgr_(0), module_state_(ModuleRunState::kStateReleased), is_resume_(false) {}

  virtual ModuleCallReturnStatus Init() = 0;
  virtual ModuleCallReturnStatus Start() = 0;
  virtual ModuleCallReturnStatus Update() = 0;
  virtual ModuleCallReturnStatus Stop() = 0;
  ////virtual ModuleCallReturnStatus PreRelease() = 0;
  virtual ModuleCallReturnStatus Release() = 0;

  /* call this free this module, system will not use this pointer of the module. */
  virtual void Free() = 0;

  ModuleRunState GetState() const { return module_state_; }

  void SetState(ModuleRunState state) { module_state_ = state; }

  /**
   * @fn	virtual void OnMemoryWarning(int level = 0)
   *
   * @brief	Executes the memory warning action. Usually it means out of memory now, we must release some cached data
   * immediately.
   *
   * @param	level	(Optional) The level. 0: normal 1 : risky 2 : critical
   */
  virtual void OnMemoryWarning(int level = 0) { level = 0; }

  virtual void OnUpdateIdle() {}

  const std::string& GetModuleName() const { return module_name_; }

  bool IsResume() const { return is_resume_; }
  void SetResume(bool is_resume) { is_resume_ = is_resume; }

 protected:
  virtual ~IModule() {}
  IInterfaceMgr* Interface() { return interface_mgr_; }

 protected:
  IInterfaceMgr* interface_mgr_;
  ModuleRunState module_state_;
  bool is_resume_;
  std::string module_name_;
};
#pragma warning(pop)
}  // namespace gbf

/*
        macro use to define depend of the modules.
        EXPECT_ON		if target module is already registed, wait for expect state, or target module goto fail
   state, this two condition all treat as successed, otherwise will return pending.
        DEPEND_ON		target module must be registed, and goto state expect, if target module not registed or
   return failed, macro will return Fail, otherwise return pending.

        Attention: use need to keep module depend not loop.
*/
#define _EXPECT_ON(module_name, ws)                                                                                    \
  {                                                                                                                    \
    gbf::ModuleRunState state = GetState();                                                                        \
    gbf::ModuleRunState wait_state = (gbf::ModuleRunState)-1;                                                  \
    switch (state) {                                                                                                   \
      case gbf::ModuleRunState::kStateIniting:                                                                     \
      case gbf::ModuleRunState::kStateReleased:                                                                    \
        wait_state = gbf::ModuleRunState::kStateInited;                                                            \
        break;                                                                                                         \
      case gbf::ModuleRunState::kStateInited:                                                                      \
      case gbf::ModuleRunState::kStateStarting:                                                                    \
        wait_state = gbf::ModuleRunState::kStateStarted;                                                           \
        break;                                                                                                         \
      case gbf::ModuleRunState::kStateStarted:                                                                     \
      case gbf::ModuleRunState::kStateUpdating:                                                                    \
        wait_state = gbf::ModuleRunState::kStateUpdated;                                                           \
        break;                                                                                                         \
      case gbf::ModuleRunState::kStateUpdated:                                                                     \
      case gbf::ModuleRunState::kStateStoping:                                                                     \
        wait_state = gbf::ModuleRunState::kStateStopped;                                                           \
        break;                                                                                                         \
      case gbf::ModuleRunState::kStateStopped:                                                                     \
      case gbf::ModuleRunState::kStateReleasing:                                                                   \
        wait_state = gbf::ModuleRunState::kStateReleased;                                                          \
        break;                                                                                                         \
      default:                                                                                                         \
        break;                                                                                                         \
    }                                                                                                                  \
    if (wait_state == ws) {                                                                                            \
      gbf::IModule* querymodule = Interface()->QueryModule(module_name);                                           \
      if (querymodule != 0 &&                                                                                          \
          (querymodule->GetState() != wait_state && querymodule->GetState() != gbf::ModuleRunState::kStateFail)) { \
        return gbf::IModule::CallReturnStatus::Pending;                                                            \
      }                                                                                                                \
    }                                                                                                                  \
  }

#define _DEPEND_ON(module_name, ws)                                           \
  {                                                                           \
    gbf::ModuleRunState state = GetState();                               \
    gbf::ModuleRunState wait_state = (gbf::ModuleRunState)-1;         \
    switch (state) {                                                          \
      case gbf::ModuleRunState::kStateIniting:                            \
      case gbf::ModuleRunState::kStateReleased:                           \
        wait_state = gbf::ModuleRunState::kStateInited;                   \
        break;                                                                \
      case gbf::ModuleRunState::kStateInited:                             \
      case gbf::ModuleRunState::kStateStarting:                           \
        wait_state = gbf::ModuleRunState::kStateStarted;                  \
        break;                                                                \
      case gbf::ModuleRunState::kStateStarted:                            \
      case gbf::ModuleRunState::kStateUpdating:                           \
        wait_state = gbf::ModuleRunState::kStateUpdated;                  \
        break;                                                                \
      case gbf::ModuleRunState::kStateUpdated:                            \
      case gbf::ModuleRunState::kStateStoping:                            \
        wait_state = gbf::ModuleRunState::kStateStopped;                  \
        break;                                                                \
      case gbf::ModuleRunState::kStateStopped:                            \
      case gbf::ModuleRunState::kStateReleasing:                          \
        wait_state = gbf::ModuleRunState::kStateReleased;                 \
        break;                                                                \
      default:                                                                \
        break;                                                                \
    }                                                                         \
    if (wait_state == ws) {                                                   \
      gbf::IModule* querymodule = Interface()->QueryModule(module_name);  \
      if (querymodule != 0) {                                                 \
        if (querymodule->GetState() == gbf::ModuleRunState::kStateFail) { \
          return gbf::ModuleCallReturnStatus::Fail;                       \
        } else if (querymodule->GetState() != wait_state) {                   \
          return gbf::ModuleCallReturnStatus::Pending;                    \
        }                                                                     \
      } else {                                                                \
        return gbf::ModuleCallReturnStatus::Fail;                         \
      }                                                                       \
    }                                                                         \
  }

#define DEPEND_ON_INIT(module_name) _DEPEND_ON(module_name, gbf::ModuleRunState::kStateInited)
#define DEPEND_ON_START(module_name) _DEPEND_ON(module_name, gbf::ModuleRunState::kStateStarted)
#define EXPECT_ON_INIT(module_name) _EXPECT_ON(module_name, gbf::ModuleRunState::kStateInited)
#define EXPECT_ON_START(module_name) _EXPECT_ON(module_name, gbf::ModuleRunState::kStateStarted)

#if _WINDOWS
#    pragma warning(disable : 4251)
#    pragma warning(disable : 4275)
#    define EXPORTS __declspec(dllexport)
#else
#    ifdef __GNUC__
#        define EXPORTS __attribute__((visibility("default")))
#    else
#        define EXPORTS
#    endif
#endif

#pragma warning(push)
#pragma warning(disable : 4100)
#define MAKE_MODULE(module_classname, module_name) \
extern "C" { \
EXPORTS gbf::IModule* module_name##DynlibCreateModule(gbf::IInterfaceMgr* interfaceMgr)                                                                                                                                                                 \
    { \
        auto* tmodule = new module_classname(); \
        if (interfaceMgr) \
        {\
            interfaceMgr->RegisterModule(#module_name, tmodule);\
        }\
    return tmodule;\
}\
EXPORTS void module_name##DynlibDestroyModule(gbf::IModule* tmodule) { tmodule = nullptr;}                                                                                                                                                                 \
    }

#pragma warning(pop)



