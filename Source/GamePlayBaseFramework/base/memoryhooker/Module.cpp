#include "Module.h"
#include "MemoryProfile.h"
void* cross::Memory::Malloc(size_t size, const char* module)
{
#if MIMALLOCENABLE
	auto ptr = mi_malloc(size);
#else
    auto ptr = ::malloc(size);
#endif
    ALLOC_NOTAGED(ptr, size, module);
    return ptr;
}

void cross::Memory::Free(void* ptr, const char* module)
{
    FREE_NOTAGED(ptr, module);
#if MIMALLOCENABLE
	mi_free(ptr);
#else
    ::free(ptr);
#endif
}

void* cross::Memory::Realloc(void* ptr, size_t size, const char* module)
{
#if MIMALLOCENABLE
	auto ptrnew = mi_realloc(ptr, size);
#else
    auto ptrnew = ::realloc(ptr, size);
#endif
    ALLOC_NOTAGED(ptrnew, size, module);
    return ptrnew;
}

void* cross::Memory::Calloc(size_t nitems, size_t size, const char* module)
{
#if MIMALLOCENABLE
	auto ptr = mi_calloc(nitems, size);
#else
    auto ptr = ::calloc(nitems, size);
#endif
    ALLOC_NOTAGED(ptr, size, module);
    return ptr;
}

void* cross::Memory::AlignedMalloc(size_t size, size_t alignment, const char* module)
{
#if MIMALLOCENABLE
	auto ptr = mi_malloc_aligned(size, alignment);
#else
#if defined(_WIN32)
    auto ptr = ::_aligned_malloc(size, alignment);
#else
    auto ptr = ::malloc(size);
#endif
#endif
    ALLOC_NOTAGED(ptr, size, module);
    return ptr;
}

void cross::Memory::AlignedFree(void* ptr, const char* module)
{
    FREE_NOTAGED(ptr, module);
#if MIMALLOCENABLE
    mi_free(ptr);
#else
#if defined(_WIN32)
    ::_aligned_free(ptr);
#else
    ::free(ptr);
#endif
#endif
}
