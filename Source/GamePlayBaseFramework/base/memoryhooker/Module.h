#pragma once

#include <new>
#include <memory>
#if _WINDOWS

#if defined(MEMORY_HOOKER_EXPORTS)
#        define MEMORY_HOOKER_API __declspec(dllexport)
#else
#        define MEMORY_HOOKER_API __declspec(dllimport)
#endif
////#	else
////#		define GBF_REFLECTION_API
////#	endif

#else
#    define MEMORY_HOOKER_API __attribute__((visibility("default")))
#endif   // GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32 || GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WINRT
#ifndef DISABLE_MIMALLOC
#define MIMALLOCENABLE  _WINDOWS && !_DEBUG
#else
#define MIMALLOCENABLE 0
#endif

#if MIMALLOCENABLE
    #include "../../../ManagedThirdParty_new/mimalloc/Includes/mimalloc.h"
#else
    #include <stdlib.h>
#endif
namespace cross
{
    struct  Memory
    {
        MEMORY_HOOKER_API static void* Malloc(size_t size, const char* module = "Other");
        MEMORY_HOOKER_API static void Free(void* ptr, const char* module = "Other");
        MEMORY_HOOKER_API static void* Realloc(void* ptr, size_t size, const char* module = "Other");
        MEMORY_HOOKER_API static void* Calloc(size_t nitems, size_t size, const char* module = "Other");
        MEMORY_HOOKER_API static void* AlignedMalloc(size_t size, size_t alignment, const char* module = "Other");
        MEMORY_HOOKER_API static void AlignedFree(void* ptr, const char* module = "Other");
    };
}

#if MIMALLOCENABLE && !(CROSSENGINE_IOS || CROSSENGINE_ANDROID)

#if defined(_MSC_VER) && defined(_Ret_notnull_) && defined(_Post_writable_byte_size_)
// stay consistent with VCRT definitions
#define ce_decl_new(n)          _Ret_notnull_ _Post_writable_byte_size_(n)
#define ce_decl_new_nothrow(n)  _Ret_maybenull_ _Success_(return != NULL) _Post_writable_byte_size_(n)
#else
#define ce_decl_new(n)
#define ce_decl_new_nothrow(n)
#endif

#define IMPORT_MODULE \
ce_decl_new(size) void* operator new(size_t size) noexcept(false) { return cross::Memory::Malloc(size); } \
ce_decl_new(size) void* operator new[](size_t size) noexcept(false) { return cross::Memory::Malloc(size); } \
\
ce_decl_new_nothrow(size) void* operator new(size_t size, const std::nothrow_t&) noexcept { return cross::Memory::Malloc(size); } \
ce_decl_new_nothrow(size) void* operator new[](size_t size, const std::nothrow_t&) noexcept { return cross::Memory::Malloc(size); } \
\
void operator delete(void* ptr) noexcept { cross::Memory::Free(ptr); } \
void operator delete[](void* ptr) noexcept { cross::Memory::Free(ptr); } \
\
void operator delete(void* ptr, const std::nothrow_t&) noexcept { cross::Memory::Free(ptr); } \
void operator delete[](void* ptr, const std::nothrow_t&) noexcept { return cross::Memory::Free(ptr); } \
\
void operator delete(void* ptr, size_t) noexcept { cross::Memory::Free(ptr); } \
void operator delete[](void* ptr, size_t) noexcept { cross::Memory::Free(ptr); } \
\
ce_decl_new(size) void* operator new(size_t size, std::align_val_t alignment) noexcept(false) { return cross::Memory::AlignedMalloc(size, static_cast<size_t>(alignment)); } \
ce_decl_new(size) void* operator new[](size_t size, std::align_val_t alignment) noexcept(false) { return cross::Memory::AlignedMalloc(size, static_cast<size_t>(alignment)); } \
\
ce_decl_new_nothrow(size) void* operator new(size_t size, std::align_val_t alignment, const std::nothrow_t&) noexcept { return cross::Memory::AlignedMalloc(size, static_cast<size_t>(alignment)); } \
ce_decl_new_nothrow(size) void* operator new[](size_t size, std::align_val_t alignment, const std::nothrow_t&) noexcept { return cross::Memory::AlignedMalloc(size, static_cast<size_t>(alignment)); } \
\
void operator delete(void* ptr, std::align_val_t) noexcept { cross::Memory::AlignedFree(ptr); } \
void operator delete[](void* ptr, std::align_val_t) noexcept { cross::Memory::AlignedFree(ptr); } \
\
void operator delete(void* ptr, std::align_val_t, const std::nothrow_t&) noexcept { cross::Memory::AlignedFree(ptr); } \
void operator delete[](void* ptr, std::align_val_t, const std::nothrow_t&) noexcept { cross::Memory::AlignedFree(ptr); } \
\
void operator delete(void* ptr, size_t, std::align_val_t ) noexcept { cross::Memory::AlignedFree(ptr); } \
void operator delete[](void* ptr, size_t, std::align_val_t) noexcept { cross::Memory::AlignedFree(ptr); }

#else
#define IMPORT_MODULE
#endif


#if MIMALLOCENABLE && !(CROSSENGINE_IOS || CROSSENGINE_ANDROID)
    template<class T>
    using ce_stl_allocator = mi_stl_allocator<T>;
#else
    template<class T>
    using ce_stl_allocator = std::allocator<T>;
#endif
