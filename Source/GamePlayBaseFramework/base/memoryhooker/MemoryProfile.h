#pragma once
#ifndef _MANAGED
#    include "tracy/Tracy.hpp"
#    include "tracy/TracyC.h"
#endif
#ifdef USE_PROFILERSTACKTRACEANDMEMORY
#    define ALLOC_TAGED(var, size, name)   TracyAllocNS(var, size, 3, name);
#    define FREE_TAGED(var, name)          TracyFreeNS(var, 3, name);
#    define ALLOC_NOTAGED(var, size, name) TracyAllocS(var, size, 3);
#    define FREE_NOTAGED(var, name)        TracyFreeS(var, 3);
#    define ALLOC_NGI_TAGED(var, size)     TracyAllocNS(var, size, 3, "NGIMemory");
#    define FREE_NGI_TAGED(var)            TracyFreeNS(var, 3, "NGIMemory");
#else
#    define ALLOC_TAGED(var, size, name)
#    define FREE_TAGED(var, name)
#    define ALLOC_NOTAGED(var, size, name)
#    define FREE_NOTAGED(var, name)
#    define ALLOC_NGI_TAGED(var, size)
#    define FREE_NGI_TAGED(var)
#endif