#pragma once

//
//  CrossEngine Global Memory Override Header
//
//  This file is force-included by the build system for all C++ source files.
//  It ensures that operator new/delete are globally and consistently overridden
//  using the definitions from Module.h. This prevents memory allocation
//  mismatches across different modules (DLLs).
//
//  DO NOT INCLUDE THIS FILE MANUALLY.
//

#include "Module.h"
// Apply the memory overrides globally.
// This must only be done ONCE in a single, force-included file like this one.
IMPORT_MODULE
