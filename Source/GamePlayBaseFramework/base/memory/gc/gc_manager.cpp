#include "memory/gc/gc_manager.hpp"

#include <atomic>
#include <cassert>

#include "memory/allocator/range_allocator.hpp"
#include "memory/gc/gc_info.hpp"
#include "memory/gc/run_scope.hpp"

namespace gbf {
namespace gc {

//-------------------------------------------------------------------------------------------
const int kMaxGCManagerNum = 16;
std::atomic<size_t> s_gcm_id_count{size_t{0}};
gc_manager* s_all_gc_manager[kMaxGCManagerNum];

gc_manager::gc_manager(bool is_main) : m_is_main(is_main) {
  m_id = s_gcm_id_count++;
  assert(m_id < kMaxGCManagerNum && "over max allowed gc manager number!");
  s_all_gc_manager[m_id] = this;

  static GCStatusNotifyListener sDefaultListner;
  m_gc_status_notify_listener = &sDefaultListner;

  m_now_mark_flag = (size_t)gc_flag_type::kMarkedGreen;


  m_all_objects.reserve(2048);
  m_pending_all_objects.reserve(2048);

  m_range_allocator = new allocator::RangeAllocator(allocator::kRangeAllocatorInitSize, allocator::kRangeAllocatorExtendSize);
}

gc_manager::~gc_manager() {
  // free all objs
  for (gc_info* o : m_all_objects) {
    o->dispose();
    free_memory(o);
  }

  s_all_gc_manager[m_id] = nullptr;

  for (Listener* listener : m_gc_listener_array) {
    delete listener;
  }
  m_gc_listener_array.clear();

  delete m_range_allocator;
  m_range_allocator = nullptr;
}

std::pair<gc_info*, void*> gc_manager::allocate(size_t size, size_t align /*= 0*/, int flags /*= MAY_TRIGGER_GC*/) {

  m_bytes_allocated_since_gc += size;

  if ((flags & MAY_TRIGGER_GC) != 0) {
    may_trigger_gc();
  }

  size_t align_size = bitset_detail::align_to(8, size);

  auto alloc_ret = m_range_allocator->Allocate(align_size + sizeof(gc_info), align == 0? 8: align);
  uint8_t* data_ptr =  static_cast<uint8_t*>(alloc_ret.first);
  gc_info* info = (gc_info*)(data_ptr + align_size);

  info->flags_ = 0;
  info->parent_id_ = m_id;
  info->obj_handle_ = ++m_obj_count;
  info->obj_offset_ = align_size;

  // we clear the memory here for the strategy like c# and lua
  memset(data_ptr, 0, align_size);

  ////RunScope* scope = current_scope();
  ////if (scope) {
  ////  scope->add_to_scope(info);
  ////} else {
  ////  m_all_objects.insert(m_all_objects.end(), info);
  ////}

  return std::make_pair(info, (void*)data_ptr);
}

void gc_manager::_mark(StatsInfo& stats) {
  // mark all the objects
  gc_info** slot;
  while ((slot = m_pending_stack.pop()) != nullptr) {
    // request deferred marking of the properties
    stats.slowly_marked++;
    mark_internal_object_scope(*slot);
  }
}

void gc_manager::dispose_one_obj(gc_info* info) {
  info->dispose();
  info->free_memory(this);
}

void gc_manager::free_memory(gc_info* info) {
  m_range_allocator->Free(info->get_obj_ptr());
}

void gc_manager::_sweep(StatsInfo& stats) {
  assert(m_pending_all_objects.empty());

  // collect unmarked objects, as well as clearing the mark of live objects
  for (gc_info* obj : m_all_objects) {
    if (obj->is_marked(m_now_mark_flag)) {
      m_pending_all_objects.push_back(obj);
      stats.not_collected++;
    } else {
      // dead, destroy
      dispose_one_obj(obj);
      ////::operator delete();
      stats.collected++;
    }
  }
}

////void GCManager::free_scope(std::uint64_t scope_id, const std::vector<GcInfo*>& scope_objects) noexcept {
////  used_scope_map_.erase(scope_id);
////  m_all_objects.insert(m_all_objects.end(), scope_objects.begin(), scope_objects.end());
////}

void gc_manager::trigger_gc() {
  ////ENSURE_THREAD_JOB_TYPE(ThreadJobSystemType::LogicJob);
  assert(m_pending_stack.empty());

  {
    m_gc_stage = gc_stage_state::kMarkStage;
  }

  m_gc_status_notify_listener->gc_start(this);
  StatsInfo stats;

  // setup new
  // mark global object
  TGCPageForGlobal::iterator page_iter(all_global_table_);
  while (gc_info** o = page_iter.get_used_and_next()) {
    mark_one_obj(*o);
    stats.on_global++;
  }

  // mark object in run scope
  for(auto& scope_pair: used_scope_map_)
  {
    auto scope = scope_pair.second.lock();
    if(scope)
    {
      // ToDo: now only handle trigger_gc thread, other thread need handle?
      scope->do_scope_mark();
      stats.on_stack += scope->total_objects();
      stats.not_collected += scope->total_objects();  // all scope objects need not free now
    }
  }

  // mark
  // mark object in pending stack
  m_gc_status_notify_listener->mark_start(this);
  _mark(stats);
  m_gc_status_notify_listener->mark_end(this);

  // atomic stage
  {
    m_gc_stage = gc_stage_state::kAtomicStage;

    // remark gray list here
    {
      for (gc_info* obj : m_weak_gray_list) {
        *m_pending_stack.push() = obj;
      }
      m_weak_gray_list.clear();
      _mark(stats);  // mark all pending objects again
    }

    // try add new insert object here

    // clear all not marked weak objects here.
    tpage<gc_info*>::iterator weak_page_iter_ts(m_weak_table_ts);
    while (gc_info** o = weak_page_iter_ts.get_used_and_next()) {
      gc_info* obj = *o;
      if (obj) {
        if (!obj->is_marked(m_now_mark_flag)) {
          *o = nullptr;  // clear weak reference
        }
      }
    }
  }

  // sweep
  {
    m_gc_stage = gc_stage_state::kSweepStage;
  }
  m_gc_status_notify_listener->sweep_start(this);
  _sweep(stats);
  m_gc_status_notify_listener->sweep_end(this);

  m_all_objects.swap(m_pending_all_objects);
  m_pending_all_objects.clear();  // clear pending vector

  m_now_mark_flag = (~m_now_mark_flag) & kMarkMasks;

  m_gc_status_notify_listener->gc_end(this, stats);

  {
    m_gc_stage = gc_stage_state::kOutGc;
  }
}

void gc_manager::may_trigger_gc() {
  if (m_bytes_allocated_since_gc >= m_cfg_gc_interval_bytes) {
    trigger_gc();
    m_bytes_allocated_since_gc = 0;
  }
}

void gc_manager::mark_one_obj(gc_info* info) {
  if (GBF_UNLIKELY(info == nullptr)) return;

  //return if already disposed
  if( info->is_disposed() ) return;

  // return if already marked
  if (info->is_marked(m_now_mark_flag)) return;

  // try init gc members
  // if (!obj->test_flag(GCObjFlagType::LinkMemberLoaded))
  //{
  //     //init gc members
  //     obj->do_gc_members_link();
  //     obj->set_flag(GCObjFlagType::LinkMemberLoaded);
  // }

  // mark
  info->do_mark_flag(m_now_mark_flag);

  // push to the mark stack
  if(info->vtbl_->has_child_)
  {
    *m_pending_stack.push() = info;
  }
}

void gc_manager::mark_internal_object_scope(gc_info* info) {
  info->vtbl_->mark_childs_(info, this);
}

gc_info** gc_manager::_allocate_global_slot() {
  return all_global_table_.allocate();
}

void gc_manager::_free_global_slot(gc_info** obj_slot) {
  all_global_table_.free(obj_slot);
}

gc_info** gc_manager::_allocate_local_slot() {
  return all_local_table_.allocate();
}

void gc_manager::_free_local_slot(gc_info** obj_slot) {
  all_local_table_.free(obj_slot);
}

bool gc_manager::_is_local_slot(gc_info** obj_slot) const {
  return all_local_table_.contains(obj_slot);
}

gc_info** gc_manager::_allocate_weak_slot() {
  gc_info** weak_slot = m_weak_table_ts.allocate();
  ////if (!obj->IsDisposed()) {
  ////  *weak_slot = obj;
  ////  ////m_obj_to_weak_multimap_ts.insert(std::make_pair(obj, weak_slot));
  ////} else {
  ////  // not save it to multimap(not need)
  ////  *weak_slot = nullptr;
  ////}

  return weak_slot;
}

void gc_manager::_free_weak_slot(gc_info** weak_slot) {
  m_weak_table_ts.free(weak_slot);
}

gc_info* gc_manager::_try_get_object_from_weak(gc_info** weak_slot) {


  gc_info* obj = *weak_slot;
  if (m_gc_stage == gc_stage_state::kOutGc) {
    //; no extra work need do
  } else {
    if (obj) {
      // need save weak to another list, so we can remark it in atomic stage for right reference.
      m_weak_gray_list.push_back(*weak_slot);
    }
  }

  return obj;
}

void gc_manager::update() {
  unsigned long now_time = m_timer.GetMicroseconds();
  if (now_time < m_last_trigger_gc_time_ms + m_trigger_gc_period_ms) {
    ;
  } else {
    // trigger gc
    trigger_gc();

    m_execute_gc_period_ms = now_time - m_last_trigger_gc_time_ms;
    m_last_trigger_gc_time_ms = now_time;
    m_mark_sweep_cost_ms = m_timer.GetMicroseconds() - now_time;
  }
}

bool gc_manager::is_contain(gc_info* obj) const noexcept { return m_id == obj->get_parent_id(); }

gc_manager* gc_manager::get_manager_by_id(size_t manger_id) noexcept { return s_all_gc_manager[manger_id]; }

bool gc_manager::is_gc_object(const void* obj_pointer) noexcept {
  if (obj_pointer) {
    return m_range_allocator->IsContain((char*)(const_cast<void*>(obj_pointer)) - sizeof(gc_info));
  }
  return false;
}

void gc_manager::add_listener(Listener* listener) {}

void gc_manager::remove_listener(Listener* listner) {}

void gc_manager::_fire_object_destroy(gc_info* obj, size_t obj_handle) {
  for (auto* listner : m_gc_listener_array) {
    listner->on_gc_object_destroy(obj, obj_handle);
  }
}

uint32_t gc_manager::get_mark_sweep_costs() const { return static_cast<uint32_t>(m_mark_sweep_cost_ms); }

uint32_t gc_manager::get_execute_gc_period() const { return static_cast<uint32_t>(m_execute_gc_period_ms); }

uint32_t gc_manager::get_gc_object_count() const { return static_cast<uint32_t>(m_all_objects.size()); }

gbf::gc::run_scope_ptr gc_manager::make_run_scope() {
  auto used_id = ++scope_id_count_;
  auto scope = std::make_shared<run_scope>(used_id);
  run_scope_weak_ptr weak_scope = scope;
  used_scope_map_.emplace(std::make_pair(used_id, weak_scope));
  return scope;
}

void gc_manager::free_scope(std::uint64_t scope_id, const std::vector<gc_info*>& scope_objects) noexcept {
  used_scope_map_.erase(scope_id);

  std::vector<gc_info*> need_store_objects;
  need_store_objects.reserve(scope_objects.size());

  //If object not assigned, just free it
  for(auto* info: scope_objects) {
    if(GBF_UNLIKELY(info->test_flag(gc_flag_type::kBeenAssigned))) {
      need_store_objects.emplace_back(info);
    } else {
      info->dispose();
      info->free_memory(this);
    }
  }

  m_all_objects.insert(m_all_objects.end(), need_store_objects.begin(), need_store_objects.end());
}

void gc_manager::push_alone_object(gc_info* alone_one) {
  //ToDo: do not handle right now, will be add to global array when update????
  m_all_objects.emplace_back(alone_one);
}

}  // namespace gc
}  // namespace gbf
