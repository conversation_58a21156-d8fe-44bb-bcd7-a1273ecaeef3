#pragma once

#include "memory/gc/container/gc_vector.hpp"

namespace gbf {
namespace gc {

template <typename T>
class typed_gc_vector : public gc_vector {
   static_assert(std::negation<std::is_const<T>>::value, "Const type is not allowed here!");
   ////static_assert(std::is_convertible<T, GCObject*>::value, "Generic GCObject type not allowed!");
 public:
  using value_type = local_ptr<T>;
  using pointer = value_type*;
  using const_pointer = value_type const*;
  using reference = value_type&;
  using const_reference = value_type const&;
  using size_type = size_t;
  using difference_type = std::ptrdiff_t;
  using allocator_type = gc_vector::allocator_type;

  class iterator {
   public:
    using iterator_impl = gc_vector::iterator;
    using const_iterator_impl = gc_vector::const_iterator;
    using constierator = gc_vector::const_iterator;
    using value_type = local_ptr<T>;
    using pointer = value_type*;
    using reference = value_type;
    using iterator_category = iterator_impl::iterator_category;
    using difference_type = iterator_impl::difference_type;

   public:
    iterator(iterator_impl impl) : impl_(impl) {}
    iterator() : impl_() {}
    iterator(iterator const&) = default;
    iterator& operator=(iterator const&) = default;
    iterator(iterator&&) noexcept = default;
    iterator& operator=(iterator&&) noexcept = default;
    ~iterator() = default;

    reference operator*() const { return (*impl_).cast_static<T>(); }

    pointer operator->() const { return static_cast<pointer>(impl_.operator->()); }

    iterator& operator++() {
      ++impl_;
      return *this;
    }

    iterator operator++(int) {
      iterator tmp = *this;
      ++*this;
      return tmp;
    }

    iterator& operator--() {
      --impl_;
      return *this;
    }

    iterator operator--(int) {
      iterator tmp = *this;
      --*this;
      return tmp;
    }

    iterator& operator+=(difference_type const offset) {
      impl_ += offset;
      return *this;
    }

    iterator operator+(difference_type const offset) const {
      iterator tmp = *this;
      return tmp += offset;
    }

    iterator operator-=(difference_type const offset) {
      impl_ -= offset;
      return *this;
    }

    iterator operator-(difference_type const offset) const {
      iterator tmp = *this;
      return tmp -= offset;
    }

    difference_type operator-(iterator other) const { return impl_ - other.impl_; }

    reference operator[](difference_type const offset) const { return impl_.operator[](offset).cast_static<T>(); }

    iterator_impl get_impl() const noexcept { return impl_; }

    bool operator==(iterator const& other) const noexcept { return impl_ == other.impl_; }

    bool operator!=(iterator const& other) const noexcept { return impl_ != other.impl_; }

    bool operator>(iterator const& other) const noexcept { return impl_ > other.impl_; }

    bool operator<(iterator const& other) const noexcept { return impl_ < other.impl_; }

    bool operator>=(iterator const& other) const noexcept { return impl_ >= other.impl_; }

    bool operator<=(iterator const& other) const noexcept { return impl_ <= other.impl_; }

    explicit operator iterator_impl() const noexcept { return get_impl(); }

    explicit operator const_iterator() const noexcept { return get_impl(); }

   private:
    iterator_impl impl_;
  };

  class const_iterator {
   public:
    using iterator_impl = gc_vector::const_iterator;
    using value_type = local_ptr<T> const;
    using pointer = value_type*;
    using const_pointer = value_type const*;
    using reference = value_type;
    using iterator_category = iterator_impl::iterator_category;
    using difference_type = iterator_impl::difference_type;

   public:
    const_iterator(iterator_impl impl) : impl_(impl) {}
    const_iterator() : impl_() {}
    const_iterator(const_iterator const&) = default;
    const_iterator& operator=(const_iterator const&) = default;
    const_iterator(const_iterator&&) noexcept = default;
    const_iterator& operator=(const_iterator&&) noexcept = default;
    ~const_iterator() = default;

    reference operator*() const { return *((*impl_).cast_static<T>()); }

    const_pointer operator->() const { return static_cast<const_pointer>(impl_.operator->()); }

    const_iterator& operator++() {
      ++impl_;
      return *this;
    }

    const_iterator operator++(int) {
      const_iterator tmp = *this;
      ++*this;
      return tmp;
    }

    const_iterator& operator--() {
      --impl_;
      return *this;
    }

    const_iterator operator--(int) {
      const_iterator tmp = *this;
      --*this;
      return tmp;
    }

    const_iterator& operator+=(difference_type const offset) {
      impl_ += offset;
      return *this;
    }

    const_iterator operator+(difference_type const offset) const {
      const_iterator tmp = *this;
      return tmp += offset;
    }

    const_iterator operator-=(difference_type const offset) {
      impl_ -= offset;
      return *this;
    }

    const_iterator operator-(difference_type const offset) const {
      const_iterator tmp = *this;
      return tmp -= offset;
    }

    difference_type operator-(const_iterator other) const { return impl_ - other.impl_; }

    reference operator[](difference_type const offset) const { return impl_.operator[](offset).cast_static<T>(); }

    iterator_impl get_impl() const noexcept { return impl_; }

    explicit operator iterator_impl() const noexcept { return get_impl(); }

    bool operator==(const_iterator const& other) const noexcept { return impl_ == other.impl_; }

    bool operator!=(const_iterator const& other) const noexcept { return impl_ != other.impl_; }

    bool operator>(const_iterator const& other) const noexcept { return impl_ > other.impl_; }

    bool operator<(const_iterator const& other) const noexcept { return impl_ < other.impl_; }

    bool operator>=(const_iterator const& other) const noexcept { return impl_ >= other.impl_; }

    bool operator<=(const_iterator const& other) const noexcept { return impl_ <= other.impl_; }

   private:
    iterator_impl impl_;
  };

  using reverse_iterator = std::reverse_iterator<iterator>;
  using const_reverse_iterator = std::reverse_iterator<const_iterator>;

 public:
  typed_gc_vector() { static_assert(sizeof(gc_vector) == sizeof(typed_gc_vector), "memory layout must be equal!"); };
  ~typed_gc_vector() = default;
  typed_gc_vector(typed_gc_vector const&) = default;
  typed_gc_vector(typed_gc_vector&&) noexcept = default;
  typed_gc_vector& operator=(typed_gc_vector const&) = default;
  typed_gc_vector& operator=(typed_gc_vector&&) noexcept = default;

  ////static value_type fast_cast(GCObject* obj) { return static_cast<value_type>(obj); }

  ////static value_type safe_cast(GCObject* obj) { return obj->as_type<T>(); }

  void set(size_t idx, value_type v) { gc_vector::operator[](idx) = v.to_any(); }

  value_type operator[](size_t index) const { return gc_vector::operator[](index).template cast_static<T>(); }

  value_type at(size_type pos) const { return gc_vector::at(pos).template cast_static<T>(); }

  value_type front() const { return gc_vector::front().template cast_static<T>(); }

  value_type back() const { return gc_vector::back().template cast_static<T>(); }

  iterator begin() noexcept { return iterator{gc_vector::begin()}; }

  iterator end() noexcept { return iterator{gc_vector::end()}; }

  const_iterator begin() const noexcept { return const_iterator{gc_vector::cbegin()}; }

  const_iterator end() const noexcept { return const_iterator{gc_vector::cend()}; }

  const_iterator cbegin() const noexcept { return const_iterator{gc_vector::cbegin()}; }

  const_iterator cend() const noexcept { return const_iterator{gc_vector::cend()}; }

  reverse_iterator rbegin() noexcept { return reverse_iterator{end()}; }

  reverse_iterator rend() noexcept { return reverse_iterator{begin()}; }

  void swap(typed_gc_vector& other) noexcept { gc_vector::swap(other); }

  iterator erase(iterator pos) { return iterator{gc_vector::erase(static_cast<typename iterator::iterator_impl>(pos))}; }

  ////iterator erase(iterator begin, iterator end) { return iterator{gc_vector::erase(static_cast<typename iterator::iterator_impl>(begin), static_cast<typename iterator::iterator_impl>(end))}; }

  iterator insert(const_iterator pos, value_type value) { return gc_vector::insert(static_cast<typename const_iterator::iterator_impl>(pos), value); }

  ////template <typename InputIterator>
  ////iterator insert(const_iterator pos, InputIterator first, InputIterator last) {
  ////  return gc_vector::insert(static_cast<typename const_iterator::iterator_impl>(pos), first, last);
  ////}

  iterator insert(const_iterator pos, size_type count, value_type v) { return gc_vector::insert(static_cast<typename const_iterator::iterator_impl>(pos), count, v); }

  void push_back(value_type v) { gc_vector::push_back(v.to_any()); }

  local_ptr<typed_gc_vector> clone() const { return gc_vector::clone().template cast_static<typed_gc_vector>(); }
};
}  // namespace gc
}  // namespace gbf

