#include "memory/allocator/range_allocator.hpp"

#include <algorithm>
#include <cassert>
#include <stdexcept>
#include <cstring>
#include <iostream>
#include <new> // For std::bad_alloc
#include <mutex> // For std::mutex in RangeAllocatorResourceThreadSafeV2

#include "memory/allocator/tlsf_alloc_impl.hpp"
#include "memoryhooker/Module.h"
namespace gbf { namespace allocator {
    RangeAllocator::RangeAllocator(size_t preAllocateSize, size_t extendSize)
        : mPreAllocateSize(preAllocateSize)
        , mExtendSize(extendSize)
    {
        // Set index to default tlsf pool.
        mNowUsePoolIndex = 0;
    }

    RangeAllocator::~RangeAllocator()
    {
        for (auto& tlsfPool : mTLSFPoolArray)
        {
            DestroyTLSFPool(tlsfPool);
        }
        mTLSFPoolArray.clear();
    }

    AllocateResult RangeAllocator::Allocate(size_t bytes, size_t alignment)
    {

        OnChunkPreAllocate(bytes);

        if (mTLSFPoolArray.size() == 0)
        {
            auto tlsfPool = CreateTLSFPool((uint32_t)mPreAllocateSize);
            mTLSFPoolArray.emplace_back(tlsfPool);
            mNowUsePoolIndex = 0;
        }
        auto& usedTlsfPool = mTLSFPoolArray[mNowUsePoolIndex];

        void* dataPtr = usedTlsfPool.Allocate(bytes, alignment);   ////Instrusive::tlsf_memalign(usedTlsfPool.tlsfPool, alignment, bytes);

        // try to allocate in exist pools
        if (!dataPtr)
        {
            dataPtr = TryAllocateChunkInExistPools(bytes, alignment, mNowUsePoolIndex);
        }

        // try to allocate in a new create pool
        if (!dataPtr)
        {
            dataPtr = RequestNewPoolAndAllocateOneChunk(mExtendSize, bytes, alignment);
        }

        size_t chunkSize = 0;
        if (dataPtr) // Check dataPtr before using it
        {
            chunkSize = allocator::tlsf_block_size(dataPtr);
        }
        ////AtomicAdd(&mTotalAllocatedMemory, chunkSize);

        return AllocateResult(dataPtr, chunkSize);
    }

    void RangeAllocator::Free(void* mem)
    {
        ////AtomicSub(&mTotalAllocatedMemory, chunkSize);
        for (size_t i = 0; i < mTLSFPoolArray.size(); i++)
        {
            if (mTLSFPoolArray[i].IsContain(mem))
            {
                mTLSFPoolArray[i].Free(mem);
                OnChunkFreed(i);
                break;
            }
        }
    }

    AllocateResult RangeAllocator::Reallocate(void* mem, size_t newsize, size_t alignment)
    {
        if (mem == 0)
        {
            return Allocate(newsize, alignment);
        }

        if (newsize == 0)
        {
            Free(mem);
            return AllocateResult(nullptr, 0);
        }

        assert(IsContain(mem) && "RangeAllocator::Reallocate() must reallocate a chunk in itself!");

        {
            size_t oldsize = GetChunkSize(mem);
            AllocateResult retResult = Allocate(newsize, alignment);
            if (retResult.first) // Check if new allocation was successful
            {
                memcpy(retResult.first, mem, std::min<size_t>(newsize, oldsize));
            }
            Free(mem); // Free old memory regardless of new allocation success
            return retResult;
        }
    }

    void RangeAllocator::ForceTrim()
    {
        // Do nothing here
    }

    void RangeAllocator::Reset()
    {
    // 1. Destroy all pools except for the first one.
    for (size_t i = 1; i < mTLSFPoolArray.size(); ++i)
    {
        DestroyTLSFPool(mTLSFPoolArray[i]);
    }

    // 2. If there are any pools, resize the array to keep only the first one.
    if (!mTLSFPoolArray.empty())
    {
        mTLSFPoolArray.resize(1);

        // 3. Get a reference to the first (and now only) pool.
        TLSFPoolInfo& mainPool = mTLSFPoolArray[0];

        // 4. Re-initialize the TLSF control structure within the existing memory segment.
        // This effectively resets the pool without freeing its main memory block.
        if (mainPool.poolSegmentBegin)
        {
            // Destroy the old TLSF pool metadata before creating a new one
            if(mainPool.tlsfPool)
            {
                allocator::tlsf_destroy(mainPool.tlsfPool);
            }
            mainPool.tlsfPool = allocator::tlsf_create_with_pool(mainPool.poolSegmentBegin, mainPool.poolReservedSize);
            mainPool.totalAllocationCount = 0;
            mainPool.poolAllocatedSize = 0;
        }
    }
    else
    {
        // If all pools were somehow freed, just clear the vector.
        mTLSFPoolArray.clear();
    }

    // 5. Reset the active pool index to the beginning.
    mNowUsePoolIndex = 0;
    }
    bool RangeAllocator::IsContain(void* mem) const
    {
        for (size_t i = 0; i < mTLSFPoolArray.size(); i++)
        {
            if (mTLSFPoolArray[i].IsContain(mem))
            {
                return true;
            }
        }

        return false;
    }

    size_t RangeAllocator::GetChunkSize(void* mem) const
    {
        if (!mem) return 0; // Handle null pointer case
        return allocator::tlsf_block_size(mem);
    }

    bool RangeAllocator::IsThreadSafe() const
    {
        return false;
    }

    size_t RangeAllocator::GetAllocatedSize() const
    {
        size_t totalAllocateSize = 0;
        for (const auto& poolInfo : mTLSFPoolArray)
        {
            totalAllocateSize += poolInfo.poolAllocatedSize;
        }
        return totalAllocateSize;
    }

    size_t RangeAllocator::GetReservedSize() const
    {
        size_t totalReservedSize = 0;
        for (const auto& poolInfo : mTLSFPoolArray)
        {
            totalReservedSize += poolInfo.poolReservedSize;
        }
        return totalReservedSize;
    }

    size_t RangeAllocator::GetMaxAllocatedSize() const
    {
        return mExtendSize / 2;
    }

    RangeAllocator::TLSFPoolInfo RangeAllocator::CreateTLSFPool(uint32_t expectSize)
    {
        TLSFPoolInfo poolInfo;
        // Handle expectSize = 0 to prevent malloc(0) if it's problematic
        // tlsf_create_with_pool also needs a minimum size for its own overhead.
        // A very small expectSize might cause tlsf_create_with_pool to fail.
        // For simplicity, assume expectSize is validated or large enough by caller.
        // If expectSize is 0, malloc might return nullptr or a unique ptr not usable for a pool.
        if (expectSize < tlsf_pool_overhead() + tlsf_block_size_min()) {
             // If expected size is too small for a functional TLSF pool,
             // either throw or try to use a minimum sensible size.
             // For now, let it proceed; tlsf_create_with_pool might return null.
        }

        poolInfo.poolSegmentBegin = (std::uint8_t*)(cross::Memory::Malloc(expectSize));
        if (!poolInfo.poolSegmentBegin && expectSize > 0) // only throw if expectSize > 0 and malloc failed
        {
            throw std::bad_alloc();
        }
        poolInfo.poolSegmentEnd = poolInfo.poolSegmentBegin ? poolInfo.poolSegmentBegin + expectSize : nullptr;
        poolInfo.poolReservedSize = expectSize;
        // Only create tlsfPool if memory was actually allocated
        poolInfo.tlsfPool = poolInfo.poolSegmentBegin ? allocator::tlsf_create_with_pool(poolInfo.poolSegmentBegin, expectSize) : nullptr;
        
        // If tlsf_create_with_pool failed (e.g. expectSize too small), tlsfPool will be null.
        // Subsequent allocations to this pool will then fail, which is correct.
        return poolInfo;
    }

    void RangeAllocator::DestroyTLSFPool(TLSFPoolInfo& poolInfo)
    {
        if (poolInfo.tlsfPool)
        {
            allocator::tlsf_destroy(poolInfo.tlsfPool);
        }
        if (poolInfo.poolSegmentBegin)
        {
            cross::Memory::Free(poolInfo.poolSegmentBegin);
        }
        poolInfo.poolSegmentBegin = nullptr;
        poolInfo.poolSegmentEnd = nullptr;
        poolInfo.poolReservedSize = 0;
        poolInfo.tlsfPool = nullptr;
    }

    void* RangeAllocator::TryAllocateChunkInExistPools(size_t bytes, size_t alignment, int exceptPoolIndex)
    {
        for (size_t i = 0; i < mTLSFPoolArray.size(); i++)
        {
            if (i == (size_t)exceptPoolIndex)
                continue;
            
            if (!mTLSFPoolArray[i].tlsfPool) // Skip unusable pools
                continue;

            void* dataPtr = mTLSFPoolArray[i].Allocate(bytes, alignment);
            if (dataPtr)
            {
                mNowUsePoolIndex = (int)i;
                return dataPtr;
            }
        }

        return nullptr;
    }

    void* RangeAllocator::RequestNewPoolAndAllocateOneChunk(size_t poolSize, size_t bytes, size_t alignment)
    {
        auto poolInfo = CreateTLSFPool((uint32_t)poolSize);
        // Only add and use if pool creation was successful (tlsfPool is not null)
        if (!poolInfo.tlsfPool) {
            // If pool creation failed (e.g. poolSize too small or malloc failed),
            // we can't use it. Destroy any partially allocated resources.
            DestroyTLSFPool(poolInfo); // Cleans up poolSegmentBegin if it was allocated
            return nullptr;
        }

        mTLSFPoolArray.emplace_back(poolInfo);
        mNowUsePoolIndex = (int)mTLSFPoolArray.size() - 1;
        return mTLSFPoolArray[mNowUsePoolIndex].Allocate(bytes, alignment);
    }

    void RangeAllocator::OnChunkFreed(size_t poolIndex)
    {
        if (poolIndex >= mTLSFPoolArray.size()) return; // Bounds check

        if (mTLSFPoolArray[poolIndex].IsEmpty())
        {
            // try to free it
            DestroyTLSFPool(mTLSFPoolArray[poolIndex]);
            mTLSFPoolArray.erase(mTLSFPoolArray.begin() + poolIndex);

            // Reset now use pool index
            // If all pools are freed, mNowUsePoolIndex might become -1 if not careful.
            // It should point to a valid pool or 0 if list becomes non-empty again.
            // If mTLSFPoolArray is empty, mNowUsePoolIndex = 0 is fine for next allocation to create first pool.
            mNowUsePoolIndex = 0; 
            if (!mTLSFPoolArray.empty()) {
                 // A simple strategy: point to the first available pool.
                 // Or, if you want to keep using the "end" for new allocations,
                 // mNowUsePoolIndex = mTLSFPoolArray.size() -1; (but this might be an invalid index if size is 0)
                 // Sticking to 0 is safer if array might become empty.
            }
        }
    }
    void RangeAllocator::TLSFPoolInfo::Free(void* ptr)
    {
        if (!this->tlsfPool || !ptr) return; // Safety checks
        std::uint32_t chunkSize = (uint32_t)allocator::tlsf_block_size(ptr);
        allocator::tlsf_free(this->tlsfPool, ptr);
        this->poolAllocatedSize -= chunkSize;
        this->totalAllocationCount--;
    }

    void* RangeAllocator::TLSFPoolInfo::Allocate(size_t bytes, size_t alignment)
    {
        if (!this->tlsfPool) return nullptr; // Safety check

        void* dataPtr = allocator::tlsf_memalign(this->tlsfPool, alignment, bytes);
        if (dataPtr)
        {
            std::uint32_t chunkSize = (uint32_t)allocator::tlsf_block_size(dataPtr);
            this->poolAllocatedSize += chunkSize;
            this->totalAllocationCount++;
        }
        return dataPtr;
    }

    RangeAllocatorResource::RangeAllocatorResource(size_t preAllocateSize, size_t extendSize)
        : mAllocator(preAllocateSize, extendSize)
    {
    }

    void* RangeAllocatorResource::do_allocate(std::size_t bytes, std::size_t alignment)
    {
        const auto ptr = mAllocator.Allocate(bytes, alignment).first;
        return ptr;
    }

    void RangeAllocatorResource::do_deallocate(void* p, std::size_t bytes, std::size_t alignment)
    {
        (void)bytes; (void)alignment; // Suppress unused parameter warnings
        mAllocator.Free(p);
    }

    bool RangeAllocatorResource::do_is_equal(const memory_resource& that) const noexcept
    {
        auto other = dynamic_cast<const RangeAllocatorResource*>(&that);
        return other && (&mAllocator == &other->mAllocator);
    }

    RangeAllocatorResourceThreadSafe::RangeAllocatorResourceThreadSafe(size_t preAllocateSize, size_t extendSize)
        : mAllocator(preAllocateSize, extendSize)
    {}

    void* RangeAllocatorResourceThreadSafe::do_allocate(std::size_t bytes, std::size_t alignment)
    {
        std::scoped_lock lock(mMtx);
        const auto ptr = mAllocator.Allocate(bytes, alignment).first;
        return ptr;
    }

    void RangeAllocatorResourceThreadSafe::do_deallocate(void* p, std::size_t bytes, std::size_t alignment)
    {
        (void)bytes; (void)alignment; // Suppress unused parameter warnings
        std::scoped_lock lock(mMtx);
        mAllocator.Free(p);
    }

    bool RangeAllocatorResourceThreadSafe::do_is_equal(const memory_resource& that) const noexcept
    {
        auto other = dynamic_cast<const RangeAllocatorResourceThreadSafe*>(&that);
        return other && (&mAllocator == &other->mAllocator);
    }

    // V2 Implementations
    RangeAllocatorV2::RangeAllocatorV2(size_t preAllocateSize)
        : mPreAllocateSize(preAllocateSize)
    {
        mNowUsePoolIndex = 0;
    }

    RangeAllocatorV2::~RangeAllocatorV2()
    {
        for (auto& tlsfPool : mTLSFPoolArray)
        {
            DestroyTLSFPool(tlsfPool);
        }
        mTLSFPoolArray.clear();
    }

    AllocateResult RangeAllocatorV2::Allocate(size_t bytes, size_t alignment)
    {
        OnChunkPreAllocate(bytes);

        if (mTLSFPoolArray.empty())
        {
            size_t firstPoolSize = mPreAllocateSize;
            // Ensure a non-zero pool size to avoid issues with malloc(0) or tlsf_create_with_pool.
            // tlsf_pool_overhead() + tlsf_block_size_min() is the absolute minimum for a usable pool.
            size_t minPracticalPoolSize = tlsf_pool_overhead() + tlsf_block_size_min();
            if (firstPoolSize < minPracticalPoolSize) {
                 firstPoolSize = minPracticalPoolSize;
            }
             // If bytes requested is larger than the first pool, it will fail and trigger expansion.
            auto tlsfPool = CreateTLSFPool((uint32_t)firstPoolSize);
            if (tlsfPool.tlsfPool) { // Check if pool creation was successful
                mTLSFPoolArray.emplace_back(tlsfPool);
                mNowUsePoolIndex = 0;
            } else {
                // First pool creation failed (e.g. malloc failed for even minPracticalPoolSize)
                DestroyTLSFPool(tlsfPool); // Clean up if segment was allocated but tlsf_create failed
                return AllocateResult(nullptr, 0); // Cannot allocate
            }
        }
        
        // If mTLSFPoolArray is still empty here, it means the first pool creation failed.
        if (mTLSFPoolArray.empty()) {
            return AllocateResult(nullptr, 0);
        }

        auto& usedTlsfPool = mTLSFPoolArray[mNowUsePoolIndex];
        void* dataPtr = nullptr;
        if (usedTlsfPool.tlsfPool) { // Ensure current pool is usable
             dataPtr = usedTlsfPool.Allocate(bytes, alignment);
        }


        if (!dataPtr)
        {
            dataPtr = TryAllocateChunkInExistPools(bytes, alignment, mNowUsePoolIndex);
        }

        if (!dataPtr) // Allocation failed in existing pools, need a new one.
        {
            // This implies mTLSFPoolArray is NOT empty, because the first pool creation
            // is handled by the `if (mTLSFPoolArray.empty())` block above.
            assert(!mTLSFPoolArray.empty() && "RangeAllocatorV2: Attempting to create a subsequent pool, but no initial pool exists.");

            size_t prevPoolSize = mTLSFPoolArray.back().poolReservedSize;
            
            // Determine the size for the new pool.
            // Strategy: New pool size is the maximum of:
            // 1. Twice the requested 'bytes' (heuristic for overhead and some future allocations).
            // 2. Twice the previous pool's size (geometric growth).
            // 3. Absolute minimum required to satisfy the current 'bytes' request plus TLSF overheads.

            size_t sizeForRequestPayload = bytes * 2; 
            size_t sizeFromPrevPoolGrowth = prevPoolSize * 2;

            size_t nextPoolSize = std::max(sizeForRequestPayload, sizeFromPrevPoolGrowth);

            // Calculate absolute minimum size for the new pool to hold the current allocation.
            // This includes: requested bytes, potential alignment padding, TLSF block header, and TLSF pool structure overhead.
            size_t requiredBytesForBlock = bytes;
            if (alignment > tlsf_align_size()) { // tlsf_align_size is typically sizeof(void*)
                // tlsf_memalign may use up to (alignment - tlsf_align_size()) extra space for padding.
                requiredBytesForBlock += (alignment - tlsf_align_size());
            }
            // Add overhead for the block structure itself (header) and the minimum payload size TLSF manages.
            // tlsf_block_size_min() is the smallest allocatable payload. A block also has a header.
            // A safe estimate for block related overheads could be tlsf_block_size_max() - bytes, or more simply a fixed value.
            // Let's use a generous fixed overhead for block structure + min payload capability.
            requiredBytesForBlock += tlsf_block_size_min(); // Accounts for min block size handling and some header.

            size_t absoluteMinPoolSizeForRequest = requiredBytesForBlock + tlsf_pool_overhead();
            
            nextPoolSize = std::max(nextPoolSize, absoluteMinPoolSizeForRequest);

            // Final check: ensure it's not smaller than the smallest possible functional TLSF pool.
            size_t minPracticalTLSFPoolSize = tlsf_pool_overhead() + tlsf_block_size_min();
            if (nextPoolSize < minPracticalTLSFPoolSize) {
                nextPoolSize = minPracticalTLSFPoolSize;
            }
            
            dataPtr = RequestNewPoolAndAllocateOneChunk(nextPoolSize, bytes, alignment);
        }

        size_t chunkSize = 0;
        if (dataPtr)
        {
            chunkSize = allocator::tlsf_block_size(dataPtr);
        }
        return AllocateResult(dataPtr, chunkSize);
    }

    void RangeAllocatorV2::Free(void* mem)
    {
        for (size_t i = 0; i < mTLSFPoolArray.size(); i++)
        {
            if (mTLSFPoolArray[i].IsContain(mem))
            {
                mTLSFPoolArray[i].Free(mem);
                OnChunkFreed(i);
                break;
            }
        }
    }

    AllocateResult RangeAllocatorV2::Reallocate(void* mem, size_t newsize, size_t alignment)
    {
        if (mem == 0)
        {
            return Allocate(newsize, alignment);
        }
        if (newsize == 0)
        {
            Free(mem);
            return AllocateResult(nullptr, 0);
        }
        assert(IsContain(mem) && "RangeAllocatorV2::Reallocate() must reallocate a chunk in itself!");
        {
            size_t oldsize = GetChunkSize(mem);
            AllocateResult retResult = Allocate(newsize, alignment);
            if (retResult.first)
            {
                memcpy(retResult.first, mem, std::min<size_t>(newsize, oldsize));
            }
            Free(mem);
            return retResult;
        }
    }

    void RangeAllocatorV2::ForceTrim()
    {
        // Do nothing here
    }
    void RangeAllocatorV2::Reset()
    {
            // 1. Destroy all pools except for the first one.
    for (size_t i = 1; i < mTLSFPoolArray.size(); ++i)
    {
        DestroyTLSFPool(mTLSFPoolArray[i]);
    }

    // 2. If there are any pools, resize the array to keep only the first one.
    if (!mTLSFPoolArray.empty())
    {
        mTLSFPoolArray.resize(1);

        // 3. Get a reference to the first (and now only) pool.
        TLSFPoolInfoV2& mainPool = mTLSFPoolArray[0];

        // 4. Re-initialize the TLSF control structure within the existing memory segment.
        // This effectively resets the pool without freeing its main memory block.
        if (mainPool.poolSegmentBegin)
        {
            // Destroy the old TLSF pool metadata before creating a new one
            if(mainPool.tlsfPool)
            {
                allocator::tlsf_destroy(mainPool.tlsfPool);
            }
            mainPool.tlsfPool = allocator::tlsf_create_with_pool(mainPool.poolSegmentBegin, mainPool.poolReservedSize);
            mainPool.totalAllocationCount = 0;
            mainPool.poolAllocatedSize = 0;
        }
    }
    else
    {
        // If all pools were somehow freed, just clear the vector.
        mTLSFPoolArray.clear();
    }
    // 5. Reset the active pool index to the beginning.
    mNowUsePoolIndex = 0;
    }
    bool RangeAllocatorV2::IsContain(void* mem) const
    {
        for (size_t i = 0; i < mTLSFPoolArray.size(); i++)
        {
            if (mTLSFPoolArray[i].IsContain(mem))
            {
                return true;
            }
        }
        return false;
    }

    size_t RangeAllocatorV2::GetChunkSize(void* mem) const
    {
        if (!mem) return 0;
        return allocator::tlsf_block_size(mem);
    }

    bool RangeAllocatorV2::IsThreadSafe() const
    {
        return false;
    }

    size_t RangeAllocatorV2::GetAllocatedSize() const
    {
        size_t totalAllocateSize = 0;
        for (const auto& poolInfo : mTLSFPoolArray)
        {
            totalAllocateSize += poolInfo.poolAllocatedSize;
        }
        return totalAllocateSize;
    }

    size_t RangeAllocatorV2::GetReservedSize() const
    {
        size_t totalReservedSize = 0;
        for (const auto& poolInfo : mTLSFPoolArray)
        {
            totalReservedSize += poolInfo.poolReservedSize;
        }
        return totalReservedSize;
    }

    size_t RangeAllocatorV2::GetMaxAllocatedSize() const
    {
        // This is a heuristic. The largest single allocation depends on the largest pool,
        // which can grow. mInitialMinPoolSize / 2 is a baseline.
        if (mPreAllocateSize > 0) return mPreAllocateSize / 2;
        // If no initial sizes, it's hard to give a meaningful max.
        // Could also iterate pools and find max possible in largest pool.
        // For now, a simple heuristic:
        if (!mTLSFPoolArray.empty()) {
            return mTLSFPoolArray.back().poolReservedSize / 2;
        }
        return 0;
    }

    RangeAllocatorV2::TLSFPoolInfoV2 RangeAllocatorV2::CreateTLSFPool(uint32_t expectSize)
    {
        TLSFPoolInfoV2 poolInfo;
         if (expectSize < tlsf_pool_overhead() + tlsf_block_size_min()) {
             // This size is too small for a functional TLSF pool.
             // Set pool as unusable.
             poolInfo.poolSegmentBegin = nullptr;
             poolInfo.poolSegmentEnd = nullptr;
             poolInfo.poolReservedSize = expectSize; // Still record what was requested
             poolInfo.tlsfPool = nullptr;
             return poolInfo;
        }

        poolInfo.poolSegmentBegin = (std::uint8_t*)(cross::Memory::Malloc(expectSize));
        if (!poolInfo.poolSegmentBegin) // Malloc failed
        {
            // throw std::bad_alloc(); // Or handle more gracefully by returning an invalid pool
            poolInfo.poolSegmentEnd = nullptr;
            poolInfo.poolReservedSize = expectSize;
            poolInfo.tlsfPool = nullptr;
            return poolInfo;
        }
        poolInfo.poolSegmentEnd = poolInfo.poolSegmentBegin + expectSize;
        poolInfo.poolReservedSize = expectSize;
        poolInfo.tlsfPool = allocator::tlsf_create_with_pool(poolInfo.poolSegmentBegin, expectSize);
        
        if (!poolInfo.tlsfPool) { // tlsf_create_with_pool failed
            cross::Memory::Free(poolInfo.poolSegmentBegin);
            poolInfo.poolSegmentBegin = nullptr;
            poolInfo.poolSegmentEnd = nullptr;
            // poolInfo.poolReservedSize remains expectSize to indicate attempt
        }
        return poolInfo;
    }

    void RangeAllocatorV2::DestroyTLSFPool(TLSFPoolInfoV2& poolInfo)
    {
        if (poolInfo.tlsfPool)
        {
            allocator::tlsf_destroy(poolInfo.tlsfPool);
        }
        if (poolInfo.poolSegmentBegin)
        {
            cross::Memory::Free(poolInfo.poolSegmentBegin);
        }
        poolInfo.poolSegmentBegin = nullptr;
        poolInfo.poolSegmentEnd = nullptr;
        poolInfo.poolReservedSize = 0;
        poolInfo.tlsfPool = nullptr;
        poolInfo.poolAllocatedSize = 0;
        poolInfo.totalAllocationCount = 0;
    }

    void* RangeAllocatorV2::TryAllocateChunkInExistPools(size_t bytes, size_t alignment, int exceptPoolIndex)
    {
        for (size_t i = 0; i < mTLSFPoolArray.size(); i++)
        {
            if (i == (size_t)exceptPoolIndex)
                continue;
            
            if (!mTLSFPoolArray[i].tlsfPool) // Skip unusable pools
                continue;

            void* dataPtr = mTLSFPoolArray[i].Allocate(bytes, alignment);
            if (dataPtr)
            {
                mNowUsePoolIndex = (int)i;
                return dataPtr;
            }
        }
        return nullptr;
    }

    void* RangeAllocatorV2::RequestNewPoolAndAllocateOneChunk(size_t poolSize, size_t bytes, size_t alignment)
    {
        auto poolInfo = CreateTLSFPool((uint32_t)poolSize);
        if (!poolInfo.tlsfPool) { // If pool creation failed
            DestroyTLSFPool(poolInfo); // Ensure cleanup
            return nullptr;
        }
        mTLSFPoolArray.emplace_back(poolInfo);
        mNowUsePoolIndex = (int)mTLSFPoolArray.size() - 1;
        return mTLSFPoolArray[mNowUsePoolIndex].Allocate(bytes, alignment);
    }

    void RangeAllocatorV2::OnChunkFreed(size_t poolIndex)
    {
        if (poolIndex >= mTLSFPoolArray.size()) return;

        if (mTLSFPoolArray[poolIndex].IsEmpty())
        {
            DestroyTLSFPool(mTLSFPoolArray[poolIndex]);
            mTLSFPoolArray.erase(mTLSFPoolArray.begin() + poolIndex);
            
            // Adjust mNowUsePoolIndex carefully
            if (mTLSFPoolArray.empty()) {
                mNowUsePoolIndex = 0; // Will trigger new pool creation on next allocate
            } else {
                // If the erased pool was before or at mNowUsePoolIndex, decrement mNowUsePoolIndex
                if ((int)poolIndex <= mNowUsePoolIndex) {
                     mNowUsePoolIndex = std::max(0, mNowUsePoolIndex -1);
                }
                // Or, always reset to a known state, e.g., the last pool or first pool
                // mNowUsePoolIndex = mTLSFPoolArray.size() - 1; // Point to last pool
                // For simplicity, if current mNowUsePoolIndex becomes invalid due to erase, reset to 0.
                if (mNowUsePoolIndex >= (int)mTLSFPoolArray.size()) {
                    mNowUsePoolIndex = 0;
                }

            }
        }
    }

    void RangeAllocatorV2::TLSFPoolInfoV2::Free(void* ptr)
    {
        if (!this->tlsfPool || !ptr) return;
        std::uint32_t chunkSize = (uint32_t)allocator::tlsf_block_size(ptr);
        allocator::tlsf_free(this->tlsfPool, ptr);
        this->poolAllocatedSize -= chunkSize;
        this->totalAllocationCount--;
    }

    void* RangeAllocatorV2::TLSFPoolInfoV2::Allocate(size_t bytes, size_t alignment)
    {
        if (!this->tlsfPool) return nullptr;
        void* dataPtr = allocator::tlsf_memalign(this->tlsfPool, alignment, bytes);
        if (dataPtr)
        {
            std::uint32_t chunkSize = (uint32_t)allocator::tlsf_block_size(dataPtr);
            this->poolAllocatedSize += chunkSize;
            this->totalAllocationCount++;
        }
        return dataPtr;
    }

    RangeAllocatorResourceV2::RangeAllocatorResourceV2(size_t preAllocateSize)
        : mAllocator(preAllocateSize)
    {
    }

    void* RangeAllocatorResourceV2::do_allocate(std::size_t bytes, std::size_t alignment)
    {
        const auto ptr = mAllocator.Allocate(bytes, alignment).first;
        return ptr;
    }

    void RangeAllocatorResourceV2::do_deallocate(void* p, std::size_t bytes, std::size_t alignment)
    {
        (void)bytes; (void)alignment;
        mAllocator.Free(p);
    }

    bool RangeAllocatorResourceV2::do_is_equal(const memory_resource& that) const noexcept
    {
        auto other = dynamic_cast<const RangeAllocatorResourceV2*>(&that);
        return other && (&mAllocator == &other->mAllocator);
    }

    RangeAllocatorResourceThreadSafeV2::RangeAllocatorResourceThreadSafeV2(size_t preAllocateSize)
        : mAllocator(preAllocateSize)
    {}

    void* RangeAllocatorResourceThreadSafeV2::do_allocate(std::size_t bytes, std::size_t alignment)
    {
        std::scoped_lock lock(mMtx); // mMtx is a member of RangeAllocatorResourceThreadSafeV2 from hpp
        const auto ptr = mAllocator.Allocate(bytes, alignment).first;
        return ptr;
    }

    void RangeAllocatorResourceThreadSafeV2::do_deallocate(void* p, std::size_t bytes, std::size_t alignment)
    {
        (void)bytes; (void)alignment;
        std::scoped_lock lock(mMtx); // mMtx is a member of RangeAllocatorResourceThreadSafeV2 from hpp
        mAllocator.Free(p);
    }

    bool RangeAllocatorResourceThreadSafeV2::do_is_equal(const memory_resource& that) const noexcept
    {
        auto other = dynamic_cast<const RangeAllocatorResourceThreadSafeV2*>(&that);
        return other && (&mAllocator == &other->mAllocator);
    }

}   // namespace allocator
}   // namespace gbf
