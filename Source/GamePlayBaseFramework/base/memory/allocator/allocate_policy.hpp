#pragma once

#include <functional>
#include "memory/memory_export.hpp"
#include "memory/memory_define.hpp"

namespace gbf { namespace allocator {

    class AllocatePolicy
    {
    public:
        GBF_MEMORY_API AllocatePolicy() {}
        AllocatePolicy(const AllocatePolicy&) = delete;
        GBF_MEMORY_API virtual ~AllocatePolicy() {}

        GBF_MEMORY_API virtual AllocateResult Allocate(size_t bytes, size_t alignment) = 0;

        GBF_MEMORY_API virtual void Free(void* mem) = 0;

        GBF_MEMORY_API virtual void FreeAlign(void* mem, size_t alignment)
        {
            // Default version just use free to work
            Free(mem);
        }

        ////virtual bool TryFree(void* mem) = 0;

        GBF_MEMORY_API virtual AllocateResult Reallocate(void* mem, size_t newsize, size_t alignment) = 0;

        virtual void ForceTrim() = 0;

        ////virtual bool IsContain(void* mem) const { return false; }

        GBF_MEMORY_API virtual size_t GetChunkSize(void* mem) const = 0;

        GBF_MEMORY_API virtual bool IsThreadSafe() const
        {
            return false;
        }

        GBF_MEMORY_API virtual size_t GetAllocatedSize() const = 0;

        GBF_MEMORY_API virtual size_t GetReservedSize() const = 0;

        ////virtual size_t GetTotalFreeChunks() const { return 0; }

        GBF_MEMORY_API virtual size_t GetMaxAllocatedSize() const = 0;

        GBF_MEMORY_API virtual void FrameUpdate() {}

        //        static AllocatePolicy* TryGetAllocatePolicyFromChunk(void* mem);

        void SetOnMaxChunkWarningFunction(OnMaxAllocateChunkWarningFunc&& func)
        {
            mWarningFunc = std::move(func);
        }

        void ClearOnMaxChunkWarningFunction()
        {
            OnMaxAllocateChunkWarningFunc tmpFunc;
            mWarningFunc.swap(tmpFunc);
        }

        void SetStartWarningChunkSize(size_t warningSize)
        {
            mStartWarningSize = warningSize;
        }

    protected:
        void OnChunkPreAllocate(size_t size);

    protected:
        OnMaxAllocateChunkWarningFunc mWarningFunc;
        size_t mStartWarningSize = 128 * 1024;

        int mMaxChunkSize = 0;
    };

}}   // namespace gbf::allocator
