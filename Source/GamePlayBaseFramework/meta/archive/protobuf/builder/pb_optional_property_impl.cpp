#include "archive/protobuf/builder/pb_optional_property_impl.h"

#include <cassert>

#include "core/error/errors.hpp"
#include "archive/protobuf/builder/pb_converter_tool.h"
#include "archive/protobuf/pb.h"

namespace gbf {
namespace reflection {
namespace detail {

PbOptionalPropertyImpl::PbOptionalPropertyImpl(IdRef name, ValueKind valKind, TypeId propertyTypeId, uint32_t tag,
                                               protobuf::cxx::FieldDataType dataType, uint32_t dataOffset,
                                               uint32_t dataSize, uint32_t offsetForHas)
    : SimpleProperty(name, valKind, propertyTypeId),
      pb_data_type_(dataType),
      build_kind_(protobuf::cxx::TypeHelper::ToBuildKind(pb_data_type_)),
      data_offset_(dataOffset),
      data_size_(dataSize),
      offset_for_has_(offsetForHas) {
  implement_type_ = PropertyImplementType::kPbOptionalProperty;
  tag_number_ = tag;
}

PbOptionalPropertyImpl::~PbOptionalPropertyImpl() {}

bool PbOptionalPropertyImpl::HasThis(const UserObject& object) const {
  // Check with has flag first
  if (offset_for_has_ != 0) {
    return protobuf::PbConverterTool::GetHasFlag(object.GetPointer(), data_offset_, offset_for_has_);
  } else {
    switch (build_kind_) {
      case protobuf::cxx::FieldBuildKind::kEnum:
      case protobuf::cxx::FieldBuildKind::kPrimitive:
        return !protobuf::PbConverterTool::IsPrimitiveOrEnumDefaultValue(object.GetPointer(), data_offset_, data_size_);
      case protobuf::cxx::FieldBuildKind::kString:
        return !protobuf::PbConverterTool::IsStringDefaultValue(object.GetPointer(), data_offset_, data_size_);
      case protobuf::cxx::FieldBuildKind::kBytes:
        return !protobuf::PbConverterTool::IsBytesDefaultValue(object.GetPointer(), data_offset_, data_size_);
      case protobuf::cxx::FieldBuildKind::kMessage:
        return !protobuf::PbConverterTool::IsMessageDefaultValue(object.GetPointer(), data_offset_, data_size_);
        //-------------------------------------------------------------------------------------
      case protobuf::cxx::FieldBuildKind::kExtension:
      default:
        GBF_ERROR(NotImplementError("pb not support build type find here!"));
        break;
    }
  }
}

void PbOptionalPropertyImpl::SetHasThis(const UserObject& object) const {
  if (offset_for_has_ != 0) {
    protobuf::PbConverterTool::ChangeHasFlag(object.GetPointer(), data_offset_, offset_for_has_, true);
  } else {
    // Do nothing, has not flag here~~
  }
}

void PbOptionalPropertyImpl::ClearThis(const UserObject& object) const {
  if (offset_for_has_ != 0) {
    protobuf::PbConverterTool::ChangeHasFlag(object.GetPointer(), data_offset_, offset_for_has_, false);
  } else {
    switch (build_kind_) {
      case protobuf::cxx::FieldBuildKind::kEnum:
      case protobuf::cxx::FieldBuildKind::kPrimitive:
        protobuf::PbConverterTool::SetPrimitiveOrEnumToDefaultValue(object.GetPointer(), data_offset_, data_size_);
        break;
      case protobuf::cxx::FieldBuildKind::kString:
        protobuf::PbConverterTool::SetStringToDefaultValue(object.GetPointer(), data_offset_, data_size_);
        break;
      case protobuf::cxx::FieldBuildKind::kBytes:
        protobuf::PbConverterTool::SetBytesToDefaultValue(object.GetPointer(), data_offset_, data_size_);
        break;
      case protobuf::cxx::FieldBuildKind::kMessage:
        protobuf::PbConverterTool::SetMessageToDefaultValue(object.GetPointer(), data_offset_, data_size_);
        break;
        //-------------------------------------------------------------------------------------
      case protobuf::cxx::FieldBuildKind::kExtension:
      default:
        GBF_ERROR(NotImplementError("pb not support build type find here!"));
        break;
    }
  }
}

bool PbOptionalPropertyImpl::IsReadable() const { return true; }

bool PbOptionalPropertyImpl::IsWritable() const { return true; }

Value PbOptionalPropertyImpl::GetValue(const UserObject& object) const {
  switch (build_kind_) {
    case protobuf::cxx::FieldBuildKind::kEnum:
      return protobuf::PbConverterTool::EnumPointerGetAsValue(object.GetPointer(), data_offset_, data_size_);
    case protobuf::cxx::FieldBuildKind::kString:
      return protobuf::PbConverterTool::StringPointerGetAsValue(object.GetPointer(), data_offset_, data_size_);
    case protobuf::cxx::FieldBuildKind::kMessage:
      return protobuf::PbConverterTool::MessagePointerGetAsValue(object.GetPointer(), type_index(), data_offset_,
                                                               data_size_);
    case protobuf::cxx::FieldBuildKind::kPrimitive:
      return protobuf::PbConverterTool::PrimitivePointerGetAsValue(object.GetPointer(), pb_data_type_, data_offset_, data_size_);
    case protobuf::cxx::FieldBuildKind::kBytes:
      return protobuf::PbConverterTool::BytesPointerGetAsValue(object.GetPointer(), data_offset_, data_size_);
    //-------------------------------------------------------------------------------------
    case protobuf::cxx::FieldBuildKind::kExtension:
    default:
      GBF_ERROR(NotImplementError("pb not support build type find here!"));
      break;
  }

  return Value::nothing;
}

void PbOptionalPropertyImpl::SetValue(const UserObject& object, const Value& value) const {
  bool setValueSuc = false;
  switch (build_kind_) {
    case protobuf::cxx::FieldBuildKind::kEnum:
      setValueSuc = protobuf::PbConverterTool::EnumPointerSetAsValue(object.GetPointer(), data_offset_, data_size_, value);
      break;
    case protobuf::cxx::FieldBuildKind::kString:
      setValueSuc = protobuf::PbConverterTool::StringPointerSetAsValue(object.GetPointer(), data_offset_, data_size_, value);
      break;
    case protobuf::cxx::FieldBuildKind::kMessage:
      setValueSuc = protobuf::PbConverterTool::MessagePointerSetAsValue(object.GetPointer(), type_index(), data_offset_,
                                                                      data_size_, value);
      break;
    case protobuf::cxx::FieldBuildKind::kPrimitive:
      setValueSuc = protobuf::PbConverterTool::PrimitivePointerSetAsValue(object.GetPointer(), pb_data_type_, data_offset_,
                                                                        data_size_, value);
      break;
    case protobuf::cxx::FieldBuildKind::kBytes:
      setValueSuc = protobuf::PbConverterTool::BytesPointerSetAsValue(object.GetPointer(), data_offset_, data_size_, value);
      break;
    //-------------------------------------------------------------------------------------
    case protobuf::cxx::FieldBuildKind::kExtension:
    default:
      GBF_ERROR(NotImplementError("Extension not support here!"));
      break;
  }

  if (setValueSuc) {
    SetHasThis(object);
  }
}

}  // namespace detail
}  // namespace reflection
}  // namespace gbf
