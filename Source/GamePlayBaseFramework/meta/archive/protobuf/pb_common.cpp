/* pb_common.c: Common support functions for pb_encode.c and pb_decode.c.
 *
 * 2014 Petteri Aimonen <<EMAIL>>
 */

#include "pb_common.h"

#include <cassert>
#include <unordered_map>
////#include "reflection/meta/cpp/meta_class_clang.h"

namespace protobuf {

static std::unordered_map<uint64_t, PbMsgDesc*> gs_all_pb_meta_message_table;

static std::unordered_map<int64_t, int64_t> gs_dispatch_to_meta_table;
static std::unordered_map<int64_t, int64_t> gs_meta_to_dispatch_table;

static bool load_descriptor_values(PbFieldIter* iter) {
  uint32_t data_offset;
  int_least8_t size_offset;

  if (iter->index >= iter->descriptor->field_count) return false;

  auto* detail_field_info = &(iter->descriptor->detail_field_info_array[iter->index]);
  iter->detail_feild = detail_field_info;
  iter->type = detail_field_info->type;
  iter->array_size = detail_field_info->array_size;
  iter->tag = detail_field_info->tag;
  size_offset = detail_field_info->size_offset;
  data_offset = detail_field_info->data_offset;
  iter->data_size = detail_field_info->data_size;
  iter->field_name = detail_field_info->field_name;
  iter->used_prop_name = detail_field_info->used_prop_name;

  if (!iter->message) {
    /* Avoid doing arithmetic on null pointers, it is undefined */
    iter->pField = nullptr;
    iter->pSize = nullptr;
  } else {
    iter->pField = (char*)iter->message + data_offset;

    if (size_offset) {
      iter->pSize = (char*)iter->pField - size_offset;
    } else if (PB_HTYPE(iter->type) == PB_HTYPE_REPEATED &&
               (PB_ATYPE(iter->type) == PB_ATYPE_STATIC || PB_ATYPE(iter->type) == PB_ATYPE_POINTER)) {
      /* Fixed count array */
      iter->pSize = &iter->array_size;
    } else {
      iter->pSize = nullptr;
    }

    if (PB_ATYPE(iter->type) == PB_ATYPE_POINTER && iter->pField != nullptr) {
      iter->pData = *(void**)iter->pField;
    } else {
      iter->pData = iter->pField;
    }
  }

  if (PB_LTYPE_IS_SUBMSG(iter->type)) {
    iter->submsg_desc = PbQueryMetaMessage(iter->descriptor->submsg_info_array[iter->submessage_index]);
  } else {
    iter->submsg_desc = nullptr;
  }

  return true;
}

static void advance_iterator(PbFieldIter* iter) {
  iter->index++;

  if (iter->index >= iter->descriptor->field_count) {
    /* Restart */
    iter->index = 0;
    ////iter->field_info_index = 0;
    iter->submessage_index = 0;
    iter->required_field_index = 0;
    iter->detail_feild = &(iter->descriptor->detail_field_info_array[0]);
  } else {
    /* Increment indexes based on previous field type.
     * All field info formats have the following fields:
     * - lowest 2 bits tell the amount of words in the descriptor (2^n words)
     * - bits 2..7 give the lowest bits of tag number.
     * - bits 8..15 give the field type.
     */
    pb_type_t prev_type = iter->detail_feild->type;

    /* Add to fields.
     * The cast to pb_size_t is needed to avoid -Wconversion warning.
     * Because the data is is constants from generator, there is no danger of overflow.
     */

    ////iter->field_info_index = (pb_size_t)(iter->field_info_index + descriptor_len);
    iter->detail_feild = &(iter->descriptor->detail_field_info_array[iter->index]);
    iter->required_field_index = (pb_size_t)(iter->required_field_index + (PB_HTYPE(prev_type) == PB_HTYPE_REQUIRED));
    iter->submessage_index = (pb_size_t)(iter->submessage_index + PB_LTYPE_IS_SUBMSG(prev_type));
  }
}

bool PbFieldIterBegin(PbFieldIter* iter, const PbMsgDesc* desc, void* message) {
  memset(iter, 0, sizeof(*iter));

  iter->descriptor = desc;
  iter->message = message;

  return load_descriptor_values(iter);
}

bool PbFieldIterBeginExtension(PbFieldIter* iter, PbExtensionInfo* extension) {
  const PbMsgDesc* msg = (const PbMsgDesc*)extension->type->arg;
  bool status;

  ////uint32_t word0 = LURAPB_PROGMEM_READU32(msg->field_info[0]);
  if (PB_ATYPE(msg->detail_field_info_array[0].type) == PB_ATYPE_POINTER) {
    /* For pointer extensions, the pointer is stored directly
     * in the extension structure. This avoids having an extra
     * indirection. */
    status = PbFieldIterBegin(iter, msg, &extension->dest);
  } else {
    status = PbFieldIterBegin(iter, msg, extension->dest);
  }

  iter->pSize = &extension->found;
  return status;
}

bool PbFieldIterNext(PbFieldIter* iter) {
  advance_iterator(iter);
  (void)load_descriptor_values(iter);
  return iter->index != 0;
}

bool PbFieldIterFind(PbFieldIter* iter, uint32_t tag) {
  if (iter->tag == tag) {
    return true; /* Nothing to do, correct field already. */
  } else if (tag > iter->descriptor->largest_tag) {
    return false;
  } else {
    pb_size_t start = iter->index;
    uint32_t fieldinfo;

    if (tag < iter->tag) {
      /* Fields are in tag number order, so we know that tag is between
       * 0 and our start position. Setting index to end forces
       * advance_iterator() call below to restart from beginning. */
      iter->index = iter->descriptor->field_count;
    }

    do {
      /* Advance iterator but don't load values yet */
      advance_iterator(iter);

      /* Do fast check for tag number match */
      if (iter->detail_feild->tag == tag && PB_LTYPE(iter->detail_feild->type) != PB_LTYPE_EXTENSION) {
        /* Good candidate, check further */
        (void)load_descriptor_values(iter);

        /* Found it */
        return true;
      }
    } while (iter->index != start);

    /* Searched all the way back to start, and found nothing. */
    (void)load_descriptor_values(iter);
    return false;
  }
}

bool PbFieldIterFindExtension(PbFieldIter* iter) {
  if (PB_LTYPE(iter->type) == PB_LTYPE_EXTENSION) {
    return true;
  } else {
    pb_size_t start = iter->index;
    uint32_t fieldinfo;

    do {
      /* Advance iterator but don't load values yet */
      advance_iterator(iter);

      /* Do fast check for field type */
      ////fieldinfo = LURAPB_PROGMEM_READU32(iter->descriptor->field_info[iter->field_info_index]);

      if ((PB_LTYPE(iter->detail_feild->type) & 0xFF) == PB_LTYPE_EXTENSION) {
        return load_descriptor_values(iter);
      }
    } while (iter->index != start);

    /* Searched all the way back to start, and found nothing. */
    (void)load_descriptor_values(iter);
    return false;
  }
}

static void* pb_const_cast(const void* p) {
  /* Note: this casts away const, in order to use the common field iterator
   * logic for both encoding and decoding. The cast is done using union
   * to avoid spurious compiler warnings. */
  union {
    void* p1;
    const void* p2;
  } t;
  t.p2 = p;
  return t.p1;
}

bool PbFieldIterBeginConst(PbFieldIter* iter, const PbMsgDesc* desc, const void* message) {
  return PbFieldIterBegin(iter, desc, pb_const_cast(message));
}

bool PbFieldIterBeginExtensionConst(PbFieldIter* iter, const PbExtensionInfo* extension) {
  return PbFieldIterBeginExtension(iter, (PbExtensionInfo*)pb_const_cast(extension));
}

bool pb_default_field_callback(PbIStream* istream, PbOStream* ostream, const PbFieldIter* field) {
  if (field->data_size == sizeof(PbCallbackType)) {
    PbCallbackType* pCallback = (PbCallbackType*)field->pData;

    if (pCallback != nullptr) {
      if (istream != nullptr && pCallback->funcs.decode != nullptr) {
        return pCallback->funcs.decode(istream, field, &pCallback->arg);
      }

      if (ostream != nullptr && pCallback->funcs.encode != nullptr) {
        return pCallback->funcs.encode(ostream, field, &pCallback->arg);
      }
    }
  }

  return true; /* Success, but didn't do anything */
}

/* This function checks whether a string is valid UTF-8 text.
 *
 * Algorithm is adapted from https://www.cl.cam.ac.uk/~mgk25/ucs/utf8_check.c
 * Original copyright: Markus Kuhn <http://www.cl.cam.ac.uk/~mgk25/> 2005-03-30
 * Licensed under "Short code license", which allows use under MIT license or
 * any compatible with it.
 */

bool PbValidateUtf8(const char* str) {
  const pb_byte_t* s = (const pb_byte_t*)str;
  while (*s) {
    if (*s < 0x80) {
      /* 0xxxxxxx */
      s++;
    } else if ((s[0] & 0xe0) == 0xc0) {
      /* 110XXXXx 10xxxxxx */
      if ((s[1] & 0xc0) != 0x80 || (s[0] & 0xfe) == 0xc0) /* overlong? */
        return false;
      else
        s += 2;
    } else if ((s[0] & 0xf0) == 0xe0) {
      /* 1110XXXX 10Xxxxxx 10xxxxxx */
      if ((s[1] & 0xc0) != 0x80 || (s[2] & 0xc0) != 0x80 || (s[0] == 0xe0 && (s[1] & 0xe0) == 0x80) || /* overlong? */
          (s[0] == 0xed && (s[1] & 0xe0) == 0xa0) ||                                                   /* surrogate? */
          (s[0] == 0xef && s[1] == 0xbf && (s[2] & 0xfe) == 0xbe)) /* U+FFFE or U+FFFF? */
        return false;
      else
        s += 3;
    } else if ((s[0] & 0xf8) == 0xf0) {
      /* 11110XXX 10XXxxxx 10xxxxxx 10xxxxxx */
      if ((s[1] & 0xc0) != 0x80 || (s[2] & 0xc0) != 0x80 || (s[3] & 0xc0) != 0x80 ||
          (s[0] == 0xf0 && (s[1] & 0xf0) == 0x80) ||    /* overlong? */
          (s[0] == 0xf4 && s[1] > 0x8f) || s[0] > 0xf4) /* > U+10FFFF? */
        return false;
      else
        s += 4;
    } else {
      return false;
    }
  }

  return true;
}

GBF_ARCHIVE_API const PbDetailFeildInfo* PbDetailFieldInfoFind(const PbMsgDesc* desc, uint32_t tag) {
  for (size_t i = 0; i < desc->field_count; i++) {
    if (desc->detail_field_info_array[i].tag == tag) {
      return &(desc->detail_field_info_array[i]);
    }
  }
  return nullptr;
}

GBF_ARCHIVE_API PbMsgDesc* PbCreateMetaMessage(uint64_t type_id, const char* msg_name) {
  // auto iter = gs_all_pb_meta_message_table.find(type_id);
  PbMsgDesc* meta_message = new PbMsgDesc();
  meta_message->meta_type_id = type_id;
  meta_message->struct_name = msg_name;

  gs_all_pb_meta_message_table.emplace(type_id, meta_message);
  return meta_message;
}

GBF_ARCHIVE_API const PbMsgDesc* PbQueryMetaMessageNoDeferInit(uint64_t type_id) {
  auto iter = gs_all_pb_meta_message_table.find(type_id);
  if (iter != gs_all_pb_meta_message_table.end()) {
    return iter->second;
  }
  return nullptr;
}

GBF_ARCHIVE_API const PbMsgDesc* PbQueryMetaMessage(uint64_t type_id) {
  auto iter = gs_all_pb_meta_message_table.find(type_id);
  if (iter != gs_all_pb_meta_message_table.end()) {
    return iter->second;
  }
  return nullptr;
}

//-------------------------------------------------------------------------------------
void DispatchMessageUtil::Register(DispatchType dispatch_type, int32_t msg_id, int32_t sub_msg_id, int64_t meta_id) {
  DispatchId disp_id(dispatch_type, msg_id, sub_msg_id);
  int64_t cdisp_id = disp_id.GetCombinedId();
  gs_meta_to_dispatch_table[meta_id] = cdisp_id;
  gs_dispatch_to_meta_table[cdisp_id] = meta_id;
}

bool DispatchMessageUtil::QueryMetaId(DispatchType dispatch_type, int32_t msg_id, int32_t sub_msg_id,
                                      int64_t& out_meta_id) {
  DispatchId disp_id(dispatch_type, msg_id, sub_msg_id);
  auto iter = gs_dispatch_to_meta_table.find(disp_id.GetCombinedId());
  if (GBF_LIKELY(iter != gs_dispatch_to_meta_table.end())) {
    out_meta_id = iter->second;
    return true;
  } else {
    out_meta_id = 0;
    return false;
  }
}



bool DispatchMessageUtil::QueryDispatchInfo(int64_t meta_id, DispatchType& out_dispatch_type, int32_t& out_msg_id,
                                            int32_t& out_sub_msg_id) {
  auto iter = gs_meta_to_dispatch_table.find(meta_id);
  if (GBF_LIKELY(iter != gs_meta_to_dispatch_table.end())) {
    DispatchId disp_id(iter->second);
    out_dispatch_type = disp_id.GetDispType();
    out_msg_id = disp_id.GetMsgId();
    out_sub_msg_id = disp_id.GetSubMsgId();
    return true;
  } else {
    out_dispatch_type = DispatchType::kDispatchUnknown;
    out_msg_id = 0;
    out_sub_msg_id = 0;
    return false;
  }
}


}  // namespace protobuf
