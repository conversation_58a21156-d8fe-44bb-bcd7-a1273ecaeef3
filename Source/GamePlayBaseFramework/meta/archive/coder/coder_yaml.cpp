#include <vector>
#include <yaml-cpp/yaml.h>

#include "archive/coder/coder_yaml.h"
#include "core/imodules/ilog_module.h"
#include "archive/coder/coder_helper.h"
#include "reflection/meta/array_property.hpp"
#include "reflection/meta/property.hpp"
#include "reflection/vtable/object_vtable.hpp"
#include "reflection/runtime/cxx_runtime.hpp"
#include "reflection/objects/userobject.hpp"
#include "reflection/objects/make_value.hpp"

namespace gbf {
namespace reflection {

class CoderYaml::Impl {
 public:
  Impl() = delete;
  ~Impl() = delete;

  static bool WriteUserObjectToYamlString(const UserObject& uo, YAML::Node& node, bool filterDefault = true);
  static bool WriteNodeToUserObject(const YAML::Node& node, const UserObject& uo);

 protected:
  static bool WriteArrayObjectToYamlValue(const ArrayObject& ao, YAML::Node& node, bool filterDefault = true);
  static bool WriteSimplePropToYamlValue(const UserObject& uo, const Property& prop, YAML::Node& node, bool filterDefault = true);
  static bool WriteSimpleValueToYamlValue(const Value& value, const ValueKind& kind, YAML::Node& node);

  static bool WriteSimpleYamlValToValue(const YAML::Node& node, const ValueKind& kind, Value& value);
  static bool WriteArrayYamlValToProperty(const YAML::Node& node, const Property& prop, const UserObject& uo);
  static bool WriteSimpleYamlValToProperty(const YAML::Node& node, const Property& prop, const UserObject& uo);
  static bool WriteMapYamlValToProperty(const YAML::Node& node, const Property& prop, const UserObject& uo);

};

std::string CoderYaml::CreateYamlString(const UserObject& uo, bool filterDefault) {
  YAML::Node root;
  bool result = Impl::WriteUserObjectToYamlString(uo, root, filterDefault);
  if (!result) {
    ERR_DEF("convert uo to yamlstring failed");
    return std::string();
  }

  YAML::Emitter yaml_emmiter;
  yaml_emmiter << root;
  return yaml_emmiter.c_str();
}

bool CoderYaml::Impl::WriteUserObjectToYamlString(const UserObject& uo, YAML::Node& node, bool filterDefault) {
  YAML::Node father;
  const auto& metaClass = uo.GetClass();
  const auto& className = metaClass.name();
  node[className] = father;

  std::vector<const Property*> needFields;
  if (filterDefault) {
    CoderHelper::FilterFields(metaClass, uo, needFields);
  } else {
    CoderHelper::GetAllFields(metaClass, uo, needFields);
  }


  for (auto& needField : needFields) {
    const Property& prop = *needField;
    bool ret = false;
    YAML::Node child;
    const ValueKind& kind = prop.kind();
    if (kind == ValueKind::kUser) {
      const auto& subUo = value_ref_as<UserObject>(prop.Get(uo));
      ret = WriteUserObjectToYamlString(subUo, child, filterDefault);
    } else if (kind == ValueKind::kArray) {
      const auto& subAo = value_ref_as<ArrayObject>( prop.Get(uo));
      ret = WriteArrayObjectToYamlValue(subAo, child, filterDefault);
    } else {
      ret = WriteSimplePropToYamlValue(uo, prop, child, filterDefault);
    }

    if (!ret) {
      WRN_DEF("write prop to yaml value failed, prop name: %s, kind: %d", prop.name().data(), kind);
    } else {
      father[prop.name().c_str()] = child;
    }
  }

  return true;
}

bool CoderYaml::Impl::WriteArrayObjectToYamlValue(const ArrayObject& ao, YAML::Node& node, bool filterDefault) {
  const size_t arrayLen = ao.GetSize();
  const ValueKind& subKind = ao.GetElementKind();

  for (size_t i = 0; i < arrayLen; ++i) {
    bool ret;
    YAML::Node child;
    const Value& subValue = ao.GetElement(i);

    if (subKind == ValueKind::kUser) {
      const UserObject& subUo = std::get<UserObject>(subValue.value_);
      ret = WriteUserObjectToYamlString(subUo, child, filterDefault);
    } else if (subKind == ValueKind::kArray) {
      const ArrayObject& subAo = std::get<ArrayObject>(subValue.value_);
      ret = WriteArrayObjectToYamlValue(subAo, child, filterDefault);
    } else {
      ret = WriteSimpleValueToYamlValue(subValue, subValue.kind(), child);
    }

    if (ret) {
      node.push_back(child);
    } else {
      WRN_DEF("write element in array object fail, index: %d, element kind: %d", i, subKind);
    }
  }

  return true;
}

bool CoderYaml::Impl::WriteSimpleValueToYamlValue(const Value& value, const ValueKind& kind, YAML::Node& node) {
  switch (kind) {
    case ValueKind::kEnum: {
      node = value_cast<std::string>(value);
    } break;
    case ValueKind::kBoolean: {
      node = value_cast<bool>(value);
    } break;
    case ValueKind::kInteger: {
      node = value_cast<int64_t>(value);
    } break;
    case ValueKind::kReal: {
      node = value_cast<double>(value);
    } break;
    case ValueKind::kString: {
      node = value_cast<std::string>(value);
    } break;
    default: {
      ERR_DEF("not support type: %d", kind);
      return false;
    }
  }

  return true;
}

bool CoderYaml::Impl::WriteSimplePropToYamlValue(const UserObject& uo, const Property& prop, YAML::Node& node, bool filterDefault) {
  const auto& value = prop.Get(uo);
  const auto& kind = prop.kind();
  return WriteSimpleValueToYamlValue(value, kind, node);
}

UserObject CoderYaml::CreateUserObject(const std::string& yamlString) {
  YAML::Node node = YAML::Load(yamlString);
  if (node.size() != 1) {
    ERR_DEF("class field not exist");
    return {};
  }
  const char* className = node.begin()->first.as<std::string>().c_str();
  const MetaClass* metaClass = query_meta_class_by_name(className);
  if (!metaClass) {
    ERR_DEF("class name: %s not found", className);
    return {};
  }

  UserObject uo = cxx::Create(*metaClass);
  bool result = Impl::WriteNodeToUserObject(node.begin()->second, uo);
  if (!result) {
    ERR_DEF("convert doc to userobject failed, class name: %s", className);
    return {};
  }

  return uo;
}

UserObject CoderYaml::CreateUserObject(const std::string& yamlString, const std::string& packageName) {
  YAML::Node node = YAML::Load(yamlString);
  if (node.size() != 1) {
    ERR_DEF("class field not exist");
    return {};
  }
  const char* className = node.begin()->first.as<std::string>().c_str();
  const MetaClass* metaClass = query_meta_class_by_name(packageName + "::" + className);
  if (!metaClass) {
    ERR_DEF("class name: %s not found", className);
    return {};
  }

  UserObject uo = cxx::Create(*metaClass);
  bool result = Impl::WriteNodeToUserObject(node.begin()->second, uo);
  if (!result) {
    ERR_DEF("convert doc to userobject failed, class name: %s", className);
    return {};
  }

  return uo;
}

bool CoderYaml::Impl::WriteNodeToUserObject(const YAML::Node& node, const UserObject& uo) {
  const MetaClass& metaClass = uo.GetClass();
  for (YAML::const_iterator it = node.begin(); it != node.end(); ++it) {
    const char* propName = it->first.as<std::string>().c_str();
    const auto& prop = metaClass.GetProperty(propName);
    const auto& child = it->second.as<YAML::Node>();
    const auto& kind = prop.kind();
    bool ret = true;

    if (kind == ValueKind::kUser) {
      ret = WriteMapYamlValToProperty(child, prop, uo);
    } else if (kind == ValueKind::kArray) {
      ret = WriteArrayYamlValToProperty(child, prop, uo);
    } else {
      ret = WriteSimpleYamlValToProperty(child, prop, uo);
    }

    if (!ret) {
      WRN_DEF("write sub yaml value to property failed, keyname: %s", prop.name().c_str());
    }
  }

  return true;
}

bool CoderYaml::Impl::WriteArrayYamlValToProperty(const YAML::Node& node, const Property& prop, const UserObject& uo) {
  const auto& arrayProp = static_cast<const ArrayProperty&>(prop);
  const auto& subKind = arrayProp.element_type();
  const MetaClass* subClass;

  if (subKind == ValueKind::kUser) {
    subClass = query_meta_class_by_id(arrayProp.element_type_index());

    if (!subClass) {
      ERR_DEF("element is object, while class is null, prop name: %s", arrayProp.name().data());
      return false;
    }
  }

  bool ret = false;

  if (node.IsScalar()) {
    Value value;
    ret = WriteSimpleYamlValToValue(node, subKind, value);
    if (ret) {
      arrayProp.Insert(uo, 0, value);
      return true;
    }
    WRN_DEF("parse element yaml value failed, node value: %s", node.as<std::string>().c_str());
    return false;
  } else if (node.IsMap()) {
    const UserObject subUo = cxx::Create(*subClass);
    ret = WriteNodeToUserObject(node, subUo);
    if (ret) {
      arrayProp.Insert(uo, 0, make_value(subUo));
      return true;
    }
    WRN_DEF("parse element yaml value failed, node value: %s", node.as<std::string>().c_str());
    return false;
  }

  arrayProp.ReSize(uo, node.size());
  int index = 0;
  for (YAML::const_iterator it = node.begin(); it != node.end(); ++it, ++index) {
    const auto& child = it->as<YAML::Node>();

    if (subKind == ValueKind::kUser) {
      const UserObject subUo = cxx::Create(*subClass);
      ret = WriteNodeToUserObject(child, subUo);
      if (ret) {
        arrayProp.Set(uo, index, make_value(subUo));
      }
    } else {
      Value value;
      ret = WriteSimpleYamlValToValue(child, subKind, value);
      if (ret) {
        arrayProp.Set(uo, index, value);
      }
    }

    if (!ret) {
      WRN_DEF("parse element yaml value failed, index: %d, prop name: %s", index, arrayProp.name().data());
    }
  }

  return true;
}

bool CoderYaml::Impl::WriteMapYamlValToProperty(const YAML::Node& node, const Property& prop, const UserObject& uo) {
  const MetaClass* metaClass = query_meta_class_by_id(prop.type_index());

  if (!metaClass) {
    ERR_DEF("sub class is null, prop name: %s", prop.name().data());
    return false;
  }

  const UserObject subUo = cxx::Create(*metaClass);
  const auto& kind = prop.kind();
  bool ret = WriteNodeToUserObject(node, subUo);
  if (ValueKind::kArray == kind) {
    // the prop definition is different from the yaml content
    const auto& arrayProp = static_cast<const ArrayProperty&>(prop);
    const auto& subKind = arrayProp.element_type();
    if (ret) {
      arrayProp.Insert(uo, 0, make_value(subUo));
      return true;
    }
  } else {
    if (ret) {
      prop.Set(uo, make_value(subUo));
      return true;
    }
  }

  ERR_DEF("write object yaml to user obj failed, prop name: %s", prop.name().data());
  return false;
}

bool CoderYaml::Impl::WriteSimpleYamlValToValue(const YAML::Node& node, const ValueKind& kind, Value& value) {
  switch (kind) {
    case ValueKind::kBoolean: {
      value = make_value( node.IsNull() ? false : node.as<bool>() );
    } break;
    case ValueKind::kInteger: {
      value = make_value( node.IsNull() ? 0 : node.as<int64_t>() );
    } break;
    case ValueKind::kReal: {
      value = make_value( node.IsNull() ? 0 : node.as<double>() );
    } break;
    case ValueKind::kString: {
      value = make_value( node.IsNull() ? "" : node.as<std::string>() );
    } break;
    default: {
      ERR_DEF("not support type: %d", kind);
      return false;
    }
  }
  return true;
}

bool CoderYaml::Impl::WriteSimpleYamlValToProperty(const YAML::Node& node, const Property& prop, const UserObject& uo) {
  Value value;
  const auto& kind = prop.kind();
  bool ret = false;
  if (ValueKind::kArray == kind) {
    // the prop definition is different from the yaml content
    const auto& arrayProp = static_cast<const ArrayProperty&>(prop);
    const auto& subKind = arrayProp.element_type();
    ret = WriteSimpleYamlValToValue(node, subKind, value);
    if (ret) {
      arrayProp.Insert(uo, 0, value);
      return true;
    }
  } else {
    ret = WriteSimpleYamlValToValue(node, kind, value);
    if (ret) {
      prop.Set(uo, value);
      return true;
    }
  }

  ERR_DEF("write simple yaml value to value failed, prop name: %s", prop.name().data());
  return false;
}

}  // namespace reflection
}  // namespace gbf
