
#include "reflection/meta/function.hpp"
#include "core/utils/string_util.h"
#include "reflection/objects/args.hpp"
#include "core/utils/string_util.h"
#include "reflection/meta/func_caller.hpp"

namespace gbf {
namespace reflection {

Function::~Function() {
  Clear();
}


void Function::FireBuilderChanged() {
  // Check argument number for constructor
  for (int i = 1; i < (int)FuncLanguageType::Total; i++) {
    std::map<int, int> arg_number_counts;
    for (auto& caller : reg_callers[i]) {
      int arg_num = caller->GetParamCount();
      auto iter = arg_number_counts.find(arg_num);
      if (iter != arg_number_counts.end()) {
        iter->second++;
      } else {
        arg_number_counts[arg_num] = 1;
      }
    }

    for (auto& caller : reg_callers[i]) {
      int arg_num = caller->GetParamCount();
      if (arg_number_counts[arg_num] == 1) {
        caller->set_is_just_one(true);
      } else {
        caller->set_is_just_one(false);
      }
    }
  }
}

Function::Function(IdRef name, IdRef className) : name_(name), class_name_(className) {
  meta_kind_ = MetaTypeKind::kFunction;
  full_name_ = StringUtil::Format("%s::%s", className.data(), name.data());
}

void Function::Clear() {
  for (int i = 1; i < (int)FuncLanguageType::Total; i++) {
    for (auto& caller : reg_callers[i]) {
      delete caller;
    }
    reg_callers[i].clear();
  }
}

void Function::ClearByType(FuncLanguageType caller_type) {
  int i = (int)caller_type;
  for (auto& caller : reg_callers[i]) {
    delete caller;
  }
  reg_callers[i].clear();
}

Function& Function::AddCaller(FuncLanguageType caller_type, IFuncCaller* caller) {
  reg_callers[(int)caller_type].push_back(caller);
  return *this;
}

}  // namespace reflection
}  // namespace gbf
