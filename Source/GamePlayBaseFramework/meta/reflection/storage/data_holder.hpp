#pragma once

#include <type_traits>
#include "reflection/reflection_fwd.hpp"

namespace gbf {
namespace reflection {

//-------------------------------------------------------------------------------------

struct GBF_REFLECTION_API DataHolder {
  DataHolder() = default;
  // Suppress compiler-generated copy ops to not copy anything:
  DataHolder(DataHolder const&) {}
  DataHolder& operator=(DataHolder const&) { return *this; }

  void* get_buffer_pointer() const { return const_cast<void*>(static_cast<void const*>(&buff_)); }

  void* get_remote_pointer() const { return pobj_; }

  union {
    void* pobj_ = nullptr;
    std::aligned_storage_t<kObjectStorageBuffSize> buff_;
  };
};

//-------------------------------------------------------------------------------------

namespace detail {

template <class StoragePolicy>
constexpr StorageType StoragePolicyToType() {
  if constexpr (std::is_same_v<StoragePolicy, gc_storage_policy>) {
    return StorageType::StorageGc;
  } else if constexpr (std::is_same_v<StoragePolicy, remote_storage_policy>) {
    return StorageType::StorageRemote;
  } else if constexpr (std::is_same_v<StoragePolicy, remote_shared_storage_policy>) {
      return StorageType::StorageRemoteShared;
  }
  else if constexpr (std::is_same_v<StoragePolicy, local_storage_policy>) {
      return StorageType::StorageLocal;
  } else {
    SAFE_STATIC_ASSERT_FALSE("Not supported policy found here.");
    return StorageType::StorageNone;
  }
}

}  // namespace detail

//-------------------------------------------------------------------------------------

}  // namespace reflection
}  // namespace gbf
