#pragma once

#include <type_traits>

////#include "reflection/reflection_define.hpp"

namespace gbf {
namespace reflection {
// struct InvalidType {};

// for SFINAE
template <typename... Args>
struct TVoider {
  using type = void;
};

template <typename... Args>
using TVoidT = typename TVoider<Args...>::type;

// for folder
struct TSwAllowT {
  template <typename... Args>
  constexpr TSwAllowT(Args&&...) noexcept {}
};

// compile time bool constant
template <bool Value>
using TBoolConstant = std::integral_constant<bool, Value>;

template <bool Test, typename Type = void>
using TEnableIf = std::enable_if<Test, Type>;

template <bool Test, typename Type = void>
using TEnableIfT = typename TEnableIf<Test, Type>::type;

template <bool Test, typename Type = void>
using TDisableIf = std::enable_if<!Test, Type>;

template <bool Test, typename Type = void>
using TDisableIfT = typename TDisableIf<Test, Type>::type;

template <typename T>
using _T = typename T::type;

template <typename T>
using TRemoveRcv = std::remove_cv<std::remove_reference_t<T>>;

template <typename T>
using TRemoveRcvT = typename TRemoveRcv<T>::type;

namespace detail {
template <typename T, typename = void>
struct TTraitsTypenameValueType {
  using type = void;
};

template <typename T>
struct TTraitsTypenameValueType<T, TVoidT<typename T::value_type>> {
  using type = typename T::value_type;
};

template <typename T, typename = void>
struct TTraitsTypenameElementType {
  using type = void;
};

template <typename T>
struct TTraitsTypenameElementType<T, TVoidT<typename T::element_type>> {
  using type = typename T::element_type;
};
}  // namespace detail

// traits value_type safe & unsafe version
template <typename T>
using TValueTypeT = typename T::value_type;
template <typename T>
using TSafeValueTypeT = typename detail::TTraitsTypenameValueType<T>::type;

// traits element_type safe & unsafe version
template <typename T>
using TElementTypeT = typename T::element_type;
template <typename T>
using TSafeElementTypeT = typename detail::TTraitsTypenameElementType<T>::type;

template <typename T>
struct TType2Name {
  static char const* name() noexcept { return "type::unknown"; }
};

template <typename T>
using TAddPointerIfNotPointer = std::conditional<std::is_pointer<T>::value, T, std::add_pointer_t<T>>;

template <typename T>
using TAddPointerIfNotPointerT = typename TAddPointerIfNotPointer<T>::type;
}  // namespace reflection

// Common logic
namespace reflection {
template <typename T>
using TNegation = TBoolConstant<!T::value>;

template <typename LHS, typename RHS>
using TMetaEqual = TBoolConstant<LHS::value == RHS::value>;

template <typename LHS, typename RHS>
using TMetaLessThan = TBoolConstant<(LHS::value < RHS::value)>;

template <typename LHS, typename RHS>
using TMetaGreaterThan = TBoolConstant<(LHS::value > RHS::value)>;

template <typename LHS, typename RHS>
using TMetaLessEqualThan = TBoolConstant<LHS::value <= RHS::value>;

template <typename LHS, typename RHS>
using TMetaGreaterEqualThan = TBoolConstant<LHS::value >= RHS::value>;

template <typename... Ts>
struct TConjunction;

namespace detail {
template <typename T, typename... Ts>
struct TConjunctionImpl {
  using type = std::conditional_t<T::value, TConjunction<Ts...>, std::false_type>;
};

template <typename... Ts>
using ConjunctionImplT = typename TConjunctionImpl<Ts...>::type;
}  // namespace detail

template <typename T>
struct TConjunction<T> : TBoolConstant<T::value> {};

template <typename T, typename... Ts>
struct TConjunction<T, Ts...> : detail::ConjunctionImplT<T, Ts...>::type {};

template <typename... Ts>
struct TDisjunction;

namespace detail {
template <typename T, typename... Ts>
struct TDisjunctionImpl {
  using type = std::conditional_t<T::value, std::true_type, TDisjunction<Ts...>>;
};

template <typename... Ts>
using TDisjunctionImplT = typename TDisjunctionImpl<Ts...>::type;
}  // namespace detail

template <typename T>
struct TDisjunction<T> : TBoolConstant<T::value> {};

template <typename T, typename... Ts>
struct TDisjunction<T, Ts...> : detail::TDisjunctionImplT<T, Ts...>::type {};

// inherit from folly
// Lighter-weight than Conjunction, but evaluates all sub-conditions eagerly
namespace detail {
template <bool... Bs>
struct TBools {
  using valid_type = bool;
  static constexpr std::size_t size() { return sizeof...(Bs); }
};
}  // namespace detail

template <typename... Ts>
struct TStrictConjunction : std::is_same<detail::TBools<Ts::value...>, detail::TBools<(Ts::value || true)...>> {};

template <typename... Ts>
struct TStrictDisjunction
    : TNegation<std::is_same<detail::TBools<Ts::value...>, detail::TBools<(Ts::value && false)...>>> {};
}  // namespace reflection

// boolean meta_function
namespace reflection {
// is instance of
template <typename T, template <typename...> class Tmpl>
struct TIsInstanceOf : std::false_type {};

template <template <typename...> class Tmpl, typename... Args>
struct TIsInstanceOf<Tmpl<Args...>, Tmpl> : std::true_type {};

// is pointer of
template <typename T, typename P>
struct TIsPointerOf : std::false_type {};

template <typename T>
struct TIsPointerOf<T*, T> : std::true_type {};

// is C string
template <typename T>
using TIsCstring = TDisjunction<TIsPointerOf<T, char>, TIsPointerOf<T, char const>>;

template <typename T>
using TIsVoid = std::is_void<T>;

template <typename T>
using TIsBoolean = std::is_same<std::add_const_t<T>, bool const>;

template <typename T>
using TIsStrictIntegral = TConjunction<std::is_integral<T>, TNegation<TIsBoolean<T>>>;

template <typename T>
using TIsStrictUintegral64 = std::is_same<std::remove_cv_t<T>, uint64_t>;

template <typename T>
using TIsFloatingPoint = std::is_floating_point<T>;

// is char array
namespace detail {
template <typename T>
struct TIsCharArrayImpl {
 private:
  using array_type = std::remove_reference_t<T>;
  using element_type = std::remove_all_extents_t<array_type>;

 public:
  using type =
      TConjunction<std::is_array<array_type>, TMetaEqual<std::rank<array_type>, std::integral_constant<size_t, 1>>,
                  std::is_same<std::add_const_t<element_type>, char const>>;
};
}  // namespace detail

template <typename T>
using TIsCharArray = _T<detail::TIsCharArrayImpl<T>>;

// is std string
template <typename T>
using TIsStdString = TConjunction<TIsInstanceOf<T, std::basic_string>, std::is_same<TSafeValueTypeT<T>, char>>;

// has c str
template <typename T, typename = void>
struct THasCStr : std::false_type {};

template <typename T>
struct THasCStr<T, TVoidT<decltype(std::declval<T>().c_str())>> : std::true_type {};

// is smart pointer
template <typename T, typename = void>
struct TIsSmartPtr : std::false_type {};

template <typename T>
struct TIsSmartPtr<T, TVoidT<typename T::element_type, decltype(&T::operator->), decltype(&T::operator*),
                              decltype(std::declval<T>().reset()), decltype(std::declval<T>().get()),
                              decltype(static_cast<bool>(std::declval<T>()))>> : std::true_type {};

// is shared pointer
template <typename T, typename = void>
struct TIsSharedPtr : std::false_type {};

template <typename T>
struct TIsSharedPtr<T, TVoidT<decltype(std::declval<T>().use_count())>> : TIsSmartPtr<T> {};

// has forward iterator
template <typename T, typename = void>
struct THasForwardIterator : std::false_type {};

template <typename T>
struct THasForwardIterator<T, TVoidT<typename T::iterator, decltype(std::declval<typename T::iterator>()++)>>
    : std::true_type {};

// check if T has key_type and value_type
template <typename T, typename = void>
struct THasKeyMapType : std::false_type {};

template <typename T>
struct THasKeyMapType<T, TVoidT<typename T::key_type, typename T::mapped_type>> : std::true_type {};

// check if T has value type
template <typename T, typename = void>
struct THasValueType : std::false_type {};

template <typename T>
struct THasValueType<T, TVoidT<typename T::value_type>> : std::true_type {};

// check if T is a container
template <typename T, typename = void>
struct TIsContainer : std::false_type {};

template <typename T>
struct TIsContainer<T, TVoidT<decltype(std::declval<T>().size()),                // has size method
                              decltype(std::declval<T>().begin()),               // has begin method
                              decltype(std::declval<T>().end()),                 // has end method
                              std::enable_if_t<THasValueType<T>::value>,        // has value_type exported
                              std::enable_if_t<THasForwardIterator<T>::value>,  // has forward iterator
                              TDisableIfT<THasCStr<T>::value>                  // string is not container
                              >> : std::true_type {};

// check if T is a sequential container
template <typename T>
using TIsSequentialContainer = TConjunction<TIsContainer<T>, TNegation<THasKeyMapType<T>>>;

// check if T is a associative container
template <typename T>
using TIsAssociativeContainer = TConjunction<TIsContainer<T>, THasKeyMapType<T>>;

template <typename T>
struct TRemovePointerConst {
  using type = T;
};
template <typename T>
struct TRemovePointerConst<T const*> {
  using type = T*;
};
template <typename T>
using TRemovePointerConstT = typename TRemovePointerConst<T>::type;

template <typename T, typename P, typename = void>
struct TSameSize : std::false_type {};
template <typename T, typename P>
struct TSameSize<T, P, std::enable_if_t<sizeof(T) == sizeof(P)>> : std::true_type {};

template <typename Tuple, typename T>
struct TTuplePushFront;
template <typename T, typename... Args>
struct TTuplePushFront<std::tuple<Args...>, T> {
  using type = std::tuple<T, Args...>;
};
template <typename Tuple, typename T>
using TTuplePushFrontT = typename TTuplePushFront<Tuple, T>::type;




}  // namespace reflection
}  // namespace gbf
