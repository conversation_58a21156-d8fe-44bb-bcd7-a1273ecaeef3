
#pragma once

#include <iosfwd>
#include <string>
#include <variant>

#include "reflection/reflection_fwd.hpp"
#include "reflection/type.hpp"

#include "reflection/objects/enumobject.hpp"
#include "reflection/objects/userobject.hpp"
#include "reflection/objects/buildin_value_ref.hpp"
////#include "reflection/objects/userobject_tools.hpp"
////#include "reflection/objects/valueimpl.hpp"
////#include "reflection/objects/valuemapper.hpp"

#include "reflection/objects/array_object.hpp"
////#include "reflection/objects/detail/meta_array_type.hpp"


namespace gbf {
namespace reflection {
/**
 * \brief Variant class which is used to wrap values in the Ponder system
 *
 * The Value class can store any type of variable, and supports conversion
 * to any type compatible with the stored type.
 *
 * \code
 * reflection::Value v1 = true;
 * reflection::Value v2 = 10;
 * reflection::Value v3 = "24.5";
 * reflection::Value v4 = myObject;
 *
 * bool        b = v1; // b == true
 * String      s = v2; // s == "10"
 * float       f = v3; // f == 24.5
 * MyObject    o = v4; // o == myObject
 * \endcode
 *
 * It also supports unary and binary visitation for type-safe processing
 * depending on the stored type.
 *
 * \remark The set of supported types can be extended by specializing the
 * reflection::detail::ValueMapper template.
 *
 * \sa ValueVisitor, reflection::detail::ValueMapper
 */
class GBF_REFLECTION_API Value {
  template <typename T, typename StoragePolicy>
  friend Value make_value(const T& val, StoragePolicy policy);

  ////template <typename T, typename StoragePolicy>
  ////friend Value make_value(T* val, StoragePolicy policy);

  template <typename T>
  friend T& value_ref_as(const Value& val);
 public:
  /**
   * \brief Default constructor, constructs a null value
   */
  Value();

  template<class T>
  Value(const T& val) {
    //some case not need include files support here
    if constexpr (std::is_same_v<UserObject, T>) {
      kind_ = ValueKind::kUser;
      value_ = val;
    } else if constexpr (std::is_same_v<T, EnumObject>) {
      kind_ = ValueKind::kEnum;
      value_ = val;
    } else if constexpr (std::is_enum_v<T>) {
      //Use enum as integer
      kind_ = ValueKind::kInteger;
      value_ = (int64_t)val;
    } else if constexpr (std::is_same_v<T, ArrayObject>) {
      kind_ = ValueKind::kArray;
      value_ = val;
    } else if constexpr (std::is_same_v<void, NoType>) {
      kind_ = ValueKind::kNone;
      value_ = val;
    } else if constexpr (std::is_same_v<const char*, T>) {
      kind_ = ValueKind::kString;
      value_ = reflection::String(val);
    } else if constexpr (std::is_same_v<bool, T>) {
      kind_ = ValueKind::kBoolean;
      value_ = val;
    } else if constexpr (std::is_integral<T>::value) {
      kind_ = ValueKind::kInteger;
      value_ = (int64_t)val;
    } else if constexpr (std::is_floating_point<T>::value) {
      kind_ = ValueKind::kReal;
      value_ = (double)val;
    } else if constexpr (std::is_same_v<reflection::String, T>) {
      kind_ = ValueKind::kString;
      value_ = val;
    }
    else if constexpr (std::is_same_v<detail::BuildInValueRef, T>)
    {
        kind_ = ValueKind::kReference;
        value_ = val;
    }
    else if constexpr (std::is_same_v<reflection::detail::string_view, T>) {
      kind_ = ValueKind::kString;
      value_ = reflection::String(val.data(), val.length());
    } else {
      SAFE_STATIC_ASSERT_FALSE("Can not support as Value ctor mode, please use make_value() for replace!");
      kind_ = ValueKind::kNone;
    }
  }

  /**
   * \brief Copy constructor
   *
   * \param other Value to copy
   */
  Value(const Value& other);

  /**
   * \brief Move constructor
   *
   * \param other Value to move
   */
  Value(Value&& other) noexcept;

  /**
   * \brief Assignment operator
   *
   * \param other Value to assign to this
   */
  void operator=(const Value& other);

  /**
   * \brief Return the Ponder runtime kind of the value
   *
   * \return Type of the value
   */
  ValueKind kind() const;

  /////**
  //// * \brief Convert the value to the type T
  //// *
  //// * Convert the value contained to the type provided. An exception is throw if the target
  //// * type is not compatible. The value returned will be a copy. See ref() and cref() for
  //// * referencing the internal data.
  //// *
  //// * \return Value converted to T
  //// *
  //// * \throw BadType the stored value is not convertible to T
  //// */
  ////template <typename T>
  ////T to() const{
  ////  return Ref<T>();
  ////}

  template <typename T>
  const T& ConstRef() const {
    return Ref<T>();
  }

  /**
   * \brief Get a reference to the value data contained
   *
   * Get a reference to the contained value of type T. The user is responsible for ensuring
   * that the type passed is correct. See cref() for a non-const reference, or to() to
   * convert the value.
   *
   * \return A non-const reference to the contained data.
   */
  template <typename T>
  T& Ref() const {
    using DataType = typename std::remove_const_t<T>;
    try {
      if constexpr (detail::TIsUserType<T>::value) {
        // Support for user type here
        return std::get<UserObject>(value_).Ref<DataType>();
      } else {
        // other types
        return std::get<DataType>(const_cast<Value&>(*this).value_);
      }
    } catch (...) {
      PONDER_ERROR(BadType(kind_, MapType<DataType>()));
    }
  }

  ////template <typename T>
  ////typename std::enable_if_t<!detail::TIsUserType<T>::value, T&> Ref();

  /////**
  //// * \brief Get a const reference to the value data contained
  //// *
  //// * Get a const reference to the contained value of type T. The user is responsible for
  //// * ensuring that the type passed is correct. See ref() for a const reference, or to() to
  //// * convert the value.
  //// *
  //// * \return A const reference to the contained data.
  //// */
  ////template <typename T>
  ////typename std::enable_if_t<detail::TIsUserType<T>::value, const T&> ConstRef() const;

  ////template <typename T>
  ////typename std::enable_if_t<!detail::TIsUserType<T>::value, const T&> ConstRef() const;

  /////**
  //// * \brief Check if the stored value can be converted to a type T
  //// *
  //// * If this function returns true, then calling to<T>() or operator T() will succeed.
  //// *
  //// * \return True if conversion is possible, false otherwise
  //// */
  ////template <typename T>
  ////bool IsCompatible() const;

  /**
   * \brief Operator == to compare equality between two values
   *
   * Two values are equal if their Ponder type and value are equal.
   * It uses the == operator of the stored type.
   *
   * \param other Value to compare with this
   *
   * \return True if both values are the same, false otherwise
   */
  bool operator==(const Value& other) const;

  /**
   * \brief Operator != to compare equality between two values
   *
   * \see operator==
   *
   * \return True if both values are not the same, false otherwise
   */
  bool operator!=(const Value& other) const { return !(*this == other); }

  /**
   * \brief Operator < to compare two values
   *
   * \param other Value to compare with this
   *
   * \return True if this < other
   */
  bool operator<(const Value& other) const;

  /**
   * \brief Operator > to compare two values
   *
   * \param other Value to compare with this
   *
   * \return True if this > other
   */
  bool operator>(const Value& other) const { return !(*this < other || *this == other); }

  /**
   * \brief Operator <= to compare two values
   *
   * \param other Value to compare with this
   *
   * \return True if this <= other
   */
  bool operator<=(const Value& other) const { return (*this < other || *this == other); }

  /**
   * \brief Operator >= to compare two values
   *
   * \param other Value to compare with this
   *
   * \return True if this >= other
   */
  bool operator>=(const Value& other) const { return !(*this < other); }

  static constexpr bool IsBuildInType(ValueKind _kind) {
    switch (_kind) {
      case reflection::ValueKind::kNone:
      case reflection::ValueKind::kBoolean:
      case reflection::ValueKind::kInteger:
      case reflection::ValueKind::kReal:
      case reflection::ValueKind::kString:
      ////case reflection::ValueKind::kEnum:
        return true;
      default:
        return false;
    }
  }

  static constexpr bool IsArithmaticType(ValueKind _kind) {
    return _kind == ValueKind::kInteger || _kind == ValueKind::kReal;
  }

  bool IsBuildIn() const noexcept {
    return IsBuildInType(kind_);
  }

  bool IsArithmatic() const noexcept {
    return IsArithmaticType(kind_);
  }

  bool IsDouble() const noexcept {
    return kind_ == ValueKind::kReal;
  }

  bool IsInteger() const noexcept {
    return kind_ == ValueKind::kInteger;
  }

  bool IsBool() const noexcept {
    return kind_ == ValueKind::kBoolean;
  }

  bool IsUserType() const noexcept {
    return kind_ == ValueKind::kUser;
  }

  void* GetPointer() const;

  Value Clone() const;

  Value CloneSafe() const noexcept;

  /**
   * \brief Special Value instance representing an empty value
   */
  static const Value nothing;

 public:
  ////using Variant = mapbox::util::variant<
  ////	NoType, bool, long, double, reflection::String,
  ////	EnumObject, UserObject, detail::ValueRef
  ////>;

  using Variant = std::variant<NoType, bool, int64_t, double, reflection::String, EnumObject, UserObject, ArrayObject, detail::BuildInValueRef>;

  Variant value_;   // Stored value
  ValueKind kind_;  // Ponder type of the value
};

/**
 * \brief Overload of operator >> to extract a reflection::Value from a standard stream
 *
 * \param stream Source input stream
 * \param value Value to fill
 *
 * \return Reference to the input stream
 */
GBF_REFLECTION_API std::istream& operator>>(std::istream& stream, Value& value);

/**
 * \brief Overload of operator << to print a reflection::Value into a standard stream
 *
 * \param stream Target output stream
 * \param value Value to print
 *
 * \return Reference to the output stream
 */
GBF_REFLECTION_API std::ostream& operator<<(std::ostream& stream, const Value& value);

}  // namespace reflection
}  // namespace gbf

#include "reflection/objects/value.inl"
////#include "reflection/objects/detail/array_object_impl.inl"
////#include "reflection/objects/detail/meta_array_type.inl"
