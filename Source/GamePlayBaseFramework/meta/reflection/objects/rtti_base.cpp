#include "reflection/objects/rtti_base.hpp"
#include "reflection/meta/meta_class.hpp"
#include "memoryhooker/Module.h"
IMPORT_MODULE
namespace gbf {
namespace reflection {

uint64_t RttiBase::__type_id() const noexcept {
  return __has_rtti() ? __rtti_meta()->id() : 0;
}

bool RttiBase::__is_type_match(uint64_t detect_id) const noexcept {
  return __has_rtti() ? __rtti_meta()->IsTypeMatch(detect_id): false;
}

const UsedMetaInfo* RttiBase::__used_meta_info() const noexcept {
  return __has_rtti() ? __rtti_meta()->GetUsedMetaInfoByType(__storage_type()) : nullptr;
}
}  // namespace reflection
}  // namespace gbf
