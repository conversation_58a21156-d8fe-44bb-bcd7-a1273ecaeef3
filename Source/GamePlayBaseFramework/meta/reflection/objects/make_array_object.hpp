#pragma once

#include <variant>

#include "reflection/reflection_fwd.hpp"

#include "reflection/error/errors.hpp"
#include "reflection/objects/array_object.hpp"
#include "reflection/objects/args.hpp"
////#include "reflection/meta/classget.hpp"
#include "reflection/storage/data_holder.hpp"
////#include "reflection/vtable/object_vtable.hpp"
#include "reflection/vtable/array_vtable.hpp"
#include "reflection/traits/type_traits.hpp"
#include "reflection/objects/classcast.hpp"
#include "reflection/objects/value.hpp"
#include "reflection/objects/valueimpl.hpp"

////#include "reflection/objects/objectholder.hpp"
#include "reflection/utils/util.hpp"
#include "memory/gc/gc_make.hpp"



namespace gbf {
namespace reflection {

template <typename T>
inline ArrayObject make_array_object_owned_copy(const T* other) {
  using PropTraits = typename detail::TTypeTraits<T>;
  using RealDataType = typename PropTraits::Type;
  static_assert(!PropTraits::kIsRef, "Cannot make reference to reference");

  return ArrayObject{ query_meta_array<RealDataType>(), kOwnedStorageType, other};
}

template <typename T>
inline ArrayObject make_array_object_remote(const T* other) {
  using PropTraits = typename detail::TTypeTraits<T>;
  using RealDataType = typename PropTraits::Type;
  static_assert(!PropTraits::kIsRef, "Cannot make reference to reference");

  return ArrayObject{query_meta_array<RealDataType>(), StorageType::StorageRemote, other};
}

template <typename T>
inline ArrayObject make_array_object_owned_default() {
  using PropTraits = typename detail::TTypeTraits<T>;
  using RealDataType = typename PropTraits::Type;
  static_assert(!PropTraits::kIsRef, "Cannot make reference to reference");

  return ArrayObject{query_meta_array<RealDataType>(), kOwnedStorageType};
}

inline ArrayObject make_array_object_by_name(const std::string& n) {
  const auto* meta_array = query_meta_array_by_name(n);
  if(GBF_LIKELY(meta_array)) {
    return ArrayObject{meta_array, kOwnedStorageType};
  } else {
    return ArrayObject::nothing;
  }
}

inline ArrayObject make_array_object_by_id(uint64_t tid) {
  const auto* meta_array = query_meta_array_by_id(tid);
  if (GBF_LIKELY(meta_array)) {
    return ArrayObject{meta_array, kOwnedStorageType};
  } else {
    return ArrayObject::nothing;
  }
}

}  // namespace reflection
}  // namespace gbf

