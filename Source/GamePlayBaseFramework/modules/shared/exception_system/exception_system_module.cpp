#include "exception_system/exception_system_module.h"

namespace gbf {

ExceptionSystemModule::ExceptionSystemModule() {}

ExceptionSystemModule::~ExceptionSystemModule() {}

void ExceptionSystemModule::SetUseFullDump(bool value)
{
    mUseFullDump = value;
}

std::function<void()> ExceptionSystemModule::CrashCallBack;

ModuleCallReturnStatus ExceptionSystemModule::Init()
{
#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
  static auto dumpCallback = [](const wchar_t *dumpPath, const wchar_t *id, void *context, EXCEPTION_POINTERS *exinfo, MDRawAssertionInfo *assertion,
                                bool succeeded) {
    printf("Dump path: %ls\n", dumpPath);
    return succeeded;
  };
  std::string path("C:/tmp");
  if (dump_path_.size()) {
    path = dump_path_;
  }
 
  if (!mUseFullDump)
  {
    mEH = new google_breakpad::ExceptionHandler(std::wstring(path.begin(), path.end()), NULL, dumpCallback, NULL, google_breakpad::ExceptionHandler::HANDLER_ALL);
  }
  else
  {
    mEH = new google_breakpad::ExceptionHandler(std::wstring(path.begin(), path.end()), NULL, dumpCallback, NULL, 
        google_breakpad::ExceptionHandler::HANDLER_ALL, MINIDUMP_TYPE::MiniDumpWithFullMemory, HANDLE(NULL), NULL);
  }
#elif GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_LINUX
  static char uploadCommand[200];
  static auto dumpCallback = [](const google_breakpad::MinidumpDescriptor& descriptor, void* context, bool succeeded) {
    printf("Dump path: %s\n", descriptor.path());

    sprintf(uploadCommand, "sh tools/breakpad/upload_stacktrace.sh %s", descriptor.path());
    FILE* pp = popen(uploadCommand, "r");
    if (!pp) return succeeded;
    pclose(pp);

    return succeeded;
  };
  google_breakpad::MinidumpDescriptor descriptor("/data/corefile");
  static google_breakpad::ExceptionHandler eh(descriptor, nullptr, dumpCallback, nullptr, true, -1);
#endif
  return ModuleCallReturnStatus::Succeed;
}

ModuleCallReturnStatus ExceptionSystemModule::Start() { return ModuleCallReturnStatus::Succeed; }

ModuleCallReturnStatus ExceptionSystemModule::Update() { return ModuleCallReturnStatus::Succeed; }

ModuleCallReturnStatus ExceptionSystemModule::Stop() { return ModuleCallReturnStatus::Succeed; }

ModuleCallReturnStatus ExceptionSystemModule::Release() { return ModuleCallReturnStatus::Succeed; }

void ExceptionSystemModule::SetExceptionDumpPath(const char *path) { dump_path_ = path; }

void ExceptionSystemModule::Free()
{
    delete this;
}

ModuleCallReturnStatus ExceptionSystemModule::InitWithCallBack(std::function<void()> callback)
{
    CrashCallBack = callback;
#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
    auto BreakPadCallBack = [](const wchar_t* dumpPath, const wchar_t* id, void* context, EXCEPTION_POINTERS* exinfo, MDRawAssertionInfo* assertion, bool succeeded) {
        CrashCallBack();
        PEXCEPTION_RECORD pExceptionRecord = exinfo->ExceptionRecord;
        DWORD exceptionCode = pExceptionRecord->ExceptionCode;
        PVOID exceptionAddress = pExceptionRecord->ExceptionAddress;
        switch (exceptionCode)
        {
        case EXCEPTION_ACCESS_VIOLATION:
            printf("Access violation at address %p\n", exceptionAddress);
            break;
        case EXCEPTION_STACK_OVERFLOW:
            printf("Stack overflow at address %p\n", exceptionAddress);
            break;
        default:
            printf("Exception code: 0x%08X at address %p\n", exceptionCode, exceptionAddress);
            break;
        }

        return succeeded;
    };
    std::string path("C:/tmp");
    if (dump_path_.size())
    {
        path = dump_path_;
    }

    if (!mUseFullDump)
    {
        mEH = new google_breakpad::ExceptionHandler(std::wstring(path.begin(), path.end()), NULL, BreakPadCallBack,
            this,
            google_breakpad::ExceptionHandler::HANDLER_ALL);
    }
    else
    {
        mEH = new google_breakpad::ExceptionHandler(std::wstring(path.begin(), path.end()), NULL, BreakPadCallBack,
                                                    this,
                                                    google_breakpad::ExceptionHandler::HANDLER_ALL,
                                                    MINIDUMP_TYPE::MiniDumpWithFullMemory,
                                                    HANDLE(NULL),
                                                    NULL);
    }
#elif GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_LINUX
    static char uploadCommand[200];
    BreakPadCallBack = [](const google_breakpad::MinidumpDescriptor& descriptor, void* context, bool succeeded) {
        printf("Dump path: %s\n", descriptor.path());

        sprintf(uploadCommand, "sh tools/breakpad/upload_stacktrace.sh %s", descriptor.path());
        FILE* pp = popen(uploadCommand, "r");
        if (!pp)
            return succeeded;
        pclose(pp);

        return succeeded;
    };
    google_breakpad::MinidumpDescriptor descriptor("/data/corefile");
    static google_breakpad::ExceptionHandler eh(descriptor, nullptr, BreakPadCallBack, nullptr, true, -1);
#endif
    return ModuleCallReturnStatus::Succeed;
}
}  // namespace gbf