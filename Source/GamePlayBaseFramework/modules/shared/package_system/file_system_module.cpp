#include "file_system_module.hpp"

#include <cassert>
#include <filesystem>
#include <fstream>

#include "core/core_global.hpp"
#include "imod_shared/imodules/ijob_system_module.h"
#include "core/utils/byte_buffer.h"

#if GBF_ENABLE_CPP20
#include "async_task/tasks/coro_service_manager.hpp"
#include "async_task/tasks/default_awaitable20.hpp"
#include "async_task/tasks/coroutines/cotask_awaitable20.hpp"
#endif
#include "memoryhooker/Module.h"
IMPORT_MODULE
namespace gbf {
FileSystemModule::FileSystemModule() {}

FileSystemModule::~FileSystemModule() {}

ModuleCallReturnStatus FileSystemModule::Init() {
  DEPEND_ON_INIT(gbf::kModuleJobSystemName);

  return ModuleCallReturnStatus::Succeed;
}

ModuleCallReturnStatus FileSystemModule::Start() { return ModuleCallReturnStatus::Succeed; }

ModuleCallReturnStatus FileSystemModule::Update() { return ModuleCallReturnStatus::Succeed; }

ModuleCallReturnStatus FileSystemModule::Stop() { return ModuleCallReturnStatus::Succeed; }

ModuleCallReturnStatus FileSystemModule::Release() { return ModuleCallReturnStatus::Succeed; }

void FileSystemModule::Free() { delete this; }

bool FileSystemModule::SetAppResourcePath(const std::string_view workPath) {
  std::string tmpPath(workPath.data(), workPath.length());
  StringUtil::Trim(tmpPath);

  if (tmpPath.empty()) {
    // ToDo: add log here
    return false;
  }

  auto pathobj = std::filesystem::path(tmpPath);
  if (std::filesystem::is_directory(pathobj)) {
    app_resource_path_ = tmpPath;
    // std::filesystem::current_path(pathobj);
    return true;
  } else {
    printf("not a direct :%s\n", tmpPath.c_str());
    // ToDo: add log here
    return false;
  }
}

std::string FileSystemModule::GetAppResourcePath() const { return app_resource_path_; }

std::string FileSystemModule::GetFullPath(const std::string_view relativePath) {
  // auto tmpPath = std::filesystem::path(mAppResourcePath);
  // tmpPath /= relativePath;
  // return tmpPath.string();

  return CombinePath(app_resource_path_, relativePath);
}

void FileSystemModule::SetScriptRootPath(const char* scriptRootPath) { script_root_path_.assign(scriptRootPath); }

std::string FileSystemModule::GetScriptRootPath() const { return script_root_path_; }

bool FileSystemModule::IsFileExist(const std::string_view fileFullName) {
  auto tmpPath = std::filesystem::path(fileFullName);
  return std::filesystem::is_regular_file(tmpPath);
}

bool FileSystemModule::IsDirectoryExist(const std::string_view directoryFullName) {
  auto tmpPath = std::filesystem::path(directoryFullName);
  return std::filesystem::is_directory(tmpPath);
}

std::string FileSystemModule::GetAppCurrentPath() {
  auto tmpPath = std::filesystem::current_path();
  return tmpPath.string();
}

unsigned long long FileSystemModule::GetLastWriteTime(const std::string_view fileFullName) {
  std::error_code errCode;
  auto ftime = std::filesystem::last_write_time(std::filesystem::path(fileFullName), errCode);
  return ftime.time_since_epoch().count();
}

bool FileSystemModule::WriteDataToFileSystemPath(const std::string_view fileFullName, ByteBufferPtr writeData) {
  try {
    std::fstream f;
    f.open(fileFullName.data(), std::ios::binary | std::ios::out);

    f.write((const char*)(writeData->ReadPtr()), writeData->Size());

    f.close();

    return true;
  } catch (std::exception& ex) {
    // ToDo: add log here
    return false;
  }
}

bool FileSystemModule::WriteDataToFile(const std::string_view relPath, ByteBufferPtr writeData) {
  std::string fullPath = GetFullPath(relPath);
  return WriteDataToFileSystemPath(fullPath, writeData);
}

ByteBufferPtr FileSystemModule::ReadDataFromFileSystemPath(const std::string_view fileFullName) {
  ByteBufferPtr outBuf;
  try {
    std::ifstream f;
    if (std::filesystem::is_regular_file(std::filesystem::path(fileFullName.data()))) {
      f.open(fileFullName.data(), std::ios_base::binary | std::ios_base::in);

      f.seekg(0, std::ios_base::end);
      size_t totalSize = (size_t)f.tellg();
      f.seekg(0, std::ios_base::beg);
      outBuf = std::make_shared<ByteBuffer>(totalSize + 1);
      f.read((char*)outBuf->WritePtr(), totalSize);
      outBuf->WritePtr()[totalSize] = 0;
      outBuf->WritePosition(totalSize);
      f.close();
    }

    return outBuf;
  } catch (std::exception& ex) {
    // ToDo: add log here
    outBuf.reset();
    return outBuf;
  }
}

gbf::ByteBufferPtr FileSystemModule::ReadDataFromFile(const std::string_view relPath) {
  std::string fullPath = GetFullPath(relPath);
  return ReadDataFromFileSystemPath(fullPath);
}

gbf::jobs::job_ticket_ptr FileSystemModule::ReadDataFromFileAsync(const std::string_view relPath,
                                                                       const FileLoadFunction& loadFunc) {
  auto ticket = GJobSystem->RequestTicket();
  auto fullPath = GetFullPath(relPath);
  GJobSystem->Post(
      [this, ticket, relPath, fullPath, loadFunc]() {
        ByteBufferPtr outBuf;
        try {
          std::ifstream f;
          f.open(fullPath, std::ios_base::binary | std::ios_base::in);

          f.seekg(0, std::ios_base::end);
          size_t totalSize = (size_t)f.tellg();
          f.seekg(0, std::ios_base::beg);
          outBuf = std::make_shared<ByteBuffer>(totalSize + 1);
          f.read((char*)outBuf->WritePtr(), totalSize);
          outBuf->WritePtr()[totalSize] = 0;
          outBuf->WritePosition(totalSize);

          f.close();
        } catch (std::exception& ex) {
          ERR_DEF("read file:[%s] error:%s", fullPath.c_str(), ex.what());
        }

        GJobSystem->Post(
            [outBuf, ticket, relPath, loadFunc]() {
              if (ticket) {
                loadFunc(ticket, relPath, "", outBuf);
              }
            },
            JobType::kLogicJob);
      },
      JobType::kSlowJob);

  return ticket;
}

#if GBF_ENABLE_CPP20
coro::cotask20<ByteBuffer> FileSystemModule::AwaitReadDataFromFile(const std::string_view rel_path, JobType target_job_type) { 
  auto* coro_manager = GJobSystem->GetCoroServiceManager();
  std::string full_path = GetFullPath(rel_path);
  auto ret_val = coro_manager->create_task20(JobType::kSlowJob, [full_path, target_job_type]() -> coro::cotask20<ByteBuffer> {
    ByteBuffer out_buf;
    try {
      std::ifstream f;
      f.open(full_path, std::ios_base::binary | std::ios_base::in);

      f.seekg(0, std::ios_base::end);
      size_t totalSize = (size_t)f.tellg();
      f.seekg(0, std::ios_base::beg);
      out_buf = ByteBuffer(totalSize + 1);
      f.read((char*)out_buf.WritePtr(), totalSize);
      out_buf.WritePtr()[totalSize] = 0;
      out_buf.WritePosition(totalSize);

      f.close();
    } catch (std::exception& ex) {
      ERR_DEF("read file:[%s] error:%s", full_path.c_str(), ex.what());
    }

    co_await coro::tasks::transfer(JobType::kLogicJob);
    co_return out_buf;
  });

  return ret_val; 
}
#endif

void ListFilesRecursive(const std::string_view filePath, gbf::StringVector& fileNameList, bool isRecursive) {
  try {
    for (const auto& entry : std::filesystem::directory_iterator(filePath)) {
      if (entry.is_regular_file()) {
        fileNameList.emplace_back(entry.path().string());
      } else if (entry.is_directory()) {
        if (isRecursive) {
          ListFilesRecursive(entry.path().string(), fileNameList, isRecursive);
        }
      }
    }
  } catch (const std::exception& ex) {
    // ToDo: add log here
    int a;
    a = 1;

  }
}

gbf::StringVector FileSystemModule::ListFiles(const std::string_view relPath, bool isRecursive) {
  std::string fullPath = GetFullPath(relPath);
  StringVector tmpVec;
  ListFilesRecursive(fullPath, tmpVec, isRecursive);
  return tmpVec;
}

gbf::StringVector FileSystemModule::ListFilesSystemPath(const std::string_view sysPath, bool isRecursive) {
  StringVector tmpVec;
  ListFilesRecursive(sysPath, tmpVec, isRecursive);
  return tmpVec;
}

bool FileSystemModule::CreateDirectory(const std::string_view relPath) {
  std::string fullPath = GetFullPath(relPath);
  std::error_code errCode;

  std::filesystem::create_directory(std::filesystem::path(fullPath), errCode);

  if (errCode) {
    // Todo: add log here
    return false;
  } else {
    return true;
  }
}

bool FileSystemModule::CreateDirectories(const std::string_view relPath) {
  std::string fullPath = GetFullPath(relPath);
  std::error_code errCode;
  std::filesystem::create_directories(std::filesystem::path(fullPath), errCode);

  if (errCode) {
    // Todo: add log here
    return false;
  } else {
    return true;
  }
}

bool FileSystemModule::Remove(const std::string_view relPath) {
  std::string fullPath = GetFullPath(relPath);
  std::error_code errCode;
  std::filesystem::remove(std::filesystem::path(fullPath), errCode);

  if (errCode) {
    // Todo: add log here
    return false;
  } else {
    return true;
  }
}

bool FileSystemModule::RemoveAll(const std::string_view relPath) {
  std::string fullPath = GetFullPath(relPath);
  std::error_code errCode;
  std::filesystem::remove_all(std::filesystem::path(fullPath), errCode);

  if (errCode) {
    // Todo: add log here
    return false;
  } else {
    return true;
  }
}

std::string FileSystemModule::CombinePath(const std::string_view path1, const std::string_view path2) {
  std::string tmpPath = std::string(path1);
  if (tmpPath.empty()) {
    return std::string(path2);
  } else if (StringUtil::EndsWith(tmpPath, "\\") || StringUtil::EndsWith(tmpPath, "/")) {
    tmpPath += path2;
    return tmpPath;
  } else {
#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
    tmpPath += "\\";
    tmpPath += path2;
#else
    tmpPath += "/";
    tmpPath += path2;
#endif
    return tmpPath;
  }
}

bool FileSystemModule::SetModuleRootPath(const std::string moduleName, const std::string_view workPath) {
  std::string tmpPath(workPath.data(), workPath.length());
  StringUtil::Trim(tmpPath);

  if (tmpPath.empty()) {
    // ToDo: add log here
    return false;
  }

  auto pathobj = std::filesystem::path(tmpPath);
  if (std::filesystem::is_directory(pathobj)) {
    map_module_path_.insert(std::pair<std::string, std::string>(moduleName, tmpPath));
    return true;
  } else {
    printf("not a direct :%s\n", tmpPath.c_str());
    // ToDo: add log here
    return false;
  }
}

std::string FileSystemModule::GetFullPathByModuleName(const std::string moduleName,
                                                         const std::string_view relativePath) {
  std::map<std::string, std::string>::iterator iter;
  iter = map_module_path_.find(moduleName);
  if (iter == map_module_path_.end()) {
    // printf("not find module:%s\n",moduleName.c_str());
    // ToDo: add log here
    return GetFullPath(relativePath);
  }

  auto tmpPath = std::filesystem::path(iter->second);
  tmpPath /= relativePath;
  return tmpPath.string();
}

ByteBufferPtr FileSystemModule::ReadDataFromModuleFile(const std::string moduleName,
                                                          const std::string_view relPath) {
  if (moduleName.length() == 0) {
    return ReadDataFromFile(relPath);
  }

  std::string fullPath = GetFullPathByModuleName(moduleName, relPath);
  return ReadDataFromFileSystemPath(fullPath);
}
}  // namespace gbf
