cmake_minimum_required(VERSION 3.16)

include_directories(
    ${PROJECT_SOURCE_DIR}/base
    ${PROJECT_SOURCE_DIR}/meta
    ${PROJECT_SOURCE_DIR}/async
    ${PROJECT_SOURCE_DIR}/imodules
    ${PROJECT_SOURCE_DIR}/modules/shared
    ${PROJECT_SOURCE_DIR}/modules/shared/package_system
    ${PROJECT_SOURCE_DIR}/third_party
    # ${PROJECT_SOURCE_DIR}/externals/include/
    # ${PROJECT_SOURCE_DIR}/externals/include/zlib
)

link_directories(
    ${CMAKE_BINARY_DIR}
    #${EXTERNAL_LIB_DIR}/zlib
)

add_definitions(-DGBF_PACKAGE_SYSTEM_EXPORTS)


set(all_project_src "")


file(GLOB package_system_src "*.*")
source_group(\\ FILES ${package_system_src})
list(APPEND all_project_src ${package_system_src})

file(GLOB archive_src "archive/*.*")
source_group(\\archive FILES ${archive_src})
list(APPEND all_project_src ${archive_src})

file(GLOB zip_src "zip/*.*")
source_group(\\zip FILES ${zip_src})
list(APPEND all_project_src ${zip_src})

file(GLOB sqlite_src "sqlite/*.*")
source_group(\\sqlite FILES ${sqlite_src})
list(APPEND all_project_src ${sqlite_src})

file(GLOB crypto_src "crypto/*.*")
source_group(\\crypto FILES ${crypto_src})
list(APPEND all_project_src ${crypto_src})

# no unity build support files here
set(no_unity_build_src
    "./zip/zip.c"
    "./zip/miniz.c"
    "./sqlite/sqlite3.h"
)    
set_source_files_properties(${no_unity_build_src} PROPERTIES SKIP_UNITY_BUILD_INCLUSION ON)


set(package_system_name "package_system")

add_library(${package_system_name} SHARED 
    ${all_project_src}
)

add_dependencies(${package_system_name} 
    gbf_core
    async_task
    imod_shared
    # sqlitecpp
)

target_link_libraries(${package_system_name} 
    PUBLIC gbf_core
    PUBLIC async_task
    PUBLIC imod_shared
    # PUBLIC sqlitecpp
)


# if(WIN32)
#     target_link_libraries(${package_system_name}
#        PRIVATE zlibstatic${CMAKE_STATIC_LIBRARY_SUFFIX}
#    )
#elseif(APPLE)
#elseif(UNIX)
#    target_link_libraries(${package_system_name}
#        PRIVATE zlibstatic${CMAKE_STATIC_LIBRARY_SUFFIX}
#    )
#else()
#    message(STATUS "unknown os find!")
#endif()

set_target_properties(${package_system_name} PROPERTIES UNITY_BUILD ON)
SET_PROPERTY(TARGET ${package_system_name} PROPERTY FOLDER "framework c++/modules/shared")

#define_filename_macro(${package_system_name})
