#include "GameFramework/GameObjects/Pawn.h"
#include "GameFramework/Components/ModelComponent.h"
#include "GameFramework/Components/PawnMovementComponent.h"
#include "GameFramework/GameWorld.h"
#include "GameFramework/GameObjects/GameObject.h"
#include "GameFramework/GameObjects/Controller.h"
#include "GameFramework/GameObjects/PlayerController.h"
#include "Input/InputComponent.h"
#include "GameFramework/GameWorld.h"
#include "Input/InputDelegateBinding.h"

namespace cegf
{

Pawn::Pawn()
{
    mTickFunction->bCanEverTick = true;
}

Pawn::~Pawn()
{   
    mController = nullptr;
}

void Pawn::Destroyed()
{
    mController = nullptr;
    GameObject::Destroyed();
}

void Pawn::InitializeComponents()
{
    GameObject::InitializeComponents();
    mInputComponent = AddComponent<InputComponent>();
}

void Pawn::PostInitializeComponents()
{
    GameObject::PostInitializeComponents();
    
    //Spawn default controller if need ?
}

void Pawn::FinishCreating()
{
    GameObject::FinishCreating();
    if (mController)
    {
        mController->SetControlRotation(GetWorldRotation());
    }
}

cross::TRSVector3AType Pawn::GetPawnViewLocation() const
{
    return GetWorldTranslation() + cross::TRSVector3AType(0.0f, 0.0f, mBaseEyeHeight);
}

cross::TRSQuaternionAType Pawn::GetPawnViewRotation() const
{
    if (mController != nullptr)
    {
        return mController->GetControlRotation();
    }
    return GetWorldRotation();
}

void Pawn::PossessedByController(Controller* newController)
{
    SetController(newController);
}

void Pawn::UnPossessedByController()
{
    SetController(nullptr);
}

void Pawn::AddControllerPitchInput(float val)
{
    if (val != 0.0f && mController && mController->IsPlayerController())
    {
        PlayerController* player = TYPE_CAST(PlayerController*, mController);
        assert(player != nullptr);
        player->AddPitchInput(val);
    }
}

void Pawn::AddControllerYawInput(float val)
{
    if (val != 0.0f && mController && mController->IsPlayerController())
    {
        PlayerController* player = TYPE_CAST(PlayerController*, mController);
        assert(player != nullptr);
        player->AddYawInput(val);
    }
}

void Pawn::AddControllerRollInput(float val)
{
    if (val != 0.0f && mController && mController->IsPlayerController())
    {
        PlayerController* player = TYPE_CAST(PlayerController*, mController);
        assert(player != nullptr);
        player->AddRollInput(val);
    }
}
void Pawn::SpawnDefaultController()
{
    if (mController == nullptr)
    {
        auto controller = GetWorld()->CreateGameObject<PlayerController>(cross::TRSVector3AType::Zero(), cross::TRSQuaternionAType::Identity(), cross::TRSVector3AType::One());
        mController = controller.get();
        if (mController)
        {
            mController->StartGame();
            mController->Possess(this);
        }
    }
}

int Pawn::TestPawn(int param)
{
    LOG_INFO("GameObject::TestFuncGameObject {} name = {}", param, GetName());
    return param;
}

void Pawn::PostEditChangeProperty(PropertyChangedEvent& PropertyChangedEvent)
{
    GameObject::PostEditChangeProperty(PropertyChangedEvent);
}

void Pawn::StartGame()
{
    GameObject::StartGame();

    if (mInputComponent)
    {
        InputDelegateBinding::BindInputDelegatesWithSubojects(this, mInputComponent);
    }
    if (mController == nullptr)
    {
        SpawnDefaultController();
    }
}

PawnMovementComponent* Pawn::GetMovementComponent() const
{
    return dynamic_cast<PawnMovementComponent*>(GetComponent<PawnMovementComponent>());
}

void Pawn::EndGame()
{
    DetachFromController();
    GameObject::EndGame();
}

void Pawn::DetachFromController()
{
    if (mController && mController->GetPawn() == this)
    {
        mController->PawnPendingDestroy(this);
        if (mController != nullptr)
        {
            mController->UnPossess();
            mController = nullptr;
        }
    }
}

void Pawn::AddMovementInput(cross::Float3 worldDirection, float ScaleValue, bool bForce /*=false*/)
{
    //PawnMovementComponent* MovementComponent = dynamic_cast<PawnMovementComponent*>(GetComponent<PawnMovementComponent>());
    PawnMovementComponent* MovementComponent = GetMovementComponent();
    if (MovementComponent)
    {
        MovementComponent->AddInputVector(worldDirection * ScaleValue, bForce);
    }
}

}   // namespace cegf
