#include "GameFramework/GameObjects/Character.h"
#include "GameFramework/Components/ModelComponent.h"
#include "GameFramework/Components/CapsuleComponent.h"
#include "GameFramework/Components/CharacterMovementComponent.h"
#include "GameFramework/GameObjects/Pawn.h"

namespace cegf
{

Character::Character()
{
    
}

Character::~Character()
{
    mModelComponent = nullptr;
    mCapsuleComponent = nullptr;
    mCharacterMovementComponent = nullptr;
}

void Character::Destroyed()
{
    mModelComponent = nullptr;
    mCapsuleComponent = nullptr;
    mCharacterMovementComponent = nullptr;
    Pawn::Destroyed();
}

void Character::InitializeComponents()
{
    mModelComponent = AddComponent<ModelComponent>();
    if (mModelComponent->GetModelAssetPath().empty())
        mModelComponent->SetModelAssetPath("EngineResource/Model/Mannequin_180.nda");

    mCapsuleComponent = AddComponent<CapsuleComponent>();
    mCharacterMovementComponent = AddComponent<CharacterMovementComponent>();
    mCharacterMovementComponent->SetUpdatedComponent(mModelComponent);
    mCharacterMovementComponent->SetOwnerCharacter(this);
    mCharacterMovementComponent->SetOwnerPawn(this);
    mAnimationComponent = AddComponent<AnimationComponent>();
    //mAnimationComponent->SetSkeletonAnimation("EngineResource/Animation/Mannequin/Mannequin_180_SK.nda", "EngineResource/Animation/Mannequin/TPS_Character.stb.nda");
    Pawn::InitializeComponents();
}

void Character::Jump()
{
    mCharacterMovementComponent->Jump();
}

void Character::StopJumping()
{
    mCharacterMovementComponent->StopJumping();
}
void Character::SetSkeletonAnimation(std::string _SkeletonPath, std::string _AnimatorResourcePath) 
{
    mAnimationComponent->SetSkeletonAnimation(_SkeletonPath, _AnimatorResourcePath);
}
std::string Character::GetAnimatorAssetPath()
{
    return mAnimationComponent->GetAnimatorAssetPath();
}
std::string Character::GetAnimatorName()
{
    return mAnimationComponent->GetAnimatorName();
}

}   // namespace cegf