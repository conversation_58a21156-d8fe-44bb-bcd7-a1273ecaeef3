#include "CECommon/Common/EngineGlobal.h"
#include "Runtime/Input/InputManager.h"
#include "GameFramework/GameWorld.h"
#include "GameFramework/GameObjects/PlayerController.h"
#include "GameFramework/Components/WorkFlowComponent.h"
#include "GameFramework/Camera/CameraManager.h"
#include "GameFramework/GameObjects/Pawn.h"
#include "GameFramework/Input/InputComponent.h"
#include "GameFramework/Components/WorkFlowComponent.h"

namespace cegf
{
PlayerController::PlayerController() 
{
    mTickFunction->bCanEverTick = true;
    mIsPlayerController = true;
}

PlayerController::~PlayerController()
{
    mCameraMgr.reset();
}

void PlayerController::Tick(float deltaTime)
{
    GameObject::Tick(deltaTime);

    //tick player input
    TickPlayerInput();
    //update control rotation
    UpdateRotation(deltaTime);

    if (mCameraMgr)
    {
        mCameraMgr->UpdateCamera(deltaTime);
    }

    //clear old axis inputs
    mRotationInput = cross::Float3::Zero();
}

void PlayerController::BeginDestroy()
{
    if (mCameraMgr)
    {
        mCameraMgr->BeginDestroy();
    }
    Controller::BeginDestroy();
}

void PlayerController::Destroyed()
{
    if (mCameraMgr)
    {
        mCameraMgr->Destroyed();
    }
    Controller::Destroyed();
}

void PlayerController::PostInitializeComponents()
{
    Controller::PostInitializeComponents();
    
    CreateCameraManager();
}

GameObject* PlayerController::GetViewTarget() const
{
    return mCameraMgr ? mCameraMgr->GetViewTarget() : nullptr;
}

void PlayerController::SetViewTarget(GameObject* viewTarget)
{
    if (mCameraMgr)
    {
        mCameraMgr->SetViewTarget(viewTarget);
    }
}

void PlayerController::UpdateRotation(float deltaTime)
{
    //calculate Delta to be applied on ViewRotation
    //rotator DeltaRot(RotationInput);
    cross::Float3A deltaRotEuler(mRotationInput);
    cross::QuaternionA viewRotation(GetControlRotation());
    
    if (mCameraMgr)
    {
        mCameraMgr->ProcessViewRotation(deltaTime, viewRotation, deltaRotEuler);
    }
    
    SetControlRotation(cross::TRSQuaternionAType(viewRotation));

//  Pawn* const P = GetPawnOrSpectator();
//  if (P)
//  {
//      P->FaceRotation(viewRotation, deltaTime);
//  }
}

void PlayerController::AddPitchInput(float val)
{
    mRotationInput.x += val * mInputPitchScale;
}

void PlayerController::AddYawInput(float val)
{
    mRotationInput.y += val * mInputYawScale;
}

void PlayerController::AddRollInput(float val)
{
    mRotationInput.z += val * mInputRollScale;
}

void PlayerController::CreateCameraManager()
{
    CreateGameObjectParameters param;
    param.mParent = this;

    GameWorld* world = GetWorld();
    mCameraMgr = world->CreateGameObject<CameraManager>(GetLocalTranslation(), GetLocalRotation(), GetLocalScale(), param);
    mCameraMgr->Init(this);
}

void PlayerController::TickPlayerInput()
{
    //TODO complete player input
    if (GetPawn() == nullptr || GetPawn()->GetComponent<cegf::InputComponent>() == nullptr)
    {
        return;
    }

    cross::EngineGlobal& global = cross::EngineGlobal::Inst();
    cross::InputManager* inputMgr = global.GetInputManager();
    auto user = inputMgr->GetUser({0});

    InputComponent* IC = static_cast<InputComponent*>(GetPawn()->GetComponent<InputComponent>());

    for (const InputKeyBinding& KeyBinding : IC->KeyBindings)
    {
        // handle input
        if (KeyBinding.Chord.bAlt && !user->IsPressed(cross::input::CEKeys::LeftAlt) && !user->IsPressed(cross::input::CEKeys::RightAlt) ||
            KeyBinding.Chord.bControl && !user->IsPressed(cross::input::CEKeys::LeftControl) && !user->IsPressed(cross::input::CEKeys::RightControl) ||
            KeyBinding.Chord.bShift && !user->IsPressed(cross::input::CEKeys::LeftShift) && !user->IsPressed(cross::input::CEKeys::RightShift) ||
            KeyBinding.Chord.bCommand && !user->IsPressed(cross::input::CEKeys::LeftCommand) && !user->IsPressed(cross::input::CEKeys::RightCommand))
        {
            continue;
        }

        // TODO: remove this if-else
        if (KeyBinding.KeyEvent == cross::input::CEInputEvent::Type::Pressed)
        {
            if (user->IsPressed(KeyBinding.Chord.Key))
            {
                if (KeyBinding.KeyDelegate)
                {
                    ExecDelegateNode(KeyBinding.KeyDelegate, KeyBinding.Chord.Key);
                }

                if (KeyBinding.TypeScriptKeyDelegate)
                {
                    KeyBinding.TypeScriptKeyDelegate();
                }
                    
            }
        }
        else if (KeyBinding.KeyEvent == cross::input::CEInputEvent::Type::Released)
        {
            if (user->WasJustReleased(KeyBinding.Chord.Key))
            {
                if (KeyBinding.KeyDelegate)
                {
                    ExecDelegateNode(KeyBinding.KeyDelegate, KeyBinding.Chord.Key);
                }

                if (KeyBinding.TypeScriptKeyDelegate)
                {
                    KeyBinding.TypeScriptKeyDelegate();
                }
            }
        }
        else if (KeyBinding.KeyEvent == cross::input::CEInputEvent::Type::Axis)
        {
            // TODO(hendrikwang): Support 3D key value
            const cross::Float3 value = user->GetKeyValue(KeyBinding.Chord.Key);
            if (value != cross::Float3::Zero())
            {
                if (KeyBinding.KeyDelegate)
                {
                    ExecDelegateNode(KeyBinding.KeyDelegate, KeyBinding.Chord.Key, value.x, value.y, value.z);
                }

                if (KeyBinding.TypeScriptKeyDelegate)
                {
                    KeyBinding.TypeScriptKeyDelegate();
                }
            }
        }

    }

    for (const InputActionBinding& ActionBinding : IC->ActionBindings)
    {
        std::string ActionName = ActionBinding.InputActionName;
        if (ActionBinding.TriggerEventState == user->GetInputActionTriggerState(ActionName))
        {
            cross::InputActionValue actionValue = user->GetInputActionValue(ActionName);
            ExecDelegateNode(ActionBinding.ActionDelegate, actionValue);

            if (ActionBinding.TypeScriptActionDelegate)
            {
                ActionBinding.TypeScriptActionDelegate(actionValue);
            }
        }
    }

}

void PlayerController::ExecDelegateNode(RttiBase* node, const cross::input::CEKey& Key) const
{
    if (!node)
    {
        return;
    }
    WorkFlowComponent* comp = static_cast<WorkFlowComponent*>(GetPawn()->GetComponent<WorkFlowComponent>());
    if (comp)
    {
        auto key = std::make_shared<cross::input::CEKey>(Key);
        gbf::reflection::UserObject param = gbf::reflection::make_user_object(key.get(), gbf::reflection::remote_shared_storage_policy{});
        comp->ReceiveDelegate(node, gbf::machine::VValue{param});
    }
}

void PlayerController::ExecDelegateNode(RttiBase* node, const cross::input::CEKey& Key, float x, float y, float z) const
{
    if (!node)
    {
        return;
    }
    WorkFlowComponent* comp = static_cast<WorkFlowComponent*>(GetPawn()->GetComponent<WorkFlowComponent>());
    if (comp)
    {
        auto key = std::make_shared<cross::input::CEKey>(Key);
        gbf::reflection::UserObject param = gbf::reflection::make_user_object(key.get(), gbf::reflection::remote_shared_storage_policy{});
        gbf::logic::BlueprintEventParamList param_list;
        param_list.push_back(gbf::machine::VValue{param});
        param_list.push_back(gbf::machine::VValue{x});
        param_list.push_back(gbf::machine::VValue{y});
        param_list.push_back(gbf::machine::VValue{z});

        comp->ReceiveDelegate(node, param_list);
    }
}

void PlayerController::ExecDelegateNode(RttiBase* node, const cross::InputActionValue& actionValue) const
{
    if (!node)
    {
        return;
    }
    WorkFlowComponent* comp = static_cast<WorkFlowComponent*>(GetPawn()->GetComponent<WorkFlowComponent>());
    if (comp)
    {
        gbf::logic::BlueprintEventParamList param_list;
        param_list.push_back(gbf::machine::VValue{actionValue.Value.x});
        param_list.push_back(gbf::machine::VValue{actionValue.Value.y});
        param_list.push_back(gbf::machine::VValue{actionValue.Value.z});

        comp->ReceiveDelegate(node, param_list);
    }
}

}
