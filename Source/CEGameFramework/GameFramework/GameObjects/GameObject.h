#pragma once
#include "TypeScriptObject.h"
#include "TypeScriptEngine/TypeScriptModule.h"
#include "GameFramework/GameFrameworkTypes.h"
#include "GameFramework/GameFrameworkGlobals.h"
#include "GameFramework/Components/Component.h"
#include "GameFramework/Ticking/TickManager.h"
#include "visual/blueprint/blueprint_define.hpp"

#include "visual/blueprint/details/node/blueprint_action_node.h"

#include "Resource/TsResource.h"
#include "CrossBase/CEMetaMacros.h"
#include "Runtime/Input/Core/InputKeys.h"

namespace cross::scripts
{
  class GameObjectScript;
}

  namespace cegf
{
struct CreateGameObjectParameters;

namespace evt {
    class ScriptListener;
}

class GameWorld;
class GameWorldBlock;

class GameObject;
using GameObjectPtr = std::shared_ptr<GameObject>;

class GameObjectComponent;
using GameObjectComponentPtr = std::shared_ptr<GameObjectComponent>;
using GameObjectComponentList = std::vector<GameObjectComponentPtr>;

class PlayerController;
class ViewTargetInfo;

class GAMEFRAMEWORK_API CEMeta(Cli) Vector_std_string_wrapper
{
public:
    CEMeta(Cli)
    Vector_std_string_wrapper() = default;
    CEMeta(Cli)
    Vector_std_string_wrapper(std::vector<std::string> && other)
        : holder(std::move(other))
    {}

    CEMeta(Cli)
    std::vector<std::string> holder;
};

struct GAMEFRAMEWORK_API CEMeta(Cli) PropertyChangedEvent
{
    CEMeta(Cli) 
    std::string PropertyName;
};

struct BuiltInScriptFuncNames
{
    static inline const char* OnStartGame = "OnStartGame";
    static inline const char* OnEndGame = "OnEndGame";
    static inline const char* OnTick = "OnTick";
};


const std::string EDITOR_SCRIPT_FUNCTION_PREFIX = "Editor_";

struct BuiltInEditorScriptFuncNames
{
    static inline const char* Editor_OnTick = "Editor_OnTick";
};

CEMeta(Reflect)
enum class ScriptVarType : int
{
    eInt = 0,
    eFloat = 1,
    eBoolean,
    eStruct,
    eDouble,
    eString,
    eResource,
    eCount
};
CEMeta(Reflect)
struct ScriptJsonProperty
{
    ScriptJsonProperty(){}
    ScriptJsonProperty(const ScriptJsonProperty & rhs)
    {
        Name = rhs.Name;
        Type = rhs.Type;
        Tips = rhs.Tips;
        mValueNodes = (rhs.mValueNodes.Clone());
    }

    ScriptJsonProperty& operator=(const ScriptJsonProperty & rhs)
    {
        Name = rhs.Name;
        Type = rhs.Type;
        Tips = rhs.Tips;
        mValueNodes = (rhs.mValueNodes.Clone());
        return *this;
    }

    CE_Serialize_Deserialize

    CEProperty(Serialize)
    std::string Name;

    CEProperty(Serialize)
    std::string Type;

    CEProperty(Serialize)
    std::string Tips;

    DeserializeNode mValueNodes;


     CEMeta(AdditionalDeserialize)
    void AdditionalDeserialize(const DeserializeNode& inNode, SerializeContext& context);
    
    //CEProperty(Serialize)
    //std::string DefaultValue;

    //CEProperty(Serialize)
    //std::string Value;

    ScriptVarType GetType() const
    {
        if (Type == "Integer")
            return ScriptVarType::eInt;

        if (Type == "Float")
            return ScriptVarType::eFloat;

        if (Type == "Boolean")
            return ScriptVarType::eBoolean;

        if (Type == "Resource")
            return ScriptVarType::eResource;

        if (Type == "Struct")
            return ScriptVarType::eStruct;

        if (Type == "Double")
            return ScriptVarType::eDouble;

        if (Type == "String")
            return ScriptVarType::eString;

        return ScriptVarType::eCount;
    }
};


class GAMEFRAMEWORK_API CEMeta(Reflect, Puerts, Cli, WorkflowType) GameObject : public ObjectBase
{
public:
    CEGameplayInternal()
    StaticMetaClassName(GameObject)
    static inline const std::string SK_METACLASSTYPE = "MetaClassType";
    static inline const std::string SK_OBJECTNAME = "ObjectName";
    static inline const std::string SK_LOCAL_ROTATION = "LocalRotation";
    static inline const std::string SK_LOCAL_TRANSLATION = "LocalTranslation";
    static inline const std::string SK_LOCAL_SCALE = "LocalScale";
    static inline const std::string SCRIPT_PATH = "ScriptPath";
    static inline const std::string SK_COMPONENTS = "GameComponents";
    static inline const std::string SK_COMPONENTTYPE = "ComponentType";
    static inline const std::string SK_SCRIPTEDITORFIELDS = "ScriptEditorFields";


    static inline const std::string SK_FIELDS = "Fields";
    static inline const std::string SK_FIELDS_NAME = "Name";
    static inline const std::string SK_FIELDS_TYPE = "Type";
    static inline const std::string SK_FIELDS_VALUE = "Value";
    static inline const std::string SK_FIELDS_TIPS = "Tips";
    static inline const std::string SK_FIELDS_DEFAULT_VALUE = "DefaultValue";

      static inline const std::string SK_FIELDS_TYPE_RESOURCE = "Resource";

    GameObject();

    virtual ~GameObject() override;

    //virtual ~GameObject();
    virtual void Tick(float deltaTime);

    virtual void Serialize(SerializeNode & node, SerializeContext & context) const;
    
    virtual bool Deserialize(const DeserializeNode& in, SerializeContext& context);

    virtual void GetReferenceResource(cross::ResourcePtr resource) const;

    virtual void StartGame();

    virtual void EndGame();

    virtual void OnSpawn();

    CEFunction(Cli)
    void SetScriptPath(const std::string& path);

    CEFunction(Cli)
    std::string GetScriptPath();

    CEFunction(Cli)
    const std::string& GetScriptEditorFieldsJson() const;

    CEFunction(Cli)
    void RebindScriptWithEditorFieldsJson(const std::string&);

    virtual void BeginDestroy() override;

    virtual void Destroyed() override;

    CEFunction(Reflect, ScriptCallable)
    GameWorld* GetWorld() const;

    CEFunction(ScriptCallable)
    const std::string& GetName() const override;

    void SetWorldBlock(GameWorldBlock* block) { mBlock = block; }

    void RegisterTickFunction();
    void UnRegisterTickFunction();

    void RegisterScriptTickFunction();
    void UnRegisterScriptTickFunction();

    void RegisterEventListeners();
    void UnRegisterEventListeners();

    GameWorldBlock* GetWorldBlock() const;

    const gbf::logic::UBlueprintGraphGroup* GetBlueprint() const;

    // world transform interface
    CEFunction(ScriptCallable)
    cross::TRSMatrixType GetWorldMatrix() const;
    CEFunction(ScriptCallable, WorkflowExecutable)
    cross::TRSVector3Type GetWorldTranslation() const;
    CEFunction(ScriptCallable, WorkflowExecutable)
    cross::TRSFloat3ReturnType GetTilePosition() const;
    CEFunction(ScriptCallable, WorkflowExecutable)
    cross::TRSQuaternionType GetWorldRotation() const;
    CEFunction(ScriptCallable, WorkflowExecutable)
    cross::TRSVector3Type GetWorldScale() const;
    CEFunction(Reflect, ScriptCallable, WorkflowExecutable)
    void SetWorldTranslation(const cross::TRSVector3Type& translation);
    CEFunction(ScriptCallable, WorkflowExecutable)
    void SetWorldRotation(const cross::TRSQuaternionType& rotation);
    CEFunction(ScriptCallable, WorkflowExecutable)
    void SetWorldScale(const cross::TRSVector3Type& scale);

    // location transform interface
    CEFunction(ScriptCallable, WorkflowExecutable)
    cross::TRSVector3Type GetLocalTranslation() const;
    CEFunction(ScriptCallable, WorkflowExecutable)
    cross::TRSQuaternionType GetLocalRotation() const;
    CEFunction(ScriptCallable, WorkflowExecutable)
    cross::TRSVector3Type GetLocalScale() const;
    CEFunction(ScriptCallable, WorkflowExecutable)
    void SetLocalTranslation(const cross::TRSVector3Type& translation);
    CEFunction(ScriptCallable, WorkflowExecutable)
    void SetLocalRotation(const cross::TRSQuaternionType& rotation);
    CEFunction(ScriptCallable, WorkflowExecutable)
    void SetLocalScale(const cross::TRSVector3Type& scale);

    CEFunction(ScriptCallable)
    GameObject* GetParent() const;

    CEFunction(ScriptCallable)
    GameObject* GetChild(size_t index) const;

    CEFunction(ScriptCallable)
    GameObject* FindChild(const char* childName) const;

    CEFunction(ScriptCallable)
    GameObject* FindChildRecursive(const char* childName) const;

    // Find first object with name in entire world
    CEFunction(ScriptCallable)
    GameObject* FindInWorld(const char* name);

    CEFunction(ScriptCallable)
    size_t GetChildNum() const;

    std::vector<GameObject*> GetChildren() const;

    CEFunction(ScriptCallable, ScriptAsProperty)
    GameObject* GetFirstChild() const;

    CEFunction(ScriptCallable, ScriptAsProperty)
    GameObject* GetLastChild() const;

    CEFunction(ScriptCallable, ScriptAsProperty)
    GameObject* GetNextSiblings() const;

    CEFunction(ScriptCallable, ScriptAsProperty)
    GameObject* GetPreSiblings() const;

    CEFunction(ScriptCallable, WorkflowExecutable)
    GameObject* FindSiblingByName(const std::string& name) const;

    CEFunction(ScriptCallable)
    std::string GetEUID();

    CEFunction(ScriptCallable)
    void Joint(GameObject * Parent);
    CEFunction(ScriptCallable)
    bool GetVisible();
    CEFunction(ScriptCallable)
    void SetVisible(bool visible);
    CEFunction(ScriptCallable)
    void SetVisibleHierarchy(bool visible);
    CEFunction(Cli)
    const cross::ecs::EntityID& GetObjectEntityID() const { return mObjectEntityID; }
   
    void SetObjectEntityID(const cross::ecs::EntityID& entityID) { mObjectEntityID = entityID; }
    
    virtual void PostCreatingInitialize(GameWorld * world, const std::string& name, const cross::TRSVector3AType& localLocation, const cross::TRSQuaternionAType& localRotation, const cross::TRSVector3AType& localScale, const CreateGameObjectParameters& Parameters);

    virtual void FinishCreating();

    virtual void PreInitializeComponents();

    virtual void InitializeComponents();

    virtual void PostInitializeComponents();

    CEFunction(Reflect)
    bool HasStarted() const { return mHasStarted; }

    bool HasInitialized() const { return mHasInitialized; }

    GameObject* GetParent0() const { return mParent; }

    void SetParent(GameObject* parent) { mParent = parent; }
    
    bool HasComponent(GameObjectComponent* comp) const;

    template<typename T>
    bool HasComponent() const;

    CEFunction(ScriptCallable)
    void SetCanEverTick(bool enable);
    CEFunction(ScriptCallable)
    bool GetCanEverTick() const;

    CEFunction(ScriptCallable)
    void SetTickGroup(TickingGroup tickGroup);
    CEFunction(ScriptCallable)
    TickingGroup GetTickGroup() const;

    CEFunction(ScriptCallable)
    void SetCanTickInEditorViewport(bool enable);
    CEFunction(ScriptCallable)
    bool GetCanTickInEditorViewport() const;

    const GameObjectComponentList& GetAllComponents() const { return mOwnedComponents; }
    template<typename T>
    T* GetComponent() const;

    CEMeta(Cli)
    GameObjectComponent* GetComponentByName(const std::string& compName) const;

    CEFunction(ScriptCallable)
    GameObjectComponent* GetComponentByNameScript(const std::string& compName) const;

    CEFunction(Cli, WorkflowExecutable)
    GameObjectComponent* GetComponentByMetaClassName(const std::string& compName) const;

    CEFunction(WorkflowExecutable)
    GameObjectComponent* GetComponentByMetaClass(const gbf::reflection::MetaClass* metaClass) const;

    CEFunction(Cli, ScriptCallable)
    bool CreateComponentByClassName(const std::string& compName);

    CEFunction(Cli, ScriptCallable)
    bool RemoveComponentByClassName(const std::string& compName);

    CEMeta(Cli)
    void SyncGOSerializer();

    GameObjectComponent* GetRootComponent() const
    {
        return mRootComponent;
    }

    void SetRootComponent(GameObjectComponent * comp);

    GameObjectComponent* AddComponent(const std::string& className);

    template<typename T>
    T* AddComponent();

    void RemoveComponent(GameObjectComponent* component);

    void OnDestroyComponent(const cross::ecs::ComponentBitMask& changedMaskBit);

    template<typename T>
    auto GetECSComponent() const
    {
        cross::GameWorld* crossWorld = GetCrossGameWorld();
        return crossWorld->GetComponent<T>(GetObjectEntityID());
    }

    bool IsJointed() const { return mIsJointed; }

    virtual void EndViewTarget(PlayerController* controller);

    virtual void BecomeViewTarget(PlayerController* controller);

    virtual void CalcCamera(float deltaTime, ViewTargetInfo& outTargetInfo);

    CEFunction(ScriptCallable)
    int TestFuncGameObject(int param);

    CEFunction(ScriptImplable)
    int TestGameObjectPuertsImplable(int param);

    CEFunction(ScriptImplable)
    void TestGameObjectPuertsImplableNoReturn(int param);

    CEFunction(WorkflowExecutable)
    int TestFunc(int param);

    CEFunction(WorkflowExecutable)
    int TestFunc2(int param, GameObject* other);

    CEFunction(WorkflowPure)
    int TestFuncPure(int param)
    {
        return 456;
    }

    CEFunction(WorkflowExecutable)
    void PrintKey(cross::input::CEKey key);

public:
    //Editor interfaces
    CEMeta(Cli)
    virtual void PostEditChangeProperty(PropertyChangedEvent & PropertyChangedEvent);

    CEProperty(ScriptReadWrite)
    bool mHasInitialized = false;

    CEProperty(ScriptReadWrite)
    bool mHasStarted = false;

    CEProperty(ScriptReadOnly)
    bool mReadOnlyTest = false;
    auto& GetTsResource() const {return mTsResource;}
    auto& GetTsObject()const 
    {
        return mTSObject;
    }

    void RunConstructionScript();

protected:
    cross::GameWorld* GetCrossGameWorld() const;

    template<typename T>
    T* GetSystem() const
    {
        cross::GameWorld* crossWorld = GetCrossGameWorld();
        return crossWorld->GetGameSystem<T>();
    }

    void BindScriptObject();

    // use latest reflect json to update script bind variables;
    void UpdateLoadedScriptJsonProperty(std::string originalScriptEditorFieldsJson);

protected:
    GameObjectComponentList mOwnedComponents;

    GameObjectTickFunctionPtr mTickFunction;

    TypeScriptTickFunctionPtr mScriptTickFunction;

    CEMeta(Cli)
    cross::ecs::EntityID mObjectEntityID;

    GameWorldBlock* mBlock = nullptr;

    GameObjectComponent* mRootComponent = nullptr;

    GameObject* mParent = nullptr;

    evt::ScriptListener* mScriptListener = nullptr;

    bool mIsJointed = false;
    friend class cross::scripts::GameObjectScript;
    
    cross::TsPtr mTsResource;

    // this is the value from deserialization
    std::string mScriptEditorFieldsJson;

    cross::TypeScriptObject mTSObject;
    friend class GOContext;
};

template<typename T>
bool GameObject::HasComponent() const
{
    return GetComponent<T>() != nullptr;
}

template<typename T>
T* GameObject::GetComponent() const
{
    static_assert(std::is_base_of_v<GameObjectComponent, T>);
    auto metaClass = QueryMetaClass<T>();
    if (metaClass)
    {
        for (auto& comp : mOwnedComponents)
        {
            if (comp->GetMetaClass() == metaClass || comp->GetMetaClass()->SearchBaseClass(metaClass->id()))
            {
                return dynamic_cast<T*>(comp.get());
            }
        }
    }
    return nullptr;
}

template<typename T>
T* GameObject::AddComponent()
{
    GameObjectComponentPtr objComponent = NewObject<T>();
    objComponent->SetName(ObjectBase::MakeUniqueObjectName(QueryMetaClass<T>()));
    objComponent->SetOwner(this);
    objComponent->Register();

    if (mHasInitialized)
    {
        //after game object initialized, add component need to notify ecs
        cross::GameWorld* crossWorld = GetCrossGameWorld();
        cross::ecs::ComponentBitMask newBitMask;
        objComponent->GetRelatedECSComponentBitMask(newBitMask);
        if (!newBitMask.IsZero())
        {
            crossWorld->DispatchEntityCreateEvent(cross::EntityLifeCycleEventFlag::CreateComponent, mObjectEntityID, newBitMask);
            crossWorld->DispatchEntityMoveEvent(cross::EntityLifeCycleEventFlag::MoveComponent, mObjectEntityID);
        }
    }
    mOwnedComponents.emplace_back(objComponent);
    return TYPE_CAST(T*, objComponent.get());
}

} //cegf
