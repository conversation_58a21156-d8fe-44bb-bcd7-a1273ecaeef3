#pragma once
#include "CECommon/Common/SystemEvent.h"

namespace cegf
{

class GameWorld;

class GameWorldEventProcessor : public cross::SystemEventReceiver
{
public:
    GameWorldEventProcessor();

    virtual ~GameWorldEventProcessor();

    void Init(GameWorld* world);

    void Destroyed();

    virtual void NotifyEvent(const cross::SystemEventBase& eventBase, UInt32& flag);

protected:
    void ProcessWorldChangedEvent(const cross::SystemEventBase& eventBase, UInt32& flag);

    void ProcessWorldBlockChangedEvent(const cross::SystemEventBase& eventBase, UInt32& flag);

    void ProcessEntityStatusUpdateEvent(const cross::SystemEventBase& eventBase, UInt32& flag);

    void ProcessEntityCreateEvent(const cross::RemainedEventUpdatedEvent* remainedEvent, UInt32& flag);

    void ProcessEntityDestroyEvent(const cross::RemainedEventUpdatedEvent* remainedEvent, UInt32& flag);

    void ProcessEntityMoveEvent(const cross::RemainedEventUpdatedEvent* remainedEvent, UInt32& flag);

private:
    GameWorld* mGameWorld;
};
}
