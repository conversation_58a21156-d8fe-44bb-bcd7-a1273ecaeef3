#include "GameFramework/Components/CapsuleComponent.h"
#include "Runtime/GameWorld/PhysicsSystemG.h"

namespace cegf
{

static constexpr float kDefaultCapsuleRadius = 20.0f;
static constexpr float kDefaultCapsuleHalfHeight = 40.0f;

CapsuleComponent::CapsuleComponent()
{
    mCapsule = cross::PhysicsGeometryCapsule(cross::Float3::Zero(), cross::Quaternion::Identity(), kDefaultCapsuleRadius, kDefaultCapsuleHalfHeight);
}

CapsuleComponent::~CapsuleComponent()
{
    
}

void CapsuleComponent::Init()
{
    PhysicsComponent::Init();
    cross::PhysicsSystemG* physSys = GetSystem<cross::PhysicsSystemG>();

    if (physSys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        auto* extra_shape = physSys->GetExtraShape(comp.Read());
        if (physSys->GetExtraCapsules(comp.Read()).empty())
        {
            physSys->AddExtraCapsuleShape(comp.Write(), mCapsule);
        }
    }
}

void CapsuleComponent::SetCapsuleSize(float radius, float halfHeight)
{
    mCapsule.radius = radius;
    mCapsule.halfHeight = halfHeight;
    cross::PhysicsSystemG* physSys = GetSystem<cross::PhysicsSystemG>();
    if (physSys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        auto collisions = physSys->GetExtraShape(comp.Read());
        if (collisions == nullptr)
        {
            return;
        }

        for (auto& capsule : collisions->mCapsuleGeometry)
        {
            capsule.radius = radius;
            capsule.halfHeight = halfHeight;
        }
    }
}

float CapsuleComponent::GetCapsuleRadius() const
{
    cross::PhysicsSystemG* physSys = GetSystem<cross::PhysicsSystemG>();
    if (physSys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        auto const& capsules = physSys->GetExtraCapsules(comp.Read());
        if (!capsules.empty())
        {
            return capsules[0].radius;
        }
    }
    return kDefaultCapsuleRadius;
}

float CapsuleComponent::GetCapsuleHalfHeight() const
{
    cross::PhysicsSystemG* physSys = GetSystem<cross::PhysicsSystemG>();
    if (physSys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        auto const& capsules = physSys->GetExtraCapsules(comp.Read());
        if (!capsules.empty())
        {
            return capsules[0].halfHeight;
        }
    }
    return kDefaultCapsuleHalfHeight;
}

}

