#pragma once

#include "GameFramework/Components/Component.h"

namespace cegf
{
class GAMEFRAMEWORK_API CEMeta(Reflect, Cli, WorkflowType, Puerts) SkeltSocketComponent : public GameObjectComponent
{
public:
    CEMeta(Reflect) SkeltSocketComponent() = default;
    CEFunction(Reflect, Cli, ScriptCallable) bool SetSocket(std::string inAttachedBone, cross::Float3 const& inTranslate, cross::Quaternion const& inRotate, cross::Float3 const& inScale);
    CEFunction(Reflect, Cli, ScriptCallable) cross::Float3 GetReltvTranslation();
    CEFunction(Reflect, Cli, ScriptCallable) cross::Quaternion GetReltvRotation();
    CEFunction(Reflect, Cli, ScriptCallable) cross::Float3 GetReltvScale();
    CEFunction(Reflect, <PERSON><PERSON>, ScriptCallable) std::string GetBoneName();
    
    virtual void GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const override;
};
} // namespace cegf
