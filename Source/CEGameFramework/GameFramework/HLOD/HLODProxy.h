#pragma once
#include "GameFramework/GameFrameworkTypes.h"
#include "GameFramework/HLOD/HLODObject.h"
#include "GameFramework/HLOD/HLODComponent.h"

namespace cegf
{

class GAMEFRAMEWORK_API CEMeta(Reflect, Cli, Puerts) HLODProxyObject : public GameObject
{
public:
    CEGameplayInternal()
    StaticMetaClassName(HLODProxyObject)

    static HLODProxyObject* AsHLODProxy(GameObject * object);

    CEMeta(Cli)
    HLODProxyObject();
    virtual ~HLODProxyObject() = default;

    virtual void InitializeComponents() override;

    virtual void PostInitializeComponents() override;

    virtual void StartGame() override;
    virtual void Tick(float deltaTime) override;

    void AddHLODObject(HLODObject * hlodObject);
    void RemoveHLODObject(HLODObject * hlodObject);

    void OnHLODSettingAssetChanged();

    void OnHLODSettingChanged();

    void OnForcedHLODModelChanged();

    int GetHLODLevel(HLODObject* hlodObject) const;

    void AddUpdateTasks(int count);

    void RemoveUpdateTasks(int count);

private:
    std::vector<HLODObject*> mHLODObjects;
    std::vector<HLODObject*> mUninitializedHLODObjects;
    HLODComponent* mHLODComponent;
    mutable std::mutex mMutex;
    std::atomic<int> mWaitingUpdateTasks = 0;
};

} // namespace cegf