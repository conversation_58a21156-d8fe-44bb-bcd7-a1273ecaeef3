#include "GameFramework/HLOD/HLODObject.h"
#include "CECommon/Common/EngineGlobal.h"
#include "Resource/ResourceManager.h"
#include "Resource/HLODResource.h"
#include "Resource/Resource.h"
#include "Runtime/GameWorld/AABBSystemG.h"
#include "Runtime/GameWorld/CameraSystemG.h"
#include "Runtime/GameWorld/ModelSystemG.h"
#include "Runtime/GameWorld/RenderPropertySystemG.h"
#include "Runtime/GameWorld/PrimitiveRenderSystemG.h"
#include "GameFramework/GameWorld.h"
#include "GameFramework/HLOD/HLODProxy.h"

namespace cegf
{

HLODObject* HLODObject::AsHLODObject(GameObject* object)
{
    return dynamic_cast<HLODObject*>(object);
}

HLODObject::HLODObject()
{
    // Enable tick in editor
    //mTickFunction->bCanEverTick = true;
    //mTickFunction->bCanTickInEditorViewport = true;
}

bool HLODObject::Deserialize(const DeserializeNode& in, SerializeContext& context)
{
    GameObject::Deserialize(in, context);

    return true;
}

void HLODObject::InitializeComponents()
{
    GameObject::InitializeComponents();
    mModelComponent = AddComponent<ModelComponent>();
}

void HLODObject::PostInitializeComponents()
{
    GameObject::PostInitializeComponents();

    // Ensure model asset
    auto* modelSys = GetSystem<cross::ModelSystemG>();
    auto modelComp = GetECSComponent<cross::ModelComponentG>();
    Assert(modelComp.IsValid());
    const auto& asset = modelSys->GetModelAsset(modelComp.Read(), 0);

    // If model asset is not existed, use a default cube model
    if (!asset)
    {
        modelSys->SetModelAssetPath(modelComp.Write(), "EngineResource/Model/Cube.nda");
    }

    // NOTE: GetParent() is not guranteed to be valid in this stage, move related logic to update stage
}

void HLODObject::BeginDestroy()
{
    // FIXME: This logic will always be inaccessible
    // because `Disjoint` is called before `BeginDestroy` so that `GetParent` will always return nullptr
    // UPDATE: Store a parent pointer here so that we can unregister self from HLODProxyObject
    // All we have to do is to ensure removal of HLODObjects happens between ticks of HLODProxyObject
    if (auto parent = mParent.lock())
    {
        auto proxy = std::dynamic_pointer_cast<HLODProxyObject>(parent);
        if (proxy)
        {
            proxy->RemoveHLODObject(this);
        }
    }

    GameObject::BeginDestroy();
}

void HLODObject::Tick(float deltaTime)
{
    QUICK_SCOPED_CPU_TIMING("HLODObject::Tick");

    GameObject::Tick(deltaTime);

    //Update();
}

void HLODObject::Update(bool fullUpdate, bool forceHide)
{
    QUICK_SCOPED_CPU_TIMING("HLODObject::Update");

    if (!GetCrossGameWorld()->IsEntityAlive(GetObjectEntityID()))
    {
        return;
    }

    // Get parent
    if (mParent.expired())
    {
        mParent = std::dynamic_pointer_cast<GameObject>(GetParent()->shared_from_this());
    }

    // Only do updates if top-level HLOD proxy exists
    auto* proxy = GetProxy();
    if (!proxy)
    {
        return;
    }

    // Update HLOD level if it is invalid
    if (LODLevel == -1)
    {
        LODLevel = proxy->GetHLODLevel(this);
    }
    
    // Perform full update if hierarchy is changed
    if (GetChildNum() != mSubObjects.size())
    {
        ClearSubObjects();
        for (const auto& child : GetChildren())
        {
            AddSubObject(child);

            auto subobject = AsHLODObject(child);

            if (subobject)
            {
                subobject->SetTransitionScreenSize(mTransitionScreenSize);
            }
        }
        fullUpdate = true;
    }

    // Update visibility if parent is invisible
    bool visible = false;
    if (!forceHide)
    {
        if (mForcedLODLevel != -1)
        {
            visible = (mForcedLODLevel == LODLevel);
        }
        else
        {
            float currentDistance = GetCurrentDrawDistance();
            visible = (currentDistance >= mDrawDistance);
        }
    }

    // Perform full update if current visibility is changed
    if (visible != GetObjectVisibility(this))
    {
        mVisible = visible;
        fullUpdate = true;
    }

    // Update children visibility in async threads if needed
    {
        std::scoped_lock lock(mMutex);

        SetObjectVisibility(this, visible);

        if (!visible || fullUpdate)
        {
            QUICK_SCOPED_CPU_TIMING("HLODObject::Dispatch");

            proxy->AddUpdateTasks(GetChildNum());

            // Dispatch immediately
            for (const auto& child : GetChildren())
            {
                // Dispatch immediately
                cross::threading::Dispatch([=](auto) {
                    const auto& subObject = child;
                    auto* hlodSubObject = AsHLODObject(subObject);
                    if (hlodSubObject)
                    {
                        hlodSubObject->Update(fullUpdate, visible || forceHide);
                    }
                    else if (IsValidForUpdate(subObject))
                    {
                        SetObjectVisibility(subObject, !visible && !forceHide);
                    }

                    proxy->RemoveUpdateTasks(1);
                });
            }
        }
    }

    // Debug feature
    if (DebugBoundingSphere)
    {
        DrawDebugBoundingSphere();
    }
}

int HLODObject::GetLODLevel() const
{
    return LODLevel;
}

void HLODObject::AddSubObject(GameObject* object)
{
    std::scoped_lock lock(mMutex);

    mSubObjects.emplace_back(object);
}

void HLODObject::RemoveSubObject(GameObject* object)
{
    std::scoped_lock lock(mMutex);

    for (auto it = mSubObjects.begin(); it != mSubObjects.end();)
    {
        const auto& subObject = *it;
        if (object == subObject)
        {
            mSubObjects.erase(it);
            break;
        }
        else
        {
            it++;
        }
    }
}

void HLODObject::ClearSubObjects()
{
    std::scoped_lock lock(mMutex);

    mSubObjects.clear();
}

bool HLODObject::HasSubObjects() const
{
    return !mSubObjects.empty();
}

void HLODObject::SetForcedLODLevel(int value)
{
    mForcedLODLevel = value;
    for (const auto& subObject : GetChildren())
    {
        auto* subHlodObject = AsHLODObject(subObject);
        if (subHlodObject)
        {
            subHlodObject->SetForcedLODLevel(value);
        }
    }
}

float HLODObject::GetDrawDistanceWithOverride() const
{
    return mDrawDistance;
}

float HLODObject::CalculateDrawDistance(GameObject* object, float screenSize)
{
    auto* aabbSys = GetSystem<cross::AABBSystemG>();
    auto aabbComp = object->GetECSComponent<cross::AABBComponentG>();
    const auto& worldAABB = aabbSys->GetWorldAABB(aabbComp.Read());

    auto* camera = GetWorld()->GetMainCamera();
    auto* cameraSys = GetSystem<cross::CameraSystemG>();
    auto cameraComp = camera->GetECSComponent<cross::CameraComponentG>();
    const auto& projectionMatrix = cameraSys->GetProjMatrix(cameraComp.Read());

    cross::BoundingSphere boundingSphere;
    cross::BoundingSphere::CreateFromBoundingBox(boundingSphere, worldAABB);

    // Get projection multiple accounting for view scaling
    float screenMultiple = std::max(0.5f * projectionMatrix.m00, 0.5f * projectionMatrix.m11);

    // Screen size is the projected diameter, so halve it
    float screenRadius = std::max(1e-8f, screenSize * 0.5f);

    return (screenMultiple * boundingSphere.GetRadius()) / screenRadius;
}

void HLODObject::SetTransitionScreenSize(float screenSize)
{
    mTransitionScreenSize = screenSize;
    float drawDistance = CalculateDrawDistance(this, screenSize);
    SetDrawDistance(drawDistance);

    for (const auto& subObject : GetChildren())
    {
        auto* hlodSubObject = AsHLODObject(subObject);
        if (hlodSubObject)
        {
            hlodSubObject->SetTransitionScreenSize(screenSize);
        }
    }
}

void HLODObject::SetCullingDistance(GameObject* object, float min, float max)
{
    auto* modelSys = GetSystem<cross::ModelSystemG>();
    auto comp = object->GetECSComponent<cross::ModelComponentG>();

    cross::EntityDistanceCulling distanceCulling;
    distanceCulling.minCullingDistance = min;
    distanceCulling.maxCullingDistance = max;
    modelSys->SetModelEnityDistanceCulling(comp.Write(), distanceCulling);
}

float HLODObject::GetCurrentDrawDistance()
{
    auto* aabbSys = GetSystem<cross::AABBSystemG>();
    auto aabbComp = GetECSComponent<cross::AABBComponentG>();
    const auto& worldAABB = aabbSys->GetWorldAABB(aabbComp.Read());

    auto* cameraSys = GetSystem<cross::CameraSystemG>();
    auto cameraComp = GetWorld()->GetMainCamera()->GetECSComponent<cross::CameraComponentG>();
    const auto& cameraView = cameraSys->GetCameraView(cameraComp.Read());

    cross::Float3 cameraWorldPos{cameraView.mInvertViewMatrix.m30, cameraView.mInvertViewMatrix.m31, cameraView.mInvertViewMatrix.m32};
    cameraWorldPos += cameraView.mCameraTilePosition * LENGTH_PER_TILE_F;
    cross::Float3 distance = worldAABB.GetCenter() - cameraWorldPos;

    return distance.Length();
}

float HLODObject::GetCurrentScreenSize()
{
    auto* aabbSys = GetSystem<cross::AABBSystemG>();
    auto aabbComp = GetECSComponent<cross::AABBComponentG>();
    const auto& worldAABB = aabbSys->GetWorldAABB(aabbComp.Read());

    auto* cameraSys = GetSystem<cross::CameraSystemG>();
    auto cameraComp = GetWorld()->GetMainCamera()->GetECSComponent<cross::CameraComponentG>();
    const auto& cameraView = cameraSys->GetCameraView(cameraComp.Read());
    auto projectionMode = cameraSys->GetProjectionMode(cameraComp.Read());

    cross::Float3 center = worldAABB.GetCenter();
    center -= cameraView.mCameraTilePosition * LENGTH_PER_TILE_F;

    cross::Float3 extent = worldAABB.GetExtent();

    cross::Float2 outSize;
    outSize = cameraSys->GetProjectScreenSize(cameraComp.Read(), center, extent);

    return outSize.y;
}

bool HLODObject::GetObjectVisibility(GameObject* object) const
{
    if (object == this)
    {
        return mVisible;
    }

    auto* modelSys = GetSystem<cross::ModelSystemG>();
    auto modelComp = object->GetECSComponent<cross::ModelComponentG>();

    return modelSys->IsModelVisible(modelComp.Read(), 0);
}

void HLODObject::SetObjectVisibility(GameObject* object, bool visibility)
{
    auto* modelSys = GetSystem<cross::ModelSystemG>();
    auto modelComp = object->GetECSComponent<cross::ModelComponentG>();

    for (UInt32 modelIndex = 0; modelIndex < modelSys->GetModelCount(modelComp.Read()); modelIndex++)
    {
        modelSys->SetModelVisibility(modelComp.Write(), visibility, modelIndex);
    }
}

void HLODObject::SetDrawDistance(float distance)
{
    std::scoped_lock lock(mMutex);

    mDrawDistance = distance;
    //SetCullingDistance(this, distance, 0.0f, true);

    for (const auto& subObject : GetChildren())
    {
        auto* subHLODObject = AsHLODObject(subObject);
        if (subHLODObject)
        {
            subHLODObject->SetDrawDistance(distance);
        }
    }
}

void HLODObject::SetOverrideMaterial(const std::string& materialPath)
{
    std::scoped_lock lock(mMutex);

    auto* modelSys = GetSystem<cross::ModelSystemG>();
    auto modelComp = GetECSComponent<cross::ModelComponentG>();

    for (UInt32 modelIndex = 0; modelIndex < modelSys->GetModelCount(modelComp.Read()); modelIndex++)
    {
        for (UInt32 lodIndex = 0; lodIndex < modelSys->GetLODCount(modelComp.Read(), modelIndex); lodIndex++)
        {
            for (UInt32 subModelIndex = 0; subModelIndex < modelSys->GetSubModelCount(modelComp.Read(), modelIndex, lodIndex); subModelIndex++)
            {
                modelSys->SetModelMaterialPath(modelComp.Write(), materialPath, subModelIndex, lodIndex, modelIndex);
            }
        }
    }

    for (const auto& subObject : GetChildren())
    {
        auto* subHLODObject = AsHLODObject(subObject);
        if (subHLODObject)
        {
            subHLODObject->SetOverrideMaterial(materialPath);
        }
        else if (IsValidForUpdate(subObject))
        {
            auto subObjectModelComp = subObject->GetECSComponent<cross::ModelComponentG>();
            if (!subObjectModelComp.IsValid())
            {
                continue;
            }

            for (UInt32 modelIndex = 0; modelIndex < modelSys->GetModelCount(subObjectModelComp.Read()); modelIndex++)
            {
                for (UInt32 lodIndex = 0; lodIndex < modelSys->GetLODCount(subObjectModelComp.Read(), modelIndex); lodIndex++)
                {
                    for (UInt32 subModelIndex = 0; subModelIndex < modelSys->GetSubModelCount(subObjectModelComp.Read(), modelIndex, lodIndex); subModelIndex++)
                    {
                        modelSys->SetModelMaterialPath(subObjectModelComp.Write(), materialPath, subModelIndex, lodIndex, modelIndex);
                    }
                }
            }
        }
    }
}

void HLODObject::DrawDebugBoundingSphere()
{
    auto* aabbSys = GetSystem<cross::AABBSystemG>();
    auto aabbComp = GetECSComponent<cross::AABBComponentG>();
    const auto& worldAABB = aabbSys->GetWorldAABB(aabbComp.Read());

    auto* primitiveRenderSys = GetSystem<cross::PrimitiveRenderSystemG>();
    cross::PrimitiveData data;

    cross::BoundingSphere boundingSphere;
    cross::BoundingSphere::CreateFromBoundingBox(boundingSphere, worldAABB);
    cross::PrimitiveGenerator::GenerateBoundingSphereFrame(&data, boundingSphere.GetRadius(), 32, boundingSphere.GetCenter());
    primitiveRenderSys->DrawPrimitive(&data);
}

HLODProxyObject* HLODObject::GetProxy() const
{
    auto* proxy = HLODProxyObject::AsHLODProxy(mParent.lock().get());
    if (proxy)
    {
        return proxy;
    }

    auto* hlodParent = HLODObject::AsHLODObject(mParent.lock().get());
    if (hlodParent)
    {
        return hlodParent->GetProxy();
    }

    return nullptr;
}

bool HLODObject::IsValidForUpdate(GameObject* object) const
{
    return object && GetCrossGameWorld()->IsEntityAlive(object->GetObjectEntityID()) && object->HasComponent<ModelComponent>();
}

} // namespace cegf