#pragma once

#include "InputChord.h"
#include "visual/blueprint/details/node/event/blueprint_meta_event_node.h"
#include "visual/blueprint/blueprint_define.hpp"

#include "visual/blueprint/details/node/blueprint_event_node.h"

#include "visual/virtual_machine/runtime/serialize/ivmstreamreadnode.h"
#include "visual/virtual_machine/runtime/serialize/ivmstreamwriter.h"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"
#include "GameFrameworkTypes.h"

namespace cegf {

class GAMEFRAMEWORK_API CEMeta(Reflect) BlueprintInputActionEventNode : public gbf::logic::UBlueprintMetaEventNode
{
public:
    CEMeta(Reflect)
    BlueprintInputActionEventNode(){};

    virtual void set_event_name(const std::string& event_name) override;
    virtual void get_editor_fields(std::vector<std::pair<std::string, gbf::machine::VValue>> & result) override;
    virtual bool update_editor_field(const std::string& field_name, const gbf::machine::VValue& field_value) override;

    virtual const gbf::reflection::MetaClass* GetDynamicBindingClass() const override;

    virtual void RegisterDynamicBinding(DynamicBlueprintBinding * BindingObject) const override;

protected:
    virtual void update_title() override;

    void SerializeToJson(gbf::machine::IVMStreamWriter & writer) override;
    void DeserializeFields(gbf::machine::IVMStreamReadNode & node) override;

    std::string InputActionName = "";
    std::string TriggerEventState = "Triggered";    //default trigger type 
    bool bConsumeInput{false};
    bool bExecuteWhenPaused{false};
    bool bOverrideParentBinding{false};
};

}   // namespace cegf
