#include "GameWorld.h"
#include "Runtime/Interface/CrossEngineImp.h"
#include "Runtime/GameWorld/EntityMetaSystem.h"
#include "Runtime/GameWorld/CameraSystemG.h"
#include "Runtime/GameWorld/WorldPartition/WorldLoadingSystemG.h"
#include "Processors/GameWorldEventProcessor.h"
#include "GameFramework/GameObjects/Controller.h"
#include "GameFramework/GameObjects/PlayerController.h"
#include "GameBlock.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/SettingsManager.h"
#include "CrossBase/Threading/TaskSystem.h"
#include "GameObjects/GameObjectIterator.h"

namespace cegf
{

namespace
{
    bool CheckNotInEditor(GameWorld const* world)
    {
        return cross::EngineGlobal::GetSettingMgr()->GetAppStartUpType() != cross::AppStartUpTypeCrossEditor || world->GetWorldType() == cross::WorldTypeTag::PIEWorld;
    }
}

GameWorld::GameWorld()
{
    
}
    
GameWorld::~GameWorld()
{
    mPreWorldBlock.reset();
    mWorldBlocks.clear();
    {
        std::unique_lock lock(mCreatingObjectsMutex);
        mRuntimeCreatingObjects.clear();
    }
    mControllerList.clear();
    mPlayerControllerList.clear();
    mCrossGameWorld = nullptr;
    mEventProcessor = nullptr;

    if (mTickTaskLevel)
    {
        delete mTickTaskLevel;
        mTickTaskLevel = nullptr;
    }
}

void GameWorld::Init(cross::GameWorld* crossWorld)
{
    mCrossGameWorld = crossWorld;
    mWorldRuntimeID = crossWorld->GetRuntimeID();
    mEventProcessor = std::make_shared<GameWorldEventProcessor>();
    mEventProcessor->Init(this);

    Assert(mTickTaskLevel == nullptr);
    mTickTaskLevel = new TickTaskLevel();   // TickTaskManager::Get()::AllocateTickTaskLevel();

    CreateEngineEventDispatchers();
}

void GameWorld::BeginDestroy()
{
    for (auto& [_, block] : mWorldBlocks)
    {
        block->BeginDestroy();
    }
    ObjectBase::BeginDestroy();
}

void GameWorld::Destroyed()
{
    if (mHasStarted)
    {
        EndGame();
    }

    assert(mIsPendingDestroy);
    mEventProcessor->Destroyed();
   

    //destroy all blocks
    for (auto& [_, block] : mWorldBlocks)
    {
        block->Destroyed();
    }
    mPreWorldBlock.reset();
    mWorldBlocks.clear();
    
    ObjectBase::Destroyed();
}

void GameWorld::OnBeginFrame(float deltaTime)
{
    
    // finish some process after postDeserialization
    // call onAwake
    // call start game if game is started
    // we use a temporary set to avoid potential deadlock.
    // In the case of create new Gameobject in the onAwake or OnStartGame, this newly create object would start in next frame
    // additional effeort like complex spawnParameters or loop while empty check may ease the potential problem
    std::unique_lock lock(mAwakeObjectMutex);
    do
    {
        GameObjectSet objectsCreatesInThisFrame = std::move(mNewlyCreateObjects);
        lock.unlock();

        for (auto& itr : objectsCreatesInThisFrame)
        {
            itr->FinishCreating();
        }

        for (auto& itr : objectsCreatesInThisFrame)
        {
            const bool IsStarted = HasStarted();
            if (IsStarted)
            {
                itr->StartGame();
            }
        }

        lock.lock();
    } while (!mNewlyCreateObjects.empty());

    
    if (mCrossGameWorld->GetWorldType() == cross::WorldTypeTag::PreviewWorld || mCrossGameWorld->GetWorldType() == cross::WorldTypeTag::PrefabWorld || mCrossGameWorld->GetWorldType() == cross::WorldTypeTag::ThumbnailWorld)
    {
        return;
    }

}

static bool IsValidTickWorldType(cross::WorldTypeTag tag)
{
    return !(tag == cross::WorldTypeTag::PreviewWorld || tag == cross::WorldTypeTag::PrefabWorld || tag == cross::WorldTypeTag::ThumbnailWorld);
}

void GameWorld::PreTick(float deltaTime)
{
    if(!IsValidTickWorldType(mCrossGameWorld->GetWorldType()))
    {
        return;
    }
}

void GameWorld::Tick(float deltaTime)
{
    //tick all levels ?
    //LOG_DEBUG("GameWorld::Tick --- {}", deltaTime);

    if(!IsValidTickWorldType(mCrossGameWorld->GetWorldType()))
    {
        return;
    }

    LevelTick tick_type = mLevelTick;
    if (cross::EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeCrossEditor && mCrossGameWorld->GetWorldType() == cross::WorldTypeTag::DefaultWorld)
    {
        tick_type = LevelTick::LEVELTICK_ViewportsOnly;
    }

    TickTaskManager::Get().StartFrame(this, deltaTime, tick_type);

    TickTaskManager::Get().RunTickGroup(TickingGroup::TG_PreTick);
    TickTaskManager::Get().RunTickGroup(TickingGroup::TG_0);
    TickTaskManager::Get().RunTickGroup(TickingGroup::TG_1);
    TickTaskManager::Get().RunTickGroup(TickingGroup::TG_2);
    TickTaskManager::Get().RunTickGroup(TickingGroup::TG_3);
    TickTaskManager::Get().RunTickGroup(TickingGroup::TG_4);
    TickTaskManager::Get().RunTickGroup(TickingGroup::TG_5);
    TickTaskManager::Get().RunTickGroup(TG_PostTick);
}

void GameWorld::PostTick(float deltaTime)
{
    if(!IsValidTickWorldType(mCrossGameWorld->GetWorldType()))
    {
        return;
    }
    
}

void GameWorld::StartGame()
{
    mHasStarted = true;
    for (auto& [_, block] : mWorldBlocks)
    {
        block->StartGame();
    }

    RuntimeCreatedGOStartGame();
}

void GameWorld::EndGame()
{
    for (auto& [_, block] : mWorldBlocks)
    {
        block->EndGame();
    }

    UnInitEventDispatchers();
    
    RuntimeCreatedGOEndGame();
}

void GameWorld::OnLevelBlockLoaded(UInt64 blockId)
{
    if (IsPendingDestroy())
    {
        return;
    }

    if (mPreWorldBlock && (mPreWorldBlock->GetBlockId() == blockId))
    {
        mPreWorldBlock->Loaded();
        return;
    }
    
    cross::WorldLoadingSystemG* loadingSys = mCrossGameWorld->GetGameSystem<cross::WorldLoadingSystemG>();
    cross::WorldPartitionBlockGrid* blockGrid = loadingSys->GetBlockGrid();

    //create custom world block
    if (blockGrid->GetCustomBlocks().contains(blockId))
    {
        GameWorldBlockPtr customBlock = CreateWorldBlock(blockId, GameWorldBlockType::CustomBlock);
        customBlock->Loaded();
        return;
    }
}

void GameWorld::OnLevelBlockUnloaded(UInt64 blockId)
{
    auto iter = mWorldBlocks.find(blockId);
    if (iter != mWorldBlocks.end())
    {
        GameWorldBlockPtr block = iter->second;
        block->Unloaded();
        
        if (block->IsPreBlock())
        {
            mPreWorldBlock.reset();
        }
        mWorldBlocks.erase(iter);
    }
    else
    {
        LOG_DEBUG("OnLevelBlockUnloaded world {} can not find block by {}", GetName().GetString(), blockId);
    }
}

void GameWorld::OnWorldConfigLoaded()
{
    assert(!IsPendingDestroy());
    cross::WorldLoadingSystemG* loadingSys = mCrossGameWorld->GetGameSystem<cross::WorldLoadingSystemG>();
    cross::WorldPartitionBlockGrid* blockGrid = loadingSys ? loadingSys->GetBlockGrid() : nullptr;
    if (blockGrid)
    {
        //create pre game block
        mPreWorldBlock = CreateWorldBlock(blockGrid->GetPreBlock()->GetBlockUintId(), GameWorldBlockType::PreBlock);
    }
}

void GameWorld::OnWorldLoaded()
{
    assert(!IsPendingDestroy());
    LOG_DEBUG("GameWorld {} loaded world type {}", GetName().GetString(), GetWorldType());
    if ((GetWorldType() == cross::WorldTypeTag::PIEWorld || cross::EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpTypeStandAlone) && IsLoaded())
    {
        //runtime world loaded
        StartGame();
    }
}

void GameWorld::OnCreateGameObject(const cross::ecs::EntityID& entityID, const cross::ecs::ComponentBitMask& compBitMask)
{   
    if (mCrossGameWorld->IsLoaded())
    {
        std::shared_lock lock(mCreatingObjectsMutex);
        auto iter = mRuntimeCreatingObjects.find(entityID);
        if (iter != mRuntimeCreatingObjects.end())
        {
            // create game object triggered by GameFramework
            //iter->second->FinishCreating();
            LOG_WARN("Create A GameObject that already existed {}", entityID.GetValue());
        }
        else
        {
            // create entity triggered by ecs
        }
    }
    else
    {
        // load entity or create entity runtime, whether create game object associated entity, according to entity meta component
    }
}

void GameWorld::OnChangeBelongedBlock(const std::vector<cross::ecs::EntityID>& entities, UInt64 sourceBlockId, UInt64 targetBlockId)
{
    if (targetBlockId == 0)
    {
        return;
    }

    auto iterTarget = mWorldBlocks.find(targetBlockId);
    if (iterTarget == mWorldBlocks.end())
    {
        LOG_ERROR("OnChangeBelongedBlock can not find target block by {}", targetBlockId);
        return;
    }
    
    assert(sourceBlockId != targetBlockId);
    GameWorldBlockPtr targetBlock = iterTarget->second;
    GameWorldBlockPtr srcBlock;
    if (sourceBlockId != 0)
    {
        auto srcIter = mWorldBlocks.find(sourceBlockId);
        if (srcIter == mWorldBlocks.end())
        {
            LOG_ERROR("OnChangeBelongedBlock can not find srouce block by {}", sourceBlockId);
            return;
        }
        srcBlock = srcIter->second;
    }

    for (auto& entity : entities)
    {
        if (srcBlock)
        {
            srcBlock->ChangeBelongedBlock(entity, targetBlock.get());
        }
        else
        {
            // get creating GameObject
            std::unique_lock lock(mCreatingObjectsMutex);
            auto entityIter = mRuntimeCreatingObjects.find(entity);
            if (entityIter != mRuntimeCreatingObjects.end())
            {
                GameObjectPtr gameObj = entityIter->second;
                targetBlock->SetBelongedBlock(gameObj);
                //gameObj->FinishCreating();
                {
                    mRuntimeCreatingObjects.erase(entityIter);
                }
            }
        }
    }
}

void GameWorld::RuntimeCreatedGOStartGame()
{
    Assert(cross::threading::TaskSystem::IsInGameThread());
    // make sure it's in game thread
    //std::shared_lock lock(mCreatingObjectsMutex);
    //for (auto&& itr = mRuntimeCreatingObjects.begin(); itr != mRuntimeCreatingObjects.end();itr ++)
    //{
    //    itr->second->StartGame();
    //}
    
    for (GameObjectIterator It(this); It; ++It)
    {
        It->StartGame();
    }
}

void GameWorld::RuntimeCreatedGOEndGame()
{
    Assert(cross::threading::TaskSystem::IsInGameThread());
    // make sure it's in game thread
    //std::shared_lock lock(mCreatingObjectsMutex);
    //for (auto&& itr = mRuntimeCreatingObjects.begin(); itr != mRuntimeCreatingObjects.end(); itr++)
    //{
    //    itr->second->EndGame();
    //}

    for (GameObjectIterator It(this); It; ++It)
    {
        It->EndGame();
    }
}

GameWorldBlockPtr GameWorld::CreateWorldBlock(UInt64 blockId, GameWorldBlockType blockType)
{
    GameWorldBlockPtr block = NewObject<GameWorldBlock>(blockId, blockType, this);
    mWorldBlocks.emplace(blockId, block);
    return block;
}

GameWorldBlock* GameWorld::GetWorldBlock(UInt64 blockId) const
{
    auto iter = mWorldBlocks.find(blockId);
    if (iter != mWorldBlocks.end())
    {
        return iter->second.get();
    }
    return nullptr;
}

GameObject* GameWorld::FindGameObject(const std::string& name) const
{
    {
        std::shared_lock lock(mCreatingObjectsMutex);
        auto it = std::find_if(mRuntimeCreatingObjects.begin(), mRuntimeCreatingObjects.end(), [&](const auto& pair) {
            return pair.second->GetName() == name;
        });
        if (it != mRuntimeCreatingObjects.end())
            return it->second.get();
    }

    // find in block

    for (auto& [_, block] : mWorldBlocks)
    {
        if (auto result = block->FindGameObject(name); result)
        {
            return result;
        }
    }

    return nullptr;
}

GameObject* GameWorld::GetGameObject(const cross::ecs::EntityID& entityId) const
{
    if (entityId == cross::ecs::EntityID::InvalidHandle())
    {
        return nullptr;
    }

    if (!mCrossGameWorld->IsEntityAlive(entityId))
    {
        return nullptr;
    }

    {
        std::shared_lock lock(mCreatingObjectsMutex);
        auto itr = mRuntimeCreatingObjects.find(entityId);
        if (itr != mRuntimeCreatingObjects.end())
        {
            return itr->second.get();
        }
    }


    auto metaComp = mCrossGameWorld->GetComponent<cross::ecs::EntityMetaComponentG>(entityId);
    auto metaSys = mCrossGameWorld->GetGameSystem<cross::EntityMetaSystem>();
    auto blockInst = metaSys->GetBelongedBlockInstance(metaComp.Read());
    if (blockInst)
    {
        UInt64 blockId = blockInst->GetBlockUintId();
        GameWorldBlock* worldBlock = GetWorldBlock(blockId);
        if (worldBlock)
        {
            return worldBlock->GetGameObject(entityId);
        }
    }
    return nullptr;
}

GameObject* GameWorld::GetGameObjectByUUID(const std::string& entity_uuid) const
{
    cross::ecs::EntityID entity = mCrossGameWorld->GetEntity(cross::EUID(entity_uuid));
    return GetGameObject(entity);
}

GameObject* GameWorld::GetRootGameObject() const
{
    auto* tranSys = mCrossGameWorld->GetGameSystem<cross::TransformSystemG>();
    return GetGameObject(tranSys->GetRootEntity());
}
GameObjectPtr GameWorld::CreateGameObject(
    const std::string& className,
    const cross::TRSVector3AType& localLocation,
    const cross::TRSQuaternionAType& localRotation,
    const cross::TRSVector3AType& localScale,
    const CreateGameObjectParameters& Parameters /*= CreateGameObjectParameters()*/)
{
    if (className.empty())
    {
        LOG_ERROR("create game object failed, empty class name");
        return nullptr;
    }
    return CreateGameObject(QueryMetaClass(className), localLocation, localRotation, localScale, Parameters);
}

GameObjectPtr GameWorld::CreateGameObject(
    const std::string& className,
    const cross::TRSVector3AType* localLocation /*= nullptr*/,
    const cross::TRSQuaternionAType* localRotation /*= nullptr*/,
    const cross::TRSVector3AType* localScale /*= nullptr*/,
    const CreateGameObjectParameters& Parameters /*= CreateGameObjectParameters()*/)
{   
    return CreateGameObject(className, *localLocation, *localRotation, *localScale, Parameters);
}

GameObject* GameWorld::CreateGameObject(const std::string& name, GameObject* parent, const cross::TRSVector3Type& localLocation, const cross::TRSQuaternionType& localRotation, const cross::TRSVector3Type& localScale)
{
    CreateGameObjectParameters params;
    params.mGameObjectName = name;
    params.mParent = parent;
    return CreateGameObject<cegf::GameObject>(localLocation, localRotation, localScale, params).get();
}

GameObject* GameWorld::CreateGameObjectByMetaClassName(const std::string& name, const std::string& metaClassName, GameObject* parent, const cross::TRSVector3Type& localLocation, const cross::TRSQuaternionType& localRotation, const cross::TRSVector3Type& localScale)
{
    CreateGameObjectParameters params;
    params.mGameObjectName = name;
    params.mParent = parent;
    return CreateGameObject(metaClassName, localLocation, localRotation, localScale, params).get();
}

GameObjectPtr GameWorld::CreateGameObject(
    const ObjectMetaClass* metaClass,
    const cross::TRSVector3AType& localLocation,
    const cross::TRSQuaternionAType& localRotation,
    const cross::TRSVector3AType& localScale,
    const CreateGameObjectParameters& Parameters /*= CreateGameObjectParameters()*/)
{
    if (IsPendingDestroy())
    {
        LOG_ERROR("create game object failed, game world is pending destroy");
        return nullptr;
    }

    if (metaClass == nullptr)
    {
        LOG_ERROR("create game object failed, object meta class is nullptr");
        return nullptr;
    }

    // check parent is valid
    if (Parameters.mParent)
    {
        const cross::ecs::EntityID& parentEntityID = Parameters.mParent->GetObjectEntityID();
        if (!parentEntityID)
        {
            LOG_ERROR("create game object failed, invalid parent entity id {}", parentEntityID.GetValue());
            return nullptr;
        }

        GameWorld* parentWorld = Parameters.mParent->GetWorld();
        if (parentWorld != this)
        {
            LOG_ERROR("create game object failed, parent world {} is not current world {}", parentWorld->GetRuntimeID(), mWorldRuntimeID);
            return nullptr;
        }
    }

    if (auto itr = mRuntimeCreatingObjects.find(Parameters.mLocalEntity); itr != mRuntimeCreatingObjects.end())
    {
        LOG_ERROR("GameObject already created for Entity: {}!", Parameters.mLocalEntity);
        return itr->second;
    }

    // game object name
    std::string newObjectName = Parameters.mGameObjectName;
    if (newObjectName.empty())
    {
        newObjectName = MakeUniqueObjectName(metaClass);
    }

    // new object
    GameObjectPtr gameObj = TYPE_CAST_SHARD_PTR(GameObject, NewObject(metaClass));
    if (!gameObj)
    {
        LOG_ERROR("create game object failed, new object failed {}", metaClass->name());
        return nullptr;
    } 

    if (Parameters.mLocalEntity != cross::ecs::EntityID::InvalidHandle())
    {
        gameObj->SetObjectEntityID(Parameters.mLocalEntity);
        gameObj->SyncGOSerializer();
    }

    gameObj->PostCreatingInitialize(this, newObjectName, localLocation, localRotation, localScale, Parameters);

    if (gameObj->IsJointed())
    {
        std::lock_guard lock(mCreatingObjectsMutex);
        mRuntimeCreatingObjects[gameObj->GetObjectEntityID()] = gameObj;
    }

    if (Parameters.OnGameObjectCreated != nullptr)
    {
        Parameters.OnGameObjectCreated(gameObj.get(), this);
    }

    {
        std::unique_lock lock(mAwakeObjectMutex);
        mNewlyCreateObjects.insert(gameObj);
    }

    return gameObj;
}

struct MarkComponentEndOfFrameDrawState
{
    static void Set(GameObjectComponent* component, ComponentMarkedForEndOfFrameDrawState drawState)
    {
        component->mMarkedForEndOfFrameDrawState = drawState;
    }
};

void GameWorld::EndOfFrameDraw() const
{
    std::vector<GameObjectComponent*> deferredDraw;
    bool notInEditor = CheckNotInEditor(this);
    deferredDraw.reserve(mComponentsNeedEndOfFrameDraw.size() + notInEditor ? 0 : mComponentsNeedEndOfFrameDrawOnlyOnEditor.size());

    for (auto* component : mComponentsNeedEndOfFrameDraw)
    {
        if (component && component->IsRegistered())
        {
            deferredDraw.emplace_back(component);
        }
    }

    if (!notInEditor)
    {
        for (auto* component : mComponentsNeedEndOfFrameDrawOnlyOnEditor)
        {
            if (component && component->IsRegistered())
            {
                deferredDraw.emplace_back(component);
            }
        }
    }

    for (auto* component : deferredDraw)
    {
        component->DoDeferredDraw();
    }
}

bool GameWorld::HasEndOfFrameDraw() const
{
    return mComponentsNeedEndOfFrameDraw.size() > 0 || mComponentsNeedEndOfFrameDrawOnlyOnEditor.size() > 0;
}

void GameWorld::MarkComponentForNeededEndOfFrameDraw(GameObjectComponent* component)
{
    auto currentState = component->GetMarkedForEndOfFrameDrawState();

    if (currentState == ComponentMarkedForEndOfFrameDrawState::Unmarked)
    {
        if (component->IsDrawOnlyOnEditor())
        {
            MarkComponentEndOfFrameDrawState::Set(component, ComponentMarkedForEndOfFrameDrawState::MarkedOnlyOnEditor);
            mComponentsNeedEndOfFrameDrawOnlyOnEditor.emplace(component);
        }
        else
        {
            MarkComponentEndOfFrameDrawState::Set(component, ComponentMarkedForEndOfFrameDrawState::Marked);
            mComponentsNeedEndOfFrameDraw.emplace(component);
        }
    }
}

void GameWorld::RegisterEventListener(evt::Listener* inListener)
{
    for (auto dispatcher : mEventDispatchers)
    {
        dispatcher->RegisterListener(inListener);
    }
}

void GameWorld::UnregisterEventListener(evt::Listener* inListener)
{
    for (auto dispatcher : mEventDispatchers)
    {
        dispatcher->UnregisterListener(inListener);
    }
}

bool GameWorld::AddEventDispatcher(evt::GOEventDispatcherPtr inDispatcher)
{
    if (inDispatcher == nullptr)
    {
        LOG_ERROR("AddEventDispatcher: Dispatcher is nullptr");
        return false;
    }

    if (inDispatcher->GetGameWorld() == nullptr)
    {
        LOG_ERROR("AddEventDispatcher: Dispatcher->GameWorld is nullptr");
        return false;
    }

    if (inDispatcher->GetGameWorld()->GetRuntimeID() != mCrossGameWorld->GetRuntimeID())
    {
        LOG_ERROR("AddEventDispatcher: Dispatcher->GameWorld is nullptr");
        return false;
    }

    //if ((GetWorldType() == cross::WorldTypeTag::PIEWorld || cross::EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpTypeStandAlone))
    {
        // runtime world loaded
        inDispatcher->Init();
    }

    mEventDispatchers.push_back(std::move(inDispatcher));
    return true;
}

void GameWorld::InitEventDispatchers()
{
    for (auto dispatcher : mEventDispatchers)
    {
        dispatcher->Init();
    }
}

void GameWorld::UnInitEventDispatchers()
{
    for (auto dispatcher : mEventDispatchers)
    {
        dispatcher->UnInit();
    }
}

void GameWorld::CreateEngineEventDispatchers()
{
    mEventDispatchers.clear();
    mEventDispatchers.reserve(10);

    AddEventDispatcher(std::make_shared<evt::TransformParentJointEventDispatcher>(mCrossGameWorld));
    AddEventDispatcher(std::make_shared<evt::TransformChildrenJointEventDispatcher>(mCrossGameWorld));
}


void GameWorld::MarkComponentForNoNeedEndOfFrameDraw(GameObjectComponent* component)
{
    auto currentState = component->GetMarkedForEndOfFrameDrawState();

    if (currentState == ComponentMarkedForEndOfFrameDrawState::MarkedOnlyOnEditor)
    {
        MarkComponentEndOfFrameDrawState::Set(component, ComponentMarkedForEndOfFrameDrawState::Unmarked);
        mComponentsNeedEndOfFrameDrawOnlyOnEditor.erase(component);
    }
    else if (currentState == ComponentMarkedForEndOfFrameDrawState::Marked)
    {
        MarkComponentEndOfFrameDrawState::Set(component, ComponentMarkedForEndOfFrameDrawState::Unmarked);
        mComponentsNeedEndOfFrameDraw.erase(component);
    }
}

GameObject* GameWorld::GetMainCamera() const {
    auto* camera = mCrossGameWorld->GetGameSystem<cross::CameraSystemG>();
    return GetGameObject(camera->GetMainCamera());
}

    
AudioManager const * GameWorld::GetAudioManager() const {
    return &mAudioManager;
}

bool GameWorld::DestroyGameObject(GameObject* gameObj)
{
    assert(gameObj);

    //check whether game object is in this world
    GameWorld* gameWorld = gameObj->GetWorld();
    if (gameWorld == nullptr || gameWorld != this)
    {
        LOG_ERROR("destroy game object failed, game object {} doesn't have a valid game world pointer", gameObj->GetName());
        return false;
    }

    //if already on list to be deleted, pretend the call was successful
    if (gameObj->IsPendingDestroy())
    {
        return true;
    }

    //tell this game object
    gameObj->BeginDestroy();

    //tell ecs destroy entity
    const cross::ecs::EntityID& gameObjEntityID = gameObj->GetObjectEntityID();
    assert(gameObjEntityID);
    mCrossGameWorld->DestroyEntity(gameObjEntityID);
    return true;
}

void GameWorld::OnDestroyGameObject(const cross::ecs::EntityID& entityID, UInt64 blockId)
{
    //find related game object by entity id
    //if (blockId == 0)
    //{
    //    return;
    //}

    auto TryDestroyGameObject = [](GameObject* obj) {
        if (obj == nullptr)
        {
            // entity id not related GameObject
            return;
        }

        if (obj->IsPendingDestroy())
        {
            // destroy game object triggered be game play
            obj->Destroyed();
        }
        else
        {
            // destroy game object triggered by engine internals
            obj->BeginDestroy();
            obj->Destroyed();
        }
    };

    auto iter = blockId == 0 ? mWorldBlocks.begin() : mWorldBlocks.find(blockId);

    if (iter != mWorldBlocks.end())
    {
        GameObject* obj = iter->second->GetGameObject(entityID);
        TryDestroyGameObject(obj);

        iter->second->RemoveGameObject(entityID);
    }

    auto* obj = this->GetGameObject(entityID);
    if (obj)
    {
        TryDestroyGameObject(obj);
        {
            std::lock_guard lock(mCreatingObjectsMutex);
            auto runtimeIter = mRuntimeCreatingObjects.find(entityID);
            if (runtimeIter != mRuntimeCreatingObjects.end())
            {
                mRuntimeCreatingObjects.erase(runtimeIter);
            }
        }
    }
}

void GameWorld::OnDestroyComponent(const cross::ecs::EntityID& entityID, const cross::ecs::ComponentBitMask& changedComponentMask)
{
    GameObject* obj = GetGameObject(entityID);
    if (obj == nullptr || obj->IsPendingDestroy())
    {
        return;
    }
    obj->OnDestroyComponent(changedComponentMask);
}

void GameWorld::AddController(Controller* controller)
{
    assert(controller);
    mControllerList.emplace_back(controller);
    if (controller->IsPlayerController())
    {
        mPlayerControllerList.emplace_back(TYPE_CAST(PlayerController*, controller));
    }
}

void GameWorld::RemoveController(Controller* controller)
{
    assert(controller);
    auto iter = std::find_if(mControllerList.begin(), mControllerList.end(), [&](const Controller* tempController) { return tempController == controller; });
    if (iter == mControllerList.end())
    {
        return;
    }
    mControllerList.erase(iter);

    if (controller->IsPlayerController())
    {
        auto playerIter = std::find_if(mPlayerControllerList.begin(), mPlayerControllerList.end(), [&](const PlayerController* tempController) { return tempController == controller; });
        if (playerIter != mPlayerControllerList.end())
        {
            mPlayerControllerList.erase(playerIter);
        }
    }
}

bool GameWorld::Save(const char* savePath) const
{
   return GetCrossGameWorld()->SaveWorld(savePath);
}

GameObject* GameWorld::CreateRootGameObject() const
{
    const auto transSys = GetCrossGameWorld()->GetGameSystem<cross::TransformSystemG>();
    transSys->CreateRootEntity();
    const auto rootEntity = transSys->GetRootEntity();
    return GetGameObject(rootEntity);
}

}   // namespace cegf
