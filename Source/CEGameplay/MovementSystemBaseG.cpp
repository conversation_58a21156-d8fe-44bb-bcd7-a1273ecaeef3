#include "EnginePrefix.h"
#include "MovementSystemBaseG.h"
#include "ECS/Develop/Framework/Types.h"
#include "CECommon/Common/EngineGlobal.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/TransformSystemG.h"

namespace cross {
/////////////////////////////////////////////
// Member Funcs
//
/////////////////////////////////////////////
const PhysicsGeometryBase* MovementSystemBaseG::GetPhysicsGeometry(ecs::EntityID inEntity) const 
{
    return GetPhysicsGeometry_DefaultImpl(mGameWorld, inEntity);
}

void MovementSystemBaseG::HandleImpact(ecs::EntityID inEntity, const HitResult& hit, float posRatio, const Float3& moveDelta) {}

Float3 MovementSystemBaseG::ComputeSlideVector(ecs::EntityID inEntity, const Float3& inDelta, float posRatio, const Float3& normal, const HitResult& hit) const 
{
    return inDelta.ProjectOnPlane(normal) * posRatio;
}

float MovementSystemBaseG::SlideAlongSurface(ecs::EntityID inEntity, const Float3& inDelta, float posRatio, const Float3& normal, HitResult& outHitResult, bool handleImpact)
{
    if (!outHitResult.bBlockingHit)
    {
        return 0.f;
    }

    auto transformSys = mGameWorld->GetGameSystem<TransformSystemG>();
    auto worldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(inEntity);

    float posRatioApplied = 0.f;

    Float3 slideDelta = ComputeSlideVector(inEntity, inDelta, posRatio, normal, outHitResult);
    if (slideDelta.Dot(inDelta) > 0.f)
    {
        const Quaternion rot = transformSys->GetWorldRotation(worldTransComp.Read());
        SafeMovement(inEntity, slideDelta, rot, true, &outHitResult);

        const float firstHitRatio = outHitResult.PositionRatio;
        posRatioApplied = firstHitRatio;
        if (outHitResult.IsValidBlockingHit())
        {
            // Process first impact
            if (handleImpact)
            {
                HandleImpact(inEntity, outHitResult, firstHitRatio * posRatio, slideDelta);
            }

            // Compute new slide normal when hitting multiple surfaces.
            TwoSurfaceAdjust(inEntity, slideDelta, outHitResult, normal);

            // Only proceed if the new direction is of significant length and not in reverse of original attempted move.
            if (!slideDelta.IsNearlyZero() && slideDelta.Dot(inDelta) > 0.f)
            {
                // Perform second move
                SafeMovement(inEntity, slideDelta, rot, true, &outHitResult);
                const float secondHitRatio = outHitResult.PositionRatio * (1.f - firstHitRatio);
                posRatioApplied += secondHitRatio;

                // Process second impact
                if (handleImpact && outHitResult.bBlockingHit)
                {
                    HandleImpact(inEntity, outHitResult, secondHitRatio * posRatio, slideDelta);
                }
            }
        }

        MathUtils::Clamp(posRatioApplied, 0.f, 1.f);
    }

    return 0.f;
}

void MovementSystemBaseG::TwoSurfaceAdjust(ecs::EntityID inEntity, Float3& outDelta, const HitResult& hit, const Float3& oldHitNormal) const
{
    Float3 delta = outDelta;
    const Float3 hitNormal = hit.Normal;

    // 90 or less corner, so use cross product for direction
    if (oldHitNormal.Dot(hitNormal) <= 0.f)
    {
        const Float3 desiredDir = delta;
        Float3 newDir = hitNormal.Cross(oldHitNormal);
        newDir = newDir.SafeNormal();

        delta = delta.Dot(newDir) * (1.f - hit.PositionRatio) * newDir;
        if (desiredDir.Dot(delta) < 0.f)
        {
            delta = -1.f * delta;
        }
    }
    // Adjust to new surface
    else
    {
        const Float3 desiredDir = delta;
        delta = ComputeSlideVector(inEntity, delta, 1.f - hit.PositionRatio, hitNormal, hit);
        if (delta.Dot(desiredDir) <= 0.f)
        {
            delta = Float3::Zero();
        }
        else if (MathUtils::Abs(hitNormal.Dot(oldHitNormal) - 1.f) < MOVEMENT_SMALL_NUMBER)
        {
            // we hit the same wall again even after adjusting to move along it the first time
            // nudge away from it (this can happen due to precision issues)
            delta += hitNormal * 0.01f;
        }
    }

    outDelta = delta;
}

bool MovementSystemBaseG::SafeMovement(ecs::EntityID inEntity, const Float3& inDelta, const Quaternion& newRotation, bool useSweep, HitResult* outHit, bool drawDebug)
{
    return SafeMovement_DefaultImpl(mGameWorld, inEntity, inDelta, newRotation, useSweep, outHit, drawDebug);
}

Float3 MovementSystemBaseG::GetPenetrationPullOut(ecs::EntityID inEntity, const HitResult& hit)
{
    return GetPenetrationPullOut_DefaultImpl(mGameWorld, inEntity, hit);
}

bool MovementSystemBaseG::ResolvePenetration(ecs::EntityID inEntity, const HitResult& hit, const Float3& pullOut, const Quaternion& newRotation)
{
    return ResolvePenetration_DefaultImpl(mGameWorld, inEntity, hit, pullOut, newRotation);
}

bool MovementSystemBaseG::MoveImpl(ecs::EntityID inEntity, const Float3& inDelta, const Quaternion& newRotation, bool useSweep, HitResult* outHit, bool drawDebug)
{
    return MoveEntity_DefaultImpl(mGameWorld, inEntity, inDelta, newRotation, useSweep, outHit, drawDebug);
}

/////////////////////////////////////////////
// Static Member Funcs
//
/////////////////////////////////////////////
bool MovementSystemBaseG::SafeMovement_DefaultImpl(GameWorld* inWorld, ecs::EntityID inEntity, const Float3& inDelta, const Quaternion& newRotation, bool useSweep, HitResult* outHit, bool drawDebug)
{
    bool moved = MoveEntity_DefaultImpl(inWorld, inEntity, inDelta, newRotation, useSweep, outHit, drawDebug);

    // Handle initial penetration
    if (outHit->bStartPenetrating)
    {
        Float3 pullOut = GetPenetrationPullOut_DefaultImpl(inWorld, inEntity, *outHit);
        if (ResolvePenetration_DefaultImpl(inWorld, inEntity, *outHit, pullOut, newRotation))
        {
            // Retry original move
            moved = MoveEntity_DefaultImpl(inWorld, inEntity, inDelta, newRotation, useSweep, outHit, drawDebug);
        }
    }

    return moved;
}

Float3 MovementSystemBaseG::GetPenetrationPullOut_DefaultImpl(GameWorld* inWorld, ecs::EntityID inEntity, const HitResult& hit)
{
    static float sPenetrationPullbackDistance = 0.125f;

    if (!hit.bStartPenetrating)
    {
        return Float3::Zero();
    }

    const float penetrationDepth = (hit.PenetrationDepth > 0.f) ? hit.PenetrationDepth : sPenetrationPullbackDistance;

    return hit.Normal * (penetrationDepth + sPenetrationPullbackDistance);
}

bool MovementSystemBaseG::ResolvePenetration_DefaultImpl(GameWorld* inWorld, ecs::EntityID inEntity, const HitResult& hit, const Float3& pullOut, const Quaternion& newRotation)
{
    if (!pullOut.IsNearlyZero())
    {
        // We really want to make sure that precision differences or differences between the overlap test and sweep tests don't put us into another overlap,
        // so make the overlap test a bit more restrictive.
        auto phyGeo = const_cast<PhysicsGeometryBase*>(GetPhysicsGeometry_DefaultImpl(inWorld, inEntity));
        Assert(phyGeo != nullptr);

        auto phyGeoWorldTransform = WorldCollisionUtility::GetPhysicsGeometryWorldTransform(inWorld, inEntity, phyGeo);
        phyGeoWorldTransform.SetTranslation(phyGeoWorldTransform.GetTranslation() + pullOut);

        bool hasOverlap = WorldCollision::OverlapTest(inWorld, phyGeo, phyGeoWorldTransform, CollisionChannelBit::BlockAll);
        if (!hasOverlap)
        {
            // Move without sweeping.
            MoveEntity_DefaultImpl(inWorld, inEntity, pullOut, newRotation, false, nullptr);
            return true;
        }
        else
        {
            // Attempt to pull out from penetration
            HitResult pullOutResult;
            bool moved = MoveEntity_DefaultImpl(inWorld, inEntity, pullOut, newRotation, true, &pullOutResult);

            // Still in penetration
            if (!moved && pullOutResult.bStartPenetrating)
            {
                // Combine two MTD results to get a new direction that gets out of multiple surfaces.
                const Float3 secondMTD = GetPenetrationPullOut_DefaultImpl(inWorld, inEntity, pullOutResult);
                const Float3 combinedMTD = pullOut + secondMTD;
                if (secondMTD != pullOut && !combinedMTD.IsNearlyZero())
                {
                    moved = MoveEntity_DefaultImpl(inWorld, inEntity, combinedMTD, newRotation, true, nullptr);
                }
            }

            // Still in penetration
            if (!moved)
            {
                // Try moving the pullOut plus the attempted move direction. This can sometimes get out of penetrations with multiple objects
                Float3 moveDelta = hit.EndPos - hit.StartPos;
                if (!moveDelta.IsNearlyZero())
                {
                    moved = MoveEntity_DefaultImpl(inWorld, inEntity, pullOut + moveDelta, newRotation, true, nullptr);

                    // Finally, try the original move without MTD adjustments, but allowing depenetration along the MTD normal.
                    if (!moved && pullOut.Dot(moveDelta) > 0.f)
                    {
                        moved = MoveEntity_DefaultImpl(inWorld, inEntity, moveDelta, newRotation, true, nullptr);
                    }
                }
            }

            return moved;
        }
    }

    return false;
}

const PhysicsGeometryBase* MovementSystemBaseG::GetPhysicsGeometry_DefaultImpl(GameWorld* inWorld, ecs::EntityID inEntity)
{
    auto geos = WorldCollisionUtility::GetAllPhysicsGeometries(inWorld, inEntity);
    if (!geos.empty())
    {
        return geos.front();
    }
    return nullptr;
}

bool MovementSystemBaseG::MoveEntity_DefaultImpl(GameWorld* inWorld, ecs::EntityID inEntity, const Float3& inDelta, const Quaternion& newRotation, bool useSweep, HitResult* outHit, bool drawDebug)
{
    auto transformSys = inWorld->GetGameSystem<TransformSystemG>();
    auto worldTransComp = inWorld->GetComponent<WorldTransformComponentG>(inEntity);
    auto* physicsSys = inWorld->GetGameSystem<PhysicsSystemG>();
    auto physicsComponent = inWorld->GetComponent<PhysicsComponentG>(inEntity);
    const Float3 entityStartPos = transformSys->GetWorldTranslation(worldTransComp.Read());
    const Float3 entityEndPos = entityStartPos + inDelta;
    const Quaternion entityInitRotation = transformSys->GetWorldRotation(worldTransComp.Read());
    
    const auto phyGeo = GetPhysicsGeometry_DefaultImpl(inWorld, inEntity);
    const auto phyGeoWorldTransform = WorldCollisionUtility::GetPhysicsGeometryWorldTransform(inWorld, inEntity, phyGeo);
    const Float3 phyGeoStartPos = phyGeoWorldTransform.GetTranslation();
    const Float3 phyGeoEndPos = phyGeoStartPos + inDelta;

    float moveLenSq = inDelta.LengthSquared();
    // SweepMulti does nothing if moveLen < MOVEMENT_SMALL_NUMBER in distance, so it's important to not try to sweep distances smaller than that.
    const float minMovementLenSq = (useSweep ? MathUtils::Square(4.0f * MOVEMENT_SMALL_NUMBER) : 0.f);
    if (moveLenSq <= minMovementLenSq)
    {
        // Skip if no vector or rotation.
        if (Quaternion::IsEqual(entityInitRotation, newRotation))
        {
            if (outHit)
            {
                outHit->Init(phyGeoStartPos, phyGeoEndPos);
            }
            return true;
        }
        moveLenSq = 0.0f;
    }

    HitResult blockHitResult;
    blockHitResult.bBlockingHit = false;
    blockHitResult.PositionRatio = 1.0f;

    bool findHitResult = false;
    bool moved = false;

    static std::vector<HitResult> sTmpHits;

    auto PullBackHit = [&](HitResult& hit, float distance) {
        const float desiredTimeBack = MathUtils::Clamp(0.1f, 0.1f / distance, 1.f / distance) + 0.001f;
        hit.PositionRatio = MathUtils::Clamp(hit.PositionRatio - desiredTimeBack, 0.f, 1.f);
    };

    auto MovedOrNot = [&](const Float3& newPos, const Quaternion& newRot) -> bool 
    {
        auto oldPos = transformSys->GetWorldTranslation(worldTransComp.Read());
        auto oldRot = transformSys->GetWorldRotation(worldTransComp.Read());

        if ((newPos - oldPos).IsNearlyZero() && Quaternion::IsEqual(oldRot, newRot))
        {
            return false;
        }

        transformSys->SetWorldTranslation(worldTransComp.Write(), newPos);
        transformSys->SetWorldRotation(worldTransComp.Write(), newRot);
        return true;
    };

    // Not sweeping, just go directly to the new transform
    if (!useSweep)
    {
        // Set world position and world rotation
        moved = MovedOrNot(entityEndPos, newRotation);
    }
    else
    {
        Float3 desiredPos = entityStartPos;

        if (moveLenSq > 0.f)
        {
            sTmpHits.clear();
            bool haveBlockHit = WorldCollision::SweepMulti(inWorld, inEntity, inDelta, sTmpHits, physicsSys->GetCollisionMask(physicsComponent.Read()), true, drawDebug);

            // Pull back hit pos slightly
            if (!sTmpHits.empty())
            {
                float moveLen = std::sqrt(moveLenSq);
                for (auto& hit : sTmpHits)
                {
                    PullBackHit(hit, moveLen);
                }
            }

            // If we had a valid blocking hit, store it.
            if (haveBlockHit)
            {
                SInt32 blockHitIndex = -1;
                float blockHitNormalDotDelta = std::numeric_limits<float>::max();
                for (SInt32 hitIdx = 0; hitIdx < sTmpHits.size(); ++hitIdx)
                {
                    const HitResult& curHit = sTmpHits[hitIdx];
                    auto phyComp = inWorld->GetComponent<PhysicsComponentG>(curHit.HitEntity);
                    if (physicsSys->GetIsDynamic(phyComp.Read()) && !physicsSys->GetIsKinematic(phyComp.Read()))
                    {
                        continue;
                    }
                    if (curHit.bBlockingHit)
                    {
                        if (curHit.bStartPenetrating)
                        {
                            // We may have multiple initial hits, and want to choose the one with the normal most opposed to our movement.
                            const float normalDotDelta = curHit.ImpactNormal.Dot(inDelta);
                            if (normalDotDelta < blockHitNormalDotDelta)
                            {
                                blockHitNormalDotDelta = normalDotDelta;
                                blockHitIndex = hitIdx;
                            }
                        }
                        else if (blockHitIndex == -1)
                        {
                            // First non-overlapping blocking hit should be used, if an overlapping hit was not.
                            // This should be the only non-overlapping blocking hit, and last in the results.
                            blockHitIndex = hitIdx;
                            break;
                        }
                    }
                }

                // Update blocking hit, if there was a valid one.
                if (blockHitIndex >= 0)
                {
                    blockHitResult = sTmpHits[blockHitIndex];
                    findHitResult = true;
                }
            }

            if (!blockHitResult.bBlockingHit)
            {
                desiredPos = entityEndPos;
            }
            else
            {
                Assert(findHitResult);

                desiredPos = entityStartPos + blockHitResult.PositionRatio * inDelta;

                // Re-check validation
                Float3 toDesiredPos = desiredPos - entityStartPos;
                if (toDesiredPos.LengthSquared() <= minMovementLenSq)
                {
                    // We don't want really small movements to put us on or inside a surface.
                    desiredPos = entityStartPos;
                    blockHitResult.PositionRatio = 0.f;
                }
            }
        }

        // Set world position and world rotation
        moved = MovedOrNot(desiredPos, newRotation);
    }

    if (outHit)
    {
        if (findHitResult)
        {
            *outHit = blockHitResult;
        }
        else
        {
            outHit->Init(phyGeoStartPos, phyGeoEndPos);
        }
    }

    // Return whether we have moved
    return moved;
}

}