#include "EnginePrefix.h"
#include "WorldCollision.h"
#include "Runtime/GameWorld/TransformSystemG.h"
#include "Runtime/GameWorld/PrimitiveRenderSystemG.h"
#include "Runtime/GameWorld/ModelSystemG.h"
#include "RenderEngine/PrimitiveGenerator.h"

namespace cross {

/*
    TODO(hydrochen):
    The scene query methods in WorldCollision is repetitive with PhysicsQuery,
    and the xxxInternal implement always use more expensive multiple query.
    Remove WorldCollsion!!!
*/
bool WorldCollision::SweepMulti(GameWorld* inWorld, ecs::EntityID inEntity, const Float3& inDelta, std::vector<HitResult>& outHitResults, CollisionChannelBit inCollisionChannel, const PhysicsActor* self, bool drawDebug)
{
    outHitResults.clear();

    if (inEntity == ecs::EntityID::InvalidHandle())
    {
        LOG_ERROR("No Valid Entity");
        return false;
    }
    auto transformSystem = inWorld->GetGameSystem<TransformSystemG>();
    auto phySystem = inWorld->GetGameSystem<PhysicsSystemG>();

    if (phySystem == nullptr)
    {
        LOG_ERROR("No Valid PhysicsSystemG");
        return false;
    }

    bool haveBlockHit = false;
    auto phyShapes = WorldCollisionUtility::GetAllPhysicsGeometries(inWorld, inEntity);
    if (!phyShapes.empty())
    {
        auto worldTransComp = inWorld->GetComponent<WorldTransformComponentG>(inEntity);
        TRSMatrixType GlobalStartTransformMatrix = transformSystem->GetWorldMatrixT(worldTransComp.Read());
 
        std::vector<HitResult> curHitResults;
        for (const auto shape : phyShapes)
        {
            Assert(shape != nullptr);
            // Skip complex geometry
            if (shape->GetGeometryType() == PhysicsGeometryBase::Type::Plane || shape->GetGeometryType() == PhysicsGeometryBase::Type::Mesh || shape->GetGeometryType() == PhysicsGeometryBase::Type::Convex)
            {
                continue;
            }

            TRS shapeTransform = WorldCollisionUtility::GetPhysicsGeometryLocalTransform(shape);
            TRSMatrixType localStartTransformMatrix = TRSMatrixType::Compose(shapeTransform.mScale, shapeTransform.mRotation, shapeTransform.mTranslation);
            (localStartTransformMatrix * GlobalStartTransformMatrix).Decompose(shapeTransform.mScale, shapeTransform.mRotation, shapeTransform.mTranslation);

            curHitResults.clear();
            if (SweepInternal(phySystem, const_cast<PhysicsGeometryBase*>(shape), shapeTransform, inDelta, curHitResults, DefaultHitFlag(), inCollisionChannel, self))
            {
                haveBlockHit = true;
            }

            outHitResults.insert(outHitResults.end(), curHitResults.begin(), curHitResults.end());
            std::sort(outHitResults.begin(), outHitResults.end());
        }

         if (drawDebug)
        {
             for (const auto& hit : outHitResults)
             {
                 WorldCollisionUtility::VisualizeHitResult(inWorld, hit);
             }
         }
    }
    return haveBlockHit;
}

bool WorldCollision::SweepMulti(GameWorld* inWorld, PhysicsGeometryBase* geometry, const TRS& globalStartTransform, 
    const Float3& inDelta, std::vector<HitResult>& outHitResults, HitFlag hitFlag, CollisionChannelBit inCollisionChannel, bool drawDebug)
{
    auto physicsSys = inWorld->GetGameSystem<PhysicsSystemG>();
    bool blockHit = SweepInternal(physicsSys, geometry, globalStartTransform, inDelta, outHitResults, hitFlag, inCollisionChannel);
    if (blockHit)
    {
        if (drawDebug)
        {
            for (const auto& hit : outHitResults)
            {
                WorldCollisionUtility::VisualizeHitResult(inWorld, hit);
            }
        }

        std::sort(outHitResults.begin(), outHitResults.end());
    }

    return blockHit;
}

bool WorldCollision::SweepSingle(GameWorld* inWorld, PhysicsGeometryBase* geometry, const TRS& globalStartTransform, 
    const Float3& inDelta, HitResult& outHitResult, HitFlag hitFlag, CollisionChannelBit inCollisionChannel,const PhysicsActor* self, bool drawDebug)
{
    auto physicsSys = inWorld->GetGameSystem<cross::PhysicsSystemG>();

    std::vector<HitResult> results;
    bool blockHit = SweepInternal(physicsSys, geometry, globalStartTransform, inDelta, results, hitFlag, inCollisionChannel, self);
    if (blockHit)
    {
        if (drawDebug)
        {
            for (const auto& hit : results)
            {
                WorldCollisionUtility::VisualizeHitResult(inWorld, hit);
            }
        }

        std::sort(results.begin(), results.end());

        outHitResult = results.front();
    }

    return blockHit;
}

bool WorldCollision::RayCastSingle(GameWorld* inWorld, const TRSVector3Type& startPos, const Float3& inDelta, HitResult& outHitResult, HitFlag hitFlag, CollisionChannelBit inCollisionChannel, const PhysicsActor* self, bool drawDebug)
{
    auto physicsSys = inWorld->GetGameSystem<cross::PhysicsSystemG>();

    //if (drawDebug)
    //{
    //    WorldCollisionUtility::VisualizeRayCast(inWorld, startPos, inDelta);
    //}

    std::vector<HitResult> results;
    bool blockHit = RayCastInternal(physicsSys, startPos, inDelta, results, hitFlag, inCollisionChannel, self);
    if (blockHit)
    {
        if (drawDebug)
        {
            for (const auto& hit : results)
            {
                WorldCollisionUtility::VisualizeHitResult(inWorld, hit);
            }
        }

        std::sort(results.begin(), results.end());

        outHitResult = results.front();
    }

    return blockHit;
}

bool WorldCollision::OverlapTest(GameWorld* inWorld, PhysicsGeometryBase* geometry, const TRS& globalTransform, CollisionChannelBit inCollisionChannel) 
{
    constexpr static UInt32 sMaxHit = 5;

    auto physicsSys = inWorld->GetGameSystem<cross::PhysicsSystemG>();
    std::array<cross::PhysicsHitResult, sMaxHit> result;

    const UInt32 hitNum = physicsSys->Overlap(
        geometry, 
        globalTransform.mTranslation, 
        globalTransform.mRotation, 
        globalTransform.mScale,
        inCollisionChannel,
        sMaxHit,
        nullptr,
        result.data());

    return hitNum > 0U;
}

bool WorldCollision::SweepInternal(PhysicsSystemG* physicsSys, PhysicsGeometryBase* geometry, const TRS& globalStartTransform, const Float3& inDelta, std::vector<HitResult>& outHitResults, HitFlag hitFlag,
                                   CollisionChannelBit inCollisionChannel, const PhysicsActor* self)
{
    constexpr static UInt32 sMaxHit = 5;

    std::array<cross::PhysicsHitResult, sMaxHit> result;
    const UInt32 hitNum =
        physicsSys->Sweep(
        geometry, 
        globalStartTransform.mTranslation, 
        globalStartTransform.mRotation, 
        globalStartTransform.mScale, 
        inDelta.Normalized(), 
        inDelta.Length(), 
        inCollisionChannel, 
        hitFlag, 
        sMaxHit, self, result.data());

    bool haveBlockHit = false;
    if (hitNum > 0)
    {
        for (UInt32 i = 0; i < hitNum; ++i) 
        {
            HitResult curHitResult;
            if (ConvertTraceResult(result[i], hitFlag, curHitResult, globalStartTransform, inDelta))
            {
                haveBlockHit = true;
                outHitResults.emplace_back(std::move(curHitResult));
            }
        }
    }

    return haveBlockHit;
}

bool WorldCollision::RayCastInternal(PhysicsSystemG* physicsSys, const TRSVector3Type& startPos, const Float3& inDelta, std::vector<HitResult>& outHitResults, HitFlag hitFlag, CollisionChannelBit inCollisionChannel, const PhysicsActor* self)
{
    constexpr static UInt32 sMaxHit = 5;

    std::array<cross::PhysicsHitResult, sMaxHit> result;
    const UInt32 hitNum = physicsSys->RayCast(startPos, inDelta.Normalized(), inDelta.Length(), inCollisionChannel, hitFlag, sMaxHit, self, result.data());

    TRS globalStartTransform;
    globalStartTransform.mTranslation = startPos;

    bool haveBlockHit = false;
    if (hitNum > 0)
    {
        for (UInt32 i = 0; i < hitNum; ++i)
        {
            HitResult curHitResult;
            if (ConvertTraceResult(result[i], hitFlag, curHitResult, globalStartTransform, inDelta))
            {
                haveBlockHit = true;
                outHitResults.emplace_back(std::move(curHitResult));
            }
        }
    }

    return haveBlockHit;
}

bool WorldCollision::ConvertTraceResult(const PhysicsHitResult& hit, HitFlag hitFlag, HitResult& outHitResult, const TRS& globalStartTransform, const Float3& delta)
{
    const PhysicsActor* hitActor = hit.hitActor;
    const PhysicsShape* hitShape = hit.hitShape;
    const bool initialOverlap = (hit.distance <= 0.f);

    if (hitShape == nullptr || hitActor == nullptr)
    {
        outHitResult.Init();
        return false;
    }

    outHitResult.HitEntity = hitActor->GetCustomData<CustomPhyData>()->entityId;

    if (initialOverlap)
    {
        ConvertOverlappedResult(hit, hitFlag, outHitResult, globalStartTransform, delta);
        return true;
    }

    outHitResult.bBlockingHit = true;
    outHitResult.bStartPenetrating = initialOverlap;
    outHitResult.PositionRatio = hit.distance / delta.Length();
    outHitResult.Distance = hit.distance;
    outHitResult.Location = globalStartTransform.mTranslation + TRSVector3Type(outHitResult.PositionRatio * delta);


    if (EnumHasAnyFlags(hitFlag, HitFlag::Position) && !initialOverlap)
    {
        #ifdef CE_USE_DOUBLE_TRANSFORM 
            outHitResult.ImpactPoint = GetAbsolutePosition(hit.tile,hit.position);
        #else
            outHitResult.ImpactPoint = hit.position
        #endif
    }

    if (EnumHasAnyFlags(hitFlag, HitFlag::Normal) && !initialOverlap)
    {
        Float3 normal = hit.normal.SafeNormal();
        outHitResult.Normal = normal;
        outHitResult.ImpactNormal = normal;
    }
    outHitResult.StartPos = globalStartTransform.mTranslation;
    outHitResult.EndPos = outHitResult.StartPos + TRSVector3Type(delta);

    return true;
}

void WorldCollision::ConvertOverlappedResult(const PhysicsHitResult& hit, HitFlag hitFlag, HitResult& outHitResult, const TRS& globalStartTransform, const Float3& delta)
{
    outHitResult.bBlockingHit = true;
    outHitResult.bStartPenetrating = true;

    outHitResult.PositionRatio = 0.f;
    outHitResult.Distance = 0.f;
     
    outHitResult.Location = globalStartTransform.mTranslation;

    if (EnumHasAnyFlags(hitFlag, HitFlag::Position)) 
    {
        #ifdef CE_USE_DOUBLE_TRANSFORM
            outHitResult.ImpactPoint = GetAbsolutePosition(hit.tile, hit.position);
        #else
            outHitResult.ImpactPoint = hit.position
        #endif
    }

    outHitResult.StartPos = globalStartTransform.mTranslation;
    outHitResult.EndPos = globalStartTransform.mTranslation + TRSVector3Type(delta);

    if (EnumHasAnyFlags(hitFlag, HitFlag::Normal))
    {
        Float3 normal = hit.normal.SafeNormal();
        outHitResult.PenetrationDepth = MathUtils::Abs(hit.distance);
        outHitResult.ImpactNormal = normal;
        outHitResult.Normal = outHitResult.ImpactNormal;
    }
}

/////////////////////////////////////////////
// WorldCollisionUtility
//
/////////////////////////////////////////////
std::vector<const PhysicsGeometryBase*> WorldCollisionUtility::GetPhysicsGeometries(GameWorld* inWorld, ecs::EntityID inEntity, PhysicsGeometryBase::Type geoType)
{
    static std::vector<const PhysicsGeometryBase*> result;
    result.clear();

    if (inEntity == ecs::EntityID::InvalidHandle())
    {
        return result;
    }

    auto physicsComp = inWorld->GetComponent<PhysicsComponentG>(inEntity);
    if (!physicsComp.IsValid())
    {
        return result;
    }
    auto physicsCompReader = physicsComp.Read();

    auto physicsSys = inWorld->GetGameSystem<cross::PhysicsSystemG>();

    // Collect shapes extra added
    auto extraShape = physicsSys->GetExtraShape(physicsCompReader);
    // Collect shapes from imported mesh
    auto staticShape = physicsSys->GetPhysicsGeometry(physicsCompReader);

    switch (geoType)
    {
    case PhysicsGeometryBase::Type::Box:
        if (extraShape && !extraShape->mBoxGeometry.empty())
        {
            for (const auto& box : extraShape->mBoxGeometry)
            {
                result.emplace_back(&box);
            }
        }
        if (!staticShape->mBoxGeometry.empty())
        {
            for (const auto& box : staticShape->mBoxGeometry)
            {
                result.emplace_back(&box);
            }
        }
        break;

    case PhysicsGeometryBase::Type::Sphere:
        if (extraShape && !extraShape->mSphereGeometry.empty())
        {
            for (const auto& sphere : extraShape->mSphereGeometry)
            {
                result.emplace_back(&sphere);
            }
        }
        if (!staticShape->mSphereGeometry.empty())
        {
            for (const auto& sphere : staticShape->mSphereGeometry)
            {
                result.emplace_back(&sphere);
            }
        }
        break;

    case PhysicsGeometryBase::Type::Capsule:
        if (extraShape && !extraShape->mCapsuleGeometry.empty())
        {
            for (const auto& capsule : extraShape->mCapsuleGeometry)
            {
                result.emplace_back(&capsule);
            }
        }
        if (!staticShape->mCapsuleGeometry.empty())
        {
            for (const auto& capsule : staticShape->mCapsuleGeometry)
            {
                result.emplace_back(&capsule);
            }
        }
        break;
    default:
        break;
    }

    return result;
}

std::vector<const PhysicsGeometryBase*> WorldCollisionUtility::GetAllPhysicsGeometries(GameWorld* inWorld, ecs::EntityID inEntity)
{
    static std::vector<const PhysicsGeometryBase*> result;
    result.clear();

    if (inEntity == ecs::EntityID::InvalidHandle())
    {
        return result;
    }

    auto physicsComp = inWorld->GetComponent<PhysicsComponentG>(inEntity);
    if (!physicsComp.IsValid())
    {
        return result;
    }
    auto physicsCompReader = physicsComp.Read();

    auto physicsSys = inWorld->GetGameSystem<cross::PhysicsSystemG>();

    //Collect shapes from imported mesh
    auto physicsGeometry = physicsSys->GetPhysicsGeometry(physicsCompReader);
    if (physicsGeometry != nullptr)
    {
        const auto& boxShapes = physicsSys->GetBoxGeometry(physicsCompReader);
        if (!boxShapes.empty())
        {
            for (const auto& box : boxShapes)
            {
                result.emplace_back(&box);
            }
        }

        const auto& sphereShapes = physicsSys->GetSphereGeometry(physicsCompReader);
        if (!sphereShapes.empty())
        {
            for (const auto& sphere : sphereShapes)
            {
                result.emplace_back(&sphere);
            }
        }

        const auto& capsuleShapes = physicsSys->GetCapsuleGeometry(physicsCompReader);
        if (!capsuleShapes.empty())
        {
            for (auto& capsule : capsuleShapes)
            {
                result.emplace_back(&capsule);
            }
        }
    }

    return result;
}

TRS WorldCollisionUtility::GetPhysicsGeometryLocalTransform(const PhysicsGeometryBase* inPhyGeo)
{
    if (inPhyGeo == nullptr)
    {
        return TRS();
    }
    TRS trs;
    switch (inPhyGeo->GetGeometryType())
    {
    case PhysicsGeometryBase::Type::Box:
    {
        const PhysicsGeometryBox* box = static_cast<const PhysicsGeometryBox*>(inPhyGeo);
        trs.mRotation = TRSQuaternionType(box->rotate);
        trs.mTranslation = TRSVector3Type(box->position);
        break;
    }
    case PhysicsGeometryBase::Type::Sphere:
    {
        const PhysicsGeometrySphere* sphere = static_cast<const PhysicsGeometrySphere*>(inPhyGeo);
        trs.mTranslation = TRSVector3Type(sphere->position);
        break;
    }
    case PhysicsGeometryBase::Type::Capsule:
    {
        const PhysicsGeometryCapsule* capsule = static_cast<const PhysicsGeometryCapsule*>(inPhyGeo);
        trs.mRotation = TRSQuaternionType(capsule->rotate);
        trs.mTranslation = TRSVector3Type(capsule->position);
        break;
    }
    default:
        break;
    }
    return trs;
}

TRS WorldCollisionUtility::GetPhysicsGeometryWorldTransform(GameWorld* inWorld, ecs::EntityID bindEntity, const PhysicsGeometryBase* inPhyGeo)
{

    if (inPhyGeo == nullptr || bindEntity == ecs::EntityID::InvalidHandle())
    {
        return TRS();
    }
    auto transformSys = inWorld->GetGameSystem<TransformSystemG>();
    auto worldTransComp = inWorld->GetComponent<WorldTransformComponentG>(bindEntity);

    TRS localTRS = WorldCollisionUtility::GetPhysicsGeometryLocalTransform(inPhyGeo);
    TRS worldTRS;
    TRSMatrixAType localMatrix = TRSMatrixAType::Compose(localTRS.mScale, localTRS.mRotation, localTRS.mTranslation);
    (localMatrix * transformSys->GetWorldMatrixT(worldTransComp.Read())).Decompose(worldTRS.mScale, worldTRS.mRotation, worldTRS.mTranslation);
    return worldTRS;
}

void WorldCollisionUtility::VisualizePhysicsGeometry(GameWorld* inWorld, const PhysicsGeometryBase* inPhyGeo, const Float4x4& globalTransform)
{
    auto primitiveSys = inWorld->GetGameSystem<PrimitiveRenderSystemG>();
    cross::PrimitiveData shapePrim;

    switch (inPhyGeo->GetGeometryType())
    {
    case PhysicsGeometryBase::Type::Box:
    {
        const PhysicsGeometryBox* box = static_cast<const PhysicsGeometryBox*>(inPhyGeo);
        cross::PrimitiveGenerator::GenerateCubeFrame(&shapePrim, box->halfExtents, Float3::Zero());
        break;
    }
    case PhysicsGeometryBase::Type::Sphere:
    {
        const PhysicsGeometrySphere* sphere = static_cast<const PhysicsGeometrySphere*>(inPhyGeo);
        cross::PrimitiveGenerator::GenerateSphereFrame(&shapePrim, sphere->radius);
        break;
    }
    case PhysicsGeometryBase::Type::Capsule:
    {
        const PhysicsGeometryCapsule* capsule = static_cast<const PhysicsGeometryCapsule*>(inPhyGeo);
        cross::PrimitiveGenerator::GenerateCapsule(&shapePrim, capsule->radius, capsule->halfHeight * 2.0f);
        break;
    }
    default:
        break;
    }

    primitiveSys->DrawPrimitive(&shapePrim, globalTransform, cross::PrimitiveRenderSystemG::PrimitiveLook(ColorRGBAf(1.0f, 0.0f, 0.0f)));
}

void WorldCollisionUtility::VisualizeHitResult(GameWorld* inWorld, const HitResult& hitResult)
{
    auto primitiveSys = inWorld->GetGameSystem<PrimitiveRenderSystemG>();

    // Draw impact point
    cross::PrimitiveData spherePrim;
    cross::PrimitiveGenerator::GenerateSphere(&spherePrim, 5.0f);
    TRSMatrixAType globalTransform = TRSMatrixAType::Compose(TRSVector3Type::One(), TRSQuaternionType::Identity(), hitResult.ImpactPoint);
    Float3 tilePosition, offset;
    GetTileAndOffsetForAbsolutePosition(hitResult.ImpactPoint, tilePosition, offset);
    Float4x4A relativeMatrix = AbsoluteMatrixToRelativeMatrix(tilePosition, globalTransform);
    primitiveSys->DrawPrimitive(&spherePrim, relativeMatrix, cross::PrimitiveRenderSystemG::PrimitiveLook(ColorRGBAf(1.0f, 0.0f, 0.0f)));

    // Draw impact normal
    cross::PrimitiveData linePrim;
    cross::PrimitiveGenerator::GenerateLine(&linePrim, offset, offset + hitResult.ImpactNormal * 500.0f);
    primitiveSys->DrawPrimitive(&linePrim, Float4x4::Identity(), cross::PrimitiveRenderSystemG::PrimitiveLook(ColorRGBAf(0.0f, 1.0f, 1.0f)), tilePosition);
}

void WorldCollisionUtility::VisualizeRayCast(GameWorld* inWorld, const Float3& startPos, const Float3& delta)
{
    auto primitiveSys = inWorld->GetGameSystem<PrimitiveRenderSystemG>();

    // Draw ray
    cross::PrimitiveData linePrim;
    cross::PrimitiveGenerator::GenerateLine(&linePrim, startPos, startPos + delta);
    primitiveSys->DrawPrimitive(&linePrim, Float4x4::Identity(), cross::PrimitiveRenderSystemG::PrimitiveLook(ColorRGBAf(0.0f, 1.0f, 0.0f)));
}

}   // namespace cross