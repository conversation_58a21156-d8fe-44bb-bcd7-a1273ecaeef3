#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CharacterMovementComponent.h"
#include "CharacterMovementSystemG.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"

namespace cross
{
    ecs::ComponentDesc* cross::CharacterMovementComponentG::GetDesc()
    {
        return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::CharacterMovementComponentG>(
            { false, true, true },
            &CharacterMovementSystemG::SerializeCharacterMovementComponent, 
            &CharacterMovementSystemG::DeserializeCharacterMovementComponent, 
            &CharacterMovementSystemG::PostDeserializeCharacterMovementComponent);
    }
}