#include "CEGameplayModule.h"
#include "memoryhooker/Module.h"
IMPORT_MODULE
#pragma warning(push)
#pragma warning(disable : 4100)
void CEGameplayRegister();
void SystemRegisterR();
void SystemRegisterG();
namespace cross::scripts {
void CodeGenRegisterGeneratedClass();
}
namespace cross {
CEGameplayModule::CEGameplayModule()
{
    CEGameplayRegister();
    SystemRegisterG();
    SystemRegisterR();
    scripts::CodeGenRegisterGeneratedClass();
}
}   // namespace cross
#pragma warning(pop)